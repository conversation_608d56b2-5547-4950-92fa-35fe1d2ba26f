import { date, Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib/index';

describe('Purchase return node - test control on returnItems', () => {
    it('Create purchase return with non stock item and update returnItems', () =>
        Test.withContext(
            async context => {
                const purchaseReceipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR25' });
                const purchaseReceiptLine = await purchaseReceipt.lines.elementAt(0);
                const purchaseOrderId = await (await purchaseReceiptLine.getOrderDocumentLine())?.documentId;
                assert.isDefined(purchaseOrderId);
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#EUR',
                    returnRequestDate: date.make(2020, 8, 10),
                    returnItems: false,
                    lines: [
                        {
                            item: '#NonStockManagedItem',
                            quantity: 70,
                            unit: '#KILOGRAM',
                            grossPrice: 10,
                            reason: { id: 'R1' },
                            currency: '#EUR',
                            purchaseReceiptLine: {
                                purchaseReceiptLine,
                                returnedQuantity: 70,
                            },
                        },
                    ],
                });

                await newPurchaseReturn.$.save();
                await context.flushDeferredSaves();

                Test.assertDeepEqualDiagnoses(newPurchaseReturn.$.context.diagnoses, [
                    {
                        severity: 2,
                        path: ['lines', '<???>'],
                        message: 'The item: NonStockManagedItem, is not managed for the supplier: LECLERC.',
                    },
                    {
                        severity: 2,
                        path: [],
                        message:
                            'While saving PurchaseCreditMemo(<???>): The item: NonStockManagedItem, is not managed for the supplier: LECLERC.',
                    },
                    {
                        severity: 2,
                        path: [],
                        message: `While saving PurchaseReceipt(${purchaseReceipt._id}): The item: NonStockManagedItem, is not managed for the supplier: LECLERC.`,
                    },
                    {
                        message: `While saving PurchaseOrder(${purchaseOrderId}): The item: NonStockManagedItem, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${purchaseOrderId}): While saving PurchaseReceipt(<???>): The item: NonStockManagedItem, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                    {
                        message: `While saving PurchaseOrder(${purchaseOrderId}): While saving PurchaseInvoice(<???>): The item: NonStockManagedItem, is not managed for the supplier: LECLERC.`,
                        path: [],
                        severity: 2,
                    },
                ]);
            },
            { today: '2020-08-10' },
        ));

    it('Create purchase return  - Close', () =>
        Test.withContext(
            async context => {
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#EUR',
                    returnRequestDate: date.today(),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#KILOGRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: '#PR20|1',
                                returnedQuantity: 10,
                                returnedQuantityInStockUnit: 10000,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save({ flushDeferredActions: true });

                // Submitted for approval
                await newPurchaseReturn.$.set({ approvalStatus: 'pendingApproval' });
                await newPurchaseReturn.$.set({ status: 'pending' });
                await newPurchaseReturn.lines.forEach(async line => {
                    await line.$.set({ status: 'pending', approvalStatus: 'pendingApproval' });
                });
                await newPurchaseReturn.$.save();

                // Approved
                await newPurchaseReturn.$.set({ approvalStatus: 'approved' });
                await newPurchaseReturn.$.set({ status: 'inProgress' });
                await newPurchaseReturn.lines.forEach(async line => {
                    await line.$.set({ status: 'inProgress', approvalStatus: 'approved' });
                });
                await newPurchaseReturn.$.save();

                // Close
                const closed = await xtremPurchasing.nodes.PurchaseReturn.close(context, newPurchaseReturn);
                assert.equal(closed, true);
            },
            { today: '2020-08-10' },
        ));

    it('Create purchase return attribute type restricted to', () =>
        Test.withContext(
            async context => {
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: '#PR8|1',
                                returnedQuantity: 10,
                                returnedQuantityInStockUnit: 10,
                            },
                            storedAttributes: {
                                employee: '',
                                task: 'TASK1',
                                project: '',
                            },
                        },
                    ],
                });
                await assert.isRejected(newPurchaseReturn.$.save());
                assert.deepEqual(newPurchaseReturn.$.context.diagnoses, [
                    {
                        severity: 3,
                        path: ['lines', '-1000000002', 'storedAttributes'],
                        message: 'The Project attribute needs to be filled in.',
                    },
                ]);
            },
            { today: '2020-08-10' },
        ));

    it('Create purchase return default dimensions and attributes', () =>
        Test.withContext(
            async context => {
                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#USD',
                    returnRequestDate: date.make(2020, 8, 10),
                    lines: [
                        {
                            item: '#Muesli',
                            quantity: 10,
                            unit: '#GRAM',
                            grossPrice: 30,
                            reason: { id: 'R1' },
                            purchaseReceiptLine: {
                                purchaseReceiptLine: '#PR8|1',
                                returnedQuantity: 10,
                                returnedQuantityInStockUnit: 10,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save();
                assert.deepEqual(await (await newPurchaseReturn.lines.elementAt(0)).storedDimensions, {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                });

                assert.equal(
                    JSON.stringify(await (await newPurchaseReturn.lines.elementAt(0)).storedAttributes),
                    JSON.stringify({
                        project: 'AttPROJ',
                        task: 'Task1',
                    }),
                );
            },
            { today: '2020-08-10' },
        ));

    it('Create & delete purchase return with an invoiced purchase receipt line', () =>
        Test.withContext(
            async context => {
                let receipt57 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR57' });
                let receiptLine1 = await receipt57.lines.elementAt(0);
                assert.equal(await receiptLine1.lineInvoiceStatus, 'invoiced');
                assert.equal(await receiptLine1.lineReturnStatus, 'notReturned');
                assert.equal(await receiptLine1.lineStatus, 'closed');
                assert.equal(await receiptLine1.status, 'closed');

                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#US017',
                    currency: '#EUR',
                    lines: [
                        {
                            item: '#NonStockManagedItem',
                            quantity: 1,
                            unit: '#KILOGRAM',
                            grossPrice: 100,
                            reason: '#R1',
                            purchaseReceiptLine: {
                                purchaseReceiptLine: receiptLine1._id,
                                returnedQuantity: 1,
                                returnedQuantityInStockUnit: 1000,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save();
                receipt57 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR57' });
                receiptLine1 = await receipt57.lines.elementAt(0);
                assert.equal(await receiptLine1.lineInvoiceStatus, 'invoiced');
                assert.equal(await receiptLine1.lineReturnStatus, 'notReturned');
                assert.equal(await receiptLine1.lineStatus, 'closed');
                assert.equal(await receiptLine1.status, 'closed');

                await newPurchaseReturn.$.delete();
                assert.deepEqual(newPurchaseReturn.$.context.diagnoses, []);
                receipt57 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR57' });
                receiptLine1 = await receipt57.lines.elementAt(0);
                assert.equal(await receiptLine1.lineInvoiceStatus, 'invoiced');
                assert.equal(await receiptLine1.lineReturnStatus, 'notReturned');
                assert.equal(await receiptLine1.lineStatus, 'closed');
                assert.equal(await receiptLine1.status, 'closed');
            },
            { today: '2020-08-10' },
        ));

    it('Create & delete purchase return with a not invoiced purchase receipt line', () =>
        Test.withContext(
            async context => {
                let receipt44 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR44' });
                let receiptLine1 = await receipt44.lines.elementAt(0);
                assert.equal(await receiptLine1.lineInvoiceStatus, 'notInvoiced');
                assert.equal(await receiptLine1.lineReturnStatus, 'notReturned');
                assert.equal(await receiptLine1.lineStatus, 'pending');
                assert.equal(await receiptLine1.status, 'pending');

                const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                    returnSite: '#US001',
                    businessRelation: '#LECLERC',
                    currency: '#EUR',
                    lines: [
                        {
                            item: '#RSV9',
                            quantity: 5,
                            unit: '#PAIR',
                            grossPrice: 100,
                            reason: '#R1',
                            purchaseReceiptLine: {
                                purchaseReceiptLine: receiptLine1._id,
                                returnedQuantity: 5,
                                returnedQuantityInStockUnit: 5,
                            },
                        },
                    ],
                });
                await newPurchaseReturn.$.save();
                receipt44 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR44' });
                receiptLine1 = await receipt44.lines.elementAt(0);
                assert.equal(await receiptLine1.lineInvoiceStatus, 'notInvoiced');
                assert.equal(await receiptLine1.lineReturnStatus, 'notReturned');
                assert.equal(await receiptLine1.lineStatus, 'pending');
                assert.equal(await receiptLine1.status, 'pending');

                await newPurchaseReturn.$.delete();
                assert.deepEqual(newPurchaseReturn.$.context.diagnoses, []);
                receipt44 = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number: 'PR44' });
                receiptLine1 = await receipt44.lines.elementAt(0);
                assert.equal(await receiptLine1.lineInvoiceStatus, 'notInvoiced');
                assert.equal(await receiptLine1.lineReturnStatus, 'notReturned');
                assert.equal(await receiptLine1.lineStatus, 'pending');
                assert.equal(await receiptLine1.status, 'pending');
            },
            { today: '2020-08-10' },
        ));
    // Reproduce of the issue XT-87973
    it('Create purchase return - non inventory item', () =>
        Test.withContext(async context => {
            // Manage a specific use case on sites  :
            // - 501 swindon is inventory and not finance  //  ETS2-S01
            // - 500 uk limited is finance not inventory  // ETS1-S01
            // - 221 mg limited is inventory and not finance // DEP1-S01
            //  order of the sites in the company must be 221 -  500 - 501

            const companyS01 = await context.read(xtremSystem.nodes.Company, { id: 'S1' });

            const firstSite = await companyS01.sites.elementAt(0);
            const secondSite = await companyS01.sites.elementAt(1);
            const thirdSite = await companyS01.sites.elementAt(2);

            assert.equal(await firstSite.id, 'DEP1-S01');
            assert.equal(await secondSite.id, 'ETS1-S01');
            assert.equal(await thirdSite.id, 'ETS2-S01');

            const ukLimited = await context.read(xtremSystem.nodes.Site, { id: 'DEP1-S01' }, { forUpdate: true });
            await ukLimited.$.set({ isInventory: true, isFinance: false, financialSite: '#ETS1-S01' });
            await ukLimited.$.save();

            const swindon = await context.read(xtremSystem.nodes.Site, { id: 'ETS1-S01' }, { forUpdate: true });
            await swindon.$.set({ isInventory: false, isFinance: true });
            await swindon.$.save();

            const mgLimited = await context.read(xtremSystem.nodes.Site, { id: 'ETS2-S01' }, { forUpdate: true });
            await mgLimited.$.set({ isInventory: true, isFinance: false, financialSite: '#ETS1-S01' });
            await mgLimited.$.save();

            const itemSite = await context.create(xtremMasterData.nodes.ItemSite, {
                item: '#Service001',
                site: '#ETS2-S01',
            });
            await itemSite.$.save();

            const serviceItem = await context.read(
                xtremMasterData.nodes.Item,
                { id: 'Service001' },
                { forUpdate: true },
            );
            await serviceItem.$.set({ itemTaxGroup: '#SERVICES_NORMAL' });
            await serviceItem.$.save();

            const taxSolution = await context.read(xtremTax.nodes.TaxSolution, { id: 'FRSOL' }, { forUpdate: true });
            await taxSolution.$.set({ lines: [{ _action: 'delete', _sortValue: 200 }] });
            await taxSolution.$.save();

            const receipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
                site: '#ETS2-S01',
                businessRelation: '#LECLERC',
                currency: '#EUR',
                date: date.make(2020, 8, 10),
                lines: [
                    {
                        item: '#Service001',
                        quantity: 10,
                        unit: '#EACH',
                        grossPrice: 30,
                        storedDimensions: {
                            dimensionType01: '300',
                        },
                    },
                ],
            });
            await receipt.$.save();

            await xtremPurchasing.nodes.PurchaseReceipt.post(context, receipt);

            const lineId = (await receipt.lines.elementAt(0))._id;

            const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, {
                returnSite: '#ETS2-S01',
                businessRelation: '#LECLERC',
                currency: '#EUR',
                returnRequestDate: date.make(2020, 8, 10),
                lines: [
                    {
                        item: '#Service001',
                        quantity: 10,
                        unit: '#EACH',
                        grossPrice: 30,
                        reason: { id: 'R1' },
                        purchaseReceiptLine: {
                            purchaseReceiptLine: lineId,
                            returnedQuantity: 10,
                            returnedQuantityInStockUnit: 10,
                        },
                        storedDimensions: {
                            dimensionType01: '300',
                        },
                    },
                ],
            });
            await newPurchaseReturn.$.save();
        }));
});
