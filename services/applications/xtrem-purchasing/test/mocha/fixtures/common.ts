import type { Context, NodeSelectOptions, decimal, integer } from '@sage/xtrem-core';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

/** compare ProjectedStockRecord */
export function compareData(
    actual: xtremStockData.interfaces.ProjectedStockRecord[] | null,
    expected: xtremStockData.interfaces.ProjectedStockRecord[] | null,
): boolean {
    if (actual && expected) {
        assert.equal(actual.length, expected.length);

        for (let i: number = 0; i < actual.length; i += 1) {
            assert.equal(actual[i].day.toString(), expected[i].day.toString());
            assert.equal(actual[i].demand, expected[i].demand);
            assert.equal(actual[i].supply, expected[i].supply);
            assert.equal(actual[i].stockLevel, expected[i].stockLevel);
        }
        return true;
    }
    return actual === expected;
}

export async function changeRequisitionApprovalToDraft(context: Context, reqisitionNumber: string) {
    const requistion = await context.read(
        xtremPurchasing.nodes.PurchaseRequisition,
        { number: reqisitionNumber },
        { forUpdate: true },
    );
    await requistion.$.set({ approvalStatus: 'draft' });

    if (!(await requistion.$.trySave())) {
        throw new Error(context.diagnoses.join(' '));
    }
}

export async function getLastBaseDocumentLineId(context: Context): Promise<integer> {
    const maxId = await context
        .queryAggregate(xtremMasterData.nodes.BaseDocumentLine, {
            group: {},
            values: { _id: { max: true } },
        })
        .elementAt(0);

    return maxId.values._id.max;
}

export type LandedCostLineToTest = {
    documentLine: { _id: integer };
    _documentLine_constructor: string;
    landedCost: { id: string };
    landedCostAllocation: {
        line: { documentLine: { _id: integer } };
    };
    actualCostAmountInCompanyCurrency: decimal;
    actualAllocatedCostAmountInCompanyCurrency: decimal;
};

export type StockCorrectionDetailToTest = {
    documentLine: { _id: integer };
    _documentLine_constructor: string;
    reasonCode: { id: string };
    amountToAbsorb: decimal;
    nonAbsorbedAmount: decimal;
    impactedQuantity: decimal;
};

export async function assertionLandedCostLine(
    context: Context,
    selectOptions: NodeSelectOptions<xtremLandedCost.nodes.LandedCostLine>,
    expectedLandedCostLines: LandedCostLineToTest[],
    message?: string,
) {
    const actualLandedCostLines = await context.select(
        xtremLandedCost.nodes.LandedCostLine,
        {
            documentLine: { _id: true },
            landedCost: { id: true },
            landedCostAllocation: { line: { documentLine: { _id: true } } },
            actualCostAmountInCompanyCurrency: true,
            actualAllocatedCostAmountInCompanyCurrency: true,
        },
        selectOptions,
    );

    actualLandedCostLines.forEach((actualLandedCostLine, index) => {
        assert.deepEqual(
            actualLandedCostLine,
            expectedLandedCostLines[index],
            `${message ?? ''} landed cost line index=${index}`,
        );
    });
}

export async function assertionStockCorrectionDetail(
    context: Context,
    selectOptions: NodeSelectOptions<xtremStockData.nodes.StockCorrectionDetail>,
    expectedStockCorrectionDetails: StockCorrectionDetailToTest[],
    message?: string,
) {
    const actualStockCorrectionDetails = await context.select(
        xtremStockData.nodes.StockCorrectionDetail,
        {
            documentLine: { _id: true },
            reasonCode: { id: true },
            amountToAbsorb: true,
            nonAbsorbedAmount: true,
            impactedQuantity: true,
        },
        selectOptions,
    );

    assert.deepEqual(actualStockCorrectionDetails.length, expectedStockCorrectionDetails.length);

    actualStockCorrectionDetails.forEach((actualLandedCostLine, index) => {
        assert.deepEqual(
            actualLandedCostLine,
            expectedStockCorrectionDetails[index],
            `${message} stock correction detail index=${index}`,
        );
    });
}
