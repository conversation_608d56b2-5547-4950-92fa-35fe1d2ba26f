import type { Context, UpdateAction, decimal, integer } from '@sage/xtrem-core';
import { asyncArray, date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../lib';

export type PurchaseOrderLineToTest = {
    numberOfReceipts: integer;
    receivedQuantity: decimal;
    receivedQuantityInStockUnit: decimal;
    quantityToReceive: decimal;
    quantityToReceiveInStockUnit: decimal;
    quantityReceivedInProgress: decimal;
    lineReceiptStatus: Awaited<xtremPurchasing.nodes.PurchaseOrderLine['lineReceiptStatus']>;
    status: Awaited<xtremPurchasing.nodes.PurchaseOrderLine['status']>;
};

export async function assertionPurchaseOrderLine(
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
    expectedResult: PurchaseOrderLineToTest,
    message?: string,
) {
    const actualResult: PurchaseOrderLineToTest = {
        numberOfReceipts: await purchaseOrderLine.purchaseReceiptLines.length,
        receivedQuantity: await purchaseOrderLine.receivedQuantity,
        receivedQuantityInStockUnit: await purchaseOrderLine.receivedQuantityInStockUnit,
        quantityToReceive: await purchaseOrderLine.quantityToReceive,
        quantityToReceiveInStockUnit: await purchaseOrderLine.quantityToReceiveInStockUnit,
        lineReceiptStatus: await purchaseOrderLine.lineReceiptStatus,
        quantityReceivedInProgress: await purchaseOrderLine.quantityReceivedInProgress,
        status: await purchaseOrderLine.status,
    };

    assert.deepEqual(actualResult, expectedResult, message);
}

export async function createReceiptFromOrder(
    context: Context,
    orderLines: { purchaseOrderLineId: integer; receivedQuantity: decimal; defaultLocation?: string }[],
) {
    const purchaseOrderLines = context.query(xtremPurchasing.nodes.PurchaseOrderLine, {
        filter: { _id: { _in: orderLines.map(line => line.purchaseOrderLineId) } },
    });
    const firstLine = await purchaseOrderLines.elementAt(0);

    const site = await firstLine.site;

    const purchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
        date: date.make(2020, 1, 3),
        site,
        businessRelation: await firstLine.supplier,
        currency: await firstLine.currency,
        status: 'pending',
        supplierAddress: await (await firstLine.document).supplierAddress,
        lines: await asyncArray(orderLines)
            .map(async orderLine => {
                const purchaseOrderLine = await purchaseOrderLines.find(
                    line => line._id === orderLine.purchaseOrderLineId,
                );
                assert.isDefined(purchaseOrderLine);
                const item = await purchaseOrderLine?.item;
                const quantityInStockUnit =
                    (orderLine.receivedQuantity * ((await purchaseOrderLine?.quantityInStockUnit) ?? 0)) /
                    ((await purchaseOrderLine?.quantity) ?? 0);
                const stockUnit = await purchaseOrderLine?.stockUnit;
                return {
                    item,
                    grossPrice: await purchaseOrderLine?.grossPrice,
                    unit: await purchaseOrderLine?.unit,
                    quantity: orderLine.receivedQuantity,
                    stockUnit,
                    quantityInStockUnit,
                    stockDetails: [
                        {
                            site,
                            item,
                            location: orderLine.defaultLocation,
                            status: '#A',
                            stockUnit,
                            stockDetailQuantityInStockUnit: quantityInStockUnit,
                            stockDetailQuantity: orderLine.receivedQuantity,
                            owner: await site.id,
                        },
                    ],
                    purchaseOrderLine: {
                        purchaseOrderLine: orderLine.purchaseOrderLineId,
                    },
                    _action: 'create' as UpdateAction,
                };
            })
            .toArray(),
    });
    await purchaseReceipt.$.save();
    return purchaseReceipt;
}

export async function closeOrder(context: Context, poNumber: string) {
    const purchaseOrder = await context.read(xtremPurchasing.nodes.PurchaseOrder, {
        number: poNumber,
    });

    const isClosed = await xtremPurchasing.nodes.PurchaseOrder.close(context, purchaseOrder);
    assert.isTrue(isClosed);
}
