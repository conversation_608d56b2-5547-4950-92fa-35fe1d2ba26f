import { Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import {
    isPurchaseOrderLineActionDisabled,
    isPurchaseOrderLinePropertyDisabled,
    isPurchaseRequisitionLineActionDisabled,
    isPurchaseRequisitionLinePropertyDisabled,
} from '../../../lib/shared-functions/index';

describe('Purchasing edit-rules shared functions - is grid line action/property disabled', () => {
    it('Purchase requisition - isPurchaseRequisitionLinePropertyDisabled testing', () =>
        Test.withContext(() => {
            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('', '', '', ''));

            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('draft', '', '', ''));
            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('draft', '', '', ''));
            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('draft', 'draft', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('draft', 'pending', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('draft', 'inProgress', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('draft', 'closed', '', ''));

            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('pending', '', '', ''));
            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('pending', 'draft', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('pending', 'pending', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('pending', 'inProgress', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('pending', 'closed', '', ''));

            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('inProgress', '', '', ''));
            assert.isFalse(isPurchaseRequisitionLinePropertyDisabled('inProgress', 'draft', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('inProgress', 'pending', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('inProgress', 'inProgress', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('inProgress', 'closed', '', ''));

            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('closed', '', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('closed', 'draft', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('closed', 'pending', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('closed', 'inProgress', '', ''));
            assert.isTrue(isPurchaseRequisitionLinePropertyDisabled('closed', 'closed', '', ''));
        }));
    it('Purchase requisition - isPurchaseRequisitionLineActionDisabled testing', () =>
        Test.withContext(() => {
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('', '', ''));

            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', '', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', 'draft', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'pending', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'inProgress', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'closed', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', 'draft', 'applyDefaultSuppliers'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', 'pending', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'inProgress', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'closed', 'applyDefaultSuppliers'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', 'draft', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', 'pending', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'inProgress', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'closed', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'draft', 'close'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', 'pending', 'close'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('draft', 'inProgress', 'close'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('draft', 'closed', 'close'));

            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', '', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', 'draft', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'pending', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'inProgress', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'closed', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', 'draft', 'applyDefaultSuppliers'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', 'pending', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'inProgress', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'closed', 'applyDefaultSuppliers'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', 'draft', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', 'pending', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'inProgress', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'closed', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'draft', 'close'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', 'pending', 'close'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('pending', 'inProgress', 'close'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('pending', 'closed', 'close'));

            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', '', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', 'draft', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'pending', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'inProgress', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'closed', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', 'draft', 'applyDefaultSuppliers'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', 'pending', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'inProgress', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'closed', 'applyDefaultSuppliers'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', 'draft', ''));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', 'pending', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'inProgress', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'closed', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'draft', 'close'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', 'pending', 'close'));
            assert.isFalse(isPurchaseRequisitionLineActionDisabled('inProgress', 'inProgress', 'close'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('inProgress', 'closed', 'close'));

            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', '', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'draft', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'pending', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'inProgress', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'closed', ''));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'draft', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'pending', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'inProgress', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'closed', 'applyDefaultSuppliers'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'draft', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'pending', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'inProgress', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'closed', 'edit'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'draft', 'close'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'pending', 'close'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'inProgress', 'close'));
            assert.isTrue(isPurchaseRequisitionLineActionDisabled('closed', 'closed', 'close'));
        }));
    it('Purchase order - isPurchaseOrderLinePropertyDisabled testing', () =>
        Test.withContext(() => {
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('', '', '', ''));

            assert.isFalse(isPurchaseOrderLinePropertyDisabled('draft', '', '', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('draft', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'closed', '', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('draft', 'draft', 'expectedReceiptDate', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('draft', 'pending', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'inProgress', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'closed', 'expectedReceiptDate', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('draft', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'pending', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'inProgress', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('draft', 'closed', 'item', ''));

            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', '', '', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'closed', '', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'draft', 'expectedReceiptDate', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'pending', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'inProgress', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'closed', 'expectedReceiptDate', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'pending', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'inProgress', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'closed', 'item', ''));

            assert.isFalse(isPurchaseOrderLinePropertyDisabled('inProgress', '', '', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('inProgress', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'closed', '', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('inProgress', 'draft', 'expectedReceiptDate', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('inProgress', 'pending', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'inProgress', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'closed', 'expectedReceiptDate', ''));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('inProgress', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'pending', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'inProgress', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('inProgress', 'closed', 'item', ''));

            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', '', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'closed', '', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'draft', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'pending', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'inProgress', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'closed', 'expectedReceiptDate', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'pending', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'pending', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'inProgress', 'item', ''));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('closed', 'closed', 'item', ''));

            // Approval status - Confirmed

            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', '', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'draft', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'pending', '', 'confirmed'));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'inProgress', '', 'confirmed'));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'closed', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'draft', 'expectedReceiptDate', 'confirmed'));
            assert.isFalse(
                isPurchaseOrderLinePropertyDisabled('pending', 'pending', 'expectedReceiptDate', 'confirmed'),
            );
            assert.isTrue(
                isPurchaseOrderLinePropertyDisabled('pending', 'inProgress', 'expectedReceiptDate', 'confirmed'),
            );
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'closed', 'expectedReceiptDate', 'confirmed'));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'draft', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLinePropertyDisabled('pending', 'pending', 'item', 'confirmed'));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'inProgress', 'item', 'confirmed'));
            assert.isTrue(isPurchaseOrderLinePropertyDisabled('pending', 'closed', 'item', 'confirmed'));
        }));
    it('Purchase order - isPurchaseOrderLineActionDisabled testing', () =>
        Test.withContext(() => {
            assert.isFalse(isPurchaseOrderLineActionDisabled('', '', '', ''));

            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', '', '', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'closed', '', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', 'draft', 'dimensions', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', 'pending', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'inProgress', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'closed', 'dimensions', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'pending', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'inProgress', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'closed', 'edit', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', 'draft', 'close', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', 'pending', 'close', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('draft', 'inProgress', 'close', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('draft', 'closed', 'close', ''));

            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', '', '', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', '', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', 'dimensions', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'pending', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'inProgress', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', 'dimensions', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'pending', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'inProgress', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', 'edit', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', 'close', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'pending', 'close', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'inProgress', 'close', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', 'close', ''));

            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', '', '', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'closed', '', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', 'draft', 'dimensions', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', 'pending', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'inProgress', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'closed', 'dimensions', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'pending', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'inProgress', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'closed', 'edit', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', 'draft', 'close', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', 'pending', 'close', ''));
            assert.isFalse(isPurchaseOrderLineActionDisabled('inProgress', 'inProgress', 'close', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('inProgress', 'closed', 'close', ''));

            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', '', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'draft', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'pending', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'inProgress', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'closed', '', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'draft', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'pending', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'inProgress', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'closed', 'dimensions', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'draft', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'pending', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'inProgress', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'closed', 'edit', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'draft', 'close', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'pending', 'close', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'inProgress', 'close', ''));
            assert.isTrue(isPurchaseOrderLineActionDisabled('closed', 'closed', 'close', ''));

            // Approval status - Confirmed
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', '', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'pending', '', 'confirmed'));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'inProgress', '', 'confirmed'));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', 'dimensions', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'pending', 'dimensions', 'confirmed'));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'inProgress', 'dimensions', 'confirmed'));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', 'dimensions', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', '', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'pending', 'edit', 'confirmed'));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'inProgress', 'edit', 'confirmed'));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', 'edit', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'draft', 'close', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'pending', 'close', 'confirmed'));
            assert.isFalse(isPurchaseOrderLineActionDisabled('pending', 'inProgress', 'close', 'confirmed'));
            assert.isTrue(isPurchaseOrderLineActionDisabled('pending', 'closed', 'close', 'confirmed'));
        }));
});
