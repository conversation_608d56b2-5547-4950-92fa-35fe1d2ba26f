{
    xtremPurchasing {
        purchaseReceipt {
            query(filter: "{number: 'PR1'}") {
                edges {
                    node {
                        number
                        status
                        displayStatus
                        invoiceStatus
                        returnStatus
                        date
                        paymentTerm {
                            name
                        }
                        text {
                            value
                        }
                        totalAmountExcludingTax
                        totalAmountExcludingTaxInCompanyCurrency
                        totalTaxAmount
                        totalTaxableAmount
                        totalExemptAmount
                        taxCalculationStatus
                        totalAmountIncludingTax
                        totalAmountIncludingTaxInCompanyCurrency
                        totalTaxAmountAdjusted
                        supplier {
                            businessEntity {
                                id
                            }
                        }
                        currency {
                            id
                        }
                        site {
                            id
                        }
                        currency {
                            id
                        }
                        transactionCurrency {
                            id
                        }
                        companyCurrency {
                            id
                        }
                        companyFxRate
                        companyFxRateDivisor
                        taxes {
                            query {
                                edges {
                                    node {
                                        currency {
                                            id
                                        }
                                        taxCategory
                                        tax
                                        nonTaxableAmount
                                        taxRate
                                        taxAmount
                                        exemptAmount
                                        taxableAmount
                                        taxAmountAdjusted
                                    }
                                }
                            }
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        status
                                        lineInvoiceStatus
                                        lineReturnStatus
                                        item {
                                            id
                                        }
                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        stockUnit {
                                            id
                                        }
                                        amountExcludingTax
                                        amountExcludingTaxInCompanyCurrency
                                        taxableAmount
                                        taxAmount
                                        exemptAmount
                                        taxDate
                                        amountIncludingTax
                                        amountIncludingTaxInCompanyCurrency
                                        taxAmountAdjusted
                                        #taxCalculationStatus
                                        text {
                                            value
                                        }
                                        taxes {
                                            query {
                                                edges {
                                                    node {
                                                        currency {
                                                            id
                                                        }
                                                        taxCategory
                                                        tax
                                                        nonTaxableAmount
                                                        taxRate
                                                        taxAmount
                                                        exemptAmount
                                                        taxableAmount
                                                        taxAmountAdjusted
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        purchaseReturns {
                            query {
                                edges {
                                    node {
                                        purchaseReturnLine {
                                            item {
                                                name
                                            }
                                        }
                                        returnedQuantity
                                        returnedQuantityInStockUnit
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
