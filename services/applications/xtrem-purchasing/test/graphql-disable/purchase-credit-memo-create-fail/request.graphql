mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            create(
                data: {
                    site: 1
                    billBySupplier: 2
                    totalTaxAmount: 1
                    totalAmountExcludingTax: 10
                    reason: "#R1"
                    supplierDocumentNumber: "Sage-CM658"
                    creditMemoDate: "2020-09-01"
                    lines: [{ item: 81, quantity: 10, unit: "#LITER", amountExcludingTax: 30 }]
                }
            ) {
                number
                site {
                    _id
                }
                status
                creditMemoDate
                dueDate
                supplierDocumentNumber
                text {
                    value
                }
                reason {
                    id
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                paymentTerm {
                    name
                }
                totalAmountExcludingTax
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                totalTaxAmount
                totalAmountIncludingTax
                billBySupplier {
                    _id
                }
                payToSupplier {
                    _id
                }
            }
        }
    }
}
