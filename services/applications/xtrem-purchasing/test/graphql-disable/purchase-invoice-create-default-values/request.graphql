mutation {
    xtremPurchasing {
        purchaseInvoice {
            create(
                data: {
                    site: 1
                    billBySupplier: "#LECLERC"
                    currency: "#EUR"
                    invoiceDate: "2020-08-10"
                    text: { value: "value" }
                    matchingStatus: "variance"
                    totalAmountExcludingTax: 30
                    totalTaxAmount: 12
                    lines: [{ item: 81, quantity: 10, unit: "#LITER", amountExcludingTax: 30 }]
                    matchingUser: "#<EMAIL>"
                }
            ) {
                lines {
                    query {
                        edges {
                            node {
                                unit {
                                    id
                                }
                                grossPrice
                                priceOrigin
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                amountIncludingTax
                                amountIncludingTaxInCompanyCurrency
                            }
                        }
                    }
                }
            }
        }
    }
}
