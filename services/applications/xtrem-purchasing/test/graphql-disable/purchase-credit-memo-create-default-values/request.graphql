mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            create(
                data: {
                    site: 1
                    billBySupplier: "#LECLERC"
                    totalAmountExcludingTax: 200
                    totalTaxAmount: 10
                    reason: "#R1"
                    text: { value: "test" }
                    supplierDocumentNumber: "LECLERC845"
                    lines: [
                        { item: 81, quantity: 10, unit: "#LITER", amountExcludingTax: 20 }
                        {
                            item: 81
                            quantity: 20
                            unit: "#LITER"
                            amountExcludingTax: 180
                            grossPrice: 10
                            discount: 10
                            charge: 0
                        }
                    ]
                }
            ) {
                number
                calculatedTotalAmountExcludingTax
                site {
                    _id
                }
                status
                creditMemoDate
                dueDate
                supplierDocumentNumber
                text {
                    value
                }
                reason {
                    id
                }
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                paymentTerm {
                    name
                }
                totalAmountExcludingTax
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                totalTaxAmount
                totalAmountIncludingTax
                calculatedTotalAmountIncludingTaxInCompanyCurrency
                billBySupplier {
                    _id
                }
                payToSupplier {
                    _id
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                discount
                                charge
                                netPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
