import type { decimal } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremPurchasing from '../../../../index';

export interface ValueCorrectionData {
    correctedDocumentLineID: decimal;
    reasonCodeID: string;
    absorbedAmount: decimal;
    nonAbsorbedAmount: decimal;
}

export interface ValueCorrections {
    valueCorrections: ValueCorrectionData[];
}

export interface ValueCorrectionPostData {
    result: string;
    linesPosted: ValueCorrections[];
}

export interface PostInvoiceResult {
    invoiceLine: xtremPurchasing.nodes.PurchaseInvoiceLine;
    postResult: xtremFinanceData.interfaces.MutationResult & {
        stockPostingResult: string;
    };
}
