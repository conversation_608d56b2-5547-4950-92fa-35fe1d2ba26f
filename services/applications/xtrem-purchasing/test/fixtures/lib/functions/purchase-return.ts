import type * as xtremPurchasing from '../../../../lib';

export async function getExpectedPT250001JE(purchaseReturn: xtremPurchasing.nodes.PurchaseReturn, batchId: string) {
    const financialSite = await purchaseReturn.financialSite;
    const currency = await financialSite.currency;
    const supplier = await purchaseReturn.supplier;
    const companyCurrency = await (await financialSite.legalCompany).currency;
    const purchaseReturnLine1 = await purchaseReturn.lines.at(0);

    return {
        documentSysId: purchaseReturn._id,
        batchId,
        financialSiteSysId: financialSite._id,
        documentNumber: 'PT250001',
        documentDate: '2025-02-07',
        currencySysId: currency._id,
        batchSize: 1,
        documentType: 'purchaseReturn',
        targetDocumentType: 'journalEntry',
        documentLines: [
            {
                baseDocumentLineSysId: purchaseReturnLine1?._id,
                movementType: 'stockJournal',
                sourceDocumentNumber: 'PR250004',
                currencySysId: companyCurrency._id,
                companyFxRate: '1',
                companyFxRateDivisor: '1',
                sourceDocumentType: 'purchaseReceipt',
                fxRateDate: '2025-02-07',
                supplierSysId: supplier._id,
                itemSysId: (await purchaseReturnLine1?.item)?._id,
                amounts: [
                    {
                        amountType: 'amount',
                        amount: '35',
                        documentLineType: 'documentLine',
                    },
                ],
                storedDimensions: {
                    dimensionType01: '300',
                    dimensionType02: 'CHANNELVALUE1',
                    dimensionType04: 'DIMTYPE2VALUE2',
                },
                storedAttributes: {
                    businessSite: 'DEP1-S01',
                    project: 'AttPROJ',
                    financialSite: 'DEP1-S01',
                    stockSite: 'DEP1-S01',
                    item: 'AI_ITEM_001',
                    supplier: 'DESUP01',
                },
            },
        ],
    };
}
