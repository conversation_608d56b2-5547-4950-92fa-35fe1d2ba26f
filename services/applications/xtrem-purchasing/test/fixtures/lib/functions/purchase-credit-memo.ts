import type * as xtremPurchasing from '../../../../lib';

export async function getExpectedPC250001JE(
    purchaseCreditMemo: xtremPurchasing.nodes.PurchaseCreditMemo,
    batchId: string,
) {
    const site = await purchaseCreditMemo.site;
    const financialSite = await purchaseCreditMemo.financialSite;
    const currency = await financialSite.currency;
    const companyCurrency = await (await financialSite.legalCompany).currency;
    const purchaseCreditMemoLine1 = await purchaseCreditMemo.lines.at(0);
    const paymentTerm = await purchaseCreditMemo.paymentTerm;
    const supplier = await purchaseCreditMemo.supplier;
    const payToSupplierSysId = await purchaseCreditMemo.payToSupplier;
    return {
        documentSysId: purchaseCreditMemo._id,
        batchId,
        financialSiteSysId: financialSite._id,
        documentNumber: 'PC250001',
        documentDate: '2025-02-13',
        dueDate: '2025-03-15',
        supplierDocumentDate: '2025-02-13',
        supplierDocumentNumber: '',
        currencySysId: currency._id,
        batchSize: 1,
        documentType: 'purchaseCreditMemo',
        targetDocumentType: 'accountsPayableInvoice',
        paymentTermSysId: paymentTerm._id,
        sourceLines: [],
        documentLines: [
            {
                baseDocumentLineSysId: purchaseCreditMemoLine1?._id,
                movementType: 'document',
                sourceDocumentNumber: 'PI250006',
                sourceDocumentType: 'purchaseInvoice',
                payToSupplierSysId: payToSupplierSysId._id,
                payToSupplierAddressSysId: (await supplier.payToAddress)._id,
                supplierSysId: supplier._id,
                recipientSiteSysId: site._id,
                currencySysId: companyCurrency._id,
                companyFxRate: '1',
                companyFxRateDivisor: '1',
                fxRateDate: '2025-02-13',
                itemSysId: (await purchaseCreditMemoLine1?.item)?._id,
                amounts: [
                    {
                        amountType: 'amountExcludingTax',
                        amount: '20',
                        documentLineType: 'documentLine',
                        baseTaxSysId: 2484,
                        taxSysId: 81,
                    },
                ],
                storedDimensions: {},
                storedAttributes: {
                    businessSite: 'DES01',
                    financialSite: 'DES01',
                    stockSite: 'DES01',
                    item: 'NonStockManagedItem',
                    supplier: 'US017',
                },
                taxDate: '2025-02-13',
                taxes: [{ baseTaxSysId: 2484 }],
            },
        ],
        taxes: [{ baseTaxSysId: 2048 }],
    };
}
