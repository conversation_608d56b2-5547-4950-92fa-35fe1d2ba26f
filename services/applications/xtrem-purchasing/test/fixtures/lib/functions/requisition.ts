import type { Context } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../../lib';

/** US002 and Milk */
export async function createRequisition(context: Context) {
    const requisition = await context.create(xtremPurchasing.nodes.PurchaseRequisition, {
        site: '#US002',
        requestDate: date.make(2020, 8, 10),
        lines: [{ item: '#Milk', quantity: 10, unit: '#KILOGRAM' }],
    });
    await requisition.$.save({ flushDeferredActions: true });

    return requisition;
}

export async function checkStatus(
    context: Context,
    param: {
        document: xtremPurchasing.nodes.PurchaseRequisition;
        status: xtremPurchasing.enums.PurchaseDocumentStatus;
        approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus;
    },
) {
    const { document, ...expectedStatus } = param;

    const loadDocument = await context.read(
        xtremPurchasing.nodes.PurchaseRequisition,
        { _id: document._id },
        { forUpdate: true },
    );

    const status = {
        status: await loadDocument.status,
        approvalStatus: await loadDocument.approvalStatus,
    };

    assert.deepEqual(status, expectedStatus);
}
