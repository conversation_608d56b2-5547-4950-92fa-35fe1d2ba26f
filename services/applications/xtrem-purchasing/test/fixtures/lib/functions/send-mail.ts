import { Test } from '@sage/xtrem-core';
import * as xtremMailer from '@sage/xtrem-mailer';
import { assert } from 'chai';
import type * as sinon from 'sinon';

export function patchConfigMailer() {
    Test.patchConfig({
        packages: {
            '@sage/xtrem-mailer': {
                service: 'SendGrid',
                redirectUrl: 'http://localhost:8240',
                auth: { apiKey: 'Dummy<PERSON>ey' },
                from: {
                    address: '<EMAIL>',
                    name: 'Toto',
                },
            },
        } as Partial<xtremMailer.interfaces.MailerConfig>,
    });
}

export function stubSendMail(
    sandbox: sinon.SinonSandbox,
    toTest: {
        recipients: xtremMailer.interfaces.Recipients;
        subject: string;
        htmlMatcher: (string | RegExp)[];
    },
) {
    patchConfigMailer();

    sandbox
        .stub(xtremMailer.classes.SendGridMailService.prototype, 'sendMail')
        .callsFake(
            async (
                recipients: xtremMailer.interfaces.Recipients,
                subject: string,
                messageBody: xtremMailer.interfaces.MessageBody,
            ) => {
                await Promise.resolve();
                assert.isNotNull(recipients);
                assert.isArray(recipients.to);
                assert.equal(recipients.to[0].address, toTest.recipients.to[0].address);
                assert.equal(recipients.to[0].name, toTest.recipients.to[0].name);
                assert.equal(subject, toTest.subject, `>${subject}< !== >${toTest.subject}<`);
                toTest.htmlMatcher.forEach(match => {
                    if (match instanceof RegExp) {
                        assert.match(messageBody.html, match);
                    } else {
                        assert.include(messageBody.html, match);
                    }
                });
                assert.isNotNull(messageBody);
            },
        );

    return sandbox.spy(xtremMailer.classes.SendGridMailService.prototype, 'send');
}
