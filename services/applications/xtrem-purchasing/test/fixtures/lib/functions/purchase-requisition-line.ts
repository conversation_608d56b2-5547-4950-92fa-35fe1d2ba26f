import type { Context } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremPurchasing from '../../../../lib';

export async function checkRequisitionLInes(
    context: Context,
    filter: { site: string; supplier: string; currency: string },
    wantedRequisitionLines: string[],
) {
    const filteredIds = await xtremPurchasing.nodes.PurchaseRequisitionLine.getFilteredList(
        context,
        filter.site,
        filter.supplier,
        filter.currency,
    );
    const requisitionLines = await asyncArray(filteredIds)
        .map(line => line.$.getNaturalKeyValue())
        .toArray();

    assert.equal(requisitionLines.length, wantedRequisitionLines.length, requisitionLines.join(','));
    assert.deepEqual(requisitionLines, wantedRequisitionLines);
}

/** site US001  LECLERC  */
export const requisitionLines1 = [
    'REQ10|10',
    'REQ10|20',
    'REQ11|10',
    'REQ11|20',
    'REQ11|30',
    'REQ15|10',
    'REQ17|10',
    'REQ17|20',
    'REQ19|10',
    'REQ19|20',
    'REQ20|10',
    'REQ20|20',
    'REQ21|10',
    'REQ21|20',
    'REQ24|10',
    'REQ24|20',
    'REQ7|10',
    'REQ8|10',
    'REQ8|20',
    'REQ8|30',
    'REQ9|10',
    'REQ9|20',
];
