import type { Context, integer } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as sinon from 'sinon';
import * as xtremPurchasing from '../../../../lib';

export function activateFakeReceiptLineUpdate(context: Context) {
    sinon.stub(context, 'notify').callsFake(
        async (
            event: string,
            params: {
                documentIds: integer[];
                documentLineIds: integer[];
            },
        ) => {
            if (event === 'BaseDocument/updateLineTo/start') {
                await xtremMasterData.nodes.BaseDocument.updateLineTo(
                    context,
                    params.documentIds,
                    params.documentLineIds,
                );
            }
            return Promise.resolve('');
        },
    );
}

export function simulateReceiptPostToStock(context: Context, receiptNumbers: string[]) {
    return asyncArray(receiptNumbers)
        .map(async number => {
            // Simulate that the receipt is posted to stock
            const receipt = await context.read(xtremPurchasing.nodes.PurchaseReceipt, { number }, { forUpdate: true });
            const purchaseReceiptLineId = (await receipt.lines.elementAt(0))._id;
            await receipt.$.set({
                lines: [
                    {
                        _id: purchaseReceiptLineId,
                        _action: 'update',
                        stockTransactionStatus: 'completed',
                    },
                ],
            });
            return purchaseReceiptLineId;
        })
        .toArray();
}
