mutation {
    xtremPurchasing {
        purchaseReceipt {
            update(
                data: {
                    _id: "#PR34"
                    carrier: null
                    lines: [
                        {
                            _action: "update"
                            _sortValue: "1"
                            charge: 0
                            completed: true
                            discount: 0
                            grossPrice: 100
                            item: "#Muesli"
                            itemDescription: null
                            lineInvoiceStatus: "notInvoiced"
                            lineReturnStatus: "notReturned"
                            status: "pending"
                            netPrice: 100
                            priceOrigin: null
                            purchaseOrderLine: { purchaseOrderLine: "#PO23|3800" }
                            unit: "#KILOGRAM"
                            quantity: 4
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    text: null
                    currency: "#EUR"
                    businessRelation: "#LECLERC"
                    site: "#US001"
                    number: "PR34"
                    date: "2021-11-16"
                    supplierDocumentNumber: null
                    status: "draft"
                    returnStatus: "notReturned"
                    invoiceStatus: "notInvoiced"
                }
            ) {
                supplierAddress {
                    country__name: country {
                        name
                        id
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                receivingAddress {
                    country__name: country {
                        name
                        id
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                returnAddress {
                    country__name: country {
                        name
                        id
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                number
                site {
                    primaryAddress___id: primaryAddress {
                        country___id: country {
                            id
                        }
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                    name
                    id
                    isLocationManaged
                }
                supplier {
                    businessEntity {
                        currency__id: currency {
                            name
                            id
                            decimalDigits
                            symbol
                        }
                        name
                        id
                    }
                    primaryAddress___id: primaryAddress {
                        country___id: country {
                            id
                        }
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                }
                currency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                date
                supplierDocumentNumber
                totalAmountExcludingTax
                status
                returnStatus
                invoiceStatus
                text {
                    value
                }
                lines {
                    query(first: 10, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                purchaseOrderLine__receivedQuantity: purchaseOrderLine {
                                    receivedQuantity
                                }
                                purchaseOrderLine {
                                    purchaseUnit__id: unit {
                                        decimalDigits
                                        id
                                    }
                                    purchaseReceiptLine__document__number: purchaseReceiptLine {
                                        document {
                                            number
                                        }
                                        completed
                                    }
                                    purchaseOrderLine__document__number: purchaseOrderLine {
                                        document {
                                            number
                                        }
                                        document__number: document {
                                            number
                                            status
                                        }
                                        quantityToReceive
                                        quantity
                                        receivedQuantity
                                        status
                                    }
                                    receivedQuantity
                                }
                                currency__id: currency {
                                    name
                                    id
                                    decimalDigits
                                    rounding
                                    symbol
                                }
                                stockUnit__id: stockUnit {
                                    id
                                    name
                                    decimalDigits
                                }
                                purchaseUnit__id: unit {
                                    id
                                    name
                                    decimalDigits
                                }
                                item__name: item {
                                    stockUnit__id: stockUnit {
                                        name
                                        id
                                    }
                                    name
                                    id
                                    description
                                    lotManagement
                                }
                                status
                                lineReturnStatus
                                lineInvoiceStatus
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                origin
                                grossPrice
                                discount
                                charge
                                netPrice
                                amountExcludingTax
                                priceOrigin
                                storedAttributes
                                storedDimensions
                                completed
                            }
                        }
                    }
                }
                carrier {
                    businessEntity {
                        name
                        id
                    }
                }
            }
        }
    }
}
