{"Create purchase order - fails (no lines) ": {"executionMode": "normal", "input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "orderDate": "2020-08-10", "fxRateDate": "2020-08-10"}, "arrayProperties": {}}, "output": {"diagnoses": [{"message": "The document needs at least one line.", "path": [], "severity": 3}]}}, "Create purchase order - fails (no time unit allowed, no existing item-site, no conversion factor PU to SU, no price, no receiving site ) ": {"executionMode": "normal", "input": {"properties": {"site": "#US002", "businessRelation": "#LECLERC", "currency": "#EUR", "orderDate": "2020-08-10", "fxRateDate": "2020-08-10", "lines": [{"item": "#Service001", "quantity": 10, "unit": "#LITER"}]}}, "output": {"diagnoses": [{"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "item"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "unitToStockUnitConversionFactor"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "quantityInStockUnit"], "severity": 3}, {"message": "The item: Service001, is not managed for the site: US002.", "path": ["lines", "-1000000002"], "severity": 3}, {"message": "The item: Service001, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000002"], "severity": 2}]}}, "Create purchase order - fails (expected receipt date before order date) ": {"executionMode": "normal", "input": {"properties": {"site": "#US002", "businessRelation": "#LECLERC", "currency": "#EUR", "orderDate": "2020-08-10", "fxRateDate": "2020-08-10", "lines": [{"item": "#Service001", "quantity": 10, "unit": "#LITER", "expectedReceiptDate": "2020-08-09"}]}}, "output": {"diagnoses": [{"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "item"], "severity": 3}, {"message": "The expected receipt date must be later than the order date.", "path": ["lines", "-1000000002", "expectedReceiptDate"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "unitToStockUnitConversionFactor"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "quantityInStockUnit"], "severity": 3}, {"message": "The item: Service001, is not managed for the site: US002.", "path": ["lines", "-1000000002"], "severity": 3}, {"message": "The item: Service001, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000002"], "severity": 2}]}}, "Create purchase order - fails (The order date cannot be later than today.) ": {"executionMode": "normal", "input": {"properties": {"number": "POTEST", "site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "orderDate": "3020-08-10", "lines": [{"item": "#Chemical C", "quantity": 10, "unit": "#KILOGRAM", "grossPrice": 100, "stockSite": "#US001"}]}}, "output": {"diagnoses": [{"message": "The order date cannot be later than today.", "path": ["orderDate"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000002"], "severity": 2}]}}, "Create purchase order -  externalNote provided without isExternalNote  ": {"executionMode": "normal", "input": {"properties": {"number": "POTEST", "site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "fxRateDate": "2020-08-10", "externalNote": {"value": " must throw "}, "lines": [{"item": "#ChairLeg", "quantity": 10, "unit": "#EACH", "grossPrice": 100, "stockSite": "#US001", "externalNote": {"value": " must throw "}}]}}, "output": {"diagnoses": [{"message": "The external note must be empty if the 'isExternalNote' property is false.", "path": ["lines", "-1000000002", "externalNote"], "severity": 3}, {"message": "The external note must be empty if the 'isExternalNote' property is false.", "path": ["externalNote"], "severity": 3}]}}, "Create purchase order with data from stock transfer order": {"executionMode": "normal", "input": {"properties": {"number": "test", "site": "#CAS01", "businessRelation": "#CAS02", "lines": [{"item": "#Milk", "quantity": "10"}]}}, "output": {"diagnoses": [{"severity": 3, "path": ["businessRelation"], "message": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value."}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Milk, is not managed for the supplier: CAS02."}]}}}