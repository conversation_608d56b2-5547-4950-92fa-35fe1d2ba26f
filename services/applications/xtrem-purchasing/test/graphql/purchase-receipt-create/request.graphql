mutation {
    xtremPurchasing {
        purchaseReceipt {
            create(
                data: {
                    site: "#US001"
                    businessRelation: "#LECLERC"
                    currency: "#EUR"
                    date: "2020-08-10"
                    carrier: "#700"
                    lines: [
                        {
                            stockDetailStatus: "notRequired"
                            item: "#Chemical C"
                            quantity: 10
                            unit: "#KILOGRAM"
                            grossPrice: 10
                            discount: 10
                            jsonStockDetails: "[{\"item\": \"#Chemical C\", \"site\": 3, \"location\": 1, \"stockUnit\": \"#GRAM\", \"status\": \"#A\", \"quantityInStockUnit\": 10000}]"
                        }
                    ]
                }
            ) {
                number
                status
                returnStatus
                invoiceStatus
                date
                paymentTerm {
                    name
                }
                text {
                    value
                }
                totalAmountExcludingTax
                supplier {
                    businessEntity {
                        id
                    }
                }
                carrier {
                    businessEntity {
                        id
                    }
                }
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                site {
                    id
                }
                financialSite {
                    legalCompany {
                        currency {
                            id
                        }
                    }
                }
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                returnAddress {
                    name
                }
                lines {
                    query {
                        edges {
                            node {
                                stockDetailStatus
                                status
                                lineInvoiceStatus
                                lineReturnStatus
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                netPrice
                                orderCost
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                workInProgress {
                                    documentType
                                    status
                                    startDate
                                    endDate
                                    expectedQuantity
                                    actualQuantity
                                    outstandingQuantity
                                }
                                taxDate
                            }
                        }
                    }
                }
            }
        }
    }
}
