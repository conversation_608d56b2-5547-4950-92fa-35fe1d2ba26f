{"data": {"xtremPurchasing": {"purchaseCreditMemo": {"query": {"edges": [{"node": {"number": "PCM1", "site": {"name": "Chem. Atlanta"}, "creditMemoDate": "2020-08-18", "billBySupplier": {"businessEntity": {"name": "ZA 700"}}, "billByLinkedAddress": {"name": "Main address", "addressLine1": "10 One street"}, "billByAddress": {"name": "Fittings", "addressLine1": "16 Marble"}, "billByContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "payToSupplier": {"businessEntity": {"name": "ZA 700"}}, "payToLinkedAddress": {"name": "Main address", "addressLine1": "10 One street"}, "payToAddress": {"name": "Fittings", "addressLine1": "234 Marble"}, "payToContact": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "paymentTerm": {"name": "Net 30"}, "supplierDocumentNumber": "Sage-CM658", "reason": {"name": "Reason 1"}, "currency": {"name": "Euro"}, "transactionCurrency": {"name": "Euro"}, "companyCurrency": {"name": "US Dollar"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "totalAmountExcludingTax": "1234.56", "calculatedTotalAmountExcludingTaxInCompanyCurrency": "24048", "totalTaxAmount": "45.32", "totalAmountIncludingTax": "1279.88", "calculatedTotalAmountIncludingTaxInCompanyCurrency": "24048", "calculatedTotalAmountExcludingTax": "24000", "calculatedTotalAmountIncludingTax": "24000", "calculatedTotalTaxAmount": "0", "calculatedTotalTaxAmountAdjusted": "0", "calculatedTotalTaxableAmount": "0", "calculatedTotalExemptAmount": "24000", "taxCalculationStatus": "notDone", "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "10", "taxAmount": "10", "exemptAmount": "5", "taxableAmount": "100", "taxAmountAdjusted": "10"}}]}}, "lines": {"query": {"edges": [{"node": {"item": {"id": "SalesItem81"}, "quantity": "1200", "unit": {"id": "GRAM"}, "grossPrice": "20", "quantityInStockUnit": "1200", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "24000", "amountExcludingTaxInCompanyCurrency": "24048", "text": {"value": "testtest"}, "providerLinkedAddress": {"region": "Gauteng", "country": {"id": "ZA"}}, "consumptionLinkedAddress": {"region": "MD", "country": {"id": "US"}}, "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "0", "taxAmount": "0", "exemptAmount": "24000", "taxableAmount": "0", "taxAmountAdjusted": "0"}}]}}}}]}}}}]}}}}}