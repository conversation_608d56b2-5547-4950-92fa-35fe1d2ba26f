{
    xtremPurchasing {
        purchaseCreditMemo {
            query(filter: "{number: 'PCM1'}") {
                edges {
                    node {
                        number
                        site {
                            name
                        }
                        creditMemoDate
                        billBySupplier {
                            businessEntity {
                                name
                            }
                        }
                        billByLinkedAddress {
                            name
                            addressLine1
                        }
                        billByAddress {
                            name
                            addressLine1
                        }
                        billByContact {
                            firstName
                            lastName
                        }
                        payToSupplier {
                            businessEntity {
                                name
                            }
                        }
                        payToLinkedAddress {
                            name
                            addressLine1
                        }
                        payToAddress {
                            name
                            addressLine1
                        }
                        payToContact {
                            firstName
                            lastName
                        }
                        paymentTerm {
                            name
                        }
                        supplierDocumentNumber
                        reason {
                            name
                        }
                        currency {
                            name
                        }
                        transactionCurrency {
                            name
                        }
                        companyCurrency {
                            name
                        }
                        companyFxRate
                        companyFxRateDivisor
                        totalAmountExcludingTax
                        calculatedTotalAmountExcludingTaxInCompanyCurrency
                        totalTaxAmount
                        totalAmountIncludingTax
                        calculatedTotalAmountIncludingTaxInCompanyCurrency
                        calculatedTotalAmountExcludingTax
                        calculatedTotalAmountIncludingTax
                        calculatedTotalTaxAmount
                        calculatedTotalTaxAmountAdjusted
                        calculatedTotalTaxableAmount
                        calculatedTotalExemptAmount
                        taxCalculationStatus
                        taxes {
                            query {
                                edges {
                                    node {
                                        currency {
                                            id
                                        }
                                        taxCategory
                                        tax
                                        nonTaxableAmount
                                        taxRate
                                        taxAmount
                                        exemptAmount
                                        taxableAmount
                                        taxAmountAdjusted
                                    }
                                }
                            }
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        item {
                                            id
                                        }
                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        stockUnit {
                                            id
                                        }
                                        amountExcludingTax
                                        amountExcludingTaxInCompanyCurrency
                                        text {
                                            value
                                        }
                                        providerLinkedAddress {
                                            region
                                            country {
                                                id
                                            }
                                        }
                                        consumptionLinkedAddress {
                                            region
                                            country {
                                                id
                                            }
                                        }
                                        taxes {
                                            query {
                                                edges {
                                                    node {
                                                        currency {
                                                            id
                                                        }
                                                        taxCategory
                                                        tax
                                                        nonTaxableAmount
                                                        taxRate
                                                        taxAmount
                                                        exemptAmount
                                                        taxableAmount
                                                        taxAmountAdjusted
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
