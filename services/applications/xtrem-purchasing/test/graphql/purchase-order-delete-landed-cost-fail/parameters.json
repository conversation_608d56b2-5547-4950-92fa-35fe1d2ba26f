{"Delete purchase order which has an invoice allocating landed costs - fail ": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"_id": "#PO32"}, "output": {"errors": [{"message": "The record was not deleted.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseOrder", "deleteById"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "55"], "message": "You cannot delete this order line, as landed cost allocations exist on it. Check the following associated invoices:\n\nPI17"}]}}]}}}