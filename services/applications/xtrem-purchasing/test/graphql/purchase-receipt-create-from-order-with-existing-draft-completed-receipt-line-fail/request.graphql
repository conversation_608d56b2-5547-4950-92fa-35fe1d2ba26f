mutation {
    xtremPurchasing {
        purchaseReceipt {
            create(
                data: {
                    carrier: null
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            charge: 0
                            completed: true
                            discount: 0
                            grossPrice: 10
                            item: "#Muesli"
                            status: "draft"
                            netPrice: 10
                            priceOrigin: null
                            purchaseOrderLine: { purchaseOrderLine: "#PO23|3800" }
                            unit: "#KILOGRAM"
                            unitToStockUnitConversionFactor: 1000
                            quantity: 5
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    text: null
                    currency: "#EUR"
                    businessRelation: "#LECLERC"
                    site: "#US001"
                    number: null
                    date: "2020-08-10"
                    supplierDocumentNumber: null
                    status: "draft"
                    returnStatus: "notReturned"
                    invoiceStatus: "notInvoiced"
                }
            ) {
                _id
            }
        }
    }
}
