{"Update purchase requisition - test updatedValue on status/approvalStatus ": {"input": {"properties": {"_id": "#REQ1", "lines": [{"_action": "update", "_sortValue": "10", "quantity": 12}]}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "draft", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "draft", "item": {"id": "Chemical C"}, "quantity": "12", "unit": {"id": "GRAM"}, "grossPrice": "0", "quantityInStockUnit": "12", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition - test updatedValue on status/approvalStatus 2 ": {"input": {"properties": {"_id": "#REQ1"}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "draft", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "draft", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "GRAM"}, "grossPrice": "0", "quantityInStockUnit": "10", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ1 - updating header approvalStatus to pendingApproval ": {"input": {"properties": {"_id": "#REQ1", "approvalStatus": "pendingApproval", "lines": [{"_action": "update", "_sortValue": "10", "quantity": 100}]}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "pendingApproval", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "pendingApproval", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "0", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ16 (pending approval) - updating to all changeRequested ": {"input": {"properties": {"_id": "#REQ16", "approvalStatus": "changeRequested", "lines": [{"_action": "update", "_sortValue": "10"}, {"_action": "update", "_sortValue": "20"}]}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "changeRequested", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "changeRequested", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "changeRequested", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ16 (pending approval) - updating to initial csv state ": {"input": {"properties": {"_id": "#REQ16", "approvalStatus": "pendingApproval", "status": "draft", "orderStatus": "notOrdered", "lines": [{"_action": "update", "_sortValue": "10", "status": "draft", "lineOrderStatus": "notOrdered"}, {"_action": "update", "_sortValue": "20", "status": "draft", "lineOrderStatus": "notOrdered"}]}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "pendingApproval", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "pendingApproval", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "pendingApproval", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ16 (pending approval) - updating to all rejected ": {"input": {"properties": {"_id": "#REQ16", "approvalStatus": "rejected", "lines": [{"_action": "update", "_sortValue": "10"}, {"_action": "update", "_sortValue": "20"}]}}, "output": {"update": {"status": "closed", "orderStatus": "notOrdered", "approvalStatus": "rejected", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineOrderStatus": "notOrdered", "approvalStatus": "rejected", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "closed", "lineOrderStatus": "notOrdered", "approvalStatus": "rejected", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ16 (pending approval) - updating to initial csv state bis": {"input": {"properties": {"_id": "#REQ16", "approvalStatus": "pendingApproval", "status": "draft", "orderStatus": "notOrdered", "lines": [{"_action": "update", "_sortValue": "10", "status": "draft", "lineOrderStatus": "notOrdered"}, {"_action": "update", "_sortValue": "20", "status": "draft", "lineOrderStatus": "notOrdered"}]}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "pendingApproval", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "pendingApproval", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "pendingApproval", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ16 (pending approval) - updating to all approved ": {"input": {"properties": {"_id": "#REQ16", "approvalStatus": "approved", "lines": [{"_action": "update", "_sortValue": "10"}, {"_action": "update", "_sortValue": "20"}]}}, "output": {"update": {"status": "pending", "orderStatus": "notOrdered", "approvalStatus": "approved", "lines": {"query": {"edges": [{"node": {"status": "pending", "lineOrderStatus": "notOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "pending", "lineOrderStatus": "notOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ17 (pending) - updating to all ordered || partiallyOrdered ": {"input": {"properties": {"_id": "#REQ17", "lines": [{"_action": "update", "_sortValue": "10", "lineOrderStatus": "ordered"}, {"_action": "update", "_sortValue": "20", "lineOrderStatus": "partiallyOrdered"}]}}, "output": {"update": {"status": "inProgress", "orderStatus": "partiallyOrdered", "approvalStatus": "approved", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineOrderStatus": "ordered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "inProgress", "lineOrderStatus": "partiallyOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ17 (approved) - updating to all closed || pending ": {"input": {"properties": {"_id": "#REQ17", "lines": [{"_action": "update", "_sortValue": "10", "status": "closed"}, {"_action": "update", "_sortValue": "20", "status": "pending"}]}}, "output": {"update": {"status": "pending", "orderStatus": "notOrdered", "approvalStatus": "approved", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineOrderStatus": "notOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "pending", "lineOrderStatus": "notOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ17 (approved) - updating to all closed || ordered ": {"input": {"properties": {"_id": "#REQ17", "lines": [{"_action": "update", "_sortValue": "10", "status": "closed"}, {"_action": "update", "_sortValue": "20", "lineOrderStatus": "ordered"}]}}, "output": {"update": {"status": "closed", "orderStatus": "partiallyOrdered", "approvalStatus": "approved", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineOrderStatus": "notOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "closed", "lineOrderStatus": "ordered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "15", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "15000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ16 (pending approval) - update line approvalStatus to changeRequested ": {"executionMode": "skip", "comment": "This test is not valid anymore, as the approvalStatus must be deleted from the lines.", "input": {"properties": {"_id": "#REQ16", "lines": [{"_action": "update", "_sortValue": "10", "approvalStatus": "changeRequested"}]}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "changeRequested", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "changeRequested", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "0", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ05 (pending) - update the supplier (one of the allowed properties to update on the pending state)": {"input": {"properties": {"_id": "#REQ5", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"update": {"status": "draft", "orderStatus": "notOrdered", "approvalStatus": "pendingApproval", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineOrderStatus": "notOrdered", "approvalStatus": "pendingApproval", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "GRAM"}, "grossPrice": "0", "quantityInStockUnit": "10", "stockUnit": {"id": "GRAM"}}}]}}}}}, "Update purchase requisition REQ24 (approved) - update the Required date (one of the allowed properties to update on the approval status)": {"input": {"properties": {"_id": "#REQ24", "lines": [{"_action": "update", "_sortValue": "10", "needByDate": "2023-12-06"}, {"_action": "update", "_sortValue": "20", "needByDate": "2023-12-06"}]}}, "output": {"update": {"status": "pending", "orderStatus": "notOrdered", "approvalStatus": "approved", "lines": {"query": {"edges": [{"node": {"status": "pending", "lineOrderStatus": "notOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "5", "unit": {"id": "KILOGRAM"}, "grossPrice": "50", "quantityInStockUnit": "5000", "stockUnit": {"id": "GRAM"}}}, {"node": {"status": "pending", "lineOrderStatus": "notOrdered", "approvalStatus": "approved", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "500", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}}}]}}}}}}