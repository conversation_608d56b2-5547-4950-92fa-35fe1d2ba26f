mutation {
    xtremPurchasing {
        purchaseRequisition {
            update(data:{{inputParameters}})
             {
                status
                orderStatus
                approvalStatus
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineOrderStatus
                                approvalStatus
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
