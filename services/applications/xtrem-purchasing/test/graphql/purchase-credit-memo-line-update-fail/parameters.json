{"Update item on a posted credit memo line - fail ": {"input": {"properties": {"_id": "#PCM2", "creditMemoDate": "2021-11-03", "totalAmountExcludingTax": 10, "totalTaxAmount": 1, "text": {"value": "blabla"}, "lines": [{"_action": "update", "_sortValue": "270200", "item": "#SalesItem81", "unit": "#GRAM"}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "item"], "message": "PurchaseCreditMemoLine.item: cannot set value on frozen property"}]}}]}, "envConfigs": {"today": "2021-11-04"}}, "Update internalNote on a posted credit memo line - fail ": {"input": {"properties": {"_id": "#PCM4", "creditMemoDate": "2021-11-03", "totalAmountExcludingTax": 10, "totalTaxAmount": 1, "text": {"value": "blabla"}, "lines": [{"_action": "update", "_sortValue": "270500", "internalNote": {"value": "test "}}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "internalNote"], "message": "PurchaseCreditMemoLine.internalNote: cannot set value on frozen property"}]}}]}, "envConfigs": {"today": "2021-11-04"}}, "Update externalNote on a posted credit memo line - fail ": {"input": {"properties": {"_id": "#PCM4", "creditMemoDate": "2021-11-03", "totalAmountExcludingTax": 10, "totalTaxAmount": 1, "text": {"value": "blabla"}, "lines": [{"_action": "update", "_sortValue": "270500", "externalNote": {"value": "test "}}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "externalNote"], "message": "PurchaseCreditMemoLine.externalNote: cannot set value on frozen property"}]}}]}, "envConfigs": {"today": "2021-11-04"}}, "Update isExternalNote on a posted credit memo line - fail ": {"input": {"properties": {"_id": "#PCM4", "creditMemoDate": "2021-11-03", "totalAmountExcludingTax": 10, "totalTaxAmount": 1, "text": {"value": "blabla"}, "lines": [{"_action": "update", "_sortValue": "270500", "isExternalNote": true}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseCreditMemo", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "isExternalNote"], "message": "PurchaseCreditMemoLine.isExternalNote: cannot set value on frozen property"}]}}]}, "envConfigs": {"today": "2021-11-04"}}}