{"Update default buyer on a closed purchase order- fail ": {"input": {"properties": {"_id": "#PO10", "defaultBuyer": "#<EMAIL>"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseOrder", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["defaultBuyer"], "message": "PurchaseOrder.defaultBuyer: cannot set value on frozen property"}]}}], "data": {"xtremPurchasing": {"purchaseOrder": {"update": null}}}, "extensions": {"diagnoses": [{"severity": 4, "path": ["defaultBuyer"], "message": "PurchaseOrder.defaultBuyer: cannot set value on frozen property"}]}}}, "Update delivery mode on a closed purchase order- fail ": {"input": {"properties": {"_id": "#PO10", "deliveryMode": "#SEA"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseOrder", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["deliveryMode"], "message": "PurchaseOrder.deliveryMode: cannot set value on frozen property"}]}}], "data": {"xtremPurchasing": {"purchaseOrder": {"update": null}}}, "extensions": {"diagnoses": [{"severity": 4, "path": ["deliveryMode"], "message": "PurchaseOrder.deliveryMode: cannot set value on frozen property"}]}}}, "Update internalNote on a closed purchase order- fail ": {"input": {"properties": {"_id": "#PO10", "internalNote": {"value": "internal note on document"}}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseOrder", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["internalNote"], "message": "PurchaseOrder.internalNote: cannot set value on frozen property"}]}}], "data": {"xtremPurchasing": {"purchaseOrder": {"update": null}}}, "extensions": {"diagnoses": [{"severity": 4, "path": ["internalNote"], "message": "PurchaseOrder.internalNote: cannot set value on frozen property"}]}}}, "Update externalNote on a closed purchase order- fail ": {"input": {"properties": {"_id": "#PO10", "externalNote": {"value": "internal note on document"}}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseOrder", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["externalNote"], "message": "PurchaseOrder.externalNote: cannot set value on frozen property"}]}}], "data": {"xtremPurchasing": {"purchaseOrder": {"update": null}}}, "extensions": {"diagnoses": [{"severity": 4, "path": ["externalNote"], "message": "PurchaseOrder.externalNote: cannot set value on frozen property"}]}}}, "Update isExternalNote on a closed purchase order- fail ": {"input": {"properties": {"_id": "#PO10", "isExternalNote": true}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseOrder", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["isExternalNote"], "message": "PurchaseOrder.isExternalNote: cannot set value on frozen property"}]}}], "data": {"xtremPurchasing": {"purchaseOrder": {"update": null}}}, "extensions": {"diagnoses": [{"severity": 4, "path": ["isExternalNote"], "message": "PurchaseOrder.isExternalNote: cannot set value on frozen property"}]}}}, "Update to close order line without using dedicated mutation - fail ": {"input": {"properties": {"_id": "#PO32", "lines": [{"_sortValue": "10", "status": "closed"}, {"_sortValue": "20", "status": "pending"}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseOrder", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "55", "status"], "message": "It is forbidden to change the status of an order line to close it. Use the dedicated function."}]}}], "data": {"xtremPurchasing": {"purchaseOrder": {"update": null}}}, "extensions": {"diagnoses": [{"severity": 3, "path": ["lines", "55", "status"], "message": "An order line that is not received and with landed costs attached cannot be closed."}, {"severity": 2, "path": ["lines", "55"], "message": "The item: STOAVC, is not managed for the supplier: US017."}]}}, "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}}}