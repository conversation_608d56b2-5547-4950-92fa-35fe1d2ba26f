{"Update purchase requisition without lines - fail ": {"input": {"properties": {"_id": "#REQ7", "lines": []}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You cannot delete the purchase requisition line.", "path": [], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremPurchasing", "purchaseRequisition", "update"]}]}}, "Update purchase requisition approvalStatus on non draft document- fail ": {"input": {"properties": {"_id": "#REQ7", "approvalStatus": "rejected"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only change the approval status for Draft documents.", "path": ["approvalStatus"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremPurchasing", "purchaseRequisition", "update"]}]}}, "Update purchase requisition line approvalStatus on non draft document - fail ": {"input": {"properties": {"_id": "#REQ7", "approvalStatus": "changeRequested", "lines": [{"_action": "update", "_sortValue": "10"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only change the approval status for Draft documents.", "path": ["approvalStatus"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremPurchasing", "purchaseRequisition", "update"]}]}}, "Update purchase requisition REQ23 (in progress) - update the supplier (one of the not allowed properties to update on the in progress state)": {"input": {"properties": {"_id": "#REQ23", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseRequisition", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "supplier"], "message": "PurchaseRequisitionLine.supplier: cannot set value on frozen property"}]}}]}}, "Update purchase requisition REQ23 (approved) - update the supplier (one of the not allowed properties to update on the in progress state)": {"input": {"properties": {"_id": "#REQ23", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseRequisition", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "supplier"], "message": "PurchaseRequisitionLine.supplier: cannot set value on frozen property"}]}}]}}}