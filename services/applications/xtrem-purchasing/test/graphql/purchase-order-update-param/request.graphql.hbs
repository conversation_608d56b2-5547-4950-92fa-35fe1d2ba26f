mutation {
    xtremPurchasing {
        purchaseOrder {
            update(data:{{inputParameters}})
             {
                paymentTerm {
                name
                }
                status
                receiptStatus
                approvalStatus
                displayStatus
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineReceiptStatus
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                            }
                        }
                    }
                }
            }
        }
    }
}
