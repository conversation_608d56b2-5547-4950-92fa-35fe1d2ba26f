{"Update purchase order - test updatedValue on status/approvalStatus ": {"input": {"properties": {"_id": "#PO1", "lines": [{"_sortValue": "100", "quantity": 12}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "draft", "receiptStatus": "notReceived", "approvalStatus": "draft", "displayStatus": "draft", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "12", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "12000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "1200"}}]}}}}}, "Update purchase order - test updatedValue on status/approvalStatus 2 ": {"input": {"properties": {"_id": "#PO1", "paymentTerm": "#TEST_NET_30_SUPPLIER"}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "draft", "receiptStatus": "notReceived", "approvalStatus": "draft", "displayStatus": "draft", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "1000"}}]}}}}}, "Update purchase order PO11 - updating header approvalStatus to pendingApproval ": {"input": {"properties": {"_id": "#PO11", "approvalStatus": "pendingApproval", "lines": [{"_sortValue": "1900", "quantity": 100}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "draft", "receiptStatus": "notReceived", "approvalStatus": "pendingApproval", "displayStatus": "pendingApproval", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "100000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000"}}]}}}}}, "Update purchase order PO12 (pending approval) - updating to all pending approval ": {"input": {"properties": {"_id": "#PO12", "lines": [{"_sortValue": "2100", "quantity": "100"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "draft", "receiptStatus": "notReceived", "approvalStatus": "pendingApproval", "displayStatus": "pendingApproval", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "100000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000"}}]}}}}}, "Update purchase order PO12 (pending approval) - updating to all rejected ": {"executionMode": "skip", "input": {"properties": {"_id": "#PO12", "approvalStatus": "rejected"}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "closed", "receiptStatus": "notReceived", "approvalStatus": "rejected", "displayStatus": "rejected", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "150000"}}, {"node": {"status": "closed", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000"}}]}}}}}, "Update purchase order PO12 (pending approval) - updating to all approved ": {"executionMode": "skip", "input": {"properties": {"_id": "#PO12", "approvalStatus": "approved"}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "pending", "receiptStatus": "notReceived", "approvalStatus": "approved", "displayStatus": "approved", "lines": {"query": {"edges": [{"node": {"status": "pending", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "15000"}}, {"node": {"status": "pending", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000"}}]}}}}}, "Update purchase order PO12 (pending approval) - update line approvalStatus to changeRequested ": {"input": {"properties": {"_id": "#PO12", "approvalStatus": "changeRequested"}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "draft", "receiptStatus": "notReceived", "approvalStatus": "changeRequested", "displayStatus": "draft", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "15000"}}, {"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000"}}]}}}}}, "Update purchase order PO13 (pending) - update expected receipt date ": {"input": {"properties": {"_id": "#PO13", "lines": [{"_sortValue": "2400", "expectedReceiptDate": "2020-08-11"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "pending", "receiptStatus": "notReceived", "approvalStatus": "approved", "displayStatus": "approved", "lines": {"query": {"edges": [{"node": {"status": "pending", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000"}}]}}}}}}