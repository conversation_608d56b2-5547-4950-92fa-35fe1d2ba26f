{
    xtremPurchasing {
        purchaseOrder {
            query(filter: "{number: 'PO1'}") {
                edges {
                    node {
                        number
                        status
                        approvalStatus
                        displayStatus
                        orderDate
                        paymentTerm {
                            name
                        }
                        text {
                            value
                        }
                        changeRequestedDescription {
                            value
                        }
                        totalAmountExcludingTax
                        totalAmountExcludingTaxInCompanyCurrency
                        supplier {
                            businessEntity {
                                id
                            }
                        }
                        currency {
                            id
                        }
                        site {
                            id
                        }
                        stockSite {
                            id
                        }
                        currency {
                            id
                        }
                        transactionCurrency {
                            id
                        }
                        companyCurrency {
                            id
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        status
                                        item {
                                            id
                                        }
                                        stockSite {
                                            id
                                        }
                                        stockSiteAddress {
                                            name
                                            addressLine1
                                            addressLine2
                                        }
                                        stockSiteContact {
                                            title
                                            firstName
                                            lastName
                                        }
                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        stockUnit {
                                            id
                                        }
                                        changeRequestedDescription
                                        amountExcludingTax
                                        amountExcludingTaxInCompanyCurrency
                                        text {
                                            value
                                        }
                                        expectedReceiptDate
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
