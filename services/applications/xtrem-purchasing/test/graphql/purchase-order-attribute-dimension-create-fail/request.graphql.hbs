mutation purchaseOrder($storedAttributes: Json, $storedDimensions: Json) {
  xtremPurchasing {
    purchaseOrder {
      create(
        data: {
          site: "#US001"
          businessRelation: "#LECLERC"
          currency: "#EUR"
          orderDate: "2021-03-02"
          fxRateDate: "2021-03-02"
          lines: [
            {
              item: "#Chemical C"
              quantity: 10
              unit: "#KILOGRAM"
              grossPrice: 100
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
            }
          ]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
