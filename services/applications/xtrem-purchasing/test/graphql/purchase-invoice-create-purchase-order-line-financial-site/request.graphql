mutation {
    xtremPurchasing {
        purchaseInvoice {
            create(
                data: {
                    text: null
                    payToLinkedAddress: "#LECLERC|1200"
                    payToSupplier: "#LECLERC"
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            charge: 0
                            discount: 0
                            grossPrice: 5
                            item: "#SamA20"
                            netPrice: 5
                            priceOrigin: null
                            purchaseOrderLine: { purchaseOrderLine: "#PO28|3600" }
                            unit: "#EACH"
                            unitToStockUnitConversionFactor: 1
                            quantity: 10
                            stockUnit: "#EACH"
                            amountExcludingTax: 50
                            recipientSite: "#US010"
                        }
                    ]
                    pdfSupplierInvoice: null
                    paymentTerm: "#TEST_NET_30_SUPPLIER"
                    currency: "#EUR"
                    billBySupplier: "#LECLERC"
                    site: "#US020"
                    number: "PI5"
                    invoiceDate: "2020-11-05"
                    dueDate: "2020-12-05"
                    status: "draft"
                    supplierDocumentNumber: null
                    totalAmountExcludingTax: 50
                    totalTaxAmount: 0
                }
            ) {
                number
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                calculatedTotalAmountIncludingTaxInCompanyCurrency
            }
        }
    }
}
