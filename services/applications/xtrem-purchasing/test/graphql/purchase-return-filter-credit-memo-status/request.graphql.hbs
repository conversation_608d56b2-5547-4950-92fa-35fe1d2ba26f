{
  xtremPurchasing {
    purchaseReturn {
      query(filter: "{number: '{{returnId}}'}") {
        edges {
          node {
            number
            status
            returnRequestDate
            supplier {
              businessEntity {
                id
              }
            }
            currency {
              id
            }
            returnSite {
              id
            }
            currency {
              id
            }
            creditStatus
            lines {
              query {
                edges {
                  node {
                    lineCreditStatus
                    creditedQuantity
                    quantityInStockUnit
                    purchaseCreditMemoLines {
                      query {
                        edges {
                          node {
                            creditMemoQuantity
                          }
                        }
                      }
                    }
                    item {
                      id
                    }
                    quantity
                    unit {
                      id
                    }
                    grossPrice
                    amountExcludingTax
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
