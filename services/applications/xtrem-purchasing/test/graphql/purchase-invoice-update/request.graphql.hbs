mutation purchaseInvoice($quantity: Decimal, $price: Decimal) {
  xtremPurchasing {
    purchaseInvoice {
      update(
        data: {
          _id: "#PI11"
          site: "#US001"
          billBySupplier: "#LECLERC"
          invoiceDate: "2020-08-10"
          supplierDocumentDate: "2021-03-02"
          text: { value: "value" }
          lines: [
            {
              item: "#Chemical C"
              quantity: $quantity
              unit: "#KILOGRAM"
              grossPrice: $price
              purchaseReceiptLine: { purchaseReceiptLine: "#PR4|1" }
            }
          ]
        }
      ) {
        number
        invoiceDate
        supplierDocumentDate
        paymentTerm {
          name
        }
        totalAmountExcludingTax
        billBySupplier {
          businessEntity {
            id
          }
        }
        currency {
          id
        }
        site {
          id
        }
        currency {
          id
        }
        text {
          value
        }
        payToLinkedAddress {
          name
        }
        lines {
          query {
            edges {
              node {
                item {
                  id
                }
                quantity
                unit {
                  id
                }
                grossPrice
                quantityInStockUnit
                stockUnit {
                  id
                }
                amountExcludingTax
                text {
                  value
                }
                matchingStatus
                taxDate
              }
            }
          }
        }
      }
    }
  }
}
