{" Control that order line cannot be deleted ": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"purchaseOrderLine": "#PO32|10"}, "output": {"controlDelete": ["You cannot delete this order line, as landed cost allocations exist on it. Check the following associated invoices:\n\nPI17"]}}, " Control that the other order line can be deleted ": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"purchaseOrderLine": "#PO32|20"}, "output": {"controlDelete": []}}}