mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            update(
                data: {
                    _id: "#PCM2"
                    creditMemoDate: "2021-11-03"
                    supplierDocumentDate: "2022-01-10"
                    totalAmountExcludingTax: 10
                    totalTaxAmount: 1
                    text: { value: "blabla" }
                    lines: [
                        {
                            _action: "create"
                            item: "#LandedCost002"
                            grossPrice: 10
                            unit: "#KILOGRAM"
                            quantity: 1
                            site: "#US001"
                            stockUnit: "#KILOGRAM"
                        }
                        {
                            _action: "create"
                            item: "#LandedCost002"
                            grossPrice: 100
                            unit: "#KILOGRAM"
                            quantity: 1
                            site: "#US001"
                            stockUnit: "#KILOGRAM"
                            landedCost: {
                                allocationRule: "byVolume"
                                allocationRuleUnit: "#CUBIC_METER"
                                allocationMethod: "manual"
                            }
                        }
                    ]
                }
            ) {
                creditMemoDate
                supplierDocumentDate
                text {
                    value
                }
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                totalAmountExcludingTax
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                totalTaxAmount
                totalAmountIncludingTax
                calculatedTotalAmountIncludingTaxInCompanyCurrency
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                billByAddress {
                    name
                    addressLine1
                    addressLine2
                    postcode
                    city
                    region
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                priceOrigin
                                taxDate
                                landedCost {
                                    costAmountToAllocate
                                    costAmountToAllocateInCompanyCurrency
                                    allocationRule
                                    allocationRuleUnit {
                                        id
                                    }
                                    allocationMethod
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
