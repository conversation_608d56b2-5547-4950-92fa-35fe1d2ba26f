{"Approve purchase return - fail ": {"input": {"properties": {"_id": "#RET025", "lines": [{"_action": "update", "_sortValue": "1", "approvalStatus": "approved", "status": "inProgress"}], "approvalStatus": "pendingApproval", "status": "draft"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReturn", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": [], "message": "You cannot change the status of the purchase return to the status you selected."}]}}]}, "envConfigs": {"today": "2020-08-10"}}, "Reject purchase return - fail ": {"input": {"properties": {"_id": "#RET026", "lines": [{"_action": "update", "_sortValue": "1", "approvalStatus": "rejected", "status": "closed"}], "approvalStatus": "rejected", "status": "closed"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReturn", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": [], "message": "You cannot change the status of the purchase return to the status you selected."}]}}]}, "envConfigs": {"today": "2020-08-10"}}, "Submit purchase return approval - fail ": {"input": {"properties": {"_id": "#RET027", "lines": [{"_action": "update", "_sortValue": "1", "approvalStatus": "pendingApproval", "status": "pending"}], "approvalStatus": "pendingApproval", "status": "pending"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReturn", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": [], "message": "You cannot change the status of the purchase return to the status you selected."}]}}]}, "envConfigs": {"today": "2020-08-10"}}, "Submit revert purchase return draft - fail ": {"input": {"properties": {"_id": "#RET024", "status": "draft"}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReturn", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["status"], "message": "Cannot revert purchase return status back to draft."}]}}]}, "envConfigs": {"today": "2020-08-10"}}}