{"Create purchase receipt - attribute type inactive, attribute active ": {"variables": {"storedAttributes": "{\"employee\":\"AttEMPL\"}"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "storedAttributes"], "message": "The AttEMPL attribute or \"employee\" attribute type is inactive."}, {"message": "The record cannot be referenced because it is inactive.", "path": ["lines", "-1000000002", "analyticalData", "employee"], "severity": 3}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - attribute type inactive, attribute inactive": {"variables": {"storedAttributes": "{\"employee\":\"AttEMPL2\"}"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "storedAttributes"], "message": "The AttEMPL2 attribute or \"employee\" attribute type is inactive."}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - Dimension type inactive, dimension active ": {"variables": {"storedDimensions": "{\"dimensionType06\":\"DIMTYPE4VALUE2\"}"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "storedDimensions"], "message": "The DIMTYPE4VALUE2 dimension or \"dimensionType06\" dimension type is inactive."}, {"message": "The record cannot be referenced because it is inactive.", "path": ["lines", "-1000000002", "analyticalData", "dimension06"], "severity": 3}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - dimension type inactive, dimension inactive ": {"variables": {"storedDimensions": "{\"dimensionType06\":\"DIMTYPE4VALUE1\"}"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "storedDimensions"], "message": "The DIMTYPE4VALUE1 dimension or \"dimensionType06\" dimension type is inactive."}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - dimension type/value in storedAttribute  ": {"variables": {"storedAttributes": "{\"dimensionType06\":\"DIMTYPE4VALUE2\"}"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "storedAttributes"], "message": "Attribute {\"dimensionType06\":\"DIMTYPE4VALUE2\"} invalid"}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - invalid dimension": {"variables": {"storedDimensions": "{\"dimensionType20\":\"DIMTYPE9UE1\"}"}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["lines", "", "analyticalData"], "message": "Dimension: record not found: {\"dimensionType\":\"#dimensionType20\",\"id\":\"DIMTYPE9UE1\"}"}]}}]}, "envConfigs": {"today": "2021-03-02"}}}