{
    xtremPurchasing {
        purchaseInvoice {
            query(filter: "{number: 'PI2'}") {
                edges {
                    node {
                        site {
                            name
                        }
                        invoiceDate
                        billBySupplier {
                            businessEntity {
                                id
                            }
                        }
                        billByLinkedAddress {
                            name
                            addressLine1
                        }
                        billByAddress {
                            name
                            addressLine1
                        }
                        billByContact {
                            firstName
                            lastName
                        }
                        payToSupplier {
                            businessEntity {
                                id
                            }
                        }
                        payToLinkedAddress {
                            name
                            addressLine1
                        }
                        payToAddress {
                            name
                            addressLine1
                        }
                        payToContact {
                            firstName
                            lastName
                        }
                        paymentTerm {
                            name
                        }
                        supplierDocumentNumber
                        currency {
                            name
                        }
                        companyCurrency {
                            name
                        }
                        transactionCurrency {
                            name
                        }
                        calculatedTotalAmountIncludingTax
                        calculatedTotalAmountIncludingTaxInCompanyCurrency
                        calculatedTotalTaxAmount
                        calculatedTotalTaxAmountAdjusted
                        calculatedTotalTaxableAmount
                        calculatedTotalExemptAmount
                        taxCalculationStatus
                        taxes {
                            query {
                                edges {
                                    node {
                                        currency {
                                            id
                                        }
                                        taxCategory
                                        tax
                                        nonTaxableAmount
                                        taxRate
                                        taxAmount
                                        exemptAmount
                                        taxableAmount
                                        taxAmountAdjusted
                                    }
                                }
                            }
                        }

                        lines {
                            query {
                                edges {
                                    node {
                                        document {
                                            number
                                        }
                                        item {
                                            name
                                        }
                                        itemDescription
                                        quantity
                                        stockUnit {
                                            name
                                        }
                                        currency {
                                            name
                                        }
                                        text {
                                            value
                                        }
                                        unit {
                                            name
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        amountExcludingTax
                                        purchaseReceiptLine {
                                            invoicedQuantity
                                            invoicedQuantityInStockUnit
                                            purchaseReceiptLine {
                                                itemDescription
                                            }
                                            purchaseInvoiceLine {
                                                item {
                                                    name
                                                    description
                                                }
                                            }
                                        }
                                        providerLinkedAddress {
                                            region
                                            country {
                                                id
                                            }
                                        }
                                        consumptionLinkedAddress {
                                            region
                                            country {
                                                id
                                            }
                                        }
                                        taxes {
                                            query {
                                                edges {
                                                    node {
                                                        currency {
                                                            id
                                                        }
                                                        taxCategory
                                                        tax
                                                        nonTaxableAmount
                                                        taxRate
                                                        taxAmount
                                                        exemptAmount
                                                        taxableAmount
                                                        taxAmountAdjusted
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        totalAmountExcludingTax
                        calculatedTotalAmountExcludingTaxInCompanyCurrency
                    }
                }
            }
        }
    }
}
