{"data": {"xtremPurchasing": {"purchaseInvoice": {"query": {"edges": [{"node": {"site": {"name": "Chem. Atlanta"}, "invoiceDate": "2020-08-18", "billBySupplier": {"businessEntity": {"id": "700"}}, "billByLinkedAddress": {"name": "Main address", "addressLine1": "10 One street"}, "billByAddress": {"name": "Fittings", "addressLine1": "16 Marble"}, "billByContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "payToSupplier": {"businessEntity": {"id": "700"}}, "payToLinkedAddress": {"name": "Main address", "addressLine1": "10 One street"}, "payToAddress": {"name": "Fittings", "addressLine1": "234 Marble"}, "payToContact": {"firstName": "<PERSON>", "lastName": "<PERSON>"}, "paymentTerm": {"name": "Net 30"}, "supplierDocumentNumber": "Invoice-Sage2", "currency": {"name": "Euro"}, "companyCurrency": {"name": "US Dollar"}, "transactionCurrency": {"name": "Euro"}, "calculatedTotalAmountIncludingTax": "2464.75", "calculatedTotalAmountIncludingTaxInCompanyCurrency": "2469.68", "calculatedTotalTaxAmount": "406", "calculatedTotalTaxAmountAdjusted": "406", "calculatedTotalTaxableAmount": "2030", "calculatedTotalExemptAmount": "28.75", "taxCalculationStatus": "notDone", "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "10", "taxAmount": "10", "exemptAmount": "5", "taxableAmount": "100", "taxAmountAdjusted": "10"}}]}}, "lines": {"query": {"edges": [{"node": {"document": {"number": "PI2"}, "item": {"name": "Chemical description C"}, "itemDescription": "Item _id=5 invoice line", "quantity": "23", "stockUnit": {"name": "Gram"}, "currency": {"name": "Euro"}, "text": {"value": "texttext"}, "unit": {"name": "Kilogram"}, "grossPrice": "1.25", "quantityInStockUnit": "2300", "amountExcludingTax": "28.75", "purchaseReceiptLine": {"invoicedQuantity": "23", "invoicedQuantityInStockUnit": "2300", "purchaseReceiptLine": {"itemDescription": "item description"}, "purchaseInvoiceLine": {"item": {"name": "Chemical description C", "description": "Chemical contains klm"}}}, "providerLinkedAddress": {"region": "Gauteng", "country": {"id": "ZA"}}, "consumptionLinkedAddress": {"region": "MD", "country": {"id": "US"}}, "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "0", "taxAmount": "0", "exemptAmount": "28.75", "taxableAmount": "0", "taxAmountAdjusted": "0"}}]}}}}, {"node": {"document": {"number": "PI2"}, "item": {"name": "Chemical description C"}, "itemDescription": "Item _id=4 invoice line", "quantity": "60", "stockUnit": {"name": "Gram"}, "currency": {"name": "Euro"}, "text": {"value": "…"}, "unit": {"name": "Gram"}, "grossPrice": "20.2", "quantityInStockUnit": "600", "amountExcludingTax": "2020", "purchaseReceiptLine": {"invoicedQuantity": "60", "invoicedQuantityInStockUnit": "600", "purchaseReceiptLine": {"itemDescription": "item description"}, "purchaseInvoiceLine": {"item": {"name": "Chemical description C", "description": "Chemical contains klm"}}}, "providerLinkedAddress": {"region": "Gauteng", "country": {"id": "ZA"}}, "consumptionLinkedAddress": {"region": "MD", "country": {"id": "US"}}, "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "20", "taxAmount": "404", "exemptAmount": "0", "taxableAmount": "2020", "taxAmountAdjusted": "404"}}]}}}}, {"node": {"document": {"number": "PI2"}, "item": {"name": "Chemical description C"}, "itemDescription": "Item _id=2 invoice line", "quantity": "40", "stockUnit": {"name": "Gram"}, "currency": {"name": "Euro"}, "text": {"value": "!!!"}, "unit": {"name": "Gram"}, "grossPrice": "0.25", "quantityInStockUnit": "40", "amountExcludingTax": "10", "purchaseReceiptLine": {"invoicedQuantity": "40", "invoicedQuantityInStockUnit": "40", "purchaseReceiptLine": {"itemDescription": "item description"}, "purchaseInvoiceLine": {"item": {"name": "Chemical description C", "description": "Chemical contains klm"}}}, "providerLinkedAddress": {"region": "Gauteng", "country": {"id": "ZA"}}, "consumptionLinkedAddress": {"region": "MD", "country": {"id": "US"}}, "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "20", "taxAmount": "2", "exemptAmount": "0", "taxableAmount": "10", "taxAmountAdjusted": "2"}}]}}}}]}}, "totalAmountExcludingTax": "1234.56", "calculatedTotalAmountExcludingTaxInCompanyCurrency": "2062.87"}}]}}}}}