{"Read purchase order with landed costs. Get summary by landed cost types": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"number": "PO29"}, "output": {"query": {"edges": [{"node": {"number": "PO29", "jsonAggregateLandedCostTypes": "[{\"landedCostType\":\"customs\",\"actualCostAmountInCompanyCurrency\":\"1002\",\"actualAllocatedCostAmountInCompanyCurrency\":\"1002\"},{\"landedCostType\":\"freight\",\"actualCostAmountInCompanyCurrency\":\"5010\",\"actualAllocatedCostAmountInCompanyCurrency\":\"5010\"}]"}}]}}}}