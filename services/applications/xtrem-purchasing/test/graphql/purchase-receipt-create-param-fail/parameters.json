{"Create purchase receipt - fails (no lines) ": {"executionMode": "normal", "input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "date": "2020-08-10", "lines": []}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document needs at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremPurchasing", "purchaseReceipt", "create"]}]}}, "Create purchase receipt - fails (no time unit allowed, no existing item-site, no conversion factor PU to SU, no price) ": {"executionMode": "normal", "input": {"properties": {"site": "#US002", "businessRelation": "#LECLERC", "currency": "#EUR", "date": "2020-08-10", "lines": [{"item": "#Service001", "quantity": 10, "unit": "#LITER"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "item"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "unitToStockUnitConversionFactor"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "quantityInStockUnit"], "severity": 3}, {"message": "The item: Service001, is not managed for the site: US002.", "path": ["lines", "-1000000002"], "severity": 3}, {"message": "The item: Service001, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000002"], "severity": 2}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremPurchasing", "purchaseReceipt", "create"]}]}}, "Create purchase receipt - fails (The receipt date cannot be later than today.) ": {"executionMode": "normal", "input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "date": "3020-08-10", "lines": [{"item": "#Chemical C", "quantity": 10, "unit": "#KILOGRAM", "grossPrice": 100}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The receipt date cannot be later than today.", "path": ["date"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000002"], "severity": 2}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremPurchasing", "purchaseReceipt", "create"]}]}}}