{"Update purchase order approvalStatus on non draft document- fail ": {"input": {"properties": {"_id": "#PO3", "approvalStatus": "rejected"}, "arrayProperties": {}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You can only change the approval status for Draft documents.", "path": ["approvalStatus"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremPurchasing", "purchaseOrder", "update"]}]}}, "Update purchase order line expected receipt date when status is inProgress- fail ": {"input": {"properties": {"_id": "#PO8"}, "arrayProperties": {"lines": [{"_sortValue": "1400", "expectedReceiptDate": "2020-08-15"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "PurchaseOrderLine.expectedReceiptDate: cannot set value on frozen property", "path": ["lines", "", "expectedReceiptDate"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremPurchasing", "purchaseOrder", "update"]}]}}, "Update purchase order line quantity when status is pending- fail ": {"input": {"properties": {"_id": "#PO8"}, "arrayProperties": {"lines": [{"_sortValue": "1500", "quantity": "21"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "PurchaseOrderLine.quantity: cannot set value on frozen property", "path": ["lines", "", "quantity"], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremPurchasing", "purchaseOrder", "update"]}]}}}