{"request": {"method": "POST", "url": "https://api.sendgrid.com/v3/mail/send", "headers": {"Content-Type": "application/json"}, "data": {"personalizations": [{"to": [{"email": "<EMAIL>", "name": "Unit, test"}], "subject": "Purchase credit memo PCM7: Matching request"}], "from": {"name": "sender.name", "email": "<EMAIL>"}, "content": [{"type": "text/plain", "value": "\n\nPurchase credit memo notification\n\nHello,\n\nYou have a pending purchase credit memo to match:\n                                                    \n                                                        Purchase credit memo\n                                                        PCM7 <NAME_EMAIL>\n\nYou can find details for this credit memo by following this link :\n                                                    Click here\n\n"}, {"type": "text/html", "value": "<!DOCTYPE html><html><head><meta charset=\"utf-8\">\n        <style>\n            :root {\n                --background: #FFFFFF;\n                --black55: rgba(0, 0, 0, 0.55);\n                --black65: rgba(0, 0, 0, 0.65);\n                --black90: rgba(0, 0, 0, 0.90);\n                --error: #C7384F;\n                --gold: #FFB500;\n                --info: #0077C8;\n                --logo: #00DC00;\n                --slate: #003349;\n                --slate20: #335C6D;\n                --slate40: #668592;\n                --slate60: #99ADB6;\n                --slate80: #CCD6DB;\n                --slate90: #E5EAEC;\n                --slate95: #F2F5F6;\n                --success: #00B000;\n                --tableSeparator: #D9E0E4;\n                --textAndLabels: rgba(0, 0, 0, 0.85);\n                --themePrimary: #008A21;\n                --themePrimaryHover: #005C9A;\n                --warning: #E96400;\n            }\n\n            @media print { body { -webkit-print-color-adjust: exact; } }\n            body {\n                font-size: 12pt;\n                margin: 0;\n            }\n\n            h1,h2,h3,h4,th{\n                color: var(--themePrimary);\n            }\n\n            .xtrem-page-break {\n                page-break-after: always;\n            }\n\n                    \n        .ExternalClass {\n            width: 100%;\n        }\n\n        .ExternalClass,\n        .ExternalClass p,\n        .ExternalClass span,\n        .ExternalClass font,\n        .ExternalClass td,\n        .ExternalClass div {\n            line-height: 100%;\n        }\n\n        body {\n            background-image: none;\n            background-repeat: repeat;\n            background-position: top left;\n            background-attachment: scroll;\n            -ms-text-size-adjust: none;\n            -webkit-text-size-adjust: none;\n            -webkit-font-smoothing: antialiased;\n            margin-top: 0;\n            margin-bottom: 0;\n            margin-right: 0;\n            margin-left: 0;\n            padding-top: 0;\n            padding-bottom: 0;\n            padding-right: 0;\n            padding-left: 0;\n            color: rgb(0, 0, 0, .9);\n        }\n\n        a[href^=tel] {\n            color: #FFFFFF;\n            text-decoration: none;\n        }\n\n        img {\n            display: block;\n            border: none;\n            outline: none;\n            text-decoration: none;\n            -ms-interpolation-mode: bicubic;\n        }\n\n        table td,\n        table th {\n            border-collapse: collapse;\n        }\n\n        table {\n            border-collapse: collapse;\n            mso-table-lspace: 0pt;\n            mso-table-rspace: 0pt;\n            table-layout: fixed;\n        }\n\n        tr {\n            font-size: 0px;\n            line-height: 0px;\n            border-collapse: collapse;\n        }\n\n        table table {\n            table-layout: auto;\n        }\n\n        a {\n            color: #008200;\n        }\n\n        a:hover {\n            color: #009000 !important;\n        }\n\n        a img {\n            border: none;\n        }\n\n        @media print {\n            body {\n                -webkit-print-color-adjust: exact;\n            }\n        }\n\n        body,\n        table,\n        td,\n        a {\n            -webkit-text-size-adjust: 100%;\n            -ms-text-size-adjust: 100%;\n        }\n\n        img {\n            -ms-interpolation-mode: bicubic;\n        }\n\n        \n\n        img {\n            border: 0 !important;\n            line-height: 100%;\n            outline: none;\n            text-decoration: none;\n        }\n\n        table td {\n            border-collapse: collapse !important;\n        }\n\n        body {\n            height: 100% !important;\n            margin: 0 !important;\n            padding: 0 !important;\n            width: 100% !important;\n        }\n\n        \n\n        a[x-apple-data-detectors] {\n            color: inherit !important;\n            text-decoration: none !important;\n            font-size: inherit !important;\n            font-family: inherit !important;\n            font-weight: inherit !important;\n            line-height: inherit !important;\n        }\n\n        .content-calendar {\n            padding: 15px 0 30px 4%;\n\n        }\n\n        \n\n        body,\n        table,\n        td {\n            font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;\n            font-size: 16px;\n            font-weight: 300;\n            line-height: 1.5;\n            color: #191919\n        }\n\n\n        \n\n        .sage-button {\n            background-color: #009000;\n            display: inline-block;\n            cursor: pointer;\n            color: #ffffff;\n            text-decoration: none;\n            width: 232px;\n        }\n\n        .sage-button a {\n            color: #ffffff;\n        }\n\n        .sage-button:hover {\n            background-color: #008000;\n\n        }\n\n        .sage-button a:hover {\n            color: #ffffff !important;\n\n        }\n\n        .sage-button-white {\n            background-color: #ffffff;\n            display: inline-block;\n            cursor: pointer;\n            color: #191919;\n            text-decoration: none;\n            width: 232px;\n        }\n\n        .sage-button-white a {\n\n            color: #191919;\n\n        }\n\n        .sage-button-white:hover {\n            background-color: #eeeeee;\n\n        }\n\n        .sage-button-white a:hover {\n\n            color: #191919 !important;\n\n        }\n\n        .sage-trans-button {\n            background-color: transparent;\n            display: inline-block;\n            cursor: pointer;\n            color: #008200;\n            text-decoration: none;\n            border-color: #008200;\n            border-style: solid;\n            border-width: 1px;\n            width: 232px;\n\n        }\n\n        .sage-trans-button a {\n            color: #008200;\n        }\n\n        .sage-button:active,\n        .sage-button-white:active {\n            position: relative;\n            top: 1px;\n        }\n\n        .SBC-icons {\n\n            \n            height: 125px;\n            display: block !important;\n            padding: 0px !important;\n\n        }\n\n        a.SBC-icon-title:link {\n\n            text-align: center;\n            color: #004b87;\n            font-size: 20px;\n            font-weight: 300;\n\n        }\n\n        .SBC-icon-title {\n\n            text-align: center;\n            color: #004b87;\n            font-size: 20px;\n            font-weight: 300;\n\n        }\n\n        \n\n        @media screen and (max-width: 600px) {\n            h1 {\n                font-size: 32px;\n                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;\n                line-height: 1.25 !important;\n\n            }\n\n            h2 {\n                font-size: 24px;\n                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;\n                line-height: 1.25 !important;\n            }\n\n            h3 {\n                font-size: 16px;\n                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;\n            }\n\n            .img-max {\n                width: 100% !important;\n                max-width: 100% !important;\n                height: auto !important;\n                min-width: 100px !important;\n            }\n\n            .img-max-center {\n                width: 100% !important;\n                max-width: 100% !important;\n                height: auto !important;\n                min-width: 100px !important;\n                text-align: center !important;\n                float: none !important;\n                margin: 0 auto !important;\n                padding: 0 !important;\n            }\n\n            .img-max-pad-top {\n                width: 100% !important;\n                max-width: 100% !important;\n                height: auto !important;\n                min-width: 100px !important;\n                padding-top: 10px !important;\n            }\n\n            .container {\n                width: 100% !important;\n                max-width: 600px !important;\n                height: auto !important;\n                min-width: 0 !important;\n            }\n\n            .img-pad-top {\n                padding-top: 10px;\n            }\n\n            .pad-bottom {\n                padding-bottom: 20px;\n            }\n\n            .pad-bottom-center {\n                padding-bottom: 20px;\n                margin-left: auto !important;\n                margin-right: auto !important;\n            }\n\n            .extra-marg-bottom {\n                margin-bottom: 30px !important;\n            }\n\n            .no-pad-bottom {\n                padding-bottom: 0px !important;\n\n            }\n\n            .no-pad-bottom-center {\n                padding-bottom: 0px !important;\n                margin-left: auto !important;\n                margin-right: auto !important;\n                float: none !important;\n            }\n\n            .text-center-right {\n                text-align: center !important;\n                padding-right: 20px !important;\n                padding-left: 0px !important;\n            }\n\n            .text-center-right-pad {\n                text-align: center !important;\n                padding-left: 0px !important;\n            }\n\n            .center-table {\n                text-align: center !important;\n            }\n\n            .text-center {\n                text-align: center !important;\n            }\n\n            .text-center-75 {\n                text-align: center !important;\n                max-width: 75% !important;\n            }\n\n            .ul-style {\n                padding-left: 20px !important;\n            }\n\n            .max-width {\n                max-width: 100% !important;\n                width: 100% !important;\n\n            }\n\n            .max-width-remove-height {\n                max-width: 100% !important;\n                width: 100% !important;\n                ;\n                height: auto !important;\n            }\n\n            .max-width-center {\n                max-width: 100% !important;\n                width: 100% !important;\n\n                text-align: center !important;\n                margin: 0 !important;\n                padding: 0 !important;\n            }\n\n            .max-width-pad {\n                max-width: 100% !important;\n                width: 100% !important;\n                margin-bottom: 12px !important;\n            }\n\n            .max-width-top-pad {\n                max-width: 100% !important;\n                width: 100% !important;\n\n                margin-top: 20px !important;\n            }\n\n\n            .max-width-no-pad {\n                max-width: 100% !important;\n                width: 100% !important;\n\n            }\n\n            .max-width-no-pad-l {\n                max-width: 100% !important;\n                width: 100% !important;\n                padding-left: 0 !important;\n            }\n\n            .max-width-no-pad-a {\n                max-width: 100% !important;\n                width: 100% !important;\n                padding-left: 0 !important;\n                padding-right: 0 !important;\n                padding-bottom: 0 !important;\n                padding-top: 0 !important;\n            }\n\n            .max-width-center-pad {\n                max-width: 100% !important;\n                width: 100% !important;\n\n                text-align: center !important;\n                margin: 0 auto !important;\n                margin-bottom: 30px !important;\n            }\n\n            .mobile-wrapper {\n                width: 90% !important;\n                max-width: 90% !important;\n            }\n\n            .mobile-padding {\n                padding-left: 5% !important;\n                padding-right: 5% !important;\n            }\n\n            .fix-margin {\n                margin-top: 0px;\n                padding-top: 0px;\n            }\n\n            .mobile-center {\n                text-align: center !important;\n                padding-bottom: 5px !important;\n            }\n\n            .mobile-stacked {\n                padding-top: 12px !important;\n                padding-bottom: 12px !important;\n            }\n\n\n            .remove-height {\n                height: auto !important;\n            }\n\n            .remove-pad {\n                padding: 0px !important;\n            }\n\n            .content {\n                padding-left: 25px !important;\n                padding-right: 25px !important;\n            }\n\n            .sage_logo {\n                width: auto !important;\n                height: 32px !important;\n            }\n\n\n            .desktop-masthead {\n                width: 100% !important;\n                height: auto !important;\n            }\n\n            .img-center {\n                margin-left: auto !important;\n                margin-right: auto !important;\n            }\n\n            .two-col-icons {\n                width: 50% !important;\n                height: 50% !important;\n                display: block;\n            }\n\n            .quote-signature {\n                padding-left: 10px !important;\n            }\n\n            .SBC-icons {\n                width: 138px !important;\n                height: auto !important;\n            }\n\n            .content-SBC-icons {\n                padding-left: 40px !important;\n                padding-right: 40px !important;\n            }\n\n            .content-calendar {\n                padding: 15px 0 30px 14%;\n            }\n\n\n            /* Media query for mobile viewport\n             * Developer:  hero graphics should be 2 x width for HD rendering.\n             */\n            @media only screen and (max-width: 480px) {\n\n                h1 {\n\n                    font-size: 24px !important;\n                    font-weight: bold;\n                }\n\n                h2 {\n                    font-size: 20px !important;\n                    font-weight: bold;\n                }\n\n                h3 {\n                    font-size: 16px;\n                    font-weight: bold;\n                }\n\n                table[class=max-width-pad] {\n                    max-width: 100% !important;\n                    width: 100% !important;\n                    padding-top: 15px !important;\n                    height: auto !important;\n                }\n\n                table[class=max-width] {\n                    max-width: 100% !important;\n                    width: 100% !important;\n                    height: auto !important;\n                }\n\n                table[class=container] {\n                    margin: 0 auto !important;\n                }\n\n                .desktop-masthead {\n                    display: none !important;\n                }\n\n                *[class].hidden, *[class=desktop-masthead] {\n                    display: none !important;\n                }\n\n                *[class].elastic {\n                    width: 100% !important;\n                    height: auto !important;\n                }\n\n                *[class].centered {\n                    text-align: center !important;\n                }\n\n                *[class].fluid,\n                [class=fluid-mob] {\n                    width: 100% !important;\n                }\n\n                [class=fluid-mob] {\n                    position: relative;\n                }\n\n                .mobile-show {\n                    display: table-cell !important;\n                    width: auto !important;\n                    height: auto !important;\n                    max-height: none !important;\n                    overflow: visible !important;\n                    visibility: visible !important;\n                    position: relative !important;\n                    text-align: center !important;\n                }\n\n                .show-mob {\n                    display: block !important;\n                    max-height: none !important;\n                    width: auto !important;\n                    visibility: visible !important;\n                }\n\n                *[class].mob-masthead {\n                    width: 100% !important;\n                    display: block !important;\n                    height: auto !important;\n                    max-height: none !important;\n                    padding: 0 !important;\n                }\n\n                *[class].mob-masthead-img {\n                    position: absolute !important;\n                    top: 0px !important;\n                    display: block !important;\n                    height: auto !important;\n                    max-height: none !important;\n                    padding: 0 !important;\n                    width: auto !important;\n                }\n\n                [class=fluid-mob] {\n                    display: table-cell !important;\n                    width: auto !important;\n                    height: auto !important;\n                    max-height: none !important;\n                    overflow: visible !important;\n                    visibility: visible !important;\n                    position: relative !important;\n                    text-align: center !important;\n                }\n\n                .SBC-icons {\n                    margin: 2px;\n                    height: auto !important;\n                }\n\n                .content-quote {\n                    margin-top: 20px\n                }\n\n                .quote-signature {\n                    padding-left: 0px !important;\n                }\n\n            }\n\n            @media only screen and (max-width: 410px) {\n                .SBC-icons {\n\n                    height: auto !important;\n                    clear: none;\n\n                }\n\n                .content-SBC-icons {\n                    padding-left: 35px !important;\n                    padding-right: 15px !important;\n                    margin: 4px;\n\n                }\n\n                .content-calendar {\n                    padding: 15px 7% 30px;\n                }\n            }\n\n        </style>\n    </head>\n    <body class=\"ck ck-content\"><table class=\"ab_section\" data-description=\"Body\" border=\"0\" bgcolor=\"#F2F5F6\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"padding-top: 0;\n        padding-right: 0px;\n        padding-bottom: 0px;\n        padding-left: 0px;\n        mso-cellspacing: 0px;\n        mso-padding-alt: 0px 0px 0px 0px;\n        border-collapse: collapse;\n        mso-table-lspace: 0pt;\n        mso-table-rspace: 0pt;\n        background-color: #f2f5f6;\">\n    <tbody>\n        <tr>\n            <td align=\"center\" style=\"text-align: center\">\n                <table bgcolor=\"#ffffff\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" align=\"center\" width=\"600\" style=\"display: table;\n                        width: 600px;\n                        background-color: #ffffff;\n                        margin: 0 auto !important;\n                        padding: 0px;\n                        mso-table-lspace: 0pt;\n                        mso-table-rspace: 0pt;\n                        border-collapse: collapse;\n                        border-style: solid;\n                        border-width: 1px;\n                        border-color: #cccccc;\" class=\"container\">\n                    <tbody>\n                        <tr>\n                            <td align=\"center\" valign=\"top\" class=\"ab_cloneable\" data-description=\"B1 Hero banner image 1\" style=\"margin: 0;\n                                    padding-top: 0;\n                                    padding-right: 0px;\n                                    padding-bottom: 0px;\n                                    padding-left: 0px;\n                                    mso-cellspacing: 0px;\n                                    mso-padding-alt: 0px 0px 0px 0px;\n                                    border-collapse: collapse;\n                                    mso-table-lspace: 0pt;\n                                    mso-table-rspace: 0pt;\">\n                                <table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" class=\"fluid\" style=\"display: table;\n                                        padding-top: 0;\n                                        padding-right: 0px;\n                                        padding-bottom: 0px;\n                                        padding-left: 0px;\n                                        mso-cellspacing: 0px;\n                                        mso-padding-alt: 0px 0px 0px 0px;\n                                        border-collapse: collapse;\n                                        mso-table-lspace: 0pt;\n                                        mso-table-rspace: 0pt;\">\n                                    <tbody>\n                                        <tr>\n                                            <td style=\"margin: 0;\n                                                    padding-top: 0;\n                                                    padding-right: 0px;\n                                                    padding-bottom: 0px;\n                                                    padding-left: 0px;\n                                                    mso-cellspacing: 0px;\n                                                    mso-padding-alt: 0px 0px 0px 0px;\n                                                    border-collapse: collapse;\n                                                    mso-table-lspace: 0pt;\n                                                    mso-table-rspace: 0pt;\">\n                                                <div class=\"show-mob\" style=\"font-size: 0;\n                                                        max-height: 0;\n                                                        overflow: hidden;\n                                                        display: none;\n                                                        line-height: 0;\n                                                        visibility: hidden;\">\n                                                    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\">\n                                                        <tbody>\n                                                            <tr>\n                                                                <th width=\"600\" align=\"center\" valign=\"top\" bgcolor=\"#51534a\" class=\"fluid\" style=\"width: 600px;\n                                                                        padding-top: 0;\n                                                                        padding-right: 0px;\n                                                                        padding-bottom: 0px;\n                                                                        padding-left: 0px;\n                                                                        mso-cellspacing: 0px;\n                                                                        mso-padding-alt: 0px 0px 0px 0px;\n                                                                        border-collapse: collapse;\n                                                                        mso-table-lspace: 0pt;\n                                                                        mso-table-rspace: 0pt;\">\n                                                                    <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\" border=\"0\" bgcolor=\"#51534A\" class=\"fluid-mob\" style=\"mso-cellspacing: 0px;\n                                                                            mso-padding-alt: 0px 0px 0px 0px;\n                                                                            border-collapse: collapse;\n                                                                            mso-table-lspace: 0pt;\n                                                                            mso-table-rspace: 0pt;\n                                                                            font-size: 0;\n                                                                            max-height: 0;\n                                                                            overflow: hidden;\n                                                                            display: none;\n                                                                            line-height: 0;\n                                                                            visibility: hidden;\">\n                                                                        <tbody>\n                                                                            <tr>\n                                                                                <td align=\"left\" bgcolor=\"#51534A\" class=\"mobile-show\" style=\"font-size: 0;\n                                                                                        max-height: 0;\n                                                                                        overflow: hidden;\n                                                                                        display: none;\n                                                                                        line-height: 0;\n                                                                                        visibility: hidden;\">\n                                                                                    <table width=\"100%\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n                                                                                        <tbody>\n                                                                                            <tr>\n                                                                                                <td align=\"center\" style=\"background-color: #ffffff;\" class=\"mobile-show\">\n                                                                                                    <a class=\"ab_editable\">\n                                                                                                        <h1 style=\"margin-top: 20px;\n                                                                                                                margin-left: 10px;\">\n                                                                                                            Purchase credit memo notification\n                                                                                                        </h1>\n                                                                                                    </a>\n                                                                                                </td>\n                                                                                            </tr>\n                                                                                        </tbody>\n                                                                                    </table>\n                                                                                </td>\n                                                                            </tr>\n                                                                        </tbody>\n                                                                    </table>\n                                                                </th>\n                                                            </tr>\n                                                        </tbody>\n                                                    </table>\n                                                </div>\n                                            </td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </td>\n                        </tr>\n                        <tr class=\"ab_cloneable\" data-description=\"C1 Single column copy\">\n                            <td align=\"center\" valign=\"top\" style=\"background-color: #f2f5f6;\n                                    padding: 32px 32px 32px 32px;\n                                    mso-table-lspace: 0pt;\n                                    mso-table-rspace: 0pt;\n                                    border-collapse: collapse;\" class=\"content\">\n                                <table align=\"left\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"mso-cellspacing: 0px; mso-padding-alt: 0px; border-collapse: collapse\">\n                                    <tbody>\n                                        <tr>\n                                            <td valign=\"top\" align=\"left\" style=\"font-family:\n                                                        AdelleSansSAGE,\n                                                        Arial,\n                                                        Roboto,\n                                                        Segoe UI,\n                                                        Helvetica Neue;\n                                                    font-weight: 300;\n                                                    color: #191919;\n                                                    font-size: 16px;\n                                                    line-height: 1.5;\">\n                                                <span class=\"ab_editable\" data-rawhtml=\"true\">\n                                                    Hello,\n                                                    <br>\n                                                    <br>\n                                                    You have a pending purchase credit memo to match:\n                                                    <h3 style=\"font-size: 16px;\n                                                            font-weight: bold;\n                                                            margin: 12px 0;\n                                                            line-height: 1.25;\" class=\"ab_editable\">\n                                                        Purchase credit memo\n                                                        PCM7 <NAME_EMAIL>\n                                                    </h3>\n                                                    <br>\n                                                    You can find details for this credit memo by following this link :\n                                                    <a href=\"http://localhost:8240/@sage/xtrem-purchasing/PurchaseCreditMemo/eyJfaWQiOiIxNzMifQ==\" style=\"font-size: 16px;\n                                                            font-family:\n                                                                AdelleSansSAGE,\n                                                                Arial,\n                                                                Roboto,\n                                                                Segoe UI,\n                                                                Helvetica Neue;\n                                                            display: inline-block;\n                                                            font-weight: bold;\n                                                            text-decoration: underline;\n                                                            color: #008200;\" class=\"red ab_editable\"><span class=\"ab_editable\" data-rawhtml=\"true\">Click here</span></a>\n                                                    <br>\n                                                    <br>\n                                                </span>\n                                            </td>\n                                        </tr>\n                                    </tbody>\n                                </table>\n                            </td>\n                        </tr>\n                    </tbody>\n                </table>\n            </td>\n        </tr>\n    </tbody>\n</table>\n<br><br>\n\n    </body></html>"}], "mail_settings": {"bcc": {"enable": false}, "footer": {"enable": false}, "sandbox_mode": {"enable": false}, "check_spam": {"enable": false}}}}, "response": {"headers": {"isMock": true}, "data": ""}}