mutation purchaseOrder($storedAttributes: Json, $storedDimensions: Json) {
  xtremPurchasing {
    purchaseReturn {
      create(
        data: {
          returnSite: "#US001"
          businessRelation: "#LECLERC"
          currency: "#EUR"
          returnRequestDate: "2021-03-02"
          lines: [
            {
              item: "#Chemical C"
              quantity: 10
              unit: "#KILOGRAM"
              grossPrice: 100
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
              reason: "#R1"
              purchaseReceiptLine: {
                purchaseReceiptLine: 1004
                returnedQuantity: 10
                returnedQuantityInStockUnit: 10000
              }
            }
          ]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
