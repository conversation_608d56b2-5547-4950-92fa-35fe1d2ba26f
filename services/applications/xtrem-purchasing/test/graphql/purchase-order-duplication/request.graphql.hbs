mutation {
  xtremPurchasing {
    purchaseOrder {
      duplicate(_id: "{{_id}}") {
        number
        displayStatus
        internalNote {
          value
        }
        externalNote {
          value
        }
        isExternalNote
        isTransferHeaderNote
        lines {
          query {
            edges {
              node {
                status
                internalNote {
                  value
                }
                externalNote {
                  value
                }
                isExternalNote
              }
            }
          }
        }
      }
    }
  }
}
