mutation purchaseRequisition($storedAttributes: Json, $storedDimensions: Json) {
  xtremPurchasing {
    purchaseRequisition {
      update(
        data: {
          _id: "#REQ5"
          lines: [{ _sortValue: "10", storedAttributes: $storedAttributes, storedDimensions: $storedDimensions }]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
