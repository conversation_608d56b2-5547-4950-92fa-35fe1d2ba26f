mutation {
    xtremPurchasing {
        purchaseOrder {
            create(
                data: {
                    paymentTerm: "#TEST_NET_30_SUPPLIER"
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            changeRequestedDescription: null
                            charge: 0
                            discount: 0
                            expectedReceiptDate: "2022-03-21"
                            grossPrice: 0
                            item: "#5467"
                            itemDescription: null
                            lineInvoiceStatus: "notInvoiced"
                            lineReceiptStatus: "notReceived"
                            status: "draft"
                            netPrice: 0
                            priceOrigin: null
                            unit: "#GRAM"
                            unitToStockUnitConversionFactor: 1
                            quantity: 11
                            site: "#US001"
                            stockSite: "#US001"
                            stockSiteLinkedAddress: "#US001|7100"
                            stockSiteAddress: {
                                country: "#US"
                                name: "US001"
                                locationPhoneNumber: "+***********"
                                addressLine1: "First address line site"
                                addressLine2: "test"
                                city: "test"
                                region: "test"
                                postcode: "20746"
                            }
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    changeRequestedDescription: null
                    text: null
                    currency: "#USD"
                    supplierAddress: {
                        country: "#FR"
                        name: "960 street 44"
                        locationPhoneNumber: "+***********"
                        addressLine1: "960 street 44"
                        addressLine2: "test"
                        city: "test"
                        region: "test"
                        postcode: "20746"
                    }
                    supplierLinkedAddress: "#US017|1300"
                    siteAddress: {
                        country: "#US"
                        name: "US001"
                        locationPhoneNumber: "+***********"
                        addressLine1: "First address line site"
                        addressLine2: "test"
                        city: "test"
                        region: "test"
                        postcode: "20746"
                    }
                    businessEntityAddress: "#US001|7100"
                    businessRelation: "#US017"
                    stockSite: "#US001"
                    site: "#US001"
                    number: null
                    orderDate: "2022-03-11"
                    fxRateDate: "2022-03-11"
                    status: "draft"
                    approvalStatus: "draft"
                    receiptStatus: "notReceived"
                    invoiceStatus: "notInvoiced"
                }
            ) {
                number
                site {
                    businessEntity {
                        id
                    }
                    purchaseOrderSubstituteApprover {
                        email
                        firstName
                        lastName
                        displayName
                    }
                    purchaseOrderDefaultApprover {
                        email
                        firstName
                        lastName
                        displayName
                    }
                    legalCompany {
                        currency {
                            name
                            id
                            symbol
                            decimalDigits
                        }
                        name
                        id
                    }
                    name
                    id
                    isPurchase
                    isInventory
                }
                stockSite {
                    businessEntity {
                        id
                    }
                    name
                    id
                    isPurchase
                    isInventory
                }
                supplier {
                    businessEntity {
                        currency {
                            name
                            id
                            decimalDigits
                            symbol
                        }
                        name
                        id
                    }
                }
                siteAddress {
                    country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                businessEntityAddress {
                    country {
                        id
                        name
                        regionLabel
                        zipLabel
                    }
                    isActive
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                supplierLinkedAddress {
                    name
                    businessEntity {
                        id
                    }
                    country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    isActive
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                supplierAddress {
                    country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                orderDate
                currency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                companyCurrency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                fxRateDate
                companyFxRate
                rateDescription
                totalAmountExcludingTax
                totalAmountExcludingTaxInCompanyCurrency
                text {
                    value
                }
                status
                approvalStatus
                receiptStatus
                invoiceStatus
                changeRequestedDescription {
                    value
                }
                lines {
                    query(first: 10, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                stockSiteAddress {
                                    country {
                                        name
                                        id
                                        regionLabel
                                        zipLabel
                                    }
                                    name
                                    locationPhoneNumber
                                    addressLine1
                                    addressLine2
                                    city
                                    region
                                    postcode
                                    concatenatedAddress
                                }
                                stockSiteLinkedAddress {
                                    businessEntity {
                                        id
                                    }
                                    country {
                                        name
                                        id
                                        regionLabel
                                        zipLabel
                                    }
                                    isActive
                                    name
                                    addressLine1
                                    addressLine2
                                    city
                                    region
                                    postcode
                                    locationPhoneNumber
                                    concatenatedAddress
                                }
                                currency {
                                    name
                                    id
                                    decimalDigits
                                    rounding
                                    symbol
                                }
                                stockUnit {
                                    name
                                    decimalDigits
                                    id
                                }
                                unit {
                                    id
                                    name
                                    decimalDigits
                                }
                                stockSite {
                                    businessEntity {
                                        id
                                    }
                                    name
                                    id
                                    isPurchase
                                    isInventory
                                }
                                site {
                                    legalCompany {
                                        name
                                        id
                                    }
                                    name
                                    id
                                }
                                item {
                                    purchaseUnit {
                                        id
                                    }
                                    stockUnit {
                                        id
                                    }
                                    image {
                                        value
                                    }
                                    name
                                    id
                                    description
                                }
                                status
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                grossPrice
                                discount
                                charge
                                netPrice
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                remainingAmountToReceiveExcludingTax
                                remainingAmountToReceiveExcludingTaxInCompanyCurrency
                                priceOrigin
                                expectedReceiptDate
                                changeRequestedDescription
                                lineReceiptStatus
                                lineInvoiceStatus
                                origin
                                storedAttributes
                                storedDimensions
                                receivedQuantity
                                quantityToReceive
                                quantityToReceiveInStockUnit
                            }
                        }
                    }
                }
                paymentTerm {
                    name
                    description
                }
            }
        }
    }
}
