{"data": {"xtremPurchasing": {"purchaseOrder": {"create": {"number": "PO220002", "site": {"businessEntity": {"id": "US001"}, "purchaseOrderSubstituteApprover": null, "purchaseOrderDefaultApprover": null, "legalCompany": {"currency": {"name": "US Dollar", "id": "USD", "symbol": "$", "decimalDigits": 2}, "name": "US Process Manufacturing 001", "id": "US001"}, "name": "Chem. Atlanta", "id": "US001", "isPurchase": true, "isInventory": true}, "stockSite": {"businessEntity": {"id": "US001"}, "name": "Chem. Atlanta", "id": "US001", "isPurchase": true, "isInventory": true}, "supplier": {"businessEntity": {"currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "symbol": "€"}, "name": "Siège social S01 PARIS", "id": "US017"}}, "siteAddress": {"country": {"name": "United States of America", "id": "US", "regionLabel": "state", "zipLabel": "zipCode"}, "name": "US001", "concatenatedAddress": "US001\nFirst address line site\ntest\ntest\ntest\n20746\nUnited States of America", "locationPhoneNumber": "+***********", "addressLine1": "First address line site", "addressLine2": "test", "city": "test", "region": "test", "postcode": "20746"}, "businessEntityAddress": {"country": {"id": "US", "name": "United States of America", "regionLabel": "state", "zipLabel": "zipCode"}, "isActive": true, "name": "US001", "concatenatedAddress": "US001\nFirst address line site\nMD\n20746\nUnited States of America", "locationPhoneNumber": "+***********", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746"}, "supplierLinkedAddress": {"name": "960 street 44", "businessEntity": {"id": "US017"}, "country": {"name": "France", "id": "FR", "regionLabel": "department", "zipLabel": "postalCode"}, "isActive": true, "addressLine1": "960 street 44", "addressLine2": "", "city": "", "region": "First region", "postcode": "20746", "locationPhoneNumber": "+***********"}, "supplierAddress": {"country": {"name": "France", "id": "FR", "regionLabel": "department", "zipLabel": "postalCode"}, "name": "960 street 44", "concatenatedAddress": "960 street 44\n960 street 44\ntest\ntest\ntest\n20746\nFrance", "locationPhoneNumber": "+***********", "addressLine1": "960 street 44", "addressLine2": "test", "city": "test", "region": "test", "postcode": "20746"}, "orderDate": "2022-03-11", "currency": {"name": "US Dollar", "id": "USD", "decimalDigits": 2, "rounding": 0, "symbol": "$"}, "companyCurrency": {"name": "US Dollar", "id": "USD", "decimalDigits": 2, "rounding": 0, "symbol": "$"}, "fxRateDate": "2022-03-11", "companyFxRate": "1", "rateDescription": "1 USD = 1 USD", "totalAmountExcludingTax": "0", "totalAmountExcludingTaxInCompanyCurrency": "0", "text": {"value": ""}, "status": "draft", "approvalStatus": "draft", "receiptStatus": "notReceived", "invoiceStatus": "notInvoiced", "changeRequestedDescription": {"value": ""}, "lines": {"query": {"edges": [{"node": {"stockSiteAddress": {"country": {"name": "United States of America", "id": "US", "regionLabel": "state", "zipLabel": "zipCode"}, "name": "US001", "locationPhoneNumber": "+***********", "addressLine1": "First address line site", "addressLine2": "test", "city": "test", "region": "test", "postcode": "20746", "concatenatedAddress": "US001\nFirst address line site\ntest\ntest\ntest\n20746\nUnited States of America"}, "stockSiteLinkedAddress": {"businessEntity": {"id": "US001"}, "country": {"name": "United States of America", "id": "US", "regionLabel": "state", "zipLabel": "zipCode"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********", "concatenatedAddress": "US001\nFirst address line site\nMD\n20746\nUnited States of America"}, "currency": {"name": "US Dollar", "id": "USD", "decimalDigits": 2, "rounding": 0, "symbol": "$"}, "stockUnit": {"name": "Gram", "decimalDigits": 2, "id": "GRAM"}, "unit": {"id": "GRAM", "name": "Gram", "decimalDigits": 2}, "stockSite": {"businessEntity": {"id": "US001"}, "name": "Chem. Atlanta", "id": "US001", "isPurchase": true, "isInventory": true}, "site": {"legalCompany": {"name": "US Process Manufacturing 001", "id": "US001"}, "name": "Chem. Atlanta", "id": "US001"}, "item": {"purchaseUnit": {"id": "LITER"}, "stockUnit": {"id": "LITER"}, "image": null, "name": "Glycerin", "id": "5467", "description": "Glycerin"}, "status": "draft", "itemDescription": "", "quantity": "11", "quantityInStockUnit": "11", "unitToStockUnitConversionFactor": "1", "grossPrice": "0", "discount": "0", "charge": "0", "netPrice": "0", "amountExcludingTax": "0", "amountExcludingTaxInCompanyCurrency": "0", "remainingAmountToReceiveExcludingTax": "0", "remainingAmountToReceiveExcludingTaxInCompanyCurrency": "0", "priceOrigin": null, "expectedReceiptDate": "2022-03-21", "changeRequestedDescription": "", "lineReceiptStatus": "notReceived", "lineInvoiceStatus": "notInvoiced", "origin": "direct", "storedAttributes": null, "storedDimensions": null, "receivedQuantity": "0", "quantityToReceive": "11", "quantityToReceiveInStockUnit": "11"}}]}}, "paymentTerm": {"name": "Net 30", "description": ""}}}}}}