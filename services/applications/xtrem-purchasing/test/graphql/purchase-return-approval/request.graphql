mutation {
    xtremPurchasing {
        purchaseReturn {
            update(
                data: {
                    _id: "#RET024"
                    lines: [
                        {
                            _action: "update"
                            _sortValue: "1"
                            approvalStatus: "approved"
                            item: "#Aqua"
                            status: "inProgress"
                            grossPrice: 10
                            priceOrigin: null
                            purchaseReceiptLine: { returnedQuantity: "15" }
                            quantity: 15
                            reason: "#R1"
                            shippedStatus: "notShipped"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    text: { value: "" }
                    businessRelation: "#US017"
                    returnSite: "#US005"
                    number: "RET024"
                    returnRequestDate: "2020-08-10"
                    returnItems: true
                    supplierReturnReference: null
                    approvalStatus: "approved"
                    status: "pending"
                    shippingStatus: "notShipped"
                }
            ) {
                status
                approvalStatus
                shippingStatus
                lines {
                    query {
                        edges {
                            node {
                                _id
                                origin
                                status
                                approvalStatus
                                shippedStatus
                                quantity
                                grossPrice
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                amountExcludingTax
                                priceOrigin
                                storedAttributes
                                storedDimensions
                            }
                        }
                    }
                }
            }
        }
    }
}
