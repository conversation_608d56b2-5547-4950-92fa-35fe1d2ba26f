{"The return request date cannot be later than today.": {"executionMode": "normal", "input": {"properties": {"returnSite": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "returnRequestDate": "2023-02-01", "lines": [{"item": "#Chemical C", "quantity": 150, "unit": "#KILOGRAM", "grossPrice": 100, "reason": "#R1", "purchaseReceiptLine": {"purchaseReceiptLine": 1004, "returnedQuantity": 10, "returnedQuantityInStockUnit": 10000}}, {"item": "#Chemical C", "quantity": 100, "unit": "#GRAM", "grossPrice": 100, "reason": "#R1", "purchaseReceiptLine": {"purchaseReceiptLine": 1006, "returnedQuantity": 10, "returnedQuantityInStockUnit": 10}}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReturn", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["returnRequestDate"], "message": "The return request date cannot be later than today."}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}, {"severity": 2, "path": ["lines", "-1000000008"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}]}}], "extensions": {"diagnoses": [{"severity": 3, "path": ["returnRequestDate"], "message": "The order date cannot be later than today."}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}, {"severity": 2, "path": ["lines", "-1000000008"], "message": "The item: Chemical C, is not managed for the supplier: LECLERC."}]}}, "envConfigs": {"today": "2023-01-31"}}, "Create purchase return with data from stock transfer order": {"executionMode": "normal", "input": {"properties": {"number": "test", "returnSite": "#CAS01", "businessRelation": "#CAS02", "lines": [{"item": "#Milk", "quantity": "10", "purchaseReceiptLine": {"purchaseReceiptLine": 1004, "returnedQuantity": 10, "returnedQuantityInStockUnit": 10000}}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseReturn", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["businessRelation"], "message": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value."}, {"severity": 3, "path": ["lines", "-1000000002", "purchaseReceiptLine", "returnedQuantityInStockUnit"], "message": "The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit."}, {"severity": 3, "path": ["lines", "-1000000002", "purchaseReceiptLine"], "message": "The purchase return must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase receipt."}, {"severity": 4, "path": ["lines", "-1000000002", "reason"], "message": "PurchaseReturnLine.reason: property is required"}]}}], "extensions": {"diagnoses": [{"severity": 3, "path": ["businessRelation"], "message": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value."}, {"severity": 3, "path": ["lines", "-1000000002", "purchaseReceiptLine", "returnedQuantityInStockUnit"], "message": "The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit."}, {"severity": 3, "path": ["lines", "-1000000002", "purchaseReceiptLine"], "message": "The purchase return must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase receipt."}, {"severity": 4, "path": ["lines", "-1000000002", "reason"], "message": "PurchaseReturnLine.reason: property is required"}]}}}}