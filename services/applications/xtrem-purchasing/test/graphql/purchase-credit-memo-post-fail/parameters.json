{"Post purchase credit memo fail when status is not pending": {"input": {"creditMemoId": "#PCM4"}, "output": {"errors": [{"message": "Post failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseCreditMemo", "post"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The status is not Draft, the credit memo cannot be posted.", "path": [], "severity": 4}]}}], "data": {"xtremPurchasing": {"purchaseCreditMemo": {"post": null}}}}}, "Post purchase credit memo fail when totals doesnt match": {"input": {"creditMemoId": "#PCM3"}, "output": {"errors": [{"message": "Post failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseCreditMemo", "post"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The user entered total tax excluded amount is not equal to the sum of the lines.", "path": [], "severity": 4}]}}], "data": {"xtremPurchasing": {"purchaseCreditMemo": {"post": null}}}}}}