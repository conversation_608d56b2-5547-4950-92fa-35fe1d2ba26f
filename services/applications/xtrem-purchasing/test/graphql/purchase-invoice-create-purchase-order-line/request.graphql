mutation {
    xtremPurchasing {
        purchaseInvoice {
            create(
                data: {
                    site: "#US001"
                    billBySupplier: "#LECLERC"
                    currency: "#USD"
                    invoiceDate: "2020-08-10"
                    text: { value: "value" }
                    matchingStatus: "variance"
                    totalAmountExcludingTax: 30
                    totalTaxAmount: 12
                    lines: [
                        {
                            item: "#Chair"
                            quantity: 1
                            unit: "#BOX"
                            grossPrice: 100
                            purchaseOrderLine: { purchaseOrderLine: "#PO10|1700" }
                            unitToStockUnitConversionFactor: 12
                        }
                    ]
                    matchingUser: "#<EMAIL>"
                }
            ) {
                number
                matchingStatus
                dueDate
                totalAmountIncludingTax
                calculatedTotalAmountExcludingTax
                matchingUser {
                    firstName
                }
                returnLinkedAddress {
                    name
                }
                invoiceDate
                paymentTerm {
                    name
                }
                totalAmountExcludingTax
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                billByLinkedAddress {
                    name
                    addressLine1
                }
                billByAddress {
                    name
                    addressLine1
                }
                billByContact {
                    firstName
                    lastName
                }
                payToSupplier {
                    businessEntity {
                        name
                    }
                }

                payToLinkedAddress {
                    name
                    addressLine1
                    addressLine2
                }
                payToAddress {
                    name
                    addressLine1
                    addressLine2
                }
                payToContact {
                    firstName
                    lastName
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                site {
                    id
                }
                currency {
                    id
                }
                text {
                    value
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
