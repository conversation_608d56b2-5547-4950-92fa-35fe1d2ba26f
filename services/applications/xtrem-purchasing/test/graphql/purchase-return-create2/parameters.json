{"Purchase return - create 2": {"executionMode": "normal", "input": {"properties": {"returnSite": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "returnRequestDate": "2020-08-10", "lines": [{"item": "#Chemical C", "quantity": 150, "unit": "#KILOGRAM", "grossPrice": 100, "reason": "#R1", "purchaseReceiptLine": {"purchaseReceiptLine": 1004, "returnedQuantity": 10, "returnedQuantityInStockUnit": 10000}}, {"item": "#Chemical C", "quantity": 100, "unit": "#GRAM", "grossPrice": 100, "reason": "#R1", "purchaseReceiptLine": {"purchaseReceiptLine": 1006, "returnedQuantity": 10, "returnedQuantityInStockUnit": 10}}]}}, "output": {"number": "PT200001", "status": "draft", "invoiceStatus": "notInvoiced", "returnRequestDate": "2020-08-10", "returnToAddress": {"name": "Store"}, "supplierAddress": {"name": "Store"}, "totalAmountExcludingTax": "25000", "totalAmountExcludingTaxInCompanyCurrency": "25050", "supplier": {"businessEntity": {"id": "LECLERC"}}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "returnSite": {"id": "US001"}, "lines": {"query": {"edges": [{"node": {"status": "draft", "lineInvoiceStatus": "notInvoiced", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "reason": {"id": "R1"}, "grossPrice": "100", "amountExcludingTax": "15000", "amountExcludingTaxInCompanyCurrency": "15030"}}, {"node": {"status": "draft", "lineInvoiceStatus": "notInvoiced", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "reason": {"id": "R1"}, "grossPrice": "100", "amountExcludingTax": "10000", "amountExcludingTaxInCompanyCurrency": "10020"}}]}}}, "envConfigs": {"today": "2020-08-10"}}}