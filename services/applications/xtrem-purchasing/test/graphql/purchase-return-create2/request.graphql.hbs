mutation {
    xtremPurchasing {
        purchaseReturn {
            create(data: {{inputParameters}}) {
                number
                status
                invoiceStatus
                returnRequestDate
                returnToAddress {
                    name
                }
                supplierAddress {
                    name
                }
                totalAmountExcludingTax
                totalAmountExcludingTaxInCompanyCurrency
                supplier {
                    businessEntity {
                        id
                    }
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                returnSite {
                    id
                }
                currency {
                    id
                }
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineInvoiceStatus
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                reason {
                                    id
                                }
                                grossPrice
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                            }
                        }
                    }
                }
            }
        }
    }
}
