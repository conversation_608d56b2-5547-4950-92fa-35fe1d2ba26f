{"Create purchase return - attributes project": {"executionMode": "normal", "variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}"}, "output": {"create": {"number": "PT220001", "currency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"Chemical C\",\"businessSite\":\"US001\",\"supplier\":\"LECLERC\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}}}, "envConfigs": {"today": "2022-04-13"}}, "Create purchase return - attributes and dimensions": {"variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}, "output": {"create": {"number": "PT220001", "currency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"Chemical C\",\"businessSite\":\"US001\",\"supplier\":\"LECLERC\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}}]}}}}, "envConfigs": {"today": "2022-04-13"}}}