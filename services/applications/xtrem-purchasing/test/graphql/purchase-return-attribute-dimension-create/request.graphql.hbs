mutation purchaseOrder($storedAttributes: Json, $storedDimensions: Json) {
  xtremPurchasing {
    purchaseReturn {
      create(
        data: {
          returnSite: "#US001"
          businessRelation: "#LECLERC"
          currency: "#EUR"
          returnRequestDate: "2022-04-13"
          lines: [
            {
              item: "#Chemical C"
              quantity: 10
              unit: "#KILOGRAM"
              grossPrice: 100
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
              reason: "#R1"
              purchaseReceiptLine: {
                purchaseReceiptLine: 1004
                returnedQuantity: 10
                returnedQuantityInStockUnit: 10000
              }
            }
          ]
        }
      ) {
        number
        currency {
          id
        }
        companyCurrency {
          id
        }
        transactionCurrency {
          id
        }
        companyFxRate
        companyFxRateDivisor
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
