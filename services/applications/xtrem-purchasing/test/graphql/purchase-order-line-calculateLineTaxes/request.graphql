mutation {
    xtremPurchasing {
        purchaseOrderLine {
            calculateLineTaxes(
                data: {
                    site: "#ETS1-S01"
                    businessPartner: "#500"
                    item: "#SalesItem81"
                    currency: "#EUR"
                    amountExcludingTax: 10
                    quantity: 10
                    taxes: [{ taxReference: "#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT" }]
                }
            ) {
                taxAmount
                taxAmountAdjusted
                amountIncludingTax
                taxes {
                    taxCategoryReference {
                        name
                    }
                    taxCategory
                    taxRate
                    taxAmount
                    isTaxMandatory
                    isSubjectToGlTaxExcludedAmount
                    nonTaxableAmount
                    exemptAmount
                    taxableAmount
                    currency {
                        name
                    }
                    deductibleTaxRate
                    deductibleTaxAmount
                    isReverseCharge
                    taxAmountAdjusted
                    taxReference {
                        name
                    }
                    tax
                }
            }
        }
    }
}
