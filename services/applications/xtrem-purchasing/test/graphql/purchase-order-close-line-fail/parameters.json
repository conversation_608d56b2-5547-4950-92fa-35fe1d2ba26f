{" Close line fails : PO1 (having line status='draft')": {"input": {"purchaseOrderLine": "#PO1|100"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval order line.", "path": ["lines", "1", "status"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "1"], "severity": 2}]}, "locations": [{"column": 7, "line": 4}], "message": "Close line failed.", "path": ["xtremPurchasing", "purchaseOrder", "closeLine"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Close line fails : PO2 (having approvalStatus='pendingApproval')": {"input": {"purchaseOrderLine": "#PO2|200"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval order line.", "path": ["lines", "2", "status"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "2"], "severity": 2}]}, "locations": [{"column": 7, "line": 4}], "message": "Close line failed.", "path": ["xtremPurchasing", "purchaseOrder", "closeLine"]}]}, "envConfigs": {"today": "2020-08-10"}}}