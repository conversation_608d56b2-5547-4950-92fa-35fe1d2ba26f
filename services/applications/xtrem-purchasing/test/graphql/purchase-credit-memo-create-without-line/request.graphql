mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            create(
                data: {
                    site: "#US001"
                    billBySupplier: "#LECLERC"
                    totalAmountExcludingTax: 10
                    totalTaxAmount: 1
                    reason: "#R1"
                    text: { value: "test" }
                    supplierDocumentNumber: "LECLERC845"
                }
            ) {
                number
                calculatedTotalAmountExcludingTax
                site {
                    id
                }
                status
                creditMemoDate
                dueDate
                supplierDocumentNumber
                text {
                    value
                }
                reason {
                    id
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                paymentTerm {
                    name
                }
                totalAmountExcludingTax
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                totalTaxAmount
                totalAmountIncludingTax
                calculatedTotalAmountIncludingTaxInCompanyCurrency
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                payToSupplier {
                    businessEntity {
                        id
                    }
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
