{" Approve purchase requisition": {"input": {"document": "#REQ1", "isSafeToRetry": false}, "output": {"data": {"xtremPurchasing": {"purchaseRequisition": {"confirm": null}}}, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Document status needs to be \"Draft\" and approval workflow disabled.", "path": [], "severity": 4}]}, "locations": [{"column": 7, "line": 4}], "message": "Confirm failed.", "path": ["xtremPurchasing", "purchaseRequisition", "confirm"]}]}}, "Approve purchase requisition - isSafeToRetryTrue": {"input": {"document": "#REQ1", "isSafeToRetry": true}, "output": {"data": {"xtremPurchasing": {"purchaseRequisition": {"confirm": {"approvalStatus": "draft"}}}}}}}