{
    xtremPurchasing {
        purchaseReceipt {
            query(filter: "{number: 'PR2'}") {
                edges {
                    node {
                        number
                        status
                        displayStatus
                        invoiceStatus
                        returnStatus
                        date
                        paymentTerm {
                            name
                        }
                        text {
                            value
                        }
                        totalAmountExcludingTax
                        totalAmountExcludingTaxInCompanyCurrency
                        supplier {
                            businessEntity {
                                id
                            }
                        }
                        currency {
                            id
                        }
                        site {
                            id
                        }
                        currency {
                            id
                        }
                        companyCurrency {
                            id
                        }
                        transactionCurrency {
                            id
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        status
                                        lineInvoiceStatus
                                        lineReturnStatus
                                        item {
                                            id
                                        }
                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        stockUnit {
                                            id
                                        }
                                        amountExcludingTax
                                        amountExcludingTaxInCompanyCurrency
                                        text {
                                            value
                                        }
                                        numberOfPurchaseInvoiceLines
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
