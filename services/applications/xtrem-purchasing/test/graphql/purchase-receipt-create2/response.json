{"data": {"xtremPurchasing": {"purchaseReceipt": {"create": {"number": "PR200001", "status": "draft", "displayStatus": "draft", "invoiceStatus": "notInvoiced", "returnStatus": "notReturned", "date": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "text": {"value": ""}, "totalAmountExcludingTax": "10015", "totalAmountExcludingTaxInCompanyCurrency": "10035.03", "supplier": {"businessEntity": {"id": "LECLERC"}}, "currency": {"id": "EUR"}, "site": {"id": "US001"}, "transactionCurrency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "<PERSON><PERSON><PERSON>"}, "quantity": "0.15", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "15", "amountExcludingTaxInCompanyCurrency": "15.03", "text": {"value": ""}}}, {"node": {"status": "draft", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "<PERSON><PERSON><PERSON>"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000", "amountExcludingTaxInCompanyCurrency": "10020", "text": {"value": ""}}}]}}}}}}}