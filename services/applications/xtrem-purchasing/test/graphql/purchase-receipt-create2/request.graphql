mutation {
    xtremPurchasing {
        purchaseReceipt {
            create(
                data: {
                    site: "#US001"
                    businessRelation: "#LECLERC"
                    currency: "#EUR"
                    date: "2020-08-10"
                    lines: [
                        {
                            item: "#Muesli"
                            quantity: 0.15
                            unit: "#KILOGRAM"
                            grossPrice: 100
                            jsonStockDetails: "[{\"item\": \"#Muesli\", \"site\": 3, \"location\": 1, \"stockUnit\": \"#GRAM\", \"status\": \"#A\", \"quantityInStockUnit\": 0.15}]"
                        }
                        {
                            item: "#Muesli"
                            quantity: 100
                            unit: "#GRAM"
                            grossPrice: 100
                            jsonStockDetails: "[{\"item\": \"#Muesli\", \"site\": 3, \"location\": 1, \"stockUnit\": \"#GRAM\", \"status\": \"#A\", \"quantityInStockUnit\": 100}]"
                        }
                    ]
                }
            ) {
                number
                status
                displayStatus
                invoiceStatus
                returnStatus
                date
                paymentTerm {
                    name
                }
                text {
                    value
                }
                totalAmountExcludingTax
                totalAmountExcludingTaxInCompanyCurrency
                supplier {
                    businessEntity {
                        id
                    }
                }
                currency {
                    id
                }

                site {
                    id
                }
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineInvoiceStatus
                                lineReturnStatus
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
