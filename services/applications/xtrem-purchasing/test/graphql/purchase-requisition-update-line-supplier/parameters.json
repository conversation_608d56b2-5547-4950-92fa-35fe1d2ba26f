{"Update REQ1 - add the supplier on the first line": {"executionMode": "normal", "input": {"properties": {"_id": "#REQ1", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"update": {"status": "draft"}}}, "Update REQ10  add the supplier on the first line": {"executionMode": "normal", "input": {"properties": {"_id": "#REQ10", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"update": {"status": "pending"}}}, "Update REQ16 - add the supplier on the first line": {"executionMode": "normal", "input": {"properties": {"_id": "#REQ16", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"update": {"status": "draft"}}}, "Update REQ17  add the supplier on the first line": {"input": {"properties": {"_id": "#REQ17", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"update": {"status": "pending"}}}}