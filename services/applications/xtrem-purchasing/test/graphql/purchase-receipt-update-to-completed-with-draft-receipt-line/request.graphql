mutation {
    xtremPurchasing {
        purchaseReceipt {
            update(data: { _id: "#PR32", lines: [{ _action: "update", _sortValue: "1", completed: true }] }) {
                supplierAddress {
                    country {
                        name
                        id
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                receivingAddress {
                    country {
                        name
                        id
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                returnAddress {
                    country {
                        name
                        id
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                number
                site {
                    primaryAddress {
                        country {
                            id
                        }
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                    name
                    id
                    isLocationManaged
                }
                supplier {
                    businessEntity {
                        name
                        currency {
                            name
                            id
                            decimalDigits
                            symbol
                        }
                    }
                    primaryAddress {
                        country {
                            id
                        }
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                }
                currency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                date
                supplierDocumentNumber
                totalAmountExcludingTax
                status
                returnStatus
                invoiceStatus
                text {
                    value
                }
                lines {
                    query(first: 10, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                purchaseOrderLine {
                                    receivedQuantity
                                }
                                purchaseOrderLine {
                                    unit {
                                        decimalDigits
                                        id
                                    }
                                    purchaseReceiptLine {
                                        document {
                                            number
                                        }
                                        completed
                                    }
                                    purchaseOrderLine {
                                        document {
                                            number
                                        }
                                        document {
                                            number
                                            status
                                        }
                                        quantityToReceive
                                        quantity
                                        receivedQuantity
                                        status
                                    }
                                    receivedQuantity
                                }
                                currency {
                                    name
                                    id
                                    decimalDigits
                                    rounding
                                    symbol
                                }
                                stockUnit {
                                    id
                                    name
                                    decimalDigits
                                }
                                unit {
                                    id
                                    name
                                    decimalDigits
                                }
                                item {
                                    stockUnit {
                                        name
                                        id
                                    }
                                    name
                                    id
                                    description
                                    lotManagement
                                }
                                status
                                lineReturnStatus
                                lineInvoiceStatus
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                origin
                                grossPrice
                                discount
                                charge
                                netPrice
                                amountExcludingTax
                                priceOrigin
                                jsonStockDetails
                                storedAttributes
                                storedDimensions
                                completed
                            }
                        }
                    }
                }
                carrier {
                    businessEntity {
                        name
                    }
                }
            }
        }
    }
}
