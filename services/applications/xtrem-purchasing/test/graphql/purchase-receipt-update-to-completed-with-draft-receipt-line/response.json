{"data": {"xtremPurchasing": {"purchaseReceipt": {"update": {"supplierAddress": {"country": {"id": "FR", "name": "France"}, "name": "Store", "concatenatedAddress": "Store\n1 First avenue\nFirst region\n0100\nFrance", "locationPhoneNumber": "+***********", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100"}, "receivingAddress": {"addressLine1": "First address line site", "addressLine2": "", "city": "", "concatenatedAddress": "US001\nFirst address line site\nMD\n20746\nUnited States of America", "country": {"id": "US", "name": "United States of America"}, "locationPhoneNumber": "+***********", "name": "US001", "postcode": "20746", "region": "MD"}, "returnAddress": null, "number": "PR32", "site": {"primaryAddress": {"country": {"id": "US"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********"}, "name": "Chem. Atlanta", "id": "US001", "isLocationManaged": true}, "supplier": {"businessEntity": {"name": "LECLERC supermarket", "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "symbol": "€"}}, "primaryAddress": {"country": {"id": "FR"}, "isActive": true, "name": "Store", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100", "locationPhoneNumber": "+***********"}}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "date": "2021-11-16", "supplierDocumentNumber": "", "totalAmountExcludingTax": "500", "status": "draft", "returnStatus": "notReturned", "invoiceStatus": "notInvoiced", "text": {"value": ""}, "lines": {"query": {"edges": [{"node": {"purchaseOrderLine": {"unit": {"decimalDigits": 2, "id": "KILOGRAM"}, "purchaseReceiptLine": {"document": {"number": "PR32"}, "completed": true}, "purchaseOrderLine": {"document": {"number": "PO22", "status": "inProgress"}, "quantityToReceive": "0", "quantity": "10", "receivedQuantity": "5", "status": "inProgress"}, "receivedQuantity": "5"}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "stockUnit": {"id": "GRAM", "name": "Gram", "decimalDigits": 2}, "unit": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "item": {"stockUnit": {"name": "Gram", "id": "GRAM"}, "name": "A box of muesli", "id": "<PERSON><PERSON><PERSON>", "description": "A 500g box of muesli", "lotManagement": "lotManagement"}, "status": "draft", "lineReturnStatus": "notReturned", "lineInvoiceStatus": "notInvoiced", "itemDescription": "", "quantity": "5", "quantityInStockUnit": "5000", "unitToStockUnitConversionFactor": "1000", "origin": "purchaseOrder", "grossPrice": "100", "discount": "0", "charge": "0", "netPrice": "100", "amountExcludingTax": "500", "priceOrigin": null, "jsonStockDetails": "{}", "storedAttributes": null, "storedDimensions": null, "completed": true}}]}}, "carrier": null}}}}}