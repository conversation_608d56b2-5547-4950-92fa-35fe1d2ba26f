{
    xtremPurchasing {
        purchaseReturn {
            query(filter: "{number: 'RET001'}") {
                edges {
                    node {
                        number
                        status
                        invoiceStatus
                        returnRequestDate
                        totalAmountExcludingTax
                        totalAmountExcludingTaxInCompanyCurrency
                        supplier {
                            businessEntity {
                                id
                            }
                        }

                        currency {
                            id
                        }
                        returnSite {
                            id
                        }
                        currency {
                            id
                        }
                        companyCurrency {
                            id
                        }
                        transactionCurrency {
                            id
                        }
                        companyFxRate
                        companyFxRateDivisor
                        lines {
                            query {
                                edges {
                                    node {
                                        status
                                        lineInvoiceStatus
                                        item {
                                            id
                                        }
                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        amountExcludingTax
                                        amountExcludingTaxInCompanyCurrency
                                        purchaseReceiptLine {
                                            returnedQuantity
                                            returnedQuantityInStockUnit
                                            purchaseReceiptLine {
                                                item {
                                                    id
                                                }
                                                document {
                                                    site {
                                                        id
                                                    }
                                                    supplier {
                                                        businessEntity {
                                                            id
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
