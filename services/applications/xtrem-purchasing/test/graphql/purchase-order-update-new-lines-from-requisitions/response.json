{"data": {"xtremPurchasing": {"purchaseOrder": {"update": {"number": "PO17", "site": {"name": "Chem. Atlanta", "id": "US001", "isPurchase": true, "isInventory": true}, "supplier": {"businessEntity": {"name": "LECLERC supermarket", "id": "LECLERC"}}, "orderDate": "2020-08-10", "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "transactionCurrency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "totalAmountExcludingTax": "450", "totalAmountExcludingTaxInCompanyCurrency": "508.5", "text": {"value": "supplier text to come"}, "status": "draft", "approvalStatus": "draft", "receiptStatus": "notReceived", "changeRequestedDescription": {"value": ""}, "lines": {"query": {"edges": [{"node": {"stockUnit": {"name": "Gram", "decimalDigits": 2, "id": "GRAM"}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "unit": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "stockSite": {"name": "Chem. Atlanta", "id": "US001", "isPurchase": true, "isInventory": true}, "site": {"name": "Chem. Atlanta", "id": "US001"}, "item": {"name": "A box of muesli", "id": "<PERSON><PERSON><PERSON>"}, "status": "draft", "itemDescription": "A 500g box of muesli", "quantity": "15", "grossPrice": "10", "amountExcludingTax": "150", "amountIncludingTaxInCompanyCurrency": "169.5", "expectedReceiptDate": "2020-08-10", "changeRequestedDescription": "", "quantityInStockUnit": "15000", "lineReceiptStatus": "notReceived", "origin": "purchaseRequisition", "storedAttributes": "{\"project\":\"AttPROJ\",\"task\":\"Task1\"}", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}, {"node": {"stockUnit": {"name": "Gram", "decimalDigits": 2, "id": "GRAM"}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "unit": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "stockSite": {"name": "Chem. Atlanta", "id": "US001", "isPurchase": true, "isInventory": true}, "site": {"name": "Chem. Atlanta", "id": "US001"}, "item": {"name": "Chemical description C", "id": "Chemical C"}, "status": "draft", "itemDescription": "Chemical contains klm", "quantity": "20", "grossPrice": "10", "amountExcludingTax": "200", "amountIncludingTaxInCompanyCurrency": "226", "expectedReceiptDate": "2020-08-10", "changeRequestedDescription": "", "quantityInStockUnit": "20000", "lineReceiptStatus": "notReceived", "origin": "purchaseRequisition", "storedAttributes": "{\"project\":\"AttPROJ\",\"task\":\"Task1\"}", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}, {"node": {"stockUnit": {"name": "Gram", "decimalDigits": 2, "id": "GRAM"}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "unit": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "stockSite": {"name": "Chem. Atlanta", "id": "US001", "isPurchase": true, "isInventory": true}, "site": {"name": "Chem. Atlanta", "id": "US001"}, "item": {"name": "A box of muesli", "id": "<PERSON><PERSON><PERSON>"}, "status": "draft", "itemDescription": "A 500g box of muesli", "quantity": "10", "grossPrice": "10", "amountExcludingTax": "100", "amountIncludingTaxInCompanyCurrency": "113", "expectedReceiptDate": "2020-08-10", "changeRequestedDescription": "", "quantityInStockUnit": "10000", "lineReceiptStatus": "notReceived", "origin": "purchaseRequisition", "storedAttributes": "{\"project\":\"AttPROJ\",\"task\":\"Task1\"}", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "paymentTerm": {"name": "Net 30", "description": ""}}}}}}