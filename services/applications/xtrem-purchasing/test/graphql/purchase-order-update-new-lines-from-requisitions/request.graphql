mutation {
    xtremPurchasing {
        purchaseOrder {
            update(
                data: {
                    _id: "#PO17"
                    lines: [
                        {
                            _action: "create"
                            expectedReceiptDate: "2020-08-10"
                            item: "#Muesli"
                            grossPrice: 10
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: "#REQ8|20", orderedQuantity: 15 }]
                            unit: "#KILOGRAM"
                            quantity: 15
                            site: "#US001"
                            stockSite: "#US001"
                            stockUnit: "#GRAM"
                        }
                        {
                            _action: "create"
                            expectedReceiptDate: "2020-08-10"
                            item: "#Chemical C"
                            grossPrice: 10
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: "#REQ8|30", orderedQuantity: 20 }]
                            unit: "#KILOGRAM"
                            quantity: 20
                            site: "#US001"
                            stockSite: "#US001"
                            stockUnit: "#GRAM"
                        }
                        {
                            _action: "create"
                            expectedReceiptDate: "2020-08-10"
                            item: "#Muesli"
                            grossPrice: 10
                            purchaseRequisitionLines: [{ purchaseRequisitionLine: "#REQ9|20", orderedQuantity: 10 }]
                            unit: "#KILOGRAM"
                            quantity: 10
                            site: "#US001"
                            stockSite: "#US001"
                            stockUnit: "#GRAM"
                        }
                        { _sortValue: "3100", _action: "delete" }
                    ]
                }
            ) {
                number
                site {
                    name
                    id
                    isPurchase
                    isInventory
                }
                supplier {
                    businessEntity {
                        name
                        id
                    }
                }
                orderDate
                currency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                totalAmountExcludingTax
                totalAmountExcludingTaxInCompanyCurrency
                text {
                    value
                }
                status
                approvalStatus
                receiptStatus
                changeRequestedDescription {
                    value
                }
                lines {
                    query {
                        edges {
                            node {
                                stockUnit {
                                    name
                                    decimalDigits
                                    id
                                }
                                currency {
                                    name
                                    id
                                    decimalDigits
                                    rounding
                                    symbol
                                }
                                unit {
                                    id
                                    name
                                    decimalDigits
                                }
                                stockSite {
                                    name
                                    id
                                    isPurchase
                                    isInventory
                                }
                                site {
                                    name
                                    id
                                }
                                item {
                                    name
                                    id
                                }
                                status
                                itemDescription
                                quantity
                                grossPrice
                                amountExcludingTax
                                amountIncludingTaxInCompanyCurrency
                                expectedReceiptDate
                                changeRequestedDescription
                                quantityInStockUnit
                                lineReceiptStatus
                                origin
                                storedAttributes
                                storedDimensions
                            }
                        }
                    }
                }
                paymentTerm {
                    name
                    description
                }
            }
        }
    }
}
