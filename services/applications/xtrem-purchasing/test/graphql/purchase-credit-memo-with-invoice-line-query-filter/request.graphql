{
    xtremPurchasing {
        purchaseCreditMemo {
            query(filter: "{number: 'PCM2'}") {
                edges {
                    node {
                        number
                        site {
                            name
                        }
                        creditMemoDate
                        billBySupplier {
                            businessEntity {
                                name
                            }
                        }

                        billByLinkedAddress {
                            name
                            addressLine1
                        }
                        billByAddress {
                            name
                            addressLine1
                        }
                        billByContact {
                            firstName
                            lastName
                        }
                        payToSupplier {
                            businessEntity {
                                name
                            }
                        }
                        payToLinkedAddress {
                            name
                            addressLine1
                        }
                        payToAddress {
                            name
                            addressLine1
                        }
                        payToContact {
                            firstName
                            lastName
                        }
                        paymentTerm {
                            name
                        }
                        supplierDocumentNumber
                        reason {
                            name
                        }
                        currency {
                            name
                        }
                        companyCurrency {
                            name
                        }
                        transactionCurrency {
                            name
                        }
                        companyFxRate
                        companyFxRateDivisor
                        totalAmountExcludingTax
                        calculatedTotalAmountExcludingTaxInCompanyCurrency
                        totalTaxAmount
                        totalAmountIncludingTax
                        calculatedTotalAmountIncludingTaxInCompanyCurrency
                        lines {
                            query {
                                edges {
                                    node {
                                        item {
                                            id
                                        }
                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        stockUnit {
                                            id
                                        }
                                        amountExcludingTax
                                        amountExcludingTaxInCompanyCurrency
                                        text {
                                            value
                                        }
                                        purchaseInvoiceLine {
                                            purchaseInvoiceLine {
                                                document {
                                                    number
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
