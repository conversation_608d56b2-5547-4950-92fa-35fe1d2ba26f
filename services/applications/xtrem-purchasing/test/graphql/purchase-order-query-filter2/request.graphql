{
    xtremPurchasing {
        purchaseOrder {
            query(filter: "{number: 'PO2'}") {
                edges {
                    node {
                        number
                        status
                        approvalStatus
                        displayStatus
                        orderDate
                        paymentTerm {
                            name
                        }
                        text {
                            value
                        }
                        changeRequestedDescription {
                            value
                        }
                        totalAmountExcludingTax
                        totalAmountExcludingTaxInCompanyCurrency
                        supplier {
                            businessEntity {
                                id
                            }
                        }
                        currency {
                            id
                        }
                        transactionCurrency {
                            id
                        }
                        companyCurrency {
                            id
                        }
                        site {
                            id
                        }
                        stockSite {
                            id
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        status
                                        item {
                                            id
                                        }
                                        stockSite {
                                            id
                                        }
                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        stockUnit {
                                            id
                                        }
                                        changeRequestedDescription
                                        amountExcludingTax
                                        amountExcludingTaxInCompanyCurrency
                                        text {
                                            value
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
