mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            update(data: { _id: "#PCM2", lines: [{ _action: "delete", _sortValue: "270200" }] }) {
                creditMemoDate
                calculatedTotalAmountExcludingTax
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                priceOrigin
                            }
                        }
                    }
                }
            }
        }
    }
}
