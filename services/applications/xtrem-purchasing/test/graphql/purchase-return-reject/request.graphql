mutation {
    xtremPurchasing {
        purchaseReturn {
            update(
                data: {
                    _id: "#RET024"
                    lines: [
                        {
                            _action: "update"
                            _sortValue: "1"
                            approvalStatus: "rejected"
                            item: "#Aqua"
                            status: "closed"
                            grossPrice: 10
                            priceOrigin: null
                            purchaseReceiptLine: { returnedQuantity: "15" }
                            quantity: 15
                            reason: "#R1"
                            shippedStatus: "notShipped"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    text: { value: "" }
                    businessRelation: "#US017"
                    returnSite: "#US005"
                    number: "RET024"
                    returnRequestDate: "2020-08-10"
                    returnItems: true
                    supplierReturnReference: null
                    approvalStatus: "rejected"
                    status: "closed"
                    shippingStatus: "notShipped"
                }
            ) {
                number
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor

                status
                approvalStatus
                shippingStatus
                lines {
                    query {
                        edges {
                            node {
                                _id
                                origin
                                status
                                approvalStatus
                                shippedStatus
                                quantity
                                grossPrice
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                priceOrigin
                                storedAttributes
                                storedDimensions
                            }
                        }
                    }
                }
            }
        }
    }
}
