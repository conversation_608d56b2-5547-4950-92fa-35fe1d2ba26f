{"Read purchase receipt with landed costs. Get summary by landed cost types": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"number": "PR46"}, "output": {"query": {"edges": [{"node": {"number": "PR46", "jsonAggregateLandedCostTypes": "[{\"landedCostType\":\"customs\",\"actualCostAmountInCompanyCurrency\":\"214.72\",\"actualAllocatedCostAmountInCompanyCurrency\":\"0\"},{\"landedCostType\":\"freight\",\"actualCostAmountInCompanyCurrency\":\"1002\",\"actualAllocatedCostAmountInCompanyCurrency\":\"0\"}]"}}]}}}}