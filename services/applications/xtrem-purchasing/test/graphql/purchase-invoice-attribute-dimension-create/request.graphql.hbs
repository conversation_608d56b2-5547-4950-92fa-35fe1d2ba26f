mutation purchaseOrder($storedAttributes: Json, $storedDimensions: Json) {
  xtremPurchasing {
    purchaseInvoice {
      create(
        data: {
          site: "#US001"
          billBySupplier: "#LECLERC"
          currency: "#EUR"
          invoiceDate: "2021-03-02"
          totalAmountExcludingTax: 4.67
          lines: [
            {
              item: "#Chemical C"
              quantity: 1
              unit: "#KILOGRAM"
              grossPrice: 100
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
              purchaseReceiptLine: { purchaseReceiptLine: "#PR4|1" }
            }
          ]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
