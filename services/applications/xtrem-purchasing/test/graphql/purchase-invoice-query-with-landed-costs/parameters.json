{"Total allocated amount less than the landed cost amount to allocate": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"invoiceNumber": "PI15"}, "output": {"data": {"xtremPurchasing": {"purchaseInvoice": {"query": {"edges": [{"node": {"calculatedTotalAmountIncludingTax": "169", "calculatedTotalAmountIncludingTaxInCompanyCurrency": "169.34", "calculatedTotalTaxAmount": "0", "calculatedTotalTaxAmountAdjusted": "0", "calculatedTotalTaxableAmount": "0", "calculatedTotalExemptAmount": "0", "lines": {"query": {"edges": [{"node": {"item": {"name": "Landed cost 003 byAmount"}, "itemDescription": "Landed cost byAmount", "quantity": "13", "grossPrice": "13", "landedCostCheckResult": "{\"title\":\"Allocated amount missing\",\"message\":\"You need to enter an allocated amount greater than zero for each allocated document.\"}"}}]}}, "totalAmountExcludingTax": "169", "calculatedTotalAmountExcludingTaxInCompanyCurrency": "169.34"}}]}}}}}}, "several lines with error on landed costs": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"invoiceNumber": "PI11"}, "output": {"data": {"xtremPurchasing": {"purchaseInvoice": {"query": {"edges": [{"node": {"calculatedTotalAmountIncludingTax": "13000", "calculatedTotalAmountIncludingTaxInCompanyCurrency": "13026", "calculatedTotalTaxAmount": "0", "calculatedTotalTaxAmountAdjusted": "0", "calculatedTotalTaxableAmount": "0", "calculatedTotalExemptAmount": "0", "lines": {"query": {"edges": [{"node": {"item": {"name": "Landed cost 001 byQuantity"}, "itemDescription": "Landed cost byWeight", "quantity": "10", "grossPrice": "110", "landedCostCheckResult": "{\"title\":\"Allocated amount insufficient\",\"message\":\"The total allocated amount (€100) cannot be less than the landed cost amount to allocate (€1100).\"}"}}, {"node": {"item": {"name": "Landed cost 002 byWeight"}, "itemDescription": "Landed cost byWeight", "quantity": "20", "grossPrice": "120", "landedCostCheckResult": null}}, {"node": {"item": {"name": "Landed cost 003 byAmount"}, "itemDescription": "Landed cost byWeight", "quantity": "30", "grossPrice": "130", "landedCostCheckResult": "{\"title\":\"Allocated amount insufficient\",\"message\":\"The total allocated amount (€300) cannot be less than the landed cost amount to allocate (€3900).\"}"}}, {"node": {"item": {"name": "Landed cost 004 byVolume"}, "itemDescription": "Landed cost byWeight", "quantity": "40", "grossPrice": "140", "landedCostCheckResult": "{\"title\":\"Allocated amount missing\",\"message\":\"You need to enter an allocated amount greater than zero for each allocated document.\"}"}}]}}, "totalAmountExcludingTax": "13000", "calculatedTotalAmountExcludingTaxInCompanyCurrency": "13026"}}]}}}}}}}