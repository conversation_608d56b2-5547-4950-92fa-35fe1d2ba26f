{
  xtremPurchasing {
    purchaseInvoice {
      query(filter: "{number: '{{{invoiceNumber}}}' }") {
        edges {
          node {
            calculatedTotalAmountIncludingTax
            calculatedTotalAmountIncludingTaxInCompanyCurrency
            calculatedTotalTaxAmount
            calculatedTotalTaxAmountAdjusted
            calculatedTotalTaxableAmount
            calculatedTotalExemptAmount
            lines {
              query {
                edges {
                  node {
                    item {
                      name
                    }
                    itemDescription
                    quantity
                    grossPrice
                    landedCostCheckResult
                  }
                }
              }
            }
            totalAmountExcludingTax
            calculatedTotalAmountExcludingTaxInCompanyCurrency
          }
        }
      }
    }
  }
}
