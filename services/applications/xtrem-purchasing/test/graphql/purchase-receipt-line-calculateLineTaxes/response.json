{"data": {"xtremPurchasing": {"purchaseReceiptLine": {"calculateLineTaxes": {"taxAmount": "0.55", "taxAmountAdjusted": "0.55", "amountIncludingTax": "10.55", "taxes": [{"taxCategoryReference": {"name": "Value Added Tax"}, "taxCategory": "Value Added Tax", "taxRate": "5.5", "taxAmount": "0.55", "isTaxMandatory": true, "isSubjectToGlTaxExcludedAmount": false, "nonTaxableAmount": "0", "exemptAmount": "0", "taxableAmount": "10", "currency": {"name": "South African Rand"}, "deductibleTaxRate": "100", "deductibleTaxAmount": "0.55", "isReverseCharge": false, "taxAmountAdjusted": "0.55", "taxReference": {"name": "Tax collected on payments, reduced rate"}, "tax": "Tax collected on payments, reduced rate"}, {"taxCategoryReference": {"name": "TEST"}, "taxCategory": "TEST", "taxRate": "0", "taxAmount": "0", "isTaxMandatory": true, "isSubjectToGlTaxExcludedAmount": true, "nonTaxableAmount": "0", "exemptAmount": "0", "taxableAmount": "10", "currency": {"name": "South African Rand"}, "deductibleTaxRate": "0", "deductibleTaxAmount": "0", "isReverseCharge": false, "taxAmountAdjusted": "0", "taxReference": null, "tax": ""}]}}}}}