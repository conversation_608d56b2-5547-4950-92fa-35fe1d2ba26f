{"data": {"xtremPurchasing": {"purchaseReceipt": {"update": {"supplierAddress": {"country__name": {"id": "FR", "name": "France"}, "name": "Store", "concatenatedAddress": "Store\n1 First avenue\nFirst region\n0100\nFrance", "locationPhoneNumber": "+***********", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100"}, "receivingAddress": {"addressLine1": "First address line site", "addressLine2": "", "city": "", "concatenatedAddress": "US001\nFirst address line site\nMD\n20746\nUnited States of America", "country__name": {"id": "US", "name": "United States of America"}, "locationPhoneNumber": "+***********", "name": "US001", "postcode": "20746", "region": "MD"}, "returnAddress": null, "number": "PR36", "site": {"primaryAddress___id": {"country___id": {"id": "US"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********"}, "name": "Chem. Atlanta", "id": "US001", "isLocationManaged": true}, "supplier": {"businessEntity": {"currency__id": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "symbol": "€"}, "name": "LECLERC supermarket", "id": "LECLERC"}, "primaryAddress___id": {"country___id": {"id": "FR"}, "isActive": true, "name": "Store", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100", "locationPhoneNumber": "+***********"}}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "date": "2021-11-16", "supplierDocumentNumber": "", "totalAmountExcludingTax": "400", "status": "pending", "returnStatus": "notReturned", "invoiceStatus": "notInvoiced", "text": {"value": ""}, "lines": {"query": {"edges": [{"node": {"purchaseOrderLine__receivedQuantity": {"receivedQuantity": "4"}, "purchaseOrderLine": {"purchaseUnit__id": {"decimalDigits": 2, "id": "KILOGRAM"}, "purchaseReceiptLine__document__number": {"document": {"number": "PR36"}, "completed": false}, "purchaseOrderLine__document__number": {"document": {"number": "PO24"}, "document__number": {"number": "PO24", "status": "inProgress"}, "quantityToReceive": "0", "quantity": "10", "receivedQuantity": "10", "status": "inProgress"}, "receivedQuantity": "4"}, "currency__id": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "stockUnit__id": {"id": "GRAM", "name": "Gram", "decimalDigits": 2}, "purchaseUnit__id": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "item__name": {"stockUnit__id": {"name": "Gram", "id": "GRAM"}, "name": "A box of muesli", "id": "<PERSON><PERSON><PERSON>", "description": "A 500g box of muesli", "lotManagement": "lotManagement"}, "status": "pending", "lineReturnStatus": "notReturned", "lineInvoiceStatus": "notInvoiced", "itemDescription": "", "quantity": "4", "quantityInStockUnit": "4000", "unitToStockUnitConversionFactor": "1000", "origin": "purchaseOrder", "grossPrice": "100", "discount": "0", "charge": "0", "netPrice": "100", "amountExcludingTax": "400", "priceOrigin": null, "storedAttributes": null, "storedDimensions": null, "completed": false}}]}}, "carrier": null}}}}}