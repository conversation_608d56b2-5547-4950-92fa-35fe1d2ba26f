mutation {
    xtremPurchasing {
        purchaseReceipt {
            create(
                data: {
                    site: "#US001"
                    businessRelation: "#LECLERC"
                    currency: "#EUR"
                    date: "2020-08-09"
                    carrier: "#700"
                    supplierAddress: {
                        name: "test new supplierAddress"
                        country: "#FR"
                        addressLine1: "test site"
                        addressLine2: ""
                        city: ""
                        region: "HDF"
                        postcode: "59650"
                        locationPhoneNumber: "1234567890"
                    }
                    receivingAddress: {
                        name: "test new receivingAddress"
                        country: "#FR"
                        addressLine1: "test site"
                        addressLine2: ""
                        city: ""
                        region: "HDF"
                        postcode: "59650"
                        locationPhoneNumber: "1234567890"
                    }
                    returnAddress: {
                        name: "test new returnAddress"
                        country: "#FR"
                        addressLine1: "test site"
                        addressLine2: ""
                        city: ""
                        region: "HDF"
                        postcode: "59650"
                        locationPhoneNumber: "1234567890"
                    }
                    lines: [
                        {
                            item: "#Chemical C"
                            quantity: 10
                            unit: "#KILOGRAM"
                            grossPrice: 10
                            jsonStockDetails: "[{\"item\": \"#Chemical C\", \"site\": 3, \"location\": 1, \"stockUnit\": \"#GRAM\", \"status\": \"#A\", \"quantityInStockUnit\": 10}]"
                        }
                    ]
                }
            ) {
                number
                supplierAddress {
                    name
                }
                receivingAddress {
                    name
                }
                returnAddress {
                    name
                }
            }
        }
    }
}
