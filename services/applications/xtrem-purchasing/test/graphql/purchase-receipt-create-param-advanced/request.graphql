mutation {
    xtremPurchasing {
        purchaseReceipt {
            create(
                data: {
                    site: "#US001"
                    businessRelation: "#LECLERC"
                    currency: "#EUR"
                    lines: [
                        {
                            item: "#Chemical C"
                            quantity: 10
                            unit: "#KILOGRAM"
                            grossPrice: 100
                            jsonStockDetails: "[{\"item\": \"#Chemical C\", \"site\": 3, \"location\": 1, \"stockUnit\": \"#GRAM\", \"status\": \"#A\", \"quantityInStockUnit\": 10}]"
                        }
                    ]
                }
            ) {
                number
                returnAddress {
                    name
                }
            }
        }
    }
}
