mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            update(
                data: {
                    _id: "#PCM3"
                    lines: [
                        {
                            _action: "update"
                            _sortValue: "270400"
                            quantity: 10
                            quantityInStockUnit: 10
                            purchaseReturnLine: { purchaseReturnLine: "#RET026|1" }
                        }
                    ]
                }
            ) {
                creditMemoDate
                calculatedTotalAmountExcludingTax
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                text {
                                    value
                                }
                                priceOrigin
                            }
                        }
                    }
                }
            }
        }
    }
}
