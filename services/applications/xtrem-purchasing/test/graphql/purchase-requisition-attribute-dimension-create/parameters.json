{"Create purchase requisition - attributes project": {"variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}"}, "output": {"create": {"number": "PQ210001", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"Chemical C\",\"businessSite\":\"US001\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": null}}]}}}}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase requisition - attributes and dimensions": {"variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}, "output": {"create": {"number": "PQ210001", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"Chemical C\",\"businessSite\":\"US001\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}}]}}}}, "envConfigs": {"today": "2021-03-02"}}}