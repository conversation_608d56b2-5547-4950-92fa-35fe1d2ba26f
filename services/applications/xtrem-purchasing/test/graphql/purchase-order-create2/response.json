{"data": {"xtremPurchasing": {"purchaseOrder": {"create": {"number": "PO200001", "status": "draft", "receiptStatus": "notReceived", "approvalStatus": "draft", "orderDate": "2020-08-10", "fxRateDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "text": {"value": ""}, "changeRequestedDescription": {"value": ""}, "totalAmountExcludingTax": "25000", "totalAmountExcludingTaxInCompanyCurrency": "25050", "supplier": {"businessEntity": {"id": "LECLERC"}}, "supplierAddress": {"name": "test new supplierLinkedAddress"}, "billBySupplier": {"businessEntity": {"name": "LECLERC supermarket"}}, "currency": {"id": "EUR"}, "site": {"id": "US001"}, "siteAddress": {"name": "test new siteAddress"}, "transactionCurrency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "companyFxRate": "1.002", "lines": {"query": {"edges": [{"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "stockSite": {"id": "US001"}, "stockSiteAddress": {"name": "test address line 1", "addressLine1": "test suplier", "addressLine2": ""}, "stockSiteContact": {"title": "mr", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "changeRequestedDescription": "", "amountExcludingTax": "15000", "amountExcludingTaxInCompanyCurrency": "15030", "text": {"value": ""}, "expectedReceiptDate": "2020-08-10", "workInProgress": {"documentType": "purchaseOrder", "status": "planned", "startDate": "2020-08-10", "endDate": "2020-08-10", "expectedQuantity": "150000", "actualQuantity": "0", "outstandingQuantity": "150000"}}}, {"node": {"status": "draft", "lineReceiptStatus": "notReceived", "item": {"id": "Chemical C"}, "stockSite": {"id": "US001"}, "stockSiteAddress": {"name": "test address line 1", "addressLine1": "test suplier", "addressLine2": ""}, "stockSiteContact": null, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "changeRequestedDescription": "", "amountExcludingTax": "10000", "amountExcludingTaxInCompanyCurrency": "10020", "text": {"value": ""}, "expectedReceiptDate": "2020-08-10", "workInProgress": {"documentType": "purchaseOrder", "status": "planned", "startDate": "2020-08-10", "endDate": "2020-08-10", "expectedQuantity": "100", "actualQuantity": "0", "outstandingQuantity": "100"}}}]}}}}}}}