mutation {
    xtremPurchasing {
        purchaseOrder {
            create(
                data: {
                    site: "#US001"
                    businessRelation: "#LECLERC"
                    currency: "#EUR"
                    orderDate: "2020-08-10"
                    fxRateDate: "2020-08-10"
                    siteAddress: {
                        name: "test new siteAddress"
                        country: "#FR"
                        addressLine1: "test site"
                        addressLine2: ""
                        city: ""
                        region: "HDF"
                        postcode: "59650"
                        locationPhoneNumber: "0695125874"
                    }
                    supplierAddress: {
                        name: "test new supplierLinkedAddress"
                        country: "#FR"
                        addressLine1: "test suplier"
                        addressLine2: ""
                        city: ""
                        region: "HDF"
                        postcode: "59650"
                        locationPhoneNumber: "0695125874"
                    }
                    lines: [
                        {
                            item: "#Chemical C"
                            stockSite: "#US001"
                            quantity: 150
                            unit: "#KILOGRAM"
                            grossPrice: 100
                            stockSiteAddress: {
                                name: "test address line 1"
                                country: "#FR"
                                addressLine1: "test suplier"
                                addressLine2: ""
                                city: ""
                                region: "HDF"
                                postcode: "59650"
                                locationPhoneNumber: "0695125874"
                            }
                            stockSiteContact: {
                                title: "mr"
                                firstName: "John"
                                lastName: "Doe"
                                preferredName: "John"
                                role: "mainContact"
                                position: "CEO"
                                locationPhoneNumber: "+27825554321"
                                email: "<EMAIL>"
                            }
                        }
                        {
                            item: "#Chemical C"
                            stockSite: "#US001"
                            quantity: 100
                            unit: "#GRAM"
                            grossPrice: 100
                            stockSiteAddress: {
                                name: "test address line 1"
                                country: "#FR"
                                addressLine1: "test suplier"
                                addressLine2: ""
                                city: ""
                                region: "HDF"
                                postcode: "59650"
                                locationPhoneNumber: "0695125874"
                            }
                        }
                    ]
                }
            ) {
                number
                status
                receiptStatus
                approvalStatus
                orderDate
                fxRateDate
                paymentTerm {
                    name
                }
                text {
                    value
                }
                changeRequestedDescription {
                    value
                }
                totalAmountExcludingTax
                totalAmountExcludingTaxInCompanyCurrency
                supplier {
                    businessEntity {
                        id
                    }
                }
                supplierAddress {
                    name
                }
                billBySupplier {
                    businessEntity {
                        name
                    }
                }
                currency {
                    id
                }
                site {
                    id
                }
                siteAddress {
                    name
                }
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                companyFxRate
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineReceiptStatus
                                item {
                                    id
                                }
                                stockSite {
                                    id
                                }
                                stockSiteAddress {
                                    name
                                    addressLine1
                                    addressLine2
                                }
                                stockSiteContact {
                                    title
                                    firstName
                                    lastName
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                changeRequestedDescription
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                expectedReceiptDate
                                workInProgress {
                                    documentType
                                    status
                                    startDate
                                    endDate
                                    expectedQuantity
                                    actualQuantity
                                    outstandingQuantity
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
