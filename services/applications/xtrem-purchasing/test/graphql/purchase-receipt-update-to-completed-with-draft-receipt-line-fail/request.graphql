mutation {
    xtremPurchasing {
        purchaseReceipt {
            update(
                data: {
                    _id: "#PR36"
                    carrier: null
                    lines: [
                        {
                            _action: "update"
                            _sortValue: "1"
                            charge: 0
                            completed: true
                            discount: 0
                            grossPrice: 100
                            item: "#Muesli"
                            itemDescription: null
                            lineInvoiceStatus: "notInvoiced"
                            lineReturnStatus: "notReturned"
                            status: "draft"
                            netPrice: 100
                            priceOrigin: null
                            purchaseOrderLine: { purchaseOrderLine: "#PO24|3900" }
                            unit: "#KILOGRAM"
                            quantity: 4
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    text: null
                    currency: "#EUR"
                    businessRelation: "#LECLERC"
                    site: "#US001"
                    number: "PR36"
                    date: "2021-11-16"
                    supplierDocumentNumber: null
                    status: "draft"
                    returnStatus: "notReturned"
                    invoiceStatus: "notInvoiced"
                }
            ) {
                number
            }
        }
    }
}
