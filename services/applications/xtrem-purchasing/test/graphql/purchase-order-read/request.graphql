{
    xtremPurchasing {
        purchaseOrder {
            query(filter: "{number: 'PO1'}") {
                edges {
                    node {
                        number
                        status
                        approvalStatus
                        displayStatus
                        orderDate
                        paymentTerm {
                            name
                        }
                        text {
                            value
                        }
                        changeRequestedDescription {
                            value
                        }
                        totalAmountExcludingTax
                        totalAmountExcludingTaxInCompanyCurrency
                        supplier {
                            businessEntity {
                                id
                            }
                        }
                        currency {
                            id
                        }
                        transactionCurrency {
                            id
                        }
                        companyCurrency {
                            id
                        }
                        site {
                            id
                        }

                        stockSite {
                            id
                        }
                        currency {
                            id
                        }
                        totalTaxAmount
                        totalTaxableAmount
                        totalExemptAmount
                        taxCalculationStatus
                        totalAmountIncludingTax
                        totalAmountIncludingTaxInCompanyCurrency
                        totalTaxAmountAdjusted
                        taxes {
                            query {
                                edges {
                                    node {
                                        currency {
                                            id
                                        }
                                        taxCategory
                                        tax
                                        nonTaxableAmount
                                        taxRate
                                        taxAmount
                                        exemptAmount
                                        taxableAmount
                                        taxAmountAdjusted
                                    }
                                }
                            }
                        }
                        internalNote {
                            value
                        }
                        externalNote {
                            value
                        }
                        isExternalNote
                        lines {
                            query {
                                edges {
                                    node {
                                        status
                                        item {
                                            id
                                        }
                                        stockSite {
                                            id
                                        }
                                        stockSiteAddress {
                                            name
                                            addressLine1
                                            addressLine2
                                        }
                                        stockSiteContact {
                                            title
                                            firstName
                                            lastName
                                        }
                                        stockSiteLinkedAddress {
                                            name
                                            addressLine1
                                            addressLine2
                                            contacts {
                                                query {
                                                    edges {
                                                        node {
                                                            title
                                                            firstName
                                                            lastName
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        quantity
                                        unit {
                                            id
                                        }
                                        grossPrice
                                        quantityInStockUnit
                                        stockUnit {
                                            id
                                        }
                                        changeRequestedDescription
                                        amountExcludingTax
                                        text {
                                            value
                                        }
                                        expectedReceiptDate
                                        taxableAmount
                                        taxAmount
                                        exemptAmount
                                        taxDate
                                        amountIncludingTax
                                        amountIncludingTaxInCompanyCurrency
                                        taxAmountAdjusted
                                        taxCalculationStatus
                                        internalNote {
                                            value
                                        }
                                        externalNote {
                                            value
                                        }
                                        isExternalNote
                                        taxes {
                                            query {
                                                edges {
                                                    node {
                                                        currency {
                                                            id
                                                        }
                                                        taxCategory
                                                        tax
                                                        nonTaxableAmount
                                                        taxRate
                                                        taxAmount
                                                        exemptAmount
                                                        taxableAmount
                                                        taxAmountAdjusted
                                                    }
                                                }
                                            }
                                        }
                                        numberOfPurchaseReceiptLines
                                        numberOfPurchaseInvoiceLines
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
