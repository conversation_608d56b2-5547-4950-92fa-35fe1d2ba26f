{"data": {"xtremPurchasing": {"purchaseOrder": {"query": {"edges": [{"node": {"number": "PO1", "status": "draft", "approvalStatus": "draft", "displayStatus": "draft", "orderDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "text": {"value": "supplier text to come"}, "changeRequestedDescription": {"value": ""}, "totalAmountExcludingTax": "1000", "totalAmountExcludingTaxInCompanyCurrency": "1000", "supplier": {"businessEntity": {"id": "LECLERC"}}, "currency": {"id": "EUR"}, "transactionCurrency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "site": {"id": "US001"}, "stockSite": {"id": "US001"}, "totalTaxAmount": "0", "totalTaxableAmount": "0", "totalExemptAmount": "0", "taxCalculationStatus": "notDone", "totalAmountIncludingTax": "1000", "totalAmountIncludingTaxInCompanyCurrency": "0", "totalTaxAmountAdjusted": "0", "internalNote": {"value": "internal note on document"}, "externalNote": {"value": "external note on document"}, "isExternalNote": true, "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "10", "taxAmount": "10", "exemptAmount": "5", "taxableAmount": "100", "taxAmountAdjusted": "10"}}]}}, "lines": {"query": {"edges": [{"node": {"status": "draft", "item": {"id": "Chemical C"}, "stockSite": {"id": "US001"}, "stockSiteAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building"}, "stockSiteContact": {"title": "mr", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "stockSiteLinkedAddress": {"name": "US001", "addressLine1": "First address line site", "addressLine2": "", "contacts": {"query": {"edges": []}}}, "quantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}, "changeRequestedDescription": "", "amountExcludingTax": "1000", "text": {"value": "item text to come"}, "expectedReceiptDate": "2020-08-10", "taxableAmount": "0", "taxAmount": "0", "exemptAmount": "0", "taxDate": "2020-08-10", "amountIncludingTax": "0", "amountIncludingTaxInCompanyCurrency": "0", "taxAmountAdjusted": "0", "taxCalculationStatus": "done", "internalNote": {"value": "internal note on line"}, "externalNote": {"value": "external note on line"}, "isExternalNote": true, "taxes": {"query": {"edges": [{"node": {"currency": {"id": "EUR"}, "taxCategory": "Purchase tax", "tax": "Purchase tax", "nonTaxableAmount": "0", "taxRate": "0", "taxAmount": "0", "exemptAmount": "168", "taxableAmount": "0", "taxAmountAdjusted": "0"}}]}}, "numberOfPurchaseReceiptLines": 0, "numberOfPurchaseInvoiceLines": 0}}]}}}}]}}}}}