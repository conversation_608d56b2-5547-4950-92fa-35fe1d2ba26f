mutation {
    xtremPurchasing {
        purchaseReceipt {
            update(
                data: {
                    _id: "#PR34"
                    carrier: null
                    lines: [
                        {
                            _action: "update"
                            _sortValue: 1
                            stockDetailStatus: "entered"
                            completed: true
                            grossPrice: 100
                            item: "#Muesli"
                            itemDescription: null
                            lineInvoiceStatus: "notInvoiced"
                            lineReturnStatus: "notReturned"
                            status: "pending"
                            netPrice: 100
                            priceOrigin: null
                            purchaseOrderLine: { purchaseOrderLine: "#PO23|3800" }
                            unit: "#KILOGRAM"
                            quantity: 4
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    text: null
                    currency: "#EUR"
                    businessRelation: "#LECLERC"
                    site: "#US001"
                    number: "PR34"
                    date: "2021-11-16"
                    supplierDocumentNumber: null
                    status: "draft"
                    returnStatus: "notReturned"
                    invoiceStatus: "notInvoiced"
                }
            ) {
                number
                status
                lines {
                    query {
                        edges {
                            node {
                                completed
                                quantity
                                stockDetailStatus
                                status
                                purchaseOrderLine {
                                    purchaseOrderLine {
                                        document {
                                            number
                                            status
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
