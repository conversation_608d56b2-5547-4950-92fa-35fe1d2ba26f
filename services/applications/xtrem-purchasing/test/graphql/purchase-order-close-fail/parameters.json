{" Close fails : PO1 (having status='draft')": {"executionMode": "normal", "input": {"purchaseOrder": "#PO1"}, "output": {"close": true, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval order.", "path": [], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "Close failed.", "path": ["xtremPurchasing", "purchaseOrder", "close"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Close fails : PO2 (having approvalStatus='pendingApproval')": {"input": {"purchaseOrder": "#PO2"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval order.", "path": [], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "Close failed.", "path": ["xtremPurchasing", "purchaseOrder", "close"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Close fails : PO9 (having status='closed')": {"input": {"purchaseOrder": "#PO9"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval order.", "path": [], "severity": 4}]}, "locations": [{"column": 13, "line": 4}], "message": "Close failed.", "path": ["xtremPurchasing", "purchaseOrder", "close"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Close fails : PO31 (having receipt not posted)": {"input": {"purchaseOrder": "#PO31"}, "output": {"close": null, "errors": [{"message": "Close failed.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseOrder", "close"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "51", "status"], "message": "An order line with associated receipts that are not posted cannot be closed. Check the following purchase receipts: PR49"}, {"severity": 2, "path": ["lines", "51"], "message": "The item: STOAVC, is not managed for the supplier: US016."}, {"severity": 3, "path": ["lines", "52", "status"], "message": "An order line with associated receipts that are not posted cannot be closed. Check the following purchase receipts: PR49"}, {"severity": 2, "path": ["lines", "52"], "message": "The item: STOFIFO, is not managed for the supplier: US016."}, {"severity": 3, "path": ["lines", "53", "status"], "message": "An order line with associated receipts that are not posted cannot be closed. Check the following purchase receipts: PR49"}, {"severity": 2, "path": ["lines", "53"], "message": "The item: NonStockManagedItem, is not managed for the supplier: US016."}, {"severity": 3, "path": ["lines", "54", "status"], "message": "An order line with associated receipts that are not posted cannot be closed. Check the following purchase receipts: PR49"}, {"severity": 2, "path": ["lines", "54"], "message": "The item: STOFIFO, is not managed for the supplier: US016."}]}}]}, "envConfigs": {"today": "2024-01-08", "testActiveServiceOptions": ["landedCostOption"]}}}