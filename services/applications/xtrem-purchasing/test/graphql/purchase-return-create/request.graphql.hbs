mutation {
    xtremPurchasing {
        purchaseReturn {
            create(data: {{inputParameters}}) {
                number
                status
                invoiceStatus
                approvalStatus
                shippingStatus
                returnItems
                returnRequestDate
                totalAmountExcludingTax
                returnToAddress {
                    name
                }
                supplierAddress {
                    name
                }
                supplier {
                    businessEntity {
                        id
                    }
                }
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                currency {
                    id
                }
                returnSite {
                    id
                }
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineInvoiceStatus
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                discount
                                charge
                                netPrice
                                amountExcludingTax
                                approvalStatus
                                shippedStatus
                                reason {
                                    id
                                }
                                workInProgress {
                                    documentType
                                    status
                                    startDate
                                    endDate
                                    expectedQuantity
                                    actualQuantity
                                    outstandingQuantity
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
