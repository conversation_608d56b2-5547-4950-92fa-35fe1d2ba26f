mutation {
    xtremPurchasing {
        purchaseOrder {
            create(
                data: {
                    site: "#US001"
                    businessRelation: "#LECLERC"
                    currency: "#EUR"
                    orderDate: "2020-08-10"
                    fxRateDate: "2020-08-10"
                    lines: [
                        { item: "#Chemical C", stockSite: "#US001", quantity: 150, unit: "#KILOGRAM", grossPrice: 100 }
                        { item: "#Chemical C", stockSite: "#US001", quantity: 100, unit: "#GRAM", grossPrice: 100 }
                    ]
                }
            ) {
                number
                status
                receiptStatus
                approvalStatus
                orderDate
                paymentTerm {
                    name
                }
                text {
                    value
                }
                changeRequestedDescription {
                    value
                }
                totalAmountExcludingTax
                totalAmountExcludingTaxInCompanyCurrency
                supplier {
                    businessEntity {
                        id
                    }
                }
                supplierLinkedAddress {
                    name
                }
                currency {
                    id
                }
                site {
                    id
                }
                siteAddress {
                    name
                }
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineReceiptStatus
                                item {
                                    id
                                }

                                stockSite {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                changeRequestedDescription
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                expectedReceiptDate
                            }
                        }
                    }
                }
            }
        }
    }
}
