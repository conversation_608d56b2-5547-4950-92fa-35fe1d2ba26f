mutation {
    xtremPurchasing {
        purchaseReturn {
            update(
                data: {
                    _id: "#RET025"
                    lines: [
                        {
                            _action: "update"
                            _sortValue: "1"
                            approvalStatus: "pendingApproval"
                            item: "#Aqua"
                            status: "pending"
                            grossPrice: 10
                            priceOrigin: null
                            purchaseReceiptLine: { returnedQuantity: "15" }
                            quantity: 15
                            reason: "#R1"
                            shippedStatus: "notShipped"
                            storedAttributes: null
                            storedDimensions: null
                        }
                    ]
                    text: { value: "" }
                    businessRelation: "#US017"
                    returnSite: "#US005"
                    number: "RET025"
                    returnRequestDate: "2020-08-10"
                    returnItems: true
                    supplierReturnReference: null
                    approvalStatus: "pendingApproval"
                    status: "pending"
                    shippingStatus: "notShipped"
                }
            ) {
                status
                approvalStatus
                shippingStatus
                lines {
                    query {
                        edges {
                            node {
                                _id
                                origin
                                status
                                approvalStatus
                                shippedStatus
                                quantity
                                grossPrice
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                amountExcludingTax
                                priceOrigin
                                storedAttributes
                                storedDimensions
                            }
                        }
                    }
                }
            }
        }
    }
}
