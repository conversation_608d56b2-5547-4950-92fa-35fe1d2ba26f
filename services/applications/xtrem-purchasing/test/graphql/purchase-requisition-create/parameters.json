{"Create purchase requisition - warning number provided in payload ": {"executionMode": "normal", "input": {"properties": {"number": "POTEST", "requester": "#<EMAIL>", "site": "#US001", "lines": [{"item": "#Chemical C", "supplier": "#LECLERC", "currency": "#EUR", "quantity": 10, "unit": "#KILOGRAM", "grossPrice": 100}]}}, "output": {"create": {"number": "POTEST"}}}, "Create purchase requisition - with pending status": {"executionMode": "normal", "input": {"properties": {"number": "POTEST", "requester": "#<EMAIL>", "site": "#US001", "status": "pending", "lines": [{"item": "#Chemical C", "supplier": "#LECLERC", "currency": "#EUR", "quantity": 10, "unit": "#KILOGRAM", "grossPrice": 100}]}}, "output": {"create": {"number": "POTEST"}}}, "Create purchase requisition": {"executionMode": "normal", "input": {"properties": {"requester": "#<EMAIL>", "site": "#US001", "requestDate": "2020-08-10", "internalNote": {"value": "internal note information"}, "lines": [{"item": "#Chemical C", "quantity": 20, "unit": "#GRAM", "grossPrice": 2, "currency": "#ZAR", "internalNote": {"value": "internal note information"}}]}}, "output": {"create": {"number": "PQ200001"}}, "envConfigs": {"today": "2020-08-10"}}}