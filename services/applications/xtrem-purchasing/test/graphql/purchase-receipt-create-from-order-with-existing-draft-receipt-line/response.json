{"data": {"xtremPurchasing": {"purchaseReceipt": {"create": {"supplierAddress": {"country__name": {"name": "France", "id": "FR"}, "name": "Store", "concatenatedAddress": "Store\n1 First avenue\nFirst region\n0100\nFrance", "locationPhoneNumber": "+***********", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100"}, "receivingAddress": {"country__name": {"name": "United States of America", "id": "US"}, "name": "US001", "concatenatedAddress": "US001\nFirst address line site\nMD\n20746\nUnited States of America", "locationPhoneNumber": "+***********", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746"}, "returnAddress": {"country__name": {"name": "France", "id": "FR"}, "name": "Store", "concatenatedAddress": "Store\n1 First avenue\nFirst region\n0100\nFrance", "locationPhoneNumber": "+***********", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100"}, "number": "PR200001", "site": {"primaryAddress___id": {"country___id": {"id": "US"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********"}, "name": "Chem. Atlanta", "id": "US001", "isLocationManaged": true}, "supplier": {"businessEntity": {"currency__id": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "symbol": "€"}, "name": "LECLERC supermarket", "id": "LECLERC"}, "primaryAddress___id": {"country___id": {"id": "FR"}, "isActive": true, "name": "Store", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100", "locationPhoneNumber": "+***********"}}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "transactionCurrency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "date": "2020-08-10", "supplierDocumentNumber": "", "totalAmountExcludingTax": "50", "status": "draft", "returnStatus": "notReturned", "invoiceStatus": "notInvoiced", "text": {"value": ""}, "lines": {"query": {"edges": [{"node": {"purchaseOrderLine__receivedQuantity": {"receivedQuantity": "5"}, "purchaseOrderLine": {"purchaseUnit__id": {"decimalDigits": 2, "id": "KILOGRAM"}, "purchaseReceiptLine__document__number": {"document": {"number": "PR200001"}, "completed": false}, "purchaseOrderLine__document__number": {"document": {"number": "PO24"}, "document__number": {"number": "PO24", "status": "inProgress"}, "quantityToReceive": "0", "quantity": "10", "receivedQuantity": "15", "status": "inProgress"}, "receivedQuantity": "5"}, "currency__id": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "stockUnit__id": {"id": "GRAM", "name": "Gram", "decimalDigits": 2}, "purchaseUnit__id": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "item__name": {"stockUnit__id": {"name": "Gram", "id": "GRAM"}, "name": "A box of muesli", "id": "<PERSON><PERSON><PERSON>", "description": "A 500g box of muesli", "lotManagement": "lotManagement"}, "status": "draft", "lineReturnStatus": "notReturned", "lineInvoiceStatus": "notInvoiced", "itemDescription": "A 500g box of muesli", "quantity": "5", "quantityInStockUnit": "5000", "unitToStockUnitConversionFactor": "1000", "origin": "purchaseOrder", "grossPrice": "10", "discount": "0", "charge": "0", "netPrice": "10", "amountExcludingTax": "50", "amountExcludingTaxInCompanyCurrency": "50.1", "priceOrigin": null, "jsonStockDetails": "{}", "storedAttributes": null, "storedDimensions": null, "completed": false}}]}}, "carrier": null}}}}}