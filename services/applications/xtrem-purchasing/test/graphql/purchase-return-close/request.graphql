mutation {
    xtremPurchasing {
        purchaseReturn {
            update(
                data: {
                    _id: "#RET033"
                    lines: [{ _action: "update", _sortValue: "10", status: "closed" }]
                    approvalStatus: "approved"
                    status: "closed"
                    shippingStatus: "notShipped"
                }
            ) {
                status
                approvalStatus
                shippingStatus
                allocationStatus
                lines {
                    query {
                        edges {
                            node {
                                _id
                                origin
                                status
                                approvalStatus
                                shippedStatus
                                quantity
                                grossPrice
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                amountExcludingTax
                                priceOrigin
                                storedAttributes
                                storedDimensions
                                quantityAllocated
                                allocationStatus
                            }
                        }
                    }
                }
            }
        }
    }
}
