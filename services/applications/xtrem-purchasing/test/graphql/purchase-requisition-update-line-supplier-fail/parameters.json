{"Update REQ7 - add the supplier on the first line": {"executionMode": "skip", "comments": " need a decision if we want to allow this or not", "input": {"properties": {"_id": "#REQ7", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase requisition is approved. You cannot add or delete a line. You can only edit certain fields.", "path": ["lines", "5062"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not updated.", "path": ["xtremPurchasing", "purchaseRequisition", "update"]}]}}, "Update REQ8 - add the supplier on the first line": {"executionMode": "skip", "comments": " need a decision if we want to allow this or not", "input": {"properties": {"_id": "#REQ8", "lines": [{"_action": "update", "_sortValue": "10", "supplier": "#700"}]}}, "output": {"errors": [{"message": "The record was not updated.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseRequisition", "update"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "path": ["unit"], "message": "PurchaseRequisitionLine.unit: cannot set value on frozen property"}]}}]}}}