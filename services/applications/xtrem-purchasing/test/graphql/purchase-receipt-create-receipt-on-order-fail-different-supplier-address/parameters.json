{"Create purchase receipt - fails (different supplier address - name) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC FAIL", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "quantity": 7, "quantityInStockUnit": 7000, "unit": "#KILOGRAM", "stockUnit": "#GRAM", "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": "#PO15|2700"}}]}, {"item": "#Chemical C", "grossPrice": 100, "quantity": 8, "quantityInStockUnit": 8000, "unit": "#KILOGRAM", "stockUnit": "#GRAM", "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": "#PO16|2800"}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - fails (different supplier address - addressLine1) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 Allée du Tenailler  FAIL", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - fails (different supplier address - addressLine2) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": " FAIL", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - fails (different supplier address - city) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": " FAIL", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - fails (different supplier address - region) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF FAIL", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - fails (different supplier address - postcode) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650 FAIL", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - fails (different supplier address - locationPhoneNumber) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874 FAIL", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - fails (different supplier address - country) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#US"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n supplier address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}}