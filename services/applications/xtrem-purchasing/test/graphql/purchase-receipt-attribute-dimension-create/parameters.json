{"Create purchase receipt - attributes project": {"executionMode": "normal", "variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}"}, "output": {"create": {"number": "PR210001", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"Chemical C\",\"supplier\":\"LECLERC\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}}}, "envConfigs": {"today": "2021-03-02"}}, "Create purchase receipt - attributes and dimensions": {"executionMode": "normal", "variables": {"storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}, "output": {"create": {"number": "PR210001", "lines": {"query": {"edges": [{"node": {"computedAttributes": "{\"financialSite\":\"US001\",\"stockSite\":\"US001\",\"item\":\"Chemical C\",\"supplier\":\"LECLERC\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}", "storedDimensions": "{\"dimensionType03\":\"DIMTYPE1VALUE1\",\"dimensionType04\":\"DIMTYPE2VALUE2\"}"}}]}}}}, "envConfigs": {"today": "2021-03-02"}}}