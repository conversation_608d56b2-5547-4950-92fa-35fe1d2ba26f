mutation purchaseOrder($storedAttributes: Json, $storedDimensions: Json) {
  xtremPurchasing {
    purchaseReceipt {
      create(
        data: {
          site: "#US001"
          businessRelation: "#LECLERC"
          currency: "#EUR"
          date: "2021-03-02"
          lines: [
            {
              item: "#Chemical C"
              quantity: 10
              unit: "#KILOGRAM"
              grossPrice: 100
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
              jsonStockDetails: "[{\"item\": \"#Chemical C\", \"site\": 3, \"location\": 1, \"stockUnit\": \"#GRAM\", \"status\": \"#A\", \"quantityInStockUnit\": 10}]"
            }
          ]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
