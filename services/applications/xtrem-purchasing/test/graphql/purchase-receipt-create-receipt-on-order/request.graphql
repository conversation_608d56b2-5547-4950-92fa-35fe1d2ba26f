mutation {
    xtremPurchasing {
        purchaseReceipt {
            create(
                data: {
                    site: "#US001"
                    businessRelation: "#LECLERC"
                    currency: "#USD"
                    receivingAddress: {
                        name: "Main address"
                        addressLine1: "1 Some street"
                        addressLine2: "Some building"
                        city: "Somewhere"
                        region: "Gauteng"
                        postcode: "0123"
                        locationPhoneNumber: "+***********"
                        country: "#ZA"
                    }
                    supplierAddress: {
                        name: "COSMETIC"
                        addressLine1: "26 Allée du Tenailler"
                        addressLine2: ""
                        city: ""
                        region: "HDF"
                        postcode: "59650"
                        locationPhoneNumber: "0695125874"
                        country: "#FR"
                    }
                    lines: [
                        {
                            _action: "create"
                            item: "#Muesli"
                            charge: 0
                            discount: 0
                            grossPrice: 100
                            status: "draft"
                            netPrice: 100
                            unit: "#KILOGRAM"
                            unitToStockUnitConversionFactor: 1000
                            quantity: 8
                            stockUnit: "#GRAM"
                        }
                        {
                            _action: "create"
                            item: "#Chemical C"
                            charge: 0
                            discount: 0
                            grossPrice: 10
                            itemDescription: "muesli po line1"
                            netPrice: 10
                            unit: "#KILOGRAM"
                            unitToStockUnitConversionFactor: 1000
                            quantity: 7
                            stockUnit: "#GRAM"
                        }
                    ]
                }
            ) {
                number
                site {
                    primaryAddress {
                        country {
                            name
                        }
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                    name
                    isLocationManaged
                    businessEntity {
                        addresses {
                            query {
                                edges {
                                    node {
                                        country {
                                            name
                                        }
                                        isActive
                                        name
                                        addressLine1
                                        addressLine2
                                        city
                                        region
                                        postcode
                                        locationPhoneNumber
                                    }
                                }
                            }
                        }
                    }
                }
                receivingAddress {
                    country {
                        name
                    }
                    name
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                stockSite {
                    primaryAddress {
                        country {
                            name
                        }
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                    name
                    isLocationManaged
                    businessEntity {
                        addresses {
                            query {
                                edges {
                                    node {
                                        country {
                                            name
                                        }
                                        isActive
                                        name
                                        addressLine1
                                        addressLine2
                                        city
                                        region
                                        postcode
                                        locationPhoneNumber
                                    }
                                }
                            }
                        }
                    }
                }
                supplier {
                    businessEntity {
                        currency {
                            name
                            decimalDigits
                            symbol
                        }
                        name
                        addresses {
                            query {
                                edges {
                                    node {
                                        country {
                                            name
                                        }
                                        isActive
                                        name
                                        addressLine1
                                        addressLine2
                                        city
                                        region
                                        postcode
                                        locationPhoneNumber
                                    }
                                }
                            }
                        }
                    }
                    primaryAddress {
                        country {
                            name
                        }
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                    returnToAddress {
                        country {
                            name
                        }
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                }
                supplierAddress {
                    country {
                        name
                    }
                    name
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                returnAddress {
                    country {
                        name
                    }
                    name
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                currency {
                    name
                    decimalDigits
                    rounding
                    symbol
                }
                date
                supplierDocumentNumber
                totalAmountExcludingTax
                status
                returnStatus
                invoiceStatus
                lines {
                    query {
                        edges {
                            node {
                                purchaseOrderLine {
                                    receivedQuantity
                                }
                                purchaseOrderLine {
                                    unit {
                                        decimalDigits
                                    }
                                    purchaseOrderLine {
                                        document {
                                            number
                                        }
                                        document {
                                            number
                                            status
                                        }
                                        quantityToReceive
                                        quantity
                                        receivedQuantity
                                        status
                                    }
                                    receivedQuantity
                                }
                                currency {
                                    name
                                    decimalDigits
                                    rounding
                                    symbol
                                }
                                stockUnit {
                                    name
                                    decimalDigits
                                }
                                unit {
                                    name
                                    decimalDigits
                                }
                                item {
                                    stockUnit {
                                        name
                                    }
                                    name
                                    description
                                    lotManagement
                                }
                                status
                                lineReturnStatus
                                lineInvoiceStatus
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                origin
                                grossPrice
                                discount
                                charge
                                netPrice
                                amountExcludingTax
                                priceOrigin
                                storedAttributes
                                storedDimensions
                            }
                        }
                    }
                }
                carrier {
                    businessEntity {
                        name
                    }
                }
            }
        }
    }
}
