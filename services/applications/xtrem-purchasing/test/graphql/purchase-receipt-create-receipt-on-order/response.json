{"data": {"xtremPurchasing": {"purchaseReceipt": {"create": {"number": "PR220001", "site": {"primaryAddress": {"country": {"name": "United States of America"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********"}, "name": "Chem. Atlanta", "isLocationManaged": true, "businessEntity": {"addresses": {"query": {"edges": [{"node": {"country": {"name": "United States of America"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********"}}, {"node": {"country": {"name": "United States of America"}, "isActive": true, "name": "US001 Warehouse", "addressLine1": "Warehouse address line", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": ""}}]}}}}, "receivingAddress": {"country": {"name": "South Africa"}, "name": "Main address", "locationPhoneNumber": "+***********", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123"}, "stockSite": {"primaryAddress": {"country": {"name": "United States of America"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********"}, "name": "Chem. Atlanta", "isLocationManaged": true, "businessEntity": {"addresses": {"query": {"edges": [{"node": {"country": {"name": "United States of America"}, "isActive": true, "name": "US001", "addressLine1": "First address line site", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": "+***********"}}, {"node": {"country": {"name": "United States of America"}, "isActive": true, "name": "US001 Warehouse", "addressLine1": "Warehouse address line", "addressLine2": "", "city": "", "region": "MD", "postcode": "20746", "locationPhoneNumber": ""}}]}}}}, "supplier": {"businessEntity": {"currency": {"name": "Euro", "decimalDigits": 2, "symbol": "€"}, "name": "LECLERC supermarket", "addresses": {"query": {"edges": [{"node": {"country": {"name": "France"}, "isActive": true, "name": "Store", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100", "locationPhoneNumber": "+***********"}}, {"node": {"country": {"name": "France"}, "isActive": true, "name": "E.LECLERC Cran <PERSON>", "addressLine1": "66 route des Creuses", "addressLine2": "", "city": "", "region": "Haute Savoie", "postcode": "74960", "locationPhoneNumber": "+330450693046"}}]}}}, "primaryAddress": {"country": {"name": "France"}, "isActive": true, "name": "Store", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100", "locationPhoneNumber": "+***********"}, "returnToAddress": {"country": {"name": "France"}, "name": "Store", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100", "locationPhoneNumber": "+***********"}}, "supplierAddress": {"country": {"name": "France"}, "name": "COSMETIC", "locationPhoneNumber": "0695125874", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650"}, "returnAddress": {"country": {"name": "France"}, "name": "Store", "locationPhoneNumber": "+***********", "addressLine1": "1 First avenue", "addressLine2": "", "city": "", "region": "First region", "postcode": "0100"}, "currency": {"name": "US Dollar", "decimalDigits": 2, "rounding": 0, "symbol": "$"}, "date": "2022-01-27", "supplierDocumentNumber": "", "totalAmountExcludingTax": "870", "status": "draft", "returnStatus": "notReturned", "invoiceStatus": "notInvoiced", "lines": {"query": {"edges": [{"node": {"purchaseOrderLine": null, "currency": {"name": "US Dollar", "decimalDigits": 2, "rounding": 0, "symbol": "$"}, "stockUnit": {"name": "Gram", "decimalDigits": 2}, "unit": {"name": "Kilogram", "decimalDigits": 2}, "item": {"stockUnit": {"name": "Gram"}, "name": "A box of muesli", "description": "A 500g box of muesli", "lotManagement": "lotManagement"}, "status": "draft", "lineReturnStatus": "notReturned", "lineInvoiceStatus": "notInvoiced", "itemDescription": "A 500g box of muesli", "quantity": "8", "quantityInStockUnit": "8000", "unitToStockUnitConversionFactor": "1000", "origin": "direct", "grossPrice": "100", "discount": "0", "charge": "0", "netPrice": "100", "amountExcludingTax": "800", "priceOrigin": null, "storedAttributes": "{\"project\":\"AttPROJ\",\"task\":\"Task1\"}", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}, {"node": {"purchaseOrderLine": null, "currency": {"name": "US Dollar", "decimalDigits": 2, "rounding": 0, "symbol": "$"}, "stockUnit": {"name": "Gram", "decimalDigits": 2}, "unit": {"name": "Kilogram", "decimalDigits": 2}, "item": {"stockUnit": {"name": "Gram"}, "name": "Chemical description C", "description": "Chemical contains klm", "lotManagement": "notManaged"}, "status": "draft", "lineReturnStatus": "notReturned", "lineInvoiceStatus": "notInvoiced", "itemDescription": "muesli po line1", "quantity": "7", "quantityInStockUnit": "7000", "unitToStockUnitConversionFactor": "1000", "origin": "direct", "grossPrice": "10", "discount": "0", "charge": "0", "netPrice": "10", "amountExcludingTax": "70", "priceOrigin": null, "storedAttributes": "{\"project\":\"AttPROJ\",\"task\":\"Task1\"}", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\"}"}}]}}, "carrier": null}}}}}