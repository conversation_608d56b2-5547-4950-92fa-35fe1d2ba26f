mutation {
  xtremPurchasing {
    purchaseInvoice {
      create(data:{{inputParameters}} ) {
                number
                matchingStatus
                dueDate
                totalAmountIncludingTax
                calculatedTotalAmountExcludingTax
                displayStatus
                matchingUser {
                    firstName
                }
                returnLinkedAddress {
                    name
                }
                invoiceDate
                paymentTerm {
                    name
                }
                totalAmountExcludingTax
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                billByLinkedAddress {
                    name
                    addressLine1
                }
                billByAddress {
                    name
                    addressLine1
                }
                billByContact {
                    firstName
                    lastName
                }
                payToSupplier {
                    businessEntity {
                        name
                    }
                }
                payToLinkedAddress {
                    name
                    addressLine1
                    addressLine2
                }
                payToAddress {
                    name
                    addressLine1
                    addressLine2
                }
                payToContact {
                    firstName
                    lastName
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                site {
                    id
                }
                currency {
                    id
                }
                text {
                    value
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                signedQuantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                signedAmountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                signedAmountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                landedCost {
                                    costAmountToAllocate
                                    costAmountToAllocateInCompanyCurrency
                                    allocationRule
                                    allocationRuleUnit {
                                        id
                                    }
                                    allocationMethod
                                }
                            }
                        }
                    }
                }
      }
    }
  }
}
