{"Purchase invoice creation - minimal - item must not be stock managed": {"executionMode": "normal", "envConfigs": {"today": "2020-08-10"}, "input": {"properties": {"site": "#US001", "billBySupplier": "#LECLERC", "currency": "#EUR", "supplierDocumentDate": "2020-08-01", "lines": [{"item": "#NonStockManagedItem", "quantity": 10, "unit": "#KILOGRAM", "grossPrice": 100}]}}, "output": {"number": "PI200001", "matchingStatus": "variance", "dueDate": "2020-08-31", "totalAmountIncludingTax": "0", "calculatedTotalAmountExcludingTax": "1000", "displayStatus": "variance", "matchingUser": null, "returnLinkedAddress": {"name": "Store"}, "invoiceDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "0", "billBySupplier": {"businessEntity": {"id": "LECLERC"}}, "billByLinkedAddress": {"name": "Store", "addressLine1": "1 First avenue"}, "billByAddress": {"name": "Store", "addressLine1": "1 First avenue"}, "billByContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "payToSupplier": {"businessEntity": {"name": "LECLERC supermarket"}}, "payToLinkedAddress": {"name": "Store", "addressLine1": "1 First avenue", "addressLine2": ""}, "payToAddress": {"name": "Store", "addressLine1": "1 First avenue", "addressLine2": ""}, "payToContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "site": {"id": "US001"}, "text": {"value": ""}, "lines": {"query": {"edges": [{"node": {"item": {"id": "NonStockManagedItem"}, "quantity": "10", "signedQuantity": "10", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "10000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "1000", "signedAmountExcludingTax": "1000", "amountExcludingTaxInCompanyCurrency": "1002", "signedAmountExcludingTaxInCompanyCurrency": "1002", "text": {"value": ""}, "landedCost": null}}]}}}}, "Purchase invoice creation ": {"executionMode": "normal", "input": {"properties": {"site": "#US001", "billBySupplier": "#LECLERC", "currency": "#EUR", "invoiceDate": "2020-08-10", "text": {"value": "value"}, "matchingStatus": "variance", "totalAmountExcludingTax": 30, "totalTaxAmount": 12, "lines": [{"item": "#Chemical C", "quantity": 1, "unit": "#KILOGRAM", "grossPrice": 100, "purchaseReceiptLine": {"purchaseReceiptLine": "#PR4|1"}}], "matchingUser": "#<EMAIL>"}}, "output": {"number": "PI200001", "matchingStatus": "variance", "dueDate": "2020-09-09", "totalAmountIncludingTax": "42", "calculatedTotalAmountExcludingTax": "100", "displayStatus": "variance", "matchingUser": {"firstName": "Demo"}, "returnLinkedAddress": {"name": "Store"}, "invoiceDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "30", "billBySupplier": {"businessEntity": {"id": "LECLERC"}}, "billByLinkedAddress": {"name": "Store", "addressLine1": "1 First avenue"}, "billByAddress": {"name": "Store", "addressLine1": "1 First avenue"}, "billByContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "payToSupplier": {"businessEntity": {"name": "LECLERC supermarket"}}, "payToLinkedAddress": {"name": "Store", "addressLine1": "1 First avenue", "addressLine2": ""}, "payToAddress": {"name": "Store", "addressLine1": "1 First avenue", "addressLine2": ""}, "payToContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "site": {"id": "US001"}, "text": {"value": "value"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "Chemical C"}, "quantity": "1", "signedQuantity": "1", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "1000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "100", "signedAmountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100.2", "signedAmountExcludingTaxInCompanyCurrency": "100.2", "text": {"value": ""}, "landedCost": null}}]}}}, "envConfigs": {"today": "2020-08-10", "testActiveServiceOptions": ["landedCostOption"]}}}