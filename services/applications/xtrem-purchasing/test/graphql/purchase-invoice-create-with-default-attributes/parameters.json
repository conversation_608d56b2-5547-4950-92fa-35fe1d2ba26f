{"Create purchase invoice with defaulted attributes and dimensions": {"input": {"properties": {"site": "#DEP1-S01", "billBySupplier": "#US017", "currency": "#EUR", "invoiceDate": "2020-08-10", "text": {"value": "value"}, "matchingStatus": "variance", "totalAmountExcludingTax": 30, "totalTaxAmount": 12, "lines": [{"item": "#SalesItem81", "quantity": 1, "unit": "#LITER", "grossPrice": 100, "recipientSite": "#ETS1-S01"}], "matchingUser": "#<EMAIL>"}}, "output": {"create": {"number": "PI200001", "matchingStatus": "variance", "dueDate": "2020-09-09", "totalAmountIncludingTax": "42", "calculatedTotalAmountExcludingTax": "100", "invoiceDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "30", "billBySupplier": {"businessEntity": {"id": "US017"}}, "payToSupplier": {"businessEntity": {"name": "Siège social S01 PARIS"}}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "EUR"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1", "companyFxRateDivisor": "1", "site": {"id": "DEP1-S01"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "SalesItem81"}, "quantity": "1", "unit": {"id": "LITER"}, "grossPrice": "100", "quantityInStockUnit": "1", "stockUnit": {"id": "LITER"}, "amountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\",\"dimensionType03\":\"WAREHOUSE\",\"dimensionType04\":\"DIMTYPE2VALUE2\",\"dimensionType05\":\"COMMERCIAL\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}"}}]}}}}, "executionMode": "normal", "envConfigs": {"today": "2020-08-10", "testActiveServiceOptions": ["landedCostOption"]}}, "Create purchase invoice with empty attributes and dimensions (not defaulted)": {"input": {"properties": {"site": "#DEP1-S01", "billBySupplier": "#US017", "currency": "#EUR", "invoiceDate": "2020-08-10", "text": {"value": "value"}, "matchingStatus": "variance", "totalAmountExcludingTax": 30, "totalTaxAmount": 12, "lines": [{"item": "#SalesItem81", "quantity": 1, "unit": "#LITER", "grossPrice": 100, "recipientSite": "#ETS1-S01", "storedDimensions": "{}", "storedAttributes": "{}"}], "matchingUser": "#<EMAIL>"}}, "output": {"create": {"number": "PI200001", "matchingStatus": "variance", "dueDate": "2020-09-09", "totalAmountIncludingTax": "42", "calculatedTotalAmountExcludingTax": "100", "invoiceDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "30", "billBySupplier": {"businessEntity": {"id": "US017"}}, "payToSupplier": {"businessEntity": {"name": "Siège social S01 PARIS"}}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "EUR"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1", "companyFxRateDivisor": "1", "site": {"id": "DEP1-S01"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "SalesItem81"}, "quantity": "1", "unit": {"id": "LITER"}, "grossPrice": "100", "quantityInStockUnit": "1", "stockUnit": {"id": "LITER"}, "amountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100", "storedDimensions": "{}", "storedAttributes": "{}"}}]}}}}, "executionMode": "normal", "envConfigs": {"today": "2020-08-10", "testActiveServiceOptions": ["landedCostOption"]}}}