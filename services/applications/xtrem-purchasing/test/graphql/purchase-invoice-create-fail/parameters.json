{"Purchase invoice creation - fail": {"executionMode": "normal", "input": {"properties": {"number": "PI1", "site": "#US001", "billBySupplier": "#LECLERC", "currency": "#EUR", "invoiceDate": "3020-10-08", "text": {"value": "value"}, "totalAmountExcludingTax": 4, "supplierDocumentNumber": "Invoice-Sage2", "lines": [{"item": "#Chemical C", "quantity": 1, "unit": "#KILOGRAM", "grossPrice": 100, "purchaseReceiptLine": {"purchaseReceiptLine": "#PR4|1"}}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseInvoice", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["invoiceDate"], "message": "The date cannot be later than the document date."}, {"severity": 3, "path": ["supplierDocumentDate"], "message": "The date cannot be later than the current date."}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000002"], "severity": 2}]}}]}}, "Create purchase invoice with data from stock transfer order": {"executionMode": "normal", "input": {"properties": {"number": "test", "site": "#CAS01", "billBySupplier": "#CAS02", "lines": [{"item": "#Milk", "quantity": "10"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseInvoice", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["billBySupplier"], "message": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value."}, {"severity": 3, "path": ["lines", "-1000000002", "item"], "message": "The record is not valid. You need to select a different record."}, {"severity": 2, "path": ["lines", "-1000000002"], "message": "The item: Milk, is not managed for the supplier: CAS02."}]}}]}}}