mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            create(data: {{inputParameters}}) {
                number
                totalAmountIncludingTax
                creditMemoDate
                paymentTerm {
                    name
                }
                totalAmountExcludingTax
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                payToSupplier {
                    businessEntity {
                        name
                    }
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                site {
                    id
                }
                currency {
                    id
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                              	storedDimensions
                              	storedAttributes
                            }
                        }
                    }
                }
            }
        }
    }
}
