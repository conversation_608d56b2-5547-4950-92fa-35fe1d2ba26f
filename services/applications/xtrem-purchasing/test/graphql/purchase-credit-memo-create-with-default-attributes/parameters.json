{"Create purchase credit memo with defaulted attributes and dimensions": {"input": {"properties": {"site": "#DEP1-S01", "billBySupplier": "#US017", "currency": "#EUR", "creditMemoDate": "2020-08-10", "totalAmountExcludingTax": 100, "totalTaxAmount": 20, "reason": "#R1", "lines": [{"item": "#NonStockManagedItem", "quantity": 1, "unit": "#KILOGRAM", "grossPrice": 100, "recipientSite": "#ETS1-S01"}]}}, "output": {"create": {"number": "PC200001", "totalAmountIncludingTax": "120", "creditMemoDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "100", "billBySupplier": {"businessEntity": {"id": "US017"}}, "payToSupplier": {"businessEntity": {"name": "Siège social S01 PARIS"}}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "EUR"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1", "companyFxRateDivisor": "1", "site": {"id": "DEP1-S01"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "NonStockManagedItem"}, "quantity": "1", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "1000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\",\"dimensionType03\":\"WAREHOUSE\",\"dimensionType04\":\"DIMTYPE2VALUE2\",\"dimensionType05\":\"COMMERCIAL\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}"}}]}}}}, "executionMode": "normal", "envConfigs": {"today": "2020-08-10", "testActiveServiceOptions": ["landedCostOption"]}}, "Create purchase credit memo with empty attributes and dimensions (not defaulted)": {"input": {"properties": {"site": "#DEP1-S01", "billBySupplier": "#US017", "currency": "#EUR", "creditMemoDate": "2020-08-10", "totalAmountExcludingTax": 100, "totalTaxAmount": 20, "reason": "#R1", "lines": [{"item": "#NonStockManagedItem", "quantity": 1, "unit": "#KILOGRAM", "grossPrice": 100, "recipientSite": "#ETS1-S01", "storedDimensions": "{}", "storedAttributes": "{}"}]}}, "output": {"create": {"number": "PC200001", "totalAmountIncludingTax": "120", "creditMemoDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "100", "billBySupplier": {"businessEntity": {"id": "US017"}}, "payToSupplier": {"businessEntity": {"name": "Siège social S01 PARIS"}}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "EUR"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1", "companyFxRateDivisor": "1", "site": {"id": "DEP1-S01"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "NonStockManagedItem"}, "quantity": "1", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "1000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100", "storedDimensions": "{}", "storedAttributes": "{}"}}]}}}}, "executionMode": "normal", "envConfigs": {"today": "2020-08-10", "testActiveServiceOptions": ["landedCostOption"]}}}