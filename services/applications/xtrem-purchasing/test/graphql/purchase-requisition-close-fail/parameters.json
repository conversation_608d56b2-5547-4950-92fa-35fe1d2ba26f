{" Close fails : REQ1 (having status='draft')": {"input": {"purchaseRequisition": "#REQ1"}, "output": {"close": true, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval requisition.", "path": [], "severity": 4}]}, "locations": [{"column": 7, "line": 4}], "message": "Close failed.", "path": ["xtremPurchasing", "purchaseRequisition", "close"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Close fails : REQ3 (having approvalStatus='pendingApproval')": {"input": {"purchaseRequisition": "#REQ3"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval requisition.", "path": [], "severity": 4}]}, "locations": [{"column": 7, "line": 4}], "message": "Close failed.", "path": ["xtremPurchasing", "purchaseRequisition", "close"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Close fails : REQ12 (having status='closed')": {"input": {"purchaseRequisition": "#REQ12"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval requisition.", "path": [], "severity": 4}]}, "locations": [{"column": 7, "line": 4}], "message": "Close failed.", "path": ["xtremPurchasing", "purchaseRequisition", "close"]}]}, "envConfigs": {"today": "2020-08-10"}}}