{"Total allocated amount less than the landed cost amount to allocate": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"invoiceId": "#PI11"}, "output": {"errors": [{"message": "Post failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseInvoice", "post"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "The total allocated amount (€100) cannot be less than the landed cost amount to allocate (€1100).", "path": []}]}}], "data": {"xtremPurchasing": {"purchaseInvoice": {"post": null}}}}}, "Allocated amount must be greater than zero for each allocated document": {"executionMode": "normal", "envConfigs": {"testActiveServiceOptions": ["landedCostOption"]}, "input": {"invoiceId": "#PI15"}, "output": {"errors": [{"message": "Post failed.", "locations": [{"line": 4, "column": 7}], "path": ["xtremPurchasing", "purchaseInvoice", "post"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 4, "message": "You need to enter an allocated amount greater than zero for each allocated document.", "path": []}]}}], "data": {"xtremPurchasing": {"purchaseInvoice": {"post": null}}}}}}