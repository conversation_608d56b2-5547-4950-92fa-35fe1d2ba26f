mutation {
    xtremPurchasing {
        purchaseOrder {
            create(
                data: {
                    paymentTerm: "#TEST_NET_30_SUPPLIER"
                    changeRequestedDescription: null
                    text: null
                    currency: "#EUR"
                    supplierAddress: {
                        country: "#FR"
                        name: "960 street 44"
                        locationPhoneNumber: "+***********"
                        addressLine1: "960 street 44"
                        addressLine2: null
                        city: null
                        region: "First region"
                        postcode: "20746"
                    }
                    supplierLinkedAddress: "#US017|1300"
                    siteAddress: {
                        country: "#FR"
                        name: "Siege Social S1 Paris"
                        locationPhoneNumber: "+***********"
                        addressLine1: "1 Avenue des champs elisés"
                        addressLine2: null
                        city: "PARIS"
                        region: null
                        postcode: "75001"
                    }
                    businessEntityAddress: "#ETS1-S01|510100"
                    stockSite: "#ETS1-S01"
                    orderDate: "2022-04-15"
                    fxRateDate: "2022-04-15"
                    status: "draft"
                    approvalStatus: "draft"
                    receiptStatus: "notReceived"
                    invoiceStatus: "notInvoiced"
                    taxCalculationStatus: "notDone"
                    isSent: false
                    totalTaxAmount: 52.2103954
                    totalTaxAmountAdjusted: 52.2103954
                    site: "#ETS1-S01"
                    businessRelation: "#US017"
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            changeRequestedDescription: null
                            charge: 0
                            discount: 12.3
                            expectedReceiptDate: "2022-04-16"
                            grossPrice: 12.73
                            item: "#StockItem29"
                            itemDescription: null
                            amountIncludingTax: 149.9904
                            amountIncludingTaxInCompanyCurrency: 149.99
                            lineInvoiceStatus: "notInvoiced"
                            lineReceiptStatus: "notReceived"
                            status: "draft"
                            netPrice: 11.16421
                            priceOrigin: "manual"
                            unit: "#GRAM"
                            unitToStockUnitConversionFactor: 1
                            quantity: 11.2
                            site: "#ETS1-S01"
                            stockSite: "#ETS1-S01"
                            stockSiteLinkedAddress: "#ETS1-S01|510100"
                            stockSiteAddress: {
                                country: "#FR"
                                name: "Siege Social S1 Paris"
                                locationPhoneNumber: "+***********"
                                addressLine1: "1 Avenue des champs elisés"
                                addressLine2: null
                                city: "PARIS"
                                region: null
                                postcode: "75001"
                            }
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                            taxAmount: 24.9984
                            taxAmountAdjusted: 24.9984
                            uiTaxes: "{\"taxEngine\":\"genericTaxCalculation\",\"taxes\":[{\"_id\":\"-1\",\"currency\":{\"_id\":\"2\",\"name\":\"Euro\"},\"deductibleTaxAmount\":124.992,\"deductibleTaxRate\":100,\"exemptAmount\":\"0\",\"isReverseCharge\":false,\"nonTaxableAmount\":\"0\",\"tax\":\"VAT on receipts\",\"taxAmount\":24.9984,\"taxAmountAdjusted\":24.9984,\"taxCategory\":\"Value added tax\",\"taxCategoryReference\":{\"_id\":\"1\",\"name\":\"Value added tax\",\"isTaxMandatory\":true},\"taxRate\":20,\"taxReference\":\"FR005\",\"taxableAmount\":124.992}]}"
                        }
                        {
                            _action: "create"
                            _id: "-2"
                            changeRequestedDescription: null
                            charge: 32.3
                            discount: 0
                            expectedReceiptDate: "2022-04-16"
                            grossPrice: 4.82
                            item: "#StockItem27"
                            itemDescription: null
                            amountIncludingTax: 122.7074
                            amountIncludingTaxInCompanyCurrency: 122.71
                            lineInvoiceStatus: "notInvoiced"
                            lineReceiptStatus: "notReceived"
                            status: "draft"
                            netPrice: 6.38
                            priceOrigin: "manual"
                            unit: "#GRAM"
                            unitToStockUnitConversionFactor: 1
                            quantity: 18.23
                            site: "#ETS1-S01"
                            stockSite: "#ETS1-S01"
                            stockSiteLinkedAddress: "#ETS1-S01|510100"
                            stockSiteAddress: {
                                country: "#FR"
                                name: "Siege Social S1 Paris"
                                locationPhoneNumber: "+***********"
                                addressLine1: "1 Avenue des champs elisés"
                                addressLine2: null
                                city: "PARIS"
                                region: null
                                postcode: "75001"
                            }
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                            taxAmount: 6.4
                            taxAmountAdjusted: 6.4
                            uiTaxes: "{\"taxes\":[{\"_id\":\"-1\",\"taxCategoryReference\":{\"_id\":\"1\",\"name\":\"Value added tax\",\"isTaxMandatory\":true},\"taxCategory\":\"VAT\",\"taxRate\":\"5.5\",\"taxAmount\":\"6.4\",\"nonTaxableAmount\":\"0\",\"exemptAmount\":\"0\",\"taxableAmount\":\"116.31\",\"currency\":{\"_id\":\"2\",\"name\":\"Euro\"},\"deductibleTaxRate\":\"100\",\"deductibleTaxAmount\":\"6.4\",\"isReverseCharge\":false,\"taxAmountAdjusted\":\"6.4\",\"taxReference\":\"FR002\",\"tax\":\"VAT reduced rate\"}],\"taxEngine\":\"genericTaxCalculation\"}"
                        }
                        {
                            _action: "create"
                            _id: "-3"
                            changeRequestedDescription: null
                            charge: 0
                            discount: 0
                            expectedReceiptDate: "2022-04-16"
                            grossPrice: 78.22
                            item: "#StockItem27"
                            itemDescription: null
                            amountIncludingTax: 1011.8593954
                            amountIncludingTaxInCompanyCurrency: 1011.86
                            lineInvoiceStatus: "notInvoiced"
                            lineReceiptStatus: "notReceived"
                            status: "draft"
                            netPrice: 78.22
                            priceOrigin: "manual"
                            unit: "#GRAM"
                            unitToStockUnitConversionFactor: 1
                            quantity: 12.67
                            site: "#ETS1-S01"
                            stockSite: "#ETS1-S01"
                            stockSiteLinkedAddress: "#ETS1-S01|510100"
                            stockSiteAddress: {
                                country: "#FR"
                                name: "Siege Social S1 Paris"
                                locationPhoneNumber: "+***********"
                                addressLine1: "1 Avenue des champs elisés"
                                addressLine2: null
                                city: "PARIS"
                                region: null
                                postcode: "75001"
                            }
                            stockUnit: "#GRAM"
                            storedAttributes: null
                            storedDimensions: null
                            taxAmount: 20.8119954
                            taxAmountAdjusted: 20.8119954
                            uiTaxes: "{\"taxEngine\":\"genericTaxCalculation\",\"taxes\":[{\"_id\":\"-1\",\"currency\":{\"_id\":\"2\",\"name\":\"Euro\"},\"deductibleTaxAmount\":991.0474,\"deductibleTaxRate\":100,\"exemptAmount\":\"0\",\"isReverseCharge\":false,\"nonTaxableAmount\":\"0\",\"tax\":\"VAT super reduced rate\",\"taxAmount\":20.8119954,\"taxAmountAdjusted\":20.8119954,\"taxCategory\":\"Value added tax\",\"taxCategoryReference\":{\"_id\":\"1\",\"name\":\"Value added tax\",\"isTaxMandatory\":true},\"taxRate\":2.1,\"taxReference\":\"FR003\",\"taxableAmount\":991.0474}]}"
                        }
                    ]
                }
            ) {
                site {
                    businessEntity {
                        id
                    }
                    purchaseOrderSubstituteApprover {
                        email
                        firstName
                        lastName
                        displayName
                    }
                    purchaseOrderDefaultApprover {
                        email
                        firstName
                        lastName
                        displayName
                    }
                    legalCompany {
                        currency {
                            name
                            id
                            symbol
                            decimalDigits
                        }
                        name
                        id
                        taxEngine
                    }
                    name
                    id
                    isPurchase
                    isInventory
                }
                stockSite {
                    businessEntity {
                        id
                    }
                    name
                    id
                    isPurchase
                    isInventory
                }
                taxEngine
                supplier {
                    businessEntity {
                        currency {
                            name
                            id
                            decimalDigits
                            symbol
                        }
                        name
                        id
                    }
                    businessEntity {
                        id
                    }
                }
                siteAddress {
                    country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                businessEntityAddress {
                    country {
                        id
                        name
                        regionLabel
                        zipLabel
                    }
                    isActive
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                supplierLinkedAddress {
                    name
                    businessEntity {
                        id
                    }
                    country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    isActive
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                supplierAddress {
                    country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                orderDate
                currency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                companyCurrency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                fxRateDate
                companyFxRate
                companyFxRateDivisor
                rateDescription
                text {
                    value
                }
                status
                approvalStatus
                receiptStatus
                invoiceStatus
                taxCalculationStatus
                isSent
                changeRequestedDescription {
                    value
                }
                lines {
                    query(first: 10, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                stockSiteAddress {
                                    country {
                                        name
                                        id
                                        regionLabel
                                        zipLabel
                                    }
                                    name
                                    locationPhoneNumber
                                    addressLine1
                                    addressLine2
                                    city
                                    region
                                    postcode
                                    concatenatedAddress
                                }
                                stockSiteLinkedAddress {
                                    businessEntity {
                                        id
                                    }
                                    country {
                                        name
                                        id
                                        regionLabel
                                        zipLabel
                                    }
                                    isActive
                                    name
                                    addressLine1
                                    addressLine2
                                    city
                                    region
                                    postcode
                                    locationPhoneNumber
                                    concatenatedAddress
                                }
                                currency {
                                    name
                                    id
                                    decimalDigits
                                    rounding
                                    symbol
                                }
                                stockUnit {
                                    name
                                    decimalDigits
                                    id
                                }
                                unit {
                                    id
                                    name
                                    decimalDigits
                                }
                                stockSite {
                                    businessEntity {
                                        id
                                    }
                                    name
                                    id
                                    isPurchase
                                    isInventory
                                }
                                site {
                                    legalCompany {
                                        name
                                        id
                                    }
                                    name
                                    id
                                }
                                item {
                                    purchaseUnit {
                                        id
                                    }
                                    stockUnit {
                                        id
                                    }
                                    name
                                    id
                                }
                                status
                                taxCalculationStatus
                                itemDescription
                                quantity
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                grossPrice
                                discount
                                charge
                                netPrice
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                taxAmount
                                taxes {
                                    query {
                                        edges {
                                            node {
                                                taxRate
                                                taxCategoryReference {
                                                    name
                                                }
                                                taxReference {
                                                    name
                                                }
                                                deductibleTaxRate
                                                isReverseCharge
                                                taxCategory
                                                tax
                                                currency {
                                                    name
                                                    symbol
                                                }
                                                deductibleTaxAmount
                                                taxAmountAdjusted
                                                nonTaxableAmount
                                                taxAmount
                                                exemptAmount
                                                taxableAmount
                                            }
                                        }
                                    }
                                }
                                taxDate
                                amountIncludingTax
                                amountIncludingTaxInCompanyCurrency
                                priceOrigin
                                expectedReceiptDate
                                changeRequestedDescription
                                lineReceiptStatus
                                lineInvoiceStatus
                                origin
                                storedAttributes
                                storedDimensions
                                receivedQuantity
                                quantityToReceive
                                quantityToReceiveInStockUnit
                            }
                        }
                    }
                }
                paymentTerm {
                    name
                    description
                }
                totalAmountExcludingTax
                totalTaxAmount
                totalTaxAmountAdjusted
                totalAmountIncludingTax
                taxDetails: taxes {
                    query(first: 10, orderBy: "{\"taxCategory\":1,\"tax\":1}") {
                        edges {
                            node {
                                taxCategory
                                tax
                                taxableAmount
                                taxRate
                                taxAmount
                                isReverseCharge
                            }
                        }
                    }
                }
                totalAmountExcludingTaxInCompanyCurrency
                totalAmountIncludingTaxInCompanyCurrency
            }
        }
    }
}
