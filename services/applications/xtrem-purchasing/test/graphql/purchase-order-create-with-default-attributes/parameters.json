{"Create purchase order with defaulted attributes and dimensions": {"input": {"properties": {"site": "#DEP1-S01", "businessRelation": "#US017", "currency": "#EUR", "orderDate": "2020-08-10", "totalAmountExcludingTax": 30, "lines": [{"item": "#SalesItem81", "quantity": 1, "unit": "#LITER", "grossPrice": 100, "stockSite": "#ETS1-S01"}]}}, "output": {"create": {"number": "PO200001", "totalAmountIncludingTax": "50", "orderDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "30", "businessRelation": {"businessEntity": {"id": "US017"}}, "billBySupplier": {"businessEntity": {"name": "Siège social S01 PARIS"}}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "EUR"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1", "companyFxRateDivisor": "1", "site": {"id": "DEP1-S01"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "SalesItem81"}, "quantity": "1", "unit": {"id": "LITER"}, "grossPrice": "100", "quantityInStockUnit": "1", "stockUnit": {"id": "LITER"}, "amountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100", "remainingAmountToReceiveExcludingTax": "100", "remainingAmountToReceiveExcludingTaxInCompanyCurrency": "100", "storedDimensions": "{\"dimensionType01\":\"300\",\"dimensionType02\":\"CHANNELVALUE1\",\"dimensionType03\":\"WAREHOUSE\",\"dimensionType04\":\"DIMTYPE2VALUE2\",\"dimensionType05\":\"COMMERCIAL\"}", "storedAttributes": "{\"project\":\"AttPROJ\"}"}}]}}}}, "executionMode": "normal", "envConfigs": {"today": "2020-08-10", "testActiveServiceOptions": ["landedCostOption"]}}, "Create purchase order with empty attributes and dimensions (not defaulted)": {"input": {"properties": {"site": "#DEP1-S01", "businessRelation": "#US017", "currency": "#EUR", "orderDate": "2020-08-10", "totalAmountExcludingTax": 30, "lines": [{"item": "#SalesItem81", "quantity": 1, "unit": "#LITER", "grossPrice": 100, "stockSite": "#ETS1-S01", "storedDimensions": "{}", "storedAttributes": "{}"}]}}, "output": {"create": {"number": "PO200001", "totalAmountIncludingTax": "50", "orderDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "30", "businessRelation": {"businessEntity": {"id": "US017"}}, "billBySupplier": {"businessEntity": {"name": "Siège social S01 PARIS"}}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "EUR"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1", "companyFxRateDivisor": "1", "site": {"id": "DEP1-S01"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "SalesItem81"}, "quantity": "1", "unit": {"id": "LITER"}, "grossPrice": "100", "quantityInStockUnit": "1", "stockUnit": {"id": "LITER"}, "amountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100", "remainingAmountToReceiveExcludingTax": "100", "remainingAmountToReceiveExcludingTaxInCompanyCurrency": "100", "storedDimensions": "{}", "storedAttributes": "{}"}}]}}}}, "executionMode": "normal", "envConfigs": {"today": "2020-08-10", "testActiveServiceOptions": ["landedCostOption"]}}}