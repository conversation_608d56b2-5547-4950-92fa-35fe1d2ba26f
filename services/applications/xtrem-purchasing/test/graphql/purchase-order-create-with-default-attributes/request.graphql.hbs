mutation {
    xtremPurchasing {
        purchaseOrder {
            create(data: {{inputParameters}}) {
                number
                totalAmountIncludingTax
                orderDate
                paymentTerm {
                    name
                }
                totalAmountExcludingTax
                businessRelation {
                    businessEntity {
                        id
                    }
                }
                billBySupplier {
                    businessEntity {
                        name
                    }
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                site {
                    id
                }
                currency {
                    id
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                remainingAmountToReceiveExcludingTax
                                remainingAmountToReceiveExcludingTaxInCompanyCurrency
                              	storedDimensions
                              	storedAttributes
                            }
                        }
                    }
                }
            }
        }
    }
}
