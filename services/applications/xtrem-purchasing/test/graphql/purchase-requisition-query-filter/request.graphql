query {
    xtremPurchasing {
        purchaseRequisition {
            query(filter: "{number: 'REQ1'}") {
                edges {
                    node {
                        number
                        status
                        orderStatus
                        displayStatus
                        requestDate
                        internalNote {
                            value
                        }
                        changeRequestedDescription {
                            value
                        }
                        lines {
                            query {
                                edges {
                                    node {
                                        status
                                        lineOrderStatus
                                        item {
                                            id
                                        }
                                        requestedItemDescription
                                        quantity
                                        unit {
                                            id
                                        }
                                        changeRequestedDescription
                                        internalNote {
                                            value
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
