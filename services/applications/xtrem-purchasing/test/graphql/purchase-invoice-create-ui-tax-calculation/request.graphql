mutation {
    xtremPurchasing {
        purchaseInvoice {
            create(
                data: {
                    taxes: [
                        {
                            _action: "create"
                            _id: "-1"
                            isReverseCharge: false
                            tax: "Normal rate deductible on debits"
                            taxAmount: 52.8
                            taxAmountAdjusted: 60
                            taxCategory: "VAT"
                            taxCategoryReference: "#VAT"
                            taxRate: 20
                            taxReference: "#FR_TVA_NORMAL_DEDUCTIBLE_ON_DEBITS"
                            taxableAmount: 264
                        }
                        {
                            _action: "create"
                            _id: "-2"
                            isReverseCharge: false
                            tax: "Reduced rate collected on payment"
                            taxAmount: 48.4
                            taxAmountAdjusted: 48.4
                            taxCategory: "VAT"
                            taxCategoryReference: "#VAT"
                            taxRate: 20
                            taxReference: "#FR_TVA_REDUCED_COLLECTED_ON_PAYMENT"
                            taxableAmount: 242
                        }
                        {
                            _action: "create"
                            _id: "-3"
                            isReverseCharge: true
                            tax: "EEC VAT on receipts"
                            taxAmount: 26.4
                            taxAmountAdjusted: 26.4
                            taxCategory: "VAT"
                            taxCategoryReference: "#US_TVA"
                            taxRate: 20
                            taxReference: "#FR015"
                            taxableAmount: 132
                        }
                    ]
                    text: null
                    payToAddress: {
                        country: "#FR"
                        name: "960 street 44"
                        locationPhoneNumber: "+99999999999"
                        addressLine1: "960 street 44"
                        addressLine2: null
                        city: null
                        region: "First region"
                        postcode: "20746"
                    }
                    payToLinkedAddress: "#US017|1300"
                    payToSupplier: "#US017"
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            discount: null
                            grossPrice: 12
                            item: "#SalesItem81"
                            amountExcludingTax: 132
                            amountExcludingTaxInCompanyCurrency: 132
                            amountIncludingTax: 158.4
                            amountIncludingTaxInCompanyCurrency: 158.4
                            netPrice: 12
                            priceOrigin: "manual"
                            unit: "#LITER"
                            unitToStockUnitConversionFactor: 1
                            quantity: 11
                            recipientSite: "#ETS1-S01"
                            site: "#ETS1-S01"
                            stockUnit: "#LITER"
                            taxAmount: 26.4
                            taxAmountAdjusted: 26.4
                            uiTaxes: "{\"taxes\":[{\"taxCategoryReference\":{\"_id\":\"1\",\"name\":\"VAT\",\"isTaxMandatory\":true},\"taxCategory\":\"VAT\",\"taxRate\":\"20\",\"taxAmount\":\"26.4\",\"nonTaxableAmount\":\"0\",\"exemptAmount\":\"0\",\"taxableAmount\":\"132\",\"currency\":{\"_id\":\"2\",\"name\":\"Euro\"},\"deductibleTaxRate\":\"100\",\"deductibleTaxAmount\":\"26.4\",\"isReverseCharge\":false,\"taxAmountAdjusted\":\"26.4\",\"taxReference\":\"FR_TVA_NORMAL_DEDUCTIBLE_ON_DEBITS\",\"tax\":\"Normal rate deductible on debits\"}],\"taxEngine\":\"genericTaxCalculation\"}"
                        }
                        {
                            _action: "create"
                            _id: "-2"
                            discount: null
                            grossPrice: 11
                            item: "#NonStockManagedItem"
                            amountExcludingTax: 242
                            amountExcludingTaxInCompanyCurrency: 242
                            amountIncludingTax: 290.4
                            amountIncludingTaxInCompanyCurrency: 290.4
                            netPrice: 11
                            priceOrigin: "manual"
                            unit: "#KILOGRAM"
                            unitToStockUnitConversionFactor: 1000
                            quantity: 22
                            recipientSite: "#ETS1-S01"
                            site: "#ETS1-S01"
                            stockUnit: "#GRAM"
                            taxAmount: 48.4
                            taxAmountAdjusted: 48.4
                            uiTaxes: "{\"taxEngine\":\"genericTaxCalculation\",\"taxes\":[{\"_id\":\"-1\",\"currency\":{\"_id\":\"2\",\"name\":\"Euro\"},\"deductibleTaxAmount\":48.4,\"deductibleTaxRate\":100,\"exemptAmount\":\"0\",\"isReverseCharge\":false,\"nonTaxableAmount\":\"0\",\"tax\":\"VAT standard rate\",\"taxAmount\":48.4,\"taxAmountAdjusted\":48.4,\"taxCategory\":\"VAT\",\"taxCategoryReference\":{\"_id\":\"1\",\"isTaxMandatory\":true,\"name\":\"VAT\"},\"taxRate\":20,\"taxReference\":\"FR001\",\"taxableAmount\":242}]}"
                        }
                        {
                            _action: "create"
                            _id: "-3"
                            grossPrice: 12
                            item: "#SalesItem81"
                            amountExcludingTax: 132
                            amountExcludingTaxInCompanyCurrency: 132
                            amountIncludingTax: 158.4
                            amountIncludingTaxInCompanyCurrency: 158.4
                            netPrice: 12
                            priceOrigin: "manual"
                            unit: "#LITER"
                            unitToStockUnitConversionFactor: 1
                            quantity: 11
                            recipientSite: "#ETS1-S01"
                            site: "#ETS1-S01"
                            stockUnit: "#LITER"
                            taxAmount: 26.4
                            taxAmountAdjusted: 26.4
                            uiTaxes: "{\"taxes\":[{\"taxCategoryReference\":{\"_id\":\"1\",\"name\":\"VAT\",\"isTaxMandatory\":true},\"taxCategory\":\"VAT\",\"taxRate\":\"20\",\"taxAmount\":\"26.4\",\"nonTaxableAmount\":\"0\",\"exemptAmount\":\"0\",\"taxableAmount\":\"132\",\"currency\":{\"_id\":\"2\",\"name\":\"Euro\"},\"deductibleTaxRate\":\"100\",\"deductibleTaxAmount\":\"26.4\",\"isReverseCharge\":false,\"taxAmountAdjusted\":\"26.4\",\"taxReference\":\"FR_TVA_NORMAL_DEDUCTIBLE_ON_DEBITS\",\"tax\":\"Normal rate deductible on debits\"}],\"taxEngine\":\"genericTaxCalculation\"}"
                        }
                        {
                            _action: "create"
                            _id: "-4"
                            grossPrice: 11
                            item: "#NonStockManagedItem"
                            amountExcludingTax: 132
                            amountExcludingTaxInCompanyCurrency: 132
                            amountIncludingTax: 132
                            amountIncludingTaxInCompanyCurrency: 132
                            netPrice: 11
                            priceOrigin: "manual"
                            unit: "#KILOGRAM"
                            unitToStockUnitConversionFactor: 1000
                            quantity: 12
                            recipientSite: "#ETS1-S01"
                            site: "#ETS1-S01"
                            stockUnit: "#GRAM"
                            taxAmount: 26.4
                            taxAmountAdjusted: 26.4
                            uiTaxes: "{\"taxEngine\":\"genericTaxCalculation\",\"taxes\":[{\"_id\":\"-1\",\"currency\":{\"_id\":\"2\",\"name\":\"Euro\"},\"deductibleTaxAmount\":26.4,\"deductibleTaxRate\":100,\"exemptAmount\":\"0\",\"isReverseCharge\":true,\"nonTaxableAmount\":\"0\",\"tax\":\"EEC VAT on receipts\",\"taxAmount\":26.4,\"taxAmountAdjusted\":26.4,\"taxCategory\":\"VAT\",\"taxCategoryReference\":{\"_id\":\"1\",\"isTaxMandatory\":true,\"name\":\"VAT\"},\"taxRate\":20,\"taxReference\":\"FR015\",\"taxableAmount\":132}]}"
                        }
                    ]
                    pdfSupplierInvoice: null
                    billByLinkedAddress: "#US017|1300"
                    paymentTerm: "#TEST_NET_30_SUPPLIER"
                    currency: "#EUR"
                    billBySupplier: "#US017"
                    site: "#ETS1-S01"
                    billByAddress: {
                        country: "#FR"
                        name: "960 street 44"
                        locationPhoneNumber: "+99999999999"
                        addressLine1: "960 street 44"
                        addressLine2: null
                        city: null
                        region: "First region"
                        postcode: "20746"
                    }
                    number: null
                    fxRateDate: "2022-04-26"
                    invoiceDate: "2022-04-26"
                    dueDate: "2022-05-26"
                    status: "draft"
                    taxCalculationStatus: "done"
                    supplierDocumentNumber: null
                    supplierDocumentDate: "2022-04-26"
                    totalAmountExcludingTax: 638
                    totalTaxAmount: 107.8
                    totalTaxAmountAdjusted: 107.8
                }
            ) {
                financeIntegrationStatus
                billByAddress {
                    country: country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                site {
                    legalCompany {
                        currency: currency {
                            name
                            id
                            symbol
                            decimalDigits
                        }
                        legislation {
                            name
                            id
                        }
                        name
                        id
                        taxEngine
                    }
                    id
                    name
                }
                billBySupplier {
                    businessEntity {
                        country: country {
                            id
                            name
                            regionLabel
                            zipLabel
                        }
                        name
                        id
                        taxIdNumber
                        currency {
                            name
                            id
                            decimalDigits
                            symbol
                        }
                    }
                    defaultBuyer {
                        email
                        firstName
                        lastName
                        displayName
                    }
                    paymentTerm {
                        name
                        description
                    }
                }
                currency {
                    name
                    id
                    decimalDigits
                    symbol
                }
                companyCurrency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                }
                fxRateDate
                companyFxRate
                companyFxRateDivisor
                rateDescription
                invoiceDate
                dueDate
                paymentTerm {
                    name
                    description
                    days
                }
                status
                matchingStatus
                taxCalculationStatus
                taxEngine
                billByLinkedAddress {
                    name
                    businessEntity {
                        id
                    }
                    country {
                        name
                        regionLabel
                        zipLabel
                    }
                    isActive
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                pdfSupplierInvoice {
                    value
                }
                supplierDocumentNumber
                supplierDocumentDate
                totalAmountExcludingTax
                totalTaxAmount
                totalAmountIncludingTax
                varianceTotalAmountExcludingTax
                varianceTotalTaxAmount
                varianceTotalAmountIncludingTax
                lines {
                    query(first: 10, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                varianceApprover {
                                    displayName
                                }
                                purchaseOrderLine {
                                    purchaseOrderLine {
                                        amountExcludingTax
                                    }
                                }
                                purchaseOrderLine {
                                    purchaseOrderLine {
                                        grossPrice
                                    }
                                }
                                purchaseReceiptLine {
                                    purchaseReceiptLine {
                                        amountExcludingTax
                                    }
                                }
                                purchaseReceiptLine {
                                    purchaseReceiptLine {
                                        grossPrice
                                    }
                                }
                                purchaseOrderLine {
                                    purchaseOrderLine {
                                        quantity
                                    }
                                }
                                purchaseReceiptLine {
                                    purchaseReceiptLine {
                                        quantity
                                    }
                                }
                                purchaseOrderLine {
                                    purchaseOrderLine {
                                        unit {
                                            id
                                            name
                                            decimalDigits
                                        }
                                        quantity
                                        grossPrice
                                        amountExcludingTax
                                    }
                                }
                                purchaseOrderLine {
                                    invoicedQuantity
                                }
                                purchaseReceiptLine {
                                    purchaseReceiptLine {
                                        unit {
                                            id
                                            name
                                            decimalDigits
                                        }
                                        quantity
                                        grossPrice
                                        amountExcludingTax
                                    }
                                }
                                stockUnit {
                                    id
                                    name
                                    decimalDigits
                                }
                                currency {
                                    name
                                    id
                                    decimalDigits
                                    rounding
                                    symbol
                                }
                                unit {
                                    id
                                    name
                                    decimalDigits
                                }
                                recipientSite {
                                    businessEntity {
                                        country: country {
                                            id
                                            name
                                            regionLabel
                                            zipLabel
                                        }
                                    }
                                    name
                                    id
                                }
                                item {
                                    stockUnit {
                                        name
                                        id
                                    }
                                    name
                                    id
                                    description
                                    lotManagement
                                }
                                document {
                                    currency {
                                        name
                                        id
                                        decimalDigits
                                        rounding
                                        symbol
                                    }
                                }
                                origin
                                matchingStatus
                                taxCalculationStatus
                                quantity
                                grossPrice
                                discount
                                charge
                                netPrice
                                priceOrigin
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                taxAmount
                                taxAmountAdjusted
                                amountIncludingTax
                                amountIncludingTaxInCompanyCurrency
                                storedAttributes
                                storedDimensions
                                varianceType
                                taxes {
                                    query(first: 10, orderBy: "{\"taxCategory\":1,\"tax\":1}") {
                                        edges {
                                            node {
                                                taxCategory
                                                tax
                                                taxableAmount
                                                taxRate
                                                taxAmount
                                                taxAmountAdjusted
                                                isReverseCharge
                                                taxCategoryReference {
                                                    name
                                                }
                                                taxReference {
                                                    name
                                                }
                                                deductibleTaxRate
                                                deductibleTaxAmount
                                                exemptAmount
                                                taxableAmount
                                            }
                                        }
                                    }
                                }
                                taxDate
                            }
                        }
                    }
                }
                payToSupplier {
                    businessEntity {
                        name
                        currency {
                            name
                            id
                            decimalDigits
                            symbol
                        }
                    }
                    paymentTerm {
                        name
                        description
                    }
                }
                payToLinkedAddress {
                    name
                    businessEntity {
                        id
                    }
                    country {
                        name
                    }
                    isActive
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                payToAddress {
                    country {
                        name
                        id
                        regionLabel
                        zipLabel
                    }
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                text {
                    value
                }
                calculatedTotalAmountExcludingTax
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                calculatedTotalTaxAmount
                totalTaxAmountAdjusted
                calculatedTotalAmountIncludingTax
                calculatedTotalAmountIncludingTaxInCompanyCurrency
                taxes {
                    query(first: 10, orderBy: "{\"taxCategory\":1,\"tax\":1}") {
                        edges {
                            node {
                                taxCategory
                                tax
                                taxableAmount
                                taxRate
                                taxAmount
                                taxAmountAdjusted
                                isReverseCharge
                                taxableAmount
                            }
                        }
                    }
                }
            }
        }
    }
}
