{"data": {"xtremPurchasing": {"purchaseInvoice": {"create": {"financeIntegrationStatus": "toBeRecorded", "billByAddress": {"country": {"name": "France", "id": "FR", "regionLabel": "department", "zipLabel": "postalCode"}, "name": "960 street 44", "concatenatedAddress": "960 street 44\n960 street 44\nFirst region\n20746\nFrance", "locationPhoneNumber": "+***********", "addressLine1": "960 street 44", "addressLine2": "", "city": "", "region": "First region", "postcode": "20746"}, "site": {"legalCompany": {"currency": {"name": "Euro", "id": "EUR", "symbol": "€", "decimalDigits": 2}, "legislation": {"name": "France", "id": "FR"}, "name": "Société S1", "id": "S1", "taxEngine": "genericTaxCalculation"}, "id": "ETS1-S01", "name": "Siège social S01  PARIS"}, "billBySupplier": {"businessEntity": {"country": {"id": "FR", "name": "France", "regionLabel": "department", "zipLabel": "postalCode"}, "name": "Siège social S01 PARIS", "id": "US017", "taxIdNumber": "FR58483849894", "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "symbol": "€"}}, "defaultBuyer": null, "paymentTerm": {"name": "Net 30", "description": ""}}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "symbol": "€"}, "companyCurrency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "fxRateDate": "2022-04-26", "companyFxRate": "1", "companyFxRateDivisor": "1", "rateDescription": "1 EUR = 1 EUR", "invoiceDate": "2022-04-26", "dueDate": "2022-05-26", "paymentTerm": {"name": "Net 30", "description": "", "days": 30}, "status": "draft", "matchingStatus": "variance", "taxCalculationStatus": "failed", "taxEngine": "genericTaxCalculation", "billByLinkedAddress": {"name": "960 street 44", "businessEntity": {"id": "US017"}, "country": {"name": "France", "regionLabel": "department", "zipLabel": "postalCode"}, "isActive": true, "addressLine1": "960 street 44", "addressLine2": "", "city": "", "region": "First region", "postcode": "20746", "locationPhoneNumber": "+***********"}, "pdfSupplierInvoice": null, "supplierDocumentNumber": "", "supplierDocumentDate": "2022-04-26", "totalAmountExcludingTax": "638", "totalTaxAmount": "107.8", "totalAmountIncludingTax": "745.8", "varianceTotalAmountExcludingTax": "0", "varianceTotalTaxAmount": "-0.6", "varianceTotalAmountIncludingTax": "-0.6", "lines": {"query": {"edges": [{"node": {"varianceApprover": null, "purchaseOrderLine": null, "purchaseReceiptLine": null, "stockUnit": {"id": "LITER", "name": "Liter", "decimalDigits": 2}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "unit": {"id": "LITER", "name": "Liter", "decimalDigits": 2}, "recipientSite": {"businessEntity": {"country": {"id": "FR", "name": "France", "regionLabel": "department", "zipLabel": "postalCode"}}, "name": "Siège social S01  PARIS", "id": "ETS1-S01"}, "item": {"stockUnit": {"name": "Liter", "id": "LITER"}, "name": "Sales Item 81", "id": "SalesItem81", "description": "Sales Item 81", "lotManagement": "lotManagement"}, "document": {"currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}}, "origin": "direct", "matchingStatus": "variance", "taxCalculationStatus": "failed", "quantity": "11", "grossPrice": "12", "discount": "0", "charge": "0", "netPrice": "12", "priceOrigin": "manual", "quantityInStockUnit": "11", "unitToStockUnitConversionFactor": "1", "amountExcludingTax": "132", "amountExcludingTaxInCompanyCurrency": "132", "taxAmount": "26.4", "taxAmountAdjusted": "33.6", "amountIncludingTax": "165.6", "amountIncludingTaxInCompanyCurrency": "165.6", "storedAttributes": null, "storedDimensions": "{\"dimensionType03\":\"WAREHOUSE\",\"dimensionType05\":\"COMMERCIAL\"}", "varianceType": "quantityAndPrice", "taxes": {"query": {"edges": [{"node": {"taxCategory": "TEST", "tax": "", "taxableAmount": "132", "taxRate": "0", "taxAmount": "0", "taxAmountAdjusted": "0", "isReverseCharge": false, "taxCategoryReference": {"name": "TEST"}, "taxReference": null, "deductibleTaxRate": "0", "deductibleTaxAmount": "0", "exemptAmount": "0"}}, {"node": {"taxCategory": "Value Added Tax", "tax": "Tax deductible on debits, standard rate", "taxableAmount": "132", "taxRate": "20", "taxAmount": "26.4", "taxAmountAdjusted": "33.6", "isReverseCharge": false, "taxCategoryReference": {"name": "Value Added Tax"}, "taxReference": {"name": "Tax deductible on debits, standard rate"}, "deductibleTaxRate": "100", "deductibleTaxAmount": "33.6", "exemptAmount": "0"}}]}}, "taxDate": "2022-04-26"}}, {"node": {"varianceApprover": null, "purchaseOrderLine": null, "purchaseReceiptLine": null, "stockUnit": {"id": "GRAM", "name": "Gram", "decimalDigits": 2}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "unit": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "recipientSite": {"businessEntity": {"country": {"id": "FR", "name": "France", "regionLabel": "department", "zipLabel": "postalCode"}}, "name": "Siège social S01  PARIS", "id": "ETS1-S01"}, "item": {"stockUnit": {"name": "Gram", "id": "GRAM"}, "name": "Non stock managed item", "id": "NonStockManagedItem", "description": "Item not managed into stock", "lotManagement": "notManaged"}, "document": {"currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}}, "origin": "direct", "matchingStatus": "variance", "taxCalculationStatus": "failed", "quantity": "22", "grossPrice": "11", "discount": "0", "charge": "0", "netPrice": "11", "priceOrigin": "manual", "quantityInStockUnit": "22000", "unitToStockUnitConversionFactor": "1000", "amountExcludingTax": "242", "amountExcludingTaxInCompanyCurrency": "242", "taxAmount": "48.4", "taxAmountAdjusted": "48.4", "amountIncludingTax": "290.4", "amountIncludingTaxInCompanyCurrency": "290.4", "storedAttributes": null, "storedDimensions": "{\"dimensionType03\":\"WAREHOUSE\",\"dimensionType05\":\"COMMERCIAL\"}", "varianceType": "quantityAndPrice", "taxes": {"query": {"edges": [{"node": {"taxCategory": "TEST", "tax": "", "taxableAmount": "242", "taxRate": "0", "taxAmount": "0", "taxAmountAdjusted": "0", "isReverseCharge": false, "taxCategoryReference": {"name": "TEST"}, "taxReference": null, "deductibleTaxRate": "0", "deductibleTaxAmount": "0", "exemptAmount": "0"}}, {"node": {"taxCategory": "VAT", "tax": "VAT standard rate", "taxableAmount": "242", "taxRate": "20", "taxAmount": "48.4", "taxAmountAdjusted": "48.4", "isReverseCharge": false, "taxCategoryReference": {"name": "VAT"}, "taxReference": {"name": "VAT standard rate"}, "deductibleTaxRate": "100", "deductibleTaxAmount": "48.4", "exemptAmount": "0"}}]}}, "taxDate": "2022-04-26"}}, {"node": {"varianceApprover": null, "purchaseOrderLine": null, "purchaseReceiptLine": null, "stockUnit": {"id": "LITER", "name": "Liter", "decimalDigits": 2}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "unit": {"id": "LITER", "name": "Liter", "decimalDigits": 2}, "recipientSite": {"businessEntity": {"country": {"id": "FR", "name": "France", "regionLabel": "department", "zipLabel": "postalCode"}}, "name": "Siège social S01  PARIS", "id": "ETS1-S01"}, "item": {"stockUnit": {"name": "Liter", "id": "LITER"}, "name": "Sales Item 81", "id": "SalesItem81", "description": "Sales Item 81", "lotManagement": "lotManagement"}, "document": {"currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}}, "origin": "direct", "matchingStatus": "variance", "taxCalculationStatus": "failed", "quantity": "11", "grossPrice": "12", "discount": "0", "charge": "0", "netPrice": "12", "priceOrigin": "manual", "quantityInStockUnit": "11", "unitToStockUnitConversionFactor": "1", "amountExcludingTax": "132", "amountExcludingTaxInCompanyCurrency": "132", "taxAmount": "26.4", "taxAmountAdjusted": "26.4", "amountIncludingTax": "158.4", "amountIncludingTaxInCompanyCurrency": "158.4", "storedAttributes": null, "storedDimensions": "{\"dimensionType03\":\"WAREHOUSE\",\"dimensionType05\":\"COMMERCIAL\"}", "varianceType": "quantityAndPrice", "taxes": {"query": {"edges": [{"node": {"taxCategory": "TEST", "tax": "", "taxableAmount": "132", "taxRate": "0", "taxAmount": "0", "taxAmountAdjusted": "0", "isReverseCharge": false, "taxCategoryReference": {"name": "TEST"}, "taxReference": null, "deductibleTaxRate": "0", "deductibleTaxAmount": "0", "exemptAmount": "0"}}, {"node": {"taxCategory": "Value Added Tax", "tax": "Tax deductible on debits, standard rate", "taxableAmount": "132", "taxRate": "20", "taxAmount": "26.4", "taxAmountAdjusted": "26.4", "isReverseCharge": false, "taxCategoryReference": {"name": "Value Added Tax"}, "taxReference": {"name": "Tax deductible on debits, standard rate"}, "deductibleTaxRate": "100", "deductibleTaxAmount": "26.4", "exemptAmount": "0"}}]}}, "taxDate": "2022-04-26"}}, {"node": {"varianceApprover": null, "purchaseOrderLine": null, "purchaseReceiptLine": null, "stockUnit": {"id": "GRAM", "name": "Gram", "decimalDigits": 2}, "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}, "unit": {"id": "KILOGRAM", "name": "Kilogram", "decimalDigits": 2}, "recipientSite": {"businessEntity": {"country": {"id": "FR", "name": "France", "regionLabel": "department", "zipLabel": "postalCode"}}, "name": "Siège social S01  PARIS", "id": "ETS1-S01"}, "item": {"stockUnit": {"name": "Gram", "id": "GRAM"}, "name": "Non stock managed item", "id": "NonStockManagedItem", "description": "Item not managed into stock", "lotManagement": "notManaged"}, "document": {"currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "rounding": 0, "symbol": "€"}}, "origin": "direct", "matchingStatus": "variance", "taxCalculationStatus": "failed", "quantity": "12", "grossPrice": "11", "discount": "0", "charge": "0", "netPrice": "11", "priceOrigin": "manual", "quantityInStockUnit": "12000", "unitToStockUnitConversionFactor": "1000", "amountExcludingTax": "132", "amountExcludingTaxInCompanyCurrency": "132", "taxAmount": "0", "taxAmountAdjusted": "0", "amountIncludingTax": "132", "amountIncludingTaxInCompanyCurrency": "132", "storedAttributes": null, "storedDimensions": "{\"dimensionType03\":\"WAREHOUSE\",\"dimensionType05\":\"COMMERCIAL\"}", "varianceType": "quantityAndPrice", "taxes": {"query": {"edges": [{"node": {"taxCategory": "TEST", "tax": "", "taxableAmount": "132", "taxRate": "0", "taxAmount": "0", "taxAmountAdjusted": "0", "isReverseCharge": false, "taxCategoryReference": {"name": "TEST"}, "taxReference": null, "deductibleTaxRate": "0", "deductibleTaxAmount": "0", "exemptAmount": "0"}}, {"node": {"taxCategory": "VAT", "tax": "EEC VAT on receipts", "taxableAmount": "132", "taxRate": "20", "taxAmount": "26.4", "taxAmountAdjusted": "26.4", "isReverseCharge": true, "taxCategoryReference": {"name": "VAT"}, "taxReference": {"name": "EEC VAT on receipts"}, "deductibleTaxRate": "100", "deductibleTaxAmount": "26.4", "exemptAmount": "0"}}]}}, "taxDate": "2022-04-26"}}]}}, "payToSupplier": {"businessEntity": {"name": "Siège social S01 PARIS", "currency": {"name": "Euro", "id": "EUR", "decimalDigits": 2, "symbol": "€"}}, "paymentTerm": {"name": "Net 30", "description": ""}}, "payToLinkedAddress": {"name": "960 street 44", "businessEntity": {"id": "US017"}, "country": {"name": "France"}, "isActive": true, "addressLine1": "960 street 44", "addressLine2": "", "city": "", "region": "First region", "postcode": "20746", "locationPhoneNumber": "+***********"}, "payToAddress": {"country": {"name": "France", "id": "FR", "regionLabel": "department", "zipLabel": "postalCode"}, "name": "960 street 44", "concatenatedAddress": "960 street 44\n960 street 44\nFirst region\n20746\nFrance", "locationPhoneNumber": "+***********", "addressLine1": "960 street 44", "addressLine2": "", "city": "", "region": "First region", "postcode": "20746"}, "text": {"value": ""}, "calculatedTotalAmountExcludingTax": "638", "calculatedTotalAmountExcludingTaxInCompanyCurrency": "638", "calculatedTotalTaxAmount": "101.2", "totalTaxAmountAdjusted": "108.4", "calculatedTotalAmountIncludingTax": "746.4", "calculatedTotalAmountIncludingTaxInCompanyCurrency": "746.4", "taxes": {"query": {"edges": [{"node": {"taxCategory": "VAT", "tax": "EEC VAT on receipts", "taxableAmount": "132", "taxRate": "20", "taxAmount": "26.4", "taxAmountAdjusted": "26.4", "isReverseCharge": true}}, {"node": {"taxCategory": "VAT", "tax": "Normal rate deductible on debits", "taxableAmount": "264", "taxRate": "20", "taxAmount": "52.8", "taxAmountAdjusted": "60", "isReverseCharge": false}}, {"node": {"taxCategory": "VAT", "tax": "VAT standard rate", "taxableAmount": "242", "taxRate": "20", "taxAmount": "48.4", "taxAmountAdjusted": "48.4", "isReverseCharge": false}}]}}}}}}}