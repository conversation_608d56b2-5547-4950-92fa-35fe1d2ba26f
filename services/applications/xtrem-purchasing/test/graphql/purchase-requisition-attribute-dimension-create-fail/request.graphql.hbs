mutation purchaseRequisition($storedAttributes: <PERSON>son, $storedDimensions: Json) {
  xtremPurchasing {
    purchaseRequisition {
      create(
        data: {
          requester: "#<EMAIL>"
          site: "#US001"
          requestDate: "2021-03-02"
          lines: [
            {
              item: "#Chemical C"
              quantity: 20
              unit: "#GRAM"
              storedAttributes: $storedAttributes
              storedDimensions: $storedDimensions
            }
          ]
        }
      ) {
        number
        lines {
          query {
            edges {
              node {
                computedAttributes
                storedAttributes
                storedDimensions
              }
            }
          }
        }
      }
    }
  }
}
