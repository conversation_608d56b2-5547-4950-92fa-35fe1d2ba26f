mutation {
    xtremPurchasing {
        purchaseReceipt {
            update(data:{{inputParameters}})
             {
                paymentTerm {
                    name
                }
                status
                displayStatus
                invoiceStatus
                returnStatus
                lines {
                    query {
                        edges {
                            node {
                                status
                                lineInvoiceStatus
                                lineReturnStatus
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                            }
                        }
                    }
                }
            }
        }
    }
}
