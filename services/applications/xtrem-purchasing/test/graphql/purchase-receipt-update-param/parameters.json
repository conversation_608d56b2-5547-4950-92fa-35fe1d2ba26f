{"Update purchase receipt - test updatedValue on status 2 ": {"input": {"properties": {"_id": "#PR2", "paymentTerm": "#TEST_NET_30_SUPPLIER"}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "inProgress", "displayStatus": "partiallyInvoiced", "invoiceStatus": "partiallyInvoiced", "returnStatus": "notReturned", "lines": {"query": {"edges": [{"node": {"status": "inProgress", "lineInvoiceStatus": "partiallyInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "15000"}}, {"node": {"grossPrice": "100", "item": {"id": "Chemical C"}, "amountExcludingTax": "10000", "lineInvoiceStatus": "invoiced", "lineReturnStatus": "notReturned", "quantity": "100", "quantityInStockUnit": "100", "status": "closed", "stockUnit": {"id": "GRAM"}, "unit": {"id": "GRAM"}}}]}}}}}, "Update purchase receipt PR17 - updating to all closed ": {"input": {"properties": {"_id": "#PR17", "lines": [{"_sortValue": "1", "status": "closed"}, {"_sortValue": "2", "status": "closed"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "closed", "displayStatus": "closed", "invoiceStatus": "notInvoiced", "returnStatus": "notReturned", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "15000"}}, {"node": {"status": "closed", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "10000"}}]}}}}}, "Update purchase receipt PR29 - updating to all invoiced || partiallyInvoiced ": {"input": {"properties": {"_id": "#PR29", "lines": [{"_sortValue": "1", "lineInvoiceStatus": "invoiced"}, {"_sortValue": "2", "lineInvoiceStatus": "partiallyInvoiced"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "inProgress", "displayStatus": "partiallyInvoiced", "invoiceStatus": "partiallyInvoiced", "returnStatus": "notReturned", "lines": {"query": {"edges": [{"node": {"lineInvoiceStatus": "invoiced", "status": "closed", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "14250"}}, {"node": {"lineInvoiceStatus": "partiallyInvoiced", "status": "inProgress", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "11000"}}]}}}}}, "Update purchase receipt PR29 - updating to all returned || partiallyReturned ": {"input": {"properties": {"_id": "#PR29", "lines": [{"_sortValue": "1", "lineReturnStatus": "returned"}, {"_sortValue": "2", "lineReturnStatus": "partiallyReturned"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "inProgress", "displayStatus": "partiallyReturned", "invoiceStatus": "notInvoiced", "returnStatus": "partiallyReturned", "lines": {"query": {"edges": [{"node": {"lineReturnStatus": "returned", "lineInvoiceStatus": "notInvoiced", "status": "closed", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "14250"}}, {"node": {"lineReturnStatus": "partiallyReturned", "lineInvoiceStatus": "notInvoiced", "status": "inProgress", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "11000"}}]}}}}}, "Update purchase receipt PR29 - updating to all closed || pending ": {"input": {"properties": {"_id": "#PR29", "lines": [{"_sortValue": "1", "status": "closed"}, {"_sortValue": "2", "status": "pending"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "pending", "displayStatus": "received", "invoiceStatus": "notInvoiced", "returnStatus": "notReturned", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "14250"}}, {"node": {"status": "pending", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "11000"}}]}}}}}, "Update purchase receipt PR29 - updating to all closed || invoiced ": {"input": {"properties": {"_id": "#PR29", "lines": [{"_sortValue": "1", "status": "closed"}, {"_sortValue": "2", "lineInvoiceStatus": "invoiced"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "closed", "displayStatus": "partiallyInvoiced", "invoiceStatus": "partiallyInvoiced", "returnStatus": "notReturned", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "14250"}}, {"node": {"lineInvoiceStatus": "invoiced", "status": "closed", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "11000"}}]}}}}}, "Update purchase receipt PR29 - updating to all closed || returned ": {"input": {"properties": {"_id": "#PR29", "lines": [{"_sortValue": "1", "status": "closed"}, {"_sortValue": "2", "lineReturnStatus": "returned"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "closed", "displayStatus": "partiallyReturned", "invoiceStatus": "notInvoiced", "returnStatus": "partiallyReturned", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "14250"}}, {"node": {"lineReturnStatus": "returned", "lineInvoiceStatus": "notInvoiced", "status": "closed", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "11000"}}]}}}}}, "Update purchase receipt PR29 - updating to all closed || closed ": {"input": {"properties": {"_id": "#PR29", "lines": [{"_sortValue": "1", "status": "closed"}, {"_sortValue": "2", "status": "closed"}]}}, "output": {"update": {"paymentTerm": {"name": "Net 30"}, "status": "closed", "displayStatus": "closed", "invoiceStatus": "notInvoiced", "returnStatus": "notReturned", "lines": {"query": {"edges": [{"node": {"status": "closed", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "150", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "150000", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "14250"}}, {"node": {"status": "closed", "lineInvoiceStatus": "notInvoiced", "lineReturnStatus": "notReturned", "item": {"id": "Chemical C"}, "quantity": "100", "unit": {"id": "GRAM"}, "grossPrice": "100", "quantityInStockUnit": "100", "stockUnit": {"id": "GRAM"}, "amountExcludingTax": "11000"}}]}}}}}}