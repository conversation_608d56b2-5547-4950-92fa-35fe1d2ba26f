{"Invoice line set dimensions - PI240004 - closed ": {"input": {"lineId": "#PurchaseInvoice|PI240004|502200"}, "output": {"data": {"xtremPurchasing": {"purchaseInvoiceLine": {"setDimension": null}}}, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You cannot update dimension on a closed document.", "path": [], "severity": 4}]}, "locations": [{"column": 7, "line": 4}], "message": "Set dimension failed.", "path": ["xtremPurchasing", "purchaseInvoiceLine", "setDimension"]}]}}, "Invoice line set dimensions -PI250001 - posted invoice": {"executionMode": "skip", "comment": "This test is skipped because i don't have the time to get the right data to fix it .", "input": {"lineId": "#PurchaseInvoice|PI250001|511500"}, "output": {"data": {"xtremPurchasing": {"purchaseInvoiceLine": {"setDimension": null}}}, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "You cannot update a document that was posted: document PI250001.", "path": ["lines", "5115"], "severity": 3}, {"message": "Item failed.", "path": ["lines", "5115", "item"], "severity": 3}, {"message": "The LandedCost003 item is not managed for the US016 supplier.", "path": [], "severity": 2}]}, "locations": [{"column": 7, "line": 4}], "message": "Set dimension failed.", "path": ["xtremPurchasing", "purchaseInvoiceLine", "setDimension"]}]}}}