{" Close line fails : REQ1 (having line status='draft')": {"input": {"purchaseRequisitionLine": "#REQ1|10"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval requisition line.", "path": [], "severity": 4}]}, "locations": [{"column": 7, "line": 4}], "message": "Close line failed.", "path": ["xtremPurchasing", "purchaseRequisitionLine", "closeLine"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Close line fails : PO2 (having approvalStatus='pendingApproval')": {"input": {"purchaseRequisitionLine": "#REQ3|10"}, "output": {"close": null, "errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "Unable to update status on already closed, received, pending approval requisition line.", "path": [], "severity": 4}]}, "locations": [{"column": 7, "line": 4}], "message": "Close line failed.", "path": ["xtremPurchasing", "purchaseRequisitionLine", "closeLine"]}]}, "envConfigs": {"today": "2020-08-10"}}}