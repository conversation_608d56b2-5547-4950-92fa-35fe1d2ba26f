{"data": {"xtremPurchasing": {"purchaseInvoice": {"create": {"number": "PI200001", "matchingStatus": "variance", "dueDate": "2020-09-09", "totalAmountIncludingTax": "42", "calculatedTotalAmountExcludingTax": "100", "matchingUser": {"firstName": "Demo"}, "returnLinkedAddress": {"name": "Store"}, "invoiceDate": "2020-08-10", "paymentTerm": {"name": "Net 30"}, "totalAmountExcludingTax": "30", "billBySupplier": {"businessEntity": {"id": "LECLERC"}}, "billByLinkedAddress": {"name": "Store", "addressLine1": "1 First avenue"}, "billByAddress": {"name": "Store", "addressLine1": "1 First avenue"}, "billByContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "payToSupplier": {"businessEntity": {"id": "LECLERC"}}, "payToLinkedAddress": {"name": "Store", "addressLine1": "1 First avenue", "addressLine2": ""}, "payToAddress": {"name": "Store", "addressLine1": "1 First avenue", "addressLine2": ""}, "payToContact": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "currency": {"id": "EUR"}, "companyCurrency": {"id": "USD"}, "transactionCurrency": {"id": "EUR"}, "companyFxRate": "1.002", "companyFxRateDivisor": "1", "site": {"id": "US001"}, "text": {"value": "value"}, "lines": {"query": {"edges": [{"node": {"item": {"id": "LandedCost002"}, "quantity": "1", "unit": {"id": "KILOGRAM"}, "grossPrice": "100", "quantityInStockUnit": "1", "stockUnit": {"id": "KILOGRAM"}, "amountExcludingTax": "100", "amountExcludingTaxInCompanyCurrency": "100.2", "text": {"value": ""}, "landedCost": {"costAmountToAllocate": "100", "costAmountToAllocateInCompanyCurrency": "100.2", "allocationRule": "byVolume", "allocationRuleUnit": {"id": "CUBIC_METER"}, "allocationMethod": "manual"}}}]}}}}}}}