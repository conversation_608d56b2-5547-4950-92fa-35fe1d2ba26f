{" Create from REQ7 ": {"executionMode": "normal", "input": {"document": "#REQ7"}, "output": {"createPurchaseOrders": [{"number": "PO200001"}]}, "envConfigs": {"today": "2020-08-10"}}, " Create from REQ8 ": {"executionMode": "normal", "input": {"document": "#REQ8"}, "output": {"createPurchaseOrders": [{"number": "PO200001"}]}, "envConfigs": {"today": "2020-08-10"}}, " Create from REQ9 ": {"executionMode": "normal", "input": {"document": "#REQ9"}, "output": {"createPurchaseOrders": [{"number": "PO200001"}]}, "envConfigs": {"today": "2020-08-10"}}, " Create from REQ19 ": {"executionMode": "normal", "input": {"document": "#REQ19"}, "output": {"createPurchaseOrders": [{"number": "PO200001"}]}, "envConfigs": {"today": "2020-08-10"}}, " Create from REQ20 ": {"executionMode": "normal", "input": {"document": "#REQ20"}, "output": {"createPurchaseOrders": [{"number": "PO200001"}]}, "envConfigs": {"today": "2020-08-10"}}, " Create from REQ21 ": {"executionMode": "normal", "input": {"document": "#REQ21"}, "output": {"createPurchaseOrders": [{"number": "PO200001"}]}, "envConfigs": {"today": "2020-08-10"}}, " Create from REQ24 ": {"executionMode": "normal", "input": {"document": "#REQ24"}, "output": {"createPurchaseOrders": [{"number": "PO200001"}]}, "envConfigs": {"today": "2020-08-10"}}}