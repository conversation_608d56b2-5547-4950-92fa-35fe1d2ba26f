mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            update(
                data: {
                    _id: "#PCM2"
                    creditMemoDate: "2021-11-03"
                    totalAmountExcludingTax: 10
                    totalTaxAmount: 1
                    text: { value: "blabla" }
                    lines: [
                        {
                            _action: "create"
                            item: "#SalesItem81"
                            grossPrice: 10
                            unit: "#KILOGRAM"
                            quantity: 20
                            quantityInStockUnit: 20000
                            site: "#US001"
                            stockUnit: "#GRAM"
                            purchaseInvoiceLine: { purchaseInvoiceLine: "2610" }
                        }
                    ]
                }
            ) {
                creditMemoDate
                supplierDocumentDate
                text {
                    value
                }
                currency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                totalAmountExcludingTax
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                totalTaxAmount
                totalAmountIncludingTax
                calculatedTotalAmountIncludingTaxInCompanyCurrency
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                billByAddress {
                    name
                    addressLine1
                    addressLine2
                    postcode
                    city
                    region
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                priceOrigin
                                taxDate
                            }
                        }
                    }
                }
            }
        }
    }
}
