mutation {
    xtremPurchasing {
        purchaseCreditMemo {
            update(
                data: {
                    _id: "#PCM3"
                    lines: [
                        {
                            _action: "create"
                            item: "#Aqua"
                            grossPrice: 10
                            unit: "#LITER"
                            quantity: 5
                            quantityInStockUnit: 5
                            site: "#US005"
                            stockUnit: "#LITER"
                            purchaseReturnLine: { purchaseReturnLine: "2445" }
                        }
                    ]
                }
            ) {
                creditMemoDate
                text {
                    value
                }
                currency {
                    id
                }
                companyCurrency {
                    id
                }
                transactionCurrency {
                    id
                }
                companyFxRate
                companyFxRateDivisor
                totalAmountExcludingTax
                calculatedTotalAmountExcludingTaxInCompanyCurrency
                totalTaxAmount
                totalAmountIncludingTax
                calculatedTotalAmountIncludingTaxInCompanyCurrency
                billBySupplier {
                    businessEntity {
                        id
                    }
                }
                billByAddress {
                    name
                    addressLine1
                    addressLine2
                    postcode
                    city
                    region
                }
                lines {
                    query {
                        edges {
                            node {
                                item {
                                    id
                                }
                                quantity
                                unit {
                                    id
                                }
                                grossPrice
                                quantityInStockUnit
                                stockUnit {
                                    id
                                }
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                text {
                                    value
                                }
                                priceOrigin
                            }
                        }
                    }
                }
            }
        }
    }
}
