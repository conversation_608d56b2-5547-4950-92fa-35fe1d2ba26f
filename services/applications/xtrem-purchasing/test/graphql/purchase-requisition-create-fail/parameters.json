{"Create purchase requisition - fails (no lines) ": {"executionMode": "normal", "input": {"properties": {"requester": "#<EMAIL>", "site": "#US001", "requestDate": "2020-08-11"}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The request date cannot be later than today.", "path": ["requestDate"], "severity": 3}, {"message": "The document needs at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremPurchasing", "purchaseRequisition", "create"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Create purchase requisition - fails (no time unit allowed, no existing item-site, no conversion factor PU to SU, no price) ": {"input": {"properties": {"requester": "#<EMAIL>", "site": "#US002", "requestDate": "2020-08-11", "lines": [{"item": "#Service001", "supplier": "#LECLERC", "currency": "#EUR", "quantity": 10, "unit": "#LITER"}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The request date cannot be later than today.", "path": ["requestDate"], "severity": 3}, {"message": "The record is not valid. You need to select a different record.", "path": ["lines", "-1000000002", "item"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "unitToStockUnitConversionFactor"], "severity": 3}, {"message": "property cannot be equal to 0", "path": ["lines", "-1000000002", "quantityInStockUnit"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremPurchasing", "purchaseRequisition", "create"]}]}, "envConfigs": {"today": "2020-08-10"}}, "Create purchase requisition - fails (price without currency) ": {"executionMode": "normal", "input": {"properties": {"requester": "#<EMAIL>", "site": "#US001", "lines": [{"item": "#Chemical C", "quantity": 10, "unit": "#KILOGRAM", "grossPrice": 100}]}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The gross price currency is mandatory.", "path": ["lines", "-1000000002", "currency"], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremPurchasing", "purchaseRequisition", "create"]}]}}, "Create purchase requisition - with pending status - fail": {"executionMode": "normal", "input": {"properties": {"number": "POTEST", "requester": "#<EMAIL>", "site": "#US001", "status": "draft", "lines": []}}, "output": {"errors": [{"extensions": {"code": "operation-error", "diagnoses": [{"message": "The document needs at least one line.", "path": [], "severity": 3}]}, "locations": [{"column": 13, "line": 4}], "message": "The record was not created.", "path": ["xtremPurchasing", "purchaseRequisition", "create"]}]}}, "Create purchase requisition with data from stock transfer order": {"executionMode": "normal", "input": {"properties": {"number": "test", "requester": "#<EMAIL>", "site": "#CAS01", "lines": [{"item": "#Milk", "quantity": "10", "supplier": "#CAS02"}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseRequisition", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"severity": 3, "path": ["lines", "-1000000002", "supplier"], "message": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value."}]}}]}}}