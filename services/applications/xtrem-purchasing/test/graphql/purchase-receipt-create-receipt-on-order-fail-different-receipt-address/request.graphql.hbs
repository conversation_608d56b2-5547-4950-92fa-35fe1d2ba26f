{{! Playground : https://handlebarsjs.com/examples/literal-segments.html}}
mutation {
    xtremPurchasing {
        purchaseReceipt {
            create(data:{ 
{{#each properties}}
    {{#if this.[0].[0]}} {{! This.[0].[0] equals # for "#KEY" }}
    		{{@key}} : "{{this}}"
    {{else}}
    	{{#unless this.[0]}}
    		{{@key}} : {{this}}
    	{{/unless}}
    {{/if}}
    {{#if this.[0]}} {{! For generation of references when defined as '{}'}}
        	{{#each this}}
              {{#each this}}
              {{@key}} :
                {{#each this}}
                    {{#if @first}}
                        {
                    {{/if}}
                    {{#unless this}}
                    {{else}}
                    {{#if this.[0]}}
						{{@key}} : "{{this}}"
                    {{else}}
                      {{@key}} : {{this}}
                    {{/if}}
                    {{/unless}}
                    {{#if @last}}
                        }
                    {{/if}}
                {{/each}}
            {{/each}}
          {{/each}}
      {{/if}}
{{/each}}
{{#each arrayProperties}}
    {{@key}} :
    [{{#each this}}
    { {{#each this}}
        {{#if this.[0].[0]}} {{! This.[0].[0] equals # for "#KEY" }}
    		{{@key}} : "{{this}}"
        {{else}}
        	{{#unless this.[0]}}
            	{{@key}} : {{this}}
            {{/unless}}
        {{/if}}
        {{! For generation of references when defined as '{}'}}
        {{#if this.[0]}}
        	{{#each this}}
              {{#each this}}
              {{@key}} :
                {{#each this}}
                    {{#if @first}}
                        {
                    {{/if}}
					{{#if this.[0].[0]}} {{! This.[0].[0] equals # for "#KEY" }}
    					{{@key}} : "{{this}}"
                    {{else}}
                        {{#unless this.[0]}}
                            {{@key}} : {{this}}
                        {{/unless}}
                    {{/if}}
                    {{#if @last}}
                        }
                    {{/if}}
                {{/each}}
            {{/each}}
          {{/each}}
      {{/if}}
     {{/each}}
    	}
    {{/each}}
    ]
{{/each}}
}) { number } } } }
