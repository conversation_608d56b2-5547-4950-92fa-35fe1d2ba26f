{"Create purchase receipt - fails (different receiving site address - name) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address FAIL", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": "#PO15|2700"}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": "#PO16|2800"}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}, "Create purchase receipt - fails (different receiving site address - addressLine1) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street FAIL", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}, "Create purchase receipt - fails (different receiving site address - addressLine2) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building FAIL", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}, "Create purchase receipt - fails (different receiving site address - city) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere FAIL", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}, "Create purchase receipt - fails (different receiving site address - region) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng FAIL", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}, "Create purchase receipt - fails (different receiving site address - postcode) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123 FAIL", "locationPhoneNumber": "+***********", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}, "Create purchase receipt - fails (different receiving site address - locationPhoneNumber) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+*********** FAIL", "country": "#ZA"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}, "Create purchase receipt - fails (different receiving site address - country) ": {"input": {"properties": {"site": "#US001", "businessRelation": "#LECLERC", "currency": "#EUR", "referenceProperties": [{"receivingAddress": {"name": "Main address", "addressLine1": "1 Some street", "addressLine2": "Some building", "city": "Somewhere", "region": "Gauteng", "postcode": "0123", "locationPhoneNumber": "+***********", "country": "#US"}}, {"supplierAddress": {"name": "COSMETIC", "addressLine1": "26 <PERSON><PERSON>ailler", "addressLine2": "", "city": "", "region": "HDF", "postcode": "59650", "locationPhoneNumber": "0695125874", "country": "#FR"}}]}, "arrayProperties": {"lines": [{"item": "#Mu<PERSON>li", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 7, "stockUnit": "#GRAM", "quantityInStockUnit": 7000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 27}}]}, {"item": "#Chemical C", "grossPrice": 100, "unit": "#KILOGRAM", "quantity": 8, "stockUnit": "#GRAM", "quantityInStockUnit": 8000, "arrayReferenceProperties": [{"purchaseOrderLine": {"purchaseOrderLine": 28}}]}]}}, "output": {"errors": [{"message": "The record was not created.", "locations": [{"line": 4, "column": 13}], "path": ["xtremPurchasing", "purchaseReceipt", "create"], "extensions": {"code": "operation-error", "diagnoses": [{"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000004", "purchaseOrderLine"], "severity": 3}, {"message": "The purchase receipt must have the same values for the following properties: \n receiving site address", "path": ["lines", "-1000000010", "purchaseOrderLine"], "severity": 3}, {"message": "The item: Chemical C, is not managed for the supplier: LECLERC.", "path": ["lines", "-1000000010"], "severity": 2}]}}]}, "envConfigs": {"today": "2022-01-27"}}}