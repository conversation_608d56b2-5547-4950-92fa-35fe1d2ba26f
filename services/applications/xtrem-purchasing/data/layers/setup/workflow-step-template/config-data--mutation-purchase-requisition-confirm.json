{"selector": "{ _id approvalStatus status}", "stepVariables": [{"node": "PurchaseRequisition", "path": "purchaseRequisition._id", "type": "IntReference", "title": "Purchase requisition / 🆔", "isCustom": false}], "localizedTitle": {"base": "Purchase requisition - confirm", "en-US": "Purchase requisition - confirm"}, "actionParameters": [{"name": "document", "value": "purchaseRequisition._id", "isVariable": true}], "mutationArguments": [{"name": "document", "node": "PurchaseRequisition", "type": "reference", "origin": "fromParameter", "isMandatory": true}, {"name": "isSafeToRetry", "type": "boolean", "value": true, "origin": "manual"}], "mutationNaturalKey": "PurchaseRequisition|confirm", "outputVariableName": "purchaseRequisition"}