"id";"_vendor";"name";"description";"node_name";"is_active";"is_default";"template_use";"default_parameters";"csv_template"
"Purchase Receipt Serial Number";"sage";"{""en"":""Purchase Receipt Serial Number"",""base"":""Purchase Receipt Serial Number"",""en-US"":""Purchase Receipt Serial Number""}";"{""en"":"""",""base"":"""",""en-US"":""""}";"PurchaseReceipt";"Y";"N";"importAndExport";"file:default-parameters--purchase-receipt-serial-number.json";"file:csv-template--purchase-receipt-serial-number.json"
"PurchaseReceipt";"sage";"{""en"":""PurchaseReceipt"",""base"":""PurchaseReceipt"",""en-US"":""PurchaseReceipt""}";"{""en"":"""",""base"":"""",""en-US"":""""}";"PurchaseReceipt";"Y";"Y";"importAndExport";"file:default-parameters--purchase-receipt.json";"file:csv-template--purchase-receipt.json"
"PurchaseOrder";"sage";"{""en"":""PurchaseOrder"",""base"":""PurchaseOrder"",""en-US"":""PurchaseOrder""}";"{""en"":"""",""base"":"""",""en-US"":""""}";"PurchaseOrder";"Y";"N";"importAndExport";"file:default-parameters--purchase-order.json";"file:csv-template--purchase-order.json"
"PurchaseOrderLineInquiry";"sage";"{""en"":""Purchase Order Line Inquiry"",""base"":""Purchase Order Line Inquiry"",""en-US"":""Purchase Order Line Inquiry""}";"{""en"":""Default export template for purchase order line inquiry"",""base"":""Default export template for purchase order line inquiry"",""en-US"":""Default export template for purchase order line inquiry""}";"PurchaseOrderLine";"Y";"Y";"exportOnly";"file:default-parameters--purchase-order-line-inquiry.json";"file:csv-template--purchase-order-line-inquiry.json"
