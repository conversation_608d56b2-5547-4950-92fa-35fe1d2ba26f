{"data": [{"_id": "0", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "1050", "path": "site.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "1060", "path": "site.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "1020", "path": "site.legalCompany", "locale": "", "dataType": "reference", "description": "legal company (#id)"}, {"_id": "1030", "path": "site.legalCompany.name", "locale": "", "dataType": "string", "description": "name"}, {"_id": "1040", "path": "site.legalCompany.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "130", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1080", "path": "item.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1090", "path": "item.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "1100", "path": "item.description", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"description (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "240", "path": "quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity"}, {"_id": "1110", "path": "purchaseUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase unit (#id)"}, {"_id": "1140", "path": "purchaseUnit.name", "locale": "en", "dataType": "localized text", "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1130", "path": "purchaseUnit.id", "locale": "", "dataType": "string", "description": "id"}, {"_id": "280", "path": "netPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "net price"}, {"_id": "310", "path": "status", "locale": "", "dataType": "enum(draft,pending,inProgress,closed,posted,error)", "isCustom": false, "description": "line status"}, {"_id": "290", "path": "lineReceiptStatus", "locale": "", "dataType": "enum(notReceived,partiallyReceived,received)", "isCustom": false, "description": "line receipt status"}, {"_id": "300", "path": "lineInvoiceStatus", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "line invoice status"}, {"_id": "1120", "path": "expectedReceiptDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expected receipt date (yyyy-MM-dd)"}, {"_id": "320", "path": "amountExcludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax"}, {"_id": "330", "path": "amountExcludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax in company currency"}, {"_id": "340", "path": "amountIncludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax"}, {"_id": "350", "path": "amountIncludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax in company currency"}]}