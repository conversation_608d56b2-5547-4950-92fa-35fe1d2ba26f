{"data": [{"_id": "0", "path": "number", "locale": "", "dataType": "string", "isCustom": false, "description": "number"}, {"_id": "10", "path": "*site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "20", "path": "businessEntityAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "business entity address (#businessEntity|_sortValue)"}, {"_id": "30", "path": "stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "40", "path": "*supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "50", "path": "billBySupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by supplier (#businessEntity)"}, {"_id": "60", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "70", "path": "paymentTerm", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "80", "path": "text", "locale": "", "dataType": "textStream", "isCustom": false, "description": "text"}, {"_id": "90", "path": "totalTaxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total tax amount"}, {"_id": "100", "path": "totalTaxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total taxable amount"}, {"_id": "110", "path": "totalExemptAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total exempt amount"}, {"_id": "120", "path": "taxCalculationStatus", "locale": "", "dataType": "enum(notDone,inProgress,done,failed)", "isCustom": false, "description": "tax calculation status"}, {"_id": "130", "path": "totalTaxAmountAdjusted", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total tax amount adjusted"}, {"_id": "140", "path": "internalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "150", "path": "externalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "external note"}, {"_id": "160", "path": "isExternalNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is external note (false/true)"}, {"_id": "170", "path": "isTransferHeaderNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is transfer header note (false/true)"}, {"_id": "180", "path": "isTransferLineNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is transfer line note (false/true)"}, {"_id": "190", "path": "isOverwriteNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is overwrite note (false/true)"}, {"_id": "200", "path": "date", "locale": "", "dataType": "date", "isCustom": false, "description": "receipt date (YYYY-MM-DD)"}, {"_id": "210", "path": "supplierDocumentNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier document number"}, {"_id": "220", "path": "supplierReceiptReference", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier receipt reference"}, {"_id": "230", "path": "carrier", "locale": "", "dataType": "reference", "isCustom": false, "description": "carrier (#businessEntity)"}, {"_id": "240", "path": "fxRateDate", "locale": "", "dataType": "date", "isCustom": false, "description": "fx rate date (YYYY-MM-DD)"}, {"_id": "250", "path": "companyFxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "company fx rate"}, {"_id": "260", "path": "companyFxRateDivisor", "locale": "", "dataType": "decimal", "isCustom": false, "description": "company fx rate divisor"}, {"_id": "270", "path": "invoiceStatus", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "invoice status"}, {"_id": "280", "path": "returnStatus", "locale": "", "dataType": "enum(notReturned,partiallyReturned,returned)", "isCustom": false, "description": "return status"}, {"_id": "290", "path": "status", "locale": "", "dataType": "enum(draft,pending,inProgress,closed,posted,error)", "isCustom": false, "description": "status"}, {"_id": "300", "path": "totalAmountExcludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total amount excluding tax"}, {"_id": "310", "path": "totalAmountExcludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total amount excluding tax in company currency"}, {"_id": "320", "path": "displayStatus", "locale": "", "dataType": "enum(draft,taxCalculationFailed,postingInProgress,error,received,partiallyInvoiced,invoiced,partiallyReturned,returned,closed)", "isCustom": false, "description": "display status"}, {"_id": "330", "path": "/siteAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "site address"}, {"_id": "340", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "350", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "360", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "370", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "380", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "390", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "400", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "410", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "420", "path": "/returnAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "return address"}, {"_id": "430", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "440", "path": "addressLine1#1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "450", "path": "addressLine2#1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "460", "path": "city#1", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "470", "path": "region#1", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "480", "path": "postcode#1", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "490", "path": "country#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "500", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "510", "path": "/supplierAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier address"}, {"_id": "520", "path": "*name#2", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "530", "path": "addressLine1#2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "540", "path": "addressLine2#2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "550", "path": "city#2", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "560", "path": "region#2", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "570", "path": "postcode#2", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "580", "path": "country#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "590", "path": "locationPhoneNumber#2", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "600", "path": "/receivingAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "receiving address"}, {"_id": "610", "path": "*name#3", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "620", "path": "addressLine1#3", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "630", "path": "addressLine2#3", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "640", "path": "city#3", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "650", "path": "region#3", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "660", "path": "postcode#3", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "670", "path": "country#3", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "680", "path": "locationPhoneNumber#3", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "690", "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": "700", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "710", "path": "siteLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "site linked address (#businessEntity|_sortValue)"}, {"_id": "720", "path": "taxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}, {"_id": "730", "path": "taxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "740", "path": "exemptAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "750", "path": "taxDate", "locale": "", "dataType": "date", "isCustom": false, "description": "tax date (YYYY-MM-DD)"}, {"_id": "760", "path": "taxAmountAdjusted", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "770", "path": "internalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "780", "path": "externalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "external note"}, {"_id": "790", "path": "isExternalNote#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is external note (false/true)"}, {"_id": "800", "path": "stockTransactionStatus", "locale": "", "dataType": "enum(draft,inProgress,completed,error)", "isCustom": false, "description": "stock transaction status"}, {"_id": "810", "path": "transientPurchaseOrderLine", "locale": "", "dataType": "integer", "isCustom": false, "description": "transient purchase order line"}, {"_id": "820", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "830", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "840", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "850", "path": "itemDescription", "locale": "", "dataType": "string", "isCustom": false, "description": "item description"}, {"_id": "860", "path": "stockSite#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "870", "path": "stockSiteLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site linked address (#businessEntity|_sortValue)"}, {"_id": "880", "path": "stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "890", "path": "text#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "text"}, {"_id": "900", "path": "valuedCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "valued cost"}, {"_id": "910", "path": "unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase unit (#id)"}, {"_id": "920", "path": "unitToStockUnitConversionFactor", "locale": "", "dataType": "decimal", "isCustom": false, "description": "purchase unit to stock unit conversion factor"}, {"_id": "930", "path": "quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity"}, {"_id": "940", "path": "grossPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "gross price"}, {"_id": "950", "path": "quantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "960", "path": "priceOrigin", "locale": "", "dataType": "enum(manual,supplierPriceList)", "isCustom": false, "description": "price origin"}, {"_id": "970", "path": "netPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "net price"}, {"_id": "980", "path": "lineReturnStatus", "locale": "", "dataType": "enum(notReturned,partiallyReturned,returned)", "isCustom": false, "description": "line return status"}, {"_id": "990", "path": "lineInvoiceStatus", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "line invoice status"}, {"_id": "1000", "path": "orderCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "order cost"}, {"_id": "1010", "path": "status", "locale": "", "dataType": "enum(draft,pending,inProgress,closed,posted,error)", "isCustom": false, "description": "line status"}, {"_id": "1020", "path": "amountExcludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax"}, {"_id": "1030", "path": "amountExcludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax in company currency"}, {"_id": "1040", "path": "amountIncludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax"}, {"_id": "1050", "path": "amountIncludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax in company currency"}, {"_id": "1060", "path": "completed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "completed (false/true)"}, {"_id": "1070", "path": "##taxes", "locale": "", "dataType": "collection", "isCustom": false, "description": "taxes"}, {"_id": "1080", "path": "taxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax rate"}, {"_id": "1090", "path": "taxCategoryReference", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax category reference (#id)"}, {"_id": "1100", "path": "taxReference", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax reference (#id)"}, {"_id": "1110", "path": "deductibleTaxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax rate"}, {"_id": "1120", "path": "isReverseCharge", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is reverse charge (false/true)"}, {"_id": "1130", "path": "isTaxMandatory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tax mandatory (true/false)"}, {"_id": "1140", "path": "isSubjectToGlTaxExcludedAmount", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is subject to gl tax excluded amount (false/true)"}, {"_id": "1150", "path": "jurisdictionName", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction name"}, {"_id": "1160", "path": "numberOfTaxableUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of taxable units"}, {"_id": "1170", "path": "numberOfNonTaxableUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of non taxable units"}, {"_id": "1180", "path": "numberOfExemptUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of exempt units"}, {"_id": "1190", "path": "country#4", "locale": "", "dataType": "string", "isCustom": false, "description": "country"}, {"_id": "1200", "path": "region#4", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "1210", "path": "jurisdictionCode", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction code"}, {"_id": "1220", "path": "jurisdictionType", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction type"}, {"_id": "1230", "path": "taxCategory", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax category (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1240", "path": "tax", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1250", "path": "deductibleTaxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax amount"}, {"_id": "1260", "path": "taxAmountAdjusted#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "1270", "path": "nonTaxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "non taxable amount"}, {"_id": "1280", "path": "taxAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "1290", "path": "exemptAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "1300", "path": "taxableAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}, {"_id": "1310", "path": "//purchaseOrderLine", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase order line"}, {"_id": "1320", "path": "receivedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "received quantity"}, {"_id": "1330", "path": "receivedQuantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "received quantity in stock unit"}, {"_id": "1340", "path": "//workInProgress", "locale": "", "dataType": "reference", "isCustom": false, "description": "work in progress"}, {"_id": "1350", "path": "remainingQuantityToAllocate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "remaining quantity to allocate"}, {"_id": "1360", "path": "documentType", "locale": "", "dataType": "enum(workOrder,materialNeed,purchaseOrder,purchaseReceipt,purchaseReturn,salesOrder)", "isCustom": false, "description": "document type"}, {"_id": "1370", "path": "item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1380", "path": "site#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "1390", "path": "status#1", "locale": "", "dataType": "enum(firm,planned,suggested,closed)", "isCustom": false, "description": "status"}, {"_id": "1400", "path": "startDate", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (YYYY-MM-DD)"}, {"_id": "1410", "path": "endDate", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (YYYY-MM-DD)"}, {"_id": "1420", "path": "expectedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected quantity"}, {"_id": "1430", "path": "actualQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual quantity"}, {"_id": "1440", "path": "outstandingQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "outstanding quantity"}, {"_id": "1450", "path": "//returnAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "return address"}, {"_id": "1460", "path": "*name#4", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "1470", "path": "addressLine1#4", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "1480", "path": "addressLine2#4", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "1490", "path": "city#4", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "1500", "path": "region#5", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "1510", "path": "postcode#4", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "1520", "path": "country#5", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "1530", "path": "locationPhoneNumber#4", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "1540", "path": "##stockTransactions", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock transactions"}, {"_id": "1550", "path": "notificationId", "locale": "", "dataType": "string", "isCustom": false, "description": "notification id"}, {"_id": "1560", "path": "status#2", "locale": "", "dataType": "enum(inProgress,succeeded,error)", "isCustom": false, "description": "status"}, {"_id": "1570", "path": "message", "locale": "", "dataType": "string", "isCustom": false, "description": "message"}, {"_id": "1580", "path": "resultAction", "locale": "", "dataType": "enum(none,created,increased,decreased,deleted,adjusted,changed,allocated,deallocated,corrected,noChange,valueChange)", "isCustom": false, "description": "result action"}, {"_id": "1590", "path": "##discountCharges", "locale": "", "dataType": "collection", "isCustom": false, "description": "discount charges"}, {"_id": "1600", "path": "sign", "locale": "", "dataType": "enum(increase,decrease)", "isCustom": false, "description": "sign"}, {"_id": "1610", "path": "valueType", "locale": "", "dataType": "enum(percentage,amount)", "isCustom": false, "description": "value type"}, {"_id": "1620", "path": "calculationBasis", "locale": "", "dataType": "enum(grossPrice,grossPriceAndCompound)", "isCustom": false, "description": "calculation basis"}, {"_id": "1630", "path": "calculationRule", "locale": "", "dataType": "enum(byUnit,byLine)", "isCustom": false, "description": "calculation rule"}, {"_id": "1640", "path": "basisDeterminated", "locale": "", "dataType": "decimal", "isCustom": false, "description": "basis determinated"}, {"_id": "1650", "path": "*value", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value"}, {"_id": "1660", "path": "valueDeterminated", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value determinated"}, {"_id": "1670", "path": "basis", "locale": "", "dataType": "decimal", "isCustom": false, "description": "basis"}, {"_id": "1680", "path": "amount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "amount"}, {"_id": "1690", "path": "##stockDetails", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock details"}, {"_id": "1700", "path": "*site#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "1710", "path": "*item#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1720", "path": "stockUnit#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "1730", "path": "quantityInStockUnit#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "1740", "path": "movementType", "locale": "", "dataType": "enum(receipt,issue,change,adjustment,correction,valueChange)", "isCustom": false, "description": "movement type"}, {"_id": "1750", "path": "supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "1760", "path": "*status", "locale": "", "dataType": "reference", "isCustom": false, "description": "status (#id)"}, {"_id": "1770", "path": "effectiveDate", "locale": "", "dataType": "date", "isCustom": false, "description": "effective date (YYYY-MM-DD)"}, {"_id": "1780", "path": "startingSerialNumber", "locale": "", "dataType": "reference", "isCustom": false, "description": "starting serial number (#item|id)"}, {"_id": "1790", "path": "orderCost#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "order cost"}, {"_id": "1800", "path": "valuedCost#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "valued cost"}, {"_id": "1810", "path": "location", "locale": "", "dataType": "reference", "isCustom": false, "description": "location (#id|locationZone)"}, {"_id": "1820", "path": "existingLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "existing lot (#item|id|sublot)"}, {"_id": "1830", "path": "lotCreateData", "locale": "", "dataType": "json", "isCustom": false, "description": "lot create data"}, {"_id": "1840", "path": "owner", "locale": "", "dataType": "string", "isCustom": false, "description": "owner"}, {"_id": "1850", "path": "###stockDetailSerialNumbers", "locale": "", "dataType": "collection", "isCustom": false, "description": "stock detail serial numbers"}, {"_id": "1860", "path": "supplierSerialNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier serial number"}, {"_id": "1870", "path": "serialNumber", "locale": "", "dataType": "reference", "isCustom": false, "description": "serial number (#item|id)"}, {"_id": "1880", "path": "newSerialNumberId", "locale": "", "dataType": "string", "isCustom": false, "description": "new serial number id"}, {"_id": "1890", "path": "///stockDetailLot", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock detail lot"}, {"_id": "1900", "path": "lot", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot (#item|id|sublot)"}, {"_id": "1910", "path": "lotNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "lot number"}, {"_id": "1920", "path": "supplierLot", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier lot"}, {"_id": "1930", "path": "expirationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expiration date (YYYY-MM-DD)"}, {"_id": "1940", "path": "sublot", "locale": "", "dataType": "string", "isCustom": false, "description": "sublot"}, {"_id": "1950", "path": "#taxes", "locale": "", "dataType": "collection", "isCustom": false, "description": "taxes"}, {"_id": "1960", "path": "taxRate#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax rate"}, {"_id": "1970", "path": "taxCategoryReference#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax category reference (#id)"}, {"_id": "1980", "path": "taxReference#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax reference (#id)"}, {"_id": "1990", "path": "deductibleTaxRate#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax rate"}, {"_id": "2000", "path": "isReverseCharge#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is reverse charge (false/true)"}, {"_id": "2010", "path": "isTaxMandatory#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tax mandatory (true/false)"}, {"_id": "2020", "path": "isSubjectToGlTaxExcludedAmount#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is subject to gl tax excluded amount (false/true)"}, {"_id": "2030", "path": "jurisdictionName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction name"}, {"_id": "2040", "path": "country#6", "locale": "", "dataType": "string", "isCustom": false, "description": "country"}, {"_id": "2050", "path": "region#6", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "2060", "path": "jurisdictionCode#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction code"}, {"_id": "2070", "path": "jurisdictionType#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction type"}, {"_id": "2080", "path": "taxCategory#1", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax category (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "2090", "path": "tax#1", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"tax (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "2100", "path": "deductibleTaxAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax amount"}, {"_id": "2110", "path": "taxAmountAdjusted#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "2120", "path": "nonTaxableAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "non taxable amount"}, {"_id": "2130", "path": "taxAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "2140", "path": "exemptAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "2150", "path": "taxableAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}]}