{"data": [{"_id": "0", "path": "number", "locale": "", "dataType": "string", "isCustom": false, "description": "number"}, {"_id": "10", "path": "*site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "20", "path": "businessEntityAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "business entity address (#businessEntity|_sortValue)"}, {"_id": "30", "path": "stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "40", "path": "*businessRelation", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "50", "path": "billBySupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by supplier (#businessEntity)"}, {"_id": "60", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "70", "path": "paymentTerm", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "80", "path": "text", "locale": "", "dataType": "textStream", "isCustom": false, "description": "text"}, {"_id": "90", "path": "totalTaxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total tax amount"}, {"_id": "100", "path": "totalTaxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total taxable amount"}, {"_id": "110", "path": "totalExemptAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total exempt amount"}, {"_id": "120", "path": "taxCalculationStatus", "locale": "", "dataType": "enum(notDone,inProgress,done,failed)", "isCustom": false, "description": "tax calculation status"}, {"_id": "130", "path": "totalTaxAmountAdjusted", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total tax amount adjusted"}, {"_id": "140", "path": "internalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "150", "path": "externalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "external note"}, {"_id": "160", "path": "isExternalNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is external note (false/true)"}, {"_id": "170", "path": "isTransferHeaderNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is transfer header note (false/true)"}, {"_id": "180", "path": "isTransferLineNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is transfer line note (false/true)"}, {"_id": "190", "path": "isOverwriteNote", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is overwrite note (false/true)"}, {"_id": "200", "path": "isPurchaseOrderSuggestion", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase order suggestion (false/true)"}, {"_id": "210", "path": "supplierLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier linked address (#businessEntity|_sortValue)"}, {"_id": "220", "path": "orderDate", "locale": "", "dataType": "date", "isCustom": false, "description": "order date (yyyy-MM-dd)"}, {"_id": "230", "path": "changeRequestedDescription", "locale": "", "dataType": "textStream", "isCustom": false, "description": "change requested description"}, {"_id": "240", "path": "supplierOrderReference", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier order reference"}, {"_id": "250", "path": "isSent", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sent (false/true)"}, {"_id": "260", "path": "deliveryMode", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery mode (#id)"}, {"_id": "270", "path": "defaultBuyer", "locale": "", "dataType": "reference", "isCustom": false, "description": "default buyer (#email)"}, {"_id": "280", "path": "isPrinted", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is printed (false/true)"}, {"_id": "290", "path": "fxRateDate", "locale": "", "dataType": "date", "isCustom": false, "description": "fx rate date (yyyy-MM-dd)"}, {"_id": "300", "path": "companyFxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "company fx rate"}, {"_id": "310", "path": "companyFxRateDivisor", "locale": "", "dataType": "decimal", "isCustom": false, "description": "company fx rate divisor"}, {"_id": "320", "path": "receiptStatus", "locale": "", "dataType": "enum(notReceived,partiallyReceived,received)", "isCustom": false, "description": "receipt status"}, {"_id": "330", "path": "earliestExpectedDate", "locale": "", "dataType": "date", "isCustom": false, "description": "earliest expected date (yyyy-MM-dd)"}, {"_id": "340", "path": "invoiceStatus", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "invoice status"}, {"_id": "350", "path": "status", "locale": "", "dataType": "enum(draft,pending,inProgress,closed,posted,error)", "isCustom": false, "description": "status"}, {"_id": "360", "path": "totalAmountExcludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total amount excluding tax"}, {"_id": "370", "path": "totalAmountExcludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "total amount excluding tax in company currency"}, {"_id": "380", "path": "approvalStatus", "locale": "", "dataType": "enum(draft,pendingApproval,approved,rejected,changeRequested,confirmed)", "isCustom": false, "description": "approval status"}, {"_id": "390", "path": "displayStatus", "locale": "", "dataType": "enum(draft,pendingApproval,approved,rejected,partiallyReceived,received,closed,taxCalculationFailed,confirmed)", "isCustom": false, "description": "display status"}, {"_id": "400", "path": "/siteAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "site address"}, {"_id": "410", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "420", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "430", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "440", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "450", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "460", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "470", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "480", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "490", "path": "/supplierAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier address"}, {"_id": "500", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "510", "path": "addressLine1#1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "520", "path": "addressLine2#1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "530", "path": "city#1", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "540", "path": "region#1", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "550", "path": "postcode#1", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "560", "path": "country#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "570", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "580", "path": "/supplierContact", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier contact"}, {"_id": "590", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "600", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "610", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "620", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "630", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "640", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "650", "path": "locationPhoneNumber#2", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "660", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "670", "path": "#lines", "locale": "", "dataType": "collection", "isCustom": false, "description": "lines"}, {"_id": "680", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "690", "path": "siteLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "site linked address (#businessEntity|_sortValue)"}, {"_id": "700", "path": "taxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}, {"_id": "710", "path": "taxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "720", "path": "exemptAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "730", "path": "taxDate", "locale": "", "dataType": "date", "isCustom": false, "description": "tax date (yyyy-MM-dd)"}, {"_id": "740", "path": "taxAmountAdjusted", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "750", "path": "internalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "760", "path": "externalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "external note"}, {"_id": "770", "path": "isExternalNote#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is external note (false/true)"}, {"_id": "780", "path": "isPurchaseOrderSuggestion#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase order suggestion (false/true)"}, {"_id": "790", "path": "approvalStatus#1", "locale": "", "dataType": "enum(draft,pendingApproval,approved,rejected,changeRequested,confirmed)", "isCustom": false, "description": "approval status"}, {"_id": "800", "path": "changeRequestedDescription#1", "locale": "", "dataType": "string", "isCustom": false, "description": "change requested description"}, {"_id": "810", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "820", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "830", "path": "*item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "840", "path": "itemDescription", "locale": "", "dataType": "string", "isCustom": false, "description": "item description"}, {"_id": "850", "path": "stockSite#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "860", "path": "stockSiteLinkedAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site linked address (#businessEntity|_sortValue)"}, {"_id": "870", "path": "stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "880", "path": "text#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "text"}, {"_id": "890", "path": "expectedReceiptDate", "locale": "", "dataType": "date", "isCustom": false, "description": "expected receipt date (yyyy-MM-dd)"}, {"_id": "900", "path": "unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase unit (#id)"}, {"_id": "910", "path": "unitToStockUnitConversionFactor", "locale": "", "dataType": "decimal", "isCustom": false, "description": "purchase unit to stock unit conversion factor"}, {"_id": "920", "path": "quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity"}, {"_id": "930", "path": "grossPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "gross price"}, {"_id": "940", "path": "quantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity in stock unit"}, {"_id": "950", "path": "priceOrigin", "locale": "", "dataType": "enum(manual,supplierPriceList)", "isCustom": false, "description": "price origin"}, {"_id": "960", "path": "netPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "net price"}, {"_id": "970", "path": "lineReceiptStatus", "locale": "", "dataType": "enum(notReceived,partiallyReceived,received)", "isCustom": false, "description": "line receipt status"}, {"_id": "980", "path": "lineInvoiceStatus", "locale": "", "dataType": "enum(notInvoiced,partiallyInvoiced,invoiced)", "isCustom": false, "description": "line invoice status"}, {"_id": "990", "path": "status", "locale": "", "dataType": "enum(draft,pending,inProgress,closed,posted,error)", "isCustom": false, "description": "line status"}, {"_id": "1000", "path": "amountExcludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax"}, {"_id": "1010", "path": "amountExcludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount excluding tax in company currency"}, {"_id": "1020", "path": "amountIncludingTax", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax"}, {"_id": "1030", "path": "amountIncludingTaxInCompanyCurrency", "locale": "", "dataType": "decimal", "isCustom": false, "description": "line amount including tax in company currency"}, {"_id": "1040", "path": "##taxes", "locale": "", "dataType": "collection", "isCustom": false, "description": "taxes"}, {"_id": "1050", "path": "taxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax rate"}, {"_id": "1060", "path": "taxCategoryReference", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax category reference (#id)"}, {"_id": "1070", "path": "taxReference", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax reference (#id)"}, {"_id": "1080", "path": "deductibleTaxRate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax rate"}, {"_id": "1090", "path": "isReverseCharge", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is reverse charge (false/true)"}, {"_id": "1100", "path": "isTaxMandatory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tax mandatory (true/false)"}, {"_id": "1110", "path": "isSubjectToGlTaxExcludedAmount", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is subject to gl tax excluded amount (false/true)"}, {"_id": "1120", "path": "jurisdictionName", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction name"}, {"_id": "1130", "path": "numberOfTaxableUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of taxable units"}, {"_id": "1140", "path": "numberOfNonTaxableUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of non taxable units"}, {"_id": "1150", "path": "numberOfExemptUnits", "locale": "", "dataType": "decimal", "isCustom": false, "description": "number of exempt units"}, {"_id": "1160", "path": "country#2", "locale": "", "dataType": "string", "isCustom": false, "description": "country"}, {"_id": "1170", "path": "region#2", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "1180", "path": "jurisdictionCode", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction code"}, {"_id": "1190", "path": "jurisdictionType", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction type"}, {"_id": "1200", "path": "taxCategory", "locale": "en-US", "dataType": "localized text", "isCustom": false, "description": "\"default locale: en-US, other locales can be specified with (locale-name), for example \"\"tax category (de-DE)\"\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1210", "path": "tax", "locale": "en-US", "dataType": "localized text", "isCustom": false, "description": "\"default locale: en-US, other locales can be specified with (locale-name), for example \"\"tax (de-DE)\"\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1220", "path": "deductibleTaxAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax amount"}, {"_id": "1230", "path": "taxAmountAdjusted#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "1240", "path": "nonTaxableAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "non taxable amount"}, {"_id": "1250", "path": "taxAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "1260", "path": "exemptAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "1270", "path": "taxableAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}, {"_id": "1280", "path": "##purchaseRequisitionLines", "locale": "", "dataType": "collection", "isCustom": false, "description": "purchase requisition lines"}, {"_id": "1290", "path": "orderedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "ordered quantity"}, {"_id": "1300", "path": "orderedQuantityInStockUnit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "ordered quantity in stock unit"}, {"_id": "1310", "path": "//workInProgress", "locale": "", "dataType": "reference", "isCustom": false, "description": "work in progress"}, {"_id": "1320", "path": "remainingQuantityToAllocate", "locale": "", "dataType": "decimal", "isCustom": false, "description": "remaining quantity to allocate"}, {"_id": "1330", "path": "documentType", "locale": "", "dataType": "enum(workOrder,materialNeed,purchaseOrder,purchaseReceipt,purchaseReturn,salesOrder)", "isCustom": false, "description": "document type"}, {"_id": "1340", "path": "item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1350", "path": "site#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "1360", "path": "status#1", "locale": "", "dataType": "enum(firm,planned,suggested,closed)", "isCustom": false, "description": "status"}, {"_id": "1370", "path": "startDate", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (yyyy-MM-dd)"}, {"_id": "1380", "path": "endDate", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (yyyy-MM-dd)"}, {"_id": "1390", "path": "expectedQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "expected quantity"}, {"_id": "1400", "path": "actualQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "actual quantity"}, {"_id": "1410", "path": "outstandingQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "outstanding quantity"}, {"_id": "1420", "path": "//stockSiteAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site address"}, {"_id": "1430", "path": "*name#2", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "1440", "path": "addressLine1#2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "1450", "path": "addressLine2#2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "1460", "path": "city#2", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "1470", "path": "region#3", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "1480", "path": "postcode#2", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "1490", "path": "country#3", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "1500", "path": "locationPhoneNumber#3", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "1510", "path": "//stockSiteContact", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site contact"}, {"_id": "1520", "path": "*title#1", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "1530", "path": "*firstName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "1540", "path": "*lastName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "1550", "path": "preferredName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "1560", "path": "role#1", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "1570", "path": "position#1", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "1580", "path": "locationPhoneNumber#4", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "1590", "path": "email#1", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "1600", "path": "##discountCharges", "locale": "", "dataType": "collection", "isCustom": false, "description": "discount charges"}, {"_id": "1610", "path": "sign", "locale": "", "dataType": "enum(increase,decrease)", "isCustom": false, "description": "sign"}, {"_id": "1620", "path": "valueType", "locale": "", "dataType": "enum(percentage,amount)", "isCustom": false, "description": "value type"}, {"_id": "1630", "path": "calculationBasis", "locale": "", "dataType": "enum(grossPrice,grossPriceAndCompound)", "isCustom": false, "description": "calculation basis"}, {"_id": "1640", "path": "calculationRule", "locale": "", "dataType": "enum(byUnit,byLine)", "isCustom": false, "description": "calculation rule"}, {"_id": "1650", "path": "basisDeterminated", "locale": "", "dataType": "decimal", "isCustom": false, "description": "basis determinated"}, {"_id": "1660", "path": "*value", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value"}, {"_id": "1670", "path": "valueDeterminated", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value determinated"}, {"_id": "1680", "path": "basis", "locale": "", "dataType": "decimal", "isCustom": false, "description": "basis"}, {"_id": "1690", "path": "amount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "amount"}, {"_id": "1700", "path": "#taxes", "locale": "", "dataType": "collection", "isCustom": false, "description": "taxes"}, {"_id": "1710", "path": "taxRate#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax rate"}, {"_id": "1720", "path": "taxCategoryReference#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax category reference (#id)"}, {"_id": "1730", "path": "taxReference#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax reference (#id)"}, {"_id": "1740", "path": "deductibleTaxRate#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax rate"}, {"_id": "1750", "path": "isReverseCharge#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is reverse charge (false/true)"}, {"_id": "1760", "path": "isTaxMandatory#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tax mandatory (true/false)"}, {"_id": "1770", "path": "isSubjectToGlTaxExcludedAmount#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is subject to gl tax excluded amount (false/true)"}, {"_id": "1780", "path": "jurisdictionName#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction name"}, {"_id": "1790", "path": "country#4", "locale": "", "dataType": "string", "isCustom": false, "description": "country"}, {"_id": "1800", "path": "region#4", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "1810", "path": "jurisdictionCode#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction code"}, {"_id": "1820", "path": "jurisdictionType#1", "locale": "", "dataType": "string", "isCustom": false, "description": "jurisdiction type"}, {"_id": "1830", "path": "taxCategory#1", "locale": "en-US", "dataType": "localized text", "isCustom": false, "description": "\"default locale: en-US, other locales can be specified with (locale-name), for example \"\"tax category (de-DE)\"\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1840", "path": "tax#1", "locale": "en-US", "dataType": "localized text", "isCustom": false, "description": "\"default locale: en-US, other locales can be specified with (locale-name), for example \"\"tax (de-DE)\"\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\""}, {"_id": "1850", "path": "deductibleTaxAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "deductible tax amount"}, {"_id": "1860", "path": "taxAmountAdjusted#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount adjusted"}, {"_id": "1870", "path": "nonTaxableAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "non taxable amount"}, {"_id": "1880", "path": "taxAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tax amount"}, {"_id": "1890", "path": "exemptAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "exempt amount"}, {"_id": "1900", "path": "taxableAmount#2", "locale": "", "dataType": "decimal", "isCustom": false, "description": "taxable amount"}]}