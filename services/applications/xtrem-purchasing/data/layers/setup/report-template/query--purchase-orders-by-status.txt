{
  xtremPurchasing{
    purchaseOrder{
      query(orderBy:"{status:1}",first:500){
        edges{
          node{
            status
            number
            orderDate
            totalAmountExcludingTax
            lines{
              query{
                totalCount
              }
            }
            currency{
              symbol
            }
          }
        }
      }
    }
  }
}
