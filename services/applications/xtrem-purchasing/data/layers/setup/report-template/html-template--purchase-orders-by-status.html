<div class="main">
  {{#each xtremPurchasing.purchaseOrder.query.edges}} {{#printBreakIfPropertyChanged 'node.status'}}
  <h2>{{enumValue '@sage/xtrem-purchasing/PurchaseDocumentStatus' node.status}} orders</h2>
  <table class="header-table">
    <thead>
      <tr>
        <th class="column-left">Number</th>
        <th class="column-left">Order Date</th>
        <th class="column-right">Order Total</th>
        <th class="column-right">Number of Lines</th>
      </tr>
    </thead>
    <tbody>
      {{/printBreakIfPropertyChanged}}
      <tr>
        <td><strong>{{node.number}}</strong></td>
        <td>{{node.orderDate}}</td>
        <td class="column-right">{{node.currency.symbol}} {{formatNumber node.totalAmountExcludingTax 2}}</td>
        <td class="column-right">{{node.lines.query.totalCount}}</td>
      </tr>
      {{#printBreakIfPropertyWillChange 'node.status'}}
    </tbody>
  </table>
  <div class="page-break"></div>
  {{/printBreakIfPropertyWillChange}} {{/each}}
</div>
