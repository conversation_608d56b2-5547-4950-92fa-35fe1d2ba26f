<div>
  {{#with xtremPurchasing.purchaseOrder.query.edges.0.node}}
  <div>
    <h2>{{ translatedContent "fa8f1007a7d303a1f40794baaf90933b" }} {{number}}</h2>
    <div>
      <table class="header-table">
        <thead>
          <tr>
            <th class="column-left">{{ translatedContent "ec9ceefe0c19f4c029008c23e3c947b7" }}</th>
            <th class="column-left">{{ translatedContent "74002cb040b036511b8d031da591364a" }}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              {{#with supplierAddress}}
              <strong>{{name}}</strong>
              <br />
              {{addressLine1}}<br />
              {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
              {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{postcode}} {{city}}<br />
              {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
            </td>
            <td>
              {{#with siteAddress}}
              <strong>{{name}}</strong>
              <br />
              {{addressLine1}}<br />
              {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
              {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{postcode}} {{city}}<br />
              {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
            </td>
          </tr>
        </tbody>
      </table>
      <table class="header-table">
        <thead>
          <tr>
            <th class="column-left">{{ translatedContent "b81f4425b7556f2c97a12cb81ed44824" }}</th>
            <th class="column-left">{{ translatedContent "6fbc355a02359656f1e4540d58b784e1" }}</th>
            <th class="column-left">{{ translatedContent "aa9197f2f967c5f4835370031aebf17e" }}</th>
            <th class="column-left">{{ translatedContent "43606e0ad84eb4534bb72300cd68d774" }}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{number}}</td>
            <td>{{formatDate orderDate}}</td>
            <td>{{paymentTerm.name}}</td>
            <td>{{deliveryMode.name}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
<h4>{{ translatedContent "acc18bb887587768e6c89795532a4f45" }}</h4>
<table class="lines-table">
  <thead>
    <tr>
      <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
      <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
      <th class="column-right">{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</th>
      <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
      <th class="column-left">{{ translatedContent "5e8371ee34a981b6821216311a6bfbf2" }}</th>
      <th class="column-right">{{ translatedContent "59257252522b1ca42aef1046e4e50778" }}</th>
      <th class="column-right">{{ translatedContent "1b5e79adf07a2927cb97273a3b152bc1" }}</th>
      <th class="column-right">{{ translatedContent "e2773b5f3378c679f529106712a9b132" }}</th>
      {{#if (isnt taxEngine "avalaraAvaTax")}}
      <th class="column-right">{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}</th>
      <th class="column-right">{{ translatedContent "dd8085978ee9ea0602122eb2e2983ab3" }}</th>
      {{/if}}
    </tr>
  </thead>
  <tbody>
    {{#each lines.query.edges}}
    <tr>
      <td class="column-left"><strong>{{node.item.name}}</strong></td>
      <td class="column-left">{{node.item.description}}</td>
      <td class="column-right">{{formatNumber node.quantity 2}}</td>
      <td class="column-left">{{node.purchaseUnit.name}}</td>
      <td class="column-left">{{formatDate node.expectedReceiptDate}}</td>
      <td class="column-right">{{node.currency.symbol}} {{formatNumber node.grossPrice node.stockSite.legalCompany.priceScale}}</td>
      <td class="column-right">{{node.currency.symbol}} {{formatNumber node.netPrice node.stockSite.legalCompany.priceScale}}</td>
      <td class="column-right">{{node.currency.symbol}} {{formatNumber node.amountExcludingTax 2}}</td>
      {{#if (isnt ../taxEngine "avalaraAvaTax")}}
      <td class="column-right">{{formatNumber node.taxes.query.edges.0.node.taxRate 2}}{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</td>
      <td class="column-right">{{node.currency.symbol}} {{formatNumber node.amountIncludingTax 2}}</td>
      {{/if}}
    </tr>
    {{#if node.externalNote.value}}
    <tr>
      <td class="column-center" style="text-align: center; border: 0">
        <p><b>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</b></p>
      </td>
      <td class="column-left" colspan="9" style="text-align: left; border: 0">{{{node.externalNote.value}}}</td>
    </tr>
    {{/if}} {{/each}}
  </tbody>
</table>
<div class="article">
  <table class="header-table">
    <tbody>
      <tr>
        <td class="column-left" width="40%"></td>
        <td class="column-right">
          <table class="header-table">
            <thead>
              <tr>
                <th class="column-left">{{ translatedContent "d41cb50311de5b1572758d2233c1a66a" }}</th>
                {{#if (isnt taxEngine "avalaraAvaTax")}}
                <th class="column-left">{{ translatedContent "c87c9e5cac56d73f55fab44ab6c01c64" }}</th>
                <th class="column-left">{{ translatedContent "feaec14f665d58545b3630df9b07fb6b" }}</th>
                {{/if}}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="column-right">{{currency.symbol}} {{formatNumber totalAmountExcludingTax 2}}</td>
                {{#if (isnt taxEngine "avalaraAvaTax")}}
                <td class="column-right">{{currency.symbol}} {{formatNumber totalTaxAmount 2}}</td>
                <td class="column-right">{{currency.symbol}} {{formatNumber totalAmountIncludingTax 2}}</td>
                {{/if}}
              </tr>
            </tbody>
          </table>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div>
  {{#if externalNote.value}}
  <table style="width: 100%">
    <tbody>
      <tr>
        <td style="text-align: center; width: 5%; border: 0">
          <p><b>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</b></p>
        </td>
        <td style="text-align: left; border: 0">{{{externalNote.value}}}</td>
      </tr>
    </tbody>
  </table>
  {{/if}}
</div>
{{/with}}
