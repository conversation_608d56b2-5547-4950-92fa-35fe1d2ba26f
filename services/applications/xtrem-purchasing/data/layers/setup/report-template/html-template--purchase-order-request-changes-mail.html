<!DOCTYPE html
    PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml"
    xmlns:o="urn:schemas-microsoft-com:office:office">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Xtrem</title>
    <!-- Brand_EmailTemplate_V5.7.2 -->

    <!--<link rel="icon" type="image/x-icon" href="https://www.sage.com/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="https://www.sage.com/favicon.ico">-->

    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <style>
        li {
            text-indent: -1em; /* Normalise space between bullets and text */
        }
    </style>
    <![endif]-->

    <!--[if (gte mso 9)|(IE)]>
    <style type="text/css">
        table {
            border-collapse: collapse;
            border-spacing: 0;
            mso-line-height-rule: exactly;
            mso-margin-bottom-alt: 0;
            mso-margin-top-alt: 0;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
    </style>
    <![endif]-->

    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
    <!--[if gt mso 15]>
    <style type="text/css" media="all">
        /* Outlook 2016 Height Fix */
        table, tr, td {
            border-collapse: collapse;
        }

        tr {
            font-size: 0px;
            line-height: 0px;
            border-collapse: collapse;
        }
    </style>
    <![endif]-->

    <style type="text/css">
        /* Client-specific styles */
        .ExternalClass {
            width: 100%;
        }

        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        body {
            background-image: none;
            background-repeat: repeat;
            background-position: top left;
            background-attachment: scroll;
            -ms-text-size-adjust: none;
            -webkit-text-size-adjust: none;
            -webkit-font-smoothing: antialiased;
            margin-top: 0;
            margin-bottom: 0;
            margin-right: 0;
            margin-left: 0;
            padding-top: 0;
            padding-bottom: 0;
            padding-right: 0;
            padding-left: 0;
            color: rgb(0, 0, 0, .9);
        }

        a[href^=tel] {
            color: #FFFFFF;
            text-decoration: none;
        }

        img {
            display: block;
            border: none;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        table td,
        table th {
            border-collapse: collapse;
        }

        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            table-layout: fixed;
        }

        tr {
            font-size: 0px;
            line-height: 0px;
            border-collapse: collapse;
        }

        table table {
            table-layout: auto;
        }

        a {
            color: #008200;
        }

        a:hover {
            color: #009000 !important;
        }

        a img {
            border: none;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }
        }

        body,
        table,
        td,
        a {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        img {
            -ms-interpolation-mode: bicubic;
        }

        /* Reset styles */

        img {
            border: 0 !important;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        table td {
            border-collapse: collapse !important;
        }

        body {
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
        }

        /* iOS blue links */

        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }

        .content-calendar {
            padding: 15px 0 30px 4%;

        }

        /* Global styles */

        body,
        table,
        td {
            font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;
            font-size: 16px;
            font-weight: 300;
            line-height: 1.5;
            color: #191919
        }


        /* Button style */

        .sage-button {
            background-color: #009000;
            display: inline-block;
            cursor: pointer;
            color: #ffffff;
            text-decoration: none;
            width: 232px;
        }

        .sage-button a {
            color: #ffffff;
        }

        .sage-button:hover {
            background-color: #008000;

        }

        .sage-button a:hover {
            color: #ffffff !important;

        }

        .sage-button-white {
            background-color: #ffffff;
            display: inline-block;
            cursor: pointer;
            color: #191919;
            text-decoration: none;
            width: 232px;
        }

        .sage-button-white a {

            color: #191919;

        }

        .sage-button-white:hover {
            background-color: #eeeeee;

        }

        .sage-button-white a:hover {

            color: #191919 !important;

        }

        .sage-trans-button {
            background-color: transparent;
            display: inline-block;
            cursor: pointer;
            color: #008200;
            text-decoration: none;
            border-color: #008200;
            border-style: solid;
            border-width: 1px;
            width: 232px;

        }

        .sage-trans-button a {
            color: #008200;
        }

        .sage-button:active,
        .sage-button-white:active {
            position: relative;
            top: 1px;
        }

        .SBC-icons {

            /*height:170px;*/
            height: 125px;
            display: block !important;
            padding: 0px !important;

        }

        a.SBC-icon-title:link {

            text-align: center;
            color: #004b87;
            font-size: 20px;
            font-weight: 300;

        }

        .SBC-icon-title {

            text-align: center;
            color: #004b87;
            font-size: 20px;
            font-weight: 300;

        }

        /* Media query: scale from 600px to 480px break point */

        @media screen and (max-width: 600px) {
            h1 {
                font-size: 32px;
                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
                line-height: 1.25 !important;

            }

            h2 {
                font-size: 24px;
                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
                line-height: 1.25 !important;
            }

            h3 {
                font-size: 16px;
                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
            }

            .img-max {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                min-width: 100px !important;
            }

            .img-max-center {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                min-width: 100px !important;
                text-align: center !important;
                float: none !important;
                margin: 0 auto !important;
                padding: 0 !important;
            }

            .img-max-pad-top {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                min-width: 100px !important;
                padding-top: 10px !important;
            }

            .container {
                width: 100% !important;
                max-width: 600px !important;
                height: auto !important;
                min-width: 0 !important;
            }

            .img-pad-top {
                padding-top: 10px;
            }

            .pad-bottom {
                padding-bottom: 20px;
            }

            .pad-bottom-center {
                padding-bottom: 20px;
                margin-left: auto !important;
                margin-right: auto !important;
            }

            .extra-marg-bottom {
                margin-bottom: 30px !important;
            }

            .no-pad-bottom {
                padding-bottom: 0px !important;

            }

            .no-pad-bottom-center {
                padding-bottom: 0px !important;
                margin-left: auto !important;
                margin-right: auto !important;
                float: none !important;
            }

            .text-center-right {
                text-align: center !important;
                padding-right: 20px !important;
                padding-left: 0px !important;
            }

            .text-center-right-pad {
                text-align: center !important;
                padding-left: 0px !important;
            }

            .center-table {
                text-align: center !important;
            }

            .text-center {
                text-align: center !important;
            }

            .text-center-75 {
                text-align: center !important;
                max-width: 75% !important;
            }

            .ul-style {
                padding-left: 20px !important;
            }

            .max-width {
                max-width: 100% !important;
                width: 100% !important;

            }

            .max-width-remove-height {
                max-width: 100% !important;
                width: 100% !important;
                ;
                height: auto !important;
            }

            .max-width-center {
                max-width: 100% !important;
                width: 100% !important;

                text-align: center !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .max-width-pad {
                max-width: 100% !important;
                width: 100% !important;
                margin-bottom: 12px !important;
            }

            .max-width-top-pad {
                max-width: 100% !important;
                width: 100% !important;

                margin-top: 20px !important;
            }


            .max-width-no-pad {
                max-width: 100% !important;
                width: 100% !important;

            }

            .max-width-no-pad-l {
                max-width: 100% !important;
                width: 100% !important;
                padding-left: 0 !important;
            }

            .max-width-no-pad-a {
                max-width: 100% !important;
                width: 100% !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                padding-bottom: 0 !important;
                padding-top: 0 !important;
            }

            .max-width-center-pad {
                max-width: 100% !important;
                width: 100% !important;

                text-align: center !important;
                margin: 0 auto !important;
                margin-bottom: 30px !important;
            }

            .mobile-wrapper {
                width: 90% !important;
                max-width: 90% !important;
            }

            .mobile-padding {
                padding-left: 5% !important;
                padding-right: 5% !important;
            }

            .fix-margin {
                margin-top: 0px;
                padding-top: 0px;
            }

            .mobile-center {
                text-align: center !important;
                padding-bottom: 5px !important;
            }

            .mobile-stacked {
                padding-top: 12px !important;
                padding-bottom: 12px !important;
            }


            .remove-height {
                height: auto !important;
            }

            .remove-pad {
                padding: 0px !important;
            }

            .content {
                padding-left: 25px !important;
                padding-right: 25px !important;
            }

            .sage_logo {
                width: auto !important;
                height: 32px !important;
            }


            .desktop-masthead {
                width: 100% !important;
                height: auto !important;
            }

            .img-center {
                margin-left: auto !important;
                margin-right: auto !important;
            }

            .two-col-icons {
                width: 50% !important;
                height: 50% !important;
                display: block;
            }

            .quote-signature {
                padding-left: 10px !important;
            }

            .SBC-icons {
                width: 138px !important;
                height: auto !important;
            }

            .content-SBC-icons {
                padding-left: 40px !important;
                padding-right: 40px !important;
            }

            .content-calendar {
                padding: 15px 0 30px 14%;
            }


            /* Media query for mobile viewport
             * Developer:  hero graphics should be 2 x width for HD rendering.
             */
            @media only screen and (max-width: 480px) {

                h1 {

                    font-size: 24px !important;
                    font-weight: bold;
                }

                h2 {
                    font-size: 20px !important;
                    font-weight: bold;
                }

                h3 {
                    font-size: 16px;
                    font-weight: bold;
                }

                table[class=max-width-pad] {
                    max-width: 100% !important;
                    width: 100% !important;
                    padding-top: 15px !important;
                    height: auto !important;
                }

                table[class=max-width] {
                    max-width: 100% !important;
                    width: 100% !important;
                    height: auto !important;
                }

                table[class=container] {
                    margin: 0 auto !important;
                }

                .desktop-masthead {
                    display: none !important;
                }

                *[class].hidden. *[class=desktop-masthead] {
                    display: none !important;
                }

                *[class].elastic {
                    width: 100% !important;
                    height: auto !important;
                }

                *[class].centered {
                    text-align: center !important;
                }

                *[class].fluid,
                [class=fluid-mob] {
                    width: 100% !important;
                }

                [class=fluid-mob] {
                    position: relative;
                }

                .mobile-show {
                    display: table-cell !important;
                    width: auto !important;
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                    visibility: visible !important;
                    position: relative !important;
                    text-align: center !important;
                }

                .show-mob {
                    display: block !important;
                    max-height: none !important;
                    width: auto !important;
                    visibility: visible !important;
                }

                *[class].mob-masthead {
                    width: 100% !important;
                    display: block !important;
                    height: auto !important;
                    max-height: none !important;
                    padding: 0 !important;
                }

                *[class].mob-masthead-img {
                    position: absolute !important;
                    top: 0px !important;
                    display: block !important;
                    height: auto !important;
                    max-height: none !important;
                    padding: 0 !important;
                    width: auto !important;
                }

                [class=fluid-mob] {
                    display: table-cell !important;
                    width: auto !important;
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                    visibility: visible !important;
                    position: relative !important;
                    text-align: center !important;
                }

                .SBC-icons {
                    margin: 2px;
                    height: auto !important;
                }

                .content-quote {
                    margin-top: 20px
                }

                .quote-signature {
                    padding-left: 0px !important;
                }

            }

            @media only screen and (max-width: 410px) {
                .SBC-icons {

                    height: auto !important;
                    clear: none;

                }

                .content-SBC-icons {
                    padding-left: 35px !important;
                    padding-right: 15px !important;
                    margin: 4px;

                }

                .content-calendar {
                    padding: 15px 7% 30px;
                }
            }
    </style>
</head>

<body bgcolor="#dddddb"
    style="margin: 0 auto !important; padding: 0 !important; background-color: #F2F5F6; font-family:  AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; color:#191919; font-size: 16px">

    <table class="ab_section" data-description="Body" border="0" bgcolor="#F2F5F6" cellpadding="0" cellspacing="0"
        width="100%"
        style="padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt; background-color: #F2F5F6">
        <tr>
            <td align="center" style="text-align: center">
                <table bgcolor="#ffffff" border="0" cellpadding="0" cellspacing="0" align="center" width="600"
                    style="display: table; width: 600px; background-color: #ffffff; margin: 0 auto !important; padding: 0px; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse:collapse; border-style: solid; border-width: 1px; border-color: #cccccc"
                    class="container">
                    <!-- Start: A1 Top logo -->
                    <!-- Instructions: After changing the logo image and adjusting the "height" and both "width" values below, review the .sage_logo class in the media query CSS -->
                    <!-- <tr class="ab_cloneable" data-description="A1 Top Logo"> -->
                    <!-- <td align="center" valign="top" style="background-color:#FFFFFF; padding: 17px 30px 17px 30px"> -->
                    <!-- <table cellspacing="0" border="0" align="center" -->
                    <!-- style=" padding: 0; margin: 0; padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0"> -->
                    <!-- <tr> -->
                    <!-- <td align="center" valign="top"><a class="ab_editable" href="http://sage.com" -->
                    <!-- data-description="Sage Logo Link"> -->
                    <!-- <img -->
                    <!-- src="cid:logo-sage.png" -->
                    <!-- class="ab_editable sage_logo" data-description="Sage Logo Image" width="80" -->
                    <!-- border="0" alt="Sage" -->
                    <!-- style="width:100%; max-width: 80px; margin-left: auto !important; margin-right: auto !important"> -->
                    <!-- </a> -->
                    <!-- </td> -->
                    <!-- </tr> -->
                    <!-- </table> -->
                    <!-- </td> -->
                    <!-- </tr> -->
                    <!-- End: A1 Top logo -->

                    <!-- Start: B1 Hero banner image 1 1200px (2 x 600px for HD); breakpoint at 480px -->
                    <tr>
                        <td align="center" valign="top" class="ab_cloneable" data-description="B1 Hero banner image 1"
                            style="margin:0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
                            <!-- B1 Mobile email header -->
                            <!--[if !mso]><!-->
                            <table cellpadding="0" cellspacing="0" border="0" class="fluid"
                                style="display:table; padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
                                <tbody>
                                    <tr>
                                        <td
                                            style="margin:0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
                                            <div class="show-mob"
                                                style="font-size: 0; max-height: 0; overflow: hidden; display: none; line-height:0; visibility:hidden;">
                                                <table width="100%" cellpadding="0" cellspacing="0" border="0">
                                                    <tr>
                                                        <th width="600" align="center" valign="top" bgcolor="#51534a"
                                                            class="fluid"
                                                            style="width:600px; padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt;">
                                                            <table width="100%" cellpadding="0" cellspacing="0"
                                                                border="0" bgcolor="#51534A" class="fluid-mob"
                                                                style="mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt; font-size: 0; max-height: 0; overflow: hidden; display: none; line-height:0; visibility:hidden;">
                                                                <tr>
                                                                    <td align="left" bgcolor="#51534A"
                                                                        class="mobile-show"
                                                                        style="font-size: 0; max-height: 0; overflow: hidden; display: none; line-height:0; visibility:hidden;">
                                                                        <table width="100%" border="0" cellpadding="0"
                                                                            cellspacing="0">
                                                                            <tr>
                                                                                <td align="center"
                                                                                    style="background-color: #ffffff"
                                                                                    class="mobile-show">
                                                                                    <a class="ab_editable"
                                                                                        href="http://place-holder.com">
                                                                                        <h1
                                                                                            style="margin-top:20px; margin-left:10px;">
                                                                                            Purchase order changes requested</h1>
                                                                                    </a>
                                                                                </td>
                                                                            </tr>
                                                                        </table>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </th>
                                                    </tr>
                                                </table>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <!--<![endif]-->
                            <!-- End B1 Mobile email header -->

                            <!-- Desktop B1 email header -->
                            <table width="100%" cellpadding="0" cellspacing="0" border="0" class="fluid"
                                style="margin: 0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt; background-color:#f2f5f6;">
                                <tr>
                                    <td align="center"
                                        style="margin: 0;padding-top:0;padding-right:0px;padding-bottom:0px;padding-left:0px; mso-cellspacing: 0px; mso-padding-alt: 0px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt;background-color:#ffffff;">
                                        <h1 style="margin-top:20px; margin-left:10px;">Purchase order changes requested</h1>
                                    </td>
                                </tr>
                                <tr>
                                    <td align="center"
                                        style="margin: 0; mso-cellspacing: 0px; mso-padding-alt: 20px 0px 0px 0px; border-collapse:collapse; mso-table-lspace:0pt;mso-table-rspace:0pt">
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- Start: C1 Single column copy -->
                    <tr class="ab_cloneable" data-description="C1 Single column copy">
                        <td align="center" valign="top"
                            style="background-color: #f2f5f6; padding: 32px 32px 32px 32px; mso-table-lspace: 0pt; mso-table-rspace: 0pt; border-collapse:collapse"
                            class="content">
                            <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%"
                                style="mso-cellspacing: 0px; mso-padding-alt: 0px; border-collapse:collapse">
                                <tr>
                                    <td valign="top" align="left"
                                        style="font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; font-weight: 300; color: #191919; font-size: 16px; line-height: 1.5">
                                        <span class="ab_editable" data-rawhtml="true">
                                            Hello,
                                            <br />
                                            <br />
                                            You have a pending change request for : <h3
                                                style="font-size: 16px; font-weight: bold; margin: 12px 0; line-height: 1.25"
                                                class="ab_editable">Purchase order {{number}} requested by {{requester}} from {{receivingSite}} on {{requestDate}}
                                            </h3>
                                            <br />
                                            You can find details for this request by following this link :
                                            <a href="{{urlPurchaseDocument}}" target="_blank"
                                                style="font-size: 16px; font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue; display: inline-block; font-weight: bold;  text-decoration: underline; color:#008200;"
                                                class="red ab_editable"><span class="ab_editable"
                                                    data-rawhtml="true">Click here</span></a>
                                            <br />
                                            <br /></td>
                                </tr>
                                <tr>
                                    <td align="center" valign="top" class="max-width" width="100%"
                                        style="font-size:0;  padding-bottom: 30px; mso-padding-alt: 20px 0px 30px 0px;">                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <!-- End: C1 Single column copy -->

                </table>
                <br><br>
            </td>
        </tr>
    </table>
</body>

</html>
<html><head><META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=utf-8"><meta name="Robots" content="NOINDEX " /></head><body></body>
                <script type="text/javascript">
                 var gearPage = document.getElementById('GearPage');
                 if(null != gearPage)
                 {
                     gearPage.parentNode.removeChild(gearPage);
                     document.title = "Error";
                 }
                 </script>
                 </html>