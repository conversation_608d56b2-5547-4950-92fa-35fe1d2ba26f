query {
    YIAfvLiP: xtremPurchasing {
        purchaseOrder {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{order}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        stockSite {
                            name
                            primaryAddress {
                                addressLine1
                                addressLine2
                                city
                                country {
                                    name
                                }
                            }
                        }
                        number
                        orderDate
                        paymentTerm {
                            name
                        }
                        deliveryMode {
                            name
                        }
                        supplierAddress {
                            name
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            city
                            region
                            postcode
                        }
                        siteAddress {
                            postcode
                            name
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            city
                            region
                        }
                        USdXOakV: lines {
                            query (filter: "{}", orderBy: "{\"item\":{\"name\":1,\"salesUnit\":{\"name\":1}},\"itemDescription\":1,\"quantity\":1,\"expectedReceiptDate\":1,\"grossPrice\":1,\"netPrice\":1,\"amountExcludingTax\":1,\"amountExcludingTaxInCompanyCurrency\":1,\"amountIncludingTax\":1}") {
                                edges {
                                    node {
                                        _id
                                        item {
                                            name
                                        }
                                        itemDescription
                                        quantity
                                        purchaseUnit {
                                            name
                                        }
                                        expectedReceiptDate
                                        currency {
                                            symbol
                                        }
                                        grossPrice
                                        netPrice
                                        amountExcludingTax
                                        SMrJsXFX: taxes {
                                            query (filter: "{}", orderBy: "{}", first: 1) {
                                                edges {
                                                    node {
                                                        taxRate
                                                        _id
                                                    }
                                                }
                                            }
                                        }
                                        amountIncludingTax
                                    }
                                }
                            }
                        }
                        currency {
                            symbol
                        }
                        totalAmountExcludingTax
                        totalTaxAmount
                        totalAmountIncludingTax
                        isExternalNote
                        externalNote {
                            value
                        }
                        _id
                    }
                }
            }
        }
    }
}
