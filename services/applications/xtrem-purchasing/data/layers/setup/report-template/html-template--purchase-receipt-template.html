<div>
  {{#with xtremPurchasing.purchaseReceipt.query.edges.0.node}}
  <div>
    <h2>{{ translatedContent "2df202125193d3b5dbd2f0e1c5a74b14" }} {{number}}</h2>
    <div>
      <table class="header-table">
        <thead>
          <tr>
            <th class="column-left">{{ translatedContent "a7d6475ec8993b7224d6facc8cb0ead6" }}</th>
            <th class="column-left">{{ translatedContent "ec136b444eede3bc85639fac0dd06229" }}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              {{#with siteAddress}}
              <strong>{{name}}</strong>
              <br />
              {{addressLine1}}<br />
              {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
              {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{postcode}} {{city}}<br />
              {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
            </td>
            <td>
              {{#with supplierAddress}}
              <strong>{{name}}</strong>
              <br />
              {{addressLine1}}<br />
              {{#if addressLine2}}{{addressLine2}}<br />{{/if}} {{#if (is country.iso31661Alpha3 "GBR")}}{{city}}{{#if region}} {{region}}{{/if}}
              {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}}<br />
              {{else}} {{postcode}} {{city}}<br />
              {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
            </td>
          </tr>
          <tr>
            <td colspan="2"><strong>{{ translatedContent "0823c44a8108610f054f66b6a6d31164" }}</strong></td>
          </tr>
          <tr>
            <td>{{site.taxIdNumber}}</td>
            <td>{{supplier.taxIdNumber}}</td>
          </tr>
        </tbody>
      </table>
      <table style="width: 50%">
        <thead>
          <tr>
            <th class="column-left">{{ translatedContent "e518d57fda2fff897727b9c8ff565f23" }}</th>
            <th class="column-left">{{ translatedContent "7181e5089e84864e11d9edfdfb9a5a9f" }}</th>
            <th class="column-left">{{ translatedContent "914419aa32f04011357d3b604a86d7eb" }}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>{{date}}</td>
            <td>{{supplierDocumentNumber}}</td>
            <td>{{carrier.name}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <table class="lines-table">
    <thead>
      <tr>
        <th class="column-left">{{ translatedContent "3edf8ca26a1ec14dd6e91dd277ae1de6" }}</th>
        <th class="column-left">{{ translatedContent "d87d18e7c85dbee0fd1bbc3ef5fbdd24" }}</th>
        <th class="column-left">{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</th>
        <th class="column-left">{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</th>
        <th class="column-left">{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</th>
        <th class="column-right">{{ translatedContent "b38c75d79396ac1eeb4418513c26e3ce" }}</th>
        <th class="column-right">{{ translatedContent "136a0f767e6b1d8446556df08337e169" }}</th>
      </tr>
    </thead>
    <tbody>
      {{#each lines.query.edges}}
      <tr>
        <td class="column-left">
          {{#if (is node.origin "purchaseOrder")}}{{ translatedContent "d87d18e7c85dbee0fd1bbc3ef5fbdd24" }} {{else}} {{#if (is node.origin "direct")}}{{
          translatedContent "a52e81dc6c79f56bb48fc3ca52dd25b0" }} {{/if}} {{/if}}
        </td>
        <td class="column-left">{{node.purchaseOrderLine.purchaseOrderLine.documentNumber}}</td>
        <td class="column-left">{{node.item.id}}</td>
        <td class="column-left">{{node.item.name}}</td>
        <td class="column-left">{{node.purchaseUnit.name}}</td>
        <td class="column-right">{{formatNumber node.quantity 2}}</td>
        <td class="column-right">{{formatNumber node.purchaseOrderLine.purchaseOrderLine.quantityToReceive 2}}</td>
      </tr>
      {{/each}}
    </tbody>
  </table>
  <br />
  <table class="header-table">
    <tbody>
      <tr>
        <td class="column-center">
          <strong
            >{{#if site.legalCompany.legalForm}}<span style="text-transform: uppercase">{{site.legalCompany.legalForm}}</span>
            {{/if}}{{site.legalCompany.name}}</strong
          >{{#if site.legalCompany.rcs}} {{site.legalCompany.rcs}}{{/if}}{{#if site.legalCompany.naf}}
          <span>{{ translatedContent "2a33a22558b84f226250869393917562" }}</span>
          {{site.legalCompany.naf}}{{/if}}<br />
          {{#with site.legalCompany.addresses.query.edges.0.node}} {{addressLine1}} {{#if addressLine2}}{{addressLine2}}{{/if}} {{#if (is country.iso31661Alpha3
          "GBR")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}} {{else}} {{#if (is country.iso31661Alpha3 "ZAF")}}{{city}}{{#if region}}
          {{region}}{{/if}} {{postcode}} {{else}} {{#if (is country.iso31661Alpha3 "USA")}}{{city}}{{#if region}} {{region}}{{/if}} {{postcode}} {{else}}
          {{postcode}} {{city}} {{/if}} {{/if}} {{/if}} {{country.name}} {{/with}}
        </td>
      </tr>
    </tbody>
  </table>
</div>
{{/with}}
