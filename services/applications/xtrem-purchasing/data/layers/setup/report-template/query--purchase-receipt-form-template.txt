query {
    TQCllewa: xtremPurchasing {
        purchaseReceipt {
            query (filter: "{\"_id\":{\"_and\":[{\"_eq\":\"{{receipt}}\"}]}}", orderBy: "{}", first: 1) {
                edges {
                    node {
                        siteAddress {
                            name
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            city
                            postcode
                        }
                        site {
                            siret
                            taxIdNumber
                            id
                            legalCompany {
                                naf
                                legalForm
                                name
                                rcs
                            }
                        }
                        number
                        date
                        supplier {
                            siret
                            taxIdNumber
                            deliveryMode {
                                description
                            }
                        }
                        supplierDocumentNumber
                        paymentTerm {
                            description
                        }
                        carrier {
                            name
                        }
                        supplierAddress {
                            name
                            city
                            addressLine1
                            addressLine2
                            country {
                                iso31661Alpha3
                                name
                            }
                            postcode
                        }
                        iXDXyXbe: lines {
                            query (filter: "{}", orderBy: "{\"origin\":1,\"item\":{\"id\":1,\"name\":1},\"purchaseUnit\":{\"name\":1},\"quantity\":1}") {
                                edges {
                                    node {
                                        _id
                                        origin
                                        purchaseOrderLine {
                                            purchaseOrderLine {
                                                documentNumber
                                                quantityToReceive
                                            }
                                        }
                                        item {
                                            id
                                            name
                                        }
                                        purchaseUnit {
                                            name
                                        }
                                        quantity
                                    }
                                }
                            }
                        }
                        HvMfhpvW: site {
                            legalCompany {
                                addresses {
                                    query (filter: "{}", orderBy: "{}", first: 1) {
                                        edges {
                                            node {
                                                country {
                                                    iso31661Alpha3
                                                    name
                                                }
                                                city
                                                addressLine1
                                                addressLine2
                                                postcode
                                                _id
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        _id
                    }
                }
            }
        }
    }
}
