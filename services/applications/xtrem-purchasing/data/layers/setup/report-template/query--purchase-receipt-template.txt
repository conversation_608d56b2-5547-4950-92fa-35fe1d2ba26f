{
  xtremPurchasing {
    purchaseReceipt {
      query(filter: "{_id:'{{receipt}}'}") {
        edges {
          node {
            number
            date
            supplierDocumentNumber
            site {
              taxIdNumber
              legalCompany {
              name
              rcs
              naf
              addresses {
                query(filter: "{isPrimary:true}") {
                    edges {
                      node {
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        country {
                          name
                          iso31661Alpha3
                        }
                      }
                    }
                  }
                }
              }
            }
            supplierAddress {
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661Alpha3
              }
            }
            siteAddress {
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661Alpha3
              }
            }
            supplier {
              name
              taxIdNumber
            }
            carrier {
              name
            }
            text {
              value
            }
            isExternalNote
            externalNote {
              value
            }
            lines {
              query(first: 500) {
                edges {
                  node {
                    origin
                     item {
                      id
                      name
                    }
                    quantity
                    remainingQuantityToInvoice
                    purchaseUnit {
                      name
                    }
                    item {
                      name
                      description
                    }
                    isExternalNote
                    externalNote {
                      value
                    }
                    purchaseOrderLine{purchaseOrderLine{documentNumber quantityToReceive }}
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
