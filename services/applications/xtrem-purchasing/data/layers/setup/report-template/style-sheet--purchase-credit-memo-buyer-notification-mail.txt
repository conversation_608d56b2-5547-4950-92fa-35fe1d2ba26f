        /* Client-specific styles */
        .ExternalClass {
            width: 100%;
        }

        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }

        body {
            background-image: none;
            background-repeat: repeat;
            background-position: top left;
            background-attachment: scroll;
            -ms-text-size-adjust: none;
            -webkit-text-size-adjust: none;
            -webkit-font-smoothing: antialiased;
            margin-top: 0;
            margin-bottom: 0;
            margin-right: 0;
            margin-left: 0;
            padding-top: 0;
            padding-bottom: 0;
            padding-right: 0;
            padding-left: 0;
            color: rgb(0, 0, 0, .9);
        }

        a[href^=tel] {
            color: #FFFFFF;
            text-decoration: none;
        }

        img {
            display: block;
            border: none;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        table td,
        table th {
            border-collapse: collapse;
        }

        table {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            table-layout: fixed;
        }

        tr {
            font-size: 0px;
            line-height: 0px;
            border-collapse: collapse;
        }

        table table {
            table-layout: auto;
        }

        a {
            color: #008200;
        }

        a:hover {
            color: #009000 !important;
        }

        a img {
            border: none;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }
        }

        body,
        table,
        td,
        a {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        img {
            -ms-interpolation-mode: bicubic;
        }

        /* Reset styles */

        img {
            border: 0 !important;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        table td {
            border-collapse: collapse !important;
        }

        body {
            height: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
        }

        /* iOS blue links */

        a[x-apple-data-detectors] {
            color: inherit !important;
            text-decoration: none !important;
            font-size: inherit !important;
            font-family: inherit !important;
            font-weight: inherit !important;
            line-height: inherit !important;
        }

        .content-calendar {
            padding: 15px 0 30px 4%;

        }

        /* Global styles */

        body,
        table,
        td {
            font-family: AdelleSansSAGE, Arial, Roboto, Segoe UI, Helvetica Neue;
            font-size: 16px;
            font-weight: 300;
            line-height: 1.5;
            color: #191919
        }


        /* Button style */

        .sage-button {
            background-color: #009000;
            display: inline-block;
            cursor: pointer;
            color: #ffffff;
            text-decoration: none;
            width: 232px;
        }

        .sage-button a {
            color: #ffffff;
        }

        .sage-button:hover {
            background-color: #008000;

        }

        .sage-button a:hover {
            color: #ffffff !important;

        }

        .sage-button-white {
            background-color: #ffffff;
            display: inline-block;
            cursor: pointer;
            color: #191919;
            text-decoration: none;
            width: 232px;
        }

        .sage-button-white a {

            color: #191919;

        }

        .sage-button-white:hover {
            background-color: #eeeeee;

        }

        .sage-button-white a:hover {

            color: #191919 !important;

        }

        .sage-trans-button {
            background-color: transparent;
            display: inline-block;
            cursor: pointer;
            color: #008200;
            text-decoration: none;
            border-color: #008200;
            border-style: solid;
            border-width: 1px;
            width: 232px;

        }

        .sage-trans-button a {
            color: #008200;
        }

        .sage-button:active,
        .sage-button-white:active {
            position: relative;
            top: 1px;
        }

        .SBC-icons {

            /*height:170px;*/
            height: 125px;
            display: block !important;
            padding: 0px !important;

        }

        a.SBC-icon-title:link {

            text-align: center;
            color: #004b87;
            font-size: 20px;
            font-weight: 300;

        }

        .SBC-icon-title {

            text-align: center;
            color: #004b87;
            font-size: 20px;
            font-weight: 300;

        }

        /* Media query: scale from 600px to 480px break point */

        @media screen and (max-width: 600px) {
            h1 {
                font-size: 32px;
                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
                line-height: 1.25 !important;

            }

            h2 {
                font-size: 24px;
                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
                line-height: 1.25 !important;
            }

            h3 {
                font-size: 16px;
                font-family: 'AdelleSansSAGE-Bold', Arial, Roboto, Segoe UI, Helvetica Neue;
            }

            .img-max {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                min-width: 100px !important;
            }

            .img-max-center {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                min-width: 100px !important;
                text-align: center !important;
                float: none !important;
                margin: 0 auto !important;
                padding: 0 !important;
            }

            .img-max-pad-top {
                width: 100% !important;
                max-width: 100% !important;
                height: auto !important;
                min-width: 100px !important;
                padding-top: 10px !important;
            }

            .container {
                width: 100% !important;
                max-width: 600px !important;
                height: auto !important;
                min-width: 0 !important;
            }

            .img-pad-top {
                padding-top: 10px;
            }

            .pad-bottom {
                padding-bottom: 20px;
            }

            .pad-bottom-center {
                padding-bottom: 20px;
                margin-left: auto !important;
                margin-right: auto !important;
            }

            .extra-marg-bottom {
                margin-bottom: 30px !important;
            }

            .no-pad-bottom {
                padding-bottom: 0px !important;

            }

            .no-pad-bottom-center {
                padding-bottom: 0px !important;
                margin-left: auto !important;
                margin-right: auto !important;
                float: none !important;
            }

            .text-center-right {
                text-align: center !important;
                padding-right: 20px !important;
                padding-left: 0px !important;
            }

            .text-center-right-pad {
                text-align: center !important;
                padding-left: 0px !important;
            }

            .center-table {
                text-align: center !important;
            }

            .text-center {
                text-align: center !important;
            }

            .text-center-75 {
                text-align: center !important;
                max-width: 75% !important;
            }

            .ul-style {
                padding-left: 20px !important;
            }

            .max-width {
                max-width: 100% !important;
                width: 100% !important;

            }

            .max-width-remove-height {
                max-width: 100% !important;
                width: 100% !important;
                ;
                height: auto !important;
            }

            .max-width-center {
                max-width: 100% !important;
                width: 100% !important;

                text-align: center !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .max-width-pad {
                max-width: 100% !important;
                width: 100% !important;
                margin-bottom: 12px !important;
            }

            .max-width-top-pad {
                max-width: 100% !important;
                width: 100% !important;

                margin-top: 20px !important;
            }


            .max-width-no-pad {
                max-width: 100% !important;
                width: 100% !important;

            }

            .max-width-no-pad-l {
                max-width: 100% !important;
                width: 100% !important;
                padding-left: 0 !important;
            }

            .max-width-no-pad-a {
                max-width: 100% !important;
                width: 100% !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
                padding-bottom: 0 !important;
                padding-top: 0 !important;
            }

            .max-width-center-pad {
                max-width: 100% !important;
                width: 100% !important;

                text-align: center !important;
                margin: 0 auto !important;
                margin-bottom: 30px !important;
            }

            .mobile-wrapper {
                width: 90% !important;
                max-width: 90% !important;
            }

            .mobile-padding {
                padding-left: 5% !important;
                padding-right: 5% !important;
            }

            .fix-margin {
                margin-top: 0px;
                padding-top: 0px;
            }

            .mobile-center {
                text-align: center !important;
                padding-bottom: 5px !important;
            }

            .mobile-stacked {
                padding-top: 12px !important;
                padding-bottom: 12px !important;
            }


            .remove-height {
                height: auto !important;
            }

            .remove-pad {
                padding: 0px !important;
            }

            .content {
                padding-left: 25px !important;
                padding-right: 25px !important;
            }

            .sage_logo {
                width: auto !important;
                height: 32px !important;
            }


            .desktop-masthead {
                width: 100% !important;
                height: auto !important;
            }

            .img-center {
                margin-left: auto !important;
                margin-right: auto !important;
            }

            .two-col-icons {
                width: 50% !important;
                height: 50% !important;
                display: block;
            }

            .quote-signature {
                padding-left: 10px !important;
            }

            .SBC-icons {
                width: 138px !important;
                height: auto !important;
            }

            .content-SBC-icons {
                padding-left: 40px !important;
                padding-right: 40px !important;
            }

            .content-calendar {
                padding: 15px 0 30px 14%;
            }


            /* Media query for mobile viewport
             * Developer:  hero graphics should be 2 x width for HD rendering.
             */
            @media only screen and (max-width: 480px) {

                h1 {

                    font-size: 24px !important;
                    font-weight: bold;
                }

                h2 {
                    font-size: 20px !important;
                    font-weight: bold;
                }

                h3 {
                    font-size: 16px;
                    font-weight: bold;
                }

                table[class=max-width-pad] {
                    max-width: 100% !important;
                    width: 100% !important;
                    padding-top: 15px !important;
                    height: auto !important;
                }

                table[class=max-width] {
                    max-width: 100% !important;
                    width: 100% !important;
                    height: auto !important;
                }

                table[class=container] {
                    margin: 0 auto !important;
                }

                .desktop-masthead {
                    display: none !important;
                }

                *[class].hidden, *[class=desktop-masthead] {
                    display: none !important;
                }

                *[class].elastic {
                    width: 100% !important;
                    height: auto !important;
                }

                *[class].centered {
                    text-align: center !important;
                }

                *[class].fluid,
                [class=fluid-mob] {
                    width: 100% !important;
                }

                [class=fluid-mob] {
                    position: relative;
                }

                .mobile-show {
                    display: table-cell !important;
                    width: auto !important;
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                    visibility: visible !important;
                    position: relative !important;
                    text-align: center !important;
                }

                .show-mob {
                    display: block !important;
                    max-height: none !important;
                    width: auto !important;
                    visibility: visible !important;
                }

                *[class].mob-masthead {
                    width: 100% !important;
                    display: block !important;
                    height: auto !important;
                    max-height: none !important;
                    padding: 0 !important;
                }

                *[class].mob-masthead-img {
                    position: absolute !important;
                    top: 0px !important;
                    display: block !important;
                    height: auto !important;
                    max-height: none !important;
                    padding: 0 !important;
                    width: auto !important;
                }

                [class=fluid-mob] {
                    display: table-cell !important;
                    width: auto !important;
                    height: auto !important;
                    max-height: none !important;
                    overflow: visible !important;
                    visibility: visible !important;
                    position: relative !important;
                    text-align: center !important;
                }

                .SBC-icons {
                    margin: 2px;
                    height: auto !important;
                }

                .content-quote {
                    margin-top: 20px
                }

                .quote-signature {
                    padding-left: 0px !important;
                }

            }

            @media only screen and (max-width: 410px) {
                .SBC-icons {

                    height: auto !important;
                    clear: none;

                }

                .content-SBC-icons {
                    padding-left: 35px !important;
                    padding-right: 15px !important;
                    margin: 4px;

                }

                .content-calendar {
                    padding: 15px 7% 30px;
                }
            }
