<section
  class="record-context"
  data-context-object-type="PurchaseOrder"
  data-context-object-path="xtremPurchasing.purchaseOrder.query.edges.0.node"
  data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"order","data":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv"},"id":"_id","labelPath":"_id","property":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv","id":"_id","data":{"type":"IntOrString","kind":"SCALAR","name":"_id","canFilter":true,"canSort":true,"label":"_id","isOnInputType":false,"isOnOutputType":true,"dataType":"","targetNode":"","enumType":null,"isCustom":false,"isMutable":false,"iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
  data-context-list-order="{}"
  data-alias="YIAfvLiP"
>
  <!--{{#with YIAfvLiP.purchaseOrder.query.edges.0.node}}-->
  <div class="report-context-body">
    <section class="unbreakable-block">
      <div class="unbreakable-block-body">
        <figure class="table" style="width: 100%">
          <table class="ck-table-resized">
            <colgroup>
              <col style="width: 32.22%" />
              <col style="width: 67.78%" />
            </colgroup>
            <tbody>
              <tr>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"stockSite.name","key":"stockSite.name","labelKey":"Name","labelPath":"Stock site > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq stockSite.name null ) ( eq stockSite.name "" ) ( eq stockSite.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="stockSite.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseOrder"
                            >{{stockSite.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.addressLine1","key":"stockSite.primaryAddress.addressLine1","labelKey":"Address line 1","labelPath":"Stock site > Primary address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq stockSite.primaryAddress.addressLine1 null ) ( eq stockSite.primaryAddress.addressLine1 "" ) ( eq stockSite.primaryAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="stockSite.primaryAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{stockSite.primaryAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.addressLine2","key":"stockSite.primaryAddress.addressLine2","labelKey":"Address line 2","labelPath":"Stock site > Primary address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq stockSite.primaryAddress.addressLine2 null ) ( eq stockSite.primaryAddress.addressLine2 "" ) ( eq stockSite.primaryAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="stockSite.primaryAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{stockSite.primaryAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.city","key":"stockSite.primaryAddress.city","labelKey":"City","labelPath":"Stock site > Primary address > City"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq stockSite.primaryAddress.city null ) ( eq stockSite.primaryAddress.city "" ) ( eq stockSite.primaryAddress.city undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="stockSite.primaryAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{stockSite.primaryAddress.city}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"stockSite.primaryAddress.country.name","key":"stockSite.primaryAddress.country.name","labelKey":"Name","labelPath":"Stock site > Primary address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq stockSite.primaryAddress.country.name null ) ( eq stockSite.primaryAddress.country.name "" ) ( eq stockSite.primaryAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="stockSite.primaryAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{stockSite.primaryAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="background-color: #dfdfdf; text-align: right">
                  <h2>
                    <span style="color: #198e59"><strong>{{ translatedContent "d87d18e7c85dbee0fd1bbc3ef5fbdd24" }}</strong></span>
                  </h2>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        >&nbsp;<span
                          class="property"
                          data-property-display-label="Number"
                          data-property-data-type="String"
                          data-property-name="number"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{number}}</span
                        ></strong
                      ></span
                    >
                  </p>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "f4d5b114119bfdbdc5f3d9472a46fc7b" }}</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong
                        >&nbsp;<span
                          class="property"
                          data-property-display-label="Order date"
                          data-property-data-type="Date"
                          data-property-name="orderDate"
                          data-property-data-format="FullDate"
                          data-property-parent-context="PurchaseOrder"
                          >{{formatDate orderDate 'FullDate'}}</span
                        ></strong
                      ></span
                    >
                  </p>
                </td>
              </tr>
              <tr>
                <td style="background-color: transparent; border-color: transparent">&nbsp;</td>
                <td style="border-color: transparent; text-align: right">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"paymentTerm.name","key":"paymentTerm.name","labelKey":"Name","labelPath":"Payment term > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq paymentTerm.name null ) ( eq paymentTerm.name "" ) ( eq paymentTerm.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "6af6aa3c07e3f393b7c213a6a0edd35f" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="paymentTerm.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{paymentTerm.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedName","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"deliveryMode.name","key":"deliveryMode.name","labelKey":"Name","labelPath":"Delivery mode > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq deliveryMode.name null ) ( eq deliveryMode.name "" ) ( eq deliveryMode.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "0f55fedcee89c8d9407351716835247e" }}</strong>&nbsp;<span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="deliveryMode.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{deliveryMode.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <figure class="table" style="width: 100%">
          <table class="ck-table-resized">
            <colgroup>
              <col style="width: 42.69%" />
              <col style="width: 57.31%" />
            </colgroup>
            <tbody>
              <tr>
                <td style="background-color: #dfdfdf; border-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "ec136b444eede3bc85639fac0dd06229" }}</strong></span
                  >
                </td>
                <td style="background-color: #dfdfdf; border-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "5390da28367249488d76646a531ab249" }}</strong></span
                  >
                </td>
              </tr>
              <tr>
                <td style="border-color: #ffffff">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"supplierAddress.name","key":"supplierAddress.name","labelKey":"Name","labelPath":"Supplier address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.name null ) ( eq supplierAddress.name "" ) ( eq supplierAddress.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="supplierAddress.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseOrder"
                            >{{supplierAddress.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"supplierAddress.addressLine1","key":"supplierAddress.addressLine1","labelKey":"Address line 1","labelPath":"Supplier address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.addressLine1 null ) ( eq supplierAddress.addressLine1 "" ) ( eq supplierAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"supplierAddress.addressLine2","key":"supplierAddress.addressLine2","labelKey":"Address line 2","labelPath":"Supplier address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.addressLine2 null ) ( eq supplierAddress.addressLine2 "" ) ( eq supplierAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"supplierAddress.region","key":"supplierAddress.region","labelKey":"Region","labelPath":"Supplier address > Region"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"siteAddress.postcode","key":"siteAddress.postcode","labelKey":"Postal code","labelPath":"Site address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq supplierAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) ) ( not ( or ( eq supplierAddress.region null ) ( eq supplierAddress.region "" ) ( eq supplierAddress.region undefined ) ) ) ( not ( or ( eq siteAddress.postcode null ) ( eq siteAddress.postcode "" ) ( eq siteAddress.postcode undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq supplierAddress.country.iso31661Alpha3 "GBR" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"supplierAddress.region","key":"supplierAddress.region","labelKey":"Region","labelPath":"Supplier address > Region"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"supplierAddress.postcode","key":"supplierAddress.postcode","labelKey":"Postal code","labelPath":"Supplier address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq supplierAddress.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) ) ( not ( or ( eq supplierAddress.region null ) ( eq supplierAddress.region "" ) ( eq supplierAddress.region undefined ) ) ) ( not ( or ( eq supplierAddress.postcode null ) ( eq supplierAddress.postcode "" ) ( eq supplierAddress.postcode undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"supplierAddress.region","key":"supplierAddress.region","labelKey":"Region","labelPath":"Supplier address > Region"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"supplierAddress.postcode","key":"supplierAddress.postcode","labelKey":"Postal code","labelPath":"Supplier address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq supplierAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) ) ( not ( or ( eq supplierAddress.region null ) ( eq supplierAddress.region "" ) ( eq supplierAddress.region undefined ) ) ) ( not ( or ( eq supplierAddress.postcode null ) ( eq supplierAddress.postcode "" ) ( eq supplierAddress.postcode undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"3","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"supplierAddress.postcode","key":"supplierAddress.postcode","labelKey":"Postal code","labelPath":"Supplier address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"5","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( not ( eq supplierAddress.country.iso31661Alpha3 "USA" ) ) ( not ( eq supplierAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq supplierAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( or ( eq supplierAddress.postcode null ) ( eq supplierAddress.postcode "" ) ( eq supplierAddress.postcode undefined ) ) ) ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.postcode}}</span
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.city}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"supplierAddress.country.name","key":"supplierAddress.country.name","labelKey":"Name","labelPath":"Supplier address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.country.name null ) ( eq supplierAddress.country.name "" ) ( eq supplierAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{supplierAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"siteAddress.name","key":"siteAddress.name","labelKey":"Name","labelPath":"Site address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.name null ) ( eq siteAddress.name "" ) ( eq siteAddress.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="siteAddress.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseOrder"
                            >{{siteAddress.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"siteAddress.addressLine1","key":"siteAddress.addressLine1","labelKey":"Address line 1","labelPath":"Site address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.addressLine1 null ) ( eq siteAddress.addressLine1 "" ) ( eq siteAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="siteAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"siteAddress.addressLine2","key":"siteAddress.addressLine2","labelKey":"Address line 2","labelPath":"Site address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.addressLine2 null ) ( eq siteAddress.addressLine2 "" ) ( eq siteAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="siteAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"siteAddress.region","key":"siteAddress.region","labelKey":"Region","labelPath":"Site address > Region"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"siteAddress.postcode","key":"siteAddress.postcode","labelKey":"Postal code","labelPath":"Site address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) ( not ( or ( eq siteAddress.region null ) ( eq siteAddress.region "" ) ( eq siteAddress.region undefined ) ) ) ( not ( or ( eq siteAddress.postcode null ) ( eq siteAddress.postcode "" ) ( eq siteAddress.postcode undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span
                        class="property"
                        data-property-display-label="City"
                        data-property-data-type="String"
                        data-property-name="siteAddress.city"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseOrder"
                        >{{siteAddress.city}}</span
                      ><span
                        class="property"
                        data-property-display-label="Region"
                        data-property-data-type="String"
                        data-property-name="siteAddress.region"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseOrder"
                        >{{siteAddress.region}}</span
                      ><span
                        class="property"
                        data-property-display-label="Postal code"
                        data-property-data-type="String"
                        data-property-name="siteAddress.postcode"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseOrder"
                        >{{siteAddress.postcode}}</span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"}]'
                  >
                    <!--{{#if ( eq siteAddress.country.iso31661Alpha3 "GBR" )}}-->
                    <div class="conditional-block-body">
                      <span style="font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.city}}</span
                        ></span
                      ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="siteAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"siteAddress.region","key":"siteAddress.region","labelKey":"Region","labelPath":"Site address > Region"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"siteAddress.postcode","key":"siteAddress.postcode","labelKey":"Postal code","labelPath":"Site address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) ( not ( or ( eq siteAddress.region null ) ( eq siteAddress.region "" ) ( eq siteAddress.region undefined ) ) ) ( not ( or ( eq siteAddress.postcode null ) ( eq siteAddress.postcode "" ) ( eq siteAddress.postcode undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="siteAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"Region","data":{"name":"region","title":"Region","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"regionDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Region","node":"String","iconType":"csv"},"id":"siteAddress.region","key":"siteAddress.region","labelKey":"Region","labelPath":"Site address > Region"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"3","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"siteAddress.postcode","key":"siteAddress.postcode","labelKey":"Postal code","labelPath":"Site address > Postal code"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq siteAddress.region null ) ( eq siteAddress.region "" ) ( eq siteAddress.region undefined ) ) ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) ( not ( or ( eq siteAddress.postcode null ) ( eq siteAddress.postcode "" ) ( eq siteAddress.postcode undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Region"
                          data-property-data-type="String"
                          data-property-name="siteAddress.region"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.region}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Postal code","data":{"name":"postcode","title":"Postal code","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"postcodeDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Postal code","node":"String","iconType":"csv"},"id":"siteAddress.postcode","key":"siteAddress.postcode","labelKey":"Postal code","labelPath":"Site address > Postal code"},"value2":null,"key":"1","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"3","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"4","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"5","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"5","operator":"notEqual"}]'
                  >
                    <!--{{#if ( and ( not ( or ( eq siteAddress.postcode null ) ( eq siteAddress.postcode "" ) ( eq siteAddress.postcode undefined ) ) ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) ( not ( eq siteAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( eq siteAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq siteAddress.country.iso31661Alpha3 "USA" ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.postcode}}</span
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.city}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"siteAddress.country.name","key":"siteAddress.country.name","labelKey":"Name","labelPath":"Site address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.country.name null ) ( eq siteAddress.country.name "" ) ( eq siteAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="siteAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseOrder"
                          >{{siteAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <table
          class="query-table"
          data-context-object-type="PurchaseOrderLine"
          data-context-object-path="lines.query.edges"
          data-context-filter="[]"
          data-context-list-order='{"item.name":"ascending","itemDescription":"ascending","quantity":"ascending","item.salesUnit.name":"ascending","expectedReceiptDate":"ascending","grossPrice":"ascending","netPrice":"ascending","amountExcludingTax":"ascending","amountExcludingTaxInCompanyCurrency":"ascending","amountIncludingTax":"ascending"}'
          data-alias="USdXOakV"
        >
          <thead class="query-table-head">
            <tr class="query-table-row">
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}&nbsp;</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}&nbsp;</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "1395fb3a0d455f5d066d8decc667fb81" }}&nbsp;</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "0170ef61ec11c8b9dcf4d4a63782515c" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "9554ce40e7959ce198210376c97150b3" }}&nbsp;</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "c17f9183c57045cfb614e02bbb233ee1" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "20a34c4e30c5bbe1d3f870ac55f0d831" }}&nbsp;</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "41dd1e26b4321ab2f837f69a7aae8e91" }}</strong></span
                  >
                </p>
              </td>
            </tr>
          </thead>
          <tbody class="query-table-body">
            <!--{{#each USdXOakV.query.edges}}{{#with node}}-->
            <tr class="query-table-row">
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="item.name"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseOrderLine"
                      >{{item.name}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Item description"
                      data-property-data-type="String"
                      data-property-name="itemDescription"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseOrderLine"
                      >{{itemDescription}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Quantity"
                      data-property-data-type="Decimal"
                      data-property-name="quantity"
                      data-property-data-format="2"
                      data-property-parent-context="PurchaseOrderLine"
                      >{{formatNumber quantity 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="purchaseUnit.name"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseOrderLine"
                      >{{purchaseUnit.name}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Expected receipt date"
                      data-property-data-type="Date"
                      data-property-name="expectedReceiptDate"
                      data-property-data-format="FullDate"
                      data-property-parent-context="PurchaseOrderLine"
                      >{{formatDate expectedReceiptDate 'FullDate'}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseOrderLine"
                      >{{currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Gross price"
                      data-property-data-type="Decimal"
                      data-property-name="grossPrice"
                      data-property-data-format="2"
                      data-property-parent-context="PurchaseOrderLine"
                      >{{formatNumber grossPrice 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseOrderLine"
                      >{{currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Net price"
                      data-property-data-type="Decimal"
                      data-property-name="netPrice"
                      data-property-data-format="2"
                      data-property-parent-context="PurchaseOrderLine"
                      >{{formatNumber netPrice 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseOrderLine"
                      >{{currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Line amount excluding tax"
                      data-property-data-type="Decimal"
                      data-property-name="amountExcludingTax"
                      data-property-data-format="2"
                      data-property-parent-context="PurchaseOrderLine"
                      >{{formatNumber amountExcludingTax 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <section
                  class="record-context"
                  data-context-object-type="DocumentLineTax"
                  data-context-object-path="taxes.query.edges.0.node"
                  data-context-filter="[]"
                  data-context-list-order="{}"
                  data-alias="SMrJsXFX"
                >
                  <!--{{#with SMrJsXFX.query.edges.0.node}}-->
                  <div class="report-context-body">
                    <p>
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Tax rate"
                          data-property-data-type="Decimal"
                          data-property-name="taxRate"
                          data-property-data-format="2"
                          data-property-parent-context="DocumentLineTax"
                          >{{formatNumber taxRate 2}}</span
                        >{{ translatedContent "0bcef9c45bd8a48eda1b26eb0c61c869" }}</span
                      >
                    </p>
                  </div>
                  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                </section>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; height: 20px; padding: 2px; text-align: left; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Symbol"
                      data-property-data-type="String"
                      data-property-name="currency.symbol"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseOrderLine"
                      >{{currency.symbol}}</span
                    ><span
                      class="property"
                      data-property-display-label="Line amount including tax"
                      data-property-data-type="Decimal"
                      data-property-name="amountIncludingTax"
                      data-property-data-format="2"
                      data-property-parent-context="PurchaseOrderLine"
                      >{{formatNumber amountIncludingTax 2}}</span
                    ></span
                  >
                </p>
              </td>
            </tr>
            <!--{{#printBreakIfLast  'grossPrice' 'sum' 'netPrice' 'sum' 'amountExcludingTax' 'sum' 'amountIncludingTax' 'sum'}}-->
            <tr class="query-table-row" data-footer-group="footer">
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; vertical-align: middle">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "edd291d1439a6c1c18fe38bee411580f" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px"><p>&nbsp;</p></td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px"><p>&nbsp;</p></td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px"><p>&nbsp;</p></td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px"><p>&nbsp;</p></td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseOrderLine"
                        >{{currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Gross price"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.grossPrice.sum"
                        data-property-data-format="2"
                        data-property-parent-context="PurchaseOrderLine"
                        >{{formatNumber _blockAggregatedData.grossPrice.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseOrderLine"
                        >{{currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Net price"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.netPrice.sum"
                        data-property-data-format="2"
                        data-property-parent-context="PurchaseOrderLine"
                        >{{formatNumber _blockAggregatedData.netPrice.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseOrderLine"
                        >{{currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Line amount excluding tax"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.amountExcludingTax.sum"
                        data-property-data-format="2"
                        data-property-parent-context="PurchaseOrderLine"
                        >{{formatNumber _blockAggregatedData.amountExcludingTax.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right"><p>&nbsp;</p></td>
              <td class="query-table-cell" style="background-color: #dfdfdf; border: 1px solid #dfdfdf; padding: 2px; text-align: right">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong
                      ><span
                        class="property"
                        data-property-display-label="Symbol"
                        data-property-data-type="String"
                        data-property-name="currency.symbol"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseOrderLine"
                        >{{currency.symbol}}</span
                      ><span
                        class="property"
                        data-property-display-label="Line amount including tax"
                        data-property-data-type="Decimal"
                        data-property-name="_blockAggregatedData.amountIncludingTax.sum"
                        data-property-data-format="2"
                        data-property-parent-context="PurchaseOrderLine"
                        >{{formatNumber _blockAggregatedData.amountIncludingTax.sum 2}}</span
                      ></strong
                    ></span
                  >
                </p>
              </td>
            </tr>
            <!--{{/printBreakIfLast}}-->
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="10"><p>&nbsp;</p></td>
            </tr>
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="10"><p>&nbsp;</p></td>
            </tr>
            <!--{{/with}}{{/each}}-->
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="10"><p>&nbsp;</p></td>
            </tr>
          </tbody>
          <tfoot class="query-table-footer">
            <tr class="query-table-row">
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
            </tr>
          </tfoot>
        </table>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <figure class="table" style="width: 100%">
              <table class="ck-table-resized">
                <colgroup>
                  <col style="width: 46.19%" />
                  <col style="width: 53.81%" />
                </colgroup>
                <tbody>
                  <tr>
                    <td>&nbsp;</td>
                    <td>
                      <figure class="table">
                        <table>
                          <tbody>
                            <tr>
                              <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "25b77b23fda3d051fb72598c952a82d7" }}</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "ff9e6ead51b6937f49449b030045d815" }}&nbsp;</strong></span
                                >
                              </td>
                              <td style="background-color: #dfdfdf; border-style: solid; border-width: 1px">
                                <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong>{{ translatedContent "ba12e59b20a9720f70fb907a55cd2620" }}</strong></span
                                >
                              </td>
                            </tr>
                            <tr>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="PurchaseOrder"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total amount excluding tax"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalAmountExcludingTax"
                                    data-property-data-format="2"
                                    data-property-parent-context="PurchaseOrder"
                                    >{{formatNumber totalAmountExcludingTax 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><span
                                    class="property"
                                    data-property-display-label="Symbol"
                                    data-property-data-type="String"
                                    data-property-name="currency.symbol"
                                    data-property-data-format=""
                                    data-property-parent-context="PurchaseOrder"
                                    >{{currency.symbol}}</span
                                  ><span
                                    class="property"
                                    data-property-display-label="Total tax amount"
                                    data-property-data-type="Decimal"
                                    data-property-name="totalTaxAmount"
                                    data-property-data-format="2"
                                    data-property-parent-context="PurchaseOrder"
                                    >{{formatNumber totalTaxAmount 2}}</span
                                  ></span
                                >
                              </td>
                              <td style="text-align: right">
                                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                                  ><strong
                                    ><span
                                      class="property"
                                      data-property-display-label="Symbol"
                                      data-property-data-type="String"
                                      data-property-name="currency.symbol"
                                      data-property-data-format=""
                                      data-property-parent-context="PurchaseOrder"
                                      >{{currency.symbol}}</span
                                    ><span
                                      class="property"
                                      data-property-display-label="Total amount including tax"
                                      data-property-data-type="Decimal"
                                      data-property-name="totalAmountIncludingTax"
                                      data-property-data-format="2"
                                      data-property-parent-context="PurchaseOrder"
                                      >{{formatNumber totalAmountIncludingTax 2}}</span
                                    ></strong
                                  ></span
                                >
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </figure>
                    </td>
                  </tr>
                </tbody>
              </table>
            </figure>
          </div>
        </section>
        <section class="unbreakable-block">
          <div class="unbreakable-block-body">
            <section
              class="conditional-block"
              data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"External note","data":{"name":"isExternalNote","title":"External note","canSort":true,"canFilter":true,"type":"Boolean","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"External note","node":"Boolean","iconType":"csv"},"id":"isExternalNote","key":"isExternalNote","labelKey":"External note","labelPath":"External note"},"value2":"true","key":"1","operator":"equals"}]'
            >
              <!--{{#if ( eq isExternalNote true )}}-->
              <div class="conditional-block-body">
                <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                  ><strong>{{ translatedContent "fc9d3df613063ee57906c461320744e2" }}</strong>&nbsp;<span
                    class="property"
                    data-property-display-label="External note"
                    data-property-data-type="_OutputTextStream"
                    data-property-name="externalNote.value"
                    data-property-data-format=""
                    data-property-parent-context="PurchaseOrder"
                    >{{{externalNote.value}}}</span
                  ></span
                >
              </div>
              <!--{{/if}}-->
              <div class="conditional-block-footer">&nbsp;</div>
            </section>
          </div>
        </section>
      </div>
    </section>
  </div>
  <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
</section>
