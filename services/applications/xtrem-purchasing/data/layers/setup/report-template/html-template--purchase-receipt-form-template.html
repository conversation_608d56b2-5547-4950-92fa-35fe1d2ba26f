<section class="unbreakable-block">
  <div class="unbreakable-block-body">
    <section
      class="record-context"
      data-context-object-type="PurchaseReceipt"
      data-context-object-path="xtremPurchasing.purchaseReceipt.query.edges.0.node"
      data-context-filter='[{"_id":"1","label":"_id","filterType":"matches","filterValue":"receipt","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"id":"_id","labelPath":"_id","property":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv","id":"_id","data":{"name":"_id","title":"ID","canSort":true,"canFilter":true,"type":"IntOrString","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"_id","iconType":"csv"},"key":"_id","labelKey":"_id","labelPath":"_id"},"parameter":true}]'
      data-context-list-order="{}"
      data-alias="TQCllewa"
    >
      <!--{{#with TQCllewa.purchaseReceipt.query.edges.0.node}}-->
      <div class="report-context-body">
        <figure class="table" style="width: 100%">
          <table class="ck-table-resized">
            <colgroup>
              <col style="width: 37.83%" />
              <col style="width: 62.17%" />
            </colgroup>
            <tbody>
              <tr>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"siteAddress.name","key":"siteAddress.name","labelKey":"Name","labelPath":"Site address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.name null ) ( eq siteAddress.name "" ) ( eq siteAddress.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="siteAddress.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{siteAddress.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Address line 1"
                        data-property-data-type="String"
                        data-property-name="siteAddress.addressLine1"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseReceipt"
                        >{{siteAddress.addressLine1}}</span
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"siteAddress.addressLine2","key":"siteAddress.addressLine2","labelKey":"Address line 2","labelPath":"Site address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.addressLine2 null ) ( eq siteAddress.addressLine2 "" ) ( eq siteAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="siteAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( not ( eq siteAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( eq siteAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq siteAddress.country.iso31661Alpha3 "USA" ) ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"siteAddress.country.name","key":"siteAddress.country.name","labelKey":"Name","labelPath":"Site address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.country.name null ) ( eq siteAddress.country.name "" ) ( eq siteAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="siteAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"site.siret","key":"site.siret","labelKey":"SIRET","labelPath":"Site > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.siret null ) ( eq site.siret "" ) ( eq site.siret undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="SIRET"
                          data-property-data-type="String"
                          data-property-name="site.siret"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{site.siret}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"site.taxIdNumber","key":"site.taxIdNumber","labelKey":"Tax ID number","labelPath":"Site > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.taxIdNumber null ) ( eq site.taxIdNumber "" ) ( eq site.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>&nbsp;{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Tax ID number"
                          data-property-data-type="String"
                          data-property-name="site.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{site.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="background-color: #dfdfdf; text-align: right">
                  <h2><span style="font-family: 'Sage UI', Geneva, sans-serif">{{ translatedContent "b4a991403886813c0a468b0af24da2ad" }}</span></h2>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "2a39f204d4eac05604c2059e5ac2f9f7" }}&nbsp;</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Number"
                        data-property-data-type="String"
                        data-property-name="number"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseReceipt"
                        >{{number}}</span
                      ></span
                    >
                  </p>
                  <p>
                    <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><strong>{{ translatedContent "5b10e25e80cf242a265f5dfae1253db1" }}&nbsp;</strong></span
                    ><span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Receipt date"
                        data-property-data-type="Date"
                        data-property-name="date"
                        data-property-data-format="FullDate"
                        data-property-parent-context="PurchaseReceipt"
                        >{{formatDate date 'FullDate'}}</span
                      ></span
                    >
                  </p>
                </td>
              </tr>
              <tr>
                <td style="border-color: #ffffff">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"siteAddress.name","key":"siteAddress.name","labelKey":"Name","labelPath":"Site address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.name null ) ( eq siteAddress.name "" ) ( eq siteAddress.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "5eea367ea73b909880393bd1ae79fc67" }}&nbsp;</strong></span
                      ><span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="siteAddress.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{siteAddress.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ID","data":{"name":"id","title":"ID","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"id","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ID","node":"String","iconType":"csv"},"id":"site.id","key":"site.id","labelKey":"ID","labelPath":"Site > ID"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.id null ) ( eq site.id "" ) ( eq site.id undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d13d8380c3f4de07fef91a42fe6c60d7" }}</strong></span
                      >&nbsp;<span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="ID"
                          data-property-data-type="String"
                          data-property-name="site.id"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{site.id}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"SIRET","data":{"name":"siret","title":"SIRET","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"SIRET","node":"String","iconType":"csv"},"id":"supplier.siret","key":"supplier.siret","labelKey":"SIRET","labelPath":"Supplier > SIRET"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplier.siret null ) ( eq supplier.siret "" ) ( eq supplier.siret undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "d04e2c1b67f3ef0d475409516b812e8b" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="SIRET"
                          data-property-data-type="String"
                          data-property-name="supplier.siret"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplier.siret}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Tax ID number","data":{"name":"taxIdNumber","title":"Tax ID number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"taxIdentificationDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Tax ID number","node":"String","iconType":"csv"},"id":"supplier.taxIdNumber","key":"supplier.taxIdNumber","labelKey":"Tax ID number","labelPath":"Supplier > Tax ID number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplier.taxIdNumber null ) ( eq supplier.taxIdNumber "" ) ( eq supplier.taxIdNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "37a7a58f6fdc66ce34c2bf56389a2aac" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Tax ID number"
                          data-property-data-type="String"
                          data-property-name="supplier.taxIdNumber"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplier.taxIdNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td style="text-align: right">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Supplier document number","data":{"name":"supplierDocumentNumber","title":"Supplier document number","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"documentNumber","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Supplier document number","node":"String","iconType":"csv"},"id":"supplierDocumentNumber","key":"supplierDocumentNumber","labelKey":"Supplier document number","labelPath":"Supplier document number"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierDocumentNumber null ) ( eq supplierDocumentNumber "" ) ( eq supplierDocumentNumber undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "ca5e44b287636ca2903ab29300dfdf39" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Supplier document number"
                          data-property-data-type="String"
                          data-property-name="supplierDocumentNumber"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierDocumentNumber}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Description","data":{"name":"description","title":"Description","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedDescription","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Description","node":"String","iconType":"csv"},"id":"supplier.deliveryMode.description","key":"supplier.deliveryMode.description","labelKey":"Description","labelPath":"Supplier > Delivery mode > Description"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplier.deliveryMode.description null ) ( eq supplier.deliveryMode.description "" ) ( eq supplier.deliveryMode.description undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "0f55fedcee89c8d9407351716835247e" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Description"
                          data-property-data-type="String"
                          data-property-name="supplier.deliveryMode.description"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplier.deliveryMode.description}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Description","data":{"name":"description","title":"Description","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Description","node":"String","iconType":"csv"},"id":"paymentTerm.description","key":"paymentTerm.description","labelKey":"Description","labelPath":"Payment term > Description"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq paymentTerm.description null ) ( eq paymentTerm.description "" ) ( eq paymentTerm.description undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "6af6aa3c07e3f393b7c213a6a0edd35f" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Description"
                          data-property-data-type="String"
                          data-property-name="paymentTerm.description"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{paymentTerm.description}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":false,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"carrier.name","key":"carrier.name","labelKey":"Name","labelPath":"Carrier > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq carrier.name null ) ( eq carrier.name "" ) ( eq carrier.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong>{{ translatedContent "f8617a92ba0a0a4eabee724eab7c9f48" }}&nbsp;</strong
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="carrier.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{carrier.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td style="background-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "a7d6475ec8993b7224d6facc8cb0ead6" }}</strong></span
                  >
                </td>
                <td style="background-color: #dfdfdf">
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>{{ translatedContent "ec136b444eede3bc85639fac0dd06229" }}</strong></span
                  >
                </td>
              </tr>
              <tr>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"supplierAddress.name","key":"supplierAddress.name","labelKey":"Name","labelPath":"Supplier address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.name null ) ( eq supplierAddress.name "" ) ( eq supplierAddress.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="siteAddress.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{siteAddress.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <p>
                    <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                      ><span
                        class="property"
                        data-property-display-label="Address line 1"
                        data-property-data-type="String"
                        data-property-name="siteAddress.addressLine1"
                        data-property-data-format=""
                        data-property-parent-context="PurchaseReceipt"
                        >{{siteAddress.addressLine1}}</span
                      ></span
                    >
                  </p>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"siteAddress.addressLine2","key":"siteAddress.addressLine2","labelKey":"Address line 2","labelPath":"Site address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.addressLine2 null ) ( eq siteAddress.addressLine2 "" ) ( eq siteAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="siteAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq supplierAddress.city "ZAF" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq siteAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"siteAddress.country.iso31661Alpha3","key":"siteAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Site address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"4","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"siteAddress.city","key":"siteAddress.city","labelKey":"City","labelPath":"Site address > City"},"value2":null,"key":"4","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( not ( eq siteAddress.country.iso31661Alpha3 "GBR" ) ) ( not ( eq siteAddress.country.iso31661Alpha3 "ZAF" ) ) ( not ( eq siteAddress.country.iso31661Alpha3 "USA" ) ) ( not ( or ( eq siteAddress.city null ) ( eq siteAddress.city "" ) ( eq siteAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="siteAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="siteAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"localizedCountryPropertyDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"siteAddress.country.name","key":"siteAddress.country.name","labelKey":"Name","labelPath":"Site address > Country > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq siteAddress.country.name null ) ( eq siteAddress.country.name "" ) ( eq siteAddress.country.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="siteAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{siteAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
                <td>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Name","data":{"name":"name","title":"Name","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"name","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Name","node":"String","iconType":"csv"},"id":"supplierAddress.name","key":"supplierAddress.name","labelKey":"Name","labelPath":"Supplier address > Name"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.name null ) ( eq supplierAddress.name "" ) ( eq supplierAddress.name undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="supplierAddress.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{supplierAddress.name}}</span
                          ></strong
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 1","data":{"name":"addressLine1","title":"Address line 1","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 1","node":"String","iconType":"csv"},"id":"supplierAddress.addressLine1","key":"supplierAddress.addressLine1","labelKey":"Address line 1","labelPath":"Supplier address > Address line 1"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.addressLine1 null ) ( eq supplierAddress.addressLine1 "" ) ( eq supplierAddress.addressLine1 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 1"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.addressLine1"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.addressLine1}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"Address line 2","data":{"name":"addressLine2","title":"Address line 2","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"addressLineDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"Address line 2","node":"String","iconType":"csv"},"id":"supplierAddress.addressLine2","key":"supplierAddress.addressLine2","labelKey":"Address line 2","labelPath":"Supplier address > Address line 2"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.addressLine2 null ) ( eq supplierAddress.addressLine2 "" ) ( eq supplierAddress.addressLine2 undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Address line 2"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.addressLine2"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.addressLine2}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq supplierAddress.country.iso31661Alpha3 "GBR" ) ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq supplierAddress.country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"supplierAddress.country.iso31661Alpha3","key":"supplierAddress.country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Supplier address > Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( and ( eq supplierAddress.country.iso31661Alpha3 "USA" ) ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="City"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.city"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.city}}</span
                        ><span
                          class="property"
                          data-property-display-label="Postal code"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.postcode"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.postcode}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"City","data":{"name":"city","title":"City","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"cityDataType","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"supplierAddress.city","key":"supplierAddress.city","labelKey":"City","labelPath":"Supplier address > City"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq supplierAddress.city null ) ( eq supplierAddress.city "" ) ( eq supplierAddress.city undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><span
                          class="property"
                          data-property-display-label="Name"
                          data-property-data-type="String"
                          data-property-name="supplierAddress.country.name"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{supplierAddress.country.name}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
        <table
          class="query-table"
          data-context-object-type="PurchaseReceiptLine"
          data-context-object-path="lines.query.edges"
          data-context-filter="[]"
          data-context-list-order='{"origin":"ascending","item.id":"ascending","item.name":"ascending","purchaseUnit.name":"ascending","quantity":"ascending"}'
          data-alias="iXDXyXbe"
        >
          <thead class="query-table-head">
            <tr class="query-table-row">
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "3edf8ca26a1ec14dd6e91dd277ae1de6" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "d87d18e7c85dbee0fd1bbc3ef5fbdd24" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "7d74f3b92b19da5e606d737d339a9679" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "b5a7adde1af5c87d7fd797b6245c2a39" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "19c562a36aeb455d09534f93b4f5236f" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "694e8d1f2ee056f98ee488bdc4982d73" }}</strong></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="background-color: #0000001a; border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="color: #198e59; font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><strong>&nbsp;{{ translatedContent "1b4a0d88e4b9191ff6397dc921367462" }}</strong></span
                  >
                </p>
              </td>
            </tr>
          </thead>
          <tbody class="query-table-body">
            <!--{{#each iXDXyXbe.query.edges}}{{#with node}}-->
            <tr class="query-table-row">
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Origin"
                      data-property-data-type="Enum"
                      data-property-name="origin"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseReceiptLine"
                      >{{origin}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Document number"
                      data-property-data-type="String"
                      data-property-name="purchaseOrderLine.purchaseOrderLine.documentNumber"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseReceiptLine"
                      >{{purchaseOrderLine.purchaseOrderLine.documentNumber}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="ID"
                      data-property-data-type="String"
                      data-property-name="item.id"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseReceiptLine"
                      >{{item.id}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="item.name"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseReceiptLine"
                      >{{item.name}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Name"
                      data-property-data-type="String"
                      data-property-name="purchaseUnit.name"
                      data-property-data-format=""
                      data-property-parent-context="PurchaseReceiptLine"
                      >{{purchaseUnit.name}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell e-right-align" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Quantity"
                      data-property-data-type="Decimal"
                      data-property-name="quantity"
                      data-property-data-format="2"
                      data-property-parent-context="PurchaseReceiptLine"
                      >{{formatNumber quantity 2}}</span
                    ></span
                  >
                </p>
              </td>
              <td class="query-table-cell e-right-align" style="border: 1px solid #dfdfdf; padding: 2px; vertical-align: top">
                <p>
                  <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                    ><span
                      class="property"
                      data-property-display-label="Quantity to receive"
                      data-property-data-type="Decimal"
                      data-property-name="purchaseOrderLine.purchaseOrderLine.quantityToReceive"
                      data-property-data-format="2"
                      data-property-parent-context="PurchaseReceiptLine"
                      >{{formatNumber purchaseOrderLine.purchaseOrderLine.quantityToReceive 2}}</span
                    ></span
                  >
                </p>
              </td>
            </tr>
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="7"><p>&nbsp;</p></td>
            </tr>
            <!--{{/with}}{{/each}}-->
            <tr class="query-table-row" data-hidden="1">
              <td class="query-table-cell" colspan="7"><p>&nbsp;</p></td>
            </tr>
          </tbody>
          <tfoot class="query-table-footer">
            <tr class="query-table-row">
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
              <td class="query-table-cell"><p>&nbsp;</p></td>
            </tr>
          </tfoot>
        </table>
        <figure class="table">
          <table>
            <tbody>
              <tr>
                <td style="text-align: center">
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"site.legalCompany.naf","key":"site.legalCompany.naf","labelKey":"NAF","labelPath":"Site > Legal company > NAF"},"value2":null,"key":"1","operator":"notEmpty"}]'
                  >
                    <!--{{#if ( not ( or ( eq site.legalCompany.naf null ) ( eq site.legalCompany.naf "" ) ( eq site.legalCompany.naf undefined ) ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Legal form"
                            data-property-data-type="Enum"
                            data-property-name="site.legalCompany.legalForm"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{site.legalCompany.legalForm}}</span
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="site.legalCompany.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{site.legalCompany.name}}</span
                          ></strong
                        ><span
                          class="property"
                          data-property-display-label="RCS"
                          data-property-data-type="String"
                          data-property-name="site.legalCompany.rcs"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{site.legalCompany.rcs}}</span
                        >{{ translatedContent "7137a85d9b8d9e6d09f47767d2ca31ee" }}<span
                          class="property"
                          data-property-display-label="NAF"
                          data-property-data-type="String"
                          data-property-name="site.legalCompany.naf"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{site.legalCompany.naf}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="conditional-block"
                    data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"NAF","data":{"name":"naf","title":"NAF","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"description","targetNode":"","isCollection":false,"kind":"SCALAR","label":"NAF","node":"String","iconType":"csv"},"id":"site.legalCompany.naf","key":"site.legalCompany.naf","labelKey":"NAF","labelPath":"Site > Legal company > NAF"},"value2":null,"key":"1","operator":"empty"}]'
                  >
                    <!--{{#if ( or ( eq site.legalCompany.naf null ) ( eq site.legalCompany.naf "" ) ( eq site.legalCompany.naf undefined ) )}}-->
                    <div class="conditional-block-body">
                      <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                        ><strong
                          ><span
                            class="property"
                            data-property-display-label="Legal form"
                            data-property-data-type="Enum"
                            data-property-name="site.legalCompany.legalForm"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{site.legalCompany.legalForm}}</span
                          ><span
                            class="property"
                            data-property-display-label="Name"
                            data-property-data-type="String"
                            data-property-name="site.legalCompany.name"
                            data-property-data-format=""
                            data-property-parent-context="PurchaseReceipt"
                            >{{site.legalCompany.name}}</span
                          ></strong
                        ><span
                          class="property"
                          data-property-display-label="RCS"
                          data-property-data-type="String"
                          data-property-name="site.legalCompany.rcs"
                          data-property-data-format=""
                          data-property-parent-context="PurchaseReceipt"
                          >{{site.legalCompany.rcs}}</span
                        ></span
                      >
                    </div>
                    <!--{{/if}}-->
                    <div class="conditional-block-footer">&nbsp;</div>
                  </section>
                  <section
                    class="record-context"
                    data-context-object-type="CompanyAddress"
                    data-context-object-path="site.legalCompany.addresses.query.edges.0.node"
                    data-context-filter="[]"
                    data-context-list-order="{}"
                    data-alias="HvMfhpvW"
                  >
                    <!--{{#with HvMfhpvW.legalCompany.addresses.query.edges.0.node}}-->
                    <div class="report-context-body">
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"city","key":"city","labelKey":"City","labelPath":"City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( and ( eq country.iso31661Alpha3 "GBR" ) ( not ( or ( eq city null ) ( eq city "" ) ( eq city undefined ) ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="City"
                              data-property-data-type="String"
                              data-property-name="city"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{city}}</span
                            ><span
                              class="property"
                              data-property-display-label="Name"
                              data-property-data-type="String"
                              data-property-name="country.name"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{country.name}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 1"
                              data-property-data-type="String"
                              data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine1}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 2"
                              data-property-data-type="String"
                              data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine2}}</span
                            ><span
                              class="property"
                              data-property-display-label="Postal code"
                              data-property-data-type="String"
                              data-property-name="postcode"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{postcode}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"city","key":"city","labelKey":"City","labelPath":"City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( and ( eq country.iso31661Alpha3 "ZAF" ) ( not ( or ( eq city null ) ( eq city "" ) ( eq city undefined ) ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="City"
                              data-property-data-type="String"
                              data-property-name="city"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{city}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 1"
                              data-property-data-type="String"
                              data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine1}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 2"
                              data-property-data-type="String"
                              data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine2}}</span
                            ><span
                              class="property"
                              data-property-display-label="Postal code"
                              data-property-data-type="String"
                              data-property-name="postcode"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{postcode}}</span
                            ><span
                              class="property"
                              data-property-display-label="Name"
                              data-property-data-type="String"
                              data-property-name="country.name"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{country.name}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"1","operator":"equals"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"City","data":{"name":"city","title":"City","canSort":false,"canFilter":false,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"City","node":"String","iconType":"csv"},"id":"city","key":"city","labelKey":"City","labelPath":"City"},"value2":null,"key":"2","operator":"notEmpty"}]'
                      >
                        <!--{{#if ( and ( eq country.iso31661Alpha3 "USA" ) ( not ( or ( eq city null ) ( eq city "" ) ( eq city undefined ) ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="City"
                              data-property-data-type="String"
                              data-property-name="city"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{city}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 1"
                              data-property-data-type="String"
                              data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine1}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 2"
                              data-property-data-type="String"
                              data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine2}}</span
                            ><span
                              class="property"
                              data-property-display-label="Postal code"
                              data-property-data-type="String"
                              data-property-name="postcode"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{postcode}}</span
                            ><span
                              class="property"
                              data-property-display-label="Name"
                              data-property-data-type="String"
                              data-property-name="country.name"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{country.name}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                      <section
                        class="conditional-block"
                        data-context-condition='[{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"1","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"GBR","key":"1","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"2","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"ZAF","key":"2","operator":"notEqual"},{"conjunction":"and","valueType1":"property","valueType2":"constant","_id":"3","value1":{"label":"ISO 3166-1 alpha 3","data":{"name":"iso31661Alpha3","title":"ISO 3166-1 alpha 3","canSort":true,"canFilter":true,"type":"String","isCustom":false,"isMutable":false,"isOnInputType":true,"isOnOutputType":true,"enumType":null,"dataType":"","targetNode":"","isCollection":false,"kind":"SCALAR","label":"ISO 3166-1 alpha 3","node":"String","iconType":"csv"},"id":"country.iso31661Alpha3","key":"country.iso31661Alpha3","labelKey":"ISO 3166-1 alpha 3","labelPath":"Country > ISO 3166-1 alpha 3"},"value2":"USA","key":"3","operator":"notEqual"}]'
                      >
                        <!--{{#if ( and ( not ( eq country.iso31661Alpha3 "GBR" ) ) ( not ( eq country.iso31661Alpha3 "ZAF" ) ) ( not ( eq country.iso31661Alpha3 "USA" ) ) )}}-->
                        <div class="conditional-block-body">
                          <span style="font-family: 'Sage UI', Geneva, sans-serif; font-size: 10pt"
                            ><span
                              class="property"
                              data-property-display-label="City"
                              data-property-data-type="String"
                              data-property-name="city"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{city}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 1"
                              data-property-data-type="String"
                              data-property-name="addressLine1"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine1}}</span
                            ><span
                              class="property"
                              data-property-display-label="Address line 2"
                              data-property-data-type="String"
                              data-property-name="addressLine2"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{addressLine2}}</span
                            ><span
                              class="property"
                              data-property-display-label="Postal code"
                              data-property-data-type="String"
                              data-property-name="postcode"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{postcode}}</span
                            ><span
                              class="property"
                              data-property-display-label="Name"
                              data-property-data-type="String"
                              data-property-name="country.name"
                              data-property-data-format=""
                              data-property-parent-context="CompanyAddress"
                              >{{country.name}}</span
                            ></span
                          >
                        </div>
                        <!--{{/if}}-->
                        <div class="conditional-block-footer">&nbsp;</div>
                      </section>
                    </div>
                    <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
                  </section>
                </td>
              </tr>
            </tbody>
          </table>
        </figure>
      </div>
      <!--{{/with}}--><span class="report-context-footer">&nbsp;</span>
    </section>
  </div>
</section>
