{
  xtremPurchasing {
    purchaseOrder {
      query(filter: "{_id:'{{order}}'}") {
        edges {
          node {
            taxEngine
            number
            totalAmountExcludingTax
            totalAmountIncludingTax
            totalTaxAmount
            paymentTerm {
              name
            }
            orderDate
            status
            stockSite {
              legalCompany {
                name
              }
            }
            siteAddress {
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661Alpha3
              }
            }
            text {
              value
            }
            changeRequestedDescription {
              value
            }
            deliveryMode {
              name
            }
            isExternalNote
            externalNote {
              value
            }
            lines {
              query(first: 500) {
                edges {
                  node {
                    grossPrice
                    netPrice
                    amountExcludingTax
                    amountIncludingTax
                    taxes {
                      query {
                        edges {
                          node {
                            taxRate
                          }
                        }
                      }
                    }
                    quantity
                    purchaseUnit {
                      name
                    }
                    currency {
                      symbol
                    }
                    item {
                      name
                      description
                    }
                    expectedReceiptDate
                    isExternalNote
                    externalNote {
                      value
                    }
                    stockSite {
                      legalCompany {
                        priceScale
                      }
                    }
                  }
                }
              }
            }
            currency {
              symbol
            }
            supplier {
              name
              country {
                name
              }
            }
            supplierAddress {
              name
              addressLine1
              addressLine2
              city
              region
              postcode
              country {
                name
                iso31661A<PERSON>pha3
              }
            }
          }
        }
      }
    }
  }
}
