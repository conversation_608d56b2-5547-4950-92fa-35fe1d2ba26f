"role";"activity";"_vendor";"_sort_value";"has_all_permissions";"permissions";"is_active"
"100";"purchaseInvoice";"sage";"16600";"Y";"[""read"",""post""]";"Y"
"100";"purchaseOrder";"sage";"16700";"Y";"[]";"Y"
"200";"purchaseRequisition";"sage";"1100";"Y";"[]";"Y"
"200";"purchaseOrder";"sage";"1200";"N";"[""read""]";"Y"
"300";"purchaseRequisition";"sage";"1700";"N";"[""read""]";"Y"
"300";"purchaseOrder";"sage";"1800";"N";"[""read""]";"Y"
"400";"purchaseRequisition";"sage";"100";"Y";"[]";"Y"
"400";"purchaseReturn";"sage";"200";"Y";"[]";"Y"
"400";"purchaseCreditMemo";"sage";"300";"Y";"[]";"Y"
"400";"purchaseInvoice";"sage";"400";"Y";"[]";"Y"
"400";"purchaseOrder";"sage";"500";"Y";"[]";"Y"
"400";"purchaseReceipt";"sage";"600";"Y";"[]";"Y"
"500";"purchaseRequisition";"sage";"100";"Y";"[]";"Y"
"500";"purchaseReceipt";"sage";"200";"Y";"[]";"Y"
"500";"purchaseInvoice";"sage";"300";"Y";"[]";"Y"
"500";"purchaseOrder";"sage";"400";"Y";"[]";"Y"
"500";"purchaseReturn";"sage";"500";"Y";"[]";"Y"
"500";"purchaseCreditMemo";"sage";"600";"Y";"[]";"Y"
"600";"purchaseRequisition";"sage";"600";"Y";"[]";"Y"
"600";"purchaseReceipt";"sage";"700";"Y";"[]";"Y"
"600";"purchaseReturn";"sage";"750";"Y";"[]";"Y"
"700";"purchaseRequisition";"sage";"600";"Y";"[]";"Y"
"700";"purchaseReceipt";"sage";"700";"Y";"[]";"Y"
"700";"purchaseReturn";"sage";"750";"Y";"[]";"Y"
"800";"purchaseRequisition";"sage";"600";"Y";"[]";"Y"
"900";"purchaseRequisition";"sage";"600";"Y";"[]";"Y"
"Support User";"purchaseCreditMemo";"sage";"3400";"Y";"[]";"Y"
"Support User";"purchaseInvoice";"sage";"3500";"Y";"[]";"Y"
"Support User";"purchaseOrder";"sage";"3600";"Y";"[]";"Y"
"Support User";"purchaseReceipt";"sage";"3700";"Y";"[]";"Y"
"Support User";"purchaseRequisition";"sage";"3800";"Y";"[]";"Y"
"Support User";"purchaseReturn";"sage";"3900";"Y";"[]";"Y"
"Support User Read-only";"purchaseCreditMemo";"sage";"3400";"N";"[""read""]";"Y"
"Support User Read-only";"purchaseInvoice";"sage";"3500";"N";"[""read""]";"Y"
"Support User Read-only";"purchaseOrder";"sage";"3600";"N";"[""read""]";"Y"
"Support User Read-only";"purchaseReceipt";"sage";"3700";"N";"[""read""]";"Y"
"Support User Read-only";"purchaseRequisition";"sage";"3800";"N";"[""read""]";"Y"
"Support User Read-only";"purchaseReturn";"sage";"3900";"N";"[""read""]";"Y"
"Operational User";"purchaseCreditMemo";"sage";"85";"Y";"[]";"Y"
"Operational User";"purchaseInvoice";"sage";"300";"Y";"[]";"Y"
"Operational User";"purchaseReceipt";"sage";"400";"Y";"[]";"Y"
"Operational User";"purchaseRequisition";"sage";"500";"Y";"[]";"Y"
"Admin";"purchaseCreditMemo";"sage";"1100";"Y";"[]";"Y"
"Admin";"purchaseInvoice";"sage";"1200";"Y";"[]";"Y"
"Admin";"purchaseOrder";"sage";"1300";"Y";"[]";"Y"
"Admin";"purchaseReceipt";"sage";"1400";"Y";"[]";"Y"
"Admin";"purchaseRequisition";"sage";"1500";"Y";"[]";"Y"
"Admin";"purchaseReturn";"sage";"1600";"Y";"[]";"Y"
"Business User";"purchaseCreditMemo";"sage";"4800";"Y";"[]";"Y"
"Business User";"purchaseInvoice";"sage";"4900";"Y";"[]";"Y"
"Business User";"purchaseOrder";"sage";"5000";"Y";"[]";"Y"
"Business User";"purchaseReceipt";"sage";"5100";"Y";"[]";"Y"
"Business User";"purchaseRequisition";"sage";"5200";"Y";"[]";"Y"
"Business User";"purchaseReturn";"sage";"5300";"Y";"[]";"Y"
"1100";"purchaseCreditMemo";"sage";"410";"N";"[""read"",""post"",""manage""]";"Y"
"1100";"purchaseInvoice";"sage";"420";"N";"[""read"",""manage"",""acceptAllVariances"",""post""]";"Y"
