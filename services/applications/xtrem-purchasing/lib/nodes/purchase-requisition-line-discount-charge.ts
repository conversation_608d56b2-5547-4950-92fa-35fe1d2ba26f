import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../index';

@decorators.subNode<PurchaseRequisitionLineDiscountCharge>({
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    extends: () => xtremMasterData.nodes.BaseLineDiscountCharge,
})
export class PurchaseRequisitionLineDiscountCharge extends xtremMasterData.nodes.BaseLineDiscountCharge {
    @decorators.referenceProperty<PurchaseRequisitionLineDiscountCharge, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseRequisitionLine,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseRequisitionLine>;

    @decorators.decimalPropertyOverride<PurchaseRequisitionLineDiscountCharge, 'basis'>({
        dependsOn: [{ document: ['grossPrice'] }],
        async defaultValue() {
            return (await this.document).grossPrice;
        },
        updatedValue: useDefaultValue,
    })
    override readonly basis: Promise<decimal>;

    override async calculateAmount(): Promise<number> {
        return super.calculateAmount(
            await xtremMasterData.functions.getCompanyPriceScale(
                await (
                    await (
                        await this.document
                    ).receivingSite
                ).legalCompany,
            ),
        );
    }
}
