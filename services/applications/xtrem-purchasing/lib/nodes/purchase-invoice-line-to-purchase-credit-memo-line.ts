import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../index';

@decorators.node<PurchaseInvoiceLineToPurchaseCreditMemoLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { purchaseInvoiceLine: 1, purchaseCreditMemoLine: 1 },
            isUnique: true,
        },
    ],
    async controlEnd(cx) {
        /**
         * Some controls when creating a purchase credit memo based on a purchase invoice line :
         * Header level controls :
         * - same supplier/stock site/site/currency
         * Line level controls :
         * - same item/purchase unit
         */
        const invoiceLine = await this.purchaseInvoiceLine;
        const creditMemoLine = await this.purchaseCreditMemoLine;
        const invoiceLineDocument = await invoiceLine.document;
        const creditMemoLineDocument = await creditMemoLine.document;
        if (
            (await invoiceLine.item)._id !== (await creditMemoLine.item)._id ||
            (await invoiceLine.unit)._id !== (await creditMemoLine.unit)._id ||
            (await invoiceLineDocument.billBySupplier)._id !== (await creditMemoLineDocument.billBySupplier)._id ||
            (await invoiceLineDocument.site)._id !== (await creditMemoLineDocument.site)._id ||
            (await invoiceLineDocument.currency)._id !== (await creditMemoLineDocument.currency)._id ||
            (await invoiceLine.unitToStockUnitConversionFactor) !==
                (await creditMemoLine.unitToStockUnitConversionFactor)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_invoice_line_to_purchase_credit_memo_line__same_properties_invoice_to_credit_memo',
                'The purchase credit memo must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase invoice.',
            );
        }
    },
})
export class PurchaseInvoiceLineToPurchaseCreditMemoLine extends Node {
    @decorators.referenceProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'purchaseInvoiceLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLine,
    })
    readonly purchaseInvoiceLine: Reference<xtremPurchasing.nodes.PurchaseInvoiceLine>;

    @decorators.referenceProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'purchaseCreditMemoLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseCreditMemoLine,
    })
    readonly purchaseCreditMemoLine: Reference<xtremPurchasing.nodes.PurchaseCreditMemoLine>;

    @decorators.referenceProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: [{ purchaseCreditMemoLine: ['unit'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).unit;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'creditMemoQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseCreditMemoLine: ['quantity'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).quantity;
        },
    })
    readonly creditMemoQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: [{ purchaseCreditMemoLine: ['stockUnit'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'creditMemoQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseCreditMemoLine: ['quantityInStockUnit'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).quantityInStockUnit;
        },
    })
    readonly creditMemoQuantityInStockUnit: Promise<decimal>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'purchaseUnitToStockUnitConversionFactor'>(
        {
            isPublished: true,
            dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
            async getValue() {
                return (await this.purchaseCreditMemoLine).unitToStockUnitConversionFactor;
            },
        },
    )
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseCreditMemoLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'creditMemoUnitPrice'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.price,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseCreditMemoLine: ['grossPrice'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).grossPrice;
        },
    })
    readonly creditMemoUnitPrice: Promise<decimal>;

    @decorators.referenceProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'currency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ purchaseCreditMemoLine: ['currency'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<PurchaseInvoiceLineToPurchaseCreditMemoLine, 'creditMemoAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseCreditMemoLine: ['amountExcludingTax', 'currency'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).amountExcludingTax;
        },
    })
    readonly creditMemoAmount: Promise<decimal>;
}
