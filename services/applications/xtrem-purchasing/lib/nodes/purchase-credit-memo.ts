import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { BinaryStream, Collection, Context, Reference, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Decimal, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremPurchasing from '..';
import { loggers } from '../functions';
import { getPurchaseCreditMemoEmailData } from '../functions/credit-memo-send-mail';
import { BasePurchaseDocument } from './base-document-nodes'; // TODO: To confirm (We cannot use xtremPurchasing.nodes.PurchaseDocument)

// Hack to exclude defaultValue rules from upgrade
const excludeFromUpgrade = <T>(x: T) => x;

@decorators.subNode<PurchaseCreditMemo>({
    extends: () => BasePurchaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    hasAttachments: true,
    async controlDelete(cx) {
        await xtremPurchasing.events.controlDelete.documentIsPosted(this, cx);
    },
    async controlBegin(cx) {
        await xtremPurchasing.events.controls.documentIsPosted(this, cx);
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.documentNumberExistsForSupplier(this.$.context, cx, {
            supplierId: (await this.billBySupplier)._id,
            supplierDocumentNumber: await this.supplierDocumentNumber,
            nodeToQuery: this.$.factory.name,
            currentDocumentSysId: this._id,
        });
        await xtremTax.functions.validateTaxCategoryAndPaymentTerm(cx, {
            isIntacctActivationOptionEnabled: await this.$.context.isServiceOptionEnabled(
                xtremStructure.serviceOptions.intacctActivationOption,
            ),
            taxes: await this.taxes.toArray(),
            paymentTerm: await this.paymentTerm,
        });
    },
    async prepareBegin() {
        await (
            await xtremPurchasing.classes.PurchasingTaxCalculator.create(
                this.$.context,
                await this.site,
                await this.status,
                false,
                await this.wasTaxDataChanged,
            )
        ).calculateTaxData(this);

        // TODO: Remove this when the PaymentTrackingExtension is implemented after platform fixed the updatedValue problem
        await xtremPurchasing.functions.handlePaymentTrackingOnSupplierDocumentChange(this);
    },
    async createEnd() {
        await xtremPurchasing.functions.setSiteAddress(this, await this.site);
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
        await xtremPurchasing.functions.PurchaseCreditMemoLib.updateHeaderNotesOnCreation(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class PurchaseCreditMemo
    extends BasePurchaseDocument
    implements xtremMasterData.interfaces.Document, xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
{
    override getEffectiveDate() {
        return this.creditMemoDate;
    }

    @decorators.enumPropertyOverride<PurchaseCreditMemo, 'status'>({
        // dataType: () => xtremPurchasing.enums.purchaseCreditMemoStatusDataType,
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseCreditMemoStatus>;

    @decorators.enumPropertyOverride<PurchaseCreditMemo, 'displayStatus'>({
        dependsOn: ['status', 'taxCalculationStatus', 'stockTransactionStatus'],
        async updatedValue() {
            return xtremPurchasing.functions.PurchaseCreditMemoLib.calculatePurchaseCreditMemoDisplayStatus(
                await this.status,
                await this.taxCalculationStatus,
                await this.stockTransactionStatus,
                (await (await this.paymentTracking)?.status) ?? null,
            );
        },
    })
    override readonly displayStatus: Promise<xtremPurchasing.enums.PurchaseCreditMemoDisplayStatus>;

    @decorators.enumProperty<PurchaseCreditMemo, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referencePropertyOverride<PurchaseCreditMemo, 'site'>({
        filters: { control: { isFinance: true } },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseCreditMemo, 'billBySupplier'>({
        async control(cx, val) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.financialSite,
                await val.businessEntity,
            );
        },
    })
    override readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<PurchaseCreditMemo, 'businessRelation'>({
        dependsOn: ['billBySupplier'],
        defaultValue() {
            return this.billBySupplier;
        },
        updatedValue: useDefaultValue,
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.datePropertyOverride<PurchaseCreditMemo, 'date'>({
        dependsOn: ['creditMemoDate'],
        defaultValue() {
            return this.creditMemoDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly date: Promise<date>;

    @decorators.dateProperty<PurchaseCreditMemo, 'creditMemoDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
        async control(cx, val) {
            xtremPurchasing.events.controls.dateValidation(this, await this.creditMemoDate, val, cx);
        },
    })
    readonly creditMemoDate: Promise<date>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'billByLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await (await this.billBySupplier)?.billByAddress) || null;
        },
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.billBySupplier)?.businessEntity)?._id || -1;
                },
            },
        },
    })
    readonly billByLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'billByAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['billBySupplier', 'billByLinkedAddress'],
        async defaultValue() {
            return excludeFromUpgrade(await (await this.billByLinkedAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly billByAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'billByContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['billBySupplier', 'billByLinkedAddress'],
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.billByLinkedAddress)?.primaryContact)?.contact) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly billByContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'payToSupplier'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await this.billBySupplier).payToSupplier;
        },
    })
    readonly payToSupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'payToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return xtremPurchasing.functions.getDefaultPayToAddress(await this.billBySupplier);
        },
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.billBySupplier).businessEntity)._id;
                },
            },
        },
    })
    readonly payToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'payToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['payToLinkedAddress', 'payToSupplier'],
        async defaultValue() {
            return excludeFromUpgrade(await (await this.payToLinkedAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly payToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'payToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['payToSupplier', 'payToLinkedAddress'],
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.payToLinkedAddress)?.primaryContact)?.contact) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly payToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referencePropertyOverride<PurchaseCreditMemo, 'paymentTerm'>({
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await this.billBySupplier)?.paymentTerm;
        },
        filters: { control: { businessEntityType: { _in: ['supplier', 'all'] } } },
    })
    override readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.stringProperty<PurchaseCreditMemo, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.supplierDocumentNumberDataType,
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.dateProperty<PurchaseCreditMemo, 'supplierDocumentDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['creditMemoDate'],
        defaultValue() {
            return this.creditMemoDate;
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.futureDateValidation(val, cx);
        },
    })
    readonly supplierDocumentDate: Promise<date>;

    @decorators.referencePropertyOverride<PurchaseCreditMemo, 'currency'>({
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await (await this.billBySupplier).businessEntity).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.datePropertyOverride<PurchaseCreditMemo, 'fxRateDate'>({
        dependsOn: ['supplierDocumentDate'],
        defaultValue() {
            return this.supplierDocumentDate;
        },
    })
    override readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalAmountExcludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['amountExcludingTax'] }],
        getValue() {
            return this.lines.sum(line => line.amountExcludingTax);
        },
    })
    readonly calculatedTotalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalAmountExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly calculatedTotalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.binaryStreamProperty<PurchaseCreditMemo, 'pdfSupplierCreditMemo'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'binary',
        anonymizeValue: 'pdf',
    })
    readonly pdfSupplierCreditMemo: Promise<BinaryStream | null>;

    @decorators.collectionPropertyOverride<PurchaseCreditMemo, 'lines'>({
        node: () => xtremPurchasing.nodes.PurchaseCreditMemoLine,
    })
    override readonly lines: Collection<xtremPurchasing.nodes.PurchaseCreditMemoLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.dateProperty<PurchaseCreditMemo, 'dueDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['creditMemoDate', 'paymentTerm'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDueDate({
                dueDateType: await paymentTerm.dueDateType,
                days: (await paymentTerm.days) ?? 0,
                baseDate: await this.supplierDocumentDate,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly dueDate: Promise<date>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'varianceTotalAmountExcludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', 'calculatedTotalAmountExcludingTax', 'totalAmountExcludingTax'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) - (await this.calculatedTotalAmountExcludingTax),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly varianceTotalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'varianceTotalTaxAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', 'totalTaxAmountAdjusted', 'totalTaxAmount'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalTaxAmount) - (await this.totalTaxAmountAdjusted),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly varianceTotalTaxAmount: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemo, 'totalAmountIncludingTax'>({
        dependsOn: ['currency', 'totalAmountExcludingTax', 'totalTaxAmount'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) + (await this.totalTaxAmount),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    override readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'varianceTotalAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', 'calculatedTotalAmountIncludingTax', 'totalAmountIncludingTax'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountIncludingTax) - (await this.calculatedTotalAmountIncludingTax),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly varianceTotalAmountIncludingTax: Promise<decimal>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'matchingUser'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        filters: { lookup: xtremAuthorization.filters.user.interactiveUsers },
    })
    readonly matchingUser: Reference<xtremSystem.nodes.User | null>;

    @decorators.referenceProperty<PurchaseCreditMemo, 'reason'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.ReasonCode,
    })
    readonly reason: Reference<xtremMasterData.nodes.ReasonCode>;

    // property needed for the accounting interface
    @decorators.datePropertyOverride<PurchaseCreditMemo, 'documentDate'>({
        dependsOn: ['creditMemoDate'],
        getValue() {
            return this.creditMemoDate;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['calculatedTotalAmountExcludingTax', 'calculatedTotalTaxAmountAdjusted'],
        async getValue() {
            return (await this.calculatedTotalAmountExcludingTax) + (await this.calculatedTotalTaxAmountAdjusted);
        },
    })
    readonly calculatedTotalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly calculatedTotalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalTaxAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['taxAmount'] }],
        getValue() {
            return this.lines.sum(line => line.taxAmount);
        },
    })
    readonly calculatedTotalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalTaxAmountAdjusted'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['taxAmountAdjusted'] }],
        getValue() {
            return this.lines.sum(line => line.taxAmountAdjusted);
        },
    })
    readonly calculatedTotalTaxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalTaxableAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['taxableAmount'] }],
        getValue() {
            return this.lines.sum(line => line.taxableAmount);
        },
    })
    readonly calculatedTotalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseCreditMemo, 'calculatedTotalExemptAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['exemptAmount'] }],
        getValue() {
            return this.lines.sum(line => line.exemptAmount);
        },
    })
    readonly calculatedTotalExemptAmount: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemo, 'totalTaxAmountAdjusted'>({
        dependsOn: [{ lines: ['taxAmountAdjusted'] }],
        defaultValue() {
            return this.lines.sum(line => line.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
    })
    override readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.collectionProperty<PurchaseCreditMemo, 'apOpenItems'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.BaseOpenItem,
        join: {
            documentType() {
                return 'purchaseCreditMemo';
            },
            documentSysId() {
                return this._id;
            },
        },
    })
    readonly apOpenItems: Collection<xtremFinanceData.nodes.BaseOpenItem>;

    @decorators.integerProperty<PurchaseCreditMemo, 'openItemSysId'>({
        isPublished: true,
        isNullable: true,
        async getValue() {
            return (await this.apOpenItems.takeOne(node => node._id !== -1))?._id ?? null;
        },
    })
    readonly openItemSysId: Promise<number | null>;

    @decorators.booleanProperty<PurchaseCreditMemo, 'isOpenItemPageOptionActive'>({
        isPublished: true,
        serviceOptions: () => [xtremStructure.serviceOptions.openItemPageOption],
        getValue() {
            return true;
        },
    })
    readonly isOpenItemPageOptionActive: Promise<boolean>;

    @decorators.collectionProperty<PurchaseCreditMemo, 'postingDetails'>({
        isPublished: true,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType() {
                return { _in: ['purchaseCreditMemo', 'apInvoice'] };
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.mutation<typeof PurchaseCreditMemo, 'post'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseCreditMemo,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async post(
        context: Context,
        creditMemo: PurchaseCreditMemo,
    ): Promise<
        xtremFinanceData.interfaces.MutationResult & {
            stockPostingResult: string;
        }
    > {
        const postResult = {
            ...(await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoControlFromNotificationPayloadErrors(
                context,
                creditMemo,
                'purchaseCreditMemo',
            )),
            stockPostingResult: '',
        };

        if (postResult.message) {
            return postResult;
        }

        if ((await creditMemo.status) !== 'draft') {
            postResult.message = context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_post_credit_memo_when_status_is_not_draft',
                'The status is not {{draft}}, the credit memo cannot be posted.',
                {
                    draft: context.localizeEnumMember('@sage/xtrem-purchasing/PurchaseCreditMemoStatus', 'draft'),
                },
            );
            throw new BusinessRuleError(postResult.message);
        }

        if ((await creditMemo.calculatedTotalAmountExcludingTax) !== (await creditMemo.totalAmountExcludingTax)) {
            postResult.message = context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_post_credit_memo_when_totals_not_equal',
                'The user entered total tax excluded amount is not equal to the sum of the lines.',
            );

            throw new BusinessRuleError(postResult.message);
        }

        if ((await creditMemo.taxCalculationStatus) !== 'done') {
            postResult.message = context.localize(
                '@sage/xtrem-purchasing/cant_post_credit_memo_when_taxCalculationStatus_is_not_done',
                'The tax calculation is not {{done}}, the credit memo cannot be posted.',
                {
                    done: context.localizeEnumMember('@sage/xtrem-master-data/TaxCalculationStatus', 'done'),
                },
            );
            throw new BusinessRuleError(postResult.message);
        }

        xtremDistribution.functions.dateCheckControl(context, {
            discountPaymentBeforeDate: await (await creditMemo.paymentTracking)?.discountPaymentBeforeDate,
            dueDate: await creditMemo.dueDate,
        });

        await context.runInWritableContext(async writableContext => {
            const writableCreditMemo = await writableContext.read(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                {
                    _id: creditMemo._id,
                },
                { forUpdate: true },
            );

            // The reply of stock and finance updates the status of the credit memo
            // => it must be set to 'inProgress' before posting to stock and finance
            await writableCreditMemo.$.set({
                status: 'inProgress',
                forceUpdateForStock: (await writableCreditMemo.status) === 'error',
            });
            await writableCreditMemo.$.save();
        });

        postResult.stockPostingResult = await PurchaseCreditMemo.postToStock(context, [creditMemo._id]);
        const stockPostingResult = postResult.stockPostingResult.length
            ? (JSON.parse(
                  postResult.stockPostingResult,
              ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.correction>)
            : { result: '', error: '' };

        const creditMemoID = creditMemo._id;

        if (stockPostingResult.result === 'error') {
            await context.runInWritableContext(async childContext => {
                const purchaseCreditMemo = await childContext.read(
                    PurchaseCreditMemo,
                    { _id: creditMemoID },
                    { forUpdate: true },
                );
                await purchaseCreditMemo.$.set({ status: 'error', forceUpdateForFinance: true });
                postResult.wasSuccessful = false;
                postResult.message = stockPostingResult.error;
                await purchaseCreditMemo.$.save();
            });
        } else {
            postResult.wasSuccessful = true;
            postResult.message = context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_credit_memo__posted',
                'The purchase credit memo has been posted.',
            );
        }

        return postResult;
    }

    // Repost creates an update notification to the acc engine in order to update att and dimensions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof PurchaseCreditMemo, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseCreditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseCreditMemo,
            },
            {
                name: 'documentData',
                type: 'object',
                isMandatory: true,
                properties: {
                    header: {
                        type: 'object',
                        properties: {
                            supplierDocumentNumber: 'string',
                            paymentData: {
                                type: 'object',
                                properties: {
                                    supplierDocumentDate: 'date',
                                    paymentTerm: { type: 'reference', node: () => xtremMasterData.nodes.PaymentTerm },
                                },
                            },
                            totalTaxAmount: 'decimal',
                            taxes: {
                                type: 'array',
                                item: {
                                    type: 'instance',
                                    isTransientInput: true,
                                    node: () => xtremTax.nodes.DocumentTax,
                                },
                            },
                        },
                    },
                    lines: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                baseDocumentLineSysId: 'integer',
                                storedAttributes: 'json',
                                storedDimensions: 'json',
                                uiTaxes: 'string',
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static repost(
        context: Context,
        purchaseCreditMemo: PurchaseCreditMemo,
        documentData: xtremPurchasing.interfaces.financeIntegration.PurchaseInvoiceCreditMemoRepost,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremPurchasing.functions.PurchaseCreditMemoLib.repost(context, purchaseCreditMemo, documentData);
    }

    @decorators.mutation<typeof PurchaseCreditMemo, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseCreditMemo,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async financeIntegrationCheck(
        context: Context,
        creditMemo: PurchaseCreditMemo,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        const financeIntegrationCheckResult =
            await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoControlFromNotificationPayloadErrors(
                context,
                creditMemo,
                'purchaseCreditMemo',
            );

        if (!financeIntegrationCheckResult.message) {
            financeIntegrationCheckResult.wasSuccessful = true;
        }

        return financeIntegrationCheckResult;
    }

    /** Page url of the document TODO get this from the datatype // tunnelPAge of the document??  */
    @decorators.stringPropertyOverride<PurchaseCreditMemo, 'page'>({
        getValue: () => '@sage/xtrem-purchasing/PurchaseCreditMemo',
    })
    override readonly page: Promise<string>;

    async beforeSendNotificationToBuyerMail(user: xtremSystem.nodes.User) {
        const { subject, data } = await getPurchaseCreditMemoEmailData({ document: this, user });

        return {
            mailerUser: user,
            mail: { subject, template: 'purchase_credit_memo_buyer_notification_mail', data },
        };
    }

    /**
     * Operation that send an approval email to a user pre-filled email address.
     * @param context
     * @param purchaseCreditMemoNumber
     * @param url
     * @param email
     */
    @decorators.mutation<typeof PurchaseCreditMemo, 'sendNotificationToBuyerMail'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseCreditMemo,
                isWritable: true,
            },
            {
                name: 'user',
                type: 'instance',
                node: () => xtremSystem.nodes.User,
                isTransientInput: true,
            },
        ],
        return: 'boolean',
    })
    static async sendNotificationToBuyerMail(
        context: Context,
        document: PurchaseCreditMemo,
        user: xtremSystem.nodes.User,
    ): Promise<boolean> {
        const data = await document.beforeSendNotificationToBuyerMail(user);
        await xtremMasterData.functions.sendMail(context, data, document);
        return true;
    }

    @decorators.notificationListener<typeof PurchaseCreditMemo>({
        topic: 'PurchaseCreditMemo/accountingInterface',
        startsReadOnly: true,
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        // isJustForStatus indicates that the update of finance transaction node was already done
        const shouldUpdatePurchaseCreditMemoStatus =
            payload.isJustForStatus ??
            (await context.runInWritableContext(writableContext => {
                return xtremFinanceData.functions.reactToFinanceIntegrationReply(writableContext, payload);
            }));

        if (shouldUpdatePurchaseCreditMemoStatus) {
            // we need to update the status of the original Purchase credit memo depending on the reply from finance integration
            await context.runInWritableContext(async writableContext => {
                const creditMemo = await writableContext.read(
                    xtremPurchasing.nodes.PurchaseCreditMemo,
                    {
                        number: payload.documentNumber,
                    },
                    { forUpdate: true },
                );

                // the status of the credit memo is updated depending on the calculated field financeIntegrationStatus
                await creditMemo.$.set({
                    status: xtremFinanceData.functions.statusMapping(
                        await creditMemo.financeIntegrationStatus,
                        !!payload.financeExternalIntegration,
                    ),
                });

                await creditMemo.$.set({ forceUpdateForFinance: true }); // make sure the update of the Purchase credit memo will not be refused by the control in saveBegin
                await creditMemo.$.save();
            });
        }
    }

    /* ********************************
    Notification for stock engine
    ******************************** */
    static postToStock(context: Context, purchaseCreditMemoIDs: integer[]): Promise<string> {
        return xtremStockData.functions.stockValuationLib.postToStockReceiptCostValueCorrection(context, {
            documentIds: purchaseCreditMemoIDs,
            correctorDocumentNode: PurchaseCreditMemo,
            correctedDocumentNode: xtremPurchasing.nodes.PurchaseReceipt,
            functionToManageLinesWithoutStockUpdate: PurchaseCreditMemo.manageLinesWithoutStockUpdate,
            functionToGetLinesToCorrect: PurchaseCreditMemo._getLinesToCorrect,
            replyTopic: 'PurchaseCreditMemo/stock/correction/reply',
        });
    }

    static readonly _getLinesToCorrect: xtremStockData.interfaces.FunctionToGetLinesToCorrect = async (
        context: Context,
        line: xtremPurchasing.nodes.PurchaseCreditMemoLine,
    ) => {
        const { invoiceLine, receiptLine } = await line.getReceiptAndInvoice();

        if (!receiptLine || !invoiceLine) return { linesToCorrect: [] };

        if ((await invoiceLine.netPrice) !== (await line.netPrice)) {
            // When the price is different of the invoice, it's a value credit memo
            return {
                linesToCorrect: [
                    {
                        correctedDocumentLine: receiptLine,
                        impactedQuantity: await line.quantityInStockUnit,
                        amountToAbsorb:
                            -(await line.quantity) *
                            (await line.netPrice) *
                            (await (await line.document).getCompanyFxRate()),
                    },
                ],
            };
        }

        if (!(await line.purchaseReturnLine)) {
            // When the price is the same than the invoice, it must be considered as a cancellation of the invoice (except in the case of return)
            // Then we get the value that has been passed for the invoice posting
            const [oldAdjustment] = (
                await xtremPurchasing.nodes.PurchaseInvoice._getLinesToCorrect(context, invoiceLine)
            ).linesToCorrect;
            return {
                linesToCorrect: [
                    {
                        correctedDocumentLine: receiptLine,
                        impactedQuantity: await line.quantityInStockUnit,
                        amountToAbsorb:
                            ((-oldAdjustment.amountToAbsorb * (await line.quantityInStockUnit)) /
                                (await invoiceLine.quantityInStockUnit)) *
                            (await (await line.document).getCompanyFxRate()),
                    },
                ],
            };
        }

        return {
            linesToCorrect: [
                {
                    correctedDocumentLine: receiptLine,
                    impactedQuantity: await line.quantityInStockUnit,
                    amountToAbsorb: 0,
                },
            ],
        };
    };

    static async manageLinesWithoutStockUpdate(context: Context, purchaseCreditMemoID: number): Promise<number[]> {
        const creditMemo = await context.read(PurchaseCreditMemo, { _id: purchaseCreditMemoID });

        const lineResults: xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines'] =
            await creditMemo.lines
                .filter(async line => {
                    return (
                        (await xtremStockData.functions.stockDocumentLib.shouldSkipStockManagementForThisLine(line)) ||
                        !(await line.getReceiptAndInvoice()).receiptLine
                    );
                })
                .map(async line => {
                    return {
                        id: line._id,
                        sortValue: await line._sortValue,
                        stockUpdateResultStatus: 'none' as xtremStockData.enums.StockUpdateResultAction,
                        originLineIds: [],
                        stockJournalRecords: [],
                    };
                })
                .toArray();
        if (lineResults.length > 0)
            await PurchaseCreditMemo.manageStockReply(context, {
                updateResults: { correction: { documents: [{ id: creditMemo._id, lines: lineResults }] } },
                requestNotificationId: 'none',
                movementHasBeenSkipped: true,
            });
        return lineResults.map(lineResult => lineResult.id);
    }

    @decorators.notificationListener<typeof PurchaseCreditMemo>({
        startsReadOnly: true,
        topic: 'PurchaseCreditMemo/stock/correction/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, PurchaseCreditMemo);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ): Promise<void> {
        // as the reply concerns purchase receipts we need to adapt the reply payload to the purchase credit memo
        const correctionPayload = (
            await xtremStockData.functions.notificationLib.stockCorrectionReplyToOrigin(readOnlyContext, payload, [
                {
                    lineClass: xtremPurchasing.nodes.PurchaseCreditMemoLine,
                    headerClass: xtremPurchasing.nodes.PurchaseCreditMemo,
                },
            ])
        )[0];
        if (!correctionPayload) return;

        await PurchaseCreditMemo.manageStockReply(readOnlyContext, correctionPayload, payload);
    }

    static async manageStockReply(
        readOnlyContext: Context,
        correctionPayload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
        stockReplyPayload?: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ) {
        loggers.creditMemo.verbose(
            () => `manageStockReply correctionPayload=${JSON.stringify(correctionPayload, null, 4)}`,
        );
        const correctionUpdateResult = await readOnlyContext.runInWritableContext(async writableContext => {
            if (stockReplyPayload) {
                await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    stockReplyPayload,
                    xtremPurchasing.nodes.PurchaseReceipt,
                    {
                        beforeDocumentSave: xtremPurchasing.nodes.PurchaseReceipt.updateHeaderAfterStockUpdate,
                    },
                );
            }
            return (
                await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    correctionPayload,
                    PurchaseCreditMemo,
                )
            ).correction;
        });
        if (!correctionUpdateResult) return;

        if (!correctionUpdateResult.transactionHadAnError) {
            await readOnlyContext.runInWritableContext(async writableContext => {
                await correctionUpdateResult.documents.forEach(async document => {
                    if (document.isStockDocumentCompleted) {
                        const purchaseCreditMemo = await xtremMasterData.functions.getWritableNode(
                            writableContext,
                            PurchaseCreditMemo,
                            document.id,
                        );
                        await PurchaseCreditMemo.onceStockCompleted(writableContext, purchaseCreditMemo);
                    }
                });
            });
        }
    }

    static async onceStockCompleted(context: Context, creditMemo: PurchaseCreditMemo): Promise<void> {
        await creditMemo.$.set({
            paymentTracking: {
                paymentTerm: await creditMemo.paymentTerm,
            },
            status: 'posted',
            forceUpdateForFinance: true, // make sure the update of the Purchase credit memo will not be refused by the control in saveBegin
        });
        await creditMemo.$.save();

        // send notification in order to create staging table entries for the accounting engine
        await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoNotification(
            context,
            creditMemo as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
            (await creditMemo.lines.toArray()) as xtremFinanceData.interfaces.InvoiceDocumentLine[],
            'purchaseCreditMemo',
            'PurchaseCreditMemo/accountingInterface',
        );
    }

    // To use only when a purchase credit memo notification for finance was lost.
    // There should be no records on the accounting staging and there should be at list one record on finance transaction,
    // meaning that all was done except that the notification was lost\not recorded on the accounting staging.
    // Records on the finance transaction should be all in a 'recording', 'pending', 'error' or 'notRecorded' status.
    @decorators.mutation<typeof PurchaseCreditMemo, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            { name: 'purchaseCreditMemo', type: 'reference', isMandatory: true, node: () => PurchaseCreditMemo },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(
        context: Context,
        purchaseCreditMemo: PurchaseCreditMemo,
    ): Promise<boolean> {
        loggers.creditMemo.info(
            context.localize(
                '@sage/xtrem-purchasing/node__purchase_credit_memo__resend_notification_for_finance',
                'Regenerating finance notification for purchase credit memo number: {{documentNumber}}.',
                { documentNumber: await purchaseCreditMemo.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await purchaseCreditMemo.number,
                documentType: 'purchaseCreditMemo',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoNotification(
                context,
                purchaseCreditMemo as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
                (await purchaseCreditMemo.lines.toArray()) as xtremFinanceData.interfaces.InvoiceDocumentLine[],
                'purchaseCreditMemo',
                'PurchaseCreditMemo/accountingInterface',
            );
        }

        return true;
    }

    @decorators.notificationListener<typeof PurchaseCreditMemo>({
        topic: 'PurchaseCreditMemo/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const purchaseCreditMemo = await context.read(PurchaseCreditMemo, { number: document.number });

        await PurchaseCreditMemo.resendNotificationForFinance(context, purchaseCreditMemo);
    }

    @decorators.mutation<typeof PurchaseCreditMemo, 'synchronizeDisplayStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseCreditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseCreditMemo,
            },
        ],
        return: 'boolean',
    })
    static async synchronizeDisplayStatus(context: Context, purchaseCreditMemo: PurchaseCreditMemo): Promise<boolean> {
        if (
            (await purchaseCreditMemo.status) === 'inProgress' &&
            (await purchaseCreditMemo.stockTransactionStatus) === 'draft' &&
            !(await context.queryCount(xtremFinanceData.nodes.FinanceTransaction, {
                filter: {
                    documentNumber: await purchaseCreditMemo.number,
                    documentType: 'purchaseCreditMemo',
                },
            }))
        ) {
            await context.bulkUpdate(xtremMasterData.nodes.BaseDocument, {
                set: { status: 'draft', displayStatus: 'draft' },
                where: { _id: purchaseCreditMemo._id },
            });

            return true;
        }
        return false;
    }

    // Listener to update the display status of a purchase credit memo, when the open item is paid or partially paid in finance
    @decorators.notificationListener<typeof PurchaseCreditMemo>({
        topic: 'PurchaseCreditMemo/updatePaymentStatus',
        startsReadOnly: true,
    })
    static async setPurchaseCreditMemoDisplayStatus(
        readOnlyContext: Context,
        payload: { _id: number; paymentStatus: xtremFinanceData.enums.OpenItemStatus },
    ) {
        await readOnlyContext.runInWritableContext(async context => {
            const purchaseCreditMemoToUpdate = await context.read(
                PurchaseCreditMemo,
                { _id: payload._id },
                { forUpdate: true },
            );
            const displayStatus =
                xtremPurchasing.functions.PurchaseCreditMemoLib.calculatePurchaseCreditMemoDisplayStatus(
                    await purchaseCreditMemoToUpdate.status,
                    await purchaseCreditMemoToUpdate.taxCalculationStatus,
                    await purchaseCreditMemoToUpdate.stockTransactionStatus,
                    payload.paymentStatus,
                );
            await purchaseCreditMemoToUpdate.$.set({
                forceUpdateForFinance: true,
                displayStatus,
            });
            await purchaseCreditMemoToUpdate.$.save();
        });
    }

    @decorators.mutation<typeof PurchaseCreditMemo, 'enforceStatusPosted'>({
        isPublished: true,
        parameters: [
            {
                name: 'creditMemo',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseCreditMemo,
            },
        ],
        return: 'boolean',
    })
    static async enforceStatusPosted(
        context: Context,
        creditMemo: xtremPurchasing.nodes.PurchaseCreditMemo,
    ): Promise<boolean> {
        const status = await creditMemo.status;
        const displayStatus = await creditMemo.displayStatus;
        const allPosted = await creditMemo.postingDetails.every(
            async detail => (await detail.postingStatus) === 'posted',
        );
        if (status === 'inProgress' && displayStatus === 'postingInProgress' && allPosted) {
            await creditMemo.$.set({ status: 'posted', displayStatus: 'posted', forceUpdateForFinance: true });
            await creditMemo.$.save();
        }
        return true;
    }
}
