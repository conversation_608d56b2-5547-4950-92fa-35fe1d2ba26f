import type { Collection, Context, date, decimal, NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { asyncArray, decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '..';
import { loggers } from '../functions';
import { isPurchaseRequisitionLinePropertyDisabled } from '../shared-functions/index';

@decorators.subNode<PurchaseRequisitionLine>({
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.RequisitionLine.checkApprovedStatus(cx, this);

        await xtremPurchasing.events.controls.RequisitionLine.orderedQuantityGreaterThanTheRequisitionLineQuantity(
            this,
            cx,
        );
        await xtremPurchasing.events.controls.RequisitionLine.convertedQuantityInPurchaseUnitDifferentThanQuantityInStockUnit(
            this,
            cx,
        );
    },

    async controlDelete(cx): Promise<void> {
        await xtremPurchasing.events.controlDelete.purchaseRequisitionLine.controlDeleteApproved(cx, this);
    },

    async saveBegin() {
        await xtremPurchasing.events.saveBegin.purchaseRequisitionLine.setLineStatus(this);
    },
})
export class PurchaseRequisitionLine extends xtremMasterData.nodes.BaseDocumentItemLine {
    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'document'>({
        node: () => xtremPurchasing.nodes.PurchaseRequisition,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseRequisition>;

    /** deprecated */
    @decorators.referenceProperty<PurchaseRequisitionLine, 'receivingSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly receivingSite: Reference<xtremSystem.nodes.Site>;

    // To maintain  the compability of the api
    @decorators.enumProperty<PurchaseRequisitionLine, 'approvalStatus'>({
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseDocumentApprovalStatusDataType,
        async getValue() {
            return (await this.document).approvalStatus;
        },
    })
    readonly approvalStatus: Promise<xtremPurchasing.enums.PurchaseDocumentApprovalStatus>;

    /** receivingSite */
    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'site'>({
        updatedValue: useDefaultValue,
        isFrozen() {
            return this._isLinePropertyFrozen('receivingSite');
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'stockSiteLinkedAddress'>({
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['site'],
        async defaultValue() {
            return (await this.siteLinkedAddress) ?? null;
        },
        updatedValue: useDefaultValue,
    })
    override readonly stockSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    /** deprecated */
    @decorators.enumProperty<PurchaseRequisitionLine, 'lineStatus'>({
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseDocumentStatusDataType,
        getValue() {
            return this.status;
        },
    })
    readonly lineStatus: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumPropertyOverride<PurchaseRequisitionLine, 'status'>({
        dependsOn: ['lineOrderStatus', { document: ['approvalStatus'] }],
        updatedValue() {
            return xtremPurchasing.functions.RequisitionLine.getLineStatus(this);
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumProperty<PurchaseRequisitionLine, 'lineOrderStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseRequisitionOrderStatusDataType,
        /**
         * Purchase requisition - order status property :
         * - is defaulted to 'notOrdered' when record is created
         */
        dependsOn: ['orderedQuantityInStockUnit', 'quantityInStockUnit'],
        defaultValue: 'notOrdered',
        updatedValue() {
            return xtremPurchasing.functions.RequisitionLine.getLineOrderStatus(this);
        },
        duplicatedValue: useDefaultValue,
    })
    readonly lineOrderStatus: Promise<xtremPurchasing.enums.PurchaseRequisitionOrderStatus>;

    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'item'>({
        dependsOn: ['site'],
        filters: {
            control: {
                isBought: true,
                type: { _ne: 'landedCost' },
                async itemSites() {
                    return { _atLeast: 1, site: await this.site };
                },
            },
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.landedCostItemValidation(
                val,
                (await this.document).$.factory.name,
                cx,
            );
        },
        async isFrozen() {
            return !!((await this._isLinePropertyFrozen('item')) || (await this.item)?._id);
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'itemSite'>({
        node: () => xtremMasterData.nodes.ItemSite,
        dependsOn: ['item', 'site'],
    })
    override readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.referenceProperty<PurchaseRequisitionLine, 'itemSupplier'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemSupplier,
        dependsOn: ['item', 'supplier'],
        join: {
            item() {
                return this.item;
            },
            supplier() {
                return this.supplier;
            },
        },
    })
    readonly itemSupplier: Reference<xtremMasterData.nodes.ItemSupplier | null>;

    @decorators.referenceProperty<PurchaseRequisitionLine, 'itemSiteSupplier'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemSiteSupplier,
        dependsOn: ['itemSite', 'supplier'],
        join: {
            itemSite() {
                return this.itemSite;
            },
            supplier() {
                return this.supplier;
            },
        },
    })
    readonly itemSiteSupplier: Reference<xtremMasterData.nodes.ItemSiteSupplier | null>;

    @decorators.stringProperty<PurchaseRequisitionLine, 'requestedItemDescription'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.name,
        dependsOn: ['item'],
        async defaultValue() {
            return (await (await this.item)?.description) ?? '';
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.RequisitionLine.itemDescriptionValidation(this, val, cx);
        },
        isFrozen() {
            return this._isLinePropertyFrozen('requestedItemDescription');
        },
    })
    readonly requestedItemDescription: Promise<string>;

    @decorators.dateProperty<PurchaseRequisitionLine, 'needByDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: [{ document: ['requestDate'] }],
        async defaultValue() {
            return (await this.document).requestDate;
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.RequisitionLine.needByDateValidation(this, val, cx);
        },
        isFrozen() {
            return this._isLinePropertyFrozen('needByDate');
        },
    })
    readonly needByDate: Promise<date>;

    @decorators.decimalPropertyOverride<PurchaseRequisitionLine, 'quantity'>({
        isFrozen() {
            return this._isLinePropertyFrozen('quantity');
        },
    })
    override readonly quantity: Promise<decimal>;

    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'unit'>({
        filters: {
            control: {
                async type() {
                    const item = await this.item;
                    if ((await item?.type) === 'good' && (await item?.isStockManaged)) {
                        return { _nin: ['time', 'temperature'] };
                    }
                    return { _nin: ['temperature'] };
                },
                async id() {
                    return (await (
                        await this.item
                    )?.stockUnit)
                        ? {
                              _in: await xtremMasterData.functions.listOfPurchaseUnits(
                                  (await (
                                      await this.item
                                  )?.stockUnit)!,
                              ),
                          }
                        : { _ne: '' };
                },
            },
        },
        dependsOn: ['item', 'supplier'],
        async defaultValue() {
            const item = await this.item;
            const itemSupplier = await this.itemSupplier;
            if (itemSupplier) {
                return itemSupplier.purchaseUnitOfMeasure;
            }
            if (item) {
                return (await item.purchaseUnit) ?? item.stockUnit;
            }

            return null;
        },
        updatedValue: useDefaultValue,
        async control(cx, val) {
            // TODO: after extending Purchase Requisition line to Base Document line
            // await xtremPurchasing.events.controls.unitToPreviousUnitConversionFactorValidation(this, val, cx);
            if (
                val &&
                this.$.status === NodeStatus.modified &&
                (await (await this.$.old).unit)?._id !== val._id &&
                !(await xtremMasterData.functions.listOfPossibilities(val)).length
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/nodes__purchase_requisition_line__no_purchase_unit_conversion_coefficient',
                    'The current purchase unit has no conversion factor for the previous purchase unit of the line.',
                );
            }
            await xtremPurchasing.events.controls.RequisitionLine.purchaseUnitValidation(this, val, cx);
        },
        isFrozen() {
            return this._isLinePropertyFrozen('unit');
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'stockUnit'>({
        dependsOn: ['item', 'unit'],
        async isFrozen() {
            const item = await this.item;
            const stockUnit = await this.stockUnit;
            return (
                (item && stockUnit && (await item.stockUnit)._id !== stockUnit._id) ||
                this._isLinePropertyFrozen('stockUnit')
            );
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<PurchaseRequisitionLine, 'quantityInStockUnit'>({
        dependsOn: ['item', 'unit', 'stockUnit', 'quantity', 'unitToStockUnitConversionFactor', 'supplier'],
        async defaultValue() {
            const item = await this.item;
            const unit = await this.unit;
            const stockUnit = await this.stockUnit;
            return unit && stockUnit && item
                ? xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                      await this.quantity,
                      unit,
                      stockUnit,
                      true,
                      false,
                      await this._isConversionFactorFrozen,
                      this.$.status,
                      await this.unitToStockUnitConversionFactor,
                      item,
                      await this.supplier,
                  )
                : null;
        },
        updatedValue: useDefaultValue,
        isFrozen() {
            return this._isLinePropertyFrozen('quantityInStockUnit');
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseRequisitionLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', 'unit', 'stockUnit', 'supplier'],
        async defaultValue() {
            const unit = await this.unit;
            const stockUnit = await this.stockUnit;
            const item = (await this.item) ?? undefined;
            const type = item ? 'purchase' : undefined;
            const supplier = (await this.supplier) || undefined;
            return unit && stockUnit
                ? xtremMasterData.functions.getConvertCoefficient(unit, stockUnit, type, item, supplier)
                : null;
        },
        async updatedValue() {
            const unit = await this.unit;
            const stockUnit = await this.stockUnit;
            if (unit && stockUnit && (this.$.status === NodeStatus.added || !(await this._isConversionFactorFrozen))) {
                const item = (await this.item) || undefined;
                const type = item ? 'purchase' : undefined;
                const supplier = (await this.supplier) || undefined;
                return xtremMasterData.functions.getConvertCoefficient(unit, stockUnit, type, item, supplier);
            }
            return this.unitToStockUnitConversionFactor;
        },
        async isFrozen() {
            return (
                (await this._isConversionFactorFrozen) || this._isLinePropertyFrozen('unitToStockUnitConversionFactor')
            );
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.referenceProperty<PurchaseRequisitionLine, 'supplier'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        isFrozen() {
            return this._isLinePropertyFrozen('supplier');
        },
        async control(cx, val) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.receivingSite,
                await val?.businessEntity,
            );
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<PurchaseRequisitionLine, 'currency'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['supplier'],
        async defaultValue() {
            const supplier = await this.supplier;
            return (supplier && (await (await supplier.businessEntity).currency)) || null;
        },
        async isFrozen() {
            return (await this.supplier) === (await (await this.$.old).supplier)
                ? this._isLinePropertyFrozen('currency')
                : false;
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.RequisitionLine.grossPriceCurrencyValidation(this, val, cx);
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency | null>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'grossPrice'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.companyPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ document: ['requestDate'] }, 'receivingSite', 'supplier', 'currency', 'item', 'quantity', 'unit'],

        async defaultValue() {
            const grossPrice = await xtremPurchasing.functions.getPrice(this);
            return grossPrice >= 0 ? grossPrice : 0;
        },

        async isFrozen() {
            return (await this.supplier) === (await (await this.$.old).supplier)
                ? this._isLinePropertyFrozen('grossPrice')
                : false;
        },
    })
    readonly grossPrice: Promise<decimal>;

    @decorators.stringProperty<PurchaseRequisitionLine, 'changeRequestedDescription'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly changeRequestedDescription: Promise<string>;

    @decorators.collectionProperty<PurchaseRequisitionLine, 'purchaseOrderLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseRequisitionLineToPurchaseOrderLine,
        reverseReference: 'purchaseRequisitionLine',
    })
    readonly purchaseOrderLines: Collection<xtremPurchasing.nodes.PurchaseRequisitionLineToPurchaseOrderLine>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'orderedQuantity'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseOrderLines: ['orderedQuantity'] }],
        async getValue() {
            return Math.max(await this.purchaseOrderLines.sum(line => line.orderedQuantity), 0);
        },
    })
    readonly orderedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'quantityToOrder'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: ['quantity', 'orderedQuantity'],
        async getValue() {
            return Math.max((await this.quantity) - (await this.orderedQuantity), 0);
        },
    })
    readonly quantityToOrder: Promise<decimal>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'orderedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseOrderLines: ['orderedQuantityInStockUnit'] }],
        async getValue() {
            return Math.max(await this.purchaseOrderLines.sum(line => line.orderedQuantityInStockUnit), 0);
        },
    })
    readonly orderedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'quantityToOrderInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityInStockUnit', 'orderedQuantityInStockUnit'],
        async getValue() {
            return Math.max((await this.quantityInStockUnit) - (await this.orderedQuantityInStockUnit), 0);
        },
    })
    readonly quantityToOrderInStockUnit: Promise<decimal>;

    @decorators.jsonPropertyOverride<PurchaseRequisitionLine, 'storedDimensions'>({
        async control(cx, val) {
            if (val) {
                await asyncArray(Object.entries(val)).forEach(async ([dimensionType, dimension]) => {
                    if (
                        (await xtremFinanceData.functions.checkDimensionTypeInactive(
                            this.$.context,
                            xtremFinanceData.nodes.DimensionType,
                            dimensionType,
                        )) ||
                        (await xtremFinanceData.functions.checkValueInactive(
                            this.$.context,
                            xtremFinanceData.nodes.Dimension,
                            dimension as string,
                            {
                                dimensionType: { docProperty: dimensionType, isActive: true },
                            },
                        ))
                    ) {
                        cx.error.addLocalized(
                            '@sage/xtrem-purchasing/is_active_dimension_inactive',
                            'The {{dimension}} dimension or "{{dimensionType}}" dimension type is inactive.',
                            {
                                dimension,
                                dimensionType,
                            },
                        );
                    }
                });
            }
        },
        isFrozen() {
            return this._isLinePropertyFrozen('storedDimensions');
        },
    })
    override readonly storedDimensions: Promise<object>;

    @decorators.referencePropertyOverride<PurchaseRequisitionLine, 'analyticalData'>({
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
    })
    override readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonPropertyOverride<PurchaseRequisitionLine, 'storedAttributes'>({
        async control(cx, val) {
            await xtremFinanceData.events.control.documentWithDimensionsControls.storedAttributesGenericControl(
                this.$.context,
                cx,
                val,
            );
        },
        isFrozen() {
            return this._isLinePropertyFrozen('storedAttributes');
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes>;

    @decorators.jsonPropertyOverride<PurchaseRequisitionLine, 'computedAttributes'>({
        dependsOn: ['document', 'item', 'receivingSite'],
        async computeValue() {
            return xtremPurchasing.functions.computeAttributes(this.$.context, {
                site: await this.receivingSite,
                item: await this.item,
                supplier: await (await this.supplier)?.billBySupplier,
                line: this,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.enumProperty<PurchaseRequisitionLine, 'priceOrigin'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        dependsOn: ['grossPrice', 'priceOrigin'],
        dataType: () => xtremPurchasing.enums.priceOriginDataType,
        async updatedValue() {
            return (await this.grossPrice) ? this.priceOrigin : null;
        },
        async isFrozen() {
            return (await this.supplier) === (await (await this.$.old).supplier)
                ? this._isLinePropertyFrozen('priceOrigin')
                : false;
        },
    })
    readonly priceOrigin: Promise<xtremPurchasing.enums.PriceOrigin | null>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'totalTaxExcludedAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['quantity', 'netPrice'],
        async defaultValue() {
            return (await this.quantity) * (await this.netPrice);
        },
        updatedValue: useDefaultValue,
        async isFrozen() {
            return (await this.supplier) === (await (await this.$.old).supplier)
                ? this._isLinePropertyFrozen('totalTaxExcludedAmount')
                : false;
        },
    })
    readonly totalTaxExcludedAmount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'orderedPercentage'>({
        async getValue() {
            return (await this.quantity) !== 0 ? ((await this.orderedQuantity) / (await this.quantity)) * 100 : 0;
        },
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.percentage,
    })
    readonly orderedPercentage: Promise<decimal>;

    @decorators.collectionProperty<PurchaseRequisitionLine, 'discountCharges'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremPurchasing.nodes.PurchaseRequisitionLineDiscountCharge,
        dependsOn: ['grossPrice'],
        defaultValue() {
            return xtremMasterData.functions.getDiscountChargeDefaultValue();
        },
    })
    readonly discountCharges: Collection<xtremPurchasing.nodes.PurchaseRequisitionLineDiscountCharge>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'discount'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'decrease',
                val,
                await this.grossPrice,
            );
        },
    })
    readonly discount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'charge'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign'] }],
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'increase',
                val,
                await this.grossPrice,
            );
        },
    })
    readonly charge: Promise<decimal>;

    @decorators.decimalProperty<PurchaseRequisitionLine, 'netPrice'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.companyPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['grossPrice', 'quantity', { discountCharges: ['sign'] }, 'currency'],
        async defaultValue() {
            const grossPrice = await this.grossPrice;
            const currency = await this.currency;
            return grossPrice > 0 && currency
                ? xtremMasterData.functions.calculateNetPrice(
                      this.discountCharges,
                      grossPrice,
                      await this.quantity,
                      await currency.decimalDigits,
                  )
                : 0;
        },
        updatedValue: useDefaultValue,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.query<typeof PurchaseRequisitionLine, 'getFilteredList'>({
        isPublished: true,
        parameters: [
            { name: 'siteId', type: 'string', isMandatory: true },
            { name: 'supplierId', type: 'string', isMandatory: true },
            { name: 'currencyId', type: 'string', isMandatory: true },
        ],
        return: { type: 'array', item: { type: 'reference', node: () => PurchaseRequisitionLine } },
    })
    static async getFilteredList(
        context: Context,
        siteId: string,
        supplierId: string,
        currencyId: string,
    ): Promise<PurchaseRequisitionLine[]> {
        const currentSite = await context.read(xtremSystem.nodes.Site, { id: siteId });
        const be = await context.read(xtremMasterData.nodes.BusinessEntity, { id: supplierId });
        const currentSupplier = await context.read(xtremMasterData.nodes.Supplier, { businessEntity: be._id });
        const currentCurrency = await context.tryRead(xtremMasterData.nodes.Currency, { id: currencyId });

        await loggers.requisitionLine.debugAsync(
            async () =>
                `Method getFilteredList: currentSite: ${currentSite._id}-${await currentSite.name}/currentSupplier: ${
                    currentSupplier._id
                }-${await (
                    await currentSupplier.businessEntity
                ).name}/currentCurrency: ${currentCurrency?._id}-${await currentCurrency?.id}`,
        );

        const filter: NodeQueryFilter<xtremPurchasing.nodes.PurchaseRequisitionLine> = {
            status: { _in: ['pending', 'inProgress'] },
            lineOrderStatus: { _in: ['notOrdered', 'partiallyOrdered'] },
            approvalStatus: { _in: ['approved', 'confirmed'] },
            document: {
                status: { _in: ['pending', 'inProgress'] },
                orderStatus: { _in: ['notOrdered', 'partiallyOrdered'] },
            },
            site: { _in: [currentSite._id, null] },
            _or: [
                { supplier: { _in: [null, currentSupplier._id] } },
                { itemSupplier: { supplier: currentSupplier._id } },
                { itemSiteSupplier: { supplier: currentSupplier._id } },
            ],
            ...(currentCurrency ? { currency: { _in: [null, currentCurrency._id] } } : {}),
        };

        loggers.requisitionLine.debug(() => `Method getFilteredList: filter : ${JSON.stringify(filter)}`);

        const requisitionLines = await context
            .query(xtremPurchasing.nodes.PurchaseRequisitionLine, { filter })
            .toArray();

        const result = requisitionLines.map(line => line._id);

        loggers.receipt.debug(() => `Method getFilteredList: result = [${result.join(', ')}]`);

        return requisitionLines;
    }

    private async _isLinePropertyFrozen(propertyName: string): Promise<boolean> {
        const old = await this.$.old;
        const oldStatus = await (await old.document).status;
        const oldLineStatus = await old.status;
        const approvalStatus = await (await old.document).approvalStatus;
        loggers.requisitionLine.debug(
            () =>
                `Frozen : oldStatus: ${oldStatus}, oldLineStatus: ${oldLineStatus}, approvalStatus: ${approvalStatus}`,
        );

        return isPurchaseRequisitionLinePropertyDisabled(oldStatus, oldLineStatus, propertyName, approvalStatus);
    }

    private get _isConversionFactorFrozen(): Promise<boolean> {
        return (async () => {
            return (
                this.$.status === NodeStatus.modified &&
                (await (await (await this.$.old).item)?.id) === (await (await this.item)?.id) &&
                (await (await (await this.$.old).unit)?.id) === (await (await this.unit)?.id) &&
                (await (await (await this.$.old).stockUnit)?.id) === (await (await this.stockUnit)?.id)
            );
        })();
    }

    static async close(line: xtremPurchasing.nodes.PurchaseRequisitionLine): Promise<void> {
        if ((await line.status) !== 'closed' && (await line.approvalStatus) !== 'pendingApproval') {
            await line.$.set({ status: 'closed' });
        }
    }

    /**
     * Method that closes a purchase requisition line of a purchase requisition
     * @param context
     * @param purchaseRequisitionLine contains a unique purchaseRequisitionLine reference
     * @returns true or throws error
     */
    @decorators.mutation<typeof PurchaseRequisitionLine, 'closeLine'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseRequisitionLine',
                type: 'reference',
                node: () => xtremPurchasing.nodes.PurchaseRequisitionLine,
                isMandatory: true,
            },
        ],
        return: 'boolean',
    })
    static closeLine(
        context: Context,
        purchaseRequisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
    ): Promise<boolean> {
        return xtremPurchasing.functions.closeRequisitionLine(context, purchaseRequisitionLine);
    }

    /**
     * Method that applies the default supplier to the lines of a purchase requisition
     * @param context
     * @param PurchaseRequisitionLine
     * @returns true or throws error
     */
    @decorators.mutation<typeof PurchaseRequisitionLine, 'applyDefaultSupplier'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseRequisitionLine',
                type: 'reference',
                node: () => xtremPurchasing.nodes.PurchaseRequisitionLine,
                isMandatory: true,
                isWritable: true,
            },
            {
                name: 'supplier',
                type: 'reference',
                node: () => xtremMasterData.nodes.Supplier,
                isMandatory: true,
            },
            { name: 'quantity', type: 'decimal' },
            { name: 'grossPrice', type: 'decimal' },
            { name: 'netPrice', type: 'decimal' },
            { name: 'totalTaxExcludedAmount', type: 'decimal' },
            {
                name: 'priceOrigin',
                type: 'enum',
                dataType: () => xtremPurchasing.enums.priceOriginDataType,
                isMandatory: false,
            },
        ],
        return: 'boolean',
    })
    static async applyDefaultSupplier(
        _context: Context,
        purchaseRequisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
        supplier: xtremMasterData.nodes.Supplier,
        quantity: decimal,
        grossPrice: decimal,
        netPrice: decimal,
        totalTaxExcludedAmount: decimal,
        priceOrigin?: xtremPurchasing.enums.PriceOrigin,
    ): Promise<boolean> {
        await purchaseRequisitionLine.$.set({
            supplier,
            quantity,
            grossPrice,
            netPrice,
            totalTaxExcludedAmount,
            priceOrigin,
        });
        await purchaseRequisitionLine.$.save();
        return true;
    }
}
