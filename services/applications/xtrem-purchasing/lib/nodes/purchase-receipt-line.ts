import type { Collection, Context, decimal, integer, JsonType, NodeCreateData, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremPurchasing from '..';
import { controlEnd } from '../events/controlEnd/purchase-receipt-line';
import { saveBegin } from '../events/saveBegin/purchase-receipt-line';
import { loggers } from '../functions';
import { isPurchaseDocumentLinePropertyDisabled } from '../shared-functions/index';
// Hack to exclude defaultValue rules from upgrade
const excludeFromUpgrade = <T>(x: T) => x;

@decorators.subNode<PurchaseReceiptLine>({
    isPublished: true,
    canRead: true,
    canSearch: true,
    extends: () => xtremDistribution.nodes.BaseInboundReceiptDocumentLine,
    async createEnd() {
        await this.$.set({ workInProgress: {} });
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.sameSiteForHeaderAndLine(this, cx);
        await xtremPurchasing.events.controls.sameCompanyForSiteLineAndStockSiteLine(this, cx);
        await xtremPurchasing.events.controls.convertedQuantityInPurchaseUnitEqualquantityInStockUnit(this, cx);
        await xtremPurchasing.events.controls.lineItemIsFrozen(this, cx);
        await xtremPurchasing.events.controls.receivingSiteMustBelongToTheSameLegalCompany(this, cx);
        await xtremPurchasing.events.controls.itemMustHaveItemSiteAndMustBeStockManaged(this, cx);
        await xtremPurchasing.events.controls.itemMustHaveItemSiteAndNotManagedWarning(this, cx);
        await xtremPurchasing.events.controls.itemMustHaveItemSupplier(this, cx);
        await xtremPurchasing.events.controls.itemMustBeManagedForSupplierAndSite(this, cx);
        await controlEnd(this, cx);
    },
    async controlDelete(cx) {
        await xtremPurchasing.events.controls.checkLineStatusDeletion(this, cx);
    },
    async saveBegin() {
        await saveBegin(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class PurchaseReceiptLine
    extends xtremDistribution.nodes.BaseInboundReceiptDocumentLine
    implements
        xtremFinanceData.interfaces.FinanceOriginDocumentLine,
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    // returns the stock unit cost
    async getOrderCost(): Promise<decimal> {
        // retrieve orderCost value with more scale (not rounded according financial site currency)
        const document = await this.document;
        const rate = await xtremMasterData.functions.getRateOrReverseRate(
            this.$.context,
            await document.companyCurrency,
            await (
                await document.site
            ).financialCurrency,
            await document.date,
        );
        return rate.isRateFound
            ? ((await this.amountExcludingTaxInCompanyCurrency) * rate.rate) / (await this.quantityInStockUnit)
            : 0;
    }

    getValuedCost() {
        return this.valuedCost;
    }

    // eslint-disable-next-line class-methods-use-this
    getValuationParameters(): xtremStockData.interfaces.ValuationParameter {
        // change valuation is done like issues for the issue and the receipt
        return { valuationType: 'receipt' };
    }

    override getItem(): Promise<xtremMasterData.nodes.Item> {
        return this.item;
    }

    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'document'>({
        node: () => xtremPurchasing.nodes.PurchaseReceipt,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseReceipt>;

    @decorators.enumPropertyOverride<PurchaseReceiptLine, 'status'>({
        dependsOn: [
            'lineReturnStatus',
            'lineInvoiceStatus',
            'quantityInStockUnit',
            { purchaseInvoiceLines: ['invoicedQuantityInStockUnit'] },
            { purchaseReturnLines: ['returnedQuantityInStockUnit'] },
        ],
        defaultValue: 'draft',
        async updatedValue() {
            return xtremPurchasing.functions.PurchaseReceiptLib.computeReceiptLineStatus({
                status: await this.status,
                stockTransactionStatus: await this.stockTransactionStatus,
                lineInvoiceStatus: await this.lineInvoiceStatus,
                lineReturnStatus: await this.lineReturnStatus,
                remainingQuantityInStockUnit: await this.remainingQuantityInStockUnit,
            });
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumProperty<PurchaseReceiptLine, 'lineReturnStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseReceiptReturnStatusDataType,
        /**
         * Purchase order - status property :
         * - is defaulted to 'Draft' when record is created
         */
        dependsOn: ['quantityInStockUnit', 'returnedQuantityInStockUnit'],
        defaultValue() {
            return 'notReturned';
        },
        lookupAccess: true,
        async updatedValue() {
            if ((await this.returnedQuantityInStockUnit) === 0) {
                return 'notReturned';
            }
            if ((await this.returnedQuantityInStockUnit) < (await this.quantityInStockUnit)) {
                return 'partiallyReturned';
            }
            return 'returned';
        },
    })
    readonly lineReturnStatus: Promise<xtremPurchasing.enums.PurchaseReceiptReturnStatus>;

    @decorators.enumProperty<PurchaseReceiptLine, 'lineInvoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseReceiptInvoiceStatusDataType,
        /**
         * Purchase order - status property :
         * - is defaulted to 'Draft' when record is created
         */
        dependsOn: ['quantityInStockUnit', 'invoicedQuantityInStockUnit'],
        lookupAccess: true,
        defaultValue() {
            return 'notInvoiced';
        },
        async updatedValue() {
            if ((await this.invoicedQuantityInStockUnit) === 0) {
                return 'notInvoiced';
            }
            if ((await this.invoicedQuantityInStockUnit) < (await this.quantityInStockUnit)) {
                return 'partiallyInvoiced';
            }
            return 'invoiced';
        },
    })
    readonly lineInvoiceStatus: Promise<xtremPurchasing.enums.PurchaseReceiptInvoiceStatus>;

    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'item'>({
        filters: {
            control: {
                isBought: true,
                type: { _ne: 'landedCost' },
                async itemSites() {
                    return { _atLeast: 1, site: await this.stockSite };
                },
            },
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.landedCostItemValidation(
                val,
                (await this.document).$.factory.name,
                cx,
            );
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalPropertyOverride<PurchaseReceiptLine, 'grossPrice'>({
        dependsOn: [{ document: ['site', 'businessRelation', 'currency', 'documentDate'] }, 'item', 'quantity', 'unit'],
        async defaultValue() {
            const purchaseDocument = await this.document;
            const grossPrice = await xtremMasterData.functions.getPurchasePrice(this.$.context, {
                site: await purchaseDocument.site,
                supplier: await purchaseDocument.businessRelation,
                currency: await purchaseDocument.currency,
                item: await this.item,
                quantity: await this.quantity,
                unit: await this.unit,
                date: await purchaseDocument.documentDate,
                convertUnit: false,
            });
            return grossPrice >= 0 ? grossPrice : 0;
        },

        isFrozen() {
            return this._isLinePropertyFrozen('grossPrice');
        },
    })
    override readonly grossPrice: Promise<decimal>;

    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'stockUnit'>({
        async isFrozen() {
            return (await (await this.item).stockUnit)._id !== (await this.stockUnit)._id;
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<PurchaseReceiptLine, 'quantityInStockUnit'>({
        dependsOn: [
            'item',
            'unit',
            'stockUnit',
            'quantity',
            'unitToStockUnitConversionFactor',
            'purchaseOrderLine',
            'document',
        ],
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.quantity,
                await this.unit,
                await this.stockUnit,
                true,
                !!(await this.purchaseOrderLine),
                await this._isConversionFactorFrozen,
                this.$.status,
                await this.unitToStockUnitConversionFactor,
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        async updatedValue() {
            await loggers.receipt.debugAsync(
                async () => `updatedValue
            quantity = ${await this.quantity} unit = ${JSON.stringify(
                await this.unit,
            )} stockUnit = ${await this.stockUnit}
            purchaseOrderLine = ${!!(await this.purchaseOrderLine)} isFrozen = ${await this._isConversionFactorFrozen}
            status = ${this.$.status} factor = ${await this.unitToStockUnitConversionFactor}
            `,
            );
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.quantity,
                await this.unit,
                await this.stockUnit,
                true,
                !!(await this.purchaseOrderLine),
                await this._isConversionFactorFrozen,
                this.$.status,
                await this.unitToStockUnitConversionFactor,
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        isFrozen() {
            return this._isLinePropertyFrozen('quantityInStockUnit');
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    protected get _isConversionFactorFrozen(): Promise<boolean> {
        return (async () => {
            return (
                this.$.status === NodeStatus.modified &&
                (await (await (await this.$.old).item)?.id) === (await (await this.item)?.id) &&
                (await (await (await this.$.old).unit)?.id) === (await (await this.unit)?.id) &&
                (await (await (await this.$.old).stockUnit)?.id) === (await (await this.stockUnit)?.id)
            );
        })();
    }

    @decorators.decimalPropertyOverride<PurchaseReceiptLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', 'unit', 'stockUnit', 'purchaseOrderLine', { document: ['businessRelation'] }],
        async defaultValue() {
            return xtremMasterData.functions.getConvertCoefficient(
                await this.unit,
                await this.stockUnit,
                'purchase',
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        async updatedValue() {
            if (
                (this.$.status === NodeStatus.added && (await this.purchaseOrderLine)) ||
                !(await this._isConversionFactorFrozen)
            ) {
                return xtremMasterData.functions.getConvertCoefficient(
                    await this.unit,
                    await this.stockUnit,
                    'purchase',
                    await this.item,
                    await (
                        await this.document
                    ).businessRelation,
                );
            }
            return this.unitToStockUnitConversionFactor;
        },
        isFrozen() {
            return this._isConversionFactorFrozen;
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'currency'>({
        isFrozen() {
            return this._isLinePropertyFrozen('currency');
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    // @decorators.decimalPropertyOverride<PurchaseReceiptLine, 'amountExcludingTax'>({
    //     isFrozen() {
    //         return this._isLinePropertyFrozen('amountExcludingTax');
    //     },
    // })
    // override readonly amountExcludingTax: Promise<decimal>;

    // TODO:
    // @decorators.decimalPropertyOverride<PurchaseReceiptLine, 'amountExcludingTaxInCompanyCurrency'>({
    //     isFrozen() {
    //         return this._isLinePropertyFrozen('amountExcludingTaxInCompanyCurrency');
    //     },
    // })
    // override readonly amountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.jsonProperty<PurchaseReceiptLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['site', 'taxes'],
        lookupAccess: true,
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremPurchasing.nodes.PurchaseReceiptLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.enumPropertyOverride<PurchaseReceiptLine, 'priceOrigin'>({
        dependsOn: ['status'],
        isFrozen() {
            return this._isLinePropertyFrozen('priceOrigin');
        },
    })
    override readonly priceOrigin: Promise<xtremPurchasing.enums.PriceOrigin | null>;

    @decorators.integerProperty<PurchaseReceiptLine, 'transientPurchaseOrderLine'>({
        isPublished: true,
        isTransientInput: true,
    })
    readonly transientPurchaseOrderLine: Promise<integer>;

    @decorators.referenceProperty<PurchaseReceiptLine, 'purchaseOrderLine'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine,
        isVital: true,
        reverseReference: 'purchaseReceiptLine',
        isNullable: true,
        lookupAccess: true,
    })
    readonly purchaseOrderLine: Reference<xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine | null>;

    @decorators.collectionProperty<PurchaseReceiptLine, 'purchaseReturnLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine,
        reverseReference: 'purchaseReceiptLine',
        lookupAccess: true,
    })
    readonly purchaseReturnLines: Collection<xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine>;

    @decorators.collectionProperty<PurchaseReceiptLine, 'purchaseInvoiceLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine,
        reverseReference: 'purchaseReceiptLine',
        lookupAccess: true,
    })
    readonly purchaseInvoiceLines: Collection<xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine>;

    @decorators.integerProperty<PurchaseReceiptLine, 'numberOfPurchaseInvoiceLines'>({
        isPublished: true,
        dependsOn: ['purchaseInvoiceLines'],
        getValue() {
            return this.purchaseInvoiceLines.length;
        },
    })
    readonly numberOfPurchaseInvoiceLines: Promise<integer>;

    @decorators.referenceProperty<PurchaseReceiptLine, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'purchaseReceiptLine',
        node: () => xtremPurchasing.nodes.WorkInProgressPurchaseReceiptLine,
    })
    readonly workInProgress: Reference<xtremPurchasing.nodes.WorkInProgressPurchaseReceiptLine | null>;

    // the site property on line level is needed for the accounting interface
    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'site'>({
        dependsOn: [{ document: ['stockSite'] }],
        isFrozen() {
            return this._isLinePropertyFrozen('site');
        },
        filters: {
            control: {
                isInventory: true,
            },
        },
        async control(cx, site) {
            if (site) return;
            const docSite = await (await this.document).site;
            const legalCompanyName = await (await docSite.legalCompany).name;
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_receipt__line__no_inventory_site',
                'No stock site for the company: {{legalCompanyName}}.',
                { legalCompanyName },
            );
        },
        async defaultValue() {
            return (await this.document).stockSite;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'stockSite'>({
        isFrozen() {
            return this._isLinePropertyFrozen('stockSite');
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    /** Not editable stockSiteLinkedAddress */
    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'stockSiteLinkedAddress'>({
        defaultValue() {
            return this.getStockSiteAddressPrimaryActiveAddressDefaultValue();
        },
        updatedValue: useDefaultValue,
        isFrozen() {
            return this._isLinePropertyFrozen('stockSiteLinkedAddress');
        },
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.stockSite).businessEntity)._id;
                },
            },
        },
    })
    override readonly stockSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.decimalPropertyOverride<PurchaseReceiptLine, 'quantity'>({
        dependsOn: ['unit'],
        isFrozen() {
            return this._isLinePropertyFrozen('quantity');
        },
    })
    override readonly quantity: Promise<decimal>;

    @decorators.referencePropertyOverride<PurchaseReceiptLine, 'unit'>({
        filters: {
            control: {
                async type() {
                    const item = await this.item;
                    if ((await item?.type) === 'good' && (await item?.isStockManaged)) {
                        return { _nin: ['time', 'temperature'] };
                    }
                    return { _nin: ['temperature'] };
                },
                async id() {
                    return (await (
                        await this.item
                    )?.isStockManaged)
                        ? {
                              _in: await xtremMasterData.functions.listOfPurchaseUnitsByItemSupplier(
                                  await this.stockUnit,
                                  await this.item,
                                  await (
                                      await this.document
                                  )?.businessRelation,
                              ),
                          }
                        : { _regex: `.`, _options: 'i' };
                },
            },
        },
        dependsOn: ['item', 'stockUnit', { document: ['businessRelation'] }],
        async defaultValue() {
            return xtremMasterData.nodes.UnitOfMeasure.getPurchaseUnit(
                this.$.context,
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        async updatedValue() {
            if (!(await this.unit) && (await this.item) && (await (await this.document).businessRelation)) {
                const itemSupplier =
                    (await this.$.context
                        .query(xtremMasterData.nodes.ItemSupplier, {
                            filter: {
                                item: await this.item,
                                supplier: await (await this.document).businessRelation,
                            },
                        })
                        .at(0)) || null;
                return (itemSupplier && (await itemSupplier.purchaseUnitOfMeasure)) || this.unit;
            }
            return this.unit;
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.unitToStockUnitConversionFactorValidation(this, val, cx);
            await xtremPurchasing.events.controls.unitToPreviousUnitConversionFactorValidation(this, val, cx);
        },
        isFrozen() {
            return this._isLinePropertyFrozen('unit');
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    async getStockSiteAddressPrimaryActiveAddressDefaultValue(): Promise<xtremMasterData.nodes.BusinessEntityAddress | null> {
        return (
            (await (
                await (
                    await this.stockSite
                ).businessEntity
            )?.addresses.takeOne(
                async (address: xtremMasterData.nodes.BusinessEntityAddress) =>
                    (await address.isActive) && address.isPrimary,
            )) ?? null
        );
    }

    // the financialSite property on line level is needed for the accounting interface (GRNI inquiry)
    @decorators.referenceProperty<PurchaseReceiptLine, 'financialSite'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        async getValue() {
            return (await (await (await this.document).stockSite).financialSite) ?? (await this.document).stockSite;
        },
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    // the billBySupplier property on line level is needed for the accounting interface (GRNI inquiry)
    @decorators.referenceProperty<PurchaseReceiptLine, 'billBySupplier'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        async getValue() {
            return (await (await this.document).billBySupplier) ?? (await this.document).businessRelation;
        },
    })
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'returnedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseReturnLines: ['returnedQuantity'] }],
        lookupAccess: true,
        async getValue() {
            return Math.max(await this.purchaseReturnLines.sum(line => line.returnedQuantity), 0);
        },
    })
    readonly returnedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'invoicedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseInvoiceLines: ['invoicedQuantity'] }],
        lookupAccess: true,
        async getValue() {
            return Math.max(await this.purchaseInvoiceLines.sum(line => line.invoicedQuantity), 0);
        },
    })
    readonly invoicedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'remainingQuantityToInvoice'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: ['quantity', 'invoicedQuantity'],
        lookupAccess: true,
        async getValue() {
            return Math.max((await this.quantity) - (await this.invoicedQuantity), 0);
        },
    })
    readonly remainingQuantityToInvoice: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'remainingReturnQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: ['quantity', 'returnedQuantity'],
        lookupAccess: true,
        async getValue() {
            return Math.max((await this.quantity) - (await this.returnedQuantity), 0);
        },
    })
    readonly remainingReturnQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'returnedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: [{ purchaseReturnLines: ['returnedQuantityInStockUnit'] }],
        lookupAccess: true,
        async getValue() {
            return Math.max(await this.purchaseReturnLines.sum(line => line.returnedQuantityInStockUnit), 0);
        },
    })
    readonly returnedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'invoicedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: [{ purchaseInvoiceLines: ['invoicedQuantityInStockUnit'] }],
        lookupAccess: true,
        async getValue() {
            return Math.max(await this.purchaseInvoiceLines.sum(line => line.invoicedQuantityInStockUnit), 0);
        },
    })
    readonly invoicedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'remainingQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: ['quantityInStockUnit', 'returnedQuantityInStockUnit'],
        lookupAccess: true,
        async getValue() {
            return Math.max(
                (await this.quantityInStockUnit) -
                    (await this.returnedQuantityInStockUnit) -
                    (await this.invoicedQuantityInStockUnit),
                0,
            );
        },
    })
    readonly remainingQuantityInStockUnit: Promise<decimal>;

    @decorators.enumPropertyOverride<PurchaseReceiptLine, 'origin'>({
        dependsOn: ['purchaseOrderLine'],
        async defaultValue() {
            if ((await this.purchaseOrderLine) != null) {
                return 'purchaseOrder';
            }
            return 'direct';
        },
    })
    override readonly origin: Promise<xtremPurchasing.enums.PurchaseDocumentLineOrigin>;

    // TODO: remove this and un-comment in base node once https://jira.sage.com/browse/XT-22853 is implemented
    @decorators.jsonPropertyOverride<PurchaseReceiptLine, 'storedDimensions'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).businessRelation,
                item: await this.item,
            });
        },
    })
    override readonly storedDimensions: Promise<object | null>;

    @decorators.jsonPropertyOverride<PurchaseReceiptLine, 'storedAttributes'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).businessRelation,
                item: await this.item,
            });
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonPropertyOverride<PurchaseReceiptLine, 'computedAttributes'>({
        dependsOn: ['document', 'item'],
        async computeValue() {
            const document = await this.document;
            return xtremPurchasing.functions.computeAttributes(this.$.context, {
                site: await document.site,
                item: await this.item,
                supplier: await document.billBySupplier,
                line: this,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'orderCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['netPrice', { document: ['site', 'currency', 'date'] }],
        async defaultValue() {
            const document = await this.document;
            return xtremMasterData.functions.convertCurrency(
                this.$.context,
                await this.netPrice,
                await document.currency,
                await (
                    await document.site
                ).financialCurrency,
                await document.date,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'valuedCost'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['item', { document: ['site'] }],
        async defaultValue() {
            const itemSite = await this.$.context.tryRead(xtremMasterData.nodes.ItemSite, {
                item: await this.item,
                site: await (await this.document).site,
            });
            return (await itemSite?.currentCost) || 0;
        },
    })
    readonly valuedCost: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReceiptLine, 'returnAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: [{ document: ['businessRelation'] }],
        node: () => xtremMasterData.nodes.Address,
        async isFrozen() {
            return !((await (await this.document).returnStatus) === 'notReturned');
        },
        async defaultValue() {
            return (
                excludeFromUpgrade(
                    await (
                        await (
                            await (
                                await this.document
                            ).businessRelation
                        ).returnToAddress
                    )?.address,
                ) || null
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly returnAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.booleanProperty<PurchaseReceiptLine, 'completed'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        dependsOn: ['status'],
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
    })
    readonly completed: Promise<boolean>;

    @decorators.collectionProperty<PurchaseReceiptLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        dependsOn: ['orderCost', 'valuedCost', 'amountExcludingTaxInCompanyCurrency', 'quantityInStockUnit'],
        node: () => xtremStockData.nodes.StockReceiptDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockReceiptDetail>;

    @decorators.collectionProperty<PurchaseReceiptLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.enumProperty<PurchaseReceiptLine, 'stockDetailStatus'>({
        isPublished: true,
        isStored: true,
        dataType: () => xtremStockData.enums.stockDetailStatusDataType,
        dependsOn: ['item', 'stockDetails', 'quantityInStockUnit'],
        async defaultValue() {
            return xtremStockData.functions.stockDetailLib.getStockDetailStatus({
                line: this,
                quantityExpected: await this.quantityInStockUnit,
                checkSerialNumbers: false,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly stockDetailStatus: Promise<xtremStockData.enums.StockDetailStatus>;

    @decorators.jsonProperty<PurchaseReceiptLine, 'jsonStockDetails'>({
        isPublished: true,
        dependsOn: ['stockDetails', 'amountExcludingTaxInCompanyCurrency'],
        async setValue(details) {
            if (details && details.length > 0) {
                await this.$.set({
                    stockDetails: xtremStockData.functions.stockDetailLib.parseDetails(
                        details,
                    ) as NodeCreateData<xtremStockData.nodes.StockReceiptDetail>[],
                });
            }
        },
        async computeValue() {
            if ((await this.stockDetails.length) > 0) {
                return this.stockDetails
                    .map(detail =>
                        xtremStockData.functions.stockDetailLib.filterStockDetailProperties(
                            detail,
                            xtremStockData.nodes.StockReceiptDetail,
                            {
                                onlyIds: true,
                            },
                        ),
                    )
                    .toArray();
            }
            // forcing typing to accept an empty object
            return {} as any;
        },
    })
    readonly jsonStockDetails: Promise<Array<JsonType<NodeCreateData<xtremStockData.nodes.StockReceiptDetail>>>>;

    @decorators.booleanPropertyOverride<PurchaseReceiptLine, 'canHaveLandedCostLine'>({
        getValue: () => {
            return true;
        },
    })
    override readonly canHaveLandedCostLine: Promise<boolean>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'actualLandedCostInCompanyCurrency'>({
        isPublished: true,
        dependsOn: ['landedCostLines'],
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        getValue() {
            return xtremLandedCost.nodes.LandedCostLine.getTotalActualCostAmountInCompanyCurrency(this);
        },
    })
    actualLandedCostInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'discount'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        lookupAccess: true,
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            // TODO: We should update only if value changed in update mode or if value is > 0 if added
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'decrease',
                val,
                await this.grossPrice,
            );
        },
    })
    readonly discount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLine, 'charge'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        lookupAccess: true,
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            // TODO: We should  update only if value changed in update mode or if value is > 0 if added
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'increase',
                val,
                await this.grossPrice,
            );
        },
    })
    readonly charge: Promise<decimal>;

    @decorators.enumProperty<PurchaseReceiptLine, 'lineStatus'>({
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseDocumentStatusDataType,
        getValue() {
            return this.status;
        },
    })
    readonly lineStatus: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    override async getOrderDocumentLine() {
        return (await this.purchaseOrderLine)?.purchaseOrderLine;
    }

    /**
     * Calculates purchase receipt line taxes. Mutation can be used by the frontend
     * during creation or update to show taxes to the user before he creates or updates order
     * @param context
     * @param site universal name because the same parameter name will be used in purchases.
     *             This mutation name will be passed as a parameter into the tax-panel page
     * @param businessPartner universal name
     * @param item
     * @param currency
     * @param amountExcludingTax
     * @param quantity
     * @param taxes an array of objects with optional parameter taxReference.
     *              If the array is left empty, mutation tries to automatically determine tax determination.
     *
     * @returns object
     */
    @decorators.mutation<typeof PurchaseReceiptLine, 'calculateLineTaxes'>({
        // TODO change mutation to query when enhancement XT-19221 done.
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: xtremTax.functions.getCalculateLineTaxesParameterPropertyDefinition<
                    typeof PurchaseReceiptLine
                >(xtremMasterData.nodes.Supplier),
            },
        ],
        return: xtremTax.functions.getCalculateLineTaxesReturnDefinition<typeof PurchaseReceiptLine>(),
    })
    static async calculateLineTaxes(
        context: Context,
        data: xtremTax.interfaces.CalculateLineTaxesParameters<xtremMasterData.nodes.Supplier>,
    ): Promise<xtremTax.interfaces.CalculateLineTaxesReturn> {
        return xtremTax.functions.calculateLineTaxes(
            await context.create(
                xtremPurchasing.nodes.PurchaseReceipt,
                { site: data.site, businessRelation: data.businessPartner },
                { isOnlyForDefaultValues: true },
            ),
            await xtremPurchasing.classes.PurchaseReceiptTaxCalculator.create(context, data.site, 'draft', true),
            data,
        );
    }

    protected async _isLinePropertyFrozen(propertyName: string): Promise<boolean> {
        const oldLine = await this.$.old;
        const oldDocument = await oldLine.document;
        return isPurchaseDocumentLinePropertyDisabled(await oldDocument.status, await oldLine.status, propertyName);
    }
}
