import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../index';

@decorators.node<PurchaseReturnLineToPurchaseCreditMemoLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { purchaseReturnLine: 1, purchaseCreditMemoLine: 1 },
            isUnique: true,
        },
    ],

    async controlEnd(cx) {
        /**
         * Some controls when creating a purchase credit memo based on a purchase return line :
         * Header level controls :
         * - same supplier/stock site/site/currency
         * Line level controls :
         * - same item/purchase unit
         */
        const returnLine = await this.purchaseReturnLine;
        const creditMemoLine = await this.purchaseCreditMemoLine;
        const returnLineDocument = await returnLine.document;
        const creditMemoLineDocument = await creditMemoLine.document;
        if (
            (await returnLine.item)._id !== (await creditMemoLine.item)._id ||
            (await returnLine.unit)._id !== (await creditMemoLine.unit)._id ||
            (await returnLineDocument.businessRelation)._id !== (await creditMemoLineDocument.billBySupplier)._id ||
            ((await returnLineDocument.returnSite)._id !== (await creditMemoLineDocument.site)._id &&
                (await (await returnLineDocument.returnSite).financialSite)?._id !==
                    (await creditMemoLineDocument.site)._id) ||
            (await returnLineDocument.currency)._id !== (await creditMemoLineDocument.currency)._id ||
            (await returnLine.unitToStockUnitConversionFactor) !==
                (await creditMemoLine.unitToStockUnitConversionFactor)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_return_to_purchase_credit_memo__same_properties_return_to_credit_memo',
                'The purchase credit memo must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase return.',
            );
        }
    },
})
export class PurchaseReturnLineToPurchaseCreditMemoLine extends Node {
    @decorators.referenceProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'purchaseReturnLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLine,
    })
    readonly purchaseReturnLine: Reference<xtremPurchasing.nodes.PurchaseReturnLine>;

    @decorators.referenceProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'purchaseCreditMemoLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseCreditMemoLine,
    })
    readonly purchaseCreditMemoLine: Reference<xtremPurchasing.nodes.PurchaseCreditMemoLine>;

    @decorators.referenceProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: [{ purchaseReturnLine: ['unit'] }],
        async getValue() {
            return (await this.purchaseReturnLine).unit;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'creditMemoQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseCreditMemoLine: ['quantity'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).quantity;
        },
    })
    readonly creditMemoQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: [{ purchaseReturnLine: ['stockUnit'] }],
        async getValue() {
            return (await this.purchaseReturnLine).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'creditMemoQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseCreditMemoLine: ['quantityInStockUnit'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).quantityInStockUnit;
        },
    })
    readonly creditMemoQuantityInStockUnit: Promise<decimal>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async getValue() {
            return (await this.purchaseCreditMemoLine).unitToStockUnitConversionFactor;
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseCreditMemoLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'creditMemoUnitPrice'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.companyPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseCreditMemoLine: ['grossPrice'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).grossPrice;
        },
    })
    readonly creditMemoUnitPrice: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'currency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ purchaseCreditMemoLine: ['currency'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseCreditMemoLine, 'creditMemoAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseCreditMemoLine: ['amountExcludingTax', 'currency'] }],
        async getValue() {
            return (await this.purchaseCreditMemoLine).amountExcludingTax;
        },
    })
    readonly creditMemoAmount: Promise<decimal>;
}
