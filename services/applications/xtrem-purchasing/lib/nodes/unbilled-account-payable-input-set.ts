import type { Collection, Context, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, Logger, Node, SystemError, asyncArray, date, datetime, decorators } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { LocalizedError, type InitialNotificationAction } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '..';
import {
    generateUnbilledAccountPayableLine,
    getFilterPurchaseReceiptLines,
} from '../functions/unbilled-account-payable-input-set-lib';

const logger = Logger.getLogger(__filename, 'unbilled-account-payable-input-set');

@decorators.node<UnbilledAccountPayableInputSet>({
    isClearedByReset: true,
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canCreate: true,
    canSearch: true,
    canUpdate: true,
    canDelete: true,
    indexes: [
        {
            orderBy: { user: +1 },
            isUnique: true,
            isNaturalKey: true,
        },
    ],
})
export class UnbilledAccountPayableInputSet extends Node {
    @decorators.referenceProperty<UnbilledAccountPayableInputSet, 'user'>({
        isStored: true,
        isPublished: true,
        isFrozen: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            const userInfo = await this.$.context.user;
            if (!userInfo) {
                throw new SystemError('current context does not have a valid user');
            }
            return this.$.context.read(xtremSystem.nodes.User, { email: userInfo.email });
        },
    })
    readonly user: Reference<xtremSystem.nodes.User>;

    @decorators.referenceProperty<UnbilledAccountPayableInputSet, 'company'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        provides: ['company'],
        node: () => xtremSystem.nodes.Company,
    })
    readonly company: Reference<xtremSystem.nodes.Company | null>;

    @decorators.referenceArrayProperty<UnbilledAccountPayableInputSet, 'sites'>({
        isPublished: true,
        isStored: true,
        onDelete: 'restrict',
        node: () => xtremSystem.nodes.Site,
        defaultValue: [],
        isNullable: true,
    })
    readonly sites: Promise<xtremSystem.nodes.Site[] | null>;

    @decorators.referenceProperty<UnbilledAccountPayableInputSet, 'fromSupplier'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly fromSupplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.referenceProperty<UnbilledAccountPayableInputSet, 'toSupplier'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly toSupplier: Reference<xtremMasterData.nodes.Supplier | null>;

    @decorators.dateProperty<UnbilledAccountPayableInputSet, 'asOfDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        defaultValue: () => date.today().addMonths(-1).endOfMonth(),
    })
    readonly asOfDate: Promise<date>;

    @decorators.enumProperty<UnbilledAccountPayableInputSet, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.unbilledAccountPayableStatusDataType,
        defaultValue: 'draft',
    })
    readonly status: Promise<xtremPurchasing.enums.UnbilledAccountPayableStatus>;

    @decorators.datetimeProperty<UnbilledAccountPayableInputSet, 'executionDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly executionDate: Promise<datetime> | null;

    @decorators.collectionProperty<UnbilledAccountPayableInputSet, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'inputSet',
        node: () => xtremPurchasing.nodes.UnbilledAccountPayableResultLine,
    })
    readonly lines: Collection<xtremPurchasing.nodes.UnbilledAccountPayableResultLine>;

    @decorators.asyncMutation<typeof UnbilledAccountPayableInputSet, 'unbilledAccountPayableInquiry'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [{ name: 'userId', type: 'string' }],
        return: 'boolean',
    })
    static async unbilledAccountPayableInquiry(context: Context, userId: string): Promise<boolean> {
        let message = context.localize(
            '@sage/xtrem-purchasing/nodes__unbilled_account_payable-input-set__success_message',
            'Success',
        );

        await context.runInWritableContext(async writableContext => {
            const inputSet: UnbilledAccountPayableInputSet = await writableContext.read(
                UnbilledAccountPayableInputSet,
                { user: userId },
                { forUpdate: true },
            );

            // Remove all lines, set status and last execution date
            await inputSet.$.set({ lines: [], status: 'inProgress', executionDate: datetime.now() });
            await inputSet.$.save();
        });

        const result = await context.runInWritableContext(async writableContext => {
            const inputSet = await writableContext.read(
                UnbilledAccountPayableInputSet,
                { user: userId },
                { forUpdate: true },
            );

            // Do calculation.
            let newStatus: xtremPurchasing.enums.UnbilledAccountPayableStatus;
            try {
                await UnbilledAccountPayableInputSet.executeInquiry(writableContext, inputSet);
                if (await inputSet.$.control()) {
                    newStatus = 'completed';
                } else {
                    newStatus = 'error';
                    message = 'error';
                }
            } catch (error) {
                logger.error(() => JSON.stringify(error));
                if (!(error instanceof LocalizedError)) throw error;

                newStatus = 'error';
                message = error.getMessageAndDiagnosesText(writableContext.diagnoses);
            }

            // Signal completion.
            await inputSet.$.set({ status: newStatus, executionDate: datetime.now() });
            await inputSet.$.save();

            return { sysId: inputSet._id, status: newStatus, message };
        });

        // Notify user.
        await UnbilledAccountPayableInputSet.sendNotification(context, {
            sysId: result.sysId.toString(),
            success: result.status !== 'error',
            message,
        });

        if (result.status === 'error') {
            throw new BusinessRuleError(result.message);
        }
        return true;
    }

    static async executeInquiry(context: Context, inputSet: UnbilledAccountPayableInputSet) {
        const siteSysIds = (await inputSet.sites)?.map(site => site._id) || [];

        const searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountPayableSearch = {
            company: (await inputSet.company) ? await (await inputSet.company)?.id : undefined,
            stockSites: siteSysIds.length ? siteSysIds : undefined,
            fromSupplier: await (await (await inputSet.fromSupplier)?.businessEntity)?.id,
            toSupplier: await (await (await inputSet.toSupplier)?.businessEntity)?.id,
            asOfDate: await inputSet.asOfDate,
        };

        const results = await UnbilledAccountPayableInputSet.unbilledAccountPayable(context, searchCriteria);

        await asyncArray(results).forEach(async line => {
            await inputSet.lines.append(line);
        });
    }

    static unbilledAccountPayable(
        context: Context,
        searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountPayableSearch,
    ): Promise<xtremFinanceData.interfaces.FinanceUnbilledAccountPayable[]> {
        const receiptLines = context.query(xtremPurchasing.nodes.PurchaseReceiptLine, {
            filter: getFilterPurchaseReceiptLines(searchCriteria),
        });
        // loop over the array of found purchase receipt lines and calculate additional values
        const results = receiptLines.map(receiptLine =>
            generateUnbilledAccountPayableLine({
                context,
                receiptLine,
                searchCriteria,
            }),
        );

        // Return only records with an actual unbilled quantity.
        return results.filter(line => line.invoiceReceivableQuantity > 0).toArray();
    }

    static async sendNotification(context: Context, param: { sysId: string; success: boolean; message: string }) {
        const actions: InitialNotificationAction[] = [
            {
                link: context.batch.notificationStateLink,
                title: 'History',
                icon: 'link',
                style: 'tertiary',
            },
        ];

        if (param.sysId) {
            actions.push({
                link: `@sage/xtrem-purchasing/UnbilledAccountPayableInquiry/${param.sysId}`,
                title: 'Results',
                icon: 'link',
                style: 'tertiary',
            });
        }

        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-purchasing/nodes__unbilled_account_payable-input-set__success_notification_title',
                'Unbilled accounts payable calculation complete',
            ),
            description: param.message,
            icon: param.success ? 'tick' : 'error',
            level: param.success ? 'success' : 'error',
            shouldDisplayToast: true,
            actions,
        });
    }
}
