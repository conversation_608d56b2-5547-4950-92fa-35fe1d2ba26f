import type { Collection, Context, Diagnose, Reference, decimal, integer } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    LogicError,
    NodeStatus,
    ValidationSeverity,
    date,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremPurchasing from '../index';
import { isPurchaseOrderLinePropertyDisabled } from '../shared-functions/index';
import { BasePurchaseDocumentLine } from './base-document-nodes/index';

// Hack to exclude defaultValue rules from upgrade
const excludeFromUpgrade = <T>(x: T) => x;

@decorators.subNode<PurchaseOrderLine>({
    extends: () => BasePurchaseDocumentLine,
    isPublished: true,
    canUpdate: true, // TODO: I leave it for now as it's called from PurchaseOrderSuggestion page (this page has to be refactored)
    async createEnd() {
        await this.$.set({ workInProgress: {} });

        if (
            (await (await this.document).approvalStatus) === 'confirmed' &&
            !(await (
                await this.document
            ).isApprovalManaged) &&
            (await this.status) === 'draft'
        ) {
            await this.$.set({ status: 'pending' });
        }
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.OrderLine.receivedQuantityGreaterThanTheOrderLineQuantity(this, cx);
        await xtremPurchasing.events.controls.OrderLine.receivingSiteValidation(this, cx);
    },
    async saveBegin() {
        await xtremPurchasing.functions.PurchaseOrderLib.updateLineNotesOnCreation(this);
    },
    async saveEnd() {
        if (
            this.$.status === NodeStatus.modified &&
            (await (
                await this.document
            ).isPurchaseOrderSuggestion) &&
            (await this.status) === 'draft' &&
            (await (
                await (
                    await this.$.old
                ).document
            ).isPurchaseOrderSuggestion) &&
            (await this.isPurchaseOrderSuggestion) !== (await (await this.$.old).isPurchaseOrderSuggestion)
        ) {
            const purchaseOrder = await this.$.context.read(
                xtremPurchasing.nodes.PurchaseOrder,
                { _id: (await this.document)._id },
                { forUpdate: true },
            );
            const purchaseOrderLine = (await purchaseOrder.lines.find(line => line._id === this._id)) || null;
            const orderLineDocument = await purchaseOrderLine?.document;
            if (purchaseOrderLine) {
                await purchaseOrderLine.$.set({ isPurchaseOrderSuggestion: false });
                await orderLineDocument?.$.set({
                    status: 'draft',
                    isPurchaseOrderSuggestion: false,
                    number: await (
                        await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
                            sequenceNumber: orderLineDocument.sequenceNumberId,
                            currentDate: await orderLineDocument.orderDate,
                            site: await orderLineDocument.site,
                            company: await (await orderLineDocument.site)?.legalCompany,
                        })
                    ).allocate(),
                });
                await purchaseOrder.$.save({ deferred: true });
            }
        }
        await this.checkAndUpdateQuantityOnAssignment();
    },

    async controlDelete(cx) {
        await xtremPurchasing.events.controlDelete.purchaseOrderLine.controlDeleteInvoiceLink(cx, this);
    },

    async deleteBegin() {
        await this.checkAndDeleteAssignment();
    },
})
export class PurchaseOrderLine
    extends BasePurchaseDocumentLine
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
{
    // it's forbidden to close the line by changing its status without using the dedicated function
    // because after saving, we need to trigger stock posting (in another writable context)

    @decorators.booleanProperty<PurchaseOrderLine, 'isUsingFunctionToClose'>({})
    readonly isUsingFunctionToClose: Promise<boolean>;

    @decorators.referencePropertyOverride<PurchaseOrderLine, 'document'>({
        node: () => xtremPurchasing.nodes.PurchaseOrder,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseOrder>;

    @decorators.enumPropertyOverride<PurchaseOrderLine, 'status'>({
        dependsOn: [
            'lineReceiptStatus',
            { document: ['approvalStatus', 'isApprovalManaged'] },
            'quantityInStockUnit',
            'purchaseReceiptLines',
            { purchaseReceiptLines: ['receivedQuantityInStockUnit'] },
        ],
        updatedValue() {
            return xtremPurchasing.events.controls.OrderLine.updateLineStatus(this);
        },
        async control(cx, val) {
            if (await this.isClosing(val)) {
                if (!(await this.isUsingFunctionToClose)) {
                    throw new LogicError(
                        'It is forbidden to change the status of an order line to close it. Use the dedicated function.',
                    );
                }
                const old = await this.$.old;

                const diagnoses = await xtremPurchasing.functions.PurchaseOrderLib.controlCloseOrderLine(
                    this.$.context,
                    this,
                    {
                        previousLineStatus: await old.status,
                        previousLineReceiptStatus: await old.lineReceiptStatus,
                        previousLineInvoiceStatus: await old.lineInvoiceStatus,
                        newApprovalStatus: await (await this.document).approvalStatus,
                    },
                );
                diagnoses.forEach(diagnose => {
                    cx.addDiagnose(diagnose.severity, diagnose.message);
                });
            }
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.booleanProperty<PurchaseOrderLine, 'isPurchaseOrderSuggestion'>({
        isStored: true,
        isPublished: true,
        defaultValue: false,
        isFrozen() {
            return this._isLinePropertyFrozen('isPurchaseOrderSuggestion');
        },
    })
    readonly isPurchaseOrderSuggestion: Promise<boolean>;

    @decorators.enumProperty<PurchaseOrderLine, 'lineReceiptStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseOrderReceiptStatusDataType,
        /**
         * Purchase order - status property :
         * - is defaulted to 'Draft' when record is created
         */
        dependsOn: ['quantityInStockUnit', 'receivedQuantityInStockUnit'],
        defaultValue: 'notReceived',
        duplicatedValue: useDefaultValue,
        lookupAccess: true,
        updatedValue() {
            return xtremPurchasing.functions.OrderLine.getLineReceiptStatus(this);
        },
    })
    readonly lineReceiptStatus: Promise<xtremPurchasing.enums.PurchaseOrderReceiptStatus>;

    @decorators.referencePropertyOverride<PurchaseOrderLine, 'site'>({
        async defaultValue() {
            return (await this.document).site;
        },
        filters: {
            control: {
                isPurchase: true,
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    /** editable stock site address  */
    @decorators.referenceProperty<PurchaseOrderLine, 'stockSiteAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['stockSiteLinkedAddress'],
        async defaultValue() {
            return excludeFromUpgrade(await (await this.stockSiteLinkedAddress).address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly stockSiteAddress: Reference<xtremMasterData.nodes.Address | null>;

    /** editable stock site address  */
    @decorators.referenceProperty<PurchaseOrderLine, 'stockSiteContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['stockSiteLinkedAddress'],
        async defaultValue() {
            return (
                excludeFromUpgrade(await (await (await this.stockSiteLinkedAddress).primaryContact)?.contact) || null
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly stockSiteContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referencePropertyOverride<PurchaseOrderLine, 'item'>({
        filters: {
            control: {
                isBought: true,
                type: { _ne: 'landedCost' },
                async itemSites() {
                    return { _atLeast: 1, site: await this.stockSite };
                },
            },
        },
        async control(cx, item) {
            if ((await this.status) !== 'closed') {
                await xtremMasterData.functions.controls.item.inactiveItemControl(cx, item);
            }
            await xtremPurchasing.events.controls.landedCostItemValidation(
                item,
                (await this.document).$.factory.name,
                cx,
            );
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.decimalPropertyOverride<PurchaseOrderLine, 'quantityInStockUnit'>({
        dependsOn: [
            'unit',
            'stockUnit',
            'quantity',
            'unitToStockUnitConversionFactor',
            'item',
            'purchaseRequisitionLines',
            { document: ['businessRelation'] },
        ],
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.quantity,
                await this.unit,
                await this.stockUnit,
                true,
                !!(await this.purchaseRequisitionLines.length),
                await this._isConversionFactorFrozen,
                this.$.status,
                await this.unitToStockUnitConversionFactor,
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseOrderLine, 'unitToStockUnitConversionFactor'>({
        async defaultValue() {
            return xtremMasterData.functions.getConvertCoefficient(
                await this.unit,
                await this.stockUnit,
                'purchase',
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        async updatedValue() {
            // TODO: XT-9221 begin
            // 1/ Check if this is ok. Evaluation of status = added on updatedValue? Also, we should have purchaseRequisitionLines? Or should it be the opposite?
            // XT-9221 end
            if (
                (this.$.status === NodeStatus.added && (await this.purchaseRequisitionLines.length)) ||
                !(await this._isConversionFactorFrozen)
            ) {
                return xtremMasterData.functions.getConvertCoefficient(
                    await this.unit,
                    await this.stockUnit,
                    'purchase',
                    await this.item,
                    await (
                        await this.document
                    ).businessRelation,
                );
            }
            return this.unitToStockUnitConversionFactor;
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.dateProperty<PurchaseOrderLine, 'expectedReceiptDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['document', { document: ['orderDate'] }, 'item'],
        async defaultValue() {
            return (await this.document).orderDate;
        },
        async duplicatedValue() {
            const pLeadTime = await PurchaseOrderLine.getPurchaseLeadTime(
                this.$.context,
                await this.item,
                await (
                    await this.document
                ).site,
                await (
                    await this.document
                ).businessRelation,
            );
            return date.today().addDays(pLeadTime);
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.OrderLine.expectedReceiptDateValidation(this, val, cx);
        },
        isFrozen() {
            return this._isLinePropertyFrozen('expectedReceiptDate');
        },
    })
    readonly expectedReceiptDate: Promise<date>;

    @decorators.stringProperty<PurchaseOrderLine, 'changeRequestedDescription'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
        duplicatedValue: '',
    })
    readonly changeRequestedDescription: Promise<string>;

    @decorators.datePropertyOverride<PurchaseOrderLine, 'taxDate'>({
        dependsOn: [{ document: ['orderDate'] }],
        async defaultValue() {
            return (await this.document).orderDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly taxDate: Promise<date>;

    @decorators.jsonProperty<PurchaseOrderLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['site', 'taxes'],
        lookupAccess: true,
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremPurchasing.nodes.PurchaseOrderLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.collectionProperty<PurchaseOrderLine, 'purchaseRequisitionLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseRequisitionLineToPurchaseOrderLine,
        isVital: true,
        reverseReference: 'purchaseOrderLine',
    })
    readonly purchaseRequisitionLines: Collection<xtremPurchasing.nodes.PurchaseRequisitionLineToPurchaseOrderLine>;

    @decorators.collectionProperty<PurchaseOrderLine, 'purchaseReceiptLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine,
        reverseReference: 'purchaseOrderLine',
        lookupAccess: true,
        prefetch(record) {
            return { purchaseOrderLine: record._id };
        },
    })
    readonly purchaseReceiptLines: Collection<xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine>;

    @decorators.integerProperty<PurchaseOrderLine, 'numberOfPurchaseReceiptLines'>({
        isPublished: true,
        dependsOn: ['purchaseReceiptLines'],
        getValue() {
            return this.purchaseReceiptLines.length;
        },
    })
    readonly numberOfPurchaseReceiptLines: Promise<integer>;

    @decorators.referenceProperty<PurchaseOrderLine, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'purchaseOrderLine',
        node: () => xtremPurchasing.nodes.WorkInProgressPurchaseOrderLine,
    })
    readonly workInProgress: Reference<xtremPurchasing.nodes.WorkInProgressPurchaseOrderLine | null>;

    @decorators.decimalProperty<PurchaseOrderLine, 'receivedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseReceiptLines: ['receivedQuantity'] }],
        getValue() {
            return this.purchaseReceiptLines.sum(line => line.receivedQuantity);
            // TODO: Sort out rounding
        },
        lookupAccess: true,
    })
    readonly receivedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'quantityReceivedInProgress'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['purchaseReceiptLines', { purchaseReceiptLines: ['receivedQuantity'] }],
        getValue() {
            return this.purchaseReceiptLines
                .where(async line => (await (await line.purchaseReceiptLine).stockTransactionStatus) !== 'completed')
                .sum(line => line.receivedQuantityInStockUnit);
        },
    })
    readonly quantityReceivedInProgress: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'quantityToReceive'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: ['purchaseReceiptLines', 'quantity', 'receivedQuantity'],
        lookupAccess: true,
        async getValue() {
            const quantityToReceive: decimal = (await this.quantity) - (await this.receivedQuantity);
            if (quantityToReceive <= 0) {
                return 0;
            }
            const completedLine = await this.purchaseReceiptLines.some(
                async line => (await line.purchaseReceiptLine).completed,
            );
            return completedLine ? 0 : quantityToReceive;
        },
    })
    readonly quantityToReceive: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'receivedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['purchaseReceiptLines', { purchaseReceiptLines: ['receivedQuantityInStockUnit'] }],
        lookupAccess: true,
        getValue() {
            return this.purchaseReceiptLines.sum(line => line.receivedQuantityInStockUnit);
            // TODO: Sort out rounding
        },
    })
    readonly receivedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'postedQuantityReceivedInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['purchaseReceiptLines', { purchaseReceiptLines: ['receivedQuantityInStockUnit'] }],

        getValue() {
            return this.purchaseReceiptLines.sum(async line =>
                !['draft', 'error'].includes(await (await line.purchaseReceiptLine).status)
                    ? line.receivedQuantityInStockUnit
                    : 0,
            );
        },
    })
    readonly postedQuantityReceivedInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'remainingQuantityToProcessForLandedCost'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityInStockUnit', 'postedQuantityReceivedInStockUnit'],
        async getValue() {
            return Math.max(0, (await this.quantityInStockUnit) - (await this.postedQuantityReceivedInStockUnit));
        },
    })
    readonly remainingQuantityToProcessForLandedCost: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'receivedQuantityProgress'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: ['purchaseReceiptLines', { purchaseReceiptLines: ['receivedQuantityInStockUnit'] }],
        async getValue() {
            return (await this.quantityInStockUnit) !== 0
                ? ((await this.receivedQuantityInStockUnit) * 100) / (await this.quantityInStockUnit)
                : 0;
        },
    })
    readonly receivedQuantityProgress: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'quantityToReceiveInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityInStockUnit', 'receivedQuantityInStockUnit'],
        lookupAccess: true,
        async getValue() {
            return Math.max((await this.quantityInStockUnit) - (await this.receivedQuantityInStockUnit), 0);
        },
    })
    readonly quantityToReceiveInStockUnit: Promise<decimal>;

    @decorators.enumPropertyOverride<PurchaseOrderLine, 'origin'>({
        dependsOn: ['purchaseRequisitionLines'],
        async defaultValue() {
            if ((await this.purchaseRequisitionLines.length) > 0) {
                return 'purchaseRequisition';
            }
            return 'direct';
        },
    })
    override readonly origin: Promise<xtremPurchasing.enums.PurchaseDocumentLineOrigin>;

    // TODO: remove this and un-comment in base node once https://jira.sage.com/browse/XT-22853 is implemented
    @decorators.jsonPropertyOverride<PurchaseOrderLine, 'storedDimensions'>({
        async isFrozen() {
            return (
                (await this._isLinePropertyFrozen('dimensions')) && !(await (await this.document).forceUpdateForFinance)
            );
        },
        dependsOn: ['site', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            const item = await this.item;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).businessRelation,
                item,
            });
        },
    })
    override readonly storedDimensions: Promise<object | null>;

    @decorators.jsonPropertyOverride<PurchaseOrderLine, 'storedAttributes'>({
        async isFrozen() {
            return (
                (await this._isLinePropertyFrozen('dimensions')) && !(await (await this.document).forceUpdateForFinance)
            );
        },
        dependsOn: ['site', 'item'],
        async defaultValue() {
            const item = await this.item;
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).businessRelation,
                item,
            });
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonPropertyOverride<PurchaseOrderLine, 'computedAttributes'>({
        dependsOn: ['document', 'item'],
        async computeValue() {
            return xtremPurchasing.functions.computeAttributes(this.$.context, {
                site: await (await this.document).site,
                item: await this.item,
                supplier: await (await this.document).businessRelation,
                line: this,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.collectionProperty<PurchaseOrderLine, 'purchaseInvoiceLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine,
        reverseReference: 'purchaseOrderLine',
    })
    readonly purchaseInvoiceLines: Collection<xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine>;

    @decorators.integerProperty<PurchaseOrderLine, 'numberOfPurchaseInvoiceLines'>({
        isPublished: true,
        dependsOn: ['purchaseInvoiceLines'],
        getValue() {
            return this.purchaseInvoiceLines.length;
        },
    })
    readonly numberOfPurchaseInvoiceLines: Promise<integer>;

    @decorators.decimalProperty<PurchaseOrderLine, 'invoicedQuantity'>({
        isPublished: true,
        dependsOn: ['purchaseInvoiceLines', { purchaseInvoiceLines: ['invoicedQuantity'] }],
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        getValue() {
            return this.purchaseInvoiceLines.sum(line => line.invoicedQuantity);
        },
    })
    invoicedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'remainingQuantityToInvoice'>({
        isPublished: true,
        dependsOn: ['quantity', 'invoicedQuantity'],
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        async getValue() {
            return (await this.quantity) - (await this.invoicedQuantity);
        },
    })
    remainingQuantityToInvoice: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'remainingQuantityToInvoiceOnOrder'>({
        isPublished: true,
        dependsOn: [
            'remainingQuantityToInvoice',
            'purchaseReceiptLines',
            { purchaseReceiptLines: ['purchaseReceiptLine'] },
        ],
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,

        async getValue() {
            return (
                (await this.remainingQuantityToInvoice) -
                (await this.purchaseReceiptLines.sum(
                    async line => (await line.purchaseReceiptLine).remainingQuantityToInvoice,
                ))
            );
        },
    })
    remainingQuantityToInvoiceOnOrder: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'invoicedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: [{ purchaseInvoiceLines: ['invoicedQuantityInStockUnit'] }],
        async getValue() {
            return Math.max(await this.purchaseInvoiceLines.sum(line => line.invoicedQuantityInStockUnit), 0);
        },
    })
    readonly invoicedQuantityInStockUnit: Promise<decimal>;

    @decorators.enumProperty<PurchaseOrderLine, 'lineInvoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseOrderInvoiceStatusDataType,
        dependsOn: ['quantityInStockUnit', 'invoicedQuantityInStockUnit'],
        defaultValue() {
            return 'notInvoiced';
        },
        duplicatedValue: useDefaultValue,
        updatedValue() {
            return xtremPurchasing.functions.OrderLine.getLineInvoiceStatus(this);
        },
    })
    readonly lineInvoiceStatus: Promise<xtremPurchasing.enums.PurchaseOrderInvoiceStatus>;

    @decorators.collectionProperty<PurchaseOrderLine, 'assignments'>({
        isPublished: true,
        reverseReference: 'supplyDocumentLine',
        node: () => xtremStockData.nodes.OrderAssignment,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
    })
    readonly assignments: Collection<xtremStockData.nodes.OrderAssignment>;

    @decorators.referenceProperty<PurchaseOrderLine, 'uDemandOrderLine'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BaseDocumentLine,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dependsOn: ['assignments'],
        async getValue() {
            const demand = await this.assignments.takeOne(
                async assignment =>
                    (await assignment.demandType) === 'salesOrderLine' &&
                    (await assignment.supplyType) === 'purchaseOrderLine',
            );
            return (await demand?.demandDocumentLine) ?? null;
        },
    })
    readonly uDemandOrderLine: Reference<xtremMasterData.nodes.BaseDocumentLine> | null;

    @decorators.stringProperty<PurchaseOrderLine, 'uDemandOrderLineLink'>({
        isPublished: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dependsOn: ['uDemandOrderLine'],

        async computeValue() {
            return (await this.uDemandOrderLine)?.documentNumber || '';
        },
    })
    readonly uDemandOrderLineLink: Promise<string>;

    @decorators.decimalProperty<PurchaseOrderLine, 'uDemandOrderQuantity'>({
        isPublished: true,
        dependsOn: ['assignments'],
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return this.assignments.sum(assignment => assignment.quantityInStockUnit);
        },
    })
    readonly uDemandOrderQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'uSupplyOrderQuantity'>({
        isPublished: true,
        dependsOn: ['assignments'],
        serviceOptions: () => [xtremMasterData.serviceOptions.orderToOrderOption],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,

        getValue() {
            return this.assignments.sum(
                async assignment => (await (await assignment.supplyWorkInProgress)?.expectedQuantity) ?? 0,
            );
        },
    })
    readonly uSupplyOrderQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'amountForLandedCostAllocation'>({
        isPublished: true,
        dependsOn: ['amountExcludingTaxInCompanyCurrency', 'quantityToReceiveInStockUnit', 'quantityInStockUnit'],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        async getValue() {
            return (
                ((await this.amountExcludingTaxInCompanyCurrency) * (await this.quantityToReceiveInStockUnit)) /
                (await this.quantityInStockUnit)
            );
        },
    })
    readonly amountForLandedCostAllocation: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'quantityInStockUnitForLandedCostAllocation'>({
        dependsOn: ['quantityToReceiveInStockUnit'],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return this.quantityToReceiveInStockUnit;
        },
    })
    readonly quantityInStockUnitForLandedCostAllocation: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'remainingAmountToReceiveExcludingTax'>({
        dependsOn: ['quantityToReceive', 'netPrice'],
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            return (await this.quantityToReceive) * (await this.netPrice);
        },
    })
    readonly remainingAmountToReceiveExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLine, 'remainingAmountToReceiveExcludingTaxInCompanyCurrency'>({
        dependsOn: ['quantityToReceive', 'netPrice', { document: ['companyFxRate'] }],
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        async getValue() {
            return (await this.quantityToReceive) * (await this.netPrice) * (await (await this.document).companyFxRate);
        },
    })
    readonly remainingAmountToReceiveExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.booleanPropertyOverride<BasePurchaseDocumentLine, 'canHaveLandedCostLine'>({
        getValue: () => true,
    })
    override readonly canHaveLandedCostLine: Promise<boolean>;

    @decorators.decimalProperty<PurchaseOrderLine, 'actualLandedCostInCompanyCurrency'>({
        isPublished: true,
        dependsOn: ['landedCostLines'],
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        getValue() {
            return xtremLandedCost.nodes.LandedCostLine.getTotalActualCostAmountInCompanyCurrency(this);
        },
    })
    actualLandedCostInCompanyCurrency: Promise<decimal>;

    protected override async _isLinePropertyFrozen(propertyName: string): Promise<boolean> {
        return isPurchaseOrderLinePropertyDisabled(
            await (
                await (
                    await this.$.old
                ).document
            ).status,
            await (
                await this.$.old
            ).status,
            propertyName,
            await (
                await (
                    await this.$.old
                ).document
            ).approvalStatus,
        );
    }

    /**
     * Indicates if the status is modified to be closed
     * @param newLineStatus
     * @returns true if the status has been modified to 'closed'
     */
    async isClosing(newLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus) {
        if (newLineStatus === 'closed' && this.$.status !== NodeStatus.added) {
            return (await (await this.$.old).status) !== newLineStatus;
        }
        return false;
    }

    async checkAndUpdateQuantityOnAssignment(): Promise<void> {
        if (
            this.$.status === NodeStatus.modified &&
            (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption))
        ) {
            const purchaseOrderAssignment = await xtremStockData.nodes.OrderAssignment.getSupplyAssignment(
                this.$.context,
                'salesOrderLine',
                this,
            );
            if (purchaseOrderAssignment?._id) {
                const orderAssignment = await this.$.context.read(
                    xtremStockData.nodes.OrderAssignment,
                    { _id: purchaseOrderAssignment._id },
                    { forUpdate: true },
                );
                if ((await this.quantityInStockUnit) < (await orderAssignment.quantityInStockUnit)) {
                    await orderAssignment.$.set({ quantityInStockUnit: await this.quantityInStockUnit });
                    await orderAssignment.$.save();
                }
            }
        }
    }

    async checkAndDeleteAssignment(): Promise<void> {
        if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption)) {
            const result = await xtremStockData.nodes.OrderAssignment.getSupplyAssignment(
                this.$.context,
                'salesOrderLine',
                this,
            );
            if (result?._id) {
                await this.$.context.delete(xtremStockData.nodes.OrderAssignment, { _id: result._id });
            }
        }
    }

    /**
     * Method that control that a purchase order line can be closed
     * @param context
     * @param purchaseOrderLine contains a unique purchaseOrderLine reference
     * @returns {success: true if the line can be closed, false if not
     *           messages: an error message if success=false, a list of warning messages if success=true, an empty array if there is no error and warning  }
     */
    @decorators.query<typeof PurchaseOrderLine, 'controlCloseLine'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseOrderLine',
                type: 'reference',
                node: () => xtremPurchasing.nodes.PurchaseOrderLine,
                isMandatory: true,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async controlCloseLine(
        context: Context,
        purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
    ): Promise<boolean> {
        const diagnoses: Diagnose[] = await xtremPurchasing.functions.PurchaseOrderLib.controlCloseOrderLine(
            context,
            purchaseOrderLine,
            {
                previousLineStatus: await purchaseOrderLine.status,
                previousLineReceiptStatus: await purchaseOrderLine.lineReceiptStatus,
                previousLineInvoiceStatus: await purchaseOrderLine.lineInvoiceStatus,
                newApprovalStatus: await (await purchaseOrderLine.document).approvalStatus,
            },
        );

        diagnoses.forEach(diagnose => {
            if (diagnose.severity === ValidationSeverity.error) {
                throw new BusinessRuleError(diagnose.message);
            }
        });
        return true;
    }

    /**
     *  return leadTime
     *  1) ItemSiteSupplier 2) ItemSite 3) Item
     * @param context
     * @param item
     * @param site
     * @param supplier
     * @returns leadTime as a number
     */
    @decorators.query<typeof PurchaseOrderLine, 'getPurchaseLeadTime'>({
        isPublished: true,
        parameters: [
            { name: 'item', type: 'reference', node: () => xtremMasterData.nodes.Item, isMandatory: true },
            { name: 'site', type: 'reference', node: () => xtremSystem.nodes.Site, isMandatory: true },
            { name: 'supplier', type: 'reference', node: () => xtremMasterData.nodes.Supplier, isMandatory: true },
        ],
        return: {
            type: 'integer',
        },
    })
    static async getPurchaseLeadTime(
        context: Context,
        item: xtremMasterData.nodes.Item,
        site: xtremSystem.nodes.Site,
        supplier: xtremMasterData.nodes.Supplier,
    ): Promise<integer> {
        const itemSiteSupplier = await context
            .query(xtremMasterData.nodes.ItemSiteSupplier, {
                filter: {
                    itemSite: { site: site._id, item: item._id },
                    supplier: { _id: supplier._id },
                },
            })
            .toArray();

        let purchaseLeadTime = itemSiteSupplier.length && (await itemSiteSupplier[0].purchaseLeadTime);
        if (purchaseLeadTime) {
            return purchaseLeadTime;
        }

        const itemSupplier = await context
            .query(xtremMasterData.nodes.ItemSupplier, {
                filter: {
                    item: { _id: item._id },
                    supplier: { _id: supplier._id },
                },
            })
            .toArray();

        purchaseLeadTime = itemSupplier.length && (await itemSupplier[0].purchaseLeadTime);
        if (purchaseLeadTime) {
            return purchaseLeadTime;
        }
        const itemSite = await context
            .query(xtremMasterData.nodes.ItemSite, {
                filter: {
                    item: { _id: item._id },
                    site: { _id: site._id },
                },
            })
            .toArray();

        purchaseLeadTime = itemSite.length && (await itemSite[0].purchaseLeadTime);
        if (purchaseLeadTime) {
            return purchaseLeadTime;
        }

        return 0;
    }

    /**
     * Calculates purchase order line taxes. Mutation can be used by the frontend
     * during creation or update to show taxes to the user before he creates or updates order
     * @param context
     * @param site universal name because the same parameter name will be used in purchases.
     *             This mutation name will be passed as a parameter into the tax-panel page
     * @param businessPartner universal name
     * @param item
     * @param currency
     * @param amountExcludingTax
     * @param quantity
     * @param taxes an array of objects with optional parameter taxReference.
     *              If the array is left empty, mutation tries to automatically determine tax determination.
     *
     * @returns object
     */
    @decorators.mutation<typeof PurchaseOrderLine, 'calculateLineTaxes'>({
        // TODO change mutation to query when enhancement XT-19221 is done.
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: xtremTax.functions.getCalculateLineTaxesParameterPropertyDefinition<
                    typeof PurchaseOrderLine
                >(xtremMasterData.nodes.Supplier),
            },
        ],
        return: xtremTax.functions.getCalculateLineTaxesReturnDefinition<typeof PurchaseOrderLine>(),
    })
    static async calculateLineTaxes(
        context: Context,
        data: xtremTax.interfaces.CalculateLineTaxesParameters<xtremMasterData.nodes.Supplier>,
    ): Promise<xtremTax.interfaces.CalculateLineTaxesReturn> {
        return xtremTax.functions.calculateLineTaxes(
            await context.create(
                xtremPurchasing.nodes.PurchaseOrder,
                { site: data.site, stockSite: data.site, businessRelation: data.businessPartner },
                { isOnlyForDefaultValues: true },
            ),
            await xtremPurchasing.classes.PurchaseOrderTaxCalculator.create(context, data.site, 'draft', true),
            data,
        );
    }

    @decorators.query<typeof PurchaseOrderLine, 'controlDelete'>({
        isPublished: true,

        parameters: [
            {
                name: 'purchaseOrderLine',
                type: 'reference',
                node: () => xtremPurchasing.nodes.PurchaseOrderLine,
                isMandatory: true,
            },
        ],
        return: {
            type: 'array',
            name: 'invoiceNumbers',
            item: {
                type: 'string',
            },
        },
    })
    static controlDelete(
        context: Context,
        purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
    ): Promise<string[]> {
        return xtremPurchasing.functions.PurchaseOrderLib.controlDeleteInvoiceLink(context, purchaseOrderLine);
    }
}
