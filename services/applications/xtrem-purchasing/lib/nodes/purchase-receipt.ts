import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { AsyncArrayReader, Collection, Context, date, decimal, integer, Reference } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, Decimal, decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '..';
import { createEnd } from '../events/createEnd/purchase-receipt';
import { loggers } from '../functions';
import { isDateLaterThanToday } from '../functions/common';
import { checkTaxCalculationStatusForPosting, createPurchaseReturns } from '../functions/purchase-receipt-lib';
import { isPurchaseReceiptLinePropertyDisabled } from '../shared-functions/index';

// Hack to exclude defaultValue rules from upgrade
const excludeFromUpgrade = <T>(x: T) => x;

@decorators.subNode<PurchaseReceipt>({
    extends: () => xtremDistribution.nodes.BaseInboundReceiptDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    hasAttachments: true,
    async createEnd() {
        await createEnd(this);
    },
    async controlBegin(cx) {
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);
    },
    async prepareBegin() {
        await (
            await xtremPurchasing.classes.PurchaseReceiptTaxCalculator.create(
                this.$.context,
                await this.site,
                await this.status,
                true,
            )
        ).calculateTaxData(this);
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
        await xtremPurchasing.functions.PurchaseReceiptLib.updateHeaderNotesOnCreation(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class PurchaseReceipt
    extends xtremDistribution.nodes.BaseInboundReceiptDocument
    implements
        xtremFinanceData.interfaces.FinanceOriginDocumentHeader,
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    override getEffectiveDate() {
        return this.date;
    }

    override getStockSite() {
        return this.stockSite;
    }

    @decorators.enumPropertyOverride<PurchaseReceipt, 'status'>({
        // The instance was updated : the new status is calculated according to all status properties
        dependsOn: ['returnStatus', 'invoiceStatus', { lines: ['status'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseReceiptLib.computeReceiptStatusFromLinesStatuses(this);
            }
            return this.status;
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumPropertyOverride<PurchaseReceipt, 'displayStatus'>({
        dependsOn: [
            { lines: ['stockTransactionStatus'] },
            'returnStatus',
            'status',
            'invoiceStatus',
            'stockTransactionStatus',
            'taxCalculationStatus',
        ],
        async updatedValue() {
            return xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
                await this.status,
                await this.returnStatus,
                await this.invoiceStatus,
                await this.stockTransactionStatus,
                await this.taxCalculationStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremPurchasing.enums.PurchaseReceiptDisplayStatus>;

    @decorators.enumPropertyOverride<PurchaseReceipt, 'invoiceStatus'>({
        dependsOn: [{ lines: ['lineInvoiceStatus'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseReceiptLib.computeReceiptInvoiceStatusFromLinesInvoiceStatuses(
                    this,
                );
            }
            return this.invoiceStatus;
        },
    })
    override readonly invoiceStatus: Promise<xtremPurchasing.enums.PurchaseReceiptInvoiceStatus>;

    @decorators.enumProperty<PurchaseReceipt, 'returnStatus'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremPurchasing.enums.purchaseReceiptReturnStatusDataType,
        defaultValue() {
            return 'notReturned';
        },
        dependsOn: [{ lines: ['lineReturnStatus'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseReceiptLib.computeReceiptReturnStatusFromLinesReturnStatuses(
                    this,
                );
            }
            return this.returnStatus;
        },
    })
    readonly returnStatus: Promise<xtremPurchasing.enums.PurchaseReceiptReturnStatus>;

    // @decorators.enumProperty<PurchaseReceipt, 'stockTransactionStatus'>({
    //     isPublished: true,
    //     dependsOn: [{ lines: ['stockTransactionStatus'] }],
    //     lookupAccess: true,
    //     dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    //     computeValue() {
    //         return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
    //     },
    // })
    // readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    // Node architecture re-work : different business logic then the abstract node : isInventory
    @decorators.referencePropertyOverride<PurchaseReceipt, 'site'>({
        filters: { control: { isInventory: true } },
        async control(cx, val) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt__only_inventory_sites_allowed',
                    'The current site must be a stock site.',
                )
                .if(await val.isInventory)
                .is.false();
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseReceipt, 'stockSite'>({
        // override nullable: true,
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseReceipt, 'businessRelation'>({
        async control(cx, supplier) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.site,
                await supplier.businessEntity,
            );
        },
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<PurchaseReceipt, 'billBySupplier'>({
        dependsOn: ['businessRelation'],
        defaultValue() {
            return this.businessRelation;
        },
    })
    override readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<PurchaseReceipt, 'companyCurrency'>({})
    override readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.datePropertyOverride<PurchaseReceipt, 'fxRateDate'>({
        dependsOn: ['date'],
        defaultValue() {
            return this.date;
        },
    })
    override readonly fxRateDate: Promise<date>;

    @decorators.datePropertyOverride<PurchaseReceipt, 'date'>({
        updatedValue: useDefaultValue,
        async control(cx, val) {
            if (
                isDateLaterThanToday(
                    this.$.status,
                    val,
                    this.$.status === NodeStatus.modified ? await (await this.$.old).date : undefined,
                )
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/pages__purchase_receipt__receipt_date_cannot_be_future',
                    'The receipt date cannot be later than today.',
                );
            }
        },
    })
    override readonly date: Promise<date>;

    @decorators.dateProperty<PurchaseReceipt, 'receiptDate'>({
        isPublished: true,
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    readonly receiptDate: Promise<date>;

    @decorators.stringProperty<PurchaseReceipt, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.decimalPropertyOverride<PurchaseReceipt, 'totalAmountExcludingTax'>({
        dependsOn: ['currency', { lines: ['quantity', 'grossPrice', 'amountExcludingTax'] }],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTax);
            // TODO: Sort out rounding
            // return xtremPurchasing.functions.amountPrecisionRound(
            //     this.lines.reduce((amount, line) => amount + line.totalAmountExcludingTax, 0),
            //     this.currency.decimalDigits,
            // );
        },
        updatedValue: useDefaultValue,
    })
    override readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseReceipt, 'totalAmountExcludingTaxInCompanyCurrency'>({
        dependsOn: [{ lines: ['amountExcludingTaxInCompanyCurrency'] }], // ,'status', 'receiptStatus'],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency);
        },
        updatedValue: useDefaultValue,
    })
    override readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceipt, 'totalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        // @decorators.decimalPropertyOverride<PurchaseOrder, 'totalAmountIncludingTaxInCompanyCurrency'>({
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await xtremPurchasing.functions.BaseDocument.getCurrencyDecimalDigits(this),
            );
        },
    })
    readonly totalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.collectionPropertyOverride<PurchaseReceipt, 'lines'>({
        node: () => xtremPurchasing.nodes.PurchaseReceiptLine,
    })
    override readonly lines: Collection<xtremPurchasing.nodes.PurchaseReceiptLine>;

    override readonly linesWithStockTransactionStatusCollectionName = 'lines';

    override get landedCostAssignableLines() {
        return this.lines;
    }

    @decorators.referenceProperty<PurchaseReceipt, 'returnAddress'>({
        lookupAccess: true,
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['businessRelation'],
        node: () => xtremMasterData.nodes.Address,
        async isFrozen() {
            return (await this.returnStatus) !== 'notReturned';
        },
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.businessRelation).returnToAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly returnAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.collectionProperty<PurchaseReceipt, 'purchaseReturns'>({
        lookupAccess: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine,
        getFilter() {
            return { purchaseReceiptLine: { document: this } };
        },
    })
    readonly purchaseReturns: Collection<xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine>;

    @decorators.referenceProperty<PurchaseReceipt, 'carrier'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly carrier: Reference<xtremMasterData.nodes.Supplier | null>;

    // @decorators.referenceProperty<PurchaseReceipt, 'supplierAddress'>({
    //     isPublished: true,
    //     isStored: true,
    //     isMutable: true,
    //     isNullable: true,
    //     dependsOn: ['businessRelation'],
    //     node: () => xtremMasterData.nodes.Address,
    //     async isFrozen() {
    //         return (await this.status) !== 'draft';
    //     },
    //     async defaultValue() {
    //         return excludeFromUpgrade(await (await (await this.businessRelation).primaryAddress)?.address) || null;
    //     },
    //     updatedValue: useDefaultValue,
    //     lookupAccess: true,
    // })
    // readonly supplierAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<PurchaseReceipt, 'receivingAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['site'],
        node: () => xtremMasterData.nodes.Address,
        async isFrozen() {
            return (await this.status) !== 'draft';
        },
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.site).primaryAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly receivingAddress: Reference<xtremMasterData.nodes.Address | null>;

    // property needed for the accounting interface
    @decorators.datePropertyOverride<PurchaseReceipt, 'documentDate'>({
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.booleanProperty<PurchaseReceipt, 'isStockDetailRequired'>({
        isPublished: true,
        getValue() {
            return this.lines.some(async line => (await line.stockDetailStatus) === 'required');
        },
        lookupAccess: true,
    })
    readonly isStockDetailRequired: Promise<boolean>;

    // The usage of a computeValue instead of a query allows to use directly "this.lines"
    // instead of having to read the specific document first => This property can be copy/paste as is.
    @decorators.jsonProperty<PurchaseReceipt, 'jsonAggregateLandedCostTypes'>({
        isPublished: true,
        dependsOn: [{ lines: ['landedCostLines'] }],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        computeValue() {
            return xtremLandedCost.functions.landedCostLineLib.getLandedCostsPerType(this.lines);
        },
        lookupAccess: true,
    })
    readonly jsonAggregateLandedCostTypes: Promise<xtremLandedCost.interfaces.JsonAggregateLandedCostTypes>;

    // @decorators.collectionProperty<PurchaseReceipt, 'taxes'>({
    //     isVital: true,
    //     isPublished: true,
    //     reverseReference: 'document',
    //     node: () => xtremDistribution.nodes.BaseInboundDocumentTax,
    //     dependsOn: ['currency', 'status'],
    //     async isFrozen() {
    //         return (await this.status) === 'closed';
    //     },
    // })
    // readonly taxes: Collection<xtremDistribution.nodes.BaseInboundDocumentTax>;

    @decorators.referencePropertyOverride<PurchaseReceipt, 'paymentTerm'>({
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation)?.paymentTerm;
        },
        filters: { control: { businessEntityType: { _ne: 'customer' } } },
    })
    override readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    private async changeReceiptStatusToPending(): Promise<void> {
        await (this as PurchaseReceipt).$.set({ status: 'pending', displayStatus: 'postingInProgress' });
        await this.lines.forEach(line => line.$.set({ status: 'pending' }));
        await this.$.save();
    }

    /**
     * Method that creates purchase invoice for a given purchase receipt
     * All the lines have to have an item
     * @param context
     * @param purchaseReceiptNumber
     */
    @decorators.mutation<typeof PurchaseReceipt, 'createPurchaseInvoice'>({
        isPublished: true,
        parameters: [{ name: 'document', type: 'reference', node: () => PurchaseReceipt, isMandatory: true }],
        return: { type: 'array', item: { type: 'reference', node: () => xtremPurchasing.nodes.PurchaseInvoice } },
    })
    static async createPurchaseInvoice(
        context: Context,
        document: PurchaseReceipt,
    ): Promise<xtremPurchasing.nodes.PurchaseInvoice[]> {
        if (!document) {
            loggers.receipt.error(() => 'createPurchaseInvoice: document is null');
            return [];
        }

        if (['closed'].includes(await document.status) || ['invoiced'].includes(await document.invoiceStatus)) {
            loggers.receipt.error(() => 'createPurchaseInvoice: document is closed or already invoiced');
            return [];
        }

        // check stock transaction is completed
        if ((await document.stockTransactionStatus) !== 'completed') {
            loggers.receipt.error(() => 'createPurchaseInvoice: stock transaction is not completed');
            return [];
        }

        // All lines have to have an item
        if (await document.lines.map(line => line.item).some(item => !item)) {
            loggers.receipt.error(() => 'createPurchaseInvoice: all lines have to have an item');
            return [];
        }
        // Group lines per potential supplier and site criteria
        const purchaseInvoiceData =
            await xtremPurchasing.functions.PurchaseReceiptLib.initPurchaseInvoiceCreateData(document);

        if (!purchaseInvoiceData.length) {
            loggers.receipt.error(() => 'createPurchaseInvoice: no purchase invoice data');
            return [];
        }

        return xtremPurchasing.functions.PurchaseReceiptLib.createPurchaseInvoice(
            context,
            document,
            purchaseInvoiceData,
        );
    }

    /**
     * Method that creates purchase receipts for a given purchase order
     * The purchase order cannot be 'Closed', 'Fully completed' or 'Fully received'
     * All the lines have to have an item
     * @param context
     * @param purchaseReceiptNumber
     */
    @decorators.mutation<typeof PurchaseReceipt, 'createPurchaseReturns'>({
        isPublished: true,
        parameters: [{ name: 'document', type: 'reference', isMandatory: true, node: () => PurchaseReceipt }],
        return: { type: 'array', item: { type: 'reference', node: () => xtremPurchasing.nodes.PurchaseReturn } },
    })
    static async createPurchaseReturns(
        context: Context,
        document: xtremPurchasing.nodes.PurchaseReceipt,
    ): Promise<xtremPurchasing.nodes.PurchaseReturn[]> {
        if (!(await document.number)) {
            loggers.receipt.error(() => 'createPurchaseReturns: document number is null');
            return [];
        }

        if ((await document.returnStatus) === 'returned') {
            loggers.receipt.error(() => 'createPurchaseReturns: document is returned');
            return [];
        }

        // Group lines per potential supplier and site criteria
        const purchaseReturnData = await xtremPurchasing.functions.PurchaseReceiptLib.initPurchaseReturnCreateData(
            context,
            document,
        );

        loggers.receipt.debug(() => `createReturn data=${JSON.stringify(purchaseReturnData)}`);
        // Add Address data to the receipt
        const supplierAddress = await document.supplierAddress;
        const returnAddress = await document.returnAddress;

        await asyncArray(purchaseReturnData).forEach(async purchaseReturn => {
            purchaseReturn.supplierAddress = supplierAddress
                ? {
                      name: await supplierAddress.name,
                      addressLine1: await supplierAddress.addressLine1,
                      addressLine2: await supplierAddress.addressLine2,
                      city: await supplierAddress.city,
                      region: await supplierAddress.region,
                      postcode: await supplierAddress.postcode,
                      country: await supplierAddress.country,
                      locationPhoneNumber: await supplierAddress.locationPhoneNumber,
                  }
                : undefined;

            purchaseReturn.returnToAddress = returnAddress
                ? {
                      name: await returnAddress.name,
                      addressLine1: await returnAddress.addressLine1,
                      addressLine2: await returnAddress.addressLine2,
                      city: await returnAddress.city,
                      region: await returnAddress.region,
                      postcode: await returnAddress.postcode,
                      country: await returnAddress.country,
                      locationPhoneNumber: await returnAddress.locationPhoneNumber,
                  }
                : undefined;
        });

        return createPurchaseReturns(context, purchaseReturnData);
    }

    override async executePost(context: Context): Promise<boolean> {
        if ((await this.taxCalculationStatus) === 'failed') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt_post__tax_calculation_failed',
                    'You need to correct lines that failed the tax calculation before you can post.',
                ),
            );
        }

        if ((await this.status) !== 'draft') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt_post__already_done',
                    'The purchase receipt was already posted.',
                ),
            );
        }

        const receiptLines = this.lines.filter(async line => (await line.stockDetailStatus) === 'required');

        if ((await receiptLines.length) > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt_post__stock_details_required',
                    'You need to enter stock details for all lines before you can post.',
                ),
            );
        }

        await checkTaxCalculationStatusForPosting(context, this);
        const notPostedAllocatingDocuments =
            await xtremLandedCost.functions.landedCostLineLib.checkLandedCostForPosting(context, this);
        if (notPostedAllocatingDocuments.length > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt__invoices_must_be_posted_first',
                    'You need to post all the landed cost purchase invoices associated with the purchase order lines received before you can post the receipt.\n\n{{invoices}}',
                    { invoices: notPostedAllocatingDocuments.slice(0, 5).join(' - ') },
                ),
            );
        }

        const postResult: xtremFinanceData.interfaces.MutationResult = await context.runInWritableContext(
            writableContext => {
                return xtremPurchasing.nodes.PurchaseReceipt.financeIntegrationCheck(writableContext, this);
            },
        );
        if (postResult.message.length) throw new BusinessRuleError(postResult.message);

        const receiptId = this._id;

        await context.runInWritableContext(async childContext => {
            const writableReceipt = await xtremMasterData.functions.getWritableNode(
                childContext,
                xtremPurchasing.nodes.PurchaseReceipt,
                receiptId,
            );
            await writableReceipt.changeReceiptStatusToPending();
        });

        // this will skip the stock posting for all the non-stock items and complete them
        await PurchaseReceipt.onStockReply(context, {
            requestNotificationId: 'none',
            movementHasBeenSkipped: true,
            updateResults: {
                receipt: {
                    documents: [
                        {
                            id: receiptId,
                            lines: await this.lines
                                .filter(line =>
                                    xtremStockData.functions.stockDocumentLib.shouldSkipStockManagementForThisLine(
                                        line,
                                    ),
                                )
                                .map(async line => {
                                    return {
                                        id: line._id,
                                        sortValue: await line._sortValue,
                                        stockUpdateResultStatus: 'none' as xtremStockData.enums.StockUpdateResultAction,
                                        originLineIds: [],
                                        stockJournalRecords: [],
                                    };
                                })
                                .toArray(),
                        },
                    ],
                },
            },
        });

        // create Landed cost lines + StockCorrectionDetail records linked to landed costs or invoice on orders
        await xtremPurchasing.functions.PurchaseReceiptLib.prepareReceiptStockPosting(context, receiptId);

        // must be called after onStockReply (for non-stock items) and changeReceiptStatusToPending are called.
        const stockMovementRequestResult = JSON.parse(
            await PurchaseReceipt.postToStock(context, [receiptId]),
        ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.receipt>;

        // even if all lines where skipped (non-stock item) the result here will be requested so we can make the diff with errors
        return stockMovementRequestResult.result === 'requested';
    }

    static postToStock(context: Context, documentIds: integer[]): Promise<string> {
        return context.runInWritableContext(childContext => {
            return xtremStockData.functions.notificationLib.stockReceiptRequestNotification(childContext, {
                documentClass: PurchaseReceipt,
                documentIds,
                additionalSteps: [{ movementType: 'correction' as xtremStockData.enums.StockMovementType }],
            });
        });
    }

    @decorators.notificationListener<typeof PurchaseReceipt>({
        startsReadOnly: true,
        topic: 'PurchaseReceipt/stock/receipt/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                xtremPurchasing.nodes.PurchaseReceipt,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.receipt>,
    ): Promise<void> {
        const { receipt: receiptUpdateResult, correction: correctionUpdateResult } =
            await readOnlyContext.runInWritableContext(writableContext =>
                xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    payload,
                    PurchaseReceipt,
                    {
                        beforeDocumentSave: PurchaseReceipt.updateHeaderAfterStockUpdate,
                    },
                ),
            );

        // A purchase receipt can be posted several times: if the corresponding purchase invoice triggered an error, it's possible to "post" again the receipt
        // The finance notification must be sent only if the stock posting was successful
        // But the order-to-order process must be done only once (the 1st time it has been completed)
        const mustPostFinance =
            (receiptUpdateResult || correctionUpdateResult) &&
            !receiptUpdateResult?.transactionHadAnError &&
            !correctionUpdateResult?.transactionHadAnError;
        const firstPostingCompleted = receiptUpdateResult && !receiptUpdateResult.transactionHadAnError;

        const documentsPostedToFinance: number[] = [];

        await receiptUpdateResult.documents.forEach(async document => {
            if (document.isStockDocumentCompleted) {
                await readOnlyContext.runInWritableContext(async writableContext => {
                    const purchaseReceipt = await writableContext.read(PurchaseReceipt, { _id: document.id });
                    if (firstPostingCompleted) {
                        await PurchaseReceipt.onceStockCompleted(writableContext, purchaseReceipt);
                    }
                    if (mustPostFinance) {
                        documentsPostedToFinance.push(document.id);
                        await PurchaseReceipt.sendFinanceNotification(writableContext, {
                            purchaseReceipt,
                        });
                    }
                });
            }
        });

        if (mustPostFinance) {
            await correctionUpdateResult?.documents
                ?.filter(document => !documentsPostedToFinance.includes(document.id))
                .forEach(async document => {
                    if (document.isStockDocumentCompleted) {
                        await readOnlyContext.runInWritableContext(async writableContext => {
                            const purchaseReceipt = await writableContext.read(PurchaseReceipt, { _id: document.id });
                            await PurchaseReceipt.sendFinanceNotification(writableContext, {
                                purchaseReceipt,
                                stockJournalRecords: document.stockJournalRecords,
                            });
                        });
                    }
                });
        }
    }

    static async updateHeaderAfterStockUpdate(
        document: xtremStockData.interfaces.DocumentHeaderWithStockPosting,
    ): Promise<void> {
        const receipt = document as PurchaseReceipt;

        const existingDisplayStatus = await receipt.displayStatus;
        const displayStatus = xtremPurchasing.functions.PurchaseReceiptLib.calculatePurchaseReceiptDisplayStatus(
            await receipt.status,
            await receipt.returnStatus,
            await receipt.invoiceStatus,
            await receipt.stockTransactionStatus,
            await receipt.taxCalculationStatus,
        );
        if (existingDisplayStatus !== displayStatus) {
            await receipt.$.set({ displayStatus });
        }
    }

    static async onceStockCompleted(context: Context, purchaseReceipt: PurchaseReceipt): Promise<void> {
        if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.orderToOrderOption)) {
            await purchaseReceipt.lines.forEach(async line => {
                const purchaseOrderLine = await line.purchaseOrderLine;
                if (purchaseOrderLine !== null) {
                    await xtremStockData.functions.orderAssignment.allocateDemandOrderLine(
                        context,
                        line,
                        await purchaseOrderLine.purchaseOrderLine,
                    );
                }
            });
        }
    }

    static async sendFinanceNotification(
        context: Context,
        args: {
            purchaseReceipt: PurchaseReceipt;
            stockJournalRecords?: AsyncArrayReader<xtremStockData.nodes.StockJournal['_id']>;
        },
    ) {
        if (await (await (await args.purchaseReceipt.stockSite).legalCompany).doStockPosting) {
            // Send notification in order to create staging table entries for the accounting engine
            await xtremPurchasing.functions.FinanceIntegration.purchaseReceiptNotification(
                context,
                args.purchaseReceipt as xtremFinanceData.interfaces.PurchaseFinanceDocument,
                (await args.purchaseReceipt.lines.toArray()) as xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine[],
                await args.stockJournalRecords?.toArray(),
            );
        }
    }

    private async _isLinePropertyFrozen(): Promise<boolean> {
        return isPurchaseReceiptLinePropertyDisabled(await (await this.$.old).status, '');
    }

    @decorators.notificationListener<typeof PurchaseReceipt>({
        topic: 'PurchaseReceipt/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    /**
     * Repost creates an update notification to the acc engine ir order to update att and dimenions.
     * In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
     * If we have a finance transaction record, we assume this repost is driven from a purchase invoice (price variances),
     * so we need to update the accounting staging records where the base document lines are the invoice lines.
     * If we don't have a finance transaction record, we assume this repost is driven from the purchase receipt.
     * The exception to this is when this repost is driven from a landed cost invoice control (invoice not posted yet).
     * In this case we don't have a finance transaction record and we need to update the accounting staging records for the purchase receipt,
     * but just if the accounting staging records were not processed yet.
     * @param purchaseReceipt reference to a purchase receipt to repost
     * @param receiptLines array of objects containing the baseDocumentLineSysId, storedAttributes and storedDimensions, being baseDocumentLineSysId the id of the receipt lne in all cases
     * @param saveOnly if true, when the update notification is sent, the accounting staging records are not processed immediatly
     * @param landedCostControl if true, the update notification is triggered only if the accounting staging records for the purchase receipt were not processed yet
     * @param financeTransaction reference to a finance transaction record. If present, the repost is driven from a purchase invoice (receipt is a source document)
     */
    @decorators.mutation<typeof PurchaseReceipt, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseReceipt',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseReceipt,
            },
            {
                name: 'receiptLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
            { name: 'saveOnly', type: 'boolean' },
            { name: 'landedCostControl', type: 'boolean' },
            {
                name: 'financeTransaction',
                type: 'reference',
                isWritable: true,
                node: () => xtremFinanceData.nodes.FinanceTransaction,
            },
        ],
        return: { type: 'object', properties: { wasSuccessful: 'boolean', message: 'string' } },
    })
    static async repost(
        context: Context,
        purchaseReceipt: PurchaseReceipt,
        receiptLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
        saveOnly: boolean,
        landedCostControl: boolean,
        financeTransaction?: xtremFinanceData.nodes.FinanceTransaction,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        // If we have a finance transaction (we want to update the invoice acc staging records, not the receipt ones)
        // we need to check the status of the document of the finance transaction and not the
        // receipt one
        if (
            !xtremFinanceData.functions.canRepost(
                financeTransaction ? await financeTransaction.status : await purchaseReceipt.financeIntegrationStatus,
            ) &&
            !landedCostControl
        ) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase-receipt__cant_repost_purchase_receipt_when_status_is_not_failed',
                    "You can only repost a purchase receipt if the status is 'Failed' or 'Not recorded.'",
                ),
            );
        }

        const updateLines = receiptLines.map(receiptLine => {
            return {
                _id: receiptLine.baseDocumentLineSysId,
                storedAttributes: receiptLine.storedAttributes,
                storedDimensions: receiptLine.storedDimensions,
            };
        });

        await purchaseReceipt.$.set({
            forceUpdateForFinance: true,
            lines: updateLines,
        });
        await purchaseReceipt.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        if (!landedCostControl) {
            await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                document: purchaseReceipt,
                lines: financeTransaction
                    ? await xtremPurchasing.nodes.PurchaseReceipt.getRelatedInvoiceLines(context, purchaseReceipt.lines)
                    : await purchaseReceipt.lines.toArray(),
                documentType: financeTransaction ? await financeTransaction.documentType : 'purchaseReceipt',
                replyTopic: 'PurchaseReceipt/accountingInterface',
                doNotPostOnUpdate: saveOnly,
                financeTransactionRecord: financeTransaction,
                sourceDocumentNumber: await purchaseReceipt.number,
            });
        }
        // If we are editing the dimensions coming from a purchase invoice (this is the case where we
        // edit source document dimensions on a landed cost invoice)
        if (
            ((await financeTransaction?.documentType) === 'purchaseInvoice' &&
                (await financeTransaction?.targetDocumentType) === 'journalEntry') ||
            landedCostControl
        ) {
            // we check if the journal entry for the purchase receipt itself was already created or not
            // if not, we update the accounting staging records for it also
            const prFinanceTransaction = context.query(xtremFinanceData.nodes.FinanceTransaction, {
                filter: {
                    documentNumber: await purchaseReceipt.number,
                    documentType: 'purchaseReceipt',
                    targetDocumentType: 'journalEntry',
                    targetDocumentSysId: 0,
                },
            });
            if ((await prFinanceTransaction.length) === 1) {
                await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
                    document: purchaseReceipt,
                    lines: await purchaseReceipt.lines.toArray(),
                    documentType: 'purchaseReceipt',
                    replyTopic: 'PurchaseReceipt/accountingInterface',
                    doNotPostOnUpdate: true,
                });
            }
        }

        if (saveOnly) {
            return {
                wasSuccessful: true,
                message: context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase-receipt__document_was_saved',
                    'The purchase receipt was saved.',
                ),
            };
        }

        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-purchasing/nodes__purchase-receipt__document_was_posted',
                'The purchase receipt posted.',
            ),
        };
    }

    static async getRelatedInvoiceLines(
        context: Context,
        receiptLines: Collection<xtremPurchasing.nodes.PurchaseReceiptLine>,
    ): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLineNodeUpdate[]> {
        const stockJournalLines = (
            await context
                .query(xtremStockData.nodes.StockJournal, {
                    filter: {
                        documentLine: { _in: await receiptLines.map(line => line._id).toArray() },
                        sourceDocumentLine: { _ne: null },
                    },
                })
                .toArray()
        ).filter(
            async stockJournalLine =>
                (await stockJournalLine.sourceDocumentLine)?.constructor.name === 'PurchaseInvoiceLine',
        );

        return asyncArray(stockJournalLines)
            .map(async stockJournalLine => {
                const receiptLine = await receiptLines.find(
                    async line => line._id === (await stockJournalLine.documentLine)._id,
                );
                return {
                    _id: (await stockJournalLine.sourceDocumentLine)?._id || 0,
                    sourceBaseDocumentLineSysId: receiptLine?._id,
                    storedAttributes: (await receiptLine?.storedAttributes) || {},
                    storedDimensions: (await receiptLine?.storedDimensions) || {},
                    computedAttributes: (await receiptLine?.computedAttributes) || {},
                };
            })
            .toArray();
    }

    @decorators.mutation<typeof PurchaseReceipt, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseReceipt',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseReceipt,
            },
            { name: 'receiptNumber', type: 'string', isMandatory: false },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
                validationMessages: {
                    type: 'array',
                    isMandatory: false,
                    item: {
                        type: 'object',
                        properties: {
                            message: 'string',
                            lineNumber: { type: 'integer', isMandatory: false },
                            type: {
                                type: 'enum',
                                dataType: () => xtremFinanceData.dataTypes.validationSeverityDataType,
                            },
                            sourceDocumentNumber: 'string',
                        },
                    },
                },
            },
        },
    })
    static financeIntegrationCheck(
        context: Context,
        purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
        receiptNumber?: string,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremPurchasing.functions.FinanceIntegration.purchaseReceiptControlAndCreateMutationResult(
            context,
            purchaseReceipt,
            receiptNumber,
        );
    }

    @decorators.mutation<typeof PurchaseReceipt, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseReceipt',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseReceipt,
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(context: Context, purchaseReceipt: PurchaseReceipt): Promise<boolean> {
        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await purchaseReceipt.number,
                documentType: 'purchaseReceipt',
            })
        ) {
            await PurchaseReceipt.sendFinanceNotification(context, {
                purchaseReceipt,
            });
        }

        return true;
    }

    @decorators.notificationListener<typeof PurchaseReceipt>({
        topic: 'PurchaseReceipt/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const purchaseReceipt = await context.read(PurchaseReceipt, { number: document.number });

        await PurchaseReceipt.resendNotificationForFinance(context, purchaseReceipt);
    }

    override updateDocumentLineTo(
        lines:
            | Collection<xtremPurchasing.nodes.PurchaseReceiptLine>
            | AsyncArrayReader<xtremPurchasing.nodes.PurchaseReceiptLine>,
    ): Promise<boolean> {
        return xtremPurchasing.functions.PurchaseInvoiceLib.updateDocumentLineStatuses(this, lines);
    }

    @decorators.mutation<typeof PurchaseReceipt, 'beforePrintPurchaseReceipt'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'receipt',
                type: 'reference',
                isMandatory: true,
                node: () => xtremPurchasing.nodes.PurchaseReceipt,
            },
        ],
        return: { type: 'boolean' },
    })
    static async beforePrintPurchaseReceipt(context: Context, receipt: PurchaseReceipt): Promise<boolean> {
        if ((await receipt.taxCalculationStatus) === 'failed') {
            await context.batch.logMessage(
                'error',
                `Cannot print the document. Tax calculation failed. Document details: ${JSON.stringify({
                    number: await receipt.number,
                    status: await receipt.status,
                    taxCalculationStatus: await receipt.taxCalculationStatus,
                    displayStatus: await receipt.displayStatus,
                })}`,
            );
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt__print_tax_calculation_failed',
                    'You need to resolve tax calculation issues before you can print the receipt.',
                ),
            );
        }

        if ((await receipt.status) === 'error') {
            await context.batch.logMessage(
                'error',
                `Cannot print the document. Document details: ${JSON.stringify({
                    number: await receipt.number,
                    status: await receipt.status,
                    taxCalculationStatus: await receipt.taxCalculationStatus,
                    displayStatus: await receipt.displayStatus,
                })}`,
            );

            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt__print_error',
                    'You need to correct the receipt details before you can print the receipt.',
                ),
            );
        }

        await context.batch.logMessage(
            'info',
            `Purchase receipt ${await receipt.number} validation for printing completed successfully.`,
        );

        return true;
    }
}
