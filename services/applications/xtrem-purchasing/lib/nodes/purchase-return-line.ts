import type { Collection, Reference, date, decimal } from '@sage/xtrem-core';
import { NodeStatus, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import { controlDelete } from '../events/controlDelete/purchase-return-line';
import { controlEnd } from '../events/controlEnd/purchase-return-line';
import { saveBegin } from '../events/saveBegin/purchase-return-line';
import { saveEnd } from '../events/saveEnd/purchase-return-line';
import * as xtremPurchasing from '../index';
import { BasePurchaseDocumentLine } from './base-document-nodes/index';

@decorators.subNode<PurchaseReturnLine>({
    extends: () => BasePurchaseDocumentLine,
    isPublished: true,
    canSearch: true,
    canRead: true,
    async createEnd() {
        await this.$.set({ workInProgress: {} });
    },
    async controlEnd(cx) {
        await controlEnd(this, cx);
    },
    async controlDelete(cx) {
        await controlDelete(this, cx);
    },
    async saveBegin() {
        await saveBegin(this);
    },
    async saveEnd() {
        await saveEnd(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class PurchaseReturnLine
    extends BasePurchaseDocumentLine
    implements xtremFinanceData.interfaces.ReturnDocumentLine, xtremStockData.interfaces.DocumentLineWithStockAllocation
{
    async getOrderCost(): Promise<decimal> {
        return xtremStockData.functions.stockValuationLib.getStockCost(this.$.context, {
            costType: 'order',
            movementType: 'issue',
            documentLineId: (await (await this.purchaseReceiptLine)?.purchaseReceiptLine)?._id,
            itemSite: await this.$.context.read(xtremMasterData.nodes.ItemSite, {
                item: await this.item,
                site: await this.site,
            }),
        });
    }

    async getValuedCost(): Promise<decimal> {
        return xtremStockData.functions.stockValuationLib.getStockCost(this.$.context, {
            costType: 'value',
            movementType: 'issue',
            documentLineId: (await (await this.purchaseReceiptLine)?.purchaseReceiptLine)?._id,
            itemSite: await this.$.context.read(xtremMasterData.nodes.ItemSite, {
                item: await this.item,
                site: await this.site,
            }),
        });
    }

    async getValuationParameters(): Promise<xtremStockData.interfaces.ValuationParameter> {
        return {
            valuationType: 'negativeReceipt',
            originDocumentLine: await (await this.purchaseReceiptLine).purchaseReceiptLine,
        };
    }

    @decorators.referencePropertyOverride<PurchaseReturnLine, 'document'>({
        node: () => xtremPurchasing.nodes.PurchaseReturn,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseReturn>;

    @decorators.enumPropertyOverride<PurchaseReturnLine, 'status'>({
        dependsOn: [
            'lineInvoiceStatus',
            'quantityInStockUnit',
            { purchaseInvoiceLines: ['invoicedQuantityInStockUnit'] },
        ],
        async updatedValue() {
            if ((await this.status) === 'draft') {
                return this.status;
            }
            switch (await this.lineInvoiceStatus) {
                case 'notInvoiced':
                    return 'pending';
                case 'partiallyInvoiced':
                    return 'inProgress';
                case 'invoiced':
                    return 'closed';
                default:
                    return this.status;
            }
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumProperty<PurchaseReturnLine, 'lineInvoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseReturnInvoiceStatusDataType,
        lookupAccess: true,
        /**
         * Purchase order - status property :
         * - is defaulted to 'Draft' when record is created
         */
        dependsOn: ['invoicedQuantityInStockUnit', 'quantityInStockUnit'],
        defaultValue() {
            return 'notInvoiced';
        },
        async updatedValue() {
            if ((await this.invoicedQuantityInStockUnit) === 0) {
                return 'notInvoiced';
            }
            if ((await this.invoicedQuantityInStockUnit) < (await this.quantityInStockUnit)) {
                return 'partiallyInvoiced';
            }
            return 'invoiced';
        },
    })
    readonly lineInvoiceStatus: Promise<xtremPurchasing.enums.PurchaseReturnInvoiceStatus>;

    @decorators.enumProperty<PurchaseReturnLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocate', 'quantityAllocated', 'item'],
        async getValue() {
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocate,
                await this.quantityAllocated,
                await (
                    await this.item
                ).isStockManaged,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.enumProperty<PurchaseReturnLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.decimalPropertyOverride<PurchaseReturnLine, 'quantityInStockUnit'>({
        dependsOn: [
            'item',
            'unit',
            'stockUnit',
            'quantity',
            'unitToStockUnitConversionFactor',
            'purchaseReceiptLine',
            'document',
        ],
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.quantity,
                await this.unit,
                await this.stockUnit,
                true,
                !!(await this.purchaseReceiptLine),
                await this._isConversionFactorFrozen,
                this.$.status,
                await this.unitToStockUnitConversionFactor,
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseReturnLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', 'unit', 'stockUnit', { document: ['businessRelation'] }, 'purchaseReceiptLine', 'document'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async defaultValue() {
            return xtremMasterData.functions.getConvertCoefficient(
                await this.unit,
                await this.stockUnit,
                'purchase',
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        async updatedValue() {
            if (
                (this.$.status === NodeStatus.added && (await this.purchaseReceiptLine)) ||
                !(await this._isConversionFactorFrozen)
            ) {
                return xtremMasterData.functions.getConvertCoefficient(
                    await this.unit,
                    await this.stockUnit,
                    'purchase',
                    await this.item,
                    await (
                        await this.document
                    ).businessRelation,
                );
            }
            return this.unitToStockUnitConversionFactor;
        },
        isFrozen() {
            return this._isConversionFactorFrozen;
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.datePropertyOverride<PurchaseReturnLine, 'taxDate'>({
        dependsOn: [{ document: ['returnRequestDate'] }],
        async defaultValue() {
            return (await this.document).returnRequestDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly taxDate: Promise<date>;

    @decorators.jsonProperty<PurchaseReturnLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['site', 'taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremPurchasing.nodes.PurchaseReturnLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.dateProperty<PurchaseReturnLine, 'expectedReturnDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['document', { document: ['returnRequestDate'] }],
        async defaultValue() {
            return (await this.document).returnRequestDate;
        },
        async control(cx, val) {
            if (
                (this.$.status === NodeStatus.added ||
                    (this.$.status === NodeStatus.modified &&
                        val.compare(await (await this.$.old).expectedReturnDate) !== 0)) &&
                val.compare(await (await this.document).returnRequestDate) < 0
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/nodes__purchase_return_line__expected_return_date_inferior_to_return_date',
                    'The expected return date should not be before the return date',
                );
            }
        },
    })
    readonly expectedReturnDate: Promise<date>;

    @decorators.decimalProperty<PurchaseReturnLine, 'totalTaxExcludedAmount'>({
        isStored: true,
        isPublished: true,
        // isNotZero: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['quantity', 'netPrice'],
        async defaultValue() {
            return (await this.quantity) * (await this.netPrice);
        },
        updatedValue: useDefaultValue,
    })
    readonly totalTaxExcludedAmount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLine, 'calculatedTotalAmountExcludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [
            'totalTaxExcludedAmount',
            { document: ['returnSite', 'currency', 'companyFxRateDivisor', 'companyFxRate'] },
        ],
        async defaultValue() {
            // convert transaction amount into company currency
            const document = await this.document;
            return xtremMasterData.sharedFunctions.convertAmount(
                await this.totalTaxExcludedAmount,
                await document.companyFxRate,
                await document.companyFxRateDivisor,
                await (
                    await document.currency
                ).decimalDigits,
                await (
                    await (
                        await (
                            await document.returnSite
                        ).legalCompany
                    ).currency
                ).decimalDigits,
            );
        },
        updatedValue: useDefaultValue,
        isFrozen() {
            return this._isLinePropertyFrozen('calculatedTotalAmountExcludingTaxInCompanyCurrency');
        },
    })
    readonly calculatedTotalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReturnLine, 'purchaseReceiptLine'>({
        isPublished: true,
        isVital: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine,
        reverseReference: 'purchaseReturnLine',
        lookupAccess: true,
    })
    readonly purchaseReceiptLine: Reference<xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine>;

    @decorators.collectionProperty<PurchaseReturnLine, 'purchaseInvoiceLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLineToPurchaseInvoiceLine,
        reverseReference: 'purchaseReturnLine',
    })
    readonly purchaseInvoiceLines: Collection<xtremPurchasing.nodes.PurchaseReturnLineToPurchaseInvoiceLine>;

    @decorators.referenceProperty<PurchaseReturnLine, 'workInProgress'>({
        isPublished: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'purchaseReturnLine',
        node: () => xtremPurchasing.nodes.WorkInProgressPurchaseReturnLine,
        // TODO: move instance create from createEnd here once the platform fixes usage of defaultValue on vital
        // defaultValue() {
        //     // TODO: create entry according to document line criteria
        //     this.$.set({workInProgress: {}});
        // },
    })
    readonly workInProgress: Reference<xtremPurchasing.nodes.WorkInProgressPurchaseReturnLine | null>;

    @decorators.decimalProperty<PurchaseReturnLine, 'invoicedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseInvoiceLines: ['invoicedQuantity'] }],
        lookupAccess: true,
        getValue() {
            return this.purchaseInvoiceLines.sum(line => line.invoicedQuantity);
            // TODO: Sort out rounding
        },
    })
    readonly invoicedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLine, 'invoicedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseInvoiceLines: ['invoicedQuantityInStockUnit'] }],
        lookupAccess: true,
        getValue() {
            return this.purchaseInvoiceLines.sum(line => line.invoicedQuantityInStockUnit);
            // TODO: Sort out rounding
        },
    })
    readonly invoicedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLine, 'quantityToInvoice'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: ['quantity', 'invoicedQuantity'],
        async getValue() {
            return Math.max((await this.quantity) - (await this.invoicedQuantity), 0);
        },
    })
    readonly quantityToInvoice: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLine, 'quantityToInvoiceInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityInStockUnit', 'invoicedQuantityInStockUnit'],
        async getValue() {
            return Math.max((await this.quantityInStockUnit) - (await this.invoicedQuantityInStockUnit), 0);
        },
    })
    readonly quantityToInvoiceInStockUnit: Promise<decimal>;

    @decorators.enumPropertyOverride<PurchaseReturnLine, 'origin'>({
        dependsOn: ['purchaseReceiptLine'],
        async defaultValue() {
            if (await this.purchaseReceiptLine) {
                return 'purchaseReceipt';
            }
            return 'direct';
        },
    })
    override readonly origin: Promise<xtremPurchasing.enums.PurchaseDocumentLineOrigin>;

    // TODO: remove this and un-comment in base node once https://jira.sage.com/browse/XT-22853 is implemented
    @decorators.jsonPropertyOverride<PurchaseReturnLine, 'storedDimensions'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).billBySupplier,
                item: await this.item,
            });
        },
    })
    override readonly storedDimensions: Promise<object | null>;

    @decorators.jsonPropertyOverride<PurchaseReturnLine, 'storedAttributes'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).billBySupplier,
                item: await this.item,
            });
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonPropertyOverride<PurchaseReturnLine, 'computedAttributes'>({
        dependsOn: ['document', 'item'],
        async computeValue() {
            const document = await this.document;
            return xtremPurchasing.functions.computeAttributes(this.$.context, {
                site: await document.returnSite,
                item: await this.item,
                supplier: await document.billBySupplier,
                line: this,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.enumProperty<PurchaseReturnLine, 'shippedStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseReturnShippingStatusDataType,
        defaultValue: 'notShipped',
    })
    readonly shippedStatus: Promise<xtremPurchasing.enums.PurchaseReturnShippingStatus>;

    @decorators.enumProperty<PurchaseReturnLine, 'approvalStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseDocumentApprovalStatusDataType,
        defaultValue: 'draft',
    })
    readonly approvalStatus: Promise<xtremPurchasing.enums.PurchaseDocumentApprovalStatus>;

    @decorators.referenceProperty<PurchaseReturnLine, 'reason'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.ReasonCode,
    })
    readonly reason: Reference<xtremMasterData.nodes.ReasonCode>;

    @decorators.decimalProperty<PurchaseReturnLine, 'creditedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        getValue() {
            return this.purchaseCreditMemoLines.sum(creditLine => creditLine.creditMemoQuantity);
        },
    })
    readonly creditedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLine, 'creditedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseCreditMemoLines: ['creditMemoQuantityInStockUnit'] }],
        getValue() {
            return this.purchaseCreditMemoLines.sum(line => line.creditMemoQuantityInStockUnit);
        },
    })
    readonly creditedQuantityInStockUnit: Promise<decimal>;

    @decorators.enumProperty<PurchaseReturnLine, 'lineCreditStatus'>({
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseReturnCreditStatusDataType,
        dependsOn: ['quantity', 'creditedQuantity'],
        async getValue() {
            if ((await this.creditedQuantity) === 0) {
                return 'notCredited';
            }
            if ((await this.creditedQuantity) >= (await this.quantity)) {
                return 'credited';
            }
            return 'partiallyCredited';
        },
    })
    readonly lineCreditStatus: Promise<xtremPurchasing.enums.PurchaseReturnCreditStatus>;

    @decorators.collectionProperty<PurchaseReturnLine, 'purchaseCreditMemoLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLineToPurchaseCreditMemoLine,
        reverseReference: 'purchaseReturnLine',
    })
    readonly purchaseCreditMemoLines: Collection<xtremPurchasing.nodes.PurchaseReturnLineToPurchaseCreditMemoLine>;

    @decorators.decimalProperty<PurchaseReturnLine, 'remainingQuantityToCredit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        lookupAccess: true,
        async getValue() {
            return (await this.quantity) - (await this.creditedQuantity) - (await this.invoicedQuantity);
        },
    })
    readonly remainingQuantityToCredit: Promise<decimal>;

    @decorators.collectionProperty<PurchaseReturnLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.decimalProperty<PurchaseReturnLine, 'quantityAllocated'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockAllocations'],
        getValue() {
            return this.stockAllocations.sum(allocation => allocation.quantityInStockUnit);
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLine, 'remainingQuantityToAllocate'>({
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'quantityAllocated'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.quantityInStockUnit) - (await this.quantityAllocated);
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    // the sourceDocumentNumber property on line level is needed for the accounting interface
    @decorators.stringProperty<PurchaseReturnLine, 'sourceDocumentNumber'>({
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        dependsOn: ['origin', 'purchaseReceiptLine'],
        async getValue() {
            return (await this.origin) === 'purchaseReceipt'
                ? (await (await (await this.purchaseReceiptLine).purchaseReceiptLine).document).number
                : '';
        },
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.enumProperty<PurchaseReturnLine, 'sourceDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        dependsOn: ['origin'],
        async getValue() {
            return (await this.origin) === 'purchaseReceipt' ? 'purchaseReceipt' : null;
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    // the site property on line level is needed for the accounting interface
    @decorators.referencePropertyOverride<PurchaseReturnLine, 'site'>({
        // async getValue() {
        //     return (await this.document).stockSite;
        // },
        filters: {
            control: {
                isPurchase: true,
                isInventory: true,
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseReturnLine, 'item'>({
        filters: {
            control: {
                isBought: true,
                type: { _ne: 'landedCost' },
                async itemSites() {
                    return { _atLeast: 1, site: await this.stockSite };
                },
            },
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.landedCostItemValidation(
                val,
                (await this.document).$.factory.name,
                cx,
            );
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.collectionProperty<PurchaseReturnLine, 'stockTransactions'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    @decorators.collectionProperty<PurchaseReturnLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        dependsOn: ['item', 'itemSite'],
        node: () => xtremStockData.nodes.StockIssueDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.StockIssueDetail>;

    @decorators.collectionProperty<PurchaseReturnLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;
}
