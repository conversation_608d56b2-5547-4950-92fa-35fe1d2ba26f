import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { Collection, Context, Reference, TextStream, ValidationContext } from '@sage/xtrem-core';
import { NodeStatus, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '..';
import { controlDelete } from '../events/controlDelete/purchase-document';
import { isDateLaterThanToday } from '../functions/common';
import { updateChangeRequested } from '../functions/document-approval';
import { controlConfirm } from '../functions/purchase-requisition-lib';
import { areChangeRequestedText, getPurchaseRequistionApprovalData } from '../functions/requisition-approval';

@decorators.subNode<PurchaseRequisition>({
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    extends: () => xtremMasterData.nodes.BaseDocument,
    hasAttachments: true,
    async controlEnd(cx) {
        // Disable while grid saving is not available. At the moment we cannot do the create of parent and
        // lines in one pass, we have to create the parent record before creating the lines
        await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, this);

        await cx.error
            .withMessage(
                '@sage/xtrem-purchasing/nodes__purchase_requisition__item_mandatory_on_lines',
                'The purchase requisition can be ordered only if all lines have an associated item.',
            )
            .if((await this.status) === 'pending' && (await this.lines.some(async line => (await line.item) == null)))
            .is.true();
    },
    async controlDelete(cx: ValidationContext) {
        await controlDelete(this, cx);
    },
    async saveBegin() {
        await xtremMasterData.functions.controlDocumentNumber(this);
    },
})
export class PurchaseRequisition extends xtremMasterData.nodes.BaseDocument {
    @decorators.enumPropertyOverride<PurchaseRequisition, 'status'>({
        dependsOn: [{ lines: ['status'] }, 'orderStatus', 'approvalStatus'],
        updatedValue() {
            return xtremPurchasing.functions.computeRequisitionStatusFromLinesStatuses(this);
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumPropertyOverride<PurchaseRequisition, 'displayStatus'>({
        dependsOn: ['status', 'orderStatus', 'approvalStatus'],
        async updatedValue() {
            return xtremPurchasing.functions.calculatePurchaseRequisitionDisplayStatus(
                await this.status,
                await this.orderStatus,
                await this.approvalStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremPurchasing.enums.PurchaseRequisitionDisplayStatus>;

    @decorators.enumProperty<PurchaseRequisition, 'orderStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseRequisitionOrderStatusDataType,
        defaultValue: 'notOrdered',
        dependsOn: [{ lines: ['lineOrderStatus'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.computeRequisitionOrderStatusFromLinesOrderStatuses(this);
            }
            return this.orderStatus;
        },
        duplicatedValue: useDefaultValue,
    })
    readonly orderStatus: Promise<xtremPurchasing.enums.PurchaseRequisitionOrderStatus>;

    @decorators.enumPropertyOverride<PurchaseRequisition, 'approvalStatus'>({
        async control(cx, val) {
            await xtremPurchasing.events.controls.Requisition.checkApprovalStatus(cx, this, val);
        },
    })
    override readonly approvalStatus: Promise<xtremPurchasing.enums.PurchaseDocumentApprovalStatus>;

    @decorators.referenceProperty<PurchaseRequisition, 'receivingSite'>({
        node: () => xtremSystem.nodes.Site,
        getValue() {
            return this.site;
        },
    })
    readonly receivingSite: Reference<xtremSystem.nodes.Site>;

    /** receivingSite */
    @decorators.referencePropertyOverride<PurchaseRequisition, 'site'>({
        filters: { control: { isInventory: true, isPurchase: true } },
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<PurchaseRequisition, 'requester'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremSystem.nodes.User,
        async defaultValue() {
            return this.$.context.read(xtremSystem.nodes.User, { email: (await this.$.context.user)?.email });
        },
        filters: { lookup: xtremAuthorization.filters.user.interactiveUsers },
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    readonly requester: Reference<xtremSystem.nodes.User>;

    @decorators.datePropertyOverride<PurchaseRequisition, 'date'>({
        dependsOn: ['requestDate'],
        defaultValue() {
            return this.requestDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly date: Promise<date>;

    @decorators.datePropertyOverride<PurchaseRequisition, 'documentDate'>({
        dependsOn: ['requestDate'],
        getValue() {
            return this.requestDate;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.dateProperty<PurchaseRequisition, 'requestDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        defaultValue: () => date.today(),
        duplicatedValue: useDefaultValue,
        duplicateRequiresPrompt: true,
        async control(cx, val) {
            if (
                isDateLaterThanToday(
                    this.$.status,
                    val,
                    this.$.status === NodeStatus.modified ? await (await this.$.old).requestDate : undefined,
                )
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/nodes__purchase_requisition__request_date_cannot_be_future',
                    'The request date cannot be later than today.',
                );
            }
        },
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    readonly requestDate: Promise<date>;

    @decorators.textStreamProperty<PurchaseRequisition, 'changeRequestedDescription'>({
        isStored: true,
        isPublished: true,
    })
    readonly changeRequestedDescription: Promise<TextStream>;

    @decorators.collectionPropertyOverride<PurchaseRequisition, 'lines'>({
        dependsOn: ['requestDate'],
        node: () => xtremPurchasing.nodes.PurchaseRequisitionLine,
    })
    override readonly lines: Collection<xtremPurchasing.nodes.PurchaseRequisitionLine>;

    @decorators.textStreamPropertyOverride<PurchaseRequisition, 'internalNote'>({})
    override readonly internalNote: Promise<TextStream>;

    @decorators.booleanPropertyOverride<PurchaseRequisition, 'isTransferHeaderNote'>({
        duplicatedValue: false,
    })
    override readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<PurchaseRequisition, 'isTransferLineNote'>({
        duplicatedValue: false,
    })
    override readonly isTransferLineNote: Promise<boolean>;

    @decorators.booleanProperty<PurchaseRequisition, 'isApplyDefaultSupplierHidden'>({
        isPublished: true,
        async getValue() {
            if (
                (await this.lines.some(
                    async line => ['draft', 'pending'].includes(await line.status) && (await line.supplier) == null,
                )) &&
                (await this.approvalStatus) !== 'rejected'
            ) {
                return false;
            }
            return true;
        },
    })
    readonly isApplyDefaultSupplierHidden: Promise<boolean>;

    @decorators.booleanProperty<PurchaseRequisition, 'isSetDimensionHidden'>({
        isPublished: true,
        async getValue() {
            return !(await this.lines.some(async line => ['draft', 'pending'].includes(await line.status)));
        },
    })
    readonly isSetDimensionHidden: Promise<boolean>;

    @decorators.booleanProperty<PurchaseRequisition, 'isCreateOrderLinesHidden'>({
        isPublished: true,
        getValue() {
            return this.lines.some(async line => ['pending', 'inProgress'].includes(await line.status));
        },
    })
    readonly isCreateOrderLinesHidden: Promise<boolean>;

    @decorators.mutation<typeof PurchaseRequisition, 'confirm'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                node: () => PurchaseRequisition,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'isSafeToRetry', type: 'boolean' },
        ],
        return: { type: 'reference', node: () => PurchaseRequisition },
    })
    static async confirm(
        context: Context,
        document: PurchaseRequisition,
        isSafeToRetry = false,
    ): Promise<PurchaseRequisition> {
        const isValid = controlConfirm(document.$.context, {
            status: await document.status,
            isPurchaseRequisitionApprovalManaged: await (await document.site).isPurchaseRequisitionApprovalManaged,
            isSafeToRetry,
        });
        if (!isValid) {
            return document;
        }

        await document.$.set({ approvalStatus: 'confirmed', status: 'pending' });

        await document.lines.forEach(async line => {
            await line.$.set({ status: 'pending' });
        });

        await document.$.save();
        return document;
    }

    /** Page url of the document TODO get this from the datatype // tunnelPAge of the document??  */
    @decorators.stringPropertyOverride<PurchaseRequisition, 'page'>({
        getValue: () => '@sage/xtrem-purchasing/PurchaseRequisition',
    })
    override readonly page: Promise<string>;

    @decorators.stringPropertyOverride<PurchaseRequisition, 'approvalUrl'>({
        getValue: () => '@sage/xtrem-purchasing/PurchaseRequisitionApproval',
    })
    override readonly approvalUrl: Promise<string>;

    override async beforeSendApprovalRequestMail(_context: Context, user: xtremSystem.nodes.User) {
        const { subject, data } = await getPurchaseRequistionApprovalData(this.$.context, this);

        return { subject, template: 'purchase_requisition_approval_mail', mailerUser: user, data };
    }

    async beforeSendRequestChangesMail(user: xtremSystem.nodes.User) {
        const { data } = await getPurchaseRequistionApprovalData(this.$.context, this);
        return {
            subject: await areChangeRequestedText(this.$.context, this),
            template: 'purchase_requisition_request_changes_mail',
            mailerUser: user,
            data,
        };
    }

    @decorators.mutation<typeof PurchaseRequisition, 'sendRequestChangesMail'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => PurchaseRequisition, isWritable: true },
            {
                name: 'user',
                type: 'instance',
                node: () => xtremSystem.nodes.User,
                isTransientInput: true,
            },
        ],
        return: 'boolean',
    })
    static async sendRequestChangesMail(
        context: Context,
        document: PurchaseRequisition,
        user: xtremSystem.nodes.User,
    ): Promise<boolean> {
        const { subject, template, data } = await document.beforeSendRequestChangesMail(user);
        await updateChangeRequested(document);
        await xtremMasterData.functions.sendMail(
            context,
            { mailerUser: user, mail: { data, subject, template } },
            document,
        );
        return true;
    }

    async managePurchaseDocumentStatus(approve: boolean): Promise<boolean> {
        await xtremPurchasing.functions.managePurchaseDocumentStatus(this, approve);
        return true;
    }

    /**
     * @param context
     * @param purchaseRequisitionNumber
     */
    @decorators.mutation<typeof PurchaseRequisition, 'approve'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                node: () => PurchaseRequisition,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'approve', type: 'boolean', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async approve(_context: Context, document: PurchaseRequisition, approve: boolean): Promise<boolean> {
        await document.managePurchaseDocumentStatus(approve);
        return true;
    }

    /**
     *
     * @param context
     * @param purchaseRequisitionNumber
     */
    @decorators.mutation<typeof PurchaseRequisition, 'createPurchaseOrders'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                node: () => PurchaseRequisition,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: { type: 'array', item: { type: 'reference', node: () => xtremPurchasing.nodes.PurchaseOrder } },
    })
    static async createPurchaseOrders(
        context: Context,
        document: PurchaseRequisition,
    ): Promise<xtremPurchasing.nodes.PurchaseOrder[]> {
        if (!document) {
            return [];
        }

        if (['closed'].includes(await document.status)) {
            return [];
        }
        // All lines have to have an item
        if (await document.lines.map(line => line.item).some(item => !item)) {
            return [];
        }
        // Group lines per potential supplier and site criteria
        const purchaseOrderData = await xtremPurchasing.functions.initPurchaseOrderCreateData(context, document);

        // Hack: Set requisition status to draft so that the parent node is modified when saving in order for
        // updatedValue to calculate the status. If the parent node is not updated from here, the status is
        // NodeStatus.unchanged when saving and updatedValue has no effect
        // purchaseRequisitionToOrder.status = 'draft';

        return xtremPurchasing.functions.createPurchaseOrders(context, purchaseOrderData);
    }

    /**
     * Method that closes the purchase order
     * @param context
     * @param purchaseRequisitionNumber
     */
    @decorators.mutation<typeof PurchaseRequisition, 'close'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseRequisition',
                type: 'reference',
                node: () => xtremPurchasing.nodes.PurchaseRequisition,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: 'boolean',
    })
    static close(context: Context, purchaseRequisition: xtremPurchasing.nodes.PurchaseRequisition): Promise<boolean> {
        return xtremPurchasing.functions.closeRequisition(context, purchaseRequisition);
    }
}
