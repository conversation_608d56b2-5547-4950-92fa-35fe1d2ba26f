import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../index';

@decorators.subNode<PurchaseCreditMemoLineDiscountCharge>({
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    extends: () => xtremMasterData.nodes.BaseLineDiscountCharge,
})
export class PurchaseCreditMemoLineDiscountCharge extends xtremMasterData.nodes.BaseLineDiscountCharge {
    @decorators.referenceProperty<PurchaseCreditMemoLineDiscountCharge, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseCreditMemoLine,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseCreditMemoLine>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemoLineDiscountCharge, 'basis'>({
        dependsOn: [{ document: ['grossPrice'] }],
        async defaultValue() {
            return (await this.document).grossPrice;
        },
        updatedValue: useDefaultValue,
    })
    override readonly basis: Promise<decimal>;

    override async calculateAmount(): Promise<number> {
        return super.calculateAmount(
            await xtremMasterData.functions.getCompanyPriceScale(await (await (await this.document).site).legalCompany),
        );
    }
}
