import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type {
    BinaryStream,
    Collection,
    Context,
    Reference,
    ValidationContext,
    decimal,
    integer,
} from '@sage/xtrem-core';
import { BusinessRuleError, Decimal, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremPurchasing from '..';
import { loggers } from '../functions';
import { setMatchingUser } from '../functions/document-approval';
import { getPurchaseInvoiceEmailData } from '../functions/invoice-send-mail';
import { initPaymentTracking } from '../functions/payment-tracking';
import { BasePurchaseDocument } from './base-document-nodes';

// Hack to exclude defaultValue rules from upgrade
const excludeFromUpgrade = <T>(x: T) => x;

@decorators.subNode<PurchaseInvoice>({
    extends: () => BasePurchaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    hasAttachments: true,
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.documentNumberExistsForSupplier(this.$.context, cx, {
            supplierId: (await this.billBySupplier)._id,
            supplierDocumentNumber: await this.supplierDocumentNumber,
            nodeToQuery: this.$.factory.name,
            currentDocumentSysId: this._id,
        });
        await xtremTax.functions.validateTaxCategoryAndPaymentTerm(cx, {
            isIntacctActivationOptionEnabled: await this.$.context.isServiceOptionEnabled(
                xtremStructure.serviceOptions.intacctActivationOption,
            ),
            taxes: await this.taxes.toArray(),
            paymentTerm: await this.paymentTerm,
        });
    },
    async prepareBegin() {
        await (
            await xtremPurchasing.classes.PurchasingTaxCalculator.create(
                this.$.context,
                await this.site,
                await this.status,
                false,
                await this.wasTaxDataChanged,
            )
        ).calculateTaxData(this);

        // TODO: Remove this when the PaymentTrackingExtension is implemented after platform fixed the updatedValue problem
        await xtremPurchasing.functions.handlePaymentTrackingOnSupplierDocumentChange(this);
    },
    async createEnd() {
        await xtremPurchasing.functions.PurchaseInvoiceLib.updateHeaderNotesOnCreation(this);
        await xtremPurchasing.functions.setSiteAddress(this, await this.site);
    },
    async controlBegin(cx: ValidationContext) {
        if (xtremMasterData.functions.nodeIsBeingModified(this)) {
            await xtremPurchasing.events.controls.documentIsPosted(this, cx);
        }
        await xtremMasterData.functions.controlDocumentNumber(this);
    },
    async controlDelete(cx: ValidationContext) {
        await xtremPurchasing.events.controls.checkStatusDeletion(this, cx);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class PurchaseInvoice
    extends BasePurchaseDocument
    implements xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
{
    override getEffectiveDate() {
        return this.invoiceDate;
    }

    @decorators.enumPropertyOverride<PurchaseInvoice, 'status'>({
        // dataType: () => xtremPurchasing.enums.purchaseInvoiceStatusDataType,
        defaultValue: () => 'draft',
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseInvoiceStatus>;

    @decorators.enumPropertyOverride<PurchaseInvoice, 'displayStatus'>({
        defaultValue: 'noVariance',
        dependsOn: ['status', 'taxCalculationStatus', 'stockTransactionStatus', 'matchingStatus', 'paymentTracking'],
        async updatedValue() {
            const status = await this.status;
            const taxCalculationStatus = await this.taxCalculationStatus;
            const stockTransactionStatus = await this.stockTransactionStatus;
            const matchingStatus = await this.matchingStatus;
            return xtremPurchasing.functions.PurchaseInvoiceLib.calculatePurchaseInvoiceDisplayStatus(
                status,
                taxCalculationStatus,
                stockTransactionStatus,
                matchingStatus,
                (await (await this.paymentTracking)?.status) ?? null,
            );
        },
    })
    override readonly displayStatus: Promise<xtremPurchasing.enums.PurchaseInvoiceDisplayStatus>;

    @decorators.enumProperty<PurchaseInvoice, 'matchingStatus'>({
        isStored: true,
        isPublished: true,
        dependsOn: [{ lines: ['quantity', 'grossPrice', 'matchingStatus'] }],
        defaultValue() {
            return xtremPurchasing.functions.PurchaseInvoiceLib.computeMatchingStatusFromLinesStatuses(this);
        },
        updatedValue: useDefaultValue,
        dataType: () => xtremPurchasing.enums.purchaseInvoiceMatchingStatusDataType,
    })
    readonly matchingStatus: Promise<xtremPurchasing.enums.PurchaseInvoiceMatchingStatus>;

    @decorators.referencePropertyOverride<PurchaseInvoice, 'site'>({
        filters: {
            control: {
                isFinance: true,
            },
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseInvoice, 'stockSite'>({
        dependsOn: ['site'],
        async updatedValue() {
            return (
                (await ((await (
                    await this.site
                )?.isInventory)
                    ? this.site
                    : (await this.site) &&
                      this.$.context
                          .query(xtremSystem.nodes.Site, {
                              filter: { legalCompany: await (await this.site).legalCompany, isInventory: true },
                          })
                          .at(0))) ?? useDefaultValue
            );
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseInvoice, 'billBySupplier'>({
        async control(cx, val) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.financialSite,
                await val.businessEntity,
            );
        },
    })
    override readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<PurchaseInvoice, 'businessRelation'>({
        dependsOn: ['billBySupplier'],
        defaultValue() {
            return this.billBySupplier;
        },
        updatedValue: useDefaultValue,
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.datePropertyOverride<PurchaseInvoice, 'date'>({
        dependsOn: ['invoiceDate'],
        defaultValue() {
            return this.invoiceDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly date: Promise<date>;

    @decorators.dateProperty<PurchaseInvoice, 'invoiceDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
        async control(cx, val) {
            xtremPurchasing.events.controls.dateValidation(this, await this.invoiceDate, val, cx);
        },
    })
    readonly invoiceDate: Promise<date>;

    @decorators.referenceProperty<PurchaseInvoice, 'billByLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await (await this.billBySupplier)?.billByAddress) || null;
        },
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.billBySupplier)?.businessEntity)?._id || -1;
                },
            },
        },
    })
    readonly billByLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<PurchaseInvoice, 'billByAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['billByLinkedAddress', 'billBySupplier'],
        async defaultValue() {
            return excludeFromUpgrade(await (await this.billByLinkedAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly billByAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<PurchaseInvoice, 'billByContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['billByLinkedAddress', 'billBySupplier'],
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.billByLinkedAddress)?.primaryContact)?.contact) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly billByContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referenceProperty<PurchaseInvoice, 'payToSupplier'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await this.billBySupplier).payToSupplier;
        },
    })
    readonly payToSupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<PurchaseInvoice, 'payToLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['payToSupplier', 'billBySupplier'],
        async defaultValue() {
            return (await this.payToSupplier)
                ? xtremPurchasing.functions.getDefaultPayToAddress(await this.payToSupplier)
                : xtremPurchasing.functions.getDefaultPayToAddress(await this.billBySupplier);
        },
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.billBySupplier).businessEntity)._id;
                },
            },
        },
    })
    readonly payToLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.referenceProperty<PurchaseInvoice, 'payToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: ['payToLinkedAddress', 'payToSupplier'],
        async defaultValue() {
            return excludeFromUpgrade(await (await this.payToLinkedAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly payToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<PurchaseInvoice, 'payToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: ['payToLinkedAddress', 'payToSupplier'],
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.payToLinkedAddress)?.primaryContact)?.contact) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly payToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.referencePropertyOverride<PurchaseInvoice, 'paymentTerm'>({
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await this.billBySupplier)?.paymentTerm;
        },
        filters: { control: { businessEntityType: { _in: ['supplier', 'all'] } } },
    })
    override readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.stringProperty<PurchaseInvoice, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.supplierDocumentNumberDataType,
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.dateProperty<PurchaseInvoice, 'supplierDocumentDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['invoiceDate'],
        defaultValue() {
            return this.invoiceDate;
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.futureDateValidation(val, cx);
        },
    })
    readonly supplierDocumentDate: Promise<date>;

    @decorators.referencePropertyOverride<PurchaseInvoice, 'currency'>({
        dependsOn: ['billBySupplier', 'site'],
        async defaultValue() {
            return (await (await this.billBySupplier).businessEntity).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    // property needed for the accounting interface
    @decorators.referencePropertyOverride<PurchaseInvoice, 'transactionCurrency'>({
        // dependsOn: ['lines'],
        // TODO: To uncomment once platform fix https://jira.sage.com/browse/XT-22873 for build error : "isFrozen can't be redefined by an extension"
        // isFrozen() {
        //     return !!await (this.lines.length);
        // },
    })
    override readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referencePropertyOverride<PurchaseInvoice, 'companyCurrency'>({
        dependsOn: ['site'],
        async getValue() {
            return (await (await this.site).legalCompany).currency;
        },
        // TODO: To uncomment once platform fix https://jira.sage.com/browse/XT-22873 for build error : "isFrozen can't be redefined by an extension"
        // isFrozen() {
        //     return !!await (this.lines.length);
        // },
    })
    override readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.datePropertyOverride<PurchaseInvoice, 'fxRateDate'>({
        dependsOn: ['supplierDocumentDate'],
        defaultValue() {
            return this.supplierDocumentDate;
        },
        updatedValue() {
            return this.supplierDocumentDate;
        },
    })
    override readonly fxRateDate: Promise<date>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalAmountExcludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTax'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountExcludingTax),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly calculatedTotalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalAmountExcludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly calculatedTotalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'varianceTotalAmountExcludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', 'calculatedTotalAmountExcludingTax', 'totalAmountExcludingTax'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) - (await this.calculatedTotalAmountExcludingTax),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly varianceTotalAmountExcludingTax: Promise<decimal>;

    @decorators.binaryStreamProperty<PurchaseInvoice, 'pdfSupplierInvoice'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'binary',
        anonymizeValue: 'pdf',
    })
    readonly pdfSupplierInvoice: Promise<BinaryStream | null>;

    @decorators.collectionPropertyOverride<PurchaseInvoice, 'lines'>({
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLine,
    })
    override readonly lines: Collection<xtremPurchasing.nodes.PurchaseInvoiceLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.referenceProperty<PurchaseInvoice, 'returnLinkedAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['billBySupplier'],
        async defaultValue() {
            return (await (await this.billBySupplier).returnToAddress) || (await this.billBySupplier).primaryAddress;
        },
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.billBySupplier).businessEntity)._id;
                },
            },
        },
    })
    readonly returnLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    @decorators.dateProperty<PurchaseInvoice, 'dueDate'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['supplierDocumentDate', 'paymentTerm'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            return xtremMasterData.sharedFunctions.getDueDate({
                dueDateType: await paymentTerm.dueDateType,
                days: (await paymentTerm.days) ?? 0,
                baseDate: await this.supplierDocumentDate,
            });
        },
        updatedValue: useDefaultValue,
    })
    readonly dueDate: Promise<date>;

    @decorators.decimalProperty<PurchaseInvoice, 'varianceTotalTaxAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', 'totalTaxAmountAdjusted', 'totalTaxAmount'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalTaxAmount) - (await this.totalTaxAmountAdjusted),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly varianceTotalTaxAmount: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseInvoice, 'totalAmountIncludingTax'>({
        dependsOn: ['currency', 'totalAmountExcludingTax', 'totalTaxAmount'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) + (await this.totalTaxAmount), // TODO: other docs use this.totalTaxAmountAdjusted ???
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    override readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly calculatedTotalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'varianceTotalAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', 'calculatedTotalAmountIncludingTax', 'totalAmountIncludingTax'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountIncludingTax) - (await this.calculatedTotalAmountIncludingTax),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly varianceTotalAmountIncludingTax: Promise<decimal>;

    @decorators.referenceProperty<PurchaseInvoice, 'matchingUser'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        filters: {
            lookup: xtremAuthorization.filters.user.interactiveUsers,
        },
    })
    readonly matchingUser: Reference<xtremSystem.nodes.User | null>;

    // property needed for the accounting interface
    @decorators.datePropertyOverride<PurchaseInvoice, 'documentDate'>({
        getValue() {
            return this.invoiceDate;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalAmountIncludingTax'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['calculatedTotalAmountExcludingTax', 'calculatedTotalTaxAmountAdjusted'],
        async getValue() {
            return (await this.calculatedTotalAmountExcludingTax) + (await this.calculatedTotalTaxAmountAdjusted);
        },
    })
    readonly calculatedTotalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalTaxAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['taxAmount'] }],
        getValue() {
            return this.lines.sum(line => line.taxAmount);
        },
    })
    readonly calculatedTotalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalTaxAmountAdjusted'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['taxAmountAdjusted'] }],
        getValue() {
            return this.lines.sum(line => line.taxAmountAdjusted);
        },
    })
    readonly calculatedTotalTaxAmountAdjusted: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseInvoice, 'totalTaxAmountAdjusted'>({
        dependsOn: [{ lines: ['taxAmountAdjusted'] }],
        defaultValue() {
            return this.lines.sum(line => line.taxAmountAdjusted);
        },
        updatedValue: useDefaultValue,
    })
    override readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalTaxableAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['taxableAmount'] }],
        getValue() {
            return this.lines.sum(line => line.taxableAmount);
        },
    })
    readonly calculatedTotalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalExemptAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['exemptAmount'] }],
        getValue() {
            return this.lines.sum(line => line.exemptAmount);
        },
    })
    readonly calculatedTotalExemptAmount: Promise<decimal>;

    @decorators.enumProperty<PurchaseInvoice, 'financeStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseInvoiceStatusDataType,
        dependsOn: ['status'],
        defaultValue() {
            return this.status;
        },
    })
    readonly financeStatus: Promise<xtremPurchasing.enums.PurchaseInvoiceStatus>;

    @decorators.enumProperty<PurchaseInvoice, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.decimalProperty<PurchaseInvoice, 'calculatedTotalRemainingQuantityToCredit'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        dependsOn: ['currency', { lines: ['remainingQuantityToCredit'] }],
        getValue() {
            return this.lines.sum(line => line.remainingQuantityToCredit);
        },
    })
    readonly calculatedTotalRemainingQuantityToCredit: Promise<decimal>;

    @decorators.collectionProperty<PurchaseInvoice, 'apOpenItems'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.BaseOpenItem,
        join: {
            documentType: () => 'purchaseInvoice',
            documentSysId() {
                return this._id;
            },
        },
    })
    readonly apOpenItems: Collection<xtremFinanceData.nodes.BaseOpenItem>;

    @decorators.integerProperty<PurchaseInvoice, 'openItemSysId'>({
        isPublished: true,
        isNullable: true,
        async computeValue() {
            return (await this.apOpenItems.at(0))?._id ?? null;
        },
    })
    readonly openItemSysId: Promise<number | null>;

    @decorators.booleanProperty<PurchaseInvoice, 'isOpenItemPageOptionActive'>({
        isPublished: true,
        serviceOptions: () => [xtremStructure.serviceOptions.openItemPageOption],
        getValue() {
            return true;
        },
    })
    readonly isOpenItemPageOptionActive: Promise<boolean>;

    @decorators.collectionProperty<PurchaseInvoice, 'postingDetails'>({
        isPublished: true,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType() {
                return { _in: ['purchaseInvoice', 'apInvoice'] };
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.mutation<typeof PurchaseInvoice, 'acceptAllVariances'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => PurchaseInvoice, isMandatory: true, isWritable: true },
            { name: 'approve', type: 'boolean', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async acceptAllVariances(context: Context, document: PurchaseInvoice, approve: boolean): Promise<boolean> {
        const user = await context.read(xtremSystem.nodes.User, { _id: (await context.user)?._id });

        const linesUpdatedCount = await context.bulkUpdate(xtremPurchasing.nodes.PurchaseInvoiceLine, {
            set: {
                matchingStatus: approve ? 'varianceApproved' : 'variance',
                varianceApprover: user,
            },
            where: {
                document: { _id: document._id },
                matchingStatus: 'variance',
            },
        });

        if (linesUpdatedCount > 0) {
            const remainingVarianceLines = await context.queryCount(xtremPurchasing.nodes.PurchaseInvoiceLine, {
                filter: {
                    document: { _id: document._id },
                    matchingStatus: 'variance',
                },
            });

            if (remainingVarianceLines === 0) {
                await document.$.set({
                    matchingStatus: 'varianceApproved',
                });
                await document.$.save();
            }
            return true;
        }

        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_invoice__invalid_variance_approval',
                'Invalid status for approval.',
            ),
        );
    }

    /** Page url of the document TODO get this from the datatype // tunnelPAge of the document??  */
    @decorators.stringPropertyOverride<PurchaseInvoice, 'page'>({
        getValue: () => '@sage/xtrem-purchasing/PurchaseInvoice',
    })
    override readonly page: Promise<string>;

    async beforeSendNotificationToBuyerMail(user: xtremSystem.nodes.User) {
        const { subject, data } = await getPurchaseInvoiceEmailData(this);
        return {
            mailerUser: user,
            mail: { subject, template: 'purchase_invoice_buyer_notification_mail', data },
        };
    }

    /**
     * Operation that send an approval email to a user pre-filled email address.
     * The email is send only if the status is 'Draft' and the approval status is 'not submitted', 'change requested' or 'not submitted'
     * @param context
     * @param purchaseInvoiceNumber
     * @param url
     * @param email
     */
    @decorators.mutation<typeof PurchaseInvoice, 'sendNotificationToBuyerMail'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseInvoice,
                isWritable: true,
            },
            {
                name: 'user',
                type: 'instance',
                node: () => xtremSystem.nodes.User,
                isTransientInput: true,
            },
        ],
        return: 'boolean',
    })
    static async sendNotificationToBuyerMail(
        context: Context,
        document: PurchaseInvoice,
        user: xtremSystem.nodes.User,
    ): Promise<boolean> {
        const data = await document.beforeSendNotificationToBuyerMail(user);
        await xtremMasterData.functions.sendMail(context, data, document);
        await setMatchingUser({ document, user });
        return true;
    }

    @decorators.mutation<typeof PurchaseInvoice, 'post'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'purchaseInvoice',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseInvoice,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
                validationMessages: {
                    type: 'array',
                    isMandatory: false,
                    item: {
                        type: 'object',
                        properties: {
                            message: 'string',
                            lineNumber: { type: 'integer', isMandatory: false },
                            type: {
                                type: 'enum',
                                dataType: () => xtremFinanceData.dataTypes.validationSeverityDataType,
                            },
                            sourceDocumentNumber: 'string',
                        },
                    },
                },
                stockPostingResult: 'string',
            },
        },
    })
    static async post(
        context: Context,
        purchaseInvoice: PurchaseInvoice,
    ): Promise<
        xtremFinanceData.interfaces.MutationResult & {
            stockPostingResult: string;
        }
    > {
        const postResult = await context.runInWritableContext(async writableContext => {
            return {
                ...(await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoControlFromNotificationPayloadErrors(
                    writableContext,
                    purchaseInvoice,
                    'purchaseInvoice',
                )),
                stockPostingResult: '',
            };
        });

        if (!(await purchaseInvoice.lines.length)) {
            postResult.message = context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_whitout_lines',
                'You need to add lines to the purchase invoice before you can post it.',
            );
        }

        if (postResult.message) {
            return postResult;
        }

        if ((await purchaseInvoice.taxCalculationStatus) !== 'done') {
            postResult.message = context.localize(
                '@sage/xtrem-purchasing/cant_post_invoice_when_taxCalculationStatus_is_not_done',
                'The tax calculation is not {{done}}, the invoice cannot be posted.',
                {
                    done: context.localizeEnumMember('@sage/xtrem-master-data/TaxCalculationStatus', 'done'),
                },
            );
            throw new BusinessRuleError(postResult.message);
        }

        if (
            (await purchaseInvoice.calculatedTotalAmountExcludingTax) !==
            (await purchaseInvoice.totalAmountExcludingTax)
        ) {
            postResult.message = context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_when_totals_not_equal',
                'The user entered total tax excluded amount is not equal to the sum of the lines.',
            );
            throw new BusinessRuleError(postResult.message);
        }

        await purchaseInvoice.lines.forEach(async line => {
            if ((await line.stockTransactionStatus) === 'draft') {
                const receiptLine = await (await line.purchaseReceiptLine)?.purchaseReceiptLine;
                if (receiptLine && (await receiptLine.stockTransactionStatus) !== 'completed') {
                    throw new BusinessRuleError(
                        context.localize(
                            '@sage/xtrem-purchasing/nodes__purchase_invoice__cant_post_invoice_when_receipt_line_not_completed',
                            'The purchase invoice cannot be posted. The stock status of all related purchase receipt lines needs to be Completed.',
                        ),
                    );
                }
            }

            const checkResult = await line.checkLandedCostCanBePosted();
            if (checkResult) {
                throw new BusinessRuleError(checkResult.message);
            }
        });

        xtremDistribution.functions.dateCheckControl(context, {
            discountPaymentBeforeDate: await (await purchaseInvoice.paymentTracking)?.discountPaymentBeforeDate,
            dueDate: await purchaseInvoice.dueDate,
        });

        await context.runInWritableContext(async writableContext => {
            const writablePurchaseInvoice = await writableContext.read(
                xtremPurchasing.nodes.PurchaseInvoice,
                {
                    _id: purchaseInvoice._id,
                },
                { forUpdate: true },
            );

            if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption)) {
                await writablePurchaseInvoice.lines.forEach(async line => {
                    if (await line.landedCost) {
                        await xtremLandedCost.functions.postLib.manageLandedCostAllocationsOnDocumentLinePost(
                            writableContext,
                            line as xtremMasterData.nodes.BaseDocumentLine as xtremLandedCost.interfaces.DocumentLineAllocatingLandedCost,
                        );
                    }
                });
            }

            // The reply of stock and finance updates the status of the invoice
            // => it must be set to 'inProgress' before posting to stock and finance
            // use 'forceUpdateForStock' to avoid the control in saveBegin because it could already be 'error'
            await writablePurchaseInvoice.$.set({
                status: 'inProgress',
                forceUpdateForStock: (await writablePurchaseInvoice.status) === 'error',
            });
            await writablePurchaseInvoice.$.save();
        });

        try {
            postResult.stockPostingResult = await PurchaseInvoice.postToStock(context, [purchaseInvoice._id]);
        } catch (error) {
            // We catch the error here to reset the status of the purchase invoice back to 'draft' and throw the error
            // again to be handled by the caller. Otherwise the status would stay 'inProgress' and the user would not be
            // able to post the invoice again.
            await context.runInWritableContext(async writableContext => {
                const writablePurchaseInvoice = await writableContext.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    {
                        _id: purchaseInvoice._id,
                    },
                    { forUpdate: true },
                );

                await writablePurchaseInvoice.$.set({
                    status: 'draft',
                    forceUpdateForStock: true, // Make sure the update is not refused because of the posting status.
                });
                await writablePurchaseInvoice.$.save();
            });
            throw error;
        }

        const stockPostingResult = postResult.stockPostingResult.length
            ? (JSON.parse(
                  postResult.stockPostingResult,
              ) as xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.correction>)
            : { result: '', error: '' };

        const purchaseID = purchaseInvoice._id;

        if (stockPostingResult.result === 'error') {
            await context.runInWritableContext(async childContext => {
                const invoice = await childContext.read(PurchaseInvoice, { _id: purchaseID }, { forUpdate: true });
                await invoice.$.set({ forceUpdateForFinance: true, status: 'error' });
                await invoice.$.save();
                return {
                    ...postResult,
                    wasSuccessful: false,
                    message: stockPostingResult.error,
                };
            });
        }

        return {
            ...postResult,
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_invoice__posted',
                'The purchase invoice posted.',
            ),
        };
    }

    @decorators.mutation<typeof PurchaseInvoice, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'invoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseInvoice,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
                validationMessages: {
                    type: 'array',
                    isMandatory: false,
                    item: {
                        type: 'object',
                        properties: {
                            message: 'string',
                            lineNumber: { type: 'integer', isMandatory: false },
                            type: {
                                type: 'enum',
                                dataType: () => xtremFinanceData.dataTypes.validationSeverityDataType,
                            },
                            sourceDocumentNumber: 'string',
                        },
                    },
                },
            },
        },
    })
    static async financeIntegrationCheck(
        context: Context,
        invoice: PurchaseInvoice,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        const financeIntegrationCheckResult =
            await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoControlFromNotificationPayloadErrors(
                context,
                invoice,
                'purchaseInvoice',
            );

        if (!financeIntegrationCheckResult.message) {
            financeIntegrationCheckResult.wasSuccessful = true;
        }

        return financeIntegrationCheckResult;
    }

    @decorators.notificationListener<typeof PurchaseInvoice>({
        topic: 'PurchaseInvoice/accountingInterface',
        startsReadOnly: true,
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        // isJustForStatus indicates that the update of finance transaction node was already done
        const shouldUpdatePurchaseInvoiceStatus =
            payload.isJustForStatus ??
            (await context.runInWritableContext(writableContext => {
                return xtremFinanceData.functions.reactToFinanceIntegrationReply(writableContext, payload);
            }));

        if (shouldUpdatePurchaseInvoiceStatus) {
            // we need to update the status of the original Purchase invoice depending on the reply from finance integration
            await context.runInWritableContext(async writableContext => {
                const invoice = await writableContext.read(
                    xtremPurchasing.nodes.PurchaseInvoice,
                    {
                        number: payload.documentNumber,
                    },
                    { forUpdate: true },
                );

                // the financeStatus of the invoice is updated depending on the calculated field financeIntegrationStatus
                await invoice.$.set({
                    financeStatus: xtremFinanceData.functions.statusMapping(
                        await invoice.financeIntegrationStatus,
                        !!payload.financeExternalIntegration,
                    ),
                });
                // TODO: later the status will be a combination of financeStatus and stockTransactionStatus
                // for the moment it is identical to financeStatus
                await invoice.$.set({
                    status: await invoice.financeStatus,
                    forceUpdateForFinance: true, // make sure the update of the Purchase invoice will not be refused by the control in saveBegin
                });
                await invoice.$.save();
            });
        }
    }

    @decorators.query<typeof PurchaseInvoice, 'getRateOrReverseRate'>({
        isPublished: true,
        parameters: [
            { name: 'sourceCurrencyId', type: 'string' },
            { name: 'destinationCurrencyId', type: 'string' },
            { name: 'rateDate', type: 'date' },
        ],
        return: 'decimal',
    })
    static async getRateOrReverseRate(
        context: Context,
        sourceCurrencyId: string,
        destinationCurrencyId: string,
        rateDate: date,
    ): Promise<decimal> {
        const rate = await xtremMasterData.functions.getRateOrReverseRate(
            context,
            sourceCurrencyId,
            destinationCurrencyId,
            rateDate,
        );
        return rate.isRateFound ? rate.rate : 0;
    }

    @decorators.query<typeof PurchaseInvoice, 'getRateOrReverseDivisor'>({
        isPublished: true,
        parameters: [
            { name: 'sourceCurrencyId', type: 'string' },
            { name: 'destinationCurrencyId', type: 'string' },
            { name: 'rateDate', type: 'date' },
        ],
        return: 'decimal',
    })
    static async getRateOrReverseDivisor(
        context: Context,
        sourceCurrencyId: string,
        destinationCurrencyId: string,
        rateDate: date,
    ): Promise<decimal> {
        const rate = await xtremMasterData.functions.getRateOrReverseRate(
            context,
            sourceCurrencyId,
            destinationCurrencyId,
            rateDate,
        );
        return rate.isRateFound ? rate.divisor : 0;
    }

    @decorators.query<typeof PurchaseInvoice, 'rateDescription'>({
        isPublished: true,
        parameters: [
            { name: 'sourceCurrencyId', type: 'string' },
            { name: 'destinationCurrencyId', type: 'string' },
            { name: 'companyFxRate', type: 'decimal' },
            { name: 'companyFxRateDivisor', type: 'decimal' },
        ],
        return: 'string',
    })
    static rateDescription(
        context: Context,
        sourceCurrencyId: string,
        destinationCurrencyId: string,
        companyFxRate: decimal,
        companyFxRateDivisor: decimal,
    ) {
        return xtremMasterData.functions.rateDescription(
            sourceCurrencyId,
            destinationCurrencyId,
            companyFxRate,
            companyFxRateDivisor,
        );
    }

    /* ********************************
    Notification for stock engine
    ******************************** */
    static postToStock(context: Context, purchaseInvoiceIDs: integer[]): Promise<string> {
        return xtremStockData.functions.stockValuationLib.postToStockReceiptCostValueCorrection(context, {
            documentIds: purchaseInvoiceIDs,
            correctorDocumentNode: PurchaseInvoice,
            correctedDocumentNode: xtremPurchasing.nodes.PurchaseReceipt,
            functionToManageLinesWithoutStockUpdate: PurchaseInvoice.manageLinesWithoutStockUpdate,
            functionToGetLinesToCorrect: PurchaseInvoice._getLinesToCorrect,
            replyTopic: 'PurchaseInvoice/stock/correction/reply',
        });
    }

    static readonly _getLinesToCorrect: xtremStockData.interfaces.FunctionToGetLinesToCorrect = async (
        writableContext: Context,
        line: xtremPurchasing.nodes.PurchaseInvoiceLine,
    ) => {
        loggers.invoice.verbose(() => `_getLinesToCorrect line._id=${line._id}`);

        const landedCost = await line.landedCost;
        if (landedCost) {
            const reasonCode = await xtremStockData.functions.reasonCodeLib.getDefaultReasonCode(
                writableContext,
                'landedCostAdjustment',
            );

            await loggers.invoice.verboseAsync(
                async () => `landedCost.allocations=${JSON.stringify(await landedCost.allocations.toArray(), null, 4)}`,
            );
            // At this Step, we know that at least 1 allocation concerns a purchase receipt line with an item stock managed
            // because manageLinesWithoutStockUpdate did a filter
            return {
                reasonCode: reasonCode ?? undefined,
                linesToCorrect: await landedCost.allocations
                    .filter(allocation =>
                        xtremPurchasing.functions.LandedCostLib.isAllocatedDocumentLineToCorrect(allocation),
                    )
                    .map(async allocation => {
                        const allocatedDocumentLine =
                            (await allocation.allocatedDocumentLine) as xtremPurchasing.nodes.PurchaseReceiptLine;
                        return {
                            correctedDocumentLine: allocatedDocumentLine,
                            impactedQuantity: await allocatedDocumentLine.quantityInStockUnit,
                            amountToAbsorb: await allocation.costAmountInCompanyCurrency,
                        };
                    })
                    .toArray(),
            };
        }

        const receiptLine = await (await line.purchaseReceiptLine)?.purchaseReceiptLine;
        if (!receiptLine) return { linesToCorrect: [] };

        return {
            linesToCorrect: [
                {
                    correctedDocumentLine: receiptLine,
                    // XT-46359: The quantity to use in the calculation of the amount to absorb is the quantity of the receipt line
                    impactedQuantity: await receiptLine.quantityInStockUnit,
                    amountToAbsorb:
                        (await receiptLine.quantity) *
                        ((await line.netPrice) - (await receiptLine.netPrice)) *
                        (await (await line.document).getCompanyFxRate()),
                },
            ],
        };
    };

    static async manageLinesWithoutStockUpdate(readOnlyContext: Context, purchaseInvoiceID: number): Promise<number[]> {
        loggers.invoice.verbose(() => `manageLinesWithoutStockUpdate purchaseInvoiceID=${purchaseInvoiceID}`);

        const invoice = await readOnlyContext.read(PurchaseInvoice, { _id: purchaseInvoiceID });

        const lineResults: xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines'] =
            await invoice.lines
                .filter(async line => {
                    if (['completed', 'inProgress'].includes(await line.stockTransactionStatus)) return false;

                    const landedCost = await line.landedCost;
                    const receiptLine = await (await line.purchaseReceiptLine)?.purchaseReceiptLine;
                    // If the line is linked to a landed cost and the stock transaction status is in error,
                    // we check if the landed cost is allocated to at least 1 document line to correct that is in error.
                    // If not, it means the error has been fixed (post of the receipt directly) and the line can be skipped
                    if (landedCost && (await line.stockTransactionStatus) === 'error') {
                        const hasAllocationForReceiptInError = await landedCost.allocations.some(async allocation => {
                            const isAllocationForReceipt =
                                await xtremPurchasing.functions.LandedCostLib.isAllocatedDocumentLineToCorrect(
                                    allocation,
                                );
                            if (!isAllocationForReceipt) return false;
                            const allocatedReceiptLine =
                                (await allocation.allocatedDocumentLine) as xtremPurchasing.nodes.PurchaseReceiptLine;
                            return (await allocatedReceiptLine.stockTransactionStatus) === 'error';
                        });
                        return !hasAllocationForReceiptInError;
                    }
                    // The line is skipped if the item is not stock managed or if there is no variance
                    // or if the landed cost is not allocated to a document line to correct
                    if (!landedCost && !receiptLine) return true;
                    if (
                        receiptLine &&
                        (await xtremStockData.functions.stockDocumentLib.shouldSkipStockManagementForThisLine(line))
                    )
                        return true;
                    if (receiptLine && (await line.netPrice) - (await receiptLine.netPrice) === 0) return true;
                    if (
                        !!landedCost &&
                        !(await xtremPurchasing.functions.LandedCostLib.existAllocatedDocumentLineToCorrect(landedCost))
                    )
                        return true;

                    return false;
                })
                .map(async line => {
                    return {
                        id: line._id,
                        sortValue: await line._sortValue,
                        stockUpdateResultStatus: 'none' as xtremStockData.enums.StockUpdateResultAction,
                        originLineIds: [],
                        stockJournalRecords: [],
                    };
                })
                .toArray();
        if (lineResults.length > 0)
            await PurchaseInvoice.manageStockReply(readOnlyContext, {
                requestNotificationId: 'none',
                movementHasBeenSkipped: true,
                updateResults: { correction: { documents: [{ id: invoice._id, lines: lineResults }] } },
            });
        const res = lineResults.map(lineResult => lineResult.id);
        loggers.invoice.verbose(() => ` -> lineResults=${JSON.stringify(res, null, 4)}`);
        return res;
    }

    @decorators.notificationListener<typeof PurchaseInvoice>({
        startsReadOnly: true,
        topic: 'PurchaseInvoice/stock/correction/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(context, envelope, error, PurchaseInvoice);
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ): Promise<void> {
        // as the reply concerns purchase receipts we need to adapt the reply payload to the purchase invoice
        const correctionPayload = (
            await xtremStockData.functions.notificationLib.stockCorrectionReplyToOrigin(readOnlyContext, payload, [
                {
                    lineClass: xtremPurchasing.nodes.PurchaseInvoiceLine,
                    headerClass: xtremPurchasing.nodes.PurchaseInvoice,
                },
            ])
        )[0];
        if (!correctionPayload) return;

        await PurchaseInvoice.manageStockReply(readOnlyContext, correctionPayload, payload);
    }

    static async manageStockReply(
        readOnlyContext: Context,
        correctionPayload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
        stockReplyPayload?: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.correction>,
    ) {
        loggers.invoice.verbose(
            () => `manageStockReply correctionPayload=${JSON.stringify(correctionPayload, null, 4)}`,
        );
        loggers.invoice.verbose(
            () => `manageStockReply stockReplyPayload=${JSON.stringify(stockReplyPayload, null, 4)}`,
        );

        const correctionUpdateResult = await readOnlyContext.runInWritableContext(async writableContext => {
            // reactToStockMovementReply for the invoice must be called before the one for the receipt
            // otherwise a "deadlock" error can occur (2nd post with stock error on the 1st line)
            const reactInvoiceResult = (
                await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    correctionPayload,
                    PurchaseInvoice,
                )
            ).correction;
            if (stockReplyPayload) {
                await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                    writableContext,
                    stockReplyPayload,
                    xtremPurchasing.nodes.PurchaseReceipt,
                    {
                        beforeDocumentSave: xtremPurchasing.nodes.PurchaseReceipt.updateHeaderAfterStockUpdate,
                    },
                );
            }
            return reactInvoiceResult;
        });
        if (!correctionUpdateResult) return;

        await readOnlyContext.runInWritableContext(async writableContext => {
            await correctionUpdateResult.documents.forEach(async document => {
                const purchaseInvoice = await xtremMasterData.functions.getWritableNode(
                    writableContext,
                    PurchaseInvoice,
                    document.id,
                );
                if (correctionUpdateResult.transactionHadAnError) {
                    await purchaseInvoice.$.set({
                        status: 'error',
                        forceUpdateForFinance: true, // make sure the update of the Purchase invoice will not be refused by the control in saveBegin
                    });
                    await purchaseInvoice.$.save();
                } else if (document.isStockDocumentCompleted) {
                    await PurchaseInvoice.onceStockCompleted(writableContext, purchaseInvoice);
                }
            });
        });
    }

    static async onceStockCompleted(writableContext: Context, purchaseInvoice: PurchaseInvoice): Promise<void> {
        await purchaseInvoice.$.set({
            paymentTracking: {
                paymentTerm: await purchaseInvoice.paymentTerm,
            },
            status: 'posted',
            forceUpdateForFinance: true, // make sure the update of the Purchase invoice will not be refused by the control in saveBegin
        });
        await purchaseInvoice.$.save();

        // send notification in order to create staging table entries for the accounting engine
        await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoNotification(
            writableContext,
            purchaseInvoice as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
            (await purchaseInvoice.lines.toArray()) as xtremFinanceData.interfaces.InvoiceDocumentLine[],
            'purchaseInvoice',
            'PurchaseInvoice/accountingInterface',
        );
    }

    @decorators.mutation<typeof PurchaseInvoice, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'purchaseInvoice', type: 'reference', isMandatory: true, node: () => PurchaseInvoice }],
        return: {
            type: 'boolean',
        },
    })
    static async resendNotificationForFinance(context: Context, purchaseInvoice: PurchaseInvoice): Promise<boolean> {
        loggers.invoice.info(
            context.localize(
                '@sage/xtrem-purchasing/node__purchase_invoice__resend_notification_for_finance',
                'Regenerating finance notification for purchase invoice number: {{documentNumber}}.',
                { documentNumber: await purchaseInvoice.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await purchaseInvoice.number,
                documentType: 'purchaseInvoice',
            })
        ) {
            // send notification in order to create a staging table entry for the accounting engine
            await xtremPurchasing.functions.FinanceIntegration.purchaseInvoiceCreditMemoNotification(
                context,
                purchaseInvoice as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
                (await purchaseInvoice.lines.toArray()) as xtremFinanceData.interfaces.InvoiceDocumentLine[],
                'purchaseInvoice',
                'PurchaseInvoice/accountingInterface',
            );
        }

        return true;
    }

    @decorators.notificationListener<typeof PurchaseInvoice>({
        topic: 'PurchaseInvoice/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const purchaseInvoice = await context.read(PurchaseInvoice, { number: document.number });

        await PurchaseInvoice.resendNotificationForFinance(context, purchaseInvoice);
    }

    /**
     * Method creates a purchase credit memo from your purchase invoice
     * @param context
     * @param purchaseInvoiceNumber
     */
    @decorators.mutation<typeof PurchaseInvoice, 'createCreditMemoFromInvoice'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => PurchaseInvoice, isMandatory: true },
            { name: 'reasonCode', type: 'reference', node: () => xtremMasterData.nodes.ReasonCode, isMandatory: true },
            { name: 'totalAmountExcludingTax', type: 'decimal', isMandatory: true },
            { name: 'supplierDocumentDate', type: 'date', isMandatory: true },
        ],
        return: {
            type: 'reference',
            node: () => xtremPurchasing.nodes.PurchaseCreditMemo,
        },
    })
    static async createCreditMemoFromInvoice(
        context: Context,
        document: PurchaseInvoice,
        reasonCode: xtremMasterData.nodes.ReasonCode,
        totalAmountExcludingTax: decimal,
        supplierDocumentDate: date,
    ): Promise<xtremPurchasing.nodes.PurchaseCreditMemo> {
        await loggers.invoice.debugAsync(async () => `createCreditMemo invoiceNumber=${await document.number}`);

        if (['draft'].includes(await document.status)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase-invoice__cant_create_credit_memo',
                    'You need to post the purchase invoice before you create the credit memo.',
                ),
            );
        }

        const purchaseCreditMemo = await xtremPurchasing.functions.PurchaseInvoiceLib.initPurchaseCreditMemoCreateData(
            document,
            reasonCode,
            totalAmountExcludingTax,
            supplierDocumentDate,
        );

        return xtremPurchasing.functions.PurchaseInvoiceLib.createPurchaseCreditMemo(context, purchaseCreditMemo);
    }

    // Repost creates an update notification to the acc engine ir order to update att and dimenions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof PurchaseInvoice, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseInvoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseInvoice,
            },
            {
                name: 'documentData',
                type: 'object',
                isMandatory: true,
                properties: {
                    header: {
                        type: 'object',
                        properties: {
                            supplierDocumentNumber: 'string',
                            paymentData: {
                                type: 'object',
                                properties: {
                                    supplierDocumentDate: 'date',
                                    paymentTerm: { type: 'reference', node: () => xtremMasterData.nodes.PaymentTerm },
                                },
                            },
                            totalTaxAmount: 'decimal',
                            taxes: {
                                type: 'array',
                                item: {
                                    type: 'instance',
                                    isTransientInput: true,
                                    node: () => xtremTax.nodes.DocumentTax,
                                },
                            },
                        },
                    },
                    lines: {
                        type: 'array',
                        item: {
                            type: 'object',
                            properties: {
                                baseDocumentLineSysId: 'integer',
                                storedAttributes: 'json',
                                storedDimensions: 'json',
                                uiTaxes: 'string',
                            },
                        },
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static repost(
        context: Context,
        purchaseInvoice: PurchaseInvoice,
        documentData: xtremPurchasing.interfaces.financeIntegration.PurchaseInvoiceCreditMemoRepost,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremPurchasing.functions.PurchaseInvoiceLib.repost(context, purchaseInvoice, documentData);
    }

    @decorators.mutation<typeof PurchaseInvoice, 'resynchronizeDisplayStatus'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseInvoice',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseInvoice,
            },
        ],
        return: {
            type: 'object',
            properties: {
                oldDisplayStatus: {
                    type: 'enum',
                    dataType: () => xtremPurchasing.enums.purchaseInvoiceDisplayStatusDataType,
                },
                newDisplayStatus: {
                    type: 'enum',
                    dataType: () => xtremPurchasing.enums.purchaseInvoiceDisplayStatusDataType,
                },
            },
        },
    })
    static async resynchronizeDisplayStatus(
        _context: Context,
        purchaseInvoice: PurchaseInvoice,
    ): Promise<{
        oldDisplayStatus: xtremPurchasing.enums.PurchaseInvoiceDisplayStatus;
        newDisplayStatus: xtremPurchasing.enums.PurchaseInvoiceDisplayStatus;
    }> {
        let status = await purchaseInvoice.status;
        const taxCalculationStatus = await purchaseInvoice.taxCalculationStatus;
        const stockTransactionStatus = await purchaseInvoice.stockTransactionStatus;
        const matchingStatus = await purchaseInvoice.matchingStatus;
        const allFinancePosted = await purchaseInvoice.postingDetails.every(
            async detail => (await detail.postingStatus) === 'posted',
        );

        loggers.invoice.verbose(
            () =>
                `resynchronizeDisplayStatus ${JSON.stringify(
                    { status, taxCalculationStatus, stockTransactionStatus, matchingStatus, allFinancePosted },
                    null,
                    4,
                )} `,
        );

        if (
            status === 'inProgress' &&
            taxCalculationStatus === 'done' &&
            stockTransactionStatus === 'completed' &&
            allFinancePosted
        ) {
            status = 'posted';
            loggers.invoice.verbose(() => `resynchronizeDisplayStatus status 'inProgress' -> 'posted'`);
            await purchaseInvoice.$.set({ status, financeStatus: 'posted', forceUpdateForResync: true });
            await purchaseInvoice.$.save();
        }

        const oldDisplayStatus = await purchaseInvoice.displayStatus;
        const newDisplayStatus = xtremPurchasing.functions.PurchaseInvoiceLib.calculatePurchaseInvoiceDisplayStatus(
            status,
            taxCalculationStatus,
            stockTransactionStatus,
            matchingStatus,
            (await (await purchaseInvoice.paymentTracking)?.status) ?? null,
        );
        loggers.invoice.verbose(
            () =>
                `resynchronizeDisplayStatus oldDisplayStatus=${oldDisplayStatus} newDisplayStatus=${newDisplayStatus}`,
        );
        if (oldDisplayStatus !== newDisplayStatus) {
            await purchaseInvoice.$.set({ displayStatus: newDisplayStatus, forceUpdateForResync: true });
            await purchaseInvoice.$.save();
        }

        return { oldDisplayStatus, newDisplayStatus };
    }

    // Listener to update the display status of a purchase invoice, when the open item is paid or partially paid in finance
    @decorators.notificationListener<typeof PurchaseInvoice>({
        topic: 'PurchaseInvoice/updatePaymentStatus',
        startsReadOnly: true,
    })
    static async setPurchaseInvoiceDisplayStatus(
        readOnlyContext: Context,
        payload: { _id: number; paymentStatus: xtremFinanceData.enums.OpenItemStatus },
    ) {
        await readOnlyContext.runInWritableContext(async context => {
            const purchaseInvoiceToUpdate = await context.read(
                PurchaseInvoice,
                { _id: payload._id },
                { forUpdate: true },
            );
            const displayStatus = xtremPurchasing.functions.PurchaseInvoiceLib.calculatePurchaseInvoiceDisplayStatus(
                await purchaseInvoiceToUpdate.status,
                await purchaseInvoiceToUpdate.taxCalculationStatus,
                await purchaseInvoiceToUpdate.stockTransactionStatus,
                await purchaseInvoiceToUpdate.matchingStatus,
                payload.paymentStatus,
            );
            await purchaseInvoiceToUpdate.$.set({
                forceUpdateForFinance: true,
                displayStatus,
            });
            await purchaseInvoiceToUpdate.$.save();
        });
    }

    // Listener to create the payment tracking for each purchase invoice or credit memo with a finance transaction record and
    // add a reference to it to the finance transaction record and notify the AP invoice if it is already created.
    // It is called when the service option paymentTrackingOption is activated
    @decorators.notificationListener<typeof PurchaseInvoice>({
        topic: 'SysServiceOptionState/paymentTrackingOption/activate',
    })
    static async paymentTrackingOptionActivate(context: Context): Promise<void> {
        await initPaymentTracking(context);
    }
}
