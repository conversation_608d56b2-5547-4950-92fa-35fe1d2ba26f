import type { Reference, date, decimal } from '@sage/xtrem-core';
import { Node, decorators } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

@decorators.node<UnbilledAccountPayableResultLine>({
    storage: 'sql',
    isPublished: true,
    canRead: true,
    canSearch: true,
    isVitalCollectionChild: true,
})
export class UnbilledAccountPayableResultLine extends Node {
    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'inputSet'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.UnbilledAccountPayableInputSet,
    })
    readonly inputSet: Reference<xtremPurchasing.nodes.UnbilledAccountPayableInputSet>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'supplier'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'currency'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'financialSite'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        ignoreIsActive: true,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'purchaseUnit'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
    })
    readonly purchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'netPrice'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly netPrice: Promise<decimal>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'item'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Item,
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'stockSite'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        node: () => xtremSystem.nodes.Site,
        ignoreIsActive: true,
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'quantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly quantity: Promise<decimal>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'account'>({
        isPublished: true,
        isStored: true,
        node: () => xtremFinanceData.nodes.Account,
        ignoreIsActive: true,
        isNullable: true,
    })
    readonly account: Reference<xtremFinanceData.nodes.Account> | null;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'accountItem'>({
        isPublished: true,
        isStored: true,
        node: () => xtremFinanceData.nodes.Account,
        ignoreIsActive: true,
        isNullable: true,
    })
    readonly accountItem: Reference<xtremFinanceData.nodes.Account> | null;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'invoicedQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly invoicedQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'creditedQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly creditedQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'returnedQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly returnedQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'invoiceReceivableQuantity'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
    })
    readonly invoiceReceivableQuantity: Promise<decimal>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'invoiceReceivableAmount'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly invoiceReceivableAmount: Promise<decimal>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'company'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        ignoreIsActive: true,
        provides: ['company'],
        async getValue() {
            return (await this.financialSite).legalCompany;
        },
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.stringProperty<UnbilledAccountPayableResultLine, 'receiptNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.propertyDataTypePurchasing,
    })
    readonly receiptNumber: Promise<string>;

    // Note: - Even if the property is filled with an internal ID, we need it in the page as a reference to enable
    //         tunneling. The internal ID (_id) is stored as a number.
    //       - We can accept this here for we need this data only for showing data in the inquiry page.
    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'receiptInternalId'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremPurchasing.nodes.PurchaseReceipt,
    })
    readonly receiptInternalId: Reference<xtremPurchasing.nodes.PurchaseReceipt> | null;

    @decorators.dateProperty<UnbilledAccountPayableResultLine, 'documentDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly documentDate: Promise<date | null>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'billBySupplier'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        ignoreIsActive: true,
        node: () => xtremMasterData.nodes.Supplier,
    })
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'invoiceReceivableAmountInCompanyCurrency'>({
        isPublished: true,
        isStored: true,
        isRequired: false,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
    })
    readonly invoiceReceivableAmountInCompanyCurrency: Promise<decimal | null>;

    @decorators.decimalProperty<UnbilledAccountPayableResultLine, 'invoiceReceivableAmountInCompanyCurrencyAtAsOfDate'>(
        {
            isPublished: true,
            isStored: true,
            isRequired: false,
            isNullable: true,
            dataType: () => xtremMasterData.dataTypes.priceInSalesPrice,
        },
    )
    readonly invoiceReceivableAmountInCompanyCurrencyAtAsOfDate: Promise<decimal | null>;

    @decorators.referenceProperty<UnbilledAccountPayableResultLine, 'companyCurrency'>({
        isStored: true,
        isPublished: true,
        isRequired: false,
        isNullable: true,
        node: () => xtremMasterData.nodes.Currency,
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency | null>;
}
