import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../index';

const mayNotDeleteStatus = ['posted', 'inProgress', 'error'];
@decorators.node<PurchaseOrderLineToPurchaseInvoiceLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { purchaseOrderLine: 1, purchaseInvoiceLine: 1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        await xtremPurchasing.functions.BaseDocument.updateLineToStatuses(this);
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.OrderLineToInvoiceLine.sameProperties(this, cx);
    },
    async controlDelete(cx) {
        if (mayNotDeleteStatus.includes(await (await (await this.purchaseInvoiceLine).document).status)) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_order_purchase_invoice_line__deletion_forbidden_reason_status',
                'The invoice line cannot be deleted as it is in a posted status.',
            );
        }
    },
    async deleteEnd() {
        await xtremPurchasing.functions.BaseDocument.updateLineToStatuses(this);
    },
})
export class PurchaseOrderLineToPurchaseInvoiceLine extends Node {
    @decorators.referenceProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'purchaseInvoiceLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLine,
        isVitalParent: true,
        lookupAccess: true,
    })
    readonly purchaseInvoiceLine: Reference<xtremPurchasing.nodes.PurchaseInvoiceLine>;

    @decorators.referenceProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'purchaseOrderLine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLine,
    })
    readonly purchaseOrderLine: Reference<xtremPurchasing.nodes.PurchaseOrderLine>;

    @decorators.referenceProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: [{ purchaseInvoiceLine: ['unit'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).unit;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'invoicedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseInvoiceLine: ['quantity'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).quantity;
        },
    })
    readonly invoicedQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: [{ purchaseInvoiceLine: ['stockUnit'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async getValue() {
            return (await this.purchaseInvoiceLine).unitToStockUnitConversionFactor;
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        dependsOn: [{ purchaseInvoiceLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'invoicedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseInvoiceLine: ['quantityInStockUnit'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).quantityInStockUnit;
        },
    })
    readonly invoicedQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'invoicedUnitPrice'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.price,
        dependsOn: [{ purchaseInvoiceLine: ['grossPrice'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).grossPrice;
        },
    })
    readonly invoicedUnitPrice: Promise<decimal>;

    @decorators.referenceProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'currency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ purchaseInvoiceLine: ['currency'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseInvoiceLine, 'invoicedAmount'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingPriceDataType,
        dependsOn: [{ purchaseInvoiceLine: ['amountExcludingTax', 'currency'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).amountExcludingTax;
        },
    })
    readonly invoicedAmount: Promise<decimal>;
}
