import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { loggers, logStatusLine, orderLineToPurchaseLineControlEnd } from '../functions';
import { manageStatus } from '../functions/order-line';
import * as xtremPurchasing from '../index';

@decorators.node<PurchaseOrderLineToPurchaseReceiptLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [
        { orderBy: { purchaseReceiptLine: 1, purchaseOrderLine: 1 }, isUnique: true },
        { orderBy: { purchaseOrderLine: 1 } },
    ],
    async deleteBegin() {
        const purchaseOrderLine = await this.updatePurchaseOrder();

        if (purchaseOrderLine) {
            const workInProgress = await purchaseOrderLine.workInProgress;
            if (workInProgress?._id)
                await xtremMasterData.nodes.WorkInProgress.updateQuantities(
                    this.$.context,
                    workInProgress._id,
                    await (
                        await this.purchaseReceiptLine
                    ).quantityInStockUnit,
                );
        }
    },
    async saveEnd() {
        const purchaseOrderLine = await this.updatePurchaseOrder();

        // as the WorkInProgressPurchaseOrderLine record is not in the same vital graph, the update must be done explicitly
        if (purchaseOrderLine) {
            const workInProgress = await purchaseOrderLine.workInProgress;
            if (workInProgress?._id) {
                await xtremMasterData.nodes.WorkInProgress.updateQuantities(this.$.context, workInProgress._id);
            }
        }
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.OrderLineToReceiptLine.sameProperties(this, cx);
        await orderLineToPurchaseLineControlEnd(loggers.receipt, this);
    },
    async deleteEnd() {
        const purchaseOrderLine = await this.purchaseOrderLine;
        const purchaseOrder = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseOrder,
            { _id: (await (await this.purchaseOrderLine).document)._id },
            { forUpdate: true },
        );

        if (purchaseOrderLine) {
            let newLineReceiptStatus: xtremPurchasing.enums.PurchaseOrderReceiptStatus = 'notReceived';
            let newLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus = 'pending';
            if ((await purchaseOrderLine.receivedQuantityInStockUnit) - (await this.receivedQuantityInStockUnit) > 0) {
                newLineReceiptStatus = 'partiallyReceived';
                newLineStatus = 'inProgress';
            }
            await purchaseOrderLine.$.set({ lineReceiptStatus: newLineReceiptStatus, status: newLineStatus });
            await purchaseOrder.$.save({ deferred: true });
        }
    },
})
export class PurchaseOrderLineToPurchaseReceiptLine extends Node {
    @decorators.referenceProperty<PurchaseOrderLineToPurchaseReceiptLine, 'purchaseReceiptLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLine,
        lookupAccess: true,
    })
    readonly purchaseReceiptLine: Reference<xtremPurchasing.nodes.PurchaseReceiptLine>;

    @decorators.referenceProperty<PurchaseOrderLineToPurchaseReceiptLine, 'purchaseOrderLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLine,
        lookupAccess: true,
    })
    readonly purchaseOrderLine: Reference<xtremPurchasing.nodes.PurchaseOrderLine>;

    @decorators.referenceProperty<PurchaseOrderLineToPurchaseReceiptLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: { control: { type: { _nin: ['time', 'temperature'] } } },
        dependsOn: [{ purchaseOrderLine: ['unit'] }],
        async getValue() {
            return (await this.purchaseOrderLine).unit;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseReceiptLine, 'receivedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseReceiptLine: ['quantity'] }],
        async getValue() {
            return (await this.purchaseReceiptLine).quantity;
        },
    })
    readonly receivedQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseOrderLineToPurchaseReceiptLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseOrderLine: ['stockUnit'] }],
        async getValue() {
            return (await this.purchaseOrderLine).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseReceiptLine, 'receivedQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseReceiptLine: ['quantityInStockUnit'] }],
        async getValue() {
            return (await this.purchaseReceiptLine).quantityInStockUnit;
        },
    })
    readonly receivedQuantityInStockUnit: Promise<decimal>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseOrderLineToPurchaseReceiptLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async getValue() {
            return (await this.purchaseOrderLine).unitToStockUnitConversionFactor;
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseOrderLineToPurchaseReceiptLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseOrderLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseOrderLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;

    async updatePurchaseOrder(): Promise<xtremPurchasing.nodes.PurchaseOrderLine | null> {
        const purchaseOrder = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseOrder,
            { _id: (await (await this.purchaseOrderLine).document)._id },
            { forUpdate: true },
        );
        const purchaseOrderLineId = (await this.purchaseOrderLine)._id; // Performance: Value is invariant.
        const purchaseOrderLine = await purchaseOrder.lines.find(line => line._id === purchaseOrderLineId);

        if (!purchaseOrderLine || !(await purchaseOrderLine.purchaseReceiptLines.length)) {
            return null;
        }

        // The order line may have been closed directly
        if ((await purchaseOrderLine?.status) === 'closed') {
            return purchaseOrderLine;
        }
        const { isUsingFunctionToClose, lineReceiptStatus, status } = await manageStatus(purchaseOrderLine);

        if (
            lineReceiptStatus !== (await purchaseOrderLine.lineReceiptStatus) ||
            status !== (await purchaseOrderLine.status) ||
            this.$.status === NodeStatus.added ||
            this.$.status === NodeStatus.deleted
        ) {
            await loggers.orderToReceipt.debugAsync(
                async () => `
                new receipt status ${lineReceiptStatus} - old: ${await purchaseOrderLine.lineReceiptStatus}
                new status ${status} - old: ${await purchaseOrderLine.status}
            `,
            );
            await purchaseOrder.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: purchaseOrderLine._id,
                        lineReceiptStatus,
                        status,
                        isUsingFunctionToClose,
                    },
                ],
            });
            await purchaseOrder.$.save({ deferred: true });
        }
        await logStatusLine(loggers.orderToReceipt, purchaseOrderLine);
        return purchaseOrderLine;
    }
}
