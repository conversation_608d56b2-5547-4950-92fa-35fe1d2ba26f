import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { Collection, Context, date, decimal, integer, Reference, TextStream } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    decorators,
    LogicError,
    NodeStatus,
    registerSqlFunction,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremPurchasing from '../index';
import { BasePurchaseDocumentLine } from './base-document-nodes/index';

@decorators.subNode<PurchaseInvoiceLine>({
    isPublished: true,
    canSearch: true,
    canRead: true,
    extends: () => BasePurchaseDocumentLine,
    async controlBegin(cx) {
        await xtremPurchasing.events.controls.InvoiceLine.checkLineStatus(cx, this);
        await xtremPurchasing.events.controls.documentIsPosted(await this.document, cx);
    },
    async createEnd() {
        await xtremPurchasing.functions.PurchaseInvoiceLib.updateLineNotesOnCreation(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class PurchaseInvoiceLine
    extends BasePurchaseDocumentLine
    implements
        xtremFinanceData.interfaces.InvoiceDocumentLine,
        xtremLandedCost.interfaces.DocumentLineAllocatingLandedCost
{
    override getItem() {
        return this.item;
    }

    @decorators.referencePropertyOverride<PurchaseInvoiceLine, 'document'>({
        node: () => xtremPurchasing.nodes.PurchaseInvoice,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseInvoice>;

    @decorators.enumPropertyOverride<PurchaseInvoiceLine, 'origin'>({
        dependsOn: ['purchaseReceiptLine', 'purchaseReturnLines', 'purchaseOrderLine'],
        async defaultValue() {
            if ((await this.purchaseReceiptLine) != null) {
                return 'purchaseReceipt';
            }
            if ((await this.purchaseReturnLines.length) > 0) {
                return 'purchaseReturn';
            }
            if ((await this.purchaseOrderLine) != null) {
                return 'purchaseOrder';
            }
            return 'direct';
        },
    })
    override readonly origin: Promise<xtremPurchasing.enums.PurchaseDocumentLineOrigin>;

    @decorators.referencePropertyOverride<PurchaseInvoiceLine, 'site'>({
        isFrozen: true,
        filters: {
            control: {
                isFinance: true,
            },
        },
        dependsOn: [{ document: ['site'] }],

        async defaultValue() {
            return (await this.document).site;
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<PurchaseInvoiceLine, 'matchingStatus'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['origin', 'varianceType'],
        async defaultValue() {
            if ((await this.origin) === 'direct') {
                return 'variance';
            }

            return (await this.varianceType) === 'noVariance' ? 'noVariance' : 'variance';
        },
        updatedValue: useDefaultValue,
        dataType: () => xtremPurchasing.enums.purchaseInvoiceMatchingStatusDataType,
    })
    readonly matchingStatus: Promise<xtremPurchasing.enums.PurchaseInvoiceMatchingStatus>;

    @decorators.referencePropertyOverride<PurchaseInvoiceLine, 'item'>({
        dependsOn: ['origin', 'recipientSite'],
        filters: {
            control: {
                isBought: true,
                itemSites() {
                    return { _atLeast: 1, site: this.recipientSite };
                },
                async isStockManaged() {
                    if ((await this.origin) === 'direct') {
                        return false;
                    }
                    return { _in: [true, false] };
                },
                async type() {
                    if (await this.$.context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption)) {
                        return { _in: ['service', 'good', 'landedCost'] };
                    }
                    return { _in: ['service', 'good'] };
                },
            },
        },
        async control(cx, item) {
            if ((await this.status) !== 'closed') {
                await xtremMasterData.functions.controls.item.inactiveItemControl(cx, item);
            }

            const receiptLine = await this.purchaseReceiptLine;
            await xtremPurchasing.events.controls.itemUpdateValidation(
                this,
                await receiptLine?.purchaseReceiptLine,
                item,
                cx,
            );
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.enumPropertyOverride<PurchaseInvoiceLine, 'priceOrigin'>({
        dependsOn: [
            'grossPrice',
            { document: ['site', 'billBySupplier', 'currency', 'invoiceDate'] },
            'item',
            'quantity',
            'unit',
        ],

        async defaultValue() {
            const document = await this.document;
            const price = await xtremMasterData.functions.getPurchasePrice(this.$.context, {
                site: await document.site,
                supplier: await document.billBySupplier,
                currency: await document.currency,
                item: await this.item,
                quantity: await this.quantity,
                unit: await this.unit,
                date: await document.invoiceDate,
                convertUnit: false,
            });

            return price >= 0 ? 'supplierPriceList' : null;
        },
    })
    override readonly priceOrigin: Promise<xtremPurchasing.enums.PriceOrigin | null>;

    @decorators.referencePropertyOverride<PurchaseInvoiceLine, 'unit'>({
        async control(cx, val) {
            const receiptLine = await this.purchaseReceiptLine;
            await xtremPurchasing.events.controls.purchaseUnitUpdateValidation(this, receiptLine, val, cx);
        },
        dependsOn: ['item', 'stockUnit', { document: ['billBySupplier'] }, 'origin'],
        async defaultValue() {
            return (await this.origin) === 'direct'
                ? xtremMasterData.nodes.UnitOfMeasure.getPurchaseUnit(
                      this.$.context,
                      await this.item,
                      await (
                          await this.document
                      ).billBySupplier,
                  )
                : null;
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referencePropertyOverride<PurchaseInvoiceLine, 'stockUnit'>({
        dependsOn: ['item'],
        async isFrozen() {
            return (await (await this.item).stockUnit)._id !== (await this.stockUnit)._id;
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<PurchaseInvoiceLine, 'quantityInStockUnit'>({
        dependsOn: [
            'item',
            'unit',
            'stockUnit',
            'quantity',
            'unitToStockUnitConversionFactor',
            'origin',
            'purchaseReceiptLine',
        ],
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.quantity,
                await this.unit,
                await this.stockUnit,
                true,
                !!(await this.purchaseOrderLine) || !!(await this.purchaseReceiptLine),
                await this._isConversionFactorFrozen,
                this.$.status,
                await this.unitToStockUnitConversionFactor,
                await this.item,
                await (
                    await this.document
                ).billBySupplier,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseInvoiceLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', 'unit', 'stockUnit', 'purchaseReceiptLine'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async defaultValue() {
            return xtremMasterData.functions.getConvertCoefficient(await this.unit, await this.stockUnit);
        },
        async updatedValue() {
            if (
                (this.$.status === NodeStatus.added && (await this.purchaseReceiptLine)) ||
                !(await this._isConversionFactorFrozen)
            ) {
                return xtremMasterData.functions.getConvertCoefficient(await this.unit, await this.stockUnit);
            }
            return this.unitToStockUnitConversionFactor;
        },
        isFrozen() {
            return this._isConversionFactorFrozen;
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.datePropertyOverride<PurchaseInvoiceLine, 'taxDate'>({
        dependsOn: [
            { document: ['supplierDocumentDate'] },
            { purchaseReceiptLine: [{ purchaseReceiptLine: [] }] },
            { purchaseReceiptLine: [{ purchaseReceiptLine: ['taxDate'] }] },
        ],
        defaultValue() {
            return this.getTaxDateDefaultValue();
        },
        updatedValue: useDefaultValue,
    })
    override readonly taxDate: Promise<date>;

    @decorators.referenceProperty<PurchaseInvoiceLine, 'recipientSite'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        lookupAccess: true,
        filters: {
            control: {
                async itemSites() {
                    return { _atLeast: 1, item: await this.item };
                },
                async legalCompany() {
                    return (await this.site)?.legalCompany;
                },
                _or: [{ isInventory: true }, { isPurchase: true }],
            },
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
        dependsOn: ['document', 'site', { document: ['site'] }],
        async defaultValue() {
            if ((await (await this.site).isPurchase) || (await (await this.site).isInventory)) {
                return (await this.document).site;
            }
            return null;
        },
        async control(cx, val) {
            const receiptLine = await this.purchaseReceiptLine;
            await xtremPurchasing.events.controls.recipientSiteValidation(val, cx);
            await xtremPurchasing.events.controls.matchingSiteValidation(this, val, receiptLine, cx);
        },
    })
    readonly recipientSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseInvoiceLine, 'stockSite'>({
        dependsOn: ['recipientSite', { itemSite: ['site', 'stockSite'] }, { site: ['stockSite'] }],
        updatedValue: useDefaultValue,
        async defaultValue() {
            return (
                (await this.recipientSite) ??
                (await (await this.itemSite)?.site)?.stockSite ??
                (await this.site).stockSite
            );
        },
        isFrozen() {
            return this._isLinePropertyFrozen('stockSite');
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<PurchaseInvoiceLine, 'purchaseReceiptLine'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine,
        isNullable: true,
        isVital: true,
        reverseReference: 'purchaseInvoiceLine',
    })
    readonly purchaseReceiptLine: Reference<xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine | null>;

    @decorators.collectionProperty<PurchaseInvoiceLine, 'purchaseReturnLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLineToPurchaseInvoiceLine,
        isVital: true,
        reverseReference: 'purchaseInvoiceLine',
    })
    readonly purchaseReturnLines: Collection<xtremPurchasing.nodes.PurchaseReturnLineToPurchaseInvoiceLine>;

    @decorators.stringProperty<PurchaseInvoiceLine, 'sourceDocumentNumber'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async getValue() {
            const receiptLine = await this.purchaseReceiptLine;
            return (await this.origin) === 'purchaseReceipt' && receiptLine != null
                ? (await (await receiptLine.purchaseReceiptLine).document).number
                : '';
        },
    })
    readonly sourceDocumentNumber: Promise<string>;

    @decorators.enumProperty<PurchaseInvoiceLine, 'sourceDocumentType'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        async getValue() {
            return (await this.origin) === 'purchaseReceipt' && (await this.purchaseReceiptLine) != null
                ? 'purchaseReceipt'
                : null;
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;

    // needed for the accounting engine
    @decorators.integerProperty<PurchaseInvoiceLine, 'sourceDocumentSysId'>({
        isPublished: true,
        async getValue() {
            const receiptLine = await this.purchaseReceiptLine;
            return (await this.origin) === 'purchaseReceipt' && receiptLine != null
                ? (await (await receiptLine.purchaseReceiptLine).document)._id
                : 0;
        },
    })
    readonly sourceDocumentSysId: Promise<integer>;

    @decorators.enumProperty<PurchaseInvoiceLine, 'varianceType'>({
        isPublished: true,
        dataType: () => xtremPurchasing.enums.PurchaseInvoiceVarianceTypeDataType,
        dependsOn: ['origin', 'grossPrice', 'quantityInStockUnit'],
        getValue() {
            return PurchaseInvoiceLine.getVarianceType(this);
        },
    })
    readonly varianceType: Promise<xtremPurchasing.enums.PurchaseInvoiceVarianceType>;

    @decorators.jsonProperty<PurchaseInvoiceLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['taxes'],
        lookupAccess: true,
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremPurchasing.nodes.PurchaseInvoiceLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.decimalPropertyOverride<PurchaseInvoiceLine, 'taxAmount'>({
        dependsOn: ['taxes'],
        defaultValue() {
            return this.taxes.sum(tax => tax.taxAmount);
        },
        updatedValue: useDefaultValue,
    })
    override readonly taxAmount: Promise<decimal>;

    @decorators.enumProperty<PurchaseInvoiceLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<PurchaseInvoiceLine, 'stockTransactions'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    static async getVarianceType(
        purchaseInvoiceLine: xtremPurchasing.nodes.PurchaseInvoiceLine,
    ): Promise<xtremPurchasing.enums.PurchaseInvoiceVarianceType> {
        if ((await purchaseInvoiceLine.origin) === 'direct') {
            return 'quantityAndPrice';
        }
        const receiptLine = await purchaseInvoiceLine.purchaseReceiptLine;
        if (receiptLine) {
            const receiptQty = await (await receiptLine.purchaseReceiptLine).quantityInStockUnit;
            const receiptPrice = await (await receiptLine.purchaseReceiptLine).grossPrice;

            if (
                receiptQty !== (await purchaseInvoiceLine.quantityInStockUnit) &&
                receiptPrice === (await purchaseInvoiceLine.grossPrice)
            ) {
                return 'quantity';
            }
            if (
                receiptQty === (await purchaseInvoiceLine.quantityInStockUnit) &&
                receiptPrice !== (await purchaseInvoiceLine.grossPrice)
            ) {
                return 'price';
            }
            if (
                receiptQty !== (await purchaseInvoiceLine.quantityInStockUnit) &&
                receiptPrice !== (await purchaseInvoiceLine.grossPrice)
            ) {
                return 'quantityAndPrice';
            }
            return 'noVariance';
        }
        return 'quantityAndPrice';
    }

    @decorators.textStreamProperty<PurchaseInvoiceLine, 'varianceText'>({
        isStored: true,
        isPublished: true,
        async isFrozen() {
            return (await this.origin) === 'direct';
        },
    })
    readonly varianceText: Promise<TextStream | null>;

    @decorators.referenceProperty<PurchaseInvoiceLine, 'varianceApprover'>({
        isPublished: true,
        isStored: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        filters: {
            lookup: xtremAuthorization.filters.user.activeApplicationUsers,
        },
        async control(cx, val) {
            if (val && (await val.userType) !== 'application') {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/nodes__purchase_invoice_line__only__applicative_users',
                    'Only applicative users can approve variances.',
                );
            }
        },
    })
    readonly varianceApprover: Reference<xtremSystem.nodes.User | null>;

    @decorators.jsonPropertyOverride<PurchaseInvoiceLine, 'storedDimensions'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).billBySupplier,
                item: await this.item,
            });
        },
    })
    override readonly storedDimensions: Promise<object | null>;

    @decorators.jsonPropertyOverride<PurchaseInvoiceLine, 'storedAttributes'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).billBySupplier,
                item: await this.item,
            });
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonPropertyOverride<PurchaseInvoiceLine, 'computedAttributes'>({
        dependsOn: ['document', 'item'],
        async computeValue() {
            const document = await this.document;

            return xtremPurchasing.functions.computeAttributes(this.$.context, {
                site: await document.site,
                item: await this.item,
                supplier: await document.billBySupplier,
                line: this,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.referenceProperty<PurchaseInvoiceLine, 'purchaseOrderLine'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine,
        isNullable: true,
        isVital: true,
        reverseReference: 'purchaseInvoiceLine',
    })
    readonly purchaseOrderLine: Reference<xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine | null>;

    @decorators.collectionProperty<PurchaseInvoiceLine, 'purchaseCreditMemoLines'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLineToPurchaseCreditMemoLine,
        reverseReference: 'purchaseInvoiceLine',
    })
    readonly purchaseCreditMemoLines: Collection<xtremPurchasing.nodes.PurchaseInvoiceLineToPurchaseCreditMemoLine>;

    @decorators.decimalProperty<PurchaseInvoiceLine, 'creditedQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        lookupAccess: true,
        getValue() {
            return this.purchaseCreditMemoLines.sum(creditLine => creditLine.creditMemoQuantity);
        },
    })
    readonly creditedQuantity: Promise<decimal>;

    @decorators.decimalProperty<PurchaseInvoiceLine, 'remainingQuantityToCredit'>({
        isPublished: true,
        dependsOn: ['quantity', 'creditedQuantity'],
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        lookupAccess: true,
        async getValue() {
            return (await this.quantity) - (await this.creditedQuantity);
        },
    })
    readonly remainingQuantityToCredit: Promise<decimal>;

    @decorators.referenceProperty<PurchaseInvoiceLine, 'providerLinkedAddress'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async getValue() {
            return (await (
                await this.document
            ).billByLinkedAddress)!;
        },
    })
    readonly providerLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<PurchaseInvoiceLine, 'consumptionLinkedAddress'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async getValue() {
            return (await (
                await this.recipientSite
            ).primaryAddress)!;
        },
    })
    readonly consumptionLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.booleanPropertyOverride<BasePurchaseDocumentLine, 'canHaveLandedCost'>({
        getValue: () => true,
    })
    override readonly canHaveLandedCost: Promise<boolean>;

    @decorators.jsonProperty<PurchaseInvoiceLine, 'landedCostCheckResult'>({
        isPublished: true,
        isNullable: true,
        computeValue() {
            return this.checkLandedCostCanBePosted();
        },
    })
    readonly landedCostCheckResult: Promise<{ title: string; message: string } | null>;

    override getLandedCostAmountToAllocate(): Promise<decimal> {
        return xtremPurchasing.functions.getLandedCostAmountToAllocate(this);
    }

    private async getTaxDateDefaultValue(): Promise<date> {
        const receiptLine = await this.purchaseReceiptLine;
        if (receiptLine) {
            return (await receiptLine.purchaseReceiptLine).taxDate;
        }
        return (await this.document).supplierDocumentDate;
    }

    /**
     * Check if the line can be posted. If not, it returns an error message and the title to display in the dialog box
     */
    async checkLandedCostCanBePosted(): Promise<{ title: string; message: string } | null> {
        if ((await (await this.item).type) !== 'landedCost') return null;

        const landedCost = await this.landedCost;
        if (!landedCost) {
            // This error shouldn't happen as the landedCost property is created when the line is created
            throw new LogicError(
                `The landedCost property is not defined for the line ${this._id} even though the item ${await (
                    await this.item
                ).id} is a landed cost`,
            );
        }

        if (
            await landedCost.allocations.some(async allocation => {
                const allocatedLine = await allocation.allocatedDocumentLine;
                return (
                    allocatedLine instanceof xtremPurchasing.nodes.PurchaseReceiptLine &&
                    (await allocatedLine.stockTransactionStatus) !== 'completed'
                );
            })
        ) {
            return {
                title: this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_invoice_line__allocated_receipt_lines',
                    'Purchase receipts allocated but not completed',
                ),
                message: this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_invoice_line__allocated_purchase_receipt_line_not_completed',
                    'The purchase receipt lines allocated to landed cost need to have the stock status Completed.',
                ),
            };
        }

        if (await landedCost.allocations.some(async allocation => (await allocation.costAmount) <= 0)) {
            return {
                title: this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_invoice_line__missing_amount_allocation',
                    'Allocated amount missing',
                ),
                message: this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_invoice_line__landed_cost_zero_allocation',
                    'You need to enter an allocated amount greater than zero for each allocated document.',
                ),
            };
        }

        if ((await this.getLandedCostAmountToAllocate()) > (await landedCost.totalAmountAllocated)) {
            return {
                title: this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_invoice_line__insufficient_allocations',
                    'Allocated amount insufficient',
                ),
                message: this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_invoice_line__landed_cost_not_fully_allocated',
                    'The total allocated amount ({{currencySymbol}}{{allocatedAmount}}) cannot be less than the landed cost amount to allocate ({{currencySymbol}}{{amountToAllocate}}).',
                    {
                        currencySymbol: (await (await this.currency).symbol) || '',
                        allocatedAmount: await landedCost.totalAmountAllocated,
                        amountToAllocate: await landedCost.costAmountToAllocate,
                    },
                ),
            };
        }
        return null;
    }

    @decorators.mutation<typeof PurchaseInvoiceLine, 'acceptAllVariancesLine'>({
        isPublished: true,
        parameters: [
            { name: 'purchaseInvoiceId', type: 'integer', isMandatory: true },
            { name: 'purchaseInvoiceLineId', type: 'integer', isMandatory: true },
            { name: 'approve', type: 'boolean', isMandatory: true },
        ],
        return: 'boolean',
    })
    static async acceptAllVariancesLine(
        context: Context,
        purchaseInvoiceId: integer,
        purchaseInvoiceLineId: integer,
        approve: boolean,
    ): Promise<boolean> {
        if (
            await xtremPurchasing.functions.PurchaseInvoiceLib.managePurchaseInvoiceLineMatchingStatus(
                context,
                purchaseInvoiceId,
                purchaseInvoiceLineId,
                approve,
            )
        ) {
            return true;
        }

        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_invoice_line__invalid_variance_approval',
                'Invalid approval status',
            ),
        );
    }

    /**
     * Calculates purchase order line taxes. Mutation can be used by the frontend
     * during creation or update to show taxes to the user before he creates or updates order
     * @param context
     * @param site universal name because the same parameter name will be used in purchases.
     *             This mutation name will be passed as a parameter into the tax-panel page
     * @param businessPartner universal name
     * @param item
     * @param currency
     * @param amountExcludingTax
     * @param quantity
     * @param taxes an array of objects with optional parameter taxReference.
     *              If the array is left empty, mutation tries to automatically determine tax determination.
     *
     * @returns object
     */
    @decorators.mutation<typeof PurchaseInvoiceLine, 'calculateLineTaxes'>({
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: xtremTax.functions.getCalculateLineTaxesParameterPropertyDefinition<
                    typeof PurchaseInvoiceLine
                >(xtremMasterData.nodes.Supplier),
            },
        ],
        return: xtremTax.functions.getCalculateLineTaxesReturnDefinition<typeof PurchaseInvoiceLine>(),
    })
    static async calculateLineTaxes(
        context: Context,
        data: xtremTax.interfaces.CalculateLineTaxesParameters<xtremMasterData.nodes.Supplier>,
    ): Promise<xtremTax.interfaces.CalculateLineTaxesReturn> {
        return xtremTax.functions.calculateLineTaxes(
            await context.create(
                xtremPurchasing.nodes.PurchaseInvoice,
                { site: data.site, billBySupplier: data.businessPartner },
                { isOnlyForDefaultValues: true },
            ),
            await xtremPurchasing.classes.PurchasingTaxCalculator.create(context, data.site),
            data,
        );
    }
}

registerSqlFunction('PurchaseInvoiceLine.getVarianceType', PurchaseInvoiceLine.getVarianceType);
