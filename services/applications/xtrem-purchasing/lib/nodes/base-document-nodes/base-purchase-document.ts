import type { Collection, Reference, TextStream } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import { isPostedErrorClosed } from '../../functions/purchase-document-common';
import * as xtremPurchasing from '../../index';

@decorators.subNode<BasePurchaseDocument>({
    extends: () => xtremDistribution.nodes.BaseDistributionDocument,
    isAbstract: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    isCustomizable: true,
    async controlBegin(cx) {
        await xtremPurchasing.events.controls.checkWrongTaxType(cx, this);
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.sameCompanyForSiteAndStockSite(this, cx);
    },
    async controlDelete(cx) {
        await xtremPurchasing.events.controls.checkStatusDeletion(this, cx);
    },
})
export class BasePurchaseDocument extends xtremDistribution.nodes.BaseDistributionDocument {
    @decorators.enumPropertyOverride<BasePurchaseDocument, 'status'>({})
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumPropertyOverride<BasePurchaseDocument, 'displayStatus'>({})
    override readonly displayStatus: Promise<xtremDistribution.enums.InboundDisplayStatus>;

    @decorators.referencePropertyOverride<BasePurchaseDocument, 'stockSite'>({
        /* isNullable: false,  */
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<BasePurchaseDocument, 'supplier'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        getValue() {
            return this.businessRelation;
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<BasePurchaseDocument, 'businessRelation'>({
        node: () => xtremMasterData.nodes.Supplier,
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<BasePurchaseDocument, 'billBySupplier'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        lookupAccess: true,
    })
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<BasePurchaseDocument, 'currency'>({
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await (await (await this.businessRelation)?.businessEntity)?.currency) || null;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referencePropertyOverride<BasePurchaseDocument, 'paymentTerm'>({
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation)?.paymentTerm;
        },
        filters: { control: { businessEntityType: { _ne: 'customer' } } },
    })
    override readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.textStreamPropertyOverride<BasePurchaseDocument, 'internalNote'>({
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation).internalNote;
        },
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<BasePurchaseDocument, 'externalNote'>({
        dependsOn: ['status'],
        duplicatedValue: useDefaultValue,
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user knows that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<BasePurchaseDocument, 'isExternalNote'>({
        dependsOn: ['status'],
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<BasePurchaseDocument, 'isTransferHeaderNote'>({
        dependsOn: ['status'],
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<BasePurchaseDocument, 'isTransferLineNote'>({
        dependsOn: ['status'],
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferLineNote: Promise<boolean>;

    @decorators.collectionPropertyOverride<BasePurchaseDocument, 'lines'>({
        dependsOn: ['companyFxRate'],
        node: () => xtremPurchasing.nodes.BasePurchaseDocumentLine,
    })
    override readonly lines: Collection<xtremPurchasing.nodes.BasePurchaseDocumentLine>;

    // async getCompanyFxRate(): Promise<number> {
    //     return (await this.companyFxRate) !== 1.0 ? this.companyFxRate : 1.0 / (await this.companyFxRateDivisor);
    // }
}
