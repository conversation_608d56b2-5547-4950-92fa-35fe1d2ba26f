import type { Reference, decimal } from '@sage/xtrem-core';
import { NodeStatus, TextStream, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../..';
import { isPostedErrorClosed } from '../../functions/purchase-document-common';
import { isPurchaseDocumentLinePropertyDisabled } from '../../shared-functions/index';

@decorators.subNode<BasePurchaseDocumentLine>({
    extends: () => xtremDistribution.nodes.BaseDistributionDocumentLine,
    isAbstract: true,
    canSearch: true,
    canRead: true,
    async isFrozen() {
        return (await this.status) === 'closed';
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.sameSiteForHeaderAndLine(this, cx);
        await xtremPurchasing.events.controls.sameCompanyForSiteLineAndStockSiteLine(this, cx);
        await xtremPurchasing.events.controls.convertedQuantityInPurchaseUnitEqualquantityInStockUnit(this, cx);
        await xtremPurchasing.events.controls.lineItemIsFrozen(this, cx);
        await xtremPurchasing.events.controls.receivingSiteMustBelongToTheSameLegalCompany(this, cx);
        await xtremPurchasing.events.controls.itemMustHaveItemSiteAndMustBeStockManaged(this, cx);
        await xtremPurchasing.events.controls.itemMustHaveItemSiteAndNotManagedWarning(this, cx);
        await xtremPurchasing.events.controls.itemMustHaveItemSupplier(this, cx);
        await xtremPurchasing.events.controls.itemMustBeManagedForSupplierAndSite(this, cx);
    },
    async controlDelete(cx) {
        await xtremPurchasing.events.controls.checkLineStatusDeletion(this, cx);
    },
})
export class BasePurchaseDocumentLine extends xtremDistribution.nodes.BaseDistributionDocumentLine {
    @decorators.referencePropertyOverride<BasePurchaseDocumentLine, 'document'>({
        node: () => xtremPurchasing.nodes.BasePurchaseDocument,
    })
    override readonly document: Reference<xtremPurchasing.nodes.BasePurchaseDocument>;

    /** deprecated */
    @decorators.enumProperty<BasePurchaseDocumentLine, 'lineStatus'>({
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseDocumentStatusDataType,
        getValue() {
            return this.status;
        },
    })
    readonly lineStatus: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumPropertyOverride<BasePurchaseDocumentLine, 'status'>({})
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.referencePropertyOverride<BasePurchaseDocumentLine, 'item'>({
        isFrozen: true,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    // TODO: TO BE DELETED .
    @decorators.referenceProperty<BasePurchaseDocumentLine, 'supplier'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        dependsOn: [{ document: ['businessRelation'] }],
        async getValue() {
            return (await this.document).businessRelation;
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    // TO be deleted
    @decorators.stringProperty<BasePurchaseDocumentLine, 'supplierName'>({
        isPublished: true,
        dependsOn: [{ document: ['businessRelation'] }],
        async getValue() {
            return (await (await (await this.document).businessRelation).businessEntity).name;
        },
    })
    readonly supplierName: Promise<string>;

    @decorators.referenceProperty<BasePurchaseDocumentLine, 'itemSupplier'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemSupplier,
        join: {
            item() {
                return this.item;
            },
            supplier() {
                return this.supplier;
            },
        },
        prefetch(record) {
            return { item: record.item, supplier: record.document?.businessRelation };
        },
        lookupAccess: true,
    })
    readonly itemSupplier: Reference<xtremMasterData.nodes.ItemSupplier | null>;

    @decorators.stringPropertyOverride<BasePurchaseDocumentLine, 'itemDescription'>({
        isFrozen() {
            return this._isLinePropertyFrozen('itemDescription');
        },
    })
    override readonly itemDescription: Promise<string>;

    /** Receiving site  */
    @decorators.referencePropertyOverride<BasePurchaseDocumentLine, 'site'>({
        isFrozen() {
            return this._isLinePropertyFrozen('site');
        },
        async control(cx, site) {
            if (site) return;
            const docSite = await (await this.document).site;
            const legalCompanyName = await (await docSite.legalCompany).name;
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__base_purchase_document__line__no_inventory_site',
                'No stock site for the company. {{legalCompanyName}}.',
                { legalCompanyName },
            );
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    /**      * Receiving Site     */
    @decorators.referencePropertyOverride<BasePurchaseDocumentLine, 'stockSite'>({
        dependsOn: [{ document: ['status', 'stockSite'] }, 'status'],
        async updatedValue() {
            const document = await this.document;
            const oldStockSite = await (await document.$.old).stockSite;
            const stockSite = await document.stockSite;
            return document.$.status === NodeStatus.modified && oldStockSite !== stockSite ? stockSite : this.stockSite;
        },
        isFrozen() {
            return this._isLinePropertyFrozen('stockSite');
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    /** Not editable stockSiteLinkedAddress */
    @decorators.referencePropertyOverride<BasePurchaseDocumentLine, 'stockSiteLinkedAddress'>({
        defaultValue() {
            return this.getStockSiteAddressPrimaryActiveAddressDefaultValue();
        },
        isFrozen() {
            return this._isLinePropertyFrozen('stockSiteLinkedAddress');
        },
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.stockSite).businessEntity)._id;
                },
            },
        },
    })
    override readonly stockSiteLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    async getStockSiteAddressPrimaryActiveAddressDefaultValue(): Promise<xtremMasterData.nodes.BusinessEntityAddress | null> {
        return (
            (await (
                await (
                    await this.stockSite
                ).businessEntity
            )?.addresses.takeOne(
                async (address: xtremMasterData.nodes.BusinessEntityAddress) =>
                    (await address.isActive) && address.isPrimary,
            )) ?? null
        );
    }

    /**  In purchase unit     */
    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'quantity'>({
        // dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: ['unit'],
        isFrozen() {
            return this._isLinePropertyFrozen('quantity');
        },
    })
    override readonly quantity: Promise<decimal>;

    @decorators.referenceProperty<BasePurchaseDocumentLine, 'purchaseUnit'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
        async setValue(unit) {
            await this.$.set({ unit });
        },
    })
    readonly purchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referencePropertyOverride<BasePurchaseDocumentLine, 'unit'>({
        filters: {
            control: {
                async type() {
                    const item = await this.item;
                    if ((await item?.type) === 'good' && (await item?.isStockManaged)) {
                        return { _nin: ['time', 'temperature'] };
                    }
                    return { _nin: ['temperature'] };
                },
                async id() {
                    return (await (
                        await this.item
                    )?.isStockManaged)
                        ? {
                              _in: await xtremMasterData.functions.listOfPurchaseUnitsByItemSupplier(
                                  await this.stockUnit,
                                  await this.item,
                                  await (
                                      await this.document
                                  )?.businessRelation,
                              ),
                          }
                        : { _regex: `.`, _options: 'i' };
                },
            },
        },
        dependsOn: ['item', 'stockUnit', { document: ['businessRelation'] }],
        async defaultValue() {
            return xtremMasterData.nodes.UnitOfMeasure.getPurchaseUnit(
                this.$.context,
                await this.item,
                await (
                    await this.document
                ).businessRelation,
            );
        },
        async updatedValue() {
            if (!(await this.unit) && (await this.item) && (await (await this.document).businessRelation)) {
                const itemSupplier =
                    (await this.$.context
                        .query(xtremMasterData.nodes.ItemSupplier, {
                            filter: {
                                item: await this.item,
                                supplier: await (await this.document).businessRelation,
                            },
                        })
                        .at(0)) || null;
                return (itemSupplier && (await itemSupplier.purchaseUnitOfMeasure)) || this.unit;
            }
            return this.unit;
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.unitToStockUnitConversionFactorValidation(this, val, cx);
            await xtremPurchasing.events.controls.unitToPreviousUnitConversionFactorValidation(this, val, cx);
        },
        isFrozen() {
            return this._isLinePropertyFrozen('unit');
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<BasePurchaseDocumentLine, 'companyCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await this.document).companyCurrency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<BasePurchaseDocumentLine, 'company'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        dependsOn: ['site'],
        async getValue() {
            return (await this.site).legalCompany;
        },
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'grossPrice'>({
        dependsOn: [{ document: ['site', 'businessRelation', 'currency', 'documentDate'] }, 'item', 'quantity', 'unit'],
        async defaultValue() {
            const purchaseDocument = await this.document;
            const grossPrice = await xtremMasterData.functions.getPurchasePrice(this.$.context, {
                site: await purchaseDocument.site,
                supplier: await purchaseDocument.businessRelation,
                currency: await purchaseDocument.currency,
                item: await this.item,
                quantity: await this.quantity,
                unit: await this.unit,
                date: await purchaseDocument.documentDate,
                convertUnit: false,
            });
            return grossPrice >= 0 ? grossPrice : 0;
        },

        isFrozen() {
            return this._isLinePropertyFrozen('grossPrice');
        },
    })
    override readonly grossPrice: Promise<decimal>;

    @decorators.referencePropertyOverride<BasePurchaseDocumentLine, 'stockUnit'>({
        async isFrozen() {
            return (
                ((await this.item) &&
                    (await this.stockUnit) &&
                    (await (await this.item).stockUnit)._id !== (await this.stockUnit)._id) ||
                this._isLinePropertyFrozen('item')
            );
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'quantityInStockUnit'>({
        dependsOn: ['item', 'unit', 'stockUnit', 'quantity', 'unitToStockUnitConversionFactor'],
        isFrozen() {
            return this._isLinePropertyFrozen('quantityInStockUnit');
        },
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    protected get _isConversionFactorFrozen(): Promise<boolean> {
        return (async () => {
            return (
                this.$.status === NodeStatus.modified &&
                (await (await (await this.$.old).item)?.id) === (await (await this.item)?.id) &&
                (await (await (await this.$.old).unit)?.id) === (await (await this.unit)?.id) &&
                (await (await (await this.$.old).stockUnit)?.id) === (await (await this.stockUnit)?.id)
            );
        })();
    }

    @decorators.decimalProperty<BasePurchaseDocumentLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        getValue() {
            return this.unitToStockUnitConversionFactor;
        },
        async setValue(factor) {
            await this.$.set({ unitToStockUnitConversionFactor: factor });
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', 'unit', 'stockUnit', { document: ['businessRelation'] }],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async isFrozen() {
            return (
                (await this._isConversionFactorFrozen) || this._isLinePropertyFrozen('unitToStockUnitConversionFactor')
            );
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'amountExcludingTax'>({
        isFrozen() {
            return this._isLinePropertyFrozen('amountExcludingTax');
        },
    })
    override readonly amountExcludingTax: Promise<decimal>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'amountExcludingTaxInCompanyCurrency'>({
        isFrozen() {
            return this._isLinePropertyFrozen('amountExcludingTax');
        },
    })
    override readonly amountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.enumPropertyOverride<BasePurchaseDocumentLine, 'priceOrigin'>({
        dependsOn: ['grossPrice', 'priceOrigin'],
        async updatedValue() {
            return (await this.grossPrice) ? this.priceOrigin : null;
        },
    })
    override readonly priceOrigin: Promise<xtremPurchasing.enums.PriceOrigin | null>;

    @decorators.decimalProperty<BasePurchaseDocumentLine, 'discount'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        lookupAccess: true,
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'decrease');
        },
        async setValue(val: decimal) {
            // TODO: We should update only if value changed in update mode or if value is > 0 if added
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'decrease',
                val,
                await this.grossPrice,
            );
        },
    })
    readonly discount: Promise<decimal>;

    @decorators.decimalProperty<BasePurchaseDocumentLine, 'charge'>({
        excludedFromPayload: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.baseDecimal,
        dependsOn: ['grossPrice', { discountCharges: ['sign', 'basis', 'valueType', 'value'] }],
        lookupAccess: true,
        getValue() {
            return xtremMasterData.functions.computeDiscountChargeValue(this.discountCharges, 'increase');
        },
        async setValue(val: decimal) {
            // TODO: We should  update only if value changed in update mode or if value is > 0 if added
            await xtremMasterData.functions.setDiscountChargeValue(
                this.discountCharges,
                'increase',
                val,
                await this.grossPrice,
            );
        },
    })
    readonly charge: Promise<decimal>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'netPrice'>({
        dependsOn: ['grossPrice', 'quantity', 'discountCharges', { discountCharges: ['sign'] }, 'currency'],
        defaultValue() {
            return this.computeNetPrice();
        },
        duplicatedValue: useDefaultValue,
        updatedValue: useDefaultValue,
    })
    override readonly netPrice: Promise<decimal>;

    @decorators.textStreamPropertyOverride<BasePurchaseDocumentLine, 'internalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status) || isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<BasePurchaseDocumentLine, 'externalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status) || isPostedErrorClosed(await this.status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    /** to be sure the user know that externalNode will go to external documents */
    @decorators.booleanPropertyOverride<BasePurchaseDocumentLine, 'isExternalNote'>({
        async isFrozen() {
            return isPostedErrorClosed(await (await this.document).status) || isPostedErrorClosed(await this.status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    private async computeNetPrice(): Promise<decimal> {
        const grossPrice = await this.grossPrice;
        const company = await (await this.site).legalCompany;
        return grossPrice > 0 && company
            ? xtremMasterData.functions.calculateNetPrice(
                  this.discountCharges,
                  grossPrice,
                  await this.quantity,
                  await xtremMasterData.functions.getCompanyPriceScale(company),
              )
            : 0;
    }

    protected async _isLinePropertyFrozen(propertyName: string): Promise<boolean> {
        const oldLine = await this.$.old;
        const oldDocument = await oldLine.document;
        return isPurchaseDocumentLinePropertyDisabled(await oldDocument.status, await oldLine.status, propertyName);
    }
}
