import type { decimal, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../index';

const mayNotDeleteStatus: xtremPurchasing.enums.PurchaseInvoiceStatus[] = ['posted', 'inProgress', 'error'];

@decorators.node<PurchaseReceiptLineToPurchaseInvoiceLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isPublished: true,
    isVitalReferenceChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [{ orderBy: { purchaseReceiptLine: 1, purchaseInvoiceLine: 1 }, isUnique: true }],

    async controlEnd(cx) {
        await xtremPurchasing.events.controls.ReceiptLineToInvoiceLine.sameProperties(this, cx);
        if (
            this.$.status === NodeStatus.added &&
            ['error', 'inProgress'].includes(await (await this.purchaseReceiptLine).stockTransactionStatus)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_receipt_purchase_invoice_line__stock_transaction_status_not_completed',
                'The stock status of the purchase receipt line needs to be Completed.',
            );
        }
    },
    async controlDelete(cx) {
        await xtremPurchasing.events.controlDelete.lineToInvoiceLine.checkInvoiceLineStatus(cx, this);
    },
    async saveEnd() {
        await xtremPurchasing.functions.BaseDocument.updateLineToStatuses(this);
    },
    async deleteEnd() {
        await xtremPurchasing.functions.BaseDocument.updateLineToStatuses(this);
        if (mayNotDeleteStatus.includes(await (await (await this.purchaseInvoiceLine).document).status)) {
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt_purchase_invoice_line__deletion_forbidden_reason_status',
                    'The purchase invoice line cannot be deleted. The invoice is already posted',
                ),
            );
        }
    },
})
export class PurchaseReceiptLineToPurchaseInvoiceLine extends Node {
    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'purchaseReceiptLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLine,
        lookupAccess: true,
    })
    readonly purchaseReceiptLine: Reference<xtremPurchasing.nodes.PurchaseReceiptLine>;

    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'purchaseInvoiceLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLine,
        lookupAccess: true,
    })
    readonly purchaseInvoiceLine: Reference<xtremPurchasing.nodes.PurchaseInvoiceLine>;

    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseInvoiceLine: ['unit'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).unit;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'invoicedQuantity'>({
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        dependsOn: [{ purchaseInvoiceLine: ['quantity'] }],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async getValue() {
            return (await this.purchaseInvoiceLine).quantity;
        },
    })
    readonly invoicedQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            lookup: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseInvoiceLine: ['stockUnit'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'invoicedQuantityInStockUnit'>({
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [{ purchaseInvoiceLine: ['quantityInStockUnit'] }],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async getValue() {
            return (await this.purchaseInvoiceLine).quantityInStockUnit;
        },
    })
    readonly invoicedQuantityInStockUnit: Promise<decimal>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async getValue() {
            return (await this.purchaseInvoiceLine).unitToStockUnitConversionFactor;
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseInvoiceLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'price'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingPriceDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseInvoiceLine: ['grossPrice'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).grossPrice;
        },
    })
    readonly price: Promise<decimal>;

    // Property needed by the amount's xtremPurchasing.dataTypes.purchasingPriceDataType
    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'currency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: [{ purchaseInvoiceLine: ['currency'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseInvoiceLine, 'amountExcludingTax'>({
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseInvoiceLine: ['amountExcludingTax'] }],
        async getValue() {
            return (await this.purchaseInvoiceLine).amountExcludingTax;
        },
    })
    readonly amountExcludingTax: Promise<decimal>;
}
