import type { decimal, NodeUpdateData, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, Node, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '..';

@decorators.node<PurchaseRequisitionLineToPurchaseOrderLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { purchaseRequisitionLine: 1, purchaseOrderLine: 1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        const requisitionSysId = (await (await this.purchaseRequisitionLine).document)._id;
        const requisition = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseRequisition,
            { _id: requisitionSysId },
            { forUpdate: true },
        );
        const requisitionLine =
            (await requisition.lines.find(async line => line._id === (await this.purchaseRequisitionLine)._id)) || null;
        if (requisitionLine) {
            const line: NodeUpdateData<xtremPurchasing.nodes.PurchaseRequisitionLine> = {
                lineOrderStatus: 'partiallyOrdered',
                status: 'inProgress',
            };

            if (await requisitionLine.purchaseOrderLines.toArray()) {
                const orderedQty = await requisitionLine.purchaseOrderLines.sum(
                    orderLine => orderLine.orderedQuantityInStockUnit,
                );
                if (orderedQty >= (await requisitionLine.quantityInStockUnit)) {
                    line.lineOrderStatus = 'ordered';
                    line.status = 'closed';
                }
            }
            await requisitionLine.$.set(line);

            if (!(await requisition.$.trySave())) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__error_updating_requisition_status',
                        'Fail updating the status on the requisition line: {{errors}}',
                        { errors: this.$.context.diagnoses },
                    ),
                );
            }
        }
    },
    async controlEnd(cx) {
        await xtremPurchasing.events.controls.RequisitionLineToOrderLine.samesProperties(cx, this);
    },
    async deleteEnd() {
        const requisition = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseRequisition,
            { _id: (await (await this.purchaseRequisitionLine).document)._id },
            { forUpdate: true },
        );
        const requisitionLine =
            (await requisition.lines.find(async line => line._id === (await this.purchaseRequisitionLine)._id)) || null;
        if (requisitionLine) {
            const line: NodeUpdateData<xtremPurchasing.nodes.PurchaseRequisitionLine> = {
                lineOrderStatus: 'notOrdered',
                status: 'pending',
            };

            if ((await requisitionLine.orderedQuantityInStockUnit) - (await this.orderedQuantityInStockUnit) > 0) {
                line.lineOrderStatus = 'partiallyOrdered';
                line.status = 'inProgress';
            }
            await requisitionLine.$.set(line);

            if (!(await requisition.$.trySave())) {
                throw new BusinessRuleError(
                    this.$.context.localize(
                        '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__error_updating_requisition_status',
                        'Fail updating the status on the requisition line: {{errors}}',
                        { errors: this.$.context.diagnoses },
                    ),
                );
            }
        }
    },
})
export class PurchaseRequisitionLineToPurchaseOrderLine extends Node {
    @decorators.referenceProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'purchaseRequisitionLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseRequisitionLine,
        lookupAccess: true,
    })
    readonly purchaseRequisitionLine: Reference<xtremPurchasing.nodes.PurchaseRequisitionLine>;

    @decorators.referenceProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'purchaseOrderLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLine,
        lookupAccess: true,
    })
    readonly purchaseOrderLine: Reference<xtremPurchasing.nodes.PurchaseOrderLine>;

    @decorators.decimalProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'orderedQuantity'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly orderedQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseRequisitionLine: ['unit'] }],
        async getValue() {
            return (await (
                await this.purchaseRequisitionLine
            ).unit)!;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseRequisitionLine: ['stockUnit'] }],
        async getValue() {
            return (await (
                await this.purchaseRequisitionLine
            ).stockUnit)!;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'orderedQuantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.stockQuantity,
        dependsOn: [
            'orderedQuantity',
            'stockUnit',
            'unit',
            'unitToStockUnitConversionFactor',
            { purchaseRequisitionLine: ['unitToStockUnitConversionFactor'] },
            'purchaseOrderLine',
        ],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            if (
                val !==
                (await xtremMasterData.functions.convertFromTo(
                    await this.unit,
                    await this.stockUnit,
                    await this.orderedQuantity,
                    true,
                    await (
                        await this.purchaseRequisitionLine
                    ).unitToStockUnitConversionFactor,
                ))
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/nodes__purchase_requisition_line_to_purchase_order_line__provided_stock_quantity_error',
                    'The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.',
                );
            }
        },
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.orderedQuantity,
                await this.unit,
                await this.stockUnit,
                true,
                true,
                true,
                (await this.purchaseOrderLine).$.status,
                await this.unitToStockUnitConversionFactor,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly orderedQuantityInStockUnit: Promise<decimal>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async getValue() {
            return (await this.purchaseRequisitionLine).unitToStockUnitConversionFactor;
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseRequisitionLineToPurchaseOrderLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseRequisitionLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseRequisitionLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;
}
