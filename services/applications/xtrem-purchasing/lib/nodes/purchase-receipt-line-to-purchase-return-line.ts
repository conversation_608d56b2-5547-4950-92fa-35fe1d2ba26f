import type { Reference, decimal } from '@sage/xtrem-core';
import { Node, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { loggers } from '../functions';
import * as xtremPurchasing from '../index';

@decorators.node<PurchaseReceiptLineToPurchaseReturnLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isVitalReferenceChild: true,
    isPublished: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [{ orderBy: { purchaseReturnLine: 1, purchaseReceiptLine: 1 }, isUnique: true }],
    async saveEnd() {
        loggers.receiptToReturn.debug(() => 'receipt to return saveEnd');
        const purchaseReceipt = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseReceipt,
            { _id: (await (await this.purchaseReceiptLine).document)._id },
            { forUpdate: true },
        );
        const purchaseReceiptLine =
            (await purchaseReceipt.lines.find(async line => line._id === (await this.purchaseReceiptLine)._id)) || null;
        if (purchaseReceiptLine) {
            let newLineReturnStatus: xtremPurchasing.enums.PurchaseReceiptReturnStatus = 'partiallyReturned';
            let newLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus = 'inProgress';
            if (await purchaseReceiptLine.purchaseReturnLines.toArray()) {
                const returnedQtyInStockUnit = await purchaseReceiptLine.purchaseReturnLines.sum(
                    line => line.returnedQuantityInStockUnit,
                );
                if (returnedQtyInStockUnit >= (await purchaseReceiptLine.quantityInStockUnit)) {
                    newLineReturnStatus = 'returned';
                    newLineStatus = 'closed';
                } else {
                    const invoicedQtyInStockUnit = await purchaseReceiptLine.purchaseInvoiceLines.sum(
                        line => line.invoicedQuantityInStockUnit,
                    );
                    if (
                        returnedQtyInStockUnit + invoicedQtyInStockUnit >=
                        (await purchaseReceiptLine.quantityInStockUnit)
                    ) {
                        newLineStatus = 'closed';
                    }
                }
            }
            await purchaseReceipt.$.set({
                canUpdateClosedDocument: true,
                lines: [
                    {
                        _id: purchaseReceiptLine._id,
                        lineReturnStatus: newLineReturnStatus,
                        status: newLineStatus,
                        _action: 'update',
                    },
                ],
                returnStatus:
                    await xtremPurchasing.functions.PurchaseReceiptLib.computeReceiptReturnStatusFromLinesReturnStatuses(
                        purchaseReceipt,
                    ),
                status: await xtremPurchasing.functions.PurchaseReceiptLib.computeReceiptStatusFromLinesStatuses(
                    purchaseReceipt,
                ),
            });
            // await logStatusDocument(loggers.receiptToReturn, purchaseReceipt);
            // await logStatusLine(loggers.receiptToReturn, purchaseReceiptLine);

            await purchaseReceipt.$.save({ deferred: true });
            loggers.receiptToReturn.debug(() => 'receipt to return saveEnd end');
        }
    },
    async controlEnd(cx) {
        /**
         * Some controls when creating a purchase receipt based on a purchase order line :
         * Header level controls :
         * - same supplier/stock site/site/currency
         * Line level controls :
         * - same item/purchase unit
         */
        const receiptLine = await this.purchaseReceiptLine;
        const returnLine = await this.purchaseReturnLine;
        const receiptLineDocument = await receiptLine.document;
        const returnLineDocument = await returnLine.document;
        if (
            (await receiptLine.item)._id !== (await returnLine.item)._id ||
            (await receiptLine.unit)._id !== (await returnLine.unit)._id ||
            (await receiptLineDocument.businessRelation)._id !== (await returnLineDocument.businessRelation)._id ||
            (await receiptLineDocument.site)._id !== (await returnLineDocument.returnSite)._id ||
            (await receiptLineDocument.currency)._id !== (await returnLineDocument.currency)._id ||
            (await receiptLine.unitToStockUnitConversionFactor) !== (await returnLine.unitToStockUnitConversionFactor)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_return__same_properties_receipt_to_return',
                'The purchase return must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase receipt.',
            );
        }

        const validStatuses = ['pending', 'inProgress', 'closed'];
        // Validate receipt document status
        if (!validStatuses.includes(await receiptLineDocument.status)) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_return__receipt_status_error',
                'You need to select a purchase receipt that is at Pending, In progress or Closed status.',
            );
        }
        // Validate receipt line status
        if (!validStatuses.includes(await receiptLine.status)) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_receipt_to_purchase_return__receipt_line_status_error',
                'You need to select a purchase receipt line that is at Pending, In progress or Closed status.',
            );
        }
    },
    async deleteEnd() {
        const purchaseReceipt = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseReceipt,
            { _id: (await (await this.purchaseReceiptLine).document)._id },
            { forUpdate: true },
        );
        const purchaseReceiptLine =
            (await purchaseReceipt.lines.find(async line => line._id === (await this.purchaseReceiptLine)._id)) || null;
        if (purchaseReceiptLine) {
            let newStatus: xtremPurchasing.enums.PurchaseReceiptReturnStatus = 'returned';
            const newReturnedQuantityForReceiptLine =
                (await purchaseReceiptLine.returnedQuantityInStockUnit) - (await this.returnedQuantityInStockUnit);
            if (newReturnedQuantityForReceiptLine < (await purchaseReceiptLine.quantityInStockUnit)) {
                newStatus = newReturnedQuantityForReceiptLine > 0 ? 'partiallyReturned' : 'notReturned';
            }
            // enables to delete a purchase return line in case it over returns a purchase receipt line (for which return status will remain to 'returned')
            if (newStatus !== (await purchaseReceiptLine.lineReturnStatus)) {
                await purchaseReceipt.$.set({
                    canUpdateClosedDocument: true,
                    lines: [
                        {
                            _id: purchaseReceiptLine._id,
                            lineReturnStatus: newStatus,
                            _action: 'update',
                        },
                    ],
                });
                await purchaseReceipt.$.save({ deferred: true });
            }
        }
    },
})
export class PurchaseReceiptLineToPurchaseReturnLine extends Node {
    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseReturnLine, 'purchaseReturnLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLine,
        lookupAccess: true,
    })
    readonly purchaseReturnLine: Reference<xtremPurchasing.nodes.PurchaseReturnLine>;

    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseReturnLine, 'purchaseReceiptLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLine,
        lookupAccess: true,
    })
    readonly purchaseReceiptLine: Reference<xtremPurchasing.nodes.PurchaseReceiptLine>;

    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseReturnLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseReceiptLine: ['unit'] }],
        async getValue() {
            return (await this.purchaseReceiptLine).unit;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseReturnLine, 'returnedQuantity'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly returnedQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReceiptLineToPurchaseReturnLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseReceiptLine: ['stockUnit'] }],
        async getValue() {
            return (await this.purchaseReceiptLine).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseReturnLine, 'returnedQuantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [
            'returnedQuantity',
            'unit',
            'stockUnit',
            'unitToStockUnitConversionFactor',
            // { purchaseReturnLine: ['unit', 'stockUnit', 'unitToStockUnitConversionFactor'] },
            { purchaseReceiptLine: ['unitToStockUnitConversionFactor'] },
        ],
        async control(cx, quantity) {
            await cx.error.if(quantity).is.negative();
            const controlQuantity = await xtremMasterData.functions.convertFromTo(
                await this.unit,
                await this.stockUnit,
                await this.returnedQuantity,
                true,
                await (
                    await this.purchaseReturnLine
                ).unitToStockUnitConversionFactor,
            );

            if (quantity !== controlQuantity) {
                loggers.receiptToReturn.debug(
                    () => `ERROR : returnedQuantityInStockUnit : ${quantity} control:${controlQuantity}`,
                );
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/nodes__purchase_receipt_line_to_purchase_return_line__provided_stock_quantity_error',
                    'The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.',
                );
            }
        },
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.returnedQuantity,
                await this.unit,
                await this.stockUnit,
                true,
                true,
                true,
                (await this.purchaseReturnLine).$.status,
                await this.unitToStockUnitConversionFactor,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly returnedQuantityInStockUnit: Promise<decimal>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseReturnLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async getValue() {
            return (await this.purchaseReceiptLine).unitToStockUnitConversionFactor;
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReceiptLineToPurchaseReturnLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseReceiptLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseReceiptLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;
}
