import type { NotificationEnvelope } from '@sage/xtrem-communication';
import type { Collection, Context, Reference, decimal, integer } from '@sage/xtrem-core';
import { BusinessRuleError, Decimal, Logger, NodeStatus, date, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { controlEnd } from '../events/controlEnd/purchase-return';
import { controlStatus, modifyCreatedDocument, checkApprovalStatus } from '../events/controls/purchase-return';
import { createEnd } from '../events/createEnd/purchase-return';
import { deleteBegin } from '../events/deleteBegin/purchase-return';
import { isDateLaterThanToday } from '../functions/common';
import { managePurchaseDocumentApprovalStatus, submitForApproval } from '../functions/document-approval';
import * as xtremPurchasing from '../index';
import { BasePurchaseDocument } from './base-document-nodes/index'; // TODO: Validate by Basalt (We cannot use xtremPurchasing.nodes.PurchaseDocument)

// Hack to exclude defaultValue rules from upgrade
const excludeFromUpgrade = <T>(x: T) => x;

const logger = Logger.getLogger(__filename, 'purchase-return');

@decorators.subNode<PurchaseReturn>({
    extends: () => BasePurchaseDocument,
    isPublished: true,
    canCreate: true,
    canRead: true,
    canUpdate: true,
    canDelete: true,
    canDuplicate: true,
    hasAttachments: true,
    async controlEnd(cx) {
        await controlEnd(this, cx);
    },
    async createEnd() {
        await createEnd(this);
    },
    /**
     * Purchase return delete controls :
     * - only if status is 'Draft' or 'Ready for processing'
     */
    async deleteBegin() {
        await deleteBegin(this);
    },
    async saveBegin() {
        await modifyCreatedDocument(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithStockPosting>()
export class PurchaseReturn
    extends BasePurchaseDocument
    implements
        xtremFinanceData.interfaces.PurchaseFinanceDocument,
        xtremStockData.interfaces.DocumentHeaderWithStockAllocation
{
    @decorators.enumPropertyOverride<PurchaseReturn, 'status'>({
        // The instance was updated : the new status is calculated according to all status properties
        dependsOn: ['invoiceStatus', { lines: ['status'] }],
        async control(cx, newStatus) {
            await controlStatus(this, cx, newStatus);
        },
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseReturnLib.computeReturnStatusFromLinesStatuses(this);
            }
            return this.status;
        },
    })
    override readonly status: Promise<xtremPurchasing.enums.PurchaseDocumentStatus>;

    @decorators.enumPropertyOverride<PurchaseReturn, 'displayStatus'>({
        dependsOn: ['status', 'stockTransactionStatus', 'approvalStatus', 'taxCalculationStatus'],
        async updatedValue() {
            return xtremPurchasing.functions.PurchaseReturnLib.calculatePurchaseReturnDisplayStatus(
                await this.status,
                await this.stockTransactionStatus,
                await this.approvalStatus,
                await this.taxCalculationStatus,
            );
        },
    })
    override readonly displayStatus: Promise<xtremPurchasing.enums.PurchaseReturnDisplayStatus>;

    @decorators.enumPropertyOverride<PurchaseReturn, 'invoiceStatus'>({
        dependsOn: [{ lines: ['lineInvoiceStatus'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseReturnLib.computeReturnInvoiceStatusFromLinesInvoiceStatuses(
                    this,
                );
            }
            return this.invoiceStatus;
        },
    })
    override readonly invoiceStatus: Promise<xtremPurchasing.enums.PurchaseReturnInvoiceStatus>;

    @decorators.enumProperty<PurchaseReturn, 'stockTransactionStatus'>({
        isPublished: true,
        isStored: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        defaultValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(this);
        },
        updatedValue: useDefaultValue,
        lookupAccess: true,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referenceProperty<PurchaseReturn, 'returnSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        node: () => xtremSystem.nodes.Site,
        // provides: ['site'], // It will be provided by the 'site' property (through the base-purchase-document)
        filters: {
            control: {
                isInventory: true,
            },
        },
    })
    readonly returnSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseReturn, 'site'>({
        dependsOn: ['returnSite'],
        filters: { control: { isPurchase: true } },
        defaultValue() {
            return this.returnSite;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseReturn, 'siteAddress'>({
        dependsOn: ['site'],
    })
    override readonly siteAddress: Reference<xtremMasterData.nodes.Address>;

    @decorators.referencePropertyOverride<PurchaseReturn, 'stockSite'>({
        dependsOn: ['returnSite'],
        defaultValue() {
            return this.returnSite;
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseReturn, 'businessRelation'>({
        async control(cx, supplier) {
            await xtremDistribution.events.DistributionDocument.controlSitesLegalCompanies(
                cx,
                this.$.context,
                await this.returnSite,
                await supplier.businessEntity,
            );
        },
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referencePropertyOverride<PurchaseReturn, 'billBySupplier'>({
        dependsOn: ['businessRelation'],
        defaultValue() {
            return this.businessRelation;
        },
    })
    override readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>; // | null;

    @decorators.referencePropertyOverride<PurchaseReturn, 'transactionCurrency'>({
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referencePropertyOverride<PurchaseReturn, 'companyCurrency'>({
        async isFrozen() {
            return !!(await this.lines.length);
        },
    })
    override readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.datePropertyOverride<PurchaseReturn, 'fxRateDate'>({
        dependsOn: ['returnRequestDate'],
        defaultValue() {
            return this.returnRequestDate;
        },
    })
    override readonly fxRateDate: Promise<date>;

    @decorators.datePropertyOverride<PurchaseReturn, 'date'>({
        dependsOn: ['returnRequestDate'],
        defaultValue() {
            return this.returnRequestDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly date: Promise<date>;

    @decorators.dateProperty<PurchaseReturn, 'returnRequestDate'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
        async control(cx, val) {
            if (
                isDateLaterThanToday(
                    this.$.status,
                    val,
                    this.$.status === NodeStatus.modified ? await (await this.$.old).returnRequestDate : undefined,
                )
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/pages__purchase_return__return_request_date_cannot_be_future',
                    'The return request date cannot be later than today.',
                );
            }
        },
    })
    readonly returnRequestDate: Promise<date>;

    // TODO: maybe refactored but is mandatory for stock management
    @decorators.dateProperty<PurchaseReturn, 'effectiveDate'>({
        isPublished: true,
        getValue() {
            return this.returnRequestDate;
        },
    })
    readonly effectiveDate: Promise<date>;

    @decorators.stringProperty<PurchaseReturn, 'supplierDocumentNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly supplierDocumentNumber: Promise<string>;

    @decorators.decimalPropertyOverride<PurchaseReturn, 'totalAmountExcludingTax'>({
        dependsOn: ['currency', { lines: ['amountExcludingTax'] }],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTax);
            // TODO: Sort out rounding
            // return xtremPurchasing.functions.amountPrecisionRound(
            //     this.lines.reduce((amount, line) => amount + line.amountExcludingTax, 0),
            //     this.currency.decimalDigits,
            // );
        },
        updatedValue: useDefaultValue,
    })
    override readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseReturn, 'totalAmountExcludingTaxInCompanyCurrency'>({
        dependsOn: [{ lines: ['amountExcludingTaxInCompanyCurrency'] }], // ,'status', 'receiptStatus'],
        defaultValue() {
            return this.lines.sum(line => line.amountExcludingTaxInCompanyCurrency);
        },
        updatedValue: useDefaultValue,
    })
    override readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    // TODO: uncomment in base purchase document and override here, once the platform enhancement https://jira.sage.com/browse/XT-22884 is implemented (override for compute value)
    @decorators.decimalProperty<PurchaseReturn, 'totalAmountIncludingTaxInCompanyCurrency'>({
        isPublished: true,
        dataType: () => xtremPurchasing.dataTypes.purchasingAmountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        // @decorators.decimalPropertyOverride<PurchaseOrder, 'totalAmountIncludingTaxInCompanyCurrency'>({
        dependsOn: ['currency', { lines: ['amountExcludingTaxInCompanyCurrency'] }],
        async getValue() {
            return Decimal.roundAt(
                await this.lines.sum(line => line.amountIncludingTaxInCompanyCurrency),
                await xtremPurchasing.functions.BaseDocument.getCurrencyDecimalDigits(this),
            );
        },
    })
    readonly totalAmountIncludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReturn, 'supplierAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['businessRelation'],
        node: () => xtremMasterData.nodes.Address,
        async isFrozen() {
            return (await this.invoiceStatus) === 'invoiced';
        },
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.businessRelation).primaryAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly supplierAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<PurchaseReturn, 'returnToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['businessRelation'],
        node: () => xtremMasterData.nodes.Address,
        async isFrozen() {
            return (await this.invoiceStatus) === 'invoiced';
        },
        async defaultValue() {
            return excludeFromUpgrade(await (await (await this.businessRelation).returnToAddress)?.address) || null;
        },
        updatedValue: useDefaultValue,
    })
    readonly returnToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.collectionPropertyOverride<PurchaseReturn, 'lines'>({
        node: () => xtremPurchasing.nodes.PurchaseReturnLine,
    })
    override readonly lines: Collection<xtremPurchasing.nodes.PurchaseReturnLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.enumPropertyOverride<PurchaseReturn, 'approvalStatus'>({
        async control(cx, status) {
            if (this.$.status === NodeStatus.modified && status !== (await (await this.$.old).approvalStatus)) {
                await checkApprovalStatus(status, this, cx);
            }
        },
    })
    override readonly approvalStatus: Promise<xtremPurchasing.enums.PurchaseDocumentApprovalStatus>;

    @decorators.enumProperty<PurchaseReturn, 'shippingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseReturnShippingStatusDataType,
        defaultValue: 'notShipped',
        // The instance was updated : the new status is calculated according to all status properties
        dependsOn: [{ lines: ['status'] }],
        async updatedValue() {
            if (
                await this.lines.some(line =>
                    [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
                )
            ) {
                return xtremPurchasing.functions.PurchaseReturnLib.computeShippingReturnStatusFromLinesStatuses(this);
            }
            return this.shippingStatus;
        },
    })
    readonly shippingStatus: Promise<xtremPurchasing.enums.PurchaseReturnShippingStatus>;

    @decorators.booleanProperty<PurchaseReturn, 'returnItems'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        dependsOn: ['allocationStatus', 'approvalStatus'],
        async control(cx, val) {
            // If we uncheck return items throw an error if there are allocations
            if (!val && !['notAllocated', 'notManaged'].includes(await this.allocationStatus)) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/purchase_return__uncheck_return_items_with_allocations',
                    'Remove the allocations before disabling the item returns.',
                );
            }
            // If we uncheck return items throw an error if it has been approved or rejected
            if (!val && ['approved', 'rejected'].includes(await this.approvalStatus)) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/purchase_return__uncheck_return_items_approved_or_rejected',
                    'You cannot enable or disable item returns. The approval process has been completed.',
                );
            }
        },
    })
    readonly returnItems: Promise<boolean>;

    @decorators.stringProperty<PurchaseReturn, 'supplierReturnReference'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
    })
    readonly supplierReturnReference: Promise<string>;

    @decorators.stringProperty<PurchaseReturn, 'changeRequestDescription'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.description,
    })
    readonly changeRequestDescription: Promise<string>;

    readonly allocableLinesCollectionName = 'lines';

    @decorators.enumProperty<PurchaseReturn, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: [{ lines: ['allocationStatus'] }],
        computeValue() {
            return xtremStockData.functions.allocationLib.computeHeaderAllocationStatus(
                this,
                xtremPurchasing.functions.PurchaseReturnLib.isPurchaseReturnLineConsideredForAllocationStatus,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.enumProperty<PurchaseReturn, 'creditStatus'>({
        isPublished: true,
        dataType: () => xtremPurchasing.enums.purchaseReturnCreditStatusDataType,
        dependsOn: [{ lines: ['lineCreditStatus'] }],
        async getValue() {
            if (await this.lines.every(async line => (await line.lineCreditStatus) === 'notCredited')) {
                return 'notCredited';
            }
            if (await this.lines.every(async line => (await line.lineCreditStatus) === 'credited')) {
                return 'credited';
            }
            return 'partiallyCredited';
        },
    })
    readonly creditStatus: Promise<xtremPurchasing.enums.PurchaseReturnCreditStatus>;

    // property needed for the accounting interface
    @decorators.datePropertyOverride<PurchaseReturn, 'documentDate'>({
        dependsOn: ['returnRequestDate'],
        getValue() {
            return this.returnRequestDate;
        },
    })
    override readonly documentDate: Promise<date>;

    @decorators.booleanProperty<PurchaseReturn, 'isApprovalManaged'>({
        isPublished: true,
        async getValue() {
            return (await this.site).isPurchaseReturnApprovalManaged;
        },
    })
    readonly isApprovalManaged: Promise<boolean>;

    @decorators.collectionProperty<PurchaseReturn, 'postingDetails'>({
        isPublished: true,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType() {
                return 'purchaseReturn';
            },
        },
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        lookupAccess: true,
    })
    readonly postingDetails: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    /**
     *
     * Method that creates purchase invoice for a given purchase receipt
     * All the lines have to have an item
     * @param context
     * @param purchaseReturnNumber
     */
    @decorators.mutation<typeof PurchaseReturn, 'createPurchaseInvoice'>({
        isPublished: true,
        parameters: [
            {
                name: 'returnDoc',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseReturn,
            },
        ],
        return: { type: 'array', item: { type: 'reference', node: () => xtremPurchasing.nodes.PurchaseInvoice } },
    })
    static async createPurchaseInvoice(
        context: Context,
        returnDoc: xtremPurchasing.nodes.PurchaseReturn,
    ): Promise<Array<xtremPurchasing.nodes.PurchaseInvoice>> {
        if (!returnDoc) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/purchase_return__purchase_return_not_found',
                    'Purchase return not found',
                ),
            );
        }
        if ((await returnDoc.status) === 'closed' || (await returnDoc.invoiceStatus) === 'invoiced') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/purchase_return__purchase_return_already_closed',
                    'The purchase return is already closed.',
                ),
            );
        }
        // All lines have to have an item
        if (await returnDoc.lines.map(line => line.item).some(item => !item)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/purchase_return__purchase_return_line_without_item',
                    'All lines need to have an item.',
                ),
            );
        }

        // Group lines per potential supplier and site criteria
        const purchaseInvoiceData =
            await xtremPurchasing.functions.PurchaseReturnLib.initPurchaseInvoiceFromReturnCreateData(returnDoc);

        return xtremPurchasing.functions.PurchaseReturnLib.createPurchaseInvoiceFromReturn(
            context,
            purchaseInvoiceData,
        );
    }

    /**
     * Method that triggers the stock engine for a given purchase return
     * and mark as closed & shipped the lines for non stock managed item
     * @param context
     * @param reference to a purchase return
     */
    @decorators.mutation<typeof PurchaseReturn, 'post'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'purchaseReturn',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseReturn,
            },
        ],
        return: { type: 'boolean' },
    })
    static async post(context: Context, purchaseReturn: PurchaseReturn): Promise<boolean> {
        const returnId = purchaseReturn._id;
        const skippedLines: Array<integer> = [];

        const purchaseReturnLines = purchaseReturn.lines.filter(async line =>
            ['notAllocated', 'partiallyAllocated'].includes(await line.allocationStatus),
        );

        if ((await purchaseReturnLines.length) > 0) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_return__post__allocation_status',
                    'You need to allocate stock to all lines before you can post.',
                ),
            );
        }

        const notPostedDocumentIds = await context.runInWritableContext(writableContext => {
            return xtremStockData.functions.stockLib.setStockTransactionToProgress(writableContext, {
                documentClass: PurchaseReturn,
                documentIds: [purchaseReturn._id],
            });
        });
        if (!notPostedDocumentIds.length) {
            return true;
        }

        // this will skip the stock posting for all the non-stock items and complete them
        await PurchaseReturn.onStockReply(context, {
            requestNotificationId: 'none',
            movementHasBeenSkipped: true,
            updateResults: {
                issue: {
                    documents: [
                        {
                            id: returnId,
                            lines: await purchaseReturn.lines
                                .filter(line =>
                                    xtremStockData.functions.stockDocumentLib.shouldSkipStockManagementForThisLine(
                                        line,
                                    ),
                                )
                                .map(async line => {
                                    skippedLines.push(line._id);
                                    return {
                                        id: line._id,
                                        sortValue: await line._sortValue,
                                        stockUpdateResultStatus: 'none',
                                        originLineIds: [],
                                        stockJournalRecords: [],
                                    } as xtremStockData.interfaces.StockUpdateResultCommonType['documents'][0]['lines'][0];
                                })
                                .toArray(),
                        },
                    ],
                },
            },
        });

        await context.runInWritableContext(async childContext => {
            const writableReturn = await xtremMasterData.functions.getWritableNode(
                childContext,
                xtremPurchasing.nodes.PurchaseReturn,
                returnId,
            );

            const linesToChange = writableReturn.lines.filter(
                async line =>
                    skippedLines.includes(line._id) &&
                    (await line.shippedStatus) !== 'shipped' &&
                    (await line.status) !== 'closed',
            );
            const needToSaveTheReturn = !!(await linesToChange.map(async line => {
                await line.$.set({
                    status: 'closed',
                    shippedStatus: 'shipped',
                });
                return line;
            }).length);

            if (needToSaveTheReturn) await writableReturn.$.save();
        });

        // must be called after onStockReply (for non-stock items) and the lines update are done.
        const stockMovementRequestReturn: xtremStockData.interfaces.PostToStockReturnJsonType<xtremStockData.enums.StockMovementTypeEnum.issue> =
            JSON.parse(await PurchaseReturn.postToStock(context, [purchaseReturn._id]));

        return stockMovementRequestReturn.result === 'requested';
    }

    static async postToStock(context: Context, documentIds: integer[]): Promise<string> {
        const result = await context.runInWritableContext(childContext =>
            xtremStockData.functions.notificationLib.stockIssueRequestNotification(childContext, {
                documentClass: PurchaseReturn,
                documentIds,
                stockUpdateParameters: {
                    stockDetailData: { isDocumentWithoutStockDetails: true },
                    allocationData: { isUsingAllocations: true },
                },
            }),
        );
        return result;
    }

    // Repost creates an update notification to the acc engine ir order to update att and dimensions.
    // In fact, the existing finance document headers are preserved while the lines are all rebuilt (old ones deleted and new ones calculated)
    @decorators.mutation<typeof PurchaseReturn, 'repost'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseReturn',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseReturn,
            },
            {
                name: 'documentLines',
                type: 'array',
                item: {
                    type: 'object',
                    properties: {
                        baseDocumentLineSysId: 'integer',
                        storedAttributes: 'json',
                        storedDimensions: 'json',
                    },
                },
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static async repost(
        context: Context,
        purchaseReturn: PurchaseReturn,
        documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        if (!xtremFinanceData.functions.canRepost((await purchaseReturn.financeIntegrationStatus) || 'submitted')) {
            // submitted used in case of undefined to assure that function will return false
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_return__cant_repost_purchase_return_when_status_is_not_failed',
                    "You can only repost a purchase return if the status is 'Failed.'",
                ),
            );
        }

        const updateLines = documentLines.map(documentLine => {
            return {
                _id: documentLine.baseDocumentLineSysId,
                storedAttributes: documentLine.storedAttributes,
                storedDimensions: documentLine.storedDimensions,
            };
        });
        await purchaseReturn.$.set({ forceUpdateForFinance: true, lines: updateLines });
        await purchaseReturn.$.save();

        // send notification in order to update a staging table entry for the accounting engine
        await xtremFinanceData.functions.financeDocumentUpdateNotification(context, {
            document: purchaseReturn,
            lines: await purchaseReturn.lines.toArray(),
            documentType: 'purchaseReturn',
            replyTopic: 'PurchaseReturnUpdate/accountingInterface',
            doNotPostOnUpdate: false,
        });
        return {
            wasSuccessful: true,
            message: context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_return__document_was_posted',
                'The purchase return was posted.',
            ),
        };
    }

    @decorators.mutation<typeof PurchaseReturn, 'close'>({
        isPublished: true,
        parameters: [
            {
                name: 'returnDoc',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseReturn,
                isWritable: true,
            },
        ],
        return: { type: 'boolean' },
    })
    static async close(_context: Context, returnDoc: PurchaseReturn): Promise<boolean> {
        await returnDoc.lines.forEach(async line => {
            await line.$.set({ status: 'closed' });
        });
        await returnDoc.$.set({ status: 'closed' });
        await returnDoc.$.save();
        return true;
    }

    @decorators.notificationListener<typeof PurchaseReturn>({
        startsReadOnly: true,
        topic: 'PurchaseReturn/stock/issue/reply',
        onError(
            context: Context,
            envelope: NotificationEnvelope<
                xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>
            >,
            error: Error,
        ) {
            return xtremStockData.functions.notificationLib.errorHandler(
                context,
                envelope,
                error,
                xtremPurchasing.nodes.PurchaseReturn,
            );
        },
    })
    static async onStockReply(
        readOnlyContext: Context,
        payload: xtremStockData.interfaces.StockReplyPayload<xtremStockData.enums.StockMovementTypeEnum.issue>,
    ): Promise<void> {
        const issueUpdateResult = await readOnlyContext.runInWritableContext(
            async writableContext =>
                (
                    await xtremStockData.functions.notificationLib.reactToStockMovementReply(
                        writableContext,
                        payload,
                        PurchaseReturn,
                        {
                            onLineSucceeded: PurchaseReturn.updateLineOnSuccess,
                            beforeDocumentSave: PurchaseReturn.updateHeaderAfterStockUpdate,
                        },
                    )
                ).issue,
        );
        if (!issueUpdateResult) return;

        if (!issueUpdateResult.transactionHadAnError) {
            await issueUpdateResult.documents.forEach(async document => {
                if (document.isStockDocumentCompleted) {
                    await readOnlyContext.runInWritableContext(async writableContext => {
                        const purchaseReturn = await xtremMasterData.functions.getWritableNode(
                            writableContext,
                            PurchaseReturn,
                            document.id,
                        );
                        await PurchaseReturn.onceStockCompleted(writableContext, purchaseReturn);
                    });
                }
            });
        }
    }

    static async updateHeaderAfterStockUpdate(
        document: xtremStockData.interfaces.DocumentHeaderWithStockPosting,
    ): Promise<void> {
        const purchaseReturn = document as PurchaseReturn;
        const existingDisplayStatus = await purchaseReturn.displayStatus;
        const displayStatus = xtremPurchasing.functions.PurchaseReturnLib.calculatePurchaseReturnDisplayStatus(
            await purchaseReturn.status,
            await purchaseReturn.stockTransactionStatus,
            await purchaseReturn.approvalStatus,
            await purchaseReturn.taxCalculationStatus,
        );
        if (existingDisplayStatus !== displayStatus) {
            await purchaseReturn.$.set({ displayStatus });
        }
    }

    static async updateLineOnSuccess(_context: Context, line: xtremPurchasing.nodes.PurchaseReturnLine): Promise<void> {
        await line.$.set({
            status: 'closed',
            shippedStatus: 'shipped',
        });
    }

    static async onceStockCompleted(writableContext: Context, purchaseReturn: PurchaseReturn): Promise<void> {
        if (await (await (await purchaseReturn.stockSite).legalCompany).doStockPosting) {
            // Send notification in order to create staging table entries for the accounting engine
            await xtremPurchasing.functions.FinanceIntegration.purchaseReturnNotification(
                writableContext,
                purchaseReturn as xtremFinanceData.interfaces.PurchaseFinanceDocument,
                (await purchaseReturn.lines.toArray()) as xtremFinanceData.interfaces.ReturnDocumentLine[],
            );
        }
    }

    @decorators.notificationListener<typeof PurchaseReturn>({
        topic: 'PurchaseReturn/accountingInterface',
    })
    static async onFinanceIntegrationReply(
        context: Context,
        payload: xtremFinanceData.interfaces.FinanceTransactionData,
    ): Promise<void> {
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, payload);
    }

    @decorators.mutation<typeof PurchaseReturn, 'confirm'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                node: () => PurchaseReturn,
                isMandatory: true,
                isWritable: true,
            },
        ],
        return: 'boolean',
    })
    static async confirm(context: Context, document: PurchaseReturn): Promise<boolean> {
        await xtremPurchasing.functions.PurchaseReturnLib.confirmPurchaseReturn(context, document);
        return true;
    }

    /**
     * Method that approve or reject the purchase return
     * @param context
     * @param purchaseReturnNumber
     */
    @decorators.mutation<typeof PurchaseReturn, 'approve'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                isMandatory: true,
                node: () => PurchaseReturn,
                isWritable: true,
            },
            {
                name: 'toBeApproved',
                type: 'boolean',
            },
        ],
        return: 'boolean',
    })
    static async approve(_context: Context, document: PurchaseReturn, toBeApproved: boolean): Promise<boolean> {
        await document.managePurchaseDocumentApprovalStatus(toBeApproved);
        return true;
    }

    async managePurchaseDocumentApprovalStatus(toBeApproved: boolean) {
        await managePurchaseDocumentApprovalStatus(this, toBeApproved);
    }

    @decorators.mutation<typeof PurchaseReturn, 'submitForApproval'>({
        isPublished: true,
        parameters: [
            {
                name: 'document',
                type: 'reference',
                isWritable: true,
                isMandatory: true,
                node: () => PurchaseReturn,
            },
        ],
        return: { type: 'boolean' },
    })
    static async submitForApproval(_context: Context, document: PurchaseReturn): Promise<boolean> {
        await submitForApproval(document);
        return true;
    }

    @decorators.mutation<typeof PurchaseReturn, 'financeIntegrationCheck'>({
        isPublished: true,
        parameters: [
            {
                name: 'purchaseReturn',
                type: 'reference',
                isMandatory: true,
                isWritable: true,
                node: () => PurchaseReturn,
            },
        ],
        return: {
            type: 'object',
            properties: {
                wasSuccessful: 'boolean',
                message: 'string',
            },
        },
    })
    static financeIntegrationCheck(
        context: Context,
        purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
    ): Promise<xtremFinanceData.interfaces.MutationResult> {
        return xtremPurchasing.functions.FinanceIntegration.purchaseReturnControlAndCreateMutationResult(
            context,
            purchaseReturn,
        );
    }

    @decorators.mutation<typeof PurchaseReturn, 'resendNotificationForFinance'>({
        isPublished: true,
        parameters: [{ name: 'purchaseReturn', type: 'reference', isMandatory: true, node: () => PurchaseReturn }],
        return: { type: 'boolean' },
    })
    static async resendNotificationForFinance(context: Context, purchaseReturn: PurchaseReturn): Promise<boolean> {
        logger.info(
            context.localize(
                '@sage/xtrem-purchasing/node__purchase_return__resend_notification_for_finance',
                'Resending finance notification for purchase return: {{purchaseReturnNumber}}.',
                { purchaseReturnNumber: await purchaseReturn.number },
            ),
        );

        if (
            await xtremFinanceData.functions.AccountingEngineCommon.validateResendNotificationForFinance(context, {
                documentNumber: await purchaseReturn.number,
                documentType: 'purchaseReturn',
            })
        ) {
            if (await (await (await purchaseReturn.stockSite).legalCompany).doStockPosting) {
                await xtremPurchasing.functions.FinanceIntegration.purchaseReturnNotification(
                    context,
                    purchaseReturn as xtremFinanceData.interfaces.PurchaseFinanceDocument,
                    (await purchaseReturn.lines.toArray()) as xtremFinanceData.interfaces.ReturnDocumentLine[],
                );
            }
        }

        return true;
    }

    @decorators.notificationListener<typeof PurchaseReturn>({
        topic: 'PurchaseReturn/resendNotificationForFinance',
    })
    static async onResendNotificationForFinance(context: Context, document: { number: number }) {
        const purchaseReturn = await context.read(PurchaseReturn, { number: document.number });

        await PurchaseReturn.resendNotificationForFinance(context, purchaseReturn);
    }
}
