import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

@decorators.subNode<WorkInProgressPurchaseOrderLine>({
    isPublished: true,
    extends: () => xtremMasterData.nodes.WorkInProgress,
    canRead: true,
    canSearch: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressPurchaseOrderLine extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressPurchaseOrderLine, 'purchaseOrderLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLine,
    })
    readonly purchaseOrderLine: Reference<xtremPurchasing.nodes.PurchaseOrderLine>;

    @decorators.referencePropertyOverride<WorkInProgressPurchaseOrderLine, 'item'>({
        dependsOn: [{ purchaseOrderLine: ['item'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.purchaseOrderLine).item;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referencePropertyOverride<WorkInProgressPurchaseOrderLine, 'site'>({
        dependsOn: [{ purchaseOrderLine: ['stockSite'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.purchaseOrderLine).stockSite;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site | null>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        switch (await (await this.purchaseOrderLine).status) {
            case 'draft':
            case 'pending':
                return 'planned';
            case 'inProgress':
                return 'firm';
            case 'closed':
                return 'closed';
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressPurchaseOrderLine, 'status'>({
        dependsOn: [{ purchaseOrderLine: ['status'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType | null>;

    @decorators.datePropertyOverride<WorkInProgressPurchaseOrderLine, 'startDate'>({
        dependsOn: [{ purchaseOrderLine: [{ document: ['orderDate'] }] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await (await this.purchaseOrderLine).document).orderDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date | null>;

    @decorators.datePropertyOverride<WorkInProgressPurchaseOrderLine, 'endDate'>({
        dependsOn: [{ purchaseOrderLine: ['expectedReceiptDate'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.purchaseOrderLine).expectedReceiptDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date | null>;

    /* quantities are expressed in item.stockUnit */
    @decorators.decimalPropertyOverride<WorkInProgressPurchaseOrderLine, 'expectedQuantity'>({
        dependsOn: [{ purchaseOrderLine: ['quantityInStockUnit'] }],
        defaultValue() {
            return this.determineExpectedQuantity();
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressPurchaseOrderLine, 'actualQuantity'>({
        dependsOn: [{ purchaseOrderLine: ['receivedQuantityInStockUnit'] }],
        defaultValue() {
            return this.determineActualQuantity();
        },
        updatedValue: useDefaultValue,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressPurchaseOrderLine, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        async defaultValue() {
            return Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
        updatedValue: useDefaultValue,
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressPurchaseOrderLine, 'documentType'>({
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return 'purchaseOrder';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    @decorators.stringPropertyOverride<WorkInProgressPurchaseOrderLine, 'documentNumber'>({
        dependsOn: [{ purchaseOrderLine: [{ document: ['number'] }] }],
        async getValue() {
            return (await (await this.purchaseOrderLine).document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressPurchaseOrderLine, 'documentLine'>({
        // TODO: Platform issue to create for 'WorkInProgressPurchaseOrderLine.documentLine: invalid 'dependsOn' value:
        // PurchaseOrderLine: PurchaseOrderLine._sortValue : property not found'
        // dependsOn: [{ purchaseOrderLine: ['_sortValue'] }], // dependsOn: [{ purchaseOrderLine: ['_sortValue'] }],
        async getValue() {
            return (await this.purchaseOrderLine)._sortValue;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressPurchaseOrderLine, 'documentId'>({
        async getValue() {
            return (await (await this.purchaseOrderLine).document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressPurchaseOrderLine, 'originDocumentType'>({
        getValue() {
            return null;
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    override async determineExpectedQuantity(): Promise<decimal> {
        return (await this.purchaseOrderLine).quantityInStockUnit;
    }

    override async determineActualQuantity(): Promise<decimal> {
        return (await this.purchaseOrderLine).receivedQuantityInStockUnit;
    }
}
