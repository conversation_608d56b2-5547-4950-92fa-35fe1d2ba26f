import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

@decorators.subNode<WorkInProgressPurchaseReceiptLine>({
    isPublished: true,
    extends: () => xtremMasterData.nodes.WorkInProgress,
    canRead: true,
    canSearch: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressPurchaseReceiptLine extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressPurchaseReceiptLine, 'purchaseReceiptLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseReceiptLine,
    })
    readonly purchaseReceiptLine: Reference<xtremPurchasing.nodes.PurchaseReceiptLine>;

    @decorators.referencePropertyOverride<WorkInProgressPurchaseReceiptLine, 'item'>({
        dependsOn: [{ purchaseReceiptLine: ['item'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.purchaseReceiptLine).item;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referencePropertyOverride<WorkInProgressPurchaseReceiptLine, 'site'>({
        dependsOn: [{ purchaseReceiptLine: [{ document: ['site'] }] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await (await this.purchaseReceiptLine).document).site;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site | null>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        switch (await (await this.purchaseReceiptLine).status) {
            case 'draft':
            case 'pending':
                return 'planned';
            case 'inProgress':
                return 'firm';
            case 'closed':
                return 'closed';
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressPurchaseReceiptLine, 'status'>({
        dependsOn: [{ purchaseReceiptLine: ['status'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType | null>;

    @decorators.datePropertyOverride<WorkInProgressPurchaseReceiptLine, 'startDate'>({
        dependsOn: [{ purchaseReceiptLine: [{ document: ['date'] }] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await (await this.purchaseReceiptLine).document).date;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date | null>;

    @decorators.datePropertyOverride<WorkInProgressPurchaseReceiptLine, 'endDate'>({
        dependsOn: [{ purchaseReceiptLine: [{ document: ['date'] }] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await (await this.purchaseReceiptLine).document).date;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date | null>;

    /* quantities are expressed in item.stockUnit */
    @decorators.decimalPropertyOverride<WorkInProgressPurchaseReceiptLine, 'expectedQuantity'>({
        dependsOn: [{ purchaseReceiptLine: ['quantityInStockUnit'] }],
        async defaultValue() {
            return (await this.purchaseReceiptLine).quantityInStockUnit;
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressPurchaseReceiptLine, 'actualQuantity'>({
        dependsOn: ['expectedQuantity'],
        defaultValue() {
            return this.expectedQuantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressPurchaseReceiptLine, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        async defaultValue() {
            return -Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
        updatedValue: useDefaultValue,
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressPurchaseReceiptLine, 'documentType'>({
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return 'purchaseReceipt';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    @decorators.stringPropertyOverride<WorkInProgressPurchaseReceiptLine, 'documentNumber'>({
        dependsOn: [{ purchaseReceiptLine: [{ document: ['number'] }] }],
        async getValue() {
            return (await (await this.purchaseReceiptLine).document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressPurchaseReceiptLine, 'documentLine'>({
        // TODO: Platform issue to create for 'WorkInProgressPurchaseReceiptLine.documentLine: invalid 'dependsOn' value:
        // PurchaseReceiptLine: PurchaseReceiptLine._sortValue : property not found'
        // dependsOn: [{ purchaseReceiptLine: ['_sortValue'] }], // dependsOn: [{ purchaseOrderLine: ['_sortValue'] }],
        async getValue() {
            return (await this.purchaseReceiptLine)._sortValue;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressPurchaseReceiptLine, 'documentId'>({
        async getValue() {
            return (await (await this.purchaseReceiptLine).document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressPurchaseReceiptLine, 'originDocumentType'>({
        getValue() {
            return null;
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;
}
