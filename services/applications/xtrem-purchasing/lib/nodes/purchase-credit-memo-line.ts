import type { Collection, Context, date, decimal, integer, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremPurchasing from '..';
import { BasePurchaseDocumentLine } from './base-document-nodes'; // TODO: To confirm (We cannot use xtremPurchasing.nodes.BasePurchaseDocument)

@decorators.subNode<PurchaseCreditMemoLine>({
    isPublished: true,
    canSearch: true,
    canRead: true,
    extends: () => BasePurchaseDocumentLine,
    async controlBegin(cx) {
        await xtremPurchasing.events.controls.CreditMemo.addNewLinePostedStatus(this, cx);
        await xtremPurchasing.events.controls.CreditMemo.updateCreditMemoAlreadyPosted(this, cx);
    },
    async saveBegin() {
        await xtremPurchasing.functions.PurchaseCreditMemoLib.updateLineNotesOnCreation(this);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class PurchaseCreditMemoLine
    extends BasePurchaseDocumentLine
    implements xtremFinanceData.interfaces.InvoiceDocumentLine
{
    @decorators.referencePropertyOverride<PurchaseCreditMemoLine, 'document'>({
        node: () => xtremPurchasing.nodes.PurchaseCreditMemo,
    })
    override readonly document: Reference<xtremPurchasing.nodes.PurchaseCreditMemo>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoLine, 'site'>({
        isFrozen: true,
        filters: { control: { isFinance: true } },
        dependsOn: [{ document: ['site'] }],
        async defaultValue() {
            const site = await (await this.document).site;
            if (site && (await site.isFinance)) {
                return site;
            }
            const financialSite = await (await site.legalCompany).sites.takeOne(companySite => companySite.isFinance);
            if (financialSite) {
                return financialSite;
            }
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_credit_memo__line__no_financial_site',
                    'No financial site on legal company. {{legalCompany}}',
                    { legalCompany: await (await site.legalCompany).name },
                ),
            );
        },
    })
    override readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoLine, 'stockSite'>({
        dependsOn: ['site', 'document', { document: ['site'] }],
        async defaultValue() {
            return (await (await this.itemSite)?.site)?.stockSite ?? (await this.site).stockSite;
        },
        isFrozen() {
            return this._isLinePropertyFrozen('stockSite');
        },
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    // Do we still need this property?
    @decorators.referenceProperty<PurchaseCreditMemoLine, 'recipientSite'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        lookupAccess: true,
        dependsOn: ['stockSite', 'origin', 'site', 'item'],
        filters: {
            control: {
                _or: [{ isInventory: true }, { isPurchase: true }],
                async itemSites() {
                    return { _atLeast: 1, item: await this.item };
                },
                async legalCompany() {
                    return (await this.site)?.legalCompany;
                },
            },
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
        defaultValue() {
            return this.stockSite;
        },
        async control(cx, val) {
            await xtremPurchasing.events.controls.recipientSiteValidation(val, cx);
        },
    })
    readonly recipientSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoLine, 'item'>({
        filters: {
            control: {
                isBought: true,
                itemSites() {
                    return { _atLeast: 1, site: this.site };
                },
                async isStockManaged() {
                    return (await this.origin) === 'direct' ? false : { _in: [true, false] };
                },
            },
        },
        async control(cx, val) {
            let referencedDocumentLineType: xtremPurchasing.interfaces.BaseDocumentLine;
            const purchaseReturnLine = await this.purchaseReturnLine;
            const purchaseInvoiceLine = await this.purchaseInvoiceLine;
            const origin = await this.origin;
            if (origin === 'purchaseReturn' || origin === 'purchaseInvoice') {
                if (origin === 'purchaseReturn') {
                    referencedDocumentLineType = await purchaseReturnLine?.purchaseReturnLine;
                }
                if (origin === 'purchaseInvoice') {
                    referencedDocumentLineType = await purchaseInvoiceLine?.purchaseInvoiceLine;
                }
                if (!referencedDocumentLineType) {
                    throw new BusinessRuleError('Document not provided');
                }
                await xtremPurchasing.events.controls.itemUpdateValidation(this, referencedDocumentLineType, val, cx);
            }
        },
    })
    override readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.stringPropertyOverride<PurchaseCreditMemoLine, 'itemDescription'>({
        dependsOn: ['item', 'origin'],
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
    })
    override readonly itemDescription: Promise<string>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemoLine, 'grossPrice'>({
        dependsOn: [{ document: ['site', 'billBySupplier', 'currency', 'creditMemoDate'] }, 'item', 'quantity', 'unit'],

        async defaultValue() {
            const document = await this.document;
            const grossPrice = await xtremMasterData.functions.getPurchasePrice(this.$.context, {
                site: await document.site,
                supplier: await document.billBySupplier,
                currency: await document.currency,
                item: await this.item,
                quantity: await this.quantity,
                unit: await this.unit,
                date: await document.creditMemoDate,
                convertUnit: false,
            });

            return grossPrice >= 0 ? grossPrice : 0;
        },
    })
    override readonly grossPrice: Promise<decimal>;

    @decorators.enumPropertyOverride<PurchaseCreditMemoLine, 'priceOrigin'>({
        dependsOn: [
            'grossPrice',
            { document: ['site', 'billBySupplier', 'currency', 'creditMemoDate'] },
            'item',
            'quantity',
            'unit',
            'landedCost',
        ],

        async defaultValue() {
            const document = await this.document;
            const price = await xtremMasterData.functions.getPurchasePrice(this.$.context, {
                site: await document.site,
                supplier: await document.billBySupplier,
                currency: await document.currency,
                item: await this.item,
                quantity: await this.quantity,
                unit: await this.unit,
                date: await document.creditMemoDate,
                convertUnit: false,
            });

            return price >= 0 ? 'supplierPriceList' : null;
        },
        async updatedValue() {
            return (await this.grossPrice) ? this.priceOrigin : null;
        },
    })
    override readonly priceOrigin: Promise<xtremPurchasing.enums.PriceOrigin | null>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoLine, 'unit'>({
        async control(cx, val) {
            const purchaseReturnLine = await this.purchaseReturnLine;
            const purchaseInvoiceLine = await this.purchaseInvoiceLine;
            let referencedDocumentLineType: xtremPurchasing.interfaces.BaseLineToLine = null;
            const origin = await this.origin;
            if (origin === 'purchaseReturn' || origin === 'purchaseInvoice') {
                if (origin === 'purchaseReturn') {
                    referencedDocumentLineType = purchaseReturnLine;
                }
                if (origin === 'purchaseInvoice') {
                    referencedDocumentLineType = purchaseInvoiceLine;
                }

                if (!referencedDocumentLineType) {
                    throw new BusinessRuleError('Document not provided');
                }

                await xtremPurchasing.events.controls.purchaseUnitUpdateValidation(
                    this,
                    referencedDocumentLineType,
                    val,
                    cx,
                );
            }
        },
        dependsOn: ['item', 'stockUnit', { document: ['billBySupplier'] }],
        async defaultValue() {
            return (await this.origin) === 'direct'
                ? xtremMasterData.nodes.UnitOfMeasure.getPurchaseUnit(
                      this.$.context,
                      await this.item,
                      await (
                          await this.document
                      ).billBySupplier,
                  )
                : null;
        },
        async isFrozen() {
            return (await this.origin) !== 'direct';
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // Because we want to display in the line table 2 fields for quantity (value and unit)
    // and we want to display the quantity value + unit in a unique field in the detailPanel,
    // we must set a technical _ property in node to be bound to the _ field in the page
    @decorators.referenceProperty<PurchaseCreditMemoLine, 'uPurchaseUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['unit'],
        getValue() {
            return this.unit;
        },
    })
    readonly uPurchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    // Because we want to display in the line table 2 fields for quantity (value and unit)
    // and we want to display the quantity value + unit in a unique field in the detailPanel,
    // we must set a technical _ property in node to be bound to the _ field in the page
    @decorators.referenceProperty<PurchaseCreditMemoLine, 'uStockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        dependsOn: ['stockUnit'],
        getValue() {
            return this.stockUnit;
        },
    })
    readonly uStockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referencePropertyOverride<PurchaseCreditMemoLine, 'stockUnit'>({
        async isFrozen() {
            return (await (await this.item).stockUnit)._id !== (await this.stockUnit)._id;
        },
    })
    override readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemoLine, 'quantityInStockUnit'>({
        dependsOn: [
            'item',
            'unit',
            'stockUnit',
            'quantity',
            'unitToStockUnitConversionFactor',
            'purchaseInvoiceLine',
            'purchaseReturnLine',
        ],
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.quantity,
                await this.unit,
                await this.stockUnit,
                true,
                !!(await this.purchaseInvoiceLine) || !!(await this.purchaseReturnLine),
                await this._isConversionFactorFrozen,
                this.$.status,
                await this.unitToStockUnitConversionFactor,
                await this.item,
                await (
                    await this.document
                ).billBySupplier,
            );
        },
        updatedValue: useDefaultValue,
    })
    override readonly quantityInStockUnit: Promise<decimal>;

    @decorators.decimalPropertyOverride<PurchaseCreditMemoLine, 'unitToStockUnitConversionFactor'>({
        dependsOn: ['item', 'unit', 'stockUnit', 'purchaseReturnLine', 'purchaseInvoiceLine'],
        async defaultValue() {
            return xtremMasterData.functions.getConvertCoefficient(await this.unit, await this.stockUnit);
        },
        async updatedValue() {
            if (
                (this.$.status === NodeStatus.added &&
                    ((await this.purchaseReturnLine) || (await this.purchaseInvoiceLine))) ||
                !(await this._isConversionFactorFrozen)
            ) {
                return xtremMasterData.functions.getConvertCoefficient(await this.unit, await this.stockUnit);
            }
            return this.unitToStockUnitConversionFactor;
        },
        isFrozen() {
            return this._isConversionFactorFrozen;
        },
    })
    override readonly unitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.referenceProperty<PurchaseCreditMemoLine, 'purchaseInvoiceLine'>({
        isPublished: true,
        dependsOn: ['stockUnit', 'unit'],
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLineToPurchaseCreditMemoLine,
        isNullable: true,
        isVital: true,
        reverseReference: 'purchaseCreditMemoLine',
    })
    readonly purchaseInvoiceLine: Reference<xtremPurchasing.nodes.PurchaseInvoiceLineToPurchaseCreditMemoLine | null>;

    @decorators.referenceProperty<PurchaseCreditMemoLine, 'purchaseReturnLine'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLineToPurchaseCreditMemoLine,
        isVital: true,
        isNullable: true,
        reverseReference: 'purchaseCreditMemoLine',
    })
    readonly purchaseReturnLine: Reference<xtremPurchasing.nodes.PurchaseReturnLineToPurchaseCreditMemoLine | null>;

    @decorators.stringProperty<PurchaseCreditMemoLine, 'sourceDocumentNumber'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async getValue() {
            const origin = await this.origin;
            if (origin === 'purchaseInvoice') {
                const purchaseInvoiceLine = await this.purchaseInvoiceLine;
                return purchaseInvoiceLine != null
                    ? (await (await purchaseInvoiceLine.purchaseInvoiceLine).document).number
                    : '';
            }
            const returnLine = await this.purchaseReturnLine;
            return origin === 'purchaseReturn' && returnLine != null
                ? (await (await returnLine.purchaseReturnLine).document).number
                : '';
        },
    })
    readonly sourceDocumentNumber: Promise<string>;

    // needed for the accounting engine
    @decorators.integerProperty<PurchaseCreditMemoLine, 'sourceDocumentSysId'>({
        isPublished: true,
        async getValue() {
            const origin = await this.origin;
            if (origin === 'purchaseInvoice') {
                const purchaseInvoiceLine = await this.purchaseInvoiceLine;
                return purchaseInvoiceLine != null
                    ? (await (await purchaseInvoiceLine.purchaseInvoiceLine).document)._id
                    : 0;
            }
            const returnLine = await this.purchaseReturnLine;
            return origin === 'purchaseReturn' && returnLine != null
                ? (await (await returnLine.purchaseReturnLine).document)._id
                : 0;
        },
    })
    readonly sourceDocumentSysId: Promise<integer>;

    // needed for the accounting engine
    @decorators.enumProperty<PurchaseCreditMemoLine, 'sourceDocumentType'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.sourceDocumentTypeDataType,
        async getValue() {
            const origin = await this.origin;
            if (origin === 'purchaseInvoice') {
                return 'purchaseInvoice';
            }
            return 'purchaseReturn';
        },
    })
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType>;

    // needed for the accounting engine
    @decorators.stringProperty<PurchaseCreditMemoLine, 'receiptNumber'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        async getValue() {
            const origin = await this.origin;
            if (origin === 'purchaseInvoice') {
                const purchaseReceiptLine =
                    await xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromInvoiceLine(this);
                return purchaseReceiptLine != null ? (await purchaseReceiptLine.document).number : '';
            }
            const receiptLine =
                await xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromReturnLine(this);
            return origin === 'purchaseReturn' && receiptLine != null ? (await receiptLine.document).number : '';
        },
    })
    readonly receiptNumber: Promise<string>;

    // needed for the accounting engine
    @decorators.integerProperty<PurchaseCreditMemoLine, 'receiptSysId'>({
        isPublished: true,
        async getValue() {
            const origin = await this.origin;
            if (origin === 'purchaseInvoice') {
                const purchaseReceiptLine =
                    await xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromInvoiceLine(this);
                return purchaseReceiptLine != null ? (await purchaseReceiptLine.document)._id : 0;
            }
            const receiptLine =
                await xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromReturnLine(this);
            return origin === 'purchaseReturn' && receiptLine != null ? (await receiptLine.document)._id : 0;
        },
    })
    readonly receiptSysId: Promise<integer>;

    @decorators.enumPropertyOverride<PurchaseCreditMemoLine, 'origin'>({
        dependsOn: ['purchaseReturnLine', 'purchaseInvoiceLine'],
        async defaultValue() {
            if ((await this.purchaseInvoiceLine)?._id) {
                return 'purchaseInvoice';
            }
            if ((await this.purchaseReturnLine)?._id) {
                return 'purchaseReturn';
            }
            return 'direct';
        },
    })
    override readonly origin: Promise<xtremPurchasing.enums.PurchaseDocumentLineOrigin>;

    @decorators.jsonPropertyOverride<PurchaseCreditMemoLine, 'storedDimensions'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultDimensions(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).billBySupplier,
                item: await this.item,
            });
        },
    })
    override readonly storedDimensions: Promise<object | null>;

    @decorators.jsonPropertyOverride<PurchaseCreditMemoLine, 'storedAttributes'>({
        dependsOn: ['document', 'item'],
        async defaultValue() {
            const site = await (await this.document).site;
            return xtremFinanceData.functions.getDefaultAttributes(this.$.context, {
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: (await site.legalCompany)._id,
                site,
                supplier: await (await this.document).billBySupplier,
                item: await this.item,
            });
        },
    })
    override readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonPropertyOverride<PurchaseCreditMemoLine, 'computedAttributes'>({
        dependsOn: [{ document: ['billBySupplier', 'site'] }, 'item'],
        async computeValue() {
            const document = await this.document;
            return xtremPurchasing.functions.computeAttributes(this.$.context, {
                site: await document.site,
                item: await this.item,
                supplier: await document.billBySupplier,
            });
        },
    })
    override readonly computedAttributes: Promise<object>;

    @decorators.datePropertyOverride<PurchaseCreditMemoLine, 'taxDate'>({
        dependsOn: [
            { document: ['supplierDocumentDate'] },
            { purchaseInvoiceLine: [{ purchaseInvoiceLine: [] }] },
            { purchaseInvoiceLine: [{ purchaseInvoiceLine: ['taxDate'] }] },
            { purchaseReturnLine: [{ purchaseReturnLine: [] }] },
            { purchaseReturnLine: [{ purchaseReturnLine: [{ purchaseReceiptLine: [] }] }] },
            {
                purchaseReturnLine: [
                    { purchaseReturnLine: [{ purchaseReceiptLine: [{ purchaseReceiptLine: ['taxDate'] }] }] },
                ],
            },
            'document',
        ],
        async defaultValue() {
            const invoiceLine = await this.purchaseInvoiceLine;
            if (invoiceLine) {
                return (await invoiceLine.purchaseInvoiceLine)?.taxDate;
            }
            const taxDate = await (
                await (
                    await (
                        await (
                            await this.purchaseReturnLine
                        )?.purchaseReturnLine
                    )?.purchaseReceiptLine
                )?.purchaseReceiptLine
            )?.taxDate;
            if (taxDate) {
                return taxDate;
            }
            return (await this.document).supplierDocumentDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly taxDate: Promise<date>;

    @decorators.referenceProperty<PurchaseCreditMemoLine, 'providerLinkedAddress'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async getValue() {
            return (await (
                await this.document
            ).billByLinkedAddress)!;
        },
    })
    readonly providerLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<PurchaseCreditMemoLine, 'consumptionLinkedAddress'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async getValue() {
            return (await this.site).primaryAddress;
        },
    })
    readonly consumptionLinkedAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.jsonProperty<PurchaseCreditMemoLine, 'uiTaxes'>({
        excludedFromPayload: true,
        isPublished: true,
        dependsOn: ['taxes'],
        async computeValue() {
            return xtremTax.functions.prepareTaxesToUiTaxes<xtremPurchasing.nodes.PurchaseCreditMemoLine>(
                this,
                String(await (await this.document).taxEngine),
            );
        },
        async setValue(val: xtremTax.interfaces.UiTaxes | null) {
            await xtremTax.functions.updateTaxesFromUiTaxes(val, this, String(await (await this.document).taxEngine));
        },
    })
    readonly uiTaxes: Promise<xtremTax.interfaces.UiTaxes | null>;

    @decorators.enumPropertyOverride<PurchaseCreditMemoLine, 'taxCalculationStatus'>({
        dependsOn: ['taxes', { document: ['taxCalculationStatus'] }],
        async getValue() {
            if (
                (await (await this.document).taxCalculationStatus) === 'failed' &&
                (await this.taxes.some(async tax => (await tax.isTaxMandatory) && !(await tax.taxReference)))
            ) {
                return 'failed';
            }
            if ((await (await this.document).taxCalculationStatus) === 'notDone') {
                return 'notDone';
            }
            return 'done';
        },
    })
    override readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.enumProperty<PurchaseCreditMemoLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return 'draft';
        },
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.collectionProperty<PurchaseCreditMemoLine, 'stockTransactions'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    override getLandedCostAmountToAllocate(): Promise<decimal> {
        return xtremPurchasing.functions.getLandedCostAmountToAllocate(this);
    }

    @decorators.booleanPropertyOverride<BasePurchaseDocumentLine, 'canHaveLandedCost'>({
        getValue: () => true,
    })
    override readonly canHaveLandedCost: Promise<boolean>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'signedQuantity'>({
        async getValue() {
            return -1 * (await this.quantity);
        },
    })
    override readonly signedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'signedAmountExcludingTax'>({
        async getValue() {
            return -1 * (await this.amountExcludingTax);
        },
    })
    override readonly signedAmountExcludingTax: Promise<decimal>;

    @decorators.decimalPropertyOverride<BasePurchaseDocumentLine, 'signedAmountExcludingTaxInCompanyCurrency'>({
        async getValue() {
            return -1 * (await this.amountExcludingTaxInCompanyCurrency);
        },
    })
    override readonly signedAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    /**
     * Returns the invoice line and the receipt line linked to the credit memo line.
     */
    async getReceiptAndInvoice(): Promise<{
        invoiceLine: xtremPurchasing.nodes.PurchaseInvoiceLine | null;
        receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine | null;
    }> {
        let invoiceLine: xtremPurchasing.nodes.PurchaseInvoiceLine | null =
            (await (await this.purchaseInvoiceLine)?.purchaseInvoiceLine) ?? null;
        let receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine | null =
            (await (await invoiceLine?.purchaseReceiptLine)?.purchaseReceiptLine) ?? null;

        // TODO:
        // the credit memo line should always have an invoiceLine
        // But as it's not done with purchase return yet, by default, we use the first invoice of the receipt
        if (!invoiceLine) {
            receiptLine =
                (await (
                    await (
                        await (
                            await this.purchaseReturnLine
                        )?.purchaseReturnLine
                    )?.purchaseReceiptLine
                )?.purchaseReceiptLine) ?? null;
            if (receiptLine) {
                invoiceLine =
                    (await (
                        await this.$.context
                            .query(xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine, {
                                filter: {
                                    purchaseReceiptLine: receiptLine._id,
                                },
                                first: 1,
                            })
                            .at(0)
                    )?.purchaseInvoiceLine) ?? null;
            }
        }
        return { invoiceLine, receiptLine };
    }

    /**
     * Calculates credit memo order line taxes. Mutation can be used by the frontend
     * during creation or update to show taxes to the user before he creates or updates credit memo
     * @param context
     * @param site universal name because the same parameter name will be used in purchases.
     *             This mutation name will be passed as a parameter into the tax-panel page
     * @param businessPartner universal name
     * @param item
     * @param currency
     * @param amountExcludingTax
     * @param quantity
     * @param taxes an array of objects with optional parameter taxReference.
     *              If the array is left empty, mutation tries to automatically determine tax determination.
     *
     * @returns object
     */
    @decorators.mutation<typeof PurchaseCreditMemoLine, 'calculateLineTaxes'>({
        // TODO change mutation to query when enhancement XT-19221 is done.
        isPublished: true,
        parameters: [
            {
                name: 'data',
                type: 'object',
                properties: xtremTax.functions.getCalculateLineTaxesParameterPropertyDefinition<
                    typeof PurchaseCreditMemoLine
                >(xtremMasterData.nodes.Supplier),
            },
        ],
        return: xtremTax.functions.getCalculateLineTaxesReturnDefinition<typeof PurchaseCreditMemoLine>(),
    })
    static async calculateLineTaxes(
        context: Context,
        data: xtremTax.interfaces.CalculateLineTaxesParameters<xtremMasterData.nodes.Supplier>,
    ): Promise<xtremTax.interfaces.CalculateLineTaxesReturn> {
        return xtremTax.functions.calculateLineTaxes(
            await context.create(
                xtremPurchasing.nodes.PurchaseCreditMemo,
                { site: data.site, billBySupplier: data.businessPartner },
                { isOnlyForDefaultValues: true },
            ),
            await xtremPurchasing.classes.PurchasingTaxCalculator.create(context, data.site),
            data,
        );
    }
}
