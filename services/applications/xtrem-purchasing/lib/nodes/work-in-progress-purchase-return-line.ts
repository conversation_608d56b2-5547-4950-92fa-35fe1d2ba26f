import type { date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

@decorators.subNode<WorkInProgressPurchaseReturnLine>({
    isPublished: true,
    extends: () => xtremMasterData.nodes.WorkInProgress,
    canRead: true,
    canSearch: true,
    isVitalReferenceChild: true,
})
export class WorkInProgressPurchaseReturnLine extends xtremMasterData.nodes.WorkInProgress {
    @decorators.referenceProperty<WorkInProgressPurchaseReturnLine, 'purchaseReturnLine'>({
        isPublished: true,
        isStored: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLine,
    })
    readonly purchaseReturnLine: Reference<xtremPurchasing.nodes.PurchaseReturnLine>;

    @decorators.referencePropertyOverride<WorkInProgressPurchaseReturnLine, 'item'>({
        dependsOn: [{ purchaseReturnLine: ['item'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.purchaseReturnLine).item;
        },
        updatedValue: useDefaultValue,
    })
    override readonly item: Reference<xtremMasterData.nodes.Item | null>;

    @decorators.referencePropertyOverride<WorkInProgressPurchaseReturnLine, 'site'>({
        dependsOn: [{ purchaseReturnLine: [{ document: ['returnSite'] }] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await (await this.purchaseReturnLine).document).returnSite;
        },
        updatedValue: useDefaultValue,
    })
    override readonly site: Reference<xtremSystem.nodes.Site | null>;

    private async _setStatus(): Promise<xtremMasterData.enums.OrderType> {
        switch (await (await this.purchaseReturnLine).status) {
            case 'draft':
            case 'pending': {
                return 'planned';
            }
            case 'inProgress': {
                return 'firm';
            }
            case 'closed': {
                return 'closed';
            }
            default:
                return 'closed';
        }
    }

    @decorators.enumPropertyOverride<WorkInProgressPurchaseReturnLine, 'status'>({
        dependsOn: [{ purchaseReturnLine: ['status'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return this._setStatus();
        },
        updatedValue: useDefaultValue,
    })
    override readonly status: Promise<xtremMasterData.enums.OrderType | null>;

    @decorators.datePropertyOverride<WorkInProgressPurchaseReturnLine, 'startDate'>({
        dependsOn: [{ purchaseReturnLine: [{ document: ['returnRequestDate'] }] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await (await this.purchaseReturnLine).document).returnRequestDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly startDate: Promise<date | null>;

    @decorators.datePropertyOverride<WorkInProgressPurchaseReturnLine, 'endDate'>({
        dependsOn: [{ purchaseReturnLine: ['expectedReturnDate'] }],
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        async defaultValue() {
            return (await this.purchaseReturnLine).expectedReturnDate;
        },
        updatedValue: useDefaultValue,
    })
    override readonly endDate: Promise<date | null>;

    /* quantities are expressed in item.stockUnit */
    @decorators.decimalPropertyOverride<WorkInProgressPurchaseReturnLine, 'expectedQuantity'>({
        dependsOn: [{ purchaseReturnLine: ['quantityInStockUnit'] }],
        async defaultValue() {
            return (await this.purchaseReturnLine).quantityInStockUnit;
        },
        updatedValue: useDefaultValue,
    })
    override readonly expectedQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressPurchaseReturnLine, 'actualQuantity'>({
        dependsOn: ['expectedQuantity'],
        defaultValue() {
            return this.expectedQuantity;
        },
        updatedValue: useDefaultValue,
    })
    override readonly actualQuantity: Promise<decimal>;

    @decorators.decimalPropertyOverride<WorkInProgressPurchaseReturnLine, 'outstandingQuantity'>({
        dependsOn: ['expectedQuantity', 'actualQuantity'],
        async defaultValue() {
            return Math.max((await this.expectedQuantity) - (await this.actualQuantity), 0);
        },
        updatedValue: useDefaultValue,
    })
    override readonly outstandingQuantity: Promise<decimal>;

    @decorators.enumPropertyOverride<WorkInProgressPurchaseReturnLine, 'documentType'>({
        // TODO: we cannot yet query on computed properties; for now, we set isStored on some and defaultValue to
        defaultValue() {
            return 'purchaseReturn';
        },
    })
    override readonly documentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;

    @decorators.stringPropertyOverride<WorkInProgressPurchaseReturnLine, 'documentNumber'>({
        dependsOn: [{ purchaseReturnLine: [{ document: ['number'] }] }],
        async getValue() {
            return (await (await this.purchaseReturnLine).document).number;
        },
    })
    override readonly documentNumber: Promise<string>;

    @decorators.integerPropertyOverride<WorkInProgressPurchaseReturnLine, 'documentLine'>({
        // TODO: Platform issue to create for 'WorkInProgress<...>.documentLine: invalid 'dependsOn' value:
        // PurchaseReturnLine: PurchaseReturnLine._sortValue : property not found'
        // dependsOn: [{ purchaseReturnLine: ['_sortValue'] }],
        async getValue() {
            return (await this.purchaseReturnLine)._sortValue;
        },
    })
    override readonly documentLine: Promise<integer>;

    @decorators.integerPropertyOverride<WorkInProgressPurchaseReturnLine, 'documentId'>({
        async getValue() {
            return (await (await this.purchaseReturnLine).document)._id;
        },
    })
    override readonly documentId: Promise<integer>;

    @decorators.enumPropertyOverride<WorkInProgressPurchaseReturnLine, 'originDocumentType'>({
        getValue() {
            return null;
        },
    })
    override readonly originDocumentType: Promise<xtremMasterData.enums.WorkInProgressDocumentType | null>;
}
