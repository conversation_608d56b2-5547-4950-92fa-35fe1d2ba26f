import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, Node, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { loggers } from '../functions';
import * as xtremPurchasing from '../index';

@decorators.node<PurchaseReturnLineToPurchaseInvoiceLine>({
    package: 'xtrem-purchasing',
    storage: 'sql',
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    indexes: [
        {
            orderBy: { purchaseReturnLine: 1, purchaseInvoiceLine: 1 },
            isUnique: true,
        },
    ],
    async saveEnd() {
        loggers.returnToInvoice.debug(() => 'return to invoice saveEnd');
        const purchaseReturn = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseReturn,
            { _id: (await (await this.purchaseReturnLine).document)._id },
            { forUpdate: true },
        );
        const purchaseReturnLine =
            (await purchaseReturn.lines.find(async line => line._id === (await this.purchaseReturnLine)._id)) || null;
        if (purchaseReturnLine) {
            let newLineInvoiceStatus: xtremPurchasing.enums.PurchaseReturnInvoiceStatus = 'partiallyInvoiced';
            let newLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus = 'inProgress';
            if (await purchaseReturnLine.purchaseInvoiceLines.toArray()) {
                const invoicedQty = await purchaseReturnLine.purchaseInvoiceLines.sum(
                    line => line.invoicedQuantityInStockUnit,
                );
                if (invoicedQty >= (await purchaseReturnLine.quantityInStockUnit)) {
                    newLineInvoiceStatus = 'invoiced';
                    newLineStatus = 'closed';
                }
            }
            await purchaseReturnLine.$.set({ lineInvoiceStatus: newLineInvoiceStatus, status: newLineStatus });
            await purchaseReturn.$.set({
                invoiceStatus:
                    await xtremPurchasing.functions.PurchaseReturnLib.computeReturnInvoiceStatusFromLinesInvoiceStatuses(
                        purchaseReturn,
                    ),
                status: await xtremPurchasing.functions.PurchaseReturnLib.computeReturnStatusFromLinesStatuses(
                    purchaseReturn,
                ),
            });
            await loggers.returnToInvoice.debugAsync(
                async () => `PurchaseReturnLineToPurchaseInvoiceLine.saveEnd:
                -returnLine.invoiceStatus=${newLineInvoiceStatus},
                -returnLine.status=${newLineStatus},
                -return.invoiceStatus=${await purchaseReturn.invoiceStatus},
                -return.status=${await purchaseReturn.status}`,
            );
            await purchaseReturn.$.save();
            loggers.returnToInvoice.debug(() => 'return to invoice saveEnd end');
        }
    },
    async controlEnd(cx) {
        /**
         * Some controls when creating a purchase receipt based on a purchase order line :
         * Header level controls :
         * - same supplier/stock site/site/currency
         * Line level controls :
         * - same item/purchase unit
         */
        const returnLine = await this.purchaseReturnLine;
        const invoiceLine = await this.purchaseInvoiceLine;
        const returnLineDocument = await returnLine.document;
        const invoiceLineDocument = await invoiceLine.document;

        if (
            (await returnLine.item)._id !== (await invoiceLine.item)._id ||
            (await returnLine.unit)._id !== (await invoiceLine.unit)._id ||
            (await returnLineDocument.businessRelation)._id !== (await invoiceLineDocument.billBySupplier)._id ||
            (await returnLineDocument.returnSite)._id !== (await invoiceLineDocument.site)._id ||
            (await returnLineDocument.currency)._id !== (await invoiceLineDocument.currency)._id ||
            (await returnLine.unitToStockUnitConversionFactor) !== (await invoiceLine.unitToStockUnitConversionFactor)
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_return_to_purchase_invoice__same_properties_return_to_invoice',
                'The purchase invoice must have the same values for item, purchase unit, supplier, site, currency and conversion factor as the purchase return.',
            );
        }
    },
    async deleteEnd() {
        const purchaseReturn = await this.$.context.read(
            xtremPurchasing.nodes.PurchaseReturn,
            { _id: (await (await this.purchaseReturnLine).document)._id },
            { forUpdate: true },
        );
        const purchaseReturnLine =
            (await purchaseReturn.lines.find(async line => line._id === (await this.purchaseReturnLine)._id)) || null;
        if (purchaseReturnLine) {
            let newLineInvoiceStatus: xtremPurchasing.enums.PurchaseReturnInvoiceStatus = 'notInvoiced';
            let newLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus = 'pending';
            if ((await purchaseReturnLine.invoicedQuantityInStockUnit) - (await this.invoicedQuantityInStockUnit) > 0) {
                newLineInvoiceStatus = 'partiallyInvoiced';
                newLineStatus = 'inProgress';
            }
            await purchaseReturnLine.$.set({ lineInvoiceStatus: newLineInvoiceStatus, status: newLineStatus });
            await purchaseReturn.$.save({ deferred: true });
        }
    },
})
export class PurchaseReturnLineToPurchaseInvoiceLine extends Node {
    @decorators.referenceProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'purchaseReturnLine'>({
        isStored: true,
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseReturnLine,
    })
    readonly purchaseReturnLine: Reference<xtremPurchasing.nodes.PurchaseReturnLine>;

    @decorators.referenceProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'purchaseInvoiceLine'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremPurchasing.nodes.PurchaseInvoiceLine,
    })
    readonly purchaseInvoiceLine: Reference<xtremPurchasing.nodes.PurchaseInvoiceLine>;

    @decorators.referenceProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'unit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseReturnLine: ['unit'] }],
        async getValue() {
            return (await this.purchaseReturnLine).unit;
        },
    })
    readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'invoicedQuantity'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInPurchaseUnit,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly invoicedQuantity: Promise<decimal>;

    @decorators.referenceProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'stockUnit'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        filters: {
            control: {
                type: { _nin: ['time', 'temperature'] },
            },
        },
        dependsOn: [{ purchaseReturnLine: ['stockUnit'] }],
        async getValue() {
            return (await this.purchaseReturnLine).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'invoicedQuantityInStockUnit'>({
        isStored: true,
        isPublished: true,
        isNotZero: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: [
            'invoicedQuantity',
            'unitToStockUnitConversionFactor',
            { purchaseReturnLine: ['unitToStockUnitConversionFactor'] },
            'unit',
            'stockUnit',
        ],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
            if (
                val !==
                (await xtremMasterData.functions.convertFromTo(
                    await this.unit,
                    await this.stockUnit,
                    await this.invoicedQuantity,
                    true,
                    await (
                        await this.purchaseInvoiceLine
                    ).unitToStockUnitConversionFactor,
                ))
            ) {
                cx.error.addLocalized(
                    '@sage/xtrem-purchasing/nodes__purchase_return_line_to_purchase_invoice_line__provided_stock_quantity_error',
                    'The provided quantity in stock unit is different then the converted quantity from purchase unit to stock unit.',
                );
            }
        },
        async defaultValue() {
            return xtremPurchasing.functions.computeQuantityInStockUnitWithConversionFactor(
                await this.invoicedQuantity,
                await this.unit,
                await this.stockUnit,
                true,
                true,
                true,
                (await this.purchaseReturnLine).$.status,
                await this.unitToStockUnitConversionFactor,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly invoicedQuantityInStockUnit: Promise<decimal>;

    /** deprecated */
    @decorators.decimalProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'purchaseUnitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async getValue() {
            return (await this.purchaseReturnLine).unitToStockUnitConversionFactor;
        },
    })
    readonly purchaseUnitToStockUnitConversionFactor: Promise<decimal>;

    @decorators.decimalProperty<PurchaseReturnLineToPurchaseInvoiceLine, 'unitToStockUnitConversionFactor'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.unitConversionCoefficient,
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: [{ purchaseReturnLine: ['unitToStockUnitConversionFactor'] }],
        async getValue() {
            return (await this.purchaseReturnLine).unitToStockUnitConversionFactor;
        },
    })
    readonly unitToStockUnitConversionFactor: Promise<decimal>;
}
