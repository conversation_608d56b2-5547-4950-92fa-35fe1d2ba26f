import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseRequisitionOrderStatusEnum {
    notOrdered = 1,
    partiallyOrdered = 2,
    ordered = 3,
}

export type PurchaseRequisitionOrderStatus = keyof typeof PurchaseRequisitionOrderStatusEnum;

export const purchaseRequisitionOrderStatusDataType = new EnumDataType<PurchaseRequisitionOrderStatus>({
    enum: PurchaseRequisitionOrderStatusEnum,
    filename: __filename,
});
