import { EnumDataType } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';

export enum PurchaseReturnDisplayStatusEnum {
    draft = xtremDistribution.enums.InboundDisplayStatusEnum.draft,
    pendingApproval = xtremDistribution.enums.InboundDisplayStatusEnum.pendingApproval,
    approved = xtremDistribution.enums.InboundDisplayStatusEnum.approved,
    rejected = xtremDistribution.enums.InboundDisplayStatusEnum.rejected,
    closed = xtremDistribution.enums.InboundDisplayStatusEnum.closed,
    taxCalculationFailed = xtremDistribution.enums.InboundDisplayStatusEnum.taxCalculationFailed,
    postingInProgress = xtremDistribution.enums.InboundDisplayStatusEnum.postingInProgress,
    returned = xtremDistribution.enums.InboundDisplayStatusEnum.returned,
    error = xtremDistribution.enums.InboundDisplayStatusEnum.error,
    confirmed = xtremDistribution.enums.InboundDisplayStatusEnum.confirmed,
}

export type PurchaseReturnDisplayStatus = keyof typeof PurchaseReturnDisplayStatusEnum;

export const purchaseReturnDisplayStatusDataType = new EnumDataType<PurchaseReturnDisplayStatus>({
    enum: PurchaseReturnDisplayStatusEnum,
    filename: __filename,
});
