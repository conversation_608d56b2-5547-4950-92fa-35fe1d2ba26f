import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseInvoiceMatchingStatusEnum {
    noVariance,
    variance,
    varianceApproved,
}

export type PurchaseInvoiceMatchingStatus = keyof typeof PurchaseInvoiceMatchingStatusEnum;

export const purchaseInvoiceMatchingStatusDataType = new EnumDataType<PurchaseInvoiceMatchingStatus>({
    enum: PurchaseInvoiceMatchingStatusEnum,
    filename: __filename,
});
