import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseOrderInvoiceStatusEnum {
    notInvoiced = 1,
    partiallyInvoiced = 2,
    invoiced = 3,
}

export type PurchaseOrderInvoiceStatus = keyof typeof PurchaseOrderInvoiceStatusEnum;

export const purchaseOrderInvoiceStatusDataType = new EnumDataType<PurchaseOrderInvoiceStatus>({
    enum: PurchaseOrderInvoiceStatusEnum,
    filename: __filename,
});
