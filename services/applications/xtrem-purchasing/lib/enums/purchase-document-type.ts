import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseDocumentTypeEnum {
    purchaseRequisition = 1,
    purchaseOrder = 2,
    purchaseSuggestion = 3,
    purchaseReceipt = 4,
    purchaseReturn = 5,
    purchaseInvoice = 6,
    purchaseCreditMemo = 7,
}

export type PurchaseDocumentType = keyof typeof PurchaseDocumentTypeEnum;

export const purchaseDocumentTypeDataType = new EnumDataType<PurchaseDocumentType>({
    enum: PurchaseDocumentTypeEnum,
    filename: __filename,
});
