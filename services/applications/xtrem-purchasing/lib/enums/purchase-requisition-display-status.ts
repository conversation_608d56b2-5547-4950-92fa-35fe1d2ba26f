import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseRequisitionDisplayStatusEnum {
    draft,
    pendingApproval,
    approved,
    confirmed,
    rejected,
    partiallyOrdered,
    ordered,
    closed,
}

export type PurchaseRequisitionDisplayStatus = keyof typeof PurchaseRequisitionDisplayStatusEnum;

export const purchaseRequisitionDisplayStatusDataType = new EnumDataType<PurchaseRequisitionDisplayStatus>({
    enum: PurchaseRequisitionDisplayStatusEnum,
    filename: __filename,
});
