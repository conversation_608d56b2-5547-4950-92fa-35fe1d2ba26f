import { EnumDataType } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';

export enum PurchaseCreditMemoDisplayStatusEnum {
    draft = xtremDistribution.enums.InboundDisplayStatusEnum.draft,
    taxCalculationFailed = xtremDistribution.enums.InboundDisplayStatusEnum.taxCalculationFailed,
    postingInProgress = xtremDistribution.enums.InboundDisplayStatusEnum.postingInProgress,
    postingError = xtremDistribution.enums.InboundDisplayStatusEnum.postingError,
    posted = xtremDistribution.enums.InboundDisplayStatusEnum.posted,
    stockError = xtremDistribution.enums.InboundDisplayStatusEnum.stockError,
    paid = xtremDistribution.enums.InboundDisplayStatusEnum.paid,
    partiallyPaid = xtremDistribution.enums.InboundDisplayStatusEnum.partiallyPaid,
}

export type PurchaseCreditMemoDisplayStatus = keyof typeof PurchaseCreditMemoDisplayStatusEnum;

export const purchaseCreditMemoDisplayStatusDataType = new EnumDataType<PurchaseCreditMemoDisplayStatus>({
    enum: PurchaseCreditMemoDisplayStatusEnum,
    filename: __filename,
});
