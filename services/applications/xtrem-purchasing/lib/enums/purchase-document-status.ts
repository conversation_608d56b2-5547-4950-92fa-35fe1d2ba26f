import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseDocumentStatusEnum {
    draft = 1,
    pending = 2,
    inProgress = 3,
    closed = 4,
    posted = 5,
    error = 6,
}

export type PurchaseDocumentStatus = keyof typeof PurchaseDocumentStatusEnum;

export const purchaseDocumentStatusDataType = new EnumDataType<PurchaseDocumentStatus>({
    enum: PurchaseDocumentStatusEnum,
    filename: __filename,
});
