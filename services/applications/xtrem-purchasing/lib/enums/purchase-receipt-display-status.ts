import { EnumDataType } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';

export enum PurchaseReceiptDisplayStatusEnum {
    draft = xtremDistribution.enums.InboundDisplayStatusEnum.draft,
    taxCalculationFailed = xtremDistribution.enums.InboundDisplayStatusEnum.taxCalculationFailed,
    postingInProgress = xtremDistribution.enums.InboundDisplayStatusEnum.postingInProgress,
    error = xtremDistribution.enums.InboundDisplayStatusEnum.error,
    received = xtremDistribution.enums.InboundDisplayStatusEnum.received,
    partiallyInvoiced = xtremDistribution.enums.InboundDisplayStatusEnum.partiallyInvoiced,
    invoiced = xtremDistribution.enums.InboundDisplayStatusEnum.invoiced,
    partiallyReturned = xtremDistribution.enums.InboundDisplayStatusEnum.partiallyReturned,
    returned = xtremDistribution.enums.InboundDisplayStatusEnum.returned,
    closed = xtremDistribution.enums.InboundDisplayStatusEnum.closed,
}

export type PurchaseReceiptDisplayStatus = keyof typeof PurchaseReceiptDisplayStatusEnum;

export const purchaseReceiptDisplayStatusDataType = new EnumDataType<PurchaseReceiptDisplayStatus>({
    enum: PurchaseReceiptDisplayStatusEnum,
    filename: __filename,
});
