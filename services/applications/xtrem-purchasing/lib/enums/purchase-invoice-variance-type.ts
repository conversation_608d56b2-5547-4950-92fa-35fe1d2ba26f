import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseInvoiceVarianceTypeEnum {
    noVariance,
    quantity,
    price,
    quantityAndPrice,
}

export type PurchaseInvoiceVarianceType = keyof typeof PurchaseInvoiceVarianceTypeEnum;

export const PurchaseInvoiceVarianceTypeDataType = new EnumDataType<PurchaseInvoiceVarianceType>({
    enum: PurchaseInvoiceVarianceTypeEnum,
    filename: __filename,
});
