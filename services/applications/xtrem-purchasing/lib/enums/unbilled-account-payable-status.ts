import { EnumDataType } from '@sage/xtrem-core';

export enum UnbilledAccountPayableStatusEnum {
    inProgress,
    completed,
    draft,
    error,
}

export type UnbilledAccountPayableStatus = keyof typeof UnbilledAccountPayableStatusEnum;

export const unbilledAccountPayableStatusDataType = new EnumDataType<UnbilledAccountPayableStatus>({
    enum: UnbilledAccountPayableStatusEnum,
    filename: __filename,
});
