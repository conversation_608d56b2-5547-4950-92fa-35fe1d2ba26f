import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseReturnShippingStatusEnum {
    notShipped = 1,
    partiallyShipped = 2,
    shipped = 3,
}

export type PurchaseReturnShippingStatus = keyof typeof PurchaseReturnShippingStatusEnum;

export const purchaseReturnShippingStatusDataType = new EnumDataType<PurchaseReturnShippingStatus>({
    enum: PurchaseReturnShippingStatusEnum,
    filename: __filename,
});
