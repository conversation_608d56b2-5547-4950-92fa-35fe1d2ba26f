import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseDocumentApprovalStatusEnum {
    draft = 1,
    pendingApproval = 2,
    approved = 3,
    rejected = 4,
    changeRequested = 5,
    confirmed = 6,
}

export type PurchaseDocumentApprovalStatus = keyof typeof PurchaseDocumentApprovalStatusEnum;

export const purchaseDocumentApprovalStatusDataType = new EnumDataType<PurchaseDocumentApprovalStatus>({
    enum: PurchaseDocumentApprovalStatusEnum,
    filename: __filename,
});
