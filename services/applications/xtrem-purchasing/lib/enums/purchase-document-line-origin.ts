import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseDocumentLineOriginEnum {
    direct = 1,
    purchaseRequisition = 2,
    purchaseOrder = 3,
    purchaseSuggestion = 4,
    purchaseReceipt = 5,
    purchaseReturn = 6,
    purchaseInvoice = 7,
    purchaseCreditMemo = 8,
}

export type PurchaseDocumentLineOrigin = keyof typeof PurchaseDocumentLineOriginEnum;

export const purchaseDocumentLineOriginDataType = new EnumDataType<PurchaseDocumentLineOrigin>({
    enum: PurchaseDocumentLineOriginEnum,
    filename: __filename,
});
