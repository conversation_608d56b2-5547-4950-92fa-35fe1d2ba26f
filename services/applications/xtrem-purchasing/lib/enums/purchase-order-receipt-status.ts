import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseOrderReceiptStatusEnum {
    notReceived = 1,
    partiallyReceived = 2,
    received = 3,
}

export type PurchaseOrderReceiptStatus = keyof typeof PurchaseOrderReceiptStatusEnum;

export const purchaseOrderReceiptStatusDataType = new EnumDataType<PurchaseOrderReceiptStatus>({
    enum: PurchaseOrderReceiptStatusEnum,
    filename: __filename,
});
