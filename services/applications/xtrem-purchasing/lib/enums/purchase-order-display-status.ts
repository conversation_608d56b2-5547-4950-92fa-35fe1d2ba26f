import { EnumDataType } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';

export enum PurchaseOrderDisplayStatusEnum {
    draft = xtremDistribution.enums.InboundDisplayStatusEnum.draft,
    pendingApproval = xtremDistribution.enums.InboundDisplayStatusEnum.pendingApproval,
    approved = xtremDistribution.enums.InboundDisplayStatusEnum.approved,
    rejected = xtremDistribution.enums.InboundDisplayStatusEnum.rejected,
    partiallyReceived = xtremDistribution.enums.InboundDisplayStatusEnum.partiallyReceived,
    received = xtremDistribution.enums.InboundDisplayStatusEnum.received,
    closed = xtremDistribution.enums.InboundDisplayStatusEnum.closed,
    taxCalculationFailed = xtremDistribution.enums.InboundDisplayStatusEnum.taxCalculationFailed,
    confirmed = xtremDistribution.enums.InboundDisplayStatusEnum.confirmed,
}

export type PurchaseOrderDisplayStatus = keyof typeof PurchaseOrderDisplayStatusEnum;

export const purchaseOrderDisplayStatusDataType = new EnumDataType<PurchaseOrderDisplayStatus>({
    enum: PurchaseOrderDisplayStatusEnum,
    filename: __filename,
});
