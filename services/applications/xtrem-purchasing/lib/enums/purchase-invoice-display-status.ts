import { EnumDataType } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';

export enum PurchaseInvoiceDisplayStatusEnum {
    taxCalculationFailed = xtremDistribution.enums.InboundDisplayStatusEnum.taxCalculationFailed,
    postingInProgress = xtremDistribution.enums.InboundDisplayStatusEnum.postingInProgress,
    postingError = xtremDistribution.enums.InboundDisplayStatusEnum.postingError,
    partiallyCredited = xtremDistribution.enums.InboundDisplayStatusEnum.partiallyCredited,
    credited = xtremDistribution.enums.InboundDisplayStatusEnum.credited,
    posted = xtremDistribution.enums.InboundDisplayStatusEnum.posted,
    stockError = xtremDistribution.enums.InboundDisplayStatusEnum.stockError,
    noVariance = xtremDistribution.enums.InboundDisplayStatusEnum.noVariance,
    variance = xtremDistribution.enums.InboundDisplayStatusEnum.variance,
    varianceApproved = xtremDistribution.enums.InboundDisplayStatusEnum.varianceApproved,
    paid = xtremDistribution.enums.InboundDisplayStatusEnum.paid,
    partiallyPaid = xtremDistribution.enums.InboundDisplayStatusEnum.partiallyPaid,
}

export type PurchaseInvoiceDisplayStatus = keyof typeof PurchaseInvoiceDisplayStatusEnum;

export const purchaseInvoiceDisplayStatusDataType = new EnumDataType<PurchaseInvoiceDisplayStatus>({
    enum: PurchaseInvoiceDisplayStatusEnum,
    filename: __filename,
});
