import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseReceiptReturnStatusEnum {
    notReturned = 1,
    partiallyReturned = 2,
    returned = 3,
}

export type PurchaseReceiptReturnStatus = keyof typeof PurchaseReceiptReturnStatusEnum;

export const purchaseReceiptReturnStatusDataType = new EnumDataType<PurchaseReceiptReturnStatus>({
    enum: PurchaseReceiptReturnStatusEnum,
    filename: __filename,
});
