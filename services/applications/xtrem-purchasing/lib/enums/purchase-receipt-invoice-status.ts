import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseReceiptInvoiceStatusEnum {
    notInvoiced = 1,
    partiallyInvoiced = 2,
    invoiced = 3,
}

export type PurchaseReceiptInvoiceStatus = keyof typeof PurchaseReceiptInvoiceStatusEnum;

export const purchaseReceiptInvoiceStatusDataType = new EnumDataType<PurchaseReceiptInvoiceStatus>({
    enum: PurchaseReceiptInvoiceStatusEnum,
    filename: __filename,
});
