import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseReturnInvoiceStatusEnum {
    notInvoiced = 1,
    partiallyInvoiced = 2,
    invoiced = 3,
}

export type PurchaseReturnInvoiceStatus = keyof typeof PurchaseReturnInvoiceStatusEnum;

export const purchaseReturnInvoiceStatusDataType = new EnumDataType<PurchaseReturnInvoiceStatus>({
    enum: PurchaseReturnInvoiceStatusEnum,
    filename: __filename,
});
