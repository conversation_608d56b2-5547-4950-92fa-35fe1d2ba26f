import { EnumDataType } from '@sage/xtrem-core';

export enum PurchaseReturnCreditStatusEnum {
    notCredited = 1,
    partiallyCredited = 2,
    credited = 3,
}

export type PurchaseReturnCreditStatus = keyof typeof PurchaseReturnCreditStatusEnum;

export const purchaseReturnCreditStatusDataType = new EnumDataType<PurchaseReturnCreditStatus>({
    enum: PurchaseReturnCreditStatusEnum,
    filename: __filename,
});
