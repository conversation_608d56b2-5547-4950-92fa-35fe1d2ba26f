import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';

/**
 * This asynchronous function checks if the site has purchase return that are in pending approval status :
 * - a site
 * - a supplier
 * @param _id : the _id of the current site
 * @param graph :
 */
export async function purchaseReturnPendingApproval(
    graph: ui.GraphQLApi<GraphApi>,
    siteSysId: string,
): Promise<boolean> {
    const pendingApproval = extractEdges(
        await graph
            .node('@sage/xtrem-purchasing/PurchaseReturn')
            .aggregate.query(
                ui.queryUtils.edgesSelector(
                    { group: { _id: { _by: 'value' } } },
                    { filter: { site: { _eq: siteSysId }, approvalStatus: { _eq: 'pendingApproval' } }, first: 1 },
                ),
            )
            .execute(),
    );

    return !!pendingApproval.length;
}
