import * as authorizationFilters from '@sage/xtrem-authorization/build/lib/client-functions/filters';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import { Decimal } from '@sage/xtrem-decimal';
import {
    convertFromTo,
    getCompanyPriceScale,
    getPurchaseUnit,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import type { GraphApi, PurchaseRequisitionLine } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isPurchaseRequisitionLineActionDisabled } from '../shared-functions/edit-rules';
import type * as userInterfaces from '../shared-functions/interfaces';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getPurchasePrice,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    ActionsParameter,
    ApplyDefaultSupplierActionParameter,
    ApproveRejectActionParameter,
    CalculatePricesParameter,
    CalculatePricesResult,
    ConfirmActionParameters,
    CreateActionParameter,
    FilterLinesParameter,
    GetDefaultSupplierParameter,
    LoadApproversParameters,
    SetDimensionActionParameter,
    SiteApprover,
    UserApprover,
} from './interfaces/purchase-requisition-actions-functions';

async function getDefaultSupplier(parameters: GetDefaultSupplierParameter) {
    const { item, site, purchaseRequisitionPage } = parameters;
    const defaultSupplier = await purchaseRequisitionPage.$.graph
        .node('@sage/xtrem-master-data/Supplier')
        .queries.getDefaultSupplier(
            {
                _id: true,
                businessEntity: {
                    id: true,
                    name: true,
                    currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                },
            },
            { item, site },
        )
        .execute();

    return defaultSupplier || {};
}

function filterLinesForDefaultSupplier(parameters: FilterLinesParameter) {
    return parameters.lines.filter(
        line =>
            !line.supplier?._id &&
            !isPurchaseRequisitionLineActionDisabled(parameters.status, line.status || '', 'applyDefaultSuppliers'),
    );
}

export function calculatePrices(parameters: CalculatePricesParameter): CalculatePricesResult {
    const { grossPrice, charge, discount, quantity, legalCompany, netPrice, totalTaxExcludedAmount } = parameters;
    if (parameters.netPrice !== undefined && parameters.netPrice >= 0) {
        const scale = getCompanyPriceScale(legalCompany);
        const chargeAmount = Decimal.roundAt((grossPrice * charge || 0) / 100, scale);
        const discountAmount = Decimal.roundAt((grossPrice * discount || 0) / 100, scale);
        const netUnitPrice = Decimal.roundAt(grossPrice + chargeAmount - discountAmount, scale);

        return {
            resultNetPrice: netUnitPrice.toString(),
            resultTotalTaxExcludedAmount: (netUnitPrice * quantity).toString(),
        };
    }
    return {
        resultNetPrice: netPrice.toString(),
        resultTotalTaxExcludedAmount: totalTaxExcludedAmount.toString(),
    };
}

export async function applyDefaultSupplier(parameters: ApplyDefaultSupplierActionParameter) {
    const linesWithoutSupplier = filterLinesForDefaultSupplier({ lines: parameters.lines, status: parameters.status });
    await Promise.all(
        linesWithoutSupplier.map(async lineWithoutSupplier => {
            if (lineWithoutSupplier.item?._id && lineWithoutSupplier.site?._id) {
                lineWithoutSupplier.supplier = await getDefaultSupplier({
                    purchaseRequisitionPage: parameters.purchaseRequisitionPage,
                    item: lineWithoutSupplier.item._id,
                    site: lineWithoutSupplier.site._id,
                });
            }
            if (lineWithoutSupplier.supplier?._id) {
                lineWithoutSupplier.currency = lineWithoutSupplier.supplier.businessEntity?.currency;
                lineWithoutSupplier.unit = await getPurchaseUnit(
                    parameters.purchaseRequisitionPage.$.graph,
                    lineWithoutSupplier.item?._id || '',
                    lineWithoutSupplier.supplier._id,
                );
                const quantityToConvert = lineWithoutSupplier.quantity ? Number(lineWithoutSupplier.quantity) : 1;
                const conversion = await convertFromTo(
                    parameters.purchaseRequisitionPage.$.graph,
                    lineWithoutSupplier.unit?._id || '',
                    lineWithoutSupplier.item?.stockUnit?._id || '',
                    quantityToConvert,
                    lineWithoutSupplier.item?._id,
                    lineWithoutSupplier.supplier?._id,
                    '',
                    'purchase',
                    false,
                );
                if (conversion.conversionFactor === 0) {
                    parameters.purchaseRequisitionPage.$.showToast(
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_requisition__no_coefficient_exists',
                            'No coefficient exists for the selected purchase unit and the item stock unit, please add the coefficient for the units.',
                        ),
                        { type: 'error' },
                    );
                }

                const { resultNetPrice, resultTotalTaxExcludedAmount } = calculatePrices({
                    netPrice: Number(lineWithoutSupplier.netPrice),
                    grossPrice: Number(lineWithoutSupplier.grossPrice),
                    charge: lineWithoutSupplier.charge ? Number(lineWithoutSupplier.charge) : 0,
                    discount: lineWithoutSupplier.discount ? Number(lineWithoutSupplier.discount) : 0,
                    quantity: Number(lineWithoutSupplier.quantity),
                    totalTaxExcludedAmount: Number(lineWithoutSupplier.totalTaxExcludedAmount),
                    legalCompany: parameters.legalCompany,
                });
                lineWithoutSupplier.netPrice = resultNetPrice;
                lineWithoutSupplier.totalTaxExcludedAmount = resultTotalTaxExcludedAmount;
                const purchasePrice = await getPurchasePrice(
                    parameters.purchaseRequisitionPage.$.graph,
                    lineWithoutSupplier.site?._id || '',
                    lineWithoutSupplier.supplier?._id,
                    lineWithoutSupplier.currency?._id || '',
                    lineWithoutSupplier.item?._id || '',
                    lineWithoutSupplier.unit?._id || '',
                    parseFloat(lineWithoutSupplier.quantity || '0'),
                    new Date(parameters.requestDate),
                    false,
                );
                lineWithoutSupplier.grossPrice = String(purchasePrice.price);
                if (purchasePrice.priceOrigin) {
                    lineWithoutSupplier.priceOrigin = purchasePrice.priceOrigin;
                }
                if (!parameters.isCalledFromRecordPage) {
                    await parameters.purchaseRequisitionPage.$.graph
                        .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
                        .mutations.applyDefaultSupplier(true, {
                            purchaseRequisitionLine: lineWithoutSupplier._id,
                            supplier: lineWithoutSupplier.supplier._id,
                            quantity: Number(lineWithoutSupplier.quantity),
                            grossPrice: Number(lineWithoutSupplier.grossPrice),
                            netPrice: Number(lineWithoutSupplier.netPrice),
                            totalTaxExcludedAmount: Number(lineWithoutSupplier.totalTaxExcludedAmount),
                            priceOrigin: lineWithoutSupplier.priceOrigin,
                        })
                        .execute();
                }
            }
            return lineWithoutSupplier;
        }),
    );
    if (!parameters.isCalledFromRecordPage) {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__apply_default_supplier_success',
                'Default supplier applied',
            ),
            { type: 'success' },
        );
    }
    await parameters.purchaseRequisitionPage.$.router.refresh();
    await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
    return linesWithoutSupplier as ui.PartialNodeWithId<PurchaseRequisitionLine>[];
}

export async function getPurchaseRequisitionLines(parameters: ActionsParameter) {
    return extractEdges(
        await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        site: { _id: true },
                        _sortValue: true,
                        document: {
                            _id: true,
                        },
                        item: {
                            _id: true,
                            name: true,
                            id: true,
                            stockUnit: {
                                _id: true,
                                id: true,
                            },
                            type: true,
                        },
                        netPrice: true,
                        totalTaxExcludedAmount: true,
                        status: true,
                        grossPrice: true,
                        priceOrigin: true,
                        quantity: true,
                        supplier: { _id: true },
                        currency: {
                            _id: true,
                            name: true,
                            id: true,
                            decimalDigits: true,
                            rounding: true,
                        },
                    },

                    {
                        filter: {
                            document: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function confirmAction(parameters: ConfirmActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_title',
                'Confirm purchase requisition',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__confirm_dialog_content',
                'You are about to set this purchase requisition to "Confirmed"',
            ),
            ui.localize('@sage/xtrem-purchasing/pages_confirm_button', 'Confirm'),
        ))
    ) {
        parameters.purchaseRequisitionPage.$.loader.isHidden = false;
        const purchaseRequisition = await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisition')
            .mutations.confirm({ approvalStatus: true }, { document: parameters.recordId || '' })
            .execute();
        if (purchaseRequisition && purchaseRequisition.approvalStatus === 'confirmed') {
            parameters.purchaseRequisitionPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__confirm_status_updated',
                    'Status updated.',
                ),
                { type: 'success' },
            );
        }
        await parameters.purchaseRequisitionPage.$.router.refresh();
        await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
        parameters.purchaseRequisitionPage.$.loader.isHidden = true;
    }
}

async function getPurchaseRequisitionLinesForDimensions(parameters: ActionsParameter) {
    return extractEdges(
        await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        status: true,
                    },

                    {
                        filter: {
                            document: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseRequisitionLines = await getPurchaseRequisitionLinesForDimensions({
        purchaseRequisitionPage: parameters.purchaseRequisitionPage,
        recordId: parameters.recordId,
    });
    const linesToSetDimensions = purchaseRequisitionLines.filter(
        line => !isPurchaseRequisitionLineActionDisabled(parameters.status || '', line.status || '', 'dimensions'),
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            if (!dataDetermined) {
                const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                    page: parameters.purchaseRequisitionPage,
                    site: parameters.site || null,
                    supplier: parameters.supplier || null,
                });
                const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                    await getDimensionsForPurchaseLines({
                        line: lineToSetDimensions,
                        purchasePage: parameters.purchaseRequisitionPage,
                        defaultDimensionsAttributes,
                        defaultedFromItem,
                    });
                if (!resultDataDetermined) {
                    break;
                }
                dimensionsToSet = resultDimensionsToSet;
                attributesToSet = resultAttributesToSet;
                dataDetermined = resultDataDetermined;
            }
        }
        if (dataDetermined) {
            await parameters.purchaseRequisitionPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.purchaseRequisitionPage.$.showToast(
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__apply_dimensions_success',
            'Dimensions applied.',
        ),
        { type: 'success' },
    );
}

async function approveOrReject(parameters: ApproveRejectActionParameter) {
    parameters.purchaseRequisitionPage.$.loader.isHidden = false;
    if (
        await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisition')
            .mutations.approve(true, {
                document: parameters.purchaseRequisitionPage.$.recordId ?? parameters.recordId,
                approve: parameters.approve,
            })
            .execute()
    ) {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__approval_status_updated',
                'Approval status updated.',
            ),
            { type: 'success' },
        );
    }
    await parameters.purchaseRequisitionPage.$.router.refresh();
    await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
    parameters.purchaseRequisitionPage.$.loader.isHidden = true;
}

export async function approve(parameters: ApproveRejectActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_title', 'Confirm approval'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__approve_dialog_content',
                'You are about to approve this requisition.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
        )
    ) {
        await approveOrReject(parameters);
    }
}
export async function reject(parameters: ApproveRejectActionParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_title', 'Confirm rejection'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__reject_dialog_content',
                'You are about to reject this requisition.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-reject', 'Reject'),
        )
    ) {
        await approveOrReject(parameters);
    }
}

export async function close(parameters: ActionsParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseRequisitionPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_title',
                'Confirm status change',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__close_order_dialog_content',
                'You are about to change the status of this requisition to closed. You cannot undo this change.',
            ),
            ui.localize('@sage/xtrem-purchasing/closeRequisition____title', 'Close requisition'),
        )
    ) {
        parameters.purchaseRequisitionPage.$.loader.isHidden = false;

        if (
            await parameters.purchaseRequisitionPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisition')
                .mutations.close(true, { purchaseRequisition: parameters.recordId || '' })
                .execute()
        ) {
            parameters.purchaseRequisitionPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__status_updated',
                    'Requisition status updated.',
                ),
                { type: 'success' },
            );
        } else {
            parameters.purchaseRequisitionPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__status_not_updated',
                    'Unable to update status.',
                ),
                { type: 'error' },
            );
        }
        await parameters.purchaseRequisitionPage.$.router.refresh();
        await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
        parameters.purchaseRequisitionPage.$.loader.isHidden = true;
    }
}
export async function loadApprovers(
    parameters: LoadApproversParameters,
): Promise<{ usersList: UserApprover[]; emailAddressApproval: string }> {
    let purchaseRequisitionDefaultApprover: userInterfaces.FilteredUsers | null = null;
    let purchaseRequisitionSubstituteApprover: userInterfaces.FilteredUsers | null = null;
    let emailAddressApproval = '';
    const usersList: UserApprover[] = [];
    let sortOrder = 0;

    const usersQuery = (await parameters.purchaseRequisitionPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseOrder')
        .queries.getFilteredUsers(
            {
                _id: true,
                email: true,
                firstName: true,
                lastName: true,
            },
            {
                criteria: authorizationFilters.user.activeApplicationUsers,
            },
        )
        .execute()) as userInterfaces.FilteredUsers[];

    const siteApprovers: SiteApprover[] = withoutEdges(
        await parameters.purchaseRequisitionPage.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        id: true,
                        purchaseRequisitionDefaultApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                        purchaseRequisitionSubstituteApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    {
                        filter: {
                            _id: parameters.siteId,
                        },
                    },
                ),
            )
            .execute(),
    );

    if (siteApprovers.length > 0) {
        purchaseRequisitionDefaultApprover = siteApprovers[0].purchaseRequisitionDefaultApprover
            ? siteApprovers[0].purchaseRequisitionDefaultApprover
            : null;
        purchaseRequisitionSubstituteApprover = siteApprovers[0].purchaseRequisitionSubstituteApprover
            ? siteApprovers[0].purchaseRequisitionSubstituteApprover
            : null;

        if (purchaseRequisitionDefaultApprover?._id) {
            usersList.push({
                ...purchaseRequisitionDefaultApprover,
                type: ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__default_approver', 'Default'),
                sortOrder,
            });
            sortOrder += 1;
            emailAddressApproval = purchaseRequisitionDefaultApprover.email;
        }
        if (
            purchaseRequisitionSubstituteApprover &&
            purchaseRequisitionSubstituteApprover?._id !== purchaseRequisitionDefaultApprover?._id
        ) {
            usersList.push({
                ...purchaseRequisitionSubstituteApprover,
                type: ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__substitute_approver',
                    'Substitute',
                ),
                sortOrder,
            });
            sortOrder += 1;
            if (!purchaseRequisitionDefaultApprover) {
                emailAddressApproval = purchaseRequisitionSubstituteApprover.email;
            }
        }
    }
    usersList.push(
        ...usersQuery
            .filter(
                userFilter =>
                    ![purchaseRequisitionDefaultApprover?._id, purchaseRequisitionSubstituteApprover?._id].includes(
                        userFilter._id,
                    ),
            )
            .map((user, index) => ({
                ...user,
                type: '',
                sortOrder: sortOrder + index,
            })),
    );
    return { usersList, emailAddressApproval };
}

function confirmPurchaseOrderCreation(purchaseRequisitionPage: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        purchaseRequisitionPage,
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_title',
            'Confirm purchase order creation',
        ),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_content',
            'You are about to create a purchase order from this requisition.',
        ),
        ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
    );
}

/** Only lines containing a supplier are included */
function onlyLinesWithSupplier(purchaseRequisitionPage: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        purchaseRequisitionPage,
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_title',
            'Confirm purchase order creation',
        ),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_requisition__create_order_dialog_lines_without_supplier',
            'Only lines containing a supplier are included.',
        ),
        ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
    );
}

export async function createPurchaseOrderAction(parameters: CreateActionParameter) {
    const { lines } = parameters;
    if (await confirmPurchaseOrderCreation(parameters.purchaseRequisitionPage)) {
        if (lines.length && lines.every(row => !row.supplier?._id && row.status === 'pending')) {
            throw new Error(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition_select_supplier',
                    'You need to select a supplier before creating an order.',
                ),
            );
        }
        if (lines.some(row => !row.supplier?._id && row.status === 'pending')) {
            if (!(await onlyLinesWithSupplier(parameters.purchaseRequisitionPage))) {
                return;
            }
            return;
        }
    }

    parameters.purchaseRequisitionPage.$.loader.isHidden = false;
    const orders = (await parameters.purchaseRequisitionPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseRequisition')
        .mutations.createPurchaseOrders({ number: true }, { document: parameters.recordId })
        .execute()) as { number: string }[];

    if (orders.length) {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__purchase_order_created',
                'Purchase order(s) created: {{orderNumbers}}.',
                {
                    orderNumbers: orders.map(order => order.number).join('\n'),
                },
            ),
            { type: 'success' },
        );
    } else {
        parameters.purchaseRequisitionPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__Purchase_order_not_created',
                'Could not create purchase order.',
            ),
            { type: 'error' },
        );
    }

    parameters.purchaseRequisitionPage.$.setPageClean();

    await parameters.purchaseRequisitionPage.$.router.refresh();
    await parameters.purchaseRequisitionPage.$.refreshNavigationPanel();
    parameters.purchaseRequisitionPage.$.loader.isHidden = true;
}
