import type { decimal, ExtractEdgesPartial } from '@sage/xtrem-client';
import { notesOverwriteWarning } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type { FinanceUnbilledAccountPayable } from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/accounting-integration';
import type * as dimensionPanelInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/index';
import type { Item, Supplier, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as masterDataCommon from '@sage/xtrem-master-data/build/lib/client-functions/common';
import type {
    PurchaseOrderLine,
    PurchaseOrderLineBinding,
    PurchaseReceiptLineBinding,
    PurchaseRequisitionLine,
    PurchaseRequisitionLineToPurchaseOrderLine,
    GraphApi as PurchasingApi,
} from '@sage/xtrem-purchasing-api';
import type { Site, GraphApi as SystemApi } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { PurchasePrice } from './interfaces/common';
import type { PrintParameters } from './interfaces/purchase-receipt-actions-functions';

/** deprecated , please use getItemSupplierPrice */
export async function getPurchasePrice(
    graphApi: ui.GraphQLApi<PurchasingApi>,
    siteId: string,
    supplierId: string,
    currencyId: string,
    itemId: string,
    unitId: string,
    quantity: decimal,
    date: Date,
    convertUnit: boolean,
): Promise<PurchasePrice> {
    if (currencyId && itemId && unitId) {
        const price =
            +(await graphApi
                .node('@sage/xtrem-master-data/ItemSupplierPrice')
                .queries.getPurchasePrice(false, {
                    priceParameters: {
                        site: siteId,
                        supplier: supplierId,
                        currency: currencyId,
                        item: itemId,
                        quantity,
                        unit: unitId,
                        date: date.toISOString().substring(0, 10),
                        convertUnit,
                    },
                })
                .execute()) || 0;
        if (price >= 0) {
            return {
                priceOrigin: 'supplierPriceList',
                price,
            };
        }
    }
    return { priceOrigin: 'manual', price: 0 };
}

export const printPurchaseOrder = async (parameters: PrintParameters) => {
    const { page, _id } = parameters;
    await page.$.dialog
        .page(
            '@sage/xtrem-reporting/PrintDocument',
            { reportName: 'purchaseOrder', order: _id },
            { size: 'extra-large', resolveOnCancel: true },
        )
        .then(async () => {
            page.$.showToast(
                ui.localize('@sage/xtrem-purchasing/pages__purchase_order__printed', 'The purchase order was printed.'),
                { type: 'success' },
            );
            await page.$.router.refresh();
            await page.$.refreshNavigationPanel();
        });
};

export const printPurchaseReceipt = async (parameters: PrintParameters) => {
    const { page, _id } = parameters;

    await page.$.dialog
        .page(
            '@sage/xtrem-reporting/PrintDocument',
            { reportName: 'purchaseReceipt', receipt: _id },
            { size: 'extra-large', resolveOnCancel: true },
        )
        .then(async () => {
            page.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_receipt__printed',
                    'The purchase receipt was printed.',
                ),
                { type: 'success' },
            );
            await page.$.router.refresh();
            await page.$.refreshNavigationPanel();
        });
};

export function confirmDialogWithAcceptButtonText(
    page: ui.Page,
    title: string,
    message: string,
    acceptButtonText: string,
    cancelButtonText?: string,
) {
    const options = {
        acceptButton: { text: acceptButtonText },
        cancelButton: {
            text: cancelButtonText || ui.localize('@sage/xtrem-purchasing/pages-confirm-cancel', 'Cancel'),
        },
    };
    return page.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}

export function setOverwriteNote(
    page: ui.Page,
    documentSetup: {
        linesFromSingleDocument: boolean;
        isTransferHeaderNote: boolean;
        isTransferLineNote: boolean;
        headerNotesChanged: boolean;
        lineNotesChanged: boolean;
    },
) {
    if (
        documentSetup.linesFromSingleDocument &&
        ((documentSetup.headerNotesChanged && documentSetup.isTransferHeaderNote) ||
            (documentSetup.lineNotesChanged && documentSetup.isTransferLineNote))
    ) {
        return notesOverwriteWarning(page);
    }
    return false;
}

/**
 * Get dimensions and attributes to set them in purchase lines from main grid
 * @param line
 * @param purchasePage
 */
export async function getDimensionsForPurchaseLines(options: {
    line: ui.PartialNodeWithId<Node>;
    purchasePage: ui.Page<SystemApi>;
    defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions;
    defaultedFromItem?: string;
}) {
    const purchaseInvoiceBase = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(options.line);
    const rowWithDimensions = dimensionPanelHelpers.editDisplayDimensions(
        options.purchasePage,
        { documentLine: purchaseInvoiceBase },
        { editable: true, calledFromMainGrid: true },
        options.defaultDimensionsAttributes,
        options.defaultedFromItem,
    );
    if ((await rowWithDimensions).storedAttributes !== '' || (await rowWithDimensions).storedDimensions !== '') {
        return {
            resultDimensionsToSet: (await rowWithDimensions).storedDimensions,
            resultAttributesToSet: (await rowWithDimensions).storedAttributes,
            resultDataDetermined: true,
        };
    }
    return { resultDimensionsToSet: '', resultAttributesToSet: '', resultDataDetermined: false };
}

export function checkPurchaseOrderLineMinimumQuantity(
    pageInstance: ui.Page<PurchasingApi>,
    data: {
        supplierMinimumQuantity?: decimal;
        supplierPurchaseUnit?: ExtractEdgesPartial<UnitOfMeasure> | null;

        unit?: ExtractEdgesPartial<UnitOfMeasure> | null;
        purchaseQuantity?: decimal | null;
    },
) {
    const supplierMinimumQuantity = data.supplierMinimumQuantity ?? 0;
    const supplierPurchaseUnit = data.supplierPurchaseUnit ?? data.unit;

    if (
        supplierMinimumQuantity > 0 &&
        supplierPurchaseUnit &&
        data.purchaseQuantity &&
        data.purchaseQuantity < supplierMinimumQuantity &&
        data.unit?._id === supplierPurchaseUnit?._id
    ) {
        pageInstance.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/page__reorder_purchase_order_panel__lower-than-min-quantity',
                'The purchase quantity is lower than the minimum quantity ({{supplierMinimumQuantity}}).',
                { supplierMinimumQuantity },
            ),
            { type: 'warning' },
        );
    }
}

export async function setPurchaseUnitQuantity(
    pageInstance: ui.Page<PurchasingApi>,
    data: {
        purchaseQuantityField: ui.fields.Numeric;

        stockUnit?: ExtractEdgesPartial<UnitOfMeasure> | null;
        unit?: ExtractEdgesPartial<UnitOfMeasure> | null;
        stockQuantity?: decimal | null;

        item?: ExtractEdgesPartial<Item> | null;
        supplier?: ExtractEdgesPartial<Supplier> | null;

        supplierMinimumQuantity?: decimal;
        supplierPurchaseUnit?: ExtractEdgesPartial<UnitOfMeasure> | null;
        formatToUnitDecimalDigits?: boolean;
    },
) {
    if (data.stockUnit?._id && data.unit?._id && data.stockQuantity && data.stockUnit._id !== data.unit._id) {
        data.purchaseQuantityField.value = (
            await masterDataCommon.convertFromTo(
                pageInstance.$.graph,
                data.stockUnit._id,
                data.unit._id,
                data.stockQuantity,
                data.item?._id || undefined,
                data.supplier?._id || undefined,
                '',
                'purchase',
                data.formatToUnitDecimalDigits ?? false,
            )
        ).convertedQuantity;
        checkPurchaseOrderLineMinimumQuantity(pageInstance, {
            supplierMinimumQuantity: data.supplierMinimumQuantity,
            supplierPurchaseUnit: data.supplierPurchaseUnit,
            unit: data.unit,
            purchaseQuantity: data.purchaseQuantityField.value ?? 0,
        });
    } else {
        data.purchaseQuantityField.value = data.stockQuantity ?? 0;
    }
}

export async function setStockUnitQuantity(
    pageInstance: ui.Page<PurchasingApi>,
    data: {
        stockQuantityField: ui.fields.Numeric;

        stockUnit?: ExtractEdgesPartial<UnitOfMeasure> | null;
        unit?: ExtractEdgesPartial<UnitOfMeasure> | null;
        purchaseQuantity?: decimal | null;

        item?: ExtractEdgesPartial<Item> | null;
        supplier?: ExtractEdgesPartial<Supplier> | null;
        formatToUnitDecimalDigits?: boolean;
    },
) {
    if (data.unit?._id && data.stockUnit?._id && data.purchaseQuantity && data.unit._id !== data.stockUnit._id) {
        data.stockQuantityField.value = (
            await masterDataCommon.convertFromTo(
                pageInstance.$.graph,
                data.unit._id,
                data.stockUnit._id,
                data.purchaseQuantity,
                data.item?._id || undefined,
                data.supplier?._id || undefined,
                '',
                'purchase',
                data.formatToUnitDecimalDigits ?? false,
            )
        ).convertedQuantity;
    } else {
        data.stockQuantityField.value = data.purchaseQuantity ?? 0;
    }
}

export async function setPurchaseGrossPriceAndPriceOrigin(
    pageInstance: ui.Page<PurchasingApi>,
    data: {
        grossPriceField: ui.fields.Numeric;
        priceOriginField?: ui.fields.Label;

        supplier?: ExtractEdgesPartial<Supplier> | null;
        purchasingSite: ExtractEdgesPartial<Site> | null;
        item?: ExtractEdgesPartial<Item> | null;
        unit?: ExtractEdgesPartial<UnitOfMeasure> | null;
        purchaseQuantity?: decimal | null;
        orderDate?: string | null;
    },
) {
    const itemId = data.item?._id;
    const purchaseUnitId = data.unit?._id;

    if (
        data.supplier?._id &&
        data.purchasingSite?._id &&
        data.supplier?.currency?._id &&
        itemId &&
        purchaseUnitId &&
        data.purchaseQuantity &&
        data.orderDate
    ) {
        const purchasePrice = await getPurchasePrice(
            pageInstance.$.graph,
            data.purchasingSite._id,
            data.supplier._id,
            data.supplier?.currency?._id,
            itemId,
            purchaseUnitId,
            data.purchaseQuantity,
            new Date(data.orderDate),
            false,
        );

        data.grossPriceField.value = purchasePrice.price;
        if (data.priceOriginField) {
            data.priceOriginField.value = purchasePrice.priceOrigin;
        }
    }
}

/**
 * Get site and supplier as partial nodes
 * @param page
 * @param siteId _id of the site to read from DB
 * @param supplierId _id of the supplier to read from DB
 * @returns Promise<{ site: ExtractEdgesPartial<Site>; supplier: ExtractEdgesPartial<Supplier> }>
 */
export async function getSiteAndSupplier(options: {
    page: ui.Page<SystemApi>;
    siteId: string;
    supplierId: string;
}): Promise<{ site: ExtractEdgesPartial<Site>; supplier: ExtractEdgesPartial<Supplier> | null }> {
    const site = await options.page.$.graph
        .node('@sage/xtrem-system/Site')
        .read(
            {
                _id: true,
                id: true,
                legalCompany: {
                    _id: true,
                },
                storedAttributes: true,
                storedDimensions: true,
            },
            options.siteId,
        )
        .execute();
    const supplier = options.supplierId
        ? await options.page.$.graph
              .node('@sage/xtrem-master-data/Supplier')
              .read(
                  {
                      _id: true,
                      storedAttributes: true,
                      storedDimensions: true,
                  },
                  options.supplierId,
              )
              .execute()
        : null;
    return { site, supplier };
}

/**
 * Get a string with all attributes defaulted from item and default dimensions attributes to show on 'Set dimensions'
 * dialog called from main list
 * @param page
 * @param site
 * @param supplier
 * @returns Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }>
 */
export async function getValuesForSetDimensionsFromMainList(options: {
    page: ui.Page<SystemApi>;
    site: ExtractEdgesPartial<Site> | null;
    supplier: ExtractEdgesPartial<Supplier> | null;
}): Promise<{ defaultedFromItem: string; defaultDimensionsAttributes: dimensionPanelInterfaces.DefaultDimensions }> {
    const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
        page: options.page,
        dimensionDefinitionLevel: 'purchasingDirect',
        companyId: Number(options.site?.legalCompany?._id),
    });
    const defaultDimensionsAttributes = await attributesAndDimensions.initDefaultDimensions({
        page: options.page,
        dimensionDefinitionLevel: 'purchasingDirect',
        site: options.site || null,
        supplier: options.supplier,
    });

    return { defaultedFromItem, defaultDimensionsAttributes };
}

export function isFinanceUnbilledAccountPayable(rowData: any): rowData is FinanceUnbilledAccountPayable {
    if (rowData.receiptInternalId && rowData.receiptNumber) {
        return !!rowData;
    }
    return false;
}

export function isUiPartialCollectionValuePurchaseRequisitionLine(
    rowData: any,
): rowData is ui.PartialCollectionValue<PurchaseRequisitionLine> {
    if (rowData.stockUnit?.symbol || rowData.unit?.symbol || rowData.currency?.symbol) {
        return !!rowData;
    }
    return false;
}

export function isPurchaseRequisitionLineToPurchaseOrderLine(
    rowData: any,
): rowData is PurchaseRequisitionLineToPurchaseOrderLine {
    if (rowData.purchaseOrderLine) {
        return !!rowData;
    }
    return false;
}

export function isUiPartialCollectionValuePurchaseReceiptLineBinding(
    rowData: any,
): rowData is ui.PartialCollectionValue<PurchaseReceiptLineBinding> {
    if (!rowData) {
        return false;
    }
    return !!rowData;
}

export function isPurchaseOrderLine(rowData: any): rowData is PurchaseOrderLine {
    if (rowData.item.type) {
        return !!rowData;
    }
    return false;
}

export function isPurchaseOrderLineBinding(rowData: any): rowData is PurchaseOrderLineBinding {
    if (rowData.item.stockUnit.symbol) {
        return !!rowData;
    }
    return false;
}

export function getNotFullyPaidFilter() {
    return {
        title: 'Not fully paid',
        graphQLFilter: { displayStatus: { _in: ['posted', 'partiallyPaid'] } },
    };
}

export function getFullyPaidFilter() {
    return {
        title: 'Fully paid',
        graphQLFilter: { displayStatus: { _eq: 'paid' } },
    };
}
