import type {
    PurchaseCreditMemoStatus,
    PurchaseDocumentApprovalStatus,
    PurchaseDocumentStatus,
    PurchaseInvoiceMatchingStatus,
    PurchaseInvoiceStatus,
    PurchaseOrderInvoiceStatus,
    PurchaseOrderReceiptStatus,
    PurchaseReceiptInvoiceStatus,
    PurchaseReceiptReturnStatus,
    PurchaseRequisitionOrderStatus,
    PurchaseReturnShippingStatus,
    UnbilledAccountPayableStatus,
} from '@sage/xtrem-purchasing-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

// document statuses
function purchaseOrderInvoiceStatusColor(status: PurchaseOrderInvoiceStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notInvoiced':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'partiallyInvoiced':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'invoiced':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseReceiptInvoiceStatusColor(status: PurchaseReceiptInvoiceStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notInvoiced':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'partiallyInvoiced':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'invoiced':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseReceiptReturnStatusColor(status: PurchaseReceiptReturnStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notReturned':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'partiallyReturned':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'returned':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseRequisitionOrderStatusColor(status: PurchaseRequisitionOrderStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notOrdered':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'partiallyOrdered':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'ordered':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseReturnShippingStatusColor(status: PurchaseReturnShippingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notShipped':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'partiallyShipped':
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case 'shipped':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseInvoiceMatchingStatusColor(status: PurchaseInvoiceMatchingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'noVariance':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'variance':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'varianceApproved':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseOrderReceiptStatusColor(status: PurchaseOrderReceiptStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notReceived':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'partiallyReceived':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'received':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseDocumentApprovalStatusColor(status: PurchaseDocumentApprovalStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pendingApproval':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'approved':
        case 'confirmed':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'rejected':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'changeRequested':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

// header's document statuses
function purchaseInvoiceStatusColor(status: PurchaseInvoiceStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseCreditMemoStatusColor(status: PurchaseCreditMemoStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function purchaseDocumentStatusColor(status: PurchaseDocumentStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function UnbilledAccountPayableStatusColor(status: UnbilledAccountPayableStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'completed':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?:
        | PurchaseCreditMemoStatus
        | PurchaseDocumentApprovalStatus
        | PurchaseDocumentStatus
        | PurchaseInvoiceMatchingStatus
        | PurchaseInvoiceStatus
        | PurchaseOrderInvoiceStatus
        | PurchaseOrderReceiptStatus
        | PurchaseReceiptInvoiceStatus
        | PurchaseReceiptReturnStatus
        | PurchaseRequisitionOrderStatus
        | PurchaseReturnShippingStatus
        | UnbilledAccountPayableStatus
        | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'PurchaseOrderInvoiceStatus':
                return purchaseOrderInvoiceStatusColor(status as PurchaseOrderInvoiceStatus, coloredElement);
            case 'PurchaseReceiptInvoiceStatus':
                return purchaseReceiptInvoiceStatusColor(status as PurchaseReceiptInvoiceStatus, coloredElement);
            case 'PurchaseReceiptReturnStatus':
                return purchaseReceiptReturnStatusColor(status as PurchaseReceiptReturnStatus, coloredElement);
            case 'PurchaseRequisitionOrderStatus':
                return purchaseRequisitionOrderStatusColor(status as PurchaseRequisitionOrderStatus, coloredElement);
            case 'PurchaseReturnShippingStatus':
                return purchaseReturnShippingStatusColor(status as PurchaseReturnShippingStatus, coloredElement);
            case 'PurchaseInvoiceMatchingStatus':
                return purchaseInvoiceMatchingStatusColor(status as PurchaseInvoiceMatchingStatus, coloredElement);
            case 'PurchaseOrderReceiptStatus':
                return purchaseOrderReceiptStatusColor(status as PurchaseOrderReceiptStatus, coloredElement);
            case 'PurchaseDocumentApprovalStatus':
                return purchaseDocumentApprovalStatusColor(status as PurchaseDocumentApprovalStatus, coloredElement);
            case 'PurchaseInvoiceStatus':
                return purchaseInvoiceStatusColor(status as PurchaseInvoiceStatus, coloredElement);
            case 'PurchaseCreditMemoStatus':
                return purchaseCreditMemoStatusColor(status as PurchaseCreditMemoStatus, coloredElement);
            case 'PurchaseDocumentStatus':
                return purchaseDocumentStatusColor(status as PurchaseDocumentStatus, coloredElement);
            case 'UnbilledAccountPayableStatus':
                return UnbilledAccountPayableStatusColor(status as UnbilledAccountPayableStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };
    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    switch (booleanEntry) {
        default:
            return colorfulPillPatternDefaulted(coloredElement);
    }
}
