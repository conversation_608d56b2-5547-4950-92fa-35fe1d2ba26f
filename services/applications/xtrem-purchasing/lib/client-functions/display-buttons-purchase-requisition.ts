import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-purchase-requisition';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-purchase-requisition';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'draft' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'inProgress'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (
            data.parameters.status === 'draft' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'inProgress'
        ) {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (
        data.recordId &&
        data.parameters.approvalStatus &&
        (data.parameters.status === 'draft' ||
            (data.parameters.status === 'pending' &&
                ['confirmed', 'pendingApproval'].includes(data.parameters.approvalStatus)))
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.DeleteParameters>,
) {
    return isHiddenButtonDeleteAction(data);
}

/* -------------------*/

export function isHiddenButtonApproveAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ApproveParameters>,
): boolean {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return !!data.isDirty;
    }
    return true;
}

export function isDisabledButtonApproveAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ApproveParameters>,
) {
    return isHiddenButtonApproveAction(data);
}

/* -------------------*/

export function isHiddenButtonRejectAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RejectParameters>,
): boolean {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return !!data.isDirty;
    }
    return true;
}

export function isDisabledButtonRejectAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RejectParameters>,
) {
    return isHiddenButtonRejectAction(data);
}

/* -------------------*/

export function isHiddenButtonRequestApprovalAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RequestApprovalParameters>,
): boolean {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.isApprovalManaged &&
        data.parameters.approvalStatus &&
        !['pendingApproval', 'approved', 'rejected', 'confirmed'].includes(data.parameters.approvalStatus)
    ) {
        return !!data.isDirty;
    }
    return true;
}

export function isDisabledButtonRequestApprovalAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RequestApprovalParameters>,
) {
    return isHiddenButtonRequestApprovalAction(data);
}

/* -------------------*/

export function isHiddenButtonRequestChangesAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RequestChangesParameters>,
) {
    if (data.recordId && data.parameters.approvalStatus === 'changeRequested') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRequestChangesAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RequestChangesParameters>,
) {
    return isHiddenButtonRequestChangesAction(data);
}

/* -------------------*/

export function isHiddenButtonCloseRequisitionAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CloseParameters>,
): boolean {
    if (
        data.recordId &&
        data.parameters.status &&
        !(['draft', 'closed'].includes(data.parameters.status) || data.parameters.approvalStatus === 'pendingApproval')
    ) {
        return !!data.isDirty;
    }
    return true;
}

export function isDisableButtonCloseRequisitionAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CloseParameters>,
) {
    return isHiddenButtonCloseRequisitionAction(data);
}

/* -------------------*/

export function isHiddenButtonCreatePurchaseOrderAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreatePurchaseOrderParameters>,
): boolean {
    if (
        data.parameters.status !== 'draft' &&
        data.parameters.status !== 'closed' &&
        (data.parameters.approvalStatus === 'approved' || data.parameters.approvalStatus === 'confirmed') &&
        data.parameters.receivingSite &&
        data.parameters.islinesCreateOrder
    ) {
        return !!data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreatePurchaseOrderAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CreatePurchaseOrderParameters>,
) {
    return isHiddenButtonCreatePurchaseOrderAction(data);
}

/* -------------------*/

function commonIsHiddenProperties(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    return (
        (data.parameters &&
            data.parameters.status &&
            !['closed', 'pending', 'inProgress'].includes(data.parameters.status) &&
            data.parameters.receivingSite &&
            data.parameters.requestDate &&
            data.parameters.requester) ||
        (data.parameters &&
            data.parameters.status &&
            !['closed', 'inProgress'].includes(data.parameters.status) &&
            data.parameters.receivingSite &&
            data.parameters.requestDate &&
            data.parameters.requester &&
            data.parameters.approvalStatus &&
            ['confirmed'].includes(data.parameters.approvalStatus))
    );
}

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (commonIsHiddenProperties(data)) {
        return false;
    }
    return true;
}

export function isDisabledButtonAddPurchaseRequisitionLineAction(
    data: isDisabledButtonsCommon.TableFieldActionsActionButtonParameters<isDisabledButtons.AddPurchaseRequisitionLineParameters>,
) {
    if (
        data.parameters &&
        data.parameters.status !== 'closed' &&
        data.parameters.receivingSite &&
        data.parameters.requestDate &&
        data.parameters.requester
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.receivingSite && data.parameters.requestDate && data.parameters.requester) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ConfirmParameters>,
): boolean {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        !data.parameters.isApprovalManaged &&
        data.parameters.approvalStatus &&
        !['pendingApproval', 'approved', 'rejected', 'changeRequested', 'confirmed'].includes(
            data.parameters.approvalStatus,
        )
    ) {
        return !!data.isDirty;
    }
    return true;
}

export function isDisabledButtonConfirmAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ConfirmParameters>,
) {
    return isHiddenButtonConfirmAction(data);
}

/* -------------------*/
