import { extractEdges } from '@sage/xtrem-client';
import type { PurchaseReturn as PurchaseReturnNode } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    ActionParameter,
    ActionParameterIdAndNum,
    ActionParameterWithId,
    ApproveOrRejectActionParameters,
    RejectActionParameters,
    SetDimensionActionParameter,
    ConfirmActionParameters,
} from './interfaces/purchase-return-actions-functions';

async function getPurchaseReturnLinesForDimensions(parameters: ActionParameter) {
    return extractEdges(
        await parameters.purchaseReturnPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReturnLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                        status: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

async function getPurchaseReturnLine(parameters: ActionParameterIdAndNum) {
    return extractEdges(
        await parameters.purchaseReturnPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReturnLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        allocationStatus: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function close(parameters: ActionParameterIdAndNum) {
    let confirmationMessage;
    const purchaseReturnLines = await getPurchaseReturnLine({
        purchaseReturnPage: parameters.purchaseReturnPage,
        recordNumber: parameters.recordNumber,
        recordId: parameters.recordId,
    });
    if (purchaseReturnLines.some(line => ['allocated', 'partiallyAllocated'].includes(line.allocationStatus))) {
        confirmationMessage = ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_2',
            'You are about to change the status of this return to closed and reverse all stock allocations. You cannot undo this change.',
        );
    } else {
        confirmationMessage = ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_1',
            'You are about the change the status of this return to closed. You cannot undo this change.',
        );
    }
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseReturnPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_title',
                'Confirm status change',
            ),
            confirmationMessage,
            ui.localize('@sage/xtrem-purchasing/closeReturn____title', 'Close return'),
        )
    ) {
        if (
            await parameters.purchaseReturnPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReturn')
                .mutations.close(true, {
                    returnDoc: parameters.recordId,
                })
                .execute()
        ) {
            parameters.purchaseReturnPage.$.showToast(
                ui.localize('@sage/xtrem-purchasing/pages__purchase_return__status_updated', 'Return status updated.'),
                { type: 'success' },
            );
        }
    }
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const linesToSetDimensions = await getPurchaseReturnLinesForDimensions({
        purchaseReturnPage: parameters.purchaseReturnPage,
        recordNumber: parameters.recordNumber,
    });

    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            if (!dataDetermined) {
                const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                    page: parameters.purchaseReturnPage,
                    site: parameters.site || null,
                    supplier: parameters.supplier || null,
                });
                const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                    await getDimensionsForPurchaseLines({
                        line: lineToSetDimensions,
                        purchasePage: parameters.purchaseReturnPage,
                        defaultDimensionsAttributes,
                        defaultedFromItem,
                    });
                if (!resultDataDetermined) {
                    break;
                }
                dimensionsToSet = resultDimensionsToSet;
                attributesToSet = resultAttributesToSet;
                dataDetermined = resultDataDetermined;
            }
        }
        if (dataDetermined) {
            await parameters.purchaseReturnPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReturnLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.purchaseReturnPage.$.showToast(
        ui.localize('@sage/xtrem-purchasing/pages__purchase_return__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getStockTransactionStatus(parameters: ActionParameterWithId) {
    const purchaseReturn = extractEdges(
        await parameters.purchaseReturnPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReturn')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        stockTransactionStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as PurchaseReturnNode[];
    return purchaseReturn[0].stockTransactionStatus;
}

async function checkForUpdate(parameters: ActionParameterWithId) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter: number;
    refreshCounter = 0;
    const checkForStockTransaction = async () => {
        const stockTransStatus = await getStockTransactionStatus({
            purchaseReturnPage: parameters.purchaseReturnPage,
            recordId: parameters.recordId,
        });
        if (['completed', 'error'].includes(stockTransStatus)) {
            await parameters.purchaseReturnPage.$.router.refresh(true);
            await parameters.purchaseReturnPage.$.refreshNavigationPanel();
            parameters.purchaseReturnPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 10) {
            // TODO: XT-79005
            // eslint-disable-next-line
            setTimeout(checkForStockTransaction, 1000);
        }
    };
    await checkForStockTransaction();
}

export async function post(parameters: ActionParameterIdAndNum) {
    if (parameters.recordId !== null) {
        const purchaseReturnLines = await getPurchaseReturnLine({
            purchaseReturnPage: parameters.purchaseReturnPage,
            recordNumber: parameters.recordNumber,
            recordId: parameters.recordId,
        });
        if (purchaseReturnLines.some(line => ['notAllocated', 'partiallyAllocated'].includes(line.allocationStatus))) {
            throw new Error(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_return__post__allocation_status',
                    'You need to allocate stock to all lines before you can post.',
                ),
            );
        }
    }
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseReturnPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_return__post_action_dialog_title', 'Confirm posting'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__post_action_dialog_content',
                "You are about to set this purchase return to 'Returned'.",
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
        )
    ) {
        await parameters.purchaseReturnPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReturn')
            .mutations.post(true, { purchaseReturn: parameters.recordId })
            .execute();
        await checkForUpdate({ purchaseReturnPage: parameters.purchaseReturnPage, recordId: parameters.recordId });
        parameters.purchaseReturnPage.$.showToast(
            ui.localize('@sage/xtrem-purchasing/pages__purchase_return__post_from_main_row', 'Return posted'),
            { type: 'success' },
        );
    }
}

async function approveOrReject(parameters: ApproveOrRejectActionParameters) {
    parameters.purchaseReturnPage.$.loader.isHidden = false;
    const isApprove = await parameters.purchaseReturnPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseReturn')
        .mutations.approve(true, {
            document: parameters.recordId ?? '',
            toBeApproved: parameters.isApproved,
        })
        .execute();

    if (isApprove) {
        parameters.purchaseReturnPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__approval_status_updated',
                'Approval status updated.',
            ),
            { type: 'success' },
        );
        if (parameters.isCalledFromRecordPage && parameters.recordId) {
            parameters.purchaseReturnPage.$.setPageClean();
            parameters.purchaseReturnPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseReturn`, {
                _id: parameters.recordId,
            });
        }
    } else {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__approval_status_not_updated',
                'Unable to update approval status.',
            ),
        );
    }
    parameters.purchaseReturnPage.$.loader.isHidden = true;
}

export async function approveAction(parameters: ApproveOrRejectActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseReturnPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_return__approve_dialog_title', 'Confirm approval'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__approve_dialog_content',
                'You are about to approve this return.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
        ))
    ) {
        await approveOrReject(parameters);
    }
}

export async function rejectAction(parameters: RejectActionParameters) {
    if (parameters.recordId !== null) {
        const purchaseReturnLines = await getPurchaseReturnLine({
            purchaseReturnPage: parameters.purchaseReturnPage,
            recordNumber: parameters.recordNumber,
            recordId: parameters.recordId,
        });
        let confirmationContent;
        if (purchaseReturnLines.every(line => line.allocationStatus === 'notAllocated')) {
            confirmationContent = ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_content_1',
                'You are about to reject this purchase return. This action cannot be undone.',
            );
        } else {
            confirmationContent = ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_content_2',
                'You are about to reject this purchase return and reverse all stock allocations. This action cannot be undone.',
            );
        }
        if (
            await confirmDialogWithAcceptButtonText(
                parameters.purchaseReturnPage,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_return__reject_order_dialog_title',
                    'Confirm rejection',
                ),
                confirmationContent,
                ui.localize('@sage/xtrem-purchasing/pages-confirm-reject', 'Reject'),
            )
        ) {
            await approveOrReject({
                ...parameters,
                isApproved: !parameters.isRejected,
            });
        }
    }
}

export async function submitForApproval(parameters: ActionParameterWithId) {
    if (
        await parameters.purchaseReturnPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReturn')
            .mutations.submitForApproval(true, { document: parameters.recordId })
            .execute()
    ) {
        parameters.purchaseReturnPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__submit_for_approval_from_main_row',
                'Submitted for approval',
            ),
            { type: 'success' },
        );
    }
}

export async function confirmAction(parameters: ConfirmActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseReturnPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_return__confirm_dialog_title', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__confirm_dialog_content',
                'You are about to set this purchase return to "Confirmed"',
            ),
            ui.localize('@sage/xtrem-purchasing/pages_confirm_button', 'Confirm'),
        ))
    ) {
        parameters.purchaseReturnPage.$.loader.isHidden = false;
        const isConfirm = await parameters.purchaseReturnPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReturn')
            .mutations.confirm(true, { document: parameters.recordId || '' })
            .execute();

        if (isConfirm) {
            parameters.purchaseReturnPage.$.showToast(
                ui.localize('@sage/xtrem-purchasing/pages__purchase_return__confirm_status_updated', 'Status updated.'),
                { type: 'success' },
            );
        }
        await parameters.purchaseReturnPage.$.router.refresh();
        await parameters.purchaseReturnPage.$.refreshNavigationPanel();
        parameters.purchaseReturnPage.$.loader.isHidden = true;
    }
}
