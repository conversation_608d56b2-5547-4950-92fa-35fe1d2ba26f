import { DateValue } from '@sage/xtrem-date-time';
import type { BusinessRelationType } from '@sage/xtrem-master-data-api';
import type { PurchaseInvoice as PurchaseInvoicePage } from '../pages/purchase-invoice';

export function openRecordPage(page: PurchaseInvoicePage): Promise<string> {
    return page.$.dialog.page(
        '@sage/xtrem-finance/RecordPayment',
        {
            openParams: JSON.stringify({
                bankAccount: page.site.value?.legalCompany?.bankAccount,
                financialSite: page.site.value?.isFinance ? page.site.value : page.site.value?.financialSite,
                baseBusinessRelation: page.billBySupplier.value,
                type: 'supplier' as BusinessRelationType,
                date: DateValue.today().toString(),
                currency: page.currency.value,
                amount: page.netBalance.value ?? 0,
                number: page.number.value,
            }),
        },
        { size: 'extra-large', resolveOnCancel: true },
    );
}

export function openRecordPageFromMainList(parameters: {
    page: PurchaseInvoicePage;
    openParams: string;
}): Promise<string> {
    return parameters.page.$.dialog.page(
        '@sage/xtrem-finance/RecordPayment',
        {
            openParams: parameters.openParams,
        },
        { size: 'extra-large', resolveOnCancel: true },
    );
}
