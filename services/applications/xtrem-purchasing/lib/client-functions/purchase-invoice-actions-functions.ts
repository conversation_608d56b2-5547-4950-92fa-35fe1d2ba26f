import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { MutationResult } from '@sage/xtrem-finance-data/build/lib/shared-functions/interfaces/common';
import type { BusinessEntityAddress, Currency, PaymentTerm, Supplier } from '@sage/xtrem-master-data-api';
import type {
    PurchaseInvoiceLine as PurchaseInvoiceLineNode,
    PurchaseInvoice as PurchaseInvoiceNode,
} from '@sage/xtrem-purchasing-api';
import type { RegionLabel } from '@sage/xtrem-structure-api';
import * as ui from '@sage/xtrem-ui';
import type { PurchaseInvoice } from '../pages/purchase-invoice';
import { isPurchaseInvoiceLineActionDisabled } from '../shared-functions/edit-rules';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    AcceptAllVariancesParameter,
    ActionParameters,
    ActionParametersWithId,
    AddressDetails,
    CreateCreditMemoAction,
    FinanceIntegrationCheckParameter,
    PostActionParameter,
    SetDimensionActionParameter,
} from './interfaces/purchase-invoice-actions-functions';

async function getPurchaseInvoiceLinesForDimensions(parameters: ActionParameters) {
    return extractEdges(
        await parameters.purchaseInvoicePage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseInvoiceLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            number: true,
                        },
                        status: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseRequisitionLines = await getPurchaseInvoiceLinesForDimensions({
        purchaseInvoicePage: parameters.purchaseInvoicePage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = purchaseRequisitionLines.filter(
        line =>
            !isPurchaseInvoiceLineActionDisabled(parameters.status || '', line.status || '', 'dimensions') ||
            parameters.isRepost,
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            if (!dataDetermined) {
                const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                    page: parameters.purchaseInvoicePage,
                    site: parameters.site || null,
                    supplier: parameters.supplier || null,
                });
                const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                    await getDimensionsForPurchaseLines({
                        line: lineToSetDimensions,
                        purchasePage: parameters.purchaseInvoicePage,
                        defaultDimensionsAttributes,
                        defaultedFromItem,
                    });
                if (!resultDataDetermined) {
                    break;
                }
                dimensionsToSet = resultDimensionsToSet;
                attributesToSet = resultAttributesToSet;
                dataDetermined = resultDataDetermined;
            }
        }
        if (dataDetermined) {
            await parameters.purchaseInvoicePage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseInvoiceLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.purchaseInvoicePage.$.showToast(
        ui.localize('@sage/xtrem-purchasing/pages__purchase_invoice__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getDisplayStatus(parameters: ActionParametersWithId) {
    const purchasingInvoice = extractEdges(
        await parameters.purchaseInvoicePage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseInvoice')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as PurchaseInvoiceNode[];
    return purchasingInvoice[0].displayStatus;
}

async function checkForUpdate(parameters: ActionParametersWithId) {
    let refreshCounter: number;
    refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            purchaseInvoicePage: parameters.purchaseInvoicePage,
            recordId: parameters.recordId,
        });
        if (displayStatus === 'posted') {
            await parameters.purchaseInvoicePage.$.router.refresh(true);
            await parameters.purchaseInvoicePage.$.refreshNavigationPanel();
            if (
                (await getDisplayStatus({
                    purchaseInvoicePage: parameters.purchaseInvoicePage,
                    recordId: parameters.recordId,
                })) === 'postingError'
            ) {
                await parameters.purchaseInvoicePage.$.refreshNavigationPanel();
                await parameters.purchaseInvoicePage.$.router.refresh(true);
            }
            parameters.purchaseInvoicePage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter < 10) {
            // eslint-disable-next-line
            setTimeout(checkForDisplayStatus, 1000);
        }
    };
    await checkForDisplayStatus();
}

export async function postAction(parameters: PostActionParameter): Promise<MutationResult | null> {
    if (parameters.lines === 0) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_invoice__post__no_line',
                'You need to add lines before posting.',
            ),
        );
    }
    if (parameters.varianceTotalAmountExcludingTax !== 0 || parameters.varianceTotalTaxAmount !== 0) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_invoice__post__variance',
                'You need to resolve the total amount variances before posting.',
            ),
        );
    }
    if (parameters.taxCalculationStatus === 'failed') {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_invoice__post__tax_calculation_failed',
                'You need to resolve tax calculation issues before posting.',
            ),
        );
    }
    if (
        parameters.taxCalculationStatus === 'done' &&
        parameters.varianceTotalAmountExcludingTax === 0 &&
        parameters.varianceTotalTaxAmount === 0
    ) {
        if (
            await confirmDialogWithAcceptButtonText(
                parameters.purchaseInvoicePage,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__post_action_dialog_title',
                    'Confirm posting',
                ),
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__post_action_dialog_content',
                    'You are about to post this purchase invoice.',
                ),
                ui.localize('@sage/xtrem-purchasing/pages-confirm-post', 'Post'),
            )
        ) {
            if (!parameters.recordId) return null;
            // Disable post button so the user cannot post twice
            parameters.purchaseInvoicePage.post.isDisabled = true;
            parameters.purchaseInvoicePage.$.loader.isHidden = false;

            const postResult = await parameters.purchaseInvoicePage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseInvoice')
                .mutations.post(
                    {
                        wasSuccessful: true,
                        message: true,
                        validationMessages: {
                            message: true,
                            lineNumber: true,
                            sourceDocumentNumber: true,
                        },
                    },
                    { purchaseInvoice: parameters.recordId },
                )
                .execute();

            if (!postResult.wasSuccessful) {
                parameters.purchaseInvoicePage.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_invoice__post_errors',
                        'Posting errors:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                parameters.purchaseInvoicePage.$.showToast(postResult.message, { type: 'success' });
            }

            await checkForUpdate({
                purchaseInvoicePage: parameters.purchaseInvoicePage,
                recordId: parameters.recordId,
            });
            parameters.purchaseInvoicePage.$.loader.isHidden = true;

            return postResult;
        }
    }
    return null;
}

export async function acceptAllVariancesAction(parameters: AcceptAllVariancesParameter) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.purchaseInvoicePage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_invoice__approve_dialog_title',
                'Accept all variances for this invoice',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_invoice__approve_dialog_content',
                'You are about to approve all variances for this invoice.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
        )
    ) {
        parameters.purchaseInvoicePage.$.loader.isHidden = false;
        const isAccepted = await parameters.purchaseInvoicePage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseInvoice')
            .mutations.acceptAllVariances(true, {
                document: parameters.recordId,
                approve: true,
            })
            .execute();

        if (isAccepted) {
            parameters.purchaseInvoicePage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__variances_status_updated',
                    'Variance status updated.',
                ),
                { type: 'success' },
            );
            parameters.purchaseInvoicePage.$.setPageClean();
            await parameters.purchaseInvoicePage.$.router.refresh();
            await parameters.purchaseInvoicePage.$.refreshNavigationPanel();
        }
    }
}

export async function CreateCreditMemo(parameters: CreateCreditMemoAction) {
    const response = await parameters.purchaseInvoicePage.$.dialog.page(
        '@sage/xtrem-purchasing/PurchaseInvoiceToCreditNotePanel',
        {
            currency: parameters.currency,
        },
        {
            size: 'extra-large',
            resolveOnCancel: true,
            height: 500,
        },
    );

    if (response && Object.keys(response).length) {
        parameters.purchaseInvoicePage.$.loader.isHidden = false;

        const creditMemo = await parameters.purchaseInvoicePage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseInvoice')
            .mutations.createCreditMemoFromInvoice(
                { _id: true },
                {
                    document: parameters.recordId ?? '0',
                    reasonCode: response.reasonCreditNote,
                    totalAmountExcludingTax: response.totalAmountExcludingTaxCreditNote,
                    supplierDocumentDate: response.supplierDocumentDateCreditNote,
                },
            )
            .execute();

        if (Number(creditMemo._id) > 0) {
            parameters.purchaseInvoicePage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__to_credit_memo_success',
                    'Purchase credit memo created.',
                ),
                { type: 'success' },
            );
            if (parameters.isCalledFromRecordPage) {
                parameters.purchaseInvoicePage.$.setPageClean();
                parameters.purchaseInvoicePage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseCreditMemo`, {
                    _id: creditMemo._id,
                });
            }
        } else {
            parameters.purchaseInvoicePage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__to_credit_memo_fail',
                    'This purchase credit memo cannot be created.',
                ),
                { type: 'error' },
            );
        }
        if (!parameters.isCalledFromRecordPage) {
            await parameters.purchaseInvoicePage.$.router.refresh(true);
            await parameters.purchaseInvoicePage.$.refreshNavigationPanel();
        }

        parameters.purchaseInvoicePage.$.loader.isHidden = true;
    }
}

export async function financeIntegrationCheck(parameters: FinanceIntegrationCheckParameter): Promise<MutationResult> {
    const financeIntegrationCheckResult = await parameters.purchaseInvoicePage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseInvoice')
        .mutations.financeIntegrationCheck(
            {
                wasSuccessful: true,
                message: true,
                validationMessages: {
                    message: true,
                    lineNumber: true,
                    sourceDocumentNumber: true,
                },
            },
            { invoice: parameters.recordId || '' },
        )
        .execute();

    if (!financeIntegrationCheckResult.wasSuccessful) {
        parameters.purchaseInvoicePage.$.showToast(
            `**${
                parameters.isWarning
                    ? ui.localize(
                          '@sage/xtrem-purchasing/pages__purchase_invoice__save_warnings',
                          'Warnings while saving:',
                      )
                    : ui.localize('@sage/xtrem-purchasing/pages__purchase_invoice__finance_errors', 'Errors:')
            }**\n${financeIntegrationCheckResult.message}`,
            { type: parameters.isWarning ? 'warning' : 'error', timeout: 5000 },
        );
    }
    return financeIntegrationCheckResult;
}

export function updateLineVarianceType(rowData: ui.PartialCollectionValue<PurchaseInvoiceLineNode>) {
    if (rowData.purchaseReceiptLine) {
        const receiptQty = rowData.purchaseReceiptLine?.purchaseReceiptLine?.quantity
            ? +rowData.purchaseReceiptLine.purchaseReceiptLine.quantity
            : 0;
        const receiptPrice = rowData.purchaseReceiptLine?.purchaseReceiptLine?.grossPrice
            ? +rowData.purchaseReceiptLine.purchaseReceiptLine.grossPrice
            : 0;
        const quantity = rowData.quantity ? +rowData.quantity : 0;
        const grossPrice = rowData.grossPrice ? +rowData.grossPrice : 0;

        if (receiptQty !== quantity && receiptPrice === grossPrice) {
            return 'quantity';
        }
        if (receiptQty === quantity && receiptPrice !== grossPrice) {
            return 'price';
        }
        if (receiptQty !== quantity && receiptPrice !== grossPrice) {
            return 'quantityAndPrice';
        }
        return 'noVariance';
    }
    return 'quantityAndPrice';
}

function fetchBillAddressDetails(
    purchaseInvoicePage: ui.Page,
    billBySupplierId: string | number,
): Promise<AddressDetails> {
    const query = {
        billByAddress: {
            country: {
                _id: true,
                name: true,
                id: true,
                flag: { value: true },
                zipLabel: true,
                regionLabel: true,
            },
            postcode: true,
            region: true,
            city: true,
            addressLine2: true,
            addressLine1: true,
            locationPhoneNumber: true,
            concatenatedAddress: true,
            name: true,
            _id: true,
        },
        billByLinkedAddress: {
            _id: true,
            name: true,
            isActive: true,
            businessEntity: {
                id: true,
                _id: true,
            },
            locationPhoneNumber: true,
            country: {
                zipLabel: true,
                regionLabel: true,
                name: true,
                _id: true,
            },
            postcode: true,
            region: true,
            city: true,
            addressLine2: true,
            addressLine1: true,
        },
        payToAddress: {
            country: {
                _id: true,
                name: true,
                id: true,
                flag: { value: true },
                zipLabel: true,
                regionLabel: true,
            },
            postcode: true,
            region: true,
            city: true,
            addressLine2: true,
            addressLine1: true,
            locationPhoneNumber: true,
            concatenatedAddress: true,
            name: true,
            _id: true,
        },
    };

    const variables = {
        data: {
            billBySupplier: billBySupplierId ?? '',
        },
    };

    return purchaseInvoicePage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseInvoice')
        .getDefaults(query, variables)
        .execute()
        .then((result: AddressDetails) => {
            if (result.billByLinkedAddress?.country?.regionLabel) {
                result.billByLinkedAddress.country.regionLabel = result.billByLinkedAddress.country
                    .regionLabel as RegionLabel;
            }
            return result;
        });
}

function updatePropertiesFromDocument(
    purchaseInvoicePage: PurchaseInvoice,
    data: {
        supplier: ExtractEdgesPartial<Supplier> | undefined;
        currency: ExtractEdgesPartial<Currency> | undefined;
        paymentTerm: ExtractEdgesPartial<PaymentTerm> | undefined;
    },
): void {
    purchaseInvoicePage.billBySupplier.value = data.supplier ?? null;
    purchaseInvoicePage.payToSupplier.value = data?.supplier ?? null;
    purchaseInvoicePage.currency.value = data.currency ?? null;
    purchaseInvoicePage.paymentTerm.value = data.paymentTerm ?? null;
}

export async function setBillDetails(
    purchaseInvoicePage: PurchaseInvoice,
    args: {
        supplier: ExtractEdgesPartial<Supplier> | undefined;
        currency: ExtractEdgesPartial<Currency> | undefined;
        paymentTerm: ExtractEdgesPartial<PaymentTerm> | undefined;
    },
): Promise<void> {
    const billAddressDetails = await fetchBillAddressDetails(purchaseInvoicePage, args.supplier?._id ?? '');

    updatePropertiesFromDocument(purchaseInvoicePage, {
        supplier: args.supplier,
        currency: args.currency,
        paymentTerm: args.paymentTerm,
    });
    purchaseInvoicePage.billByAddress.value = billAddressDetails.billByAddress;
    purchaseInvoicePage.payToAddress.value = billAddressDetails.payToAddress;
    purchaseInvoicePage.billByLinkedAddress.value =
        billAddressDetails?.billByLinkedAddress as unknown as ExtractEdgesPartial<BusinessEntityAddress>;
}
