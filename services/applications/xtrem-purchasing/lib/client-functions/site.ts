import type { Site } from '@sage/xtrem-master-data/build/lib/pages/site';
import * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import type { SiteExtension } from '../page-extensions/site-extension';
import { purchaseOrdersPendingApproval } from './purchase-order';
import { purchaseReturnPendingApproval } from './purchase-return';
import { purchaseRequisitionsPendingApproval } from './purchase-requisition';

export function showApprovalProcessMessage(sitePage: ExtensionMembers<Site & SiteExtension>) {
    sitePage.$.showToast(
        ui.localize(
            '@sage/xtrem-purchasing/pages__site_extension_control_PO_approval',
            'You need to approve or reject pending documents before disabling the approval process.',
        ),
        { type: 'error' },
    );
}

export async function onChangeIsPurchaseRequisitionApprovalManaged(
    sitePage: ExtensionMembers<Site & SiteExtension>,
    wasDirtyBeforeChanging: boolean,
) {
    if (sitePage.isPurchaseRequisitionApprovalManaged.value) {
        sitePage.purchaseRequisitionDefaultApprover.isReadOnly = false;
        sitePage.purchaseRequisitionSubstituteApprover.isReadOnly = false;
        return;
    }

    const _anyPurchaseRequisitionsPendingApproval =
        sitePage.$.recordId && (await purchaseRequisitionsPendingApproval(sitePage.$.graph, sitePage.$.recordId));

    if (_anyPurchaseRequisitionsPendingApproval) {
        showApprovalProcessMessage(sitePage);
        sitePage.isPurchaseRequisitionApprovalManaged.value = true;
        if (!wasDirtyBeforeChanging) {
            sitePage.$.setPageClean();
        }
        return;
    }

    sitePage.purchaseRequisitionDefaultApprover.isReadOnly = true;
    sitePage.purchaseRequisitionSubstituteApprover.isReadOnly = true;
}

export async function onChangeIsPurchaseOrderApprovalManaged(
    sitePage: ExtensionMembers<Site & SiteExtension>,
    wasDirtyBeforeChanging: boolean,
) {
    if (sitePage.isPurchaseOrderApprovalManaged.value) {
        sitePage.purchaseOrderDefaultApprover.isReadOnly = false;
        sitePage.purchaseOrderSubstituteApprover.isReadOnly = false;
        return;
    }

    const _anyPurchaseOrdersPendingApproval =
        sitePage.$.recordId && (await purchaseOrdersPendingApproval(sitePage.$.graph, sitePage.$.recordId));

    if (_anyPurchaseOrdersPendingApproval) {
        showApprovalProcessMessage(sitePage);
        sitePage.isPurchaseOrderApprovalManaged.value = true;
        if (!wasDirtyBeforeChanging) {
            sitePage.$.setPageClean();
        }
        return;
    }

    sitePage.purchaseOrderDefaultApprover.isReadOnly = true;
    sitePage.purchaseOrderSubstituteApprover.isReadOnly = true;
}

export async function onChangeIsPurchaseReturnApprovalManaged(
    sitePage: ExtensionMembers<Site & SiteExtension>,
    isDirty: boolean,
) {
    const _anyPurchaseReturnPendingApproval =
        sitePage.$.recordId && (await purchaseReturnPendingApproval(sitePage.$.graph, sitePage.$.recordId));

    if (_anyPurchaseReturnPendingApproval) {
        showApprovalProcessMessage(sitePage);
        sitePage.isPurchaseReturnApprovalManaged.value = true;
        if (!isDirty) {
            sitePage.$.setPageClean();
        }
    }
}
