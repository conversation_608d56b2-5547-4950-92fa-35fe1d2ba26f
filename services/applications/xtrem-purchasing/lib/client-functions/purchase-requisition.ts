import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';

/**
 * Checks if the site has purchase requisitions in pending approval status.
 * @param _id : the _id of the site.
 * @param graph
 */
export async function purchaseRequisitionsPendingApproval(
    graph: ui.GraphQLApi<GraphApi>,
    siteSysId: string,
): Promise<boolean> {
    const pendingApproval = extractEdges(
        await graph
            .node('@sage/xtrem-purchasing/PurchaseRequisition')
            .aggregate.query(
                ui.queryUtils.edgesSelector(
                    { group: { _id: { _by: 'value' } } },
                    {
                        filter: { site: { _eq: siteSysId }, approvalStatus: { _eq: 'pendingApproval' } },
                        first: 1,
                    },
                ),
            )
            .execute(),
    );
    return !!pendingApproval.length;
}
