import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-purchase-return';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-purchase-return';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status !== 'closed') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status !== 'closed') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (
        (data.recordId &&
            !(
                data.parameters.status === 'inProgress' ||
                data.parameters.status === 'pending' ||
                data.parameters.status === 'closed'
            )) ||
        (data.parameters.approvalStatus === 'confirmed' && data.parameters.status === 'pending')
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.DeleteParameters<isDisabledButtons.DeleteParameters>,
) {
    return isHiddenButtonDeleteAction(data);
}

/* -------------------*/

export function isHiddenButtonApproveAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusApprovalStatusParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonApproveAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ApproveParameters>,
) {
    return isHiddenButtonApproveAction(data);
}

/* -------------------*/

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusApprovalStatusParameters>,
): boolean {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        !data.parameters.isApprovalManaged &&
        data.parameters.approvalStatus &&
        !['pendingApproval', 'approved', 'rejected', 'changeRequested', 'confirmed'].includes(
            data.parameters.approvalStatus,
        )
    ) {
        return !!data.isDirty;
    }
    return true;
}

export function isHiddenButtonRejectAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusApprovalStatusParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRejectAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RejectParameters>,
) {
    return isHiddenButtonRejectAction(data);
}

/* -------------------*/

export function isHiddenButtonRequestApprovalAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusApprovalStatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.isApprovalManaged &&
        data.parameters.approvalStatus &&
        !['pendingApproval', 'approved', 'rejected', 'confirmed'].includes(data.parameters.approvalStatus)
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRequestApprovalAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RequestApprovalParameters>,
) {
    return isHiddenButtonRequestApprovalAction(data);
}

/* -------------------*/

export function isHiddenButtonCloseAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.StatusApprovalStatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.status !== 'draft' &&
        (data.parameters.approvalStatus === 'approved' || data.parameters.approvalStatus === 'confirmed')
    ) {
        return !!data.isDirty;
    }
    return true;
}

export function isDisabledButtonCloseAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CloseParameters>,
) {
    return isHiddenButtonCloseAction(data);
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.returnItems &&
        (data.parameters.approvalStatus === 'approved' || data.parameters.approvalStatus === 'confirmed') &&
        data.parameters.stockTransactionStatus === 'draft'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostParameters>,
) {
    return isHiddenButtonPostAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.isRepost) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        ['inProgress', 'error', 'closed'].includes(data.parameters.status)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.returnSite && data.parameters.supplier) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonSelectFromPurchaseReceiptLinesAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromPurchaseReceiptLinesParameters>,
) {
    if (
        data.parameters &&
        data.parameters.returnSite &&
        data.parameters.supplier &&
        data.parameters.status &&
        (data.parameters.status === 'draft' ||
            ((data.parameters.status === 'inProgress' || data.parameters.status === 'pending') &&
                data.parameters.approvalStatus === 'confirmed'))
    ) {
        return false;
    }
    return true;
}
