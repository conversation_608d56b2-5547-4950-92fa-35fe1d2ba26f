import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import type {
    PurchaseOrderLine,
    PurchaseOrderLineToPurchaseReceiptLine,
    PurchaseReceiptLine,
} from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { PurchaseReceipt as PurchaseReceiptPage } from '../pages/purchase-receipt';
import * as PillColorPurchase from './pill-color';
import { calculatePrices } from './purchase-price';

export function convertOrderLineToOrderToReceiptLine(
    data: ui.PartialCollectionValue<PurchaseOrderLine>,
): ui.PartialCollectionValue<PurchaseOrderLineToPurchaseReceiptLine> {
    return {
        purchaseOrderLine: {
            _id: data._id,
            _sortValue: data._sortValue,
            document: {
                _id: data.document?._id,
            },
        },
    };
}

function convertOrderLineToReceiptLine(
    receiptPage: PurchaseReceiptPage,
    orderLine: ui.PartialCollectionValue<PurchaseOrderLine & { selectedQuantity: number }>,
): ui.PartialCollectionValue<PurchaseReceiptLine> {
    const { item } = orderLine;
    const purchaseOrderLine = convertOrderLineToOrderToReceiptLine(orderLine);
    const currency = receiptPage.currency.value;
    if (!currency) {
        throw new Error('Currency is required');
    }
    delete orderLine._id;
    delete orderLine._sortValue;
    delete orderLine.document;

    return {
        ...orderLine,
        status: 'draft',
        origin: 'purchaseOrder',
        quantity: `${orderLine.selectedQuantity}`,
        quantityInStockUnit: (
            (orderLine.selectedQuantity ? +orderLine.selectedQuantity : 0) *
            (orderLine.unitToStockUnitConversionFactor ? +orderLine.unitToStockUnitConversionFactor : 0)
        ).toString(),
        currency,
        site: receiptPage.site.value ?? undefined,
        purchaseOrderLine,
        stockDetailStatus: item?.isStockManaged ? 'required' : 'notRequired',
        stockTransactionStatus: 'draft',
        internalNote: { value: '' },
    };
}

function filterForOrderLine(receiptPage: PurchaseReceiptPage): Filter<PurchaseOrderLine> {
    const receiptLinesSysId = receiptPage.lines.value.map(line => line._id);
    const purchaseOrderLines = receiptPage.lines.value
        .filter(poLine => poLine.purchaseOrderLine)
        .map(line => line.purchaseOrderLine?.purchaseOrderLine?._id ?? '');
    return {
        _id: { _nin: purchaseOrderLines },
        document: {
            supplier: { _id: receiptPage.businessRelation.value?._id ?? '' },
            currency: { _id: receiptPage.currency.value?._id ?? '' },
            status: { _in: ['pending', 'inProgress'] },
            receiptStatus: { _in: ['notReceived', 'partiallyReceived'] },
            ...(receiptPage.supplierAddress.value
                ? { supplierAddress: { _id: receiptPage.supplierAddress.value?._id ?? '' } }
                : {}),
        },
        stockSite: { _id: receiptPage.site.value?._id ?? '' },
        status: { _in: ['pending', 'inProgress'] },
        lineReceiptStatus: { _in: ['notReceived', 'partiallyReceived'] },
        quantityToReceiveInStockUnit: { _gt: '0' },
        ...(receiptPage.receivingAddress.value
            ? { stockSiteAddress: { _id: receiptPage.receivingAddress.value?._id ?? '' } }
            : {}),
        // Not really sure about this one
        _or: [
            { purchaseReceiptLines: { _atMost: 1, purchaseReceiptLine: { _id: { _nin: receiptLinesSysId } } } },
            { purchaseReceiptLines: { _atLeast: 1, purchaseReceiptLine: { completed: { _eq: false } } } },
        ],
    };
}

// return type of lookupOrderLine function
export type OrderLineLookup = Awaited<ReturnType<typeof lookupOrderLine>>[0];

function overOrdered(line: ExtractEdgesPartial<PurchaseOrderLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) > +(line.quantity ?? 0) - +(line.receivedQuantity ?? 0);
}
function underOrdered(line: ExtractEdgesPartial<PurchaseOrderLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) < +(line.quantity ?? 0) - +(line.receivedQuantity ?? 0);
}

export function lookupOrderLine(
    receiptPage: PurchaseReceiptPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
) {
    return receiptPage.$.dialog.lookup<PurchaseOrderLine & { selectedQuantity: number }>({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select order lines',
        node: '@sage/xtrem-purchasing/PurchaseOrderLine',
        selectedRecordId: selectedLines?.map(line => line._id),
        filter: filterForOrderLine(receiptPage),
        orderBy: { document: { number: +1 }, expectedReceiptDate: +1, _sortValue: +1 },
        mapServerRecord(record) {
            const { quantityToReceive } = record;
            const selectedLine = selectedLines?.find(line => line._id === record._id);
            const quantity = +(selectedLine?.selectedQuantity ?? quantityToReceive ?? 0);
            return { ...record, selectedQuantity: record.selectedQuantity ?? quantity };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, size: 'small' }),
            ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'document',
                valueField: 'number',
                node: '@sage/xtrem-purchasing/PurchaseOrder',
                title: 'Purchase order',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to receive',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                warningMessage: (_id, row) => {
                    if (overOrdered(row as ExtractEdgesPartial<PurchaseOrderLine>)) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_order_table_panel__warning_over_ordered',
                            'You have over ordered this line.',
                        );
                    }
                    if (underOrdered(row as ExtractEdgesPartial<PurchaseOrderLine>)) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_order_table_panel__warning_under_ordered',
                            'You have ordered less than needed for this line.',
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.numeric({ bind: 'receivedQuantity', unitMode: 'unitOfMeasure' }),
            ui.nestedFields.numeric({
                bind: 'grossPrice',
                isReadOnly: true,
                unit: (_id, rowData) => rowData?.document?.currency,
            }),
            ui.nestedFields.date({ bind: 'expectedReceiptDate', isReadOnly: true }),
            ui.nestedFields.text({ title: 'Item ID', bind: { item: { id: true } }, isReadOnly: true }),
            ui.nestedFields.text({ bind: 'itemDescription', title: 'Item description', isReadOnly: true }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({ bind: 'quantityToReceive' }),
            ui.nestedFields.technical({ bind: 'quantity' }),
            ui.nestedFields.technical({ bind: 'charge' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
            ui.nestedFields.technical({ bind: 'amountIncludingTax' }),
            ui.nestedFields.technical({ bind: 'taxAmount' }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'priceOrigin' }),
        ],
    });
}

function checkOrderlinesMessage(lines: ui.PartialCollectionValue<PurchaseOrderLine & { selectedQuantity: number }>[]) {
    const isUnderOrdered = lines.some(underOrdered);
    const isOverOrdered = lines.some(overOrdered);
    if (isOverOrdered && isUnderOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm__lower_greater_quantity',
            'You are about to create one or more receipt lines with a quantity less or greater than the ordered quantity.',
        );
    }

    if (isUnderOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm_lower_quantity',
            'You are about to create one or more receipt lines with a quantity less than the ordered quantity.',
        );
    }

    if (isOverOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order_table_panel__confirm_greater_quantity',
            'You are about to create one or more receipt lines with a quantity greater than the ordered quantity.',
        );
    }
    return null;
}

function confirmReceiptQuantity(receiptPage: PurchaseReceiptPage, message: string) {
    return receiptPage.$.dialog
        .confirmation(
            'warn',
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order_table_panel__create_purchase_dialog_title',
                'Confirm receipt quantity',
            ),
            message,
        )
        .then(() => true)
        .catch(() => false);
}

export async function addLineFromOrder(
    receiptPage: PurchaseReceiptPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
) {
    const orderLines = (await lookupOrderLine(receiptPage, selectedLines)) as unknown as ui.PartialCollectionValue<
        PurchaseOrderLine & { selectedQuantity: number }
    >[];
    if (!orderLines || !isArray(orderLines) || orderLines.length === 0) {
        return;
    }

    const message = checkOrderlinesMessage(orderLines);
    if (message && !(await confirmReceiptQuantity(receiptPage, message))) {
        await addLineFromOrder(
            receiptPage,
            orderLines.map(line => ({ _id: line._id ?? '', selectedQuantity: line.selectedQuantity ?? 0 })),
        );
        return;
    }

    await asyncArray(orderLines).forEach(async orderLine => {
        ui.console.log(`orderLine._sortValue: ${orderLine._sortValue}  ${orderLine.document?.number}`);
        // We are still having an heavy computation on the calculatePrices function !! ( one mnutation for each line we will add )
        const receiptLine = await calculatePrices(receiptPage, convertOrderLineToReceiptLine(receiptPage, orderLine));

        receiptPage.lines.addRecord(receiptLine);
    });
}
