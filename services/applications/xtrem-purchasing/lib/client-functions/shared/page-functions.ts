import type { decimal, ExtractEdgesPartial } from '@sage/xtrem-client';
import { Decimal } from '@sage/xtrem-decimal';
import type { Currency, Supplier } from '@sage/xtrem-master-data-api';
import { convertAmount } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type { GraphApi, PurchaseOrderLine, PurchaseRequisitionLine } from '@sage/xtrem-purchasing-api';
import type { Dict } from '@sage/xtrem-shared';
import type { Site } from '@sage/xtrem-system-api';
import { calculateTaxes } from '@sage/xtrem-tax/build/lib/client-functions/calculate-taxes';
import type * as ui from '@sage/xtrem-ui';
import type { CalculatePriceInputValues, CalculatePriceOutputValues } from './interfaces';

export async function responseOrReject<T>(
    page: ui.Page<GraphApi> & { _id: ui.fields.Numeric },
    promise: Promise<T>,
    message?: string,
    path?: string,
    toCurrent = false,
    skipValidation = false,
) {
    try {
        let validation: string[] = [];
        if (!skipValidation) {
            validation = await page.$.page.validate();
        }

        if (validation.length === 0) {
            const result = await promise;
            let queryParameters = {};
            if (toCurrent) {
                queryParameters = { _id: page._id.value, skipDirtyCheck: true };
            }
            if (message) {
                page.$.showToast(message, { type: 'success' });
            }
            if (path) {
                page.$.router.goTo(path, queryParameters);
            } else {
                return result;
            }
        } else {
            page.$.showToast(validation.join('\n'), { type: 'error' });
        }
    } catch (e) {
        page.$.showToast(e.message, { timeout: 30000, type: 'error' });
    }
    return undefined;
}

export function statusText(documentStatus: any, approvalStatus?: any) {
    switch (documentStatus) {
        case 'draft':
            return approvalStatus || documentStatus;
        case 'pending':
            return approvalStatus || documentStatus;
        default:
            return documentStatus;
    }
}

export async function calculateLinePrices(inputValues: CalculatePriceInputValues): Promise<CalculatePriceOutputValues> {
    const outputValues: CalculatePriceOutputValues = {
        netPrice: 0,
        amountExcludingTax: 0,
        amountIncludingTax: 0,
        taxAmount: inputValues.taxAmount,
        taxAmountAdjusted: inputValues.taxAmountAdjusted,
        uiTaxes: inputValues.taxes.uiTaxes,
        amountExcludingTaxInCompanyCurrency: 0,
        amountIncludingTaxInCompanyCurrency: 0,
        taxCalculationStatus: 'failed',
    };

    if (inputValues.grossPrice >= 0) {
        const chargeAmount = Decimal.roundAt(
            (inputValues.grossPrice * (inputValues.charge ?? 0)) / 100,
            inputValues.netPriceScale,
        );
        const discountAmount = Decimal.roundAt(
            (inputValues.grossPrice * (inputValues.discount ?? 0)) / 100,
            inputValues.netPriceScale,
        );
        outputValues.netPrice = Decimal.roundAt(
            +inputValues.grossPrice + chargeAmount - discountAmount,
            inputValues.netPriceScale,
        );

        outputValues.amountExcludingTax = Number(outputValues.netPrice * (inputValues.quantity ?? 0));
        const { uiTaxes, taxAmount, taxAmountAdjusted, taxCalculationStatus } = await calculateTaxes({
            ...inputValues.taxes,
            amountExcludingTax: outputValues.amountExcludingTax,
            quantity: inputValues.quantity,
            taxAmount: inputValues.taxAmount,
            taxAmountAdjusted: inputValues.taxAmountAdjusted,
        });

        outputValues.uiTaxes = uiTaxes;
        outputValues.taxAmount = taxAmount;
        outputValues.taxAmountAdjusted = taxAmountAdjusted;
        outputValues.taxCalculationStatus = taxCalculationStatus;
        outputValues.amountIncludingTax = Number(outputValues.amountExcludingTax + taxAmountAdjusted);

        // Company currency amounts
        outputValues.amountExcludingTaxInCompanyCurrency = convertAmount(
            outputValues.amountExcludingTax,
            inputValues.rateMultiplication,
            inputValues.rateDivision,
            inputValues.fromDecimals,
            inputValues.toDecimals,
        );
        outputValues.amountIncludingTaxInCompanyCurrency = convertAmount(
            outputValues.amountIncludingTax,
            inputValues.rateMultiplication,
            inputValues.rateDivision,
            inputValues.fromDecimals,
            inputValues.toDecimals,
        );
    }
    return outputValues;
}

// Show/hide Exchange rate from Purchase pages
export function isExchangeRateHidden(
    currency: ExtractEdgesPartial<Currency> | null,
    site: ExtractEdgesPartial<Site> | null,
    supplier: ExtractEdgesPartial<Supplier> | null,
) {
    if (site?.legalCompany?.currency?.id && currency?.id && supplier) {
        return site.legalCompany.currency.id === currency.id;
    }
    return true;
}

/**
 * Open Dialog for the adding or editing the line on purchase pages,
 * @param page the calling page's this (used to interact with the framework)
 * @param dialogTitle message to be displayed on the dialog title
 * @param sections array of sections to be displayed on the dialog
 * @returns Promise
 */
export function addEditLineDialog(
    page: ui.Page<GraphApi>,
    dialogTitle: string,
    sections: ui.containers.SectionControlObject[],
    isViewOnly = false,
) {
    // Show section before the custom dialog is triggered
    sections.forEach(showSection => {
        showSection.isHidden = false;
    });
    const acceptButtonOptions = { isHidden: isViewOnly };
    return page.$.dialog
        .custom('info', sections, { rightAligned: true, dialogTitle, acceptButton: acceptButtonOptions })
        .finally(() => {
            // Hide section before the custom dialog is closed
            sections.forEach(showSection => {
                showSection.isHidden = true;
            });
        });
}

/**
 * Verifies if the current date input is later than today for the purchase documents page
 * @param date date input
 * @param message Message to be triggered
 * @returns string | undefined
 */
export function laterThanToday(date: string, message: string) {
    return Date.parse(date) > Date.now() ? message : undefined;
}
export function convertQuantityToStockUnit(
    quantity: decimal,
    rowData: ui.PartialNodeWithId<PurchaseOrderLine | PurchaseRequisitionLine>,
): decimal {
    if (rowData.quantity) {
        const dps = rowData.stockUnit?.decimalDigits ?? 2;
        const stockQuantity = (quantity * +(rowData.quantityInStockUnit ?? 0)) / +rowData.quantity;
        return +stockQuantity.toFixed(dps);
    }
    return 0;
}

/**
 * get default values of a LandedCostDocumentLine
 * @param page
 * @param nodeName The parent node name where the property 'lines' is defined with a property 'landedCost' (e.g. 'PurchaseInvoice')
 * @param itemID the item _id
 * @returns  null or an object { landedCostAllocationRule: string , landedCostAllocationRuleUnit: {id: string} }
 */
export async function fetchDefaultLandedCostDocumentLine(page: ui.Page, nodeName: string, itemID?: string) {
    if (!page.$.isServiceOptionEnabled('landedCostOption') || !itemID || Number(itemID) <= 0) return null;

    const valuesProcessed: Dict<any> = {};
    const { values } = page.$;
    Object.keys(values).forEach(key => {
        if (Number(values[key]) && typeof values[key] === 'object') {
            valuesProcessed[key] = Number(values[key]); // convert decimals to strings because of errors
        } else {
            valuesProcessed[key] = values[key];
        }
    });

    const result = await page.$.graph
        .node(`@sage/xtrem-purchasing/${nodeName}`)
        .getDefaults(
            {
                lines: {
                    query: {
                        edges: { node: { landedCost: { allocationRule: true, allocationRuleUnit: { id: true } } } },
                    },
                },
            },
            { data: { ...valuesProcessed, lines: [{ item: itemID }] } },
        )
        .execute();

    return result?.lines?.query.edges[0].node.landedCost;
}
