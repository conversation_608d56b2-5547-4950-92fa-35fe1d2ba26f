import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { TaxCalculationStatus } from '@sage/xtrem-master-data-api';
import type { PurchaseOrderLineBinding, PurchaseRequisitionLine } from '@sage/xtrem-purchasing-api';
import type { SerialNumber, Stock, StockMovementDetail } from '@sage/xtrem-stock-data-api';
import type { Taxes } from '@sage/xtrem-tax/build/lib/client-functions/interfaces';
import type * as ui from '@sage/xtrem-ui';

export interface StockIssueDetail extends StockMovementDetail {
    stockRecord: Stock;
    serialNumbers?: Array<SerialNumber>;
}

export interface CalculatePriceInputValues {
    grossPrice: number;
    charge: number;
    discount: number;
    netPriceScale: number;
    quantity: number;
    amountExcludingTax: number;
    amountIncludingTax: number;
    taxAmount: number;
    taxAmountAdjusted: number;
    rateMultiplication: number;
    rateDivision: number;
    fromDecimals: number;
    toDecimals: number;
    taxes: Taxes;
}

export interface CalculatePriceOutputValues {
    netPrice: number;
    taxAmount: number;
    taxAmountAdjusted: number;
    uiTaxes: string;
    amountExcludingTax: number;
    amountIncludingTax: number;
    amountExcludingTaxInCompanyCurrency: number;
    amountIncludingTaxInCompanyCurrency: number;
    taxCalculationStatus: TaxCalculationStatus;
}

export interface RequisitionSelection {
    lines: ui.PartialCollectionValue<PurchaseRequisitionLine>[];
}

export interface SupplierNodeLookUpData {
    _id: string;
    id: string;
    name: string;
    internalNote: { value: string };
    currency: { _id: string; id: string; name: string; decimalDigits: number; symbol: string };
}

export interface SupplierLookUpData extends SupplierNodeLookUpData {
    purchaseLeadTime: number;
    type: string;
    sortOrder: string;
}

export interface AddRequisitionLinesParameters {
    siteId: string;
    stockSiteId: string;
    supplierId: string;
    currencyId: string;
    orderLines: ui.PartialCollectionValue<PurchaseOrderLineBinding>[];
    requisitionLines: ExtractEdgesPartial<PurchaseRequisitionLine>[];
}

export interface PurchaseCreditMemoStepSequenceStatus {
    create: ui.StepSequenceStatus;
    post: ui.StepSequenceStatus;
    pay?: ui.StepSequenceStatus;
}
