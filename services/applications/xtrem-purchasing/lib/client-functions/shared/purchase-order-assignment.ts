import type { ExtractEdges, ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { PreferredProcess } from '@sage/xtrem-master-data-api';
import type { PurchaseOrderLine } from '@sage/xtrem-purchasing-api';
import type { OrderAssignment, OrderToOrderDemandType, OrderToOrderSupplyType } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';
import type { PurchaseOrder as PurchaseOrderPage } from '../../pages/purchase-order';
import type { PurchaseOrderAssignmentDetailsPanel } from '../../pages/purchase-order-assignment-details-panel';
import type { OrderAssignmentLine } from '../interfaces/order-assignment';

export function isOrderToOrderServiceOptionActivated(
    line: ui.PartialCollectionValue<PurchaseOrderLine>,
    preferredProcess?: PreferredProcess,
) {
    return (
        line._id &&
        line.itemSite && // il faut ajouter ca dans la page ou faire une requete
        line.itemSite.isOrderToOrder === true &&
        (preferredProcess === undefined || (preferredProcess && line.itemSite.preferredProcess === preferredProcess))
    );
}

async function getOrderAssignments(
    page: PurchaseOrderPage,
    line: ui.PartialCollectionValue<PurchaseOrderLine>,
    demandType: OrderToOrderDemandType,
    supplyType?: OrderToOrderSupplyType,
) {
    return extractEdges<Partial<OrderAssignment>>(
        await page.$.graph
            .node('@sage/xtrem-stock-data/OrderAssignment')
            .query(
                ui.queryUtils.edgesSelector(
                    { _id: true, quantityInStockUnit: true },
                    {
                        filter: {
                            supplyDocumentLine: { _eq: line._id },
                            demandType: { _eq: demandType },
                            supplyType: { _eq: supplyType },
                        },
                    },
                ),
            )
            .execute(),
    );
}

async function openAssignmentDialog(page: PurchaseOrderPage, dialogTitle: string, dialogText: string) {
    if (
        await page.$.dialog
            .confirmation('warn', dialogTitle, dialogText, {
                acceptButton: {
                    text: ui.localize(
                        '@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-continue',
                        'Continue',
                    ),
                },
                cancelButton: {
                    text: ui.localize('@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-cancel', 'Cancel'),
                },
            })
            .then(() => true)
            .catch(() => false)
    ) {
        return true;
    }
    return false;
}

async function assignedOrderAssignment(
    page: PurchaseOrderPage, // | PurchaseOrderLinePanelPage, // | SalesOrderTableCardViewPage,
    line: ui.PartialCollectionValue<PurchaseOrderLine>,
    demandType: OrderToOrderDemandType,
    supplyType?: OrderToOrderSupplyType,
    preferredProcess?: PreferredProcess,
): Promise<{
    line: ui.PartialCollectionValue<PurchaseOrderLine>;
    orderAssignments: ExtractEdges<Partial<OrderAssignment>>[];
} | null> {
    if (isOrderToOrderServiceOptionActivated(line, preferredProcess)) {
        const orderAssignments = await getOrderAssignments(page, line, demandType, supplyType);
        if (orderAssignments.length) {
            return { line, orderAssignments };
        }
    }
    return null;
}

async function assignedOrderAssignments(
    page: PurchaseOrderPage,
    demandType: OrderToOrderDemandType,
    supplyType?: OrderToOrderSupplyType,
    preferredProcess?: PreferredProcess,
): Promise<
    { line: ExtractEdgesPartial<PurchaseOrderLine>; orderAssignments: ExtractEdges<Partial<OrderAssignment>>[] }[]
> {
    const documentLines: {
        line: ExtractEdgesPartial<PurchaseOrderLine>;
        orderAssignments: ExtractEdges<Partial<OrderAssignment>>[];
    }[] = [];
    await Promise.all(
        page.lines.value.map(async line => {
            const assignedOrder = await assignedOrderAssignment(page, line, demandType, supplyType, preferredProcess);
            if (assignedOrder !== null) {
                documentLines.push(assignedOrder);
            }
        }),
    );
    return documentLines;
}

export function isAssignmentCheckedOnPurchaseOrderLineQuantityChanged(
    page: PurchaseOrderPage,
    line: ui.PartialCollectionValue<PurchaseOrderLine>,
) {
    return line.uDemandOrderLineLink !== '' &&
        line.quantity &&
        line.quantityToReceiveInStockUnit &&
        line.quantityToReceive &&
        line.quantity < line.quantityToReceive
        ? openAssignmentDialog(
              page,
              ui.localize(
                  '@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_quantity_changed_dialog_title',
                  'Decrease purchase order line quantity',
              ),
              ui.localize(
                  '@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_quantity_changed_dialog_content',
                  'When you decrease the purchase order line quantity, the quantity on the link to the original order is also decreased.',
              ),
          )
        : true;
}

export async function isPurchaseOrderAssignmentCheckedOnPurchaseOrderLineDelete(
    page: PurchaseOrderPage,
    line: ui.PartialCollectionValue<PurchaseOrderLine>,
    // supplyType?: OrderToOrderSupplyType,
) {
    const assignedOrder = await assignedOrderAssignment(page, line, 'salesOrderLine', 'purchaseOrderLine');
    if (assignedOrder) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_deletion_dialog_title',
                'Delete purchase order line',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_deletion_dialog_content',
                'When you delete this purchase order line, the link to the original order is also deleted.',
            ),
        );
    }
    return true;
}

export async function isPurchaseOrderAssignmentCheckedOnPurchaseOrderLineClose(
    page: PurchaseOrderPage,
    line: ui.PartialCollectionValue<PurchaseOrderLine>,
    //    supplyType?: OrderToOrderSupplyType,
) {
    const assignedOrder = await assignedOrderAssignment(page, line, 'salesOrderLine', 'purchaseOrderLine');
    if (assignedOrder) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_closing_dialog_title',
                'Close purchase order line',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages_confirm_assignment_purchase_order_line_closing_dialog_content',
                'When you close this purchase order line, the link to the original order remains.',
            ),
        );
    }
    return true;
}

export async function isPurchaseOrderAssignmentCheckedOnPurchaseOrderClose(page: PurchaseOrderPage) {
    const assignedOrders = await assignedOrderAssignments(page, 'salesOrderLine', 'purchaseOrderLine');
    if (assignedOrders && assignedOrders.length) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_title',
                'Close purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_content',
                'When you close this purchase order, the links to the original orders remain.',
            ),
        );
    }
    return true;
}

export async function isPurchaseOrderAssignmentCheckedOnPurchaseOrderDelete(page: PurchaseOrderPage) {
    const assignedOrders = await assignedOrderAssignments(page, 'salesOrderLine', 'purchaseOrderLine');
    if (assignedOrders && assignedOrders.length) {
        return openAssignmentDialog(
            page,
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_title',
                'Delete purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_content',
                'When you delete this purchase order, the links to the original orders are also deleted.',
            ),
        );
    }
    return true;
}
export function openDemandOrderAssignments(
    page: PurchaseOrderPage,
    line: ui.PartialCollectionValue<PurchaseOrderLine>,
): Promise<OrderAssignment | null> {
    return page.$.dialog.page(
        '@sage/xtrem-purchasing/PurchaseOrderAssignmentDetailsPanel',
        {
            purchaseOrderLine: JSON.stringify({
                purchaseOrderLineId: line._id,
                quantityInStockUnit: line.quantityInStockUnit,
                itemStockUnit: line.stockUnit,
                status: line.status,
            }),
        },
        { rightAligned: false, size: 'large', resolveOnCancel: true },
    );
}

export async function queryOrderAssignment({
    page,
    supplyDocumentLine,
}: {
    page: PurchaseOrderAssignmentDetailsPanel;
    supplyDocumentLine: string;
}): Promise<OrderAssignmentLine[]> {
    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-stock-data/OrderAssignment')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        demandType: true,
                        demandDocumentLine: { _id: true, documentId: true, documentNumber: true },
                        demandWorkInProgress: { expectedQuantity: true },
                        quantityInStockUnit: true,
                    },
                    { filter: { supplyDocumentLine: { _eq: supplyDocumentLine } } },
                ),
            )
            .execute(),
    );
}
