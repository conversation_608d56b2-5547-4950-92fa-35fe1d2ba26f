import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { ItemSite } from '@sage/xtrem-master-data-api';
import { convertFromTo, getPurchaseUnit } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import type { PurchaseOrderLineBinding } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import type { PurchaseOrder as PurchaseOrderPage } from '../../pages/purchase-order';

export function computeTotalAmounts(page: PurchaseOrderPage) {
    const {
        totalAmountExcludingTax,
        totalAmountExcludingTaxInCompanyCurrency,
        totalAmountIncludingTaxInCompanyCurrency,
    } = page.lines.value.reduce(
        (
            amounts: {
                totalAmountExcludingTax: number;
                totalAmountExcludingTaxInCompanyCurrency: number;
                totalAmountIncludingTaxInCompanyCurrency: number;
            },
            line,
        ) => {
            amounts.totalAmountExcludingTax += Number(line.amountExcludingTax ?? 0);
            amounts.totalAmountExcludingTaxInCompanyCurrency += Number(line.amountExcludingTaxInCompanyCurrency ?? 0);
            amounts.totalAmountIncludingTaxInCompanyCurrency += Number(line.amountIncludingTaxInCompanyCurrency ?? 0);
            return amounts;
        },
        {
            totalAmountExcludingTax: 0,
            totalAmountExcludingTaxInCompanyCurrency: 0,
            totalAmountIncludingTaxInCompanyCurrency: 0,
        },
    );

    page.totalAmountExcludingTax.value = totalAmountExcludingTax;
    page.totalAmountIncludingTax.value = page.totalAmountExcludingTax.value + Number(page.totalTaxAmount.value);
    page.totalAmountExcludingTaxInCompanyCurrency.value = totalAmountExcludingTaxInCompanyCurrency;
    page.totalAmountIncludingTaxInCompanyCurrency.value = totalAmountIncludingTaxInCompanyCurrency;
}

/**
 * Refreshes the edit line dialog page in case it points to the current rowId
 * @param rowId
 * @param rowData
 */
export function refreshDialogAddress(
    page: PurchaseOrderPage,
    rowId: string,
    rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>,
) {
    if (page.currentSelectedLineId === rowId) {
        page.stockSiteLinkedAddress.value = { ...rowData.stockSiteLinkedAddress };
        // if stockSiteAddress is coming from stockSiteAddress
        // we need to replace the _id to be able to create a new stockSiteAddress
        page.stockSiteAddress.value = {
            ...(rowData.stockSiteAddress ?? { ...rowData.stockSiteLinkedAddress, _id: -1 }),
            orderLineId: rowId,
        };
        page.stockSiteAddress.value.concatenatedAddress = getConcatenatedAddress(rowData.stockSiteAddress ?? {});
        // We want the address pod displayed, not in edit mode
        page.stockSiteAddress.isReadOnly = true;
    }
}

export async function onLineItemChange(
    page: PurchaseOrderPage,
    _rowId: string,
    rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>,
): Promise<void> {
    if (rowData?.item && rowData?.stockSite) {
        rowData.expectedReceiptDate = await page.setExpectedReceiptDate(
            rowData.item._id ?? '',
            rowData.stockSite?._id ?? '',
        );
    }
    if (rowData?.item) {
        rowData.itemDescription = rowData.item.description ? rowData.item.description : rowData.item.name;
        rowData.unit = await getPurchaseUnit(
            page.$.graph,
            rowData.item._id ?? '',
            page.businessRelation.value?._id ?? '',
        );
        const quantityToConvert = rowData.quantity ? +rowData.quantity : 1;

        const conversion = await convertFromTo(
            page.$.graph,
            rowData.unit?._id ?? '',
            rowData.item.stockUnit?._id ?? '',
            quantityToConvert,
            rowData.item._id,
            page.businessRelation.value?._id,
            '',
            'purchase',
            false,
        );
        rowData.stockUnit = rowData.item.stockUnit;
        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor.toString();
        if (rowData.quantity) {
            rowData.quantityInStockUnit = conversion.convertedQuantity.toString();
        }
        const filter = {
            item: rowData.item._id,
            site: {
                _id: rowData.site?._id,
                isInventory: true,
                legalCompany: { _id: page.site.value?.legalCompany?._id },
            },
        } as Filter<ItemSite>;

        if (rowData.item) {
            const result =
                extractEdges(
                    await page.$.graph
                        .node('@sage/xtrem-master-data/ItemSite')
                        .query(
                            ui.queryUtils.edgesSelector(
                                { _id: true, site: { _id: true, id: true, name: true } },
                                { filter },
                            ),
                        )
                        .execute(),
                ).shift() ?? null;

            if (result && rowData.stockSite?._id !== result.site._id) {
                rowData.stockSite = result.site;
                rowData.stockSiteLinkedAddress = undefined;
                rowData.stockSiteAddress = undefined;
                const updatedAddress = await page.fetchDefaultStockSiteAddress(rowData);
                rowData.stockSiteLinkedAddress = updatedAddress?.stockSiteLinkedAddress;
                rowData.stockSiteAddress = updatedAddress?.stockSiteAddress;
            }
        }
    }
}
