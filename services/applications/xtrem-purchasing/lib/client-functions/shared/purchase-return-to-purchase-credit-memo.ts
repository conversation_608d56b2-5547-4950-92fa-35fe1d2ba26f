import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, PaymentTerm, Supplier } from '@sage/xtrem-master-data-api';
import type {
    PurchaseCreditMemoLine,
    PurchaseReturnLine,
    PurchaseReturnLineToPurchaseCreditMemoLine,
} from '@sage/xtrem-purchasing-api';
import type * as ui from '@sage/xtrem-ui';
import type { PurchaseCreditMemo as PurchaseCreditMemoPage } from '../../pages/purchase-credit-memo';

/** return PurchaseReturnLineToPurchaseCreditMemoLine */
function mapReturnTocreditMemoLine(returnLine: ui.PartialCollectionValue<PurchaseReturnLine>) {
    return {
        purchaseReturnLine: {
            _id: returnLine._id,
            document: {
                _id: returnLine.document?._id,
                number: returnLine.document?.number,
                isTransferHeaderNote: returnLine.document?.isTransferHeaderNote,
                isTransferLineNote: returnLine.document?.isTransferLineNote,
            },
            status: returnLine.status,
            quantity: returnLine.quantity,
            item: { _id: returnLine.item?._id, name: returnLine.item?.name },
            grossPrice: returnLine.grossPrice,
            totalTaxExcludedAmount: returnLine.amountExcludingTax,
            storedAttributes: returnLine.storedAttributes,
            storedDimensions: returnLine.storedDimensions,
        },
        unit: returnLine.unit,
    } as ui.PartialCollectionValue<PurchaseReturnLineToPurchaseCreditMemoLine>;
}

export function createCreditMemoLineFromReturnLine(
    page: PurchaseCreditMemoPage,
    returnLine: ui.PartialCollectionValue<PurchaseReturnLine>,
): ui.PartialCollectionValue<PurchaseCreditMemoLine> {
    const creditMemoLineData = { ...returnLine };

    delete creditMemoLineData._id;
    delete creditMemoLineData.status;
    delete creditMemoLineData.supplier;
    delete creditMemoLineData.currency;
    delete creditMemoLineData.document;

    const quantityInStockUnit = (
        parseFloat(returnLine.remainingQuantityToCredit ?? '') *
        parseFloat(returnLine.unitToStockUnitConversionFactor ?? '')
    ).toString();

    return {
        ...creditMemoLineData,
        status: 'draft',
        origin: 'purchaseReturn',
        quantity: returnLine.remainingQuantityToCredit,
        unitToStockUnitConversionFactor: returnLine.unitToStockUnitConversionFactor,
        quantityInStockUnit,
        currency: page.currency.value,
        purchaseReturnLine: mapReturnTocreditMemoLine(returnLine),
        amountExcludingTax: returnLine.totalTaxExcludedAmount,
        amountExcludingTaxInCompanyCurrency: returnLine.calculatedTotalAmountExcludingTaxInCompanyCurrency,
        storedAttributes: returnLine.storedAttributes,
        storedDimensions: returnLine.storedDimensions,
        taxDate: returnLine.taxDate,
        uiTaxes: returnLine.uiTaxes,
        recipientSite: returnLine.document?.returnSite,
        uPurchaseUnit: returnLine.unit,
        uStockUnit: returnLine.stockUnit,
        internalNote: { value: '' },
        externalNote: { value: '' },
        isExternalNote: false,
    } as ui.PartialCollectionValue<PurchaseCreditMemoLine>;
}

export async function addCreditMemoLinesFromReturnLines(
    page: PurchaseCreditMemoPage,
    returnLinesToCredit: ui.PartialCollectionValue<PurchaseReturnLine>[],
) {
    let headerValues: {
        supplier?: ExtractEdgesPartial<Supplier>;
        currency?: ExtractEdgesPartial<Currency>;
        paymentTerm?: ExtractEdgesPartial<PaymentTerm>;
    } = {};
    let sortValue = page.lines.value.reduce((max, row) => Math.max(max, row._sortValue ?? 0), 0) + 10;

    await asyncArray(returnLinesToCredit).forEach(async returnLine => {
        if (!headerValues && !page.$.recordId && !page.billBySupplier.value) {
            headerValues = {
                supplier: returnLine.document?.supplier,
                currency: returnLine.document?.currency,
                paymentTerm: returnLine.document?.paymentTerm,
            };
        }
        const creditMemoLine = createCreditMemoLineFromReturnLine(page, returnLine);

        await page.calculatePrices(creditMemoLine);

        page.lines.addOrUpdateRecordValue({ ...creditMemoLine, _sortValue: sortValue });
        sortValue += 10;
    });
}
