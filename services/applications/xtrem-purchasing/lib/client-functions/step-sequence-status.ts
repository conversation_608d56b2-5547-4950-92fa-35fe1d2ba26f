import type { Dict } from '@sage/xtrem-client';
import type { PurchaseDocumentApprovalStatus } from '@sage/xtrem-purchasing-api';
import type { StockAllocationStatus, StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as ui from '@sage/xtrem-ui';
import type { PurchaseReturnStepSequenceStatus } from './interfaces/step-sequence';

const returnStepSequenceCreate = ui.localize(
    '@sage/xtrem-purchasing/pages__purchase_return__step_sequence_creation',
    'Create',
);

const returnStepSequenceApprove = ui.localize(
    '@sage/xtrem-purchasing/pages__purchase_return__step_sequence_approve',
    'Approve',
);

const returnStepSequenceConfirm = ui.localize(
    '@sage/xtrem-purchasing/pages__purchase_return__step_sequence_confirm',
    'Confirm',
);

const returnStepSequenceAllocate = ui.localize(
    '@sage/xtrem-purchasing/pages__purchase_return__step_sequence_allocate',
    'Allocate',
);
let returnStepSequenceApproveOrConfirm: string;

const returnStepSequencePost = ui.localize('@sage/xtrem-purchasing/pages__purchase_return__step_sequence_post', 'Post');

function setApproveOrConfirmStepSequence(
    isApprovalManaged: boolean,
    approvalStatus: PurchaseDocumentApprovalStatus,
): string {
    return (isApprovalManaged ||
        ['pendingApproval', 'approved', 'rejected', 'changeRequested'].includes(approvalStatus)) &&
        approvalStatus !== 'confirmed'
        ? returnStepSequenceApprove
        : returnStepSequenceConfirm;
}

export function getPurchaseReturnStepSequence(
    isApprovalManaged: boolean,
    approvalStatus: PurchaseDocumentApprovalStatus,
) {
    returnStepSequenceApproveOrConfirm = setApproveOrConfirmStepSequence(isApprovalManaged, approvalStatus);

    return [
        returnStepSequenceCreate,
        returnStepSequenceApproveOrConfirm,
        returnStepSequenceAllocate,
        returnStepSequencePost,
    ];
}

function _setPurchaseReturnStepSequenceObject(
    stepSequenceValues: PurchaseReturnStepSequenceStatus,
): Dict<ui.StepSequenceStatus> {
    return {
        [returnStepSequenceCreate]: stepSequenceValues.create,
        [returnStepSequenceApproveOrConfirm]: stepSequenceValues.approve,
        [returnStepSequenceAllocate]: stepSequenceValues.allocate,
        [returnStepSequencePost]: stepSequenceValues.post,
    };
}

export function getPurchaseReturnStepSequenceValues(
    isApprovalManaged: boolean,
    stockTransactionStatus: StockDocumentTransactionStatus,
    allocationStatus: StockAllocationStatus,
    approvalStatus: PurchaseDocumentApprovalStatus,
): Dict<ui.StepSequenceStatus> {
    returnStepSequenceApproveOrConfirm = setApproveOrConfirmStepSequence(isApprovalManaged, approvalStatus);

    if (stockTransactionStatus === 'completed') {
        return _setPurchaseReturnStepSequenceObject({
            create: 'complete',
            approve: 'complete',
            confirm: 'complete',
            allocate: 'complete',
            post: 'complete',
        });
    }

    let allocate: ui.StepSequenceStatus;

    switch (allocationStatus) {
        case 'allocated':
            allocate = 'complete';
            break;
        case 'partiallyAllocated':
            allocate = 'current';
            break;
        default:
            allocate = 'incomplete';
    }

    switch (approvalStatus) {
        case 'approved':
            return _setPurchaseReturnStepSequenceObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                allocate,
                post: 'incomplete',
            });
        case 'confirmed':
            return _setPurchaseReturnStepSequenceObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                allocate,
                post: 'incomplete',
            });
        case 'pendingApproval':
            return _setPurchaseReturnStepSequenceObject({
                create: 'complete',
                approve: 'current',
                confirm: 'current',
                allocate,
                post: 'incomplete',
            });
        default:
            return _setPurchaseReturnStepSequenceObject({
                create: allocate === 'incomplete' ? 'current' : 'complete',
                approve: 'incomplete',
                confirm: 'incomplete',
                allocate,
                post: 'incomplete',
            });
    }
}
