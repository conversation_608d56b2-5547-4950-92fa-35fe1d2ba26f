import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type {
    PurchaseOrderLine,
    PurchaseRequisitionLine,
    PurchaseRequisitionLineToPurchaseOrderLine,
} from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { PurchaseOrder as PurchaseOrderPage } from '../pages/purchase-order';
import * as PillColorPurchase from './pill-color';
import { calculatePrices } from './purchase-price';

export function convertRequisitionLineToRequisitionToOrderLine(
    data: ui.PartialCollectionValue<PurchaseRequisitionLine & { selectedQuantity: number }>,
): ui.PartialCollectionValue<PurchaseRequisitionLineToPurchaseOrderLine> {
    return {
        orderedQuantity: data.selectedQuantity !== undefined ? data.selectedQuantity.toString() : undefined,
        purchaseRequisitionLine: {
            _id: data._id,
            _sortValue: data._sortValue,
            document: {
                _id: data.document?._id,
                number: data.document?.number,
                isTransferHeaderNote: data.document?.isTransferHeaderNote,
                isTransferLineNote: data.document?.isTransferLineNote,
            },
        },
    };
}

async function convertRequisitionLineToOrderLine(
    orderPage: PurchaseOrderPage,
    requisitionLine: ui.PartialCollectionValue<PurchaseRequisitionLine & { selectedQuantity: number }>,
): Promise<ui.PartialCollectionValue<PurchaseOrderLine>> {
    const purchaseRequisitionLine = convertRequisitionLineToRequisitionToOrderLine(requisitionLine);
    const currency = orderPage.currency.value;
    if (!currency) {
        throw new Error('Currency is required');
    }
    const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
        { ...requisitionLine, _id: requisitionLine._id ?? '' },
        orderPage._defaultDimensionsAttributes,
    );

    const newExpectedReceiptDate = await orderPage.setExpectedReceiptDate(
        requisitionLine.item?._id ?? '',
        requisitionLine.stockSite?._id ?? orderPage.stockSite.value?._id ?? '',
        requisitionLine.needByDate,
    );
    delete requisitionLine._id;
    delete requisitionLine._sortValue;
    delete requisitionLine.document;

    return {
        ...requisitionLine,
        status: 'draft',
        lineReceiptStatus: 'notReceived',
        origin: 'purchaseRequisition',
        quantityInStockUnit: (
            (requisitionLine.selectedQuantity ? +requisitionLine.selectedQuantity : 0) *
            (requisitionLine.unitToStockUnitConversionFactor ? +requisitionLine.unitToStockUnitConversionFactor : 0)
        ).toString(),
        site: orderPage.stockSite.value ?? undefined,
        stockSiteLinkedAddress: requisitionLine?.stockSite?.primaryAddress,
        stockSiteAddress: requisitionLine?.stockSite?.primaryAddress,
        purchaseRequisitionLines: [purchaseRequisitionLine],
        priceOrigin: requisitionLine.priceOrigin,
        unitToStockUnitConversionFactor: requisitionLine?.unitToStockUnitConversionFactor,
        quantity: (
            (requisitionLine.selectedQuantity ? +requisitionLine.selectedQuantity : 0) *
            (requisitionLine.unitToStockUnitConversionFactor ? +requisitionLine.unitToStockUnitConversionFactor : 0)
        ).toString(),
        quantityToReceiveInStockUnit: (
            (requisitionLine.selectedQuantity ? +requisitionLine.selectedQuantity : 0) *
            (requisitionLine.unitToStockUnitConversionFactor ? +requisitionLine.unitToStockUnitConversionFactor : 0)
        ).toString(),
        expectedReceiptDate: newExpectedReceiptDate,
        storedAttributes: line.storedAttributes,
        storedDimensions: line.storedDimensions,
        internalNote: { value: '' },
        externalNote: { value: '' },
        isExternalNote: false,
        stockSite: requisitionLine.stockSite ?? orderPage.stockSite.value ?? undefined,
    };
}

function filterForRequisitionLine(orderPage: PurchaseOrderPage): Filter<PurchaseRequisitionLine> {
    const orderLinesSysId = orderPage.lines.value.map(line => line._id);
    const purchaseOrderLines = orderPage.lines.value
        .filter(poLine => poLine.purchaseRequisitionLines)
        .map(line => line.purchaseRequisitionLines?.at(0)?.purchaseRequisitionLine?._id ?? '');
    return {
        _id: { _nin: purchaseOrderLines },
        status: { _in: ['pending', 'inProgress'] },
        lineOrderStatus: { _in: ['notOrdered', 'partiallyOrdered'] },
        approvalStatus: { _in: ['approved', 'confirmed'] },
        document: {
            status: { _in: ['pending', 'inProgress'] },
            orderStatus: { _in: ['notOrdered', 'partiallyOrdered'] },
        },
        purchaseOrderLines: { _atMost: 0, purchaseOrderLine: { _id: { _in: orderLinesSysId } } },
        _and: [
            { _or: [{ site: { _id: { _eq: null } } }, { site: { _id: orderPage.stockSite.value?._id ?? '' } }] },
            {
                _or: [
                    { supplier: { _id: { _eq: null } } },
                    { supplier: { _id: orderPage.businessRelation.value?._id ?? '' } },
                    { itemSupplier: { supplier: { _id: orderPage.businessRelation.value?._id ?? '' } } },
                    { itemSiteSupplier: { supplier: { _id: orderPage.businessRelation.value?._id ?? '' } } },
                ],
            },
            ...(orderPage.currency.value?._id
                ? [
                      {
                          _or: [
                              { currency: { _id: { _eq: null } } },
                              { currency: { _id: orderPage.currency.value?._id ?? '' } },
                          ],
                      },
                  ]
                : []),
        ],
    };
}

// return type of lookupRequisitionLine function
export type OrderLineLookup = Awaited<ReturnType<typeof lookupRequisitionLine>>[0];

function overOrdered(line: ExtractEdgesPartial<PurchaseRequisitionLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) > +(line.quantityToOrder ?? 0);
}
function underOrdered(line: ExtractEdgesPartial<PurchaseRequisitionLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) < +(line.quantityToOrder ?? 0);
}

export function lookupRequisitionLine(
    orderPage: PurchaseOrderPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
) {
    return orderPage.$.dialog.lookup<PurchaseRequisitionLine & { selectedQuantity: number }>({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select requisition lines',
        node: '@sage/xtrem-purchasing/PurchaseRequisitionLine',
        selectedRecordId: selectedLines?.map(line => line._id),
        filter: filterForRequisitionLine(orderPage),
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        mapServerRecord(record) {
            const { quantityToOrder } = record;
            const selectedLine = selectedLines?.find(line => line._id === record._id);
            const quantity = +(selectedLine?.selectedQuantity ?? quantityToOrder ?? 0);
            return { ...record, selectedQuantity: record.selectedQuantity ?? quantity };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image', size: 'small' }),
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'requestedItemDescription',
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference({
                bind: 'document',
                valueField: 'number',
                node: '@sage/xtrem-purchasing/PurchaseOrder',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to order',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                validation(value) {
                    if (value <= 0)
                        return ui.localize(
                            '@sage/xtrem-purchasing/requisition_to_order_zero_quantity_error',
                            'You need to enter a quantity greater than zero.',
                        );
                    return undefined;
                },
            }),
            ui.nestedFields.date({ bind: 'needByDate', title: 'Required date', isReadOnly: true }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({ bind: 'quantityToOrder' }),
            ui.nestedFields.technical({ bind: 'quantity' }),
            ui.nestedFields.technical({ bind: 'charge' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({
                bind: 'stockSite',
                node: '@sage/xtrem-system/Site',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({
                        bind: 'primaryAddress',
                        node: '@sage/xtrem-master-data/BusinessEntityAddress',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                            ui.nestedFields.technical({ bind: 'addressLine1' }),
                            ui.nestedFields.technical({ bind: 'addressLine2' }),
                            ui.nestedFields.technical({ bind: 'city' }),
                            ui.nestedFields.technical({ bind: 'region' }),
                            ui.nestedFields.technical({ bind: 'postcode' }),
                            ui.nestedFields.technical({
                                bind: 'country',
                                node: '@sage/xtrem-structure/Country',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: '_id' }),
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                                ],
                            }),
                            ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'grossPrice' }),
            ui.nestedFields.technical({ bind: 'netPrice' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'priceOrigin' }),
            ui.nestedFields.technical({ bind: 'quantityToOrderInStockUnit' }),
            ui.nestedFields.technical({ bind: 'approvalStatus' }),
            ui.nestedFields.technical({ bind: 'lineOrderStatus' }),
            ui.nestedFields.technical({ bind: 'orderedQuantity' }),
            ui.nestedFields.technical({ bind: 'orderedQuantityInStockUnit' }),
            ui.nestedFields.technical({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'rounding' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
        ],
    });
}

function checkOrderlinesMessage(
    lines: ui.PartialCollectionValue<PurchaseRequisitionLine & { selectedQuantity: number }>[],
) {
    const isUnderOrdered = lines.some(underOrdered);
    const isOverOrdered = lines.some(overOrdered);
    if (isOverOrdered && isUnderOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/requisition_to_order__confirm__lower_greater_quantity',
            'You are about to create one or more order lines with a quantity different to the ordered quantity.',
        );
    }

    if (isUnderOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/requisition_to_order__confirm_lower_quantity',
            'You are about to create one or more order lines with a quantity less than the ordered quantity.',
        );
    }

    if (isOverOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/requisition_to_order__confirm_greater_quantity',
            'You are about to create one or more order lines with a quantity greater than the ordered quantity.',
        );
    }
    return null;
}

function confirmOrderQuantity(orderPage: PurchaseOrderPage, message: string) {
    return orderPage.$.dialog
        .confirmation(
            'warn',
            ui.localize(
                '@sage/xtrem-purchasing/requisition_to_order__create_purchase_dialog_title',
                'Confirm order quantity',
            ),
            message,
        )
        .then(() => true)
        .catch(() => false);
}

export async function addLineFromRequisition(
    orderPage: PurchaseOrderPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
) {
    const requisitionLines = (await lookupRequisitionLine(
        orderPage,
        selectedLines,
    )) as unknown as ui.PartialCollectionValue<PurchaseRequisitionLine & { selectedQuantity: number }>[];
    if (!requisitionLines || !isArray(requisitionLines) || requisitionLines.length === 0) {
        return;
    }

    const message = checkOrderlinesMessage(requisitionLines);
    if (message && !(await confirmOrderQuantity(orderPage, message))) {
        await addLineFromRequisition(
            orderPage,
            requisitionLines.map(line => ({ _id: line._id ?? '', selectedQuantity: line.selectedQuantity ?? 0 })),
        );
        return;
    }

    await asyncArray(requisitionLines).forEach(async requisitionLine => {
        const orderLine = await calculatePrices(
            orderPage,
            await convertRequisitionLineToOrderLine(orderPage, requisitionLine),
        );

        orderPage.lines.addRecord(orderLine);
        orderPage.purchaseRequisitionLineToPurchaseOrderLines.addOrUpdateRecordValue(requisitionLine);
    });
}
