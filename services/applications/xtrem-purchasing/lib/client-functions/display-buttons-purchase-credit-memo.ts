import type { PurchaseCreditMemoStatus } from '@sage/xtrem-purchasing-api';
import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-purchase-credit-memo';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-purchase-credit-memo';

const postedInProgressError: PurchaseCreditMemoStatus[] = ['posted', 'inProgress', 'error'];

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.recordId && data.parameters.status === 'draft') {
        return false;
    }
    return true;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.DeleteParameters<isDisabledButtons.DeleteParameters>,
) {
    return isHiddenButtonDeleteAction(data);
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        data.parameters.taxCalculationStatus &&
        !postedInProgressError.includes(data.parameters.status) &&
        ['done', 'failed'].includes(data.parameters.taxCalculationStatus)
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostParameters>,
) {
    return isHiddenButtonPostAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.isRepost) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (
        data.parameters &&
        !['posted', 'inProgress', 'error'].includes(data.parameters.status || '') &&
        data.parameters.site &&
        data.parameters.billBySupplier
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.StatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        postedInProgressError.includes(data.parameters.status)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.site && data.parameters.billBySupplier) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonNotifyBuyerAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.StatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        !postedInProgressError.includes(data.parameters.status)
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonNotifyBuyerAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.NotifyBuyerParameters>,
) {
    return !data.recordId || data.isDirty;
}

/* -------------------*/

export function isHiddenButtonSelectFromReturnAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromReturnParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.status &&
        !postedInProgressError.includes(data.parameters.status)
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonSelectFromReturnAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromReturnParameters>,
) {
    return isHiddenButtonSelectFromReturnAction(data);
}

/* -------------------*/

export function isHiddenButtonSelectFromInvoiceAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromInvoiceParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.status &&
        !postedInProgressError.includes(data.parameters.status)
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonSelectFromInvoiceAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromInvoiceParameters>,
) {
    return isHiddenButtonSelectFromInvoiceAction(data);
}
