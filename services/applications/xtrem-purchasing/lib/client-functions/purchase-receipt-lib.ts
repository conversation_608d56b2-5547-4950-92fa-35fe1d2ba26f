import type { Graph<PERSON><PERSON> } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { confirmDialogWithAcceptButtonText } from './common';

export async function createPurchaseInvoice(purchaseReceiptPage: ui.Page<GraphApi>, purchaseReceiptNumber: string) {
    if (!purchaseReceiptNumber) {
        return;
    }
    if (
        await confirmDialogWithAcceptButtonText(
            purchaseReceiptPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt__create_invoice_dialog_title',
                'Confirm invoice creation',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt__create_invoice_dialog_content',
                'You are about to create an invoice from this purchase order.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
        )
    ) {
        purchaseReceiptPage.$.loader.isHidden = false;
        const invoices = await purchaseReceiptPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReceipt')
            .mutations.createPurchaseInvoice({ number: true }, { document: purchaseReceiptNumber })
            .execute();

        if (invoices.length === 0) {
            purchaseReceiptPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_receipt__invoice_not_created',
                    'Could not create invoice.',
                ),
                { type: 'error' },
            );
        } else {
            purchaseReceiptPage.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_receipt__invoice_created',
                    'Invoice created: {{invoiceNumbers}}.',
                    { invoiceNumbers: invoices.map(invoice => invoice.number).join('\n') },
                ),
                { type: 'success' },
            );
        }

        purchaseReceiptPage.$.setPageClean();
        purchaseReceiptPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseReceipt`, {
            _id: purchaseReceiptPage.$.recordId || '',
        });

        purchaseReceiptPage.$.loader.isHidden = true;
    }
}
