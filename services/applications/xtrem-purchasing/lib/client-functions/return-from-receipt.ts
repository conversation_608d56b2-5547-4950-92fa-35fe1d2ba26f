import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';

import { asyncArray } from '@sage/xtrem-async-helper';
import * as Pill<PERSON><PERSON><PERSON><PERSON>ommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import type {
    PurchaseReceiptLine,
    PurchaseReceiptLineToPurchaseReturnLine,
    PurchaseReturnLine,
} from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { PurchaseReturn as PurchaseReturnPage } from '../pages/purchase-return';

function filterForReceiptLine(returnPage: PurchaseReturnPage): Filter<PurchaseReceiptLine> {
    const returnLinesSysId = returnPage.lines.value.map(line => line._id);

    const receiptLines = returnPage.lines.value.map(
        invoiceLine => invoiceLine.purchaseReceiptLine?.purchaseReceiptLine?._id ?? '',
    );

    return {
        _id: { _nin: receiptLines },
        document: {
            supplier: { _id: { _eq: returnPage.businessRelation.value?._id } },
            currency: { _id: { _eq: returnPage.currency.value?._id } },
            status: { _in: ['pending', 'inProgress', 'closed'] },
            returnStatus: { _in: ['notReturned', 'partiallyReturned'] },
            site: { _id: { _eq: returnPage.returnSite.value?._id } },
        },
        lineReturnStatus: { _in: ['notReturned', 'partiallyReturned'] },
        status: { _in: ['pending', 'inProgress', 'closed'] },
        purchaseReturnLines: { _atMost: 0, purchaseReturnLine: { _id: { _in: returnLinesSysId } } },
    };
}

export type ReceiptLineLookup = Awaited<ReturnType<typeof lookUpReceiptLine>>[0];

function validateQuantity(quantity: number, rowData: PurchaseReceiptLine & { selectedQuantity: number }) {
    if (quantity <= 0) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__return_from_receipt__error_zero_or_negative',
            `You need to enter a quantity.`,
        );
    }

    if (quantity > +rowData.remainingReturnQuantity) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__return_from_receipt__greater_quantity_error',
            'The returns quantity needs to be the same as or less than the receipt.',
        );
    }
    return undefined;
}

export function lookUpReceiptLine(
    returnPage: PurchaseReturnPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
) {
    return returnPage.$.dialog.lookup<PurchaseReceiptLine & { selectedQuantity: number }>({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select receipt lines',
        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
        filter: filterForReceiptLine(returnPage),
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        selectedRecordId: selectedLines?.map(line => line._id),
        mapServerRecord(record) {
            const { remainingReturnQuantity } = record;
            const selectedQuantity = Number(remainingReturnQuantity ?? 0);
            return { selectedQuantity, ...record };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, size: 'small' }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                valueField: 'name',
                columns: [ui.nestedFields.technical({ bind: 'isStockManaged' })],
            }),
            ui.nestedFields.text({
                bind: { item: { id: true } },
                title: 'Item Id',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'document',
                valueField: 'number',
                title: 'Receipt number',
                node: '@sage/xtrem-purchasing/PurchaseReceipt',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: { businessEntity: { name: true } } }),
                            ui.nestedFields.technical({ bind: { businessEntity: { id: true } } }),
                            ui.nestedFields.technical({ bind: { businessEntity: { taxIdNumber: true } } }),
                            ui.nestedFields.technical({ bind: { businessEntity: { country: { name: true } } } }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'isTransferHeaderNote' }),
                    ui.nestedFields.technical({ bind: 'isTransferLineNote' }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: { document: { displayStatus: true } },
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.document?.displayStatus),
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to return',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                validation(value, rowData) {
                    return validateQuantity(value, rowData);
                },
            }),
            ui.nestedFields.technical({ bind: 'remainingReturnQuantity' }),
            ui.nestedFields.technical({ bind: 'grossPrice' }),
            ui.nestedFields.technical({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: { site: { legalCompany: { priceScale: true } } } }),
            ui.nestedFields.technical({ bind: 'priceOrigin' }),
            ui.nestedFields.technical({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [ui.nestedFields.technical({ bind: 'priceScale' })],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'charge' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
            ui.nestedFields.technical({ bind: 'amountIncludingTax' }),
            ui.nestedFields.technical({ bind: 'taxAmount' }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
        ],
    });
}

function overReturned(line: ExtractEdgesPartial<PurchaseReceiptLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) > +(line.remainingReturnQuantity ?? 0);
}
function underReturned(line: ExtractEdgesPartial<PurchaseReceiptLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) < +(line.remainingReturnQuantity ?? 0);
}

function checkReceiptLinesMessage(
    lines: ui.PartialCollectionValue<PurchaseReceiptLine & { selectedQuantity: number }>[],
) {
    const isUnderReturned = lines.some(underReturned);
    const isOverReturned = lines.some(overReturned);
    if (isOverReturned && isUnderReturned) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__receipt__to_return_confirm__lower_greater_quantity',
            'The returns quantity is more than the received quantity.',
        );
    }

    if (isUnderReturned) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__receipt__to_return_confirm_lower_quantity',
            'The returns quantity is less than than the received quantity.',
        );
    }

    if (isOverReturned) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__receipt__to_return__confirm_greater_quantity',
            'The returns quantity is more than the received quantity.',
        );
    }
    return null;
}

async function confirmReturnQuantity(returnPage: PurchaseReturnPage, message: string) {
    try {
        await returnPage.$.dialog.confirmation(
            'warn',
            ui.localize(
                '@sage/xtrem-purchasing/pages__receipt__to_return__create_purchase_dialog_title',
                'Confirm return quantity',
            ),
            message,
        );
        return true;
    } catch {
        return false;
    }
}

function convertReceiptLineToPurchaseReceiptLine(
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & { selectedQuantity: number }>,
): ui.PartialCollectionValue<PurchaseReceiptLineToPurchaseReturnLine> {
    return {
        purchaseReceiptLine: {
            _id: receiptLine._id,
            document: { number: receiptLine.document?.number ?? '' },
            status: receiptLine.status,
        },
        returnedQuantity: String(receiptLine.selectedQuantity ?? 0),
    };
}

function convertReceiptLineToReturnLine(
    returnPage: PurchaseReturnPage,
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & { selectedQuantity: number }>,
): ui.PartialCollectionValue<PurchaseReturnLine> {
    const purchaseReceiptLine = convertReceiptLineToPurchaseReceiptLine(receiptLine);

    delete receiptLine._id;
    delete receiptLine.document;
    delete receiptLine.taxes;
    delete receiptLine.uiTaxes;
    delete receiptLine.taxAmount;
    delete receiptLine.taxAmountAdjusted;
    delete receiptLine.taxDate;

    return {
        ...receiptLine,
        status: 'draft',
        origin: 'purchaseReceipt',
        quantity: (receiptLine.selectedQuantity ?? 0).toString(),
        unitToStockUnitConversionFactor: receiptLine.unitToStockUnitConversionFactor,
        quantityInStockUnit: (
            parseFloat(String(receiptLine.selectedQuantity)) *
            parseFloat(receiptLine.unitToStockUnitConversionFactor as string)
        ).toString(),
        currency: returnPage.currency.value ?? undefined,
        purchaseReceiptLine,
        amountExcludingTax: receiptLine.amountExcludingTax,
        amountExcludingTaxInCompanyCurrency: receiptLine.amountExcludingTaxInCompanyCurrency,
        allocationStatus: receiptLine.item?.isStockManaged ? 'notAllocated' : 'notManaged',
        internalNote: { value: '' },
        externalNote: { value: '' },
        isExternalNote: false,
    };
}

function updateLines(returnPage: PurchaseReturnPage, invoiceLine: ExtractEdgesPartial<PurchaseReturnLine>) {
    returnPage.lines.addRecord(invoiceLine);
}

export async function addLineFromReceipt(
    returnPage: PurchaseReturnPage,
    selectedLines?: { _id: string; selectedQuantity: number }[],
): Promise<void> {
    const receiptLines = (await lookUpReceiptLine(returnPage, selectedLines)) as unknown as ui.PartialCollectionValue<
        PurchaseReceiptLine & { selectedQuantity: number }
    >[];
    if (!receiptLines || !isArray(receiptLines) || receiptLines.length === 0) {
        return;
    }

    const message = checkReceiptLinesMessage(receiptLines);
    if (message && !(await confirmReturnQuantity(returnPage, message))) {
        await addLineFromReceipt(
            returnPage,
            receiptLines.map(line => ({
                _id: line._id ?? '',
                selectedQuantity: line.selectedQuantity ?? 0,
            })),
        );
        return;
    }

    await asyncArray(receiptLines).forEach(
        (receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & { selectedQuantity: number }>) => {
            const returnLine = convertReceiptLineToReturnLine(returnPage, receiptLine);
            updateLines(returnPage, returnLine);
        },
    );

    returnPage.updateAllocationStatus();
}
