import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';

export function mapLandedCostSummary(aggregateLandedCosts: LandedCostInterfaces.LandedCostTypeSummaryLine[]): {
    tableLandedCostsValues: LandedCostInterfaces.LandedCostTypeSummaryLineBinding[];
    total: number;
} {
    let total = 0;
    const tableLandedCostsValues = aggregateLandedCosts.map((line, index) => {
        total += Number(line.actualCostAmountInCompanyCurrency);
        return {
            _id: String(index + 1),
            landedCostType: line.landedCostType,
            actualCostAmountInCompanyCurrency: Number(line.actualCostAmountInCompanyCurrency),
            actualAllocatedCostAmountInCompanyCurrency: Number(line.actualAllocatedCostAmountInCompanyCurrency),
        } as LandedCostInterfaces.LandedCostTypeSummaryLineBinding;
    });
    return { tableLandedCostsValues, total };
}
