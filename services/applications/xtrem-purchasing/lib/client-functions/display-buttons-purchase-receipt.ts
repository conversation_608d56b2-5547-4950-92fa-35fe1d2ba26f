import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-purchase-receipt';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-purchase-receipt';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.parameters.isRepost) {
        return true;
    }

    if (data.recordId) {
        if (data.parameters.status !== 'closed') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status !== 'closed') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonSaveAction(data: isHiddenButtons.RepostParameters) {
    return data.isRepost || !data.isDirty;
}

export function isHiddenButtonCancelAction(data: isHiddenButtons.RepostParameters) {
    return data.isRepost || !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.StatusParameters>,
) {
    if (
        data.recordId &&
        !(
            data.parameters.status === 'inProgress' ||
            data.parameters.status === 'pending' ||
            data.parameters.status === 'closed'
        )
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.DeleteParameters<isDisabledButtons.DeleteParameters>,
) {
    return isHiddenButtonDeleteAction(data);
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        !['posted', 'inProgress', 'error', 'closed'].includes(data.parameters.status) &&
        data.parameters.stockTransactionStatus !== 'completed' &&
        data.parameters.taxCalculationStatus &&
        ((data.parameters.taxEngine === 'genericTaxCalculation' &&
            ['done', 'failed'].includes(data.parameters.taxCalculationStatus)) ||
            (data.parameters.taxEngine !== 'genericTaxCalculation' &&
                data.parameters.taxCalculationStatus !== 'inProgress'))
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostParameters>,
) {
    return isHiddenButtonPostAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.isRepost) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isHiddenButtonCreatePurchaseReturnAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreatePurchaseReturnParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'draft' &&
        data.parameters.lines !== 0 &&
        data.parameters.returnStatus !== 'returned' &&
        data.parameters.stockTransactionStatus !== 'error' &&
        !data.parameters.isRepost
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreatePurchaseReturnAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CreatePurchaseReturnParameters>,
) {
    return isHiddenButtonCreatePurchaseReturnAction(data);
}

/* -------------------*/

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.supplier &&
        data.parameters &&
        data.parameters.status !== 'closed' &&
        data.parameters.status !== 'pending'
    ) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.StatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        ['inProgress', 'error', 'closed', 'pending'].includes(data.parameters.status)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.site && data.parameters.supplier) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonSelectFromPurchaseOrderLinesAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromPurchaseOrderLinesParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.supplier &&
        data.parameters.status &&
        data.parameters.status === 'draft'
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonSelectFromPurchaseOrderLinesAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromPurchaseOrderLinesParameters>,
) {
    return isHiddenButtonSelectFromPurchaseOrderLinesAction(data);
}
