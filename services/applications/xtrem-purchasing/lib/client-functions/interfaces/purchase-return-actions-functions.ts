import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface PurchaseReturnPageParameter {
    purchaseReturnPage: ui.Page<GraphApi>;
}
export interface ActionParameter extends PurchaseReturnPageParameter {
    recordNumber: string;
}

export interface ConfirmActionParameters extends ActionParameter {
    recordId: string;
    isConfirmed: boolean;
}

export interface SetDimensionActionParameter extends ActionParameter {
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface ActionParameterWithId extends PurchaseReturnPageParameter {
    recordId: string;
}

export interface ActionParameterIdAndNum extends ActionParameterWithId {
    recordNumber: string;
}

export interface ApproveOrRejectActionParameters extends ActionParameterIdAndNum {
    isCalledFromRecordPage: boolean;
    isApproved: boolean;
}

export interface RejectActionParameters extends ActionParameterIdAndNum {
    isCalledFromRecordPage: boolean;
    isRejected: boolean;
}
