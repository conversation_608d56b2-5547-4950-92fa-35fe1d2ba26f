import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type {
    PurchaseDocumentApprovalStatus,
    PurchaseDocumentStatus,
    PurchaseRequisitionLineBinding,
} from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';

export interface StatusParameters {
    status?: PurchaseDocumentStatus | null;
}

export interface ApproveParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface RejectParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface RequestApprovalParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
    isApprovalManaged?: boolean | null;
}

export interface RequestChangesParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface CloseParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface ApplyDefaultSuppliersParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
    lines: ui.PartialNodeWithId<PurchaseRequisitionLineBinding>[];
}

export interface CreatePurchaseOrderParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
    islinesCreateOrder: boolean | null;
}

export interface DefaultDimensionParameters extends StatusParameters {
    orderStatus?: string | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
    lines: PartialCollectionValueWithIds<PurchaseRequisitionLineBinding>[];
}

export interface ConfirmParameters extends StatusParameters {
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
    isApprovalManaged?: boolean | null;
}

export interface DeleteParameters extends StatusParameters {
    orderStatus?: string | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}
