import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { PurchaseCreditMemoStatus } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';

export interface StatusParameters {
    status?: string | null;
}

export interface DeleteParameters {
    status?: PurchaseCreditMemoStatus | null;
}

export interface PostParameters {
    status?: PurchaseCreditMemoStatus | null;
    varianceTotalAmountIncludingTax?: number | null;
    taxCalculationStatus?: string | null;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface LinePhantomRowParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    billBySupplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface AddPurchaseCreditMemoLineParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    billBySupplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    billBySupplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface NotifyBuyerParameters {
    status?: string | null;
}

export interface SelectFromReturnParameters {
    site?: ExtractEdgesPartial<Site> | null;
    status?: PurchaseCreditMemoStatus | null;
}

export interface SelectFromInvoiceParameters {
    site?: ExtractEdgesPartial<Site> | null;
    status?: PurchaseCreditMemoStatus | null;
}
