import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type {
    PurchaseDocumentApprovalStatus,
    PurchaseDocumentStatus,
    PurchaseRequisitionLineBinding,
} from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface SaveParameters {
    status?: PurchaseDocumentStatus | null;
}

export interface CancelParameters {
    status?: PurchaseDocumentStatus | null;
}

export interface DeleteParameters {
    status?: PurchaseDocumentStatus | null;
    orderStatus?: string | null;
}

export interface ApproveParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface RejectParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface RequestApprovalParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface RequestChangesParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface CloseParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}

export interface ApplyDefaultSuppliersParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
    lines: ui.PartialNodeWithId<PurchaseRequisitionLineBinding>[];
}

export interface CreatePurchaseOrderParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
    islinesCreateOrder: boolean | null;
}

export interface LinePhantomRowParameters {
    status?: PurchaseDocumentStatus | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
    requestDate?: string | null;
    requester?: ExtractEdgesPartial<any> | null;
    approvalStatus?: string | null;
}

export interface AddPurchaseRequisitionLineParameters {
    status?: PurchaseDocumentStatus | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
    requestDate?: string | null;
    requester?: ExtractEdgesPartial<any> | null;
}

export interface DefaultDimensionParameters {
    receivingSite?: ExtractEdgesPartial<Site> | null;
    requestDate?: string | null;
    requester?: ExtractEdgesPartial<any> | null;
}

export interface ConfirmParameters {
    status?: PurchaseDocumentStatus | null;
    approvalStatus?: PurchaseDocumentApprovalStatus | null;
}
