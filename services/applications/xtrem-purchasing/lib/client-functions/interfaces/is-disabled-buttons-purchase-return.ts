import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface SaveParameters {
    status?: string | null;
}

export interface CancelParameters {
    status?: string | null;
}

export interface DeleteParameters {
    status?: string | null;
    orderStatus?: string | null;
}

export interface ApproveParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface RejectParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface RequestApprovalParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface CloseParameters {
    status?: string | null;
    approvalStatus?: string | null;
}

export interface PostParameters {
    status?: string | null;
    approvalStatus?: string | null;
    stockTransactionStatus?: string | null;
    allocationStatus?: string | null;
    returnItems?: Boolean | null;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface DefaultDimensionParameters {
    returnSite?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}
