import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi, PurchaseCreditMemoStatus } from '@sage/xtrem-purchasing-api';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface GetPurchaseCreditMemoLinesParameter {
    purchaseCreditMemoPage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SetDimensionActionParameter {
    purchaseCreditMemoPage: ui.Page<GraphApi>;
    recordNumber: string;
    status: string | null;
    isRepost: boolean;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface PurchaseCreditMemoPageWithPost extends ui.Page<GraphApi> {
    post: ui.PageAction;
}

export interface CreditMemoPageParameter {
    purchaseCreditMemoPage: PurchaseCreditMemoPageWithPost;
    recordId: string;
}
export interface PostParameter extends CreditMemoPageParameter {
    isCalledFromRecordPage: boolean;
    taxCalculationStatus: string;
    varianceTotalAmountIncludingTax: number | null;
    lines: number;
}

export interface CreditMemoPostingParameter extends CreditMemoPageParameter {
    isCalledFromRecordPage: boolean;
}

export interface UpdateStatusParameter extends CreditMemoPageParameter {
    status: PurchaseCreditMemoStatus;
    stockTransactionStatus: StockDocumentTransactionStatus;
}
