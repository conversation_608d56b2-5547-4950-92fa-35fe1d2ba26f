import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { PurchaseReceiptLine } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface SaveParameters {
    status?: string | null;
    isRepost: boolean;
}

export interface CancelParameters {
    status?: string | null;
}

export interface DeleteParameters {
    status?: string | null;
}

export interface IsHiddenButtonPostParameters {
    status?: string | null;
    stockTransactionStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
    lines?: ui.PartialNodeWithId<PurchaseReceiptLine>[];
}

export interface PostParameters {
    status?: string | null;
    stockTransactionStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
    lines?: ui.PartialNodeWithId<PurchaseReceiptLine>[];
    isCalledFromRecordPage: boolean;
    isStockDetailRequired?: boolean;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface CreatePurchaseReturnParameters {
    status?: string | null;
    returnStatus?: string | null;
    lines: number;
    stockTransactionStatus: string | null;
    isRepost: boolean;
}

export interface LinePhantomRowParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface AddPurchaseReceiptLineParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface SelectFromPurchaseOrderLinesParameters {
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    status?: string | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}
