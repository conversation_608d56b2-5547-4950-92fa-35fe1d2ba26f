import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, Supplier } from '@sage/xtrem-master-data-api';
import type { PurchaseOrderLineBinding } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface StatusParameters extends RepostParameters {
    status?: string | null;
}

export interface DeleteParameters extends StatusParameters {
    approvalStatus?: string | null;
}

export interface ApproveParameters extends StatusParameters {
    approvalStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
}

export interface RejectParameters extends StatusParameters {
    approvalStatus?: string | null;
}

export interface RequestApprovalParameters extends StatusParameters {
    approvalStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
}

export interface ConfirmParameters extends StatusParameters {
    approvalStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface RequestChangesParameters extends StatusParameters {
    approvalStatus?: string | null;
}

export interface CreatePurchaseReceiptParameters extends StatusParameters {
    approvalStatus?: string | null;
    totalQuantityToReceiveInStockUnit?: number | null;
}

export interface CloseParameters extends StatusParameters {
    approvalStatus?: string | null;
    lines: ui.PartialNodeWithId<PurchaseOrderLineBinding>[];
}

export interface SendMailParameters {
    approvalStatus?: string | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface LinePhantomRowParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    currency?: ExtractEdgesPartial<Currency> | null;
    approvalStatus?: string | null;
}

export interface SelectFromPurchaseRequisitionLinesParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    currency?: ExtractEdgesPartial<Currency> | null;
    approvalStatus?: string | null;
}
