import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import type { RegionLabel } from '@sage/xtrem-structure-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

interface PurchaseInvoicePageWithPost extends ui.Page<GraphApi> {
    post: ui.PageAction;
}

export interface PurchaseInvoicePage {
    purchaseInvoicePage: PurchaseInvoicePageWithPost;
}
export interface ActionParameters extends PurchaseInvoicePage {
    recordNumber?: string | null;
    recordId?: string | null;
}

export interface SetDimensionActionParameter extends ActionParameters {
    status: string | null;
    isRepost: boolean;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface ActionParametersWithId extends PurchaseInvoicePage {
    recordId: string;
}

export interface PostActionParameter extends ActionParameters {
    taxCalculationStatus: string | null;
    varianceTotalAmountExcludingTax: number;
    varianceTotalTaxAmount: number;
    recordId: string;
    lines: number;
}

export interface AcceptAllVariancesParameter extends PurchaseInvoicePage {
    recordId: string;
}

export interface CreateCreditMemoAction extends ActionParameters {
    isCalledFromRecordPage: boolean;
    currency: string | number | boolean;
}

export interface FinanceIntegrationCheckParameter extends PurchaseInvoicePage {
    recordId: string | null;
    isWarning: boolean;
}
export interface AddressDetails {
    billByAddress: {
        country: {
            _id: string;
            name: string;
            id: string;
            flag: { value: boolean };
            zipLabel: string;
            regionLabel: string;
        };
        postcode: string;
        region: string;
        city: string;
        addressLine2: string;
        addressLine1: string;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        name: string;
        _id: string;
    };
    billByLinkedAddress: {
        _id: string;
        name: string;
        isActive: boolean;
        businessEntity: {
            id: string;
            _id: string;
        };
        locationPhoneNumber: string;
        country: {
            zipLabel: string;
            regionLabel: RegionLabel;
            name: string;
            _id: string;
        };
        postcode: string;
        region: string;
        city: string;
        addressLine2: string;
        addressLine1: string;
    };
    payToAddress: {
        country: {
            _id: string;
            name: string;
            id: string;
            flag: { value: boolean };
            zipLabel: string;
            regionLabel: string;
        };
        postcode: string;
        region: string;
        city: string;
        addressLine2: string;
        addressLine1: string;
        locationPhoneNumber: string;
        concatenatedAddress: string;
        name: string;
        _id: string;
    };
}
