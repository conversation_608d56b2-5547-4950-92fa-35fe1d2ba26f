import type {
    DocumentLineNestedGrid,
    DocumentNestedGrid,
    Unit,
} from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';

export interface PurchaseReceiptLineNestedGrid extends DocumentLineNestedGrid {
    quantity: string;
    unit: Unit;
    receivedQuantity?: string;
    stockTransactionStatus: StockDocumentTransactionStatus | null;
}

export interface PurchaseReceiptNestedGrid extends DocumentNestedGrid {
    displayStatus: string | null;
    supplierDocumentNumber: string;
    supplier: {
        businessEntity: {
            name: string;
        };
    };
    lines: DocumentLineNestedGrid;
}
