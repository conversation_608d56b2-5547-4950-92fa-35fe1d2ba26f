import type { OrderToOrderDemandType } from '@sage/xtrem-stock-data-api';

export interface DemandOrderAssignmentTable {
    _id: string;
    demandType: OrderToOrderDemandType;
    demandDocumentLine: {
        _id: string;
        documentId: number;
        documentNumber: string;
    };
    demandWorkInProgress: {
        expectedQuantity: string;
    };
    quantityInStockUnit: string;
    quantityNotAssigned: number;
    action: string;
}

export interface OrderAssignmentLine extends Omit<DemandOrderAssignmentTable, 'quantityNotAssigned' | 'action'> {}
