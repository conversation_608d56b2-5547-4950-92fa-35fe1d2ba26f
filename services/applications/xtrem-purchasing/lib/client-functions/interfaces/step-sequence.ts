import type * as ui from '@sage/xtrem-ui';

export interface PurchaseReturnStepSequenceStatus {
    create: ui.StepSequenceStatus;
    approve: ui.StepSequenceStatus;
    confirm: ui.StepSequenceStatus;
    allocate: ui.StepSequenceStatus;
    post: ui.StepSequenceStatus;
}
export interface PurchaseReceiptStepSequenceStatus {
    create: ui.StepSequenceStatus;
    invoice: ui.StepSequenceStatus;
    return: ui.StepSequenceStatus;
}

export interface PurchaseOrderStepSequenceStatus {
    create: ui.StepSequenceStatus;
    approve: ui.StepSequenceStatus;
    confirm: ui.StepSequenceStatus;
    receive: ui.StepSequenceStatus;
    invoice: ui.StepSequenceStatus;
}

export interface PurchaseRequisitionStepSequenceStatus {
    create: ui.StepSequenceStatus;
    approve: ui.StepSequenceStatus;
    confirm: ui.StepSequenceStatus;
    order: ui.StepSequenceStatus;
}

export interface PurchaseInvoiceStepSequenceStatus {
    create: ui.StepSequenceStatus;
    post: ui.StepSequenceStatus;
    pay?: ui.StepSequenceStatus;
}
