import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi, PurchaseRequisitionLine } from '@sage/xtrem-purchasing-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface CalculatePricesParameter {
    netPrice: number;
    grossPrice: number;
    charge: number;
    discount: number;
    quantity: number;
    totalTaxExcludedAmount: number;
    legalCompany: ExtractEdgesPartial<Company>;
}
export interface CalculatePricesResult {
    resultNetPrice: string;
    resultTotalTaxExcludedAmount: string;
}

export interface ApplyDefaultSupplierActionParameter extends ActionsParameter {
    lines: ui.PartialNodeWithId<PurchaseRequisitionLine>[];
    status: string;
    requestDate: string;
    legalCompany: ExtractEdgesPartial<Company>;
    isCalledFromRecordPage: boolean;
}

export interface PurchaseRequisitionPageParameter {
    purchaseRequisitionPage: ui.Page<GraphApi>;
}

export interface ActionsParameter extends PurchaseRequisitionPageParameter {
    purchaseRequisitionPage: ui.Page<GraphApi>;
    recordId: string;
}

export interface GetDefaultSupplierParameter extends PurchaseRequisitionPageParameter {
    item: string;
    site: string;
}

export interface FilterLinesParameter {
    lines: ui.PartialNodeWithId<PurchaseRequisitionLine>[];
    status: string;
}

export interface ConfirmActionParameters extends ActionsParameter {
    recordNumber: string;
    isConfirmed: boolean;
}

export interface SetDimensionActionParameter extends ActionsParameter {
    status: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface ApproveRejectActionParameter extends ActionsParameter {
    recordNumber: string;
    approve: boolean;
}

export interface LoadApproversParameters {
    purchaseRequisitionPage: ui.Page<GraphApi>;
    siteId: string;
}

export interface UserApprover extends ui.PartialNodeWithId<User> {
    type: string;
    sortOrder: number;
}

interface ApproverUser {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
}

export interface SiteApprover {
    id: string;
    purchaseRequisitionDefaultApprover: ApproverUser;
    purchaseRequisitionSubstituteApprover: ApproverUser;
}
export interface CreateActionParameter extends PurchaseRequisitionPageParameter {
    lines: ui.PartialNodeWithId<PurchaseRequisitionLine>[];
    recordId: string;
}

export interface OrderParameter extends PurchaseRequisitionPageParameter {
    recordNumber: string;
}
