import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi, PurchaseReceiptLine } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface GetPurchaseReceiptLinesParameter {
    purchaseReceiptPage: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface SetDimensionActionParameter {
    purchaseReceiptPage: ui.Page<GraphApi>;
    recordNumber: string;
    status: string | null;
    isRepost: boolean;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface PostActionParameter extends ActionParameters {
    purchaseReceiptPage: ui.Page<GraphApi>;
    recordId: string;
    recordNumber: string;
    taxCalculationStatus: string | null;
}

export interface ActionParameters {
    purchaseReceiptPage: ui.Page<GraphApi>;
    recordId: string;
}

export interface CreatePurchaseReturnActionParameters extends ActionParameters {
    isCalledFromRecordPage: boolean;
    recordNumber: string;
    lines?: ui.PartialNodeWithId<PurchaseReceiptLine>[];
}

export interface PrintParameters {
    page: ui.Page<GraphApi>;
    number: string;
    _id: string;
    status: string;
}
