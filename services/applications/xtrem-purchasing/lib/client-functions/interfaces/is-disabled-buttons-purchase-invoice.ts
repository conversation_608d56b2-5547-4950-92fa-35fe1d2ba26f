import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface SaveParameters {
    status?: string | null;
}

export interface CancelParameters {
    status?: string | null;
}

export interface DeleteParameters {
    status?: string | null;
}

export interface AcceptAllVariancesParameters {
    status?: string | null;
    matchingStatus?: string | null;
}

export interface PostParameters {
    landedCostNotFullyAllocated?: boolean;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface AcceptAllVarianceParameters {
    status?: string | null;
    matchingStatus?: string | null;
}

export interface CreatePurchaseCreditNoteParameters {
    status?: string | null;
    calculatedTotalRemainingQuantityToCredit?: number | null;
}

export interface LinePhantomRowParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    billBySupplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface AddPurchaseInvoiceLineParameters {
    status?: string | null;
    site?: ExtractEdgesPartial<Site> | null;
    billBySupplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface DefaultDimensionParameters {
    site?: ExtractEdgesPartial<Site> | null;
    billBySupplier?: ExtractEdgesPartial<Supplier> | null;
}

export interface NotifyBuyerParameters {
    status?: string | null;
}

export interface SelectFromReceiptParameters {
    site?: ExtractEdgesPartial<Site> | null;
    status?: string | null;
}
