import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { PurchaseRequisitionLineBinding } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface StatusParameters {
    status?: string | null;
}
export interface StatusApprovalStatusParameters extends StatusParameters {
    isApprovalManaged?: boolean | null;
    approvalStatus?: string | null;
}

export interface ApplyDefaultSuppliersParameters extends StatusApprovalStatusParameters {
    lines: ui.PartialNodeWithId<PurchaseRequisitionLineBinding>[];
}

export interface PostParameters extends StatusApprovalStatusParameters {
    stockTransactionStatus?: string | null;
    allocationStatus?: string | null;
    returnItems?: Boolean | null;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface DefaultDimensionParameters extends StatusParameters {
    orderStatus?: string | null;
}

export interface SelectFromPurchaseReceiptLinesParameters extends StatusParameters {
    returnSite?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    approvalStatus?: string | null;
}

export interface DeleteParameters extends StatusParameters {
    orderStatus?: string | null;
    approvalStatus?: string | null;
}
