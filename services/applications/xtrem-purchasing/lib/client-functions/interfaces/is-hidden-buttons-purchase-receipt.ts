import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { PurchaseReceiptLine } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface StatusParameters {
    status?: string | null;
}
export interface PostParameters extends StatusParameters {
    stockTransactionStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
    lines?: ui.PartialNodeWithId<PurchaseReceiptLine>[];
    isCalledFromRecordPage: boolean;
    isStockDetailRequired?: boolean;
}

export interface RepostParameters {
    isRepost?: boolean;
    isDirty?: boolean;
}

export interface CreatePurchaseReturnParameters extends StatusParameters {
    returnStatus?: string | null;
    lines: number;
    stockTransactionStatus: string | null;
    isRepost: boolean;
}

export interface SelectFromPurchaseOrderLinesParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}
