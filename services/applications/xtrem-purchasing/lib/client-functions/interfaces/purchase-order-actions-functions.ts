import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import type { Site, User } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface PageParameters {
    purchaseOrderPage: ui.Page<GraphApi>;
}

export interface PageRecordIdParameters extends PageParameters {
    recordId: string;
}

export interface PageRecordNumberParameters extends PageParameters {
    recordNumber: string;
}

export interface PageRecordNumberIdParameters extends PageRecordIdParameters {
    recordNumber: string;
}
export interface ConfirmActionParameters extends PageRecordNumberIdParameters {
    isCalledFromRecordPage: boolean;
    isConfirmed: boolean;
    taxCalculationStatus: string | null;
}

export interface CreatePurchaseReceiptActionParameters extends PageRecordNumberIdParameters {
    isCalledFromRecordPage: boolean;
}

export interface ApproveOrRejectActionParameters extends PageRecordNumberIdParameters {
    isCalledFromRecordPage: boolean;
    isApproved: boolean;
}

export interface ApproveActionParameters extends PageRecordNumberIdParameters {
    isCalledFromRecordPage: boolean;
    isApproved: boolean;
}

export interface RejectActionParameters extends PageRecordNumberIdParameters {
    isCalledFromRecordPage: boolean;
    isRejected: boolean;
}

export interface LoadApproversParameters {
    purchaseOrderPage: ui.Page<GraphApi>;
    siteId: string;
}

export interface RequestApprovalParameters extends PageRecordIdParameters {
    isCalledFromRecordPage: boolean;
    siteId: string;
    isGrossPriceMissing: boolean;
}

export interface CloseActionParameters extends PageRecordNumberIdParameters {
    isCalledFromRecordPage: boolean;
}

export interface SiteApprover {
    id: string;
    purchaseOrderDefaultApprover: {
        _id: string;
        email: string;
        firstName: string;
        lastName: string;
    };
    purchaseOrderSubstituteApprover: {
        _id: string;
        email: string;
        firstName: string;
        lastName: string;
    };
}

export interface UserApprover extends ui.PartialNodeWithId<User> {
    type: string;
    sortOrder: number;
}

export interface SetDimensionActionParameter extends PageRecordNumberParameters {
    status: string | null;
    approvalStatus: string | null;
    isRepost: boolean;
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
}
