import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Currency, Supplier } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface StatusParameters {
    status?: string | null;
    isRepost?: boolean;
}
export interface ApproveParameters extends StatusParameters {
    approvalStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
}

export interface RejectParameters extends StatusParameters {
    approvalStatus?: string | null;
}

export interface ConfirmParameters extends StatusParameters {
    approvalStatus?: string | null;
    taxEngine?: string | null;
    taxCalculationStatus?: string | null;
    isApprovalManaged?: boolean | null;
}

export interface RepostParameters {
    isDirty?: boolean;
    isRepost?: boolean;
    approvalStatus?: string | null;
}

export interface RequestApprovalParameters extends StatusParameters {
    approvalStatus?: string | null;
    taxEngine?: string | null;
    isApprovalManaged?: boolean | null;
    taxCalculationStatus?: string | null;
}

export interface RequestChangesParameters extends StatusParameters {
    approvalStatus?: string | null;
}

export interface CreatePurchaseReceiptParameters extends StatusParameters {
    approvalStatus?: string | null;
    totalQuantityToReceiveInStockUnit?: number | null;
}

export interface CloseParameters extends StatusParameters {
    approvalStatus?: string | null;
    totalQuantityToReceiveInStockUnit?: number | null;
}

export interface PrintParameters extends StatusParameters {
    taxCalculationStatus?: string | null;
}

export interface SendMailParameters {
    displayStatus?: string | null;
}

export interface DefaultDimensionParameters extends StatusParameters {
    displayStatus?: string | null;
    approvalStatus?: string | null;
}

export interface SelectFromPurchaseRequisitionLinesParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    currency?: ExtractEdgesPartial<Currency> | null;
    approvalStatus?: string | null;
}

export interface DeleteParameters extends StatusParameters {
    approvalStatus?: string | null;
}
