import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Site } from '@sage/xtrem-system-api';

export interface StatusParameters {
    status?: string | null;
}
export interface AcceptAllVariancesParameters extends StatusParameters {
    matchingStatus?: string | null;
}

export interface PostParameters extends StatusParameters {
    stockTransactionStatus?: string | null;
    financeIntegrationStatus?: string | null;
    matchingStatus?: string | null;
    taxCalculationStatus?: string | null;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface AcceptAllVarianceParameters extends StatusParameters {
    matchingStatus?: string | null;
}
export interface CreatePurchaseCreditNoteParameters extends StatusParameters {
    calculatedTotalRemainingQuantityToCredit?: number | null;
}

export interface SelectFromReceiptParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
}

export interface DeleteParameters extends StatusParameters {
    approvalStatus?: string | null;
}

export interface RecordPaymentParameters extends StatusParameters {
    paymentStatus?: string | null;
    taxCalculationStatus?: string | null;
}
