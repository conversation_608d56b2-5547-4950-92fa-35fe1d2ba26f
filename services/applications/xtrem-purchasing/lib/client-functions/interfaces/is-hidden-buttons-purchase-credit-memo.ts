import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { PurchaseCreditMemoStatus } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';

export interface StatusParameters {
    status?: PurchaseCreditMemoStatus | null;
}

export interface PostParameters extends StatusParameters {
    varianceTotalAmountIncludingTax?: number | null;
    taxCalculationStatus?: string | null;
}

export interface RepostParameters {
    isRepost?: boolean;
}

export interface SelectFromReturnParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
}

export interface SelectFromInvoiceParameters extends StatusParameters {
    site?: ExtractEdgesPartial<Site> | null;
}

export interface DeleteParameters {
    status?: string | null;
    approvalStatus?: string | null;
}
