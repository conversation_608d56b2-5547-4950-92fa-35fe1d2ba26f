import type { decimal, ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import type {
    BaseDocumentLineBinding,
    Currency,
    Item,
    Location,
    Supplier,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import type { PurchaseDocumentLineOrigin, PurchaseReceiptLineInput } from '@sage/xtrem-purchasing-api';
import type { StockStatus } from '@sage/xtrem-stock-data-api';
import type { Company, Site } from '@sage/xtrem-system-api';

export interface PurchaseReceiptLineInputWithDetails extends PurchaseReceiptLineInput {
    _stockDetails: string;
    currency: Currency;
    origin: PurchaseDocumentLineOrigin;
}

// duplicated from inventory interfaces (server side)
export type LotSearchData = {
    item: Item;
    id: string;
    sublot?: string;
};

// duplicated from inventory interfaces (server side)
export interface LotCreateData extends Omit<LotSearchData, 'id'> {
    id?: string;
    supplierLot?: string | null;
    expirationDate?: string;
    potency?: number;
    documentLine?: BaseDocumentLineBinding['_id'];
    creationDate?: string;
    expirationReferenceDate?: string;
    shelfLife?: number;
}

// duplicated from inventory interfaces (server side)
export interface LotData extends LotCreateData {
    _id?: integer;
}

// duplicated from inventory interfaces (server side)
export type StockDetail = {
    stockDetailQuantityInStockUnit: decimal;
};

// duplicated from inventory interfaces (server side)
export interface StockReceiptDetail extends StockDetail {
    status: StockStatus;
    stockUnit: UnitOfMeasure;
    location?: Location;
    lot?: LotData | null;
    owner: string;
    stockDetailQuantity: decimal;
}

// duplicated from inventory interfaces (server side)
export interface StockReceiptDetailJson {
    detailLines: ExtractEdgesPartial<StockReceiptDetail>[];
}

export interface JsonDimensions {
    [type: string]: string;
}

export interface PurchaseOrderReturned {
    _id: string;
    number: string;
    lines: {
        query: {
            edges: {
                node: {
                    _id: string | null;
                    quantityInStockUnit: string | null;
                    workInProgress: { _id: string | null };
                };
            }[];
        };
    };
}

export interface ReorderRecommendation {
    item: ExtractEdgesPartial<Item>;
    site: ExtractEdgesPartial<Site> | null;
    quantity: number;
    startDate: string;
    endDate: string;
    stockSite?: ExtractEdgesPartial<Site>;
    supplier?: ExtractEdgesPartial<Supplier>;
    isFromSalesOrder?: boolean;
    legalCompany?: ExtractEdgesPartial<Company>;
    storedDimensions?: string;
    storedAttributes?: string;
}
