import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-purchase-invoice';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-purchase-invoice';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.SaveParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status === 'draft') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.recordId && data.parameters.status === 'draft') {
        return false;
    }
    return true;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.DeleteParameters<isDisabledButtons.DeleteParameters>,
) {
    return isHiddenButtonDeleteAction(data);
}

/* -------------------*/

export function isHiddenButtonPostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.PostParameters>,
) {
    if (!data.recordId) return true;
    if (!data.parameters.status) return true;
    if (!data.parameters.taxCalculationStatus) return true;
    if (!['done', 'failed'].includes(data.parameters.taxCalculationStatus)) return true;
    if (data.parameters.financeIntegrationStatus !== 'toBeRecorded') return true; // temporary condition to not allow the user to post twice
    if (!data.parameters.matchingStatus) return true;
    if (!['noVariance', 'varianceApproved'].includes(data.parameters.matchingStatus)) return true;

    if (data.parameters.stockTransactionStatus === 'error') return data.isDirty;
    if (!['posted', 'inProgress', 'error'].includes(data.parameters.status)) return data.isDirty;

    return true;
}

export function isDisabledButtonPostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.PostParameters>,
) {
    return data.parameters.landedCostNotFullyAllocated;
}
/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.isRepost) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isHiddenButtonAcceptAllVariancesAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isDisabledButtons.AcceptAllVarianceParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        !['posted', 'inProgress', 'error'].includes(data.parameters.status) &&
        data.parameters.matchingStatus === 'variance'
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonAcceptAllVariancesAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isHiddenButtons.AcceptAllVarianceParameters>,
) {
    return isHiddenButtonAcceptAllVariancesAction(data);
}

/* -------------------*/

export function isHiddenButtonCreatePurchaseCreditNoteAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreatePurchaseCreditNoteParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        !['draft', 'inProgress', 'pending', 'closed', 'error'].includes(data.parameters.status) &&
        data.parameters.calculatedTotalRemainingQuantityToCredit &&
        data.parameters.calculatedTotalRemainingQuantityToCredit !== 0
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreatePurchaseReceiptAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CreatePurchaseCreditNoteParameters>,
) {
    return isHiddenButtonCreatePurchaseCreditNoteAction(data);
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.StatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        ['inProgress', 'error', 'posted'].includes(data.parameters.status)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.site && data.parameters.billBySupplier) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonNotifyBuyerAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.StatusParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.status &&
        !['posted', 'inProgress', 'error'].includes(data.parameters.status)
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonNotifyBuyerAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.NotifyBuyerParameters>,
) {
    return !data.recordId || data.isDirty;
}

/* -------------------*/

export function isHiddenButtonSelectFromReceiptAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromReceiptParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.status &&
        !['posted', 'inProgress', 'error'].includes(data.parameters.status)
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonSelectFromReceiptAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromReceiptParameters>,
) {
    return isHiddenButtonSelectFromReceiptAction(data);
}

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (
        data.parameters &&
        data.parameters.site &&
        data.parameters.billBySupplier &&
        data.parameters.status !== 'error' &&
        data.parameters.status !== 'posted' &&
        data.parameters.status !== 'inProgress'
    ) {
        return false;
    }
    return true;
}

export function isHiddenButtonRecordPaymentAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RecordPaymentParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status &&
        ['posted'].includes(data.parameters.status) &&
        data.parameters.paymentStatus &&
        ['notPaid', 'partiallyPaid'].includes(data.parameters.paymentStatus) &&
        data.parameters.taxCalculationStatus !== 'failed'
    ) {
        return data.isDirty;
    }
    return true;
}
