import { extractEdges } from '@sage/xtrem-client';
import type { Supplier } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import type { PurchaseRequisition } from '../pages/purchase-requisition';
import type { ReorderPurchaseOrderPanel } from '../pages/reorder-purchase-order-panel';
import { confirmDialogWithAcceptButtonText } from './common';

type SupplierLookUpData = ui.PartialNodeWithId<Supplier> & {
    purchaseLeadTime: number;
    type: string;
    sortOrder: string;
};

async function getSuppliersFromItemSiteSuppliers(
    graphApi: ui.GraphQLApi<GraphApi>,
    filter: { itemId: string; siteId: string },
): Promise<SupplierLookUpData[]> {
    const defaultLocalized = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__default_supplier',
        'Default',
    );
    const referencedLocalized = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__referenced_supplier',
        'Referenced',
    );
    return extractEdges(
        await graphApi
            .node('@sage/xtrem-master-data/ItemSiteSupplier')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        supplier: {
                            _id: true,
                            id: true,
                            name: true,
                            businessEntity: {
                                id: true,
                                name: true,
                                currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                            },
                            currency: { _id: true },
                            internalNote: { value: true },
                        },
                        isDefaultItemSupplier: true,
                        purchaseLeadTime: true,
                    },
                    {
                        filter: {
                            itemSite: {
                                item: { id: filter.itemId },
                                site: { id: filter.siteId },
                            },
                            supplier: { isActive: true },
                        },
                        first: 200,
                    },
                ),
            )
            .execute(),
    ).map(itemSiteSupplier => ({
        ...itemSiteSupplier.supplier,
        purchaseLeadTime: itemSiteSupplier.purchaseLeadTime || 0,
        type: itemSiteSupplier.isDefaultItemSupplier ? defaultLocalized : referencedLocalized,
        sortOrder: itemSiteSupplier.isDefaultItemSupplier ? '1' : '3',
    }));
}

async function getSuppliersFromItemSuppliers(
    graph: ui.GraphQLApi<GraphApi>,
    filter: { itemId: string; suppliers: SupplierLookUpData[] },
) {
    const defaultLocalized = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__default_supplier',
        'Default',
    );
    const referencedLocalized = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__referenced_supplier',
        'Referenced',
    );
    const otherLocalized = ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__other_supplier', 'Other');
    return extractEdges(
        await graph
            .node('@sage/xtrem-master-data/ItemSupplier')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        supplier: {
                            _id: true,
                            id: true,
                            name: true,
                            internalNote: { value: true },
                            businessEntity: {
                                id: true,
                                name: true,
                                currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                            },
                            currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                        },
                        isDefaultItemSupplier: true,
                        purchaseLeadTime: true,
                        isActive: true,
                    },
                    {
                        filter: {
                            item: { id: filter.itemId },
                            supplier: {
                                isActive: true,
                                _id: { _nin: filter.suppliers.map(supplier => supplier._id) },
                            },
                        },
                        first: 200,
                    },
                ),
            )
            .execute(),
    ).map(itemSupplier => {
        let type = otherLocalized;
        if (itemSupplier.isActive) {
            type = itemSupplier.isDefaultItemSupplier ? defaultLocalized : referencedLocalized;
        }
        return {
            ...itemSupplier.supplier,
            purchaseLeadTime: itemSupplier.purchaseLeadTime || 0,
            type,
            sortOrder: itemSupplier.isDefaultItemSupplier ? '2' : '3',
        };
    });
}

async function getAllActiveSuppliers(graph: ui.GraphQLApi<GraphApi>, suppliers: ui.PartialNodeWithId<Supplier>[]) {
    const otherLocalized = ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__other_supplier', 'Other');
    return extractEdges(
        await graph
            .node('@sage/xtrem-master-data/Supplier')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        id: true,
                        name: true,
                        internalNote: { value: true },
                        businessEntity: {
                            _id: true,
                            id: true,
                            name: true,
                            currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                        },
                        currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                    },
                    {
                        filter: {
                            isActive: true,
                            _id: { _nin: suppliers.map(supplier => supplier._id) },
                        },
                        first: 200,
                    },
                ),
            )
            .execute(),
    ).map(supplier => ({
        ...supplier,
        purchaseLeadTime: 0,
        type: otherLocalized,
        sortOrder: '4',
    }));
}

export async function loadSuppliersLookUpData(
    graph: ui.GraphQLApi<GraphApi>,
    itemId?: string,
    siteId?: string,
): Promise<SupplierLookUpData[]> {
    let suppliers: SupplierLookUpData[] = [];

    if (itemId && siteId) {
        const suppliersFromItemSiteSuppliers = await getSuppliersFromItemSiteSuppliers(graph, { itemId, siteId });
        const suppliersFromItemSuppliers = await getSuppliersFromItemSuppliers(graph, {
            itemId,
            suppliers: suppliersFromItemSiteSuppliers,
        });
        suppliers = [...suppliersFromItemSiteSuppliers, ...suppliersFromItemSuppliers];
    }
    // what happen there if we have lot lot of suppliers ?
    const allActiveSuppliers = await getAllActiveSuppliers(graph, suppliers);
    suppliers = [...suppliers, ...allActiveSuppliers];
    return suppliers;
}

export async function suppliersLookUpOnRowClick(
    page: ReorderPurchaseOrderPanel | PurchaseRequisition,
    sortOrder: string,
    suppliers: ui.PartialNodeWithId<SupplierLookUpData>[],
) {
    if (sortOrder === '4' && suppliers.some(supplier => ['1', '2', '4'].includes(supplier.sortOrder || ''))) {
        await confirmDialogWithAcceptButtonText(
            page,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__approve_supplier_title',
                'Confirm supplier',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__approve_supplier_content',
                'You have selected a supplier that is not referenced for this item.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
        );
    }
}
