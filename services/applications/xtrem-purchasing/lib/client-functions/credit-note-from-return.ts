import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import { getCompanyPriceScale } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import type {
    PurchaseCreditMemoLine,
    PurchaseReturnLine,
    PurchaseReturnLineToPurchaseCreditMemoLine,
} from '@sage/xtrem-purchasing-api';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { PurchaseCreditMemo as PurchaseCreditMemoPage } from '../pages/purchase-credit-memo';
import * as PillColorPurchase from './pill-color';

export function convertReturnLineToReturnToCreditMemoLine(
    data: ui.PartialCollectionValue<PurchaseReturnLine & { selectedQuantity: string } & { selectedGrossPrice: string }>,
): ui.PartialCollectionValue<PurchaseReturnLineToPurchaseCreditMemoLine> {
    return {
        purchaseReturnLine: {
            _id: data._id,
            document: {
                _id: data.document?._id,
                number: data.document?.number,
                isTransferHeaderNote: data.document?.isTransferHeaderNote,
                isTransferLineNote: data.document?.isTransferLineNote,
            },
            status: data.status,
            quantity: data.selectedQuantity,
            item: { _id: data.item?._id, name: data.item?.name },
            grossPrice: data.selectedGrossPrice,
            totalTaxExcludedAmount: data.amountExcludingTax,
            storedAttributes: data.storedAttributes,
            storedDimensions: data.storedDimensions,
        },
        unit: data.unit,
    };
}

function convertReturnLineToCreditMemoLine(
    creditMemoPage: PurchaseCreditMemoPage,
    returnLine: ui.PartialCollectionValue<
        PurchaseReturnLine & { selectedQuantity: number; selectedGrossPrice: number }
    >,
): ui.PartialCollectionValue<PurchaseCreditMemoLine> {
    const purchaseReturnLine = convertReturnLineToReturnToCreditMemoLine({
        ...returnLine,
        selectedQuantity: String(returnLine.selectedQuantity ?? ''),
        selectedGrossPrice: String(returnLine.selectedGrossPrice ?? ''),
    });
    const currency = creditMemoPage.currency.value;
    if (!currency) {
        throw new Error('Currency is required');
    }
    const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
        { ...returnLine, _id: returnLine._id ?? '' },
        creditMemoPage._defaultDimensionsAttributes,
    );

    const creditMemoLineData = { ...returnLine };

    delete creditMemoLineData._id;
    delete creditMemoLineData.status;
    delete creditMemoLineData.supplier;
    delete creditMemoLineData.currency;
    delete creditMemoLineData.document;

    return {
        ...creditMemoLineData,
        status: 'draft',
        origin: 'purchaseReturn',
        quantity: String(returnLine.selectedQuantity),
        unitToStockUnitConversionFactor: returnLine.unitToStockUnitConversionFactor,
        quantityInStockUnit: (
            +(returnLine.selectedQuantity ?? 0) * +(returnLine.unitToStockUnitConversionFactor ?? 0)
        ).toString(),
        currency: creditMemoPage.currency.value ?? undefined,
        purchaseReturnLine,
        amountExcludingTax: returnLine.totalTaxExcludedAmount,
        amountExcludingTaxInCompanyCurrency: returnLine.calculatedTotalAmountExcludingTaxInCompanyCurrency,
        storedAttributes: line.storedAttributes,
        storedDimensions: line.storedDimensions,
        taxDate: returnLine.taxDate,
        uiTaxes: returnLine.uiTaxes,
        recipientSite: returnLine.document?.returnSite,
        uPurchaseUnit: returnLine.unit,
        uStockUnit: returnLine.stockUnit,
        internalNote: { value: '' },
        externalNote: { value: '' },
        isExternalNote: false,
    };
}

function filterForReturnLine(creditMemoPage: PurchaseCreditMemoPage): Filter<PurchaseReturnLine> {
    const creditMemoLinesSysId = creditMemoPage.lines.value.map(line => line._id);
    const creditMemoLines = creditMemoPage.lines.value
        .map(line => line.purchaseReturnLine?.purchaseReturnLine?._id)
        .filter((id): id is string => typeof id === 'string');

    return {
        _id: { _nin: creditMemoLines },
        document: {
            billBySupplier: { _id: { _eq: creditMemoPage.billBySupplier.value?._id } },
            currency: { _id: { _eq: creditMemoPage.currency.value?._id } },
            status: { _in: ['pending', 'inProgress', 'closed'] },
        },
        lineCreditStatus: { _in: ['notCredited', 'partiallyCredited'] },
        purchaseReceiptLine: {
            purchaseReceiptLine: {
                lineInvoiceStatus: { _in: ['partiallyInvoiced', 'invoiced'] },
            },
        },
        purchaseCreditMemoLines: { _atMost: 0, purchaseReturnLine: { _id: { _in: creditMemoLinesSysId } } },
        _or: [
            { document: { returnSite: { _id: { _eq: creditMemoPage.site.value?._id } } } },
            { document: { returnSite: { financialSite: { _id: { _eq: creditMemoPage.site.value?._id } } } } },
        ],
    };
}

// return type of lookuppurchaseReturnLine function
export type CreditMemoLineLookup = Awaited<ReturnType<typeof lookupReturnLine>>[0];

function overReturned(line: ExtractEdgesPartial<PurchaseReturnLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) > +(line.remainingQuantityToCredit ?? 0);
}
function underReturned(line: ExtractEdgesPartial<PurchaseReturnLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) < +(line.remainingQuantityToCredit ?? 0);
}

export function lookupReturnLine(
    creditMemoPage: PurchaseCreditMemoPage,
    selectedLines?: { _id: string; selectedQuantity: number; selectedGrossPrice?: number }[],
) {
    return creditMemoPage.$.dialog.lookup<
        PurchaseReturnLine & { selectedQuantity: number; selectedGrossPrice: number }
    >({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select return lines',
        node: '@sage/xtrem-purchasing/PurchaseReturnLine',
        selectedRecordId: selectedLines?.map(line => line._id),
        filter: filterForReturnLine(creditMemoPage),
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        mapServerRecord(record) {
            const { remainingQuantityToCredit, grossPrice } = record;
            const selectedQuantity = +(remainingQuantityToCredit ?? 0);
            const selectedGrossPrice = +(grossPrice ?? 0);
            return { selectedQuantity, selectedGrossPrice, ...record };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image', size: 'small' }),
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'document',
                title: 'Return number',
                valueField: 'number',
                node: '@sage/xtrem-purchasing/PurchaseReturn',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.reference({
                bind: { purchaseReceiptLine: { purchaseReceiptLine: true } },
                title: 'Receipt number',
                node: '@sage/xtrem-purchasing/purchaseReceiptLine',
                isReadOnly: true,
                valueField: 'documentNumber',
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to credit',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                validation(value) {
                    if (value <= 0)
                        return ui.localize(
                            '@sage/xtrem-purchasing/return_to_credit_memo__error_zero_or_negative_quantity',
                            'You need to enter a quantity greater than zero.',
                        );
                    return undefined;
                },
            }),
            ui.nestedFields.technical({ bind: 'grossPrice' }),
            ui.nestedFields.numeric({
                title: 'Credit memo unit price',
                bind: 'selectedGrossPrice',
                scale: (_rowId, rowData) => getCompanyPriceScale(rowData?.site.legalCompany),
                unit: (_rowId, rowData) => rowData?.currency,
                isTransient: true,
                validation(value) {
                    if (value <= 0) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/return_to_credit_memo__error_zero_or_negative_unit_price',
                            `You need to enter a unit price greater than zero.`,
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.technical({ bind: 'grossPrice' }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({ bind: 'remainingQuantityToCredit' }),
            ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
            ui.nestedFields.technical({ bind: 'totalTaxExcludedAmount' }),
            ui.nestedFields.technical({ bind: 'calculatedTotalAmountExcludingTaxInCompanyCurrency' }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    });
}

function checkOrderlinesMessage(
    lines: ui.PartialCollectionValue<PurchaseReturnLine & { selectedQuantity: number; selectedGrossPrice: number }>[],
) {
    const isUnderReturned = lines.some(underReturned);
    const isOverReturned = lines.some(overReturned);
    if (isUnderReturned) {
        return ui.localize(
            '@sage/xtrem-purchasing/return_to_credit_memo__confirm_lower_quantity',
            'You are about to create a credit memo with a quantity less than the returned quantity.',
        );
    }
    if (isOverReturned) {
        return ui.localize(
            '@sage/xtrem-purchasing/return_to_credit_memo__confirm_greater_quantity',
            'You are about to create a credit memo with a quantity greater than the returned quantity.',
        );
    }
    if (isUnderReturned && isOverReturned) {
        return ui.localize(
            '@sage/xtrem-purchasing/return_to_credit_memo__confirm__lower_greater_quantity',
            'You are about to create one or more order lines with a quantity less or greater than the returned quantity.',
        );
    }
    return null;
}

function confirmOrderQuantity(creditMemoPage: PurchaseCreditMemoPage, message: string) {
    return creditMemoPage.$.dialog
        .confirmation(
            'warn',
            ui.localize(
                '@sage/xtrem-purchasing/return_to_credit_memo__create_purchase_dialog_title',
                'Confirm credit memo quantity',
            ),
            message,
        )
        .then(() => true)
        .catch(() => false);
}

export async function addLineFromReturn(
    creditMemoPage: PurchaseCreditMemoPage,
    selectedLines?: { _id: string; selectedQuantity: number; selectedGrossPrice: number }[],
) {
    const returnLines = (await lookupReturnLine(creditMemoPage, selectedLines)) as unknown as ui.PartialCollectionValue<
        PurchaseReturnLine & { selectedQuantity: number; selectedGrossPrice: number }
    >[];
    if (!returnLines || !isArray(returnLines) || returnLines.length === 0) {
        return;
    }

    const message = checkOrderlinesMessage(returnLines);
    if (message && !(await confirmOrderQuantity(creditMemoPage, message))) {
        await addLineFromReturn(
            creditMemoPage,
            returnLines.map(line => ({
                _id: line._id ?? '',
                selectedQuantity: line.selectedQuantity ?? 0,
                selectedGrossPrice: line.selectedGrossPrice ?? 0,
            })),
        );
        return;
    }

    await asyncArray(returnLines).forEach(async returnLine => {
        const creditMemoLine = convertReturnLineToCreditMemoLine(creditMemoPage, returnLine);

        await creditMemoPage.calculatePrices(creditMemoLine);

        creditMemoPage.lines.addRecord(creditMemoLine);
        creditMemoPage.purchaseReturnLines.addOrUpdateRecordValue(returnLine);
    });

    creditMemoPage._computeTotalAmounts();
    recalculateTaxCalculationStatus(creditMemoPage.lines, creditMemoPage.taxCalculationStatus);
}
