import type { PurchaseDocumentApprovalStatus } from '@sage/xtrem-purchasing-api';
import type * as isDisabledButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-disabled-buttons';
import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isDisabledButtons from './interfaces/is-disabled-buttons-purchase-order';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-purchase-order';

export function isDisabledButtonSaveAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.parameters.isRepost) {
        return true;
    }

    if (data.recordId) {
        if (data.parameters.status !== 'closed') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

export function isDisabledButtonCancelAction(
    data: isDisabledButtonsCommon.SaveOrCancelParameters<isDisabledButtons.StatusParameters>,
) {
    if (data.recordId) {
        if (data.parameters.status !== 'closed') {
            return !data.isDirty;
        }
        return true;
    }
    return !data.isDirty;
}

const approvalStatuses: PurchaseDocumentApprovalStatus[] = ['pendingApproval', 'approved', 'draft', 'confirmed'];

export function isHiddenButtonSaveAction(data: isHiddenButtons.RepostParameters) {
    const isApprovalStatusMatched = approvalStatuses.includes(data.approvalStatus as PurchaseDocumentApprovalStatus);

    if (isApprovalStatusMatched) {
        return !data.isDirty;
    }
    return data.isRepost || false;
}

export function isHiddenButtonCancelAction(data: isHiddenButtons.RepostParameters) {
    const isApprovalStatusMatched = approvalStatuses.includes(data.approvalStatus as PurchaseDocumentApprovalStatus);

    if (isApprovalStatusMatched) {
        return !data.isDirty;
    }
    return data.isRepost || false;
}

export function isHiddenButtonDeleteAction(
    data: isHiddenButtonsCommon.DeleteParameters<isHiddenButtons.DeleteParameters>,
) {
    if (data.parameters.isRepost) {
        return true;
    }

    if (
        (data.recordId &&
            !(
                data.parameters.status === 'inProgress' ||
                data.parameters.status === 'pending' ||
                data.parameters.status === 'closed'
            )) ||
        (data.parameters.approvalStatus === 'confirmed' && data.parameters.status === 'pending')
    ) {
        return false;
    }
    return true;
}

export function isDisabledButtonDeleteAction(
    data: isDisabledButtonsCommon.DeleteParameters<isDisabledButtons.DeleteParameters>,
) {
    return isHiddenButtonDeleteAction(data);
}

/* -------------------*/

export function isHiddenButtonApproveAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ApproveParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.approvalStatus === 'pendingApproval' &&
        ((data.parameters.taxEngine === 'genericTaxCalculation' && data.parameters.taxCalculationStatus === 'done') ||
            (data.parameters.taxCalculationStatus &&
                data.parameters.taxEngine !== 'genericTaxCalculation' &&
                !['inProgress', 'failed'].includes(data.parameters.taxCalculationStatus)))
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonApproveAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ApproveParameters>,
) {
    return isHiddenButtonApproveAction(data);
}

/* -------------------*/

export function isHiddenButtonConfirmAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.ConfirmParameters>,
) {
    if (
        data.recordId &&
        data.parameters.taxCalculationStatus &&
        data.parameters.status !== 'closed' &&
        !data.parameters.isApprovalManaged &&
        data.parameters.approvalStatus &&
        !['pendingApproval', 'approved', 'rejected', 'changeRequested', 'confirmed'].includes(
            data.parameters.approvalStatus,
        ) &&
        ((data.parameters.taxEngine === 'genericTaxCalculation' &&
            ['done', 'failed'].includes(data.parameters.taxCalculationStatus)) ||
            (data.parameters.taxEngine !== 'genericTaxCalculation' &&
                data.parameters.taxCalculationStatus &&
                data.parameters.taxCalculationStatus !== 'inProgress'))
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonConfirmAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.ConfirmParameters>,
) {
    return isHiddenButtonConfirmAction(data);
}

/* -------------------*/

export function isHiddenButtonRepostAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RepostParameters>,
) {
    if (data.recordId && data.parameters.isRepost) {
        return !data.isDirty;
    }
    return true;
}

export function isDisabledButtonRepostAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RepostParameters>,
) {
    return isHiddenButtonRepostAction(data);
}

/* -------------------*/

export function isHiddenButtonRejectAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RejectParameters>,
) {
    if (data.recordId && data.parameters.status !== 'closed' && data.parameters.approvalStatus === 'pendingApproval') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRejectAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RejectParameters>,
) {
    return isHiddenButtonRejectAction(data);
}

/* -------------------*/

export function isHiddenButtonRequestApprovalAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RequestApprovalParameters>,
) {
    if (
        data.recordId &&
        data.parameters.taxCalculationStatus &&
        data.parameters.status !== 'closed' &&
        data.parameters.isApprovalManaged &&
        data.parameters.approvalStatus &&
        !['pendingApproval', 'approved', 'rejected', 'confirmed'].includes(data.parameters.approvalStatus) &&
        ((data.parameters.taxEngine === 'genericTaxCalculation' &&
            ['done', 'failed'].includes(data.parameters.taxCalculationStatus)) ||
            (data.parameters.taxEngine !== 'genericTaxCalculation' &&
                data.parameters.taxCalculationStatus &&
                data.parameters.taxCalculationStatus !== 'inProgress'))
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRequestApprovalAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RequestApprovalParameters>,
) {
    return isHiddenButtonRequestApprovalAction(data);
}

/* -------------------*/

export function isHiddenButtonRequestChangesAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.RequestChangesParameters>,
) {
    if (data.recordId && data.parameters.status !== 'close' && data.parameters.approvalStatus === 'changeRequested') {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonRequestChangesAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.RequestChangesParameters>,
) {
    return isHiddenButtonRequestChangesAction(data);
}

/* -------------------*/

export function isHiddenButtonCreatePurchaseReceiptAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CreatePurchaseReceiptParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.status !== 'draft' &&
        data.parameters.approvalStatus &&
        ['approved', 'confirmed'].includes(data.parameters.approvalStatus) &&
        data.parameters.totalQuantityToReceiveInStockUnit &&
        data.parameters.totalQuantityToReceiveInStockUnit !== 0 &&
        !data.parameters.isRepost
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCreatePurchaseReceiptAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CreatePurchaseReceiptParameters>,
) {
    return isHiddenButtonCreatePurchaseReceiptAction(data);
}

/* -------------------*/

export function isHiddenButtonCloseOrderAction(
    data: isHiddenButtonsCommon.BusinessButtonParameters<isHiddenButtons.CloseParameters>,
) {
    if (
        data.recordId &&
        data.parameters.status !== 'closed' &&
        data.parameters.status !== 'draft' &&
        data.parameters.approvalStatus &&
        ['approved', 'confirmed'].includes(data.parameters.approvalStatus) &&
        data.parameters.totalQuantityToReceiveInStockUnit !== 0 &&
        !data.parameters.isRepost
    ) {
        return data.isDirty;
    }
    return true;
}

export function isDisabledButtonCloseOrderAction(
    data: isDisabledButtonsCommon.BusinessButtonParameters<isDisabledButtons.CloseParameters>,
) {
    return isHiddenButtonCloseOrderAction(data);
}

/* -------------------*/

export function isHiddenButtonPrintAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.PrintParameters>,
) {
    return !data.recordId || data.parameters.status === 'rejected';
}

export function isDisabledButtonPrintAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.StatusParameters>,
) {
    return !data.recordId || data.isDirty || (data.parameters && data.parameters.status === 'rejected');
}

/* -------------------*/

export function isHiddenButtonSendMailAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.SendMailParameters>,
) {
    if (
        data.recordId &&
        data.parameters &&
        data.parameters.displayStatus &&
        !['approved', 'confirmed'].includes(data.parameters.displayStatus)
    ) {
        return true;
    }
    return false;
}

export function isDisabledButtonSendMailAction(
    data: isDisabledButtonsCommon.HeaderQuickActionButtonParameters<isDisabledButtons.SendMailParameters>,
) {
    return !data.recordId || data.isDirty;
}

/* -------------------*/

export function isHiddenButtonDefaultDimensionAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.DefaultDimensionParameters>,
) {
    return !(
        data.parameters &&
        data.parameters.status &&
        (data.parameters.status === 'draft' ||
            data.parameters.displayStatus === 'taxCalculationFailed' ||
            data.parameters.approvalStatus === 'pendingApproval')
    );
}

export function isDisabledButtonDefaultDimensionAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.DefaultDimensionParameters>,
) {
    if (data.parameters && data.parameters.site && data.parameters.supplier) {
        return false;
    }
    return true;
}

/* -------------------*/

function commonIsHiddenProperties(
    data:
        | isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromPurchaseRequisitionLinesParameters>
        | isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    return (
        (data.parameters &&
            data.parameters.status &&
            !['closed', 'pending', 'inProgress'].includes(data.parameters.status) &&
            data.parameters.site &&
            data.parameters.supplier &&
            data.parameters.currency) ||
        (data.parameters &&
            data.parameters.status &&
            !['closed', 'inProgress'].includes(data.parameters.status) &&
            data.parameters.site &&
            data.parameters.supplier &&
            data.parameters.currency &&
            data.parameters.approvalStatus &&
            data.parameters.approvalStatus === 'confirmed')
    );
}

export function isDisabledLinePhantomRow(
    data: isDisabledButtonsCommon.PhantomRowButtonParameters<isDisabledButtons.LinePhantomRowParameters>,
) {
    if (commonIsHiddenProperties(data)) {
        return false;
    }
    return true;
}

/* -------------------*/

export function isHiddenButtonSelectFromPurchaseRequisitionLinesAction(
    data: isHiddenButtonsCommon.HeaderDropDownActionButtonParameters<isHiddenButtons.SelectFromPurchaseRequisitionLinesParameters>,
) {
    if (commonIsHiddenProperties(data)) {
        return false;
    }
    return true;
}

export function isDisabledButtonSelectFromPurchaseRequisitionLinesAction(
    data: isDisabledButtonsCommon.HeaderDropDownActionButtonParameters<isDisabledButtons.SelectFromPurchaseRequisitionLinesParameters>,
) {
    return isHiddenButtonSelectFromPurchaseRequisitionLinesAction(data);
}
