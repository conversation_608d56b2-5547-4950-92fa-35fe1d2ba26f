import type { ExtractEdgesPartial, decimal } from '@sage/xtrem-client';
import type { Currency, Item, Supplier, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as masterDataCommon from '@sage/xtrem-master-data/build/lib/client-functions/common';
import type {
    PurchaseOrderLineBinding,
    PurchaseReceiptLineBinding,
    GraphApi as PurchasingApi,
} from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import { PurchaseOrder as PurchaseOrderPage } from '../pages/purchase-order';
import type { PurchaseRequisition as PurchaseRequisitionPage } from '../pages/purchase-requisition';
import type { PurchaseReceipt as PurchaseReceiptPage } from '../pages/purchase-receipt';
import type { PurchasePrice } from './interfaces';
import { calculateLinePrices } from './shared/page-functions';

type BasePurchaseDocumentLine = PurchaseOrderLineBinding | PurchaseReceiptLineBinding;

type PurchasePages = PurchaseOrderPage | PurchaseReceiptPage;

function getDate(page: PurchasePages) {
    if (page instanceof PurchaseOrderPage) {
        return page.orderDate.value ?? '';
    }
    return page.date.value ?? '';
}

function getLineNodeName(page: PurchasePages) {
    if (page instanceof PurchaseOrderPage) {
        return '@sage/xtrem-purchasing/PurchaseOrderLine';
    }
    return '@sage/xtrem-purchasing/PurchaseReceiptLine';
}

export async function getItemSupplierPrice(
    graphApi: ui.GraphQLApi<PurchasingApi>,
    priceParameters: {
        site: ExtractEdgesPartial<Site>;
        supplier: ExtractEdgesPartial<Supplier>;
        currency: ExtractEdgesPartial<Currency>;
        item: ExtractEdgesPartial<Item>;
        unit: ExtractEdgesPartial<UnitOfMeasure>;
        quantity: decimal;
        date: Date;
        doConvertUnit: boolean;
    },
): Promise<PurchasePrice> {
    if (!priceParameters.quantity) {
        return { priceOrigin: 'manual', price: 0 };
    }
    if (priceParameters.currency && priceParameters.item && priceParameters.unit) {
        const price =
            +(await graphApi
                .node('@sage/xtrem-master-data/ItemSupplierPrice')
                .queries.getPurchasePrice(false, {
                    priceParameters: {
                        ...(priceParameters.site?._id ? { site: priceParameters.site._id } : {}),
                        ...(priceParameters.supplier?._id ? { supplier: priceParameters.supplier._id } : {}),
                        currency: priceParameters.currency._id ?? '',
                        item: priceParameters.item._id ?? '',
                        quantity: priceParameters.quantity,
                        unit: priceParameters.unit._id ?? '',
                        date: priceParameters.date.toISOString().substring(0, 10),
                        convertUnit: priceParameters.doConvertUnit,
                    },
                })
                .execute()) || 0;
        if (price >= 0) {
            return {
                priceOrigin: 'supplierPriceList',
                price,
            };
        }
    }
    return { priceOrigin: 'manual', price: 0 };
}

export async function calculatePrices(
    page: PurchasePages,
    rowData: ui.PartialCollectionValue<BasePurchaseDocumentLine>,
): Promise<ui.PartialCollectionValue<BasePurchaseDocumentLine>> {
    const prices = await calculateLinePrices({
        grossPrice: Number(rowData.grossPrice ?? 0),
        charge: Number(rowData.charge ?? 0),
        discount: Number(rowData.discount ?? 0),
        netPriceScale: masterDataCommon.getCompanyPriceScale(page.site.value?.legalCompany),
        quantity: Number(rowData.quantity ?? 0),
        amountExcludingTax: Number(rowData.amountExcludingTax ?? 0),
        amountIncludingTax: Number(rowData.amountIncludingTax ?? 0),
        taxAmount: Number(rowData.taxAmount ?? 0),
        taxAmountAdjusted: Number(rowData.taxAmountAdjusted ?? 0),
        rateMultiplication: Number(page.companyFxRate.value ?? 0),
        rateDivision: Number(page.companyFxRateDivisor.value ?? 0),
        fromDecimals: Number(page.currency.value?.decimalDigits ?? 0),
        toDecimals: Number(page.siteCurrency.decimalDigits ?? 0),
        taxes: {
            graphObject: page.$.graph,
            site: page.site.value?._id ?? '',
            businessPartner: page.businessRelation.value?._id ?? '',
            item: rowData.item?._id ?? '',
            currency: page.currency.value?._id ?? '',
            lineNodeName: getLineNodeName(page),
            taxEngine: page.taxEngine.value ?? '',
            uiTaxes: rowData.uiTaxes,
            taxDate: getDate(page),
        },
    });

    return {
        ...rowData,
        netPrice: prices.netPrice.toString(),
        taxAmount: prices.taxAmount.toString(),
        taxAmountAdjusted: prices.taxAmountAdjusted.toString(),
        amountExcludingTax: prices.amountExcludingTax.toString(),
        amountIncludingTax: prices.amountIncludingTax.toString(),
        amountExcludingTaxInCompanyCurrency: prices.amountExcludingTaxInCompanyCurrency.toString(),
        amountIncludingTaxInCompanyCurrency: prices.amountIncludingTaxInCompanyCurrency.toString(),
        taxCalculationStatus: prices.taxCalculationStatus,
        uiTaxes: prices.uiTaxes,
        taxDate: getDate(page),
    };
}

export function recalculatePricesDialog(page: PurchasePages | PurchaseRequisitionPage) {
    return page.$.dialog.confirmation(
        'info',
        ui.localize('@sage/xtrem-purchasing/pages__purchasing__on_recalculate_price_title', 'Update price'),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchasing__recalculate_price',
            'You are about to recalculate prices, discounts and charges to reflect the latest price list.',
        ),
        {
            cancelButton: { isHidden: false },
            acceptButton: { isHidden: false },
        },
    );
}
export function updatePriceDialog(page: PurchasePages | PurchaseRequisitionPage) {
    return page.$.dialog.confirmation(
        'info',
        ui.localize('@sage/xtrem-purchasing/pages__purchasing_order__on_price_determination_title', 'Update price'),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchasing_order__update_price_from_quantity',
            'The purchase quantity was updated. Do you want to recalculate prices, discounts, and charges?',
        ),
        {
            cancelButton: { isHidden: false },
            acceptButton: {
                text: ui.localize('@sage/xtrem-purchasing/pages__purchase_order__recalculate', 'Recalculate'),
            },
        },
    );
}
