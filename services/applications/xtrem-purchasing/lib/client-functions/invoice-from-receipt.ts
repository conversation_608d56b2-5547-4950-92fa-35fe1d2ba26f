import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';

import type { Address } from '@sage/xtrem-master-data-api';
import { getCompanyPriceScale } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import * as Pill<PERSON>olor<PERSON>ommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import type {
    PurchaseInvoiceLine,
    PurchaseOrderLineToPurchaseInvoiceLine,
    PurchaseReceiptLine,
    PurchaseReceiptLineToPurchaseInvoiceLine,
} from '@sage/xtrem-purchasing-api';
import type * as xtremTax from '@sage/xtrem-tax';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { PurchaseInvoice as PurchaseInvoicePage } from '../pages/purchase-invoice';
import { getPurchasePrice } from './common';
import type { ChangeableValues } from './interfaces/common';
import { setBillDetails } from './purchase-invoice-actions-functions';
import { calculateLinePrices } from './shared/page-functions';

function filterForReceiptLine(invoicePage: PurchaseInvoicePage): Filter<PurchaseReceiptLine> {
    const invoiceLinesSysId = invoicePage.lines.value.map(line => line._id);

    const receiptLines = invoicePage.lines.value
        .filter(invoiceLine => invoiceLine.purchaseReceiptLine)
        .map(invoiceLine => invoiceLine.purchaseReceiptLine?.purchaseReceiptLine?._id ?? '');

    return {
        _id: { _nin: receiptLines },
        document: {
            ...(invoicePage.billBySupplier.value?._id
                ? { supplier: { _id: { _eq: invoicePage.billBySupplier.value?._id } } }
                : {}),
            ...(invoicePage.currency.value?._id ? { currency: { _id: { _eq: invoicePage.currency.value?._id } } } : {}),

            status: { _in: ['pending', 'inProgress', 'closed'] },
            invoiceStatus: { _in: ['notInvoiced', 'partiallyInvoiced'] },
        },
        lineInvoiceStatus: { _in: ['notInvoiced', 'partiallyInvoiced'] },
        purchaseInvoiceLines: { _atMost: 0, purchaseInvoiceLine: { _id: { _in: invoiceLinesSysId } } },

        _or: [
            { document: { site: { _id: { _eq: invoicePage.site.value?._id } } } },
            { document: { site: { financialSite: { _id: { _eq: invoicePage.site.value?._id } } } } },
        ],
    };
}

export type ReceiptLineLookup = Awaited<ReturnType<typeof lookUpReceiptLine>>[0];

export function lookUpReceiptLine(
    invoicePage: PurchaseInvoicePage,
    selectedLines?: { _id: string; selectedQuantity: number; selectedGrossPrice: number }[],
) {
    return invoicePage.$.dialog.lookup<PurchaseReceiptLine & ChangeableValues>({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select receipt lines',
        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
        filter: filterForReceiptLine(invoicePage),
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        selectedRecordId: selectedLines?.map(line => line._id),
        mapServerRecord(record) {
            const { remainingQuantityToInvoice, grossPrice } = record;
            const selectedQuantity = Number(remainingQuantityToInvoice ?? 0);
            const selectedGrossPrice = Number(grossPrice ?? 0);
            return { selectedQuantity, selectedGrossPrice, ...record };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, size: 'small' }),
            ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                valueField: 'name',
            }),
            ui.nestedFields.text({
                bind: { item: { id: true } },
                title: 'Item Id',
                isReadOnly: true,
            }),
            ui.nestedFields.reference({
                bind: 'document',
                valueField: 'number',
                title: 'Receipt number',
                node: '@sage/xtrem-purchasing/PurchaseReceipt',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: { businessEntity: { name: true } } }),
                            ui.nestedFields.technical({ bind: { businessEntity: { id: true } } }),
                            ui.nestedFields.technical({ bind: { businessEntity: { taxIdNumber: true } } }),
                            ui.nestedFields.technical({ bind: { businessEntity: { country: { name: true } } } }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'paymentTerm',
                        node: '@sage/xtrem-master-data/PaymentTerm',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'description' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'billBySupplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
                    }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: { document: { displayStatus: true } },
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.document?.displayStatus),
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to invoice',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                validation(value) {
                    if (value <= 0) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/pages__invoice_from_receipt__error_zero_or_negative_quantity',
                            `You need to enter a quantity greater than zero.`,
                        );
                    }
                    return undefined;
                },
            }),

            ui.nestedFields.technical({ bind: 'remainingQuantityToInvoice' }),
            ui.nestedFields.numeric({
                title: 'Invoice unit price',
                bind: 'selectedGrossPrice',
                scale: (_rowId, rowData) => getCompanyPriceScale(rowData?.site.legalCompany),
                unit: (_rowId, rowData) => rowData?.currency,
                isTransient: true,
                validation(value) {
                    if (value <= 0) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/pages__invoice_from_receipt__error_zero_or_negative_price',
                            `You need to enter a unit price greater than zero.`,
                        );
                    }
                    return undefined;
                },
            }),

            ui.nestedFields.technical({ bind: 'grossPrice' }),

            ui.nestedFields.reference({
                bind: { purchaseOrderLine: { purchaseOrderLine: { document: true } } },
                title: 'PO Number',
                node: '@sage/xtrem-purchasing/PurchaseOrder',
                isReadOnly: true,
                valueField: 'number',
            }),
            ui.nestedFields.text({
                title: 'Receipt packing slip',
                bind: { document: { supplierDocumentNumber: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.technical({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: { site: { legalCompany: { priceScale: true } } } }),
            ui.nestedFields.technical({ bind: 'priceOrigin' }),
            ui.nestedFields.technical({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [ui.nestedFields.technical({ bind: 'priceScale' })],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'charge' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
            ui.nestedFields.technical({ bind: 'amountIncludingTax' }),
            ui.nestedFields.technical({ bind: 'taxAmount' }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({ bind: { purchaseOrderLine: { purchaseOrderLine: { _id: true } } } }),
        ],
    });
}

function overOrdered(line: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>) {
    return +(line.selectedQuantity ?? 0) > +(line.remainingQuantityToInvoice ?? 0);
}
function underOrdered(line: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>) {
    return +(line.selectedQuantity ?? 0) < +(line.remainingQuantityToInvoice ?? 0);
}

function checkReceiptLinesMessage(lines: ui.PartialCollectionValue<PurchaseReceiptLine & ChangeableValues>[]) {
    const isUnderOrdered = lines.some(underOrdered);
    const isOverOrdered = lines.some(overOrdered);
    if (isOverOrdered && isUnderOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__receipt__to_invoice_confirm__lower_greater_quantity',
            'You are about to create one or more invoice lines with a quantity less or greater than the receipt quantity.',
        );
    }

    if (isUnderOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__receipt__to_invoice_confirm_lower_quantity',
            'You are about to create one or more invoice lines with a quantity less than the receipt quantity.',
        );
    }

    if (isOverOrdered) {
        return ui.localize(
            '@sage/xtrem-purchasing/pages__receipt__to_invoice__confirm_greater_quantity',
            'You are about to create one or more invoice lines with a quantity greater than the receipt quantity.',
        );
    }
    return null;
}

async function confirmInvoiceQuantity(invoicePage: PurchaseInvoicePage, message: string) {
    try {
        await invoicePage.$.dialog.confirmation(
            'warn',
            ui.localize(
                '@sage/xtrem-purchasing/pages__receipt__to_invoice__create_purchase_dialog_title',
                'Confirm invoice quantity',
            ),
            message,
        );
        return true;
    } catch {
        return false;
    }
}

async function setPriceOrigin(
    invoicePage: PurchaseInvoicePage,
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>,
) {
    const { price, priceOrigin } = await getPurchasePrice(
        invoicePage.$.graph,
        invoicePage.site.value?._id ?? '',
        invoicePage.billBySupplier.value?._id ?? '',
        receiptLine?.currency?._id ?? '',
        receiptLine.item?._id ?? '',
        receiptLine.unit?._id ?? '',
        receiptLine.selectedQuantity ?? 0,
        new Date(invoicePage.invoiceDate.value ?? ''),
        false,
    );

    return {
        ...receiptLine,
        grossPrice: String(price),
        priceOrigin: priceOrigin ?? undefined,
    };
}

async function updataLinesIfChanged(
    invoicePage: PurchaseInvoicePage,
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>,
) {
    if (receiptLine.selectedGrossPrice !== Number(receiptLine.grossPrice)) {
        return { ...receiptLine, priceOrigin: receiptLine.selectedGrossPrice ? 'manual' : null };
    }

    if (receiptLine.selectedQuantity !== Number(receiptLine.remainingQuantityToInvoice)) {
        await setPriceOrigin(invoicePage, receiptLine);
    }

    return receiptLine;
}

async function updateHeaderDocument(
    invoicePage: PurchaseInvoicePage,
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>,
): Promise<void> {
    invoicePage.fxRateDate.value = invoicePage.supplierDocumentDate.value;
    await invoicePage.$.fetchDefaults(['companyFxRate', 'rateDescription']);

    if (!invoicePage.billBySupplier.value) {
        const { document } = receiptLine;
        if (document) {
            await setBillDetails(invoicePage, {
                supplier: document.supplier,
                currency: document.currency,
                paymentTerm: document.paymentTerm,
            });
        }
    }
}

function convertReceiptLineToPurchaseReceiptLine(
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>,
): ui.PartialCollectionValue<PurchaseReceiptLineToPurchaseInvoiceLine> {
    return {
        purchaseReceiptLine: { _id: receiptLine._id },
    };
}

function convertReceiptLineToPurchaseOrderLine(
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>,
): ui.PartialCollectionValue<PurchaseOrderLineToPurchaseInvoiceLine> {
    return {
        purchaseOrderLine: { _id: receiptLine.purchaseOrderLine?.purchaseOrderLine?._id ?? '' },
    };
}

function convertReceiptLineToInvoiceLine(
    invoicePage: PurchaseInvoicePage,
    receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>,
): ui.PartialCollectionValue<PurchaseInvoiceLine> {
    const purchaseReceiptLine = convertReceiptLineToPurchaseReceiptLine(receiptLine);
    const purchaseOrderLine = convertReceiptLineToPurchaseOrderLine(receiptLine);

    delete receiptLine._id;
    delete receiptLine.document;

    return {
        ...receiptLine,
        status: 'draft',
        origin: 'purchaseReceipt',
        quantity: (receiptLine.selectedQuantity ?? 0).toString(),
        grossPrice: (receiptLine.selectedGrossPrice ?? 0).toString(),
        unitToStockUnitConversionFactor: receiptLine.unitToStockUnitConversionFactor,
        quantityInStockUnit: (
            parseFloat(String(receiptLine.selectedQuantity)) *
            parseFloat(receiptLine.unitToStockUnitConversionFactor as string)
        ).toString(),
        currency: invoicePage.currency.value ?? undefined,
        purchaseReceiptLine,
        purchaseOrderLine,
        recipientSite: receiptLine.site,
        site: invoicePage.site.value ?? undefined,
        amountExcludingTax: receiptLine.amountExcludingTax,
        amountExcludingTaxInCompanyCurrency: receiptLine.amountExcludingTaxInCompanyCurrency,
        internalNote: { value: '' },
        taxDate: receiptLine.taxDate,
        taxAmount: receiptLine.taxAmount,
        taxAmountAdjusted: receiptLine.taxAmountAdjusted,
    };
}

function updateLines(invoicePage: PurchaseInvoicePage, invoiceLine: ExtractEdgesPartial<PurchaseInvoiceLine>) {
    invoicePage.purchaseReceiptLineToPurchaseInvoiceLines.addOrUpdateRecordValue(
        invoiceLine.purchaseReceiptLine as ExtractEdgesPartial<PurchaseReceiptLineToPurchaseInvoiceLine>,
    );

    if (invoiceLine.purchaseOrderLine) {
        invoicePage.purchaseOrderLine.addOrUpdateRecordValue(
            invoiceLine.purchaseOrderLine as ExtractEdgesPartial<PurchaseOrderLineToPurchaseInvoiceLine>,
        );
    }

    invoicePage.lines.addRecord(invoiceLine);
}

async function setCalculatePrices(
    invoicePage: PurchaseInvoicePage,
    invoiceLine: ExtractEdgesPartial<PurchaseInvoiceLine>,
): Promise<ExtractEdgesPartial<PurchaseInvoiceLine>> {
    if (invoiceLine.recipientSite?._id) {
        const prices = await calculateLinePrices({
            grossPrice: Number(invoiceLine.grossPrice),
            charge: Number(invoiceLine.charge),
            discount: Number(invoiceLine.discount),
            netPriceScale: getCompanyPriceScale(invoicePage.site.value?.legalCompany),
            quantity: Number(invoiceLine.quantity),
            amountExcludingTax: Number(invoiceLine.amountExcludingTax),
            amountIncludingTax: Number(invoiceLine.amountIncludingTax),
            taxAmount: Number(invoiceLine.taxAmount),
            taxAmountAdjusted: Number(invoiceLine.taxAmountAdjusted),
            rateMultiplication: invoicePage.companyFxRate.value ?? 0,
            rateDivision: invoicePage.companyFxRateDivisor.value ?? 1,
            fromDecimals: invoicePage.currency.value?.decimalDigits ?? 2,
            toDecimals: invoicePage.siteCurrency.decimalDigits,
            taxes: {
                site: invoiceLine.recipientSite?._id,
                businessPartner: invoicePage.billBySupplier.value?._id,
                item: invoiceLine.item?._id,
                currency: invoicePage.currency.value?._id,
                lineNodeName: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                taxEngine: invoicePage.taxEngine.value ?? '',
                uiTaxes: invoiceLine.uiTaxes ?? '',
                graphObject: invoicePage.$.graph,
                taxDate: invoiceLine.taxDate ?? invoicePage.supplierDocumentDate.value ?? '',
            },
        });
        invoiceLine.netPrice = String(prices.netPrice);
        invoiceLine.taxAmount = String(prices?.taxAmount);
        invoiceLine.taxAmountAdjusted = String(prices.taxAmountAdjusted);
        invoiceLine.uiTaxes = prices.uiTaxes;
        invoiceLine.taxDate = invoiceLine.taxDate ?? invoicePage.invoiceDate.value ?? undefined;
        invoiceLine.amountExcludingTax = String(prices.amountExcludingTax);
        invoiceLine.amountIncludingTax = String(prices.amountIncludingTax);
        invoiceLine.amountExcludingTaxInCompanyCurrency = String(prices.amountExcludingTaxInCompanyCurrency);
        invoiceLine.amountIncludingTaxInCompanyCurrency = String(prices.amountIncludingTaxInCompanyCurrency);
    }
    return invoiceLine;
}

function resetAllUiTaxes(receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>) {
    if (receiptLine.uiTaxes) {
        const uiTaxes = JSON.parse(receiptLine.uiTaxes) as xtremTax.interfaces.UiTaxes;
        uiTaxes.taxes.forEach(tax => {
            tax.taxAmount = 0;
            tax.taxAmountAdjusted = 0;
            tax.taxableAmount = 0;
            delete tax._id;
        });
        receiptLine.uiTaxes = JSON.stringify(uiTaxes);
    }
    return receiptLine;
}

function matchingAddressLine(
    firstReceiptLineAddress: ExtractEdgesPartial<Address>,
    receiptLineAddress: ExtractEdgesPartial<Address>,
): boolean {
    return firstReceiptLineAddress && receiptLineAddress && firstReceiptLineAddress._id === receiptLineAddress._id;
}

export async function addLineFromReceipt(
    invoicePage: PurchaseInvoicePage,
    selectedLines?: { _id: string; selectedQuantity: number; selectedGrossPrice: number }[],
): Promise<void> {
    const receiptLines = (await lookUpReceiptLine(invoicePage, selectedLines)) as unknown as ui.PartialCollectionValue<
        PurchaseReceiptLine & ChangeableValues
    >[];
    if (!receiptLines || !isArray(receiptLines) || receiptLines.length === 0) {
        return;
    }

    const message = checkReceiptLinesMessage(receiptLines);
    if (message && !(await confirmInvoiceQuantity(invoicePage, message))) {
        await addLineFromReceipt(
            invoicePage,
            receiptLines.map(line => ({
                _id: line._id ?? '',
                selectedQuantity: line.selectedQuantity ?? 0,
                selectedGrossPrice: line.selectedGrossPrice ?? 0,
            })),
        );
        return;
    }

    let differentAddresses = false;

    const firstReceiptLineAddress =
        invoicePage.lines.value.length === 0
            ? (receiptLines[0].document?.billBySupplier ?? null)
            : (invoicePage.billBySupplier?.value ?? null);

    await asyncArray(receiptLines).forEach(
        async (receiptLine: ExtractEdgesPartial<PurchaseReceiptLine & ChangeableValues>) => {
            await updataLinesIfChanged(invoicePage, receiptLine);
            resetAllUiTaxes(receiptLine);

            if (invoicePage.lines.value.length === 0) {
                await updateHeaderDocument(invoicePage, receiptLine);
            }

            const billBySupplier = receiptLine.document?.billBySupplier ?? null;

            const isMatchingLine =
                firstReceiptLineAddress && billBySupplier
                    ? matchingAddressLine(firstReceiptLineAddress, billBySupplier)
                    : true;

            const invoiceLine = await setCalculatePrices(
                invoicePage,
                convertReceiptLineToInvoiceLine(invoicePage, receiptLine),
            );

            if (isMatchingLine) {
                updateLines(invoicePage, invoiceLine);
            } else {
                differentAddresses = true;
            }
        },
    );

    await TotalTaxCalculator.getInstance().updateTaxDetails(
        invoicePage.taxes,
        invoicePage.totalTaxAmountAdjusted,
        invoicePage.calculatedTotalTaxAmount,
    );
    invoicePage._computeTotalAmounts();
    recalculateTaxCalculationStatus(invoicePage.lines, invoicePage.taxCalculationStatus);

    if (differentAddresses) {
        invoicePage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__invoice_to_receipt__warning_different_information',
                'Some lines were not added as they have different bill by supplier details.',
            ),
            { timeout: 3000, type: 'warning' },
        );
    }
}
