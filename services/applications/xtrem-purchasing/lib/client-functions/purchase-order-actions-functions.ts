import { asyncArray } from '@sage/xtrem-async-helper';
import * as authorizationFilters from '@sage/xtrem-authorization/build/lib/client-functions/filters';
import { withoutEdges } from '@sage/xtrem-client';
import type { GraphApi, PurchaseReceipt } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isPurchaseOrderLineActionDisabled } from '../shared-functions/edit-rules';
import type * as userInterfaces from '../shared-functions/interfaces';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    ApproveActionParameters,
    ApproveOrRejectActionParameters,
    CloseActionParameters,
    ConfirmActionParameters,
    CreatePurchaseReceiptActionParameters,
    LoadApproversParameters,
    RejectActionParameters,
    RequestApprovalParameters,
    SetDimensionActionParameter,
    SiteApprover,
    UserApprover,
} from './interfaces/purchase-order-actions-functions';
import { getLinesForDimensions } from './manage-dimensions';

const continueText = ui.localize('@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-continue', 'Continue');
const cancelText = ui.localize('@sage/xtrem-purchasing/pages-purchase-order-dialog-assignment-cancel', 'Cancel');

async function openAssignmentDialog(page: ui.Page<GraphApi>, dialogTitle: string, dialogText: string) {
    if (
        await page.$.dialog
            .confirmation('warn', dialogTitle, dialogText, {
                acceptButton: { text: continueText },
                cancelButton: { text: cancelText },
            })
            .then(() => true)
            .catch(() => false)
    ) {
        return true;
    }
    return false;
}

export function isPurchaseOrderAssignmentCheckDelete(purchaseOrderPage: ui.Page, orderAssignmentLinked: boolean) {
    if (orderAssignmentLinked) {
        return openAssignmentDialog(
            purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_title',
                'Delete purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_deletion_dialog_content',
                'When you delete this purchase order, the links to the original orders are also deleted.',
            ),
        );
    }
    return true;
}

export function isPurchaseOrderAssignmentCheckClose(purchaseOrderPage: ui.Page, orderAssignmentLinked: boolean) {
    if (orderAssignmentLinked) {
        return openAssignmentDialog(
            purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_title',
                'Close purchase order',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages-purchase-order_confirm_assignment_closing_dialog_content',
                'When you close this purchase order, the links to the original orders remain.',
            ),
        );
    }
    return true;
}

export async function confirmAction(parameters: ConfirmActionParameters) {
    if (parameters.taxCalculationStatus === 'failed') {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order_confirm__tax_calculation_failed',
                'You need to resolve tax calculation issues before confirming.',
            ),
        );
    }
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_title', 'Confirm purchase order'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__confirm_dialog_content',
                'You are about to set this purchase order to "Confirmed"',
            ),
            ui.localize('@sage/xtrem-purchasing/pages_confirm_button', 'Confirm'),
        ))
    ) {
        parameters.purchaseOrderPage.$.loader.isHidden = false;
        const isConfirm = await parameters.purchaseOrderPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .mutations.confirm(true, {
                document: parameters.purchaseOrderPage.$.recordId || '',
                isConfirmed: parameters.isConfirmed,
            })
            .execute();

        if (isConfirm) {
            parameters.purchaseOrderPage.$.showToast(
                ui.localize('@sage/xtrem-purchasing/pages__purchase_order__confirm_status_updated', 'Status updated.'),
                { type: 'success' },
            );
            if (parameters.isCalledFromRecordPage && parameters.recordId) {
                parameters.purchaseOrderPage.$.setPageClean();
                parameters.purchaseOrderPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                    _id: parameters.recordId,
                });
            }
        }

        parameters.purchaseOrderPage.$.loader.isHidden = true;
    }
}

async function createPurchaseReceipt(
    parameters: CreatePurchaseReceiptActionParameters,
): Promise<{ message: string; type: 'success' | 'error' }> {
    const receipts = (await parameters.purchaseOrderPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseOrder')
        .mutations.createPurchaseReceipt({ number: true }, { document: parameters.recordId })
        .execute()) as PurchaseReceipt[];

    if (receipts?.length > 0) {
        return {
            message: ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__receipt_created',
                'Receipt created: {{receiptNumbers}}.',
                { receiptNumbers: receipts.map(receipt => receipt.number).join('\n') },
            ),
            type: 'success',
        };
    }
    return {
        message: ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order__receipt_not_created',
            'Could not create receipt.',
        ),
        type: 'error',
    };
}

export async function createPurchaseReceiptAction(parameters: CreatePurchaseReceiptActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_title',
                'Confirm receipt creation',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__create_receipt_dialog_content',
                'You are about to create a receipt from this purchase order.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
        ))
    ) {
        parameters.purchaseOrderPage.$.loader.isHidden = false;

        const { message, type } = await createPurchaseReceipt(parameters);
        parameters.purchaseOrderPage.$.showToast(message, { type });

        if (parameters.isCalledFromRecordPage && parameters.recordId) {
            parameters.purchaseOrderPage.$.setPageClean();
            parameters.purchaseOrderPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                _id: parameters.recordId,
            });
        }

        parameters.purchaseOrderPage.$.loader.isHidden = true;
    }
}

async function approveOrReject(parameters: ApproveOrRejectActionParameters) {
    parameters.purchaseOrderPage.$.loader.isHidden = false;

    await parameters.purchaseOrderPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseOrder')
        .mutations.approve(true, {
            document: parameters.purchaseOrderPage.$.recordId ?? parameters.recordId,
            isApproved: parameters.isApproved,
        })
        .execute();

    parameters.purchaseOrderPage.$.showToast(
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_order__approval_status_updated',
            'Approval status updated.',
        ),
        { type: 'success' },
    );
    if (parameters.isCalledFromRecordPage && parameters.recordId) {
        parameters.purchaseOrderPage.$.setPageClean();
        await parameters.purchaseOrderPage.$.router.refresh(true);
        await parameters.purchaseOrderPage.$.refreshNavigationPanel();
    }

    parameters.purchaseOrderPage.$.loader.isHidden = true;
}

export async function approveAction(parameters: ApproveActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_title', 'Confirm approval'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__approve_dialog_content',
                'You are about to approve this order.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-approve', 'Approve'),
        ))
    ) {
        await approveOrReject(parameters);
    }
}

export async function rejectAction(parameters: RejectActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_title', 'Confirm rejection'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__reject_dialog_content',
                'You are about to reject this order.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-reject', 'Reject'),
        ))
    ) {
        await approveOrReject({
            isCalledFromRecordPage: parameters.isCalledFromRecordPage,
            purchaseOrderPage: parameters.purchaseOrderPage,
            recordNumber: parameters.recordNumber,
            isApproved: !parameters.isRejected,
            recordId: parameters.recordId,
        });
    }
}

export async function closeAction(parameters: CloseActionParameters) {
    if (
        parameters.recordId !== null &&
        (await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_title',
                'Confirm status change',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__close_order_dialog_content',
                'You are about to change the status of this order to closed. You cannot undo this change.',
            ),
            ui.localize('@sage/xtrem-purchasing/closeOrder____title', 'Close order'),
        ))
    ) {
        parameters.purchaseOrderPage.$.loader.isHidden = false;

        try {
            const isClosed = await parameters.purchaseOrderPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.close(true, { purchaseOrder: parameters.recordId || '' })
                .execute();

            if (isClosed) {
                parameters.purchaseOrderPage.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__purchase_order_closed',
                        'The purchase order is closed.',
                    ),
                    { type: 'success' },
                );
                if (parameters.isCalledFromRecordPage && parameters.recordId) {
                    parameters.purchaseOrderPage.$.setPageClean();
                    parameters.purchaseOrderPage.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                        _id: parameters.recordId,
                    });
                }
            }
        } catch (error) {
            // TODO: wait for XT-62376 to be fixed and then move this call in 'onError' instead of using try/catch
            await parameters.purchaseOrderPage.$.processServerErrors(error);
        }
        parameters.purchaseOrderPage.$.loader.isHidden = true;
    }
}

export async function loadApprovers(
    parameters: LoadApproversParameters,
): Promise<{ usersList: UserApprover[]; emailAddressApproval: string }> {
    let purchaseOrderDefaultApprover: userInterfaces.FilteredUsers | null = null;
    let purchaseOrderSubstituteApprover: userInterfaces.FilteredUsers | null = null;
    let emailAddressApproval = '';
    const usersList: UserApprover[] = [];
    let sortOrder = 0;

    const usersQuery = (await parameters.purchaseOrderPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseOrder')
        .queries.getFilteredUsers(
            {
                _id: true,
                email: true,
                firstName: true,
                lastName: true,
            },
            {
                criteria: authorizationFilters.user.activeApplicationUsers,
            },
        )
        .execute()) as userInterfaces.FilteredUsers[];

    const siteApprovers: SiteApprover[] = withoutEdges(
        await parameters.purchaseOrderPage.$.graph
            .node('@sage/xtrem-system/Site')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        id: true,
                        purchaseOrderDefaultApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                        purchaseOrderSubstituteApprover: {
                            _id: true,
                            email: true,
                            firstName: true,
                            lastName: true,
                        },
                    },
                    {
                        filter: {
                            _id: parameters.siteId,
                        },
                    },
                ),
            )
            .execute(),
    );

    if (siteApprovers.length > 0) {
        purchaseOrderDefaultApprover = siteApprovers[0].purchaseOrderDefaultApprover
            ? siteApprovers[0].purchaseOrderDefaultApprover
            : null;
        purchaseOrderSubstituteApprover = siteApprovers[0].purchaseOrderSubstituteApprover
            ? siteApprovers[0].purchaseOrderSubstituteApprover
            : null;

        if (purchaseOrderDefaultApprover?._id) {
            usersList.push({
                ...purchaseOrderDefaultApprover,
                type: ui.localize('@sage/xtrem-purchasing/pages__purchase_order__default_approver', 'Default'),
                sortOrder,
            });
            sortOrder += 1;
            emailAddressApproval = purchaseOrderDefaultApprover.email;
        }

        if (
            purchaseOrderSubstituteApprover &&
            purchaseOrderSubstituteApprover?._id !== purchaseOrderDefaultApprover?._id
        ) {
            usersList.push({
                ...purchaseOrderSubstituteApprover,
                type: ui.localize('@sage/xtrem-purchasing/pages__purchase_order__substitute_approver', 'Substitute'),
                sortOrder,
            });
            sortOrder += 1;
            if (!purchaseOrderDefaultApprover) {
                emailAddressApproval = purchaseOrderSubstituteApprover.email;
            }
        }
    }
    usersList.push(
        ...usersQuery
            .filter(
                userFilter =>
                    ![purchaseOrderDefaultApprover?._id, purchaseOrderSubstituteApprover?._id].includes(userFilter._id),
            )
            .map((user, index) => ({
                ...user,
                type: '',
                sortOrder: sortOrder + index,
            })),
    );
    return { usersList, emailAddressApproval };
}

export async function isConfirmedRequestApprovalAction(parameters: RequestApprovalParameters) {
    if (
        parameters.isGrossPriceMissing &&
        !(await confirmDialogWithAcceptButtonText(
            parameters.purchaseOrderPage,
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__no_price_title', 'Confirm price'),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__no_price',
                'You are about to submit the purchase order for approval. Some lines have no unit price.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-submit', 'Submit'),
        ))
    ) {
        return false;
    }
    return true;
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseOrderLines = await getLinesForDimensions({
        purchaseOrderPage: parameters.purchaseOrderPage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = purchaseOrderLines.filter(
        line =>
            !isPurchaseOrderLineActionDisabled(
                parameters.status || '',
                line.status || '',
                'dimensions',
                parameters.approvalStatus || '',
            ) || parameters.isRepost,
    );

    await asyncArray(linesToSetDimensions).some(async lineToSetDimensions => {
        const loopResponse = false;
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.purchaseOrderPage,
                site: parameters.site || null,
                supplier: parameters.supplier || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForPurchaseLines({
                    line: lineToSetDimensions,
                    purchasePage: parameters.purchaseOrderPage,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                return true;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }

        if (dataDetermined) {
            await parameters.purchaseOrderPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrderLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }

        return loopResponse;
    });

    parameters.purchaseOrderPage.$.showToast(
        ui.localize('@sage/xtrem-purchasing/pages__purchase_order__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}
