import * as masterDataCommon from '@sage/xtrem-master-data/build/lib/client-functions/common';
import type { PurchaseReceiptLineBinding } from '@sage/xtrem-purchasing-api';
import type * as ui from '@sage/xtrem-ui';
import type { PurchaseReceipt as PurchaseReceiptPage } from '../pages/purchase-receipt';
import type { PurchasePrice } from './interfaces';
import { calculatePrices, getItemSupplierPrice, recalculatePricesDialog } from './purchase-price';

function getSupplierPriceFromLine(
    receiptPage: PurchaseReceiptPage,
    line: ui.PartialCollectionValue<PurchaseReceiptLineBinding>,
): Promise<PurchasePrice> {
    if (!line.item || !receiptPage.businessRelation.value || !receiptPage.currency.value || !line.unit) {
        return Promise.resolve({ price: 0, priceOrigin: 'manual' });
    }
    return getItemSupplierPrice(receiptPage.$.graph, {
        site: receiptPage.site.value ?? {},
        doConvertUnit: false,
        supplier: receiptPage.businessRelation.value,
        item: line.item,
        currency: receiptPage.currency.value,
        quantity: parseFloat(line.quantity ?? ''),
        date: new Date(receiptPage.date.value ?? ''),
        unit: line.unit ?? {},
    });
}

function convertLineQuantity(
    receiptPage: PurchaseReceiptPage,
    line: ui.PartialCollectionValue<PurchaseReceiptLineBinding>,
) {
    return masterDataCommon.convertFromTo(
        receiptPage.$.graph,
        line.unit?._id ?? '',
        line.item?.stockUnit?._id ?? '',
        +(line?.quantity ?? ''),
        line.item?._id,
        receiptPage.businessRelation.value?._id ?? '',
        '',
        'purchase',
        false,
    );
}

export async function setPriceOrigin(
    receiptPage: PurchaseReceiptPage,
    line: ui.PartialCollectionValue<PurchaseReceiptLineBinding>,
    isRecalculatePrices: boolean,
) {
    if (line?.item && Number(line?.quantity) >= 0 && line?.unit) {
        const { price, priceOrigin } = await getSupplierPriceFromLine(receiptPage, line);
        line.grossPrice = price.toString();
        line.priceOrigin = priceOrigin ?? 'manual';

        if (isRecalculatePrices && price === 0) {
            line.grossPrice = '0';
            line.priceOrigin = null;
        } else if (price !== 0) {
            await calculatePrices(receiptPage, line);
        }
        receiptPage.lines.addOrUpdateRecordValue(line);
    }
}

export async function onChangeLineQuantity(
    receiptPage: PurchaseReceiptPage,
    line: ui.PartialCollectionValue<PurchaseReceiptLineBinding>,
) {
    if (line?.item && Number(line?.quantity) >= 0 && line?.unit) {
        if (line.origin !== 'purchaseOrder' && +(line._id ?? -1) < 0) {
            const { price, priceOrigin } = await getSupplierPriceFromLine(receiptPage, line);
            line.grossPrice = price.toString();
            line.priceOrigin = priceOrigin ?? 'manual';
        }

        if (line.purchaseOrderLine?.purchaseOrderLine) {
            line.quantityInStockUnit = (
                +(line?.quantity ?? 0) * +(line?.unitToStockUnitConversionFactor ?? 0)
            ).toString();
        } else {
            const { convertedQuantity, conversionFactor } = await convertLineQuantity(receiptPage, line);
            line.quantityInStockUnit = convertedQuantity.toString();
            line.unitToStockUnitConversionFactor = conversionFactor.toString();
        }
        const pricedLine = await calculatePrices(receiptPage, line);
        receiptPage.lines.addOrUpdateRecordValue(pricedLine);
    }
    if (line.purchaseOrderLine?.purchaseReceiptLine) {
        line.purchaseOrderLine.purchaseReceiptLine.quantity = line.quantity;
        receiptPage.purchaseOrderLines.addOrUpdateRecordValue(line.purchaseOrderLine);
    } else if (+(line._id ?? '') < 0) {
        await setPriceOrigin(receiptPage, line, false);
        receiptPage.purchaseOrderLines.value = [];
    }
}

export async function updatePriceOrigin(
    receiptPage: PurchaseReceiptPage,
    line: ui.PartialCollectionValue<PurchaseReceiptLineBinding>,
) {
    if (+(line._id ?? '') > 0) {
        const isRecalculatePrices = await recalculatePricesDialog(receiptPage);

        if (isRecalculatePrices) {
            await setPriceOrigin(receiptPage, line, isRecalculatePrices);
        }
    }
}
