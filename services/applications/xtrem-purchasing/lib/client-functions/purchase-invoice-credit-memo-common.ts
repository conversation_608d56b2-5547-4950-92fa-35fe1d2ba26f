import { date } from '@sage/xtrem-date-time';
import { fetchDefaultsForDueDate } from '@sage/xtrem-master-data/lib/client-functions/common';
import type { PurchaseCreditMemo as PurchaseCreditMemoPage } from '../pages/purchase-credit-memo';
import type { PurchaseInvoice as PurchaseInvoicePage } from '../pages/purchase-invoice';

type PurchaseInvoiceCreditMemoPage = PurchaseCreditMemoPage | PurchaseInvoicePage;

export function dueDateDefault(page: PurchaseInvoiceCreditMemoPage) {
    page.dueDate.value = fetchDefaultsForDueDate({
        paymentTerm: page.paymentTerm.value,
        baseDate: page.supplierDocumentDate.value ? date.parse(page.supplierDocumentDate.value) : null,
        dueDateValue: page.dueDate.value,
    });
}
