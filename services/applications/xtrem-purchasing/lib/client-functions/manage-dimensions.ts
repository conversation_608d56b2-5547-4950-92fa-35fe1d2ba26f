import { extractEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import type { PageRecordNumberParameters } from './interfaces/purchase-order-actions-functions';

export async function getLinesForDimensions(parameters: PageRecordNumberParameters) {
    return extractEdges(
        await parameters.purchaseOrderPage.$.graph
            .node('@sage/xtrem-master-data/BaseDocumentItemLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        document: {
                            _id: true,
                        },
                        status: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}
