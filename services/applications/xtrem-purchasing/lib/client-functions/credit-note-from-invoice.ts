import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type { Currency, PaymentTerm, Supplier } from '@sage/xtrem-master-data-api';
import { getCompanyPriceScale } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import type {
    PurchaseCreditMemoLine,
    PurchaseInvoiceLine,
    PurchaseInvoiceLineToPurchaseCreditMemoLine,
    PurchaseReturnLine,
} from '@sage/xtrem-purchasing-api';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import * as ui from '@sage/xtrem-ui';
import { isArray } from 'lodash';
import type { PurchaseCreditMemo as PurchaseCreditMemoPage } from '../pages/purchase-credit-memo';

export function convertInvoiceLineToReturnToCreditMemoLine(
    data: ui.PartialCollectionValue<PurchaseReturnLine & { selectedQuantity: string } & { selectedGrossPrice: string }>,
): ui.PartialCollectionValue<PurchaseInvoiceLineToPurchaseCreditMemoLine> {
    return {
        purchaseInvoiceLine: {
            _id: data._id,
            document: {
                _id: data.document?._id,
                number: data.document?.number,
                isTransferHeaderNote: data.document?.isTransferHeaderNote,
                isTransferLineNote: data.document?.isTransferLineNote,
            },
            quantity: data.selectedQuantity,
            item: { _id: data.item?._id, name: data.item?.name },
            grossPrice: data.selectedGrossPrice,
            amountExcludingTax: data.amountExcludingTax,
            amountExcludingTaxInCompanyCurrency: data.amountExcludingTaxInCompanyCurrency,
        },
        unit: data.unit,
    } as ui.PartialCollectionValue<PurchaseInvoiceLineToPurchaseCreditMemoLine>;
}

function convertInvoiceLineToCreditMemoLine(
    creditMemoPage: PurchaseCreditMemoPage,
    invoiceLine: ui.PartialCollectionValue<
        PurchaseInvoiceLine & { selectedQuantity: number; selectedGrossPrice: number }
    >,
): ui.PartialCollectionValue<PurchaseCreditMemoLine> {
    const purchaseInvoiceLine = convertInvoiceLineToReturnToCreditMemoLine({
        ...invoiceLine,
        selectedQuantity: String(invoiceLine.selectedQuantity ?? ''),
        selectedGrossPrice: String(invoiceLine.selectedGrossPrice ?? ''),
    });
    const currency = creditMemoPage.currency.value;
    if (!currency) {
        throw new Error('Currency is required');
    }
    const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
        { ...invoiceLine, _id: invoiceLine._id ?? '' },
        creditMemoPage._defaultDimensionsAttributes,
    );

    const creditMemoLineData = { ...invoiceLine };

    delete creditMemoLineData._id;
    delete creditMemoLineData.supplier;
    delete creditMemoLineData.currency;
    delete creditMemoLineData.document;

    const quantityInStockUnit = (
        parseFloat(invoiceLine.remainingQuantityToCredit ?? '') *
        parseFloat(invoiceLine.unitToStockUnitConversionFactor ?? '')
    ).toString();

    return {
        ...creditMemoLineData,
        document: undefined,
        origin: 'purchaseInvoice',
        quantity:
            creditMemoLineData.selectedQuantity !== undefined ? String(creditMemoLineData.selectedQuantity) : undefined,
        unitToStockUnitConversionFactor: creditMemoLineData.unitToStockUnitConversionFactor,
        quantityInStockUnit,
        currency: creditMemoPage.currency.value ?? undefined,
        purchaseInvoiceLine,
        storedAttributes: line.storedAttributes,
        storedDimensions: line.storedDimensions,
        taxDate: invoiceLine.taxDate,
        uiTaxes: invoiceLine.uiTaxes,
        uPurchaseUnit: invoiceLine.unit,
        uStockUnit: invoiceLine.stockUnit,
        stockSite: invoiceLine.recipientSite,
        grossPrice: invoiceLine.selectedGrossPrice !== undefined ? String(invoiceLine.selectedGrossPrice) : undefined,
        netPrice: invoiceLine.selectedGrossPrice !== undefined ? String(invoiceLine.selectedGrossPrice) : undefined,
        internalNote: { value: '' },
        externalNote: { value: '' },
        isExternalNote: false,
    };
}

function filterForInvoiceLine(creditMemoPage: PurchaseCreditMemoPage): Filter<PurchaseInvoiceLine> {
    const creditMemoLinesSysId = creditMemoPage.lines.value.map(line => line._id);
    const creditMemoLines = creditMemoPage.lines.value
        .map(line => line.purchaseInvoiceLine?.purchaseInvoiceLine?._id)
        .filter((id): id is string => typeof id === 'string');

    return {
        _id: { _nin: creditMemoLines },
        document: {
            billBySupplier: { _id: { _eq: creditMemoPage.billBySupplier.value?._id } },
            currency: { _id: { _eq: creditMemoPage.currency.value?._id ?? '' } },
            status: { _in: ['posted', 'error'] },
        },
        remainingQuantityToCredit: { _gt: '0' },
        purchaseCreditMemoLines: { _atMost: 0, purchaseInvoiceLine: { _id: { _in: creditMemoLinesSysId } } },
        _or: [
            { document: { site: { _id: { _eq: creditMemoPage.site.value?._id } } } },
            { document: { site: { financialSite: { _id: { _eq: creditMemoPage.site.value?._id } } } } },
        ],
    };
}

// return type of lookuppurchaseReturnLine function
export type CreditMemoLineLookup = Awaited<ReturnType<typeof lookupInvoiceLine>>[0];

function overInvoiced(line: ExtractEdgesPartial<PurchaseReturnLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) > +(line.remainingQuantityToCredit ?? 0);
}
function underInvoiced(line: ExtractEdgesPartial<PurchaseReturnLine & { selectedQuantity: number }>) {
    return +(line.selectedQuantity ?? 0) < +(line.remainingQuantityToCredit ?? 0);
}

export function lookupInvoiceLine(
    creditMemoPage: PurchaseCreditMemoPage,
    selectedLines?: { _id: string; selectedQuantity: number; selectedGrossPrice?: number }[],
) {
    return creditMemoPage.$.dialog.lookup<
        PurchaseInvoiceLine & { selectedQuantity: number; selectedGrossPrice: number }
    >({
        resolveOnCancel: true,
        isEditable: true,
        isMultiSelect: true,
        size: 'extra-large',
        acceptButton: { text: 'Select' },
        dialogTitle: 'Select invoice lines',
        node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
        selectedRecordId: selectedLines?.map(line => line._id),
        filter: filterForInvoiceLine(creditMemoPage),
        orderBy: { document: { number: +1 }, _sortValue: +1 },
        mapServerRecord(record) {
            const { remainingQuantityToCredit, grossPrice } = record;
            const selectedQuantity = +(remainingQuantityToCredit ?? 0);
            const selectedGrossPrice = +(grossPrice ?? 0);
            return { selectedQuantity, selectedGrossPrice, ...record };
        },
        columns: [
            ui.nestedFields.image({ bind: { item: { image: true } }, title: 'Image', size: 'small' }),
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.reference({
                bind: 'item',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Item',
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                ],
            }),
            ui.nestedFields.reference({
                bind: 'document',
                title: 'Invoice number',
                valueField: 'number',
                node: '@sage/xtrem-purchasing/PurchaseInvoice',
                columns: [
                    ui.nestedFields.technical({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: { document: { displayStatus: true } },
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.document?.displayStatus),
            }),
            ui.nestedFields.numeric({
                bind: 'selectedQuantity',
                title: 'Quantity to credit',
                isTransient: true,
                unitMode: 'unitOfMeasure',
                unit: (_id, row) => row?.unit,
                validation(value) {
                    if (value <= 0)
                        return ui.localize(
                            '@sage/xtrem-purchasing/invoice_to_credit_memo__error_zero_or_negative_quantity',
                            'You need to enter a quantity.',
                        );
                    return undefined;
                },
            }),
            ui.nestedFields.technical({ bind: 'grossPrice' }),
            ui.nestedFields.numeric({
                title: 'Credit memo unit price',
                bind: 'selectedGrossPrice',
                scale: (_rowId, rowData) => getCompanyPriceScale(rowData?.site.legalCompany),
                unit: (_id, rowData) => rowData?.document?.currency,
                isTransient: true,
                validation(value) {
                    if (value <= 0) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/invoice_to_credit_memo__error_zero_or_negative_unit_price',
                            `You need to enter a unit price.`,
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.technical({
                bind: 'recipientSite',
                node: '@sage/xtrem-system/Site',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({
                        bind: { businessEntity: { country: true } },
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'grossPrice' }),
            ui.nestedFields.technical({ bind: 'netPrice' }),
            ui.nestedFields.technical({ bind: 'unitToStockUnitConversionFactor' }),
            ui.nestedFields.technical({ bind: 'remainingQuantityToCredit' }),
            ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
            ui.nestedFields.technical({ bind: 'discount' }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical({ bind: 'charge' }),
            ui.nestedFields.technical({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({
                bind: 'priceOrigin',
            }),
        ],
    });
}

function checkOrderlinesMessage(
    lines: ui.PartialCollectionValue<PurchaseReturnLine & { selectedQuantity: number; selectedGrossPrice: number }>[],
) {
    const isUnderInvoiced = lines.some(underInvoiced);
    const isOverInvoiced = lines.some(overInvoiced);
    if (isUnderInvoiced) {
        return ui.localize(
            '@sage/xtrem-purchasing/invoice_to_credit_memo__confirm_lower_quantity',
            'You are about to create a credit memo for less than the invoiced quantity.',
        );
    }
    if (isOverInvoiced) {
        return ui.localize(
            '@sage/xtrem-purchasing/invoice_to_credit_memo__confirm_greater_quantity',
            'You are about to create a credit memo for more than the invoiced quantity.',
        );
    }
    if (isUnderInvoiced && isOverInvoiced) {
        return ui.localize(
            '@sage/xtrem-purchasing/invoice_to_credit_memo__confirm__lower_greater_quantity',
            'You are about to create order lines with a quantity different from the invoiced quantity.',
        );
    }
    return null;
}

function confirmOrderQuantity(creditMemoPage: PurchaseCreditMemoPage, message: string) {
    return creditMemoPage.$.dialog
        .confirmation(
            'warn',
            ui.localize(
                '@sage/xtrem-purchasing/invoice_to_credit_memo__create_purchase_dialog_title',
                'Confirm credit memo quantity',
            ),
            message,
        )
        .then(() => true)
        .catch(() => false);
}

export async function addLineFromInvoice(
    creditMemoPage: PurchaseCreditMemoPage,
    selectedLines?: { _id: string; selectedQuantity: number; selectedGrossPrice: number }[],
) {
    const invoiceLines = (await lookupInvoiceLine(
        creditMemoPage,
        selectedLines,
    )) as unknown as ui.PartialCollectionValue<
        PurchaseInvoiceLine & { selectedQuantity: number; selectedGrossPrice: number }
    >[];
    if (!invoiceLines || !isArray(invoiceLines) || invoiceLines.length === 0) {
        return;
    }

    const message = checkOrderlinesMessage(invoiceLines);
    if (message && !(await confirmOrderQuantity(creditMemoPage, message))) {
        await addLineFromInvoice(
            creditMemoPage,
            invoiceLines.map(line => ({
                _id: line._id ?? '',
                selectedQuantity: line.selectedQuantity ?? 0,
                selectedGrossPrice: line.selectedGrossPrice ?? 0,
            })),
        );
        return;
    }

    let headerValues: {
        supplier?: ExtractEdgesPartial<Supplier>;
        currency?: ExtractEdgesPartial<Currency>;
        paymentTerm?: ExtractEdgesPartial<PaymentTerm>;
    } = {};

    await asyncArray(invoiceLines).forEach(async invoiceLine => {
        const creditMemoLine = convertInvoiceLineToCreditMemoLine(creditMemoPage, invoiceLine);

        if (!headerValues && !creditMemoPage.billBySupplier.value) {
            headerValues = {
                supplier: invoiceLine.document?.supplier,
                currency: invoiceLine.document?.currency,
                paymentTerm: invoiceLine.document?.paymentTerm,
            };
        }

        await creditMemoPage.calculatePrices(creditMemoLine);
        if (creditMemoLine.uiTaxes && creditMemoLine.taxDate) {
            await creditMemoPage.calculatePrices(creditMemoLine, creditMemoLine.taxDate);
        } else {
            await creditMemoPage.calculatePrices(creditMemoLine);
        }

        creditMemoPage.lines.addRecord(creditMemoLine);

        creditMemoPage.purchaseInvoiceLines.addOrUpdateRecordValue(invoiceLine);
    });
    creditMemoPage._computeTotalAmounts();
    recalculateTaxCalculationStatus(creditMemoPage.lines, creditMemoPage.taxCalculationStatus);

    if (headerValues && !creditMemoPage.billBySupplier.value) {
        creditMemoPage.billBySupplier.value = headerValues.supplier ?? null;
        creditMemoPage.payToSupplier.value = headerValues.supplier ?? null;
        creditMemoPage.payToLinkedAddress.value = headerValues.supplier?.primaryAddress ?? null;
        creditMemoPage.currency.value = headerValues.currency ?? null;
        creditMemoPage.paymentTerm.value = headerValues.paymentTerm ?? null;
    }
}
