import { extractEdges } from '@sage/xtrem-client';
import type { Graph<PERSON><PERSON>, PurchaseReceipt as PurchaseReceiptNode } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isPurchaseReceiptLineActionDisabled } from '../shared-functions/edit-rules';
import {
    confirmDialogWithAcceptButtonText,
    getDimensionsForPurchaseLines,
    getValuesForSetDimensionsFromMainList,
} from './common';
import type {
    ActionParameters,
    CreatePurchaseReturnActionParameters,
    GetPurchaseReceiptLinesParameter,
    PostActionParameter,
    SetDimensionActionParameter,
} from './interfaces/purchase-receipt-actions-functions';

export async function getPurchaseReceiptLines(parameters: GetPurchaseReceiptLinesParameter) {
    return extractEdges(
        await parameters.purchaseReceiptPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        storedDimensions: true,
                        storedAttributes: true,
                        netPrice: true,
                        stockDetailStatus: true,
                        document: {
                            number: true,
                        },
                        status: true,
                    },

                    {
                        filter: {
                            document: {
                                number: parameters.recordNumber,
                            },
                        },
                    },
                ),
            )
            .execute(),
    );
}

export async function setDimensions(parameters: SetDimensionActionParameter) {
    let dataDetermined = false;
    let dimensionsToSet = '';
    let attributesToSet = '';
    const purchaseReceiptLines = await getPurchaseReceiptLines({
        purchaseReceiptPage: parameters.purchaseReceiptPage,
        recordNumber: parameters.recordNumber,
    });
    const linesToSetDimensions = purchaseReceiptLines.filter(
        line =>
            !isPurchaseReceiptLineActionDisabled(parameters.status || '', line.status || '', 'dimensions') ||
            parameters.isRepost,
    );
    // eslint-disable-next-line no-restricted-syntax
    for (const lineToSetDimensions of linesToSetDimensions) {
        if (!dataDetermined) {
            const { defaultedFromItem, defaultDimensionsAttributes } = await getValuesForSetDimensionsFromMainList({
                page: parameters.purchaseReceiptPage,
                site: parameters.site || null,
                supplier: parameters.supplier || null,
            });
            const { resultDimensionsToSet, resultAttributesToSet, resultDataDetermined } =
                await getDimensionsForPurchaseLines({
                    line: lineToSetDimensions,
                    purchasePage: parameters.purchaseReceiptPage,
                    defaultDimensionsAttributes,
                    defaultedFromItem,
                });
            if (!resultDataDetermined) {
                break;
            }
            dimensionsToSet = resultDimensionsToSet;
            attributesToSet = resultAttributesToSet;
            dataDetermined = resultDataDetermined;
        }
        if (dataDetermined) {
            await parameters.purchaseReceiptPage.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReceiptLine')
                .mutations.setDimension(true, {
                    baseDocumentItemLine: lineToSetDimensions._id,
                    storedDimensions: dimensionsToSet,
                    storedAttributes: attributesToSet,
                })
                .execute();
        }
    }
    parameters.purchaseReceiptPage.$.showToast(
        ui.localize('@sage/xtrem-purchasing/pages__purchase_receipt__apply_dimensions_success', 'Dimensions applied.'),
        { type: 'success' },
    );
}

async function getDisplayStatus(parameters: ActionParameters) {
    const purchaseReceipt = extractEdges(
        await parameters.purchaseReceiptPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReceipt')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        displayStatus: true,
                    },

                    {
                        filter: {
                            _id: parameters.recordId,
                        },
                    },
                ),
            )
            .execute(),
    ) as PurchaseReceiptNode[];
    return purchaseReceipt[0].displayStatus;
}

async function checkForUpdate(parameters: ActionParameters) {
    // TODO: This is a hack. It can be removed when XT-23948 is ready.
    let refreshCounter = 0;
    const checkForDisplayStatus = async () => {
        const displayStatus = await getDisplayStatus({
            purchaseReceiptPage: parameters.purchaseReceiptPage,
            recordId: parameters.recordId,
        });
        if (['received', 'error'].includes(displayStatus)) {
            await parameters.purchaseReceiptPage.$.router.refresh(true);
            await parameters.purchaseReceiptPage.$.refreshNavigationPanel();
            parameters.purchaseReceiptPage.$.loader.isHidden = true;
            return;
        }
        refreshCounter += 1;
        if (refreshCounter <= 5) {
            // TODO: XT-79005
            // eslint-disable-next-line
            setTimeout(checkForDisplayStatus, 1000);
        }
    };
    await checkForDisplayStatus();
}

export async function post(parameters: PostActionParameter) {
    if (parameters.taxCalculationStatus === 'failed') {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt_post__tax_calculation_failed',
                'You need to correct lines that failed the tax calculation before you can post.',
            ),
        );
    }
    const purchaseRequisitionLines = await getPurchaseReceiptLines({
        purchaseReceiptPage: parameters.purchaseReceiptPage,
        recordNumber: parameters.recordNumber,
    });
    if (purchaseRequisitionLines.some(line => line.stockDetailStatus === 'required')) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt_post__stock_details_required',
                'You need to enter stock details for all lines before you can post.',
            ),
        );
    }

    let doWePostReceipt: boolean;
    doWePostReceipt = true;
    const zeroValueLines = purchaseRequisitionLines.filter(line => Number(line.netPrice) === 0).length;
    if (zeroValueLines) {
        doWePostReceipt = await confirmDialogWithAcceptButtonText(
            parameters.purchaseReceiptPage,
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_title',
                'Confirm posting of lines with values equal to zero',
            ),
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt__line_equals_zero_dialog_content',
                'You are about to post this purchase receipt with lines that have values equal to zero.',
            ),
            ui.localize('@sage/xtrem-purchasing/pages-confirm-post', 'Post'),
        );
    }
    if (doWePostReceipt) {
        // post mutation of the node called to trigger the accounting interface
        await parameters.purchaseReceiptPage.$.graph
            .node('@sage/xtrem-purchasing/PurchaseReceipt')
            .mutations.post(true, { receipt: parameters.recordId })
            .execute();
        await checkForUpdate({ purchaseReceiptPage: parameters.purchaseReceiptPage, recordId: parameters.recordId });
        parameters.purchaseReceiptPage.$.showToast(
            ui.localize('@sage/xtrem-purchasing/pages__purchase_receipt__post_from_main_row', 'Receipt posted'),
            { type: 'success' },
        );
    }
}

function confirmPurchaseReturnCreation(purchaseReceiptPage: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        purchaseReceiptPage,
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_title',
            'Confirm return creation',
        ),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_content',
            'You are about to create a return from this purchase receipt.',
        ),
        ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
    );
}

/** Only lines containing a supplier are included */
function onlyLinesWithSupplier(purchaseReturnPage: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        purchaseReturnPage,
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_title',
            'Confirm return creation',
        ),
        ui.localize(
            '@sage/xtrem-purchasing/pages__purchase_receipt__create_return_dialog_lines_without_supplier',
            'Only lines containing a supplier are included.',
        ),
        ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
    );
}

export async function createPurchaseReturnAction(parameters: CreatePurchaseReturnActionParameters) {
    const { lines } = parameters;
    if (lines && (await confirmPurchaseReturnCreation(parameters.purchaseReceiptPage))) {
        if (lines.length && lines.every(row => !row.supplier?._id && row.status === 'pending')) {
            throw new Error(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_return_select_supplier',
                    'You need to select a supplier before creating a receipt.',
                ),
            );
        }
        if (lines.some(row => !row.supplier?._id && row.status === 'pending')) {
            if (!(await onlyLinesWithSupplier(parameters.purchaseReceiptPage))) {
                return;
            }
            return;
        }
    }

    parameters.purchaseReceiptPage.$.loader.isHidden = false;
    const returns = await parameters.purchaseReceiptPage.$.graph
        .node('@sage/xtrem-purchasing/PurchaseReceipt')
        .mutations.createPurchaseReturns({ number: true }, { document: parameters.recordId })
        .execute();

    if (returns.length) {
        parameters.purchaseReceiptPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt__return_created',
                'Purchase returns created: {{returnNumbers}}.',
                {
                    returnNumbers: returns.map(order => order.number).join('\n'),
                },
            ),
            { type: 'success' },
        );
    } else {
        parameters.purchaseReceiptPage.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_receipt__return_not_created',
                'The record was not created.',
            ),
            { type: 'error' },
        );
    }

    parameters.purchaseReceiptPage.$.setPageClean();

    await parameters.purchaseReceiptPage.$.router.refresh();
    await parameters.purchaseReceiptPage.$.refreshNavigationPanel();
    parameters.purchaseReceiptPage.$.loader.isHidden = true;
}
