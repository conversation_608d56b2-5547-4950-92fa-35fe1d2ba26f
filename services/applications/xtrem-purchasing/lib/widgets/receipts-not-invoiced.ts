import * as ui from '@sage/xtrem-ui';

interface PurchaseReceiptQueryResponseNode {
    _id: string;
    number: string;
    supplier: {
        _id: string;
        name: string;
    };
    site: {
        _id: string;
        name: string;
    };
    status: string;
    date: string;
}

interface PurchaseReceiptQueryResponse {
    cursor: string;
    node: PurchaseReceiptQueryResponseNode;
}

interface PurchaseReceiptObject {
    _id: string;
    index: string;
    title: string;
    line2: string;
    titleRight: string;
    line3: string;
    line2Right: string;
    cursor: string;
}

@ui.widgets.table<ReceiptsNotInvoiced>({
    title: 'Purchase receipts not invoiced',
    category: 'Period close validations',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        const orderRecords = this.$.data?.xtremPurchasing?.purchaseReceipt?.query?.edges?.length ?? 0;
        if (orderRecords > 0) {
            return this.$.data.xtremPurchasing.purchaseReceipt.query.edges.map(
                ({ node, cursor }: PurchaseReceiptQueryResponse, index: number): PurchaseReceiptObject => ({
                    _id: node._id,
                    index: `${index}`,
                    title: node.number,
                    titleRight: node.supplier.name,
                    line2: node.site.name,
                    line2Right: ui.localizeEnumMember('@sage/xtrem-purchasing/PurchaseDocumentStatus', node.status),
                    line3: ui.formatDateToCurrentLocale(node.date),
                    cursor,
                }),
            );
        }
        return [];
    },
    callToActions: {
        SeeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-purchasing/PurchaseReceipt');
            },
        },
    },

    canSwitchViewMode: true,
    displayMode: 'table',
    dataDropdownMenu: {
        orderBy: {
            number: { title: 'Sort by number' },
            date: { title: 'Sort by receipt date' },
            status: { title: 'Sort by status' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Number',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-purchasing/PurchaseReceipt', { _id });
            },
        },
        titleRight: {
            title: 'Supplier',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Supplier', { _id: node.supplier._id });
            },
        },
        line2: {
            title: 'Site',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Site', { _id: node.site._id });
            },
        },
        line2Right: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { status } = this.getDataFromLineIndex(_id);
                    switch (status) {
                        case 'error':
                            return 'negative';
                        case 'posted':
                            return 'positive';
                        case 'inProgress':
                            return 'warning';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line3: { title: 'Receipt date' },
    },
    getQuery(args) {
        const filter = { invoiceStatus: { _ne: 'invoiced' } };
        const orderBy: { number?: number; status?: number; date?: number } = {};

        switch (this.$.options.dataOptions?.orderBy) {
            case 'number':
                orderBy.number = 1;
                break;
            case 'date':
                orderBy.date = 1;
                break;
            case 'status':
                orderBy.status = 1;
                break;
            default:
                orderBy.date = 1;
        }

        return {
            xtremPurchasing: {
                purchaseReceipt: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                supplier: {
                                    _id: true,
                                    name: true,
                                },
                                site: {
                                    _id: true,
                                    name: true,
                                },
                                status: true,
                                date: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class ReceiptsNotInvoiced extends ui.widgets.TableWidget {
    private getDataFromLineIndex(_id: string): PurchaseReceiptQueryResponseNode {
        return this.$.data.xtremPurchasing.purchaseReceipt.query.edges.find(
            (edge: PurchaseReceiptQueryResponse) => edge.node._id === _id,
        ).node;
    }
}
