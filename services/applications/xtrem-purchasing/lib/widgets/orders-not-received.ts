import * as ui from '@sage/xtrem-ui';

interface PurchaseOrderQueryResponseNode {
    _id: string;
    number: string;
    supplier: {
        _id: string;
        name: string;
    };
    site: {
        _id: string;
        name: string;
    };
    status: string;
    orderDate: string;
    earliestExpectedDate: string;
}

interface PurchaseOrderQueryResponse {
    cursor: string;
    node: PurchaseOrderQueryResponseNode;
}

interface PurchaseOrderObject {
    _id: string;
    index: string;
    title: string;
    line2: string;
    titleRight: string;
    line3: string;
    line2Right: string;
    line3Right: string;
    cursor: string;
}

@ui.widgets.table<OrdersNotReceived>({
    title: 'Purchase orders not received',
    category: 'Period close validations',
    cacheLifespan: ui.widgets.WidgetCacheLifespan.day,
    content() {
        const orderRecords = this.$.data.xtremPurchasing.purchaseOrder.query?.edges?.length;
        if (orderRecords > 0) {
            return this.$.data.xtremPurchasing.purchaseOrder.query.edges.map(
                ({ node, cursor }: PurchaseOrderQueryResponse, index: number): PurchaseOrderObject => ({
                    _id: node._id,
                    index: `${index}`,
                    title: node.number,
                    titleRight: node.supplier.name,
                    line2: node.site.name,
                    line2Right: ui.localizeEnumMember('@sage/xtrem-purchasing/PurchaseDocumentStatus', node.status),
                    line3: ui.formatDateToCurrentLocale(node.orderDate),
                    line3Right: node.earliestExpectedDate
                        ? ui.formatDateToCurrentLocale(node.earliestExpectedDate)
                        : '',
                    cursor,
                }),
            );
        }
        return [];
    },
    callToActions: {
        SeeAll: {
            title: 'See all',
            onClick() {
                this.$.router.goTo('@sage/xtrem-purchasing/PurchaseOrder');
            },
        },
    },

    canSwitchViewMode: true,
    displayMode: 'table',
    dataDropdownMenu: {
        orderBy: {
            number: { title: 'Sort by number' },
            orderDate: { title: 'Sort by order date' },
            status: { title: 'Sort by status' },
        },
    },
    rowDefinition: {
        title: {
            title: 'Number',
            renderedAs: 'link',
            onClick(_id: string) {
                this.$.router.goTo('@sage/xtrem-purchasing/PurchaseOrder', { _id });
            },
        },
        titleRight: {
            title: 'Supplier',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Supplier', { _id: node.supplier._id });
            },
        },
        line2: {
            title: 'Site',
            renderedAs: 'link',
            onClick(_id: string) {
                const node = this.getDataFromLineIndex(_id);
                this.$.router.goTo('@sage/xtrem-master-data/Site', { _id: node.site._id });
            },
        },
        line2Right: {
            title: 'Status',
            renderedAs: 'pill',
            displayOptions: {
                colorVariant(_id: string) {
                    const { status } = this.getDataFromLineIndex(_id);
                    switch (status) {
                        case 'error':
                            return 'negative';
                        case 'posted':
                            return 'positive';
                        case 'inProgress':
                            return 'warning';
                        default:
                            return 'neutral';
                    }
                },
            },
        },
        line3: { title: 'Order date' },
        line3Right: { title: 'Earliest expected date' },
    },
    getQuery(args) {
        const filter = { receiptStatus: { _ne: 'received' } };
        const orderBy: { number?: number; status?: number; orderDate?: number } = {};

        switch (this.$.options.dataOptions?.orderBy) {
            case 'number':
                orderBy.number = 1;
                break;
            case 'orderDate':
                orderBy.orderDate = 1;
                break;
            case 'status':
                orderBy.status = 1;
                break;
            default:
                orderBy.orderDate = 1;
        }

        return {
            xtremPurchasing: {
                purchaseOrder: {
                    query: {
                        __args: {
                            filter: JSON.stringify(filter),
                            orderBy: JSON.stringify(orderBy),
                            ...(args?.after?.cursor && { after: args.after.cursor }),
                        },
                        edges: {
                            cursor: true,
                            node: {
                                _id: true,
                                number: true,
                                supplier: {
                                    _id: true,
                                    name: true,
                                },
                                site: {
                                    _id: true,
                                    name: true,
                                },
                                status: true,
                                orderDate: true,
                                earliestExpectedDate: true,
                            },
                        },
                    },
                },
            },
        };
    },
})
export class OrdersNotReceived extends ui.widgets.TableWidget {
    private getDataFromLineIndex(_id: string): PurchaseOrderQueryResponseNode {
        return this.$.data.xtremPurchasing.purchaseOrder.query.edges.find(
            (edge: PurchaseOrderQueryResponse) => edge.node._id === _id,
        ).node;
    }
}
