import { Activity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import { commonPurchasingActivities } from '../functions/common';
import * as xtremPurchasing from '../index';

export const purchaseOrder = new Activity({
    description: 'Purchase order',
    node: () => xtremPurchasing.nodes.PurchaseOrder,
    __filename,
    permissions: ['read', 'manage', 'approve', 'confirm', 'close'],
    operationGrants: {
        read: [
            ...commonPurchasingActivities,
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisition],
            },
            {
                operations: ['lookup', 'getFilteredList'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisitionLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine],
            },
        ],
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'sendApprovalRequestMail',
                    'sendRequestChangesMail',
                    'financeIntegrationCheck',
                    'setIsSentPurchaseOrder',
                    'printPurchaseOrderAndEmail',
                    'createPurchaseOrderReplenishment',
                    'getFilteredUsers',
                    'printBulk',
                    'repost',
                    'beforePrintPurchaseOrder',
                    'afterPrintPurchaseOrder',
                ],
                on: [() => xtremPurchasing.nodes.PurchaseOrder],
            },
            {
                operations: ['bulkResync'],
                on: [() => xtremMasterData.nodes.BaseDocument],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => xtremReporting.nodes.Report],
            },
            {
                operations: ['lookup', 'createPurchaseOrders'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisition],
            },
            {
                operations: ['lookup', 'getFilteredList'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisitionLine],
            },
            {
                operations: [
                    'lookup',
                    'getPurchaseLeadTime',
                    'calculateLineTaxes',
                    'setDimension',
                    'controlCloseLine',
                    'controlDelete',
                ],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLine],
            },
            ...commonPurchasingActivities,
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine],
            },
        ],

        approve: [
            { operations: ['read', 'massApproval'], on: [() => xtremPurchasing.nodes.PurchaseOrder] },
            ...commonPurchasingActivities,
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisition],
            },
            {
                operations: ['lookup', 'getFilteredList'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisitionLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine],
            },
        ],
        confirm: [
            { operations: ['read'], on: [() => xtremPurchasing.nodes.PurchaseOrder] },
            ...commonPurchasingActivities,
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisition],
            },
            {
                operations: ['lookup', 'getFilteredList'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisitionLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine],
            },
        ],
        close: [
            { operations: ['read', 'closeLine'], on: [() => xtremPurchasing.nodes.PurchaseOrder] },
            ...commonPurchasingActivities,
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisition],
            },
            {
                operations: ['bulkResync'],
                on: [() => xtremMasterData.nodes.BaseDocument],
            },
            {
                operations: ['lookup', 'getFilteredList'],
                on: [() => xtremPurchasing.nodes.PurchaseRequisitionLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine],
            },
        ],
    },
});
