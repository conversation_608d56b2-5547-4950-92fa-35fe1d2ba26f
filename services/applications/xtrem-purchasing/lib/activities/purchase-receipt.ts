import * as xtremCommunication from '@sage/xtrem-communication';
import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import { commonPurchasingActivities } from '../functions/common';
import * as xtremPurchasing from '../index';

const { Site, Company } = xtremSystem.nodes;
const { Legislation } = xtremStructure.nodes;
const { FinanceTransaction } = xtremFinanceData.nodes;
const { Report } = xtremReporting.nodes;
const { SysNotificationHistory } = xtremCommunication.nodes;
const { Currency } = xtremMasterData.nodes;

const commonOperations: OperationGrant[] = [
    ...commonPurchasingActivities,
    ...xtremStockData.functions.stockDetailLib.stockReceiptOperations,
    {
        operations: ['lookup'],
        on: [() => xtremPurchasing.nodes.PurchaseOrder, () => Site, () => Company, () => Legislation, () => Currency],
    },
];

export const purchaseReceipt = new Activity({
    description: 'Purchase receipt',
    node: () => xtremPurchasing.nodes.PurchaseReceipt,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [
            ...commonOperations,
            {
                operations: ['getPostingStatusDataByDocumentId'],
                on: [() => FinanceTransaction],
            },
        ],
        manage: [
            ...commonOperations,
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'financeIntegrationCheck',
                    'sendApprovalRequestMail',
                    'beforePrintPurchaseReceipt',
                ],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },
            {
                operations: ['createPurchaseReceipt'],
                on: [() => xtremPurchasing.nodes.PurchaseOrder],
            },
            {
                operations: ['generateReportPdf', 'generateReportZip', 'generateReports'],
                on: [() => Report],
            },
            {
                operations: ['calculateLineTaxes', 'setDimension'],
                on: [() => xtremPurchasing.nodes.PurchaseReceiptLine],
            },

            {
                operations: ['read', 'unbilledAccountPayableInquiry'],
                on: [() => xtremPurchasing.nodes.UnbilledAccountPayableInputSet],
            },
        ],
        post: [
            ...commonOperations,
            {
                operations: ['read', 'repost', 'resendNotificationForFinance'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },
            {
                operations: ['read'],
                on: [() => SysNotificationHistory],
            },
        ],
    },
});
