import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import { commonPurchasingActivities } from '../functions/common';
import * as xtremPurchasing from '../index';

export const purchaseCreditMemo = new Activity({
    description: 'Purchase credit memo',
    node: () => xtremPurchasing.nodes.PurchaseCreditMemo,
    __filename,
    permissions: ['read', 'post', 'manage'],
    operationGrants: {
        read: [
            ...commonPurchasingActivities,
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReturn],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoice],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine],
            },
        ],
        post: [
            {
                operations: ['repost', 'resendNotificationForFinance'],
                on: [() => xtremPurchasing.nodes.PurchaseCreditMemo],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReturn],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoice],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine],
            },
            {
                operations: ['getPostingStatusData'],
                on: [() => xtremFinanceData.nodes.FinanceTransaction],
            },
            ...commonPurchasingActivities,
        ],
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'enforceStatusPosted',
                    'financeIntegrationCheck',
                    'sendNotificationToBuyerMail',
                    'synchronizeDisplayStatus',
                    'sendApprovalRequestMail',
                ],
                on: [() => xtremPurchasing.nodes.PurchaseCreditMemo],
            },
            {
                operations: ['createCreditMemoFromInvoice'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoice],
            },
            {
                operations: ['calculateLineTaxes', 'setDimension'],
                on: [() => xtremPurchasing.nodes.PurchaseCreditMemoLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReturn],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceiptLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoice],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseInvoiceLine],
            },

            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseReturnLine],
            },
            ...commonPurchasingActivities,
        ],
    },
});
