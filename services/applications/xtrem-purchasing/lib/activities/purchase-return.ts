import * as xtremCommunication from '@sage/xtrem-communication';
import { Activity } from '@sage/xtrem-core';

import * as xtremStockData from '@sage/xtrem-stock-data';
import { commonPurchasingActivities } from '../functions/common';
import * as xtremPurchasing from '../index';

export const purchaseReturn = new Activity({
    description: 'Purchase return',
    node: () => xtremPurchasing.nodes.PurchaseReturn,
    __filename,
    permissions: ['read', 'manage', 'post'],
    operationGrants: {
        read: [
            ...commonPurchasingActivities,
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },
        ],
        post: [
            {
                operations: ['read', 'repost', 'resendNotificationForFinance'],
                on: [() => xtremPurchasing.nodes.PurchaseReturn],
            },
            {
                operations: ['read'],
                on: [() => xtremCommunication.nodes.SysNotificationHistory],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrder],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },

            ...commonPurchasingActivities,
        ],
        manage: [
            {
                operations: [
                    'read',
                    'create',
                    'update',
                    'delete',
                    'financeIntegrationCheck',
                    'close',
                    'approve',
                    'confirm',
                    'submitForApproval',
                    'sendApprovalRequestMail',
                ],
                on: [() => xtremPurchasing.nodes.PurchaseReturn],
            },
            {
                operations: ['lookup', 'createPurchaseReturns'],
                on: [() => xtremPurchasing.nodes.PurchaseReceipt],
            },
            {
                operations: ['setDimension'],
                on: [() => xtremPurchasing.nodes.PurchaseReturnLine],
            },
            {
                operations: ['lookup'],
                on: [() => xtremPurchasing.nodes.PurchaseOrder],
            },
            ...xtremStockData.functions.allocationLib.allocationOperation,
            ...commonPurchasingActivities,
        ],
    },
});
