import type { Context } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

type LineType =
    | xtremPurchasing.nodes.PurchaseInvoiceLine
    | xtremPurchasing.nodes.PurchaseRequisitionLine
    | xtremPurchasing.nodes.PurchaseReturnLine
    | xtremPurchasing.nodes.PurchaseOrderLine
    | xtremPurchasing.nodes.PurchaseReceiptLine
    | null;

export async function computeAttributes(
    context: Context,
    data: {
        site?: xtremSystem.nodes.Site | null;
        item?: xtremMasterData.nodes.Item | null;
        supplier?: xtremMasterData.nodes.Supplier | null;
        line?: LineType;
    },
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    const { site, item, line, supplier } = data;

    const result = await xtremFinanceData.functions.computeGenericAttributes(context, {
        item,
        stockSite: site,
        financialSite: site,
    });

    let stockSite = site;
    let purchaseSite = site;

    if (line instanceof xtremPurchasing.nodes.PurchaseInvoiceLine) {
        const invoiceLineOrigin = await line?.origin;
        const receiptLine = await (await line?.purchaseReceiptLine)?.purchaseReceiptLine;
        const receiptLineOrigin = await receiptLine?.origin;
        const receipt = await receiptLine?.document;
        stockSite = await receipt?.site;

        if (invoiceLineOrigin !== 'direct' && receiptLineOrigin !== 'direct') {
            const purchaseOrderLine = await (await line?.purchaseOrderLine)?.purchaseOrderLine;
            const purchaseOrder = await purchaseOrderLine?.document;
            purchaseSite = await purchaseOrder?.site;
        } else if (invoiceLineOrigin === 'direct') {
            delete result.stockSite;
            stockSite = null;
        }
    }

    if (line instanceof xtremPurchasing.nodes.PurchaseOrderLine) {
        stockSite = await line?.stockSite;
        purchaseSite = await line?.site;
    }

    if (line instanceof xtremPurchasing.nodes.PurchaseRequisitionLine) {
        stockSite = await line?.site;
        purchaseSite = await line?.site;
    }

    if (line instanceof xtremPurchasing.nodes.PurchaseReceiptLine) {
        const origin = await line?.origin;
        const receipt = await line?.document;

        if (origin === 'purchaseOrder') {
            const purchaseOrderLine = await line?.purchaseOrderLine;
            const purchaseOrder = await (await purchaseOrderLine?.purchaseOrderLine)?.document;
            purchaseSite = await purchaseOrder?.site;
        }

        if (origin === 'direct') {
            delete result.businessSite;
            purchaseSite = null;
        }

        stockSite = await receipt?.site;
    }

    if (line instanceof xtremPurchasing.nodes.PurchaseReturnLine) {
        purchaseSite = await (await line?.document)?.returnSite;
        const origin = await line?.origin;
        if (origin === 'purchaseOrder') {
            purchaseSite = await (await line?.document)?.site;
        }
        if (origin === 'direct') {
            delete result.businessSite;
            purchaseSite = null;
        }

        stockSite = await (await line?.document)?.returnSite;
    }

    if (purchaseSite) {
        result.businessSite = await xtremFinanceData.functions.checkAttributeTypeActive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'businessSite',
            await purchaseSite.id,
        );
    }

    if (
        stockSite &&
        !(await xtremFinanceData.functions.checkAttributeTypeInactive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'stockSite',
        ))
    ) {
        if (await stockSite?.isInventory) {
            result.stockSite = await xtremFinanceData.functions.checkAttributeTypeActive(
                context,
                xtremFinanceData.nodes.AttributeType,
                'stockSite',
                await stockSite.id,
            );
        }
    }

    if (supplier) {
        result.supplier = await xtremFinanceData.functions.checkAttributeTypeActive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'supplier',
            await (
                await supplier.businessEntity
            ).id,
        );
    }

    return result;
}
