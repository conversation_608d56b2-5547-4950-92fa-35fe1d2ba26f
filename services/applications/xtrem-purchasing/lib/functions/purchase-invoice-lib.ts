import type { AsyncArrayReader, Collection, Context, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, BusinessRuleError, date, NodeStatus, UpdateAction } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { decimal } from '@sage/xtrem-shared';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderInternalNote,
    setHeaderSetupNote,
    setLineInternalNote,
} from './common';
import { loggers } from './loggers';

export const computeMatchingStatusFromLinesStatuses = async (
    purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice,
) => {
    if (
        [NodeStatus.modified, NodeStatus.added].includes(purchaseInvoice.$.status) ||
        (await purchaseInvoice.lines.some(line =>
            [NodeStatus.modified, NodeStatus.deleted, NodeStatus.added].includes(line.$.status),
        ))
    ) {
        const lineMatchingStatuses = [
            ...(await purchaseInvoice.lines.reduce(
                async (statuses, line) => statuses.add(await line.matchingStatus),
                new Set<xtremPurchasing.enums.PurchaseInvoiceMatchingStatus>(),
            )),
        ];

        if (lineMatchingStatuses.some(status => status === 'variance')) {
            return 'variance';
        }
        if (lineMatchingStatuses.some(status => status === 'varianceApproved')) {
            return 'varianceApproved';
        }
        if (lineMatchingStatuses.some(status => status === 'noVariance')) {
            return 'noVariance';
        }
    }

    return (await purchaseInvoice.lines.length) ? purchaseInvoice.matchingStatus : 'noVariance';
};

/**
 * This function is used to approve the matching status on a purchase invoice line
 * @param context - current context
 * @param purchaseInvoiceId - purchase invoice id
 * @param purchaseInvoiceLineId - purchase invoice line id
 * @param approve - true to approve
 * @returns true/false
 */
export async function managePurchaseInvoiceLineMatchingStatus(
    context: Context,
    purchaseInvoiceId: number,
    purchaseInvoiceLineId: number,
    approve: boolean,
): Promise<boolean> {
    if (!purchaseInvoiceLineId || !purchaseInvoiceId) {
        return false;
    }

    const purchaseInvoice = await context.read(
        xtremPurchasing.nodes.PurchaseInvoice,
        {
            _id: purchaseInvoiceId,
        },
        { forUpdate: true },
    );
    const purchaseInvoiceLine = await purchaseInvoice.lines.find(line => line._id === purchaseInvoiceLineId);
    if (purchaseInvoiceLine) {
        const user = await context.read(xtremSystem.nodes.User, { _id: (await context.user)?._id });
        if ((await purchaseInvoiceLine.matchingStatus) === 'variance') {
            await purchaseInvoiceLine.$.set({ matchingStatus: (approve && 'varianceApproved') || 'variance' });
            await purchaseInvoiceLine.$.set({ varianceApprover: user });
        }
        await purchaseInvoice.$.save();
        return true;
    }
    return false;
}

/**
 * This function is used to build credit memo line data
 * @param purchaseReceiptLine
 */
async function buildCreditMemoLineData(
    purchaseInvoiceLine: xtremPurchasing.nodes.PurchaseInvoiceLine,
): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemoLine>> {
    return {
        item: (await purchaseInvoiceLine.item)?._id,
        site: (await purchaseInvoiceLine.site)?._id,
        unit: (await purchaseInvoiceLine.unit)?._id,
        unitToStockUnitConversionFactor: await purchaseInvoiceLine.unitToStockUnitConversionFactor,
        quantity: await purchaseInvoiceLine.remainingQuantityToCredit,
        grossPrice: await purchaseInvoiceLine.grossPrice,
        discount: await purchaseInvoiceLine.discount,
        charge: await purchaseInvoiceLine.charge,
        priceOrigin: await purchaseInvoiceLine.priceOrigin,
        stockSite: await purchaseInvoiceLine.stockSite,
        recipientSite: await purchaseInvoiceLine.recipientSite,
        storedAttributes: await purchaseInvoiceLine.storedAttributes,
        storedDimensions: await purchaseInvoiceLine.storedDimensions,
        purchaseInvoiceLine: { purchaseInvoiceLine },
        taxes: await purchaseInvoiceLine.taxes
            .map(async tax => ({
                taxCategoryReference: (await tax.taxCategoryReference)?._id,
                taxReference: (await tax.taxReference)?._id,
                isSubjectToGlTaxExcludedAmount: await tax.isSubjectToGlTaxExcludedAmount,
                isTaxMandatory: await tax.isTaxMandatory,
                _sortValue: await tax._sortValue,
                // taxRate is only to meet field requirements. It will be rewritten to the actual rate
                // for the current date during BaseTaxCalculator execution.
                taxRate: await tax.taxRate,
            }))
            .toArray(),
    };
}

/**
 * Function to create a purchase credit memo from a purchase invoice
 * @param context
 * @param purchaseInvoice
 * @returns returns created credit memo instance
 */
export async function initPurchaseCreditMemoCreateData(
    document: xtremPurchasing.nodes.PurchaseInvoice,
    reasonCode: xtremMasterData.nodes.ReasonCode,
    totalAmountExcludingTax: decimal,
    supplierDocumentDate: date,
): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>> {
    return {
        site: await document.site,
        billBySupplier: await document.billBySupplier,
        currency: await document.currency,
        creditMemoDate: date.today(),
        paymentTerm: await document.paymentTerm,
        payToSupplier: await document.payToSupplier,
        reason: reasonCode,
        totalAmountExcludingTax,
        supplierDocumentDate,
        isOverwriteNote: true,
        paymentTracking: {
            paymentTerm: await document.paymentTerm,
        },
        lines: await document.lines
            .filter(async line => (await line.remainingQuantityToCredit) > 0)
            .map(purchaseInvoiceLine => buildCreditMemoLineData(purchaseInvoiceLine))
            .toArray(),
    };
}

/**
 * This function creates the new credit memo
 * @param context
 * @param newPurchaseCreditMemoCreateData
 * @returns the _id of the new credit memo
 */
export async function createPurchaseCreditMemo(
    context: Context,
    newPurchaseCreditMemoCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>,
): Promise<xtremPurchasing.nodes.PurchaseCreditMemo> {
    loggers.invoiceLib.debug(() => `createPurchaseCreditMemo data=${JSON.stringify(newPurchaseCreditMemoCreateData)}`);

    const newPurchaseCreditMemo = await context.create(
        xtremPurchasing.nodes.PurchaseCreditMemo,
        newPurchaseCreditMemoCreateData,
    );

    if (await newPurchaseCreditMemo.$.trySave()) {
        loggers.invoiceLib.debug(() => `Purchase credit memo created: _id=${newPurchaseCreditMemo._id}}`);
    }
    return newPurchaseCreditMemo;
}

/**
 * Calculates the display status of the invoice based on the other document status properties
 * @param status
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculatePurchaseInvoiceDisplayStatus(
    status: xtremPurchasing.enums.PurchaseDocumentStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
    matchingStatus: xtremPurchasing.enums.PurchaseInvoiceMatchingStatus,
    paymentStatus: xtremFinanceData.enums.OpenItemStatus | null,
): xtremPurchasing.enums.PurchaseInvoiceDisplayStatus {
    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }
    if (matchingStatus === 'variance') {
        return 'variance';
    }
    if (stockTransactionStatus === 'error') {
        return 'stockError';
    }
    if (paymentStatus && paymentStatus === 'paid') {
        return 'paid';
    }
    if (paymentStatus && paymentStatus === 'partiallyPaid') {
        return 'partiallyPaid';
    }
    if (status === 'posted') {
        return 'posted';
    }
    if (status === 'error') {
        return 'postingError';
    }
    if (status === 'inProgress') {
        return 'postingInProgress';
    }
    if (matchingStatus === 'noVariance') {
        return 'noVariance';
    }
    if (matchingStatus === 'varianceApproved') {
        return 'varianceApproved';
    }
    return 'noVariance';
}

export async function linkedReceiptArray(
    invoice: xtremPurchasing.nodes.PurchaseInvoice,
): Promise<xtremPurchasing.nodes.PurchaseReceipt[] | undefined> {
    const receiptLines = await invoice.lines
        .map(async line => {
            const purchaseReceiptLine = await line.purchaseReceiptLine;
            if (purchaseReceiptLine !== null) {
                return (await (await purchaseReceiptLine.purchaseReceiptLine).document)._id;
            }
            return null;
        })
        .toArray();

    if (receiptLines.length) {
        const receiptQuery = await invoice.$.context
            .query(xtremPurchasing.nodes.PurchaseReceipt, {
                filter: { _id: { _in: receiptLines } },
            })
            .toArray();
        if (receiptQuery.length) {
            return receiptQuery;
        }
    }
    return undefined;
}

export async function updateHeaderNotesOnCreation(invoice: xtremPurchasing.nodes.PurchaseInvoice) {
    if (invoice.$.status === NodeStatus.added) {
        const purchaseReceiptArray = await linkedReceiptArray(invoice);
        if (purchaseReceiptArray && purchaseReceiptArray.length === 1) {
            const isTransferHeaderNote = await purchaseReceiptArray[0].isTransferHeaderNote;

            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(invoice);
                await setHeaderInternalNote(invoice, purchaseReceiptArray, currentNote);
            }
            await setHeaderSetupNote(invoice, isTransferHeaderNote, await purchaseReceiptArray[0].isTransferLineNote);
        }
    }
}

export async function updateLineNotesOnCreation(invoiceLine: xtremPurchasing.nodes.PurchaseInvoiceLine) {
    if (invoiceLine.$.status === NodeStatus.added) {
        const purchaseReceiptLine = await invoiceLine.purchaseReceiptLine;
        if (purchaseReceiptLine !== null) {
            const linkedDocumentLine = await purchaseReceiptLine.purchaseReceiptLine;
            if (await (await linkedDocumentLine.document).isTransferLineNote) {
                const currentNote = await getCurrentLineNote(invoiceLine);
                await setLineInternalNote(invoiceLine, linkedDocumentLine, currentNote);
            }
        }
    }
}

/**
 * returns the invoice status based on the invoiced quantity
 * @param line  a purchase order line or a purchase receipt line
 */
export async function getLineInvoiceStatus(
    line: xtremPurchasing.interfaces.InvoicedDocumentLine,
): Promise<xtremPurchasing.enums.PurchaseOrderInvoiceStatus | xtremPurchasing.enums.PurchaseReceiptInvoiceStatus> {
    if ((await line.invoicedQuantityInStockUnit) === 0) {
        return 'notInvoiced';
    }
    if ((await line.invoicedQuantityInStockUnit) < (await line.quantityInStockUnit)) {
        return 'partiallyInvoiced';
    }
    return 'invoiced';
}

/**
 *  Returns the new status and invoice status of a line based on the invoiced quantity
 * @param documentLine  a purchase order line or a purchase receipt line
 */
export async function getLineStatuses(documentLine: xtremPurchasing.interfaces.InvoicedDocumentLine): Promise<{
    invoiceStatus:
        | xtremPurchasing.enums.PurchaseOrderInvoiceStatus
        | xtremPurchasing.enums.PurchaseReceiptInvoiceStatus;
    lineStatus: xtremPurchasing.enums.PurchaseDocumentStatus;
}> {
    const invoiceStatus = await getLineInvoiceStatus(documentLine);
    if (documentLine instanceof xtremPurchasing.nodes.PurchaseReceiptLine) {
        return {
            invoiceStatus,
            lineStatus: xtremPurchasing.functions.PurchaseReceiptLib.computeReceiptLineStatus({
                status: await documentLine.status,
                stockTransactionStatus: await documentLine.stockTransactionStatus,
                lineInvoiceStatus: invoiceStatus,
                lineReturnStatus: await documentLine.lineReturnStatus,
                remainingQuantityInStockUnit: await documentLine.remainingQuantityInStockUnit,
            }),
        };
    }

    if ((await documentLine.invoicedQuantityInStockUnit) === 0) {
        return { invoiceStatus, lineStatus: 'pending' };
    }

    if (
        (await documentLine.invoicedQuantityInStockUnit) >= (await documentLine.quantityInStockUnit) ||
        (await documentLine.status) === 'closed'
    ) {
        return {
            invoiceStatus,
            lineStatus: 'closed',
        };
    }

    return {
        invoiceStatus,
        lineStatus: 'inProgress',
    };
}

export async function updateDocumentLineStatuses(
    document: xtremPurchasing.interfaces.InvoicedDocument,
    documentLines:
        | Collection<xtremPurchasing.interfaces.InvoicedDocumentLine>
        | AsyncArrayReader<xtremPurchasing.interfaces.InvoicedDocumentLine>,
) {
    let result = false;
    await documentLines.forEach(async documentLine => {
        const { invoiceStatus, lineStatus } = await getLineStatuses(documentLine);
        loggers.order.debug(() => ` invoiceStatus=${invoiceStatus} lineStatus=${lineStatus}`);
        await loggers.order.debugAsync(
            async () =>
                ` oldLineInvoiceStatus=${await documentLine.lineInvoiceStatus} oldLineStatus=${await documentLine.status}`,
        );
        if (invoiceStatus !== (await documentLine.lineInvoiceStatus) || lineStatus !== (await documentLine.status)) {
            await document.$.set({
                lines: [
                    {
                        _action: 'update',
                        _id: documentLine._id,
                        lineInvoiceStatus: invoiceStatus,
                        status: lineStatus,
                        ...(documentLine instanceof xtremPurchasing.nodes.PurchaseOrderLine
                            ? { isUsingFunctionToClose: lineStatus === 'closed' }
                            : {}),
                    },
                ],
            });
            result = true;
        }
    });
    return result;
}

export async function repost(
    context: Context,
    purchaseInvoice: xtremPurchasing.nodes.PurchaseInvoice,
    documentData: xtremPurchasing.interfaces.financeIntegration.PurchaseInvoiceCreditMemoRepost,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    if (!['notRecorded', 'failed'].includes(await purchaseInvoice.financeIntegrationStatus)) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase-invoice__cant_repost_purchasing_invoice_when_status_is_not_failed',
                "You can only repost a purchase invoice if the status is 'Failed.'",
            ),
        );
    }

    loggers.invoiceLib.info(`Reposting purchase invoice ${await purchaseInvoice.number}`);

    const financeTransactions = (
        await xtremFinanceData.nodes.FinanceTransaction.getPostingStatusData(context, await purchaseInvoice.number)
    ).filter(
        financeTransaction =>
            ['postingError', 'generationError'].includes(financeTransaction.status) &&
            !financeTransaction.hasSourceForDimensionLines,
    );

    const targetDocumentTypes = financeTransactions.map(financeTransaction => {
        return financeTransaction.documentType;
    });

    loggers.invoiceLib.info(`Target document types are ${JSON.stringify(targetDocumentTypes)}`);

    const hasTaxes = documentData.lines.every(line => {
        return !!line.uiTaxes;
    });
    const isAvalaraEnabled = (await purchaseInvoice.taxEngine) === 'avalaraAvaTax';

    const financeTransaction = targetDocumentTypes.includes('accountsPayableInvoice')
        ? await context.read(xtremFinanceData.nodes.FinanceTransaction, {
              _id: financeTransactions.filter(line => line.documentType === 'accountsPayableInvoice').at(0)?._id ?? 0,
          })
        : null;

    // If the user changed the tax data, avalara is not enabled and the invoice is already posted, we need to recreate
    // the tax information on the ap-ap invoice and before that we need to remove the accounting staging data
    // related with that ap-ar invoice
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        loggers.invoiceLib.info(`Document has taxes and Avalara is not enabled`);

        await xtremFinanceData.functions.AccountingEngineCommon.removeAccountingStagingForApArInvoice(
            context,
            financeTransaction,
        );
    }

    // Prepare the lines to be updated
    const updateLines = documentData.lines.map(documentLine => {
        return {
            _action: 'update' as UpdateAction,
            _id: documentLine.baseDocumentLineSysId,
            storedAttributes: documentLine.storedAttributes,
            storedDimensions: documentLine.storedDimensions,
            uiTaxes: hasTaxes ? documentLine.uiTaxes : undefined,
        };
    });

    // Update and save the purchase invoice
    await purchaseInvoice.$.set({
        supplierDocumentNumber: documentData.header.supplierDocumentNumber,
        forceUpdateForFinance: true,
        lines: updateLines,
        wasTaxDataChanged: hasTaxes,
        totalTaxAmount: documentData.header.totalTaxAmount ?? undefined,
    });
    if (
        documentData.header.paymentData &&
        ((await (await purchaseInvoice.paymentTracking)?.status) ?? 'notPaid') === 'notPaid'
    ) {
        await purchaseInvoice.$.set({ ...documentData.header.paymentData });
    }
    if (hasTaxes && !isAvalaraEnabled) {
        const taxes = await asyncArray(documentData.header?.taxes ?? [])
            .map(async tax => {
                const _action: any = tax.$.getRawPropertyValue('_action');
                const { _id } = tax;
                return _action === 'delete'
                    ? { _action, _id }
                    : { _action, ...(await tax.$.payload({ withIds: true, inputValuesOnly: true })) };
            })
            .toArray();

        await purchaseInvoice.$.set({ taxes });
    }
    await purchaseInvoice.$.save();

    // If the user changed the tax data, avalara is not enabled and the invoice is already posted, we need to recreate
    // the tax information on the ap-ap invoice on the staging table
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        await xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStagingForApArInvoice(context, {
            financeTransaction,
            document: purchaseInvoice,
            lines: await purchaseInvoice.lines.toArray(),
            replyTopic: 'PurchaseInvoice/accountingInterface',
        });
    }

    // send notification in order to update a staging table entry for the accounting engine and recreate the finance documents
    // send notification in order to update a staging table entry for the accounting engine
    await xtremFinanceData.functions.purchaseSalesInvoiceCreditMemoUpdateNotification(context, {
        targetDocumentTypes,
        document: purchaseInvoice as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
        lines: (await purchaseInvoice.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[],
        documentType: 'purchaseInvoice',
        replyTopic: 'PurchaseInvoice/accountingInterface',
    });
    return {
        wasSuccessful: true,
        message: context.localize(
            '@sage/xtrem-purchasing/nodes__purchase-invoice__document_was_posted',
            'The purchase invoice posted.',
        ),
    };
}
