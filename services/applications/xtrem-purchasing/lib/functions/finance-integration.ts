import type { Collection, Context } from '@sage/xtrem-core';
import { ValidationSeverity, asyncArray } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremPurchasing from '../index';
import { getInvoiceInstance } from './purchase-receipt-lib';
import { getCreditMemoInstance } from './purchase-return-lib';

// -------------------------------------------------------------------
// ------------------------- Purchase Receipt ------------------------
// -------------------------------------------------------------------

/**
 * For a given purchase receipt, we run the controls for finance integration, including controlling the future invoice,
 * and create the result as a xtremFinanceData.interfaces.MutationResult interface.
 * We need to add the data reagarding stock movements because at this point we don't have
 * stock movements done on the system for the document. They are added just to run the controls.
 * @param context: A context
 * @param purchaseReceipt The purchase receipt
 * @param documentNumber The document number. We don't get it from the receipt because it my not calculated yet because of deferred actions
 * @return A MutationResult object
 */
export async function purchaseReceiptControlAndCreateMutationResult(
    context: Context,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
    documentNumber?: string,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    let financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
        wasSuccessful: false,
        message: '',
    };
    const receipt: xtremFinanceData.interfaces.PurchaseFinanceDocument = purchaseReceipt;

    const { lines }: { lines: Collection<xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine> } =
        purchaseReceipt;

    const company = await (await purchaseReceipt.financialSite).legalCompany;

    const isCompanyWithNonAbsorbedAmountsPosting = await company.doNonAbsorbedPosting;
    const doStockPosting = await company.doStockPosting;

    if (doStockPosting) {
        // adding fake data related with stock just to consider the
        // accounts that are related with the stock movement.
        await lines
            .filter(async line => (await (await line.item).isStockManaged) && !(await line.stockMovements.length))
            .forEach(async line => {
                const orderCost = (await line.orderCost) || 0;
                let valuedCost = (await line.valuedCost) || 0;
                if (valuedCost === 0) valuedCost = orderCost;
                line.stockMovementArray = [
                    {
                        movementAmount: valuedCost,
                        orderAmount: orderCost,
                        nonAbsorbedAmount: !isCompanyWithNonAbsorbedAmountsPosting ? 0 : valuedCost,
                    },
                ];
            });

        const purchaseReceiptControlFromNotificationPayloadErrors = (
            await xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
                context,
                await xtremPurchasing.functions.FinanceIntegration.getPurchaseReceiptNotificationPayload(context, {
                    document: receipt,
                    lines: await lines.toArray(),
                    isJustChecking: true,
                    documentNumber,
                }),
            )
        ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

        if (purchaseReceiptControlFromNotificationPayloadErrors.length) {
            financeIntegrationCheckResult.message = purchaseReceiptControlFromNotificationPayloadErrors
                .map(message => `* ${message.message}`)
                .join('\n');
            financeIntegrationCheckResult.validationMessages = purchaseReceiptControlFromNotificationPayloadErrors;
            return financeIntegrationCheckResult;
        }
    }

    const invoices = await getInvoiceInstance(context, purchaseReceipt);
    await asyncArray(invoices).forEach(async invoice => {
        if (!financeIntegrationCheckResult.message) {
            financeIntegrationCheckResult = await xtremPurchasing.nodes.PurchaseInvoice.financeIntegrationCheck(
                context,
                invoice,
            );
        }
    });

    if (!financeIntegrationCheckResult.message) {
        financeIntegrationCheckResult.wasSuccessful = true;
    }
    return financeIntegrationCheckResult;
}

/**
 * Purchase receipt source lines (PO lines)
 * @param lines An array of purchase receipt lines
 * @return FinanceIntegrationDocumentSourceProperties[]
 */
async function getPurchaseReceiptSourceLines(
    lines: xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine[],
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[]> {
    const sourceLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[] = await asyncArray(
        lines,
    )
        .filter(
            async line =>
                (await line.stockMovements.some(
                    async stockMovement => !!(await (await stockMovement.reasonCode)?.landedCostAdjustment),
                )) && !!(await (await line.purchaseOrderLine)?.purchaseOrderLine),
        )
        .map(async line => {
            const purchaseOrderLine = await (await line.purchaseOrderLine)?.purchaseOrderLine;
            return {
                sourceDocumentType: 'purchaseOrder',
                sourceDocumentSysId: (await purchaseOrderLine?.documentId) || 0,
                sourceDocumentNumber: (await purchaseOrderLine?.documentNumber) || '',
                isSourceForDimension: true,
            } as xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties;
        })
        .toArray();

    return _.uniqWith(sourceLines, _.isEqual);
}

async function getLandedCostLinesAllocatedToPOLine(
    context: Context,
    line: xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine,
): Promise<xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine[]> {
    if (!line.stockMovementArray || !(await line.purchaseOrderLine)) {
        return [];
    }

    // Get landed costs allocated to po line linked to the receipt line
    const landedCostAllocations = await context
        .query(xtremLandedCost.nodes.LandedCostAllocation, {
            filter: { allocatedDocumentLine: (await (await line.purchaseOrderLine)?.purchaseOrderLine)?._id || 0 },
        })
        .toArray();

    return landedCostAllocations.length
        ? (
              await asyncArray(line.stockMovementArray)
                  .map(async stockMovement => {
                      const reasonCode = await context
                          .query(xtremMasterData.nodes.ReasonCode, {
                              first: 1,
                              filter: { landedCostAdjustment: true },
                          })
                          .at(0);
                      return asyncArray(landedCostAllocations)
                          .map(async allocation => {
                              return {
                                  ...stockMovement,
                                  documentLine: line,
                                  sourceDocumentLine: await (await allocation.line).documentLine,
                                  reasonCode,
                                  item: await line.item,
                                  storedDimensions: {},
                                  storedAttributes: {},
                                  computedAttributes: {},
                              } as xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine;
                          })
                          .toArray();
                  })
                  .toArray()
          ).flat(1)
        : [];
}

async function getPurchaseReceiptLineStockMovements(
    context: Context,
    params: {
        line: xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine;
        stockJournalRecords: xtremStockData.nodes.StockJournal[];
        companyDoesNonAbsorbedPosting: boolean;
        isJustChecking: boolean;
    },
): Promise<xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine[]> {
    const stockMovements = params.stockJournalRecords.length
        ? await asyncArray(params.stockJournalRecords)
              .filter(async stockJournalRecord => (await stockJournalRecord.documentLine)._id === params.line._id)
              .toArray()
        : await params.line.stockMovements.toArray();

    return params.isJustChecking
        ? getLandedCostLinesAllocatedToPOLine(context, params.line)
        : asyncArray(stockMovements)
              // only land cost related adjustment. Also, only if we have a movement amount or a non absorbed amount and company posts non absorbed amounts
              .filter(
                  async stockMovement =>
                      !!(await (await stockMovement.reasonCode)?.landedCostAdjustment) &&
                      (!!(await stockMovement.movementAmount) ||
                          (!!(await stockMovement.nonAbsorbedAmount) && params.companyDoesNonAbsorbedPosting)),
              )
              .map(async stockJournal => {
                  return {
                      item: await stockJournal.item,
                      reasonCode: await stockJournal.reasonCode,
                      documentLine: await stockJournal.documentLine,
                      sourceDocumentLine: await stockJournal.sourceDocumentLine,
                      movementAmount: await stockJournal.movementAmount,
                      nonAbsorbedAmount: await stockJournal.nonAbsorbedAmount,
                      storedDimensions: {},
                      storedAttributes: {},
                      computedAttributes: {},
                  };
              })
              .toArray();
}

/**
 * Payload corresponding to the landed cost adjustment for a purchase receipt line previously assigned to a purchase order line
 * @param context Context
 * @param document An object that implements PurchaseFinanceDocument to get header related properties from a document
 * @param line An object that implements ReceiptDocumentLine to get line related properties
 * @param documentNumber The document number. We don't get it from the receipt because it my not calculated yet because of deferred actions
 * @return FinanceIntegrationDocumentLine[]
 */
async function purchaseReceiptLineNotificationStockManaged(
    context: Context,
    params: {
        document: xtremFinanceData.interfaces.PurchaseFinanceDocument;
        line: xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine;
        stockJournalRecords: xtremStockData.nodes.StockJournal[];
        isJustChecking: boolean;
        documentNumber?: string;
    },
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    const companyDoesNonAbsorbedPosting = await (
        await (
            await params.document.financialSite
        ).legalCompany
    ).doNonAbsorbedPosting;

    const companyDoesLandedCostGoodsInTransitPosting = await (
        await (
            await (
                await params.document.financialSite
            ).legalCompany
        ).legislation
    ).doLandedCostGoodsInTransitPosting;

    return (
        await asyncArray(
            await getPurchaseReceiptLineStockMovements(context, {
                line: params.line,
                stockJournalRecords: params.stockJournalRecords,
                companyDoesNonAbsorbedPosting,
                isJustChecking: params.isJustChecking,
            }),
        )
            .map(async stockMovement => {
                // common data for the stock and stock in transint
                const commonData = {
                    movementType: 'stockJournal',
                    documentNumber: params.isJustChecking
                        ? ''
                        : (await stockMovement.documentLine.documentNumber) || '',
                    currencySysId: (await (await params.document.financialSite).currency)._id,
                    companyFxRate: 1,
                    companyFxRateDivisor: 1,
                    fxRateDate: (await params.document.documentDate).toString(),
                    itemSysId: stockMovement.item._id,
                };

                const stockMovementAmounts: xtremFinanceData.interfaces.AccountingStagingAmount[] = [];

                if (stockMovement.movementAmount !== 0) {
                    stockMovementAmounts.push({
                        amount: stockMovement.movementAmount,
                        amountType: 'landedCostAdjustmentAmount',
                        documentLineType: 'documentLine',
                    });
                }

                if (stockMovement.nonAbsorbedAmount !== 0 && companyDoesNonAbsorbedPosting) {
                    stockMovementAmounts.push({
                        amount: stockMovement.nonAbsorbedAmount,
                        amountType: 'landedCostAdjustmentNonabsorbedAmount',
                        documentLineType: 'documentLine',
                    });
                }

                // stock - landed cost adjustment payload
                const landedCostStockAdjustment = stockMovementAmounts.length
                    ? [
                          {
                              ...commonData,
                              baseDocumentLineSysId: params.line._id,
                              sourceBaseDocumentLineSysId: stockMovement.sourceDocumentLine?._id,
                              sourceDocumentNumber: (await stockMovement.sourceDocumentLine?.documentNumber) || '',
                              sourceDocumentType:
                                  _.camelCase((await stockMovement.sourceDocumentLine?.document)?.$.factory.name) ||
                                  null,
                              uiSourceDocumentNumber: params.documentNumber, // The pr number to show when we are on the pr page
                              supplierSysId: (await params.document.billBySupplier)._id,
                              amounts: stockMovementAmounts,
                              storedDimensions: (await params.line.storedDimensions) || {},
                              storedAttributes: {
                                  ...((await params.line
                                      .storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                                  ...((await params.line.computedAttributes) as {}),
                              },
                          } as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
                      ]
                    : [];

                // stock in transit - landed cost adjustment done on PO reversal payload
                if (companyDoesLandedCostGoodsInTransitPosting) {
                    // amounts for the stock in transit adjustment
                    const stockInTransitAmounts: xtremFinanceData.interfaces.AccountingStagingAmount[] = [];

                    if (stockMovement.movementAmount !== 0) {
                        stockInTransitAmounts.push({
                            amount: stockMovement.movementAmount,
                            amountType: 'landedCostStockInTransitAdjustmentAmount',
                            documentLineType: 'documentLine',
                        });
                    }
                    if (stockMovement.nonAbsorbedAmount !== 0 && companyDoesNonAbsorbedPosting) {
                        stockInTransitAmounts.push({
                            amount: stockMovement.nonAbsorbedAmount,
                            amountType: 'landedCostStockInTransitAdjustmentNonabsorbedAmount',
                            documentLineType: 'documentLine',
                        });
                    }

                    const purchaseOrderLine = await (await params.line.purchaseOrderLine)?.purchaseOrderLine;

                    // stock in transit - reverse of the landed cost adjustment done on PO
                    const purchaseInvoiceId = await (
                        await (
                            await (
                                await (
                                    await params.line.landedCostLines?.at(0)
                                )?.landedCostAllocation
                            )?.line
                        )?.documentLine
                    )?.documentId;
                    const purchaseInvoice = purchaseInvoiceId
                        ? await context.tryRead(xtremPurchasing.nodes.PurchaseInvoice, { _id: purchaseInvoiceId })
                        : null;
                    const landedCostStockInTransitAdjustment = stockInTransitAmounts.length
                        ? [
                              {
                                  ...commonData,
                                  baseDocumentLineSysId: stockMovement.sourceDocumentLine?._id || 0,
                                  sourceBaseDocumentLineSysId: purchaseOrderLine?._id,
                                  uiSourceDocumentNumber: (await (await purchaseOrderLine?.document)?.number) || '',
                                  uiBaseDocumentLineSysId: stockMovement.documentLine._id,
                                  sourceDocumentNumber: (await (await purchaseOrderLine?.document)?.number) || '',
                                  sourceDocumentType: 'purchaseOrder',
                                  supplierSysId: (await purchaseInvoice?.billBySupplier)?._id || 0,
                                  amounts: stockInTransitAmounts,
                                  storedDimensions: (await purchaseOrderLine?.storedDimensions) || {},
                                  storedAttributes: {
                                      ...(((await purchaseOrderLine?.storedAttributes) ||
                                          {}) as xtremMasterData.interfaces.StoredAttributes),
                                      ...((await purchaseOrderLine?.computedAttributes) || {}),
                                  },
                              } as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
                          ]
                        : [];

                    return landedCostStockAdjustment.concat(landedCostStockInTransitAdjustment);
                }
                return landedCostStockAdjustment;
            })
            .toArray()
    ).flat(1);
}

/**
 * Common part to create the notifications for the finance integration of a Purchase receipt or return
 * @param context Context
 * @param document An object that implements FinanceOriginDocument or PurchaseFinanceDocument to get header related properties from a document
 * @param line An object that implements FinanceOriginDocumentLine or ReturnDocumentLine to get line related properties from a document
 * @param sourceDocumentNumber a source document number if needed in notificaton else ''
 * @param documentLineType either purchaseReturnLine or purchaseReceiptLine
 * @return FinanceIntegrationDocumentLine
 */
async function purchaseLineNotificationStockManaged(
    context: Context,
    params: {
        document: xtremFinanceData.interfaces.PurchaseFinanceDocument;
        line:
            | xtremFinanceData.interfaces.ReturnDocumentLine
            | xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine;
        sourceDocumentNumber: string;
        sourceDocumentType: xtremFinanceData.enums.SourceDocumentType | null;
        documentLineType: 'PurchaseReturnLine' | 'PurchaseReceiptLine';
    },
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    // function getStockMovementsAmounts excludes the stock movements that are related with landed costs
    const { movementAmount, orderAmount } = await xtremFinanceData.functions.getStockMovementsAmounts(params.line);
    const itemSite = await xtremFinanceData.functions.getItemSite(
        context,
        await params.line.site,
        await params.line.item,
    );
    const lineAmount = xtremFinanceData.functions.getStockLineAmount(params.documentLineType, movementAmount);
    const amounts: xtremFinanceData.interfaces.AccountingStagingAmount[] = [
        { amountType: 'amount', amount: lineAmount, documentLineType: 'documentLine' },
    ]; // always create an amount of type 'amount'
    const valuationMethod = itemSite ? await itemSite.valuationMethod : 'standardCost';
    // for purchase receipt and return we need an additional amount of type 'varianceAmount' if 'standardCost' and
    // orderAmount different from movementAmount
    if (valuationMethod === 'standardCost' && orderAmount !== movementAmount) {
        const varAmount =
            params.documentLineType === 'PurchaseReturnLine'
                ? -(orderAmount - movementAmount)
                : orderAmount - movementAmount;
        amounts.push({
            amountType: 'varianceAmount',
            amount: varAmount,
            documentLineType: 'documentLine',
        });
    }

    return [
        {
            baseDocumentLineSysId: params.line._id,
            movementType: 'stockJournal',
            sourceDocumentNumber: params.sourceDocumentNumber,
            sourceDocumentType: params.sourceDocumentType,
            fxRateDate: ((await params.document.fxRateDate) || (await params.document.documentDate)).toString(),
            currencySysId: (await (await params.document.financialSite).currency)._id, // always take currency of financial site
            companyFxRate: 1.0, // no currency conversion for 'stockJournal'
            companyFxRateDivisor: 1.0,
            itemSysId: (await params.line.item)._id,
            supplierSysId: (await params.document.billBySupplier)._id,
            amounts,
            storedDimensions: (await params.line.storedDimensions) || {},
            storedAttributes: {
                ...((await params.line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                ...((await params.line.computedAttributes) as {}),
            },
        } as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
    ];
}

/**
 * Common part to create all the notifications for the finance integration of a Purchase receipt or return
 * @param document An object that implements FinanceOriginDocument or PurchaseFinanceDocument to get header related properties from a document
 * @param line An object that implements FinanceOriginDocumentLine or ReturnDocumentLine to get line related properties from a document
 * @return FinanceIntegrationDocumentLine
 */
async function purchaseLineNotificationNotStockManaged(
    document: xtremFinanceData.interfaces.PurchaseFinanceDocument | xtremFinanceData.interfaces.FinanceOriginDocument,
    line: xtremFinanceData.interfaces.ReturnDocumentLine | xtremFinanceData.interfaces.FinanceOriginDocumentLine,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    const nonStockAmounts: xtremFinanceData.interfaces.AccountingStagingAmount[] = [
        {
            amountType: 'amountExcludingTax',
            amount: (await line.amountExcludingTax) || 0,
            documentLineType: 'documentLine',
        },
    ];
    return [
        {
            baseDocumentLineSysId: line._id,
            movementType: 'document',
            sourceDocumentNumber: '',
            fxRateDate: ((await document.fxRateDate) || (await document.documentDate)).toString(),
            currencySysId: (await document.transactionCurrency)._id,
            companyFxRate: (await document.companyFxRate) || 1,
            companyFxRateDivisor: (await document.companyFxRateDivisor) || 1,
            itemSysId: (await line.item)._id,
            amounts: nonStockAmounts,
            storedDimensions: (await line.storedDimensions) || {},
            storedAttributes: {
                ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                ...((await line.computedAttributes) as {}),
            },
        },
    ];
}

/**
 * Gets a notification payload lines when we process the complete document
 * This is the most common case, there's only one exception.
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements ReceiptDocumentLine to get line related properties from a document
 * @param isJustChecking If set to true we assume that notification is not really being sent, if just to check the payload
 */
async function purchaseReceiptPayloadLinesFromCompleteReceipt(
    context: Context,
    params: xtremPurchasing.interfaces.financeIntegration.GetPurchaseReceiptNotificationPayloadParameters,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    return (
        await asyncArray(
            await asyncArray(params.lines)
                .filter(
                    async line =>
                        !xtremFinanceData.sharedFunctions.common.legislationsWithoutRevenueRecognition.includes(
                            await (
                                await (
                                    await (
                                        await params.document.financialSite
                                    ).legalCompany
                                ).legislation
                            ).id,
                        ) || (await line.item).isStockManaged,
                )
                .toArray(),
        )
            .map(async line => {
                if (await (await line.item).isStockManaged) {
                    const purchaseLineNotificationStockManagedPayload =
                        // payload for lines managed in stock
                        await purchaseLineNotificationStockManaged(context, {
                            document: params.document,
                            line,
                            sourceDocumentNumber: '',
                            sourceDocumentType: null,
                            documentLineType: 'PurchaseReceiptLine',
                        });
                    return purchaseLineNotificationStockManagedPayload.concat(
                        // payload for lines managed in stock related with landed cost
                        // previously allocated to a purchase order
                        await purchaseReceiptLineNotificationStockManaged(context, {
                            document: params.document,
                            line,
                            stockJournalRecords: [],
                            isJustChecking: params.isJustChecking,
                            documentNumber: params.documentNumber,
                        }),
                    );
                }
                // payload for lines not managed in stock
                return purchaseLineNotificationNotStockManaged(params.document, line);
            })
            .toArray()
    ).flat(1);
}

/**
 * Gets a notification payload lines when we have a correction.
 * The current case is when we close a po line that had landed costs
 * assigned to it but the po is not fully received and we close it.
 * In this case, the remaining amounts are distriuted among the existing receipts
 * which originates the correction. In this case we don't process the all receipt,
 * but only the receipt lines that were afected by the correction.
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements ReceiptDocumentLine to get line related properties from a document
 * @param isJustChecking If set to true we assume that notification is not really being sent, if just to check the payload
 * @param stockJournalRecords An array of xtremStockData.nodes.StockJournal that were created by the correction
 */
async function purchaseReceiptPayloadLinesFromReceiptCorrection(
    context: Context,
    params: xtremPurchasing.interfaces.financeIntegration.GetPurchaseReceiptNotificationPayloadParameters,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    return (
        await asyncArray(params.lines)
            .map(receiptLine => {
                return purchaseReceiptLineNotificationStockManaged(context, {
                    document: params.document,
                    line: receiptLine,
                    stockJournalRecords:
                        params.stockJournalRecords?.filter(
                            async stockJournalRecord => (await stockJournalRecord.documentLine)._id === receiptLine._id,
                        ) || [],
                    isJustChecking: params.isJustChecking,
                });
            })
            .toArray()
    ).flat(1);
}

/**
 * Gets a notification payload needed for the finance integration of a Purchase Receipt
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @param isJustChecking If set to true we assume that notification is not really being sent, if just to check the payload
 */
export async function getPurchaseReceiptNotificationPayload(
    context: Context,
    params: xtremPurchasing.interfaces.financeIntegration.GetPurchaseReceiptNotificationPayloadParameters,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({
            document: params.document,
            isJustChecking: params.isJustChecking,
            number: params.documentNumber,
        });

    return [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'purchaseReceipt',
            targetDocumentType: 'journalEntry',
            sourceLines: (await (
                await (
                    await (
                        await params.document.financialSite
                    ).legalCompany
                ).legislation
            ).doLandedCostGoodsInTransitPosting)
                ? await getPurchaseReceiptSourceLines(params.lines)
                : [],
            documentLines: params.stockJournalRecords?.length
                ? await purchaseReceiptPayloadLinesFromReceiptCorrection(context, params)
                : await purchaseReceiptPayloadLinesFromCompleteReceipt(context, {
                      ...params,
                      documentNumber: accountingStagingCommonPayload.documentNumber,
                  }),
        },
    ];
}

/**
 * Creates all the notifications for the finance integration of a Purchase receipt
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @param isJustChecking If set to true we assume that notification is not really being sent, if just to check the payload
 * @return The number of notifications created
 */
export async function purchaseReceiptNotification(
    context: Context,
    document: xtremFinanceData.interfaces.PurchaseFinanceDocument,
    lines: xtremPurchasing.interfaces.financeIntegration.ReceiptDocumentLine[],
    stockJournalRecords?: xtremStockData.nodes.StockJournal['_id'][],
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    // we only have stock journal records when the notification is triggered
    // by a po line closing. in this case, the receipt lines to consider should
    // only be the ones linked to the stock journal records
    const stockJournals = await asyncArray(stockJournalRecords || [])
        .map(stockJournalRecord => context.read(xtremStockData.nodes.StockJournal, { _id: stockJournalRecord }))
        .toArray();

    const uniqueReceipLineIds = _.uniq(
        await asyncArray(stockJournals)
            .map(async stockMovement => (await stockMovement.documentLine)._id)
            .toArray(),
    );

    const receiptLines = stockJournals.length
        ? await asyncArray(uniqueReceipLineIds)
              .map(receipLineId => {
                  return context.read(xtremPurchasing.nodes.PurchaseReceiptLine, {
                      _id: receipLineId,
                  });
              })
              .toArray()
        : lines;

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getPurchaseReceiptNotificationPayload(context, {
            document,
            lines: receiptLines,
            isJustChecking: false,
            stockJournalRecords: stockJournals,
        }),
        replyTopic: 'PurchaseReceipt/accountingInterface',
        isUpdate: false,
    });
}

// -------------------------------------------------------------------
// ------------------------- Purchase Return -------------------------
// -------------------------------------------------------------------

/**
 * For a given purchase return, we run the controls for finance integration.
 * @param context: A context
 * @param purchaseReturn The purchase return
 * @param isJustChecking If set to true we assume that notification is not really being sent, if just to check the payload
 * @return A CreateFinanceDocumentsReturn object
 */
export async function purchaseReturnControlFromNotificationPayload(
    context: Context,
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
    isJustChecking: boolean,
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    const pReturn: xtremFinanceData.interfaces.PurchaseFinanceDocument = purchaseReturn;

    const lines: xtremFinanceData.interfaces.ReturnDocumentLine[] = await purchaseReturn.lines.toArray();

    const company = await context.read(xtremSystem.nodes.Company, {
        id: await (await (await pReturn.financialSite).legalCompany).id,
    });
    const isCompanyWithNonAbsorbedAmountsPosting = await company.doNonAbsorbedPosting;
    const doStockPosting = await company.doStockPosting;

    if (doStockPosting) {
        await asyncArray(lines).forEach(async line => {
            if (await (await line.item).isStockManaged) {
                if (!(await line.stockMovements.length)) {
                    line.stockMovementArray = [
                        {
                            movementAmount: (await line.amountExcludingTax) || 0,
                            orderAmount: (await line.amountExcludingTax) || 0,
                            nonAbsorbedAmount: !isCompanyWithNonAbsorbedAmountsPosting
                                ? 0
                                : (await line.amountExcludingTax) || 0,
                        },
                    ];
                }
            }
        });

        return xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
            context,
            await xtremPurchasing.functions.FinanceIntegration.getPurchaseReturnNotificationPayload(
                context,
                pReturn,
                lines,
                isJustChecking,
            ),
        );
    }
    return {
        documentsCreated: [],
        validationMessages: [],
    };
}

/**
 * For a given purchase return, we run the controls for finance integration
 * and create the result as a xtremFinanceData.interfaces.MutationResult interface.
 * @param context: A context
 * @param purchaseReturn The purchase return receipt
 * @return A MutationResult object
 */
export async function purchaseReturnControlAndCreateMutationResult(
    context: Context,
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    let financeIntegrationCheckResult: xtremFinanceData.interfaces.MutationResult = {
        wasSuccessful: false,
        message: '',
    };

    const purchaseReturnControlFromNotificationPayloadErrors = (
        await xtremPurchasing.functions.FinanceIntegration.purchaseReturnControlFromNotificationPayload(
            context,
            purchaseReturn,
            true,
        )
    ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

    if (purchaseReturnControlFromNotificationPayloadErrors.length) {
        return {
            wasSuccessful: false,
            message: purchaseReturnControlFromNotificationPayloadErrors
                .map(message => '* '.concat(message.message))
                .join('\n'),
        };
    }

    const creditMemos = await getCreditMemoInstance(context, purchaseReturn);
    await asyncArray(creditMemos).forEach(async creditMemo => {
        if (!financeIntegrationCheckResult.message) {
            financeIntegrationCheckResult = await xtremPurchasing.nodes.PurchaseCreditMemo.financeIntegrationCheck(
                context,
                creditMemo,
            );
        }
    });

    if (!financeIntegrationCheckResult.message) {
        financeIntegrationCheckResult.wasSuccessful = true;
    }
    return financeIntegrationCheckResult;
}

/**
 * Creates the payload of all the notifications for the finance integration of a Purchase return
 * @param context Context
 * @param document An object that implements PurchaseFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements ReturnDocumentLine to get line related properties from a document
 * @param isJustChecking If set to true we assume that notification is not really being sent, if just to check the payload
 * @return The number of notifications created
 */
export async function getPurchaseReturnNotificationPayload(
    context: Context,
    document: xtremFinanceData.interfaces.PurchaseFinanceDocument,
    lines: xtremFinanceData.interfaces.ReturnDocumentLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    return [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'purchaseReturn',
            targetDocumentType: 'journalEntry',
            documentLines: (
                await asyncArray(
                    await asyncArray(lines)
                        .filter(
                            async line =>
                                !xtremFinanceData.sharedFunctions.common.legislationsWithoutRevenueRecognition.includes(
                                    await (
                                        await (
                                            await (
                                                await document.financialSite
                                            ).legalCompany
                                        ).legislation
                                    ).id,
                                ) || (await line.item).isStockManaged,
                        )
                        .toArray(),
                )
                    .map(async line => {
                        if (await (await line.item).isStockManaged) {
                            return purchaseLineNotificationStockManaged(context, {
                                document,
                                line,
                                sourceDocumentNumber: !isJustChecking ? await line.sourceDocumentNumber : '',
                                sourceDocumentType: !isJustChecking ? await line.sourceDocumentType : null,
                                documentLineType: 'PurchaseReturnLine',
                            });
                        }

                        return purchaseLineNotificationNotStockManaged(document, line);
                    })
                    .toArray()
            ).flat(2),
        },
    ];
}

/**
 * Creates all the notifications for the finance integration of a Purchase return
 * @param context Context
 * @param document An object that implements PurchaseFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements ReturnDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function purchaseReturnNotification(
    context: Context,
    document: xtremFinanceData.interfaces.PurchaseFinanceDocument,
    lines: xtremFinanceData.interfaces.ReturnDocumentLine[],
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getPurchaseReturnNotificationPayload(context, document, lines),
        replyTopic: 'PurchaseReturn/accountingInterface',
        isUpdate: false,
    });
}

// -------------------------------------------------------------------
// ------------------------- Purchase Invoice ------------------------
// -------------------------------------------------------------------

/**
 * For a given purchase invoice or credit memo, we run the controls for finance integration.
 * We need to add the data regarding stock movements because at this point we don't have
 * stock movements done on the system for the document. They are added just to run the controls.
 * @param context: A context
 * @param document The purchase invoice or credit memo
 * @param documentType The document type
 * @return A MutationResult object
 */
export async function purchaseInvoiceCreditMemoControlFromNotificationPayloadErrors(
    context: Context,
    document: xtremPurchasing.nodes.PurchaseInvoice | xtremPurchasing.nodes.PurchaseCreditMemo,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    const purchaseDocument: xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax = document;

    const lines: xtremFinanceData.interfaces.InvoiceDocumentLine[] = await document.lines.toArray();

    // adding fake data related with stock just to consider the
    // accounts that are related with the stock movement.

    const company = await context.read(xtremSystem.nodes.Company, {
        id: await (await (await purchaseDocument.financialSite).legalCompany).id,
    });
    const isCompanyWithNonAbsorbedAmountsPosting = await company.doNonAbsorbedPosting;
    const doStockPosting = await company.doStockPosting;

    if (doStockPosting) {
        // We only add stock movement data for stock managed items or for lines with landed cost allocations
        await asyncArray(lines)
            .filter(
                async line =>
                    (await (await line.item).isStockManaged) || !!(await (await line.landedCost)?.allocations.length),
            )
            .forEach(async line => {
                line.stockMovementArray = [
                    {
                        movementAmount: await line.amountExcludingTax,
                        nonAbsorbedAmount: !isCompanyWithNonAbsorbedAmountsPosting ? 0 : await line.amountExcludingTax,
                    },
                ];
            });

        const purchaseCreditMemoControlFromNotificationPayloadErrors = (
            await xtremFinanceData.functions.FinanceDocumentGeneration.financeDownstreamDocumentControlFromNotificationPayload(
                context,
                await xtremPurchasing.functions.FinanceIntegration.getPurchaseInvoiceCreditMemoNotificationPayload(
                    context,
                    {
                        document: purchaseDocument,
                        lines: await document.lines.toArray(),
                        documentType,
                        targetDocumentType: 'accountsPayableInvoice',
                    },
                    true,
                ),
            )
        ).validationMessages.filter(validationMessage => validationMessage.type === ValidationSeverity.error);

        if (purchaseCreditMemoControlFromNotificationPayloadErrors.length) {
            return {
                wasSuccessful: false,
                message: purchaseCreditMemoControlFromNotificationPayloadErrors
                    .map(message => '* '.concat(message.message))
                    .join('\n'),
                validationMessages: purchaseCreditMemoControlFromNotificationPayloadErrors,
            };
        }
    }

    return {
        wasSuccessful: true,
        message: '',
        validationMessages: [],
    };
}

/**
 * Get Payload related with landed cost purchase invoice lines that are allocated to a purchase order line
 * @param accountingStagingCommonPayload The accounting staging payload, to not get it again
 * @param document An object that implements PurchaseFinanceDocumentWithTax to get header related properties from a document
 * @param documentLines An array of purchase invoice lines
 * @return an aray of purchase invoice lines that have landed costs allocated to purchase order lines.
 */
async function getLandedCostPurchaseInvoiceLinesAllocatedToPurchaseOrderLinesPayload(
    params: {
        accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload;
        document: xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax;
    },
    documentLines: xtremFinanceData.interfaces.InvoiceDocumentLine[],
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    return (
        await asyncArray(documentLines)
            .map(async line => {
                return (
                    (await line.landedCost)?.allocations
                        .filter(
                            async allocation =>
                                (await allocation.getAllocatedDocumentLine()).constructor.name === 'PurchaseOrderLine',
                        )
                        ?.map(async landedCostAllocation => {
                            const allocatedDocumentLine = await landedCostAllocation.getAllocatedDocumentLine();
                            const amount = await allocatedDocumentLine.landedCostLines
                                .filter(
                                    async landedCostLine =>
                                        (await landedCostLine.landedCostAllocation)._id === landedCostAllocation._id,
                                )
                                .sum(landedCostLine => landedCostLine.actualCostAmountInCompanyCurrency);
                            return {
                                uiBaseDocumentLineSysId: line._id, // purchase invoice line to highlight the line in the UI
                                uiSourceDocumentNumber: (await allocatedDocumentLine.documentNumber) || '', // source document number (PR or PO) of the given line
                                baseDocumentLineSysId: line._id,
                                sourceBaseDocumentLineSysId: allocatedDocumentLine._id,
                                documentNumber: params.accountingStagingCommonPayload.documentNumber,
                                sourceDocumentNumber: (await allocatedDocumentLine.documentNumber) || '',
                                sourceDocumentType: await landedCostAllocation.allocatedDocumentType,
                                movementType: 'document',
                                itemSysId: (await allocatedDocumentLine.item)._id,
                                supplierSysId: (await params.document.billBySupplier)._id,
                                currencySysId: (
                                    await (
                                        await (
                                            await params.document.financialSite
                                        ).legalCompany
                                    ).currency
                                )._id,
                                companyFxRate: 1,
                                companyFxRateDivisor: 1,
                                fxRateDate: (await params.document.documentDate).toString(),
                                amounts: [
                                    {
                                        amountType: 'landedCostStockInTransitAmount',
                                        amount,
                                        documentLineType: 'documentLine',
                                    },
                                ],
                                storedDimensions: (await allocatedDocumentLine.storedDimensions) || {},
                                storedAttributes: {
                                    ...(await allocatedDocumentLine.storedAttributes),
                                    ...(await allocatedDocumentLine.computedAttributes),
                                },
                            } as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine;
                        })
                        .toArray() || []
                );
            })
            .toArray()
    ).flat(1);
}

/**
 * Generate the payload for landed cost purchase invoice allocated to a purchase order
 * @param context Context
 * @param accountingStagingCommonPayload The accounting staging payload, to not get it again
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements InvoiceDocumentLine to get line related properties from a document
 * @param documentType the documentType for the notification
 * @return a finance notification payload
 */
export async function getPurchaseInvoiceOrderAdjustmentPayLoad(
    params: {
        accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload;
        document: xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax;
        lines: xtremFinanceData.interfaces.InvoiceDocumentLine[];
        documentType: xtremFinanceData.enums.FinanceDocumentType;
    },
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    // Get all purchase invoice lines that have landed cost allocations linked to a PO
    const documentLines = await asyncArray(params.lines)
        .filter(async line => {
            return !!(await (
                await line.landedCost
            )?.allocations.some(
                async allocation =>
                    (await allocation.getAllocatedDocumentLine()).constructor.name === 'PurchaseOrderLine',
            ));
        })
        .toArray();

    if (!documentLines.length) {
        return [];
    }

    // build a unique array of source information (SysId + Number + constant type 'purchaseReceipt'), 1 element for each
    // purchase receipt linked to the invoice to add to the finance transaction record later
    const sourceLines = isJustChecking
        ? []
        : await xtremFinanceData.functions.getSourceLines({
              lines: params.lines,
              isConstantSourceDocumentType: true,
              targetDocumentType: 'journalEntry',
              type: 'purchaseOrder',
          });
    // finance document payload
    return [
        {
            ...params.accountingStagingCommonPayload,
            batchSize: 0,
            documentType: params.documentType,
            targetDocumentType: 'journalEntry',
            documentLines: await getLandedCostPurchaseInvoiceLinesAllocatedToPurchaseOrderLinesPayload(
                {
                    accountingStagingCommonPayload: params.accountingStagingCommonPayload,
                    document: params.document,
                },
                documentLines,
            ),
            sourceLines,
        },
    ] as xtremFinanceData.interfaces.FinanceIntegrationDocument[];
}

async function getStockJournalLinesForCheckingCase(
    context: Context,
    line: xtremFinanceData.interfaces.InvoiceDocumentLine,
): Promise<xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine[]> {
    // Those are only used when we have a stockMovementArray, meaning that we are just checking
    // Just checking means that we are trying to see if a finance document generation will
    // generate an error or not and not actually creating the document
    if (!line.stockMovementArray) {
        return [];
    }

    // if we have a landed costs, we need to get data from the landed cost allocations
    return (await (
        await line.landedCost
    )?.allocations.length)
        ? (
              await asyncArray(line.stockMovementArray)
                  .map(async stockMovement => {
                      return (await line.landedCost)!.allocations
                          .map(async allocation => {
                              const allocatedDocumentLine =
                                  (await allocation.allocatedDocumentLine) as xtremMasterData.nodes.BaseDocumentLine &
                                      xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum>;
                              return {
                                  ...stockMovement,
                                  documentLine: line,
                                  sourceDocumentLine: allocatedDocumentLine,
                                  reasonCode: await context
                                      .query(xtremMasterData.nodes.ReasonCode, {
                                          first: 1,
                                          filter: { landedCostAdjustment: true },
                                      })
                                      .at(0),
                                  item: await allocatedDocumentLine.item,
                                  storedDimensions: (await allocatedDocumentLine.storedDimensions) || {},
                                  storedAttributes: (await allocatedDocumentLine.storedAttributes) || {},
                                  computedAttributes: (await allocatedDocumentLine.computedAttributes) || {},
                              } as xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine;
                          })
                          .toArray();
                  })
                  .toArray()
          ).flat(1)
        : asyncArray(line.stockMovementArray)
              .map(async stockMovement => {
                  return {
                      ...stockMovement,
                      documentLine: line,
                      sourceDocumentLine: null,
                      reasonCode: null,
                      item: await context.read(xtremMasterData.nodes.Item, { _id: (await line.item)._id }),
                      storedDimensions: {},
                      storedAttributes: {},
                      computedAttributes: {},
                  } as xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine;
              })
              .toArray();
}

function getStockJournalLines(
    context: Context,
    line: xtremFinanceData.interfaces.InvoiceDocumentLine,
    company: xtremSystem.nodes.Company,
): Promise<xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine[]> {
    // If we have a stockMovementArray (it is a checking case)
    // Just checking means that we are trying to see if a finance document generation will generate an error or not
    // and not actually creating the document
    return line.stockMovementArray
        ? getStockJournalLinesForCheckingCase(context, line)
        : context
              .queryAggregate(xtremStockData.nodes.StockJournal, {
                  filter: {
                      sourceDocumentLine: line._id,
                  },
                  group: {
                      item: { _id: { _by: 'value' } },
                      reasonCode: { _id: { _by: 'value' } },
                      documentLine: { _id: { _by: 'value' } },
                      sourceDocumentLine: { _id: { _by: 'value' } },
                  },
                  values: { movementAmount: { sum: true }, nonAbsorbedAmount: { sum: true } },
              })
              .filter(async stockJournalLine => {
                  return (
                      stockJournalLine.values.movementAmount.sum !== 0 ||
                      ((await company.doNonAbsorbedPosting) && stockJournalLine.values.nonAbsorbedAmount.sum !== 0)
                  );
              })
              .map(async stockJournalLine => {
                  const attributesAndDimensions = await context.read(xtremPurchasing.nodes.PurchaseReceiptLine, {
                      _id: stockJournalLine.group.documentLine._id,
                  });

                  return {
                      movementAmount: stockJournalLine.values.movementAmount.sum,
                      nonAbsorbedAmount: !(await company.doNonAbsorbedPosting)
                          ? 0
                          : stockJournalLine.values.nonAbsorbedAmount.sum,
                      documentLine: await context.read(xtremMasterData.nodes.BaseDocumentLine, {
                          _id: stockJournalLine.group.documentLine._id,
                      }),
                      sourceDocumentLine: stockJournalLine.group.sourceDocumentLine
                          ? await context.read(xtremMasterData.nodes.BaseDocumentLine, {
                                // removing the as any result on a compile error:
                                // Property '_id' does not exist on type 'AggregateGroupReturn<BaseDocumentLine, { _by: "value"; }>'
                                _id: (stockJournalLine.group.sourceDocumentLine as any)._id || 0,
                            })
                          : null,
                      reasonCode: stockJournalLine.group.reasonCode
                          ? await context.read(xtremMasterData.nodes.ReasonCode, {
                                // removing the as any result on a compile error:
                                // Property '_id' does not exist on type 'AggregateGroupReturn<ReasonCode, { _by: "value"; }>'
                                _id: (stockJournalLine.group.reasonCode as any)._id || 0,
                            })
                          : null,
                      item: await context.read(xtremMasterData.nodes.Item, { _id: stockJournalLine.group.item._id }),
                      storedDimensions: (await attributesAndDimensions.storedDimensions) || {},
                      storedAttributes: (await attributesAndDimensions.storedAttributes) || {},
                      computedAttributes: (await attributesAndDimensions.computedAttributes) || {},
                  } as xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine;
              })
              .toArray();
}

async function getPurchaseInvoiceReceiptAdjustmentPayLoadLine(params: {
    document: xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax;
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    line: xtremFinanceData.interfaces.InvoiceDocumentLine;
    stockJournalLine: xtremFinanceData.interfaces.FinanceDocumenLineStockJournalLine;
}) {
    const lineAmounts: xtremFinanceData.interfaces.AccountingStagingAmount[] = [];
    const isLandedCost = !!(await params.stockJournalLine.reasonCode?.landedCostAdjustment);

    if (params.documentType === 'purchaseCreditMemo') {
        if (params.stockJournalLine.movementAmount !== 0) {
            lineAmounts.push({
                amountType: 'adjustmentAmount',
                amount: Math.abs(params.stockJournalLine.movementAmount),
                documentLineType: 'documentLine',
            });
        }
        // We should not have nonAbsorbedAmount for legislations that do not post it but should have already be filtered before
        if (params.stockJournalLine.nonAbsorbedAmount !== 0) {
            lineAmounts.push({
                amountType: 'adjustmentNonabsorbedAmount',
                amount: Math.abs(params.stockJournalLine.nonAbsorbedAmount),
                documentLineType: 'documentLine',
            });
        }
    } else {
        if (params.stockJournalLine.movementAmount !== 0) {
            lineAmounts.push({
                amountType: isLandedCost ? 'landedCostAdjustmentAmount' : 'adjustmentAmount',
                amount: params.stockJournalLine.movementAmount,
                documentLineType: 'documentLine',
            });
        }
        // We should not have nonAbsorbedAmount for legislations that do not post it but should have already be filtered before
        if (params.stockJournalLine.nonAbsorbedAmount !== 0) {
            lineAmounts.push({
                amountType: isLandedCost ? 'landedCostAdjustmentNonabsorbedAmount' : 'adjustmentNonabsorbedAmount',
                amount: params.stockJournalLine.nonAbsorbedAmount,
                documentLineType: 'documentLine',
            });
        }
    }

    const sourceDocumentFactoryName = (await params.stockJournalLine?.documentLine?.document)?.$.factory.name ?? null;
    return {
        uiBaseDocumentLineSysId: params.line._id, // purchase invoice line to highlight the line in the UI
        uiSourceDocumentNumber: (await params.stockJournalLine.sourceDocumentLine?.documentNumber) || '', // source document number (PR or PO) of the given line
        baseDocumentLineSysId: params.stockJournalLine?.sourceDocumentLine?._id || 0,
        sourceBaseDocumentLineSysId: isLandedCost ? params.stockJournalLine.sourceDocumentLine?._id : undefined,
        movementType: 'stockJournal',
        // documentNumber is either the sales invoice number or the sales credit memo number from stockJournal.sourceDocumentLine
        documentNumber: (await params.stockJournalLine?.sourceDocumentLine?.documentNumber) || '',
        // sourceDocumentNumber is the purchase receipt number from stockJournal.documentLine
        sourceDocumentNumber: (await params.stockJournalLine?.documentLine?.documentNumber) || '',
        sourceDocumentType: sourceDocumentFactoryName ? _.camelCase(sourceDocumentFactoryName) : null,
        currencySysId: (await (await (await params.document.financialSite).legalCompany).currency)._id,
        companyFxRate: 1,
        companyFxRateDivisor: 1,
        fxRateDate: (await params.document.documentDate).toString(),
        itemSysId: params.stockJournalLine?.item?._id,
        supplierSysId: (await params.document.billBySupplier)._id,
        amounts: lineAmounts,
        storedDimensions: isLandedCost
            ? params.stockJournalLine.storedDimensions
            : (await params.line.storedDimensions) || {},
        storedAttributes: {
            ...((isLandedCost
                ? params.stockJournalLine.storedAttributes
                : await params.line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
            ...(isLandedCost
                ? params.stockJournalLine.computedAttributes
                : ((await params.line.computedAttributes) as {})),
        },
    };
}

/**
 * Generate the payload for a receipt adjustment related with a purchase invoice (regular stock variance or landed cost)
 * @param context Context
 * @param accountingStagingCommonPayload The accounting staging payload, to not get it again
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements InvoiceDocumentLine to get line related properties from a document
 * @param documentType the documentType for the notification
 * @param isJustChecking If set to true we assume that notification is not really being sent, if just to check the payload
 * @return The number of notifications created
 */
export async function getPurchaseInvoiceReceiptAdjustmentPayLoad(
    context: Context,
    params: {
        accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload;
        document: xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax;
        lines: xtremFinanceData.interfaces.InvoiceDocumentLine[];
        documentType: xtremFinanceData.enums.FinanceDocumentType;
    },
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const company = await (await params.document.financialSite).legalCompany;

    // Get all purchase invoice lines where the item is stock managed and there are any record on
    // the stock journal with this line as the source document line and it has a movement amount
    // or a non absorbed amount (in the case we post non absorbed amount for the company)
    const documentLines = await xtremFinanceData.functions.getPurchaseInvoiceLinesWithReceiptStockJournalLines(
        context,
        params.lines,
        company,
    );

    // If there are no lines to process, we should not create any notification
    // (we should not have any stock journal lines for this purchase invoice lines
    if (!(await documentLines.length)) {
        return [];
    }

    // build a unique array of source information (SysId + Number + constant type 'purchaseReceipt'), 1 element for each
    // purchase receipt linked to the invoice to add to the finance transaction record later
    const sourceLines = isJustChecking
        ? []
        : await xtremFinanceData.functions.getSourceLines({
              lines: params.lines,
              isConstantSourceDocumentType: true,
              targetDocumentType: 'journalEntry',
              type: 'purchaseReceipt',
          });

    return [
        {
            ...params.accountingStagingCommonPayload,
            batchSize: 0,
            documentType: params.documentType,
            targetDocumentType: 'journalEntry',
            documentLines: (
                await documentLines
                    .map(async line => {
                        return asyncArray(await getStockJournalLines(context, line, company))
                            .map(stockJournalLine => {
                                return getPurchaseInvoiceReceiptAdjustmentPayLoadLine({
                                    document: params.document,
                                    documentType: params.documentType,
                                    line,
                                    stockJournalLine,
                                });
                            })
                            .toArray();
                    })
                    .toArray()
            ).flat(1),
            sourceLines,
        },
    ] as xtremFinanceData.interfaces.FinanceIntegrationDocument[];
}

export async function getPurchaseInvoiceCreditMemoNotificationPayload(
    context: Context,
    params: {
        document: xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax;
        lines: xtremFinanceData.interfaces.InvoiceDocumentLine[];
        documentType: xtremFinanceData.enums.FinanceDocumentType;
        targetDocumentType: xtremFinanceData.enums.TargetDocumentType;
    },
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    // 'Regular' payload, usually to generate an ap\ar invoice
    const { accountingStagingCommonPayload, notificationsPayload } =
        await xtremFinanceData.functions.getPurchaseSalesInvoiceCreditMemoNotificationPayload(context, {
            ...params,
            isJustChecking,
        });

    if (await (await (await params.document.financialSite).legalCompany).doStockPosting) {
        // Payload for an adjustment journal entry when the invoice amounts are different than the
        // receipt amounts. Adjustment can exist due to a price variation or a landed cost allocation.
        const notificationsPayloadWithAdjustment = notificationsPayload.concat(
            await getPurchaseInvoiceReceiptAdjustmentPayLoad(
                context,
                {
                    accountingStagingCommonPayload,
                    document: params.document,
                    lines: params.lines,
                    documentType: params.documentType,
                },
                isJustChecking,
            ),
        );
        // Adjustment journal entry when we have a landed cost invoice value allocated to a
        // purchase order.
        if (
            await (
                await (
                    await (
                        await params.document.financialSite
                    ).legalCompany
                ).legislation
            ).doLandedCostGoodsInTransitPosting
        ) {
            const purchaseInvoiceOrderAdjustmentPayLoad = await getPurchaseInvoiceOrderAdjustmentPayLoad(
                {
                    accountingStagingCommonPayload,
                    document: params.document,
                    lines: params.lines,
                    documentType: params.documentType,
                },
                isJustChecking,
            );

            // XT-77727: if we have duplicates on documentNumber/targetDocumentType => concat documentLines
            const purchaseInvoiceOrderAdjustment = purchaseInvoiceOrderAdjustmentPayLoad.at(0);
            const existingJournalEntry = notificationsPayloadWithAdjustment.findIndex(
                notificationPayloadWithAdjustment =>
                    notificationPayloadWithAdjustment.documentNumber ===
                        purchaseInvoiceOrderAdjustment?.documentNumber &&
                    notificationPayloadWithAdjustment.targetDocumentType ===
                        purchaseInvoiceOrderAdjustment?.targetDocumentType &&
                    notificationPayloadWithAdjustment.batchId === purchaseInvoiceOrderAdjustment?.batchId,
            );

            if (existingJournalEntry >= 0) {
                notificationsPayloadWithAdjustment[existingJournalEntry].documentLines =
                    notificationsPayloadWithAdjustment[existingJournalEntry].documentLines.concat(
                        purchaseInvoiceOrderAdjustment?.documentLines || [],
                    );
                return notificationsPayloadWithAdjustment;
            }
            return notificationsPayloadWithAdjustment.concat(purchaseInvoiceOrderAdjustmentPayLoad);
        }
        return notificationsPayloadWithAdjustment;
    }
    return notificationsPayload;
}

/**
 * Creates all the notifications for the finance integration of a Purchase Invoice
 * @param context Context
 * @param document An object that implements PurchaseFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceInvoiceDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function purchaseInvoiceCreditMemoNotification(
    context: Context,
    document: xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
    lines: xtremFinanceData.interfaces.InvoiceDocumentLine[],
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    replyTopic: string,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getPurchaseInvoiceCreditMemoNotificationPayload(context, {
            document,
            lines,
            documentType,
            targetDocumentType: 'accountsPayableInvoice',
        }),
        replyTopic,
        isUpdate: false,
        paymentTracking: (await document.paymentTracking) || undefined,
    });
}
