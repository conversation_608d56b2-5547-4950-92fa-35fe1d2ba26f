import type { Logger, NodeCreateData, date } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../index';

export async function orderLineToPurchaseLineControlEnd(
    logger: Logger,
    orderLineToReceiptLine: xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine,
) {
    const receiptLine = await orderLineToReceiptLine.purchaseReceiptLine;
    const orderLine = await orderLineToReceiptLine.purchaseOrderLine;
    const receiptLineDocument = await receiptLine.document;
    const orderLineDocument = await orderLine.document;

    await logger.debugAsync(
        async () => `
        receiptItem = ${(await receiptLine.item)._id} /
        orderItem = ${(await orderLine.item)._id}
        receiptUnit = ${(await receiptLine.unit)._id} /
        orderUnit ${(await orderLine.unit)._id}
        receiptSupplier = ${(await (await receiptLine.document).businessRelation)._id} /
        orderSupplier = ${(await orderLineDocument.businessRelation)._id}
        receiptSupplierAddress = ${await (await receiptLineDocument.supplierAddress)?.concatenatedAddress} /
        orderSupplierAddress = ${await (await orderLineDocument.supplierLinkedAddress).concatenatedAddress} /
        orderSupplierAddressDetail = ${await (await orderLineDocument.supplierAddress)?.concatenatedAddress}
        receivingSite = ${(await receiptLineDocument.site)._id} /
        orderLineStockSite = ${(await orderLine.stockSite)._id}
        receivingSiteAddress = ${await (await receiptLineDocument.receivingAddress)?.concatenatedAddress} /
        orderLineStockSiteAddress = ${await (await orderLine.stockSiteLinkedAddress).concatenatedAddress} /
        orderLineStockSiteAddressDetail = ${await (await orderLine.stockSiteAddress)?.concatenatedAddress}
        receiptCurrency = ${(await receiptLineDocument.currency)._id} /
        orderCurrency = ${(await orderLineDocument.currency)._id}
        receiptFactor = ${await receiptLine.unitToStockUnitConversionFactor} /
        orderFactor = ${await orderLine.unitToStockUnitConversionFactor}
    `,
    );
}

export async function logPurchaseOrderCreateDataRequisition(
    logger: Logger,
    newOrder: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>,
    newOrderLine: NodeCreateData<xtremPurchasing.nodes.PurchaseOrderLine>,
    requisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
    currentDate: date,
    expectedReceiptDateRevised: date,
) {
    await logger.debugAsync(
        async () =>
            `newPurchaseOrdersCreateDataLineBreakCondition -
        line/param
        item:${newOrderLine.item}/${(await requisitionLine.item)?._id};
        lineSite:${newOrderLine.stockSite}/${(await requisitionLine.site)._id};
        headerSite:${newOrder.stockSite}/${(await (await requisitionLine.document).site)._id};
        date:${
            newOrderLine.expectedReceiptDate
        }/${await requisitionLine.needByDate}/currentDate=${currentDate}/expectedReceiptDateRevised=${expectedReceiptDateRevised};
        pu:${newOrderLine.unit}/${(await requisitionLine.unit)?._id};
        `,
    );
}
