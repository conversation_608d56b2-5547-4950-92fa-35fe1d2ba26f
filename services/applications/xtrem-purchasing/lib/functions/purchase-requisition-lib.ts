import type { Context, NodeCreateData, UpdateAction } from '@sage/xtrem-core';
import { BusinessRuleError, LogicError, asyncArray, date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import { isEmpty } from 'lodash';
import * as xtremPurchasing from '../index';
import { logPurchaseOrderCreateDataRequisition, loggers } from './index';

/**
 * This function is used for verifying that input properties are the same as the ones from the current order to create header
 * @param site
 * @param supplier
 * @param currency
 * @param data - contains the order header creation payload data
 * @returns true/false
 * Order header grouping conditioned properties :
 * - site
 * - supplier
 * - currency
 */
const _newPurchaseOrdersCreateDataBreakCondition = (
    site: xtremSystem.nodes.Site,
    supplier: xtremMasterData.nodes.Supplier,
    currency: xtremMasterData.nodes.Currency | null,
    purchaseRequisition: xtremPurchasing.nodes.PurchaseRequisition,
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>,
) => {
    loggers.requisitionLib.debug(
        () =>
            `7) newPurchaseOrdersCreateDataBreakCondition - data/param site:${data.site}/${site._id};supplier:${
                data.businessRelation
            }/${supplier._id};currency:${data.currency}/${currency ? currency._id : null}`,
    );
    return (
        data.site === site._id &&
        data.businessRelation === supplier._id &&
        (!data.orderDate || date.today().compare(data.orderDate) >= 0) &&
        (!currency || !data.currency || data.currency === currency._id)
    );
};

async function _getAttributeDimensionBreakingCondition(
    requisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
    newOrderLine: NodeCreateData<xtremPurchasing.nodes.PurchaseOrderLine>,
): Promise<boolean> {
    const reqEmployee = (await requisitionLine.storedAttributes)?.employee || null;
    const reqProject = (await requisitionLine.storedAttributes)?.project || null;
    const reqTask = (await requisitionLine.storedAttributes)?.task || null;
    const orderEmployee = newOrderLine.storedAttributes?.employee ?? null;
    const orderProject = newOrderLine.storedAttributes?.project ?? null;
    const orderTask = newOrderLine.storedAttributes?.task ?? null;
    loggers.requisitionLib.debug(
        () =>
            `_getAttributeDimensionBreakingCondition -
            line/param
            employee:${reqEmployee}/${orderEmployee};
            project:${reqProject}/${orderProject};
            task:${reqTask}/${orderTask};
            `,
    );
    Object.entries((await requisitionLine.storedDimensions) || {}).forEach(([dimensionType, dimension]) =>
        loggers.requisitionLib.debug(() => `${dimensionType}:${dimension}`),
    );
    Object.entries(newOrderLine.storedDimensions ?? {}).forEach(([dimensionType, dimension]) =>
        loggers.requisitionLib.debug(() => `${dimensionType}:${dimension}`),
    );
    return (
        (!reqEmployee || !orderEmployee || reqEmployee === orderEmployee) &&
        (!reqProject || !orderProject || reqProject === orderProject) &&
        (!reqTask || !orderTask || reqTask === orderTask) &&
        (!(await requisitionLine.storedDimensions) ||
            !newOrderLine.storedDimensions ||
            Object.entries((await requisitionLine.storedDimensions) || {}).every(
                ([dimensionType, dimension]) =>
                    dimensionType &&
                    dimension &&
                    newOrderLine.storedDimensions &&
                    Object.entries(newOrderLine.storedDimensions ?? {}).find(
                        ([orderDimensionType, orderDimension]) =>
                            dimensionType === orderDimensionType && dimension === orderDimension,
                    ),
            ))
    );
}

/**
 * This function is used for verifying that input properties are the same as the ones from the current order line to create
 * @param item
 * @param expectedReceiptDate
 * @param unit
 * @param line
 * @returns true/false
 * Order line grouping conditioned properties :
 *  * - item
 * - expectedReceiptDate
 * - unit
 */
async function _newPurchaseOrdersCreateDataLineBreakCondition(
    context: Context,
    requisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
    newOrder: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>,
    newOrderLine: NodeCreateData<xtremPurchasing.nodes.PurchaseOrderLine>,
): Promise<boolean> {
    const currentDate = date.today();

    const expectedReceiptDateRevised = await xtremPurchasing.functions.PurchaseOrderLib.getExpectedReceiptDate(
        context,
        await requisitionLine.item,
        await requisitionLine.site,
        newOrder.businessRelation as xtremMasterData.nodes.Supplier,
        currentDate,
        await requisitionLine.needByDate,
    );

    await logPurchaseOrderCreateDataRequisition(
        loggers.requisitionLib,
        newOrder,
        newOrderLine,
        requisitionLine,
        currentDate,
        expectedReceiptDateRevised,
    );

    const item = await requisitionLine.item;

    if (item === null) return false;

    if (newOrderLine.item !== item._id) return false;

    if (newOrderLine.stockSite !== (await requisitionLine.site)._id) return false;

    if (newOrder.businessRelation !== (await requisitionLine.supplier)?._id) return false;

    if (newOrder.stockSite !== (await (await requisitionLine.document).site)._id) return false;

    if (
        newOrderLine.expectedReceiptDate &&
        !newOrderLine.expectedReceiptDate.equals(expectedReceiptDateRevised) &&
        expectedReceiptDateRevised.compare(currentDate) >= 0
    )
        return false;

    if ((await requisitionLine.unit) && newOrderLine.unit && newOrderLine.unit !== (await requisitionLine.unit)?._id)
        return false;

    if (newOrderLine.unitToStockUnitConversionFactor !== (await requisitionLine.unitToStockUnitConversionFactor))
        return false;

    if (!(await _getAttributeDimensionBreakingCondition(requisitionLine, newOrderLine))) return false;

    if (newOrderLine.priceOrigin === 'manual') return false;

    return true;
}

/**
 * This function is used to find an existing order line data to create for the current purchase requisition line to be ordered
 * @param newPurchaseOrdersCreateData
 * @param purchaseRequisitionLine
 * @returns a reference to the order line data to create if any or null
 */
const _findExistingPurchaseOrderLineData = async (
    context: Context,
    newPurchaseOrdersCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>[],
    purchaseRequisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
): Promise<NodeCreateData<
    xtremPurchasing.nodes.PurchaseOrderLine & { _sortValue?: number | undefined; _action: UpdateAction }
> | null> => {
    loggers.requisitionLib.debug(
        () =>
            `Entry find existing poDataLine : newPurchaseOrdersCreateData.length:${newPurchaseOrdersCreateData.length}`,
    );
    /* eslint-disable no-restricted-syntax */
    for (const poData of newPurchaseOrdersCreateData) {
        const existingPurchaseOrderLineDataWithCurrentItem =
            (await asyncArray(poData.lines || []).find(async poDataLine => {
                loggers.requisitionLib.debug(
                    () => `Into find existing poDataLine : site:${poData.site} supplier:${poData.businessRelation}`,
                );
                const found = await _newPurchaseOrdersCreateDataLineBreakCondition(
                    context,
                    purchaseRequisitionLine,
                    poData,
                    poDataLine,
                );
                loggers.requisitionLib.debug(
                    () =>
                        `Into find existing poDataLine : site:${poData.site} supplier:${poData.businessRelation} exist line ${found}`,
                );

                return found;
            })) || null;
        if (existingPurchaseOrderLineDataWithCurrentItem) {
            return existingPurchaseOrderLineDataWithCurrentItem;
        }
    }
    return null;
};

/**
 * This function is used to unit price based on requisition line values from item-price
 * @param purchaseRequisitionLine
 */
export const getPrice = async (requisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine) => {
    const currency = await requisitionLine.currency;
    const item = await requisitionLine.item;
    const unit = await requisitionLine.unit;
    const supplier = await requisitionLine.supplier;
    return (
        (currency &&
            item &&
            unit &&
            supplier &&
            (await xtremMasterData.functions.getPurchasePrice(requisitionLine.$.context, {
                site: await requisitionLine.site,
                supplier: await requisitionLine.supplier,
                currency,
                item,
                quantity: await requisitionLine.quantity,
                unit,
                date: await (await requisitionLine.document).requestDate,
                convertUnit: false,
            }))) ||
        0
    );
};

/**
 * This function is used to add a dependency from the purchase order line to be created, to the purchase requisition line it was created from
 * @param purchaseOrderLineData
 * @param purchaseRequisitionLine
 */
const _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency = async (
    purchaseOrderLineData: NodeCreateData<
        xtremPurchasing.nodes.PurchaseOrderLine & { _sortValue?: number | undefined; _action: UpdateAction }
    >,
    purchaseRequisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
) => {
    if (!purchaseOrderLineData.purchaseRequisitionLines || !purchaseOrderLineData.purchaseRequisitionLines.length) {
        purchaseOrderLineData.purchaseRequisitionLines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseRequisitionLineToPurchaseOrderLine & {
                _sortValue?: number;
                _action: UpdateAction;
            }
        >[];
    }
    if (!purchaseOrderLineData.purchaseRequisitionLines) {
        purchaseOrderLineData.purchaseRequisitionLines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseRequisitionLineToPurchaseOrderLine & {
                _sortValue?: number;
                _action: UpdateAction;
            }
        >[];
    }
    if (!purchaseOrderLineData.purchaseRequisitionLines.some(req => req._id === purchaseRequisitionLine._id)) {
        loggers.requisitionLib.debug(
            () =>
                `9) _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency : pushing new purchaseOrderLineData.purchaseRequisitionLines`,
        );
        const length = purchaseOrderLineData.purchaseRequisitionLines.push({
            purchaseRequisitionLine,
            orderedQuantity: await purchaseRequisitionLine.quantityToOrder,
            orderedQuantityInStockUnit: await xtremMasterData.functions.convertFromTo(
                await purchaseRequisitionLine.unit,
                (await (await purchaseRequisitionLine.item)?.stockUnit) || null,
                await purchaseRequisitionLine.quantityToOrder,
                true,
                await purchaseRequisitionLine.unitToStockUnitConversionFactor,
            ),
        });
        purchaseOrderLineData.quantity = purchaseOrderLineData.purchaseRequisitionLines.reduce(
            (prev, reqLine) => prev + (reqLine.orderedQuantity ?? 0),
            0,
        );

        await loggers.requisitionLib.debugAsync(
            async () =>
                `9bis) _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency : Dependency added - increased order qty ${
                    purchaseOrderLineData.quantity ?? 0
                } with added req line qtyToOrder ${await purchaseRequisitionLine.quantityToOrder}`,
        );
        loggers.requisitionLib.debug(
            () =>
                `3) or 10) _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency : Dependency added - new length ${length}`,
        );
    }
};

/**
 * This function is used to add/update a purchase order create data
 * @param newPurchaseOrdersCreateData
 * @param purchaseRequisitionLine
 * @param orderSite
 * @param orderSupplier
 */
const _addPurchaseOrderDataEntry = async (
    context: Context,
    newPurchaseOrdersCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>[],
    purchaseRequisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
    orderSite: xtremSystem.nodes.Site,
    orderSupplier: xtremMasterData.nodes.Supplier,
) => {
    let newEntry = false;

    const newPurchaseOrdersCreateDataIndex = await asyncArray(
        newPurchaseOrdersCreateData.filter(data => data.site && data.businessRelation),
    ).findIndex(async data => {
        loggers.requisitionLib.debug(
            () =>
                `6) _addPurchaseOrderDataEntry : loop on newPurchaseOrdersCreateData for site ${data.site}/ supplier ${data.businessRelation}  calling _newPurchaseOrdersCreateDataBreakCondition`,
        );
        return _newPurchaseOrdersCreateDataBreakCondition(
            orderSite,
            orderSupplier,
            (await purchaseRequisitionLine.currency) || (await (await orderSite.legalCompany).currency),
            await purchaseRequisitionLine.document,
            data,
        );
    });
    let purchaseOrderData =
        newPurchaseOrdersCreateDataIndex !== -1 ? newPurchaseOrdersCreateData[newPurchaseOrdersCreateDataIndex] : null;
    if (!purchaseOrderData) {
        loggers.requisitionLib.debug(
            () =>
                `8) _addPurchaseOrderDataEntry : newEntry purchaseOrdersData for site ${orderSite._id}/ supplier ${orderSupplier._id}`,
        );
        purchaseOrderData = {
            site: orderSite._id,
            stockSite: orderSite._id,
            businessRelation: orderSupplier._id,
            orderDate: date.today(),
            fxRateDate: date.today(),
            currency:
                (await purchaseRequisitionLine.currency)?._id ||
                (await (await orderSite.legalCompany).currency)?._id ||
                '',
            isOverwriteNote: true,
        };
        newEntry = true;
    }
    if (!purchaseOrderData.lines || !purchaseOrderData.lines.length) {
        // No order lines data yet
        purchaseOrderData.lines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseOrderLine & { _sortValue?: number; _action: UpdateAction }
        >[];
    }
    await loggers.requisitionLib.debugAsync(
        async () =>
            `11) _addPurchaseOrderDataEntry : pushing new order line purchaseOrderData.lines.push item ${
                (await purchaseRequisitionLine.item)?._id
            }/site ${purchaseOrderData?.site}/quantity ${await purchaseRequisitionLine.quantity}`,
    );

    const expectedReceiptDateRevised = await xtremPurchasing.functions.PurchaseOrderLib.getExpectedReceiptDate(
        context,
        await purchaseRequisitionLine.item,
        orderSite,
        orderSupplier,
        date.today(),
        await purchaseRequisitionLine.needByDate,
    );

    if ((await purchaseRequisitionLine.needByDate).compare(expectedReceiptDateRevised) < 0) {
        loggers.requisitionLib.warn(() =>
            context.localize(
                '@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_required_date_not_met',
                'The expected receipt date is later than the requirement date.',
            ),
        );
    }

    const purchaseOrderDataLinesLength = purchaseOrderData.lines.push({
        item: (await purchaseRequisitionLine.item)?._id,
        itemDescription: await purchaseRequisitionLine.requestedItemDescription,
        site: purchaseOrderData.site,
        stockSite: (await purchaseRequisitionLine.site)._id,
        quantity: await purchaseRequisitionLine.quantity,
        unitToStockUnitConversionFactor: await purchaseRequisitionLine.unitToStockUnitConversionFactor,
        expectedReceiptDate: expectedReceiptDateRevised,
        unit: (await purchaseRequisitionLine.unit)?._id,
        grossPrice: await purchaseRequisitionLine.grossPrice,
        priceOrigin: await purchaseRequisitionLine.priceOrigin,
        storedAttributes: !isEmpty(await purchaseRequisitionLine.storedAttributes)
            ? await purchaseRequisitionLine.storedAttributes
            : undefined,
        storedDimensions: !isEmpty(await purchaseRequisitionLine.storedDimensions)
            ? await purchaseRequisitionLine.storedDimensions
            : undefined,
        netPrice: await purchaseRequisitionLine.netPrice,
        discount: await purchaseRequisitionLine.discount,
        charge: await purchaseRequisitionLine.charge,
    });

    if (purchaseOrderDataLinesLength) {
        loggers.requisitionLib.debug(
            () =>
                `8) or 12) _addPurchaseOrderDataEntry : calling _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency`,
        );
        await _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency(
            purchaseOrderData.lines[purchaseOrderDataLinesLength - 1],
            purchaseRequisitionLine,
        );
    }

    if (newEntry) {
        loggers.requisitionLib.debug(() => `13) _addPurchaseOrderDataEntry : newPurchaseOrdersCreateData.push`);
        newPurchaseOrdersCreateData.push(purchaseOrderData);
    }
};

/**
 * This function is used to create the purchase order create data from a single purchase requisition. It's called when ordering a purchase requisition
 * @param context
 * @param purchaseRequisition
 * @returns an array of purchase orders data that can be used as payload for the purchase order create method
 * The logic within the algorithm is the following :
 *  Is there an entry for the current requisition item in the order data ?
 *  -- If yes, we'll add the current item to that line
 *  -- If no, then
 *     --  Is there an order header having a site and a supplier couple like the ones on the purchase receipt line?
 *     --  If yes, the current requisition item will be added as new line for that order header
 *     --  If no, then we have to create a new order data entry (header and line)
 */
export const initPurchaseOrderCreateData = async (
    context: Context,
    document: xtremPurchasing.nodes.PurchaseRequisition,
) => {
    // This array will contain the purchase order create payload data
    const newPurchaseOrdersCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>[] = [];

    await document.lines
        .filter(
            async line =>
                ['notOrdered', 'partiallyOrdered'].includes(await line.lineOrderStatus) &&
                ['pending', 'inProgress'].includes(await line.status) &&
                !!(await line.item) &&
                !!(await line.supplier),
        )
        .forEach(async documentLine => {
            await loggers.requisitionLib.debugAsync(
                async () =>
                    `1) Looping on requisition lines: line ${documentLine._id}/item ${await (
                        await documentLine.item
                    )?.name}`,
            );
            const existingPurchaseOrderLineData = await _findExistingPurchaseOrderLineData(
                context,
                newPurchaseOrdersCreateData,
                documentLine,
            );
            if (existingPurchaseOrderLineData) {
                await loggers.requisitionLib.debugAsync(
                    async () =>
                        `2) Looping on requisition lines: line ${documentLine._id}/item ${await (
                            await documentLine.item
                        )?.name} _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency`,
                );
                existingPurchaseOrderLineData.quantity = existingPurchaseOrderLineData.quantity ?? 0;
                await _addNewPurchaseOrderLineToPurchaseRequisitionLineDependency(
                    existingPurchaseOrderLineData,
                    documentLine,
                );
            } else {
                // There are no order lines data for the current item, according to breaking properties, new ones will be created
                await loggers.requisitionLib.debugAsync(
                    async () =>
                        `3) Looping on requisition lines: line ${documentLine._id}/item ${await (
                            await documentLine.item
                        )?.name} _addPurchaseOrderDataEntry`,
                );
                const supplier = await documentLine.supplier;
                if (!supplier) {
                    throw new LogicError('Supplier is required');
                }
                await _addPurchaseOrderDataEntry(
                    context,
                    newPurchaseOrdersCreateData,
                    documentLine,
                    await documentLine.site,
                    supplier,
                );
            }
        });

    await asyncArray(newPurchaseOrdersCreateData).forEach(
        async (purchaseOrder: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>) => {
            if (purchaseOrder.lines) {
                await asyncArray(
                    purchaseOrder.lines.filter(
                        (
                            line: NodeCreateData<
                                xtremPurchasing.nodes.PurchaseOrderLine & {
                                    _sortValue: number | undefined;
                                    _action: 'create' | 'update' | 'delete';
                                }
                            >,
                        ) => line.priceOrigin !== 'manual' && purchaseOrder?.currency && line.item,
                    ),
                ).forEach(
                    async (
                        purchaseOrderLine: NodeCreateData<
                            xtremPurchasing.nodes.PurchaseOrderLine & {
                                _sortValue: number | undefined;
                                _action: 'create' | 'update' | 'delete';
                            }
                        >,
                    ) => {
                        const grossPrice = await xtremMasterData.functions.getPurchasePrice(context, {
                            site: await context.read(xtremSystem.nodes.Site, {
                                _id: purchaseOrder.site as string,
                            }),
                            supplier: await context.read(xtremMasterData.nodes.Supplier, {
                                _id: purchaseOrder.businessRelation as string,
                            }),
                            currency: await context.read(xtremMasterData.nodes.Currency, {
                                _id: purchaseOrder.currency as string,
                            }),
                            item: await context.read(xtremMasterData.nodes.Item, {
                                _id: purchaseOrderLine.item as string,
                            }),
                            quantity: purchaseOrderLine.quantity as number,
                            unit: await context.read(xtremMasterData.nodes.UnitOfMeasure, {
                                _id: purchaseOrderLine.unit as string,
                            }),
                            date: date.today(),
                            convertUnit: false,
                        });

                        purchaseOrderLine.grossPrice = grossPrice >= 0 ? grossPrice : 0;
                        purchaseOrderLine.priceOrigin = grossPrice >= 0 ? 'supplierPriceList' : null;

                        if (!purchaseOrderLine.priceOrigin) {
                            loggers.requisitionLib.warn(() =>
                                context.localize(
                                    '@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order__price_not_set',
                                    'Some prices could not be determined from the supplier price list. Check the purchase orders',
                                ),
                            );
                        }
                    },
                );
            }
        },
    );

    return newPurchaseOrdersCreateData;
};

/**
 * This function is used to create purchase order for each entry of the purchase order create data array. The purchase requisition header and lines status are updated
 * @param context
 * @param purchaseRequisition
 * @param newPurchaseOrdersCreateData
 * @returns an array of created purchase order numbers if any, or an empty array
 */
export const createPurchaseOrders = async (
    context: Context,
    newPurchaseOrdersCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseOrder>[],
) => {
    const orders = [] as xtremPurchasing.nodes.PurchaseOrder[];
    await asyncArray(newPurchaseOrdersCreateData).forEach(async purchaseOrderData => {
        const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, purchaseOrderData);

        if (await newPurchaseOrder.$.trySave()) {
            loggers.requisitionLib.debug(() =>
                context.localize(
                    '@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_creation_succeeded',
                    'Purchase order number created.',
                ),
            );
            orders.push(newPurchaseOrder);
        } else {
            const errorMsg = context.localize(
                '@sage/xtrem-purchasing/purchase_requisition__lib__purchase_order_creation_failed',
                'The purchase order was not created: {{newPurchaseOrderDiagnoses}}.',
                { newPurchaseOrderDiagnoses: newPurchaseOrder.$.context.diagnoses },
            );
            throw new BusinessRuleError(`${errorMsg}`);
        }
    });

    return orders;
};

export async function computeRequisitionStatusFromLinesStatuses(
    purchaseRequisition: xtremPurchasing.nodes.PurchaseRequisition,
): Promise<xtremPurchasing.enums.PurchaseDocumentStatus> {
    const lineStatuses = [
        ...(await purchaseRequisition.lines.reduce(
            async (statuses, line) => statuses.add(await line.status),
            new Set<xtremPurchasing.enums.PurchaseDocumentStatus>(),
        )),
    ];

    if (lineStatuses.some(status => status === 'draft')) {
        return 'draft';
    }

    if (lineStatuses.some(status => status === 'pending')) {
        if ((await purchaseRequisition.orderStatus) === 'partiallyOrdered') {
            return 'inProgress';
        }
        return 'pending';
    }

    if (lineStatuses.some(status => status === 'inProgress')) {
        return 'inProgress';
    }

    return 'closed';
}

export const computeRequisitionOrderStatusFromLinesOrderStatuses = async (
    purchaseRequisition: xtremPurchasing.nodes.PurchaseRequisition,
) => {
    const lineStatuses = [
        ...(await purchaseRequisition.lines.reduce(
            async (statuses, line) => statuses.add(await line.lineOrderStatus),
            new Set<xtremPurchasing.enums.PurchaseRequisitionOrderStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'partiallyOrdered')) {
        return 'partiallyOrdered';
    }
    if (lineStatuses.some(status => status === 'ordered')) {
        if (lineStatuses.some(status => status === 'notOrdered')) {
            return 'partiallyOrdered';
        }
        return 'ordered';
    }
    return 'notOrdered';
};

export const managePurchaseDocumentStatus = async (
    document: xtremPurchasing.nodes.PurchaseRequisition,
    approve: boolean,
): Promise<void> => {
    const { context } = document.$;
    if (!document) {
        throw new LogicError(' no document provided to managePurchaseDocumentStatus function');
    }

    if (!((await document.status) === 'draft' && (await document.approvalStatus) === 'pendingApproval')) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_requisition__invalid_status',
                'Invalid status for approval.',
            ),
        );
    }

    await document.$.set({
        approvalStatus: (approve && 'approved') || 'rejected',
        status: (approve && 'pending') || 'closed',
    });

    await document.lines.forEach(async line => {
        await line.$.set({ status: (approve && 'pending') || 'closed' });
    });
    await document.$.save();
};

const _isRequisitionClosable = async (purchaseRequisition: xtremPurchasing.nodes.PurchaseRequisition) => {
    return !(
        ['draft', 'closed'].includes(await purchaseRequisition.status) ||
        ['ordered'].includes(await purchaseRequisition.orderStatus) ||
        ['pendingApproval'].includes(await purchaseRequisition.approvalStatus)
    );
};

const _isRequisitionLineClosable = async (purchaseRequisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine) => {
    return !(
        ['draft', 'closed'].includes(await purchaseRequisitionLine.status) ||
        ['ordered'].includes(await purchaseRequisitionLine.lineOrderStatus) ||
        ['pendingApproval'].includes(await purchaseRequisitionLine.approvalStatus)
    );
};

export const closeRequisition = async (
    context: Context,
    purchaseRequisitionToClose: xtremPurchasing.nodes.PurchaseRequisition,
) => {
    const purchaseRequisition = await context.read(
        xtremPurchasing.nodes.PurchaseRequisition,
        { _id: purchaseRequisitionToClose._id },
        { forUpdate: true },
    );
    if (await _isRequisitionClosable(purchaseRequisition)) {
        await purchaseRequisition.lines
            .filter(async line => (await line.status) !== 'closed')
            .forEach(line => xtremPurchasing.nodes.PurchaseRequisitionLine.close(line));
    } else {
        const errorMsg = context.localize(
            '@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__error_updating_status_Requisition_not_closable',
            `Unable to update status on already closed, received, pending approval requisition.`,
        );
        loggers.requisitionLib.error(() => errorMsg);
        throw new BusinessRuleError(`${errorMsg}`);
    }
    if (await purchaseRequisition.$.control()) {
        await purchaseRequisition.$.save();

        loggers.requisitionLib.info(() =>
            context.localize(
                '@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__status_updated',
                'Requisition status updated.',
            ),
        );
        return true;
    }
    const errorMsg = context.localize(
        '@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition__error_updating_status',
        `Unable to update status. {{errorMessage}}`,
        { errorMessage: purchaseRequisition.$.context.diagnoses },
    );
    loggers.requisitionLib.error(() => errorMsg);
    throw new BusinessRuleError(`${errorMsg}`);
};

export const closeRequisitionLine = async (
    context: Context,
    purchaseRequisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
) => {
    const purchaseRequisition = await context.read(
        xtremPurchasing.nodes.PurchaseRequisition,
        { _id: (await purchaseRequisitionLine.document)._id },
        { forUpdate: true },
    );
    const lineToUpdate = await purchaseRequisition.lines.find(
        line => line._id === purchaseRequisitionLine._id && _isRequisitionLineClosable(line),
    );
    if (lineToUpdate) {
        await lineToUpdate.$.set({ status: 'closed' });
    } else {
        const errorMsg = context.localize(
            '@sage/xtrem-purchasing/functions-purchase-Requisition-lib_purchase_requisition_line__error_updating_status_Requisition_line_not_closable',
            `Unable to update status on already closed, received, pending approval requisition line.`,
        );
        loggers.requisitionLib.error(() => errorMsg);
        throw new BusinessRuleError(`${errorMsg}`);
    }
    if (await purchaseRequisition.$.control()) {
        await purchaseRequisition.$.save();

        loggers.requisitionLib.info(() =>
            context.localize(
                '@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition_line__status_updated',
                'Requisition line status updated.',
            ),
        );
        return true;
    }
    const errorMsg = context.localize(
        '@sage/xtrem-purchasing/functions-purchase-requisition-lib_purchase_requisition_line__error_updating_status',
        `Unable to update line status. {{errorMessage}}`,
        { errorMessage: purchaseRequisition.$.context.diagnoses },
    );
    loggers.requisitionLib.error(() => errorMsg);
    throw new BusinessRuleError(`${errorMsg}`);
};

/**
 * Calculates the display status of the requisition based on the other document status properties
 * @param status
 * @param orderStatus **Warning** serialNumbers must be writable
 * @param approvalStatus
 * @returns display status enum
 */
export function calculatePurchaseRequisitionDisplayStatus(
    status: xtremPurchasing.enums.PurchaseDocumentStatus,
    orderStatus: xtremPurchasing.enums.PurchaseRequisitionOrderStatus,
    approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
): xtremPurchasing.enums.PurchaseRequisitionDisplayStatus {
    if (approvalStatus === 'rejected') {
        return 'rejected';
    }
    if (orderStatus === 'ordered') {
        return 'ordered';
    }
    if (status === 'closed') {
        return 'closed';
    }
    if (orderStatus === 'partiallyOrdered') {
        return 'partiallyOrdered';
    }
    if (['pendingApproval', 'approved', 'confirmed'].includes(approvalStatus)) {
        return approvalStatus as xtremPurchasing.enums.PurchaseRequisitionDisplayStatus;
    }
    return 'draft';
}

export function controlConfirm(
    context: Context,
    requisition: {
        status: xtremPurchasing.enums.PurchaseDocumentStatus;
        isPurchaseRequisitionApprovalManaged: boolean;
        isSafeToRetry: boolean;
    },
): boolean {
    if (requisition.status !== 'draft' || requisition.isPurchaseRequisitionApprovalManaged) {
        xtremMasterData.functions.throwErrorOrLogWarn({
            errorMessage: context.localize(
                '@sage/xtrem-purchasing/status_and_is_purchase_requisition_approval_managed',
                'Document status needs to be "Draft" and approval workflow disabled.',
            ),
            isSafeToRetry: requisition.isSafeToRetry,
            logger: loggers.requisitionLib,
        });
        return false;
    }
    return true;
}
