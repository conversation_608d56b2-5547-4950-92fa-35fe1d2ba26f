import type { Context, NodeQueryFilter, date } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { merge } from 'lodash';
import type * as xtremPurchasing from '../index';

function getCompanyFilter(company: string | undefined): NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine> {
    return company ? { document: { stockSite: { legalCompany: { id: { _eq: company } } } } } : {};
}

function getStockSitesFilter(
    stockSitesIds: number[] | undefined,
): NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine> {
    return stockSitesIds && stockSitesIds.length > 0 ? { site: { _id: { _in: stockSitesIds } } } : {};
}

function getFromSupplierFilter(
    fromSupplierId: string | undefined,
): NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine> {
    return fromSupplierId
        ? {
              document: {
                  billBySupplier: {
                      businessEntity: {
                          id: {
                              _gte: fromSupplierId,
                          },
                      },
                  },
              },
          }
        : {};
}

function getToSupplierFilter(
    toSupplierId: string | undefined,
): NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine> {
    return toSupplierId
        ? {
              document: {
                  billBySupplier: {
                      businessEntity: {
                          id: {
                              _lte: toSupplierId,
                          },
                      },
                  },
              },
          }
        : {};
}

/**
 * This function is used to create the filter for the purchase receipt lines display in unbilled accounts payable
 * @param searchCriteria - Parameters introduced in the header of the query
 * @returns NodeQueryFilter<xtremPurchasing.nodes.receiptLine>
 */
export function getFilterPurchaseReceiptLines(
    searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountPayableSearch,
): NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine> {
    // build filter from search criteria
    const filter: NodeQueryFilter<xtremPurchasing.nodes.PurchaseReceiptLine> = {
        document: { date: { _lte: searchCriteria.asOfDate } },
        netPrice: { _gt: 0 },
        status: { _in: ['pending', 'closed', 'inProgress', 'posted'] },
    };

    merge(filter, getCompanyFilter(searchCriteria.company));
    merge(filter, getStockSitesFilter(searchCriteria.stockSites));
    merge(filter, getFromSupplierFilter(searchCriteria.fromSupplier));
    merge(filter, getToSupplierFilter(searchCriteria.toSupplier));

    return filter;
}

// if receipt line is partially or fully invoiced and the invoice is posted, loop over linked invoice lines and add
// invoiced quantity to the total invoice quantity. Unfortunately we can't just use the 'invoicedQuantity' property
// of the purchase receipt line as we may only add the quantities of the invoice lines with a date
// earlier than the 'As of date' of the criteria block of the inquiry.
async function getInvoiceQuantity(parameters: {
    receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine;
    asOfDate: date;
}): Promise<number> {
    if (['invoiced', 'partiallyInvoiced'].includes(await parameters.receiptLine.lineInvoiceStatus)) {
        return parameters.receiptLine.purchaseInvoiceLines
            .filter(async invoiceLine => {
                const purchaseInvoice = await (await invoiceLine.purchaseInvoiceLine).document;

                return (
                    (await purchaseInvoice.status) === 'posted' &&
                    (await purchaseInvoice.invoiceDate).compare(parameters.asOfDate) <= 0
                );
            })
            .sum(async line => (await line.purchaseInvoiceLine).quantity);
    }
    return 0;
}

// if receipt line is partially or fully returned loop over linked return lines and add returned quantity
// to the total returned quantity. Unfortunately we can't just use the 'returnedQuantity' property
// of the purchase receipt line as we may only add the quantities of the return lines with a date
// earlier than the 'As of date' of the criteria block of the inquiry.
// Additionally we accumulate the credited quantity for the same return lines
async function getReturnedAndCreditedQuantity(parameters: {
    receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine;
    asOfDate: date;
    quantities: xtremFinanceData.interfaces.UnbilledInquiriesQuantities;
}): Promise<xtremFinanceData.interfaces.UnbilledInquiriesQuantities> {
    if (['returned', 'partiallyReturned'].includes(await parameters.receiptLine.lineReturnStatus)) {
        return parameters.receiptLine.purchaseReturnLines
            .filter(
                async returnLine =>
                    (await (await (await returnLine.purchaseReturnLine).document).returnRequestDate).compare(
                        parameters.asOfDate,
                    ) <= 0,
            )
            .reduce(async (current: xtremFinanceData.interfaces.UnbilledInquiriesQuantities, line) => {
                const purchaseReturnLine = await line.purchaseReturnLine;
                return {
                    invoiced: current.invoiced,
                    credited: current.credited + +(await purchaseReturnLine.creditedQuantity),
                    returned: current.returned + +(await purchaseReturnLine.quantity),
                };
            }, parameters.quantities);
    }
    return parameters.quantities;
}

/**
 * This function is used to create the filter for the purchase receipt lines display in unbilled accounts payable
 * @param context - Context
 * @param parameters.searchCriteria - Parameters introduced in the header of the query
 * @returns NodeQueryFilter<xtremPurchasing.nodes.receiptLine>
 */
export async function generateUnbilledAccountPayableLine(parameters: {
    context: Context;
    searchCriteria: xtremFinanceData.interfaces.FinanceUnbilledAccountPayableSearch;
    receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine;
}): Promise<xtremFinanceData.interfaces.FinanceUnbilledAccountPayable> {
    const { invoiced, credited, returned } = await getReturnedAndCreditedQuantity({
        ...parameters,
        asOfDate: parameters.searchCriteria.asOfDate,
        quantities: {
            invoiced: await getInvoiceQuantity({
                ...parameters,
                asOfDate: parameters.searchCriteria.asOfDate,
            }),
            credited: 0,
            returned: 0,
        },
    });

    const { invoicedQuantity, invoicedAmount } = xtremFinanceData.functions.Common.calculateInvoicedValues({
        lineQuantity: await parameters.receiptLine.quantity,
        lineNetPrice: await parameters.receiptLine.netPrice,
        quantities: { invoiced, credited, returned },
    });

    // Performance: Some values not to await more than once
    const document = await parameters.receiptLine.document;
    const stockSite = await parameters.receiptLine.site;
    const legalCompany = await (await document.financialSite).legalCompany;
    const currency = await parameters.receiptLine.currency;
    const companyCurrency = await document.companyCurrency;
    const quantity = await parameters.receiptLine.quantity;
    const tax = await (await parameters.receiptLine.taxes.at(0))?.taxReference;

    const amountExcludingTaxInCompanyCurrency = await parameters.receiptLine.amountExcludingTaxInCompanyCurrency;
    const invoiceReceivableAmountInCompanyCurrency = Decimal.roundAt(
        (amountExcludingTaxInCompanyCurrency * invoicedQuantity) / quantity,
        await companyCurrency.decimalDigits,
    );
    const invoiceReceivableAmountInCompanyCurrencyAtAsOfDate = await xtremMasterData.functions.convertCurrency(
        parameters.context,
        invoicedAmount,
        currency,
        companyCurrency,
        parameters.searchCriteria.asOfDate,
    );

    return {
        billBySupplier: await parameters.receiptLine.billBySupplier,
        currency,
        financialSite: await parameters.receiptLine.financialSite,
        purchaseUnit: await parameters.receiptLine.unit,
        netPrice: await parameters.receiptLine.netPrice,
        item: await parameters.receiptLine.item,
        stockSite,
        quantity,
        invoicedQuantity: invoiced,
        creditedQuantity: credited,
        returnedQuantity: returned,
        invoiceReceivableQuantity: invoicedQuantity,
        invoiceReceivableAmount: invoicedAmount,
        invoiceReceivableAmountInCompanyCurrency,
        invoiceReceivableAmountInCompanyCurrencyAtAsOfDate,
        companyCurrency,
        account: await xtremFinanceData.functions.Common.getAccount(parameters.context, {
            chartOfAccount: await legalCompany.chartOfAccount,
            legislation: await legalCompany.legislation,
            type: 'supplier',
            id: 'ApGrni',
            postingClassDetailed: await (await document.billBySupplier).postingClass,
            invoicedQuantity,
            tax,
        }),
        accountItem: await xtremFinanceData.functions.Common.getAccount(parameters.context, {
            chartOfAccount: await legalCompany.chartOfAccount,
            legislation: await legalCompany.legislation,
            type: 'item',
            id: 'PurchaseExpense',
            postingClassDetailed: await (await parameters.receiptLine.item).postingClass,
            invoicedQuantity,
            tax,
        }),
        company: await stockSite.legalCompany,
        receiptNumber: await document.number,
        receiptInternalId: document._id,
        documentDate: await document.documentDate,
        supplier: (await parameters.receiptLine.supplier) || (await document.businessRelation),
    };
}
