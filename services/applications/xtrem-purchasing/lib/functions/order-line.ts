import type { PurchaseOrderReceiptStatus } from '../enums';
import type * as xtremPurchasing from '../index';

export async function getLineStatuses(orderLine: xtremPurchasing.nodes.PurchaseOrderLine): Promise<{
    newLineInvoiceStatus: xtremPurchasing.enums.PurchaseOrderInvoiceStatus;
    newLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus;
}> {
    const invoicedQty = await orderLine.purchaseInvoiceLines.sum(line => line.invoicedQuantityInStockUnit);

    if (invoicedQty >= (await orderLine.quantityInStockUnit)) {
        return { newLineInvoiceStatus: 'invoiced', newLineStatus: 'closed' };
    }
    return { newLineInvoiceStatus: 'partiallyInvoiced', newLineStatus: 'inProgress' };
}

export async function getLineInvoiceStatus(line: xtremPurchasing.nodes.PurchaseOrderLine) {
    if ((await line.invoicedQuantityInStockUnit) === 0) {
        return 'notInvoiced';
    }
    if ((await line.invoicedQuantityInStockUnit) < (await line.quantityInStockUnit)) {
        return 'partiallyInvoiced';
    }
    return 'invoiced';
}

export async function getLineReceiptStatus(line: xtremPurchasing.nodes.PurchaseOrderLine) {
    if ((await line.receivedQuantityInStockUnit) === 0) {
        return 'notReceived';
    }
    if ((await line.receivedQuantityInStockUnit) < (await line.quantityInStockUnit)) {
        return 'partiallyReceived';
    }
    // TODO: XT-64122 If lineReceiptStatus becomes 'received', the closing must be handled at the same time
    // This is the job of the node PurchaseOrderLineToPurchaseReceiptLine
    return 'received';
}

export async function manageStatus(orderLine: xtremPurchasing.nodes.PurchaseOrderLine): Promise<{
    lineReceiptStatus: PurchaseOrderReceiptStatus;
    status: xtremPurchasing.enums.PurchaseDocumentStatus;
    isUsingFunctionToClose?: boolean;
}> {
    const isLineCompleted = await orderLine.purchaseReceiptLines.some(
        async line =>
            (await (await line.purchaseReceiptLine).completed) &&
            (await (await line.purchaseReceiptLine).status) === 'pending',
    );
    const isDraftLine = await orderLine.purchaseReceiptLines.some(
        async line => (await (await line.purchaseReceiptLine).status) === 'draft',
    );
    const isLineReceived = await orderLine.purchaseReceiptLines.some(
        async line => (await (await line.purchaseReceiptLine).status) !== 'draft',
    );
    const newLineReceiptStatus: xtremPurchasing.enums.PurchaseOrderReceiptStatus = isLineReceived
        ? 'partiallyReceived'
        : 'notReceived';

    // TODO: check if purchaseOrderLine.receivedQuantityInStockUnit takes into account the current receivedQuantity
    if (
        (!isDraftLine && (await orderLine.receivedQuantityInStockUnit) >= (await orderLine.quantityInStockUnit)) ||
        isLineCompleted
    ) {
        return { lineReceiptStatus: 'received', status: 'closed', isUsingFunctionToClose: true };
    }

    return { lineReceiptStatus: newLineReceiptStatus, status: 'inProgress' };
}
