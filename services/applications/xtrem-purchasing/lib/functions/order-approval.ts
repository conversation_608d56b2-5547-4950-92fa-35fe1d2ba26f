import type { Context } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremPurchasing from '../index';
import type { PurchaseEmailDocumentData } from '../interfaces/send-mail';
import { checkApprovalStatus } from './document-approval';

export async function changesRequestedText(purchaseDocument: xtremPurchasing.nodes.PurchaseOrder): Promise<string> {
    const { context } = purchaseDocument.$;
    return context.localize(
        '@sage/xtrem-purchasing/functions__purchase__order__request_changes_email_subject',
        'Changes requested for this purchase order: [Purchase order {{number}}]',
        { number: await purchaseDocument.number },
    );
}

async function approvalRequestText(purchaseDocument: xtremPurchasing.nodes.PurchaseOrder): Promise<string> {
    const { context } = purchaseDocument.$;
    return context.localize(
        '@sage/xtrem-purchasing/functions__purchase__order__approval_email_subject',
        'This purchase order needs approval: [Purchase order {{number}}]',
        { number: await purchaseDocument.number },
    );
}

export async function getPurchaseOrderApprovalData(
    context: Context,
    purchaseDocument: xtremPurchasing.nodes.PurchaseOrder,
): Promise<PurchaseEmailDocumentData<xtremPurchasing.nodes.PurchaseOrder>> {
    checkApprovalStatus(context, await purchaseDocument.approvalStatus);

    const { urlReject, urlApprove } = xtremMasterData.functions.generateUrl(
        await purchaseDocument.approvalUrl,
        purchaseDocument._id,
    );
    const buyer = await purchaseDocument.defaultBuyer;
    return {
        subject: await approvalRequestText(purchaseDocument),
        purchaseDocument,
        data: {
            number: await purchaseDocument.number,
            supplier: await (await (await purchaseDocument.businessRelation).businessEntity).name,
            receivingSite: await (await purchaseDocument.stockSite).name, // keep receivingSite because report is using it
            orderDate: (await purchaseDocument.orderDate).toString(),
            url: await purchaseDocument.approvalUrl,
            requester: `${await buyer.firstName} ${await buyer.lastName}`,
            requestDate: date.today().toString(),
            urlReject,
            urlApprove,
            urlPurchaseDocument: await purchaseDocument.documentUrl,
        },
    };
}
