import { registerSqlFunction } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../../index';

export async function getCurrencyDecimalDigits(
    document:
        | xtremPurchasing.nodes.PurchaseOrder
        | xtremPurchasing.nodes.PurchaseReceipt
        | xtremPurchasing.nodes.PurchaseReturn,
) {
    const docSite = await document.site;
    const legalCompany = await docSite.legalCompany;
    const currency = await legalCompany.currency;
    return currency.decimalDigits;
}

registerSqlFunction('xtremPurchasing.functions.BaseDocument.getCurrencyDecimalDigits', getCurrencyDecimalDigits);

export async function updateLineToStatuses(
    orderLineToLine:
        | xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine
        | xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine,
) {
    const lineTo =
        orderLineToLine instanceof xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine
            ? await orderLineToLine.purchaseOrderLine
            : await orderLineToLine.purchaseReceiptLine;

    await xtremMasterData.functions.lineToLineLib.updateLinkedLine({
        originDocumentLine: await orderLineToLine.purchaseInvoiceLine,
        targetDocumentLine: lineTo,
    });
}
