import type { Context } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremPurchasing from '../index';
import type { PurchaseEmailDocumentData } from '../interfaces/send-mail';
import { checkApprovalStatus, getDocumenturl } from './document-approval';

export async function areChangeRequestedText(
    context: Context,
    purchaseDocument: xtremPurchasing.nodes.PurchaseRequisition,
): Promise<string> {
    return context.localize(
        '@sage/xtrem-purchasing/functions__purchase__requisition__request_changes_email_subject',
        '[Purchase requisition {{purchaseRequisitionNumber}}] changes requested',
        { purchaseRequisitionNumber: await purchaseDocument.number },
    );
}

async function approvalRequestText(
    context: Context,
    purchaseDocument: xtremPurchasing.nodes.PurchaseRequisition,
): Promise<string> {
    return context.localize(
        '@sage/xtrem-purchasing/functions__purchase__requisition__approval_email_subject',
        '[Purchase requisition {{purchaseRequisitionNumber}}] approval request',
        { purchaseRequisitionNumber: await purchaseDocument.number },
    );
}

export async function getPurchaseRequistionApprovalData(
    context: Context,
    document: xtremPurchasing.nodes.PurchaseRequisition,
): Promise<PurchaseEmailDocumentData<xtremPurchasing.nodes.PurchaseRequisition>> {
    checkApprovalStatus(context, await document.approvalStatus);

    const purchaseDocumentRequester = await document.requester;
    const { urlReject, urlApprove } = xtremMasterData.functions.generateUrl(await document.approvalUrl, document._id);
    return {
        purchaseDocument: document,
        data: {
            number: await document.number,
            requester: `${await purchaseDocumentRequester.lastName}, ${await purchaseDocumentRequester.firstName}`,
            receivingSite: await (await document.site).name,
            requestDate: (await document.requestDate).toString(),
            urlReject,
            urlApprove,
            urlPurchaseDocument: await getDocumenturl(document),
        },
        subject: await approvalRequestText(context, document),
    };
}
