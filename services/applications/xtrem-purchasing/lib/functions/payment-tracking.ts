import type { Context } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '..';

async function updatePurchaseDocument(
    context: Context,
    documentSysId: number,
): Promise<xtremPurchasing.nodes.BasePurchaseDocument | null> {
    const existingDocument = await context.tryRead(
        xtremPurchasing.nodes.BasePurchaseDocument,
        { _id: documentSysId },
        { forUpdate: true },
    );
    if (existingDocument && (await existingDocument.paymentTracking) === null) {
        await existingDocument.$.set({
            paymentTracking: {
                paymentTerm: await existingDocument.paymentTerm,
            },
            forceUpdateForFinance: true, // make sure the update of the Purchase document will not be refused by the control in saveBegin
        });
        await existingDocument.$.save();
    }
    return existingDocument;
}

async function updateFinanceTransaction(
    context: Context,
    financeTransactionSysId: number,
    paymentTracking: xtremFinanceData.nodes.PaymentTracking | null,
): Promise<void> {
    const financeTransactionRecordToUpdate = await context.read(
        xtremFinanceData.nodes.FinanceTransaction,
        { _id: financeTransactionSysId },
        { forUpdate: true },
    );
    await financeTransactionRecordToUpdate.$.set({ paymentTracking });
    await financeTransactionRecordToUpdate.$.save();
}

async function notifyApInvoice(
    context: Context,
    targetDocumentSysId: number,
    paymentTrackingSysId: number | undefined,
): Promise<boolean> {
    if (targetDocumentSysId !== 0) {
        await context.notify('AccountsReceivableInvoice/initPaymentTracking', {
            _id: targetDocumentSysId,
            paymentTracking: paymentTrackingSysId,
        });
        return true;
    }
    return false;
}

async function handleFinanceTransactionRecord(
    context: Context,
    financeTransactionRecord: xtremFinanceData.nodes.FinanceTransaction,
): Promise<boolean> {
    let apInvoiceUpdated = false;
    const existingDocument = await updatePurchaseDocument(context, await financeTransactionRecord.documentSysId);
    if (existingDocument) {
        const paymentTracking = await existingDocument.paymentTracking;

        await updateFinanceTransaction(context, financeTransactionRecord._id, paymentTracking);

        apInvoiceUpdated = await notifyApInvoice(
            context,
            await financeTransactionRecord.targetDocumentSysId,
            paymentTracking?._id,
        );
    }
    return apInvoiceUpdated;
}

export async function initPaymentTracking(context: Context): Promise<void> {
    let atLeastOneApInvoiceUpdated = false;
    const financeTransactionRecords = context.query(xtremFinanceData.nodes.FinanceTransaction, {
        filter: { targetDocumentType: 'accountsPayableInvoice', paymentTracking: null },
    });
    await financeTransactionRecords.forEach(async financeTransactionRecord => {
        atLeastOneApInvoiceUpdated =
            (await handleFinanceTransactionRecord(context, financeTransactionRecord)) || atLeastOneApInvoiceUpdated;
    });
    if (atLeastOneApInvoiceUpdated) {
        await context.notifyUser({
            title: context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_invoice__payment_tracking',
                'Payment tracking service option activated',
            ),
            description: context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_invoice__information_payment_tracking',
                'Update the open items from the AP/AR aging balance',
            ),
            icon: 'info',
            level: 'success',
            shouldDisplayToast: true,
            actions: [],
        });
    }
}

export function isPurchaseInvoiceOrPurchaseCreditMemo(
    document: xtremMasterData.nodes.BaseDocument,
): document is xtremPurchasing.nodes.PurchaseInvoice | xtremPurchasing.nodes.PurchaseCreditMemo {
    return ['PurchaseInvoice', 'PurchaseCreditMemo'].includes(document.$.factory.name);
}
