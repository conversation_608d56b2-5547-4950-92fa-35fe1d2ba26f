import type {
    AsyncArrayReader,
    Context,
    NodeCreateData,
    NodeUpdateData,
    UpdateAction,
    integer,
} from '@sage/xtrem-core';
import { BusinessRuleError, Diagnose, NodeStatus, SystemError, asyncArray, date } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { LogicError, ValidationSeverity } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremStructure from '@sage/xtrem-structure';
import type * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremPurchasing from '../index';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderInternalNote,
    setHeaderSetupNote,
    setLineInternalNote,
} from './common';
import { loggers } from './index';

interface PurchaseAddressData {
    isActive: boolean;
    name: string;
    addressLine1: string;
    addressLine2: string;
    city: string;
    region: string;
    postcode: string;
    country: xtremStructure.nodes.Country | null;
    locationPhoneNumber: string;
}

export const getPurchaseOrderLineStockSiteAddress = async (
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
) => {
    const purchaseOrderLineStockSiteAddress = await purchaseOrderLine.stockSiteLinkedAddress;
    return {
        isActive: true,
        name: await purchaseOrderLineStockSiteAddress.name,
        addressLine1: await purchaseOrderLineStockSiteAddress.addressLine1,
        addressLine2: await purchaseOrderLineStockSiteAddress.addressLine2,
        city: await purchaseOrderLineStockSiteAddress.city,
        region: await purchaseOrderLineStockSiteAddress.region,
        postcode: await purchaseOrderLineStockSiteAddress.postcode,
        country: await purchaseOrderLineStockSiteAddress.country,
        locationPhoneNumber: await purchaseOrderLineStockSiteAddress.locationPhoneNumber,
    };
};

export async function getPurchaseOrderLineStockSiteAddressDetail(
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<PurchaseAddressData> {
    const purchaseOrderLineStockSiteAddressDetail = await purchaseOrderLine.stockSiteAddress;
    return purchaseOrderLineStockSiteAddressDetail
        ? {
              isActive: true,
              name: await purchaseOrderLineStockSiteAddressDetail?.name,
              addressLine1: await purchaseOrderLineStockSiteAddressDetail?.addressLine1,
              addressLine2: await purchaseOrderLineStockSiteAddressDetail?.addressLine2,
              city: await purchaseOrderLineStockSiteAddressDetail?.city,
              region: await purchaseOrderLineStockSiteAddressDetail?.region,
              postcode: await purchaseOrderLineStockSiteAddressDetail?.postcode,
              country: await purchaseOrderLineStockSiteAddressDetail?.country,
              locationPhoneNumber: await purchaseOrderLineStockSiteAddressDetail?.locationPhoneNumber,
          }
        : getPurchaseOrderLineStockSiteAddress(purchaseOrderLine);
}

export async function getPurchaseOrderSupplierAddress(
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<PurchaseAddressData> {
    const purchaseOrderLineSupplierAddress = await (await purchaseOrderLine.document).supplierLinkedAddress;
    return {
        isActive: true,
        name: await purchaseOrderLineSupplierAddress.name,
        addressLine1: await purchaseOrderLineSupplierAddress.addressLine1,
        addressLine2: await purchaseOrderLineSupplierAddress.addressLine2,
        city: await purchaseOrderLineSupplierAddress.city,
        region: await purchaseOrderLineSupplierAddress.region,
        postcode: await purchaseOrderLineSupplierAddress.postcode,
        country: await purchaseOrderLineSupplierAddress.country,
        locationPhoneNumber: await purchaseOrderLineSupplierAddress.locationPhoneNumber,
    };
}

export async function getPurchaseOrderSupplierAddressDetail(
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<PurchaseAddressData> {
    const purchaseOrderLineSupplierAddressDetail = await (await purchaseOrderLine.document).supplierAddress;

    return purchaseOrderLineSupplierAddressDetail
        ? {
              isActive: true,
              name: await purchaseOrderLineSupplierAddressDetail.name,
              addressLine1: await purchaseOrderLineSupplierAddressDetail.addressLine1,
              addressLine2: await purchaseOrderLineSupplierAddressDetail.addressLine2,
              city: await purchaseOrderLineSupplierAddressDetail.city,
              region: await purchaseOrderLineSupplierAddressDetail.region,
              postcode: await purchaseOrderLineSupplierAddressDetail.postcode,
              country: await purchaseOrderLineSupplierAddressDetail.country,
              locationPhoneNumber: await purchaseOrderLineSupplierAddressDetail.locationPhoneNumber,
          }
        : getPurchaseOrderSupplierAddress(purchaseOrderLine);
}

async function sameSupplierAddress(
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>,
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<boolean> {
    return xtremMasterData.functions.areSameAddresses(
        data.supplierAddress as xtremMasterData.nodes.Address,
        (await getPurchaseOrderSupplierAddressDetail(purchaseOrderLine)) as unknown as xtremMasterData.nodes.Address,
    );
}

async function sameStockSiteAddress(
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>,
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<boolean> {
    return xtremMasterData.functions.areSameAddresses(
        data.receivingAddress as xtremMasterData.nodes.Address,
        (await getPurchaseOrderLineStockSiteAddressDetail(
            purchaseOrderLine,
        )) as unknown as xtremMasterData.nodes.Address,
    );
}

async function areDocumentsHavingSameAddresses(
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>,
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<boolean> {
    return (await sameSupplierAddress(data, purchaseOrderLine)) && sameStockSiteAddress(data, purchaseOrderLine);
}

/**
 * This function is used for verifying that input properties are the same as the ones from the current order to create header
 * @param site
 * @param supplier
 * @param currency
 * @param data - contains the order header creation payload data
 * @returns true/false
 * Order header grouping conditioned properties :
 * - site
 * - supplier
 * - currency
 */
async function _newPurchaseReceiptsCreateDataBreakCondition(
    stockSite: xtremSystem.nodes.Site,
    supplier: xtremMasterData.nodes.Supplier,
    currency: xtremMasterData.nodes.Currency | null,
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>,
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<boolean> {
    await loggers.orderLib.debugAsync(
        async () => `
        newPurchaseReceiptsCreateDataBreakCondition - data/param
            site: ${data.site} / ${stockSite._id}
            supplier: ${data.businessRelation} / ${supplier._id}
            currency: ${data.currency} / ${currency ? currency._id : null}
            date: ${data.documentDate} / ${await purchaseOrder.orderDate}
        `,
    );
    await loggers.orderLib.debugAsync(
        async () => `
        newPurchaseReceiptsCreateDataBreakCondition -
             data receiving address ${JSON.stringify(data.receivingAddress)}
             order line stock site address ${await (await purchaseOrderLine.stockSiteLinkedAddress).concatenatedAddress}
             order line stock site address detail ${await (
                 await purchaseOrderLine.stockSiteAddress
             )?.concatenatedAddress}
             data supplier address ${JSON.stringify(data.supplierAddress)}
             order supplier address ${await (
                 await (
                     await purchaseOrderLine.document
                 ).supplierLinkedAddress
             ).concatenatedAddress}
             order supplier address detail ${await (
                 await (
                     await purchaseOrderLine.document
                 ).supplierAddress
             )?.concatenatedAddress}`,
    );
    const stockSite_id = xtremMasterData.functions.getIdFromNodeCreateDataProperty<typeof data, xtremSystem.nodes.Site>(
        data.site,
    );
    const supplier_id = xtremMasterData.functions.getIdFromNodeCreateDataProperty<typeof data, xtremSystem.nodes.Site>(
        data.businessRelation,
    );
    const currency_id = xtremMasterData.functions.getIdFromNodeCreateDataProperty<
        typeof data,
        xtremMasterData.nodes.Currency
    >(data.currency);

    await loggers.orderLib.debugAsync(
        async () => `
        newPurchaseReceiptsCreateDataBreakCondition -
            areDocumentsHavingSameAddresses=${await areDocumentsHavingSameAddresses(data, purchaseOrderLine)}
        `,
    );
    loggers.orderLib.debug(
        () => `
        newPurchaseReceiptsCreateDataBreakCondition -
            currency_id=${currency_id}
            site_id=${stockSite_id}
            supplier_id=${supplier_id}
            `,
    );
    await loggers.orderLib.debugAsync(
        async () => `
        newPurchaseReceiptsCreateDataBreakCondition -
            returning ${
                stockSite_id &&
                stockSite_id === stockSite._id &&
                supplier_id &&
                supplier_id === supplier._id &&
                (!currency || !data.currency || (currency_id && currency_id === currency._id)) &&
                (await areDocumentsHavingSameAddresses(data, purchaseOrderLine))
            }`,
    );

    return (
        stockSite_id !== null &&
        stockSite_id === stockSite._id &&
        supplier_id !== null &&
        supplier_id === supplier._id &&
        (!currency || !data.currency || (currency_id !== null && currency_id === currency._id)) &&
        areDocumentsHavingSameAddresses(data, purchaseOrderLine)
    );
}

/**
 * This function is used to add a dependency from the purchase order line to be created, to the purchase requisition line it was created from
 * @param purchaseReceiptLineData
 * @param purchaseOrderLine
 */
const _addNewPurchaseReceiptLineToPurchaseOrderLineDependency = async (
    purchaseReceiptLineData: NodeCreateData<
        xtremPurchasing.nodes.PurchaseReceiptLine & { _sortValue?: number; _action: UpdateAction }
    >,
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
) => {
    if (!purchaseReceiptLineData.purchaseOrderLine) {
        purchaseReceiptLineData.purchaseOrderLine = await purchaseOrderLine.$.context.create(
            xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine,
            { purchaseOrderLine },
        );
    }
};

/**
 * This function is used to add/update a purchase order create data
 * @param newPurchaseReceiptsCreateData
 * @param purchaseOrderLine
 * @param receivingSite
 * @param receiptSupplier
 */
const _addPurchaseReceiptDataEntry = async (
    newPurchaseReceiptsCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>[],
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
    receivingSite: xtremSystem.nodes.Site,
    receiptSupplier: xtremMasterData.nodes.Supplier,
    isForFinanceCheck: boolean,
) => {
    let newEntry = false;
    const tmpData =
        (await asyncArray(newPurchaseReceiptsCreateData).find(
            async (data: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>): Promise<boolean> => {
                const onSameReceipt = await _newPurchaseReceiptsCreateDataBreakCondition(
                    receivingSite,
                    receiptSupplier,
                    await (
                        await purchaseOrderLine.document
                    ).currency,
                    await purchaseOrderLine.document,
                    data,
                    purchaseOrderLine,
                );
                loggers.orderLib.debug(
                    () => `
                _addPurchaseReceiptDataEntry -
                    inside newPurchaseReceiptsCreateData.find : onSameReceipt=${onSameReceipt}`,
                );
                return onSameReceipt;
            },
        )) || {};
    loggers.orderLib.debug(
        () => `
        _addPurchaseReceiptDataEntry -
            found compatible receipt header ${JSON.stringify(tmpData)}`,
    );

    const newPurchaseReceiptsCreateDataIndex = newPurchaseReceiptsCreateData.indexOf(tmpData);

    loggers.orderLib.debug(
        () => `
        _addPurchaseReceiptDataEntry -
            indexOf a compatible receipt header ${newPurchaseReceiptsCreateDataIndex}`,
    );

    let purchaseReceiptData =
        newPurchaseReceiptsCreateDataIndex !== -1
            ? newPurchaseReceiptsCreateData[newPurchaseReceiptsCreateDataIndex]
            : null;
    const purchaseOrderLineDocument = await purchaseOrderLine.document;
    const purchaseOrderLineDocumentSupplierAddress = await purchaseOrderLineDocument.supplierLinkedAddress;
    if (!purchaseReceiptData) {
        purchaseReceiptData = {
            site: receivingSite._id,
            businessRelation: String(receiptSupplier._id),
            billBySupplier: await purchaseOrderLineDocument.billBySupplier,
            currency: await purchaseOrderLineDocument.currency,
            supplierAddress: await getPurchaseOrderSupplierAddressDetail(purchaseOrderLine),
            receivingAddress: await getPurchaseOrderLineStockSiteAddressDetail(purchaseOrderLine),
            isOverwriteNote: true,
            returnAddress: {
                name: await purchaseOrderLineDocumentSupplierAddress.name,
                addressLine1: await purchaseOrderLineDocumentSupplierAddress.addressLine1,
                addressLine2: await purchaseOrderLineDocumentSupplierAddress.addressLine2,
                city: await purchaseOrderLineDocumentSupplierAddress.city,
                region: await purchaseOrderLineDocumentSupplierAddress.region,
                postcode: (await purchaseOrderLineDocumentSupplierAddress?.postcode) || '',
                country: await purchaseOrderLineDocumentSupplierAddress.country,
                locationPhoneNumber: await purchaseOrderLineDocumentSupplierAddress.locationPhoneNumber,
            },
        };
        newEntry = true;
    }
    if (!purchaseReceiptData.lines || !purchaseReceiptData.lines.length) {
        // No order lines data yet
        purchaseReceiptData.lines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseReceiptLine & { _sortValue?: number; _action: UpdateAction }
        >[];
    }

    const isZeroAmount = (await purchaseOrderLine.grossPrice) === 0;

    const purchaseReceiptDataLinesLength = purchaseReceiptData.lines.push({
        item: await purchaseOrderLine.item,
        itemDescription: await purchaseOrderLine.itemDescription,
        quantity: await purchaseOrderLine.quantityToReceive,
        unit: await purchaseOrderLine.unit,
        grossPrice: isForFinanceCheck && isZeroAmount ? 10 : await purchaseOrderLine.grossPrice,
        netPrice: isForFinanceCheck && isZeroAmount ? 10 : await purchaseOrderLine.netPrice,
        discount: await purchaseOrderLine.discount,
        charge: await purchaseOrderLine.charge,
        unitToStockUnitConversionFactor: await purchaseOrderLine.unitToStockUnitConversionFactor,
        currency: await purchaseOrderLine.currency,
        priceOrigin: await purchaseOrderLine.priceOrigin,
        storedDimensions: await purchaseOrderLine.storedDimensions,
        storedAttributes: await purchaseOrderLine.storedAttributes,
        purchaseOrderLine: { purchaseOrderLine },
        returnAddress: {
            name: await purchaseOrderLineDocumentSupplierAddress.name,
            addressLine1: await purchaseOrderLineDocumentSupplierAddress.addressLine1,
            addressLine2: await purchaseOrderLineDocumentSupplierAddress.addressLine2,
            city: await purchaseOrderLineDocumentSupplierAddress.city,
            region: await purchaseOrderLineDocumentSupplierAddress.region,
            postcode: await purchaseOrderLineDocumentSupplierAddress.postcode,
            country: await purchaseOrderLineDocumentSupplierAddress.country,
            locationPhoneNumber: await purchaseOrderLineDocumentSupplierAddress.locationPhoneNumber,
        },
        taxes: await xtremDistribution.functions.addNewDistributionDocumentLineTaxDependency(purchaseOrderLine),
    });

    if (purchaseReceiptDataLinesLength) {
        await _addNewPurchaseReceiptLineToPurchaseOrderLineDependency(
            purchaseReceiptData.lines[purchaseReceiptDataLinesLength - 1],
            purchaseOrderLine,
        );
    }
    if (newEntry) {
        newPurchaseReceiptsCreateData.push(purchaseReceiptData);
    }
};

/**
 * This function is used to create the purchase receipt create data from a single purchase order. It's called when receiving a purchase order
 * @param context
 * @param purchaseOrder
 * @returns an array of purchase receipts data that can be used as payload for the purchase receipt create method
 */
export const initPurchaseReceiptCreateData = async (
    context: Context,
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
    options?: xtremPurchasing.sharedFunctions.PrepareNodeCreateDataOptions,
) => {
    // This array will contain the purchase order create payload data
    const newPurchaseReceiptsCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>[] = [];

    const isForFinanceCheck = !!options?.isForFinanceCheck;

    await purchaseOrder.lines
        .filter(
            async line =>
                ['notReceived', 'partiallyReceived'].includes(await line.lineReceiptStatus) &&
                !(await line.purchaseReceiptLines.some(
                    async receiptLine => (await receiptLine.purchaseReceiptLine).completed,
                )) &&
                (await line.quantityToReceive) > 0 &&
                (['pending', 'inProgress'].includes(await line.status) || isForFinanceCheck) &&
                !!(await line.grossPrice),
        )
        .forEach(async purchaseOrderLine =>
            _addPurchaseReceiptDataEntry(
                newPurchaseReceiptsCreateData,
                purchaseOrderLine,
                await purchaseOrderLine.stockSite,
                await (
                    await purchaseOrderLine.document
                ).businessRelation,
                isForFinanceCheck,
            ),
        );
    return newPurchaseReceiptsCreateData;
};

/**
 * This function is used to create purchase receipt for each entry of the purchase receipt create data array. The purchase order header and lines statuses are updated
 * @param context
 * @param purchaseOrder
 * @param newPurchaseReceiptCreateData
 * @returns an array of created purchase receipt numbers if any, or an empty array
 */
export async function createPurchaseReceipt(
    context: Context,
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
    newPurchaseReceiptsCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseReceipt>[],
): Promise<xtremPurchasing.nodes.PurchaseReceipt[]> {
    await loggers.orderLib.debugAsync(
        async () => `Creating purchase receipt for order n°${await purchaseOrder.number}`,
    );
    const result: xtremPurchasing.nodes.PurchaseReceipt[] = [];
    await asyncArray(newPurchaseReceiptsCreateData).forEach(async purchaseReceiptData => {
        loggers.orderLib.debug(
            () => `Creating purchase receipt with the following payload: ${JSON.stringify(purchaseReceiptData)}`,
        );
        const newPurchaseReceipt = await context.create(xtremPurchasing.nodes.PurchaseReceipt, {
            ...purchaseReceiptData,
        });
        if (await newPurchaseReceipt.$.trySave()) {
            loggers.orderLib.debug(() => 'Purchase receipt created');
            result.push(newPurchaseReceipt);
        } else {
            const errorMsg = context.localize(
                '@sage/xtrem-purchasing/purchase_order__lib__purchase_receipt_creation_failed',
                'The creation of purchase receipt failed. {{diagnose}}.',
                { diagnose: context.diagnoses },
            );
            loggers.orderLib.error(() => errorMsg);
            throw new BusinessRuleError(`${errorMsg}`);
        }
    });

    return result;
}

/**
 * This function is used to create a purchase order in case of stock quantity is lower than the reorder point
 * @param instance (represent node extension class)
 * @param itemSiteSupplier (itemSiteSupplier node dictionary)
 * @param itemSupplier (itemSupplier node dictionary)
 * @param itemSite (itemSite node dictionary)
 * @returns null
 */
export async function createPurchaseOrderReplenishment(
    context: Context,
    item: xtremMasterData.nodes.Item,
    site: xtremSystem.nodes.Site,
    itemSiteSupplier: xtremMasterData.nodes.ItemSiteSupplier,
    itemSupplier: xtremMasterData.nodes.ItemSupplier,
    itemSite: xtremMasterData.nodes.ItemSite,
    shortfall: number,
): Promise<null> {
    let minimumPurchaseOrderQuantity = 0;
    if (itemSiteSupplier) {
        minimumPurchaseOrderQuantity = (await itemSiteSupplier.minimumPurchaseOrderQuantity) || 0;
    } else if (itemSupplier) {
        minimumPurchaseOrderQuantity = await itemSupplier.minimumPurchaseQuantity;
    }

    const quantity = xtremStockData.functions.reorderLib.reorderQuantityCalculation(
        minimumPurchaseOrderQuantity,
        await itemSite.batchQuantity,
        shortfall,
    );
    let supplierId = 0;
    if (itemSiteSupplier && (await itemSiteSupplier.supplier)) {
        supplierId = (await itemSiteSupplier.supplier)._id;
    } else if (itemSupplier && (await itemSupplier.supplier)) {
        supplierId = (await itemSupplier.supplier)._id;
    }
    // Make sure we have a supplier id otherwise we don't want to create a PO suggestion
    if (supplierId && quantity > (await itemSite.expectedQuantity)) {
        const newPurchaseOrder = await context.create(xtremPurchasing.nodes.PurchaseOrder, {
            site: site._id,
            businessRelation: supplierId,
            currency: (await (await site.legalCompany).currency)?._id,
            orderDate: date.today(),
            fxRateDate: date.today(),
            lines: [
                {
                    item: item._id,
                    quantity,
                    unit: (await item.stockUnit)._id,
                    grossPrice: await itemSite.stdCostValue,
                    status: 'draft',
                    isPurchaseOrderSuggestion: true,
                },
            ],
            status: 'draft',
            isPurchaseOrderSuggestion: true,
        });
        await newPurchaseOrder.$.save();
        await loggers.orderLib.infoAsync(async () =>
            context.localize(
                '@sage/xtrem-purchasing/functions-purchase-order-lib-purchase-order-created',
                '{{currentItem}} stock is below reordered point so purchase order was created.',
                { currentItem: await item.name },
            ),
        );
    }
    return null;
}

export const computeOrderStatusFromLinesStatuses = async (purchaseOrder: xtremPurchasing.nodes.PurchaseOrder) => {
    const lineStatuses = [
        ...(await purchaseOrder.lines.reduce(
            async (statuses, line) => statuses.add(await line.status),
            new Set<xtremPurchasing.enums.PurchaseDocumentStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'inProgress')) {
        return 'inProgress';
    }
    if (lineStatuses.some(status => status === 'pending')) {
        if ((await purchaseOrder.receiptStatus) === 'partiallyReceived') {
            return 'inProgress';
        }
        return 'pending';
    }
    if (lineStatuses.some(status => status === 'draft')) {
        return 'draft';
    }
    return 'closed';
};

export const computeOrderReceiptStatusFromLinesReceiptStatuses = async (
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
) => {
    const lineStatuses = [
        ...(await purchaseOrder.lines.reduce(
            async (statuses, line) => statuses.add(await line.lineReceiptStatus),
            new Set<xtremPurchasing.enums.PurchaseOrderReceiptStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'partiallyReceived')) {
        return 'partiallyReceived';
    }
    if (lineStatuses.some(status => status === 'received')) {
        if (lineStatuses.some(status => status === 'notReceived')) {
            return 'partiallyReceived';
        }
        return 'received';
    }
    return 'notReceived';
};

export const managePurchaseOrderStatusApproval = async (
    context: Context,
    param: { document: xtremPurchasing.nodes.PurchaseOrder; isApproved: boolean },
): Promise<void> => {
    const { document, isApproved } = param;
    if (!document) {
        throw new LogicError('Document is required');
    }

    if (!((await document.status) === 'draft' && (await document.approvalStatus) === 'pendingApproval')) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_order__invalid_approval_status',
                'Invalid status for approval.',
            ),
        );
    }

    const lineStatus: NodeUpdateData<xtremPurchasing.nodes.PurchaseOrderLine> = {
        status: isApproved ? 'pending' : 'closed',
        isUsingFunctionToClose: !isApproved,
    };

    const lines: NodeUpdateData<xtremPurchasing.nodes.PurchaseOrderLine>[] = await document.lines
        .map(async line => ({ ...lineStatus, _sortValue: await line._sortValue, _action: 'update' as UpdateAction }))
        .toArray();

    await document.$.update({
        approvalStatus: isApproved ? 'approved' : 'rejected',
        status: isApproved ? 'pending' : 'closed',
        lines,
    });
};

export const managePurchaseOrderStatusConfirmed = async (
    context: Context,
    param: { document: xtremPurchasing.nodes.PurchaseOrder; isConfirm: boolean },
): Promise<boolean> => {
    const { document, isConfirm } = param;
    if (!document) {
        throw new LogicError('Document is required');
    }

    if (await document.isApprovalManaged) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/purchase_order_approval_managed',
                'The approval workflow needs to be disabled to update the status to confirmed.',
            ),
        );
    }

    const baseLine: NodeUpdateData<xtremPurchasing.nodes.PurchaseOrderLine> = {
        status: isConfirm ? 'pending' : 'closed',
    };

    const lines: NodeUpdateData<xtremPurchasing.nodes.PurchaseOrderLine>[] = await document.lines
        .map(async line => ({ ...baseLine, _sortValue: await line._sortValue, _action: 'update' as UpdateAction }))
        .toArray();

    await document.$.update({
        approvalStatus: isConfirm ? 'confirmed' : 'rejected',
        status: isConfirm ? 'pending' : 'closed',
        lines,
    });

    return true;
};

const _isOrderClosable = async (purchaseOrder: xtremPurchasing.nodes.PurchaseOrder) => {
    const status = await purchaseOrder.status;
    const receiptStatus = await purchaseOrder.receiptStatus;
    const approvalStatus = await purchaseOrder.approvalStatus;
    loggers.orderLib.verbose(
        () =>
            `_isOrderClosable (_id=${purchaseOrder._id}): status=${status}, lineReceiptStatus=${receiptStatus}, approvalStatus=${approvalStatus}`,
    );
    return !(
        ['closed'].includes(status) ||
        ['received'].includes(receiptStatus) ||
        ['pendingApproval'].includes(approvalStatus) ||
        (['draft'].includes(status) && !['rejected'].includes(approvalStatus))
    );
};

const _isOrderLineClosable = (
    previousLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus,
    previousLineReceiptStatus: xtremPurchasing.enums.PurchaseOrderReceiptStatus,
    previousLineInvoiceStatus: xtremPurchasing.enums.PurchaseOrderInvoiceStatus,
    newApprovalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
) => {
    loggers.orderLib.verbose(
        () =>
            `_isOrderLineClosable: previousLineStatus=${previousLineStatus}, previousLineReceiptStatus=${previousLineReceiptStatus}, newApprovalStatus=${newApprovalStatus}`,
    );

    if (previousLineStatus === 'closed') return false;
    if (previousLineReceiptStatus === 'received' && previousLineInvoiceStatus === 'invoiced') return false;
    if (newApprovalStatus === 'pendingApproval') return false;
    if (previousLineStatus === 'draft' && newApprovalStatus !== 'rejected') return false;
    return true;
};

export async function controlCloseOrderLine(
    context: Context,
    purchaseOrderLine: xtremPurchasing.nodes.PurchaseOrderLine,
    statuses: {
        previousLineStatus: xtremPurchasing.enums.PurchaseDocumentStatus;
        previousLineReceiptStatus: xtremPurchasing.enums.PurchaseOrderReceiptStatus;
        previousLineInvoiceStatus: xtremPurchasing.enums.PurchaseOrderInvoiceStatus;
        newApprovalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus;
    },
): Promise<Diagnose[]> {
    if (
        !_isOrderLineClosable(
            statuses.previousLineStatus,
            statuses.previousLineReceiptStatus,
            statuses.previousLineInvoiceStatus,
            statuses.newApprovalStatus,
        )
    ) {
        return [
            new Diagnose(
                ValidationSeverity.error,
                [],
                context.localize(
                    '@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__error_updating_status_order_line_not_closable',
                    `Unable to update status on already closed, received, pending approval order line.`,
                ),
            ),
        ];
    }

    const draftReceipts = context.query(xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine, {
        filter: { purchaseOrderLine: purchaseOrderLine._id, purchaseReceiptLine: { status: 'draft' } },
    });

    if ((await draftReceipts.length) > 0) {
        return [
            new Diagnose(
                ValidationSeverity.error,
                [],
                context.localize(
                    '@sage/xtrem-purchasing/functions__purchase_order_lib__control_close_non_posted_receipts_exist',
                    'An order line with associated receipts that are not posted cannot be closed. Check the following purchase receipts: {{receiptNumbers}}',
                    {
                        receiptNumbers: await draftReceipts
                            .map(async receipt => (await receipt.purchaseReceiptLine).documentNumber)
                            .toArray(),
                    },
                ),
            ),
        ];
    }

    if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption)) {
        if (!(await purchaseOrderLine.purchaseReceiptLines.length)) {
            const invoiceNumbers =
                await xtremPurchasing.functions.LandedCostLib.getLandedCostInvoiceNumbers(purchaseOrderLine);
            if (invoiceNumbers.length > 0) {
                return [
                    new Diagnose(
                        ValidationSeverity.error,
                        [],
                        context.localize(
                            '@sage/xtrem-purchasing/functions__purchase_order_lib__control_close_landed_costs_exist',
                            'An order line that is not received and with landed costs attached cannot be closed.',
                        ),
                    ),
                ];
            }
        }
    }

    return [];
}

/**
 * update the status of purchase order lines to 'closed'
 * @param writableContext
 * @param purchaseOrderToCloseId
 * @param lineToCloseIds (optional) if entered, only these lines belonging to the purchase order will be closed.
 *  The saving of the status will be done line by line
 * @returns the list of order lines that have been updated
 */
async function changeStatusOfLinesToClose(
    writableContext: Context,
    purchaseOrderToCloseId: number,
    lineToCloseIds?: number[],
): Promise<xtremPurchasing.nodes.PurchaseOrderLine[]> {
    const purchaseOrder = await writableContext.read(
        xtremPurchasing.nodes.PurchaseOrder,
        { _id: purchaseOrderToCloseId },
        { forUpdate: true },
    );

    if (!lineToCloseIds && !(await _isOrderClosable(purchaseOrder))) {
        const errorMessage = writableContext.localize(
            '@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order__error_updating_status_order_not_closable',
            `Unable to update status on already closed, received, pending approval order.`,
        );
        loggers.orderLib.error(() => errorMessage);
        throw new BusinessRuleError(`${errorMessage}`);
    }

    // Set the new status first to trigger the controls
    const closedLines = await purchaseOrder.lines
        .filter(async line => {
            return (await line.status) !== 'closed' && (!lineToCloseIds || lineToCloseIds.includes(line._id));
        })
        .map(async line => {
            await line.$.set({ status: 'closed', isUsingFunctionToClose: true });
            return line;
        })
        .toArray();

    await purchaseOrder.$.save();

    return closedLines;
}

/**
 * Close the lines of a purchase order
 * @param context
 * @param purchaseOrderToClose
 * @param lineToCloseIds (optional), if entered, only these lines will be closed in the order
 * @returns
 */
export const closeOrderLines = async (
    context: Context,
    purchaseOrderToClose: xtremPurchasing.nodes.PurchaseOrder,
    lineToCloseIds?: number[],
) => {
    let receiptsToPost: number[] = [];

    await context.runInWritableContext(async writableContext => {
        // first, change the status of order lines
        const actualClosedLines = await changeStatusOfLinesToClose(
            writableContext,
            purchaseOrderToClose._id,
            lineToCloseIds,
        );
        // and then trigger the action to perform the correction of associated receipt lines
        receiptsToPost = _.uniq(
            await asyncArray(actualClosedLines).reduce(async (cum, line) => {
                const updatedReceipts = await xtremPurchasing.functions.PurchaseOrderLib.getReceiptsToCorrect(
                    writableContext,
                    line,
                );
                return cum.concat(updatedReceipts);
            }, [] as integer[]),
        );
    });

    // post to stock the receipts that have been modified
    loggers.orderLib.verbose(
        () => `Closing of order lines => receiptsToPost = ${JSON.stringify(receiptsToPost, null, 4)}`,
    );
    const postResults = await xtremPurchasing.nodes.PurchaseReceipt.postToStock(context, receiptsToPost);
    loggers.orderLib.verbose(() => `postResults = ${JSON.stringify(postResults, null, 4)}`);
    loggers.orderLib.info(() =>
        lineToCloseIds
            ? context.localize(
                  '@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order_line__status_updated',
                  'Order line status updated.',
              )
            : context.localize(
                  '@sage/xtrem-purchasing/functions-purchase-order-lib_purchase_order__status_updated',
                  'Order status updated.',
              ),
    );

    return true;
};

/**
 * from a list of PurchaseReceiptLine, return the corresponding list of writable lines, first reading the header to make it writable
 * @param context
 * @param purchaseOrderLineToUpdate
 * @returns
 */
function getWritableReceiptLines(
    context: Context,
    purchaseOrderLineToUpdate: xtremPurchasing.nodes.PurchaseOrderLine,
): AsyncArrayReader<xtremPurchasing.nodes.PurchaseReceiptLine> {
    const writableReceipts: Record<integer, xtremPurchasing.nodes.PurchaseReceipt> = {};

    return purchaseOrderLineToUpdate.purchaseReceiptLines.map(async line => {
        const documentId = await (await line.purchaseReceiptLine).documentId;
        if (!writableReceipts[documentId]) {
            writableReceipts[documentId] = await xtremMasterData.functions.getWritableNode(
                context,
                xtremPurchasing.nodes.PurchaseReceipt,
                documentId,
            );
        }
        const receiptLine = await writableReceipts[documentId].lines.find(
            async writableLine => writableLine._id === (await line.purchaseReceiptLine)._id,
        );
        if (!receiptLine) {
            throw new SystemError('receiptLine not found in its document !'); // can't happen
        }
        return receiptLine;
    });
}
/**
 * From a list of receipt lines, trigger the saving of corresponding headers
 * @param updatedReceiptLines
 * @returns
 */
async function saveReceiptHeaders(
    updatedReceiptLines: xtremPurchasing.nodes.PurchaseReceiptLine[],
): Promise<integer[]> {
    const receiptIds: Record<integer, integer> = {};
    const receipts: xtremPurchasing.nodes.PurchaseReceipt[] = [];
    await asyncArray(updatedReceiptLines).forEach(async updatedReceiptLine => {
        const receiptId = await updatedReceiptLine.documentId;
        loggers.orderLib.verbose(
            () =>
                `receiptLine._id = ${JSON.stringify(updatedReceiptLine._id, null, 4)} -> receipt._id = ${JSON.stringify(
                    receiptId,
                    null,
                    4,
                )}`,
        );
        if (!receiptIds[receiptId]) {
            receiptIds[receiptId] = receiptId;
            receipts.push(await updatedReceiptLine.document);
        }
    });
    await asyncArray(receipts).forEach(async receipt => {
        await receipt.$.save();
    });
    return _.values(receiptIds);
}

/**
 * actions to trigger when a purchase order line becomes closed:
 *  - dispatch remaining landed costs to existing receipt lines and create corresponding  StockCorrectionDetail records
 * @param context
 * @param purchaseOrderLineToUpdate The order line from which the receipts are to be searched
 * @returns the list of receipts that have been modified. This list of receipts must be posted to stock
 */
export async function getReceiptsToCorrect(
    context: Context,
    purchaseOrderLineToUpdate: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<integer[]> {
    // If landed cost amount remains => dispatch on existing receipts
    loggers.orderLib.verbose(
        () =>
            `correctReceipts purchaseOrderLineToUpdate._id = ${JSON.stringify(purchaseOrderLineToUpdate._id, null, 4)}`,
    );

    // The headers must be read for update and then use the corresponding writable lines
    const receiptLines = getWritableReceiptLines(context, purchaseOrderLineToUpdate);

    const updatedReceiptLines = await xtremPurchasing.functions.PurchaseReceiptLib.prepareReceiptStockCorrection(
        context,
        receiptLines,
        {
            save: false, // saving is handled at header by calling saveReceiptHeaders
            orderLine: purchaseOrderLineToUpdate,
        },
    );

    return saveReceiptHeaders(updatedReceiptLines);
}

export const getExpectedReceiptDate = async (
    context: Context,
    item: xtremMasterData.nodes.Item | null,
    site: xtremSystem.nodes.Site,
    supplier: xtremMasterData.nodes.Supplier,
    orderDate: date,
    needByDate: date,
) => {
    if (!item) {
        return needByDate.compare(orderDate) > 0 ? needByDate : orderDate;
    }

    const purchaseLeadTime = await xtremPurchasing.nodes.PurchaseOrderLine.getPurchaseLeadTime(
        context,
        item,
        site,
        supplier,
    );
    const expectedReceiptDate = orderDate.addDays(purchaseLeadTime);

    return needByDate.compare(expectedReceiptDate) > 0 ? needByDate : expectedReceiptDate;
};

export const computeOrderInvoiceStatusFromLinesInvoiceStatuses = async (
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
) => {
    const lineStatuses = [
        ...(await purchaseOrder.lines.reduce(
            async (statuses, line) => statuses.add(await line.lineInvoiceStatus),
            new Set<xtremPurchasing.enums.PurchaseReceiptInvoiceStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'partiallyInvoiced')) {
        return 'partiallyInvoiced';
    }
    if (lineStatuses.some(status => status === 'invoiced')) {
        if (lineStatuses.some(status => status === 'notInvoiced')) {
            return 'partiallyInvoiced';
        }
        return 'invoiced';
    }
    return 'notInvoiced';
};

export async function checkTaxCalculationStatusOfPurchaseOrderMethod(
    purchaseOrderToApprove: xtremPurchasing.nodes.PurchaseOrder,
) {
    const { context } = purchaseOrderToApprove.$;
    if (
        ((await purchaseOrderToApprove.taxEngine) === 'genericTaxCalculation' &&
            (await purchaseOrderToApprove.taxCalculationStatus) !== 'done') ||
        ((await purchaseOrderToApprove.taxEngine) !== 'genericTaxCalculation' &&
            ((await purchaseOrderToApprove.taxCalculationStatus) === 'inProgress' ||
                (await purchaseOrderToApprove.taxCalculationStatus) === 'failed'))
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/cant_approve_order_wrong_taxCalculationStatus',
                'The tax calculation is {{taxCalculationStatus}}, the order cannot be approved.',
                {
                    taxCalculationStatus: context.localizeEnumMember(
                        '@sage/xtrem-master-data/TaxCalculationStatus',
                        await purchaseOrderToApprove.taxCalculationStatus,
                    ),
                },
            ),
        );
    }
}

/**
 * This function check if the tax calculation status of the PO
 * @param context
 * @param purchaseOrder structure that can contain _id or number of the PO
 */
export async function checkTaxCalculationStatusOfPurchaseOrder(
    context: Context,
    purchaseOrder: {
        _id?: string;
        number?: string;
    },
): Promise<void> {
    if (!purchaseOrder._id && !purchaseOrder.number) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/missing_purchase_order_reference',
                'The purchase order reference is missing.',
            ),
        );
    }

    const purchaseOrderToApprove = await context.read(
        xtremPurchasing.nodes.PurchaseOrder,
        purchaseOrder._id
            ? {
                  _id: purchaseOrder._id,
              }
            : {
                  number: purchaseOrder.number,
              },
    );

    await checkTaxCalculationStatusOfPurchaseOrderMethod(purchaseOrderToApprove);
}

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param receiptStatus
 * @param approvalStatus
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculatePurchaseOrderDisplayStatus(
    status: xtremPurchasing.enums.PurchaseDocumentStatus,
    receiptStatus: xtremPurchasing.enums.PurchaseOrderReceiptStatus,
    approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
): xtremPurchasing.enums.PurchaseOrderDisplayStatus {
    if (approvalStatus === 'rejected') {
        return 'rejected';
    }
    if (receiptStatus === 'received') {
        return 'received';
    }
    if (status === 'closed') {
        return 'closed';
    }
    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }
    if (receiptStatus === 'partiallyReceived') {
        return 'partiallyReceived';
    }
    if (['pendingApproval', 'approved', 'confirmed'].includes(approvalStatus)) {
        return approvalStatus as xtremPurchasing.enums.PurchaseOrderDisplayStatus;
    }
    return 'draft';
}

export async function linkedRequisitionArray(
    order: xtremPurchasing.nodes.PurchaseOrder,
): Promise<xtremPurchasing.nodes.PurchaseRequisition[]> {
    const requisitionLines = await order.lines
        .map(async line => {
            const purchaseRequisitionLine = line.purchaseRequisitionLines;
            if (await purchaseRequisitionLine.length) {
                return (await (await (await purchaseRequisitionLine.elementAt(0)).purchaseRequisitionLine).document)
                    ._id;
            }
            return null;
        })
        .toArray();

    if (requisitionLines.length) {
        return order.$.context
            .query(xtremPurchasing.nodes.PurchaseRequisition, {
                filter: { _id: { _in: requisitionLines } },
            })
            .toArray();
    }

    return [];
}

export async function updateHeaderNotesOnCreation(order: xtremPurchasing.nodes.PurchaseOrder) {
    if (order.$.status === NodeStatus.added) {
        const requisitionArray = await linkedRequisitionArray(order);
        if (requisitionArray && requisitionArray.length === 1) {
            const isTransferHeaderNote = await requisitionArray[0].isTransferHeaderNote;

            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(order);
                await setHeaderInternalNote(order, requisitionArray, currentNote);
            }
            await setHeaderSetupNote(order, isTransferHeaderNote, await requisitionArray[0].isTransferLineNote);
        }
    }
}

export async function updateLineNotesOnCreation(orderLine: xtremPurchasing.nodes.PurchaseOrderLine) {
    const requisitionLine = orderLine.purchaseRequisitionLines;
    if (orderLine.$.status === NodeStatus.added && (await requisitionLine.length)) {
        if (requisitionLine !== null) {
            const linkedDocumentLine = await (await requisitionLine.elementAt(0)).purchaseRequisitionLine;
            if (await (await linkedDocumentLine.document).isTransferLineNote) {
                const currentNote = await getCurrentLineNote(orderLine);
                await setLineInternalNote(orderLine, linkedDocumentLine, currentNote);
            }
        }
    }
}

export async function controlDeleteInvoiceLink(
    context: Context,
    orderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<string[]> {
    const messages: string[] = [];
    if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption)) {
        const invoiceNumbers = await xtremPurchasing.functions.LandedCostLib.getLandedCostInvoiceNumbers(orderLine);
        if (invoiceNumbers.length > 0) {
            messages.push(
                context.localize(
                    '@sage/xtrem-purchasing/functions__purchase_order_lib__control_delete_landed_costs_exist',
                    'You cannot delete this order line, as landed cost allocations exist on it. Check the following associated invoices:\n\n{{invoiceNumbers}}',
                    { invoiceNumbers: invoiceNumbers.join(' - ') },
                ),
            );
        }
    }
    return messages;
}
export async function resynchronizeStatus(
    context: Context,
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
): Promise<boolean> {
    await context.batch.updateProgress({
        detail: await purchaseOrder.number,
        errorCount: 0,
        successCount: 0,
        phase: 'start',
    });

    await context.batch.logMessage(
        'info',
        context.localize(
            '@sage/xtrem-purchasing/purchase_order_lib__resync__start',
            'Purchase order status sync start.',
        ),
    );

    const oldInvoiceStatus = await purchaseOrder.invoiceStatus;
    let statusCorrected = false;
    let lineStatusCorrected = false;

    await purchaseOrder.lines.forEach(async line => {
        const oldLineReceiptStatus = await line.lineReceiptStatus;
        const newLineReceiptStatus = await xtremPurchasing.functions.OrderLine.getLineReceiptStatus(line);
        if (oldLineReceiptStatus !== newLineReceiptStatus) {
            loggers.orderLib.verbose(
                () =>
                    `resynchronizeStatus line._id=${line._id} oldLineReceiptStatus=${oldLineReceiptStatus} newLineReceiptStatus=${newLineReceiptStatus}`,
            );
            await purchaseOrder.$.set({
                lines: [
                    {
                        _id: line._id,
                        lineReceiptStatus: newLineReceiptStatus,
                        _action: 'update',
                    },
                ],
            });
            lineStatusCorrected = true;
        }
    });

    const newInvoiceStatus =
        await xtremPurchasing.functions.PurchaseOrderLib.computeOrderInvoiceStatusFromLinesInvoiceStatuses(
            purchaseOrder,
        );
    loggers.orderLib.verbose(
        () => `resynchronizeStatus oldInvoiceStatus=${oldInvoiceStatus} newInvoiceStatus=${newInvoiceStatus}`,
    );
    if (oldInvoiceStatus !== newInvoiceStatus) {
        await purchaseOrder.$.set({ invoiceStatus: newInvoiceStatus });
        statusCorrected = true;
    }

    if (statusCorrected || lineStatusCorrected) {
        await purchaseOrder.$.set({ forceUpdateForResync: true });
        await purchaseOrder.$.save();
    }

    await context.batch.logMessage(
        'info',
        context.localize(
            '@sage/xtrem-purchasing/purchase_order_lib__resync__end',
            'Purchase order status sync finished.',
        ),
    );
    await context.batch.updateProgress({ phase: 'finish', successCount: 1, totalCount: 1 });
    return true;
}
