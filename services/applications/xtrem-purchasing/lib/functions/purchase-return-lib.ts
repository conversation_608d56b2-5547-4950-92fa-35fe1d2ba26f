import type { Context, NodeCreateData, UpdateAction } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, asyncArray, date } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderInternalNote,
    setHeaderSetupNote,
    setLineInternalNote,
} from './common';
import { loggers } from './index';

/**
 * This function is used for verifying that input properties are the same as the ones from the current receipt to create header
 * @param site
 * @param supplier
 * @param currency
 * @param data - contains the invoice header creation payload data
 * @returns true/false
 * Order header grouping conditioned properties :
 * - site
 * - supplier
 * - currency
 */
const _newPurchaseInvoicesCreateDataBreakCondition = (
    site: xtremSystem.nodes.Site,
    supplier: xtremMasterData.nodes.Supplier,
    currency: xtremMasterData.nodes.Currency | null,
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>,
) => {
    loggers.returnLib.debug(
        () =>
            `_newPurchaseInvoicesCreateDataBreakCondition - data/param ${data.site}/${site._id};${
                data.billBySupplier
            }/${supplier._id};${data.currency}/${currency ? currency._id : null}`,
    );
    return (
        data.site === site._id &&
        data.billBySupplier === supplier._id &&
        (!currency || !data.currency || data.currency === currency._id)
    );
};

/**
 * This function is used for verifying that input properties are the same as the ones from the current receipt line to create
 * @param item
 * @param purchaseReturnLine
 * @param unit
 * @param line
 * @returns true/false
 * Order line grouping conditioned properties :
 *  * - item
 * - unit
 */
const _newPurchaseInvoicesCreateDataLineBreakCondition = async (
    item: xtremMasterData.nodes.Item,
    unit: xtremMasterData.nodes.UnitOfMeasure | null,
    purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine,
    line: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoiceLine>,
) => {
    loggers.returnLib.debug(
        () =>
            `newPurchaseReturnsCreateDataLineBreakCondition - line/param ${line.item}/${item._id};${line.unit}/${unit}`,
    );
    return (
        line.item === item._id &&
        line.grossPrice === (await purchaseReturnLine.grossPrice) &&
        (!unit || !line.unit || line.unit === unit._id) &&
        line.unitToStockUnitConversionFactor === (await purchaseReturnLine.unitToStockUnitConversionFactor)
    );
};

/**
 * This function is used to find an existing invoice line data to create for the current purchase receipt line
 * @param newPurchaseInvoicesCreateData
 * @param purchaseReturnLine
 * @returns a reference to the receipt line data to create if any or null
 */
const _findExistingPurchaseInvoiceLineData = async (
    newPurchaseInvoicesCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[],
    purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine,
): Promise<NodeCreateData<
    xtremPurchasing.nodes.PurchaseInvoiceLine & { _sortValue?: number; _action: UpdateAction }
> | null> => {
    /* eslint-disable no-restricted-syntax */
    for (const piData of newPurchaseInvoicesCreateData) {
        const existingPurchaseInvoiceLineDataWithCurrentItem =
            (await asyncArray(piData.lines || []).find(async piDataLine => {
                loggers.returnLib.debug(
                    () => `Into find existing piDataLine : ${piData.site} ${piData.billBySupplier}`,
                );
                const found = await _newPurchaseInvoicesCreateDataLineBreakCondition(
                    await purchaseReturnLine.item,
                    await purchaseReturnLine.unit,
                    purchaseReturnLine,
                    piDataLine,
                );
                loggers.returnLib.debug(
                    () => `Into find existing piDataLine : ${piData.site} ${piData.billBySupplier} exist line ${found}`,
                );
                return found;
            })) || null;
        if (existingPurchaseInvoiceLineDataWithCurrentItem) {
            return existingPurchaseInvoiceLineDataWithCurrentItem;
        }
    }
    return null;
};

/**
 * This function is used to add a dependency from the purchase invoice line to be created, to the purchase receipt line it was created from
 * @param purchaseInvoiceLineData
 * @param purchaseReturnLine
 */
const _addNewPurchaseInvoiceLineToPurchaseReturnLineDependency = async (
    purchaseInvoiceLineData: NodeCreateData<
        xtremPurchasing.nodes.PurchaseInvoiceLine & { _sortValue?: number; _action: UpdateAction }
    >,
    purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine,
) => {
    if (!purchaseInvoiceLineData.purchaseReturnLines || !purchaseInvoiceLineData.purchaseReturnLines.length) {
        purchaseInvoiceLineData.purchaseReturnLines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseReturnLineToPurchaseInvoiceLine & {
                _sortValue?: number;
                _action: UpdateAction;
            }
        >[];
    }
    if (!purchaseInvoiceLineData.purchaseReturnLines) {
        purchaseInvoiceLineData.purchaseReturnLines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseReturnLineToPurchaseInvoiceLine & {
                _sortValue?: number;
                _action: UpdateAction;
            }
        >[];
    }
    if (!purchaseInvoiceLineData.purchaseReturnLines.some(rec => rec._id === purchaseReturnLine._id)) {
        const length = purchaseInvoiceLineData.purchaseReturnLines.push({
            purchaseReturnLine,
            invoicedQuantity: await purchaseReturnLine.quantityToInvoice,
            invoicedQuantityInStockUnit: await xtremMasterData.functions.convertFromTo(
                await purchaseReturnLine.unit,
                await (
                    await purchaseReturnLine.item
                )?.stockUnit,
                await purchaseReturnLine.quantityToInvoice,
                true,
                await purchaseReturnLine.unitToStockUnitConversionFactor,
            ),
        });
        purchaseInvoiceLineData.quantity = purchaseInvoiceLineData.purchaseReturnLines.reduce(
            (prev, line) => prev + (line.invoicedQuantity ?? 0),
            0,
        );
        loggers.returnLib.debug(() => `Dependency added - new length ${length}`);
    }
};

/**
 * This function is used to add/update a purchase invoice create data
 * @param newPurchaseInvoicesCreateData
 * @param purchaseReturnLine
 * @param receivingSite
 * @param receiptSupplier
 */
const _addPurchaseInvoiceDataEntry = async (
    newPurchaseInvoicesCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[],
    purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine,
    invoiceSite: xtremSystem.nodes.Site,
    invoiceSupplier: xtremMasterData.nodes.Supplier,
) => {
    let newEntry = false;

    const purchaseReturnLineDocument = await purchaseReturnLine.document;
    const newPurchaseInvoicesCreateDataIndex = await asyncArray(
        newPurchaseInvoicesCreateData.filter(data => data.site && data.billBySupplier),
    ).findIndex(async data =>
        _newPurchaseInvoicesCreateDataBreakCondition(
            invoiceSite,
            invoiceSupplier,
            await purchaseReturnLineDocument.currency,
            data,
        ),
    );
    let purchaseInvoiceData =
        newPurchaseInvoicesCreateDataIndex !== -1
            ? newPurchaseInvoicesCreateData[newPurchaseInvoicesCreateDataIndex]
            : null;
    if (!purchaseInvoiceData) {
        purchaseInvoiceData = {
            site: invoiceSite._id,
            billBySupplier: invoiceSupplier._id,
            invoiceDate: date.today(),
            currency: (await purchaseReturnLineDocument.currency)._id,
            totalAmountExcludingTax: await purchaseReturnLine.amountExcludingTax,
        };
        newEntry = true;
    }
    if (!purchaseInvoiceData.lines || !purchaseInvoiceData.lines.length) {
        // No receipt lines data yet
        purchaseInvoiceData.lines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseInvoiceLine & { _sortValue?: number; _action: UpdateAction }
        >[];
    }
    const purchaseInvoiceDataLinesLength = purchaseInvoiceData.lines.push({
        item: (await purchaseReturnLine.item)._id,
        quantity: await purchaseReturnLine.quantityToInvoice,
        unitToStockUnitConversionFactor: await purchaseReturnLine.unitToStockUnitConversionFactor,
        unit: (await purchaseReturnLine.unit)._id,
        grossPrice: await purchaseReturnLine.grossPrice, // We get the price from the purchase receipt line
        storedAttributes: await purchaseReturnLine.storedAttributes,
        storedDimensions: await purchaseReturnLine.storedDimensions,
    });
    if (purchaseInvoiceDataLinesLength) {
        await _addNewPurchaseInvoiceLineToPurchaseReturnLineDependency(
            purchaseInvoiceData.lines[purchaseInvoiceDataLinesLength - 1],
            purchaseReturnLine,
        );
    }
    if (newEntry) {
        newPurchaseInvoicesCreateData.push(purchaseInvoiceData);
    }
};

/**
 * This function is used to create the purchase invoice create data from a single purchase receipt. It's called when receiving a purchase receipt
 * @param context
 * @param purchaseReturn
 * @returns an array of purchase invoices data that can be used as payload for the purchase invoices create method
 */
export const initPurchaseInvoiceFromReturnCreateData = async (purchaseReturn: xtremPurchasing.nodes.PurchaseReturn) => {
    // This array will contain the purchase order create payload data
    const newPurchaseInvoicesCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[] = [];

    await purchaseReturn.lines
        .filter(
            async line =>
                ['notInvoiced', 'partiallyInvoiced'].includes(await line.lineInvoiceStatus) &&
                ['pending', 'inProgress'].includes(await line.status) &&
                !!(await line.grossPrice),
        )
        .forEach(async purchaseReturnLine => {
            const existingPurchaseInvoiceLineData = await _findExistingPurchaseInvoiceLineData(
                newPurchaseInvoicesCreateData,
                purchaseReturnLine,
            );
            if (existingPurchaseInvoiceLineData) {
                existingPurchaseInvoiceLineData.quantity = existingPurchaseInvoiceLineData.quantity ?? 0;
                await _addNewPurchaseInvoiceLineToPurchaseReturnLineDependency(
                    existingPurchaseInvoiceLineData,
                    purchaseReturnLine,
                );
            } else {
                // There are no receipt lines data for the current item, according to breaking properties, new ones will be created
                const purchaseReturnLineDocument = await purchaseReturnLine.document;
                await _addPurchaseInvoiceDataEntry(
                    newPurchaseInvoicesCreateData,
                    purchaseReturnLine,
                    await purchaseReturnLineDocument.returnSite,
                    await purchaseReturnLineDocument.businessRelation,
                );
            }
        });
    return newPurchaseInvoicesCreateData;
};

/**
 * This function is used to create purchase invoice for each entry of the purchase receipt create data array. The purchase receipt header and lines statuses are updated
 * @param context
 * @param purchaseReturn
 * @param newPurchaseInvoicesCreateData
 * @returns an array of created purchase invoice numbers if any, or an empty array
 */
export const createPurchaseInvoiceFromReturn = async (
    context: Context,
    newPurchaseInvoicesCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[],
): Promise<Array<xtremPurchasing.nodes.PurchaseInvoice>> => {
    const result = [] as Array<xtremPurchasing.nodes.PurchaseInvoice>;
    await asyncArray(newPurchaseInvoicesCreateData).forEach(async purchaseInvoiceData => {
        const newPurchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData);
        if (await newPurchaseInvoice.$.trySave()) {
            result.push(newPurchaseInvoice);
        } else {
            const errorMsg = context.localize(
                '@sage/xtrem-purchasing/purchase_receipt__lib__purchase_invoice_creation_failed',
                'The purchase invoice number could not be created. {{newPurchaseInvoiceDiagnoses}}.',
                { newPurchaseInvoiceDiagnoses: newPurchaseInvoice.$.context.diagnoses },
            );
            loggers.returnLib.error(() => errorMsg);
            throw new BusinessRuleError(`${errorMsg}`);
        }
    });

    return result;
};

export const computeReturnStatusFromLinesStatuses = async (purchaseReturn: xtremPurchasing.nodes.PurchaseReturn) => {
    const lineStatuses = [
        ...(await purchaseReturn.lines.reduce(
            async (statuses, line) => statuses.add(await line.status),
            new Set<xtremPurchasing.enums.PurchaseDocumentStatus>(),
        )),
    ];
    loggers.returnLib.debug(() => `return doc line statuses: ${lineStatuses.join(', ')}`);
    if (lineStatuses.some(status => status === 'inProgress')) {
        return 'inProgress';
    }
    if (lineStatuses.some(status => status === 'pending')) {
        if ((await purchaseReturn.invoiceStatus) === 'partiallyInvoiced') {
            return 'inProgress';
        }
        return 'pending';
    }
    if (lineStatuses.some(status => status === 'draft')) {
        return 'draft';
    }
    return 'closed';
};

export const computeShippingReturnStatusFromLinesStatuses = async (
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
): Promise<xtremPurchasing.enums.PurchaseReturnShippingStatus> => {
    // All lines are considered even the item not stock managed
    // because their shipped status is immediately updated to 'shipped' on 'Post'
    const nbLinesShipped = await purchaseReturn.lines.sum(async line =>
        Number((await line.shippedStatus) === 'shipped'),
    );

    switch (nbLinesShipped) {
        case 0:
            return 'notShipped';
        case await purchaseReturn.lines.length:
            return 'shipped';
        default:
            return 'partiallyShipped';
    }
};

export const computeReturnInvoiceStatusFromLinesInvoiceStatuses = async (
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
) => {
    const lineStatuses = [
        ...(await purchaseReturn.lines.reduce(
            async (statuses, line) => statuses.add(await line.lineInvoiceStatus),
            new Set<xtremPurchasing.enums.PurchaseReturnInvoiceStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'partiallyInvoiced')) {
        return 'partiallyInvoiced';
    }
    if (lineStatuses.some(status => status === 'invoiced')) {
        if (lineStatuses.some(status => status === 'notInvoiced')) {
            return 'partiallyInvoiced';
        }
        return 'invoiced';
    }
    return 'notInvoiced';
};

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param receiptStatus
 * @param approvalStatus
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculatePurchaseReturnDisplayStatus(
    status: xtremPurchasing.enums.PurchaseDocumentStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
    approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
): xtremPurchasing.enums.PurchaseReturnDisplayStatus {
    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }
    if (approvalStatus === 'rejected') {
        return 'rejected';
    }
    if (stockTransactionStatus === 'completed') {
        return 'returned';
    }
    if (status === 'closed') {
        return 'closed';
    }
    if (stockTransactionStatus === 'error') {
        return 'error';
    }
    if (stockTransactionStatus === 'inProgress') {
        return 'postingInProgress';
    }
    if (approvalStatus === 'approved') {
        return 'approved';
    }
    if (approvalStatus === 'confirmed') {
        return 'confirmed';
    }
    if (approvalStatus === 'pendingApproval') {
        return 'pendingApproval';
    }
    return 'draft';
}

/**
 * Indicates if the purchase return line should be taken into account for computing the header allocation status
 * @param line purchase return line to check
 * @returns true if this PR line should be considered in the allocation status determination
 */
export async function isPurchaseReturnLineConsideredForAllocationStatus(
    line: xtremPurchasing.nodes.PurchaseReturnLine,
): Promise<boolean> {
    return ['inProgress', 'pending'].includes(await line.status);
}

export async function linkedReceiptArray(
    instance: xtremPurchasing.nodes.PurchaseReturn,
): Promise<xtremPurchasing.nodes.PurchaseReceipt[] | undefined> {
    const receiptLines = await instance.lines
        .map(async line => {
            const purchaseReceiptLine = await line.purchaseReceiptLine;
            if (purchaseReceiptLine !== null) {
                return (await (await purchaseReceiptLine.purchaseReceiptLine).document)._id;
            }
            return null;
        })
        .toArray();
    if (receiptLines.length) {
        const receiptQuery = await instance.$.context
            .query(xtremPurchasing.nodes.PurchaseReceipt, {
                filter: { _id: { _in: receiptLines } },
            })
            .toArray();
        if (receiptQuery.length) {
            return receiptQuery;
        }
    }
    return undefined;
}

export async function updateHeaderNotesOnCreation(purchaseReturn: xtremPurchasing.nodes.PurchaseReturn) {
    if (purchaseReturn.$.status === NodeStatus.added) {
        const purchaseReceiptArray = await linkedReceiptArray(purchaseReturn);
        if (purchaseReceiptArray && purchaseReceiptArray.length === 1) {
            const isTransferHeaderNote = await purchaseReceiptArray[0].isTransferHeaderNote;

            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(purchaseReturn);
                await setHeaderInternalNote(purchaseReturn, purchaseReceiptArray, currentNote);
            }
            await setHeaderSetupNote(
                purchaseReturn,
                isTransferHeaderNote,
                await purchaseReceiptArray[0].isTransferLineNote,
            );
        }
    }
}

export async function updateLineNotesOnCreation(returnLine: xtremPurchasing.nodes.PurchaseReturnLine) {
    if (returnLine.$.status === NodeStatus.added) {
        const purchaseReceiptLine = await returnLine.purchaseReceiptLine;
        if (purchaseReceiptLine !== null) {
            const linkedDocumentLine = await purchaseReceiptLine.purchaseReceiptLine;
            if (await (await linkedDocumentLine.document).isTransferLineNote) {
                const currentNote = await getCurrentLineNote(returnLine);
                await setLineInternalNote(returnLine, linkedDocumentLine, currentNote);
            }
        }
    }
}

export const managePurchaseReturnStatusApproval = async (
    context: Context,
    purchaseReturn: xtremPurchasing.interfaces.PurchaseReturnStatusApproval,
): Promise<boolean> => {
    if (!purchaseReturn.purchaseReturnNumber) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__approval_status_not_updated',
                'Unable to update approval status.',
            ),
        );
    }

    const isApproved = purchaseReturn.toBeApproved;

    const purchaseReturnStatusUpdate = await context.read(
        xtremPurchasing.nodes.PurchaseReturn,
        {
            number: purchaseReturn.purchaseReturnNumber,
        },
        { forUpdate: true },
    );

    if (
        !(
            (await purchaseReturnStatusUpdate.status) === 'pending' &&
            (await purchaseReturnStatusUpdate.approvalStatus) === 'pendingApproval'
        )
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/pages__purchase_return__approval_status_not_updated',
                'Unable to update approval status.',
            ),
        );
    }

    await purchaseReturnStatusUpdate.$.set({
        approvalStatus: isApproved ? 'approved' : 'rejected',
        status: isApproved ? 'inProgress' : 'closed',
    });

    await purchaseReturnStatusUpdate.lines.forEach(async line => {
        if ((await line.approvalStatus) === 'pendingApproval') {
            await line.$.set({ approvalStatus: isApproved ? 'approved' : 'rejected' });
        }
        await line.$.set({ status: isApproved ? 'inProgress' : 'closed' });
    });

    await purchaseReturnStatusUpdate.$.save();
    return true;
};

/**
 * This function is used for verifying that input properties are the same as the ones from the current return to create header
 * @param site
 * @param supplier
 * @param currency
 * @param data - contains the invoice header creation payload data
 * @returns true/false
 * Order header grouping conditioned properties :
 * - site
 * - supplier
 * - currency
 */
async function _newPurchaseCreditMemosCreateDataBreakCondition(params: {
    site: xtremSystem.nodes.Site;
    supplier: xtremMasterData.nodes.Supplier;
    currency: xtremMasterData.nodes.Currency | null;
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn;
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>;
}): Promise<boolean> {
    await loggers.returnLib.debugAsync(
        async () =>
            `_newPurchaseCreditMemosCreateDataBreakCondition - data/param ${params.data.site}/${params.site._id};${
                params.data.billBySupplier
            }/${params.supplier._id};${params.data.currency}/${
                params.currency ? params.currency._id : null
            } ${await params.purchaseReturn.returnRequestDate}`,
    );
    return (
        params.data.site === params.site._id &&
        params.data.billBySupplier === params.supplier._id &&
        (!params.currency || !params.data.currency || params.data.currency === params.currency._id)
    );
}

/**
 * This function is used to add/update a purchase credit memo create data
 * @param newPurchaseCreditMemosCreateData
 * @param purchaseReturnLine
 * @param reasonCode
 */
async function purchaseCreditMemoDataEntry(
    context: Context,
    newPurchaseCreditMemosCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>[],
    params: {
        returnLine: xtremPurchasing.nodes.PurchaseReturnLine;
        reasonCode: xtremMasterData.nodes.ReasonCode;
    },
): Promise<void> {
    let newEntry = false;

    const returnDocument = await params.returnLine.document;

    const returnSite = await returnDocument.returnSite;
    const financialSite = (await returnSite.isFinance) ? returnSite : await (await returnDocument.site).financialSite;

    if (!financialSite) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/purchase_return__lib__no_financial_site',
                'No financial site found: {{site}}.',
                { site: await returnSite.name },
            ),
        );
    }

    // why this heavy purchaseCreditMemoDataEntry ??
    // this is call from a foreach where we can have ONLY one document
    // Why is this overcomplicated??
    const newPurchaseCreditMemosCreateDataIndex = await asyncArray(
        newPurchaseCreditMemosCreateData.filter(data => data.site && data.billBySupplier),
    ).findIndex(async data =>
        _newPurchaseCreditMemosCreateDataBreakCondition({
            site: financialSite,
            supplier: await returnDocument.supplier,
            currency: await returnDocument.currency,
            purchaseReturn: returnDocument,
            data,
        }),
    );
    let purchaseCreditMemoData =
        newPurchaseCreditMemosCreateDataIndex !== -1
            ? newPurchaseCreditMemosCreateData[newPurchaseCreditMemosCreateDataIndex]
            : null;
    if (!purchaseCreditMemoData) {
        purchaseCreditMemoData = {
            site: financialSite,
            stockSite: await returnDocument.stockSite,
            billBySupplier: await returnDocument.supplier,
            creditMemoDate: date.today(),
            currency: await returnDocument.currency,
            totalAmountExcludingTax: await params.returnLine.amountExcludingTax,
            isOverwriteNote: true,
            reason: params.reasonCode,
        };
        newEntry = true;
    }
    if (!purchaseCreditMemoData?.lines || !purchaseCreditMemoData?.lines.length) {
        // No receipt lines data yet
        purchaseCreditMemoData.lines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseCreditMemoLine & { _sortValue?: number; _action: UpdateAction }
        >[];
    }
    const purchaseCreditMemoDataLinesLength = purchaseCreditMemoData.lines.push({
        item: (await params.returnLine.item)._id,
        quantity: await params.returnLine.quantity,
        unitToStockUnitConversionFactor: await params.returnLine.unitToStockUnitConversionFactor,
        unit: (await params.returnLine.unit)._id,
        grossPrice: await params.returnLine.grossPrice,
        netPrice: await params.returnLine.netPrice,
        discount: await params.returnLine.discount,
        charge: await params.returnLine.charge,
        amountExcludingTax: await params.returnLine.amountExcludingTax, // We get the price from the purchase receipt line
        storedAttributes: await params.returnLine.storedAttributes,
        storedDimensions: await params.returnLine.storedDimensions,
        // site: await params.returnLine.site,
        stockSite: await returnDocument.stockSite,
        taxes: await xtremDistribution.functions.addNewDistributionDocumentLineTaxDependency(params.returnLine),
    });
    if (
        purchaseCreditMemoDataLinesLength &&
        !purchaseCreditMemoData.lines[purchaseCreditMemoDataLinesLength - 1].purchaseReturnLine
    ) {
        const { returnLine } = params;
        purchaseCreditMemoData.lines[purchaseCreditMemoDataLinesLength - 1].purchaseReturnLine = {
            purchaseReturnLine: returnLine,
        };
        loggers.returnLib.debug(() => `Dependency added`);
    }
    if (newEntry) {
        newPurchaseCreditMemosCreateData.push(purchaseCreditMemoData);
    }
}

/**
 * This function is used to create the purchase create memos create data from a single purchase return. It's called when creating a purchase return
 * @param context
 * @param purchaseReturn
 * @returns an array of purchase credit memo data that can be used as payload for the purchase credit memos create method
 */
export async function initPurchaseCreditMemoCreateData(
    context: Context,
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
    options?: xtremPurchasing.sharedFunctions.PrepareNodeCreateDataOptions,
): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>[]> {
    const isForFinanceCheck = options?.isForFinanceCheck || false;
    const newPurchaseCreditMemosCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseCreditMemo>[] = [];

    const reasonCode = await context.query(xtremMasterData.nodes.ReasonCode, { first: 1 }).at(0);

    if (!reasonCode) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/purchase_return__lib__credit_memo_creation_failed',
                'Reason code is missing.',
            ),
        );
    }

    await purchaseReturn.lines
        .filter(
            async line =>
                (['partiallyInvoiced', 'invoiced'].includes(await line.lineInvoiceStatus) &&
                    line.purchaseInvoiceLines.some(async purchaseInvoiceLine =>
                        ['posted', 'error'].includes(
                            await (
                                await (
                                    await purchaseInvoiceLine.purchaseInvoiceLine
                                ).document
                            ).status,
                        ),
                    )) ||
                isForFinanceCheck,
        )
        .forEach(async returnLine => {
            await purchaseCreditMemoDataEntry(context, newPurchaseCreditMemosCreateData, { returnLine, reasonCode });
        });

    return newPurchaseCreditMemosCreateData;
}

export async function getCreditMemoInstance(
    context: Context,
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
): Promise<xtremPurchasing.nodes.PurchaseCreditMemo[]> {
    if (['closed'].includes(await purchaseReturn.status) || ['credited'].includes(await purchaseReturn.creditStatus)) {
        return [];
    }

    // Group lines per potential supplier and site criteria
    const newPurchaseCreditMemosCreateData =
        await xtremPurchasing.functions.PurchaseReturnLib.initPurchaseCreditMemoCreateData(context, purchaseReturn, {
            isForFinanceCheck: true,
        });

    const purchaseCreditMemos: xtremPurchasing.nodes.PurchaseCreditMemo[] = [];

    await asyncArray(newPurchaseCreditMemosCreateData).forEach(async purchaseCreditMemoData => {
        const newPurchaseCreditMemo = await context.create(
            xtremPurchasing.nodes.PurchaseCreditMemo,
            {
                ...purchaseCreditMemoData,
            },
            { isTransient: true },
        );

        if (await newPurchaseCreditMemo.$.control()) {
            purchaseCreditMemos.push(newPurchaseCreditMemo);
        }
    });
    return purchaseCreditMemos;
}

export async function confirmPurchaseReturn(
    context: Context,
    document: xtremPurchasing.nodes.PurchaseReturn,
): Promise<void> {
    const purchaseReturnStatus = await document.status;
    const isPurchaseReturnApprovalManaged = await (await document.site).isPurchaseReturnApprovalManaged;

    if (purchaseReturnStatus !== 'draft' || isPurchaseReturnApprovalManaged) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/status_and_is_purchase_return_approval_managed',
                'The document needs to be at "Draft" status and the approval workflow needs to be disabled.',
            ),
        );
    }

    await document.$.set({ approvalStatus: 'confirmed', status: 'pending' });

    const updateLines = await document.lines
        .map(line => ({
            _id: line._id,
            _action: 'update' as UpdateAction,
            approvalStatus: 'confirmed' as xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
            status: 'pending' as xtremPurchasing.enums.PurchaseDocumentStatus,
        }))
        .toArray();

    await document.$.set({
        lines: updateLines,
    });

    await document.$.save();
}
