import type { AsyncArrayReader, Collection, Context, NodeCreateData, UpdateAction, decimal } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, asyncArray, date } from '@sage/xtrem-core';
import * as xtremDistribution from '@sage/xtrem-distribution';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { integer } from '@sage/xtrem-shared';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderInternalNote,
    setHeaderSetupNote,
    setLineInternalNote,
} from './common';
import { loggers } from './index';

/**
 * This function is used for verifying that input properties are the same as the ones from the current receipt to create header
 * @param site
 * @param supplier
 * @param currency
 * @param data - contains the invoice header creation payload data
 * @returns true/false
 * Order header grouping conditioned properties :
 * - site
 * - supplier
 * - currency
 */
async function _newPurchaseInvoicesCreateDataBreakCondition(
    site: xtremSystem.nodes.Site,
    supplier: xtremMasterData.nodes.Supplier,
    currency: xtremMasterData.nodes.Currency | null,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>,
): Promise<boolean> {
    await loggers.receiptLib.debugAsync(
        async () =>
            `_newPurchaseInvoicesCreateDataBreakCondition - data/param ${data.site}/${site._id};${
                data.billBySupplier
            }/${supplier._id};${data.currency}/${currency ? currency._id : null} ${await purchaseReceipt.date}`,
    );
    return (
        data.site === site._id &&
        data.billBySupplier === supplier._id &&
        (!currency || !data.currency || data.currency === currency._id)
    );
}

/**
 * This function is used to add a dependency from the purchase invoice line to be created, to the purchase receipt line it was created from
 * @param purchaseInvoiceLineData
 * @param purchaseReceiptLine
 */
function _addNewPurchaseInvoiceLineToPurchaseReceiptLineDependency(
    purchaseInvoiceLineData: NodeCreateData<
        xtremPurchasing.nodes.PurchaseInvoiceLine & { _sortValue?: number; _action: UpdateAction }
    >,
    purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine,
) {
    if (!purchaseInvoiceLineData.purchaseReceiptLine) {
        purchaseInvoiceLineData.purchaseReceiptLine = {
            purchaseReceiptLine,
        };
        loggers.receiptLib.debug(() => `Dependency added`);
    }
}

/**
 * This function is used to add/update a purchase invoice create data
 * @param newPurchaseInvoicesCreateData
 * @param purchaseReceiptLine
 * @param receivingSite
 * @param receiptSupplier
 */
async function _addPurchaseInvoiceDataEntry(
    newPurchaseInvoicesCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[],
    purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine,
    invoiceSite: xtremSystem.nodes.Site,
    invoiceSupplier: xtremMasterData.nodes.Supplier,
): Promise<void> {
    let newEntry = false;

    const newPurchaseInvoicesCreateDataIndex = await asyncArray(
        newPurchaseInvoicesCreateData.filter(data => data.site && data.billBySupplier),
    ).findIndex(async data =>
        _newPurchaseInvoicesCreateDataBreakCondition(
            invoiceSite,
            invoiceSupplier,
            await (
                await purchaseReceiptLine.document
            ).currency,
            await purchaseReceiptLine.document,
            data,
        ),
    );
    let purchaseInvoiceData =
        newPurchaseInvoicesCreateDataIndex !== -1
            ? newPurchaseInvoicesCreateData[newPurchaseInvoicesCreateDataIndex]
            : null;
    if (!purchaseInvoiceData) {
        purchaseInvoiceData = {
            site: invoiceSite._id,
            billBySupplier: invoiceSupplier._id,
            invoiceDate: date.today(),
            currency: (await (await purchaseReceiptLine.document).currency)._id,
            totalAmountExcludingTax: await purchaseReceiptLine.amountExcludingTax,
            isOverwriteNote: true,
        };
        newEntry = true;
    }
    if (!purchaseInvoiceData.lines || !purchaseInvoiceData.lines.length) {
        // No receipt lines data yet
        purchaseInvoiceData.lines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseInvoiceLine & { _sortValue?: number; _action: UpdateAction }
        >[];
    }
    const purchaseInvoiceDataLinesLength = purchaseInvoiceData.lines.push({
        item: (await purchaseReceiptLine.item)._id,
        quantity: await purchaseReceiptLine.remainingQuantityToInvoice,
        unitToStockUnitConversionFactor: await purchaseReceiptLine.unitToStockUnitConversionFactor,
        unit: (await purchaseReceiptLine.unit)._id,
        grossPrice: await purchaseReceiptLine.grossPrice,
        netPrice: await purchaseReceiptLine.netPrice,
        discount: await purchaseReceiptLine.discount,
        charge: await purchaseReceiptLine.charge,
        amountExcludingTax: await purchaseReceiptLine.amountExcludingTax, // We get the price from the purchase receipt line
        storedAttributes: await purchaseReceiptLine.storedAttributes,
        storedDimensions: await purchaseReceiptLine.storedDimensions,
        recipientSite: await purchaseReceiptLine.site,
        taxes: await xtremDistribution.functions.addNewDistributionDocumentLineTaxDependency(purchaseReceiptLine),
    });
    if (purchaseInvoiceDataLinesLength) {
        _addNewPurchaseInvoiceLineToPurchaseReceiptLineDependency(
            purchaseInvoiceData.lines[purchaseInvoiceDataLinesLength - 1],
            purchaseReceiptLine,
        );
    }
    if (newEntry) {
        newPurchaseInvoicesCreateData.push(purchaseInvoiceData);
    }
}

/**
 * This function is used to create the purchase invoice create data from a single purchase receipt. It's called when receiving a purchase receipt
 * @param context
 * @param purchaseReceipt
 * @returns an array of purchase invoices data that can be used as payload for the purchase invoices create method
 */
export async function initPurchaseInvoiceCreateData(
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
    options?: xtremPurchasing.sharedFunctions.PrepareNodeCreateDataOptions,
): Promise<NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[]> {
    // This array will contain the purchase order create payload data
    const newPurchaseInvoicesCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[] = [];

    const isForFinanceCheck = options?.isForFinanceCheck || false;

    await purchaseReceipt.lines
        .filter(
            async line =>
                (await line.remainingQuantityInStockUnit) > 0 &&
                (['pending', 'inProgress'].includes(await line.status) || isForFinanceCheck) &&
                !!(await line.grossPrice),
        )
        .forEach(async purchaseReceiptLine => {
            // There are no receipt lines data for the current item, according to breaking properties, new ones will be created
            const document = await purchaseReceiptLine.document;
            await _addPurchaseInvoiceDataEntry(
                newPurchaseInvoicesCreateData,
                purchaseReceiptLine,
                await document.site,
                await document.businessRelation,
            );
        });
    return newPurchaseInvoicesCreateData;
}

/**
 * This function is used to create purchase invoice for each entry of the purchase receipt create data array. The purchase receipt header and lines statuses are updated
 * @param context
 * @param purchaseReceipt
 * @param newPurchaseInvoicesCreateData
 * @returns an array of created purchase invoice numbers if any, or an empty array
 */
export async function createPurchaseInvoice(
    context: Context,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
    newPurchaseInvoicesCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseInvoice>[],
): Promise<xtremPurchasing.nodes.PurchaseInvoice[]> {
    await loggers.orderLib.debugAsync(
        async () => `Creating purchase invoice  for order n°${await purchaseReceipt.number}`,
    );
    const invoicesCreated: xtremPurchasing.nodes.PurchaseInvoice[] = [];

    await asyncArray(newPurchaseInvoicesCreateData).forEach(async purchaseInvoiceData => {
        const newPurchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData);
        await newPurchaseInvoice.$.save();
        invoicesCreated.push(newPurchaseInvoice);
    });
    return invoicesCreated;
}

/**
 * This function is used for verifying that input properties are the same as the ones from the current receipt to create header
 * @param site
 * @param supplier
 * @param currency
 * @param data - contains the return header creation payload data
 * @returns true/false
 * Order header grouping conditioned properties :
 * - site
 * - supplier
 * - currency
 */
const _newPurchaseReturnsCreateDataBreakCondition = (
    site: xtremSystem.nodes.Site,
    supplier: xtremMasterData.nodes.Supplier,
    currency: xtremMasterData.nodes.Currency | null,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
    data: NodeCreateData<xtremPurchasing.nodes.PurchaseReturn>,
) => {
    loggers.receiptLib.debug(
        () =>
            `_newPurchaseReturnsCreateDataBreakCondition - data/param ${data.returnSite}/${site._id};${data.businessRelation}/${
                supplier._id
            };${data.currency}/${currency ? currency._id : null}`,
    );
    return (
        data.returnSite === site._id &&
        data.businessRelation === supplier._id &&
        (!currency || !data.currency || data.currency === currency._id)
    );
};

/**
 * This function is used for verifying that input properties are the same as the ones from the current receipt line to create
 * @param item
 * @param purchaseReceiptLine
 * @param unit
 * @param line
 * @returns true/false
 * Order line grouping conditioned properties :
 *  * - item
 * - unit
 */
const _newPurchaseReturnsCreateDataLineBreakCondition = async (
    item: xtremMasterData.nodes.Item,
    unit: xtremMasterData.nodes.UnitOfMeasure | null,
    purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine,
    line: NodeCreateData<xtremPurchasing.nodes.PurchaseReturnLine>,
) => {
    loggers.receiptLib.debug(
        () =>
            `newPurchaseReceiptsCreateDataLineBreakCondition - line/param ${line.item}/${item._id};${line.unit}/${unit}`,
    );
    return (
        line.item === item._id &&
        line.grossPrice === (await purchaseReceiptLine.grossPrice) &&
        line.priceOrigin === (await purchaseReceiptLine.priceOrigin) &&
        (!unit || !line.unit || line.unit === unit._id) &&
        line.unitToStockUnitConversionFactor === (await purchaseReceiptLine.unitToStockUnitConversionFactor)
    );
};

/**
 * This function is used to find an existing return line data to create for the current purchase receipt line
 * @param newPurchaseReturnsCreateData
 * @param purchaseReceiptLine
 * @returns a reference to the receipt line data to create if any or null
 */
const _findExistingPurchaseReturnLineData = async (
    newPurchaseReturnsCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseReturn>[],
    purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine,
): Promise<NodeCreateData<
    xtremPurchasing.nodes.PurchaseReturnLine & { _sortValue?: number; _action: UpdateAction }
> | null> => {
    /* eslint-disable no-restricted-syntax */
    for (const piData of newPurchaseReturnsCreateData) {
        const existingPurchaseReturnLineDataWithCurrentItem =
            (await asyncArray(piData.lines || []).find(async piDataLine => {
                loggers.receiptLib.debug(
                    () => `Into find existing piDataLine : ${piData.returnSite} ${piData.businessRelation}`,
                );
                const found = await _newPurchaseReturnsCreateDataLineBreakCondition(
                    await purchaseReceiptLine.item,
                    await purchaseReceiptLine.unit,
                    purchaseReceiptLine,
                    piDataLine,
                );
                loggers.receiptLib.debug(
                    () =>
                        `Into find existing piDataLine : ${piData.returnSite} ${piData.businessRelation} exist line ${found}`,
                );
                return found;
            })) || null;
        if (existingPurchaseReturnLineDataWithCurrentItem) {
            return existingPurchaseReturnLineDataWithCurrentItem;
        }
    }
    return null;
};

/**
 * This function is used to add a dependency from the purchase return line to be created, to the purchase receipt line it was created from
 * @param purchaseReturnLineData
 * @param purchaseReceiptLine
 */
const _addNewPurchaseReturnLineToPurchaseReceiptLineDependency = async (
    purchaseReturnLineData: NodeCreateData<
        xtremPurchasing.nodes.PurchaseReturnLine & { _sortValue?: number; _action: UpdateAction }
    >,
    purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine,
) => {
    if (!purchaseReturnLineData.purchaseReceiptLine) {
        purchaseReturnLineData.purchaseReceiptLine = {
            purchaseReceiptLine,
            returnedQuantity: await purchaseReceiptLine.remainingReturnQuantity,
            returnedQuantityInStockUnit: await xtremMasterData.functions.convertFromTo(
                await purchaseReceiptLine.unit,
                await (
                    await purchaseReceiptLine.item
                )?.stockUnit,
                await purchaseReceiptLine.remainingReturnQuantity,
                true,
                await purchaseReceiptLine.unitToStockUnitConversionFactor,
            ),
        };
    }
};

/**
 * This function is used to add/update a purchase return create data
 * @param newPurchaseReturnsCreateData
 * @param purchaseReceiptLine
 * @param receivingSite
 * @param receiptSupplier
 */
const _addPurchaseReturnDataEntry = async (
    newPurchaseReturnsCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseReturn>[],
    purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine,
    returnSite: xtremSystem.nodes.Site,
    returnSupplier: xtremMasterData.nodes.Supplier,
) => {
    let newEntry = false;

    const purchaseReceiptDocument = await purchaseReceiptLine.document;
    const newPurchaseReturnsCreateDataIndex = await asyncArray(
        newPurchaseReturnsCreateData.filter(data => data.returnSite && data.businessRelation),
    ).findIndex(async data =>
        _newPurchaseReturnsCreateDataBreakCondition(
            returnSite,
            returnSupplier,
            await purchaseReceiptDocument.currency,
            purchaseReceiptDocument,
            data,
        ),
    );
    let purchaseReturnData =
        newPurchaseReturnsCreateDataIndex !== -1
            ? newPurchaseReturnsCreateData[newPurchaseReturnsCreateDataIndex]
            : null;
    if (!purchaseReturnData) {
        purchaseReturnData = {
            returnSite: returnSite._id,
            businessRelation: returnSupplier._id,
            returnRequestDate: date.today(),
            currency: (await purchaseReceiptDocument.currency)._id,
            isOverwriteNote: true,
        };
        newEntry = true;
    }
    if (!purchaseReturnData.lines || !purchaseReturnData.lines.length) {
        // No receipt lines data yet
        purchaseReturnData.lines = [] as NodeCreateData<
            xtremPurchasing.nodes.PurchaseReturnLine & { _sortValue?: number; _action: UpdateAction }
        >[];
    }
    const purchaseReturnDataLinesLength = purchaseReturnData.lines.push({
        item: (await purchaseReceiptLine.item)._id,
        itemDescription: await purchaseReceiptLine.itemDescription,
        quantity: await purchaseReceiptLine.remainingReturnQuantity,
        unitToStockUnitConversionFactor: await purchaseReceiptLine.unitToStockUnitConversionFactor,
        unit: (await purchaseReceiptLine.unit)._id,
        grossPrice: await purchaseReceiptLine.grossPrice,
        netPrice: await purchaseReceiptLine.netPrice,
        discount: await purchaseReceiptLine.discount,
        charge: await purchaseReceiptLine.charge, // We get the price from the purchase receipt line
        priceOrigin: await purchaseReceiptLine.priceOrigin,
        storedAttributes: await purchaseReceiptLine.storedAttributes,
        storedDimensions: await purchaseReceiptLine.storedDimensions,
        // this needs to get updated with a selection from purchase receipt
        reason: await purchaseReceiptLine.$.context.query(xtremMasterData.nodes.ReasonCode, { first: 1 })?.at(0),
    });
    if (purchaseReturnDataLinesLength) {
        await _addNewPurchaseReturnLineToPurchaseReceiptLineDependency(
            purchaseReturnData.lines[purchaseReturnDataLinesLength - 1],
            purchaseReceiptLine,
        );
    }
    if (newEntry) {
        newPurchaseReturnsCreateData.push(purchaseReturnData);
    }
};

/**
 * This function is used to create the purchase return create data from a single purchase receipt. It's called when receiving a purchase receipt
 * @param _context
 * @param purchaseReceipt
 * @returns an array of purchase returns data that can be used as payload for the purchase returns create method
 */
export const initPurchaseReturnCreateData = async (
    _context: Context,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
) => {
    // This array will contain the purchase order create payload data
    const newPurchaseReturnsCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseReturn>[] = [];

    await purchaseReceipt.lines
        .filter(
            async line =>
                (await line.remainingReturnQuantity) > 0 &&
                ['pending', 'inProgress', 'closed'].includes(await line.status) &&
                !!(await line.grossPrice),
        )
        .forEach(async purchaseReceiptLine => {
            const existingPurchaseReturnLineData = await _findExistingPurchaseReturnLineData(
                newPurchaseReturnsCreateData,
                purchaseReceiptLine,
            );
            if (existingPurchaseReturnLineData) {
                existingPurchaseReturnLineData.quantity = existingPurchaseReturnLineData.quantity ?? 0;
                await _addNewPurchaseReturnLineToPurchaseReceiptLineDependency(
                    existingPurchaseReturnLineData,
                    purchaseReceiptLine,
                );
            } else {
                // There are no receipt lines data for the current item, according to breaking properties, new ones will be created
                const purchaseReceiptLineDocument = await purchaseReceiptLine.document;
                await _addPurchaseReturnDataEntry(
                    newPurchaseReturnsCreateData,
                    purchaseReceiptLine,
                    await purchaseReceiptLineDocument.site,
                    await purchaseReceiptLineDocument.businessRelation,
                );
            }
        });
    return newPurchaseReturnsCreateData;
};

/**
 * This function is used to create purchase return for each entry of the purchase receipt create data array. The purchase receipt header and lines statuses are updated
 * @param context
 * @param purchaseReceipt
 * @param newPurchaseReturnsCreateData
 * @returns an array of created purchase return numbers if any, or an empty array
 */
export const createPurchaseReturns = async (
    context: Context,
    newPurchaseReturnsCreateData: NodeCreateData<xtremPurchasing.nodes.PurchaseReturn>[],
): Promise<xtremPurchasing.nodes.PurchaseReturn[]> => {
    const returns = [] as xtremPurchasing.nodes.PurchaseReturn[];
    await asyncArray(newPurchaseReturnsCreateData).forEach(async purchaseReturnData => {
        const newPurchaseReturn = await context.create(xtremPurchasing.nodes.PurchaseReturn, purchaseReturnData);
        if (await newPurchaseReturn.$.trySave()) {
            loggers.requisitionLib.debug(() =>
                context.localize(
                    '@sage/xtrem-purchasing/purchase_receipt__lib__purchase_return_creation_succeeded',
                    'Purchase return created.',
                ),
            );
            returns.push(newPurchaseReturn);
        } else {
            const errorMsg = context.localize(
                '@sage/xtrem-purchasing/purchase_receipt__lib__purchase_return_creation_failed',
                'The record was not created. {{newPurchaseReturnDiagnoses}}.',
                { newPurchaseReturnDiagnoses: newPurchaseReturn.$.context.diagnoses },
            );
            loggers.receiptLib.error(() => errorMsg);
            throw new BusinessRuleError(`${errorMsg}`);
        }
    });

    return returns;
};

export const computeReceiptStatusFromLinesStatuses = async (purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt) => {
    const lineStatuses = [
        ...(await purchaseReceipt.lines.reduce(
            async (statuses, line) => statuses.add(await line.status),
            new Set<xtremPurchasing.enums.PurchaseDocumentStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'inProgress')) {
        return 'inProgress';
    }
    if (lineStatuses.some(status => status === 'pending')) {
        if (
            (await purchaseReceipt.invoiceStatus) === 'partiallyInvoiced' ||
            (await purchaseReceipt.returnStatus) === 'partiallyReturned'
        ) {
            return 'inProgress';
        }
        return 'pending';
    }
    if (lineStatuses.some(status => status === 'draft')) {
        return 'draft';
    }
    return 'closed';
};

export const computeReceiptInvoiceStatusFromLinesInvoiceStatuses = async (
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
) => {
    const lineStatuses = [
        ...(await purchaseReceipt.lines.reduce(
            async (statuses, line) => statuses.add(await line.lineInvoiceStatus),
            new Set<xtremPurchasing.enums.PurchaseReceiptInvoiceStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'partiallyInvoiced')) {
        return 'partiallyInvoiced';
    }
    if (lineStatuses.some(status => status === 'invoiced')) {
        if (lineStatuses.some(status => status === 'notInvoiced')) {
            return 'partiallyInvoiced';
        }
        return 'invoiced';
    }
    return 'notInvoiced';
};

export const computeReceiptReturnStatusFromLinesReturnStatuses = async (
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
) => {
    const lineStatuses = [
        ...(await purchaseReceipt.lines.reduce(
            async (statuses, line) => statuses.add(await line.lineReturnStatus),
            new Set<xtremPurchasing.enums.PurchaseReceiptReturnStatus>(),
        )),
    ];
    if (lineStatuses.some(status => status === 'partiallyReturned')) {
        return 'partiallyReturned';
    }
    if (lineStatuses.some(status => status === 'returned')) {
        if (lineStatuses.some(status => status === 'notReturned')) {
            return 'partiallyReturned';
        }
        return 'returned';
    }
    return 'notReturned';
};

export const computeReceiptLineStatus = (params: {
    status: xtremPurchasing.enums.PurchaseDocumentStatus;
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus;
    lineInvoiceStatus: xtremPurchasing.enums.PurchaseReceiptInvoiceStatus;
    lineReturnStatus: xtremPurchasing.enums.PurchaseReceiptReturnStatus;
    remainingQuantityInStockUnit: decimal; // = received qty - returned qty - invoiced qty
}) => {
    if (params.stockTransactionStatus === 'draft') {
        return 'draft';
    }

    if (params.lineReturnStatus === 'returned') {
        return 'closed';
    }
    // -> lineReturnStatus is 'notReturned' or 'partiallyReturned'

    if (params.lineInvoiceStatus === 'invoiced') {
        return 'closed';
    }
    // -> lineInvoiceStatus is 'notInvoiced' or 'partiallyInvoiced'

    if (params.lineInvoiceStatus === 'notInvoiced' && params.lineReturnStatus === 'notReturned') {
        return 'pending';
    }

    if (params.lineInvoiceStatus === 'notInvoiced') {
        // => lineReturnStatus is 'partiallyReturned'
        return 'inProgress';
    }
    // -> lineInvoiceStatus is 'partiallyInvoiced'

    if (params.lineReturnStatus === 'notReturned') {
        return 'inProgress';
    }
    // -> lineReturnStatus is 'partiallyReturned'

    return params.remainingQuantityInStockUnit > 0 ? 'inProgress' : 'closed';
};

export const getDefaultReceivingAddress = async (
    site: xtremSystem.nodes.Site,
): Promise<xtremMasterData.nodes.Address | null> => {
    return (await (await site.primaryAddress)?.address) ?? null;
};

export const getSiteAddressFromReceivingAddress = (
    site: xtremSystem.nodes.Site,
    receivingAddress: xtremMasterData.nodes.Address | null = null,
): Promise<xtremMasterData.nodes.Address | null> => {
    if (receivingAddress) return Promise.resolve(receivingAddress);
    return xtremPurchasing.functions.PurchaseReceiptLib.getDefaultReceivingAddress(site);
};

/**
 * This function check if the receipt can be posted, according to its tax calculation status
 * @param context
 * @param purchaseOrder structure that can contain _id or number of the PO
 */
export async function checkTaxCalculationStatusForPosting(
    context: Context,
    receipt: xtremPurchasing.nodes.PurchaseReceipt,
): Promise<void> {
    if (!receipt) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/missing_purchase_receipt_reference',
                'The purchase receipt reference is missing.',
            ),
        );
    }

    if (
        ((await receipt.taxEngine) === 'genericTaxCalculation' && (await receipt.taxCalculationStatus) !== 'done') ||
        ((await receipt.taxEngine) !== 'genericTaxCalculation' &&
            ((await receipt.taxCalculationStatus) === 'inProgress' ||
                (await receipt.taxCalculationStatus) === 'failed'))
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/cant_post_receipt_wrong_taxCalculationStatus',
                'The tax calculation is {{taxCalculationStatus}}, the receipt cannot be posted.',
                {
                    taxCalculationStatus: context.localizeEnumMember(
                        '@sage/xtrem-master-data/TaxCalculationStatus',
                        await receipt.taxCalculationStatus,
                    ),
                },
            ),
        );
    }
}

function calculateDisplayStatusForReturnedStatus(
    invoiceStatus: xtremPurchasing.enums.PurchaseReceiptInvoiceStatus,
): xtremPurchasing.enums.PurchaseReceiptDisplayStatus {
    switch (invoiceStatus) {
        case 'invoiced':
            return 'invoiced';
        case 'partiallyInvoiced':
            return 'returned';
        case 'notInvoiced':
        default:
            return 'returned';
    }
}

function calculateDisplayStatusForPartiallyReturnedStatus(
    invoiceStatus: xtremPurchasing.enums.PurchaseReceiptInvoiceStatus,
): xtremPurchasing.enums.PurchaseReceiptDisplayStatus {
    switch (invoiceStatus) {
        case 'invoiced':
            return 'invoiced';
        case 'partiallyInvoiced':
            return 'partiallyInvoiced';
        case 'notInvoiced':
        default:
            return 'partiallyReturned';
    }
}

function calculateDisplayStatusForNotReturnedStatus(parameters: {
    invoiceStatus: xtremPurchasing.enums.PurchaseReceiptInvoiceStatus;
    status: xtremPurchasing.enums.PurchaseDocumentStatus;
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus;
}): xtremPurchasing.enums.PurchaseReceiptDisplayStatus {
    switch (parameters.invoiceStatus) {
        case 'invoiced':
            return 'invoiced';
        case 'partiallyInvoiced':
            return 'partiallyInvoiced';
        default:
            if (parameters.status === 'closed') {
                return 'closed';
            }
            if (parameters.stockTransactionStatus === 'inProgress') {
                return 'postingInProgress';
            }
            if (parameters.stockTransactionStatus === 'completed') {
                return 'received';
            }
            return 'draft';
    }
}

/**
 * Calculates the display status of the receipt based on the other document status properties
 * @param status
 * @param returnStatus
 * @param invoiceStatus
 * @param stockTransactionStatus
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculatePurchaseReceiptDisplayStatus(
    status: xtremPurchasing.enums.PurchaseDocumentStatus,
    returnStatus: xtremPurchasing.enums.PurchaseReceiptReturnStatus,
    invoiceStatus: xtremPurchasing.enums.PurchaseReceiptInvoiceStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
): xtremPurchasing.enums.PurchaseReceiptDisplayStatus {
    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }
    if (stockTransactionStatus === 'error') {
        return 'error';
    }
    if (status === 'closed' && returnStatus === 'partiallyReturned' && invoiceStatus === 'partiallyInvoiced') {
        return 'closed';
    }
    switch (returnStatus) {
        case 'returned':
            return calculateDisplayStatusForReturnedStatus(invoiceStatus);
        case 'partiallyReturned':
            return calculateDisplayStatusForPartiallyReturnedStatus(invoiceStatus);
        case 'notReturned':
        default:
            return calculateDisplayStatusForNotReturnedStatus({ invoiceStatus, status, stockTransactionStatus });
    }
}

export async function linkedOrderArray(
    receipt: xtremPurchasing.nodes.PurchaseReceipt,
): Promise<xtremPurchasing.nodes.PurchaseOrder[] | undefined> {
    const orderLines = await receipt.lines
        .map(async line => {
            const purchaseOrderLine = await line.purchaseOrderLine;
            if (purchaseOrderLine !== null) {
                return (await (await purchaseOrderLine.purchaseOrderLine).document)._id;
            }
            return null;
        })
        .toArray();

    if (orderLines.length) {
        const orderQuery = await receipt.$.context
            .query(xtremPurchasing.nodes.PurchaseOrder, {
                filter: { _id: { _in: orderLines } },
            })
            .toArray();
        if (orderQuery.length) {
            return orderQuery;
        }
    }
    return undefined;
}

export async function updateHeaderNotesOnCreation(receipt: xtremPurchasing.nodes.PurchaseReceipt) {
    if (receipt.$.status === NodeStatus.added) {
        const purchaseOrderArray = await linkedOrderArray(receipt);
        if (purchaseOrderArray && purchaseOrderArray.length === 1) {
            const isTransferHeaderNote = await purchaseOrderArray[0].isTransferHeaderNote;

            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(receipt);
                await setHeaderInternalNote(receipt, purchaseOrderArray, currentNote);
            }
            await setHeaderSetupNote(receipt, isTransferHeaderNote, await purchaseOrderArray[0].isTransferLineNote);
        }
    }
}

export async function updateLineNotesOnCreation(receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine) {
    if (receiptLine.$.status === NodeStatus.added) {
        const purchaseOrderLine = await receiptLine.purchaseOrderLine;
        if (purchaseOrderLine !== null) {
            const linkedDocumentLine = await purchaseOrderLine.purchaseOrderLine;
            if (await (await linkedDocumentLine.document).isTransferLineNote) {
                const currentNote = await getCurrentLineNote(receiptLine);
                await setLineInternalNote(receiptLine, linkedDocumentLine, currentNote);
            }
        }
    }
}

export async function getInvoiceInstance(
    context: Context,
    purchaseReceipt: xtremPurchasing.nodes.PurchaseReceipt,
): Promise<xtremPurchasing.nodes.PurchaseInvoice[]> {
    if (
        ['closed'].includes(await purchaseReceipt.status) ||
        ['invoiced'].includes(await purchaseReceipt.invoiceStatus)
    ) {
        return [];
    }
    // All lines have to have an item
    if (await purchaseReceipt.lines.some(item => !item)) {
        return [];
    }
    // Group lines per potential supplier and site criteria
    const newPurchaseInvoicesCreateData =
        await xtremPurchasing.functions.PurchaseReceiptLib.initPurchaseInvoiceCreateData(purchaseReceipt, {
            isForFinanceCheck: true,
        });

    const purchaseInvoices: xtremPurchasing.nodes.PurchaseInvoice[] = [];

    await asyncArray(newPurchaseInvoicesCreateData).forEach(async purchaseInvoiceData => {
        if (purchaseInvoiceData.site) {
            const site = await context.tryRead(xtremSystem.nodes.Site, {
                _id: (purchaseInvoiceData.site || '0') as string,
            });
            if (site && !(await site.isFinance)) {
                purchaseInvoiceData.site = (await site.financialSite)?._id;
            }
        }
        const newPurchaseInvoice = await context.create(xtremPurchasing.nodes.PurchaseInvoice, purchaseInvoiceData, {
            isTransient: true,
        });

        if (await newPurchaseInvoice.$.control()) {
            purchaseInvoices.push(newPurchaseInvoice);
        }
    });
    return purchaseInvoices;
}

async function createStockCorrectionDetailsForLandedCosts(
    context: Context,
    data: {
        line: xtremPurchasing.nodes.PurchaseReceiptLine;
        landedCostLine: xtremLandedCost.nodes.LandedCostLine;
        amountToAbsorb: decimal;
        reasonCodeID: string;
    },
) {
    await xtremStockData.functions.stockDetailLib.createStockCorrectionDetailFromStockDetail(
        context,
        data.line.stockDetails,
        {
            amountToAbsorb: data.amountToAbsorb,
            absorbedAmount: data.amountToAbsorb,
            nonAbsorbedAmount: 0,
            correctedDocumentLineID: data.line._id,
            correctorDocumentLine: await (await (await data.landedCostLine.landedCostAllocation).line).documentLine,
            impactedQuantity: await data.line.quantityInStockUnit,
            reasonCodeID: data.reasonCodeID,
        },
    );
}

/**
 * when a line is linked to an order line, we have to correct the cost of the receipt with
 *   - the landed cost allocated to the order line
 * TODO: - the potential invoice of the order line
 * @param landedCostLineUpdates the list of landed cost lines for which the creation of correction is necessary
 * @returns the list of purchase receipt lines for which StockCorrectionDetail have been created
 */
export async function prepareStockCorrectionDetails(
    context: Context,
    parameters?: { updatedLandedCostLines?: xtremLandedCost.interfaces.LandedCostLineUpdate[] },
): Promise<xtremPurchasing.nodes.PurchaseReceiptLine[]> {
    const updatedLines: xtremPurchasing.nodes.PurchaseReceiptLine[] = [];
    const linesMap: Record<integer, boolean> = {};

    // Create StockCorrectionDetail associated to the landed cost lines
    if (
        (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption)) &&
        parameters?.updatedLandedCostLines
    ) {
        const reasonCode = await xtremStockData.functions.reasonCodeLib.getDefaultReasonCode(
            context,
            'landedCostAdjustment',
        );
        await asyncArray(parameters?.updatedLandedCostLines).forEach(async landedCostLineUpdate => {
            if (landedCostLineUpdate.varianceAmount === 0) {
                return;
            }

            const receiptLine = (await landedCostLineUpdate.landedCostLine
                .documentLine) as xtremPurchasing.nodes.PurchaseReceiptLine;
            await createStockCorrectionDetailsForLandedCosts(context, {
                line: receiptLine,
                landedCostLine: landedCostLineUpdate.landedCostLine,
                amountToAbsorb: landedCostLineUpdate.varianceAmount,
                reasonCodeID: (await reasonCode?.id) ?? '',
            });

            if (!linesMap[receiptLine._id]) {
                linesMap[receiptLine._id] = true;
                updatedLines.push(receiptLine);
            }
        });
    }
    // TODO: Create StockCorrectionDetail associated to the invoices linked to orders

    return updatedLines;
}

/**
 * Before the posting of a purchase receipt, delete records corresponding to a previous posting failure
 * and then prepare the correction
 */
export async function prepareReceiptStockPosting(context: Context, documentID: number) {
    const landedCostServiceOption = await context.isServiceOptionEnabled(
        xtremMasterData.serviceOptions.landedCostOption,
    );
    await context.runInWritableContext(async childContext => {
        const document = await childContext.read(
            xtremPurchasing.nodes.PurchaseReceipt,
            { _id: documentID },
            { forUpdate: true },
        );

        // When a receipt is posted, its lines must be updated by copying corresponding landed cost lines of the linked order line
        // It only concerns lines that are draft, otherwise they already have been created
        const linesToProcess = document.landedCostAssignableLines.filter(
            async receiptLine => (await receiptLine.stockTransactionStatus) === 'draft',
        );
        if (landedCostServiceOption) {
            // If LandedCostLine records exist at the posting stage, they must be deleted
            // They are likely to be the result of a failed stock posting
            await childContext.deleteMany(xtremLandedCost.nodes.LandedCostLine, {
                documentLine: { _in: await linesToProcess.map(line => line._id).toArray() },
            });
        }
        // ... then the corresponding StockCorrectionDetails must be deleted too
        // No correction record exist at the beginning of the posting
        await childContext.deleteMany(xtremStockData.nodes.StockCorrectionDetail, {
            documentLine: { _in: await linesToProcess.map(line => line._id).toArray() },
        });

        await xtremPurchasing.functions.PurchaseReceiptLib.prepareReceiptStockCorrection(childContext, linesToProcess);
    });
}

/**
 *  - Create receipt Landed cost lines from order line if available
 *  - Create StockCorrectionDetail records corresponding to landed cost lines
 *  TODO: - Create StockCorrectionDetail records corresponding to invoices on linked purchase order
 * @param context
 * @param linesToProcess
 * @param options
 * @returns
 */
export async function prepareReceiptStockCorrection(
    context: Context,
    linesToProcess:
        | AsyncArrayReader<xtremPurchasing.nodes.PurchaseReceiptLine>
        | Collection<xtremPurchasing.nodes.PurchaseReceiptLine>,
    options: {
        save: boolean;
        orderLine?: xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>;
    } = { save: true },
): Promise<xtremPurchasing.nodes.PurchaseReceiptLine[]> {
    let updatedLandedCostLines: xtremLandedCost.interfaces.LandedCostLineUpdate[] = [];
    let updatedReceiptLines: xtremPurchasing.nodes.PurchaseReceiptLine[] = [];

    if (await context.isServiceOptionEnabled(xtremMasterData.serviceOptions.landedCostOption)) {
        const result = await xtremLandedCost.functions.landedCostLineLib.propagateLandedCostLinesFromOrder(
            context,
            linesToProcess,
            options,
        );
        updatedLandedCostLines = result.updatedLandedCostLines;
        updatedReceiptLines = result.updatedTransactionDocumentLines as xtremPurchasing.nodes.PurchaseReceiptLine[];
    }

    await xtremPurchasing.functions.PurchaseReceiptLib.prepareStockCorrectionDetails(context, {
        updatedLandedCostLines,
    });

    await asyncArray(updatedReceiptLines).forEach(async line => {
        await line.$.set({ stockTransactionStatus: 'draft' });
    });

    return updatedReceiptLines;
}
