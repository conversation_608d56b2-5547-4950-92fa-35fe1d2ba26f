import type * as xtremPurchasing from '../index';

export async function getLineStatus(line: xtremPurchasing.nodes.PurchaseRequisitionLine) {
    if ((await (await line.document).approvalStatus) === 'approved') {
        switch (await line.lineOrderStatus) {
            case 'notOrdered':
                return 'pending';
            case 'partiallyOrdered':
                return 'inProgress';
            case 'ordered':
                return 'closed';
            default:
                return line.lineStatus;
        }
    }
    if ((await (await line.document).approvalStatus) === 'rejected') {
        return 'closed';
    }
    return line.lineStatus;
}

export async function getLineOrderStatus(line: xtremPurchasing.nodes.PurchaseRequisitionLine) {
    if ((await line.orderedQuantityInStockUnit) === 0) {
        return 'notOrdered';
    }
    if ((await line.orderedQuantityInStockUnit) < (await line.quantityInStockUnit)) {
        return 'partiallyOrdered';
    }
    return 'ordered';
}
