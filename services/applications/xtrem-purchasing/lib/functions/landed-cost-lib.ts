import { asyncArray } from '@sage/xtrem-core';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as _ from 'lodash';
import * as xtremPurchasing from '..';

/**
 * indicates id the allocated document line needs a stock price correction
 * @param allocation
 * @returns
 */
export async function isAllocatedDocumentLineToCorrect(
    allocation: xtremLandedCost.nodes.LandedCostAllocation,
): Promise<boolean> {
    const allocatedDocumentLine = await allocation.allocatedDocumentLine;

    return allocatedDocumentLine instanceof xtremPurchasing.nodes.PurchaseReceiptLine;
}

/** Try to find at least 1 allocation for which a price correction is required
 */
export function existAllocatedDocumentLineToCorrect(
    landedCost: xtremLandedCost.nodes.LandedCostDocumentLine,
): Promise<boolean> {
    return landedCost.allocations.some(allocation => {
        return isAllocatedDocumentLineToCorrect(allocation);
    });
}

export async function getLandedCostInvoiceNumbers(
    orderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<string[]> {
    const landedCostAllocations =
        await xtremLandedCost.functions.landedCostAllocationLib.getSortedLandedCostAllocations(
            orderLine.$.context,
            orderLine,
        );

    const invoices = await asyncArray(landedCostAllocations)
        .map(async alloc => (await (await alloc.line).documentLine).documentNumber)
        .toArray();

    return _.uniq(invoices);
}
