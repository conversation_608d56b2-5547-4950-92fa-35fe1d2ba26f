/** @ignore */ /** */
import { Logger, NodeStatus } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { BasePurchaseDocument, BasePurchaseDocumentLine } from '../nodes';

export const loggers = {
    /** invoice-lib */
    invoiceLib: Logger.getLogger(__filename, 'invoice-lib'),
    /** credit-memo-lib */
    orderLib: Logger.getLogger(__filename, 'order-lib'),
    /** receipt-lib */
    receiptLib: Logger.getLogger(__filename, 'receipt-lib'),
    /** requisition-lib */
    requisitionLib: Logger.getLogger(__filename, 'requisition-lib'),
    /** return-lib */
    returnLib: Logger.getLogger(__filename, 'return-lib'),
    /** base-document */
    baseDocument: Logger.getLogger(__filename, 'base-document'),
    /** credit memo */
    creditMemo: Logger.getLogger(__filename, 'credit-memo'),
    /** invoice */
    invoice: Logger.getLogger(__filename, 'invoice'),
    /** receipt */
    receipt: Logger.getLogger(__filename, 'receipt'),
    /** order */
    order: Logger.getLogger(__filename, 'order'),
    /** order-to-receipt */
    orderToReceipt: Logger.getLogger(__filename, 'order-to-receipt'),
    /** order-to-invoice */
    receiptToInvoice: Logger.getLogger(__filename, 'receipt-to-invoice'),
    /** receipt-to-return */
    receiptToReturn: Logger.getLogger(__filename, 'receipt-to-return'),
    /** return-to-invoice */
    returnToInvoice: Logger.getLogger(__filename, 'return-to-invoice'),
    /** requisition-line */
    requisitionLine: Logger.getLogger(__filename, 'requisition-line'),
    /** requisition */
    requisition: Logger.getLogger(__filename, 'requisition'),
    /** return */
    return: Logger.getLogger(__filename, 'return'),
    /**  same-properties */
    same: Logger.getLogger(__filename, 'same-properties'),
};

/** * `Document ${number} status: ${currentStatus}${oldStatusString} - approval: ${approval} */
export async function logStatusDocument(logger: Logger, document: BasePurchaseDocument): Promise<void> {
    const number = document.$.isValueDeferred('number') ? document._id : await document.number;
    const currentStatus = await document.status;
    const oldStatus = document.$.status === NodeStatus.added ? undefined : await document.status;
    const oldStatusString = oldStatus ? ` - old status : ${oldStatus.toString()}` : '';
    const approval = await document.approvalStatus;
    logger.debug(() => `Document ${number} status: ${currentStatus}${oldStatusString} - approval: ${approval}`);
}

/** * `Line ${sortValue} item ${itemName} status: ${currentStatus}${oldStatusString} */
export async function logStatusLine(logger: Logger, line: BasePurchaseDocumentLine): Promise<void> {
    const document = await line.document;
    const number = document.$.isValueDeferred('number') ? document._id : await document.number;
    const oldStatus = line.$.status === NodeStatus.added ? undefined : await line.status;
    const oldStatusString = oldStatus ? ` - old status : ${oldStatus.toString()}` : '';
    const currentStatus = await line.status;
    const sortValue = await line._sortValue;
    const itemName = await (await line.item).name;
    logger.debug(() => `Doc ${number} Line ${sortValue} item ${itemName} status: ${currentStatus}${oldStatusString}`);
}

export async function logNaturalKeyOfLine(
    logger: Logger,
    line: xtremMasterData.nodes.BaseDocumentItemLine,
): Promise<void> {
    await logger.debugAsync(async () => {
        const document = await line.document;
        const lineNaturalKey = document.$.isValueDeferred('number') ? line._id : await line.$.getNaturalKeyValue();
        return `${document.$.factory.name} Line : ${lineNaturalKey}`;
    });
}
