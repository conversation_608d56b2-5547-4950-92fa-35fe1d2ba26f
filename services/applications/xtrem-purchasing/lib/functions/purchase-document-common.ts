import type { Context, decimal, Node, NodeCreateData } from '@sage/xtrem-core';
import { asyncArray, NodeStatus } from '@sage/xtrem-core';
import type * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

export const getItemEqualToSite = async (context: Context, site: xtremSystem.nodes.Site) => {
    if (site) {
        const itemSites = await context
            .query(xtremMasterData.nodes.ItemSite, {
                filter: { site },
            })
            .toArray();
        return itemSites
            ? asyncArray(itemSites)
                  .map(async itemSite => (await itemSite.item).id)
                  .toArray()
            : [];
    }
    return [];
};

export const computeQuantityInStockUnitWithConversionFactor = (
    quantity: decimal,
    unit: xtremMasterData.nodes.UnitOfMeasure,
    stockUnit: xtremMasterData.nodes.UnitOfMeasure,
    format: boolean,
    hasRelatedOriginalLines: boolean,
    _isConversionFactorFrozen: boolean,
    nodeStatus: NodeStatus,
    unitToStockUnitConversionFactor: decimal,
    item?: xtremMasterData.nodes.Item | null,
    supplier?: xtremMasterData.nodes.Supplier | null,
) => {
    if (
        hasRelatedOriginalLines ||
        (nodeStatus !== NodeStatus.added && _isConversionFactorFrozen && unitToStockUnitConversionFactor !== 0)
    ) {
        return xtremMasterData.functions.convertFromTo(
            unit,
            stockUnit,
            quantity,
            format,
            unitToStockUnitConversionFactor,
            item ? 'purchase' : undefined,
            item || undefined,
            supplier || undefined,
        );
    }
    return xtremMasterData.functions.convertFromTo(
        unit,
        stockUnit,
        quantity,
        format,
        undefined,
        item ? 'purchase' : undefined,
        item || undefined,
        supplier || undefined,
    );
};

export const getDefaultPayToAddress = async (
    supplier: xtremMasterData.nodes.Supplier,
): Promise<xtremMasterData.nodes.BusinessEntityAddress | null> => {
    return (
        (await supplier?.payToAddress) ||
        (await (await (await supplier?.payToSupplier)?.businessEntity)?.addresses.find(add => add.isPrimary)) ||
        (await (await supplier?.businessEntity)?.addresses.find(add => add.isPrimary)) ||
        null
    );
};

/**
 * This function allow to dynamically set the isIgnoreActive decorator property on reference properties from nodes with 'isActive' property
 * @param instance : the current node instance
 * @returns : boolean (false if it's a new instance)
 */
export const shouldIgnoreIsActive = (instance: Node) => {
    return instance.$.status !== NodeStatus.added;
};

/**
 * @param actualDocumentLine: xtremPurchasing.interfaces.CostChangePurchaseDocumentLineInput
 * @returns true if price has changed between receipt vs invoice, receipt vs credit memo, otherwise false
 */
export const isDocumentLineCostChanged = async (
    actualDocumentLine: xtremPurchasing.interfaces.CostChangePurchaseDocumentLineInput,
): Promise<boolean> => {
    if (actualDocumentLine instanceof xtremPurchasing.nodes.PurchaseCreditMemoLine) {
        const netPrice = await (
            await (
                await (
                    await (
                        await actualDocumentLine.purchaseReturnLine
                    )?.purchaseReturnLine
                )?.purchaseReceiptLine
            )?.purchaseReceiptLine
        )?.netPrice;
        return netPrice ? (await actualDocumentLine.netPrice) !== netPrice : false;
    }
    const netPrice = await (await (await actualDocumentLine.purchaseReceiptLine)?.purchaseReceiptLine)?.netPrice;
    return netPrice ? (await actualDocumentLine.netPrice) !== netPrice : false;
};

export const getDefaultSiteAddressPayload = async (
    site: xtremSystem.nodes.Site,
): Promise<NodeCreateData<xtremMasterData.nodes.BusinessEntityAddress> | null> => {
    if (!(await site?.primaryAddress)) {
        return null;
    }

    return {
        isActive: await (await site.primaryAddress)?.isActive,
        name: await (await site.primaryAddress)?.name,
        addressLine1: await (await site.primaryAddress)?.addressLine1,
        addressLine2: await (await site.primaryAddress)?.addressLine2,
        city: await (await site.primaryAddress)?.city,
        region: await (await site.primaryAddress)?.region,
        postcode: await (await site.primaryAddress)?.postcode,
        country: await (await site.primaryAddress)?.country,
        locationPhoneNumber: await (await site.primaryAddress)?.locationPhoneNumber,
    } as NodeCreateData<xtremMasterData.nodes.BusinessEntityAddress>;
};

export function isPostedErrorClosed(status: xtremPurchasing.enums.PurchaseDocumentStatus | null) {
    return ['posted', 'error', 'closed'].includes(status || '');
}

/**
 * Returns the amount of a landed cost document line that must be spread on receipts and/or orders
 * @param documentLine a purchase document line that assigns landed costs to other documents (e.g. purchase invoice)
 * @returns
 */
export async function getLandedCostAmountToAllocate(
    documentLine: xtremPurchasing.nodes.BasePurchaseDocumentLine &
        xtremLandedCost.interfaces.DocumentLineAllocatingLandedCost,
) {
    return (
        (await documentLine.amountExcludingTax) +
        (await documentLine.taxes.sum(async tax => (await tax.taxAmountAdjusted) - (await tax.deductibleTaxAmount)))
    );
}

export async function setSiteAddress(
    document: xtremPurchasing.interfaces.BaseDocument,
    site: xtremSystem.nodes.Site,
    receivingAddress?: xtremMasterData.nodes.Address | null,
) {
    if (!(await document.siteAddress)) {
        await document.$.set({
            siteAddress: await xtremPurchasing.functions.PurchaseReceiptLib.getSiteAddressFromReceivingAddress(
                site,
                receivingAddress,
            ),
        });
    }
}

export async function handlePaymentTrackingOnSupplierDocumentChange(
    document: xtremPurchasing.nodes.PurchaseInvoice | xtremPurchasing.nodes.PurchaseCreditMemo,
) {
    if (
        document.$.status === NodeStatus.modified &&
        (await (await document.$.old).supplierDocumentDate) !== (await document.supplierDocumentDate)
    ) {
        const paymentTerm = await document.paymentTerm;
        await document.$.set({
            paymentTracking: {
                discountPaymentBeforeDate: xtremMasterData.sharedFunctions.getDiscountPaymentBeforeDate({
                    discountType: (await paymentTerm?.discountFrom) ?? 'afterInvoiceDate',
                    discountDate: (await paymentTerm?.discountDate) ?? 0,
                    baseDate: await document.supplierDocumentDate,
                }),
            },
        });
    }
}
