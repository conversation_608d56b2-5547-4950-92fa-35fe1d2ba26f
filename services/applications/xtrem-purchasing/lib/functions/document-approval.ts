import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError, LogicError } from '@sage/xtrem-core';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremPurchasing from '../index';

/** We don't send an approval mail if the document is already approved or rejected
 * Approval must not be 'draft', 'pendingApproval','changeRequested',
 * can be approved, or rejected */
export function checkApprovalStatus(
    context: Context,
    approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
) {
    const approvals: xtremPurchasing.enums.PurchaseDocumentApprovalStatus[] = ['approved', 'rejected'];
    if (approvals.includes(approvalStatus)) {
        context.logger.error(() => `Approval status is ${approvalStatus}.`);
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/functions__send_mail__for_approval__approved',
                'The document is already approved or rejected.',
            ),
        );
    }
}

export async function updatePendingApproval(
    document: xtremPurchasing.nodes.PurchaseRequisition | xtremPurchasing.nodes.PurchaseOrder,
) {
    await document.$.set({ approvalStatus: 'pendingApproval' });

    await document.$.save();
}

export async function updateChangeRequested(
    document: xtremPurchasing.nodes.PurchaseRequisition | xtremPurchasing.nodes.PurchaseOrder,
) {
    await document.$.set({ approvalStatus: 'changeRequested' });
    await document.$.save();
}

export async function setMatchingUser(creditMemo: {
    document: xtremPurchasing.nodes.PurchaseCreditMemo | xtremPurchasing.nodes.PurchaseInvoice;
    user?: xtremSystem.nodes.User;
}) {
    if (creditMemo.user) {
        await creditMemo.document.$.set({ matchingUser: creditMemo.user });
        await creditMemo.document.$.save();
    }
}

export async function getDocumenturl(document: xtremPurchasing.nodes.PurchaseRequisition) {
    return `${await document.page}/${Buffer.from(JSON.stringify({ _id: document._id.toString() })).toString('base64')}`;
}

export async function submitForApproval(
    document: xtremPurchasing.nodes.PurchaseRequisition | xtremPurchasing.nodes.PurchaseReturn,
) {
    await document.lines
        .filter(async line => (await line.status) === 'draft')
        .forEach(async line => {
            await line.$.set({ status: 'pending', approvalStatus: 'pendingApproval' });
        });
    await document.$.set({ status: 'pending', approvalStatus: 'pendingApproval' });
    await document.$.save();
}

export async function managePurchaseDocumentApprovalStatus(
    document: xtremPurchasing.nodes.PurchaseRequisition | xtremPurchasing.nodes.PurchaseReturn,
    approve: boolean,
): Promise<void> {
    if (!document) {
        throw new LogicError('No document.');
    }

    if (!((await document.status) === 'pending' && (await document.approvalStatus) === 'pendingApproval')) {
        throw new BusinessRuleError(
            document.$.context.localize(
                '@sage/xtrem-purchasing/nodes__base_purchase_document_cannot_approve_document',
                'No draft or pendingApproval document.',
            ),
        );
    }

    await document.$.set({
        approvalStatus: (approve && 'approved') || 'rejected',
        status: (approve && 'pending') || 'closed',
    });

    await document.lines.forEach(async line => {
        if ((await line.approvalStatus) === 'pendingApproval') {
            await line.$.set({ approvalStatus: (approve && 'approved') || 'rejected' });
        }
        await line.$.set({ status: (approve && 'pending') || 'closed' });
    });
    await document.$.save();
}
