import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremPurchasing from '..';

export async function updatePurchaseOrder(
    orderLineToLine: xtremPurchasing.nodes.PurchaseOrderLineToPurchaseInvoiceLine,
) {
    await xtremMasterData.functions.lineToLineLib.updateLinkedLine({
        originDocumentLine: await orderLineToLine.purchaseInvoiceLine,
        targetDocumentLine: await orderLineToLine.purchaseOrderLine,
    });
}
