import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremPurchasing from '../index';
import type { PurchaseEmailDocumentData } from '../interfaces/send-mail';

export async function getPurchaseCreditMemoEmailData(creditMemo: {
    document: xtremPurchasing.nodes.PurchaseCreditMemo;
    user: xtremSystem.nodes.User;
}): Promise<PurchaseEmailDocumentData<xtremPurchasing.nodes.PurchaseCreditMemo>> {
    const { context } = creditMemo.document.$;
    const subject = context.localize(
        '@sage/xtrem-purchasing/functions__purchase__credit_memo__buyer_notification_subject',
        'Purchase credit memo {{purchaseCreditMemoNumber}}: Matching request',
        { purchaseCreditMemoNumber: await creditMemo.document.number },
    );

    return {
        purchaseDocument: creditMemo.document,
        data: {
            number: await creditMemo.document.number,
            url: await creditMemo.document.page,
            requester: (await context.user)?.email ?? '',
            buyerId: creditMemo.user?._id,
            urlPurchaseDocument: await creditMemo.document.documentUrl,
        },
        subject,
    };
}
