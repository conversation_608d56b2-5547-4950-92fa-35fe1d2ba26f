import type * as xtremPurchasing from '../index';
import type { PurchaseEmailDocumentData } from '../interfaces/send-mail';

export async function getPurchaseInvoiceEmailData(
    purchaseDocument: xtremPurchasing.nodes.PurchaseInvoice,
): Promise<PurchaseEmailDocumentData<xtremPurchasing.nodes.PurchaseInvoice>> {
    const { context } = purchaseDocument.$;

    const subject = context.localize(
        '@sage/xtrem-purchasing/functions__purchase__invoice__buyer_notification_subject',
        '[Purchase invoice to be matched - {{purchaseInvoiceNumber}}]',
        { purchaseInvoiceNumber: await purchaseDocument.number },
    );

    return {
        purchaseDocument,
        data: {
            number: await purchaseDocument.number,
            supplier: await (await (await purchaseDocument.billBySupplier).businessEntity).name,
            site: await (await purchaseDocument.site).name,
            orderDate: (await purchaseDocument.invoiceDate).toString(),
            url: await purchaseDocument.page,
            requester: (await context.user)?.email ?? '',
            urlPurchaseDocument: await purchaseDocument.documentUrl,
        },
        subject,
    };
}
