import {
    asyncArray,
    BusinessRuleError,
    Context,
    NodeStatus,
    registerSqlFunction,
    UpdateAction,
} from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremPurchasing from '../index';
import {
    getCurrentHeaderNote,
    getCurrentLineNote,
    setHeaderInternalNote,
    setHeaderSetupNote,
    setLineInternalNote,
} from './common';

import { loggers } from './loggers';

/**
 * Calculates the display status of the order based on the other document status properties
 * @param status
 * @param taxCalculationStatus
 * @returns display status enum
 */
export function calculatePurchaseCreditMemoDisplayStatus(
    status: xtremPurchasing.enums.PurchaseDocumentStatus,
    taxCalculationStatus: xtremMasterData.enums.TaxCalculationStatus,
    stockTransactionStatus: xtremStockData.enums.StockDocumentTransactionStatus,
    paymentStatus: xtremFinanceData.enums.OpenItemStatus | null,
): xtremPurchasing.enums.PurchaseCreditMemoDisplayStatus {
    if (taxCalculationStatus === 'failed') {
        return 'taxCalculationFailed';
    }
    if (stockTransactionStatus === 'error') {
        return 'stockError';
    }
    if (paymentStatus && paymentStatus === 'paid') {
        return 'paid';
    }
    if (paymentStatus && paymentStatus === 'partiallyPaid') {
        return 'partiallyPaid';
    }
    if (status === 'posted') {
        return 'posted';
    }
    if (status === 'error') {
        return 'postingError';
    }
    if (status === 'inProgress') {
        return 'postingInProgress';
    }
    return 'draft';
}

export function isLinesOriginFromInvoice(creditMemo: xtremPurchasing.nodes.PurchaseCreditMemo) {
    return creditMemo.lines.every(async line => (await line.origin) === 'purchaseInvoice');
}

export function isLinesOriginFromReturn(creditMemo: xtremPurchasing.nodes.PurchaseCreditMemo) {
    return creditMemo.lines.every(async line => (await line.origin) === 'purchaseReturn');
}

export async function linkedInvoiceArray(
    creditMemo: xtremPurchasing.nodes.PurchaseCreditMemo,
): Promise<xtremPurchasing.nodes.PurchaseInvoice[] | undefined> {
    const toInvoiceLines = await creditMemo.lines
        .map(async line => {
            const invoiceLine = await line.purchaseInvoiceLine;
            if (invoiceLine !== null) {
                return (await (await invoiceLine.purchaseInvoiceLine).document)._id;
            }
            return null;
        })
        .toArray();

    if (toInvoiceLines.length) {
        const invoiceQuery = await creditMemo.$.context
            .query(xtremPurchasing.nodes.PurchaseInvoice, {
                filter: { _id: { _in: toInvoiceLines } },
            })
            .toArray();
        if (invoiceQuery.length) {
            return invoiceQuery;
        }
    }
    return undefined;
}

export async function linkedReturnArray(
    creditMemo: xtremPurchasing.nodes.PurchaseCreditMemo,
): Promise<xtremPurchasing.nodes.PurchaseReturn[] | undefined> {
    const returnLines = await creditMemo.lines
        .map(async line => {
            const returnLine = await line.purchaseReturnLine;
            if (returnLine !== null) {
                return (await (await returnLine.purchaseReturnLine).document)._id;
            }
            return null;
        })
        .toArray();

    if (returnLines.length) {
        const returnQuery = await creditMemo.$.context
            .query(xtremPurchasing.nodes.PurchaseReturn, {
                filter: { _id: { _in: returnLines } },
            })
            .toArray();
        if (returnQuery.length) {
            return returnQuery;
        }
    }
    return undefined;
}

export async function updateHeaderNotesOnCreation(creditMemo: xtremPurchasing.nodes.PurchaseCreditMemo) {
    if (creditMemo.$.status === NodeStatus.added) {
        let documentArray: xtremPurchasing.nodes.PurchaseInvoice[] | xtremPurchasing.nodes.PurchaseReturn[] | undefined;
        if (await isLinesOriginFromInvoice(creditMemo)) {
            documentArray = await linkedInvoiceArray(creditMemo);
        }

        if (await isLinesOriginFromReturn(creditMemo)) {
            documentArray = await linkedReturnArray(creditMemo);
        }

        if (documentArray && documentArray.length === 1) {
            const isTransferHeaderNote = await documentArray[0].isTransferHeaderNote;

            if (isTransferHeaderNote) {
                const currentNote = await getCurrentHeaderNote(creditMemo);
                await setHeaderInternalNote(creditMemo, documentArray, currentNote);
            }
            await setHeaderSetupNote(creditMemo, isTransferHeaderNote, await documentArray[0].isTransferLineNote);
        }
    }
}

async function setLineNotes(
    creditMemoLine: xtremPurchasing.nodes.PurchaseCreditMemoLine,
    line: xtremPurchasing.nodes.PurchaseReturnLine | xtremPurchasing.nodes.PurchaseInvoiceLine | undefined,
) {
    if (line && (await (await line.document).isTransferLineNote)) {
        const currentNote = await getCurrentLineNote(creditMemoLine);
        await setLineInternalNote(creditMemoLine, line, currentNote);
    }
}

export async function updateLineNotesOnCreation(creditMemoLine: xtremPurchasing.nodes.PurchaseCreditMemoLine) {
    if (creditMemoLine.$.status === NodeStatus.added) {
        const purchaseInvoiceLine = await creditMemoLine.purchaseInvoiceLine;
        const purchaseReturnLine = await creditMemoLine.purchaseReturnLine;
        if ((await creditMemoLine.origin) === 'purchaseInvoice') {
            await setLineNotes(creditMemoLine, await purchaseInvoiceLine?.purchaseInvoiceLine);
        }
        if ((await creditMemoLine.origin) === 'purchaseReturn') {
            await setLineNotes(creditMemoLine, await purchaseReturnLine?.purchaseReturnLine);
        }
    }
}

export async function getPurchaseReceiptLineFromInvoiceLine(
    creditMemoLine: xtremPurchasing.nodes.PurchaseCreditMemoLine,
): Promise<xtremPurchasing.nodes.PurchaseReceiptLine | null> {
    return (
        (await (await (await creditMemoLine.purchaseInvoiceLine)?.purchaseInvoiceLine)?.purchaseReceiptLine)
            ?.purchaseReceiptLine ?? null
    );
}

registerSqlFunction(
    'xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromInvoiceLine',
    getPurchaseReceiptLineFromInvoiceLine,
);

export async function getPurchaseReceiptLineFromReturnLine(
    creditMemoLine: xtremPurchasing.nodes.PurchaseCreditMemoLine,
): Promise<xtremPurchasing.nodes.PurchaseReceiptLine | null> {
    return (
        (await (await (await creditMemoLine.purchaseReturnLine)?.purchaseReturnLine)?.purchaseReceiptLine)
            ?.purchaseReceiptLine ?? null
    );
}

registerSqlFunction(
    'xtremPurchasing.functions.PurchaseCreditMemoLib.getPurchaseReceiptLineFromReturnLine',
    getPurchaseReceiptLineFromReturnLine,
);

export async function repost(
    context: Context,
    purchaseCreditMemo: xtremPurchasing.nodes.PurchaseCreditMemo,
    documentData: xtremPurchasing.interfaces.financeIntegration.PurchaseInvoiceCreditMemoRepost,
): Promise<xtremFinanceData.interfaces.MutationResult> {
    if (!xtremFinanceData.functions.canRepost((await purchaseCreditMemo.financeIntegrationStatus) || 'submitted')) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_credit_memo__cant_repost_purchasing_credit_memo_when_status_is_not_failed',
                "You can only repost a purchase credit memo if the status is 'Failed.'",
            ),
        );
    }

    loggers.invoiceLib.info(`Reposting purchase credit memo ${await purchaseCreditMemo.number}`);

    const financeTransactions = (
        await xtremFinanceData.nodes.FinanceTransaction.getPostingStatusData(context, await purchaseCreditMemo.number)
    ).filter(
        financeTransaction =>
            ['postingError', 'generationError'].includes(financeTransaction.status) &&
            !financeTransaction.hasSourceForDimensionLines,
    );

    const targetDocumentTypes = financeTransactions.map(financeTransaction => {
        return financeTransaction.documentType;
    });

    loggers.invoiceLib.info(`Target document types are ${JSON.stringify(targetDocumentTypes)}`);

    const hasTaxes = documentData.lines.every(line => {
        return !!line.uiTaxes;
    });
    const isAvalaraEnabled = (await purchaseCreditMemo.taxEngine) === 'avalaraAvaTax';

    const financeTransaction = targetDocumentTypes.includes('accountsPayableInvoice')
        ? await context.read(xtremFinanceData.nodes.FinanceTransaction, {
              _id: financeTransactions.filter(line => line.documentType === 'accountsPayableInvoice').at(0)?._id ?? 0,
          })
        : null;

    // If the user changed the tax data, avalara is not enabled and the invoice is already posted, we need to recreate
    // the tax information on the ap-ap invoice and before that we need to remove the accounting staging data
    // related with that ap-ar invoice
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        loggers.invoiceLib.info(`Document has taxes and Avalara is not enabled`);

        await xtremFinanceData.functions.AccountingEngineCommon.removeAccountingStagingForApArInvoice(
            context,
            financeTransaction,
        );
    }

    // Prepare the lines to be updated
    const updateLines = documentData.lines.map(documentLine => {
        return {
            _action: 'update' as UpdateAction,
            _id: documentLine.baseDocumentLineSysId,
            storedAttributes: documentLine.storedAttributes,
            storedDimensions: documentLine.storedDimensions,
            uiTaxes: hasTaxes ? documentLine.uiTaxes : undefined,
        };
    });

    // Update and save the purchase invoice
    await purchaseCreditMemo.$.set({
        supplierDocumentNumber: documentData.header.supplierDocumentNumber,
        forceUpdateForFinance: true,
        lines: updateLines,
        wasTaxDataChanged: hasTaxes,
        totalTaxAmount: documentData.header.totalTaxAmount ?? undefined,
    });
    if (
        documentData.header.paymentData &&
        ((await (await purchaseCreditMemo.paymentTracking)?.status) ?? 'notPaid') === 'notPaid'
    ) {
        await purchaseCreditMemo.$.set({ ...documentData.header.paymentData });
    }
    if (hasTaxes && !isAvalaraEnabled) {
        const taxes = await asyncArray(documentData.header?.taxes ?? [])
            .map(async tax => {
                const _action: any = tax.$.getRawPropertyValue('_action');
                const { _id } = tax;
                return _action === 'delete'
                    ? { _action, _id }
                    : { _action, ...(await tax.$.payload({ withIds: true, inputValuesOnly: true })) };
            })
            .toArray();

        await purchaseCreditMemo.$.set({ taxes });
    }
    await purchaseCreditMemo.$.save();

    // If the user changed the tax data, avalara is not enabled and the invoice is already posted, we need to recreate
    // the tax information on the ap-ap invoice on the staging table
    if (hasTaxes && !isAvalaraEnabled && !!financeTransaction) {
        await xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStagingForApArInvoice(context, {
            financeTransaction,
            document: purchaseCreditMemo,
            lines: await purchaseCreditMemo.lines.toArray(),
            replyTopic: 'PurchaseCreditMemo/accountingInterface',
        });
    }

    // send notification in order to update a staging table entry for the accounting engine and recreate the finance documents
    await xtremFinanceData.functions.purchaseSalesInvoiceCreditMemoUpdateNotification(context, {
        targetDocumentTypes,
        document: purchaseCreditMemo as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax,
        lines: (await purchaseCreditMemo.lines.toArray()) as xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[],
        documentType: 'purchaseCreditMemo',
        replyTopic: 'PurchaseCreditMemo/accountingInterface',
    });
    return {
        wasSuccessful: true,
        message: context.localize(
            '@sage/xtrem-purchasing/nodes__purchase_credit_memo__document_was_posted',
            'The purchase credit memo was posted.',
        ),
    };
}
