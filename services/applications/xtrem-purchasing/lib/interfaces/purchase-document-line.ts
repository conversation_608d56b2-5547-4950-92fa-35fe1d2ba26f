import type * as xtremPurchasing from '../index';

export type BaseDocumentLine =
    | xtremPurchasing.nodes.PurchaseReturnLine
    | xtremPurchasing.nodes.PurchaseInvoiceLine
    | xtremPurchasing.nodes.PurchaseReceiptLine
    | undefined;

export type BaseLineToLine =
    | xtremPurchasing.nodes.PurchaseReturnLineToPurchaseCreditMemoLine
    | xtremPurchasing.nodes.PurchaseInvoiceLineToPurchaseCreditMemoLine
    | xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine
    | null;
