import type { Collection, date, Reference } from '@sage/xtrem-core';
import type * as xtremFinanceData from '@sage/xtrem-finance-data';
import type * as xtremLandedCost from '@sage/xtrem-landed-cost';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremPurchasing from '../index';

// Interface for a purchase receipt line
// Used on purchase receipt posting
export interface ReceiptDocumentLine extends xtremFinanceData.interfaces.FinanceOriginDocumentLine {
    readonly purchaseOrderLine: Reference<xtremPurchasing.nodes.PurchaseOrderLineToPurchaseReceiptLine | null>;
    readonly landedCostLines?: Collection<xtremLandedCost.nodes.LandedCostLine>;
    readonly sourceDocumentType?: Promise<xtremFinanceData.enums.SourceDocumentType | null>;
}

export interface GetPurchaseReceiptNotificationPayloadParameters {
    document: xtremFinanceData.interfaces.PurchaseFinanceDocument;
    lines: ReceiptDocumentLine[];
    isJustChecking: boolean;
    stockJournalRecords?: xtremStockData.nodes.StockJournal[];
    documentNumber?: string;
}

export interface PurchaseInvoiceCreditMemoRepost {
    header: {
        supplierDocumentNumber: string;
        paymentData?: {
            supplierDocumentDate: date;
            paymentTerm: xtremMasterData.nodes.PaymentTerm;
        };
        totalTaxAmount?: number;
        taxes?: xtremTax.nodes.DocumentTax[];
    };
    lines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[];
}
