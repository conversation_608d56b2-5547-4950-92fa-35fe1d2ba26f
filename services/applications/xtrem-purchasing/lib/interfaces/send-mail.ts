import type * as xtremPurchasing from '../index';

export interface PurchaseEmailData {
    number: string;
    supplier?: string;
    receivingSite?: string;
    orderDate?: string;
    url?: string;
    requester: string;
    requestDate?: string;
    urlReject?: string;
    urlApprove?: string;
    buyerId?: number;
    defaultBuyer?: string;
    urlPurchaseDocument?: string;
    site?: string;
}

type PurchaseDocument =
    | xtremPurchasing.nodes.PurchaseRequisition
    | xtremPurchasing.nodes.PurchaseOrder
    | xtremPurchasing.nodes.PurchaseInvoice
    | xtremPurchasing.nodes.PurchaseCreditMemo;

export interface PurchaseEmailDocumentData<T extends PurchaseDocument> {
    purchaseDocument: T;
    data: PurchaseEmailData;
    subject: string;
    approvalStatus?: xtremPurchasing.enums.PurchaseDocumentApprovalStatus;
}

export interface GetDataParam {
    sysId: string;
    url: string;
    forApproval?: boolean;
    areChangesRequested?: boolean;
    email: string;
}

export interface SendPurchaseDocumentMail extends GetDataParam {
    type: xtremPurchasing.enums.PurchaseDocumentType;
    templateName: string;
    documentUrl?: string;
}
