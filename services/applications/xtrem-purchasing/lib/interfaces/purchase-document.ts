import type { TextStream, decimal } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../index';

export type BaseDocument =
    | xtremPurchasing.nodes.PurchaseOrder
    | xtremPurchasing.nodes.PurchaseCreditMemo
    | xtremPurchasing.nodes.PurchaseInvoice
    | xtremPurchasing.nodes.PurchaseReceipt
    | xtremPurchasing.nodes.PurchaseReturn;

export type PurchaseDocument =
    | xtremPurchasing.nodes.PurchaseRequisition
    | xtremPurchasing.nodes.PurchaseOrder
    | xtremPurchasing.nodes.PurchaseInvoice
    | xtremPurchasing.nodes.PurchaseCreditMemo;

export type CostChangePurchaseDocumentLineInput =
    | xtremPurchasing.nodes.PurchaseInvoiceLine
    | xtremPurchasing.nodes.PurchaseCreditMemoLine;

export interface PurchaseReturnStatusApproval {
    purchaseReturnNumber: string;
    toBeApproved: boolean;
}

export type PurchaseDocumentNotePropagation =
    | xtremPurchasing.nodes.PurchaseOrder
    | xtremPurchasing.nodes.PurchaseReceipt
    | xtremPurchasing.nodes.PurchaseInvoice
    | xtremPurchasing.nodes.PurchaseReturn
    | xtremPurchasing.nodes.PurchaseCreditMemo;

export type PurchaseDocumentLineNotePropagation =
    | xtremPurchasing.nodes.PurchaseOrderLine
    | xtremPurchasing.nodes.PurchaseReceiptLine
    | xtremPurchasing.nodes.PurchaseInvoiceLine
    | xtremPurchasing.nodes.PurchaseReturnLine
    | xtremPurchasing.nodes.PurchaseCreditMemoLine;

export type LinkedPurchaseDocument =
    | xtremPurchasing.nodes.PurchaseRequisition
    | xtremPurchasing.nodes.PurchaseOrder
    | xtremPurchasing.nodes.PurchaseReceipt
    | xtremPurchasing.nodes.PurchaseInvoice
    | xtremPurchasing.nodes.PurchaseReturn;

export type LinkedPurchaseDocumentLine =
    | xtremPurchasing.nodes.PurchaseRequisitionLine
    | xtremPurchasing.nodes.PurchaseOrderLine
    | xtremPurchasing.nodes.PurchaseReceiptLine
    | xtremPurchasing.nodes.PurchaseInvoiceLine
    | xtremPurchasing.nodes.PurchaseReturnLine;

export interface CurrentNote {
    isOverwrite: boolean;
    internalNote: TextStream;
    isExternalNote?: boolean;
    externalNote?: TextStream;
}

export interface UnbilledAccountPayableGetAccountParameters {
    type: string;
    id: string;
    receiptLine: xtremPurchasing.nodes.PurchaseReceiptLine;
    invoicedQuantity: decimal;
}

export interface DocumentForSupplierDocumentNumberValidation {
    supplierDocumentNumber: string | null;
    supplierId: number;
    nodeToQuery: string;
    currentDocumentSysId?: number;
}

export type InvoicedDocument = xtremPurchasing.nodes.PurchaseOrder | xtremPurchasing.nodes.PurchaseReceipt;

export type InvoicedDocumentLine = xtremPurchasing.nodes.PurchaseOrderLine | xtremPurchasing.nodes.PurchaseReceiptLine;
