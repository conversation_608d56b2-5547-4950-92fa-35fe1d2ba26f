import { CustomSqlAction } from '@sage/xtrem-system';

export const fillOriginPropertyInBaseDocumentItemLine = new CustomSqlAction({
    description: 'Fill origin property in base_document_item_line',
    body: async helper => {
        await helper.executeSql(`
            UPDATE ${helper.schemaName}.base_document_item_line li
            SET origin = CASE
                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_receipt_line_to_purchase_invoice_line link
                    WHERE link.purchase_invoice_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseReceipt'::${helper.schemaName}.base_origin_enum

                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_return_line_to_purchase_invoice_line link
                    WHERE link.purchase_invoice_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseReturn'::${helper.schemaName}.base_origin_enum

                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_order_line_to_purchase_invoice_line link
                    WHERE link.purchase_invoice_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseOrder'::${helper.schemaName}.base_origin_enum

                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_invoice_line_to_purchase_credit_memo_line link
                    WHERE link.purchase_credit_memo_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseInvoice'::${helper.schemaName}.base_origin_enum
                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_return_line_to_purchase_credit_memo_line link
                    WHERE link.purchase_credit_memo_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseReturn'::${helper.schemaName}.base_origin_enum

                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_order_line_to_purchase_receipt_line link
                    WHERE link.purchase_receipt_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseOrder'::${helper.schemaName}.base_origin_enum

                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_receipt_line_to_purchase_return_line link
                    WHERE link.purchase_return_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseReceipt'::${helper.schemaName}.base_origin_enum

                WHEN EXISTS (
                    SELECT 1
                    FROM ${helper.schemaName}.purchase_requisition_line_to_purchase_order_line link
                    WHERE link.purchase_order_line = li._id AND link._tenant_id = li._tenant_id
                ) THEN 'purchaseRequisition'::${helper.schemaName}.base_origin_enum

                ELSE 'direct'::${helper.schemaName}.base_origin_enum
            END;
        `);
    },
});
