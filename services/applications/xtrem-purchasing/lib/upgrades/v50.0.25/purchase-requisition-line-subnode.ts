import { CustomSqlAction } from '@sage/xtrem-system';

export const purchaseRequisitionLineSubNode = new CustomSqlAction({
    description: 'Update purchase requisition line to be a sub node of base purchase document line',
    fixes: {
        tables: ['purchase_requisition_line'],
        notNullableColumns: [{ table: 'purchase_requisition_line', column: 'purchase_requisition' }],
    },
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                prlRecord RECORD;
                base_id ${helper.schemaName}.base_document_line._id%TYPE :=1;
                BEGIN
                    -- There we are droping the constraints to avoid the constraint violation
                    SET CONSTRAINTS ALL DEFERRED;
                    FOR prlRecord IN (SELECT _id, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp FROM ${helper.schemaName}.purchase_requisition_line)
                    LOOP
                        INSERT INTO ${helper.schemaName}.base_document_line(_constructor, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp)
                        VALUES ( 'PurchaseRequisitionLine', prlRecord._tenant_id, prlRecord._create_user, prlRecord._create_stamp, prlRecord._update_user, prlRecord._update_stamp)
                        RETURNING _id INTO base_id;

                        UPDATE ${helper.schemaName}.purchase_requisition_line SET _id = base_id
                            WHERE _id = prlRecord._id AND purchase_requisition_line._tenant_id = prlRecord._tenant_id;

                        UPDATE ${helper.schemaName}.purchase_requisition_line_discount_charge SET document = base_id
                            WHERE document = prlRecord._id AND purchase_requisition_line_discount_charge._tenant_id = prlRecord._tenant_id;

                        UPDATE ${helper.schemaName}.purchase_requisition_line_to_purchase_order_line SET purchase_requisition_line = base_id
                            WHERE purchase_requisition_line = prlRecord._id AND purchase_requisition_line_to_purchase_order_line._tenant_id = prlRecord._tenant_id;
                    END LOOP;
            END $$;
            `);
    },
});
