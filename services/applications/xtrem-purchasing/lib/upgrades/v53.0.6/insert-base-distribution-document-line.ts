import { CustomSqlAction } from '@sage/xtrem-system';

export const insertBaseDistributionDocumentLine = new CustomSqlAction({
    description: 'Move some line properties from purchase to base document item line',
    body: async helper => {
        await helper.executeSql(`
                    INSERT INTO ${helper.schemaName}.base_distribution_document_line
                                (
                        _tenant_id,
                        _id,
                        _constructor,
                        _sort_value,
                        _custom_data,
                        gross_price,
                        price_origin,
                        net_price,
                        line_amount_excluding_tax,
                        line_amount_excluding_tax_in_company_currency
                                )
                    SELECT
                        line._tenant_id,
                        line._id,
                        line._constructor,
                        line._sort_value,
                        line._custom_data,
                        line.gross_price,
                        line.price_origin::text::${helper.schemaName}.base_price_origin_enum,
                        line.net_price,
                        line.line_amount_excluding_tax,
                        line.line_amount_excluding_tax_in_company_currency
                    FROM      ${helper.schemaName}.base_purchase_document_line line
                    LEFT JOIN ${helper.schemaName}.purchase_order_line AS porderline
                    ON        porderline._id = line._id
                    AND       porderline._tenant_id = line._tenant_id
                    AND       line._constructor = 'PurchaseOrderLine'
                    LEFT JOIN ${helper.schemaName}.purchase_return_line AS returnline
                    ON        returnline._id = line._id
                    AND       returnline._tenant_id = line._tenant_id
                    AND       line._constructor = 'PurchaseReturnLine'
                    LEFT JOIN ${helper.schemaName}.purchase_credit_memo_line AS pcmline
                    ON        pcmline._id = line._id
                    AND       pcmline._tenant_id = line._tenant_id
                    AND       line._constructor = 'PurchaseCreditMemoLine'
                    LEFT JOIN ${helper.schemaName}.purchase_invoice_line AS invoiceline
                    ON        invoiceline._id = line._id
                    AND       invoiceline._tenant_id = line._tenant_id
                    AND       line._constructor = 'PurchaseInvoiceLine'
                    LEFT JOIN ${helper.schemaName}.purchase_receipt_line AS receiptline
                    ON        receiptline._id = line._id
                    AND       receiptline._tenant_id = line._tenant_id
                    AND       line._constructor = 'PurchaseReceiptLine'
            `);
    },
});
