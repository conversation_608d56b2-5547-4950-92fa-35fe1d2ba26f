import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

export const insertBasePurchaseDocumentToBaseDocument = new UnsafeCustomSqlAction({
    description: 'Upgrade BasePurchaseDocuments to BaseDocument',
    maxAllowedDurationMs: 600_000,
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                pRecord RECORD;
                BEGIN
                 FOR pRecord IN (
                                        SELECT
                        doc._tenant_id,
                        doc._id,
                        doc._constructor,
                        doc.supplier,
                        doc.supplier as business_relation ,
                        doc.bill_by_supplier,
                        doc.fx_rate_date,
                        doc.payment_term,
                        doc.total_amount_excluding_tax,
                        doc.total_amount_excluding_tax_in_company_currency,
                        doc.total_tax_amount,
                        doc.total_taxable_amount,
                        doc.total_exempt_amount,
                        doc.tax_calculation_status,
                        doc.total_tax_amount_adjusted,
                        doc.company_fx_rate,
                        doc.company_fx_rate_divisor,
                        doc._custom_data,
    					COALESCE(porder.invoice_status::TEXT, return.invoice_status::TEXT, receipt.invoice_status::TEXT, 'notInvoiced')::${helper.schemaName}.invoice_status_enum AS invoice_status
                    FROM ${helper.schemaName}.base_purchase_document doc
                        left join ${helper.schemaName}.purchase_order as porder on porder._id = doc._id
                            and porder._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseOrder'
                        left join ${helper.schemaName}.purchase_return as return on return._id = doc._id
                            and return._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReturn'
                        left join ${helper.schemaName}.purchase_receipt as receipt on receipt._id = doc._id
                            and receipt._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReceipt'
                    )
                LOOP
                    INSERT INTO ${helper.schemaName}.base_distribution_document(
                            _tenant_id,
                            _id,
                            _constructor,
                            business_relation,
                            fx_rate_date,
                            company_fx_rate,
                            _custom_data,
                            payment_term,
                            tax_calculation_status,
                            company_fx_rate_divisor,
                            total_amount_excluding_tax,
                            total_tax_amount,
                            total_taxable_amount,
                            total_exempt_amount,
                            total_tax_amount_adjusted,
                            invoice_status,
                            total_amount_excluding_tax_in_company_currency
                        )
                        VALUES (
                            pRecord._tenant_id,
                            pRecord._id,
                            pRecord._constructor,
                            pRecord.business_relation,
                            pRecord.fx_rate_date,
                            pRecord.company_fx_rate,
                            pRecord._custom_data,
                            pRecord.payment_term,
                            pRecord.tax_calculation_status,
                            pRecord.company_fx_rate_divisor,
                            pRecord.total_amount_excluding_tax,
                            pRecord.total_tax_amount,
                            pRecord.total_taxable_amount,
                            pRecord.total_exempt_amount,
                            pRecord.total_tax_amount_adjusted,
                            pRecord.invoice_status,
                            pRecord.total_amount_excluding_tax_in_company_currency
                        );
                 END LOOP;
            END $$;
        `);
    },
});
