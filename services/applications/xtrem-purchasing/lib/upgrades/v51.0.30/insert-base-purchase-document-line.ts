import { CustomSqlAction } from '@sage/xtrem-system';

export const insertBasePurchaseDocumentLine = new CustomSqlAction({
    description: 'Move some line properties from purchase to base document item line',
    body: async helper => {
        await helper.executeSql(`
                   INSERT INTO ${helper.schemaName}.base_document_item_line (_constructor, _id, _sort_value, _tenant_id,
                        document, status, site, site_linked_address, item,
                        item_description, stock_site, stock_site_linked_address, stock_unit,
                        quantity_in_stock_unit, unit_to_stock_unit_conversion_factor,
                        unit, quantity, internal_note, external_note, is_external_note, analytical_data , stored_dimensions, stored_attributes)

                        SELECT line._constructor, line._id,line._sort_value, line._tenant_id, line.document,
                            line.status::text::${helper.schemaName}.base_status_enum,
                            line.site,
                            line.site_linked_address, line.item, line.item_description, line.stock_site, line.stock_site_linked_address,
                            line.stock_unit, line.quantity_in_stock_unit, line.unit_to_stock_unit_conversion_factor, line.unit,
                            line.quantity, line.internal_note, line.external_note, line.is_external_note,
                            COALESCE( porderLine.analytical_data, returnLine.analytical_data, pcmLine.analytical_data, invoiceLine.analytical_data, receiptLine.analytical_data) as analytical_data,
                            COALESCE( porderLine.stored_dimensions, returnLine.stored_dimensions, pcmLine.stored_dimensions, invoiceLine.stored_dimensions, receiptLine.stored_dimensions) as stored_dimensions,
                            COALESCE( porderLine.stored_attributes, returnLine.stored_attributes, pcmLine.stored_attributes, invoiceLine.stored_attributes, receiptLine.stored_attributes) as stored_attributes
                            FROM ${helper.schemaName}.base_purchase_document_line line
                                left join ${helper.schemaName}.purchase_order_line as porderLine on porderLine._id = line._id
                                    and porderLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseOrderLine'
                                left join ${helper.schemaName}.purchase_return_line as returnLine on returnLine._id = line._id
                                    and returnLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseReturnLine'
                                left join ${helper.schemaName}.purchase_credit_memo_line  as pcmLine on pcmLine._id = line._id
                                    and pcmLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseCreditMemoLine'
                                left join ${helper.schemaName}.purchase_invoice_line as invoiceLine on invoiceLine._id = line._id
                                    and invoiceLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseInvoiceLine'
                                left join ${helper.schemaName}.purchase_receipt_line as receiptLine on receiptLine._id = line._id
                                    and receiptLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseReceiptLine'
            `);
    },
});
