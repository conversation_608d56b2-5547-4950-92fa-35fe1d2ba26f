import { UnsafeCustomSqlAction } from '@sage/xtrem-system';

export const insertBasePurchaseDocumentToBaseDocument = new UnsafeCustomSqlAction({
    description: 'Upgrade BasePurchaseDocuments to BaseDocument',
    maxAllowedDurationMs: 600_000,
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                pRecord RECORD;
                base_id ${helper.schemaName}.base_document._id%TYPE :=1;
                start_id ${helper.schemaName}.base_document._id%TYPE :=1;
                financial_site_id ${helper.schemaName}.site._id%TYPE;
                documentType varchar(255):= '';
                start_time TIMESTAMP;
                end_time TIMESTAMP;
                elapsed_time_document_insert BIGINT :=0;
                elapsed_time_purchase_doc BIGINT :=0;
                elapsed_time_purchase_doc_line BIGINT :=0;
                elapsed_time_purchase_doc_line_tax BIGINT :=0;
                elapsed_time_purchase_doc_line_final BIGINT :=0;
                elapsed_time_purchase_doc_finance BIGINT :=0;
                elapsed_time_doc_association BIGINT :=0;

                elapsed_time_finance_transaction BIGINT :=0;
                elapsed_time_finance_transaction_line BIGINT :=0;
                elapsed_time_finance_transaction_source BIGINT :=0;
                elapsed_time_accounting_staging BIGINT :=0;
                elapsed_time_base_open_item BIGINT :=0;
                interval_time TIMESTAMP;
                interval_time_end TIMESTAMP;

                BEGIN

                    start_time := clock_timestamp();
                        -- There we are dropping the constraints to avoid the constraint violation
                        SET CONSTRAINTS ALL DEFERRED;

                        start_id  = (SELECT COALESCE(MAX(_id), 0) FROM ${helper.schemaName}.base_purchase_document);

                        -- We are setting the sequence to the max id of the base_purchase_document if it is greater than the current sequence value
                        IF start_id > COALESCE((SELECT last_value FROM ${helper.schemaName}.base_document__id_seq) , 0) THEN
                            EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || start_id+1;
                        END IF;
                    end_time := clock_timestamp();

                    RAISE WARNING 'Elapsed time for setting sequence and deferred: %', (extract(milliseconds from end_time - start_time));

                 RAISE WARNING 'BasePurchaseDocument upgrade started  ';

                 FOR pRecord IN ( SELECT doc._constructor, doc._id, doc._tenant_id, doc._create_user, doc._create_stamp, doc._update_user, doc._update_stamp, doc.number , doc.status , doc.display_status,
                    doc.site, doc.stock_site,  doc.currency, doc.internal_note, doc.external_note,
                    doc.is_external_note, doc.is_transfer_header_note, doc.is_transfer_line_note,
                    site.is_finance , site.financial_site, doc.site_address, doc.business_entity_address,
                    COALESCE(porder.approval_status, return.approval_status, 'draft') as approval_status,
                    COALESCE(porder.order_date, return.return_request_date , pcm.credit_memo_date, invoice.invoice_date, receipt.receipt_date ) as date,
                    COALESCE(porder.is_printed, false) as is_printed,
                    COALESCE(porder.is_sent, false) as is_sent,
                    COALESCE(doc.text, '') as text

                    FROM ${helper.schemaName}.base_purchase_document doc
                        inner join ${helper.schemaName}.site as site on site._id = doc.site
                            and site._tenant_id = doc._tenant_id
                        left join ${helper.schemaName}.purchase_order as porder on porder._id = doc._id
                            and porder._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseOrder'
                        left join ${helper.schemaName}.purchase_return as return on return._id = doc._id
                            and return._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReturn'
                        left join ${helper.schemaName}.purchase_credit_memo as pcm on pcm._id = doc._id
                            and pcm._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseCreditMemo'
                        left join ${helper.schemaName}.purchase_invoice as invoice on invoice._id = doc._id
                            and invoice._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseInvoice'
                        left join ${helper.schemaName}.purchase_receipt as receipt on receipt._id = doc._id
                            and receipt._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReceipt' )
                LOOP

                    financial_site_id := case when pRecord.is_finance = true then pRecord.site else pRecord.financial_site end;

                    start_time := clock_timestamp();
                    INSERT INTO ${helper.schemaName}.base_document(_constructor, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp,
                        number, status, display_status, approval_status, date , is_printed, is_sent, site, stock_site, currency, financial_site,
                        site_address, business_entity_address,
                        internal_note, external_note, is_external_note, is_transfer_header_note, is_transfer_line_note, text )

                        VALUES ( pRecord._constructor , pRecord._tenant_id, pRecord._create_user, pRecord._create_stamp, pRecord._update_user, pRecord._update_stamp,
                        pRecord.number,
                        pRecord.status::text::${helper.schemaName}.base_status_enum,
                        pRecord.display_status::text::${helper.schemaName}.base_display_status_enum,
                        pRecord.approval_status::text::${helper.schemaName}.approval_status_enum,
                        pRecord.date, pRecord.is_printed, pRecord.is_sent, pRecord.site, pRecord.stock_site, pRecord.currency, financial_site_id ,
                        pRecord.site_address, pRecord.business_entity_address, pRecord.internal_note, pRecord.external_note,
                        pRecord.is_external_note,  pRecord.is_transfer_header_note, pRecord.is_transfer_line_note, pRecord.text

                        )
                        RETURNING _id INTO base_id;
                    end_time := clock_timestamp();
                    elapsed_time_document_insert := elapsed_time_document_insert + (extract(milliseconds from end_time - start_time));

                    start_time := clock_timestamp();
                    UPDATE ${helper.schemaName}.base_purchase_document SET _id = base_id
                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id ;
                    end_time := clock_timestamp();
                    elapsed_time_purchase_doc := elapsed_time_purchase_doc + (extract(milliseconds from end_time - start_time));

                    start_time := clock_timestamp();
                    UPDATE ${helper.schemaName}.base_purchase_document_line SET document = base_id
                        WHERE document = pRecord._id AND _tenant_id = pRecord._tenant_id;
                    end_time := clock_timestamp();
                    elapsed_time_purchase_doc_line := elapsed_time_purchase_doc_line + (extract(milliseconds from end_time - start_time));

                    start_time := clock_timestamp();
                    UPDATE ${helper.schemaName}.attachment_association set source_node_id = base_id
                            where source_node_id = pRecord._id AND source_node_name = pRecord._constructor AND _tenant_id = pRecord._tenant_id;
                    end_time := clock_timestamp();
                    elapsed_time_doc_association := elapsed_time_doc_association + (extract(milliseconds from end_time - start_time));

                    start_time := clock_timestamp();
                    UPDATE ${helper.schemaName}.purchase_document_tax SET document = base_id
                        WHERE document = pRecord._id AND _tenant_id = pRecord._tenant_id;

                    end_time := clock_timestamp();
                    elapsed_time_purchase_doc_line_tax := elapsed_time_purchase_doc_line_tax + (extract(milliseconds from end_time - start_time));

                    start_time := clock_timestamp();

                    IF (pRecord._constructor='PurchaseOrder') THEN
                        UPDATE ${helper.schemaName}.purchase_order SET _id = base_id
                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;

                        documentType= 'purchaseOrder';

                    ELSIF (pRecord._constructor='PurchaseInvoice') THEN
                        UPDATE ${helper.schemaName}.purchase_invoice SET _id = base_id
                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;

                       documentType= 'purchaseInvoice';

                    ELSIF (pRecord._constructor='PurchaseCreditMemo') THEN
                        UPDATE ${helper.schemaName}.purchase_credit_memo SET _id = base_id
                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;

                        documentType= 'purchaseCreditMemo';

                    ELSIF (pRecord._constructor='PurchaseReceipt') THEN
                        UPDATE ${helper.schemaName}.purchase_receipt SET _id = base_id
                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;

                        UPDATE ${helper.schemaName}.unbilled_account_payable_result_line SET receipt_internal_id = base_id
                            WHERE receipt_internal_id = pRecord._id AND _tenant_id = pRecord._tenant_id;


                        documentType= 'purchaseReceipt';

                    ELSIF (pRecord._constructor='PurchaseReturn') THEN
                        UPDATE ${helper.schemaName}.purchase_return SET _id = base_id
                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;

                        documentType= 'purchaseReturn';

                    ELSE
                        RAISE EXCEPTION 'Unknown constructor: %', pRecord._constructor;
                    END IF;
                    end_time := clock_timestamp();
                    elapsed_time_purchase_doc_line_final := elapsed_time_purchase_doc_line_final + (extract(milliseconds from end_time - start_time));


                    start_time := clock_timestamp();

                        UPDATE ${helper.schemaName}.finance_transaction SET source_document_sys_id = base_id
                        WHERE source_document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id
                        AND source_document_type = documentType::${helper.schemaName}.source_document_type_enum;

                    interval_time := clock_timestamp();
                    elapsed_time_finance_transaction_source := elapsed_time_finance_transaction_source + (extract(milliseconds from interval_time - start_time));

                        UPDATE ${helper.schemaName}.finance_transaction_line SET source_document_sys_id = base_id
                        WHERE source_document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id
                        AND source_document_type = documentType::${helper.schemaName}.source_document_type_enum;
                    interval_time_end := clock_timestamp();
                    elapsed_time_finance_transaction_line := elapsed_time_finance_transaction_line + (extract(milliseconds from interval_time_end - interval_time));


                    IF (documentType<>'purchaseOrder') THEN
                        interval_time := clock_timestamp();
                            UPDATE ${helper.schemaName}.accounting_staging SET document_sys_id = base_id
                            WHERE document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id
                            AND document_type = documentType::${helper.schemaName}.finance_document_type_enum;
                        interval_time_end := clock_timestamp();
                        elapsed_time_accounting_staging := elapsed_time_accounting_staging + (extract(milliseconds from interval_time_end - interval_time));

                         interval_time := clock_timestamp();
                            UPDATE ${helper.schemaName}.finance_transaction SET document_sys_id = base_id
                            WHERE document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id
                            AND document_type = documentType::${helper.schemaName}.finance_document_type_enum;
                        interval_time_end := clock_timestamp();
                        elapsed_time_finance_transaction := elapsed_time_finance_transaction + (extract(milliseconds from interval_time_end - interval_time));

                        interval_time := clock_timestamp();
                            UPDATE ${helper.schemaName}.base_open_item SET document_sys_id = base_id
                            WHERE document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id
                            AND document_type = documentType::${helper.schemaName}.finance_document_type_enum;
                        interval_time_end := clock_timestamp();
                        elapsed_time_base_open_item := elapsed_time_base_open_item + (extract(milliseconds from interval_time_end - interval_time));

                    END IF;
                    end_time := clock_timestamp();
                    elapsed_time_purchase_doc_finance := elapsed_time_purchase_doc_finance + (extract(milliseconds from end_time - start_time));

                 END LOOP;

                RAISE WARNING 'BasePurchaseDocument upgrade completed  ';
                RAISE WARNING 'Elapsed time for document insert: %', elapsed_time_document_insert;
                RAISE WARNING 'Elapsed time for purchase_doc update: %', elapsed_time_purchase_doc;
                RAISE WARNING 'Elapsed time for purchase_doc_line update: %', elapsed_time_purchase_doc_line;
                RAISE WARNING 'Elapsed time for purchase_doc_line_tax update: %', elapsed_time_purchase_doc_line_tax;
                RAISE WARNING 'Elapsed time for purchase_doc_line_final update: %', elapsed_time_purchase_doc_line_final;
                RAISE WARNING 'Elapsed time for doc_association update: %', elapsed_time_doc_association;

                RAISE WARNING 'Elapsed time for purchase_doc_finance update: %', elapsed_time_purchase_doc_finance;
                RAISE WARNING ' -- Elapsed time for finance_transaction_source update: %', elapsed_time_finance_transaction_source;
                RAISE WARNING ' -- Elapsed time for finance_transaction_line update: %', elapsed_time_finance_transaction_line;
                RAISE WARNING ' -- Elapsed time for accounting_staging update: %', elapsed_time_accounting_staging;
                RAISE WARNING ' -- Elapsed time for finance_transaction update: %', elapsed_time_finance_transaction;
                RAISE WARNING ' -- Elapsed time for base_open_item update: %', elapsed_time_base_open_item;
            END $$;

        `);
    },
});
