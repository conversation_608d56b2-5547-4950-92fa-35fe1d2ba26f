import { CustomSqlAction } from '@sage/xtrem-system';

export const setStockTransactionStatus = new CustomSqlAction({
    description: 'Set the value of PurchaseReturn.stockTransactionStatus',
    fixes: {
        notNullableColumns: [{ table: 'purchase_return', column: 'stock_transaction_status' }],
    },
    body: async helper => {
        await helper.executeSql(`
UPDATE
 ${helper.schemaName}.purchase_return srd
SET
 stock_transaction_status =
                ( CASE
  /* need to find out if any purchase receipt lines have a stock transaction status of 'error' */
  WHEN ((
  SELECT
   count (stock_transaction_status)
  FROM
   ${helper.schemaName}.purchase_return_line prl
  INNER JOIN ${helper.schemaName}.base_document_item_line bdil
  ON bdil._id = prl._id
  AND bdil._tenant_id = prl._tenant_id
  WHERE
   srd._id = bdil.document
   AND srd._tenant_id = prl._tenant_id
   AND prl.stock_transaction_status = 'error') > 0)
   THEN 'error'::${helper.schemaName}.stock_document_transaction_status_enum
/* need to get find out if any purchase receipt lines have a stock transaction status of 'inProgress' */
  WHEN ((
  SELECT
   count (stock_transaction_status)
  FROM
   ${helper.schemaName}.purchase_return_line prl
  INNER JOIN ${helper.schemaName}.base_document_item_line bdil
  ON bdil._id = prl._id
  AND bdil._tenant_id = prl._tenant_id
  WHERE
   srd._id = bdil.document
   AND srd._tenant_id = prl._tenant_id
   AND prl.stock_transaction_status = 'inProgress') > 0)
   THEN 'inProgress'::${helper.schemaName}.stock_document_transaction_status_enum
 /* need to get find out if any purchase receipt lines have a stock transaction status of 'draft' */
  WHEN ((
  SELECT
   count (stock_transaction_status)
  FROM
   ${helper.schemaName}.purchase_return_line prl
  INNER JOIN ${helper.schemaName}.base_document_item_line bdil
  ON bdil._id = prl._id
  AND bdil._tenant_id = prl._tenant_id
  WHERE
   srd._id = bdil.document
   AND srd._tenant_id = prl._tenant_id
   AND prl.stock_transaction_status = 'draft') > 0)
   THEN 'draft'::${helper.schemaName}.stock_document_transaction_status_enum
 /* need to get find out if any purchase receipt lines have a stock transaction status of 'completed' */
  WHEN ((
  SELECT
   count (stock_transaction_status)
  FROM
   ${helper.schemaName}.purchase_return_line prl
  INNER JOIN ${helper.schemaName}.base_document_item_line bdil
  ON bdil._id = prl._id
  AND bdil._tenant_id = prl._tenant_id
  WHERE
   srd._id = bdil.document
   AND srd._tenant_id = prl._tenant_id
   AND prl.stock_transaction_status = 'completed') > 0)
  THEN 'completed'::${helper.schemaName}.stock_document_transaction_status_enum
  ELSE 'draft'::${helper.schemaName}.stock_document_transaction_status_enum
 END)
 `);
    },
});
