import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { BasePurchaseDocumentLine } from '../../nodes';

export const renamePropertyBasePurchaseDocumentLineForLineAmountIncludingTax = new SchemaRenamePropertyAction({
    node: () => BasePurchaseDocumentLine,
    oldPropertyName: 'lineAmountIncludingTax',
    newPropertyName: 'amountIncludingTax',
});

export const renamePropertyBasePurchaseDocumentLineForLineAmountIncludingTaxInCompanyCurrency =
    new SchemaRenamePropertyAction({
        node: () => BasePurchaseDocumentLine,
        oldPropertyName: 'lineAmountIncludingTaxInCompanyCurrency',
        newPropertyName: 'amountIncludingTaxInCompanyCurrency',
    });
