import { CustomSqlAction } from '@sage/xtrem-system';

export const basePurchaseDocumentDisplayStatus = new CustomSqlAction({
    description: 'Set the value of displayStatus into basePurchaseDocument',
    fixes: { notNullableColumns: [{ table: 'base_purchase_document', column: 'display_status' }] },
    body: async helper => {
        await helper.executeSql(`
            UPDATE ${helper.schemaName}.base_purchase_document as bpd
            SET display_status =  po.display_status::text::${helper.schemaName}.inbound_display_status_enum
            FROM ${helper.schemaName}.purchase_order as po
            WHERE po._id = bpd._id and po._tenant_id = bpd._tenant_id
            `);
        await helper.executeSql(`
                UPDATE ${helper.schemaName}.base_purchase_document as bpd
                SET display_status = pcm.display_status::text::${helper.schemaName}.inbound_display_status_enum
                FROM ${helper.schemaName}.purchase_credit_memo as pcm
                WHERE pcm._id = bpd._id and pcm._tenant_id = bpd._tenant_id
                `);
        await helper.executeSql(` UPDATE ${helper.schemaName}.base_purchase_document as bpd
                    SET display_status = pi.display_status::text::${helper.schemaName}.inbound_display_status_enum
                    FROM ${helper.schemaName}.purchase_invoice as pi
                        where pi._id = bpd._id and pi._tenant_id = bpd._tenant_id
                `);
        await helper.executeSql(` UPDATE ${helper.schemaName}.base_purchase_document as bpd
                    SET display_status =  pr.display_status::text::${helper.schemaName}.inbound_display_status_enum
                    FROM ${helper.schemaName}.purchase_receipt as pr
                    WHERE pr._id = bpd._id and pr._tenant_id = bpd._tenant_id
                `);
        await helper.executeSql(` UPDATE ${helper.schemaName}.base_purchase_document as bpd
                    SET display_status =  pret.display_status::text::${helper.schemaName}.inbound_display_status_enum
                    FROM ${helper.schemaName}.purchase_return as pret
                    WHERE pret._id = bpd._id and pret._tenant_id = bpd._tenant_id
                `);
    },
});
