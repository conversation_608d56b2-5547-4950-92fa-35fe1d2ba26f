import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { PurchaseRequisition, PurchaseRequisitionLine } from '../../nodes';

export const renamePropertyPurchaseRequisitionSite = new SchemaRenamePropertyAction({
    node: () => PurchaseRequisition,
    oldPropertyName: 'receivingSite',
    newPropertyName: 'site',
});

export const renamePropertyPurchaseRequisitionLineSite = new SchemaRenamePropertyAction({
    node: () => PurchaseRequisitionLine,
    oldPropertyName: 'receivingSite',
    newPropertyName: 'site',
});
