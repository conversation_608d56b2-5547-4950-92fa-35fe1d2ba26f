// base_business_relation
import { CustomSqlAction } from '@sage/xtrem-system';

export const purchaseRequisitionBaseDocument = new CustomSqlAction({
    description: 'Move the purchase_requisition to the base_document table',
    fixes: { tables: ['purchase_requisition'] },
    body: async helper => {
        await helper.executeSql(`
            DO $$ DECLARE
                prRecord RECORD;
                base_id ${helper.schemaName}.base_document._id%TYPE := 1;
                start_id ${helper.schemaName}.base_document._id%TYPE :=1;
                financial_site_id ${helper.schemaName}.site._id%TYPE;
            BEGIN

                -- There we are droping the constraints to avoid the constraint violation
                SET CONSTRAINTS ALL DEFERRED;

                  start_id  = (SELECT COALESCE(MAX(_id), 0) FROM ${helper.schemaName}.purchase_requisition);

                -- We are setting the sequence to the max id of the base_document if it is greater than the current sequence value
                IF start_id > COALESCE((SELECT last_value FROM ${helper.schemaName}.base_document__id_seq) , 0) THEN
                    EXECUTE 'ALTER SEQUENCE ${helper.schemaName}.base_document__id_seq RESTART WITH ' || start_id+1;
                END IF;

                FOR prRecord IN ( SELECT pr._id, pr._tenant_id, pr.number,
                    pr.status::text::${helper.schemaName}.base_status_enum,
                    pr.display_status::text::${helper.schemaName}.base_display_status_enum,
                    pr.approval_status::text::${helper.schemaName}.approval_status_enum, pr.site,
                    pr.request_date, pr.internal_note, pr.is_transfer_header_note, pr.is_transfer_line_note,
                    pr._create_user, pr._create_stamp, pr._update_user, pr._update_stamp ,
                    site.is_finance, site.financial_site, c.currency
                    FROM ${helper.schemaName}.purchase_requisition pr
                    INNER JOIN ${helper.schemaName}.site AS site ON site._id = pr.site AND pr._tenant_id = site._tenant_id
                    INNER JOIN ${helper.schemaName}.company AS c ON c._id = site.legal_company AND c._tenant_id = site._tenant_id )

                LOOP
                    financial_site_id := CASE WHEN prRecord.is_finance = true THEN prRecord.site ELSE prRecord.financial_site END;

                    INSERT INTO ${helper.schemaName}.base_document( _tenant_id, _constructor, number, status,
                    display_status, approval_status, date, is_printed, is_sent, site, currency,
                    financial_site,
                    internal_note, external_note, is_external_note, is_transfer_header_note, is_transfer_line_note, text,
                    _create_user, _create_stamp, _update_user, _update_stamp)
                    VALUES (prRecord._tenant_id, 'PurchaseRequisition', prRecord.number, prRecord.status, prRecord.display_status,
                    prRecord.approval_status, prRecord.request_date,
                    FALSE, FALSE, prRecord.site, prRecord.currency, financial_site_id,
                    prRecord.internal_note, '', FALSE, prRecord.is_transfer_header_note,
                    prRecord.is_transfer_line_note, '',
                    prRecord._create_user, prRecord._create_stamp, prRecord._update_user, prRecord._update_stamp)
                    RETURNING _id INTO base_id;

                    UPDATE ${helper.schemaName}.purchase_requisition SET _id = base_id
                        WHERE _id = prRecord._id AND purchase_requisition._tenant_id = prRecord._tenant_id;

                    UPDATE ${helper.schemaName}.purchase_requisition_line SET document = base_id
                        WHERE document = prRecord._id AND purchase_requisition_line._tenant_id = prRecord._tenant_id;

                    UPDATE ${helper.schemaName}.attachment_association set source_node_id = base_id
                        where source_node_id = prRecord._id and source_node_name = 'PurchaseRequisition';

                END LOOP;
            END $$;`);
    },
});
