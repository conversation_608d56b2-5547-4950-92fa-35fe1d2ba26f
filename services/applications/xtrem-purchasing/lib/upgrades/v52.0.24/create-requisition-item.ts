import { CustomSqlAction } from '@sage/xtrem-system';

export const createRequisitionItem = new CustomSqlAction({
    description: 'Create a requisition item',
    body: async helper => {
        await helper.executeSql(`
            INSERT INTO ${helper.schemaName}.item (
                _tenant_id,
                id,
                name,
                description,
                is_bought,
                is_manufactured,
                is_sold,
                stock_unit,
                volume,
                weight,
                density,
                is_stock_managed,
                is_phantom,
                is_potency_management,
                is_traceability_management,
                serial_number_management,
                serial_number_usage,
                minimum_sales_quantity,
                maximum_sales_quantity,
                base_price,
                minimum_price,
                intrastat_additional_unit_to_stock_unit_conversion,
                lot_management,
                is_expiry_managed,
                analytical_data,
                _create_user,
                _update_user,
                status,
                type
            )
            SELECT
                t.tenant_id,
                'requisitionItem' AS id,
                '{ "base": "requisition item" }' AS name,
                '{ "base": "requisition item" }' AS description,
                FALSE AS is_bought,
                FALSE AS is_manufactured,
                FALSE AS is_sold,
                m._id AS stock_unit,
                0 AS volume,
                0 AS weight,
                0 AS density,
                FALSE AS is_stock_managed,
                TRUE AS is_phantom,
                FALSE AS is_potency_management,
                FALSE AS is_traceability_management,
                'notManaged' AS serial_number_management,
                'issueOnly' AS serial_number_usage,
                0 AS minimum_sales_quantity,
                0 AS maximum_sales_quantity,
                0 AS base_price,
                0 AS minimum_price,
                0 AS intrastat_additional_unit_to_stock_unit_conversion,
                'notManaged' AS lot_management,
                FALSE AS is_expiry_managed,
                a._id AS analytical_data,
                u._id AS _create_user,
                u._id AS _update_user,
                'notUsable' AS status,
                'service' AS type
            FROM
                ${helper.schemaName}.sys_tenant t
            LEFT JOIN
                ${helper.schemaName}.user u
                ON u._tenant_id = t.tenant_id
                AND u.email = '<EMAIL>'
            INNER JOIN (
                SELECT DISTINCT ON (_tenant_id) _tenant_id, _id
                FROM ${helper.schemaName}.analytical_data
                ORDER BY _tenant_id, _create_stamp ASC
                ) a
                ON a._tenant_id = t.tenant_id
            INNER JOIN (
                SELECT DISTINCT ON (_tenant_id) _tenant_id, _id
                FROM ${helper.schemaName}.unit_of_measure
                ORDER BY _tenant_id, _create_stamp ASC
                ) m
                ON m._tenant_id = t.tenant_id;
        `);
    },
});
