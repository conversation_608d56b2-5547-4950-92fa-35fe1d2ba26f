import { CustomSqlAction } from '@sage/xtrem-system';

export const purchaseRequisitionBaseDocumentItemLine = new CustomSqlAction({
    description: 'Move some line properties to base document item line',
    body: async helper => {
        await helper.executeSql(`

            INSERT INTO ${helper.schemaName}.base_document_item_line (_constructor, _sort_value,_id, _tenant_id,
                document, status, site, site_linked_address, item, item_description, stock_site, stock_site_linked_address,
                stock_unit, quantity_in_stock_unit, unit_to_stock_unit_conversion_factor, unit, quantity, internal_note,
                external_note, is_external_note, analytical_data, stored_dimensions, stored_attributes)
            SELECT
                'PurchaseRequisitionLine',
                line._sort_value,
                line._id,
                line._tenant_id,
                line.document,
                line.status::text::${helper.schemaName}.base_status_enum,
                line.site,
                addressBase._id,
                COALESCE(line.item, reqItem._id) AS item,
                line.requested_item_description,
                line.site,
                addressBase._id,
                line.stock_unit,
                line.quantity_in_stock_unit,
                line.unit_to_stock_unit_conversion_factor,
                line.unit,
                line.quantity,
                line.internal_note,
                '',
                FALSE,
                line.analytical_data,
                line.stored_dimensions,
                line.stored_attributes
            FROM ${helper.schemaName}.purchase_requisition_line AS line
            INNER JOIN ${helper.schemaName}.site AS site
                ON line.site = site._id
                AND line._tenant_id = site._tenant_id
            INNER JOIN ${helper.schemaName}.business_entity_address AS businessEntityAddress
                ON site.primary_address = businessEntityAddress._id
                AND site._tenant_id = businessEntityAddress._tenant_id
            INNER JOIN ${helper.schemaName}.address_base AS addressBase
                ON businessEntityAddress._id = addressBase._id
                AND businessEntityAddress._tenant_id = addressBase._tenant_id
                AND addressBase._constructor = 'BusinessEntityAddress'
            INNER JOIN ${helper.schemaName}.address AS address
                ON addressBase.address = address._id
                AND addressBase._tenant_id = address._tenant_id
            INNER JOIN ${helper.schemaName}.item AS reqItem
                ON line._tenant_id = reqItem._tenant_id
                AND reqItem.id = 'requisitionItem';
`);
    },
});
