import * as xtremCommunication from '@sage/xtrem-communication';
import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremPurchasing from '..';

export const sysNotificationHistoryExtension = new ActivityExtension({
    extends: xtremCommunication.activities.sysNotificationHistory,
    __filename,
    permissions: [],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => xtremPurchasing.nodes.PurchaseReceipt,
                    () => xtremPurchasing.nodes.PurchaseReturn,
                    () => xtremPurchasing.nodes.PurchaseInvoice,
                ],
            },
        ],
    },
});
