import { ActivityExtension } from '@sage/xtrem-core';
import { activities as xtremMasterDataActivities } from '@sage/xtrem-master-data';
import { commonPurchasingActivities } from '../functions/common';

const { businessEntity } = xtremMasterDataActivities;

export const businessEntityExtension = new ActivityExtension({
    extends: businessEntity,
    __filename,
    permissions: [],
    operationGrants: {
        read: [...commonPurchasingActivities],
        create: [...commonPurchasingActivities],
        update: [...commonPurchasingActivities],
        delete: [...commonPurchasingActivities],
    },
});
