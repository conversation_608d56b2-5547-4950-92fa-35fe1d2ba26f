import { withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { Account } from '@sage/xtrem-finance-data-api';
import type { BusinessEntity, Currency, Item, Supplier, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { setReferenceIfSingleValue } from '@sage/xtrem-master-data/build/lib/client-functions/get-default';
import { finance } from '@sage/xtrem-master-data/build/lib/menu-items/finance';
import { setMultiReferenceIfSingleValue } from '@sage/xtrem-master-data/lib/client-functions/get-default';
import type {
    GraphApi,
    PurchaseReceipt,
    UnbilledAccountPayableInputSet,
    UnbilledAccountPayableResultLine,
    UnbilledAccountPayableStatus,
} from '@sage/xtrem-purchasing-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColor from '../client-functions/pill-color';
import { getQuantityScale } from '../client-functions/unbilled-account-payable-inquiry';

@ui.decorators.page<UnbilledAccountPayableInquiry, UnbilledAccountPayableInputSet>({
    title: 'Unbilled accounts payable',
    module: 'finance',
    mode: 'tabs',
    menuItem: finance,
    priority: 500,
    skipDirtyCheck: true,
    node: '@sage/xtrem-purchasing/UnbilledAccountPayableInputSet',
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return [this.runUnbilledAccountPayableInquiry];
    },
    headerDropDownActions() {
        return [this.$standardDeleteAction];
    },
    async defaultEntry() {
        // As there is only one record per user try to load this record.
        //
        // NOTE (for local Cucumber script test on local development instances): Please make sure that the user
        // e-mail address in the top field is set to the e-mail address of the user configured in xtrem-config.yml!
        const inputSet = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/UnbilledAccountPayableInputSet')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true },
                        { filter: { user: { email: this.$.username } }, first: 1 },
                    ),
                )
                .execute(),
        );

        // If there is such record open it. Otherwise open work with an empty page in creation mode.
        return inputSet.at(0)?._id || '$new';
    },
    async onLoad() {
        if (!this.$.recordId) {
            // In case we have no record for this user pre-set the calculation user field.
            await this.$.fetchDefaults(['user']);
        }
        if (!this.status.value || this.status.value === 'draft') {
            this.asOfDate.value = DateValue.today().addMonths(-1).endOfMonth().toString();
        }
        await setReferenceIfSingleValue([this.company]);
        await setMultiReferenceIfSingleValue([this.sites]);
    },
    navigationPanel: undefined,
    headerSection() {
        return this.mainSection;
    },
})
export class UnbilledAccountPayableInquiry extends ui.Page<GraphApi, UnbilledAccountPayableInputSet> {
    @ui.decorators.pageAction<UnbilledAccountPayableInquiry>({
        title: 'Run',
        isDisabled() {
            return this.status.value === 'inProgress';
        },
        async onClick() {
            this.$.loader.isHidden = false;
            this.status.value = 'inProgress';
            if (await this.saveInquiry()) {
                await this.runInquiry();
                await this.$.router.refresh();
            } else {
                this.status.value = 'error';
            }
            this.$.loader.isHidden = true;
        },
        buttonType: 'primary',
    })
    runUnbilledAccountPayableInquiry: ui.PageAction;

    getSerializedValues() {
        const { values } = this.$;
        values.sites = values.sites ?? [];
        return values;
    }

    async saveInquiry() {
        const validation = await this.$.page.validate();
        if (validation.length !== 0) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
            return false;
        }
        await this.$standardSaveAction.execute(true);
        // To be sure that the user is refreshed before we continue
        return true;
    }

    async runInquiry() {
        if (!this.user.value?._id) {
            return;
        }

        // Notify the user that a calculation request is on the way.
        this.$.showToast(
            ui.localize(
                '@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__notification_request_sent',
                'Unbilled accounts payable request sent.',
            ),
            { type: 'success' },
        );

        // Request calculation in an asynchronous mutation.
        const isFinished = await this.$.graph
            .node('@sage/xtrem-purchasing/UnbilledAccountPayableInputSet')
            .asyncOperations.unbilledAccountPayableInquiry.runToCompletion(true, {
                userId: this.user.value._id,
            })
            .execute();
        if (isFinished) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__unbilled_account_payable_inquiry__notification_inquiry_finished',
                    'Unbilled accounts payable finished.',
                ),
                { type: 'success' },
            );
        }
    }

    @ui.decorators.section<UnbilledAccountPayableInquiry>({ isOpen: true, isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<UnbilledAccountPayableInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.referenceField<UnbilledAccountPayableInquiry, User>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'User',
        isReadOnly: true,
        width: 'small',
        fetchesDefaults: true,
        valueField: 'displayName',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
    })
    user: ui.fields.Reference<User>;

    @ui.decorators.referenceField<UnbilledAccountPayableInquiry, Company>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Company',
        node: '@sage/xtrem-system/Company',
        tunnelPage: '@sage/xtrem-master-data/Company',
        lookupDialogTitle: 'Select company',
        minLookupCharacters: 0,
        valueField: 'name',
        helperTextField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
        ],
        filter: { isActive: { _eq: true } },
        placeholder: 'Select...',
        width: 'small',
        onChange() {
            this.sites.value = [];
        },
    })
    company: ui.fields.Reference<Company>;

    @ui.decorators.multiReferenceField<UnbilledAccountPayableInquiry, Site>({
        node: '@sage/xtrem-system/Site',
        title: 'Site',
        placeholder: 'Select site',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select site',
        valueField: { businessEntity: { name: true } },
        helperTextField: { businessEntity: { id: true } },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
        ],
        parent() {
            return this.criteriaBlock;
        },
        filter() {
            return {
                isActive: { _eq: true },
                isInventory: { _eq: true },
                ...(this.company.value ? { legalCompany: { id: { _eq: this.company.value?.id ?? '' } } } : {}),
            };
        },
    })
    sites: ui.fields.MultiReference<Site>;

    @ui.decorators.referenceField<UnbilledAccountPayableInquiry, Supplier>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'From supplier',
        lookupDialogTitle: 'Select from supplier',
        orderBy: { businessEntity: { name: +1 } },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
            ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { taxIdNumber: true } } }),
            ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { country: { name: true } } } }),
            ui.nestedFields.technical<UnbilledAccountPayableInquiry, Supplier, BusinessEntity>({
                node: '@sage/xtrem-master-data/BusinessEntity',
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<UnbilledAccountPayableInquiry, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    fromSupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<UnbilledAccountPayableInquiry, Supplier>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'To supplier',
        lookupDialogTitle: 'Select to supplier',
        orderBy: { businessEntity: { name: +1 } },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
            ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { taxIdNumber: true } } }),
            ui.nestedFields.text({ title: 'Country', bind: { businessEntity: { country: { name: true } } } }),
            ui.nestedFields.technical<UnbilledAccountPayableInquiry, Supplier, BusinessEntity>({
                node: '@sage/xtrem-master-data/BusinessEntity',
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<UnbilledAccountPayableInquiry, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    toSupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.dateField<UnbilledAccountPayableInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'As of date',
        isMandatory: true,
    })
    asOfDate: ui.fields.Date;

    @ui.decorators.relativeDateField<UnbilledAccountPayableInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Last run date',
    })
    executionDate: ui.fields.RelativeDate;

    @ui.decorators.labelField<UnbilledAccountPayableInquiry>({
        title: 'Status',
        optionType: '@sage/xtrem-stock/UnbilledAccountPayableStatus',
        parent() {
            return this.criteriaBlock;
        },
        style() {
            return PillColor.getLabelColorByStatus('UnbilledAccountPayableStatus', this.status.value);
        },
    })
    status: ui.fields.Label<UnbilledAccountPayableStatus>;

    @ui.decorators.section({ title: 'Results' }) resultsSection: ui.containers.Section;

    // Note: Infinite scrolling is automatically enabled to a table field if you apply the following:
    //       - A section (see resultsSection) that only contains this table field without any block inbetween.
    //       - The page mode decorator is set to "mode: 'tabs'".
    //       - To prevent having tabs on the screen we have to set a header section.
    @ui.decorators.tableField<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine>({
        node: '@sage/xtrem-purchasing/UnbilledAccountPayableResultLine',
        canResizeColumns: true,
        canSelect: false,
        isHelperTextHidden: true,
        canExport: true,
        title: 'Results',
        isReadOnly: true,
        orderBy: {
            financialSite: { legalCompany: { id: 1 }, id: 1 },
            supplier: { businessEntity: { id: 1 } },
            stockSite: { id: 1 },
            receiptNumber: 1,
        },
        parent() {
            return this.resultsSection;
        },
        columns: [
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Supplier>({
                title: 'Bill-by supplier',
                bind: 'billBySupplier',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
            }),
            ui.nestedFields.text({
                title: 'Bill-by supplier ID',
                bind: { billBySupplier: { businessEntity: { id: true } } },
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Currency>({
                bind: 'currency',
                title: 'Currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ title: 'Currency ID', bind: { currency: { id: true } }, isHiddenOnMainField: true }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Company>({
                title: 'Company',
                bind: 'company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ title: 'Company ID', bind: { company: { id: true } } }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Site>({
                title: 'Financial site',
                bind: 'financialSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({ title: 'Financial site ID', bind: { financialSite: { id: true } } }),
            ui.nestedFields.numeric({
                bind: 'invoiceReceivableQuantity',
                title: 'Unbilled quantity',
                scale: (_rowId, rowData) => getQuantityScale(rowData),
                postfix(_rowId, rowData) {
                    return rowData?.purchaseUnit?.symbol ?? '';
                },
                groupAggregationMethod: 'sum',
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, UnitOfMeasure>({
                bind: 'purchaseUnit',
                title: 'Purchase unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net price',
                prefix: (_value, rowData) => rowData?.currency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.currency?.decimalDigits ?? 2,
            }),
            ui.nestedFields.numeric({
                bind: 'invoiceReceivableAmount',
                title: 'Unbilled amount',
                prefix: (_value, rowData) => rowData?.currency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.currency?.decimalDigits ?? 2,
            }),
            ui.nestedFields.numeric({
                bind: 'invoiceReceivableAmountInCompanyCurrency',
                title: 'Unbilled in company currency',
                prefix: (_value, rowData) => rowData?.companyCurrency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.companyCurrency?.decimalDigits ?? 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.numeric({
                bind: 'invoiceReceivableAmountInCompanyCurrencyAtAsOfDate',
                title: 'Unbilled in company currency per the As of date',
                prefix: (_value, rowData) => rowData?.companyCurrency?.symbol ?? '',
                scale: (_value, rowData) => rowData?.companyCurrency?.decimalDigits ?? 2,
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Currency>({
                bind: 'companyCurrency',
                title: 'Company currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text({
                title: 'Company currency ID',
                bind: { companyCurrency: { id: true } },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.text<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine>({
                bind: { item: { name: true } },
                title: 'Item',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Item>({
                bind: 'item',
                title: 'Item ID',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'id',
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Site>({
                title: 'Stock site',
                bind: 'stockSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
            ui.nestedFields.text<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine>({
                bind: { stockSite: { id: true } },
                title: 'Stock site ID',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Account>({
                title: 'Unbilled accounts payable account',
                bind: 'account',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'id',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Account>({
                title: 'Account name',
                bind: 'account',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, Account>({
                title: 'Account item',
                bind: 'accountItem',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'name',
            }),
            ui.nestedFields.text({ bind: { accountItem: { id: true } }, title: 'Account item ID' }),
            ui.nestedFields.reference<UnbilledAccountPayableInquiry, UnbilledAccountPayableResultLine, PurchaseReceipt>(
                {
                    title: 'Receipt number',
                    bind: 'receiptInternalId',
                    node: '@sage/xtrem-purchasing/PurchaseReceipt',
                    tunnelPage: '@sage/xtrem-purchasing/PurchaseReceipt',
                },
            ),
            ui.nestedFields.date({ bind: 'documentDate', title: 'Document date' }),
            ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Received quantity',
                scale: (_rowId, rowData) => getQuantityScale(rowData),
                postfix(_rowId, rowData) {
                    return rowData?.purchaseUnit?.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'returnedQuantity',
                title: 'Returned quantity',
                scale: (_rowId, rowData) => getQuantityScale(rowData),
                postfix(_rowId, rowData) {
                    return rowData?.purchaseUnit?.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'invoicedQuantity',
                title: 'Invoiced quantity',
                scale: (_rowId, rowData) => getQuantityScale(rowData),
                postfix(_rowId, rowData) {
                    return rowData?.purchaseUnit?.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'creditedQuantity',
                title: 'Credited quantity',
                scale: (_rowId, rowData) => getQuantityScale(rowData),
                postfix(_rowId, rowData) {
                    return rowData?.purchaseUnit?.symbol ?? '';
                },
            }),
        ],
    })
    lines: ui.fields.Table<UnbilledAccountPayableResultLine>;
}
