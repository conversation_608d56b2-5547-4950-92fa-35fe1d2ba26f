import { formatError } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi, PurchaseDocumentApprovalStatus, PurchaseRequisition } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<PurchaseRequisitionApproval, PurchaseRequisition>({
    title: 'Purchase requisition approval',
    node: '@sage/xtrem-purchasing/PurchaseRequisition',
    mode: 'default',
    async onLoad() {
        this.$.setPageClean();
        if (this.$.queryParameters._id && this.$.queryParameters.action) {
            await this.updatePurchaseRequisitionStatus(this.$.queryParameters.action as PurchaseDocumentApprovalStatus);
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseRequisition`, {
                _id: this.$.queryParameters._id,
            });
            return;
        }
        this.goto();
    },
    onError(error) {
        this.$.showToast(formatError(this, error), { timeout: 5000, type: 'error' });
        this.goto();
    },
})
export class PurchaseRequisitionApproval extends ui.Page<GraphApi> {
    goto() {
        if (this.$.recordId) {
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseRequisition`, { _id: this.$.recordId });
        }
        this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseRequisition`);
    }

    async updatePurchaseRequisitionStatus(newStatus: PurchaseDocumentApprovalStatus) {
        if (!this.$.recordId) return;

        if (this.approvalStatus.value !== 'pendingApproval') {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__purchase_requisition_status_update_fail',
                    'Update Purchase requisition status failed - Purchase requisition status is not "pending approval".',
                ),
                { timeout: 5000, type: 'error' },
            );
            return;
        }
        const isApproved = await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisition')
            .mutations.approve(true, { document: this.$.recordId, approve: newStatus === 'approved' })
            .execute();

        if (isApproved) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__purchase_requisition_status_update_success',
                    'Update Purchase requisition status done. You will be redirected to the Purchase requisition page.',
                ),
                { timeout: 5000, type: 'info' },
            );
        }
    }

    @ui.decorators.section<PurchaseRequisitionApproval>({ title: 'General', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.textField<PurchaseRequisitionApproval>({}) number: ui.fields.Text;

    @ui.decorators.labelField<PurchaseRequisitionApproval>({}) approvalStatus: ui.fields.Label;
}
