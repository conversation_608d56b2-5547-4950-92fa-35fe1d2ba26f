import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { Decimal } from '@sage/xtrem-decimal';
import type { FinanceTransactionBinding } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';
import type {
    Address,
    BusinessEntity,
    Currency,
    Item,
    ReasonCode,
    Supplier,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    convertFromTo,
    getCompanyPriceScale,
    getPurchaseUnit,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import { scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import { stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import type {
    GraphApi,
    PurchaseDocumentApprovalStatus,
    PurchaseInvoice,
    PurchaseInvoiceLine,
    PurchaseOrderLineToPurchaseReceiptLine,
    PurchaseReceiptLine,
    PurchaseReceiptLineToPurchaseReturnLine,
    PurchaseReturnLine,
    PurchaseReturnLineBinding,
    PurchaseReturn as PurchaseReturnNode,
    PurchaseReturnShippingStatus,
} from '@sage/xtrem-purchasing-api';
import type { StockAllocationStatus, StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDetailHelper from '@sage/xtrem-stock-data/build/lib/client-functions/stock-details-helper';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableFieldActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { confirmDialogWithAcceptButtonText, getPurchasePrice, setOverwriteNote } from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-purchase-return';
import * as PillColorPurchase from '../client-functions/pill-color';
import * as actionFunctions from '../client-functions/purchase-return-actions-functions';
import { addLineFromReceipt } from '../client-functions/return-from-receipt';
import { isExchangeRateHidden, laterThanToday } from '../client-functions/shared/page-functions';
import {
    getPurchaseReturnStepSequence,
    getPurchaseReturnStepSequenceValues,
} from '../client-functions/step-sequence-status';

@ui.decorators.page<PurchaseReturn, PurchaseReturnNode>({
    title: 'Purchase return',
    objectTypeSingular: 'Purchase return',
    objectTypePlural: 'Purchase returns',
    idField() {
        return this.number;
    },
    menuItem: purchasing,
    node: '@sage/xtrem-purchasing/PurchaseReturn',
    module: 'purchasing',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 600,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.customSave,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.requestApproval,
                this.approve,
                this.reject,
                this.post,
                this.repost,
                this.close,
                this.confirm,
            ],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            quickActions: [],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.stockJournal,
                this.stockPostingError,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this._manageDisplayApplicativePageActions(isDirty);
        this.returnStepSequence.statuses = getPurchaseReturnStepSequenceValues(
            !!this.isApprovalManaged.value,
            this.stockTransactionStatus.value as StockDocumentTransactionStatus,
            this.allocationStatus.value as StockAllocationStatus,
            this.approvalStatus.value as PurchaseDocumentApprovalStatus,
        );
    },
    onLoad() {
        this.initPage();
        this.initPosting();
        this.returnStepSequence.statuses = getPurchaseReturnStepSequenceValues(
            !!this.isApprovalManaged.value,
            this.stockTransactionStatus.value as StockDocumentTransactionStatus,
            this.allocationStatus.value as StockAllocationStatus,
            this.approvalStatus.value as PurchaseDocumentApprovalStatus,
        );
        this._manageDisplayApplicativePageActions(false);
        this.manageDisplayButtonGoToSysNotificationPageAction();
        this.$.setPageClean();
        this.isRepost = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return utils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { returnRequestDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                isMandatory: true,
                page: '@sage/xtrem-purchasing/PurchaseReturn',
                queryParameters(_value, rowData) {
                    return { _id: rowData?._id ?? '' };
                },
            }),
            line2: ui.nestedFields.reference<PurchaseReturn, PurchaseReturnNode, Supplier>({
                bind: 'supplier',
                title: 'Supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            id: ui.nestedFields.text({
                bind: { supplier: { id: true } },
                title: 'Supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({
                bind: 'returnRequestDate',
                title: 'Return request date',
                isMandatory: true,
            }),
            line3: ui.nestedFields.reference<PurchaseReturn, PurchaseReturnNode, Site>({
                title: 'Return site',
                bind: 'returnSite',
                node: '@sage/xtrem-system/Site',
                tunnelPage: undefined,
                valueField: 'name',
                columns: [ui.nestedFields.technical({ bind: 'isPurchaseReturnApprovalManaged' })],
            }),
            line_4: ui.nestedFields.numeric<PurchaseReturn, PurchaseReturnNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseReturnDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line_5: ui.nestedFields.label({
                title: 'Shipping status',
                bind: 'shippingStatus',
                optionType: '@sage/xtrem-purchasing/PurchaseReturnShippingStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseReturnShippingStatus', rowData?.shippingStatus),
            }),
            line6: ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockAllocationStatus', rowData?.allocationStatus),
            }),
            line7: ui.nestedFields.checkbox<PurchaseReturn, PurchaseReturnNode>({
                title: 'Return items to supplier',
                bind: 'returnItems',
                isHiddenOnMainField: true,
            }),
            line8: ui.nestedFields.reference<PurchaseReturn, PurchaseReturnNode>({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                ],
            }),
            line9: ui.nestedFields.text({
                bind: 'supplierReturnReference',
                title: 'Supplier reference',
                isHiddenOnMainField: true,
            }),
            line10: ui.nestedFields.technical({ bind: 'status' }),
            line11: ui.nestedFields.technical({ bind: 'approvalStatus' }),
            line12: ui.nestedFields.technical({ bind: 'stockTransactionStatus' }),
            isApprovalManaged: ui.nestedFields.technical({ bind: 'isApprovalManaged' }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _nin: ['closed', 'rejected', 'returned'] } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
            { title: 'Pending approval', graphQLFilter: { displayStatus: { _eq: 'pendingApproval' } } },
            { title: 'Approved', graphQLFilter: { displayStatus: { _eq: 'approved' } } },
            { title: 'Confirmed', graphQLFilter: { displayStatus: { _eq: 'confirmed' } } },
            { title: 'Rejected', graphQLFilter: { displayStatus: { _eq: 'rejected' } } },
            { title: 'Tax calculation failed', graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } } },
            { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
            { title: 'Error', graphQLFilter: { displayStatus: { _eq: 'error' } } },
            { title: 'Returned', graphQLFilter: { displayStatus: { _eq: 'returned' } } },
            { title: 'Closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
        ],
        dropdownActions: [
            {
                icon: 'none',
                title: 'Post stock',
                refreshesMainList: 'record',
                async onClick(rowId: string, rowItem: ui.PartialNodeWithId<PurchaseReturnNode>) {
                    await actionFunctions.post({
                        purchaseReturnPage: this,
                        recordNumber: rowItem.number ?? '',
                        recordId: rowId,
                    });
                    await this.$.refreshNavigationPanel();
                },

                isHidden(rowId, rowItem: ui.PartialNodeWithId<PurchaseReturnNode>) {
                    return (
                        displayButtons.isHiddenButtonPostAction({
                            parameters: {
                                status: rowItem.status,
                                approvalStatus: rowItem.approvalStatus,
                                stockTransactionStatus: rowItem.stockTransactionStatus,
                                allocationStatus: rowItem.allocationStatus,
                                returnItems: rowItem.returnItems,
                            },
                            recordId: rowId,
                            isDirty: false,
                        }) ?? false
                    );
                },
            },
            {
                title: 'Close return',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseReturnNode>) {
                    if (rowItem.number && recordId !== '') {
                        await actionFunctions.close({
                            purchaseReturnPage: this,
                            recordId,
                            recordNumber: rowItem.number,
                        });
                    }
                },
                isHidden(recordId: string, rowItem) {
                    return displayButtons.isHiddenButtonCloseAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Submit for approval',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string) {
                    if (recordId) {
                        await actionFunctions.submitForApproval({ purchaseReturnPage: this, recordId });
                    }
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonRequestApprovalAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            isApprovalManaged: rowItem.isApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            {
                title: 'Confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.confirmAction({
                            purchaseReturnPage: this,
                            recordNumber: rowItem.number,
                            isConfirmed: true,
                            recordId,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonConfirmAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            isApprovalManaged: rowItem.isApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Approve',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.approveAction({
                            isCalledFromRecordPage: false,
                            purchaseReturnPage: this,
                            recordNumber: rowItem.number,
                            isApproved: true,
                            recordId,
                        });
                    }
                },
                isHidden: (recordId: string, rowItem) =>
                    displayButtons.isHiddenButtonApproveAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            {
                title: 'Reject',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.rejectAction({
                            isCalledFromRecordPage: false,
                            purchaseReturnPage: this,
                            recordNumber: rowItem.number,
                            isRejected: true,
                            recordId,
                        });
                    }
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonRejectAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                refreshesMainList: 'list',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<PurchaseReturnNode>) {
                    if (this.status.value !== 'closed' || this.isRepost) {
                        await actionFunctions.setDimensions({
                            purchaseReturnPage: this,
                            recordNumber: rowItem.number ?? '',
                            status: rowItem.status ?? null,
                            site: rowItem.returnSite,
                            supplier: rowItem.supplier,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return utils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseReturnNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-purchasing/PurchaseReturn',
                    });
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class PurchaseReturn
    extends ui.Page<GraphApi, PurchaseReturnNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    isRepost: boolean;

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.customSave,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.requestApproval,
                this.approve,
                this.reject,
                this.post,
                this.repost,
                this.close,
                this.confirm,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.$standardSaveAction.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonRequestApprovalAction(isDirty);
        this.manageDisplayButtonApproveAction(isDirty);
        this.manageDisplayButtonConfirmAction(isDirty);
        this.manageDisplayButtonRejectAction(isDirty);
        // this.manageDisplayButtonRequestChangesAction(isDirty); // intentionally left deactivated for the moment
        this.manageDisplayButtonCloseAction(isDirty);
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        // other header actions
        this.manageDisplayButtonSelectFromPurchaseReceiptLinesAction();
    }

    manageDisplayButtonConfirmAction(isDirty: boolean) {
        this.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                isApprovalManaged: this.isApprovalManaged.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonApproveAction(isDirty: boolean) {
        this.approve.isHidden = displayButtons.isHiddenButtonApproveAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRejectAction(isDirty: boolean) {
        this.reject.isHidden = displayButtons.isHiddenButtonRejectAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRequestApprovalAction(isDirty: boolean) {
        this.requestApproval.isHidden = displayButtons.isHiddenButtonRequestApprovalAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                isApprovalManaged: this.isApprovalManaged.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCloseAction(isDirty: boolean) {
        this.close.isHidden = displayButtons.isHiddenButtonCloseAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                stockTransactionStatus: this.stockTransactionStatus.value,
                allocationStatus: this.allocationStatus.value,
                returnItems: this.returnItems.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonSelectFromPurchaseReceiptLinesAction() {
        this.selectFromPurchaseReceiptsLookup.isHidden =
            displayButtons.isHiddenButtonSelectFromPurchaseReceiptLinesAction({
                parameters: {
                    returnSite: this.returnSite.value,
                    supplier: this.businessRelation.value,
                    status: this.status.value,
                    approvalStatus: this.approvalStatus.value,
                },
            });
    }

    @ui.decorators.pageAction<PurchaseReturn>({
        icon: 'search',
        title: 'Add lines from receipts',
        isHidden: true,
        async onClick() {
            await addLineFromReceipt(this);
        },
    })
    selectFromPurchaseReceiptsLookup: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Submit for approval',
        isHidden: true,
        async onClick() {
            if (this.$.recordId) {
                await actionFunctions.submitForApproval({ purchaseReturnPage: this, recordId: this.$.recordId });
                await this.$.refreshNavigationPanel();
                await this.$.router.refresh();
            }
        },
    })
    requestApproval: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Confirm',
        isHidden: true,
        isTransient: true,
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.confirmAction({
                    purchaseReturnPage: this,
                    recordNumber: this.number.value,
                    isConfirmed: true,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Approve',
        isHidden: true,
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.approveAction({
                    isCalledFromRecordPage: true,
                    purchaseReturnPage: this,
                    recordNumber: this.number.value,
                    isApproved: true,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    approve: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Reject',
        isHidden: true,
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.rejectAction({
                    isCalledFromRecordPage: true,
                    purchaseReturnPage: this,
                    recordNumber: this.number.value,
                    isRejected: true,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    reject: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Post stock',
        isHidden: true,
        async onClick() {
            this.$.loader.isHidden = true;
            if (this.$.recordId && this.number.value) {
                await actionFunctions.post({
                    purchaseReturnPage: this,
                    recordId: this.$.recordId,
                    recordNumber: this.number.value,
                });
            }
            await this.$.refreshNavigationPanel();
            await this.$.router.refresh();
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            const documentLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReturn')
                .mutations.repost(
                    { wasSuccessful: true, message: true },
                    { purchaseReturn: this.$.recordId ?? '', documentLines },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `**${ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_return__repost_errors',
                        'Errors occurred while reposting:',
                    )}**\n${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }
            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Close return',
        isHidden: true,
        async onClick() {
            let confirmationContent;
            if (this.lines.value.every(line => ['notAllocated', 'notManaged'].includes(line.allocationStatus))) {
                confirmationContent = ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_1',
                    'You are about the change the status of this return to closed. You cannot undo this change.',
                );
            } else {
                confirmationContent = ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_content_2',
                    'You are about to change the status of this return to closed and reverse all stock allocations. You cannot undo this change.',
                );
            }
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_return__close_order_dialog_title',
                        'Confirm status change',
                    ),
                    confirmationContent,
                    ui.localize('@sage/xtrem-purchasing/closeReturn____title', 'Close return'),
                )
            ) {
                this.$.loader.isHidden = false;
                this.status.value = 'closed';
                this.lines.value.forEach(line => {
                    if (line.status !== 'closed') {
                        line.status = 'closed';
                        this.lines.addOrUpdateRecordValue(line);
                    }
                });
                await this.$standardSaveAction.execute();
                await this.$.refreshNavigationPanel();
                await this.$.router.refresh();
                this.$.loader.isHidden = true;
            }
        },
    })
    close: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Save',
        access: { bind: '$update' },
        isHidden: true,
        async onClick() {
            if (!this.$.recordId) {
                this.isOverwriteNote.value = await setOverwriteNote(this, {
                    linesFromSingleDocument: this.liensFromSingleReceipt(),
                    headerNotesChanged: this.headerNotesChanged(),
                    lineNotesChanged: this.lineNotesChanged(),
                    isTransferHeaderNote: this.receiptIsTransferHeaderNote(),
                    isTransferLineNote: this.receiptsIsTransferLineNote(),
                });
            }
            await this.$standardSaveAction.execute(true);
        },
    })
    customSave: ui.PageAction;

    updateAllocationStatus() {
        const linesStockManaged = this.lines.value.filter((line: any) => line.item.isStockManaged);
        if (linesStockManaged.length > 0) {
            if (linesStockManaged.every(line => line.allocationStatus === 'notAllocated')) {
                this.allocationStatus.value = 'notAllocated';
            } else if (linesStockManaged.every(line => line.allocationStatus === 'allocated')) {
                this.allocationStatus.value = 'allocated';
            } else this.allocationStatus.value = 'partiallyAllocated';
        } else {
            this.allocationStatus.value = 'notManaged';
        }
    }

    getSerializedValues() {
        const { $detailPanel, ...values } = this.$.values;
        delete values.totalAmountExcludingTax;
        delete values.totalAmountExcludingTaxInCompanyCurrency;
        // delete values.totalAmountIncludingTax;
        // TODO: Fix problem with addresses as not all are being saved
        delete values.supplierAddress;
        delete values.returnToAddress;

        // TODO: labels value are not updated in this.$.value
        values.status = this.status.value;
        values.approvalStatus = this.approvalStatus.value;
        values.shippingStatus = this.shippingStatus.value;
        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }
        this.getLineValues(values.lines);
        return values;
    }

    // eslint-disable-next-line class-methods-use-this
    getLineValues = (lines: ui.PartialCollectionValue<PurchaseReturnLineBinding>[]) => {
        lines?.forEach(line => {
            delete line.currency;
            delete line.amountExcludingTax;
            delete line.amountExcludingTaxInCompanyCurrency;
            // TODO delete line.totalAmountExcludingTax
            delete line.site;
            delete line.quantityInStockUnit;
            delete line.origin;
            if (line._id && +line._id > 0) {
                delete line.unitToStockUnitConversionFactor;
            }
        });
    };

    @ui.decorators.switchField<PurchaseReturn>({})
    isApprovalManaged: ui.fields.Switch;

    @ui.decorators.section<PurchaseReturn>({ title: 'General', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<PurchaseReturn>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReturn>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseReturn>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<PurchaseReturn>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.section<PurchaseReturn>({
        title: 'Information',
        isTitleHidden: true,
    })
    informationSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReturn>({
        parent() {
            return this.informationSection;
        },
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<PurchaseReturn>({
        width: 'small',
        isTransient: true,
        isFullWidth: true,
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return getPurchaseReturnStepSequence(
                !!this.isApprovalManaged.value,
                this.approvalStatus.value as PurchaseDocumentApprovalStatus,
            );
        },
    })
    returnStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<PurchaseReturn, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Return site',
        lookupDialogTitle: 'Select return site',
        minLookupCharacters: 0,
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isPurchaseReturnApprovalManaged' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseReturn, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseReturn, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'taxEngine', title: 'Tax engine' }),
                    ui.nestedFields.reference<PurchaseReturn, Company, Legislation>({
                        bind: 'legislation',
                    }),
                    ui.nestedFields.reference<PurchaseReturn, Company, Currency>({
                        title: 'Currency',
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol' }),
                            ui.nestedFields.text({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'priceScale' }),
                    ui.nestedFields.technical({ bind: 'doStockPosting' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
        ],
        isReadOnly() {
            return !!this.$.recordId;
        },
        filter: { isActive: { _eq: true }, isInventory: { _eq: true } },
        onChange() {
            this.manageDisplayButtonSelectFromPurchaseReceiptLinesAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.returnSite.value,
                this.businessRelation.value,
            );
        },
    })
    returnSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PurchaseReturn, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Stock site',
        isHidden: true,
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PurchaseReturn, Supplier>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Supplier',
        lookupDialogTitle: 'Select supplier',
        minLookupCharacters: 3,
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<PurchaseReturn, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical<PurchaseReturn, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseReturn, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'rounding' }),
                        ],
                    }),
                ],
            }),
        ],

        async onChange() {
            if (this.businessRelation.value?.businessEntity?.currency) {
                this.currency.value = this.businessRelation.value.businessEntity.currency;
            }
            await this.$.fetchDefaults(['supplierAddress', 'returnToAddress']);
            this.manageDisplayButtonSelectFromPurchaseReceiptLinesAction();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.returnSite.value,
                this.businessRelation.value,
            );
        },
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    businessRelation: ui.fields.Reference<Supplier>;

    @ui.decorators.textField<PurchaseReturn>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<PurchaseReturn>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Return request date',
        fetchesDefaults: true,
        validation(val) {
            return laterThanToday(
                val,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_return__return_date_cannot_be_future',
                    'The return request date cannot be later than today.',
                ),
            );
        },
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    returnRequestDate: ui.fields.Date;

    @ui.decorators.referenceField<PurchaseReturn, Currency>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Company currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isMandatory: true,
        fetchesDefaults: true,
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
            ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter() {
            return { isActive: true };
        },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        onChange() {
            this.applyCompanyCurrency();
            this._applyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
    })
    companyCurrency: ui.fields.Reference<Currency>;

    private _applyCurrency() {
        this.totalAmountExcludingTax.unit = this.currency.value;
    }

    private applyCompanyCurrency() {
        this.totalAmountExcludingTaxInCompanyCurrency.unit = this.returnSite.value?.financialCurrency;
    }

    @ui.decorators.dateField<PurchaseReturn>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden() {
            return true;
        },
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseReturn>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    // Need this property on the main tab as we depend on it
    // and it will not be populated when on another tab until that tab is visible.
    @ui.decorators.labelField<PurchaseReturn>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        bind: 'approvalStatus',
        title: 'Approval status',
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentApprovalStatus',
        isHidden: true,
    })
    approvalStatusHidden: ui.fields.Label;

    @ui.decorators.textField<PurchaseReturn>({
        parent() {
            return this.headerBlock;
        },
        title: 'Supplier reference',
        isReadOnly() {
            return ['approved', 'rejected'].includes(this.approvalStatus.value ?? '');
        },
    })
    supplierReturnReference: ui.fields.Text;

    @ui.decorators.labelField<PurchaseReturn>({
        parent() {
            return this.informationBlock;
        },
        title: 'Shipping status',
        optionType: '@sage/xtrem-purchasing/PurchaseReturnShippingStatus',
        style() {
            return PillColorPurchase.getLabelColorByStatus('PurchaseReturnShippingStatus', this.shippingStatus.value);
        },
    })
    shippingStatus: ui.fields.Label<PurchaseReturnShippingStatus>;

    @ui.decorators.labelField<PurchaseReturn>({
        parent() {
            return this.informationBlock;
        },
        title: 'Allocation status',
        optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
        style() {
            return PillColorStock.getLabelColorByStatus('StockAllocationStatus', this.allocationStatus.value);
        },
        isHidden() {
            return this.shippingStatus.value === 'shipped' && this.stockTransactionStatus.value === 'completed';
        },
    })
    allocationStatus: ui.fields.Label<StockAllocationStatus>;

    @ui.decorators.referenceField<PurchaseReturn, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Transaction currency',
        lookupDialogTitle: 'Select currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter() {
            return { isActive: true };
        },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCurrency();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.returnSite.value,
                this.businessRelation.value,
            );
        },
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<PurchaseReturn>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.returnSite.value, this.businessRelation.value);
        },
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.checkboxField<PurchaseReturn>({
        parent() {
            return this.informationBlock;
        },
        title: 'Return items to supplier',
        onChange() {
            if (
                !this.returnItems.value &&
                !['notAllocated', 'notManaged'].includes(this.allocationStatus.value ?? '')
            ) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_return__return_items__uncheck_return_items_with_allocations',
                        'Remove the allocations before disabling the item returns.',
                    ),
                );
            }
        },
    })
    returnItems: ui.fields.Checkbox;

    @ui.decorators.separatorField<PurchaseReturn>({
        parent() {
            return this.informationBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    addressSeparator: ui.fields.Separator;

    @ui.decorators.podField<PurchaseReturn, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Return to address',
        width: 'small',
        bind: 'returnToAddress',
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Return to address',
                isTitleHidden: true,
                width: 'large',
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical({ bind: 'addressLine1' }),
            ui.nestedFields.technical({ bind: 'addressLine2' }),
            ui.nestedFields.technical({ bind: 'city' }),
            ui.nestedFields.technical({ bind: 'region' }),
            ui.nestedFields.technical({ bind: 'postcode' }),
            ui.nestedFields.technical({
                bind: 'country',
                node: '@sage/xtrem-structure/Country',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
    })
    returnToAddress: ui.fields.Pod<Address>;

    @ui.decorators.podField<PurchaseReturn, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Supplier address',
        width: 'small',
        bind: 'supplierAddress',
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Supplier address',
                isTitleHidden: true,
                width: 'large',
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical({ bind: 'addressLine1' }),
            ui.nestedFields.technical({ bind: 'addressLine2' }),
            ui.nestedFields.technical({ bind: 'city' }),
            ui.nestedFields.technical({ bind: 'region' }),
            ui.nestedFields.technical({ bind: 'postcode' }),
            ui.nestedFields.reference({
                bind: 'country',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
                isHidden: true,
            }),
        ],
    })
    supplierAddress: ui.fields.Pod<Address>;

    @ui.decorators.labelField<PurchaseReturn>({
        parent() {
            return this.informationBlock;
        },
        title: 'Approval status',
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentApprovalStatus',
        isHidden: true,
    })
    approvalStatus: ui.fields.Label;

    @ui.decorators.section<PurchaseReturn>({
        title: 'Totals',
    })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReturn>({
        parent() {
            return this.totalsSection;
        },
        title: 'Calculated amounts',
        width: 'large',
        isTitleHidden: true,
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.section<PurchaseReturn>({
        title: 'Posting',
        isHidden() {
            return !this.returnSite.value?.legalCompany?.doStockPosting || !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReturn>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseReturn, FinanceTransactionBinding>({
        title: 'Posting',
        isTitleHidden: true,
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page: '@sage/xtrem-finance/JournalEntry',
                queryParameters(_value, rowData: FinanceTransactionBinding) {
                    return {
                        _id: rowData.targetDocumentSysId,
                        number: rowData.targetDocumentNumber,
                    };
                },
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                onClick(_value, rowData) {
                    if (rowData.financeIntegrationAppUrl) {
                        this.$.router.goToExternal(rowData.financeIntegrationAppUrl);
                    }
                },
                isHidden() {
                    return this.postingDetails.value.at(0)?.financeIntegrationApp !== 'intacct';
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
        ],
        onRowClick(_id: string, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table;

    @ui.decorators.textAreaField<PurchaseReturn>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<PurchaseReturn>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'purchaseReturn',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.numericField<PurchaseReturn>({
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        title: 'Excluding tax',
        isReadOnly: true,
        unit() {
            return this.currency?.value;
        },
        scale: null,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.block<PurchaseReturn>({
        parent() {
            return this.totalsSection;
        },
        title: 'Amounts in company currency',
        width: 'large',
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.returnSite.value, this.businessRelation.value);
        },
    })
    totalsSectionCompanyCurrencyDetailsBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseReturn>({
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        title: 'Excluding tax',
        unit() {
            return this.returnSite.value?.financialCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.section<PurchaseReturn>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<PurchaseReturn>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<PurchaseReturn>({
        parent() {
            return this.noteBlock;
        },
        width: 'large',
        isFullWidth: true,
        helperText: 'Notes display on internal documents.',
        title: 'Internal notes',
        capabilities: validCapabilities,
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseReturn>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to the supplier document',
        onChange() {
            this.externalNote.isDisabled = !(this.isExternalNote.value ?? false);
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<PurchaseReturn>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'closed';
        },
        helperText: 'Notes display on supplier documents.',
        title: 'Supplier notes',
        capabilities: validCapabilities,
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseReturn>({
        parent() {
            return this.noteBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseReturn>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat the document notes on new documents.',
        width: 'large',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseReturn>({
        parent() {
            return this.noteBlock;
        },
        title: 'Repeat all the line notes on new documents.',
        width: 'large',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.richTextField<PurchaseReturn>({
        width: 'large',
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseReturn>({
        title: 'Add notes to the supplier document',
        isFullWidth: true,
        isTransient: true,
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value ?? false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<PurchaseReturn>({
        isFullWidth: true,
        title: 'Supplier line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on supplier documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'closed';
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.labelField<PurchaseReturn>({
        title: 'Status',
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<PurchaseReturn>({
        title: 'Display status',
        optionType: '@sage/xtrem-purchasing/PurchaseReturnDisplayStatus',
        onClick() {}, // Intentionally left empty  remove once this is fixed https://jira.sage.com/browse/XT-97877
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label;

    @ui.decorators.labelField<PurchaseReturn>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        isHidden: true,
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
    })
    stockTransactionStatus: ui.fields.Label;

    @ui.decorators.countField<PurchaseReturn>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    lineCount: ui.fields.Count;

    @ui.decorators.aggregateField<PurchaseReturn>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountExcludingTax',
        aggregationMethod: 'sum',
        title: 'Total excluding tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    totalExcludingTaxValue: ui.fields.Aggregate;

    @ui.decorators.aggregateField<PurchaseReturn>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountIncludingTax',
        aggregationMethod: 'sum',
        title: 'Total including tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    totalIncludingTaxValue: ui.fields.Aggregate;

    @ui.decorators.tableField<PurchaseReturn, PurchaseReturnLineBinding>({
        title: 'Lines',
        isTitleHidden: true,
        isFullWidth: true,
        canSelect: false,
        pageSize: 5,
        node: '@sage/xtrem-purchasing/PurchaseReturnLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to the supplier document',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentLineOrigin',
                isReadOnly: true,
            }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseReturnLineBinding, Item>({
                title: 'Item',
                lookupDialogTitle: 'Select item',
                helperTextField: 'id',
                minLookupCharacters: 3,
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                fetchesDefaults: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.technical({ bind: 'isExpiryManaged' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.reference<PurchaseReturn, Item, UnitOfMeasure>({
                        title: 'Unit',
                        bind: 'stockUnit',
                        valueField: 'symbol',
                        isHidden: true,
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
                orderBy: { name: -1, stockUnit: { id: +1 } },
                isReadOnly(_rowId, rowData) {
                    return rowData?.origin !== 'direct'; // TODO: no jsonStockDetails on PurchaseReturnLine node, uncomment when added - || rowData.jsonStockDetails?.length;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    if (rowData.item) {
                        rowData.unit = await getPurchaseUnit(
                            this.$.graph,
                            rowData.item._id ?? '',
                            this.businessRelation?.value?._id ?? '',
                        );
                        const quantityToConvert = rowData.quantity ? +rowData.quantity : 1;

                        const conversion = await convertFromTo(
                            this.$.graph,
                            rowData.unit._id ?? '',
                            rowData.item?.stockUnit?._id ?? '',
                            quantityToConvert,
                            rowData.item._id,
                            this.businessRelation.value?._id ?? '',
                            '',
                            'purchase',
                            false,
                        );
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor.toString();
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = conversion.convertedQuantity.toString();
                        }

                        if (this.stockSite.value && this.businessRelation.value && rowData.item) {
                            const { storedAttributes, storedDimensions } =
                                await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                    page: this,
                                    _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                    dimensionDefinitionLevel: 'purchasingDirect',
                                    site: this.stockSite.value,
                                    supplier: this.businessRelation.value,
                                    item: rowData.item,
                                });
                            rowData.storedAttributes = storedAttributes;
                            rowData.storedDimensions = storedDimensions;
                        }

                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                    await this._setPriceOrigin(rowData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                isHiddenOnMainField: true,
                width: 'large',
                isReadOnly(_rowId, rowData) {
                    return rowData?.status && !['draft', 'pending'].includes(rowData?.status);
                },
            }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseReturnLineBinding, UnitOfMeasure>({
                title: 'Purchase unit',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 0,
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isMandatory: true,
                fetchesDefaults: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isReadOnly(_rowId, rowData) {
                    return rowData?.origin !== 'direct' || !!rowData.purchaseReceiptLine;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    const quantityToConvert = rowData.quantity ? rowData.quantity : '1';

                    const conversion = await convertFromTo(
                        this.$.graph,
                        rowData?.unit?._id ?? '',
                        rowData?.item?.stockUnit?._id ?? '',
                        parseFloat(quantityToConvert),
                        rowData?.item?._id ?? '',
                        this.businessRelation?.value?._id ?? '',
                        '',
                        'purchase',
                        false,
                    );
                    rowData.stockUnit = rowData?.item?.stockUnit;
                    rowData.unitToStockUnitConversionFactor = conversion.conversionFactor.toString();
                    if (rowData.quantity) {
                        rowData.quantityInStockUnit = conversion.convertedQuantity.toString();
                    }
                    if (!rowData.purchaseReceiptLine) await this._setPriceOrigin(rowData);

                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    if (rowData.purchaseReceiptLine && rowData.quantity && rowData.unitToStockUnitConversionFactor) {
                        rowData.purchaseReceiptLine.returnedQuantity = rowData.quantity;
                        rowData.quantityInStockUnit = (
                            +rowData.quantity * +rowData.unitToStockUnitConversionFactor
                        ).toString();
                        this._calculatePrices(rowData);
                        this.lines.addOrUpdateRecordValue(rowData);
                        this._updatePurchaseReceiptLine(rowData.purchaseReceiptLine);
                    }
                },
            }),
            ui.nestedFields.numeric({
                bind: 'unitToStockUnitConversionFactor',
                title: 'Stock unit conversion factor',
                isReadOnly: true,
                width: 'large',
                isHiddenOnMainField: true,
                scale(_rowId, rowData) {
                    let scale = 2;
                    const split = (rowData?.unitToStockUnitConversionFactor ?? '').split('.');
                    if (split.length > 1) {
                        scale = split[1].length;
                    }
                    return scale;
                },
                isHidden(_rowId, rowData) {
                    return rowData?.item?.type === 'service';
                },
            }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseReturnLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isReadOnly: true,
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                scale: (_rowId, rowData) => rowData?.stockUnit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol ?? '',
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                scale() {
                    return getCompanyPriceScale(this.returnSite?.value?.legalCompany);
                },
                unit() {
                    return this.currency.value;
                },
                onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    rowData.priceOrigin = rowData.grossPrice ? 'manual' : undefined;
                    this._calculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isReadOnly(_rowId, rowData) {
                    return rowData?.origin !== 'direct';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                onChange(_rowId, rowData) {
                    this._calculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isReadOnly(_rowId, rowData) {
                    return rowData?.origin !== 'direct';
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                onChange(_rowId, rowData) {
                    this._calculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
                isReadOnly(_rowId, rowData) {
                    return rowData?.origin !== 'direct';
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Net price',
                bind: 'netPrice',
                scale() {
                    return getCompanyPriceScale(this.returnSite?.value?.legalCompany);
                },
                unit() {
                    return this.currency.value;
                },
            }),
            ui.nestedFields.label<PurchaseReturn, PurchaseReturnLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-purchasing/PriceOrigin',
                width: 'large',
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseReturnLineBinding, Currency>({
                title: 'Currency',
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ISO 4217 code', bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                ],
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'amountExcludingTax',
                isReadOnly: true,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                bind: 'amountExcludingTaxInCompanyCurrency',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit() {
                    return this.returnSite.value;
                },
                scale: null,
            }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseReturnLineBinding, ReasonCode>({
                lookupDialogTitle: 'Select reason',
                title: 'Return reason',
                bind: 'reason',
                isMandatory: true,
                isReadOnly(_rowId, rowData) {
                    if (rowData?.status) {
                        return !['draft', 'pending'].includes(rowData.status);
                    }
                    return false;
                },
            }),
            ui.nestedFields.label({
                title: 'Allocation status',
                bind: 'allocationStatus',
                optionType: '@sage/xtrem-stock-data/StockAllocationStatus',
                isHidden(_value, rowData: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    return rowData
                        ? !rowData?.item?.isStockManaged
                        : this.shippingStatus.value === 'shipped' && this.stockTransactionStatus.value === 'completed';
                },
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus('StockAllocationStatus', rowData?.allocationStatus),
                width: 'large',
            }),
            ui.nestedFields.numeric({
                title: 'Allocated quantity',
                bind: 'quantityAllocated',
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHidden(rowData: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    return rowData
                        ? !rowData?.item?.isStockManaged
                        : this.shippingStatus.value === 'shipped' && this.stockTransactionStatus.value === 'completed';
                },
                scale: (_rowId, rowData) => rowData?.stockUnit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol ?? '',
            }),
            ui.nestedFields.label({
                title: 'Approval status',
                bind: 'approvalStatus',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentApprovalStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentApprovalStatus', rowData?.approvalStatus),
            }),
            ui.nestedFields.label({
                title: 'Shipping status',
                bind: 'shippedStatus',
                optionType: '@sage/xtrem-purchasing/PurchaseReturnShippingStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseReturnShippingStatus', rowData?.shippedStatus),
            }),

            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<
                PurchaseReturn,
                PurchaseReturnLineBinding,
                PurchaseReceiptLineToPurchaseReturnLine
            >({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseReturnLine',
                nestedFields: [
                    ui.nestedFields.reference({
                        title: 'Purchase receipt line',
                        bind: 'purchaseReceiptLine',
                        node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                        valueField: { document: { number: true } },

                        columns: [
                            ui.nestedFields.reference({
                                bind: 'document',
                                node: '@sage/xtrem-purchasing/PurchaseReceipt',
                                valueField: 'number',
                                columns: [
                                    ui.nestedFields.text({ bind: 'number' }),
                                    ui.nestedFields.dropdownList({ bind: 'status' }),
                                ],
                            }),
                            ui.nestedFields.numeric({ bind: 'remainingReturnQuantity' }),
                            ui.nestedFields.numeric({ bind: 'quantity' }),
                            ui.nestedFields.numeric({ bind: 'returnedQuantity' }),
                            ui.nestedFields.label({ bind: 'status' }),
                        ],
                    }),
                    ui.nestedFields.numeric({ title: 'Received quantity', bind: 'returnedQuantity', canFilter: false }),
                    ui.nestedFields.reference({
                        title: 'Purchase unit',
                        bind: 'unit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        isHidden: true,
                        valueField: 'symbol',

                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol', canFilter: false }),
                            ui.nestedFields.numeric({ title: 'Scale', bind: 'decimalDigits', canFilter: false }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<
                PurchaseReturn,
                PurchaseReturnLineBinding,
                PurchaseReceiptLineToPurchaseReturnLine
            >({
                title: 'Returned quantity',
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseReturnLine',
                valueField: 'returnedQuantity',
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                bind: 'stockTransactionStatus',
                isHidden() {
                    return ['draft', 'completed'].includes(this.stockTransactionStatus.value ?? '');
                },
            }),
            // TODO: no jsonStockDetails on PurchaseReturnLine node, uncomment when added
            // ui.nestedFields.technical<PurchaseReturn, PurchaseReturnLineBinding>({
            //     isTransientInput: true,
            //     bind: 'jsonStockDetails',
            // }),
        ],
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'All open statuses', graphQLFilter: { status: { _ne: 'closed' } } },
            {
                title: 'Allocation required',
                graphQLFilter: { allocationStatus: { _nin: ['allocated', 'notManaged'] } },
            },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({ bind: 'itemDescription', title: 'Description', canFilter: false }),
            line2Right: ui.nestedFields.numeric({
                bind: 'amountIncludingTax',
                title: 'Amount',
                canFilter: false,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            line3: ui.nestedFields.reference<PurchaseReturn, PurchaseReturnLineBinding, ReasonCode>({
                bind: 'reason',
                tunnelPage: undefined,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (recordValue && +recordValue._id < 0) {
                    return ui.localize('@sage/xtrem-purchasing/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'none',
                    title: 'Issued stock',
                    async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseReturnLineBinding>) {
                        const line = utils.removeExtractEdgesPartial(rowItem);
                        await utils.catchPanelCrossQuitButtonAsNoop(
                            StockDetailHelper.editStockDetails(this as any, line, {
                                movementType: 'issue',
                                data: {
                                    isEditable: false,
                                    documentLineSortValue: line._sortValue,
                                    documentLine: recordId,
                                    item: line.item?._id,
                                    stockSite: this.stockSite.value?._id,
                                    quantity: +line.quantityInStockUnit,
                                    number: this.number.value ?? undefined,
                                    unit: line.item.stockUnit?._id,
                                    searchCriteria: {
                                        activeQuantityInStockUnit: +line.quantityInStockUnit,
                                        item: line.item?._id,
                                        site: this.stockSite.value?._id ?? '',
                                        stockUnit: line.item.stockUnit?.id,
                                        statusList: [],
                                    },
                                },
                            }),
                        );
                    },
                    isHidden(_recordId: string, rowItem: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                        const line = utils.removeExtractEdgesPartial(rowItem);
                        return (
                            (line && !(line.item && line.quantityInStockUnit && line.item.isStockManaged)) ||
                            line.stockTransactionStatus !== 'completed'
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await utils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                { documentLine: rowData },
                                { editable: this.status.value !== 'closed' || this.isRepost },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden(_rowId, rowData) {
                        return this.status.value === 'closed' || Number(rowData.quantityAllocated) > 0;
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    if (recordValue.purchaseReceiptLine) {
                        this._updatePurchaseReceiptLine(
                            this.lines.getRecordValue(recordValue._id)?.purchaseReceiptLine,
                        );
                        await this.retrievePurchaseOrderFromPurchaseReceipt(
                            recordValue.purchaseReceiptLine.purchaseReceiptLine._id,
                        );
                    }
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    this.externalNoteLine.value = recordValue.externalNote ? recordValue.externalNote.value : '';
                    this.isExternalNoteLine.value = recordValue.isExternalNote ?? false;
                    this.externalNoteLine.isDisabled = !recordValue?.isExternalNote;
                    if (this.$.isDirty) {
                        this.$.setPageClean();
                    }
                }
            },
            // eslint-disable-next-line require-await
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    recordValue.externalNote.value = this.externalNoteLine.value ? this.externalNoteLine.value : '';
                    recordValue.isExternalNote = this.isExternalNoteLine.value ?? false;
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<PurchaseReturnLineBinding>,
                    );
                }
            },
            layout() {
                return {
                    general: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: { fields: ['approvalStatus', 'shippedStatus', 'allocationStatus'] },
                            itemBlock: { fields: ['item', 'origin', 'itemDescription'] },
                            reasonBlock: { fields: ['reason'] },
                            purchase: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_purchase',
                                    'Purchase',
                                ),
                                fields: ['unit', 'quantity'],
                            },
                            stock: {
                                isHidden(_id, recordValue) {
                                    return !recordValue?.item?.isStockManaged;
                                },
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: { fields: ['grossPrice', 'discount', 'charge', 'netPrice'] },
                            mainBlock2: { fields: ['priceOrigin'] },
                            totals: { fields: ['amountExcludingTax'] },
                            totals2: { fields: ['amountExcludingTaxInCompanyCurrency'] },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.purchaseReceiptLine.value.length === 0;
                        },
                        blocks: { receiptBlock: { fields: [this.purchaseReceiptLine, this.purchaseOrderPod] } },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
        mapServerRecord(rowItem) {
            return { ...rowItem, uQuantityAllocated: rowItem.quantityAllocated };
        },

        dropdownActions: [
            {
                icon: 'three_boxes',
                title: 'Allocate stock',
                async onClick(recordId, rowItem: ui.PartialNodeWithId<PurchaseReturnLineBinding>) {
                    const allocationChanged = await this.allocateStock(recordId, rowItem);
                    if (allocationChanged) {
                        await this.lines.refresh();
                        const result = extractEdges(
                            await this.$.graph
                                .node('@sage/xtrem-purchasing/PurchaseReturn')
                                .query(
                                    ui.queryUtils.edgesSelector(
                                        { _id: true, allocationStatus: true },
                                        { filter: { _id: { _eq: this.$.recordId } } },
                                    ),
                                )
                                .execute(),
                        ) as unknown as PurchaseReturnNode[];
                        this.allocationStatus.value = result[0].allocationStatus;
                        this.returnStepSequence.statuses = getPurchaseReturnStepSequenceValues(
                            !!this.isApprovalManaged.value,
                            this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                            this.allocationStatus.value as StockAllocationStatus,
                            this.approvalStatus.value as PurchaseDocumentApprovalStatus,
                        );
                        this.manageDisplayButtonAllOtherActions(false);
                    }
                },
                isDisabled() {
                    return this.$.isDirty;
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseReturnLineBinding>) {
                    if (!this.returnItems.value) return true;
                    const line = utils.removeExtractEdgesPartial(rowItem);
                    if (['inProgress', 'completed'].includes(line.stockTransactionStatus)) return true;
                    if (['draft', 'closed', 'posted', 'error'].includes(line.status)) return true;
                    if (['draft', 'pendingApproval', 'rejected', 'changeRequested'].includes(line.approvalStatus))
                        return true;
                    if (line && !(line.item && line.quantityInStockUnit && line.item.isStockManaged)) return true;
                    return false;
                },
            },
            {
                icon: 'none',
                title: 'Issued stock',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseReturnLineBinding>) {
                    const line = utils.removeExtractEdgesPartial(rowItem);
                    await utils.catchPanelCrossQuitButtonAsNoop(
                        StockDetailHelper.editStockDetails(this, line, {
                            movementType: 'issue',
                            data: {
                                isEditable: false,
                                documentLineSortValue: line._sortValue,
                                documentLine: recordId,
                                item: line.item?._id,
                                stockSite: this.stockSite.value?._id,
                                quantity: +line.quantityInStockUnit,
                                number: this.number.value ?? '',
                                unit: line.item.stockUnit?._id,
                                searchCriteria: {
                                    activeQuantityInStockUnit: +line.quantityInStockUnit,
                                    item: line.item?._id,
                                    site: this.stockSite.value?._id ?? '',
                                    stockUnit: line.item.stockUnit?.id,
                                    statusList: [],
                                },
                            },
                        }),
                    );
                },
                isHidden(_recordId: string, rowItem: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    const line = utils.removeExtractEdgesPartial(rowItem);
                    return (
                        (line && !(line.item && line.quantityInStockUnit && line.item.isStockManaged)) ||
                        line.stockTransactionStatus !== 'completed'
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            { editable: this.status.value !== 'closed' || this.isRepost },
                        ),
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isHidden(_rowId, rowData) {
                    return this.status.value === 'closed' || Number(rowData.quantityAllocated) > 0;
                },
                // TODO: only allow this on certain status\origins?
                async onClick(recordId: string) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_return__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_return__line_delete_action_dialog_content',
                                'You are about to delete this purchase return line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(recordId);
                        this.manageDisplayButtonSelectFromPurchaseReceiptLinesAction();
                    }
                },
            },
        ],
        fieldActions() {
            return setOrderOfPageTableFieldActions({
                actions: [],
            });
        },
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.selectFromPurchaseReceiptsLookup],
            });
        },
    })
    lines: ui.fields.Table<PurchaseReturnLineBinding>;

    @ui.decorators.tableField<PurchaseReturn, PurchaseOrderLineToPurchaseReceiptLine>({
        node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
        width: 'medium',
        isTransient: true,
        canSelect: false,
        columns: [
            ui.nestedFields.link({
                title: 'Purchase order number',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                isTransient: true,
                map(_value: string, rowData) {
                    return `${rowData.number}`;
                },
                page: '@sage/xtrem-purchasing/PurchaseOrder',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?._id ?? '',
                    };
                },
            }),
        ],
    })
    purchaseOrderPod: ui.fields.Table<PurchaseOrderLineToPurchaseReceiptLine>;

    @ui.decorators.pageAction<PurchaseReturn>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: Number(this.returnSite?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.tableField<PurchaseReturn, PurchaseReceiptLineToPurchaseReturnLine>({
        title: 'Purchase receipt line to purchase return line',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseReturnLine',
        columns: [
            ui.nestedFields.reference<PurchaseReturn, PurchaseReceiptLineToPurchaseReturnLine, PurchaseReturnLine>({
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLine',
                valueField: '_id',
                isHidden: true,
                columns: [],
            }),
            ui.nestedFields.link<PurchaseReturn, PurchaseReceiptLineToPurchaseReturnLine>({
                bind: '_id',
                isTransient: true,
                title: 'Receipt number',
                isFullWidth: true,
                map(_value, rowValue: ui.PartialCollectionValue<PurchaseReceiptLineToPurchaseReturnLine>) {
                    return `${rowValue?.purchaseReceiptLine?.document?.number}`;
                },
                page: '@sage/xtrem-purchasing/PurchaseReceipt',
                queryParameters(_value, rowData) {
                    return {
                        _id: rowData?.purchaseReceiptLine?.document?._id ?? '',
                    };
                },
            }),
            ui.nestedFields.label({
                title: 'Receipt status',
                bind: { purchaseReceiptLine: { status: true } },
                isTitleHidden: true,
                isTransient: true,
                map(value, rowData) {
                    switch (rowData.purchaseReceiptLine.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseReceiptLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseReceiptLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'returnedQuantity',
                isReadOnly: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.label({
                title: 'Quantity remaining on receipt',
                postfix(_rowId, rowData) {
                    return rowData?.unit?.symbol ?? '';
                },
                isTransient: true,
                bind: '_id',
                map(_rowId, rowData) {
                    const existingQuantity = rowData._id < 0 ? rowData.returnedQuantity : 0;
                    const quantityToReturn = +rowData.purchaseReceiptLine.remainingReturnQuantity - existingQuantity;
                    return (quantityToReturn < 0 ? 0 : quantityToReturn).toString();
                },
            }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseReceiptLineToPurchaseReturnLine, PurchaseReceiptLine>({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                valueField: { document: { date: true } },
                isTransient: true,
                isHidden: true,
                columns: [
                    ui.nestedFields.reference<PurchaseReturn, PurchaseReceiptLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        valueField: 'name',
                        columns: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'returnedQuantity' }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                    ui.nestedFields.image({
                        bind: { item: { image: true } },
                    }),
                ],
            }),
        ],
    })
    purchaseReceiptLine: ui.fields.Table<PurchaseReceiptLineToPurchaseReturnLine>;

    @ui.decorators.tableField<PurchaseReturn, PurchaseInvoiceLine>({
        title: 'Purchase invoice lines',
        isTransient: true,
        isHidden: true,
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.headerSection;
        },
        columns: [
            ui.nestedFields.reference<PurchaseReturn, PurchaseInvoiceLine, PurchaseInvoice>({
                title: 'Purchase invoice',
                bind: 'document',
                node: '@sage/xtrem-purchasing/PurchaseInvoice',
                valueField: 'number',
                isReadOnly: true,
                columns: [ui.nestedFields.technical({ bind: 'number' })],
            }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseInvoiceLine, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                isReadOnly: true,
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'name' })],
            }),
            ui.nestedFields.numeric({ title: 'Quantity', bind: 'quantity', isReadOnly: true }),
            ui.nestedFields.reference<PurchaseReturn, PurchaseInvoiceLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                title: 'Purchase unit',
                valueField: 'symbol',
                size: 'small',
                isReadOnly: true,
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'symbol' })],
            }),
            ui.nestedFields.numeric({ title: 'Gross price', bind: 'grossPrice', scale: 2, isReadOnly: true }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'amountExcludingTax',
                isReadOnly: true,
                unit() {
                    return this.currency.value;
                },
                scale: null,
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'View invoice',
                onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseInvoiceLine>) {
                    this.$.router.goTo('@sage/xtrem-purchasing/PurchaseInvoice', {
                        _id: rowItem?.document?._id ?? '',
                    });
                },
                isDisabled() {
                    return this && this.$.isDirty; // TODO: this is not available here : bug issue for platform to be created
                },
            },
        ],
    })
    purchaseInvoiceLines: ui.fields.Table<PurchaseInvoiceLine>;

    initPage() {
        const oldIsDirty = this.$.isDirty;
        if (this.currency.value) {
            this.applyCurrency();
        }

        if (!this.$.recordId) {
            this.returnRequestDate.value = DateValue.today().toString();
            // If it is a new record set the address value to blank object.
            this.supplierAddress.value = {};
            this.returnToAddress.value = {};
        }
        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();

        this.returnItems.isReadOnly = ['approved', 'rejected'].includes(this.approvalStatusHidden?.value ?? '');

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    applyCurrency() {
        this.totalAmountExcludingTax.unit = this.currency.value;
    }

    async loadRelatedPurchaseInvoiceLines() {
        const oldIsDirty = this.$.isDirty;
        const returnToInvoiceLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseInvoiceLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseReturnLine: { document: { _id: true } },
                            purchaseInvoiceLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true },
                                item: { _id: true, name: true },
                                quantity: true,
                                unit: { id: true, symbol: true },
                                grossPrice: true,
                                amountExcludingTax: true,
                            },
                        },
                        { filter: { purchaseReturnLine: { document: { _id: { _eq: this.$.recordId } } } } },
                    ),
                )
                .execute(),
        );
        const uniqueLines = new Array<string>();
        returnToInvoiceLines.forEach(row => {
            if (!uniqueLines.includes(row.purchaseInvoiceLine._id)) {
                uniqueLines.push(row.purchaseInvoiceLine._id);
                this.purchaseInvoiceLines.addRecord(row.purchaseInvoiceLine);
            }
        });
        this.purchaseInvoiceLines.isHidden = this.purchaseInvoiceLines.value.length === 0;

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    @ui.decorators.pageAction<PurchaseReturn>({
        title: 'Create invoice',
        isHidden: true,
        async onClick() {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_return__create_invoice_dialog_title',
                        'Confirm invoice creation',
                    ),
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_return__create_invoice_dialog_content',
                        'You are about to create an invoice from this purchase order.',
                    ),
                    ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
                )
            ) {
                this.$.loader.isHidden = false;
                const invoices = await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseReturn')
                    .mutations.createPurchaseInvoice({ number: true }, { returnDoc: this.$.recordId?.toString() ?? '' })
                    .execute();

                if (invoices && invoices.length > 0) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_return__invoice_created',
                            'Invoice created: {{invoiceNumbers}}.',
                            { invoiceNumbers: invoices.map(invoice => invoice.number).join('\n') },
                        ),
                        { type: 'success' },
                    );
                } else {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_return__invoice_not_created',
                            'Could not create invoice.',
                        ),
                        { type: 'error' },
                    );
                }
                this.$.setPageClean();
                await this.$.router.refresh();

                this.$.loader.isHidden = true;
            }
        },
    })
    createPurchaseInvoice: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        access: { node: '@sage/xtrem-stock-data/StockJournal', bind: '$read' },
        isHidden() {
            return this.displayStatus.value !== 'returned';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.pageAction<PurchaseReturn>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.stockTransactionStatus.value ?? '');
        },
        isHidden() {
            return this.displayStatus.value !== 'error';
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines },
            );
        },
    })
    stockPostingError: ui.PageAction;

    private async _setPriceOrigin(rowData: ui.PartialCollectionValue<PurchaseReturnLine>) {
        if (rowData?.item && Number(rowData?.quantity) >= 0 && rowData?.unit) {
            const purchasePrice = await getPurchasePrice(
                this.$.graph,
                this.returnSite.value?._id ?? '',
                this.businessRelation.value?._id ?? '',
                this.currency.value?._id ?? '',
                rowData.item?._id ?? '',
                rowData.unit?._id ?? '',
                parseFloat(rowData?.quantity ?? ''),
                new Date(this.returnRequestDate?.value ?? ''),
                false,
            );
            rowData.grossPrice = String(purchasePrice.price);
            rowData.priceOrigin = purchasePrice.priceOrigin ?? 'manual';
            this.lines.addOrUpdateRecordValue(rowData);
            await this.lines.redraw();
        }
    }

    private _updatePurchaseReceiptLine(
        purchaseReceiptLine?: ExtractEdgesPartial<PurchaseReceiptLineToPurchaseReturnLine>,
    ): void {
        if (purchaseReceiptLine) {
            this.purchaseReceiptLine.addOrUpdateRecordValue(purchaseReceiptLine);
        }
    }

    async retrievePurchaseOrderFromPurchaseReceipt(rowId: string) {
        const oldIsDirty = this.$.isDirty;
        this.purchaseOrderPod.value.forEach(podLine => {
            this.purchaseOrderPod.removeRecord(podLine._id);
        });
        const result = await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine')
            .query(
                ui.queryUtils.edgesSelector(
                    { _id: true, purchaseOrderLine: { document: { _id: true, number: true } } },
                    { filter: { purchaseReceiptLine: { _id: rowId } } },
                ),
            )
            .execute();
        if (result.edges.length > 0) {
            this.purchaseOrderPod.isHidden = false;
            this.purchaseOrderPod.value = result.edges.map(e => e.node.purchaseOrderLine.document);
        } else {
            this.purchaseOrderPod.isHidden = true;
        }
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async allocateStock(rowId: string, rowItem: ui.PartialCollectionValue<PurchaseReturnLineBinding>) {
        const line = utils.removeExtractEdgesPartial(rowItem);
        const allocationChanged = await utils.confirmDialogToBoolean(
            StockDetailHelper.editStockDetails(this as any, line, {
                movementType: 'allocation',
                data: {
                    isEditable: line.shippedStatus === 'notShipped',
                    needFullAllocation: false,
                    cannotOverAllocate: true,
                    documentLineHasAlreadySomeAllocations: Number(line.quantityAllocated) > 0,
                    effectiveDate: this.returnRequestDate.value ?? '',
                    documentLineSortValue: line._sortValue,
                    documentLine: rowId,
                    item: line.item?._id,
                    stockSite: this.stockSite.value?._id,
                    quantity: +line.quantityInStockUnit,
                    unit: line.item.stockUnit?._id,
                    location: await StockDataUtils.getDefaultLocation({
                        defaultLocationType: 'outboundDefaultLocation',
                        page: this,
                        itemId: line.item?._id ?? '',
                        site: this.stockSite.value,
                    }),
                    searchCriteria: {
                        activeQuantityInStockUnit: +line.quantityInStockUnit,
                        item: line.item?._id,
                        site: this.stockSite.value?._id ?? '',
                        stockUnit: line.item.stockUnit?._id,
                        statusList: [],
                        ignoreExpirationDate: true,
                    },
                },
            }),
        );
        return allocationChanged;
    }

    private _calculatePrices = (rowData: any) => {
        if (rowData.grossPrice >= 0) {
            rowData.netPrice = Decimal.roundAt(
                rowData.grossPrice * (1 + (rowData.charge ?? 0) / 100 - (rowData.discount ?? 0) / 100),
                getCompanyPriceScale(this.returnSite?.value?.legalCompany),
            );
            rowData.amountExcludingTax = Number(rowData.netPrice * (rowData.quantity ?? 0));
        }
    };

    receiptIsTransferHeaderNote() {
        return this.lines.value.some(
            line => line.purchaseReceiptLine?.purchaseReceiptLine?.document?.isTransferHeaderNote === true,
        );
    }

    receiptsIsTransferLineNote() {
        return this.lines.value.some(
            line => line.purchaseReceiptLine?.purchaseReceiptLine?.document?.isTransferLineNote === true,
        );
    }

    liensFromSingleReceipt() {
        const orderLineNumbers = this.lines.value.map(
            line => line.purchaseReceiptLine?.purchaseReceiptLine?.document?.number,
        );
        return orderLineNumbers.every(number => number === orderLineNumbers[0]);
    }

    lineNotesChanged() {
        return this.lines.value.some(
            line => line.internalNote?.value || line.isExternalNote || line.externalNote?.value,
        );
    }

    headerNotesChanged() {
        return this.internalNote.value !== '';
    }

    initPosting() {
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }
}
