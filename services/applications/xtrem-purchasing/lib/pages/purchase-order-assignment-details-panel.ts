import type { UnitOfMeasure } from '@sage/xtrem-master-data-api';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { getScaleValue } from '@sage/xtrem-master-data/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import type { DemandOrderAssignmentTable } from '../client-functions/interfaces/order-assignment';
import { queryOrderAssignment } from '../client-functions/shared/purchase-order-assignment';

@ui.decorators.page<PurchaseOrderAssignmentDetailsPanel>({
    title: 'Assigned orders',
    node: '@sage/xtrem-stock-data/OrderAssignment',
    module: 'manufacturing',
    isTransient: true,
    access: { node: '@sage/xtrem-purchasing/PurchaseOrder', bind: '$create' },
    businessActions() {
        return [this.cancel, this.confirm];
    },
    async onLoad() {
        const { purchaseOrderLineId, itemStockUnit, quantityInStockUnit, status } = JSON.parse(
            this.$.queryParameters.purchaseOrderLine as string,
        );
        if (!purchaseOrderLineId) return;

        const orderAssignmentLines = await queryOrderAssignment({
            page: this,
            supplyDocumentLine: purchaseOrderLineId,
        });
        orderAssignmentLines.forEach(orderAssignmentLine => {
            this.assignmentLines.addOrUpdateRecordValue({
                ...orderAssignmentLine,
                quantityNotAssigned:
                    Number(orderAssignmentLine.demandWorkInProgress.expectedQuantity) -
                    Number(orderAssignmentLine.quantityInStockUnit),
            });
        });
        this.requiredQuantity.value =
            orderAssignmentLines.reduce((acc, val) => acc + Number(val.demandWorkInProgress.expectedQuantity), 0) ?? 0;

        this.oldAssignedQuantities = orderAssignmentLines.map(orderAssignmentLine => ({
            _id: orderAssignmentLine._id,
            quantityInStockUnit: parseFloat(orderAssignmentLine.quantityInStockUnit),
            originalQuantityInStockUnit: parseFloat(orderAssignmentLine.quantityInStockUnit),
        }));
        this.quantityInStockUnit = quantityInStockUnit;
        this.itemStockUnit = itemStockUnit;
        this.isRowFieldDisabled = !['draft', 'pending', 'inProgress'].includes(status);
        this.$.setPageClean();
        this.updateHeaderQuantities();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
})
export class PurchaseOrderAssignmentDetailsPanel extends ui.Page<GraphApi> {
    demandOrderIdsToDelete: string[] = [];

    isRowFieldDisabled: boolean = true;

    oldAssignedQuantities: { _id: string; quantityInStockUnit: number; originalQuantityInStockUnit: number }[] = [];

    itemStockUnit: Partial<UnitOfMeasure> = { decimalDigits: 0, id: '', symbol: '', _id: '' };

    quantityInStockUnit: number;

    @ui.decorators.pageAction<PurchaseOrderAssignmentDetailsPanel>({
        title: 'Save',
        async onClick() {
            const updateLinePromises = this.assignmentLines.value
                .filter(line => line.action === 'update')
                .map(line =>
                    // We should use bulkUpdate here to for update https://jira.sage.com/browse/XT-47259
                    // Call update method and return a promise
                    this.$.graph
                        .node('@sage/xtrem-stock-data/OrderAssignment')
                        .update(
                            { _id: true },
                            { data: { _id: line._id, quantityInStockUnit: line.quantityInStockUnit } },
                        )
                        .execute(),
                );
            // Map the demandOrderIdsToDelete to an array of delete promises
            const deleteLinePromises = this.demandOrderIdsToDelete.map(demandOrderId =>
                // Call update method and return a promise
                this.$.graph.delete({ _id: demandOrderId, nodeName: '@sage/xtrem-stock-data/OrderAssignment' }),
            );
            // Wait for all the update and delete promises to resolve
            await Promise.all([...updateLinePromises, ...deleteLinePromises]);
            this.$.finish(this.oldAssignedQuantities.length);
        },
        onError(error) {
            this.$.showToast(error.message, { timeout: 0, type: 'error' });
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrderAssignmentDetailsPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish(true);
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<PurchaseOrderAssignmentDetailsPanel>({ isOpen: true, isTitleHidden: true })
    componentSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrderAssignmentDetailsPanel>({
        parent() {
            return this.componentSection;
        },
        width: 'large',
        title: 'General',
    })
    componentBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseOrderAssignmentDetailsPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Required quantity',
        bind: 'quantityInStockUnit',
        isDisabled: true,
        scale() {
            return getScaleValue(2, this.itemStockUnit.decimalDigits);
        },
        postfix() {
            return this.itemStockUnit.symbol ?? '';
        },
    })
    requiredQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseOrderAssignmentDetailsPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Assigned quantity',
        isDisabled: true,
        scale() {
            return getScaleValue(2, this.itemStockUnit.decimalDigits);
        },
        postfix() {
            return this.itemStockUnit.symbol ?? '';
        },
    })
    assignedQuantity: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseOrderAssignmentDetailsPanel>({
        parent() {
            return this.componentBlock;
        },
        title: 'Remaining quantity',
        bind: 'quantityInStockUnit',
        isDisabled: true,
        scale() {
            return getScaleValue(2, this.itemStockUnit.decimalDigits);
        },
        postfix() {
            return this.itemStockUnit.symbol ?? '';
        },
    })
    remainingQuantity: ui.fields.Numeric;

    @ui.decorators.tableField<PurchaseOrderAssignmentDetailsPanel, DemandOrderAssignmentTable>({
        parent() {
            return this.componentSection;
        },
        title: 'Assigned orders',
        node: '@sage/xtrem-stock-data/OrderAssignment',
        pageSize: 10,
        canSelect: false,
        columns: [
            ui.nestedFields.select({
                title: 'Assigned to',
                bind: 'demandType',
                optionType: '@sage/xtrem-stock-data/OrderToOrderDemandType',
                isReadOnly() {
                    return this.isRowFieldDisabled;
                },
            }),
            ui.nestedFields.technical({ bind: { demandDocumentLine: { documentId: true } } }),
            ui.nestedFields.link({
                title: 'Order',
                bind: { demandDocumentLine: { documentNumber: true } },
                page: '@sage/xtrem-sales/SalesOrder',
                queryParameters(_value, rowData) {
                    const line = rowData as DemandOrderAssignmentTable;
                    return { _id: line.demandDocumentLine.documentId };
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity on order',
                bind: { demandWorkInProgress: { expectedQuantity: true } },
                isReadOnly() {
                    return this.isRowFieldDisabled;
                },
                scale() {
                    return getScaleValue(2, this.itemStockUnit.decimalDigits);
                },
                postfix() {
                    return this.itemStockUnit.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Assigned quantity',
                bind: 'quantityInStockUnit',
                isReadOnly() {
                    return this.isRowFieldDisabled;
                },
                scale() {
                    return getScaleValue(2, this.itemStockUnit.decimalDigits);
                },
                postfix() {
                    return this.itemStockUnit.symbol ?? '';
                },
                onChange(_rowId, rowData: DemandOrderAssignmentTable) {
                    const { _id: itemId } = rowData;
                    const quantityInStockUnit = Number(rowData.quantityInStockUnit);
                    const expectedQuantity = Number(rowData.demandWorkInProgress?.expectedQuantity) ?? 0;
                    const assignment = this.oldAssignedQuantities.find(({ _id }) => _id === itemId);

                    if (!assignment || quantityInStockUnit < 0) {
                        this.confirm.isDisabled = true;
                        return;
                    }
                    if (quantityInStockUnit > expectedQuantity || quantityInStockUnit > this.quantityInStockUnit) {
                        this.confirm.isDisabled = true; // Disable saving due to error in quantity
                        this.assignmentLines.addOrUpdateRecordValue(rowData);
                        return;
                    }
                    rowData.quantityNotAssigned = expectedQuantity - Number(rowData.quantityInStockUnit);
                    assignment.quantityInStockUnit = Number(rowData.quantityInStockUnit); // Set the oldAssignedQuantities to the updated value
                    rowData.action = 'update';
                    this.assignmentLines.addOrUpdateRecordValue(rowData); // Update the header quantities
                    this.updateHeaderQuantities();
                },
                validation(newValue: number, rowData: DemandOrderAssignmentTable) {
                    const quantityInStockUnit = Number(rowData.quantityInStockUnit);
                    const expectedQuantity = Number(rowData.demandWorkInProgress?.expectedQuantity) ?? 0;

                    if (quantityInStockUnit > expectedQuantity || quantityInStockUnit > this.quantityInStockUnit) {
                        return newValue > expectedQuantity
                            ? ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__assigned_quantity_higher_than_quantity_on_order',
                                  'You have assigned more than the quantity on order. You need to reduce the assigned quantity.',
                              )
                            : ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__assigned_quantity_higher_than_purchase_order_line_quantity_in_stock_unit',
                                  'You have assigned more than the purchase quantity. You need to reduce the assigned quantity.',
                              );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity not assigned ',
                bind: 'quantityNotAssigned',
                isReadOnly() {
                    return this.isRowFieldDisabled;
                },
                scale() {
                    return getScaleValue(2, this.itemStockUnit.decimalDigits);
                },
                postfix() {
                    return this.itemStockUnit.symbol ?? '';
                },
            }),
            ui.nestedFields.technical({ bind: 'action' }),
        ],
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                isDisabled() {
                    return this.isRowFieldDisabled;
                },
                async onClick(assignmentLineId, assignmentLineData) {
                    if (
                        await this.$.dialog
                            .confirmation(
                                'warn',
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line_deletion_dialog_title',
                                    'Delete assigned line',
                                ),
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line_deletion_dialog_content',
                                    'You are about to delete the link to the order.',
                                ),
                                {
                                    acceptButton: {
                                        text: ui.localize(
                                            '@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line-delete',
                                            'Delete',
                                        ),
                                    },
                                    cancelButton: {
                                        text: ui.localize(
                                            '@sage/xtrem-purchasing/pages__purchase_order_line_assignment_details_panel__line-cancel',
                                            'Cancel',
                                        ),
                                    },
                                },
                            )
                            .then(() => true)
                            .catch(() => false)
                    ) {
                        this.demandOrderIdsToDelete.push(assignmentLineId);
                        const index = this.oldAssignedQuantities.findIndex(obj => obj._id === assignmentLineData._id);
                        if (index >= 0) {
                            this.oldAssignedQuantities.splice(index, 1);
                        }
                        this.assignmentLines.removeRecord(assignmentLineId);
                        this.updateHeaderQuantities();
                        this.confirm.isDisabled = false;
                    }
                },
            },
        ],
    })
    assignmentLines: ui.fields.Table<DemandOrderAssignmentTable>;

    private updateHeaderQuantities() {
        this.assignedQuantity.value = this.oldAssignedQuantities.reduce(
            (acc, val) => acc + val.quantityInStockUnit,
            0.0,
        );
        if (this.requiredQuantity.value) {
            this.remainingQuantity.value = this.requiredQuantity.value - this.assignedQuantity.value;
        }
        this.confirm.isDisabled = !this.$.isDirty;
    }
}
