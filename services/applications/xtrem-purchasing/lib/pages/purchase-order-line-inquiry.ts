import { withoutEdges } from '@sage/xtrem-client';
import type { GraphApi, PurchaseOrderLine, PurchaseOrderLineToPurchaseInvoiceLine } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import * as PillColorPurchase from '../client-functions/pill-color';
import { purchaseInquiries } from '../menu-items/purchasing-inquiry';

@ui.decorators.page<PurchaseOrderLineInquiry, PurchaseOrderLine>({
    menuItem: purchaseInquiries,
    priority: 50,
    title: 'Purchase order line',
    module: 'purchasing',
    mode: 'default',
    node: '@sage/xtrem-purchasing/PurchaseOrderLine',
    access: { node: '@sage/xtrem-purchasing/PurchaseOrder', bind: '$read' },
    navigationPanel: {
        onSelect() {
            // We have to prevent selecting a line to avoid opening the "normal" page
            // (which is not even defined).
            return true;
        },
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', isHidden: true }),
            companyName: ui.nestedFields.reference({
                bind: { site: { legalCompany: true } },
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'name',
            }),
            companyId: ui.nestedFields.text({
                bind: { site: { legalCompany: { id: true } } },
                title: 'Company ID',
                isHiddenOnMainField: true,
            }),
            siteName: ui.nestedFields.reference({
                bind: 'site',
                title: 'Purchasing site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteId: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Purchasing site ID',
                isHiddenOnMainField: true,
            }),
            supplierName: ui.nestedFields.reference({
                bind: { document: { supplier: true } },
                title: 'Supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
                valueField: 'name',
            }),
            supplierId: ui.nestedFields.text({ bind: { document: { supplier: { id: true } } }, title: 'Supplier ID' }),
            supplierItem: ui.nestedFields.reference({
                bind: 'itemSupplier',
                title: 'Supplier item ID',
                node: '@sage/xtrem-master-data/ItemSupplier',
                valueField: 'id',
                isHiddenOnMainField: true,
            }),
            supplierItemCode: ui.nestedFields.text({
                bind: { itemSupplier: { supplierItemCode: true } },
                title: 'Supplier item code',
                isHiddenOnMainField: true,
            }),
            supplierItemName: ui.nestedFields.text({
                bind: { itemSupplier: { supplierItemName: true } },
                title: 'Supplier item name',
                isHiddenOnMainField: true,
            }),
            purchaseOrderNumber: ui.nestedFields.reference({
                title: 'Purchase order',
                bind: 'document',
                tunnelPage: '@sage/xtrem-purchasing/PurchaseOrder',
                valueField: 'number',
            }),
            status: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData.status),
            }),
            expectedReceiptDate: ui.nestedFields.date({ bind: 'expectedReceiptDate', title: 'Expected receipt date' }),
            orderDate: ui.nestedFields.date({ bind: { document: { orderDate: true } }, title: 'Order date' }),
            itemName: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemId: ui.nestedFields.text({ bind: { item: { id: true } }, title: 'Item Id' }),
            itemDescription: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            quantity: ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
            }),
            quantityToReceive: ui.nestedFields.numeric({
                bind: 'quantityToReceive',
                title: 'Remaining quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
            }),
            unit: ui.nestedFields.reference({
                bind: 'unit',
                title: 'Unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            lineReceiptStatus: ui.nestedFields.label({
                bind: 'lineReceiptStatus',
                title: 'Receipt status',
                optionType: '@sage/xtrem-purchasing/PurchaseOrderReceiptStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderReceiptStatus', rowData.lineReceiptStatus),
            }),
            invoiceStatus: ui.nestedFields.label({
                bind: 'lineInvoiceStatus',
                title: 'Invoice status',
                optionType: '@sage/xtrem-purchasing/PurchaseOrderInvoiceStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderInvoiceStatus', rowData.lineInvoiceStatus),
            }),
            currencyName: ui.nestedFields.reference({
                bind: { document: { currency: true } },
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            currencyId: ui.nestedFields.text({
                bind: { document: { currency: { id: true } } },
                title: 'Currency ID',
                isHiddenOnMainField: true,
            }),
            companyCurrencyName: ui.nestedFields.reference({
                bind: { document: { companyCurrency: true } },
                title: 'Company currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            companyCurrencyId: ui.nestedFields.text({
                bind: { document: { companyCurrency: { id: true } } },
                title: 'Company currency ID',
                isHiddenOnMainField: true,
            }),
            netPrice: ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net price',
                unit: (_rowId, rowData) => rowData?.document?.currency,
            }),
            amountExcludingTax: ui.nestedFields.numeric({
                bind: 'amountExcludingTax',
                title: 'Ordered amount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                groupAggregationMethod: 'sum',
            }),
            amountExcludingTaxInCompanyCurrency: ui.nestedFields.numeric({
                bind: 'amountExcludingTaxInCompanyCurrency',
                title: 'Ordered amount in company currency',
                unit: (_rowId, rowData) => rowData?.document?.companyCurrency,
                groupAggregationMethod: 'sum',
            }),
            remainingAmountToReceiveExcludingTax: ui.nestedFields.numeric({
                bind: 'remainingAmountToReceiveExcludingTax',
                title: 'Remaining amount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            remainingAmountToReceiveExcludingTaxInCompanyCurrency: ui.nestedFields.numeric({
                bind: 'remainingAmountToReceiveExcludingTaxInCompanyCurrency',
                title: 'Remaining amount in company currency',
                unit: (_rowId, rowData) => rowData?.document?.companyCurrency,
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            numberOfPurchaseReceiptLines: ui.nestedFields.technical({ bind: 'numberOfPurchaseReceiptLines' }),
            numberOfPurchaseInvoiceLines: ui.nestedFields.technical({ bind: 'numberOfPurchaseInvoiceLines' }),
        },
        dropdownActions: [
            {
                title: 'Receipt lines',
                icon: 'document_right_align',
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderLine>) {
                    return (rowItem.numberOfPurchaseReceiptLines ?? 0) <= 0;
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderLine>) {
                    await this.$.dialog.page(
                        '@sage/xtrem-purchasing/PurchaseReceiptLineInquiry',
                        {
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                purchaseOrderLine: { purchaseOrderLine: { _id: rowItem._id } },
                            }),
                        },
                        {
                            isMainListDisplayedInDialog: true,
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            },
            {
                title: 'Invoice lines',
                icon: 'document_right_align',
                isHidden(_recordId: string, rowItem: PartialCollectionValueWithIds<PurchaseOrderLine>) {
                    return (rowItem.numberOfPurchaseInvoiceLines ?? 0) <= 0;
                },
                async onClick(_recordId: string, rowItem: PartialCollectionValueWithIds<PurchaseOrderLine>) {
                    const sysIds = withoutEdges(
                        await this.$.graph
                            .node('@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine')
                            .query(
                                ui.queryUtils.edgesSelector<PurchaseOrderLineToPurchaseInvoiceLine>(
                                    {
                                        purchaseInvoiceLine: { _id: true },
                                    },
                                    {
                                        filter: {
                                            purchaseOrderLine: { _id: rowItem._id },
                                        },
                                    },
                                ),
                            )
                            .execute(),
                    ).map(line => line.purchaseInvoiceLine._id);

                    await this.$.dialog.page(
                        '@sage/xtrem-purchasing/PurchaseInvoiceCreditMemoLineInquiry',
                        {
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                _id: { _in: sysIds },
                            }),
                        },
                        {
                            isMainListDisplayedInDialog: true,
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            },
        ],
        orderBy: { site: { id: 1 }, item: { id: 1 } },
    },
})
export class PurchaseOrderLineInquiry extends ui.Page<GraphApi, PurchaseOrderLine> {}
