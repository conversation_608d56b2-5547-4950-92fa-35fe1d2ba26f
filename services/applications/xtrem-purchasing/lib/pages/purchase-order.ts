import { asyncArray } from '@sage/xtrem-async-helper';
import type { Dict, ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import type {
    Address,
    BaseDocumentLine,
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    Currency,
    DeliveryMode,
    Item,
    PaymentTerm,
    Supplier,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    convertFromTo,
    getCompanyPriceScale,
    openItemSupplierPriceList,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { resynchronizeDocument } from '@sage/xtrem-master-data/build/lib/client-functions/document';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { confirmDialogToBoolean, formatError } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import {
    getAddressDetail,
    getConcatenatedAddress,
} from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { convertAmount, scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import type {
    GraphApi,
    PurchaseInvoice,
    PurchaseInvoiceLine,
    PurchaseOrderInvoiceStatus,
    PurchaseOrderLine,
    PurchaseOrderLineBinding,
    PurchaseOrderLineToPurchaseInvoiceLine,
    PurchaseOrderLineToPurchaseReceiptLine,
    PurchaseOrder as PurchaseOrderNode,
    PurchaseReceipt,
    PurchaseReceiptLine,
    PurchaseRequisitionLine,
    PurchaseRequisitionLineToPurchaseOrderLine,
} from '@sage/xtrem-purchasing-api';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
    setOrderOfPageTableHeaderBusinessActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import { validEmail } from '@sage/xtrem-system/build/lib/shared-functions/email-validation';
import type { DocumentTax, DocumentTaxBinding } from '@sage/xtrem-tax-api';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { refreshTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/display-taxes';
import * as ui from '@sage/xtrem-ui';
import {
    confirmDialogWithAcceptButtonText,
    isUiPartialCollectionValuePurchaseReceiptLineBinding,
    printPurchaseOrder,
    setOverwriteNote,
} from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-purchase-order';
import type { PurchaseOrderStepSequenceStatus } from '../client-functions/interfaces/step-sequence';
import * as LandedCost from '../client-functions/landed-cost';
import { addLineFromRequisition } from '../client-functions/order-from-requisition';
import * as PillColorPurchase from '../client-functions/pill-color';
import * as actionFunctions from '../client-functions/purchase-order-actions-functions';
import { getItemSupplierPrice, recalculatePricesDialog, updatePriceDialog } from '../client-functions/purchase-price';
import { displayTaxes } from '../client-functions/shared/display-taxes';
import { calculateLinePrices, isExchangeRateHidden, laterThanToday } from '../client-functions/shared/page-functions';
import {
    isAssignmentCheckedOnPurchaseOrderLineQuantityChanged,
    isOrderToOrderServiceOptionActivated,
    isPurchaseOrderAssignmentCheckedOnPurchaseOrderClose,
    isPurchaseOrderAssignmentCheckedOnPurchaseOrderDelete,
    isPurchaseOrderAssignmentCheckedOnPurchaseOrderLineClose,
    isPurchaseOrderAssignmentCheckedOnPurchaseOrderLineDelete,
    openDemandOrderAssignments,
} from '../client-functions/shared/purchase-order-assignment';
import {
    computeTotalAmounts,
    onLineItemChange,
    refreshDialogAddress,
} from '../client-functions/shared/purchase-order-line';
import { isPurchaseOrderLineActionDisabled, isPurchaseOrderLinePropertyDisabled } from '../shared-functions/edit-rules';

/**
 * Please update the corresponding cucumber functional test
 * {@link /xtrem/xtrem/services/functional-tests/xtrem-distribution-test/test/distribution-crud-purchase-order.feature.deactivated}
 */
@ui.decorators.page<PurchaseOrder, PurchaseOrderNode>({
    title: 'Purchase order',
    objectTypeSingular: 'Purchase order',
    objectTypePlural: 'Purchase orders',
    idField() {
        return this.number;
    },
    menuItem: purchasing,
    node: '@sage/xtrem-purchasing/PurchaseOrder',
    module: 'purchasing',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 400,
    createAction() {
        return this.$standardNewAction;
    },
    headerLabel() {
        return this.displayStatus;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.requestApproval,
                // this.requestChanges, TODO: not set for the moment
                this.approve,
                this.reject,
                this.createPurchaseReceipt,
                this.close,
                this.confirm,
                this.repost,
            ],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            duplicate: [this.$standardDuplicateAction],
            quickActions: [this.print],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.deletePurchaseOrder],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.sendEmail,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.orderStepSequence.statuses = this.getDisplayStatusStepSequence();
        if (!isDirty && this.$.recordId) {
            this.setMinimumOrderValueTick();
        }
        this._manageDisplayApplicativePageActions(isDirty);
    },
    async onLoad() {
        this.$standardCancelAction.isHidden = true;
        this.save.isHidden = true;
        this._manageDisplayApplicativePageActions(false);
        this.orderStepSequence.statuses = this.getDisplayStatusStepSequence();
        this.isRepost = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        if (
            this.$.queryParameters.fromLandedCostPanel?.toString() === 'true' ||
            this.$.queryParameters.fromPurchaseReceiptPage?.toString() === 'true'
        ) {
            this.isRepost = true;
        }
        await this.initPage();
        this.sourceDocumentData = JSON.parse(this.$.queryParameters.sourceDocumentData?.toString() ?? '{}');
        this.$.setPageClean();
        PurchaseOrder.totalTaxCalculatorInstance().initializeTotalTaxData(this.taxes, this.taxEngine.value ?? '');
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return formatError(this, error);
    },
    navigationPanel: {
        bulkActions: [
            {
                mutation: 'printBulk',
                title: 'Print',
                icon: 'print',
                buttonType: 'primary',
                isDestructive: false,
            },
        ],
        orderBy: { orderDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({ bind: 'number', title: 'Number' }),
            line1: ui.nestedFields.reference<PurchaseOrder, PurchaseOrderNode, Supplier>({
                bind: 'supplier',
                title: 'Supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
            }),
            id: ui.nestedFields.text({
                bind: { supplier: { id: true } },
                title: 'Supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'orderDate', title: 'Order date' }),
            line3: ui.nestedFields.reference<PurchaseOrder, PurchaseOrderNode, Site>({
                title: 'Purchasing site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                columns: [],
                tunnelPage: undefined,
            }),
            line_4: ui.nestedFields.numeric<PurchaseOrder, PurchaseOrderNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total including tax',
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line_5: ui.nestedFields.date({ bind: 'earliestExpectedDate', title: 'Earliest expected date' }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseOrderDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line6: ui.nestedFields.label({
                title: 'Invoice status',
                bind: 'invoiceStatus',
                optionType: '@sage/xtrem-purchasing/PurchaseOrderInvoiceStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderInvoiceStatus', rowData?.invoiceStatus),
            }),
            line8: ui.nestedFields.icon({
                bind: 'isPrinted',
                title: 'Printed',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData.isPrinted ? 'tick' : 'none'),
            }),
            line9: ui.nestedFields.icon<PurchaseOrder, PurchaseOrderNode>({
                title: 'Sent',
                bind: 'isSent',
                isHiddenOnMainField: true,
                map: (_value, rowData) => (rowData.isSent ? 'tick' : 'none'),
            }),
            line11: ui.nestedFields.reference<PurchaseOrder, PurchaseOrderNode, PaymentTerm>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line12: ui.nestedFields.numeric<PurchaseOrder, PurchaseOrderNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line13: ui.nestedFields.numeric({
                title: 'Tax',
                bind: 'totalTaxAmount',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line14: ui.nestedFields.reference<PurchaseOrder, PurchaseOrderNode, Currency>({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                ],
            }),
            status: ui.nestedFields.technical({ bind: 'status' }),
            approvalStatus: ui.nestedFields.technical({ bind: 'approvalStatus' }),
            taxCalculationStatus: ui.nestedFields.technical({ bind: 'taxCalculationStatus' }),
            taxEngine: ui.nestedFields.technical({ bind: 'taxEngine' }),
            isOrderAssignmentLinked: ui.nestedFields.technical<PurchaseOrder, PurchaseOrderNode>({
                bind: 'isOrderAssignmentLinked',
            }),
            supplierLinkedAddress: ui.nestedFields.technical<PurchaseOrder, PurchaseOrderNode, BusinessEntityAddress>({
                bind: 'supplierLinkedAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            totalQuantityToReceiveInStockUnit: ui.nestedFields.technical({ bind: 'totalQuantityToReceiveInStockUnit' }),
            isApprovalManaged: ui.nestedFields.technical({ bind: 'isApprovalManaged' }),
            isGrossPriceMissing: ui.nestedFields.technical({ bind: 'isGrossPriceMissing' }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _nin: ['closed', 'rejected', 'received'] } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
            { title: 'Pending approval', graphQLFilter: { displayStatus: { _eq: 'pendingApproval' } } },
            { title: 'Approved', graphQLFilter: { displayStatus: { _eq: 'approved' } } },
            { title: 'Confirmed', graphQLFilter: { displayStatus: { _eq: 'confirmed' } } },
            { title: 'Partially received', graphQLFilter: { displayStatus: { _eq: 'partiallyReceived' } } },
            { title: 'Received', graphQLFilter: { displayStatus: { _eq: 'received' } } },
            { title: 'Rejected', graphQLFilter: { displayStatus: { _eq: 'rejected' } } },
            { title: 'Closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
            { title: 'Tax calculation failed', graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } } },
        ],
        inlineActions: [
            {
                title: 'Duplicate',
                icon: 'duplicate',
                id: 'duplicate',
                async onClick(rowId: string) {
                    await this.$standardDuplicateAction.execute(false, rowId);
                },
            },
        ],
        dropdownActions: [
            {
                title: 'Submit for approval',
                id: 'submitForApproval',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (rowItem.taxCalculationStatus === 'failed') {
                        throw new Error(
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_order_submit_for_approval__tax_calculation_failed',
                                'You need to resolve tax calculation issues before submitting for approval.',
                            ),
                        );
                    }
                    if (recordId && rowItem.site?._id) {
                        await this.submitForApproval({
                            siteId: rowItem.site._id,
                            isGrossPriceMissing: rowItem.isGrossPriceMissing ?? false,
                            recordId: recordId ?? '',
                        });
                    }
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonRequestApprovalAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            taxEngine: rowItem.taxEngine,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                            isApprovalManaged: rowItem.isApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            {
                title: 'Create receipt',
                id: 'createReceipt',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.createPurchaseReceiptAction({
                            isCalledFromRecordPage: false,
                            purchaseOrderPage: this,
                            recordNumber: rowItem.number,
                            recordId,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    return (
                        displayButtons.isHiddenButtonCreatePurchaseReceiptAction({
                            parameters: {
                                status: rowItem.status,
                                approvalStatus: rowItem.approvalStatus,
                                totalQuantityToReceiveInStockUnit: +(rowItem.totalQuantityToReceiveInStockUnit ?? ''),
                                isRepost: this.isRepost,
                            },
                            recordId,
                            isDirty: false,
                        }) ?? false
                    );
                },
            },
            {
                title: 'Close order',
                id: 'closeOrder',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (
                        rowItem.number &&
                        recordId &&
                        (await actionFunctions.isPurchaseOrderAssignmentCheckClose(
                            this,
                            rowItem.isOrderAssignmentLinked ?? false,
                        ))
                    ) {
                        await actionFunctions.closeAction({
                            isCalledFromRecordPage: false,
                            purchaseOrderPage: this,
                            recordNumber: rowItem.number,
                            recordId,
                        });
                    }
                },
                isHidden(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    return (
                        displayButtons.isHiddenButtonCloseOrderAction({
                            parameters: {
                                status: rowItem.status,
                                approvalStatus: rowItem.approvalStatus,
                                totalQuantityToReceiveInStockUnit: +(rowItem.totalQuantityToReceiveInStockUnit ?? ''),
                                isRepost: this.isRepost,
                            },
                            recordId,
                            isDirty: false,
                        }) ?? false
                    );
                },
            },
            {
                title: 'Confirm',
                id: 'confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.confirmAction({
                            isCalledFromRecordPage: false,
                            purchaseOrderPage: this,
                            recordNumber: rowItem.number,
                            isConfirmed: true,
                            recordId,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        });
                    }
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonConfirmAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            taxEngine: rowItem.taxEngine,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                            isApprovalManaged: rowItem.isApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            {
                title: 'Approve',
                id: 'approve',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.approveAction({
                            isCalledFromRecordPage: false,
                            purchaseOrderPage: this,
                            recordNumber: rowItem.number,
                            isApproved: true,
                            recordId,
                        });
                    }
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonApproveAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            taxEngine: rowItem.taxEngine,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            {
                title: 'Reject',
                id: 'reject',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.rejectAction({
                            isCalledFromRecordPage: false,
                            purchaseOrderPage: this,
                            recordNumber: rowItem.number,
                            isRejected: true,
                            recordId,
                        });
                    }
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonRejectAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                title: 'Print',
                id: 'print',
                icon: 'print',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (recordId && rowItem.number) {
                        await printPurchaseOrder({
                            page: this,
                            _id: rowItem._id,
                            status: rowItem.taxCalculationStatus,
                            number: '',
                        });
                    }
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonPrintAction({
                        parameters: {
                            status: rowItem.approvalStatus,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        },
                        recordId,
                    }) ?? false,
            },
            {
                title: 'Send',
                id: 'send',
                icon: 'email',
                refreshesMainList: 'list',
                async onClick(_recordId, rowItem) {
                    this.sendEmailSection.isHidden = false;
                    await this.loadContacts(rowItem.supplierLinkedAddress?._id ?? '');
                    await this.$.dialog.custom('info', this.sendEmailSection, {
                        cancelButton: { isHidden: true },
                        acceptButton: { isHidden: true },
                        resolveOnCancel: true,
                    });
                    this.sendEmailSection.isHidden = true;
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonSendMailAction({
                        parameters: { displayStatus: rowItem.displayStatus },
                        recordId,
                    }) ?? false,
                isDisabled: (recordId, rowItem) =>
                    displayButtons.isDisabledButtonSendMailAction({
                        parameters: { approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                id: 'setDimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    await actionFunctions.setDimensions({
                        purchaseOrderPage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        approvalStatus: rowItem.approvalStatus,
                        isRepost: this.isRepost,
                        site: rowItem.site,
                        supplier: rowItem.supplier,
                    });
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: {
                            status: rowItem.status,
                            displayStatus: rowItem.displayStatus,
                            approvalStatus: rowItem.approvalStatus,
                        },
                        recordId,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                id: 'delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            isRepost: this.isRepost,
                        },
                        recordId,
                    });
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseOrderNode>) {
                    if (
                        await actionFunctions.isPurchaseOrderAssignmentCheckDelete(
                            this,
                            rowItem.isOrderAssignmentLinked ?? false,
                        )
                    ) {
                        await MainListActions.deleteRecord<GraphApi>(this, {
                            _id: rowItem._id,
                            nodeName: '@sage/xtrem-purchasing/PurchaseOrder',
                        });
                    }
                },
            },
        ],
    },
})
export class PurchaseOrder
    extends ui.Page<GraphApi, PurchaseOrderNode>
    implements financeInterfaces.PageWithDefaultDimensions
{
    private saveButtonClicked = false;

    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    isAddNewLine = false;

    displayTaxesClicked = false;

    currentSelectedLineId: string;

    oldQuantityInPurchaseUnit?: number = 0;

    oldQuantityInStockUnit?: number = 0;

    isRepost: boolean;

    sourceDocumentData: { financeTransactionSysId: string; numberOfSourceDocuments: number };

    private orderStepSequenceCreate = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_order__step_sequence_creation',
        'Create',
    );

    private orderStepSequenceApprove = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_order__step_sequence_approving',
        'Approve',
    );

    private orderStepSequenceReceive = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_order__step_sequence_receiving',
        'Receive',
    );

    private orderStepSequenceInvoice = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_order__step_sequence_invoicing',
        'Invoice',
    );

    private orderStepSequenceConfirm = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_order__step_sequence_confirm',
        'Confirm',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.invoiceStatus.value === 'invoiced') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'complete',
                invoice: 'complete',
            });
        }
        if (this.invoiceStatus.value === 'partiallyInvoiced') {
            if (this.receiptStatus.value === 'partiallyReceived') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    approve: 'complete',
                    confirm: 'complete',
                    receive: 'current',
                    invoice: 'current',
                });
            }
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'complete',
                invoice: 'current',
            });
        }
        if (this.receiptStatus.value === 'received') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'complete',
                invoice: 'incomplete',
            });
        }
        if (this.receiptStatus.value === 'partiallyReceived') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'current',
                invoice: 'incomplete',
            });
        }
        if (['approved', 'rejected', 'confirmed'].includes(this.approvalStatus.value ?? '')) {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                receive: 'incomplete',
                invoice: 'incomplete',
            });
        }
        if (this.approvalStatus.value === 'pendingApproval') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'current',
                confirm: 'incomplete',
                receive: 'incomplete',
                invoice: 'incomplete',
            });
        }
        if (!this.isApprovalManaged.value && this.$.recordId) {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'current',
                confirm: 'incomplete',
                receive: 'incomplete',
                invoice: 'incomplete',
            });
        }

        return this._setStepSequenceStatusObject({
            create: 'current',
            approve: 'incomplete',
            confirm: 'incomplete',
            receive: 'incomplete',
            invoice: 'incomplete',
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.deletePurchaseOrder,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.requestApproval,
                // this.requestChanges, TODO: not set for the moment
                this.approve,
                this.reject,
                this.createPurchaseReceipt,
                this.close,
                this.confirm,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.save.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value, isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });

        this.save.isHidden = displayButtons.isHiddenButtonSaveAction({
            isRepost: this.isRepost,
            approvalStatus: this.approvalStatus.value,
            isDirty,
        });

        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isHidden = displayButtons.isHiddenButtonCancelAction({
            isRepost: this.isRepost,
            approvalStatus: this.approvalStatus.value,
            isDirty,
        });

        this.deletePurchaseOrder.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                isRepost: this.isRepost,
            },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonRequestApprovalAction(isDirty);
        this.manageDisplayButtonApproveAction(isDirty);
        this.manageDisplayButtonConfirmAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        this.manageDisplayButtonRejectAction(isDirty);
        this.manageDisplayButtonRequestChangesAction(isDirty);
        this.manageDisplayButtonCloseOrderAction(isDirty);
        this.manageDisplayButtonCreatePurchaseReceiptAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonPrintAction();
        this.manageDisplayButtonSendMailAction(isDirty);
        this.manageDisplayButtonSelectFromPurchaseRequisitionLinesAction();
        this.manageDisplayLinePhantomRow();
    }

    manageDisplayButtonApproveAction(isDirty: boolean) {
        this.approve.isHidden = displayButtons.isHiddenButtonApproveAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                taxEngine: this.taxEngine.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonConfirmAction(isDirty: boolean) {
        this.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                taxEngine: this.taxEngine.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
                isApprovalManaged: this.isApprovalManaged.value,
                isRepost: this.isRepost,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRejectAction(isDirty: boolean) {
        this.reject.isHidden = displayButtons.isHiddenButtonRejectAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRequestApprovalAction(isDirty: boolean) {
        this.requestApproval.isHidden = displayButtons.isHiddenButtonRequestApprovalAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                taxEngine: this.taxEngine.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
                isApprovalManaged: this.isApprovalManaged.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRequestChangesAction(isDirty: boolean) {
        this.requestChanges.isHidden = displayButtons.isHiddenButtonRequestChangesAction({
            parameters: { status: this.status.value, approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonCloseOrderAction(isDirty: boolean) {
        this.close.isHidden = displayButtons.isHiddenButtonCloseOrderAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                totalQuantityToReceiveInStockUnit: this.totalQuantityToReceiveInStockUnit.value,
                isRepost: this.isRepost,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonCreatePurchaseReceiptAction(isDirty: boolean) {
        this.createPurchaseReceipt.isHidden = displayButtons.isHiddenButtonCreatePurchaseReceiptAction({
            parameters: {
                status: this.status.value,
                approvalStatus: this.approvalStatus.value,
                totalQuantityToReceiveInStockUnit: this.totalQuantityToReceiveInStockUnit.value,
                isRepost: this.isRepost,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonPrintAction() {
        this.print.isHidden = displayButtons.isHiddenButtonPrintAction({
            parameters: { status: this.approvalStatus.value, taxCalculationStatus: this.taxCalculationStatus.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonSendMailAction(isDirty: boolean) {
        this.sendEmail.isHidden = displayButtons.isHiddenButtonSendMailAction({
            parameters: { displayStatus: this.displayStatus.value },
            recordId: this.$.recordId,
        });

        this.sendEmail.isDisabled = displayButtons.isDisabledButtonSendMailAction({
            parameters: { approvalStatus: this.approvalStatus.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled =
            displayButtons.isDisabledLinePhantomRow({
                parameters: {
                    status: this.status.value,
                    site: this.site.value,
                    supplier: this.businessRelation.value,
                    currency: this.currency.value,
                    approvalStatus: this.approvalStatus.value,
                },
            }) ?? this.isRepost;
    }

    private manageDisplayButtonSelectFromPurchaseRequisitionLinesAction() {
        this.selectFromRequisition.isDisabled =
            displayButtons.isDisabledButtonSelectFromPurchaseRequisitionLinesAction({
                parameters: {
                    status: this.status.value,
                    site: this.site.value,
                    supplier: this.businessRelation.value,
                    currency: this.currency.value,
                    approvalStatus: this.approvalStatus.value,
                },
            }) ||
            this.isRepost ||
            this.approvalStatus.value === 'approved';

        this.selectFromRequisition.isHidden =
            displayButtons.isHiddenButtonSelectFromPurchaseRequisitionLinesAction({
                parameters: {
                    status: this.status.value,
                    site: this.site.value,
                    supplier: this.businessRelation.value,
                    currency: this.currency.value,
                    approvalStatus: this.approvalStatus.value,
                },
            }) ?? this.isRepost;
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: {
                status: this.status.value,
                displayStatus: this.displayStatus.value,
                approvalStatus: this.approvalStatus.value,
            },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: { site: this.site.value, supplier: this.businessRelation.value },
        });
    }

    static totalTaxCalculatorInstance() {
        return TotalTaxCalculator.getInstance();
    }

    async duplicateSetNewPurchasePrice() {
        await asyncArray(this.lines.value)
            .filter(
                (line: ui.PartialCollectionValue<PurchaseOrderLineBinding>) =>
                    line.origin !== 'direct' || line.priceOrigin === 'supplierPriceList',
            )
            .forEach(async (rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) => {
                await this._setPriceOrigin(rowData, false);
                await this._onChangeCalculatePrices(rowData);
            });
    }

    @ui.decorators.switchField<PurchaseOrder>({ isHidden: true })
    isApprovalManaged: ui.fields.Switch;

    /**  Blocks & sections     */
    @ui.decorators.section<PurchaseOrder>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<PurchaseOrder>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseOrder>({ title: 'Information' })
    informationSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.informationSection;
        },
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.tile<PurchaseOrder>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    // Sections

    @ui.decorators.section<PurchaseOrder>({ title: 'Requested changes', isTitleHidden: true })
    changeRequestTextSection: ui.containers.Section;

    @ui.decorators.section<PurchaseOrder>({ title: 'Financial', isTitleHidden: true })
    financialSection: ui.containers.Section;

    @ui.decorators.section<PurchaseOrder>({
        title: 'Totals',
        isTitleHidden: true,
    })
    totalsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseOrder>({
        title: 'Landed costs',
        isTitleHidden: true,
        isLazyLoaded: true,
        isHidden() {
            return !this.$.isServiceOptionEnabled('landedCostOption');
        },
        onActive() {
            if (this.jsonAggregateLandedCostTypes.value) {
                const landedCostSummary = LandedCost.mapLandedCostSummary(
                    JSON.parse(
                        this.jsonAggregateLandedCostTypes.value,
                    ) as LandedCostInterfaces.LandedCostTypeSummaryLine[],
                );
                this.landedCosts.value = landedCostSummary.tableLandedCostsValues;
                this.totalActualLandedCostsInCompanyCurrency.value = landedCostSummary.total;
            }
        },
    })
    landedCostsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseOrder>({
        title: 'Notes',
        isTitleHidden: true,
    })
    notesSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrder>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.richTextField<PurchaseOrder>({
        parent() {
            return this.noteBlock;
        },
        width: 'large',
        isFullWidth: true,
        helperText: 'Notes display on internal documents.',
        title: 'Internal notes',
        capabilities: validCapabilities,
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseOrder>({
        parent() {
            return this.noteBlock;
        },
        title: 'Add notes to the supplier document',
        width: 'large',
        onChange() {
            this.externalNote.isDisabled = !(this.isExternalNote.value ?? false);
            this.externalNote.value = '';
        },
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
        isFullWidth: true,
    })
    isExternalNote: ui.fields.Switch;

    @ui.decorators.richTextField<PurchaseOrder>({
        parent() {
            return this.noteBlock;
        },
        isFullWidth: true,
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'closed' || this.isRepost;
        },
        helperText: 'Notes display on supplier documents.',
        title: 'Supplier notes',
        capabilities: validCapabilities,
    })
    externalNote: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseOrder>({
        parent() {
            return this.noteBlock;
        },
        width: 'large',
        title: 'Repeat the document notes on new documents.',
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseOrder>({
        parent() {
            return this.noteBlock;
        },
        width: 'large',
        title: 'Repeat all the line notes on new documents.',
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
    })
    isTransferLineNote: ui.fields.Switch;

    @ui.decorators.richTextField<PurchaseOrder>({
        width: 'large',
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseOrder>({
        title: 'Add notes to the supplier document',
        isFullWidth: true,
        isTransient: true,
        width: 'large',
        onChange() {
            this.externalNoteLine.isDisabled = !(this.isExternalNoteLine.value ?? false);
            this.externalNoteLine.value = '';
        },
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
    })
    isExternalNoteLine: ui.fields.Switch;

    @ui.decorators.richTextField<PurchaseOrder>({
        isFullWidth: true,
        title: 'Supplier line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on supplier documents.',
        isDisabled() {
            return !this.isExternalNote.value || this.status.value === 'closed' || this.isRepost;
        },
    })
    externalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseOrder>({
        isHidden: true,
        parent() {
            return this.noteBlock;
        },
        isTransientInput: true,
    })
    isOverwriteNote: ui.fields.Switch;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.changeRequestTextSection;
        },
    })
    changeRequestTextBlock: ui.containers.Block;

    /** End of block & sections  */

    @ui.decorators.stepSequenceField<PurchaseOrder>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return [
                this.orderStepSequenceCreate,
                (this.isApprovalManaged.value ||
                    ['pendingApproval', 'approved', 'rejected', 'changeRequested'].includes(
                        this.approvalStatus.value ?? '',
                    )) &&
                this.approvalStatus.value !== 'confirmed'
                    ? this.orderStepSequenceApprove
                    : this.orderStepSequenceConfirm,
                this.orderStepSequenceReceive,
                this.orderStepSequenceInvoice,
            ];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    orderStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<PurchaseOrder, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Purchasing site',
        lookupDialogTitle: 'Select purchase site',
        isMandatory: true,
        minLookupCharacters: 0,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseOrder, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.reference<PurchaseOrder, Site, Company>({
                title: 'Company',
                valueField: 'name',
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseOrder, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.reference<PurchaseOrder, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        tunnelPage: '@sage/xtrem-master-data/Currency',
                        title: 'Currency',
                        bind: 'currency',
                        valueField: 'name',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.text({ bind: 'decimalDigits', title: 'Decimal places' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'priceScale' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseOrder, Site, User>({
                bind: 'purchaseOrderDefaultApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'displayName' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseOrder, Site, User>({
                bind: 'purchaseOrderSubstituteApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'displayName' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseOrder, Site, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        isReadOnly() {
            if (this.site.value === null) {
                return false;
            }
            // must be readonly if we start to add lines
            return (this.lines.value?.length ?? 0) !== 0;
        },
        async onChange() {
            await this.fetchDefaultsFromSite();
            this.manageDisplayButtonSelectFromPurchaseRequisitionLinesAction();
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            if (this.site.value) {
                this.taxEngine.value =
                    this.site.value!.legalCompany!.legislation!.id === 'US'
                        ? 'genericTaxCalculation'
                        : String(this.site.value!.legalCompany!.taxEngine);
                TotalTaxCalculator.getInstance().taxEngineProperty = this.taxEngine.value!;
                if (this.businessRelation.value) {
                    this._defaultDimensionsAttributes = await initDefaultDimensions({
                        page: this,
                        dimensionDefinitionLevel: 'purchasingDirect',
                        site: this.site.value,
                        supplier: this.businessRelation.value,
                    });
                }
            }
            this.showHideColumns();
        },
    })
    site: ui.fields.Reference<Site>;

    async fetchDefaultsFromSite() {
        await this.$.fetchDefaults(['siteAddress', 'stockSite']);
    }

    @ui.decorators.referenceField<PurchaseOrder, Site>({
        parent() {
            return this.informationBlock;
        },
        title: 'Receiving site',
        lookupDialogTitle: 'Select receiving site',
        minLookupCharacters: 0,
        isHidden: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical<PurchaseOrder, Site, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.technical<PurchaseOrder, Site, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical<PurchaseOrder, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.dropdownListField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax engine',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isHidden: true,
    })
    taxEngine: ui.fields.DropdownList;

    @ui.decorators.referenceField<PurchaseOrder, Supplier>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Supplier',
        lookupDialogTitle: 'Select supplier',
        minLookupCharacters: 3,
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { country: { name: true } } }, title: 'Country' }),
            ui.nestedFields.technical<PurchaseOrder, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseOrder, BusinessEntity, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseOrder, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
        ],
        async onChange() {
            if (this.businessRelation.value?.businessEntity?.currency) {
                this.currency.value = this.businessRelation.value.businessEntity.currency;
            }
            if (this.businessRelation.value?.paymentTerm) {
                this.paymentTerm.value = this.businessRelation.value.paymentTerm;
            }

            await this.fetchDefaultsFromSupplier();
            this.manageDisplayButtonSelectFromPurchaseRequisitionLinesAction();
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            this.showHideColumns();
            if (this.site.value && this.businessRelation.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'purchasingDirect',
                    site: this.site.value,
                    supplier: this.businessRelation.value,
                });
            }
        },
        isReadOnly() {
            if (!this.businessRelation.value) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
    })
    businessRelation: ui.fields.Reference<Supplier>;

    async fetchDefaultsFromSupplier() {
        await this.$.fetchDefaults([
            'supplierLinkedAddress',
            'supplierAddress',
            'currency',
            'paymentTerm',
            'internalNote',
        ]);
    }

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.numericField<PurchaseOrder>({ isHidden: true })
    totalQuantityToReceiveInStockUnit: ui.fields.Numeric;

    @ui.decorators.switchField<PurchaseOrder>({ isHidden: true })
    isGrossPriceMissing: ui.fields.Switch;

    @ui.decorators.dateField<PurchaseOrder>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Order date',
        isMandatory: true,
        fetchesDefaults: true,
        isReadOnly() {
            return !this.$.recordId || this.orderDate.value === null ? false : (this.lines.value?.length ?? 0) !== 0;
        },
        validation(val) {
            return laterThanToday(
                val,
                ui.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_order__order_date_cannot_be_future',
                    'The order date cannot be later than today.',
                ),
            );
        },
    })
    orderDate: ui.fields.Date;

    @ui.decorators.dateField<PurchaseOrder>({})
    earliestExpectedDate: ui.fields.Date;

    @ui.decorators.referenceField<PurchaseOrder, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Company currency',
        lookupDialogTitle: 'Select company currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isMandatory: true,
        fetchesDefaults: true,
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
            ui.nestedFields.technical({ bind: 'symbol' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter() {
            return { isActive: true };
        },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCompanyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
    })
    companyCurrency: ui.fields.Reference<Currency>;

    private _applyCurrency() {
        this.totalAmountExcludingTax.unit = this.currency.value;
    }

    private applyCompanyCurrency() {
        this.totalAmountExcludingTax.unit = this.siteCurrency;
    }

    @ui.decorators.numericField<PurchaseOrder>({
        parent() {
            return this.tileContainer;
        },
        title: 'Minimum order amount',
        bind: { supplier: { minimumOrderAmount: true } },
        unit() {
            return this.currency.value;
        },
    })
    minimumOrderAmount: ui.fields.Numeric;

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.tileContainer;
        },
        title: 'Earliest expected date',
        isTransient: true,
        width: 'medium',
    })
    earliestExpectedDateTile: ui.fields.Text;

    @ui.decorators.aggregateField<PurchaseOrder>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountExcludingTax',
        aggregationMethod: 'sum',
        title: 'Total excluding tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    purchaseOrderExcludingValue: ui.fields.Aggregate;

    @ui.decorators.aggregateField<PurchaseOrder>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'amountIncludingTax',
        aggregationMethod: 'sum',
        title: 'Total including tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale: null,
        width: 'medium',
    })
    purchaseOrderValue: ui.fields.Aggregate;

    @ui.decorators.dateField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden: true,
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.separatorField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        isFullWidth: true,
        isHidden: true,
    })
    textSeparator: ui.fields.Separator;

    @ui.decorators.separatorField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    statusSeparator: ui.fields.Separator;

    @ui.decorators.labelField<PurchaseOrder>({
        title: 'Display status',
        optionType: '@sage/xtrem-purchasing/PurchaseOrderDisplayStatus',
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation') {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/page__at_least_one_mandatory_tax_code_not_found',
                        'At least one mandatory tax code is missing on the line.',
                    ),
                    { type: 'warning' },
                );
            }
            if (this.$.recordId) {
                await resynchronizeDocument(this, this.$.recordId);
            }
        },
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label;

    @ui.decorators.labelField<PurchaseOrder>({
        title: 'Status',
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<PurchaseOrder>({
        title: 'Approval status',
        isHidden: true,
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentApprovalStatus',
    })
    approvalStatus: ui.fields.Label;

    @ui.decorators.labelField<PurchaseOrder>({
        title: 'Receipt status',
        isHidden: true,
        optionType: '@sage/xtrem-purchasing/PurchaseOrderReceiptStatus',
    })
    receiptStatus: ui.fields.Label;

    @ui.decorators.richTextField<PurchaseOrder>({
        parent() {
            return this.changeRequestTextBlock;
        },
        title: 'Change requested',
        height: '20px',
        isFullWidth: true,
    })
    changeRequestedDescription: ui.fields.RichText;

    private _isLineDisabled(rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        return (
            (['closed', 'pending'].includes(this.status.value ?? '') ||
                ['closed', 'pending'].includes(rowData?.status ?? '')) &&
            this.approvalStatus.value !== 'confirmed'
        );
    }

    @ui.decorators.tableField<PurchaseOrder, PurchaseOrderLineBinding>({
        title: 'Lines',
        bind: 'lines',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        canAddNewLine: true,
        hasLineNumbers: true,
        node: '@sage/xtrem-purchasing/PurchaseOrderLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        onChange() {
            computeTotalAmounts(this);
            this.setEarliestExpectedDate(false);
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.image({ bind: { item: { image: true } }, isExcludedFromMainField: true }),
            ui.nestedFields.technical({ bind: { externalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { text: { value: true } } }),
            ui.nestedFields.switch({
                bind: 'isExternalNote',
                title: 'Add notes to the supplier document',
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.label({
                title: 'Tax status',
                bind: 'taxCalculationStatus',
                optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
                style: (_id, rowData) =>
                    PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', rowData?.taxCalculationStatus),
                async onClick(_id, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    this.displayTaxesClicked = true;
                    await this.callDisplayTaxes(rowItem);
                    this.displayTaxesClicked = false;
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentLineOrigin',
                isReadOnly: true,
            }),
            ui.nestedFields.reference<PurchaseOrder, PurchaseOrderLineBinding, Item>({
                title: 'Item',
                lookupDialogTitle: 'Select item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                isReadOnly() {
                    return this.approvalStatus.value === 'approved';
                },
                isDisabled(_rowId, rowData) {
                    // Only allow the changing of item if it is a new record.
                    return +(rowData?._id ?? 0) > 0;
                },
                fetchesDefaults: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical<PurchaseOrder, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseOrder, Item, UnitOfMeasure>({
                        bind: 'purchaseUnit',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
                filter: { status: 'active' },

                orderBy: { name: -1, stockUnit: { id: +1 } },
                async onChange(rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    await onLineItemChange(this, rowId, rowData);
                    await this._setPriceOrigin(rowData, false);
                    this.updateTileContainerValues(true);

                    if (this.site.value && this.businessRelation.value && rowData.item) {
                        const { storedAttributes, storedDimensions } =
                            await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                page: this,
                                _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                dimensionDefinitionLevel: 'purchasingDirect',
                                site: this.site.value,
                                supplier: this.businessRelation.value,
                                item: rowData.item,
                            });
                        rowData.storedAttributes = storedAttributes;
                        rowData.storedDimensions = storedDimensions;
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'itemDescription',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
            }),
            ui.nestedFields.reference<PurchaseOrder, PurchaseOrderLineBinding, Site>({
                title: 'Purchasing site',
                lookupDialogTitle: 'Select purchasing site',
                minLookupCharacters: 0,
                bind: 'site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'site',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                isExcludedFromMainField: true,
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.reference<PurchaseOrder, Site, Company>({
                        title: 'Company',
                        valueField: 'name',
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                        columns: [
                            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                            ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseOrder, PurchaseOrderLineBinding, Site>({
                title: 'Receiving site',
                lookupDialogTitle: 'Select receiving site',
                minLookupCharacters: 0,
                bind: 'stockSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                isReadOnly() {
                    return this.approvalStatus.value === 'approved';
                },
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'stockSite',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                fetchesDefaults: true,
                isMandatory: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.reference<PurchaseOrder, Site, Company>({
                        title: 'Company',
                        bind: 'legalCompany',
                        valueField: 'description',
                        canFilter: false,
                        node: '@sage/xtrem-system/Company',
                        tunnelPage: '@sage/xtrem-master-data/Company',
                    }),
                    ui.nestedFields.technical({ bind: 'isPurchase' }),
                    ui.nestedFields.technical({ bind: 'isInventory' }),
                    ui.nestedFields.technical<PurchaseOrder, Site, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
                    }),
                ],
                filter(rowData) {
                    return { legalCompany: { _id: { _eq: rowData.site.legalCompany._id } } };
                },
                async onChange(rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    if (rowData?.stockSite) {
                        if (rowData.item) {
                            rowData.expectedReceiptDate = await this.setExpectedReceiptDate(
                                rowData.item._id ?? '',
                                rowData.stockSite._id ?? '',
                            );
                        }
                        rowData.stockSiteLinkedAddress = undefined;
                        rowData.stockSiteAddress = undefined;
                        const updatedRowData = await this.fetchDefaultStockSiteAddress(rowData);
                        rowData.stockSiteLinkedAddress = updatedRowData?.stockSiteLinkedAddress;
                        rowData.stockSiteAddress = updatedRowData?.stockSiteLinkedAddress;
                        // Refreshing the edit line dialog page in case it points to the current rowId
                        refreshDialogAddress(this, rowId, rowData);
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.reference<PurchaseOrder, PurchaseOrderLineBinding, UnitOfMeasure>({
                title: 'Purchase unit',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 0,
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return (
                            isPurchaseOrderLinePropertyDisabled(
                                this.status.value ?? '',
                                rowData.status ?? '',
                                'unit',
                                this.approvalStatus.value ?? '',
                                this.isRepost,
                            ) || rowData.origin === 'purchaseRequisition'
                        );
                    }
                    return false;
                },
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    if (rowData?.unit) {
                        const quantityToConvert = rowData.quantity ? +rowData.quantity : 1;

                        const conversion = await convertFromTo(
                            this.$.graph,
                            rowData.unit._id ?? '',
                            rowData.item?.stockUnit?._id ?? '',
                            quantityToConvert,
                            rowData.item?._id ?? '',
                            this.businessRelation.value?._id,
                            '',
                            'purchase',
                            false,
                        );
                        rowData.stockUnit = rowData.item?.stockUnit;
                        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor.toString();
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = conversion.convertedQuantity.toString();
                        }
                        this.lines.addOrUpdateRecordValue(rowData);

                        await this._setPriceOrigin(rowData, false);
                        await this._onChangeCalculatePrices(rowData);
                    }
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'quantity',
                isMandatory: true,
                isReadOnly() {
                    return this.approvalStatus.value === 'approved';
                },
                validation(val: number) {
                    if (val <= 0) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_order_line_panel__quantity_in_purchase_unit_negative_value',
                            'The quantity in purchase unit cannot be less than or equal to 0.',
                        );
                    }
                    return undefined;
                },
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'quantity',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    const { conversionFactor, convertedQuantity } = await convertFromTo(
                        this.$.graph,
                        rowData.unit?._id ?? '',
                        rowData.item?.stockUnit?._id ?? '',
                        parseFloat(rowData.quantity ?? '0'),
                        rowData.item?._id,
                        this.businessRelation.value?._id,
                        '',
                        'purchase',
                        false,
                    );
                    const uDemandOrderQuantity = parseFloat(rowData.uDemandOrderQuantity ?? '0');
                    const convertedQuantityInStockUnit =
                        parseFloat(conversionFactor.toString()) * parseFloat(rowData.quantity ?? '0');
                    const confirmationResult =
                        convertedQuantityInStockUnit < uDemandOrderQuantity
                            ? await isAssignmentCheckedOnPurchaseOrderLineQuantityChanged(this, rowData)
                            : true;

                    if (confirmationResult && rowData?.item && Number(rowData?.quantity) >= 0 && rowData?.unit) {
                        this.oldQuantityInPurchaseUnit = parseFloat(rowData.quantity ?? '0');
                        this.oldQuantityInStockUnit = parseFloat(rowData.quantityInStockUnit ?? '0');
                        rowData.quantityInStockUnit = convertedQuantity.toString();
                        rowData.unitToStockUnitConversionFactor = conversionFactor.toString();
                    } else {
                        rowData.quantity =
                            this.oldQuantityInPurchaseUnit && this.oldQuantityInPurchaseUnit > 0
                                ? this.oldQuantityInPurchaseUnit.toString()
                                : rowData.quantityToReceive;
                    }

                    if (rowData.purchaseRequisitionLines?.length) {
                        rowData.purchaseRequisitionLines[0].orderedQuantity = rowData.quantity ?? '0';
                    }
                    this.lines.addOrUpdateRecordValue(rowData);

                    let isRecalculatePrices = false;
                    if (+(rowData._id ?? '') > 0) {
                        isRecalculatePrices = await updatePriceDialog(this);
                    }

                    await this._setPriceOrigin(rowData, isRecalculatePrices);
                    await this._onChangeCalculatePrices(rowData);
                },
            }),
            ui.nestedFields.technical({ bind: 'uSupplyOrderQuantity' }),
            ui.nestedFields.technical({ bind: 'uDemandOrderQuantity' }),
            ui.nestedFields.reference<PurchaseOrder, PurchaseOrderLineBinding, UnitOfMeasure>({
                bind: 'stockUnit',
                isReadOnly: true,
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    await this._setPriceOrigin(rowData, false);
                    await this._onChangeCalculatePrices(rowData);
                },
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),

            ui.nestedFields.numeric({
                bind: 'unitToStockUnitConversionFactor',
                title: 'Stock unit conversion factor',
                width: 'large',
                isHiddenOnMainField: true,
                isReadOnly: true,
                scale(_rowId, rowData) {
                    let scale = 2;
                    const split = (rowData?.unitToStockUnitConversionFactor ?? '').split('.');
                    if (split.length > 1) {
                        scale = split[1].length;
                    }
                    return scale;
                },
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isHiddenOnMainField: true,
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol ?? '',
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),

            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                isReadOnly() {
                    return this.approvalStatus.value === 'approved';
                },
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'grossPrice',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                scale() {
                    return getCompanyPriceScale(this.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
                async onChange(_rowId, rowData) {
                    rowData.priceOrigin = 'manual';
                    await this._onChangeCalculatePrices(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'grossPrice',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    await this._onChangeCalculatePrices(rowData);
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'grossPrice',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                async onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    await this._onChangeCalculatePrices(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Net price',
                bind: 'netPrice',
                isReadOnly: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'grossPrice',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                scale() {
                    return getCompanyPriceScale(this.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
                async onChange(_rowId, rowData) {
                    rowData.priceOrigin = 'manual';
                    await this._onChangeCalculatePrices(rowData);
                },
            }),
            ui.nestedFields.label<PurchaseOrder, PurchaseOrderLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                width: 'small',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PriceOrigin',
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'amountExcludingTax',
                width: 'small',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total tax',
                bind: 'taxAmount',
                width: 'small',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                isReadOnly: true,
            }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                bind: 'amountIncludingTax',
                width: 'large',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                bind: 'amountExcludingTaxInCompanyCurrency',
                width: 'small',
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
                isReadOnly: true,
                isExcludedFromMainField: true,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax company currency',
                bind: 'amountIncludingTaxInCompanyCurrency',
                width: 'small',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Actual landed cost in company currency',
                bind: 'actualLandedCostInCompanyCurrency',
                width: 'small',
                isReadOnly: true,
                isHiddenOnMainField: true,
                unit() {
                    return this.site.value?.legalCompany?.currency;
                },
                scale: null,
            }),
            ui.nestedFields.date({
                title: 'Expected receipt date',
                bind: 'expectedReceiptDate',
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'expectedReceiptDate',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
                validation(value) {
                    if (value && this.orderDate.value) {
                        return Date.parse(value) < Date.parse(this.orderDate.value)
                            ? ui.localize(
                                  '@sage/xtrem-purchasing/pages_purchase_order_receipt_date_cannot_be_less_than_order_date',
                                  'The expected receipt date must be later than the order date.',
                              )
                            : undefined;
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLine, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'rounding' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            ui.nestedFields.technical({
                node: '@sage/xtrem-master-data/ItemSite',
                bind: 'itemSite',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'prodLeadTime' }),
                    ui.nestedFields.technical({ bind: 'isOrderToOrder' }),
                    ui.nestedFields.technical({ bind: 'preferredProcess' }),
                ],
            }),
            ui.nestedFields.text({
                title: 'Change requested',
                bind: 'changeRequestedDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled(_value, rowData) {
                    if (rowData && isUiPartialCollectionValuePurchaseReceiptLineBinding(rowData)) {
                        return isPurchaseOrderLinePropertyDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'changeRequestedDescription',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    }
                    return false;
                },
            }),
            ui.nestedFields.label({
                title: 'Receipt status',
                bind: 'lineReceiptStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PurchaseOrderReceiptStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderReceiptStatus', rowData?.lineReceiptStatus),
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                bind: 'lineInvoiceStatus',
                optionType: '@sage/xtrem-purchasing/PurchaseOrderInvoiceStatus',
                isHiddenOnMainField: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderInvoiceStatus', rowData?.lineInvoiceStatus),
            }),
            ui.nestedFields.numeric({
                title: 'Remaining quantity to receive',
                bind: 'quantityToReceive',
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity received',
                bind: 'receivedQuantity',
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity received in progress',
                bind: 'quantityReceivedInProgress',
                isHiddenOnMainField: true,
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.progress({
                bind: 'receivedQuantityProgress',
                title: 'Receipt progression',
                isHiddenOnMainField: true,
                color(value: number) {
                    if (value < 30) {
                        return ui.tokens.colorsSemanticNegative500;
                    }
                    if (value < 50) {
                        return ui.tokens.colorsSemanticCaution500;
                    }
                    if (value > 90) {
                        return ui.tokens.colorsSemanticPositive500;
                    }
                    return ui.tokens.colorsUtilityMajor200;
                },
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.numeric({
                title: 'Quantity to receive in stock unit',
                bind: 'quantityToReceiveInStockUnit',
                isExcludedFromMainField: true,
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.stockUnit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol ?? '',
            }),
            ui.nestedFields.reference<PurchaseOrder, PurchaseOrderLineBinding, BusinessEntityAddress>({
                title: 'Receiving address',
                bind: 'stockSiteLinkedAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                valueField: 'name',
                isMandatory: true,
                isReadOnly: true,
                isExcludedFromMainField: true,
                isHidden: true,
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical<PurchaseOrder, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                    ui.nestedFields.technical<PurchaseOrder, BusinessEntityAddress, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [ui.nestedFields.text({ bind: 'id' })],
                    }),
                    ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                ],
                filter(rowValue) {
                    if (rowValue.stockSite?.businessEntity?.id) {
                        return {
                            isActive: true,
                            businessEntity: { id: rowValue.stockSite.businessEntity.id },
                        };
                    }
                    return { isActive: true };
                },
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLine, Address>({
                bind: 'stockSiteAddress',
                node: '@sage/xtrem-master-data/Address',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'locationPhoneNumber' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: '_id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
                ],
            }),
            ui.nestedFields.icon({
                title: 'Assigned to',
                bind: 'uDemandOrderLineLink',
                map: value => (value ? 'file_generic' : 'null'),
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLine, BaseDocumentLine>({
                bind: 'uDemandOrderLine',
                node: '@sage/xtrem-master-data/BaseDocumentLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'documentNumber' }),
                    ui.nestedFields.technical({ bind: 'documentId' }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Item status',
                bind: { item: { status: true } },
                isDisabled: true,
                isHiddenOnMainField: true,
            }),
        ],
        onRowAdded() {
            this.disableHeaderFields();
            this.updateTileContainerValues(true);
        },
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'All open statuses', graphQLFilter: { status: { _ne: 'closed' } } },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                id: 'openLinePanel',
                onClick(rowId) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'locked',
                title: 'Close',
                id: 'close',
                isDisabled(_recordId, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    return rowItem.status === 'closed' || this.$.isDirty || this.isRepost;
                },
                isHidden(_recordId, rowItem) {
                    return (
                        isPurchaseOrderLineActionDisabled(
                            this.status.value ?? '',
                            rowItem.status ?? '',
                            'close',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        ) ||
                        ['draft'].includes(this.status.value ?? '') ||
                        ['draft'].includes(rowItem?.status ?? '') ||
                        ['pendingApproval'].includes(this.approvalStatus.value ?? '')
                    );
                },

                async onClick(recordId, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    if (!(await this.checkLineClosable(recordId, rowItem))) {
                        return;
                    }
                    await this.clickOnLineCloseButton(rowItem);
                },
            },
            {
                icon: 'refresh',
                title: 'Update price',
                id: 'updatePrice',
                isHidden(rowId: string) {
                    return (
                        Number(rowId) <= 0 ||
                        !['draft', 'pendingApproval', 'confirmed'].includes(this.displayStatus.value ?? '')
                    );
                },
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    if (Number(rowItem._id) > 0) {
                        await this.purchaseOrderUpdatePrice(rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'file_generic',
                title: 'Assign order',
                id: 'assignOrder',
                isHidden: (_rowId, rowData) =>
                    !(isOrderToOrderServiceOptionActivated(rowData) && rowData.uDemandOrderLine?._id),
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    await openDemandOrderAssignments(this, rowItem);
                    await this.$.router.refresh();
                },
            },
            ui.menuSeparator(),
            {
                icon: 'three_boxes',
                title: 'Projected stock',
                id: 'projectedStock',
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    await this.$.dialog.page(
                        '@sage/xtrem-stock-data/ProjectedStockDialog',
                        {
                            item: JSON.stringify(rowItem.item),
                            site: JSON.stringify(this.stockSite.value),
                        },
                        {
                            rightAligned: false,
                            size: 'extra-large',
                            resolveOnCancel: true,
                        },
                    );
                },
                isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    return !rowItem?.item?.isStockManaged;
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                id: 'dimensions',
                async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            {
                                editable:
                                    !isPurchaseOrderLineActionDisabled(
                                        this.status.value ?? '',
                                        rowItem.status ?? '',
                                        'dimensions',
                                        this.approvalStatus.value ?? '',
                                    ) || this.isRepost,
                            },
                        ),
                    );
                },
            },
            {
                title: 'Price list',
                id: 'priceList',
                icon: 'none',
                async onClick(_id, recordValue) {
                    if (recordValue.quantity && recordValue.item) {
                        await openItemSupplierPriceList(this, {
                            itemId: recordValue.item._id ?? '',
                            supplierId: this.businessRelation.value?._id ?? '',
                            siteId: this.site.value?._id ?? '',
                            itemName: recordValue.item.name ?? '',
                            orderDate: this.orderDate.value ?? '',
                        });
                    }
                },
                isHidden() {
                    return this.isRepost;
                },
            },
            {
                icon: 'none',
                id: 'taxDetails',
                title: 'Tax details',
                isHidden(_rowId, rowItem) {
                    return !rowItem.uiTaxes || this.isRepost;
                },
                async onClick(_rowId, rowItem) {
                    if (rowItem.uiTaxes) {
                        await this.callDisplayTaxes(rowItem);
                    }
                },
            },
            {
                icon: 'none',
                title: 'Landed costs',
                id: 'landedCosts',
                isHidden(_rowId, rowItem) {
                    return (
                        !this.$.isServiceOptionEnabled('landedCostOption') ||
                        !rowItem.actualLandedCostInCompanyCurrency ||
                        this.isRepost
                    );
                },
                async onClick(rowId) {
                    await this.displayLandedCosts(rowId);
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                id: 'delete',
                isDestructive: true,
                isHidden(_recordId, rowItem) {
                    return this._isLineDisabled(rowItem) || this.isRepost;
                },
                isDisabled(_value, rowData) {
                    return isPurchaseOrderLineActionDisabled(
                        this.status.value ?? '',
                        rowData.status ?? '',
                        'delete',
                        this.approvalStatus.value ?? '',
                        this.isRepost,
                    );
                },
                async onClick(rowId, rowItem) {
                    await this.deleteLine(rowId, rowItem);
                },
            },
        ],
        headerBusinessActions() {
            return setOrderOfPageTableHeaderBusinessActions({
                actions: [this.selectFromRequisition],
            });
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                isTitleHidden: true,
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({
                bind: 'itemDescription',
                title: 'Description',
                canFilter: false,
            }),
            line2Right: ui.nestedFields.numeric({
                bind: 'amountIncludingTax',
                title: 'Amount',
                canFilter: false,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (+(recordValue?._id ?? '') < 0) {
                    return ui.localize('@sage/xtrem-purchasing/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'locked',
                    title: 'Close',
                    id: 'close',

                    isDisabled(_recordId, rowItem) {
                        return rowItem.status === 'closed' || this.$.isDirty || this.isRepost;
                    },
                    isHidden(_recordId, rowItem) {
                        return (
                            isPurchaseOrderLineActionDisabled(
                                this.status.value ?? '',
                                rowItem.status ?? '',
                                'close',
                                this.approvalStatus.value ?? '',
                                this.isRepost,
                            ) ||
                            ['draft'].includes(this.status.value ?? '') ||
                            ['draft'].includes(rowItem?.status ?? '') ||
                            ['pendingApproval'].includes(this.approvalStatus.value ?? '')
                        );
                    },

                    async onClick(recordId, rowItem) {
                        if (!(await this.checkLineClosable(recordId, rowItem))) {
                            return;
                        }
                        await this.clickOnLineCloseButton(rowItem);
                    },
                },
                {
                    icon: 'refresh',
                    title: 'Update price',
                    id: 'updatePrice',
                    isHidden(rowId: string) {
                        return (
                            Number(rowId) <= 0 ||
                            !['draft', 'pendingApproval', 'confirmed'].includes(this.displayStatus.value ?? '')
                        );
                    },
                    async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                        if (Number(rowItem._id) > 0) {
                            await this.purchaseOrderUpdatePrice(rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'file_generic',
                    title: 'Assign order',
                    id: 'assignOrder',
                    isHidden: (_rowId, rowData) =>
                        !(isOrderToOrderServiceOptionActivated(rowData) && rowData.uDemandOrderLine?._id),
                    async onClick(_rowId, rowItem) {
                        await openDemandOrderAssignments(this, rowItem);
                        await this.$.router.refresh();
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'three_boxes',
                    title: 'Projected stock',
                    async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                        await this.$.dialog.page(
                            '@sage/xtrem-stock-data/ProjectedStockDialog',
                            {
                                item: JSON.stringify(rowItem.item),
                                site: JSON.stringify(this.stockSite.value),
                            },
                            {
                                rightAligned: false,
                                size: 'extra-large',
                                resolveOnCancel: true,
                            },
                        );
                    },
                    isHidden(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                        return !rowItem?.item?.isStockManaged;
                    },
                },
                ui.menuSeparator(),
                {
                    title: 'Dimensions',
                    id: 'dimensions',
                    icon: 'none',
                    async onClick(_id: string, recordValue: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(recordValue);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                { documentLine: rowData },
                                {
                                    editable:
                                        !isPurchaseOrderLineActionDisabled(
                                            this.status.value ?? '',
                                            recordValue.status ?? '',
                                            'dimensions',
                                            this.approvalStatus.value ?? '',
                                        ) || this.isRepost,
                                },
                            ),
                        );
                    },
                },
                {
                    title: 'Price list',
                    id: 'pricelist',
                    icon: 'none',
                    async onClick(_id, recordValue) {
                        if (recordValue.quantity && recordValue.item) {
                            await openItemSupplierPriceList(this, {
                                itemId: recordValue.item._id ?? '',
                                supplierId: this.businessRelation.value?._id ?? '',
                                siteId: this.site.value?._id ?? '',
                                itemName: recordValue.item.name ?? '',
                                orderDate: this.orderDate.value ?? '',
                            });
                        }
                    },
                    isHidden() {
                        return this.isRepost;
                    },
                },
                {
                    title: 'Tax details',
                    id: 'taxDetails',
                    icon: 'none',
                    isHidden(_id, recordValue) {
                        return !recordValue.uiTaxes || this.isRepost;
                    },
                    async onClick(_id, recordValue) {
                        if (recordValue.uiTaxes) {
                            await this.callDisplayTaxes(recordValue);
                        }
                    },
                },
                {
                    icon: 'none',
                    title: 'Landed costs',
                    id: 'landedCosts',
                    isHidden(_rowId, recordValue) {
                        return (
                            !this.$.isServiceOptionEnabled('landedCostOption') ||
                            !recordValue.actualLandedCostInCompanyCurrency ||
                            this.isRepost
                        );
                    },
                    async onClick(rowId) {
                        await this.displayLandedCosts(rowId);
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    id: 'delete',
                    isDestructive: true,
                    isHidden(_recordId, rowItem) {
                        return this._isLineDisabled(rowItem);
                    },
                    isDisabled(_recordId, rowData) {
                        return isPurchaseOrderLineActionDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'delete',
                            this.approvalStatus.value ?? '',
                            this.isRepost,
                        );
                    },
                    async onClick(rowId, rowItem) {
                        await this.deleteLine(rowId, rowItem);
                    },
                },
            ],

            headerQuickActions: [],

            async onRecordOpened(_id, recordValue) {
                this.purchaseReceiptLines.value = [];
                this.purchaseInvoiceLines.value = [];
                this.purchaseRequisitionLines.value = [];
                if (recordValue) {
                    this.currentSelectedLineId = recordValue._id;
                    refreshDialogAddress(
                        this,
                        recordValue._id,
                        recordValue as unknown as ExtractEdgesPartial<PurchaseOrderLineBinding>,
                    );
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    this.externalNoteLine.value = recordValue.externalNote ? recordValue.externalNote.value : '';
                    this.isExternalNoteLine.value = recordValue.isExternalNote ?? false;
                    this.externalNoteLine.isDisabled = !recordValue?.isExternalNote;
                    this.stockSiteAddress.value =
                        this.stockSiteAddress.value === null
                            ? recordValue.stockSiteLinkedAddress
                            : recordValue.stockSiteAddress;
                    if (+recordValue._id > 0) {
                        await this.loadRelatedPurchaseRequisitionLines(recordValue._id, 1);
                        await this.loadRelatedPurchaseReceiptLines(recordValue._id, 1);
                        await this.loadRelatedPurchaseInvoiceLines(recordValue._id, 1);
                        this.oldQuantityInPurchaseUnit = parseFloat(recordValue.quantity);
                        this.oldQuantityInStockUnit = parseFloat(recordValue.quantityInStockUnit);
                    } else if (
                        this._defaultDimensionsAttributes.dimensions !== '{}' ||
                        this._defaultDimensionsAttributes.attributes !== '{}'
                    ) {
                        const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                            {
                                site: this.site.value,
                                currency: this.currency.value,
                                status: 'draft',
                                lineReceiptStatus: 'notReceived',
                                origin: 'direct',
                                stockSite: this.stockSite.value,
                            } as ui.PartialNodeWithId<PurchaseOrderLine>,
                            this._defaultDimensionsAttributes,
                        );
                        recordValue.storedAttributes = line.storedAttributes;
                        recordValue.storedDimensions = line.storedDimensions;
                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<PurchaseOrderLineBinding>,
                        );
                    }
                }
            },
            async onRecordConfirmed(_id, recordValue) {
                const purchaseOrderLine = recordValue as unknown as ExtractEdgesPartial<PurchaseOrderLineBinding>;
                // get the purchaseRequisitionLine corresponding to the last quantity update modification
                if (purchaseOrderLine) {
                    if (
                        purchaseOrderLine.purchaseRequisitionLines &&
                        purchaseOrderLine.purchaseRequisitionLines.length
                    ) {
                        purchaseOrderLine.purchaseRequisitionLines = purchaseOrderLine.purchaseRequisitionLines.filter(
                            (orderLine, index, array) =>
                                index ===
                                array.findIndex(
                                    line =>
                                        line._id === orderLine._id &&
                                        String(line.orderedQuantity) === String(recordValue?.quantity),
                                ),
                        );
                    }
                    purchaseOrderLine.internalNote = {
                        value: this.internalNoteLine.value ? this.internalNoteLine.value : '',
                    };
                    purchaseOrderLine.externalNote = {
                        value: this.externalNoteLine.value ? this.externalNoteLine.value : '',
                    };
                    purchaseOrderLine.isExternalNote = this.isExternalNoteLine.value ?? false;
                    purchaseOrderLine.stockSiteLinkedAddress = this.stockSiteLinkedAddress
                        .value as unknown as ExtractEdgesPartial<BusinessEntityAddress>;
                    purchaseOrderLine.stockSiteAddress = this.stockSiteAddress
                        .value as unknown as ExtractEdgesPartial<Address>;
                    this.lines.addOrUpdateRecordValue(purchaseOrderLine);
                }
                await TotalTaxCalculator.getInstance().updateTaxDetails(
                    this.taxes,
                    this.totalTaxAmountAdjusted,
                    this.totalTaxAmount,
                );
                computeTotalAmounts(this);
                this.setEarliestExpectedDate(false);
                refreshTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                if (+_id < 0) {
                    this.updateTileContainerValues(true);
                }
            },
            onRecordDiscarded() {
                this.purchaseReceiptLines.value = [];
                this.purchaseInvoiceLines.value = [];
                this.purchaseRequisitionLines.value = [];
            },
            layout() {
                return {
                    general: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            statusBlock: { fields: ['lineReceiptStatus', 'lineInvoiceStatus'] },
                            mainBlock: { fields: ['item', 'origin', 'itemDescription'] },
                            siteBlock: { fields: ['stockSite', 'expectedReceiptDate'] },
                            purchaseBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_purchase',
                                    'Purchase',
                                ),
                                fields: ['unit', 'quantity'],
                            },
                            stockBlock: {
                                isHidden: (_id, recordValue) => !recordValue?.item?.isStockManaged,
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: { fields: ['grossPrice', 'discount', 'charge', 'netPrice'] },
                            mainBlock2: { fields: ['priceOrigin'] },
                            totals: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['amountExcludingTax', 'taxAmount', 'amountIncludingTax'],
                            },
                            totals2: {
                                fields: [
                                    'amountExcludingTaxInCompanyCurrency',
                                    'amountIncludingTaxInCompanyCurrency',
                                    'actualLandedCostInCompanyCurrency',
                                ],
                            },
                        },
                    },
                    address: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_address', 'Address'),
                        blocks: {
                            mainBlock: { fields: [this.stockSiteLinkedAddress, this.stockSiteAddress] },
                        },
                    },
                    requisitions: {
                        async onActive(_id) {
                            await this.loadRelatedPurchaseRequisitionLines(_id, 1);
                            this.filterOrderingInProgressRequisitionLines(_id);
                            this.showHideRequisitionsSection();
                        },
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return this.purchaseRequisitionLines.value.length === 0;
                        },
                        blocks: { requisitionBlock: { fields: [this.purchaseRequisitionLines] } },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_progress', 'Progress'),
                        isHidden() {
                            return (
                                this.purchaseReceiptLines.value.length === 0 &&
                                this.purchaseInvoiceLines.value.length === 0
                            );
                        },
                        async onActive(_id) {
                            if (this.purchaseInvoiceLines.value.length > 0) {
                                await this.loadRelatedPurchaseInvoiceLines(_id);
                            }
                            if (this.purchaseReceiptLines.value.length > 0) {
                                await this.loadRelatedPurchaseReceiptLines(_id);
                            }
                        },
                        blocks: {
                            invoiceBlock: {
                                isHidden() {
                                    return this.purchaseInvoiceLines.value.length === 0;
                                },
                                fields: ['lineInvoiceStatus', this.purchaseInvoiceLines],
                            },
                            receiptBlock: {
                                isHidden() {
                                    return this.purchaseReceiptLines.value.length === 0;
                                },
                                fields: ['lineReceiptStatus', this.purchaseReceiptLines],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: {
                            notesBlock: {
                                fields: [this.internalNoteLine, this.isExternalNoteLine, this.externalNoteLine],
                            },
                        },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<PurchaseOrderLineBinding>;

    /**
     * Calls the getDefaults query for a line for the current page's values
     * @param line
     * @returns  { stockSiteLinkedAddress?: BusinessEntityAddress , stockSiteAddress?: Address }
     */
    async fetchDefaultStockSiteAddress(line: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        const valuesProcessed: Dict<any> = {};
        const { values } = this.$;
        Object.keys(values).forEach(key => {
            if (Number(values[key]) && typeof values[key] === 'object') {
                valuesProcessed[key] = Number(values[key]); // convert decimals to strings because of errors
            } else {
                valuesProcessed[key] = values[key];
            }
        });

        const result = await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .getDefaults(
                {
                    lines: {
                        query: {
                            edges: {
                                node: {
                                    stockSiteAddress: {
                                        country: { name: true, id: true, _id: true, regionLabel: true, zipLabel: true },
                                        _id: true,
                                        name: true,
                                        locationPhoneNumber: true,
                                        addressLine1: true,
                                        addressLine2: true,
                                        city: true,
                                        region: true,
                                        postcode: true,
                                        concatenatedAddress: true,
                                    },
                                    stockSiteLinkedAddress: {
                                        businessEntity: { id: true, _id: true },
                                        country: { name: true, id: true, _id: true, regionLabel: true, zipLabel: true },
                                        _id: true,
                                        isActive: true,
                                        name: true,
                                        addressLine1: true,
                                        addressLine2: true,
                                        city: true,
                                        region: true,
                                        postcode: true,
                                        locationPhoneNumber: true,
                                        concatenatedAddress: true,
                                    },
                                    _id: true,
                                },
                            },
                        },
                    },
                },
                {
                    data: {
                        ...valuesProcessed,
                        lines: [
                            {
                                _id: line?._id,
                                site: line?.site?._id ?? null,
                                stockSite: line?.stockSite?._id ?? null,
                                status: line?.status,
                                lineReceiptStatus: line?.lineReceiptStatus,
                            },
                        ],
                    },
                },
            )
            .execute();

        let updatedStockAddress: {
            stockSiteLinkedAddress?: ExtractEdgesPartial<BusinessEntityAddress>;
            stockSiteAddress?: ExtractEdgesPartial<Address>;
        } | null = null;
        if (result) {
            updatedStockAddress =
                (result.lines.query.edges as { node: ui.PartialCollectionValue<PurchaseOrderLineBinding> }[])
                    .filter(l => l.node._id === line._id)
                    .shift()?.node || null;
        }
        return updatedStockAddress;
    }

    @ui.decorators.block<PurchaseOrder>({
        title: 'Financial',
        isTitleHidden: true,
        parent() {
            return this.financialSection;
        },
        width: 'extra-large',
        isDisabled() {
            return this.status.value === 'closed' || this.isRepost;
        },
    })
    financialBlock: ui.containers.Block;

    @ui.decorators.labelField<PurchaseOrder>({
        parent() {
            return this.financialBlock;
        },
        title: 'Invoice status',
        optionType: '@sage/xtrem-purchasing/PurchaseOrderInvoiceStatus',
        style() {
            return PillColorPurchase.getLabelColorByStatus('PurchaseOrderInvoiceStatus', this.invoiceStatus.value);
        },
    })
    invoiceStatus: ui.fields.Label<PurchaseOrderInvoiceStatus>;

    @ui.decorators.labelField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax calculation status',
        bind: 'taxCalculationStatus',
        isHidden() {
            return !['notDone', 'inProgress'].includes(this.taxCalculationStatus.value ?? '');
        },
        optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
        style() {
            return PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', this.taxCalculationStatus.value);
        },
    })
    taxCalculationStatus: ui.fields.Label<TaxCalculationStatus>;

    @ui.decorators.checkboxField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Printed',
        isDisabled: true,
    })
    isPrinted: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Sent',
        isDisabled: true,
    })
    isSent: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        isHidden: true,
    })
    isOrderAssignmentLinked: ui.fields.Checkbox;

    @ui.decorators.referenceField<PurchaseOrder, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Currency',
        lookupDialogTitle: 'Select currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter() {
            return { isActive: true };
        },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this._applyCurrency();
            this.rateDescription.isHidden = isExchangeRateHidden(
                this.currency.value,
                this.site.value,
                this.businessRelation.value,
            );
            this.showHideColumns();
        },
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.numericField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.site.value, this.businessRelation.value);
        },
    })
    rateDescription: ui.fields.Text;

    @ui.decorators.referenceField<PurchaseOrder, PaymentTerm>({
        parent() {
            return this.informationBlock;
        },
        lookupDialogTitle: 'Select payment term',
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'description', title: 'Description' }),
        ],
        isReadOnly() {
            return this.isRepost;
        },
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.referenceField<PurchaseOrder, DeliveryMode>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/DeliveryMode',
        tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
        title: 'Delivery mode',
        valueField: { name: true },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select delivery mode',
        isAutoSelectEnabled: true,
        helperTextField: 'id',
        columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.technical({ bind: 'description' })],
        isReadOnly() {
            return this.isRepost;
        },
    })
    deliveryMode: ui.fields.Reference<DeliveryMode>;

    @ui.decorators.referenceField<PurchaseOrder, User>({
        parent() {
            return this.informationBlock;
        },
        title: 'Default buyer',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        minLookupCharacters: 3,
        lookupDialogTitle: 'Select default buyer',
        helperTextField: 'email',
        width: 'large',
        isMandatory: true,
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        isReadOnly() {
            return this.isRepost;
        },
    })
    defaultBuyer: ui.fields.Reference<User>;

    private _setSiteAddress() {
        const saveSiteAddressId = this.siteAddress.value?._id;
        this.siteAddress.value = {
            ...this.siteBusinessEntityAddress.value,
            businessEntityAddress: { ...this.siteBusinessEntityAddress.value },
        };
        this.siteAddress.value._id = saveSiteAddressId;
        this.siteAddress.value.concatenatedAddress = getConcatenatedAddress(this.siteBusinessEntityAddress.value ?? {});
    }

    @ui.decorators.referenceField<PurchaseOrder, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Site address',
        valueField: 'name',
        isReadOnly: true,
        isHidden: true,
        isTransient: true,
        filter() {
            return { businessEntity: { _id: this.site.value?.businessEntity?._id } };
        },
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<PurchaseOrder, BusinessEntityAddress, Country>({
                bind: 'country',
                node: '@sage/xtrem-structure/Country',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<PurchaseOrder, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        onChange() {
            this._setSiteAddress();
        },
    })
    siteBusinessEntityAddress: ui.fields.Reference<BusinessEntityAddress>;

    async fetchDefaultsFromSiteAddress() {
        await this.$.fetchDefaults(['siteAddress']);
    }

    @ui.decorators.vitalPodField<PurchaseOrder, Address & { businessEntityAddress: BusinessEntityAddress }>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Site address',
        width: 'small',
        bind: 'siteAddress',
        addButtonText: 'Add site address',
        isReadOnly: true,
        onAddButtonClick() {
            return PurchaseOrder.getAddressDetailFromAddress(this.siteBusinessEntityAddress.value ?? {});
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                id: 'replace',
                onClick() {
                    this.siteBusinessEntityAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value !== 'draft';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                id: 'edit',
                onClick() {
                    this.siteAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value !== 'draft' || !this.siteAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                id: 'readOnly',
                onClick() {
                    this.siteAddress.isReadOnly = true;
                    if (this.siteAddress.value) {
                        this.siteAddress.value.concatenatedAddress = getConcatenatedAddress(
                            this.siteAddress.value ?? {},
                        );
                    }
                },
                isDisabled() {
                    return this.status.value !== 'draft' || this.siteAddress.isReadOnly;
                },
            },
        ],

        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.siteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return !this.siteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.siteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.siteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.siteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.siteAddress.value);
                },
                isHidden() {
                    return this.siteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.siteAddress.value);
                },
                isHidden() {
                    return this.siteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.siteAddress.isReadOnly;
                },
            }),
        ],
    })
    siteAddress: ui.fields.VitalPod<Address & { businessEntityAddress: BusinessEntityAddress }>;

    static getAddressDetailFromAddress(address: ExtractEdgesPartial<BusinessEntityAddress>) {
        if (address) {
            const { ...values } = { ...address };
            delete values.businessEntity;
            values.concatenatedAddress = getConcatenatedAddress(values);
            return { ...values, _id: null };
        }
        return {};
    }

    @ui.decorators.referenceField<PurchaseOrder, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Supplier address',
        valueField: 'name',
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name', title: 'Name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<PurchaseOrder, BusinessEntityAddress, Country>({
                bind: 'country',

                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<PurchaseOrder, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        filter() {
            if (this.businessRelation.value) {
                return { isActive: true, businessEntity: { id: this.businessRelation.value?.businessEntity?.id } };
            }
            return { isActive: true };
        },
        async onChange() {
            await this.fetchDefaultsFromSupplierAddress();
        },
    })
    supplierLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    async fetchDefaultsFromSupplierAddress() {
        await this.$.fetchDefaults(['supplierAddress']);
    }

    @ui.decorators.vitalPodField<PurchaseOrder, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Supplier address',
        width: 'small',
        bind: 'supplierAddress',
        addButtonText: 'Add supplier address',
        onAddButtonClick() {
            return PurchaseOrder.getAddressDetailFromAddress(this.supplierLinkedAddress.value ?? {});
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                id: 'replace',
                onClick() {
                    this.supplierLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value !== 'draft';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                id: 'edit',
                onClick() {
                    this.supplierAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value !== 'draft' || !this.supplierAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                id: 'readOnly',
                onClick() {
                    this.supplierAddress.isReadOnly = true;
                    if (this.supplierAddress.value) {
                        this.supplierAddress.value.concatenatedAddress = getConcatenatedAddress(
                            this.supplierAddress.value ?? {},
                        );
                    }
                },
                isDisabled() {
                    return this.status.value !== 'draft' || this.supplierAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.supplierAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return this.supplierAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.supplierAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.supplierAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.supplierAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.supplierAddress.value);
                },
                isHidden() {
                    return this.supplierAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.supplierAddress.value);
                },
                isHidden() {
                    return this.supplierAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.select({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.supplierAddress.isReadOnly === true;
                },
            }),
        ],
    })
    supplierAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.block<PurchaseOrder>({
        title: 'Totals',
        isTitleHidden: true,
        width: 'extra-large',
        parent() {
            return this.totalsSection;
        },
    })
    totalsBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseOrder>({
        parent() {
            return this.totalsBlock;
        },
        title: 'Excluding tax',
        unit() {
            return this.currency?.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseOrder>({
        title: 'Tax',
        parent() {
            return this.totalsBlock;
        },
        unit() {
            return this.currency?.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseOrder>({
        title: 'Total tax adjusted',
        parent() {
            return this.totalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isHidden: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseOrder>({
        parent() {
            return this.totalsBlock;
        },
        title: 'Including tax',
        unit() {
            return this.currency?.value;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.tableField<PurchaseOrder, DocumentTaxBinding>({
        title: 'Summary by tax',
        isFullWidth: true,
        canSelect: false,
        pageSize: 10,
        isReadOnly: true,
        node: '@sage/xtrem-tax/DocumentTax',
        orderBy: { _sortValue: +1, tax: +1 },
        parent() {
            return this.totalsSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({ title: 'Category', bind: 'taxCategory' }),
            ui.nestedFields.text({ title: 'Tax', bind: 'tax' }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                bind: 'taxableAmount',
                scale: null,
                unit() {
                    return this.currency?.value;
                },
            }),
            ui.nestedFields.numeric({ title: 'Rate', postfix: '%', scale: 2, bind: 'taxRate' }),
            ui.nestedFields.numeric({
                title: 'Amount',
                bind: 'taxAmount',
                scale: null,
                unit() {
                    return this.currency?.value;
                },
            }),
            ui.nestedFields.technical({ bind: 'taxAmountAdjusted' }),
            ui.nestedFields.checkbox({ title: 'Reverse charge', bind: 'isReverseCharge', size: 'small' }),
        ],
    })
    taxes: ui.fields.Table<DocumentTax>;

    @ui.decorators.block<PurchaseOrder>({
        title: 'Amounts company currency',
        parent() {
            return this.totalsSection;
        },
        width: 'extra-large',
    })
    amountsInCompanyCurrencyBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseOrder>({
        parent() {
            return this.amountsInCompanyCurrencyBlock;
        },
        title: 'Excluding tax',
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseOrder>({
        parent() {
            return this.amountsInCompanyCurrencyBlock;
        },
        title: 'Including tax',
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    totalAmountIncludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.landedCostsSection;
        },
        title: 'Total in company currency',
        width: 'large',
    })
    landedCostsSectionBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseOrder>({
        title: 'Actual landed costs',
        isTransient: true,
        parent() {
            return this.landedCostsSectionBlock;
        },
        unit() {
            return this.companyCurrency?.value;
        },
        isReadOnly: true,
    })
    totalActualLandedCostsInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.textField<PurchaseOrder>({ isHidden: true, bind: 'jsonAggregateLandedCostTypes' })
    jsonAggregateLandedCostTypes: ui.fields.Text;

    @ui.decorators.tableField<PurchaseOrder, LandedCostInterfaces.LandedCostTypeSummaryLineBinding>({
        parent() {
            return this.landedCostsSection;
        },
        canSelect: false,
        title: 'Summary by landed cost type',
        isFullWidth: true,
        isTransient: true,
        isReadOnly: true,
        pageSize: 10,
        orderBy: { landedCostType: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Type',
                bind: 'landedCostType',
                optionType: '@sage/xtrem-landed-cost/LandedCostType',
            }),
            ui.nestedFields.numeric({
                title: 'Actual cost amount in company currency',
                bind: 'actualCostAmountInCompanyCurrency',
                prefix() {
                    return this.companyCurrency.value?.symbol ?? '';
                },
                scale() {
                    return this.companyCurrency.value?.decimalDigits ?? 2;
                },
            }),
            ui.nestedFields.numeric({
                title: 'Actual allocated cost amount in company currency',
                bind: 'actualAllocatedCostAmountInCompanyCurrency',
                prefix() {
                    return this.companyCurrency.value?.symbol ?? '';
                },
                scale() {
                    return this.companyCurrency.value?.decimalDigits ?? 2;
                },
            }),
        ],
    })
    landedCosts: ui.fields.Table<LandedCostInterfaces.LandedCostTypeSummaryLineBinding>;

    @ui.decorators.referenceField<PurchaseOrder, BusinessEntityAddress & { orderLineId: string }>({
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        valueField: '_id',
        title: 'Receiving site address',
        isTransient: true,
        isReadOnly: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'concatenatedAddress' }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1', width: 'large' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2', width: 'large' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference({
                bind: 'country',

                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.select({
                        bind: 'regionLabel',
                        title: 'Region label',
                        optionType: '@sage/xtrem-structure/regionLabel',
                        isHidden: true,
                    }),
                    ui.nestedFields.select({
                        bind: 'zipLabel',
                        title: 'ZIP label',
                        optionType: '@sage/xtrem-structure/zipLabel',
                        isHidden: true,
                    }),
                ],
            }),
        ],
        onChange() {
            this.stockSiteAddress.value = getAddressDetail(this.stockSiteLinkedAddress.value ?? {});
        },
        // Need to add filtering on addresses as this property is transient
        filter() {
            const relatedOrderLine = this.lines.getRecordValue(this.currentSelectedLineId);
            if (relatedOrderLine?.stockSite?.businessEntity?._id) {
                return { businessEntity: { _id: relatedOrderLine?.stockSite?.businessEntity._id } };
            }

            if (this.stockSite?.value?.businessEntity) {
                // If no receiving site set yet, we filter on addresses for stock sites of the same company as the purchase site
                return { businessEntity: { _id: this.stockSite?.value?.businessEntity._id } };
            }
            return {};
        },
    })
    stockSiteLinkedAddress: ui.fields.Reference<BusinessEntityAddress & { orderLineId: string }>;

    @ui.decorators.vitalPodField<PurchaseOrder, Address & { orderLineId: string }>({
        node: '@sage/xtrem-master-data/Address',
        title: 'Receiving site address',
        width: 'large',
        isTransient: true,
        addButtonText: 'Add site address',
        onAddButtonClick() {
            if (this.stockSiteLinkedAddress.value?.orderLineId) {
                const relatedOrderLine = this.lines.getRecordValue(this.stockSiteLinkedAddress.value?.orderLineId);
                return PurchaseOrder.getAddressDetailFromAddress(relatedOrderLine?.stockSiteLinkedAddress ?? {});
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                id: 'replace',
                onClick() {
                    this.stockSiteLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value !== 'draft';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                id: 'edit',
                onClick() {
                    this.stockSiteAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value !== 'draft' || !this.stockSiteAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                id: 'readOnly',
                onClick() {
                    this.stockSiteAddress.isReadOnly = true;
                    if (this.stockSiteAddress.value) {
                        this.stockSiteAddress.value.concatenatedAddress = getConcatenatedAddress(
                            this.stockSiteAddress.value ?? {},
                        );
                    }
                },
                isDisabled() {
                    return this.status.value !== 'draft' || this.stockSiteAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.technical({ bind: 'orderLineId' }),
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.stockSiteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return !this.stockSiteAddress.isReadOnly;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', width: 'large', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.stockSiteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.stockSiteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.stockSiteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    const rowItem = this.lines.getRecordValue(this.currentSelectedLineId);
                    return getCountryRegionTitle(rowItem?.stockSiteAddress);
                },
                isHidden() {
                    return this.stockSiteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    const rowItem = this.lines.getRecordValue(this.currentSelectedLineId);
                    return getCountryPostCodeTitle(rowItem?.stockSiteAddress);
                },
                isHidden() {
                    return this.stockSiteAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.stockSiteAddress.isReadOnly === true;
                },
            }),
        ],
        onChange() {
            if (this.stockSiteAddress.value) {
                delete this.stockSiteAddress.value._id;
                this.stockSiteAddress.value.concatenatedAddress = getConcatenatedAddress(this.stockSiteAddress.value);
            }
        },
    })
    stockSiteAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.tableField<PurchaseOrder, PurchaseRequisitionLineToPurchaseOrderLine>({
        title: 'Purchase requisition line to purchase order line',
        isTitleHidden: true, // If you hide title all title of the podCollectionField will be hidden
        isTransient: true,
        isHidden: true,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseRequisitionLineToPurchaseOrderLine',
        orderBy: { _sortValue: +1 },
        onChange() {
            this.prevPurchaseRequisitionLines?.forEach(line => {
                const currentLine = this.purchaseRequisitionLines.getRecordValue(line._id);
                if (currentLine && currentLine.orderedQuantity !== line.orderedQuantity) {
                    const actualOnOrderQuantity = +line.orderedQuantity;
                    const newOnOrderQuantity = +(currentLine.orderedQuantity ?? 0);
                    const relatedOrderLine = this.lines.getRecordValue(currentLine.purchaseOrderLine?._id ?? '');
                    if (relatedOrderLine && relatedOrderLine.purchaseRequisitionLines) {
                        const relatedRequisitionLineIndex = (
                            relatedOrderLine.purchaseRequisitionLines as Dict<any>[]
                        ).findIndex(
                            prLine => prLine.purchaseRequisitionLine === currentLine.purchaseRequisitionLine?._id,
                        );
                        if (relatedRequisitionLineIndex !== -1) {
                            // If the current onOrderQuantity is greater than the quantityToOrder from the requisition line,
                            // We force the onOrderQuantity to the quantityToOrder
                            // and the remaining quantity to be ordered is set to 0 (done in the map event of that property)
                            const newAvailableQtyToOrder = +this.onChangeOrderedQuantityPurchaseRequisitionLine(
                                currentLine,
                                +this.orderedQuantityPurchaseRequisitionLine[+currentLine._id],
                            );
                            // Warning message displayed by the event on orderedQuantity property
                            if (+newAvailableQtyToOrder < 0) {
                                currentLine.orderedQuantity = String(
                                    +(currentLine.orderedQuantity ?? 0) + +newAvailableQtyToOrder,
                                );
                            }
                            // User enters a qty > remaining qty to order. This generates an order line qty for the new qty,
                            // but the requisition related ordered qty is forced to remaining qty to order.
                            // Now, if the user changes the req qty to < remaining qty to order, we have to reset the order line qty
                            relatedOrderLine.quantity =
                                +(relatedOrderLine.quantity ?? 0) >
                                +(
                                    relatedOrderLine.purchaseRequisitionLines[relatedRequisitionLineIndex]
                                        .orderedQuantity ?? 0
                                )
                                    ? relatedOrderLine.purchaseRequisitionLines[relatedRequisitionLineIndex]
                                          .orderedQuantity
                                    : relatedOrderLine.quantity;

                            relatedOrderLine.purchaseRequisitionLines[relatedRequisitionLineIndex].orderedQuantity =
                                currentLine.orderedQuantity;
                        }
                        relatedOrderLine.quantity = String(
                            Number.parseInt(relatedOrderLine.quantity ?? '', 10) -
                                (actualOnOrderQuantity - newOnOrderQuantity),
                        );
                        this.lines.setRecordValue(relatedOrderLine);
                    }
                    // updating the technical property that allows to detect changes
                    this.prevPurchaseRequisitionLines.splice(
                        this.prevPurchaseRequisitionLines.findIndex(elt => elt._id === line._id),
                        1,
                    );
                    this.prevPurchaseRequisitionLines.push(currentLine);
                    // The pod 'orderedQuantity' is not refreshed
                    this.purchaseRequisitionLines.setRecordValue(currentLine);
                }
            });

            this.manageDisplayButtonSelectFromPurchaseRequisitionLinesAction();
        },
        columns: [
            ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLineToPurchaseOrderLine, PurchaseOrderLine>({
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.link({
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                isTransient: true,
                title: 'Requisition',
                isFullWidth: true,
                map: (_value, rowData) => `${rowData.purchaseRequisitionLine.document.number}`,
                page: '@sage/xtrem-purchasing/PurchaseRequisition',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.purchaseRequisitionLine?.document?._id ?? '' };
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: { purchaseRequisitionLine: { status: true } },
                isTitleHidden: true,
                isTransient: true,
                map(value, rowData: ui.PartialCollectionValue<PurchaseRequisitionLineToPurchaseOrderLine>) {
                    switch (rowData.purchaseRequisitionLine?.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseRequisitionLine.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseRequisitionLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseRequisitionLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseRequisitionLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseRequisitionLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Requisition quantity',
                isFullWidth: true,
                bind: 'orderedQuantity',
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Available quantity to order',
                isFullWidth: true,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                isTransient: true,
                bind: '_id',
                map(rowId, rowData: ui.PartialCollectionValue<PurchaseRequisitionLineToPurchaseOrderLine>) {
                    return rowData.purchaseRequisitionLine?.status !== 'closed'
                        ? Math.max(
                              +(rowData.purchaseRequisitionLine?.quantity ?? 0) -
                                  (+(rowData.purchaseRequisitionLine?.orderedQuantity ?? 0) +
                                      +(this.orderedQuantityPurchaseRequisitionLine.length >= +rowId &&
                                      this.orderedQuantityPurchaseRequisitionLine[rowId]
                                          ? +(rowData.orderedQuantity ?? 0) -
                                            +this.orderedQuantityPurchaseRequisitionLine[rowId]
                                          : 0)),
                              0,
                          ).toString()
                        : '0';
                },
            }),
            ui.nestedFields.technical<
                PurchaseOrder,
                PurchaseRequisitionLineToPurchaseOrderLine,
                PurchaseRequisitionLine
            >({
                bind: 'purchaseRequisitionLine',
                node: '@sage/xtrem-purchasing/PurchaseRequisitionLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: { document: { requestDate: true } } }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.image({ bind: 'image' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'needByDate' }),
                    ui.nestedFields.technical({ bind: 'orderedQuantity' }),
                    ui.nestedFields.technical({ bind: 'quantityToOrder' }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLineToPurchaseOrderLine, UnitOfMeasure>({
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    purchaseRequisitionLines: ui.fields.Table<PurchaseRequisitionLineToPurchaseOrderLine>;

    prevPurchaseRequisitionLines: any[];

    /**
     *  orderedQuantity of the PurchaseRequisitionLine - Old Values
     */
    orderedQuantityPurchaseRequisitionLine: number[] = [];

    /**
     * PodCollection
     * purchaseRequisitionLine For creation
     */
    @ui.decorators.podCollectionField<PurchaseOrder, PurchaseRequisitionLineToPurchaseOrderLine>({
        title: 'Purchase requisition line to purchase order line',
        isTitleHidden: true, // If you hide title all title of the podCollectionField will be hidden
        isTransient: true,
        node: '@sage/xtrem-purchasing/PurchaseRequisitionLineToPurchaseOrderLine',
        orderBy: { _sortValue: +1 },
        onChange() {
            this.prevPurchaseRequisitionLineToPurchaseOrderLines.forEach(line => {
                const currentLine = this.purchaseRequisitionLineToPurchaseOrderLines.getRecordValue(line._id);
                if (currentLine && currentLine.orderedQuantity !== line.orderedQuantity) {
                    const actualOnOrderQuantity = +line.orderedQuantity;
                    const newOnOrderQuantity = +(currentLine.orderedQuantity ?? 0);
                    const relatedOrderLine = this.lines.getRecordValue(currentLine.purchaseOrderLine?._id ?? '');
                    if (relatedOrderLine && relatedOrderLine.purchaseRequisitionLines) {
                        const relatedRequisitionLineIndex = (
                            relatedOrderLine.purchaseRequisitionLines as Dict<any>[]
                        ).findIndex(
                            prLine => prLine.purchaseRequisitionLine === currentLine.purchaseRequisitionLine?._id,
                        );
                        if (relatedRequisitionLineIndex !== -1) {
                            // If the current onOrderQuantity is greater than the quantityToOrder from the requisition line,
                            // We force the onOrderQuantity to the quantityToOrder
                            // and the remaining quantity to be ordered is set to 0 (done in the map event of that property)
                            const newAvailableQtyToOrder = +this.onChangeOrderedQuantityPurchaseRequisitionLine(
                                currentLine,
                                0,
                            );
                            // Warning message displayed by the event on orderedQuantity property
                            if (+newAvailableQtyToOrder < 0) {
                                currentLine.orderedQuantity = currentLine.purchaseRequisitionLine?.quantityToOrder;
                            }
                            // User enters a qty > remaining qty to order. This generates an order line qty for the new qty,
                            // but the requisition related ordered qty is forced to remaining qty to order.
                            // Now, if the user changes the req qty to < remaining qty to order, we have to reset the order line qty
                            relatedOrderLine.quantity =
                                +(relatedOrderLine.quantity ?? 0) >
                                +(
                                    relatedOrderLine.purchaseRequisitionLines[relatedRequisitionLineIndex]
                                        .orderedQuantity ?? 0
                                )
                                    ? relatedOrderLine.purchaseRequisitionLines[relatedRequisitionLineIndex]
                                          .orderedQuantity
                                    : relatedOrderLine.quantity;
                            relatedOrderLine.purchaseRequisitionLines[relatedRequisitionLineIndex].orderedQuantity =
                                currentLine.orderedQuantity;
                        }
                        relatedOrderLine.quantity = String(
                            Number.parseInt(relatedOrderLine.quantity ?? '', 10) -
                                (actualOnOrderQuantity - newOnOrderQuantity),
                        );
                        this.lines.setRecordValue(relatedOrderLine);
                    }
                    // updating the technical property that allows to detect changes
                    this.prevPurchaseRequisitionLineToPurchaseOrderLines.splice(
                        this.prevPurchaseRequisitionLineToPurchaseOrderLines.findIndex(elt => elt._id === line._id),
                        1,
                    );
                    this.prevPurchaseRequisitionLineToPurchaseOrderLines.push(currentLine);
                    // updating the technical property that contains all ordering in progress lines
                    // (used for filtering the ones related to the current order line when displaying)
                    this.allInProgressRequisitionLineToOrderLines.splice(
                        this.allInProgressRequisitionLineToOrderLines.findIndex(elt => elt._id === line._id),
                        1,
                    );
                    this.allInProgressRequisitionLineToOrderLines.push(currentLine);
                    // The pod 'orderedQuantity' is not refreshed
                    this.purchaseRequisitionLineToPurchaseOrderLines.setRecordValue(currentLine);
                }
            });
            this.manageDisplayButtonSelectFromPurchaseRequisitionLinesAction();
        },
        canAddRecord: false,
        canRemoveRecord: false,
        columns: [
            ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLineToPurchaseOrderLine, PurchaseOrderLine>({
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.link({
                title: 'Requisition',
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                isTransient: true,
                isFullWidth: true,
                // isHidden: true, // TODO: see why the link is not clickable when readOnly ???
                map: (_fieldValue, rowData) => `${rowData.purchaseRequisitionLine?.document?.number}`,
                page: '@sage/xtrem-purchasing/PurchaseRequisition',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.purchaseRequisitionLine?.document?._id ?? '' };
                },
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: { purchaseRequisitionLine: { status: true } },
                isTitleHidden: true,
                isTransient: true,
                map(value, rowData: ui.PartialCollectionValue<PurchaseRequisitionLineToPurchaseOrderLine>) {
                    switch (rowData.purchaseRequisitionLine?.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseRequisitionLine?.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseRequisitionLine?.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseRequisitionLine?.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseRequisitionLine?.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseRequisitionLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Requisition quantity',
                isFullWidth: true,
                bind: 'orderedQuantity',
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                onChange(_rowId, rowData: ui.PartialCollectionValue<PurchaseRequisitionLineToPurchaseOrderLine>) {
                    this.onChangeOrderedQuantityPurchaseRequisitionLine(rowData, 0);
                },
            }),
            ui.nestedFields.label({
                title: 'Available quantity to order',
                isFullWidth: true,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                bind: '_id',
                map: (_rowId, rowData) =>
                    rowData.purchaseRequisitionLine?.status !== 'closed'
                        ? Math.max(
                              +rowData.purchaseRequisitionLine.quantity -
                                  (+rowData.purchaseRequisitionLine.orderedQuantity + +rowData.orderedQuantity),
                              0,
                          ).toString()
                        : '0',
            }),
            ui.nestedFields.technical<
                PurchaseOrder,
                PurchaseRequisitionLineToPurchaseOrderLine,
                PurchaseRequisitionLine
            >({
                bind: 'purchaseRequisitionLine',
                node: '@sage/xtrem-purchasing/PurchaseRequisitionLine',
                isTransient: true,
                nestedFields: [
                    ui.nestedFields.technical({ bind: { document: { requestDate: true } } }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.image({ bind: 'image', isHidden: true }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical({ bind: 'orderedQuantity' }),
                    ui.nestedFields.technical({ bind: 'quantity' }),
                    ui.nestedFields.technical({ bind: 'quantityToOrder' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseRequisitionLineToPurchaseOrderLine, UnitOfMeasure>({
                bind: 'unit',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.progress({
                bind: 'orderedQuantityInStockUnit',
                isTransient: true,
                isHidden: true,
                // TODO: enhancement request for map event https://jira.sage.com/browse/XT-6322
                // map(value, rowData: ui.PartialCollectionValue<PurchaseRequisitionLineToPurchaseOrderLine>) {
                //     return rowData?.quantityInStockUnit ? (rowData.orderedQuantityInStockUnit * 100) / rowData.quantityInStockUnit : 0;
                // },
                color(value: number) {
                    // TODO: enhancement request for undefined rowData to fix
                    // value = rowData?.quantityInStockUnit ? (rowData.orderedQuantityInStockUnit * 100) / rowData.quantityInStockUnit : 0;
                    if (value < 30) {
                        return ui.tokens.colorsSemanticNegative500;
                    }
                    if (value < 50) {
                        return ui.tokens.colorsSemanticCaution500;
                    }
                    if (value > 90) {
                        return ui.tokens.colorsSemanticPositive500;
                    }
                    return ui.tokens.colorsUtilityMajor200;
                },
                title: 'Order progression',
            }),
        ],
    })
    purchaseRequisitionLineToPurchaseOrderLines: ui.fields.PodCollection<PurchaseRequisitionLineToPurchaseOrderLine>;

    /**
     * Technical property that allows to handle user changes for ordering in progress
     */
    prevPurchaseRequisitionLineToPurchaseOrderLines: any[];

    /**
     * Technical property that contains all ordering in progress lines
     * (used for filtering the ones related to the current order line when displaying)
     */
    allInProgressRequisitionLineToOrderLines: any[];

    /**
     *  Receipts Tab
     */
    @ui.decorators.tableField<PurchaseOrder, PurchaseOrderLineToPurchaseReceiptLine>({
        title: 'Purchase order line to purchase receipt line',
        isTitleHidden: true, // If you hide title all title of the podCollectionField will be hidden
        isTransient: true,
        isHidden: true,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseReceiptLine, PurchaseOrderLine>({
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseReceiptLine, PurchaseReceiptLine>({
                bind: 'purchaseReceiptLine',
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseOrder, PurchaseReceiptLine, PurchaseReceipt>({
                        bind: 'document',
                        node: '@sage/xtrem-purchasing/PurchaseReceipt',
                        nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
                    }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseReceiptLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
                    ui.nestedFields.technical({ bind: 'amountExcludingTaxInCompanyCurrency' }),
                    ui.nestedFields.technical({ bind: 'status' }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseReceiptLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'quantity' }),
                    ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                    ui.nestedFields.image({ bind: { item: { image: true } }, isHidden: true }),
                ],
            }),
            ui.nestedFields.link({
                bind: '_id',
                title: 'Receipt number',
                isTransient: true,
                isFullWidth: true,
                map: (_fieldValue, rowData) => `${rowData.purchaseReceiptLine?.document?.number}`,
                page: '@sage/xtrem-purchasing/PurchaseReceipt',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.purchaseReceiptLine.document._id ?? '' };
                },
            }),
            ui.nestedFields.label({
                title: 'Receipt status',
                isTitleHidden: true,
                bind: { purchaseReceiptLine: { status: true } },
                isTransient: true,
                // TODO: https://jira.sage.com/browse/XT-719 (solution 1) enhancement request for map event
                map(value, rowData: ui.PartialCollectionValue<PurchaseOrderLineToPurchaseReceiptLine>) {
                    switch (rowData.purchaseReceiptLine?.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseReceiptLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseReceiptLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseReceiptLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                isFullWidth: true,
                bind: 'receivedQuantity',
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseReceiptLine, UnitOfMeasure>({
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseReceiptLine, UnitOfMeasure>({
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical({ bind: 'receivedQuantityInStockUnit', isTransient: true }),
        ],
    })
    purchaseReceiptLines: ui.fields.Table<PurchaseOrderLineToPurchaseReceiptLine>;

    @ui.decorators.tableField<PurchaseOrder, PurchaseOrderLineToPurchaseInvoiceLine>({
        title: 'Purchase order line to purchase invoice line',
        isTitleHidden: true, // If you hide title all title of the podCollectionField will be hidden
        isTransient: true,
        isHidden: true,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseInvoiceLine, PurchaseOrderLine>({
                bind: 'purchaseOrderLine',
                node: '@sage/xtrem-purchasing/PurchaseOrderLine',
                nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseInvoiceLine, PurchaseInvoiceLine>({
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseOrder, PurchaseInvoiceLine, PurchaseInvoice>({
                        bind: 'document',
                        node: '@sage/xtrem-purchasing/PurchaseInvoice',
                        nestedFields: [ui.nestedFields.technical({ bind: 'number' })],
                    }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseInvoiceLine, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
                    ui.nestedFields.technical<PurchaseOrder, PurchaseInvoiceLine, Item>({
                        bind: 'item',
                        node: '@sage/xtrem-master-data/Item',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'quantity' }),
                    ui.nestedFields.technical({ bind: 'quantityInStockUnit' }),
                    ui.nestedFields.technical({ bind: 'grossPrice' }),
                ],
            }),
            ui.nestedFields.link({
                bind: '_id',
                title: 'Invoice number',
                isFullWidth: true,
                map: (_fieldValue, rowData) => `${rowData.purchaseInvoiceLine?.document?.number}`,
                page: '@sage/xtrem-purchasing/PurchaseInvoice',
                queryParameters(_value, rowData) {
                    return { _id: rowData?.purchaseInvoiceLine?.document?._id ?? '' };
                },
            }),
            ui.nestedFields.label({
                title: 'Invoice status',
                isTitleHidden: true,
                bind: { purchaseInvoiceLine: { document: { status: true } } },
                map(value, rowData: ui.PartialCollectionValue<PurchaseOrderLineToPurchaseInvoiceLine>) {
                    switch (rowData.purchaseInvoiceLine?.document?.status) {
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_invoice_status__draft',
                                rowData.purchaseInvoiceLine.document.status,
                            );
                        case 'posted':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_invoice_status__posted',
                                rowData.purchaseInvoiceLine.document.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_invoice_status__inProgress',
                                rowData.purchaseInvoiceLine.document.status,
                            );
                        case 'error':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_invoice_status__error',
                                rowData.purchaseInvoiceLine.document.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseInvoiceLine?.document?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                isFullWidth: true,
                bind: 'invoicedQuantity',
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.purchaseInvoiceLine?.currency,
            }),
            ui.nestedFields.numeric({
                title: 'Unit price',
                bind: 'invoicedUnitPrice',
                isFullWidth: true,
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.purchaseInvoiceLine?.currency,
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseInvoiceLine, UnitOfMeasure>({
                bind: 'unit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseOrder, PurchaseOrderLineToPurchaseInvoiceLine, UnitOfMeasure>({
                bind: 'stockUnit',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    purchaseInvoiceLines: ui.fields.Table<PurchaseOrderLineToPurchaseInvoiceLine>;

    @ui.decorators.pageAction<PurchaseOrder>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.section<PurchaseOrder>({ isHidden: true, title: 'Request for approval' })
    requestApprovalSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.requestApprovalSection;
        },
    })
    requestApprovalBlock: ui.containers.Block;

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.requestApprovalBlock;
        },
        title: 'To',
        helperText: 'A request for approval email will be sent to this address',
        isTransient: true,
        isFullWidth: true,
    })
    emailAddressApproval: ui.fields.Text;

    @ui.decorators.buttonField<PurchaseOrder>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.requestApprovalBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__select_approver_button_text',
                'Select approver',
            );
        },
        async onClick() {
            await this.$.dialog
                .custom('info', this.approverSelectionBlock)
                .then(() => {
                    this.emailAddressApproval.value = this.selectedUser.value?.email
                        ? this.selectedUser.value?.email
                        : this.emailAddressApproval.value;
                })
                .finally(() => {
                    this.selectedUser.value = null;
                });
        },
    })
    selectApprover: ui.fields.Button;

    @ui.decorators.buttonField<PurchaseOrder>({
        isTransient: true,
        parent() {
            return this.requestApprovalBlock;
        },
        map: () =>
            ui.localize('@sage/xtrem-purchasing/pages__purchase_order__send_approval_request_button_text', 'Send'),
        async onClick() {
            if (!this.emailAddressApproval.value) {
                return;
            }
            const isSent = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.sendApprovalRequestMail(true, {
                    document: this.$.recordId ?? '',
                    user: { email: this.emailAddressApproval.value },
                })
                .execute();

            if (!isSent) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order_send__email_cannot_be_sent',
                        'The purchase order cannot be sent by email.',
                    ),
                );
            }

            this.$.showToast(ui.localize('@sage/xtrem-purchasing/pages__purchase_order__email_sent', 'Email sent.'), {
                type: 'success',
            });

            this.requestApprovalSection.isHidden = true;
            this.$.loader.isHidden = true;
            this.$.setPageClean();
            await this.$.refreshNavigationPanel();
            await this.$.router.refresh();
            this.$.finish();
        },
    })
    sendApprovalRequestButton: ui.fields.Button;

    @ui.decorators.block<PurchaseOrder>({ title: 'Select approver' })
    approverSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseOrder, User>({
        parent() {
            return this.approverSelectionBlock;
        },
        canSelect: false,
        title: 'User',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        isReadOnly: true,
        orderBy: { sortOrder: +1 } as any,
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.label({
                title: 'Approver',
                isTransient: true,
                bind: 'type' as any,
                backgroundColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_order__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'backgroundColor');
                },
                borderColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_order__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'borderColor');
                },
                color(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_order__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'textColor');
                },
            }),
            ui.nestedFields.technical({ bind: 'sortOrder' as any, isTransient: true }),
        ],
        onRowClick(_rowId, rowData) {
            this.selectedUser.value = rowData as unknown as ui.PartialNode<User>;
        },
    })
    users: ui.fields.Table<User>;

    @ui.decorators.referenceField<PurchaseOrder, User>({
        parent() {
            return this.approverSelectionBlock;
        },
        isReadOnly: true,
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'email',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
        ],
    })
    selectedUser: ui.fields.Reference<User>;

    @ui.decorators.section<PurchaseOrder>({ isHidden: true, title: 'Request changes' })
    requestChangesSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.requestChangesSection;
        },
    })
    requestChangesBlock: ui.containers.Block;

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.requestChangesBlock;
        },
        title: 'To',
        helperText: 'A request for changes will be sent to this address',
        isTransient: true,
        isFullWidth: true,
    })
    emailAddressChanges: ui.fields.Text;

    @ui.decorators.buttonField<PurchaseOrder>({
        isTransient: true,
        parent() {
            return this.requestChangesBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-purchasing/pages__purchase_order__send_change_request_button_text', 'Send');
        },
        async onClick() {
            if (!this.$.recordId || !this.emailAddressChanges.value) {
                return;
            }
            const isSend = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.sendRequestChangesMail(true, {
                    document: this.$.recordId,
                    user: { email: this.emailAddressChanges.value },
                })
                .execute();

            if (isSend) {
                this.$.showToast(
                    ui.localize('@sage/xtrem-purchasing/pages__purchase_order__email_sent', 'Email sent.'),
                    { type: 'success' },
                );
            } else {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__email_not_sent',
                        'Could not send request email.',
                    ),
                    { type: 'error' },
                );
            }
            this.$.setPageClean();
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, { _id: this.$.recordId ?? '' });
        },
    })
    sendChangeRequestButton: ui.fields.Button;

    async initPage() {
        const oldIsDirty = this.$.isDirty;

        this.siteAddress.isReadOnly = true;
        this.supplierAddress.isReadOnly = true;
        this.changeRequestedDescription.isHidden = this.approvalStatus.value !== 'changeRequested';
        this.changeRequestTextSection.isHidden = this.changeRequestedDescription.isHidden;

        if (this.currency.value) {
            this._applyCurrency();
        }
        if (this.purchaseRequisitionLineToPurchaseOrderLines.value?.length) {
            this.purchaseRequisitionLineToPurchaseOrderLines.value.forEach(elt =>
                this.purchaseRequisitionLineToPurchaseOrderLines.removeRecord(elt._id),
            );
        }

        this.purchaseRequisitionLineToPurchaseOrderLines.isHidden = true;

        if (this.$.recordId) {
            await this.loadRelatedDocuments();
            this.siteAddress.isDisabled = this.status.value !== 'draft';
            this.supplierAddress.isDisabled = this.status.value !== 'draft';

            this.setMinimumOrderValueTick();
            this.setEarliestExpectedDate(true);
        } else {
            this.orderDate.value = '';
            this.siteAddress.isDisabled = true;
            this.supplierAddress.isDisabled = true;
        }

        if (this.site.value && this.businessRelation.value) {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                site: this.site.value,
                supplier: this.businessRelation.value,
            });
        }
        this.allInProgressRequisitionLineToOrderLines = [];

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }

        this.showHideColumns();
    }

    disableHeaderFields() {
        if (!this.$.recordId) {
            const isReadOnly: boolean = this.lines.value.length > 0;
            this.site.isReadOnly = isReadOnly;
            this.businessRelation.isReadOnly = isReadOnly;
            this.orderDate.isReadOnly = isReadOnly;
        }
    }

    private setMinimumOrderValueTick() {
        if ((this.purchaseOrderValue?.value ?? 0) > Number(this.businessRelation.value?.minimumOrderAmount)) {
            this.minimumOrderAmount.icon = 'tick';
            this.minimumOrderAmount.iconColor = ui.tokens.colorsSemanticPositive600;
        } else {
            this.minimumOrderAmount.icon = undefined;
            this.minimumOrderAmount.iconColor = undefined;
        }
        this.updateTileContainerValues(false);
    }

    private setEarliestExpectedDate(init: boolean) {
        let earliestDate: string;
        if (!init) {
            earliestDate = this.getEarliestExpectedDate().toISOString().substring(0, 10);
            this.earliestExpectedDate.value = earliestDate;
        } else {
            earliestDate = this.earliestExpectedDate.value ?? '';
        }
        this.earliestExpectedDateTile.value = earliestDate
            ? ui.formatDateToCurrentLocale(earliestDate, 'LongMonthDayYear')
            : null;
        if (earliestDate > new Date(Date.now()).toISOString().substring(0, 10)) {
            this.earliestExpectedDateTile.icon = 'tick';
            this.earliestExpectedDateTile.iconColor = ui.tokens.colorsSemanticPositive600;
        } else {
            this.earliestExpectedDateTile.icon = undefined;
            this.earliestExpectedDateTile.iconColor = undefined;
        }
    }

    private getEarliestExpectedDate() {
        if (!this.lines.value.at(0)?.expectedReceiptDate) {
            return new Date(Date.now());
        }

        const firstExpectedDate = new Date(Date.parse(this.lines.value.at(0)?.expectedReceiptDate ?? ''));

        return this.lines.value.reduce(
            (expectedDate, line) =>
                expectedDate < new Date(Date.parse(line.expectedReceiptDate ?? ''))
                    ? expectedDate
                    : new Date(Date.parse(line.expectedReceiptDate ?? '')),
            firstExpectedDate,
        );
    }

    private _updateOrderLinesGridWithRelatedRequisitionLines() {
        const oldIsDirty = this.$.isDirty;
        // TODO: relate each requisition line to the corresponding order line (as done for picking)
        // we need to relate the requisition lines from an order line to the entry from the req lines to order lines
        this.purchaseRequisitionLines.value.forEach(reqLine => {
            const orderLine = this.lines.getRecordValue(reqLine.purchaseOrderLine?._id ?? '');
            if (orderLine) {
                if (
                    !orderLine.purchaseRequisitionLines?.find(
                        prLine => prLine.purchaseRequisitionLine === reqLine.purchaseRequisitionLine?._id,
                    )
                ) {
                    orderLine.purchaseRequisitionLines = orderLine.purchaseRequisitionLines ?? [];
                    orderLine.purchaseRequisitionLines.push({
                        purchaseRequisitionLine: reqLine.purchaseRequisitionLine?._id,
                        purchaseOrderLine: reqLine.purchaseOrderLine?._id,
                        orderedQuantity: reqLine.orderedQuantity,
                    } as any);
                    this.lines.addOrUpdateRecordValue(orderLine);
                }
            }
        });

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    private updatePurchaseOrderLines = (data: any) => {
        const values = data;

        values.stockSiteLinkedAddress = data.site.primaryAddress;
        values.stockSiteAddress = data.site.primaryAddress;
        values.site = this.site.value;
        if (!values.currency) {
            values.currency = this.currency.value;
        }
        values.quantity = data.quantityToOrder;
        values.unitToStockUnitConversionFactor = data.purchaseRequisitionLine.unitToStockUnitConversionFactor;
        values.stockSite = data.site;
        values.purchaseRequisitionLines = [
            {
                purchaseRequisitionLine: {
                    _id: values.purchaseRequisitionLine._id,
                    document: {
                        _id: values.purchaseRequisitionLine.document?._id,
                        number: values.document?.number,
                        isTransferHeaderNote: values.document?.isTransferHeaderNote,
                        isTransferLineNote: values.document?.isTransferLineNote,
                    },
                },
                orderedQuantity: data.quantityToOrder,
            },
        ];
        values.status = 'draft';
        values.lineReceiptStatus = 'notReceived';
        values.origin = 'purchaseRequisition';
        values.priceOrigin = data.priceOrigin;
        delete values._id;
        delete values._sortValue;
        delete values.supplier;
        delete values.itemImage;
        delete values.purchaseRequisitionLine;
        delete values.purchaseRequisition;
        delete values.document;
        delete values.requestedItemDescription;
        delete values.needByDate;
        delete values.quantityInStockUnit;
        delete values.orderedQuantity;
        delete values.orderedQuantityInStockUnit;
        delete values.quantityToOrder;
        delete values.quantityToOrderInStockUnit;
        delete values.site;
        values.internalNote = { value: '' };
        values.externalNote = { value: '' };
        values.isExternalNote = false;
        return values;
    };

    getSerializedValues() {
        const { $detailPanel, ...values } = this.$.values;
        delete values.totalAmountExcludingTax;
        delete values.totalAmountExcludingTaxInCompanyCurrency;
        delete values.companyFxRate;
        if (values.siteAddress && Number(values.siteAddress?._id) < 0) {
            delete values.siteAddress._id;
        }
        if (values.supplierAddress && Number(values.supplierAddress?._id) < 0) {
            delete values.supplierAddress._id;
        }
        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }
        this.getLineValues(values.lines);
        return values;
    }

    // eslint-disable-next-line class-methods-use-this
    private getLineValues = (lines: Dict<Partial<PurchaseOrderLineBinding>>[]): void =>
        lines?.forEach(line => {
            delete line.currency;
            delete line.amountExcludingTax;
            delete line.amountExcludingTaxInCompanyCurrency;
            delete line.taxAmountAdjusted;
            delete line.quantityInStockUnit;
            delete line.origin;
            delete line.siteAddress;
            if (+line._id > 0) {
                delete line.unitToStockUnitConversionFactor;
            }
            if (line.purchaseRequisitionLines) {
                (
                    line.purchaseRequisitionLines as unknown as Dict<
                        Partial<PurchaseRequisitionLineToPurchaseOrderLine>
                    >[]
                )
                    .filter(prLine => +prLine._id < 0)
                    .forEach(prLine => {
                        delete prLine.purchaseRequisitionLine._id;
                        delete prLine.orderedQuantityInStockUnit;
                        delete prLine._id;
                    });
            }
            if (line.stockSiteAddress?._id) {
                delete line.stockSiteAddress._id;
            }
        });

    async clickOnLineCloseButton(rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        if (
            await confirmDialogWithAcceptButtonText(
                this,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_order__line_close_action_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_order__line_close_action_dialog_content',
                    'You are about to close this purchase order line. This action cannot be undone.',
                ),
                ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
            )
        ) {
            this.$.loader.isHidden = false;
            try {
                await this.closeLine(rowItem);
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order_line_close_action_allowed_line_closed',
                        'The purchase order line is closed.',
                    ),
                    { type: 'success' },
                );
                await this.$.router.refresh();
            } catch (error) {
                await this.$.processServerErrors(error);
            }
            this.$.loader.isHidden = true;
        }
    }

    /** Runs the closeLine mutation with parameters   */
    closeLine = (row: ui.PartialCollectionValue<PurchaseOrderLineBinding>) =>
        this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .mutations.closeLine(true, { purchaseOrderLine: `_id:${row._id}` })
            .execute();

    async loadRelatedPurchaseRequisitionLines(orderLineId: string, first = 500) {
        const oldIsDirty = this.$.isDirty;
        const filter = {
            purchaseOrderLine: {
                document: { _id: { _eq: this.$.recordId } },
                ...(orderLineId ? { _id: orderLineId } : {}),
            },
        } as Filter<PurchaseRequisitionLineToPurchaseOrderLine>;

        const resultRequisitionLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisitionLineToPurchaseOrderLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseOrderLine: { _id: true, document: { _id: true } },
                            purchaseRequisitionLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true, requestDate: true },
                                status: true,
                                item: { _id: true, name: true, image: { value: true } },
                                quantity: true,
                                grossPrice: true,
                                netPrice: true,
                                discount: true,
                                totalTaxExcludedAmount: true,
                                charge: true,
                                unit: { id: true, symbol: true, decimalDigits: true },
                                currency: { id: true, decimalDigits: true, symbol: true },
                                orderedQuantity: true,
                                orderedQuantityInStockUnit: true,
                                quantityToOrder: true,
                                quantityToOrderInStockUnit: true,
                                needByDate: true,
                                discountCharges: {
                                    query: {
                                        edges: {
                                            node: {
                                                sign: true,
                                                value: true,
                                                basis: true,
                                                amount: true,
                                                calculationRule: true,
                                                valueType: true,
                                            },
                                        },
                                    },
                                },
                            },
                            orderedQuantity: true,
                            orderedQuantityInStockUnit: true,
                            unit: { _id: true, id: true, symbol: true, decimalDigits: true },
                            stockUnit: { _id: true, id: true, symbol: true, decimalDigits: true },
                        },
                        { filter, first },
                    ),
                )
                .execute(),
        );
        this.purchaseRequisitionLines.value = [];

        this.purchaseRequisitionLines.value = resultRequisitionLines.map(row => {
            // TODO: update the row orderedQuantity with the one from the related order line one (due to possible update in the meanwhile on the page)
            const orderLine = this.lines.getRecordValue(row.purchaseOrderLine._id);
            if (orderLine && orderLine.purchaseRequisitionLines) {
                const relatedReqLine = orderLine.purchaseRequisitionLines.find(
                    prLine =>
                        prLine.purchaseOrderLine?._id === row.purchaseOrderLine._id &&
                        prLine.purchaseRequisitionLine?._id === row.purchaseRequisitionLine._id,
                );
                if (relatedReqLine) {
                    row.orderedQuantity = String(relatedReqLine.orderedQuantity) ?? '';
                }
            }
            return row;
        });
        this.purchaseRequisitionLines.isHidden = this.purchaseRequisitionLines.value.length === 0;

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    filterOrderingInProgressRequisitionLines(orderLineId: string) {
        this.allInProgressRequisitionLineToOrderLines.forEach(line => {
            if (line.purchaseOrderLine._id !== orderLineId) {
                if (
                    this.purchaseRequisitionLineToPurchaseOrderLines.value.find(
                        progressLine => progressLine._id === line._id,
                    )
                ) {
                    this.purchaseRequisitionLineToPurchaseOrderLines.removeRecord(line._id);
                }
            } else {
                this.purchaseRequisitionLineToPurchaseOrderLines.addOrUpdateRecordValue(line);
            }
        });
    }

    private _deleteFromOrderingInProgressRequisitionLines(orderLineId: string) {
        this.allInProgressRequisitionLineToOrderLines = this.allInProgressRequisitionLineToOrderLines.filter(
            line => line.purchaseOrderLine._id !== orderLineId,
        );

        this.purchaseRequisitionLineToPurchaseOrderLines.value
            .filter(progressLine => progressLine.purchaseOrderLine?._id === orderLineId)
            .forEach(progressLine => this.purchaseRequisitionLineToPurchaseOrderLines.removeRecord(progressLine._id));
    }

    showHideRequisitionsSection() {
        this.purchaseRequisitionLines.isHidden = !this.purchaseRequisitionLines.value.length;
        this.purchaseRequisitionLineToPurchaseOrderLines.isHidden =
            !this.purchaseRequisitionLineToPurchaseOrderLines.value.length;
    }

    /**
     * @param previousQty : firstValue present in orderedQuantity ( for modification of OrderedQuantity in PurchaseRequisitionLine )
     * must be 0 when creating
     */
    private onChangeOrderedQuantityPurchaseRequisitionLine(
        rowData: ui.PartialCollectionValue<PurchaseRequisitionLineToPurchaseOrderLine>,
        previousQty: number,
    ) {
        const availableQuantityToOrder =
            +(rowData.purchaseRequisitionLine?.quantity ?? 0) -
            (+(rowData.purchaseRequisitionLine?.orderedQuantity ?? 0) - previousQty + +(rowData.orderedQuantity ?? 0));

        if (Number(availableQuantityToOrder) < 0) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisitionLine_orderedQuantity_to_much',
                    'You are ordering ({{alreadyOrdered}}{{unit}})  when the requisition was generated for {{quantityToOrdered}}{{unit}}.',
                    {
                        quantityToOrdered: rowData.purchaseRequisitionLine?.quantity,
                        alreadyOrdered:
                            +(rowData.purchaseRequisitionLine?.orderedQuantity ?? 0) +
                            +(rowData.orderedQuantity ?? 0) -
                            previousQty,
                        unit: rowData?.unit?.id,
                    },
                ),
                { type: 'warning' },
            );
        }
        return Number(availableQuantityToOrder);
    }

    async loadRelatedPurchaseReceiptLines(orderLineId: string, first = 500) {
        const oldIsDirty = this.$.isDirty;
        const filter: Filter<PurchaseOrderLineToPurchaseReceiptLine> = {
            purchaseOrderLine: { document: { _id: { _eq: this.$.recordId } } },
            ...(orderLineId ? { purchaseOrderLine: { _id: orderLineId } } : {}),
        };

        const resultReceiptLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseReceiptLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseOrderLine: { _id: true, document: { _id: true } },
                            purchaseReceiptLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true },
                                status: true,
                                item: { _id: true, name: true, image: { value: true } },
                                quantity: true,
                                quantityInStockUnit: true,
                                returnedQuantity: true,
                                returnedQuantityInStockUnit: true,
                                remainingQuantityToInvoice: true,
                                remainingQuantityInStockUnit: true,
                                unit: { id: true, symbol: true, decimalDigits: true },
                                currency: { id: true, decimalDigits: true, symbol: true },
                                grossPrice: true,
                                amountExcludingTax: true,
                                amountExcludingTaxInCompanyCurrency: true,
                            },
                            receivedQuantity: true,
                            receivedQuantityInStockUnit: true,
                            unit: { _id: true, id: true, symbol: true, decimalDigits: true },
                            stockUnit: { _id: true, id: true, symbol: true, decimalDigits: true },
                        },
                        { filter, first },
                    ),
                )
                .execute(),
        );
        this.purchaseReceiptLines.value = resultReceiptLines;
        this.purchaseReceiptLines.isHidden = this.purchaseReceiptLines.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    async loadRelatedPurchaseInvoiceLines(orderLineId: string, first = 500) {
        const oldIsDirty = this.$.isDirty;
        const filter: Filter<PurchaseOrderLineToPurchaseInvoiceLine> = {
            purchaseOrderLine: {
                document: { _id: { _eq: this.$.recordId } },
                ...(orderLineId ? { _id: orderLineId } : {}),
            },
        };

        const resultInvoiceLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrderLineToPurchaseInvoiceLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseOrderLine: { _id: true, document: { _id: true } },
                            invoicedQuantityInStockUnit: true,
                            invoicedQuantity: true,
                            invoicedUnitPrice: true,
                            unit: { _id: true, symbol: true },
                            stockUnit: { _id: true, symbol: true },
                            purchaseInvoiceLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true, status: true },
                                item: { _id: true, name: true },
                                quantity: true,
                                quantityInStockUnit: true,
                                unit: { id: true, symbol: true, decimalDigits: true },
                                currency: { id: true, decimalDigits: true, symbol: true },
                                grossPrice: true,
                                amountExcludingTax: true,
                            },
                        },
                        { filter, first },
                    ),
                )
                .execute(),
        );

        this.purchaseInvoiceLines.value = resultInvoiceLines;

        this.purchaseInvoiceLines.isHidden = this.purchaseInvoiceLines.value.length === 0;
        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    static showHidePanelProgress(isInvoiceLinesHidden: boolean, isReceiptLinesHidden: boolean) {
        return isInvoiceLinesHidden && isReceiptLinesHidden;
    }

    async setExpectedReceiptDate(item: string, site: string, needByDate?: string): Promise<string> {
        const purchaseLeadTime = await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrderLine')
            .queries.getPurchaseLeadTime(true, { item, site, supplier: this.businessRelation.value?._id ?? '' })
            .execute();
        const expectedReceiptDate = new Date(
            (this.orderDate.value ? Date.parse(this.orderDate.value) : Date.now()) +
                purchaseLeadTime * 24 * 60 * 60 * 1000,
        )
            .toISOString()
            .substring(0, 10);

        return needByDate && Date.parse(needByDate) > Date.parse(expectedReceiptDate)
            ? needByDate
            : expectedReceiptDate;
    }

    /**
     * @param rowData : Price can be 0. return - 1 when record not found.
     */
    private async _setPriceOrigin(
        rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>,
        isRecalculatePrices: boolean,
    ) {
        if (rowData?.item && Number(rowData?.quantity) >= 0 && rowData?.unit) {
            const purchasePrice = await getItemSupplierPrice(this.$.graph, {
                site: this.site.value ?? {},
                supplier: this.businessRelation.value ?? {},
                currency: this.currency.value ?? {},
                item: rowData.item,
                unit: rowData.unit ?? {},
                quantity: parseFloat(rowData.quantity ?? ''),
                date: new Date(Date.parse(this.orderDate.value ?? '')),
                doConvertUnit: false,
            });

            if (isRecalculatePrices && purchasePrice.price === 0) {
                rowData.grossPrice = '0';
                rowData.priceOrigin = null;
            } else if (purchasePrice.price !== 0) {
                rowData.grossPrice = String(purchasePrice.price);
                rowData.priceOrigin = purchasePrice.priceOrigin ?? 'manual';
            }
            this.lines.addOrUpdateRecordValue(rowData);
        }
    }

    /**
     *  ********************* ALL PAGES ACTIONS ******************************
     */

    @ui.decorators.pageAction<PurchaseOrder>({
        title: 'Print',
        icon: 'print',
        async onClick() {
            if (this.$.recordId) {
                await printPurchaseOrder({
                    page: this,
                    _id: this.$.recordId,
                    status: this.taxCalculationStatus.value ?? '',
                    number: '',
                });
            }
        },
    })
    print: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        title: 'Save',
        buttonType: 'primary',
        access: { bind: '$update' },
        onError(error) {
            this.$.loader.isHidden = true;
            return formatError(this, error);
        },
        async onClick() {
            if (this.saveButtonClicked) return;
            this.saveButtonClicked = true;
            if (!this.$.recordId) {
                this.isOverwriteNote.value = await setOverwriteNote(this, {
                    linesFromSingleDocument: this.linesFromSingleRequisition(),
                    headerNotesChanged: this.headerNotesChanged(),
                    lineNotesChanged: this.lineNotesChanged(),
                    isTransferHeaderNote: this.receiptIsTransferHeaderNote(),
                    isTransferLineNote: this.receiptsIsTransferLineNote(),
                });
            }

            this.purchaseRequisitionLineToPurchaseOrderLines.isHidden =
                !this.purchaseRequisitionLineToPurchaseOrderLines.value.length;

            if (this.isPrinted.value || this.isSent.value) {
                await this.showWarningUpdate();
                this.saveButtonClicked = false;
            } else {
                const isDialog = this.$.isInDialog;
                await this.$standardSaveAction.execute(true);
                this.saveButtonClicked = false;

                if (isDialog) return;
                if (this.$.recordId && this.status.value === 'draft') {
                    const financeIntegrationCheckResult = await this.$.graph
                        .node('@sage/xtrem-purchasing/PurchaseOrder')
                        .mutations.financeIntegrationCheck(
                            { wasSuccessful: true, message: true },
                            { purchaseOrder: this.$.recordId },
                        )
                        .execute();

                    if (!financeIntegrationCheckResult.wasSuccessful) {
                        this.$.showToast(
                            `**${ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_order__save_warnings',
                                'Warnings while saving:',
                            )}**\n${financeIntegrationCheckResult.message}`,
                            { type: 'warning', timeout: 10000 },
                        );
                    }
                }
                await this.loadRelatedDocuments();
            }
        },
    })
    save: ui.PageAction;

    linesFromSingleRequisition() {
        const requisitionLineNumbers = this.lines.value.map(
            line => line.purchaseRequisitionLines?.at(0)?.purchaseRequisitionLine?.document?.number,
        );
        return requisitionLineNumbers.every(number => number === requisitionLineNumbers[0]);
    }

    headerNotesChanged() {
        return this.internalNote.value !== '';
    }

    lineNotesChanged() {
        return this.lines.value.some(line => line.internalNote?.value);
    }

    receiptIsTransferHeaderNote() {
        return this.lines.value.some(
            line =>
                line.purchaseRequisitionLines?.at(0)?.purchaseRequisitionLine?.document?.isTransferHeaderNote === true,
        );
    }

    receiptsIsTransferLineNote() {
        return this.lines.value.some(
            line =>
                line.purchaseRequisitionLines?.at(0)?.purchaseRequisitionLine?.document?.isTransferLineNote === true,
        );
    }

    @ui.decorators.pageAction<PurchaseOrder>({
        icon: 'bin',
        title: 'Delete',
        isDestructive: true,
        onError() {
            this.$.loader.isHidden = true;
        },
        async onClick() {
            const isAssignmentChecked = await isPurchaseOrderAssignmentCheckedOnPurchaseOrderDelete(this);
            if (isAssignmentChecked) {
                await this.$standardDeleteAction.execute();
            }
        },
    })
    deletePurchaseOrder: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        icon: 'search',
        title: 'Add lines from requisitions',
        isHidden: true,
        async onClick() {
            await addLineFromRequisition(this);
        },
    })
    selectFromRequisition: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Submit for approval',
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed') {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order_submit_for_approval__tax_calculation_failed',
                        'You need to resolve tax calculation issues before submitting for approval.',
                    ),
                );
            }
            if (this.$.recordId && this.site.value?._id) {
                await this.submitForApproval({
                    siteId: this.site.value._id,
                    isGrossPriceMissing: this.isGrossPriceMissing.value ?? false,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    requestApproval: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Confirm',
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.confirmAction({
                    isCalledFromRecordPage: true,
                    purchaseOrderPage: this,
                    recordNumber: this.number.value,
                    isConfirmed: true,
                    recordId: this.$.recordId,
                    taxCalculationStatus: this.taxCalculationStatus.value,
                });
            }
        },
    })
    confirm: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            let saveOnly = false;

            if (this.sourceDocumentData.numberOfSourceDocuments > 1) {
                saveOnly = !(await confirmDialogToBoolean(
                    this.$.dialog.confirmation(
                        'warn',
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_order__repost', 'Repost'),
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_order__just_save',
                            'Do you want to repost now or after you edited dimensions on other source documents?',
                        ),
                        {
                            acceptButton: {
                                text: ui.localize('@sage/xtrem-purchasing/pages__purchase_order__repost', 'Repost'),
                            },
                            cancelButton: {
                                text: ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_order__save_only',
                                    'Save without posting',
                                ),
                            },
                        },
                    ),
                ));
            }

            const orderLines = this.lines.value
                .filter(line => Number(line._id) > 0)
                .map(line => ({
                    baseDocumentLineSysId: line._id,
                    storedAttributes: line.storedAttributes,
                    storedDimensions: line.storedDimensions,
                }));

            this.$.loader.isHidden = false;

            const postResult = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.repost(
                    { wasSuccessful: true, message: true },
                    {
                        purchaseOrder: this.$.recordId ?? '',
                        orderLines,
                        saveOnly,
                        ...(this.sourceDocumentData.financeTransactionSysId
                            ? { financeTransaction: this.sourceDocumentData.financeTransactionSysId }
                            : {}),
                    },
                )
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `** {ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_order__repost_errors',
                            'Errors occurred while reposting:',
                        )} **\n ${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Request changes',
        async onClick() {
            this.requestChangesSection.isHidden = false;
            try {
                await this.$.dialog.custom('info', this.requestChangesSection, {
                    cancelButton: { isHidden: true },
                    acceptButton: { isHidden: true },
                });
                // eslint-disable-next-line no-empty
            } catch {}
            this.requestChangesSection.isHidden = true;
        },
    })
    requestChanges: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Approve',
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.approveAction({
                    isCalledFromRecordPage: true,
                    purchaseOrderPage: this,
                    recordNumber: this.number.value,
                    isApproved: true,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    approve: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Reject',
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.rejectAction({
                    isCalledFromRecordPage: true,
                    purchaseOrderPage: this,
                    recordNumber: this.number.value,
                    isRejected: true,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    reject: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        isHidden: true,
        title: 'Create receipt',
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.createPurchaseReceiptAction({
                    isCalledFromRecordPage: true,
                    purchaseOrderPage: this,
                    recordNumber: this.number.value,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    createPurchaseReceipt: ui.PageAction;

    @ui.decorators.pageAction<PurchaseOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Close order',
        async onClick() {
            const isAssignmentChecked = await isPurchaseOrderAssignmentCheckedOnPurchaseOrderClose(this);
            if (isAssignmentChecked) {
                if (this.$.recordId) {
                    await actionFunctions.closeAction({
                        isCalledFromRecordPage: true,
                        purchaseOrderPage: this,
                        recordNumber: this.number.value ?? '',
                        recordId: this.$.recordId,
                    });
                }
            }
        },
    })
    close: ui.PageAction;

    // Send purchase order by email start here
    @ui.decorators.pageAction<PurchaseOrder>({
        isTransient: true,
        isHidden: true,
        title: 'Send',
        icon: 'email',
        async onClick() {
            this.sendEmailSection.isHidden = false;
            await this.loadContacts(this.supplierLinkedAddress.value?._id ?? '');
            await this.$.dialog.custom('info', this.sendEmailSection, {
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
                resolveOnCancel: true,
            });
            this.sendEmailSection.isHidden = true;
            this.$.setPageClean();
        },
    })
    sendEmail: ui.PageAction;

    @ui.decorators.section<PurchaseOrder>({ isHidden: true, title: 'Send purchase order' })
    sendEmailSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.sendEmailSection;
        },
        title: 'To',
    })
    sendEmailBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<PurchaseOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Title',
        optionType: '@sage/xtrem-master-data/Title',
        isTransient: true,
        isFullWidth: true,
    })
    emailTitle: ui.fields.DropdownList;

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'First name',
        maxLength: 30,
        isTransient: true,
        isFullWidth: true,
    })
    emailFirstName: ui.fields.Text;

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Last name',
        maxLength: 30,
        isMandatory: true,
        isTransient: true,
        isFullWidth: true,
    })
    emailLastName: ui.fields.Text;

    @ui.decorators.textField<PurchaseOrder>({
        parent() {
            return this.sendEmailBlock;
        },
        title: 'Email',
        helperText: 'A purchase order will be sent to this address.',
        isTransient: true,
        isFullWidth: true,
        isMandatory: true,
        validation(val) {
            if (val.length > 0 && !validEmail(val)) {
                return ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_order_send__invalid-email',
                    'Email address incorrect: {{email}}',
                    { email: val },
                );
            }
            return undefined;
        },
    })
    emailAddress: ui.fields.Text;

    @ui.decorators.buttonField<PurchaseOrder>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.sendEmailBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_order__select_bill_to_contact_button_text',
                'Select supplier contact',
            );
        },
        onError() {}, // Intentionally left empty
        async onClick() {
            this.contactSelectionSection.isHidden = false;
            await this.$.dialog
                .custom('info', this.contactSelectionSection, { resolveOnCancel: true })
                .then(() => {
                    if (this.selectedContact.value?._id) {
                        this.assignEmailContactFrom({
                            _id: this.selectedContact.value._id,
                            ...this.selectedContact.value,
                        });
                    }
                })
                .finally(() => {
                    this.selectedContact.value = null;
                    this.contactSelectionSection.isHidden = false;
                });
        },
    })
    selectBillToContact: ui.fields.Button;

    @ui.decorators.buttonField<PurchaseOrder>({
        isTransient: true,
        parent() {
            return this.sendEmailBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-purchasing/pages__purchase_order__send_order_button_text', 'Send');
        },
        onError(error) {
            this.$.loader.isHidden = true;
            const message = error.message.substring(error.message.lastIndexOf(':') + 2);
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_order_send__email_exception',
                    'Could not send purchase order email. ({{exception}})',
                    { exception: message },
                ),
                { type: 'error' },
            );
        },
        async onClick() {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__send_order_dialog_title',
                        'Purchase order email sending confirmation',
                    ),
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__send_order_dialog_content',
                        'You are about to send the purchase order email.',
                    ),
                    ui.localize('@sage/xtrem-purchasing/pages-confirm-send', 'Send'),
                )
            ) {
                this.$.loader.isHidden = false;

                // Send the email
                const sendMailResult = await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseOrder')
                    .mutations.printPurchaseOrderAndEmail(true, {
                        purchaseOrder: this.$.recordId ?? '',
                        contactTitle: this.emailTitle.value ?? '',
                        contactFirstName: this.emailFirstName.value ?? '',
                        contactLastName: this.emailLastName.value ?? '',
                        contactEmail: this.emailAddress.value ?? '',
                    })
                    .execute();

                if (!sendMailResult) {
                    throw new Error(
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_order_send__email_cannot_be_sent',
                            'The purchase order cannot be sent by email.',
                        ),
                    );
                }

                // Check the isSent flag to true
                const sendResult = await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseOrder')
                    .mutations.setIsSentPurchaseOrder(true, { purchaseOrder: this.$.recordId ?? '' })
                    .execute();

                if (!sendResult) {
                    throw new Error(
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_order_send__order_cannot_be_sent',
                            "The purchase order has been sent but due to a technical error the 'Sent' checkbox could not be updated.",
                        ),
                    );
                }

                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order_send__email_sent',
                        'Purchase order sent to {{email}}.',
                        { email: this.emailAddress.value },
                    ),
                    { type: 'success' },
                );
                this.$.setPageClean();
                await this.$.refreshNavigationPanel();
                this.sendEmailSection.isHidden = true;
                this.$.loader.isHidden = true;
                this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, { _id: this.$.recordId ?? '' });
            }
        },
    })
    sendOrderButton: ui.fields.Button;

    @ui.decorators.section<PurchaseOrder>({ isHidden: true, isTitleHidden: true })
    contactSelectionSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrder>({
        parent() {
            return this.contactSelectionSection;
        },
        title: '',
        isTitleHidden: true,
    })
    contactSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseOrder, BusinessEntityContact>({
        parent() {
            return this.contactSelectionBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        canSelect: false,
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        orderBy: { lastName: +1, firstName: +1 },
        columns: [
            ui.nestedFields.dropdownList({
                title: 'Title',
                bind: 'title',
                optionType: '@sage/xtrem-master-data/enums/title',
            }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        onRowClick(_rowId, rowData) {
            this.selectedContact.value = {
                _id: rowData._id,
                lastName: rowData.lastName,
                firstName: rowData.firstName,
                title: rowData.title,
                email: rowData.email,
            };
        },
    })
    contacts: ui.fields.Table<BusinessEntityContact>;

    @ui.decorators.referenceField<PurchaseOrder, BusinessEntityContact>({
        parent() {
            return this.contactSelectionBlock;
        },
        isReadOnly: true,
        node: '@sage/xtrem-master-data/BusinessEntityContact',
        valueField: 'email',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Title', bind: 'title' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
    })
    selectedContact: ui.fields.Reference<BusinessEntityContact>;

    private async loadContacts(supplierAddressId: string) {
        // Load the primary supplier contact of the order
        const contact = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/BusinessEntityContact')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true, title: true, firstName: true, lastName: true, email: true },
                        { filter: { isPrimary: true, address: supplierAddressId } },
                    ),
                )
                .execute(),
        ).at(0);
        if (contact) {
            this.assignEmailContactFrom(contact);
        }
        // Load all contacts linked to this order supplier address into the selection grid
        this.contacts.value = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-master-data/BusinessEntityContact')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true, title: true, firstName: true, lastName: true, email: true },
                        { filter: { isActive: true, address: supplierAddressId } },
                    ),
                )
                .execute(),
        );
    }

    private assignEmailContactFrom(originContact: ui.PartialNodeWithId<BusinessEntityContact>) {
        this.emailTitle.value = originContact?.title ?? this.emailTitle.value;
        this.emailLastName.value = originContact?.lastName ?? this.emailLastName.value;
        this.emailFirstName.value = originContact?.firstName ?? this.emailFirstName.value;
        this.emailAddress.value = originContact?.email ?? this.emailAddress.value;
    }
    // Send purchase order by email end here

    private async _onChangeCalculatePrices(rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        await this._calculatePrices(rowData);
        this.lines.addOrUpdateRecordValue(rowData);
        computeTotalAmounts(this);
        this.updateTileContainerValues(true);
    }

    private async _calculatePrices(rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        const prices = await calculateLinePrices({
            grossPrice: Number(rowData.grossPrice),
            charge: Number(rowData.charge),
            discount: Number(rowData.discount),
            netPriceScale: getCompanyPriceScale(this.site?.value?.legalCompany),
            quantity: Number(rowData.quantity),
            amountExcludingTax: Number(rowData.amountExcludingTax),
            amountIncludingTax: Number(rowData.amountIncludingTax),
            taxAmount: Number(rowData.taxAmount) ?? 0,
            taxAmountAdjusted: Number(rowData.taxAmountAdjusted) ?? 0,
            rateMultiplication: Number(this.companyFxRate.value),
            rateDivision: Number(this.companyFxRateDivisor.value),
            fromDecimals: Number(this.currency.value?.decimalDigits),
            toDecimals: Number(this.siteCurrency.decimalDigits),
            taxes: {
                site: this.stockSite.value?._id,
                businessPartner: this.businessRelation.value?._id,
                item: rowData.item?._id,
                currency: this.currency.value?._id,
                lineNodeName: '@sage/xtrem-purchasing/PurchaseOrderLine',
                taxEngine: this.taxEngine.value ?? '',
                uiTaxes: rowData.uiTaxes,
                graphObject: this.$.graph,
                taxDate: this.orderDate.value ?? '',
            },
        });
        rowData.netPrice = String(prices.netPrice);
        rowData.taxAmount = String(prices.taxAmount);
        rowData.taxAmountAdjusted = String(prices.taxAmountAdjusted);
        rowData.uiTaxes = prices.uiTaxes;
        rowData.taxDate = this.orderDate.value ?? '';
        rowData.amountExcludingTax = String(prices.amountExcludingTax);
        rowData.amountIncludingTax = String(prices.amountIncludingTax);
        rowData.amountExcludingTaxInCompanyCurrency = String(prices.amountExcludingTaxInCompanyCurrency);
        rowData.amountIncludingTaxInCompanyCurrency = String(prices.amountIncludingTaxInCompanyCurrency);
        rowData.taxCalculationStatus = prices.taxCalculationStatus;
        await TotalTaxCalculator.getInstance().updateTaxDetails(
            this.taxes,
            this.totalTaxAmountAdjusted,
            this.totalTaxAmount,
        );
    }

    private async callDisplayTaxes(
        rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>,
        updateTaxDetails = true,
    ) {
        const result = await displayTaxes(
            this,
            rowItem,
            {
                currency: this.currency.value ?? {},
                taxAmount: +(rowItem.taxAmount ?? 0),
                amountExcludingTax: +(rowItem.amountExcludingTax ?? 0),
                amountIncludingTax: +(rowItem.amountIncludingTax ?? 0),
                documentType: DocumentTypeEnum.purchaseOrderLine,
                taxDate: rowItem.taxDate,
                legislation: this.site.value?.legalCompany?.legislation,
                destinationCountry: this.supplierAddress.value?.country,
                originCountry: this.stockSite.value?.primaryAddress?.country ?? {},
                validTypes: ['purchasing', 'purchasingAndSales'],
            },
            updateTaxDetails,
        );

        if (result) {
            rowItem.amountIncludingTaxInCompanyCurrency = String(
                convertAmount(
                    Number(this.lines.getRecordValue(rowItem._id ?? '')?.amountIncludingTax),
                    this.companyFxRate.value ?? 0,
                    this.companyFxRateDivisor.value ?? 0,
                    this.currency.value?.decimalDigits ?? 2,
                    this.siteCurrency.decimalDigits ?? 2,
                ),
            );
            this.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                amountIncludingTaxInCompanyCurrency: rowItem.amountIncludingTaxInCompanyCurrency,
            });
            if (updateTaxDetails) {
                computeTotalAmounts(this);
            }
        }
    }

    get siteCurrency() {
        const currency = this.site.value?.legalCompany?.currency;
        return {
            _id: currency?._id ?? '',
            decimalDigits: currency?.decimalDigits ?? 2,
            symbol: currency?.symbol ?? '',
        };
    }

    showHideColumns() {
        if (isExchangeRateHidden(this.currency.value, this.site.value, this.businessRelation.value)) {
            this.rateDescription.isHidden = true;
            this.lines.hideColumn('amountExcludingTaxInCompanyCurrency');
            this.lines.hideColumn('amountIncludingTaxInCompanyCurrency');
        } else {
            this.lines.showColumn('amountExcludingTaxInCompanyCurrency');
            this.lines.showColumn('amountIncludingTaxInCompanyCurrency');
            this.rateDescription.isHidden = false;
        }
    }

    _setStepSequenceStatusObject(stepSequenceValues: PurchaseOrderStepSequenceStatus): Dict<ui.StepSequenceStatus> {
        return {
            [this.orderStepSequenceCreate]: stepSequenceValues.create,
            [this.orderStepSequenceApprove]: stepSequenceValues.approve,
            [this.orderStepSequenceConfirm]: stepSequenceValues.confirm,
            [this.orderStepSequenceReceive]: stepSequenceValues.receive,
            [this.orderStepSequenceInvoice]: stepSequenceValues.invoice,
        };
    }

    updateTileContainerValues(isDisabled: boolean) {
        this.earliestExpectedDate.isDisabled = isDisabled;
        this.purchaseOrderValue.isDisabled = isDisabled;
        this.minimumOrderAmount.isDisabled = isDisabled;
        if (isDisabled) {
            this.minimumOrderAmount.icon = undefined;
        }
    }

    async showWarningUpdate() {
        if (
            await confirmDialogWithAcceptButtonText(
                this,
                ui.localize('@sage/xtrem-purchasing/pages__purchase_order__warning_dialog_title', 'Warning update'),
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_order__warning_dialog_content',
                    'You are about to update the purchase order sent to the supplier.',
                ),
                ui.localize('@sage/xtrem-purchasing/pages_warning_button', 'Confirm'),
            )
        ) {
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            this.$.setPageClean();
            await this.$.router.refresh();
        }
    }

    async displayLandedCosts(documentLineId: string) {
        await this.$.dialog.page(
            '@sage/xtrem-landed-cost/LandedCostSummary',
            { args: JSON.stringify({ documentLineId, companyCurrency: this.companyCurrency.value?._id ?? '' }) },
            { size: 'extra-large' },
        );
    }

    async deleteLine(rowId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        if (+rowId < 0) {
            this.lines.removeRecord(rowId);
            return;
        }

        const errorMessages = (await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrderLine')
            .queries.controlDelete(true, { purchaseOrderLine: rowId })
            .execute()) as string[];
        if (errorMessages.length > 0) {
            this.$.showToast(errorMessages.join('\n\n'), { type: 'error' });
            return;
        }
        const isAssignmentChecked = await isPurchaseOrderAssignmentCheckedOnPurchaseOrderLineDelete(this, rowItem);
        if (isAssignmentChecked) {
            if (
                await confirmDialogWithAcceptButtonText(
                    this,
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__line_delete_action_dialog_title',
                        'Confirm delete',
                    ),
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__line_delete_action_dialog_content',
                        'You are about to delete this purchase order line. This action cannot be undone.',
                    ),
                    ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                )
            ) {
                this._deleteFromOrderingInProgressRequisitionLines(rowId);
                this.lines.removeRecord(rowId);
                await TotalTaxCalculator.getInstance().updateTaxData(JSON.parse(rowItem.uiTaxes ?? '{}'), null);
                await TotalTaxCalculator.getInstance().updateTaxDetails(
                    this.taxes,
                    this.totalTaxAmountAdjusted,
                    this.totalTaxAmount,
                );
                this.manageDisplayButtonSelectFromPurchaseRequisitionLinesAction();
                computeTotalAmounts(this);
                refreshTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                this.updateTileContainerValues(true);
            }
        }
    }

    async checkLineClosable(recordId: string, rowItem: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        const isLineClosable = await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrderLine')
            .queries.controlCloseLine(true, { purchaseOrderLine: recordId })
            .execute();
        if (!isLineClosable) {
            return false;
        }

        return isPurchaseOrderAssignmentCheckedOnPurchaseOrderLineClose(this, rowItem);
    }

    private async submitForApproval(approval: { siteId: string; isGrossPriceMissing: boolean; recordId: string }) {
        const isConfirmedRequestApproval = approval.isGrossPriceMissing
            ? await actionFunctions.isConfirmedRequestApprovalAction({
                  isCalledFromRecordPage: false,
                  purchaseOrderPage: this,
                  siteId: approval.siteId,
                  isGrossPriceMissing: approval.isGrossPriceMissing,
                  recordId: approval.recordId,
              })
            : true;
        if (isConfirmedRequestApproval) {
            const { usersList, emailAddressApproval } = await actionFunctions.loadApprovers({
                purchaseOrderPage: this,
                siteId: approval.siteId,
            });
            this.users.value = usersList;
            this.emailAddressApproval.value = emailAddressApproval;

            this.requestApprovalSection.isHidden = false;
            await this.$.dialog.custom('info', this.requestApprovalSection, {
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
                resolveOnCancel: true,
            });
            this.$.setPageClean();
            this.requestApprovalSection.isHidden = true;
        }
    }

    private async purchaseOrderUpdatePrice(rowData: ui.PartialCollectionValue<PurchaseOrderLineBinding>) {
        let isRecalculatePrices = false;

        if (+(rowData._id ?? '') > 0) {
            isRecalculatePrices = await recalculatePricesDialog(this);
        }

        if (isRecalculatePrices) {
            await this._setPriceOrigin(rowData, isRecalculatePrices);
            await this._onChangeCalculatePrices(rowData);
        }
    }

    async loadRelatedDocuments() {
        await this.loadRelatedPurchaseRequisitionLines('');
        this._updateOrderLinesGridWithRelatedRequisitionLines();
        await this.loadRelatedPurchaseReceiptLines('');
        await this.loadRelatedPurchaseInvoiceLines('');
    }
}
