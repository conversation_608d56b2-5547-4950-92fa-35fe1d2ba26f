import type { Dict, ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import type { BusinessEntity, Currency, Item, Supplier, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import {
    convertFromTo,
    getCompanyPriceScale,
    getPurchaseUnit,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import type {
    GraphApi,
    PurchaseDocumentApprovalStatus,
    PurchaseDocumentStatus,
    PurchaseRequisitionLine,
    PurchaseRequisitionLineBinding,
    PurchaseRequisitionLineToPurchaseOrderLine,
    PurchaseRequisition as PurchaseRequisitionNode,
} from '@sage/xtrem-purchasing-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
    setOrderOfPageHeaderQuickActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import * as ui from '@sage/xtrem-ui';
import { toNumber } from 'lodash';
import {
    confirmDialogWithAcceptButtonText,
    getPurchasePrice,
    getSiteAndSupplier,
    isPurchaseRequisitionLineToPurchaseOrderLine,
} from '../client-functions/common';
import * as displayButtons from '../client-functions/display-buttons-purchase-requisition';
import type { PurchaseRequisitionStepSequenceStatus } from '../client-functions/interfaces/step-sequence';
import * as PillColorPurchase from '../client-functions/pill-color';
import * as actionFunctions from '../client-functions/purchase-requisitions-actions-functions';
import type { SupplierLookUpData } from '../client-functions/shared/interfaces';
import { laterThanToday } from '../client-functions/shared/page-functions';
import { loadSuppliersLookUpData, suppliersLookUpOnRowClick } from '../client-functions/supplier-lookup';
import {
    isPurchaseRequisitionLineActionDisabled,
    isPurchaseRequisitionLinePropertyDisabled,
} from '../shared-functions/edit-rules';
import { recalculatePricesDialog, updatePriceDialog } from '../client-functions/purchase-price';

/**
 * Please update the corresponding cucumber functional test
 * {@link /xtrem/xtrem/services/functional-tests/xtrem-distribution-test/test/distribution-crud-purchase-requisition.feature.deactivated}
 */
@ui.decorators.page<PurchaseRequisition, PurchaseRequisitionNode>({
    title: 'Purchase requisition',
    objectTypeSingular: 'Purchase requisition',
    objectTypePlural: 'Purchase requisitions',
    idField() {
        return this.number;
    },
    menuItem: purchasing,
    node: '@sage/xtrem-purchasing/PurchaseRequisition',
    module: 'purchasing',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 200,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [
                this.requestApproval,
                this.approve,
                this.reject,
                this.createPurchaseOrder,
                this.closeRequisition,
                this.confirm,
            ],
        });
    },
    headerQuickActions() {
        return setOrderOfPageHeaderQuickActions({
            duplicate: [this.$standardDuplicateAction],
            quickActions: [],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.applyDefaultSuppliers,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this._manageDisplayApplicativePageActions(isDirty);
        this.requisitionStepSequence.statuses = this.getDisplayStatusStepSequence();
    },
    async onLoad() {
        await this.initPage();
        this.requisitionStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(false);
        this.$.setPageClean();
    },
    validation() {
        const validationMessage = this.lines.value.some(line => !line.item && !line.requestedItemDescription)
            ? ui.localize(
                  '@sage/xtrem-purchasing/pages__purchase_requisition__item_mandatory',
                  'There is at least one line where the item or item description should be referenced.',
              )
            : '';

        const mandatoryPurchaseUnit = this.lines.value
            .filter(line => line.item && !line.unit)
            .map(line =>
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__purchase_unit_mandatory',
                    'Purchase unit is mandatory for item {item}.',
                    { item: line.item },
                ),
            );
        if (mandatoryPurchaseUnit.length) {
            validationMessage.concat(mandatoryPurchaseUnit.join('\n'));
        }
        return validationMessage;
    },
    navigationPanel: {
        orderBy: { requestDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                bind: 'number',
                title: 'Number',
                page: '@sage/xtrem-purchasing/PurchaseRequisition',
                queryParameters: (_value, rowData) => ({ _id: rowData?._id ?? '' }),
            }),
            line2: ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionNode, User>({
                bind: 'requester',
                title: 'Requester',
                node: '@sage/xtrem-system/User',
                tunnelPage: undefined,
                valueField: 'displayName',
            }),
            line2Right: ui.nestedFields.date({ bind: 'requestDate', title: 'Requisition date' }),
            line_4: ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionNode>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                title: 'Site',
                tunnelPage: undefined,
                columns: [
                    ui.nestedFields.technical({ bind: 'isPurchaseRequisitionApprovalManaged' }),
                    ui.nestedFields.technical({ bind: { businessEntity: { currency: { _id: true } } } }),
                ],
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseRequisitionDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            isApplyDefaultSupplierHidden: ui.nestedFields.technical({ bind: 'isApplyDefaultSupplierHidden' }),
            isSetDimensionHidden: ui.nestedFields.technical({ bind: 'isSetDimensionHidden' }),
            approvalStatus: ui.nestedFields.technical({ bind: 'approvalStatus' }),
            line5Technical: ui.nestedFields.technical({ bind: 'status' }),
            line6: ui.nestedFields.technical({ bind: 'orderStatus' }),
            line7: ui.nestedFields.technical({ bind: 'approvalStatus' }),
            isCreateOrderLinesHidden: ui.nestedFields.technical({ bind: 'isCreateOrderLinesHidden' }),
        },
        optionsMenu: [
            {
                title: 'All open statuses',
                graphQLFilter: { displayStatus: { _nin: ['closed', 'rejected', 'ordered'] } },
            },
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
            { title: 'Pending approval', graphQLFilter: { displayStatus: { _eq: 'pendingApproval' } } },
            { title: 'Approved', graphQLFilter: { displayStatus: { _eq: 'approved' } } },
            { title: 'Partially ordered', graphQLFilter: { displayStatus: { _eq: 'partiallyOrdered' } } },
            { title: 'Ordered', graphQLFilter: { displayStatus: { _eq: 'ordered' } } },
            { title: 'Rejected', graphQLFilter: { displayStatus: { _eq: 'rejected' } } },
            { title: 'Closed', graphQLFilter: { displayStatus: { _eq: 'closed' } } },
            { title: 'Confirmed', graphQLFilter: { displayStatus: { _eq: 'confirmed' } } },
        ],
        dropdownActions: [
            {
                title: 'Create order',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    const purchaseReqLines = await actionFunctions.getPurchaseRequisitionLines({
                        purchaseRequisitionPage: this,
                        recordId,
                    });
                    if (rowItem.number && recordId !== '') {
                        await actionFunctions.createPurchaseOrderAction({
                            purchaseRequisitionPage: this,
                            lines: purchaseReqLines,
                            recordId: rowItem._id,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonCreatePurchaseOrderAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            receivingSite: rowItem.site,
                            islinesCreateOrder: rowItem.isCreateOrderLinesHidden ?? false,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Close requisition',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    if (rowItem.number && recordId !== '') {
                        await actionFunctions.close({
                            purchaseRequisitionPage: this,
                            recordId,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonCloseRequisitionAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Submit for approval',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    if (recordId && rowItem.site?._id) {
                        await this.submitForApproval(rowItem.site._id);
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonRequestApprovalAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            isApprovalManaged: !!rowItem.site?.isPurchaseRequisitionApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Confirm',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.confirmAction({
                            purchaseRequisitionPage: this,
                            recordNumber: rowItem.number,
                            isConfirmed: true,
                            recordId,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonConfirmAction({
                        parameters: {
                            status: rowItem.status,
                            approvalStatus: rowItem.approvalStatus,
                            isApprovalManaged: !!rowItem.site?.isPurchaseRequisitionApprovalManaged,
                        },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Approve',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.approve({
                            purchaseRequisitionPage: this,
                            recordNumber: rowItem.number,
                            approve: true,
                            recordId,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonApproveAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            {
                title: 'Reject',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId, rowItem) {
                    if (rowItem.number && recordId) {
                        await actionFunctions.reject({
                            purchaseRequisitionPage: this,
                            recordNumber: rowItem.number,
                            approve: false,
                            recordId,
                        });
                    }
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonRejectAction({
                        parameters: { status: rowItem.status, approvalStatus: rowItem.approvalStatus },
                        recordId,
                        isDirty: false,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Apply default supplier',
                icon: 'person_tick',
                refreshesMainList: 'list',
                isHidden: (_recordId, rowItem) => !!rowItem.isApplyDefaultSupplierHidden,
                async onClick(recordId, rowItem) {
                    const purchaseReqLines = await actionFunctions.getPurchaseRequisitionLines({
                        purchaseRequisitionPage: this,
                        recordId,
                    });
                    await actionFunctions.applyDefaultSupplier({
                        purchaseRequisitionPage: this,
                        isCalledFromRecordPage: false,
                        lines: purchaseReqLines,
                        recordId,
                        status: rowItem.status ?? '',
                        requestDate: rowItem.requestDate ?? '',
                        legalCompany: rowItem.site?.legalCompany ?? {},
                    });
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(recordId, rowItem) {
                    if (rowItem.status) {
                        const { site, supplier } = await getSiteAndSupplier({
                            page: this,
                            siteId: rowItem.site?._id ?? '',
                            supplierId: '',
                        });
                        await actionFunctions.setDimensions({
                            purchaseRequisitionPage: this,
                            recordId,
                            status: rowItem.status,
                            site,
                            supplier,
                        });
                    }
                },
                isHidden: (_recordId, rowItem) => !!rowItem.isSetDimensionHidden,
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return utils.formatError(this, error);
                },
                async onClick(_recordId, rowItem) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-purchasing/PurchaseRequisition',
                    });
                },
                isHidden(recordId, rowItem) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: {
                            status: rowItem.status,
                            orderStatus: rowItem.orderStatus,
                            approvalStatus: rowItem.approvalStatus,
                        },
                        recordId,
                    });
                },
            },
        ],
        inlineActions: [
            {
                title: 'Duplicate',
                icon: 'duplicate',
                async onClick(rowId: string) {
                    await this.$standardDuplicateAction.execute(false, rowId);
                },
            },
        ],
    },
})
export class PurchaseRequisition extends ui.Page<GraphApi, PurchaseRequisitionNode> {
    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    currentSelectedLineId: string;

    private readonly requisitionStepSequenceCreate = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_creation',
        'Create',
    );

    private readonly requisitionStepSequenceApprove = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_approving',
        'Approve',
    );

    private readonly requisitionStepSequenceConfirm = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_confirm',
        'Confirm',
    );

    private readonly requisitionStepSequenceOrder = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_requisition__step_sequence_order',
        'Order',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.orderStatus.value === 'ordered') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                order: 'complete',
            });
        }
        if (this.orderStatus.value === 'partiallyOrdered') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                order: 'current',
            });
        }
        if (['approved', 'rejected', 'confirmed'].includes(this.approvalStatus.value ?? '')) {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'complete',
                confirm: 'complete',
                order: 'incomplete',
            });
        }
        if (this.approvalStatus.value === 'pendingApproval') {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'current',
                confirm: 'current',
                order: 'incomplete',
            });
        }
        if (!this.isApprovalManaged.value && this.$.recordId) {
            return this._setStepSequenceStatusObject({
                create: 'complete',
                approve: 'current',
                confirm: 'current',
                order: 'incomplete',
            });
        }

        return this._setStepSequenceStatusObject({
            create: 'current',
            approve: 'incomplete',
            confirm: 'incomplete',
            order: 'incomplete',
        });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
            businessActions: [
                this.$standardOpenCustomizationPageWizardAction,
                this.requestApproval,
                // this.requestChanges, TODO: not set for the moment
                this.approve,
                this.reject,
                this.createPurchaseOrder,
                this.closeRequisition,
                this.confirm,
            ],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.save.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value as PurchaseDocumentStatus },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value as PurchaseDocumentStatus },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                orderStatus: this.orderStatus.value,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
            },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonRequestApprovalAction(isDirty);
        this.manageDisplayButtonRequestChangesAction(isDirty);
        this.manageDisplayButtonApproveAction(isDirty);
        this.manageDisplayButtonConfirmAction(isDirty);
        this.manageDisplayButtonRejectAction(isDirty);
        this.manageDisplayButtonCreatePurchaseOrderAction(isDirty);
        this.manageDisplayButtonCloseRequisitionAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonApplyDefaultSuppliersAction();
        this.manageDisplayLinePhantomRow();
    }

    manageDisplayButtonApproveAction(isDirty: boolean) {
        this.approve.isHidden = displayButtons.isHiddenButtonApproveAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonConfirmAction(isDirty: boolean) {
        this.confirm.isHidden = displayButtons.isHiddenButtonConfirmAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
                isApprovalManaged: this.isApprovalManaged.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRejectAction(isDirty: boolean) {
        this.reject.isHidden = displayButtons.isHiddenButtonRejectAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRequestApprovalAction(isDirty: boolean) {
        this.requestApproval.isHidden = displayButtons.isHiddenButtonRequestApprovalAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
                isApprovalManaged: this.isApprovalManaged.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonRequestChangesAction(isDirty: boolean) {
        this.requestChanges.isHidden = displayButtons.isHiddenButtonRequestChangesAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonCloseRequisitionAction(isDirty: boolean) {
        this.closeRequisition.isHidden = displayButtons.isHiddenButtonCloseRequisitionAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    manageDisplayButtonApplyDefaultSuppliersAction() {
        this.applyDefaultSuppliers.isHidden = this.isApplyDefaultSupplierHidden.value ?? false;
    }

    private manageDisplayButtonCreatePurchaseOrderAction(isDirty: boolean) {
        this.createPurchaseOrder.isHidden = displayButtons.isHiddenButtonCreatePurchaseOrderAction({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                approvalStatus: this.approvalStatus.value as PurchaseDocumentApprovalStatus,
                receivingSite: this.site.value,
                islinesCreateOrder: this.isCreateOrderLinesHidden.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
            parameters: {
                status: this.status.value as PurchaseDocumentStatus,
                receivingSite: this.site.value,
                requestDate: this.requestDate.value,
                requester: this.requester.value,
                approvalStatus: this.approvalStatus.value,
            },
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = this.isSetDimensionHidden.value ?? false;

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: {
                receivingSite: this.site.value,
                requestDate: this.requestDate.value,
                requester: this.requester.value,
            },
        });
    }

    @ui.decorators.switchField<PurchaseRequisition>({
        isHidden: true,
        bind: { site: { isPurchaseRequisitionApprovalManaged: true } },
    })
    isApprovalManaged: ui.fields.Switch;

    @ui.decorators.pageAction<PurchaseRequisition>({
        title: 'Save',
        buttonType: 'primary',
        access: { bind: '$update' },
        async onClick() {
            this.purchaseOrderLines.isHidden = !this.purchaseOrderLines.value.length;
            await this.$standardSaveAction.execute(true);
        },
    })
    save: ui.PageAction;

    getSerializedValues() {
        const { $detailPanel, ...values } = this.$.values;
        this.getLineValues(values.lines);
        return values;
    }

    // eslint-disable-next-line class-methods-use-this
    getLineValues(lines: Partial<Dict<any>>[]) {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        lines &&
            lines.forEach(line => {
                if (+line._id > 0) {
                    delete line.unitToStockUnitConversionFactor;
                    delete line.totalTaxExcludedAmount;
                }
            });
    }

    @ui.decorators.pageAction<PurchaseRequisition>({
        isHidden: true,
        icon: 'person_tick',
        title: 'Apply default supplier',
        async onClick() {
            const linesWithSuppliers = await actionFunctions.applyDefaultSupplier({
                purchaseRequisitionPage: this,
                lines: this.lines.value,
                isCalledFromRecordPage: true,
                recordId: this.$.recordId ?? '',
                status: this.status.value ?? '',
                requestDate: this.requestDate.value ?? '',
                legalCompany: this.site.value?.legalCompany ?? {},
            });
            linesWithSuppliers.forEach(lineWithSupplier => this.lines.setRecordValue(lineWithSupplier));
            await this.lines.redraw();
        },
    })
    applyDefaultSuppliers: ui.PageAction;

    @ui.decorators.pageAction<PurchaseRequisition>({
        isTransient: true,
        isHidden: true,
        title: 'Submit for approval',
        async onClick() {
            if (this.site.value?._id && this.$.recordId) {
                await this.submitForApproval(this.site.value._id);
            }
        },
    })
    requestApproval: ui.PageAction;

    @ui.decorators.pageAction<PurchaseRequisition>({
        isTransient: true,
        isHidden: true,
        title: 'Request changes',
        async onClick() {
            this.requestChangesSection.isHidden = false;
            await this.$.dialog.custom('info', this.requestChangesSection, {
                cancelButton: { isHidden: true },
                acceptButton: { isHidden: true },
                resolveOnCancel: true,
            });
            this.requestChangesSection.isHidden = true;
        },
    })
    requestChanges: ui.PageAction;

    @ui.decorators.pageAction<PurchaseRequisition>({
        isTransient: true,
        isHidden: true,
        title: 'Approve',
        async onClick() {
            await actionFunctions.approve({
                purchaseRequisitionPage: this,
                recordNumber: this.number.value ?? '',
                approve: true,
                recordId: this.$.recordId ?? '',
            });
        },
    })
    approve: ui.PageAction;

    @ui.decorators.pageAction<PurchaseRequisition>({
        isTransient: true,
        isHidden: true,
        title: 'Reject',
        async onClick() {
            await actionFunctions.reject({
                purchaseRequisitionPage: this,
                recordNumber: this.number.value ?? '',
                approve: false,
                recordId: this.$.recordId ?? '',
            });
        },
    })
    reject: ui.PageAction;

    @ui.decorators.pageAction<PurchaseRequisition>({
        isHidden: true,
        async onClick() {
            await actionFunctions.createPurchaseOrderAction({
                purchaseRequisitionPage: this,
                lines: this.lines.value,
                recordId: String(this.$.recordId),
            });
        },
        title: 'Create order',
    })
    createPurchaseOrder: ui.PageAction;

    @ui.decorators.pageAction<PurchaseRequisition>({
        isTransient: true,
        isHidden: true,
        title: 'Close requisition',
        async onClick() {
            await actionFunctions.close({
                purchaseRequisitionPage: this,
                recordId: this.$.recordId ?? '',
            });
        },
    })
    closeRequisition: ui.PageAction;

    @ui.decorators.section<PurchaseRequisition>({ title: 'General', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.section<PurchaseRequisition>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.tile<PurchaseRequisition>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.section<PurchaseRequisition>({ title: 'Notes', isTitleHidden: true })
    noteSection: ui.containers.Section;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.noteSection;
        },
    })
    noteBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.headerSection;
        },
        isHidden: true,
    })
    approvalBlock: ui.containers.Block;

    @ui.decorators.section<PurchaseRequisition>({
        title: 'Requested changes',
        isTitleHidden: true,
        isHidden: true, // RM - functionality to be reviewed and eventually replaced by a workflow
    })
    changeRequestTextSection: ui.containers.Section;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.changeRequestTextSection;
        },
        isHidden: true, // RM - functionality to be reviewed and eventually replaced by a workflow
    })
    changeRequestTextBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseRequisition, PurchaseRequisitionLineToPurchaseOrderLine>({
        title: 'Purchase requisition line to purchase order line',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        node: '@sage/xtrem-purchasing/PurchaseRequisitionLineToPurchaseOrderLine',
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                title: 'Order number',
                isFullWidth: true,
                map(_rowId, rowData) {
                    return `${rowData.purchaseOrderLine.document.number}`;
                },
                onClick(_id, rowData: any) {
                    if (isPurchaseRequisitionLineToPurchaseOrderLine(rowData)) {
                        this.$.router.goTo('@sage/xtrem-purchasing/PurchaseOrder', {
                            _id: rowData.purchaseOrderLine.document._id,
                        });
                    }
                },
            }),
            ui.nestedFields.label({
                title: 'Order status',
                isTitleHidden: true,
                bind: { purchaseOrderLine: { status: true } },
                // optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                // TODO: https://jira.sage.com/browse/XT-719 (solution 1) enhancement request for map event
                map(value, rowData) {
                    switch (rowData.purchaseOrderLine.status) {
                        case 'closed':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__closed',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'draft':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__draft',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'inProgress':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__inProgress',
                                rowData.purchaseOrderLine.status,
                            );
                        case 'pending':
                            return ui.localize(
                                '@sage/xtrem-purchasing/enums__purchase_document_status__pending',
                                rowData.purchaseOrderLine.status,
                            );
                        default:
                            return value;
                    }
                },
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus(
                        'PurchaseDocumentStatus',
                        rowData?.purchaseOrderLine?.status,
                    ),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity ordered',
                bind: 'orderedQuantity',
                isReadOnly: true,
                isFullWidth: true,
                scale(_defaultDimensionsAttributesrowId, rowData) {
                    return rowData?.unit?.decimalDigits ?? 2;
                },
                postfix(_rowId, rowData) {
                    return rowData?.unit?.symbol ?? '';
                },
            }),
            ui.nestedFields.date({
                title: 'Order date',
                bind: { purchaseOrderLine: { document: { orderDate: true } } },
                isReadOnly: true,
            }),
            ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionLineToPurchaseOrderLine, UnitOfMeasure>({
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                bind: 'unit',
                valueField: 'symbol',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    purchaseOrderLines: ui.fields.Table<PurchaseRequisitionLineToPurchaseOrderLine>;

    @ui.decorators.stepSequenceField<PurchaseRequisition>({
        width: 'small',
        isTransient: true,
        isFullWidth: true,
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return [
                this.requisitionStepSequenceCreate,
                (this.isApprovalManaged.value ??
                    ['pendingApproval', 'approved', 'rejected', 'changeRequested'].includes(
                        this.approvalStatus.value ?? '',
                    )) &&
                this.approvalStatus.value !== 'confirmed'
                    ? this.requisitionStepSequenceApprove
                    : this.requisitionStepSequenceConfirm,
                this.requisitionStepSequenceOrder,
            ];
        },
    })
    requisitionStepSequence: ui.fields.StepSequence;

    @ui.decorators.referenceField<PurchaseRequisition, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Purchasing site',
        lookupDialogTitle: 'Select site',
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseRequisition, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseRequisition, Site, Company>({
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'description',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [ui.nestedFields.technical({ bind: 'priceScale' })],
            }),
            ui.nestedFields.technical<PurchaseRequisition, Site, User>({
                bind: 'purchaseRequisitionDefaultApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'displayName' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseRequisition, Site, User>({
                bind: 'purchaseRequisitionSubstituteApprover',
                node: '@sage/xtrem-system/User',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'displayName' }),
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
        ],
        onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
        },
        isMandatory: true,
        minLookupCharacters: 0,
        isDisabled() {
            return !!this.$.recordId;
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.textField<PurchaseRequisition>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isDisabled() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.dateField<PurchaseRequisition>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Requisition date',
        isMandatory: true,
        isReadOnly() {
            return !!this.$.recordId;
        },
        validation(val) {
            return laterThanToday(
                val,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__request_date_cannot_be_future',
                    'The request date cannot be later than today.',
                ),
            );
        },
        onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
        },
    })
    requestDate: ui.fields.Date;

    @ui.decorators.referenceField<PurchaseRequisition, User>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Requester',
        lookupDialogTitle: 'Select requester',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
        ],
        isMandatory: true,
        minLookupCharacters: 0,
        isDisabled() {
            return !!this.$.recordId;
        },
        onChange() {
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
        },
    })
    requester: ui.fields.Reference<User>;

    @ui.decorators.labelField<PurchaseRequisition>({
        title: 'Display status',
        optionType: '@sage/xtrem-purchasing/PurchaseRequisitionDisplayStatus',
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label;

    @ui.decorators.labelField<PurchaseRequisition>({
        title: 'Status',
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
    })
    status: ui.fields.Label;

    @ui.decorators.switchField<PurchaseRequisition>({
        parent() {
            return this.headerBlock;
        },
        isHidden: true,
    })
    isApplyDefaultSupplierHidden: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseRequisition>({
        parent() {
            return this.headerBlock;
        },
        isHidden: true,
    })
    isSetDimensionHidden: ui.fields.Switch;

    @ui.decorators.labelField<PurchaseRequisition>({
        parent() {
            return this.headerBlock;
        },
        title: 'Approval status',
        isHidden: true,
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentApprovalStatus',
    })
    approvalStatus: ui.fields.Label;

    @ui.decorators.labelField<PurchaseRequisition>({
        parent() {
            return this.headerBlock;
        },
        title: 'Order status',
        isHidden: true,
        optionType: '@sage/xtrem-purchasing/PurchaseRequisitionOrderStatus',
    })
    orderStatus: ui.fields.Label;

    @ui.decorators.switchField<PurchaseRequisition>({
        parent() {
            return this.headerBlock;
        },
        isHidden: true,
    })
    isCreateOrderLinesHidden: ui.fields.Switch;

    @ui.decorators.countField<PurchaseRequisition>({
        parent() {
            return this.tileContainer;
        },
        bind: 'lines',
        title: 'Number of items',
        size: 'medium',
    })
    purchaseRequisitionLineCount: ui.fields.Count;

    @ui.decorators.textField<PurchaseRequisition>({
        parent() {
            return this.tileContainer;
        },
        title: 'Earliest required date',
        isTransient: true,
        width: 'medium',
    })
    earliestExpectedDate: ui.fields.Text;

    // TODO: uncomment once currency is added on the header
    /*
    @ui.decorators.aggregateField<PurchaseRequisition>({
        parent() {
            return this.tileContainer;
        },
        aggregateOn: 'totalTaxExcludedAmount',
        aggregationMethod: 'sum',
        title: 'Total excluding tax',
        bind: 'lines',
        unit() {
            return this.currency.value;
        },
        scale() {
            return utils.getScaleValue(2, this.currency.value?.decimalDigits);
        },
        width: 'medium',
    })
    purchaseRequisitionValue: ui.fields.Aggregate;
    */

    @ui.decorators.richTextField<PurchaseRequisition>({
        parent() {
            return this.noteBlock;
        },
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        title: 'Internal notes',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.richTextField<PurchaseRequisition>({
        isTransient: true,
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
        title: 'Internal line notes',
        isDisabled() {
            return this.lines.getRecordValue(this.currentSelectedLineId)?.status === 'closed';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseRequisition>({
        title: 'Repeat the document notes on new documents',
        parent() {
            return this.noteBlock;
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferHeaderNote: ui.fields.Switch;

    @ui.decorators.switchField<PurchaseRequisition>({
        title: 'Repeat all line notes',
        parent() {
            return this.noteBlock;
        },
        isDisabled() {
            return this.status.value === 'closed';
        },
        width: 'large',
    })
    isTransferLineNote: ui.fields.Switch;

    // RM - functionality to be reviewed and eventually replaced by a workflow
    /*     @ui.decorators.richTextField<PurchaseRequisition>({
        parent() {
            return this.changeRequestTextBlock;
        },
        title: 'Change requested',
        isFullWidth: true,
        height: '10px',
        isHidden: true,
    })
    changeRequestedDescription: ui.fields.RichText; */

    private _isLineDisabled(value: any, rowData: any) {
        return this.status.value === 'closed' || rowData?.status === 'closed';
    }

    @ui.decorators.tableField<PurchaseRequisition, PurchaseRequisitionLine & { supplierLink: string }>({
        title: 'Lines',
        isTitleHidden: true,
        bind: 'lines',
        canSelect: false,
        pageSize: 10,
        canAddNewLine: true,
        hasLineNumbers: true,
        isReadOnly() {
            return this.approvalStatus.value === 'approved';
        },
        node: '@sage/xtrem-purchasing/PurchaseRequisitionLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        onChange() {
            this.setEarliestExpectedDate();
        },
        columns: [
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionLine, Item>({
                title: 'Item',
                lookupDialogTitle: 'Select item',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                minLookupCharacters: 3,
                isMandatory: true,
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                fetchesDefaults: true,
                isDisabled(rowId, rowData) {
                    return this._isLineDisabled(rowId, rowData);
                },
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.reference<PurchaseRequisition, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        valueField: 'symbol',
                        isHidden: true,
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.reference<PurchaseRequisition, Item, UnitOfMeasure>({
                        bind: 'purchaseUnit',
                        valueField: 'symbol',
                        isHidden: true,
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.image({ bind: 'image', title: 'Image', canFilter: false, isHidden: true }),
                ],
                async onChange(_rowId, rowData) {
                    if (rowData?.item) {
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        let unit = await getPurchaseUnit(this.$.graph, rowData.item._id, rowData.supplier?._id ?? '');

                        const quantityToConvert = rowData.quantity ? rowData.quantity : 1;
                        let conversion = await convertFromTo(
                            this.$.graph,
                            unit._id,
                            rowData.item.stockUnit._id,
                            quantityToConvert,
                            rowData.item._id,
                            rowData.supplier?._id ?? '',
                            '',
                            'purchase',
                            false,
                        );
                        if (conversion.conversionFactor === 0 && rowData.supplier) {
                            this.$.showToast(
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_requisition__no_coefficient_exists',
                                    'No coefficient exists for the selected purchase unit and the item stock unit, please add the coefficient for the units.',
                                ),
                                { type: 'error' },
                            );
                            rowData.supplier = null;
                            unit = await getPurchaseUnit(this.$.graph, rowData.item._id, rowData.supplier?._id ?? '');
                            conversion = await convertFromTo(
                                this.$.graph,
                                unit._id,
                                rowData.item.stockUnit._id,
                                quantityToConvert,
                                rowData.item._id,
                                rowData.supplier?._id,
                                '',
                                'purchase',
                                false,
                            );
                        }
                        rowData.unit = unit;
                        rowData.requestedItemDescription = rowData.item.description ?? '';
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor;
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = conversion.convertedQuantity;
                        }

                        await this._setPriceOrigin(rowData, false);
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isReadOnly: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'requestedItemDescription',
                isHiddenOnMainField: true,
                width: 'large',
                isDisabled(_rowId, rowData) {
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        rowData?.status ?? '',
                        'requestedItemDescription',
                        this.approvalStatus.value ?? '',
                    );
                },
            }),
            ui.nestedFields.link({
                bind: 'supplierLink',
                isTransient: true,
                map() {
                    return ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_requisition__select_supplier_link_text',
                        'Select supplier',
                    );
                },
                isHidden(_rowId, rowData) {
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        rowData?.status ?? '',
                        'supplierLink',
                        this.approvalStatus.value ?? '',
                    );
                },
                async onClick(_rowId, rowData) {
                    await this._loadSuppliers(rowData);
                    this.supplierSelectionSection.isHidden = false;
                    await this.$.dialog
                        .custom('info', this.supplierSelectionSection)
                        .then(async () => {
                            const currentReqLine = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                            let error = false;
                            if (currentReqLine?.item) {
                                const quantityToConvert = currentReqLine.quantity
                                    ? toNumber(currentReqLine.quantity)
                                    : 1;
                                const unit = await getPurchaseUnit(
                                    this.$.graph,
                                    currentReqLine.item?._id ?? '',
                                    this.selectedSupplier.value?._id ?? '',
                                );
                                const conversion = await convertFromTo(
                                    this.$.graph,
                                    unit._id,
                                    currentReqLine.item.stockUnit?._id ?? '',
                                    quantityToConvert,
                                    currentReqLine.item._id,
                                    this.selectedSupplier.value?._id ?? '',
                                    '',
                                    'purchase',
                                    false,
                                );
                                if (conversion.conversionFactor === 0) {
                                    this.$.showToast(
                                        ui.localize(
                                            '@sage/xtrem-purchasing/pages__purchase_requisition__no_coefficient_exists',
                                            'No coefficient exists for the selected purchase unit and the item stock unit, please add the coefficient for the units.',
                                        ),
                                        { type: 'error' },
                                    );
                                    error = true;
                                } else {
                                    currentReqLine.unit = unit;
                                    currentReqLine.stockUnit = currentReqLine.item.stockUnit;
                                    currentReqLine.unitToStockUnitConversionFactor =
                                        conversion.conversionFactor.toString();

                                    if (currentReqLine.quantity) {
                                        currentReqLine.quantityInStockUnit = conversion.convertedQuantity.toString();
                                    }
                                }
                            }
                            if (!error) {
                                this.setSupplierInfo(currentReqLine);
                                await this._setPriceOrigin(currentReqLine, false);
                                this._onChangeCalculatePrices(currentReqLine);
                            }
                            this.lines.addOrUpdateRecordValue(currentReqLine);
                        })
                        .finally(() => {
                            this.selectedSupplier.value = null;
                            this.supplierSelectionSection.isHidden = true;
                        });
                },
                isHiddenOnMainField: true,
            }),
            ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionLine, Supplier>({
                bind: 'supplier',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
                fetchesDefaults: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
                    ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
                    ui.nestedFields.technical<PurchaseRequisition, Supplier, BusinessEntity>({
                        bind: 'businessEntity',
                        node: '@sage/xtrem-master-data/BusinessEntity',
                        nestedFields: [
                            ui.nestedFields.technical<PurchaseRequisition, BusinessEntity, Currency>({
                                bind: 'currency',
                                node: '@sage/xtrem-master-data/Currency',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'rounding' }),
                                ],
                            }),
                        ],
                    }),
                ],
                async onChange(_rowId, rowData) {
                    if (rowData.supplier?._id) {
                        rowData.currency = rowData.supplier.currency;
                        await this._setPriceOrigin(rowData, false);
                        this._onChangeCalculatePrices(rowData);
                    } else {
                        rowData.price = '0';
                        rowData.totalTaxExcludedAmount = '0';
                        rowData.priceOrigin = null;
                    }

                    this.lines.addOrUpdateRecordValue(rowData);
                    await this.lines.redraw();
                },
            }),
            ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionLine, Currency>({
                title: 'Transaction currency',
                lookupDialogTitle: 'Select currency',
                minLookupCharacters: 1,
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                helperTextField: 'id',
                isDisabled(_rowId, rowData) {
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        rowData?.status ?? '',
                        'currency',
                        this.approvalStatus.value ?? '',
                    );
                },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ISO 4217 code', bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'rounding' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
                filter() {
                    return { isActive: true };
                },

                async onChange() {
                    await this.lines.redraw('totalTaxExcludedAmount');
                },
            }),
            ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionLine, Site>({
                title: 'Site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: { businessEntity: { name: true } },
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: { businessEntity: { name: true } } }),
                    ui.nestedFields.text({ bind: { businessEntity: { id: true } } }),
                    ui.nestedFields.reference<PurchaseRequisition, Site, Company>({
                        bind: 'legalCompany',
                        valueField: 'description',
                        node: '@sage/xtrem-system/Company',
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionLine, UnitOfMeasure>({
                title: 'Purchase unit',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 0,
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isMandatory: true,
                isDisabled(_rowId, rowData) {
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        rowData?.status ?? '',
                        'unit',
                        this.approvalStatus.value ?? '',
                    );
                },
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                ],
                async onChange(_rowId, rowData) {
                    if (rowData?.item && rowData?.unit) {
                        const quantityToConvert = rowData.quantity ? rowData.quantity : 1;
                        const conversion = await convertFromTo(
                            this.$.graph,
                            rowData.unit._id,
                            rowData.item.stockUnit._id,
                            quantityToConvert,
                            rowData.item._id,
                            rowData.supplier?._id ?? '',
                            '',
                            'purchase',
                            false,
                        );
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor;
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = conversion.convertedQuantity;
                        }
                    }

                    await this._setPriceOrigin(rowData, false);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'quantity',
                isMandatory: true,
                isDisabled(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        line?.status ?? '',
                        'quantity',
                        this.approvalStatus.value ?? '',
                    );
                },
                scale(_rowId, rowData) {
                    return utils.getScaleValue(2, rowData?.unit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.unit?.symbol ?? '';
                },
                async onChange(_rowId, rowData) {
                    if (rowData?.item) {
                        const conversion = await convertFromTo(
                            this.$.graph,
                            rowData.unit._id,
                            rowData.item.stockUnit._id,
                            rowData.quantity,
                            rowData.item._id,
                            rowData.supplier?._id ?? '',
                            '',
                            'purchase',
                            false,
                        );
                        rowData.stockUnit = rowData.item.stockUnit;

                        rowData.unitToStockUnitConversionFactor = conversion.conversionFactor;
                        if (rowData.quantity) {
                            rowData.quantityInStockUnit = conversion.convertedQuantity;
                        }
                        this.lines.addOrUpdateRecordValue(rowData);
                    }

                    if (rowData?.item && Number(rowData?.quantity) >= 0 && rowData?.unit) {
                        let isRecalculatePrices = true;

                        if (+(rowData._id ?? '') > 0) {
                            isRecalculatePrices = await updatePriceDialog(this);
                        }

                        if (isRecalculatePrices) {
                            await this._setPriceOrigin(rowData, false);
                            this._onChangeCalculatePrices(rowData);
                            this.lines.addOrUpdateRecordValue(rowData);
                        }
                    }
                },
            }),
            ui.nestedFields.numeric({
                bind: 'unitToStockUnitConversionFactor',
                title: 'Stock unit conversion factor',
                isHiddenOnMainField: true,
                isReadOnly: true,
                width: 'large',
                scale(_rowId, rowData) {
                    const split = (rowData?.unitToStockUnitConversionFactor ?? '').split('.');
                    if (split.length > 1) {
                        return split[1].length;
                    }
                    return 2;
                },
                isHidden(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return line.item?.type === 'service';
                },
            }),
            ui.nestedFields.reference<PurchaseRequisition, PurchaseRequisitionLine, UnitOfMeasure>({
                title: 'Stock unit',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 0,
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                helperTextField: 'symbol',
                isReadOnly: true,
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                ],
                isHidden(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return line.item?.type === 'service';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                scale(_rowId, rowData) {
                    return utils.getScaleValue(2, rowData?.stockUnit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol ?? '';
                },
                isHidden(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return line.item?.type === 'service';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                isDisabled(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        line.status ?? '',
                        'grossPrice',
                        this.approvalStatus.value ?? '',
                    );
                },
                scale() {
                    return getCompanyPriceScale(this.site.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,

                onChange(_rowId, rowData) {
                    rowData.priceOrigin = 'manual';
                    this._onChangeCalculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        line.status ?? '',
                        'discount',
                        this.approvalStatus.value ?? '',
                    );
                },
                onChange(_rowId, rowData) {
                    this._onChangeCalculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        line.status ?? '',
                        'Charge',
                        this.approvalStatus.value ?? '',
                    );
                },
                onChange(_rowId, rowData) {
                    this._onChangeCalculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Net price',
                bind: 'netPrice',
                scale() {
                    return getCompanyPriceScale(this.site.value?.legalCompany);
                },
                prefix(_rowId, rowData) {
                    return rowData?.currency?.symbol ?? '';
                },
            }),
            ui.nestedFields.label<PurchaseRequisition, PurchaseRequisitionLine>({
                title: 'Price origin',
                bind: 'priceOrigin',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PriceOrigin',
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                isReadOnly: true,
                bind: 'totalTaxExcludedAmount',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.date({
                title: 'Required date',
                bind: 'needByDate',
                isDisabled(_rowId, rowData) {
                    const line = rowData as ui.PartialCollectionValue<PurchaseRequisitionLineBinding>;
                    return isPurchaseRequisitionLinePropertyDisabled(
                        this.status.value ?? '',
                        line.status ?? '',
                        'needByDate',
                        this.approvalStatus.value ?? '',
                    );
                },
                validation(value) {
                    if (value && this.requestDate.value && value < this.requestDate.value) {
                        return ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_requisition__required_date_cannot_be_before_requisition_date',
                            'You need to select a required date that is on or after the requisition date.',
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.label({
                title: 'Approval status',
                bind: 'approvalStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentApprovalStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentApprovalStatus', rowData?.approvalStatus),
            }),
            ui.nestedFields.label({
                title: 'Order status',
                bind: 'lineOrderStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PurchaseRequisitionOrderStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseRequisitionOrderStatus', rowData?.lineOrderStatus),
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.progress({
                bind: 'orderedPercentage',
                isExcludedFromMainField: true,
                color(value: number) {
                    if (value < 30) {
                        return ui.tokens.colorsSemanticCaution500;
                    }
                    if (value < 50) {
                        return ui.tokens.colorsSemanticCaution500;
                    }
                    if (value > 90) {
                        return ui.tokens.colorsSemanticPositive500;
                    }
                    return ui.tokens.colorsUtilityMajor200;
                },
                title: 'Order progress',
            }),
            ui.nestedFields.numeric({
                title: 'Ordered quantity',
                bind: 'orderedQuantity',
                isExcludedFromMainField: true,
                isReadOnly: true,
                scale(_rowId, rowData) {
                    return utils.getScaleValue(2, rowData?.unit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.unit?.symbol ?? '';
                },
            }),
            ui.nestedFields.numeric({
                title: 'Quantity to order',
                bind: 'quantityToOrder',
                isExcludedFromMainField: true,
                isReadOnly: true,
                scale(_rowId, rowData) {
                    return utils.getScaleValue(2, rowData?.unit?.decimalDigits);
                },
                postfix(_rowId, rowData) {
                    return rowData?.unit?.symbol ?? '';
                },
            }),
        ],
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'All open statuses', graphQLFilter: { status: { _ne: 'closed' } } },
        ],
        optionsMenuType: 'dropdown',
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId: string) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'locked',
                title: 'Close',
                isHidden(_rowId, rowData) {
                    return (
                        isPurchaseRequisitionLineActionDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'close',
                        ) ||
                        ['pendingApproval'].includes(rowData.approvalStatus ?? '') ||
                        ['pendingApproval'].includes(this.approvalStatus.value ?? '')
                    );
                },
                async onClick(rowId, rowData) {
                    await this.clickOnLineCloseButton(rowId, rowData);
                },
            },
            {
                icon: 'refresh',
                title: 'Update price',
                id: 'updatePrice',
                isHidden(_rowId, rowData: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
                    return (
                        !['draft', 'pendingApproval', 'confirmed'].includes(this.displayStatus.value ?? '') &&
                        !rowData.supplier?.id
                    );
                },
                async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
                    if (Number(rowItem._id) > 0) {
                        await this.purchaseRequisitionUpdatePrice(rowItem);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await utils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            {
                                editable: !isPurchaseRequisitionLineActionDisabled(
                                    this.status.value ?? '',
                                    rowItem.status ?? '',
                                    'dimensions',
                                ),
                            },
                        ),
                    );
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                // FIXME: XT-9220 begin
                // 1/ Different logic on server side, we can delete purchase requisition lines even if the document is closed
                // XT-9220 end
                isHidden(_rowId, rowData) {
                    return (
                        isPurchaseRequisitionLineActionDisabled(
                            this.status.value ?? '',
                            rowData.status ?? '',
                            'delete',
                        ) || ['approved'].includes(rowData.approvalStatus ?? '')
                    );
                },
                async onClick(rowId) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_requisition__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_requisition__line_delete_action_dialog_content',
                                'You are about to delete this purchase requisition line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        if (!this.$.values.lines?.length) {
                            this.enableDisableHeader(false);
                        }
                    }
                },
            },
        ],
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            titleRight: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            line2: ui.nestedFields.text({ bind: 'requestedItemDescription', title: 'Description' }),
            line2Right: ui.nestedFields.numeric({
                bind: 'totalTaxExcludedAmount',
                title: 'Total excluding tax',
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (+(recordValue?._id ?? 0) < 0) {
                    return ui.localize('@sage/xtrem-purchasing/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item?.name} - ${recordValue?.item?.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'locked',
                    title: 'Close',
                    isHidden(_rowId, rowData) {
                        return (
                            isPurchaseRequisitionLineActionDisabled(
                                this.status.value ?? '',
                                rowData.status ?? '',
                                'close',
                            ) ||
                            ['pendingApproval'].includes(rowData.approvalStatus ?? '') ||
                            ['pendingApproval'].includes(this.approvalStatus.value ?? '')
                        );
                    },

                    async onClick(rowId, rowData) {
                        await this.clickOnLineCloseButton(rowId, rowData);
                    },
                },
                {
                    icon: 'refresh',
                    title: 'Update price',
                    id: 'updatePrice',
                    isHidden(_rowId, rowData: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
                        return (
                            !['draft', 'pendingApproval', 'confirmed'].includes(this.displayStatus.value ?? '') &&
                            !rowData.supplier?.id
                        );
                    },
                    async onClick(_rowId: string, rowItem: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
                        if (Number(rowItem._id) > 0) {
                            await this.purchaseRequisitionUpdatePrice(rowItem);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseRequisitionLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await utils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                { documentLine: rowData },
                                {
                                    editable: !isPurchaseRequisitionLineActionDisabled(
                                        this.status.value ?? '',
                                        rowItem.status ?? '',
                                        'dimensions',
                                    ),
                                },
                            ),
                        );
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden(_rowId, rowData) {
                        const status = this.status.value ?? '';
                        return (
                            isPurchaseRequisitionLineActionDisabled(status, rowData.status ?? '', 'delete') ||
                            ['approved'].includes(rowData.approvalStatus ?? '')
                        );
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],

            // eslint-disable-next-line require-await
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<PurchaseRequisitionLineBinding>,
                    );
                }
            },

            async onRecordOpened(_id, recordValue) {
                if (_id !== '$new') {
                    await this._loadRelatedPurchaseOrderLines(_id);
                }
                if (recordValue) {
                    this.currentSelectedLineId = recordValue._id;
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    if (
                        +recordValue._id < 0 &&
                        (this._defaultDimensionsAttributes.dimensions !== '{}' ||
                            this._defaultDimensionsAttributes.attributes !== '{}')
                    ) {
                        const line = {
                            approvalStatus: 'draft',
                            status: 'draft',
                            lineOrderStatus: 'notOrdered',
                            site: this.site.value,
                            storedDimensions: this._defaultDimensionsAttributes.dimensions,
                            storedAttributes: this._defaultDimensionsAttributes.attributes,
                            item: '',
                        } as ui.PartialCollectionValue<PurchaseRequisitionLine>;

                        recordValue.storedAttributes = line.storedAttributes ?? '';
                        recordValue.storedDimensions = line.storedDimensions ?? '';
                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<PurchaseRequisitionLineBinding>,
                        );
                    }
                }
            },
            layout() {
                return {
                    information: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            orderStatusBlock: { fields: ['lineOrderStatus'] },
                            mainBlock: { fields: ['item'] },
                            main2Block: { fields: ['requestedItemDescription'] },
                            main3Block: { fields: ['supplier', 'supplierLink', 'currency', 'needByDate'] },
                            purchaseBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_purchase',
                                    'Purchase',
                                ),
                                fields: ['unit', 'quantity'],
                            },
                            stockBlock: {
                                isHidden(_rowId, rowItem) {
                                    return !rowItem?.item?.isStockManaged;
                                },
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    Price: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: { fields: ['grossPrice', 'discount', 'charge', 'netPrice'] },
                            priceOriginBlock: { fields: ['priceOrigin'] },
                            totalExcludingBlock: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['totalTaxExcludedAmount'],
                            },
                        },
                    },
                    progress: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_progress', 'Progress'),
                        isHidden() {
                            return this.purchaseOrderLines.value.length === 0;
                        },
                        blocks: {
                            ordersBlock: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_order', 'Order'),
                                fields: ['lineOrderStatus', 'orderedPercentage', 'orderedQuantity', 'quantityToOrder'],
                            },
                            purchaseOrderBlock: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_orders', 'Orders'),
                                fields: [this.purchaseOrderLines],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: { notesBlock: { fields: [this.internalNoteLine] } },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<PurchaseRequisitionLine>;

    async clickOnLineCloseButton(_rowId: any, rowItem: any) {
        if (
            await confirmDialogWithAcceptButtonText(
                this,
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__line_close_action_dialog_title',
                    'Confirm update',
                ),
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_requisition__line_close_action_dialog_content',
                    'You are about to close this purchase requisition line. This action cannot be undone.',
                ),
                ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
            )
        ) {
            this.$.loader.isHidden = false;
            await this.closeLine(rowItem);
            this.$.loader.isHidden = true;
            await this.$.router.refresh();
        }
    }

    /**
     * Runs the closeLine mutation with parameters
     */
    closeLine(row: any) {
        return this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseRequisitionLine')
            .mutations.closeLine(true, { purchaseRequisitionLine: `_id:${row._id}` })
            .execute();
    }

    private enableDisableHeader(hide: boolean) {
        this.number.isReadOnly = hide;
        this.site.isReadOnly = hide;
        this.requestDate.isReadOnly = hide;
        this.requester.isReadOnly = hide;
    }

    private async _loadRelatedPurchaseOrderLines(requisitionLineSysId: string) {
        const oldIsDirty = this.$.isDirty;
        const filter: Filter<PurchaseRequisitionLineToPurchaseOrderLine> = {
            _and: [
                { purchaseRequisitionLine: { document: { _id: { _eq: this.$.recordId } } } },
                ...(requisitionLineSysId ? [{ purchaseRequisitionLine: { _id: requisitionLineSysId } }] : []),
            ],
        };
        const resultOrderLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisitionLineToPurchaseOrderLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            purchaseRequisitionLine: { _id: true, document: { _id: true } },
                            purchaseOrderLine: {
                                _id: true,
                                _sortValue: true,
                                document: { _id: true, number: true, orderDate: true },
                                status: true,
                                quantity: true,
                                unit: { id: true, symbol: true, decimalDigits: true },
                            },
                            orderedQuantity: true,
                            unit: { _id: true, id: true, symbol: true, decimalDigits: true },
                        },
                        { filter },
                    ),
                )
                .execute(),
        );
        this.purchaseOrderLines.value.forEach(line => this.purchaseOrderLines.removeRecord(line._id));
        resultOrderLines.forEach(row => this.purchaseOrderLines.addOrUpdateRecordValue(row));

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    private async _loadSuppliers(rowData: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
        if (rowData.item) {
            this.suppliers.value = await loadSuppliersLookUpData(this.$.graph, rowData.item.id, this.site.value?.id);
        }
        this.selectedSupplier.value = rowData.supplier ?? this.selectedSupplier.value;
    }

    /** * @param requisitionLine : Price can be 0. return - 1 when record not found.  */
    private async _setPriceOrigin(
        requisitionLine: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>,
        isRecalculatePrices: boolean,
    ) {
        if (
            requisitionLine?.item &&
            Number(requisitionLine?.quantity) >= 0 &&
            requisitionLine?.currency &&
            requisitionLine?.unit &&
            requisitionLine?.supplier &&
            this.requestDate.value !== null
        ) {
            const purchasePrice = await getPurchasePrice(
                this.$.graph,
                requisitionLine.site?._id ?? '',
                requisitionLine.supplier?._id ?? '',
                requisitionLine.currency?._id ?? '',
                requisitionLine.item?._id ?? '',
                requisitionLine.unit?._id ?? '',
                parseFloat(requisitionLine.quantity ?? ''),
                new Date(this.requestDate.value),
                false,
            );
            if (isRecalculatePrices && purchasePrice.price === 0) {
                requisitionLine.grossPrice = '0';
                requisitionLine.priceOrigin = undefined;
            } else {
                requisitionLine.grossPrice = String(purchasePrice.price);
                requisitionLine.priceOrigin = purchasePrice.priceOrigin ?? undefined;
                requisitionLine.netPrice = String(purchasePrice.price);
                requisitionLine.totalTaxExcludedAmount = String(
                    purchasePrice.price >= 0 ? purchasePrice.price * Number(requisitionLine.quantity) : 0,
                );
            }
        }
    }

    private _onChangeCalculatePrices(requisitionLine: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
        const { resultNetPrice, resultTotalTaxExcludedAmount } = actionFunctions.calculatePrices({
            netPrice: Number(requisitionLine.netPrice),
            grossPrice: Number(requisitionLine.grossPrice),
            charge: Number(requisitionLine.charge),
            discount: Number(requisitionLine.discount),
            quantity: Number(requisitionLine.quantity),
            totalTaxExcludedAmount: Number(requisitionLine.totalTaxExcludedAmount),
            legalCompany: this.site.value?.legalCompany ?? {},
        });
        requisitionLine.netPrice = resultNetPrice;
        requisitionLine.totalTaxExcludedAmount = resultTotalTaxExcludedAmount;
    }

    @ui.decorators.pageAction<PurchaseRequisition>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: Number(this.site.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.section<PurchaseRequisition>({ isHidden: true, title: 'Request for approval' })
    requestApprovalSection: ui.containers.Section;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.requestApprovalSection;
        },
    })
    requestApprovalBlock: ui.containers.Block;

    @ui.decorators.textField<PurchaseRequisition>({
        parent() {
            return this.requestApprovalBlock;
        },
        title: 'To',
        helperText: 'A request for approval email will be sent to this address',
        isTransient: true,
        isFullWidth: true,
    })
    emailAddressApproval: ui.fields.Text;

    @ui.decorators.buttonField<PurchaseRequisition>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.requestApprovalBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__select_approver_button_text',
                'Select approver',
            );
        },
        async onClick() {
            await this.$.dialog
                .custom('info', this.approverSelectionBlock)
                .then(() => {
                    this.emailAddressApproval.value = this.selectedUser.value?.email
                        ? this.selectedUser.value?.email
                        : this.emailAddressApproval.value;
                })
                .finally(() => {
                    this.selectedUser.value = null;
                });
        },
    })
    selectApprover: ui.fields.Button;

    @ui.decorators.buttonField<PurchaseRequisition>({
        isTransient: true,
        parent() {
            return this.requestApprovalBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__send_approval_request_button_text',
                'Send',
            );
        },
        async onClick() {
            if (!this.emailAddressApproval.value) {
                return;
            }
            this.$.loader.isHidden = false;
            const isSend = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisition')
                .mutations.sendApprovalRequestMail(true, {
                    document: this.$.recordId ?? this.$.recordId ?? '',
                    user: { email: this.emailAddressApproval.value },
                })
                .execute();

            if (!isSend) {
                throw new Error(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_requisition__email_not_sent',
                        'Could not send request email.',
                    ),
                );
            }
            this.$.showToast(
                ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__email_sent', 'Email sent.'),
                { type: 'success' },
            );

            this.requestApprovalSection.isHidden = true;
            this.$.loader.isHidden = true;
            this.$.setPageClean();
            await this.$.refreshNavigationPanel();
            await this.$.router.refresh();
            this.$.finish();
        },
    })
    sendApprovalRequestButton: ui.fields.Button;

    @ui.decorators.block<PurchaseRequisition>({ title: 'Select approver' })
    approverSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseRequisition, User & { sortOrder: number; type: string }>({
        parent() {
            return this.approverSelectionBlock;
        },
        canSelect: false,
        title: 'User',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        isReadOnly: true,
        orderBy: { lastName: +1 },
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.label({
                title: 'Approver',
                bind: 'type',
                backgroundColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'backgroundColor');
                },
                borderColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'borderColor');
                },
                color(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__default_approver', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'textColor');
                },
            }),
            ui.nestedFields.technical({ bind: 'sortOrder' }),
        ],
        onRowClick(_rowId, rowData) {
            this.selectedUser.value = rowData;
        },
    })
    users: ui.fields.Table<User & { sortOrder: number; type: string }>;

    @ui.decorators.referenceField<PurchaseRequisition, User>({
        parent() {
            return this.approverSelectionBlock;
        },
        isReadOnly: true,
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'email',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
        ],
    })
    selectedUser: ui.fields.Reference;

    @ui.decorators.section<PurchaseRequisition>({
        isHidden: true,
        title: 'Request changes',
    })
    requestChangesSection: ui.containers.Section;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.requestChangesSection;
        },
    })
    requestChangesBlock: ui.containers.Block;

    @ui.decorators.textField<PurchaseRequisition>({
        parent() {
            return this.requestChangesBlock;
        },
        title: 'To',
        helperText: 'A request for changes will be sent to this address',
        isTransient: true,
        isFullWidth: true,
    })
    emailAddressChanges: ui.fields.Text;

    @ui.decorators.buttonField<PurchaseRequisition>({
        isTransient: true,
        parent() {
            return this.requestChangesBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_requisition__send_change_request_button_text',
                'Send',
            );
        },
        async onClick() {
            if (!this.$.recordId || !this.emailAddressChanges.value) {
                return;
            }
            const isSend = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseRequisition')
                .mutations.sendRequestChangesMail(true, {
                    document: this.$.recordId,
                    user: { email: this.emailAddressChanges.value },
                })
                .execute();

            if (isSend) {
                this.$.showToast(
                    ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__email_sent', 'Email sent.'),
                    { type: 'success' },
                );
            } else {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_requisition__email_not_sent',
                        'Could not send request email.',
                    ),
                    { type: 'error' },
                );
            }
            this.$.setPageClean();
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseRequisition`, { _id: this.$.recordId });
        },
    })
    sendChangeRequestButton: ui.fields.Button;

    @ui.decorators.section<PurchaseRequisition>({ isHidden: true, isTitleHidden: true })
    supplierSelectionSection: ui.containers.Section;

    @ui.decorators.block<PurchaseRequisition>({
        parent() {
            return this.supplierSelectionSection;
        },
        isTitleHidden: true,
    })
    supplierSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseRequisition, SupplierLookUpData>({
        parent() {
            return this.supplierSelectionBlock;
        },
        canSelect: false,
        title: 'Supplier',
        isTitleHidden: true,
        isTransient: true,
        isReadOnly: true,
        pageSize: 50,
        orderBy: { sortOrder: +1 },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.numeric({ title: 'Lead time', bind: 'purchaseLeadTime', canFilter: false }),
            ui.nestedFields.label({
                title: 'Type',
                bind: 'type',
                backgroundColor(value) {
                    switch (value) {
                        case ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_requisition__default_supplier',
                            'Default',
                        ):
                            return ui.tokens.colorsSemanticPositive500;
                        case ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_requisition__referenced_supplier',
                            'Referenced',
                        ):
                            return ui.tokens.colorsSemanticCaution500;
                        case ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__other_supplier', 'Other'):
                            return ui.tokens.colorsSemanticNegative500;
                        default:
                            return '#FFFFFF';
                    }
                },
            }),
            ui.nestedFields.technical({ bind: 'sortOrder' }),
            ui.nestedFields.reference<PurchaseRequisition, SupplierLookUpData, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isHidden: true,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
        ],
        async onRowClick(_rowId, rowData: SupplierLookUpData) {
            await suppliersLookUpOnRowClick(this, rowData.sortOrder, this.suppliers.value);
            this.$.loader.isHidden = false;
            this.selectedSupplier.value = rowData;
            this.$.loader.isHidden = true;
        },
    })
    suppliers: ui.fields.Table<SupplierLookUpData>;

    @ui.decorators.referenceField<PurchaseRequisition, Supplier>({
        parent() {
            return this.supplierSelectionBlock;
        },
        isReadOnly: true,
        isTransient: true,
        isFullWidth: true,
        node: '@sage/xtrem-master-data/Supplier',
        tunnelPage: '@sage/xtrem-master-data/Supplier',
        valueField: { businessEntity: { name: true } },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
            ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { taxIdNumber: true } } }),
            ui.nestedFields.text({ title: 'Country', bind: { businessEntity: { country: { name: true } } } }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                    ui.nestedFields.technical<PurchaseRequisition, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
        ],
    })
    selectedSupplier: ui.fields.Reference<Supplier>;

    async initPage() {
        if (this.$.recordId) {
            await this._loadRelatedPurchaseOrderLines('');
            this.setEarliestExpectedDate();
        } else {
            this.requestDate.value = '';
        }
        this._defaultDimensionsAttributes = dimensionPanelHelpers.initDefaultDimensions();
    }

    @ui.decorators.pageAction<PurchaseRequisition>({
        title: 'Confirm',
        isHidden: true,
        isTransient: true,
        isDisabled() {
            return this.$.isDirty;
        },
        async onClick() {
            if (this.number.value && this.$.recordId) {
                await actionFunctions.confirmAction({
                    purchaseRequisitionPage: this,
                    recordNumber: this.number.value,
                    isConfirmed: true,
                    recordId: this.$.recordId,
                });
            }
        },
    })
    confirm: ui.PageAction;

    setSupplierInfo(rowData: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
        rowData.supplier = this.selectedSupplier.value ?? undefined;
        rowData.currency = this.selectedSupplier.value?.businessEntity?.currency;
        rowData.internalNote = this.selectedSupplier.value?.internalNote;
        this.internalNoteLine.value = this.selectedSupplier.value?.internalNote?.value ?? '';
    }

    _setStepSequenceStatusObject(
        stepSequenceValues: PurchaseRequisitionStepSequenceStatus,
    ): Dict<ui.StepSequenceStatus> {
        return {
            [this.requisitionStepSequenceCreate]: stepSequenceValues.create,
            [this.requisitionStepSequenceApprove]: stepSequenceValues.approve,
            [this.requisitionStepSequenceConfirm]: stepSequenceValues.confirm,
            [this.requisitionStepSequenceOrder]: stepSequenceValues.order,
        };
    }

    private setEarliestExpectedDate() {
        this.earliestExpectedDate.icon = undefined;
        this.earliestExpectedDate.iconColor = undefined;
        const earliestDateLine = this.lines.value.reduce(
            (prevLine, line) =>
                line?.needByDate && prevLine?.needByDate && prevLine.needByDate < line.needByDate ? prevLine : line,
            {} as ui.PartialNodeWithId<PurchaseRequisitionLine>,
        );
        if (earliestDateLine?.needByDate) {
            this.earliestExpectedDate.value = earliestDateLine?.needByDate
                ? `${ui.formatDateToCurrentLocale(earliestDateLine.needByDate, 'LongMonthDayYear')}`
                : null;
            if (earliestDateLine.needByDate > new Date(Date.now()).toISOString().substring(0, 10)) {
                this.earliestExpectedDate.icon = 'tick';
                this.earliestExpectedDate.iconColor = ui.tokens.colorsSemanticPositive600;
            }
        }
    }

    private async submitForApproval(receivingSite: string) {
        const { usersList, emailAddressApproval } = await actionFunctions.loadApprovers({
            purchaseRequisitionPage: this,
            siteId: receivingSite,
        });
        this.users.value = usersList;
        this.emailAddressApproval.value = emailAddressApproval;

        this.requestApprovalSection.isHidden = false;
        await this.$.dialog.custom('info', this.requestApprovalSection, {
            cancelButton: { isHidden: true },
            acceptButton: { isHidden: true },
            resolveOnCancel: true,
        });
        this.$.setPageClean();
        this.requestApprovalSection.isHidden = true;
    }

    private async purchaseRequisitionUpdatePrice(rowData: ui.PartialCollectionValue<PurchaseRequisitionLineBinding>) {
        let isRecalculatePrices = true;

        if (+(rowData._id ?? '') > 0) {
            isRecalculatePrices = await recalculatePricesDialog(this);
        }

        if (isRecalculatePrices) {
            await this._setPriceOrigin(rowData, isRecalculatePrices);
            this._onChangeCalculatePrices(rowData);
            this.lines.addOrUpdateRecordValue(rowData);
        }
    }
}
