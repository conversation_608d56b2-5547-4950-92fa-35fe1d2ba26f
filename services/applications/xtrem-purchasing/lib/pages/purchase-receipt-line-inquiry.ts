import { withoutEdges } from '@sage/xtrem-client';
import type {
    Graph<PERSON>pi,
    PurchaseReceiptLine,
    PurchaseReceiptLineToPurchaseInvoiceLine,
} from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import * as PillColorPurchase from '../client-functions/pill-color';
import { purchaseInquiries } from '../menu-items/purchasing-inquiry';

@ui.decorators.page<PurchaseReceiptLineInquiry, PurchaseReceiptLine>({
    menuItem: purchaseInquiries,
    priority: 70,
    title: 'Purchase receipt line',
    module: 'purchasing',
    mode: 'default',
    node: '@sage/xtrem-purchasing/PurchaseReceiptLine',
    access: { node: '@sage/xtrem-purchasing/PurchaseReceipt', bind: '$read' },
    navigationPanel: {
        onSelect() {
            // We have to prevent selecting a line to avoid opening the "normal" page (which is not even defined).
            return true;
        },
        listItem: {
            title: ui.nestedFields.text({ bind: '_id', isHidden: true }),
            companyName: ui.nestedFields.reference({
                bind: { site: { legalCompany: true } },
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'name',
            }),
            companyId: ui.nestedFields.text({
                bind: { site: { legalCompany: { id: true } } },
                title: 'Company ID',
                isHiddenOnMainField: true,
            }),
            siteName: ui.nestedFields.reference({
                bind: 'site',
                title: 'Purchasing site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
                isHiddenOnMainField: true,
            }),
            siteId: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Purchasing site ID',
                isHiddenOnMainField: true,
            }),
            stockSiteName: ui.nestedFields.reference({
                bind: 'stockSite',
                title: 'Receiving site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            stockSiteId: ui.nestedFields.text({
                bind: { stockSite: { id: true } },
                title: 'Receiving site ID',
                isHiddenOnMainField: true,
            }),
            supplierName: ui.nestedFields.reference({
                bind: { document: { supplier: true } },
                title: 'Supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
                valueField: 'name',
            }),
            supplierId: ui.nestedFields.text({ bind: { document: { supplier: { id: true } } }, title: 'Supplier ID' }),
            supplierItem: ui.nestedFields.reference({
                bind: 'itemSupplier',
                title: 'Supplier item',
                node: '@sage/xtrem-master-data/ItemSupplier',
                valueField: 'supplierItemName',
                isHiddenOnMainField: true,
            }),
            supplierItemCode: ui.nestedFields.text({
                bind: { itemSupplier: { supplierItemCode: true } },
                title: 'Supplier item code',
                isHiddenOnMainField: true,
            }),
            purchaseOrderNumber: ui.nestedFields.reference({
                title: 'Purchase receipt',
                bind: 'document',
                tunnelPage: '@sage/xtrem-purchasing/PurchaseReceipt',
                valueField: 'number',
            }),
            status: ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData.status),
            }),
            date: ui.nestedFields.date({ bind: { document: { date: true } }, title: 'Receipt date' }),
            itemName: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            itemId: ui.nestedFields.text({ bind: { item: { id: true } }, title: 'Item ID' }),
            itemDescription: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            quantity: ui.nestedFields.numeric({
                bind: 'quantity',
                title: 'Quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
            }),
            unit: ui.nestedFields.reference({
                bind: 'unit',
                title: 'Unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            invoiceStatus: ui.nestedFields.label({
                bind: 'lineInvoiceStatus',
                title: 'Invoice status',
                optionType: '@sage/xtrem-purchasing/PurchaseOrderInvoiceStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderInvoiceStatus', rowData.lineInvoiceStatus),
            }),
            lineReturnStatus: ui.nestedFields.label({
                bind: 'lineReturnStatus',
                title: 'Return status',
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptReturnStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseReceiptReturnStatus', rowData.lineReturnStatus),
            }),
            invoicedQuantity: ui.nestedFields.numeric({
                bind: 'invoicedQuantity',
                title: 'Invoiced quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            returnedQuantity: ui.nestedFields.numeric({
                bind: 'returnedQuantity',
                title: 'Returned quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            remainingQuantityToInvoice: ui.nestedFields.numeric({
                bind: 'remainingQuantityToInvoice',
                title: 'Remaining quantity to invoice',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            remainingReturnQuantity: ui.nestedFields.numeric({
                bind: 'remainingReturnQuantity',
                title: 'Remaining quantity to return',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                groupAggregationMethod: 'sum',
                isHiddenOnMainField: true,
            }),
            currencyName: ui.nestedFields.reference({
                bind: { document: { currency: true } },
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            currencyId: ui.nestedFields.text({
                bind: { document: { currency: { id: true } } },
                title: 'Currency ID',
                isHiddenOnMainField: true,
            }),
            companyCurrencyName: ui.nestedFields.reference({
                bind: { document: { companyCurrency: true } },
                title: 'Company currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            companyCurrencyId: ui.nestedFields.text({
                bind: { document: { companyCurrency: { id: true } } },
                title: 'Company currency ID',
                isHiddenOnMainField: true,
            }),
            netPrice: ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net price',
                unit: (_rowId, rowData) => rowData?.document?.currency,
            }),
            amountExcludingTax: ui.nestedFields.numeric({
                bind: 'amountExcludingTax',
                title: 'Received amount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                groupAggregationMethod: 'sum',
            }),
            amountExcludingTaxInCompanyCurrency: ui.nestedFields.numeric({
                bind: 'amountExcludingTaxInCompanyCurrency',
                title: 'Received amount in company currency',
                unit: (_rowId, rowData) => rowData?.document?.companyCurrency,
                groupAggregationMethod: 'sum',
            }),
            numberOfPurchaseInvoiceLines: ui.nestedFields.technical({ bind: 'numberOfPurchaseInvoiceLines' }),
        },
        dropdownActions: [
            {
                title: 'Invoice lines',
                icon: 'document_right_align',
                isHidden(_recordId: string, rowItem: PartialCollectionValueWithIds<PurchaseReceiptLine>) {
                    return (rowItem.numberOfPurchaseInvoiceLines ?? 0) <= 0;
                },
                async onClick(_recordId: string, rowItem: PartialCollectionValueWithIds<PurchaseReceiptLine>) {
                    const sysIds = withoutEdges(
                        await this.$.graph
                            .node('@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseInvoiceLine')
                            .query(
                                ui.queryUtils.edgesSelector<PurchaseReceiptLineToPurchaseInvoiceLine>(
                                    {
                                        purchaseInvoiceLine: { _id: true },
                                    },
                                    {
                                        filter: {
                                            purchaseReceiptLine: { _id: rowItem._id },
                                        },
                                    },
                                ),
                            )
                            .execute(),
                    ).map(line => line.purchaseInvoiceLine._id);

                    await this.$.dialog.page(
                        '@sage/xtrem-purchasing/PurchaseInvoiceCreditMemoLineInquiry',
                        {
                            [ui.QUERY_PARAM_CUSTOM_MAIN_LIST_FILTER]: JSON.stringify({
                                _id: { _in: sysIds },
                            }),
                        },
                        {
                            isMainListDisplayedInDialog: true,
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            },
        ],
        orderBy: { site: { id: 1 }, item: { id: 1 } },
    },
})
export class PurchaseReceiptLineInquiry extends ui.Page<GraphApi, PurchaseReceiptLine> {}
