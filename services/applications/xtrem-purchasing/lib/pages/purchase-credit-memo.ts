import { asyncArray } from '@sage/xtrem-async-helper';
import * as authorizationFilters from '@sage/xtrem-authorization/build/lib/client-functions/filters';
import type { Dict, ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import { date } from '@sage/xtrem-date-time';
import { isPostedInProgressError } from '@sage/xtrem-distribution/build/lib/client-functions/common';
import type { BaseOpenItem, FinanceTransactionBinding, PaymentTracking } from '@sage/xtrem-finance-data-api';
import * as attributesAndDimensions from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import { initDefaultDimensions } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import * as dimensionPanelHelpers from '@sage/xtrem-finance-data/build/lib/client-functions/dimension-panel-helpers';
import * as displayButtonsFinance from '@sage/xtrem-finance-data/build/lib/client-functions/display-buttons-finance';
import type * as financeInterfaces from '@sage/xtrem-finance-data/build/lib/client-functions/interfaces/dimension';
import * as PillColorFinance from '@sage/xtrem-finance-data/build/lib/client-functions/pill-color';

import type {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    Currency,
    Item,
    PaymentTerm,
    ReasonCode,
    Supplier,
    TaxCalculationStatus,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import {
    convertFromTo,
    getCompanyPriceScale,
    getPurchaseUnit,
} from '@sage/xtrem-master-data/build/lib/client-functions/common';
import {
    getCountryPostCodeTitle,
    getCountryRegionTitle,
} from '@sage/xtrem-master-data/build/lib/client-functions/country-fields-title';
import { validCapabilities } from '@sage/xtrem-master-data/build/lib/client-functions/fields-properties';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import { purchasing } from '@sage/xtrem-master-data/build/lib/menu-items/purchasing';
import { getConcatenatedAddress } from '@sage/xtrem-master-data/build/lib/shared-functions/address-functions';
import { convertAmount, scaleOfCurrent } from '@sage/xtrem-master-data/build/lib/shared-functions/common';
import { fetchDefaultsForDueDate, stockJournalTitle } from '@sage/xtrem-master-data/lib/client-functions/common';
import { checkStockSite } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import type {
    GraphApi,
    PurchaseCreditMemoDisplayStatus,
    PurchaseCreditMemoLine,
    PurchaseCreditMemoLineBinding,
    PurchaseCreditMemo as PurchaseCreditMemoNode,
    PurchaseCreditMemoStatus,
    PurchaseInvoiceLine,
    PurchaseInvoiceLineToPurchaseCreditMemoLine,
    PurchaseReturnLine,
    PurchaseReturnLineToPurchaseCreditMemoLine,
} from '@sage/xtrem-purchasing-api';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as StockDataUtils from '@sage/xtrem-stock-data/build/lib/client-functions/utils';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import type { Company, Site, User } from '@sage/xtrem-system-api';
import {
    setDisplayOfCommonPageActions,
    setOrderOfPageBusinessActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as MainListActions from '@sage/xtrem-system/build/lib/client-functions/main-list-actions';
import type { DocumentTax, DocumentTaxBinding, DocumentTaxInput } from '@sage/xtrem-tax-api';
import { TotalTaxCalculator } from '@sage/xtrem-tax/build/lib/client-functions/classes/total-tax-calculator';
import { recalculateTaxCalculationStatus } from '@sage/xtrem-tax/build/lib/client-functions/display-taxes';
import { DocumentTypeEnum } from '@sage/xtrem-tax/build/lib/client-functions/interfaces/display-taxes';
import * as ui from '@sage/xtrem-ui';
import type { PdfPluginProperties } from '@sage/xtrem-ui-plugin-pdf';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import {
    confirmDialogWithAcceptButtonText,
    getFullyPaidFilter,
    getNotFullyPaidFilter,
    getPurchasePrice,
    setOverwriteNote,
} from '../client-functions/common';
import { addLineFromInvoice } from '../client-functions/credit-note-from-invoice';
import { addLineFromReturn } from '../client-functions/credit-note-from-return';
import * as displayButtons from '../client-functions/display-buttons-purchase-credit-memo';
import * as PillColorPurchase from '../client-functions/pill-color';
import * as actionFunctions from '../client-functions/purchase-credit-memo-actions-functions';
import { dueDateDefault } from '../client-functions/purchase-invoice-credit-memo-common';
import { displayTaxes } from '../client-functions/shared/display-taxes';
import type { PurchaseCreditMemoStepSequenceStatus } from '../client-functions/shared/interfaces';
import { calculateLinePrices, isExchangeRateHidden } from '../client-functions/shared/page-functions';
import { isPurchaseCreditMemoLineActionDisabled } from '../shared-functions/edit-rules';

@ui.decorators.page<PurchaseCreditMemo, PurchaseCreditMemoNode>({
    title: 'Purchase credit memo',
    objectTypeSingular: 'Purchase credit memo',
    objectTypePlural: 'Purchase credit memos',
    idField() {
        return this.number;
    },
    menuItem: purchasing,
    node: '@sage/xtrem-purchasing/PurchaseCreditMemo',
    module: 'purchasing',
    hasAttachmentsSection: true,
    mode: 'tabs',
    priority: 800,
    headerLabel() {
        return this.displayStatus;
    },
    createAction() {
        return this.$standardNewAction;
    },
    businessActions() {
        return setOrderOfPageBusinessActions({
            save: this.save,
            cancel: this.$standardCancelAction,
            businessActions: [this.post, this.repost],
        });
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [
                this.$standardOpenRecordHistoryAction,
                this.$standardOpenCustomizationPageWizardAction,
                ui.menuSeparator(),
                this.defaultDimension,
                ui.menuSeparator(),
                this.notifyBuyer,
                ui.menuSeparator(),
                this.stockJournal,
            ],
        });
    },
    headerSection() {
        return this.headerSection;
    },
    onDirtyStateUpdated(isDirty: boolean) {
        this.creditMemoStepSequence.statuses = this.getDisplayStatusStepSequence();
        this._manageDisplayApplicativePageActions(isDirty);
    },
    async onLoad() {
        this.creditMemoStepSequence.statuses = this.getDisplayStatusStepSequence();
        await this.initPage();
        this._manageDisplayApplicativePageActions(false);
        this.initPosting();
        if (this.pdfSupplierCreditMemo.value) {
            this.pdf.value = this.pdfSupplierCreditMemo.value.value;
        }
        if (!this.totalAmountIncludingTax.value) {
            this.totalAmountIncludingTax.value = 0;
        }
        this.$.setPageClean();
        TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxes, this.taxEngine.value ?? '');
        this._manageHeaderProperties();
    },
    onError(error: string | (Error & { errors: Array<any> })) {
        return MasterDataUtils.formatError(this, error);
    },
    navigationPanel: {
        orderBy: { creditMemoDate: -1, number: -1 },
        listItem: {
            title: ui.nestedFields.link({
                title: 'Number',
                bind: 'number',
                page: '@sage/xtrem-purchasing/PurchaseCreditMemo',
                queryParameters: (_value, rowValue) => ({ _id: rowValue?._id ?? '' }),
                isMandatory: true,
            }),
            line2: ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoNode, Supplier>({
                bind: 'billBySupplier',
                title: 'Bill-by supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                valueField: { businessEntity: { name: true } },
                columns: [
                    ui.nestedFields.technical<PurchaseCreditMemo, Supplier, User>({
                        bind: 'defaultBuyer',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'email' }),
                            ui.nestedFields.technical({ bind: 'firstName' }),
                            ui.nestedFields.technical({ bind: 'lastName' }),
                        ],
                    }),
                ],
            }),
            id: ui.nestedFields.text({
                bind: { billBySupplier: { id: true } },
                title: 'Bill-by supplier ID',
                isHiddenOnMainField: true,
            }),
            line2Right: ui.nestedFields.date({ bind: 'creditMemoDate', title: 'Credit memo date', isMandatory: true }),
            line3: ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoNode, Site>({
                title: 'Financial site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                tunnelPage: undefined,
            }),
            line_4: ui.nestedFields.numeric<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'totalAmountIncludingTax',
                title: 'Total Including tax',
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
                scale: null,
            }),
            line_5: ui.nestedFields.date({ title: 'Due date', bind: 'dueDate' }),
            line6: ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                title: 'Reason',
                bind: 'reason',
                tunnelPage: undefined,
            }),
            titleRight: ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseCreditMemoDisplayStatus',
                isMandatory: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            line7: ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Currency',
                isHiddenOnMainField: true,
            }),
            line8: ui.nestedFields.numeric<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'totalAmountExcludingTax',
                title: 'Total excluding tax',
                isHiddenOnMainField: true,
                scale: null,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
            }),
            line9: ui.nestedFields.numeric<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'totalTaxAmount',
                title: 'Tax',
                isHiddenOnMainField: true,
                scale: null,
                unit: (_rowId, rowData) => rowData?.transactionCurrency,
            }),
            line11: ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'paymentTerm',
                isHiddenOnMainField: true,
            }),
            line12: ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoNode, Supplier>({
                title: 'Pay to supplier',
                bind: 'payToSupplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: undefined,
                isHiddenOnMainField: true,
                valueField: { businessEntity: { name: true } },
            }),
            idPayToSupplier: ui.nestedFields.text({
                bind: { payToSupplier: { id: true } },
                title: 'Pay-to supplier ID',
                isHiddenOnMainField: true,
            }),
            line13: ui.nestedFields.text<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                title: 'Supplier document reference',
                bind: 'supplierDocumentNumber',
                isHiddenOnMainField: true,
            }),
            line14: ui.nestedFields.date<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                title: 'Supplier document date',
                bind: 'supplierDocumentDate',
                isHiddenOnMainField: true,
            }),
            line15: ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'transactionCurrency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: undefined,
                valueField: 'name',
                title: 'Transaction currency',
                isHiddenOnMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
            paymentStatus: ui.nestedFields.label<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: { paymentTracking: { status: true } },
                title: 'Payment status',
                optionType: '@sage/xtrem-finance-data/OpenItemStatus',
                isHiddenOnMainField: true,
                isHidden() {
                    return !this.$.isServiceOptionEnabled('paymentTrackingOption');
                },
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.paymentTracking?.status),
            }),
            totalPayments: ui.nestedFields.numeric<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: { paymentTracking: { amountPaid: true } },
                title: 'Total payments',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                isHidden() {
                    return !this.$.isServiceOptionEnabled('paymentTrackingOption');
                },
            }),
            netBalance: ui.nestedFields.numeric<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'netBalance',
                title: 'Net balance',
                isHiddenOnMainField: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            status: ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoNode>({ bind: 'status' }),
            taxCalculationStatus: ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'taxCalculationStatus',
            }),
            varianceTotalAmountIncludingTax: ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'varianceTotalAmountIncludingTax',
            }),
            totalLinesCount: ui.nestedFields.aggregate<PurchaseCreditMemo>({
                aggregateOn: '_id',
                aggregationMethod: 'distinctCount',
                bind: 'lines',
                isHidden: true,
            }),
            openItemSysId: ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'openItemSysId',
            }),
            isOpenItemPageOptionActive: ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoNode>({
                bind: 'isOpenItemPageOptionActive',
            }),
        },
        optionsMenu(_graph, _storage, _queryParam, _username, _userCode, serviceOptions) {
            return Promise.resolve([
                {
                    title: 'All open statuses',
                    graphQLFilter: { displayStatus: { _nin: ['posted', 'partiallyPaid', 'paid'] } },
                },
                { title: 'All statuses', graphQLFilter: {} },
                { title: 'Draft', graphQLFilter: { displayStatus: { _eq: 'draft' } } },
                { title: 'Posted', graphQLFilter: { displayStatus: { _eq: 'posted' } } },
                { title: 'Posting in progress', graphQLFilter: { displayStatus: { _eq: 'postingInProgress' } } },
                ...(serviceOptions.paymentTrackingOption ? [getNotFullyPaidFilter()] : []),
                ...(serviceOptions.paymentTrackingOption ? [getFullyPaidFilter()] : []),
                { title: 'Posting error', graphQLFilter: { displayStatus: { _eq: 'postingError' } } },
                { title: 'Tax calculation failed', graphQLFilter: { displayStatus: { _eq: 'taxCalculationFailed' } } },
                { title: 'Stock error', graphQLFilter: { displayStatus: { _eq: 'stockError' } } },
            ] as ui.containers.OptionsMenuItemType<PurchaseCreditMemoNode>[]);
        },
        dropdownActions: [
            {
                title: 'Post',
                icon: 'none',
                refreshesMainList: 'list',
                async onClick(recordId: string, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    if (recordId && rowItem.taxCalculationStatus) {
                        await actionFunctions.postAction({
                            purchaseCreditMemoPage: this,
                            recordId,
                            isCalledFromRecordPage: false,
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                            varianceTotalAmountIncludingTax: Number(rowItem.varianceTotalAmountIncludingTax),
                            lines: Number(rowItem.lines),
                        });
                    }
                    this._manageHeaderProperties();
                },
                isHidden: (recordId, rowItem) =>
                    displayButtons.isHiddenButtonPostAction({
                        parameters: {
                            status: rowItem.status as PurchaseCreditMemoStatus,
                            varianceTotalAmountIncludingTax: Number(rowItem.varianceTotalAmountIncludingTax),
                            taxCalculationStatus: rowItem.taxCalculationStatus,
                        },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            {
                title: 'Open item',
                icon: 'none',
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    await this.$.dialog.page(
                        `@sage/xtrem-finance/AccountsPayableOpenItem`,
                        { _id: rowItem.openItemSysId ?? '', fromPurchasing: true },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
                isHidden(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    return rowItem.status !== 'posted' || !rowItem.isOpenItemPageOptionActive;
                },
            },
            ui.menuSeparator(),
            {
                title: 'Send for matching',
                icon: 'email_switch',
                refreshesMainList: 'record',
                async onClick(_recordId, rowItem) {
                    this.billBySupplier.value = rowItem.billBySupplier ?? null;
                    this.requestApprovalSection.isHidden = false;

                    await this._loadBuyers();
                    await this.$.dialog.custom('info', this.requestApprovalSection, {
                        cancelButton: { isHidden: true },
                        acceptButton: { isHidden: true },
                        resolveOnCancel: true,
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    return displayButtons.isHiddenButtonNotifyBuyerAction({
                        parameters: { status: rowItem.status as PurchaseCreditMemoStatus },
                        recordId,
                    });
                },
                isDisabled: (recordId, rowItem) =>
                    displayButtons.isDisabledButtonNotifyBuyerAction({
                        parameters: { status: rowItem.status },
                        recordId,
                        isDirty: false,
                    }) ?? false,
            },
            ui.menuSeparator(),
            {
                icon: 'none',
                title: 'Set dimensions',
                async onClick(_rowId, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    await actionFunctions.setDimensions({
                        purchaseCreditMemoPage: this,
                        recordNumber: rowItem.number ?? '',
                        status: rowItem.status ?? null,
                        isRepost: this.isRepost,
                        site: rowItem.site,
                        supplier: rowItem.supplier,
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    return displayButtons.isHiddenButtonDefaultDimensionAction({
                        parameters: { status: rowItem.status as PurchaseCreditMemoStatus },
                        recordId,
                    });
                },
            },
            ui.menuSeparator(),
            {
                title: 'Delete',
                icon: 'bin',
                refreshesMainList: 'list',
                isDestructive: true,
                onError(error: string | (Error & { errors: Array<any> })) {
                    this.$.loader.isHidden = true;
                    return MasterDataUtils.formatError(this, error);
                },
                async onClick(_recordId: string, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    await MainListActions.deleteRecord<GraphApi>(this, {
                        _id: rowItem._id,
                        nodeName: '@sage/xtrem-purchasing/PurchaseCreditMemo',
                    });
                },
                isHidden(recordId, rowItem: ui.PartialNodeWithId<PurchaseCreditMemoNode>) {
                    return displayButtons.isHiddenButtonDeleteAction({
                        parameters: { status: rowItem.status },
                        recordId,
                    });
                },
            },
        ],
    },
})
export class PurchaseCreditMemo extends ui.Page<GraphApi> {
    displayTaxesClicked = false;

    isRepost: boolean;

    canEditPaymentDataAfterPost: boolean;

    canEditTaxDataAfterPost: boolean;

    wereTaxesEdited: boolean;

    currentSelectedLineId: string;

    private readonly creditMemoStepSequenceCreate = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_creation',
        'Create',
    );

    private readonly creditMemoStepSequencePost = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_posting',
        'Post',
    );

    private readonly creditMemoStepSequencePay = ui.localize(
        '@sage/xtrem-purchasing/pages__purchase_credit_memo__step_sequence_pay',
        'Pay',
    );

    getDisplayStatusStepSequence(): Dict<ui.StepSequenceStatus> {
        if (this.$.isServiceOptionEnabled('paymentTrackingOption')) {
            if (this.paymentTracking?.value?.status === 'paid') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    post: 'complete',
                    pay: 'complete',
                });
            }
            if (this.paymentTracking?.value?.status === 'partiallyPaid') {
                return this._setStepSequenceStatusObject({
                    create: 'complete',
                    post: 'complete',
                    pay: 'current',
                });
            }
        }
        if (['inProgress', 'error'].includes(this.status.value ?? '')) {
            return this._setStepSequenceStatusObject({ create: 'complete', post: 'current' });
        }
        if (this.status.value === 'posted') {
            return this._setStepSequenceStatusObject({ create: 'complete', post: 'complete' });
        }
        return this._setStepSequenceStatusObject({ create: 'current', post: 'incomplete' });
    }

    private _manageDisplayApplicativePageActions(isDirty = false) {
        setDisplayOfCommonPageActions<PurchaseCreditMemo>({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
            businessActions: [this.post, this.repost],
        });
        this._manageDisplayAdditionalPageActions(isDirty);
    }

    private _manageDisplayAdditionalPageActions(isDirty = false) {
        this.manageDisplayButtonCRUDActions(isDirty);
        this.manageDisplayButtonAllOtherActions(isDirty);
    }

    private manageDisplayButtonCRUDActions(isDirty = false) {
        this.save.isDisabled = displayButtons.isDisabledButtonSaveAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardCancelAction.isDisabled = displayButtons.isDisabledButtonCancelAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
        this.$standardDeleteAction.isHidden = displayButtons.isHiddenButtonDeleteAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
        });
    }

    private manageDisplayButtonAllOtherActions(isDirty = false) {
        // footer business actions
        this.manageDisplayButtonPostAction(isDirty);
        this.manageDisplayButtonRepostAction(isDirty);
        // other header actions
        this.manageDisplayButtonDefaultDimensionAction();
        this.manageDisplayButtonNotifyBuyerAction(isDirty);
        this.manageDisplayButtonSelectFromReturnAction();
        this.manageDisplayButtonSelectFromInvoiceAction();
        this.manageDisplayLinePhantomRow();
        this.manageDisplayButtonGoToSysNotificationPageAction();
    }

    private manageDisplayButtonPostAction(isDirty: boolean) {
        this.post.isHidden = displayButtons.isHiddenButtonPostAction({
            parameters: {
                status: this.status.value as PurchaseCreditMemoStatus,
                varianceTotalAmountIncludingTax: this.varianceTotalAmountIncludingTax.value,
                taxCalculationStatus: this.taxCalculationStatus.value,
            },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonRepostAction(isDirty: boolean) {
        this.repost.isHidden = displayButtons.isHiddenButtonRepostAction({
            parameters: { isRepost: this.isRepost },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayLinePhantomRow() {
        this.lines.isPhantomRowDisabled = displayButtons.isDisabledLinePhantomRow({
            parameters: { status: this.status.value, site: this.site.value, billBySupplier: this.billBySupplier.value },
        });
    }

    private manageDisplayButtonNotifyBuyerAction(isDirty: boolean) {
        this.notifyBuyer.isHidden = displayButtons.isHiddenButtonNotifyBuyerAction({
            parameters: { status: this.status.value as PurchaseCreditMemoStatus },
            recordId: this.$.recordId,
        });

        this.notifyBuyer.isDisabled = displayButtons.isDisabledButtonNotifyBuyerAction({
            parameters: { status: this.status.value },
            recordId: this.$.recordId,
            isDirty,
        });
    }

    private manageDisplayButtonDefaultDimensionAction() {
        this.defaultDimension.isHidden = displayButtons.isHiddenButtonDefaultDimensionAction({
            parameters: { status: this.status.value as PurchaseCreditMemoStatus },
            recordId: this.$.recordId,
        });

        this.defaultDimension.isDisabled = displayButtons.isDisabledButtonDefaultDimensionAction({
            parameters: { site: this.site.value, billBySupplier: this.billBySupplier.value },
        });
    }

    private manageDisplayButtonSelectFromReturnAction() {
        this.selectFromReturn.isDisabled = displayButtons.isDisabledButtonSelectFromReturnAction({
            parameters: { site: this.site.value, status: this.status.value as PurchaseCreditMemoStatus },
        });

        this.selectFromReturn.isHidden = displayButtons.isHiddenButtonSelectFromReturnAction({
            parameters: { site: this.site.value, status: this.status.value as PurchaseCreditMemoStatus },
        });
    }

    private manageDisplayButtonSelectFromInvoiceAction() {
        this.selectFromInvoice.isDisabled = displayButtons.isDisabledButtonSelectFromInvoiceAction({
            parameters: { site: this.site.value, status: this.status.value as PurchaseCreditMemoStatus },
        });

        this.selectFromInvoice.isHidden = displayButtons.isHiddenButtonSelectFromInvoiceAction({
            parameters: { site: this.site.value, status: this.status.value as PurchaseCreditMemoStatus },
        });
    }

    private manageDisplayButtonGoToSysNotificationPageAction() {
        this.goToSysNotificationPage.isHidden = displayButtonsFinance.isHiddenButtonGoToSysNotificationPageAction({
            parameters: {
                financeIntegrationStatus: this.postingDetails.value.map(
                    financeDocument => financeDocument.postingStatus || '',
                ),
            },
            recordId: this.$.recordId,
        });
    }

    _defaultDimensionsAttributes: financeInterfaces.DefaultDimensions;

    private _isAddNewLine: boolean = false;

    getSerializedValues() {
        const { values } = this.$;
        if (Number(values.billByAddress._id) < 0) {
            delete values.billByAddress._id;
        }
        if (Number(values.payToAddress._id) < 0) {
            delete values.payToAddress._id;
        }
        delete values.calculatedTotalAmountExcludingTax;

        if (this.isOverwriteNote.value !== undefined) {
            values.isOverwriteNote = this.isOverwriteNote.value;
        }
        values.lines?.forEach((line: Partial<PurchaseCreditMemoLineBinding>) => {
            delete line.currency;
            delete line.quantityInStockUnit;
            delete line.origin;
            // unitToStockUnitConversionFactor is Frozen in update mode
            if (+(line._id ?? 0) > 0) {
                delete line.unitToStockUnitConversionFactor;
            }
        });
        return values;
    }

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        title: 'Save',
        buttonType: 'primary',
        access: { bind: '$update' },
        onError() {
            this.$.loader.isHidden = true;
        },
        async onClick() {
            if (!this.$.recordId) {
                this.isOverwriteNote.value = await setOverwriteNote(this, {
                    linesFromSingleDocument: this.linesFromSingleDocument(),
                    headerNotesChanged: this.headerNotesChanged(),
                    lineNotesChanged: this.lineNotesChanged(),
                    isTransferHeaderNote: this.linkedDocumentIsTransferHeaderNote(),
                    isTransferLineNote: this.linkedDocumentIsTransferLineNote(),
                });
            }
            const isDialog = this.$.isInDialog;
            await this.$standardSaveAction.execute(true);
            if (isDialog) return;
            if (this.$.recordId) {
                const financeIntegrationCheckResult = await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseCreditMemo')
                    .mutations.financeIntegrationCheck(
                        { wasSuccessful: true, message: true },
                        { creditMemo: this.$.recordId },
                    )
                    .execute();

                if (!financeIntegrationCheckResult.wasSuccessful) {
                    this.$.showToast(
                        `**${ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_credit_memo__save_warnings',
                            'Warnings while saving:',
                        )}**\n${financeIntegrationCheckResult.message}`,
                        { type: 'warning', timeout: 20000 },
                    );
                }
            }
        },
    })
    save: ui.PageAction;

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        isTransient: true,
        isHidden: true,
        icon: 'email_switch',
        title: 'Send for matching',
        async onClick() {
            this.requestApprovalSection.isHidden = false;
            try {
                await this._loadBuyers();
                await this.$.dialog.custom('info', this.requestApprovalSection, {
                    cancelButton: { isHidden: true },
                    acceptButton: { isHidden: true },
                });
            } catch {
                // Intentionally left empty
            }
            this.requestApprovalSection.isHidden = true;
        },
        isDisabled() {
            return this && this.$.isDirty;
        },
    })
    notifyBuyer: ui.PageAction;

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        icon: 'search',
        title: 'Add lines from returns',
        isHidden: true,
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            await addLineFromReturn(this);
        },
    })
    selectFromReturn: ui.PageAction;

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        icon: 'search',
        title: 'Add lines from invoices',
        isHidden: true,
        onError() {
            // Intentionally left empty
        },
        async onClick() {
            await addLineFromInvoice(this);
        },
    })
    selectFromInvoice: ui.PageAction;

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        title: 'Post',
        isHidden: true,
        onError(error: string | (Error & { errors: Array<any> })) {
            return MasterDataUtils.formatError(this, error);
        },
        async onClick() {
            if (this.$.recordId && this.taxCalculationStatus.value) {
                await actionFunctions.postAction({
                    purchaseCreditMemoPage: this,
                    recordId: this.$.recordId,
                    isCalledFromRecordPage: true,
                    taxCalculationStatus: this.taxCalculationStatus.value,
                    varianceTotalAmountIncludingTax: Number(this.varianceTotalAmountIncludingTax.value),
                    lines: this.lines.value?.length ?? 0,
                });
            }
        },
    })
    post: ui.PageAction;

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        title: 'Repost',
        isHidden: true,
        async onClick() {
            const decimalDigits = this.currency.value?.decimalDigits || 2;
            if (
                Number(this.varianceTotalAmountExcludingTax.value) !== 0 ||
                Number(Number(this.varianceTotalTaxAmount.value).toFixed(decimalDigits)) !== 0
            ) {
                throw new Error(
                    `${ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_credit_memo__repost__variance',
                        'You need to resolve the total amount variances before posting. The new total supplier tax is: ',
                    )}${Number((Number(this.totalTaxAmount.value ?? 0) + Number(this.varianceTotalTaxAmount.value ?? 0)).toFixed(decimalDigits))}`,
                );
            }

            await asyncArray(this.postingDetails.value)
                .filter(document => document?.postingStatus === 'posted')
                .forEach(async postedDocument => {
                    switch (postedDocument.targetDocumentType) {
                        case 'journalEntry': {
                            await this.$.dialog.message(
                                'info',
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_documents_already_posted_title',
                                    'Repost',
                                ),
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_journal_entry_already_posted',
                                    'The journal entry posted successfully. You can access this document in your financial solution to take further action.',
                                ),
                            );
                            break;
                        }
                        case 'accountsPayableInvoice': {
                            await this.$.dialog.message(
                                'info',
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_documents_already_posted_title',
                                    'Repost',
                                ),
                                ui.localize(
                                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_accounts_payable_invoice_already_posted',
                                    'The accounts payable credit memo posted successfully. You can access this document in your financial solution to take further action.',
                                ),
                            );
                            break;
                        }
                        default: {
                            break;
                        }
                    }
                });

            const documentLines = this.lines.value
                .filter((line: PartialCollectionValueWithIds<PurchaseCreditMemoLineBinding>) => Number(line._id) > 0)
                .map((line: PartialCollectionValueWithIds<PurchaseCreditMemoLineBinding>) => {
                    const lineData = {
                        baseDocumentLineSysId: line._id,
                        storedAttributes: line.storedAttributes,
                        storedDimensions: line.storedDimensions,
                    };
                    return this.canEditTaxDataAfterPost && this.wereTaxesEdited
                        ? { ...lineData, uiTaxes: line.uiTaxes }
                        : { ...lineData };
                });

            this.$.loader.isHidden = false;

            const repostArgs: {
                purchaseCreditMemo: string;
                documentData: {
                    header: {
                        supplierDocumentNumber?: string;
                        paymentData?: {
                            supplierDocumentDate: string;
                            paymentTerm: integer | string;
                        };
                        totalTaxAmount?: number;
                        taxes?: DocumentTaxInput[];
                    };
                    lines?: {
                        baseDocumentLineSysId?: integer | string;
                        storedAttributes?: string;
                        storedDimensions?: string;
                        uiTaxes?: any;
                    }[];
                };
            } = {
                purchaseCreditMemo: this.$.recordId ?? '',
                documentData: {
                    header: { supplierDocumentNumber: this.supplierDocumentNumber.value ?? '' },
                    lines: documentLines,
                },
            };

            if (this.wereTaxesEdited) {
                const { taxes } = this.$.values;
                repostArgs.documentData.header.totalTaxAmount = this.totalTaxAmount.value ?? undefined;
                repostArgs.documentData.header.taxes = taxes.map((tax: DocumentTaxInput) => ({
                    ...tax,
                    document: this.$.recordId,
                }));
            }

            if (this.canEditPaymentDataAfterPost) {
                repostArgs.documentData.header.paymentData = {
                    supplierDocumentDate: this.supplierDocumentDate.value ?? '',
                    paymentTerm: this.paymentTerm?.value?._id ?? 0,
                };
            }

            const postResult = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseCreditMemo')
                .mutations.repost({ wasSuccessful: true, message: true }, { ...repostArgs })
                .execute();

            this.$.loader.isHidden = true;
            if (!postResult.wasSuccessful) {
                this.$.showToast(
                    `** ${ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_credit_memo__repost_errors',
                        'Errors while reposting:',
                    )} **\n ${postResult.message}`,
                    { type: 'error', timeout: 20000 },
                );
            } else {
                this.$.showToast(postResult.message, { type: 'success' });
            }

            this.$.finish();
        },
    })
    repost: ui.PageAction;

    @ui.decorators.textField<PurchaseCreditMemo>({
        parent() {
            return this.requestApprovalBlock;
        },
        title: 'To',
        helperText: 'A notification for this purchase credit memo will be sent to this address',
        isTransient: true,
        isFullWidth: true,
    })
    emailAddressApproval: ui.fields.Text;

    @ui.decorators.tableField<PurchaseCreditMemo, User>({
        parent() {
            return this.approverSelectionBlock;
        },
        canSelect: false,
        title: 'User',
        isTitleHidden: true,
        isTransient: true,
        pageSize: 10,
        isReadOnly: true,
        orderBy: { sortOrder: +1 } as any,
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.label({
                title: 'Buyer',
                isTransient: true,
                bind: 'type' as any,
                backgroundColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_credit_memo__default_buyer', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'backgroundColor');
                },
                borderColor(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_credit_memo__default_buyer', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'borderColor');
                },
                color(value) {
                    const isDefaultUser =
                        value ===
                        ui.localize('@sage/xtrem-purchasing/pages__purchase_credit_memo__default_buyer', 'Default');
                    return PillColorCommon.setBooleanStatusColors('isDefaultUser', isDefaultUser, 'textColor');
                },
            }),
            ui.nestedFields.technical({ bind: 'sortOrder' as any, isTransient: true }),
        ],
        onRowClick(_rowId, rowData) {
            this.selectedUser.value = rowData as unknown as ui.PartialNode<User>;
        },
    })
    users: ui.fields.Table<User>;

    @ui.decorators.buttonField<PurchaseCreditMemo>({
        isTransient: true,
        width: 'small',
        parent() {
            return this.requestApprovalBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_credit_memo__select_buyer_button_text',
                'Select buyer',
            );
        },
        async onClick() {
            await this.$.dialog
                .custom('info', this.approverSelectionBlock)
                .then(() => {
                    this.emailAddressApproval.value = this.selectedUser.value?.email
                        ? this.selectedUser.value?.email
                        : this.emailAddressApproval.value;
                })
                .finally(() => {
                    this.selectedUser.value = null;
                });
        },
    })
    selectBuyer: ui.fields.Button;

    @ui.decorators.referenceField<PurchaseCreditMemo, User>({
        parent() {
            return this.approverSelectionBlock;
        },
        isReadOnly: true,
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'email',
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
            ui.nestedFields.text({ title: 'Name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
        ],
    })
    selectedUser: ui.fields.Reference<User>;

    @ui.decorators.buttonField<PurchaseCreditMemo>({
        isTransient: true,
        parent() {
            return this.requestApprovalBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__purchase_credit_memo__send_approval_request_button_text',
                'Send',
            );
        },
        async onClick() {
            if (!this.emailAddressApproval.value) {
                return;
            }

            const isSend = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseCreditMemo')
                .mutations.sendNotificationToBuyerMail(true, {
                    document: this.$.recordId ?? '',
                    user: { email: this.emailAddressApproval.value },
                })
                .execute();

            if (isSend) {
                this.$.showToast(
                    ui.localize('@sage/xtrem-purchasing/pages__purchase_credit_memo__email_sent', 'Email sent.'),
                    { type: 'success' },
                );
            } else {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_credit_memo__email_not_sent',
                        'Could not send buyer notification email.',
                    ),
                    { type: 'error' },
                );
            }
            this.$.setPageClean();
            this.requestApprovalSection.isHidden = true;
        },
    })
    sendApprovalRequestButton: ui.fields.Button;

    @ui.decorators.section<PurchaseCreditMemo>({ title: 'Lines' })
    itemsSection: ui.containers.Section;

    @ui.decorators.section<PurchaseCreditMemo>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<PurchaseCreditMemo>({ title: 'Information', isTitleHidden: true })
    informationSection: ui.containers.Section;

    @ui.decorators.section<PurchaseCreditMemo>({ title: 'Financial' })
    financialSection: ui.containers.Section;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.headerSection;
        },
        width: 'large',
    })
    headerStepSequenceBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.headerSection;
        },
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.stepSequenceField<PurchaseCreditMemo>({
        parent() {
            return this.headerStepSequenceBlock;
        },
        options() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption') || this.status.value !== 'posted'
                ? [this.creditMemoStepSequenceCreate, this.creditMemoStepSequencePost]
                : [this.creditMemoStepSequenceCreate, this.creditMemoStepSequencePost, this.creditMemoStepSequencePay];
        },
        width: 'small',
        isTransient: true,
        isFullWidth: true,
    })
    creditMemoStepSequence: ui.fields.StepSequence;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.financialSection;
        },
        width: 'large',
    })
    financialBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.totalsSection;
        },
        title: 'Supplier document information',
        isTitleHidden: true,
        isHidden: true,
    })
    supplierBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.totalsSection;
        },
        title: 'Variances',
        isHidden: true,
    })
    varianceBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.itemsSection;
        },
        width: 'small',
        title: 'Attachment: Supplier credit memo',
    })
    pdfBlock: ui.containers.Block;

    @ui.decorators.section<PurchaseCreditMemo>({ isHidden: true, title: 'Notification of purchase credit memo' })
    requestApprovalSection: ui.containers.Section;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.requestApprovalSection;
        },
    })
    requestApprovalBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseCreditMemo>({ title: 'Select approver' })
    approverSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseCreditMemo>({
        title: 'Purchase return lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                bind: 'link',
                title: 'Return number',
                isTransient: true,
                map: (_fieldValue, rowData) => `${rowData.number}`,
                page: (_value, rowData) => rowData?.nodeName,
                queryParameters: (_value, rowData) => ({ _id: rowData.url }),
            }),
            ui.nestedFields.technical({ bind: 'number' }),
            ui.nestedFields.technical({ bind: 'url' }),
            ui.nestedFields.technical({ bind: 'nodeName' }),
            ui.nestedFields.technical({ bind: 'linkTitle' }),
            ui.nestedFields.label({
                title: 'Return status',
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                isTransient: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData.status),
            }),
            ui.nestedFields.numeric({
                bind: 'creditedQuantity',
                title: 'Quantity in purchase unit ',
                isDisabled: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.numeric({
                bind: 'remainingQuantity',
                title: 'Remaining quantity to credit',
                isTransient: true,
                isDisabled: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.technical({ bind: '_idReceipt' }),
            ui.nestedFields.link({
                bind: 'linkReceipt',
                title: 'Purchase receipt number',
                isTransient: true,
                map: (_fieldValue, rowData) => `${rowData.numberReceipt}`,
                page: (_value, rowData) => rowData?.nodeNameReceipt,
                queryParameters: (_value, rowData) => ({ _id: rowData.urlReceipt }),
            }),
            ui.nestedFields.technical({ bind: 'numberReceipt' }),
            ui.nestedFields.technical({ bind: 'urlReceipt' }),
            ui.nestedFields.technical({ bind: 'nodeNameReceipt' }),
            ui.nestedFields.technical({ bind: 'linkTitleReceipt' }),
        ],
    })
    purchaseReturnLines: ui.fields.Table<PurchaseReturnLine>;

    @ui.decorators.tableField<PurchaseCreditMemo>({
        title: 'Purchase invoice lines',
        isTitleHidden: true,
        isTransient: true,
        canSelect: false,
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.link({
                bind: 'link',
                title: 'Invoice number',
                isTransient: true,
                map: (_fieldValue, rowData) => `${rowData.number}`,
                page: (_value, rowData) => rowData?.nodeName,
                queryParameters: (_value, rowData) => ({ _id: rowData.url }),
            }),
            ui.nestedFields.technical({ isTransient: true, bind: 'number' }),
            ui.nestedFields.technical({ isTransient: true, bind: 'url' }),
            ui.nestedFields.technical({ isTransient: true, bind: 'nodeName' }),
            ui.nestedFields.technical({ isTransient: true, bind: 'linkTitle' }),
            ui.nestedFields.label({
                title: 'Invoice status',
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                isTransient: true,
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData.status),
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit ',
                bind: 'creditedQuantity',
                isDisabled: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
            ui.nestedFields.numeric({
                bind: 'remainingQuantity',
                title: 'Remaining quantity to credit',
                isTransient: true,
                isDisabled: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
            }),
        ],
    })
    purchaseInvoiceLines: ui.fields.Table<PurchaseInvoiceLine>;

    @ui.decorators.richTextField<PurchaseCreditMemo>({
        width: 'large',
        isFullWidth: true,
        title: 'Internal line notes',
        capabilities: validCapabilities,
        isTransient: true,
        helperText: 'Notes display on internal documents.',
        isDisabled() {
            return this.status.value === 'closed';
        },
    })
    internalNoteLine: ui.fields.RichText;

    @ui.decorators.referenceField<PurchaseCreditMemo, Site>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Financial site',
        lookupDialogTitle: 'Select financial site',
        minLookupCharacters: 0,
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
            ui.nestedFields.technical({ bind: 'isInventory' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
            ui.nestedFields.technical({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical<PurchaseCreditMemo, BusinessEntity, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseCreditMemo, Site, Company>({
                title: 'Company',
                valueField: 'name',
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseCreditMemo, Company, Legislation>({
                        node: '@sage/xtrem-structure/Legislation',
                        bind: 'legislation',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'taxEngine' }),
                    ui.nestedFields.reference<PurchaseCreditMemo, Company, Currency>({
                        node: '@sage/xtrem-master-data/Currency',
                        bind: 'currency',
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.text({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'priceScale' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseCreditMemo, Site, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical({ bind: { name: true } }),
                    ui.nestedFields.technical<PurchaseCreditMemo, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                        ],
                    }),
                ],
            }),
        ],
        isReadOnly() {
            if (this.site.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
        async onChange() {
            this.manageDisplayButtonSelectFromReturnAction();
            this.manageDisplayButtonSelectFromInvoiceAction();
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            if (this.site.value) {
                this.taxEngine.value =
                    this.site.value!.legalCompany!.legislation!.id === 'US'
                        ? 'genericTaxCalculation'
                        : String(this.site.value!.legalCompany!.taxEngine);
                TotalTaxCalculator.getInstance().taxEngineProperty = this.taxEngine.value!;
                if (this.billBySupplier.value) {
                    this._defaultDimensionsAttributes = await initDefaultDimensions<PurchaseCreditMemo>({
                        page: this,
                        dimensionDefinitionLevel: 'purchasingDirect',
                        site: this.site.value,
                        supplier: this.billBySupplier.value,
                    });
                }
            }
            this.showHideColumns();
            this._defaultDimensionsAttributes = await initDefaultDimensions<PurchaseCreditMemo>({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                site: this.site.value,
            });
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<PurchaseCreditMemo, Supplier>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Bill-by supplier',
        minLookupCharacters: 3,
        lookupDialogTitle: 'Select bill-by supplier',
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ bind: { businessEntity: { name: true } }, title: 'Name' }),
            ui.nestedFields.text({ bind: { businessEntity: { id: true } }, title: 'ID' }),
            ui.nestedFields.text({ bind: { businessEntity: { taxIdNumber: true } }, title: 'Tax ID' }),
            ui.nestedFields.reference({
                bind: { businessEntity: { country: true } },
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseCreditMemo, Supplier, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
            ui.nestedFields.reference<PurchaseCreditMemo, Supplier, Currency>({
                bind: { businessEntity: { currency: true } },
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
                isHidden: true,
            }),
            ui.nestedFields.technical<PurchaseCreditMemo, Supplier, User>({
                bind: 'defaultBuyer',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'email' }),
                    ui.nestedFields.technical({ bind: 'firstName' }),
                    ui.nestedFields.technical({ bind: 'lastName' }),
                ],
            }),
            ui.nestedFields.technical<PurchaseCreditMemo, Supplier>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: { id: true } })],
            }),
            ui.nestedFields.technical<PurchaseCreditMemo, Supplier, BusinessEntityAddress>({
                bind: 'primaryAddress',
                node: '@sage/xtrem-master-data/BusinessEntityAddress',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'isActive' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'addressLine1' }),
                    ui.nestedFields.technical({ bind: 'addressLine2' }),
                    ui.nestedFields.technical({ bind: 'city' }),
                    ui.nestedFields.technical({ bind: 'region' }),
                    ui.nestedFields.technical({ bind: 'postcode' }),
                    ui.nestedFields.technical<PurchaseCreditMemo, BusinessEntityAddress, Country>({
                        bind: 'country',
                        node: '@sage/xtrem-structure/Country',
                        nestedFields: [ui.nestedFields.text({ bind: 'name' })],
                    }),
                    ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
                ],
            }),
        ],
        orderBy: { businessEntity: { name: +1 } },
        async onChange() {
            await this.$.fetchDefaults(['billByLinkedAddress', 'billByAddress', 'currency', 'paymentTerm']);
            this.manageDisplayButtonDefaultDimensionAction();
            this.manageDisplayLinePhantomRow();
            this.showHideColumns();
            if (this.site.value && this.billBySupplier.value) {
                this._defaultDimensionsAttributes = await initDefaultDimensions({
                    page: this,
                    dimensionDefinitionLevel: 'purchasingDirect',
                    site: this.site.value,
                    supplier: this.billBySupplier.value,
                });
            }
        },
        isReadOnly() {
            if (this.billBySupplier.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
    })
    billBySupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.textField<PurchaseCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Number',
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    number: ui.fields.Text;

    @ui.decorators.labelField<PurchaseCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Tax calculation status',
        bind: 'taxCalculationStatus',
        isHidden() {
            return !['notDone', 'inProgress'].includes(this.taxCalculationStatus.value ?? '');
        },
        optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
        style() {
            return PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', this.taxCalculationStatus.value);
        },
    })
    taxCalculationStatus: ui.fields.Label<TaxCalculationStatus>;

    @ui.decorators.dateField<PurchaseCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Credit memo date',
        isReadOnly() {
            return !!this.$.recordId;
        },
        onChange() {
            this.dueDate.value = fetchDefaultsForDueDate({
                paymentTerm: this.paymentTerm.value,
                baseDate: this.creditMemoDate.value ? date.parse(this.creditMemoDate.value) : null,
                dueDateValue: this.dueDate.value,
            });
        },
        validation(value) {
            if (Date.parse(value) > Date.now()) {
                return ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__credit_memo_date__cannot__be__future',
                    'The credit memo date cannot be later than today.',
                );
            }

            return undefined;
        },
    })
    creditMemoDate: ui.fields.Date;

    @ui.decorators.tile<PurchaseCreditMemo>({
        parent() {
            return this.headerSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total credit memo amount incl. tax',
        bind: 'totalAmountIncludingTax',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseCreditMemoIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total variance amount excl. tax',
        bind: 'varianceTotalAmountExcludingTax',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseCreditMemoExcludingTaxVariance: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total tax variance',
        bind: 'varianceTotalTaxAmount',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseCreditMemoTaxVariance: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        title: 'Total variance amount incl. tax',
        bind: 'varianceTotalAmountIncludingTax',
        unit() {
            return this.currency.value;
        },
        width: 'small',
    })
    purchaseCreditMemoIncludingTaxVariance: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Total payments',
        parent() {
            return this.tileContainer;
        },
        unit() {
            return this.currency.value;
        },
        size: 'small',
        bind: { paymentTracking: { amountPaid: true } },
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    amountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.tileContainer;
        },
        title: 'Net balance',
        size: 'small',
        unit() {
            return this.currency.value;
        },
        isHidden() {
            return !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    netBalance: ui.fields.Numeric;

    @ui.decorators.referenceField<PurchaseCreditMemo, Currency>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Company currency',
        lookupDialogTitle: 'Select company currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isMandatory: true,
        fetchesDefaults: true,
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
            ui.nestedFields.technical({ bind: 'symbol' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter: { isActive: true },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        onChange() {
            this.applyCompanyCurrency();
        },
        isReadOnly() {
            if (this.currency.value === null) {
                return false;
            }
            return (this.lines.value?.length ?? 0) !== 0;
        },
    })
    companyCurrency: ui.fields.Reference<Currency>;

    get siteCurrency() {
        const currency = this.site.value?.legalCompany?.currency;
        return {
            _id: currency?._id ?? '',
            decimalDigits: currency?.decimalDigits ?? 2,
            symbol: currency?.symbol ?? '',
        };
    }

    applyCurrency() {
        this.totalAmountExcludingTax.unit = this.currency.value;
        this.calculatedTotalAmountExcludingTaxInCompanyCurrency.unit = this.siteCurrency;
        this.calculatedTotalAmountIncludingTaxInCompanyCurrency.unit = this.siteCurrency;
    }

    private applyCompanyCurrency() {
        this.calculatedTotalAmountExcludingTaxInCompanyCurrency.unit = this.siteCurrency;
    }

    @ui.decorators.dateField<PurchaseCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate date',
        isMandatory: true,
        isReadOnly: true,
        fetchesDefaults: true,
        isHidden: true,
    })
    fxRateDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Exchange rate',
        isReadOnly: true,
        scale() {
            return this.companyFxRate.value ? Math.max(scaleOfCurrent(this.companyFxRate.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRate: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate divisor',
        isReadOnly: true,
        scale() {
            return this.companyFxRateDivisor.value ? Math.max(scaleOfCurrent(this.companyFxRateDivisor.value), 2) : 2;
        },
        isHidden: true,
    })
    companyFxRateDivisor: ui.fields.Numeric;

    @ui.decorators.referenceField<PurchaseCreditMemo, ReasonCode>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        lookupDialogTitle: 'Select reason',
        minLookupCharacters: 0,
        fetchesDefaults: true,
        isMandatory: true,
    })
    reason: ui.fields.Reference<ReasonCode>;

    @ui.decorators.labelField<PurchaseCreditMemo>({
        title: 'Status',
        optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
    })
    status: ui.fields.Label;

    @ui.decorators.labelField<PurchaseCreditMemo>({
        title: 'Display status',
        optionType: '@sage/xtrem-purchasing/PurchaseCreditMemoDisplayStatus',
        async onClick() {
            if (this.taxCalculationStatus.value === 'failed' && this.taxEngine.value === 'genericTaxCalculation') {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/page__purchase_credit_memo__at_least_one_mandatory_tax_code_not_found',
                        'At least one mandatory tax code is missing on the line.',
                    ),
                    { type: 'warning' },
                );
            }

            if (
                await actionFunctions.updateCreditMemoStatus({
                    status: this.status.value as PurchaseCreditMemoStatus,
                    stockTransactionStatus: this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                    recordId: this.$.recordId ?? '',
                    purchaseCreditMemoPage: this,
                })
            ) {
                await this.displayStatus.refresh();
            }

            const allPosted = this.postingDetails.value.every(detail => detail.postingStatus === 'posted');
            if (this.displayStatus.value === 'postingInProgress' && this.status.value === 'inProgress' && allPosted) {
                await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseCreditMemo')
                    .mutations.enforceStatusPosted(true, {
                        creditMemo: this.$.recordId || '',
                    })
                    .execute();
                await this.$.router.refresh();
            }
        },
        style() {
            return PillColorCommon.getDisplayStatusPillFeatures(this.displayStatus.value);
        },
    })
    displayStatus: ui.fields.Label<PurchaseCreditMemoDisplayStatus>;

    @ui.decorators.dropdownListField<PurchaseCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Tax engine',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isHidden: true,
    })
    taxEngine: ui.fields.DropdownList;

    @ui.decorators.referenceField<PurchaseCreditMemo, BusinessEntityAddress>({
        parent() {
            return this.informationBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Bill-by address',
        valueField: { name: true },
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<PurchaseCreditMemo, BusinessEntityAddress, Country>({
                bind: 'country',
                title: 'Country',
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<PurchaseCreditMemo, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                tunnelPage: '@sage/xtrem-master-data/BusinessEntity',
                valueField: 'id',
                isHidden: true,
                columns: [ui.nestedFields.text({ bind: 'id' })],
            }),
        ],
        orderBy: { isActive: +1 },
        filter() {
            if (this.billBySupplier.value) {
                return { isActive: true, businessEntity: { id: this.billBySupplier.value.businessEntity?.id } };
            }
            return { isActive: true };
        },
        async onChange() {
            await this.$.fetchDefaults(['billByAddress']);
        },
    })
    billByLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.fileField<PurchaseCreditMemo>({
        parent() {
            return this.pdfBlock;
        },
        fileTypes: 'application/pdf',
        text: 'Supplier credit memo',
        onChange() {
            if (this.pdfSupplierCreditMemo.value) {
                this.pdf.value = this.pdfSupplierCreditMemo.value.value;
            } else {
                this.pdf.value = '';
            }
        },
    })
    pdfSupplierCreditMemo: ui.fields.File;

    @ui.decorators.pluginField<PurchaseCreditMemo, PdfPluginProperties>({
        parent() {
            return this.pdfBlock;
        },

        pluginPackage: '@sage/xtrem-ui-plugin-pdf',
        height: 800,
        isFullWidth: true,
        isTransient: true,
    })
    pdf: ui.fields.Plugin<PdfPluginProperties>;

    @ui.decorators.textField<PurchaseCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Supplier credit memo reference',
    })
    supplierDocumentNumber: ui.fields.Text;

    @ui.decorators.dateField<PurchaseCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Supplier document date',
        isMandatory: true,
        validation(value) {
            if (Date.parse(value) > Date.now()) {
                return ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_credit_memo__supplier_document_date__cannot__be__future',
                    'The supplier document date cannot be later than today.',
                );
            }

            return undefined;
        },
        onChange() {
            dueDateDefault(this);
        },
    })
    supplierDocumentDate: ui.fields.Date;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Total credit memo amount excl. tax',
        unit() {
            return this.currency.value;
        },
        scale: null,
        isMandatory: true,
        onChange() {
            this.totalAmountIncludingTax.value = Number(
                Number(this.totalAmountExcludingTax.value ?? 0) + Number(this.totalTaxAmount.value ?? 0),
            );
            this._computeTotalAmounts();
        },
    })
    totalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.headerBlock;
        },
        width: 'small-medium',
        title: 'Total credit memo tax',
        unit() {
            return this.currency.value;
        },
        scale: null,
        onChange() {
            if (!this.totalTaxAmount.value) {
                this.totalTaxAmount.value = 0;
            }
            this.totalAmountIncludingTax.value = Number(
                Number(this.totalAmountExcludingTax.value ?? 0) + Number(this.totalTaxAmount.value ?? 0),
            );
            this._computeTotalAmounts();
        },
    })
    totalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.supplierBlock;
        },
        title: 'Supplier total including tax',
        isReadOnly: true,
        scale: null,
        unit() {
            return this.currency.value;
        },
        isHidden: true,
    })
    totalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.varianceBlock;
        },
        title: 'Total amount excluding tax variance',
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        isHidden: true,
    })
    varianceTotalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.varianceBlock;
        },
        title: 'Total tax amount variance',
        unit() {
            return this.currency.value;
        },
        isReadOnly: true,
        isHidden: true,
    })
    varianceTotalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        parent() {
            return this.varianceBlock;
        },
        title: 'Total amount including tax variance',
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        isHidden: true,
    })
    varianceTotalAmountIncludingTax: ui.fields.Numeric;

    private _isLineDisabled() {
        return isPostedInProgressError(this.status.value);
    }

    @ui.decorators.tableField<PurchaseCreditMemo, PurchaseCreditMemoLineBinding>({
        title: 'Lines',
        isTitleHidden: true,
        canSelect: false,
        pageSize: 10,
        canAddNewLine: true,
        hasLineNumbers: true,
        width: 'large',
        node: '@sage/xtrem-purchasing/PurchaseCreditMemoLine',
        orderBy: { _sortValue: +1 },
        parent() {
            return this.itemsSection;
        },
        onChange() {
            this._computeTotalAmounts();
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, PurchaseCreditMemoNode>({
                bind: 'document',
                node: '@sage/xtrem-purchasing/PurchaseCreditMemo',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'number' }),
                    ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoNode, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'rounding' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.label({
                title: 'Tax status',
                bind: 'taxCalculationStatus',
                isHidden() {
                    return (
                        this.taxCalculationStatus.value !== 'failed' || this.taxEngine.value !== 'genericTaxCalculation'
                    );
                },
                optionType: '@sage/xtrem-master-data/TaxCalculationStatus',
                style: (_id, rowData) =>
                    PillColorCommon.getLabelColorByStatus('TaxCalculationStatus', rowData?.taxCalculationStatus),
                async onClick(_rowId, rowItem) {
                    this.displayTaxesClicked = true;
                    await this.callDisplayTaxes(rowItem, true);
                    this.displayTaxesClicked = false;
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Origin',
                bind: 'origin',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentLineOrigin',
                isReadOnly: true,
            }),
            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, Item>({
                title: 'Item',
                lookupDialogTitle: 'Select item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                helperTextField: 'id',
                shouldSuggestionsIncludeColumns: true,
                isAutoSelectEnabled: true,
                isMandatory: true,
                fetchesDefaults: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'description' }),
                    ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
                    ui.nestedFields.image({ bind: 'image', isHidden: true }),
                    ui.nestedFields.technical({ bind: 'type' }),
                    ui.nestedFields.technical({ bind: 'isStockManaged' }),
                    ui.nestedFields.technical<PurchaseCreditMemo, Item, UnitOfMeasure>({
                        bind: 'stockUnit',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                ],
                orderBy: { name: -1, stockUnit: { id: +1 } },
                isDisabled: (_rowId, rowData) => Number(rowData?._id) > 0,
                async onChange(_rowId, rowData) {
                    if (rowData?.item) {
                        rowData.itemDescription = rowData.item.description
                            ? rowData.item.description
                            : rowData.item.name;
                        rowData.itemImage = rowData.item.image;
                        rowData.unit = await getPurchaseUnit(
                            this.$.graph,
                            rowData.item._id,
                            this.billBySupplier.value?._id ?? '',
                        );
                        rowData.uPurchaseUnit = rowData.unit;
                        await this._convertQuantity(rowData);
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.uStockUnit = rowData.stockUnit;
                        if (rowData.recipientSite) {
                            rowData.recipientSite = await checkStockSite(this, rowData.item, rowData.recipientSite);
                        }
                        if (this.site.value && this.billBySupplier.value && rowData.item) {
                            const { storedAttributes, storedDimensions } =
                                await attributesAndDimensions.defaultAttributesAndDimensionsWithItem({
                                    page: this,
                                    _defaultDimensionsAttributes: this._defaultDimensionsAttributes,
                                    dimensionDefinitionLevel: 'purchasingDirect',
                                    site: this.site.value,
                                    supplier: this.billBySupplier.value,
                                    item: rowData.item,
                                });
                            rowData.storedAttributes = storedAttributes;
                            rowData.storedDimensions = storedDimensions;
                        }
                        this.lines.addOrUpdateRecordValue(rowData);
                        this._computeTotalAmounts();
                    }
                    await this._setPriceOrigin(rowData);
                },
            }),
            ui.nestedFields.text({
                title: 'Item ID',
                bind: { item: { id: true } },
                isDisabled: true,
            }),
            ui.nestedFields.text({
                title: 'Item description',
                bind: 'itemDescription',
                width: 'large',
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
            }),
            ui.nestedFields.technical<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, Site>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                nestedFields: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, Site>({
                title: 'Recipient site',
                lookupDialogTitle: 'Select recipient site',
                minLookupCharacters: 0,
                bind: 'recipientSite',
                tunnelPage: '@sage/xtrem-master-data/Site',
                fetchesDefaults: true,
                isMandatory: true,
                isDisabled: (_rowId, rowData) => rowData?.origin !== 'direct' || rowData?._id > 0,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                    ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { taxIdNumber: true } } }),
                    ui.nestedFields.reference({
                        bind: { businessEntity: { country: true } },
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'regionLabel' }),
                            ui.nestedFields.technical({ bind: 'zipLabel' }),
                        ],
                    }),
                    ui.nestedFields.technical<PurchaseCreditMemo, Site, Site['legalCompany']>({
                        bind: 'legalCompany',
                        node: '@sage/xtrem-system/Company',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
            }),
            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, UnitOfMeasure>({
                title: 'Purchase unit',
                lookupDialogTitle: 'Select purchase unit',
                minLookupCharacters: 0,
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isMandatory: true,
                fetchesDefaults: true,
                isHiddenOnMainField: true,
                isDisabled: (_rowId, rowData) => rowData?.origin !== 'direct' || rowData?._id > 0,
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                async onChange(_rowId, rowData) {
                    if (rowData?.unit && rowData?.item) {
                        rowData.uPurchaseUnit = rowData.unit;
                        await this._convertQuantity(rowData);
                        rowData.stockUnit = rowData.item.stockUnit;
                        rowData.uStockUnit = rowData.stockUnit;
                        await this._setPriceOrigin(rowData);
                        this._computeTotalAmounts();
                    }
                },
            }),

            // Because we want to display in the line table 2 fields for quantity (value and unit)
            // and we want to display the quantity value + unit in a unique field in the detailPanel,
            // we must set a technical _ property in node to be bound to the _ field in the page
            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, UnitOfMeasure>({
                title: 'Unit',
                lookupDialogTitle: 'Select unit',
                minLookupCharacters: 0,
                bind: 'uPurchaseUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isExcludedFromMainField: true,
                isMandatory: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),

            ui.nestedFields.numeric({
                title: 'Quantity in purchase unit',
                bind: 'quantity',
                isMandatory: true,
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData: ui.PartialNodeWithId<PurchaseCreditMemoLine>) {
                    switch (rowData.origin) {
                        case 'purchaseReturn':
                            await this.changeQuantityReturn(rowData);
                            break;
                        case 'purchaseInvoice':
                            await this.changeQuantityInvoice(rowData);
                            break;
                        default:
                            break;
                    }
                    if (rowData.origin === 'direct') {
                        await this._setPriceOrigin(rowData);
                    } else {
                        await this._convertQuantity(rowData);
                        await this.calculatePrices(rowData);
                        this.lines.addOrUpdateRecordValue(rowData);
                    }
                    this._computeTotalAmounts();
                },
            }),
            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, UnitOfMeasure>({
                title: 'Stock unit',
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'symbol',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                columns: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Quantity in stock unit',
                bind: 'quantityInStockUnit',
                isReadOnly: true,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                scale: (_rowId, rowData) => rowData?.stockUnit?.decimalDigits ?? 2,
                postfix: (_rowId, rowData) => rowData?.stockUnit?.symbol ?? '',
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                bind: 'unitToStockUnitConversionFactor',
                title: 'Stock unit conversion factor',
                width: 'large',
                isHiddenOnMainField: true,
                isReadOnly: true,
                scale: (_rowId, rowData) =>
                    rowData?.unitToStockUnitConversionFactor.toString().split('.')[1].length ?? 2,
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),
            ui.nestedFields.numeric({
                title: 'Gross price',
                bind: 'grossPrice',
                scale() {
                    return getCompanyPriceScale(this?.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData) {
                    rowData.priceOrigin = rowData.grossPrice ? 'manual' : null;
                    await this.calculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                    this._computeTotalAmounts();
                },
            }),
            ui.nestedFields.numeric({
                title: 'Discount',
                bind: 'discount',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData) {
                    await this.calculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                    this._computeTotalAmounts();
                },
            }),
            ui.nestedFields.numeric({
                bind: 'charge',
                title: 'Charge',
                postfix: '%',
                scale: 2,
                isHiddenOnMainField: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                async onChange(_rowId, rowData) {
                    await this.calculatePrices(rowData);
                    this.lines.addOrUpdateRecordValue(rowData);
                    this._computeTotalAmounts();
                },
            }),
            ui.nestedFields.numeric({
                isReadOnly: true,
                title: 'Net price',
                bind: 'netPrice',
                scale() {
                    return getCompanyPriceScale(this?.site?.value?.legalCompany);
                },
                unit: (_rowId, rowData) => rowData?.currency,
            }),
            ui.nestedFields.label({
                title: 'Price origin',
                bind: 'priceOrigin',
                optionType: '@sage/xtrem-purchasing/PriceOrigin',
                width: 'large',
                isHiddenOnMainField: true,
            }),

            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, Currency>({
                title: 'Currency',
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
                isExcludedFromMainField: true,
                fetchesDefaults: true,
                isDisabled() {
                    return this._isLineDisabled();
                },
                columns: [
                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                    ui.nestedFields.text({ title: 'ISO 4217 code', bind: 'id' }),
                    ui.nestedFields.numeric({ title: 'Decimal places', bind: 'decimalDigits', canFilter: false }),
                    ui.nestedFields.numeric({ title: 'Rounding', bind: 'rounding', canFilter: false }),
                    ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
                isReadOnly: true,
            }),

            // Because we want to display in the line table 2 fields for quantity (value and unit)
            // and we want to display the quantity value + unit in a unique field in the detailPanel,
            // we must set a technical _ property in node to be bound to the _ field in the page
            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseCreditMemoLineBinding, UnitOfMeasure>({
                title: 'Unit',
                bind: 'uStockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
                isExcludedFromMainField: true,
                isMandatory: true,
                isReadOnly: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
                isHidden: (_rowId, rowData) => rowData?.item?.type === 'service',
            }),

            ui.nestedFields.numeric({
                title: 'Total excluding tax',
                bind: 'amountExcludingTax',
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total excluding tax company currency',
                bind: 'amountExcludingTaxInCompanyCurrency',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit() {
                    return this.siteCurrency;
                },
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total tax',
                bind: 'taxAmount',
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax',
                bind: 'amountIncludingTax',
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Tax amount adjusted',
                bind: 'taxAmountAdjusted',
                isReadOnly: true,
                unit: (_rowId, rowData) => rowData?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Total including tax company currency',
                bind: 'amountIncludingTaxInCompanyCurrency',
                isReadOnly: true,
                isExcludedFromMainField: true,
                unit() {
                    return this.siteCurrency;
                },
                scale: null,
            }),
            ui.nestedFields.technical({ bind: 'storedAttributes' }),
            ui.nestedFields.technical({ bind: 'storedDimensions' }),
            ui.nestedFields.technical<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseReturnLineToPurchaseCreditMemoLine
            >({
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseCreditMemoLine',
                nestedFields: [
                    ui.nestedFields.technical({ bind: { purchaseReturnLine: { _id: true } } }),
                    ui.nestedFields.technical<
                        PurchaseCreditMemo,
                        PurchaseReturnLineToPurchaseCreditMemoLine,
                        PurchaseReturnLine
                    >({
                        bind: 'purchaseReturnLine',
                        node: '@sage/xtrem-purchasing/PurchaseReturnLine',
                        nestedFields: [
                            ui.nestedFields.technical<PurchaseCreditMemo, PurchaseReturnLine, UnitOfMeasure>({
                                bind: 'unit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'symbol' }),
                                    ui.nestedFields.technical({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                            ui.nestedFields.technical({ bind: 'quantity' }),
                            ui.nestedFields.technical({ bind: 'grossPrice' }),
                            ui.nestedFields.technical({ bind: 'amountExcludingTax' }),
                        ],
                    }),
                    ui.nestedFields.technical<
                        PurchaseCreditMemo,
                        PurchaseReturnLineToPurchaseCreditMemoLine,
                        PurchaseCreditMemoLine
                    >({
                        bind: 'purchaseCreditMemoLine',
                        node: '@sage/xtrem-purchasing/PurchaseCreditMemoLine',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
            }),
            ui.nestedFields.technical<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseReturnLineToPurchaseCreditMemoLine
            >({
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseCreditMemoLine',
                nestedFields: [ui.nestedFields.technical({ bind: { creditMemoAmount: true } })],
            }),

            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseInvoiceLineToPurchaseCreditMemoLine
            >({
                title: 'Invoice line',
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLineToPurchaseCreditMemoLine',
                valueField: { purchaseInvoiceLine: { _id: true } },
                isExcludedFromMainField: true,
                columns: [
                    ui.nestedFields.text({ title: 'ID', bind: '_id' }),
                    ui.nestedFields.reference<
                        PurchaseCreditMemo,
                        PurchaseInvoiceLineToPurchaseCreditMemoLine,
                        PurchaseInvoiceLine
                    >({
                        title: 'Purchase credit memo',
                        bind: 'purchaseInvoiceLine',
                        node: '@sage/xtrem-purchasing/PurchaseInvoiceLine',
                        valueField: '_id',
                        columns: [
                            ui.nestedFields.text({ title: 'ID', bind: '_id' }),
                            ui.nestedFields.reference<PurchaseCreditMemo, PurchaseInvoiceLine, UnitOfMeasure>({
                                title: 'Purchase unit',
                                bind: 'unit',
                                node: '@sage/xtrem-master-data/UnitOfMeasure',
                                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                                valueField: 'symbol',
                                isMandatory: true,
                                fetchesDefaults: true,
                                columns: [
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
                                    ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                                ],
                            }),
                            ui.nestedFields.numeric({
                                title: 'Ordered quantity',
                                bind: 'quantity',
                                isExcludedFromMainField: true,
                                scale: (_rowId, rowData) =>
                                    rowData?.purchaseOrderLine?.purchaseOrderLine?.unit?.decimalDigits ?? 2,
                                postfix: (_rowId, rowData) =>
                                    rowData?.purchaseOrderLine?.purchaseOrderLine?.unit?.symbol ?? '',
                            }),
                            ui.nestedFields.numeric({
                                title: 'Order unit price',
                                bind: 'grossPrice',
                                isExcludedFromMainField: true,
                                scale() {
                                    return getCompanyPriceScale(this?.site?.value?.legalCompany);
                                },
                                unit: (_rowId, rowData) => rowData?.currency,
                            }),
                            ui.nestedFields.numeric({
                                title: 'Total amount from order',
                                bind: 'amountExcludingTax',
                                isExcludedFromMainField: true,
                                unit: (_rowId, rowData) => rowData?.currency,
                            }),
                            ui.nestedFields.numeric({
                                title: 'Total amount from order company currency',
                                bind: 'amountExcludingTaxInCompanyCurrency',
                                isExcludedFromMainField: true,
                                unit() {
                                    return this.siteCurrency;
                                },
                            }),
                        ],
                    }),
                    ui.nestedFields.reference<
                        PurchaseCreditMemo,
                        PurchaseInvoiceLineToPurchaseCreditMemoLine,
                        PurchaseCreditMemoLine
                    >({
                        title: 'Purchase credit memo line',
                        bind: 'purchaseCreditMemoLine',
                        node: '@sage/xtrem-purchasing/PurchaseCreditMemoLine',
                        valueField: '_id',
                        columns: [ui.nestedFields.text({ title: 'ID', bind: '_id' })],
                    }),
                ],
            }),
            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseReturnLineToPurchaseCreditMemoLine
            >({
                title: 'Received quantity',
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseCreditMemoLine',
                valueField: { purchaseReturnLine: { quantity: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseInvoiceLineToPurchaseCreditMemoLine
            >({
                title: 'Invoiced quantity',
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLineToPurchaseCreditMemoLine',
                valueField: { purchaseInvoiceLine: { quantity: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseReturnLineToPurchaseCreditMemoLine
            >({
                title: 'Return unit price',
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseCreditMemoLine',
                valueField: { purchaseReturnLine: { grossPrice: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseReturnLineToPurchaseCreditMemoLine
            >({
                title: 'Total amount from return',
                bind: 'purchaseReturnLine',
                node: '@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseCreditMemoLine',
                valueField: { purchaseReturnLine: { amountExcludingTax: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseInvoiceLineToPurchaseCreditMemoLine
            >({
                title: 'Invoice unit price',
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLineToPurchaseCreditMemoLine',
                valueField: { purchaseInvoiceLine: { grossPrice: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseInvoiceLineToPurchaseCreditMemoLine
            >({
                title: 'Total amount from credit memo',
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLineToPurchaseCreditMemoLine',
                valueField: { purchaseInvoiceLine: { amountExcludingTax: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.reference<
                PurchaseCreditMemo,
                PurchaseCreditMemoLineBinding,
                PurchaseInvoiceLineToPurchaseCreditMemoLine
            >({
                title: 'Total amount from credit memo',
                bind: 'purchaseInvoiceLine',
                node: '@sage/xtrem-purchasing/PurchaseInvoiceLineToPurchaseCreditMemoLine',
                valueField: { purchaseInvoiceLine: { amountExcludingTaxInCompanyCurrency: true } },
                isExcludedFromMainField: true,
                isReadOnly: true,
            }),
            ui.nestedFields.label<PurchaseCreditMemo, PurchaseCreditMemoLine>({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
                isHidden() {
                    if (this.stockTransactionStatus.value)
                        return ['draft', 'completed'].includes(this.stockTransactionStatus.value);
                    return false;
                },
                async onClick(_id: string, data) {
                    await StockDataUtils.onStockTransactionStatusClick(this, data.stockTransactionStatus, {
                        origin: 'line',
                        _id,
                    });
                },
            }),
            ui.nestedFields.label({
                bind: 'varianceTypeSeparator' as any,
                title: '',
                isExcludedFromMainField: true,
                isTransient: true,
                map: () => 'dummy',
                borderColor: ui.tokens.colorsYang100,
                color: () => ui.tokens.colorsYang100,
            }),
            ui.nestedFields.technical({ bind: 'uiTaxes' }),
            ui.nestedFields.technical({ bind: 'taxDate' }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
            ui.nestedFields.technical({ bind: { text: { value: true } } }),
        ],
        inlineActions: [
            {
                icon: 'box_arrow_left',
                title: 'Open line panel',
                onClick(rowId) {
                    this.lines.openSidebar(rowId);
                },
            },
        ],
        dropdownActions: [
            {
                icon: 'none',
                title: 'Dimensions',
                async onClick(_rowId, rowItem) {
                    const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                    await MasterDataUtils.applyPanelToLineIfChanged(
                        this.lines,
                        dimensionPanelHelpers.editDisplayDimensions(
                            this,
                            { documentLine: rowData },
                            {
                                editable:
                                    !isPurchaseCreditMemoLineActionDisabled(
                                        this.status.value ?? '',
                                        '',
                                        'dimensions',
                                    ) || this.isRepost,
                            },
                        ),
                    );
                },
            },
            {
                icon: 'none',
                title: 'Tax details',
                isHidden: (_rowId, rowItem) => !rowItem.uiTaxes,
                async onClick(_rowId, rowItem) {
                    if (rowItem.uiTaxes) {
                        await this.callDisplayTaxes(rowItem, true);
                    }
                },
            },
            ui.menuSeparator(),
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                async onClick(rowId, rowItem) {
                    if (
                        await confirmDialogWithAcceptButtonText(
                            this,
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_credit-memo__line_delete_action_dialog_title',
                                'Confirm delete',
                            ),
                            ui.localize(
                                '@sage/xtrem-purchasing/pages__purchase_credit-memo__line_delete_action_dialog_content',
                                'You are about to delete this purchase creit memo line. This action cannot be undone.',
                            ),
                            ui.localize('@sage/xtrem-purchasing/pages-confirm-continue', 'Continue'),
                        )
                    ) {
                        this.lines.removeRecord(rowId);
                        await TotalTaxCalculator.getInstance().updateTaxData(JSON.parse(rowItem.uiTaxes ?? '{}'), null);
                        await TotalTaxCalculator.getInstance().updateTaxDetails(
                            this.taxes,
                            this.totalTaxAmountAdjusted,
                            this.calculatedTotalTaxAmount,
                        );
                        recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                        this._computeTotalAmounts();
                    }
                },

                isHidden() {
                    return this._isLineDisabled();
                },
            },
        ],
        addItemActions() {
            return [this.selectFromReturn, this.selectFromInvoice];
        },
        mobileCard: {
            image: ui.nestedFields.image({ bind: { item: { image: true } } }),
            title: ui.nestedFields.text({ bind: { item: { name: true } }, title: 'Product', isTitleHidden: true }),
            line2: ui.nestedFields.text({ bind: { itemDescription: true }, title: 'Description' }),
            line2Right: ui.nestedFields.numeric({
                bind: 'amountIncludingTax',
                title: 'Amount',
                canFilter: false,
                unit: (_rowId, rowData) => rowData?.currency,
            }),
        },
        sidebar: {
            title(_id, recordValue) {
                if (+(recordValue?._id ?? 0) < 0) {
                    return ui.localize('@sage/xtrem-purchasing/edit-create-line', 'Add new line');
                }
                return `${recordValue?.item.name} - ${recordValue?.item.id}`;
            },
            headerDropdownActions: [
                {
                    icon: 'none',
                    title: 'Dimensions',
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseCreditMemoLine>) {
                        const rowData = dimensionPanelHelpers.fromPartialToBaseDocumentLineWithAnalytical(rowItem);
                        await MasterDataUtils.applyPanelToLineIfChanged(
                            this.lines,
                            dimensionPanelHelpers.editDisplayDimensions(
                                this,
                                { documentLine: rowData },
                                {
                                    editable:
                                        !isPurchaseCreditMemoLineActionDisabled(
                                            this.status.value ?? '',
                                            '',
                                            'dimensions',
                                        ) || this.isRepost,
                                },
                            ),
                        );
                    },
                },
                {
                    icon: 'none',
                    title: 'Tax details',
                    isHidden: (_rowId, rowItem) => !rowItem.uiTaxes,
                    async onClick(_rowId, rowItem: ui.PartialCollectionValue<PurchaseCreditMemoLine>) {
                        if (rowItem.uiTaxes) {
                            await this.callDisplayTaxes(rowItem, true);
                        }
                    },
                },
                ui.menuSeparator(),
                {
                    icon: 'bin',
                    title: 'Delete',
                    isDestructive: true,
                    isHidden() {
                        return this._isLineDisabled();
                    },
                    onClick(id) {
                        this.lines.removeRecord(id);
                    },
                },
            ],
            headerQuickActions: [],
            async onRecordOpened(_id, recordValue) {
                if (recordValue) {
                    this.currentSelectedLineId = recordValue._id;
                    this._isAddNewLine = recordValue.origin === 'direct';
                    this.internalNoteLine.value = recordValue.internalNote ? recordValue.internalNote.value : '';
                    if (+recordValue._id > 0) {
                        await this.fillPodBaseDocumentLines(_id, recordValue);
                    } else if (
                        this._defaultDimensionsAttributes.dimensions !== '{}' ||
                        this._defaultDimensionsAttributes.attributes !== '{}'
                    ) {
                        const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
                            {
                                site: this.site.value,
                                currency: this.currency.value,
                                origin: 'direct',
                                recipientSite:
                                    this.site.value?.isFinance &&
                                    (this.site.value?.isPurchase ?? this.site.value?.isInventory)
                                        ? this.site.value
                                        : null,
                            } as ui.PartialNodeWithId<PurchaseCreditMemoLine>,
                            this._defaultDimensionsAttributes,
                        );
                        recordValue.storedAttributes = line.storedAttributes;
                        recordValue.storedDimensions = line.storedDimensions;
                        this.lines.addOrUpdateRecordValue(
                            recordValue as unknown as ExtractEdgesPartial<PurchaseCreditMemoLineBinding>,
                        );
                        if (this.$.isDirty) {
                            this.$.setPageClean();
                        }
                    }
                }
            },
            async onRecordConfirmed(_id, recordValue) {
                if (recordValue) {
                    recordValue.internalNote.value = this.internalNoteLine.value ? this.internalNoteLine.value : '';
                    this.lines.addOrUpdateRecordValue(
                        recordValue as unknown as ExtractEdgesPartial<PurchaseCreditMemoLineBinding>,
                    );
                }
                await TotalTaxCalculator.getInstance().updateTaxDetails(
                    this.taxes,
                    this.totalTaxAmountAdjusted,
                    this.totalTaxAmount,
                );
                recalculateTaxCalculationStatus(this.lines, this.taxCalculationStatus);
                this._computeTotalAmounts();
            },
            layout() {
                return {
                    general: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_information', 'Information'),
                        blocks: {
                            mainBlock: { fields: ['item', 'origin', 'itemDescription'] },
                            siteBlock: { fields: ['recipientSite'] },
                            purchaseBlock: {
                                title: ui.localize(
                                    '@sage/xtrem-purchasing/pages_sidebar_block_title_purchase',
                                    'Purchase',
                                ),
                                fields: ['unit', 'quantity'],
                            },
                            stockBlock: {
                                isHidden: (_id, recordValue) => !recordValue?.item?.isStockManaged,
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_stock', 'Stock'),
                                fields: ['stockUnit', 'quantityInStockUnit', 'unitToStockUnitConversionFactor'],
                            },
                        },
                    },
                    price: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_price', 'Price'),
                        blocks: {
                            mainBlock: { fields: ['grossPrice', 'discount', 'charge', 'netPrice'] },
                            mainBlock2: { fields: ['priceOrigin'] },
                            totals: {
                                title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_block_title_totals', 'Totals'),
                                fields: ['amountExcludingTax', 'taxAmount', 'amountIncludingTax'],
                            },
                            totals2: {
                                fields: ['amountExcludingTaxInCompanyCurrency', 'amountIncludingTaxInCompanyCurrency'],
                            },
                        },
                    },
                    origin: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_origin', 'Origin'),
                        isHidden() {
                            return (
                                this.purchaseReturnLines.value.length === 0 &&
                                this.purchaseInvoiceLines.value.length === 0
                            );
                        },
                        blocks: {
                            invoiceBlock: {
                                isHidden() {
                                    return this.purchaseInvoiceLines.value.length === 0;
                                },
                                fields: [this.purchaseInvoiceLines],
                            },
                            receiptBlock: {
                                isHidden() {
                                    return this.purchaseReturnLines.value.length === 0;
                                },
                                fields: [this.purchaseReturnLines],
                            },
                        },
                    },
                    notes: {
                        title: ui.localize('@sage/xtrem-purchasing/pages_sidebar_tab_title_line_notes', 'Line notes'),
                        blocks: { notesBlock: { fields: [this.internalNoteLine] } },
                    },
                };
            },
        },
    })
    lines: ui.fields.Table<PurchaseCreditMemoLineBinding>;

    _computeTotalAmounts() {
        const { totalAmountExcludingTax, totalAmountExcludingTaxInCompanyCurrency } = this.lines.value.reduce(
            (amounts: { totalAmountExcludingTax: number; totalAmountExcludingTaxInCompanyCurrency: number }, line) => {
                amounts.totalAmountExcludingTax += Number(line.amountExcludingTax ?? 0);
                amounts.totalAmountExcludingTaxInCompanyCurrency += Number(
                    line.amountExcludingTaxInCompanyCurrency ?? 0,
                );
                return amounts;
            },
            { totalAmountExcludingTax: 0, totalAmountExcludingTaxInCompanyCurrency: 0 },
        );

        this.calculatedTotalAmountExcludingTax.value = totalAmountExcludingTax;
        this.calculatedTotalAmountIncludingTax.value = Number(
            this.calculatedTotalAmountExcludingTax.value + (this.totalTaxAmountAdjusted.value ?? 0),
        );
        this.calculatedTotalAmountExcludingTaxInCompanyCurrency.value = totalAmountExcludingTaxInCompanyCurrency;
        this.calculatedTotalAmountIncludingTaxInCompanyCurrency.value = convertAmount(
            Number(this.calculatedTotalAmountIncludingTax.value),
            this.companyFxRate.value ?? 0,
            this.companyFxRateDivisor.value ?? 0,
            this.currency.value?.decimalDigits ?? 2,
            this.siteCurrency.decimalDigits,
        );

        if ((this.totalAmountIncludingTax.value ?? 0) + this.calculatedTotalAmountExcludingTax.value > 0) {
            this.varianceTotalAmountExcludingTax.value = Number(
                (this.calculatedTotalAmountExcludingTax.value ?? 0) - (this.totalAmountExcludingTax.value ?? 0),
            );
        }

        if (Number(this.totalTaxAmount.value ?? 0) + (this.totalTaxAmountAdjusted.value ?? 0) >= 0) {
            this.varianceTotalTaxAmount.value = Number(
                (this.totalTaxAmountAdjusted.value ?? 0) - (this.totalTaxAmount.value ?? 0),
            );
        }

        this.varianceTotalAmountIncludingTax.value = Number(
            (this.varianceTotalTaxAmount.value ?? 0) + (this.varianceTotalAmountExcludingTax.value ?? 0),
        );
    }

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        icon: 'none',
        title: 'Set dimensions',
        async onClick() {
            const defaultedFromItem = await attributesAndDimensions.getAttributesAndDimensionsFromItem({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                companyId: Number(this.site?.value?.legalCompany?._id),
            });
            this._defaultDimensionsAttributes = await dimensionPanelHelpers.editAndApplyDefaultDimensionsIfNeeded(
                this,
                this.lines,
                this._defaultDimensionsAttributes,
                () => true,
                defaultedFromItem,
            );
        },
    })
    defaultDimension: ui.PageAction;

    @ui.decorators.block<PurchaseCreditMemo>({
        title: 'Information',
        isTitleHidden: true,
        width: 'large',
        parent() {
            return this.informationSection;
        },
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.labelField<PurchaseCreditMemo>({
        title: 'Stock status',
        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
        parent() {
            return this.informationBlock;
        },
        style() {
            return PillColorStock.getLabelColorByStatus(
                'StockDocumentTransactionStatus',
                this.stockTransactionStatus.value,
            );
        },
    })
    stockTransactionStatus: ui.fields.Label<StockDocumentTransactionStatus>;

    @ui.decorators.referenceField<PurchaseCreditMemo, Currency>({
        parent() {
            return this.informationBlock;
        },
        title: 'Transaction currency',
        lookupDialogTitle: 'Select currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        filter: { isActive: true },
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
        onChange() {
            this.applyCurrency();
            this.showHideColumns();
        },
        isReadOnly() {
            return !!this.$.recordId || this.lines.value?.length > 0;
        },
    })
    currency: ui.fields.Reference<Currency>;

    @ui.decorators.textField<PurchaseCreditMemo>({
        parent() {
            return this.informationBlock;
        },
        title: 'Exchange rate',
        isReadOnly: true,
        isHidden() {
            return isExchangeRateHidden(this.currency.value, this.site.value, this.billBySupplier.value);
        },
    })
    rateDescription: ui.fields.Text;

    // We need this information to decide if we should allow some properties to be changed.
    // It is hidden at the moment, but it can be made visible on nest US's for Payment Tracking
    @ui.decorators.referenceField<PurchaseCreditMemo, PaymentTracking>({
        node: '@sage/xtrem-finance-data/PaymentTracking',
        columns: [
            ui.nestedFields.technical({ bind: 'status' }),
            ui.nestedFields.technical({ bind: 'amountPaid' }),
            ui.nestedFields.technical({ bind: 'forcedAmountPaid' }),
            ui.nestedFields.technical({ bind: 'discountPaymentBeforeDate' }),
            ui.nestedFields.technical({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: '_id' }),
                ],
            }),
        ],
    })
    paymentTracking: ui.fields.Reference<PaymentTracking>;

    @ui.decorators.vitalPodField<PurchaseCreditMemo, Address>({
        parent() {
            return this.informationSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Bill-by address',
        width: 'small',
        isMandatory: true,
        onAddButtonClick() {
            if (this.billByLinkedAddress.value) {
                const { ...values } = { ...this.billByLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.billByLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.billByAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.billByAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.billByAddress.isReadOnly = true;
                    if (this.billByAddress.value) {
                        this.billByAddress.value.concatenatedAddress = getConcatenatedAddress(this.billByAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.billByAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Bill-by address',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return this.billByAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.billByAddress.value);
                },
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.billByAddress.value);
                },
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.billByAddress.isReadOnly === true;
                },
                onChange() {
                    // TODO: how switching titles after country changed? ==> in each pod?
                },
            }),
        ],
    })
    billByAddress: ui.fields.VitalPod<Address>;

    @ui.decorators.referenceField<PurchaseCreditMemo, Supplier>({
        parent() {
            return this.financialBlock;
        },
        title: 'Pay to supplier',
        lookupDialogTitle: 'Select pay to supplier',
        isMandatory: true,
        fetchesDefaults: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
            ui.nestedFields.technical<PurchaseCreditMemo, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical<PurchaseCreditMemo, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical<PurchaseCreditMemo, Supplier, PaymentTerm>({
                bind: 'paymentTerm',
                node: '@sage/xtrem-master-data/PaymentTerm',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'description' }),
                ],
            }),
        ],
        isReadOnly() {
            return !!this.$.recordId;
        },
    })
    payToSupplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<PurchaseCreditMemo, BusinessEntityAddress>({
        parent() {
            return this.financialBlock;
        },
        node: '@sage/xtrem-master-data/BusinessEntityAddress',
        title: 'Pay-to address',
        valueField: { name: true },
        minLookupCharacters: 1,
        isAutoSelectEnabled: true,
        fetchesDefaults: true,
        isMandatory: true,
        isHidden: true,
        columns: [
            ui.nestedFields.checkbox({ bind: 'isActive', title: 'Active' }),
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'addressLine1', title: 'Address line 1' }),
            ui.nestedFields.text({ bind: 'addressLine2', title: 'Address line 2' }),
            ui.nestedFields.text({ bind: 'city', title: 'City' }),
            ui.nestedFields.text({ bind: 'region', title: 'Region' }),
            ui.nestedFields.text({ bind: 'postcode', title: 'ZIP code' }),
            ui.nestedFields.reference<PurchaseCreditMemo, BusinessEntityAddress, Country>({
                bind: 'country',
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.technical<PurchaseCreditMemo, BusinessEntityAddress, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        orderBy: { isActive: +1 },
        filter() {
            return {
                isActive: true,
                ...(this.payToSupplier.value?.businessEntity?.id
                    ? { businessEntity: { id: this.payToSupplier.value.businessEntity.id } }
                    : {}),
            };
        },
        async onChange() {
            await this.$.fetchDefaults(['payToAddress']);
        },
    })
    payToLinkedAddress: ui.fields.Reference<BusinessEntityAddress>;

    @ui.decorators.vitalPodField<PurchaseCreditMemo, Address>({
        parent() {
            return this.financialSection;
        },
        node: '@sage/xtrem-master-data/Address',
        title: 'Pay-to address',
        width: 'small',
        isMandatory: true,
        onAddButtonClick() {
            if (this.payToLinkedAddress.value) {
                const { ...values } = { ...this.payToLinkedAddress.value };
                delete values.businessEntity;
                return { ...values, _id: null };
            }
            return {};
        },
        dropdownActions: [
            {
                icon: 'lookup',
                title: 'Replace',
                onClick() {
                    this.payToLinkedAddress.openDialog();
                },
                isDisabled() {
                    return this.status.value === 'closed';
                },
            },
            {
                icon: 'edit',
                title: 'Edit',
                onClick() {
                    this.payToAddress.isReadOnly = false;
                },
                isDisabled() {
                    return this.status.value === 'closed' || !this.payToAddress.isReadOnly;
                },
            },
            {
                icon: 'tick',
                title: 'Read-only',
                onClick() {
                    this.payToAddress.isReadOnly = true;
                    if (this.payToAddress.value) {
                        this.payToAddress.value.concatenatedAddress = getConcatenatedAddress(this.payToAddress.value);
                    }
                },
                isDisabled() {
                    return this.status.value === 'closed' || this.payToAddress.isReadOnly;
                },
            },
        ],
        isReadOnly: true,
        columns: [
            ui.nestedFields.text({
                bind: 'name',
                title: 'Name',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.textArea({
                bind: 'concatenatedAddress',
                title: 'Pay-to address',
                isTitleHidden: true,
                width: 'large',
                isHidden() {
                    return this.payToAddress.isReadOnly === false;
                },
            }),
            ui.nestedFields.text({ bind: 'locationPhoneNumber', title: 'Phone number' }),
            ui.nestedFields.text({
                bind: 'addressLine1',
                title: 'Address line 1',
                width: 'large',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'addressLine2',
                title: 'Address line 2',
                width: 'large',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'city',
                title: 'City',
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'region',
                title() {
                    return getCountryRegionTitle(this.payToAddress.value);
                },
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.text({
                bind: 'postcode',
                title() {
                    return getCountryPostCodeTitle(this.payToAddress.value);
                },
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
            }),
            ui.nestedFields.reference({
                bind: 'country',
                lookupDialogTitle: 'Select country',
                minLookupCharacters: 1,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ISO 3166-1 alpha-2 code' }),
                    ui.nestedFields.technical({ bind: 'regionLabel' }),
                    ui.nestedFields.technical({ bind: 'zipLabel' }),
                ],
                isHidden() {
                    return this.payToAddress.isReadOnly === true;
                },
                onChange() {
                    // TODO: how switching titles after country changed? ==> in each pod?
                },
            }),
        ],
    })
    payToAddress: ui.fields.VitalPod;

    @ui.decorators.section<PurchaseCreditMemo>({
        title: 'Totals',
    })
    totalsSection: ui.containers.Section;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.totalsSection;
        },
        title: 'Calculated amounts',
        width: 'large',
        isTitleHidden: true,
    })
    totalsSectionTaxTotalsBlock: ui.containers.Block;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountExcludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Excluding tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountExcludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalTaxAmount: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Adjusted tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        isHidden: true,
    })
    totalTaxAmountAdjusted: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionTaxTotalsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountIncludingTax: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Including tax',
        parent() {
            return this.totalsSectionCompanyCurrencyDetailsBlock;
        },
        unit() {
            return this.siteCurrency;
        },
        scale: null,
        isReadOnly: true,
    })
    calculatedTotalAmountIncludingTaxInCompanyCurrency: ui.fields.Numeric;

    @ui.decorators.tableField<PurchaseCreditMemo, DocumentTaxBinding>({
        title: 'Summary by tax',
        width: 'large',
        canSelect: false,
        pageSize: 10,
        node: '@sage/xtrem-tax/DocumentTax',
        orderBy: { _sortValue: +1, tax: +1 },
        parent() {
            return this.totalsSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.text({ title: 'Category', bind: 'taxCategory', isReadOnly: true }),
            ui.nestedFields.text({ title: 'Tax', bind: 'tax', isReadOnly: true }),
            ui.nestedFields.numeric({
                title: 'Taxable base',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxableAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({ title: 'Rate', postfix: '%', scale: 2, bind: 'taxRate', isReadOnly: true }),
            ui.nestedFields.numeric({
                title: 'Amount',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmount',
                isReadOnly: true,
            }),
            ui.nestedFields.numeric({
                title: 'Adjusted amount',
                size: 'small',
                unit() {
                    return this.currency.value;
                },
                scale: null,
                bind: 'taxAmountAdjusted',
                onChange() {
                    this.totalTaxAmountAdjusted.value = this.taxes.value.reduce(
                        (amount: number, line) => Number(amount + Number(line.taxAmountAdjusted ?? 0)),
                        0,
                    );
                    this._computeTotalAmounts();
                    TotalTaxCalculator.getInstance().initializeTotalTaxData(this.taxes, this.taxEngine.value ?? '');
                    this.wereTaxesEdited = true;
                },
                isReadOnly() {
                    return isPostedInProgressError(this.status.value) && !this.canEditTaxDataAfterPost;
                },
            }),
            ui.nestedFields.checkbox({
                title: 'Reverse charge',
                bind: 'isReverseCharge',
                isReadOnly: true,
                size: 'small',
            }),
            ui.nestedFields.technical({ bind: { taxCategoryReference: { _id: true } } }),
            ui.nestedFields.technical({ bind: { taxReference: { _id: true } } }),
        ],
    })
    taxes: ui.fields.Table<DocumentTax>;

    // Tab for posting information (accounting integration)
    @ui.decorators.section<PurchaseCreditMemo>({
        title: 'Posting',
        isHidden() {
            return !this.postingDetails.value.length;
        },
    })
    postingSection: ui.containers.Section;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.totalsSection;
        },
        title: 'Calculated amounts company currency',
        width: 'large',
    })
    totalsSectionCompanyCurrencyDetailsBlock: ui.containers.Block;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.postingSection;
        },
        title: 'Error details',
    })
    postingMessageBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseCreditMemo, FinanceTransactionBinding>({
        title: 'Results',
        canSelect: false,
        bind: 'postingDetails',
        canUserHideColumns: false,
        isHelperTextHidden: true,
        pageSize: 10,
        parent() {
            return this.postingSection;
        },
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.select({
                title: 'Document type',
                bind: 'targetDocumentType',
                optionType: '@sage/xtrem-finance-data/TargetDocumentType',
                isReadOnly: true,
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: 'targetDocumentNumber',
                page(_value, rowData) {
                    switch (rowData?.targetDocumentType) {
                        case 'journalEntry':
                            return '@sage/xtrem-finance/JournalEntry';
                        case 'accountsPayableInvoice':
                            return '@sage/xtrem-finance/AccountsPayableInvoice';
                        default:
                            return '@sage/xtrem-finance/AccountsPayableInvoice';
                    }
                },
                queryParameters: (_value, rowData) => ({
                    _id: rowData?.targetDocumentSysId ?? 0,
                    number: rowData?.targetDocumentNumber ?? '',
                }),
            }),
            ui.nestedFields.link({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                page: '',
                isHidden: true,
            }),
            ui.nestedFields.text({
                title: 'Accounting integration reference',
                bind: 'financeIntegrationAppRecordId',
                isHidden: false,
                isReadOnly: true,
            }),
            ui.nestedFields.label({
                title: 'Status',
                bind: 'postingStatus',
                optionType: '@sage/xtrem-finance-data/PostingStatus',
                style: (_id, rowData) =>
                    PillColorFinance.getLabelColorByStatus('FinanceDocumentPostingStatus', rowData?.postingStatus),
            }),
            ui.nestedFields.technical({ bind: 'message' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationApp' }),
            ui.nestedFields.technical({ bind: 'financeIntegrationAppUrl' }),
            ui.nestedFields.technical({ bind: 'documentSysId' }),
        ],
        onRowClick(_id, rowData) {
            this.messages.value = rowData.message;
            this.postingMessageBlock.isHidden = this.messages.value === '';
        },
        fieldActions() {
            return [this.goToSysNotificationPage];
        },
    })
    postingDetails: ui.fields.Table<FinanceTransactionBinding>;

    @ui.decorators.textAreaField<PurchaseCreditMemo>({
        parent() {
            return this.postingMessageBlock;
        },
        isTransient: true,
        isReadOnly: true,
        isFullWidth: true,
        rows: 10,
    })
    messages: ui.fields.TextArea;

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        icon: 'u_turn_right',
        title: 'Retry',
        isTitleHidden: true,
        isHidden: true,
        onClick() {
            return this.$.dialog.page(
                '@sage/xtrem-communication/SysNotificationHistory',
                {
                    recordFilter: JSON.stringify({
                        documentType: 'purchaseCreditMemo',
                        documentNumber: this.number.value,
                        _id: {
                            _nin: this.postingDetails.value
                                .filter(financeTransaction =>
                                    ['toBeGenerated', 'posted'].includes(financeTransaction.postingStatus ?? ''),
                                )
                                .map(financeTransaction => financeTransaction._id),
                        },
                    }),
                },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
    })
    goToSysNotificationPage: ui.PageAction;

    @ui.decorators.pageAction<PurchaseCreditMemo>({
        icon: 'three_boxes',
        title() {
            return stockJournalTitle(this.displayStatus.value ?? '');
        },
        isHidden() {
            return !['posted'].includes(this.displayStatus.value || '');
        },
        async onClick() {
            await StockDataUtils.onStockTransactionStatusClick(
                this,
                this.stockTransactionStatus.value as StockDocumentTransactionStatus,
                { origin: 'header', tableField: this.lines, isSourceDocumentLine: true },
            );
        },
    })
    stockJournal: ui.PageAction;

    @ui.decorators.section<PurchaseCreditMemo>({ title: 'Payments' })
    paymentsSection: ui.containers.Section;

    @ui.decorators.block<PurchaseCreditMemo>({
        parent() {
            return this.paymentsSection;
        },
        width: 'extra-large',
        title: 'Payments',
        isTitleHidden: true,
    })
    paymentsBlock: ui.containers.Block;

    @ui.decorators.referenceField<PurchaseCreditMemo, PaymentTerm>({
        parent() {
            return this.paymentsBlock;
        },
        lookupDialogTitle: 'Select payment term',
        filter: { businessEntityType: { _in: ['supplier', 'all'] } },
        minLookupCharacters: 0,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.technical({ bind: 'dueDateType' }),
            ui.nestedFields.technical({ bind: 'days' }),
        ],
        isReadOnly() {
            return !this.canEditPaymentDataAfterPost;
        },
        onChange() {
            dueDateDefault(this);
        },
    })
    paymentTerm: ui.fields.Reference<PaymentTerm>;

    @ui.decorators.dateField<PurchaseCreditMemo>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Due date',
    })
    dueDate: ui.fields.Date;

    @ui.decorators.tableField<PurchaseCreditMemo, BaseOpenItem>({
        bind: { apOpenItems: true },
        isHidden: true,
        node: '@sage/xtrem-finance-data/BaseOpenItem',
        columns: [
            ui.nestedFields.technical({ bind: { _id: true } }),
            ui.nestedFields.technical({ bind: { documentNumber: true } }),
        ],
    })
    apOpenItems: ui.fields.Table<BaseOpenItem>;

    @ui.decorators.checkboxField<PurchaseCreditMemo>({ isHidden: true })
    isOpenItemPageOptionActive: ui.fields.Checkbox;

    @ui.decorators.linkField<PurchaseCreditMemo>({
        parent() {
            return this.paymentsBlock;
        },
        title: 'Open item',
        width: 'small',
        isTransient: true,
        async onClick() {
            await this.$.dialog.page(
                `@sage/xtrem-finance/AccountsPayableOpenItem`,
                { _id: this.apOpenItems.value?.at(0)?._id ?? '', fromPurchasing: true },
                { fullScreen: true, resolveOnCancel: true },
            );
        },
        map() {
            return this.apOpenItems.value?.at(0)?.documentNumber ?? '';
        },
        isHidden() {
            return this.status.value !== 'posted' || !this.isOpenItemPageOptionActive.value;
        },
        isDisabled() {
            return this.$.queryParameters.fromFinance?.toString() === 'true';
        },
    })
    documentNumberLink: ui.fields.Link;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Total amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        scale: null,
        isReadOnly: true,
        bind: { paymentTracking: { amountPaid: true } },
        width: 'small',
        isHidden() {
            return this.status.value !== 'posted' || !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
    })
    totalAmountPaid: ui.fields.Numeric;

    @ui.decorators.numericField<PurchaseCreditMemo>({
        title: 'Forced amount paid',
        parent() {
            return this.paymentsBlock;
        },
        unit() {
            return this.currency.value;
        },
        isReadOnly: true,
        width: 'small',
        bind: { paymentTracking: { forcedAmountPaid: true } },
        isHidden() {
            return (
                this.status.value !== 'posted' ||
                !this.$.isServiceOptionEnabled('paymentTrackingOption') ||
                !this.forcedAmountPaid.value ||
                this.forcedAmountPaid.value === 0
            );
        },
    })
    forcedAmountPaid: ui.fields.Numeric;

    @ui.decorators.fragmentFields<PurchaseCreditMemo>({
        isTitleHidden: true,
        parent() {
            return this.paymentsBlock;
        },
        fragment: '@sage/xtrem-distribution/PaymentLine',
    })
    payments: ui.containers.FragmentFields;

    @ui.decorators.section<PurchaseCreditMemo>({ title: 'Notes', isTitleHidden: true })
    notesSection: ui.containers.Section;

    @ui.decorators.block<PurchaseCreditMemo>({
        title: 'Notes',
        isTitleHidden: true,
        parent() {
            return this.notesSection;
        },
    })
    notesBlock: ui.containers.Block;

    @ui.decorators.richTextField<PurchaseCreditMemo>({
        parent() {
            return this.notesBlock;
        },
        width: 'large',
        title: 'Internal notes',
        isFullWidth: true,
        capabilities: validCapabilities,
        helperText: 'Notes display on internal documents.',
    })
    internalNote: ui.fields.RichText;

    @ui.decorators.switchField<PurchaseCreditMemo>({
        parent() {
            return this.notesBlock;
        },
        isTransientInput: true,
        isHidden: true,
    })
    isOverwriteNote: ui.fields.Switch;

    async initPage() {
        const oldIsDirty = this.$.isDirty;
        this.billByAddress.isReadOnly = true;
        if (this.$.recordId) {
            if (this.status.value === 'closed') {
                this.billByAddress.isDisabled = true;
            } else {
                this.billByAddress.isDisabled = false;
            }
            this.documentNumberLink.value = this.apOpenItems.value?.at(0)?.documentNumber ?? '';
        }
        if (this.currency.value) {
            this.applyCurrency();
        }

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
        if (this.site.value && this.billBySupplier.value) {
            this._defaultDimensionsAttributes = await initDefaultDimensions({
                page: this,
                dimensionDefinitionLevel: 'purchasingDirect',
                site: this.site.value,
                supplier: this.billBySupplier.value,
            });
        }
        this.varianceBlock.isHidden = true;
        this.notesSection.isDisabled = isPostedInProgressError(this.status.value);
        this.showHideColumns();
    }

    initPosting() {
        this.postingMessageBlock.isHidden = true;
        if (this.postingDetails.value.length === 1) {
            this.messages.value = this.postingDetails.value[0].message ?? '';
            this.postingMessageBlock.isHidden = this.messages.value === '';
        }
        this.isRepost = this.$.queryParameters.fromNotificationHistory?.toString() === 'true' || false;
        this.canEditPaymentDataAfterPost =
            this.isRepost && (this.paymentTracking.value?.status ?? 'notPaid') === 'notPaid';
        this.canEditTaxDataAfterPost = this.canEditPaymentDataAfterPost && this.taxEngine.value !== 'avalaraAvaTax';
    }

    /**
     * @param rowData : Price can be 0. return - 1 when record not found.
     */
    async _setPriceOrigin(rowData: ui.PartialNodeWithId<PurchaseCreditMemoLine>) {
        if (rowData?.item && Number(rowData?.quantity) >= 0 && rowData?.unit && rowData?.currency) {
            const purchasePrice = await getPurchasePrice(
                this.$.graph,
                this.site.value?._id ?? '',
                this.billBySupplier.value?._id ?? '',
                this.currency.value?._id ?? '',
                rowData.item._id ?? '',
                rowData.unit._id ?? '',
                parseFloat(rowData.quantity ?? ''),
                new Date(this.creditMemoDate.value ?? Date.now()),
                false,
            );
            rowData.grossPrice = String(purchasePrice.price);
            rowData.priceOrigin = purchasePrice.priceOrigin ?? 'manual';

            await this._convertQuantity(rowData);
            await this.calculatePrices(rowData);
            this.lines.addOrUpdateRecordValue(rowData);
        }
    }

    private async _convertQuantity(rowData: ui.PartialNodeWithId<PurchaseCreditMemoLine>) {
        const conversion = await convertFromTo(
            this.$.graph,
            rowData.unit?._id ?? '',
            rowData.item?.stockUnit?._id ?? '',
            +(rowData.quantity ?? 0),
            rowData.item?._id,
            this.billBySupplier.value?._id,
            '',
            'purchase',
            false,
        );

        rowData.quantityInStockUnit = String(conversion.convertedQuantity);
        rowData.unitToStockUnitConversionFactor = String(conversion.conversionFactor);
    }

    private async _loadBuyers() {
        const usersQuery = await this.$.graph
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .queries.getFilteredUsers(
                { _id: true, email: true, firstName: true, lastName: true },
                { criteria: authorizationFilters.user.activeApplicationUsers },
            )
            .execute();

        const usersList: any[] = [];
        if (this.billBySupplier.value?.defaultBuyer) {
            usersList.push({
                _id: this.billBySupplier.value.defaultBuyer._id,
                ...this.billBySupplier.value.defaultBuyer,
                type: ui.localize('@sage/xtrem-purchasing/pages__purchase_supplier__default_buyer', 'Default'),
                sortOrder: (usersList.length ?? 0) + 1,
            });
            this.emailAddressApproval.value = this.billBySupplier.value.defaultBuyer.email ?? '';
        }
        usersQuery.forEach(user => {
            if (user._id !== this.billBySupplier.value?.defaultBuyer?._id) {
                usersList.push({
                    ...user,
                    type: '',
                    sortOrder: (usersList.length ?? 0) + 1,
                });
            }
        });
        this.users.value = usersList;
    }

    async calculatePrices(rowData: any, taxDate = this.supplierDocumentDate.value) {
        const prices = await calculateLinePrices({
            grossPrice: rowData.grossPrice,
            charge: rowData.charge,
            discount: rowData.discount,
            netPriceScale: getCompanyPriceScale(this?.site?.value?.legalCompany),
            quantity: rowData.quantity,
            amountExcludingTax: rowData.amountExcludingTax ?? 0,
            amountIncludingTax: rowData.amountIncludingTax ?? 0,
            taxAmount: rowData.taxAmount ?? 0,
            taxAmountAdjusted: rowData.taxAmountAdjusted ?? 0,
            rateMultiplication: this.companyFxRate.value ?? 0,
            rateDivision: this.companyFxRateDivisor.value ?? 0,
            fromDecimals: this.currency.value?.decimalDigits ?? 2,
            toDecimals: this.site.value?.legalCompany?.currency?.decimalDigits ?? 2,
            taxes: {
                site: rowData.recipientSite?._id,
                businessPartner: this.billBySupplier.value?._id,
                item: rowData.item._id,
                currency: this.currency.value?._id,
                lineNodeName: '@sage/xtrem-purchasing/PurchaseCreditMemoLine',
                taxEngine: this.taxEngine.value ?? '',
                uiTaxes: rowData.uiTaxes,
                graphObject: this.$.graph,
                taxDate: taxDate ?? '',
            },
        });
        rowData.netPrice = prices?.netPrice;
        rowData.taxAmount = prices?.taxAmount;
        rowData.taxAmountAdjusted = prices?.taxAmountAdjusted;
        rowData.uiTaxes = prices?.uiTaxes;
        rowData.taxDate = taxDate;
        rowData.amountExcludingTax = prices.amountExcludingTax;
        rowData.amountIncludingTax = prices.amountIncludingTax;
        rowData.amountExcludingTaxInCompanyCurrency = prices.amountExcludingTaxInCompanyCurrency;
        rowData.amountIncludingTaxInCompanyCurrency = prices.amountIncludingTaxInCompanyCurrency;
        rowData.taxCalculationStatus = prices.taxCalculationStatus;
        await TotalTaxCalculator.getInstance().updateTaxDetails(
            this.taxes,
            this.totalTaxAmountAdjusted,
            this.calculatedTotalTaxAmount,
        );
    }

    async fillPodBaseDocumentLines(rowId: string, rowItem: PurchaseCreditMemoLine) {
        const oldIsDirty = this.$.isDirty;
        this.purchaseReturnLines.value = [];
        this.purchaseInvoiceLines.value = [];
        const purchaseReturnLines: Array<any> = [];
        if (rowItem.origin === 'purchaseReturn') {
            const returnPurchaseReturnLines = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseReturnLineToPurchaseCreditMemoLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            purchaseReturnLine: {
                                _id: true,
                                document: { _id: true, number: true, status: true },
                                remainingQuantityToCredit: true,
                            },
                            creditMemoQuantity: true,
                        },
                        { filter: { purchaseCreditMemoLine: { _id: rowId } } },
                    ),
                )
                .execute();
            if (returnPurchaseReturnLines.edges.length) {
                purchaseReturnLines.push(returnPurchaseReturnLines.edges[0]);
                purchaseReturnLines[0].nodeName = '@sage/xtrem-purchasing/PurchaseReturn';
                purchaseReturnLines[0].linkTitle = ui.localize(
                    '@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_return',
                    'Purchase return',
                );
                const filteredPurchaseReceiptLines = await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseReceiptLineToPurchaseReturnLine')
                    .query(
                        ui.queryUtils.edgesSelector(
                            { purchaseReceiptLine: { _id: true, document: { _id: true, number: true } } },
                            {
                                filter: {
                                    purchaseReturnLine: {
                                        _id: returnPurchaseReturnLines.edges[0].node.purchaseReturnLine._id,
                                    },
                                },
                            },
                        ),
                    )
                    .execute();
                if (filteredPurchaseReceiptLines.edges.length) {
                    purchaseReturnLines[0].nodeNameReceipt = '@sage/xtrem-purchasing/PurchaseReceipt';
                    purchaseReturnLines[0].linkTitleReceipt = ui.localize(
                        '@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_receipt_from_purchase_return',
                        'Purchase receipt',
                    );
                    purchaseReturnLines[0]._idReceipt =
                        filteredPurchaseReceiptLines.edges[0].node.purchaseReceiptLine?.document._id;
                    purchaseReturnLines[0].numberReceipt =
                        filteredPurchaseReceiptLines.edges[0].node.purchaseReceiptLine?.document.number;
                }
            }
        }

        const purchaseInvoiceLines: Array<any> = [];
        if (rowItem.origin === 'purchaseInvoice') {
            const returnPurchaseInvoiceLines = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseInvoiceLineToPurchaseCreditMemoLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            purchaseInvoiceLine: {
                                _id: true,
                                document: { _id: true, number: true, status: true },
                                remainingQuantityToCredit: true,
                            },
                            creditMemoQuantity: true,
                        },
                        { filter: { purchaseCreditMemoLine: { _id: rowId } } },
                    ),
                )
                .execute();

            if (returnPurchaseInvoiceLines.edges.length) {
                purchaseInvoiceLines.push(returnPurchaseInvoiceLines.edges[0]);
                purchaseInvoiceLines[purchaseInvoiceLines.length - 1].nodeName =
                    '@sage/xtrem-purchasing/PurchaseInvoice';
                purchaseInvoiceLines[purchaseInvoiceLines.length - 1].linkTitle = ui.localize(
                    '@sage/xtrem-purchasing/pages__purchasing_credit_memo__link_column_title_purchase_invoice',
                    'Purchase invoice',
                );
            }
        }
        this.purchaseInvoiceLines.value = purchaseInvoiceLines.map((e: any) => {
            const row = e.node.purchaseInvoiceLine;
            return {
                _id: row._id,
                number: row.document.number,
                url: row.document._id,
                nodeName: e.nodeName,
                linkTitle: e.linkTitle,
                creditedQuantity: e.node.creditMemoQuantity,
                remainingQuantity: row.remainingQuantityToCredit,
                status: row.document.status,
            };
        });

        this.purchaseReturnLines.value = purchaseReturnLines.map((e: any) => {
            const row = e.node.purchaseReturnLine;
            return {
                _id: row._id,
                number: row.document.number,
                url: row.document._id,
                nodeName: e.nodeName,
                linkTitle: e.linkTitle,
                creditedQuantity: e.node.creditMemoQuantity,
                remainingQuantity: row.remainingQuantityToCredit,
                status: row.document.status,
                numberReceipt: e.numberReceipt,
                urlReceipt: e._idReceipt,
                nodeNameReceipt: e.nodeNameReceipt,
                linkTitleReceipt: e.linkTitleReceipt,
            };
        });

        if (!oldIsDirty && this.$.isDirty) {
            this.$.setPageClean();
        }
    }

    private async callDisplayTaxes(
        rowItem: ui.PartialCollectionValue<PurchaseCreditMemoLine>,
        updateTaxDetails = true,
    ) {
        const result = await displayTaxes(
            this,
            rowItem,
            {
                currency: this.currency.value ?? {},
                taxAmount: +(rowItem.taxAmount ?? 0),
                amountExcludingTax: +(rowItem.amountExcludingTax ?? 0),
                amountIncludingTax: +(rowItem.amountIncludingTax ?? 0),
                documentType: DocumentTypeEnum.purchaseCreditMemoLine,
                taxDate: rowItem.taxDate,
                legislation: this.site.value?.legalCompany?.legislation,
                editable: !isPostedInProgressError(this.status.value) || this.canEditTaxDataAfterPost,
                taxAmountAdjusted: +(rowItem.taxAmountAdjusted ?? 0),
                destinationCountry: this.billByLinkedAddress.value?.country ?? {},
                originCountry: this.site.value?.primaryAddress?.country ?? {},
                validTypes: ['purchasing', 'purchasingAndSales'],
            },
            updateTaxDetails,
        );
        if (result) {
            this.wereTaxesEdited = true;
            rowItem.amountIncludingTaxInCompanyCurrency = String(
                convertAmount(
                    Number(this.lines.getRecordValue(rowItem._id ?? '')?.amountIncludingTax),
                    this.companyFxRate.value ?? 0,
                    this.companyFxRateDivisor.value ?? 0,
                    this.currency.value?.decimalDigits ?? 2,
                    this.siteCurrency.decimalDigits ?? 2,
                ),
            );
            this.lines.addOrUpdateRecordValue({
                _id: rowItem._id,
                amountIncludingTaxInCompanyCurrency: rowItem.amountIncludingTaxInCompanyCurrency,
            });
            if (updateTaxDetails) {
                this._computeTotalAmounts();
            }
        }
    }

    showHideColumns() {
        if (isExchangeRateHidden(this.currency.value, this.site.value, this.billBySupplier.value)) {
            this.rateDescription.isHidden = true;
            this.lines.hideColumn('amountExcludingTaxInCompanyCurrency');
            this.lines.hideColumn('amountIncludingTaxInCompanyCurrency');
        } else {
            this.lines.showColumn('amountExcludingTaxInCompanyCurrency');
            this.lines.showColumn('amountIncludingTaxInCompanyCurrency');
            this.rateDescription.isHidden = false;
        }
    }

    async changeQuantityReturn(rowData: ui.PartialNodeWithId<PurchaseCreditMemoLine>) {
        if (rowData.purchaseReturnLine?.purchaseReturnLine) {
            const purchaseReturnLineQuantity = +(rowData.purchaseReturnLine.purchaseReturnLine.quantity ?? 0);
            if (+(rowData.quantity ?? 0) !== purchaseReturnLineQuantity) {
                if (
                    !(await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_credit_memo_partial_confirm__partial_dialog_title',
                            'Confirm credit memo',
                        ),

                        +(rowData.quantity ?? 0) < purchaseReturnLineQuantity
                            ? ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_credit_memo_partial__from_return_dialog_content',
                                  'You are about to create a credit memo quantity smaller than the return quantity.',
                              )
                            : ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_credit_memo_from_return__greater_dialog_content',
                                  'You are about to create a credit memo quantity larger than the return quantity',
                              ),
                        ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
                    ))
                ) {
                    rowData.quantity = rowData.purchaseReturnLine.purchaseReturnLine.quantity;
                    this.lines.addOrUpdateRecordValue(rowData);
                }
            }
        }
    }

    async changeQuantityInvoice(rowData: ui.PartialNodeWithId<PurchaseCreditMemoLine>) {
        if (rowData.purchaseInvoiceLine?.purchaseInvoiceLine) {
            const purchaseInvoiceLineQuantity = +(rowData.purchaseInvoiceLine.purchaseInvoiceLine.quantity ?? 0);
            if (+(rowData.quantity ?? 0) !== purchaseInvoiceLineQuantity) {
                if (
                    !(await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_credit_memo_partial_confirm__partial_dialog_title',
                            'Confirm credit memo',
                        ),
                        +(rowData.quantity ?? 0) < purchaseInvoiceLineQuantity
                            ? ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_credit_memo__from_invoice__smaller_dialog_content',
                                  'You are about to create a credit memo quantity smaller than the invoice quantity.',
                              )
                            : ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_credit_memo__from_invoice__greater_dialog_content',
                                  'You are about to create a credit memo quantity larger than the invoice quantity.',
                              ),
                        ui.localize('@sage/xtrem-purchasing/pages-confirm-create', 'Create'),
                    ))
                ) {
                    rowData.quantity = rowData.purchaseInvoiceLine.purchaseInvoiceLine.quantity;
                    this.lines.addOrUpdateRecordValue(rowData);
                }
            }
        }
    }

    _setStepSequenceStatusObject(
        stepSequenceValues: PurchaseCreditMemoStepSequenceStatus,
    ): Dict<ui.StepSequenceStatus> {
        const payStepSequence = stepSequenceValues.pay
            ? { [this.creditMemoStepSequencePay]: stepSequenceValues.pay }
            : {};
        return {
            [this.creditMemoStepSequenceCreate]: stepSequenceValues.create,
            [this.creditMemoStepSequencePost]: stepSequenceValues.post,
            ...(payStepSequence as Dict<ui.StepSequenceStatus>),
        };
    }

    linkedDocumentIsTransferHeaderNote() {
        return this.lines.value.some(line => {
            if (line.origin === 'purchaseInvoice') {
                return line.purchaseInvoiceLine?.purchaseInvoiceLine?.document?.isTransferHeaderNote === true;
            }
            if (line.origin === 'purchaseReturn') {
                return line.purchaseReturnLine?.purchaseReturnLine?.document?.isTransferHeaderNote === true;
            }
            return false;
        });
    }

    linkedDocumentIsTransferLineNote() {
        return this.lines.value.some(line => {
            if (line.origin === 'purchaseInvoice') {
                return line.purchaseInvoiceLine?.purchaseInvoiceLine?.document?.isTransferLineNote === true;
            }
            if (line.origin === 'purchaseReturn') {
                return line.purchaseReturnLine?.purchaseReturnLine?.document?.isTransferLineNote === true;
            }
            return false;
        });
    }

    linesFromSingleDocument() {
        const isLinesOriginInvoice = this.lines.value.every(line => line.origin === 'purchaseInvoice');
        const isLinesOriginReturn = this.lines.value.every(line => line.origin === 'purchaseReturn');

        let documentLineNumbers: string[];
        if (isLinesOriginInvoice) {
            documentLineNumbers = this.lines.value.map(
                line => line.purchaseInvoiceLine?.purchaseInvoiceLine?.document?.number ?? '',
            );
            return documentLineNumbers.every(number => number === documentLineNumbers[0]);
        }

        if (isLinesOriginReturn) {
            documentLineNumbers = this.lines.value.map(
                line => line.purchaseReturnLine?.purchaseReturnLine?.document?.number ?? '',
            );
            return documentLineNumbers.every(number => number === documentLineNumbers[0]);
        }

        return false;
    }

    lineNotesChanged() {
        return this.lines.value.some(line => line.internalNote?.value);
    }

    headerNotesChanged() {
        return this.internalNote.value !== '';
    }

    private _manageHeaderProperties() {
        const isDisabled = ['posted', 'inProgress', 'error'].includes(this.status.value ?? '');
        this.reason.isDisabled = isDisabled;
        this.supplierDocumentNumber.isDisabled = this.isRepost ? false : isDisabled;
        this.supplierDocumentDate.isDisabled = this.canEditPaymentDataAfterPost ? false : isDisabled;
        this.totalAmountExcludingTax.isDisabled = isDisabled;
        this.totalTaxAmount.isDisabled = this.canEditTaxDataAfterPost ? false : isDisabled;
        this.dueDate.isDisabled = isDisabled;
    }
}
