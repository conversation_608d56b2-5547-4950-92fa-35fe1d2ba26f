import { formatError } from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi, PurchaseDocumentApprovalStatus } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<PurchaseOrderApproval>({
    title: 'Purchase order approval',
    node: '@sage/xtrem-purchasing/PurchaseOrder',
    onError(error) {
        this.$.showToast(formatError(this, error), { timeout: 5000, type: 'error' });
        this.goto();
    },
    async onLoad() {
        if (this.$.queryParameters._id && this.$.queryParameters.action) {
            await this.updatePurchaseOrderStatus(this.$.queryParameters.action as PurchaseDocumentApprovalStatus);
            this.$.setPageClean();
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, {
                _id: this.$.queryParameters._id,
            });
        } else {
            this.$.setPageClean();
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`);
        }
    },
})
export class PurchaseOrderApproval extends ui.Page<GraphApi> {
    goto() {
        if (this.$.recordId) {
            this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`, { _id: this.$.recordId });
        }
        this.$.router.goTo(`@sage/xtrem-purchasing/PurchaseOrder`);
    }

    /** purchaseOrderNumber is  purchaseOrderSysId */
    async updatePurchaseOrderStatus(newStatus: PurchaseDocumentApprovalStatus) {
        if (this.approvalStatus.value !== 'pendingApproval') {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_order__purchase_order_status_update_fail',
                    'Update Purchase order status failed - Purchase order status is not "pending approval".',
                ),
                { timeout: 5000, type: 'error' },
            );
            return;
        }

        if (this.approvalStatus.value === 'pendingApproval') {
            const isApproved = await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.approve(true, {
                    document: this.$.recordId,
                    isApproved: newStatus === 'approved',
                })
                .execute();

            if (isApproved) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/pages__purchase_order__purchase_order_status_update_success',
                        'Update Purchase order status done. You will be redirected to the Purchase order page.',
                    ),
                    { timeout: 5000, type: 'info' },
                );
            }
        }
    }

    @ui.decorators.textField<PurchaseOrderApproval>({}) number: ui.fields.Text;

    @ui.decorators.labelField<PurchaseOrderApproval>({}) approvalStatus: ui.fields.Label;
}
