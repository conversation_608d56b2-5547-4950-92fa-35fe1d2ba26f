import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<PurchaseInvoiceAttachment>({
    module: '',
    title: 'Attachment',
    isTransient: true,
    areNavigationTabsHidden: true,
    businessActions() {
        return [this.validate];
    },
    onLoad() {
        this.$.loader.isHidden = false;
        this.$.loader.isHidden = true;
    },
    onError() {
        this.$.loader.isHidden = true;
    },
})
export class PurchaseInvoiceAttachment extends ui.Page {
    @ui.decorators.section<PurchaseInvoiceAttachment>({ isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoiceAttachment>({
        parent() {
            return this.mainSection;
        },
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.fileField<PurchaseInvoiceAttachment>({
        parent() {
            return this.mainBlock;
        },
        fileTypes: 'application/pdf',
        text: 'Notice',
    })
    pdfSupplierInvoice: ui.fields.File;

    @ui.decorators.pageAction<PurchaseInvoiceAttachment>({
        title: 'OK',
        async onClick() {
            await this.preparePageValues();
        },
    })
    validate: ui.PageAction;

    async preparePageValues() {
        const validation = await this.$.page.validate();
        if (validation.length === 0) {
            // usage of any is forced du to incompatible partials...
            const values: any = this.pdfSupplierInvoice.value?.value;
            this.$.finish({ values });
        } else {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        }
    }
}
