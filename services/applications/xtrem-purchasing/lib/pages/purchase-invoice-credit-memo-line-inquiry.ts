import type { BaseDistributionDocumentLine } from '@sage/xtrem-distribution-api';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import * as ui from '@sage/xtrem-ui';
import * as PillColorPurchase from '../client-functions/pill-color';
import { purchaseInquiries } from '../menu-items/purchasing-inquiry';

@ui.decorators.page<PurchaseInvoiceCreditMemoLineInquiry, BaseDistributionDocumentLine>({
    menuItem: purchaseInquiries,
    priority: 50,
    title: 'Purchase invoice line',
    objectTypeSingular: 'Purchase invoice and credit memo line',
    objectTypePlural: 'Purchase invoice and credit memo lines',
    module: 'purchasing',
    mode: 'default',
    node: '@sage/xtrem-distribution/BaseDistributionDocumentLine',
    access: { node: '@sage/xtrem-purchasing/PurchaseInvoiceLine', bind: '$read' },
    navigationPanel: {
        onSelect() {
            return true;
        },
        optionsMenu: [
            {
                title: 'Invoice and credit memo lines',
                graphQLFilter: { _factory: { name: { _in: ['PurchaseInvoiceLine', 'PurchaseCreditMemoLine'] } } },
            },
        ],
        listItem: {
            companyName: ui.nestedFields.reference({
                bind: { site: { legalCompany: true } },
                title: 'Company',
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                valueField: 'name',
            }),
            companyId: ui.nestedFields.text({
                bind: { site: { legalCompany: { id: true } } },
                title: 'Company ID',
                isHiddenOnMainField: true,
            }),
            siteName: ui.nestedFields.reference({
                bind: 'site',
                title: 'Financial site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            siteId: ui.nestedFields.text({
                bind: { site: { id: true } },
                title: 'Financial site ID',
                isHiddenOnMainField: true,
            }),
            stockSiteName: ui.nestedFields.reference({
                bind: 'stockSite',
                title: 'Recipient site',
                node: '@sage/xtrem-system/Site',
                tunnelPage: '@sage/xtrem-master-data/Site',
                valueField: 'name',
            }),
            stockSiteId: ui.nestedFields.text({
                bind: { stockSite: { id: true } },
                title: 'Recipient site ID',
                isHiddenOnMainField: true,
            }),
            supplierName: ui.nestedFields.reference({
                bind: { document: { businessRelation: true } },
                title: 'Supplier',
                node: '@sage/xtrem-master-data/Supplier',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
                valueField: 'name',
            }),
            supplierId: ui.nestedFields.text({
                bind: { document: { businessRelation: { id: true } } },
                title: 'Supplier ID',
            }),
            // TODO: DNE To be added as soon as the base document line refactoring has finished.
            // supplierItemName: ui.nestedFields.text({
            //     bind: { itemSupplier: { supplierItemName: true } },
            //     title: 'Supplier item name',
            //     isHiddenOnMainField: true,
            // }),
            // supplierItemCode: ui.nestedFields.text({
            //     bind: { itemSupplier: { supplierItemCode: true } },
            //     title: 'Supplier item code',
            //     isHiddenOnMainField: true,
            documentSysId: ui.nestedFields.technical({
                bind: { document: { _id: true } },
            }), // Needed for the document link
            documentLineFactoryName: ui.nestedFields.technical({
                bind: { _factory: { name: true } },
            }), // Needed for the document link
            documentLineType: ui.nestedFields.text({
                bind: { _factory: { title: true } },
                title: 'Document line type',
            }),
            title: ui.nestedFields.link({
                isFullWidth: true,
                bind: { document: { number: true } },
                title: 'Document number',
                onClick(_value, rowData) {
                    return this.$.dialog.page(
                        DocumentLink.getDocumentPageName(rowData?._factory?.name ?? ''),
                        { _id: rowData?.document?._id || '' },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                },
            }),
            status: ui.nestedFields.label({
                bind: { document: { status: true } },
                title: 'Status',
                optionType: '@sage/xtrem-master-data/BaseStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.document.status),
            }),
            documentDate: ui.nestedFields.date({
                bind: { document: { date: true } },
                title: 'Document date',
                isHiddenOnMainField: true,
            }),
            itemName: ui.nestedFields.reference({
                bind: 'item',
                title: 'Item',
            }),
            itemId: ui.nestedFields.text({ bind: { item: { id: true } }, title: 'Item ID' }),
            itemDescription: ui.nestedFields.text({
                bind: { item: { description: true } },
                title: 'Item description',
                isHiddenOnMainField: true,
            }),
            quantity: ui.nestedFields.numeric({
                bind: 'signedQuantity',
                title: 'Quantity',
                scale: (_rowId, rowData) => rowData?.unit?.decimalDigits ?? 0,
                postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                // groupAggregationMethod: 'sum', // TODO: DNE Maybe later...
            }),
            unit: ui.nestedFields.reference({
                bind: 'unit',
                title: 'Unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                columns: [
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
                isHiddenOnMainField: true,
            }),
            currencyName: ui.nestedFields.reference({
                bind: { document: { currency: true } },
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            currencyId: ui.nestedFields.text({
                bind: { document: { currency: { id: true } } },
                title: 'Currency ID',
                isHiddenOnMainField: true,
            }),
            companyCurrencyName: ui.nestedFields.reference({
                bind: { document: { companyCurrency: true } },
                title: 'Company currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                isHiddenOnMainField: true,
            }),
            companyCurrencyId: ui.nestedFields.text({
                bind: { document: { companyCurrency: { id: true } } },
                title: 'Company currency ID',
                isHiddenOnMainField: true,
            }),
            netPrice: ui.nestedFields.numeric({
                bind: 'netPrice',
                title: 'Net price',
                scale: (_rowId, rowData) => rowData?.document?.currency?.decimalDigits ?? 2,
                prefix: (_rowId, rowData) => rowData?.document?.currency?.symbol ?? '',
            }),
            signedAmountExcludingTax: ui.nestedFields.numeric({
                bind: 'signedAmountExcludingTax',
                title: 'Amount',
                scale: (_rowId, rowData) => rowData?.document?.currency?.decimalDigits ?? 2,
                prefix: (_rowId, rowData) => rowData?.document?.currency?.symbol ?? '',
                groupAggregationMethod: 'sum',
            }),
            signedAmountExcludingTaxInCompanyCurrency: ui.nestedFields.numeric({
                bind: 'signedAmountExcludingTaxInCompanyCurrency',
                title: 'Amount in company currency',
                scale: (_rowId, rowData) => rowData?.document?.companyCurrency?.decimalDigits ?? 2,
                prefix: (_rowId, rowData) => rowData?.document?.companyCurrency?.symbol ?? '',
                groupAggregationMethod: 'sum',
            }),
        },
        orderBy: { site: { id: 1 }, item: { id: 1 } },
    },
})
export class PurchaseInvoiceCreditMemoLineInquiry extends ui.Page<GraphApi, BaseDistributionDocumentLine> {}
