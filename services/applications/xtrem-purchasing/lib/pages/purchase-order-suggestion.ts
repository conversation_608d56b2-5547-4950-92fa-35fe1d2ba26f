import type { Filter } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import { asyncArray } from '@sage/xtrem-core';
import { DateValue } from '@sage/xtrem-date-time';
import type { Currency, Item, Supplier, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { getCompanyPriceScale } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import type { GraphApi, PurchaseOrder, PurchaseOrderLine } from '@sage/xtrem-purchasing-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<PurchaseOrderSuggestion>({
    title: 'Purchase order suggestion',
    module: 'purchasing',
    mode: 'default',
    isTransient: true,
    onLoad() {},
    businessActions() {
        return [this.updatePurchaseOrder];
    },
})
export class PurchaseOrderSuggestion extends ui.Page<GraphApi> {
    @ui.decorators.section<PurchaseOrderSuggestion>({ isOpen: true, isTitleHidden: true, title: 'General' })
    mainSection: ui.containers.Section;

    @ui.decorators.block<PurchaseOrderSuggestion>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Selection criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.pageAction<PurchaseOrderSuggestion>({
        title: 'Validate',
        isDisabled: true,
        async onClick() {
            const selectedLinesId = this.orderLines.selectedRecords;
            if (selectedLinesId.length) {
                const validation = await this.$.page.validate();
                if (validation.length) {
                    this.$.showToast(validation.join('\n'), { type: 'error' });
                } else {
                    this.$.loader.isHidden = false;
                    await asyncArray(selectedLinesId).forEach(async id => {
                        const orderLine = this.orderLines.getRecordValue(id);
                        if (orderLine?._id) {
                            await this.$.graph
                                .node('@sage/xtrem-purchasing/PurchaseOrderLine')
                                .update(
                                    { _id: true },
                                    { data: { _id: orderLine._id, isPurchaseOrderSuggestion: false } },
                                )
                                .execute();
                        }
                    });
                    this.$.loader.isHidden = true;
                    this.$.showToast(
                        this.orderLines.selectedRecords.length > 1
                            ? ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_order_suggestion__creation__success_multi',
                                  '{{num}} purchase orders updated',
                                  { num: this.orderLines.selectedRecords.length },
                              )
                            : ui.localize(
                                  '@sage/xtrem-purchasing/pages__purchase_order_suggestion__creation__success',
                                  '{{num}} purchase order(s) updated',
                                  { num: this.orderLines.selectedRecords.length },
                              ),
                        { timeout: 10000, type: 'success' },
                    );
                    this.$.finish();
                }
                this.$.setPageClean();
                this.$.router.goTo('@sage/xtrem-purchasing/PurchaseOrderSuggestion/');
            }
        },
        buttonType: 'primary',
    })
    updatePurchaseOrder: ui.PageAction;

    @ui.decorators.referenceField<PurchaseOrderSuggestion, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        lookupDialogTitle: 'Select site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        valueField: { id: true },
        minLookupCharacters: 0,
        isMandatory: true,
        placeholder: 'Select site',
        width: 'small',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({
                bind: 'legalCompany',
                node: '@sage/xtrem-system/Company',
                nestedFields: [ui.nestedFields.technical({ bind: 'priceScale' })],
            }),
        ],
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.dateField<PurchaseOrderSuggestion>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Order date from',
    })
    issueDateFrom: ui.fields.Date;

    @ui.decorators.dateField<PurchaseOrderSuggestion>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Order date to',
    })
    issueDateTo: ui.fields.Date;

    @ui.decorators.referenceField<PurchaseOrderSuggestion, Item>({
        title: 'Item from',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        lookupDialogTitle: 'Select item from',
        minLookupCharacters: 3,
        valueField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
        parent() {
            return this.criteriaBlock;
        },
    })
    itemFrom: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<PurchaseOrderSuggestion, Item>({
        title: 'Item to',
        lookupDialogTitle: 'Select item to',
        minLookupCharacters: 3,
        valueField: 'id',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
        ],
        parent() {
            return this.criteriaBlock;
        },
    })
    itemTo: ui.fields.Reference<Item>;

    @ui.decorators.referenceField<PurchaseOrderSuggestion, Supplier>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Supplier',
        lookupDialogTitle: 'Select supplier',
        minLookupCharacters: 3,
        shouldSuggestionsIncludeColumns: true,
    })
    businessRelation: ui.fields.Reference<Supplier>;

    @ui.decorators.buttonField<PurchaseOrderSuggestion>({
        width: 'small',
        parent() {
            return this.criteriaBlock;
        },
        map: () => ui.localize('@sage/xtrem-purchasing/search', 'Search'),
        async onClick() {
            await this.search();
        },
    })
    searchButton: ui.fields.Button;

    @ui.decorators.block<PurchaseOrderSuggestion>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Purchase order suggestions',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.tableField<PurchaseOrderSuggestion, PurchaseOrderLine>({
        canUserHideColumns: false,
        isReadOnly: true,
        isHelperTextHidden: true,
        parent() {
            return this.mainBlock;
        },
        columns: [
            ui.nestedFields.reference<PurchaseOrderSuggestion, PurchaseOrderLine, PurchaseOrder>({
                title: 'Purchase order number',
                bind: 'document',
                node: '@sage/xtrem-purchasing/PurchaseOrder',
                valueField: 'number',
            }),
            ui.nestedFields.text({ title: 'Supplier', bind: 'supplierName' }),
            ui.nestedFields.reference<PurchaseOrderSuggestion, PurchaseOrderLine, Item>({
                bind: 'item',
                title: 'Item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'id',
            }),
            ui.nestedFields.reference<PurchaseOrderSuggestion, PurchaseOrderLine, Item>({
                bind: 'item',
                title: 'Item name',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
            }),
            ui.nestedFields.text({ bind: 'itemDescription', title: 'Item description' }),
            ui.nestedFields.numeric({ bind: 'quantity', title: 'Quantity' }),
            ui.nestedFields.reference<PurchaseOrderSuggestion, PurchaseOrderLine, UnitOfMeasure>({
                title: 'Purchase unit',
                bind: 'unit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                valueField: 'name',
            }),
            ui.nestedFields.reference<PurchaseOrderSuggestion, PurchaseOrderLine, Currency>({
                title: 'Currency',
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                valueField: 'id',
            }),
            ui.nestedFields.numeric({
                title: 'Price',
                bind: 'grossPrice',
                scale() {
                    return getCompanyPriceScale(this?.stockSite?.value?.legalCompany);
                },
            }),
            ui.nestedFields.date({ title: 'Expected receipt date', bind: 'expectedReceiptDate' }),
        ],
        onRowSelected() {
            this.updatePurchaseOrder.isDisabled = false;
        },
        onRowUnselected() {
            if (!this.orderLines.selectedRecords.length) {
                this.updatePurchaseOrder.isDisabled = true;
            }
        },
    })
    orderLines: ui.fields.Table<PurchaseOrderLine>;

    async search() {
        const validation = await this.$.page.validate();
        if (validation.length) {
            this.$.showToast(validation.join('\n'), { type: 'error' });
        } else {
            this.$.loader.isHidden = false;

            this.orderLines.value = withoutEdges(
                await this.$.graph
                    .node('@sage/xtrem-purchasing/PurchaseOrderLine')
                    .query(
                        {
                            edges: {
                                node: {
                                    _id: true,
                                    item: { id: true, name: true },
                                    itemDescription: true,
                                    quantity: true,
                                    unit: { description: true, symbol: true },
                                    grossPrice: true,
                                    currency: { id: true },
                                    expectedReceiptDate: true,
                                    document: { _id: true, number: true },
                                    supplierName: true,
                                },
                            },
                        },
                        { filter: this.getFilter() },
                    )

                    .execute()
                    .finally(() => {
                        this.$.loader.isHidden = true;
                    }),
            );

            this.$.loader.isHidden = true;
        }
    }

    getFilter(): Filter<PurchaseOrderLine> {
        // Set default toDate to today
        let dateTo = DateValue.today().toString();
        if (this.issueDateTo.value) {
            dateTo = this.issueDateTo.value;
        } else {
            this.issueDateTo.value = dateTo;
        }

        let dateFrom = DateValue.today().addYears(-1).toString();
        if (this.issueDateFrom.value) {
            dateFrom = this.issueDateFrom.value;
        } else {
            this.issueDateFrom.value = dateFrom;
        }

        const item: Filter<PurchaseOrderLine> =
            this.itemFrom.value || this.itemTo.value
                ? {
                      item: {
                          id: {
                              ...(this.itemFrom.value?.id ? { _gte: this.itemFrom.value.id } : {}),
                              ...(this.itemTo.value?.id ? { _gte: this.itemTo.value.id } : {}),
                          },
                      },
                  }
                : {};

        return {
            _and: [
                {
                    status: { _eq: 8 },
                    expectedReceiptDate: { _gte: dateFrom, _lte: dateTo },
                    ...item,
                    ...(this.stockSite.value?.id ? { site: { id: { _eq: this.stockSite.value.id } } } : {}),
                    ...(this.businessRelation.value?._id
                        ? { document: { supplier: { _id: this.businessRelation.value._id } } }
                        : {}),
                },
            ],
        };
    }
}
