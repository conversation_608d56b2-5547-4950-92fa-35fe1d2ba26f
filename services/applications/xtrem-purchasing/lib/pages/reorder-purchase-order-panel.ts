import type { ExtractEdgesPartial, Filter, Logical } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import { initDefaultDimensionsOrderToOrder } from '@sage/xtrem-finance-data/build/lib/client-functions/attributes-and-dimensions';
import type { BusinessEntity, Currency, Item, Supplier, UnitOfMeasure } from '@sage/xtrem-master-data-api';
import { getCompanyPriceScale } from '@sage/xtrem-master-data/build/lib/client-functions/common';
import { getScaleValue } from '@sage/xtrem-master-data/lib/client-functions/utils';
import type { GraphApi, PriceOrigin } from '@sage/xtrem-purchasing-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as purchasingCommon from '../client-functions/common';
import type { SupplierLookUpData } from '../client-functions/shared/interfaces';
import { loadSuppliersLookUpData, suppliersLookUpOnRowClick } from '../client-functions/supplier-lookup';
import type { PurchaseOrderReturned, ReorderRecommendation } from '../client-functions/utils/interfaces';

@ui.decorators.page<ReorderPurchaseOrderPanel>({
    title: 'New purchase order',
    mode: 'default',
    node: '@sage/xtrem-purchasing/PurchaseOrder',
    module: 'xtrem-purchasing',

    businessActions() {
        return [this.cancelPurchaseOrder, this.createPurchaseOrder];
    },
    async onLoad() {
        if (this.$.queryParameters.recommendation) {
            const recommendation: ReorderRecommendation = JSON.parse(this.$.queryParameters.recommendation as string);

            this.item.value = recommendation.item;
            this.site.value = recommendation.site;
            await this.setDefaultLegalCompany(recommendation);
            await this.setDefaultSupplier(recommendation);
            this.selectedSupplier.value = this.supplier.value;
            await this.setDefaultUnits(recommendation);
            await this.setPurchaseUnitsAndMinimumQuantity();
            this.setDefaultDates(recommendation);
            await purchasingCommon.setPurchaseUnitQuantity(this, {
                purchaseQuantityField: this.purchaseQuantity,

                stockUnit: this.stockUnit.value,
                unit: this.unit.value,
                stockQuantity: recommendation.quantity,

                item: this.item.value,
                supplier: this.supplier.value,

                supplierMinimumQuantity: this.supplierMinimumQuantity,
                supplierPurchaseUnit: this.supplierPurchaseUnit,
                formatToUnitDecimalDigits: true,
            });
            await purchasingCommon.setStockUnitQuantity(this, {
                stockQuantityField: this.stockQuantity,

                stockUnit: this.stockUnit.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,

                item: this.item.value,
                supplier: this.supplier.value,
                formatToUnitDecimalDigits: true,
            });
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,
                priceOriginField: this.priceOrigin,

                supplier: this.supplier.value,
                purchasingSite: this.site.value,
                item: this.item.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.date.value,
            });

            this.isFromSalesOrder = recommendation.isFromSalesOrder ? recommendation.isFromSalesOrder : false;
            const storedAttributes = recommendation.storedAttributes ?? '{}';
            const storedDimensions = recommendation.storedDimensions ?? '{}';
            if (this.isFromSalesOrder) {
                const attributesDimensions = await initDefaultDimensionsOrderToOrder({
                    page: this,
                    dimensionDefinitionLevel: 'purchasingOrderToOrder',
                    site: recommendation.site,
                    supplier: this.supplier.value,
                    item: this.item.value,
                    storedDimensions,
                    storedAttributes,
                });
                this.storedAttributes = attributesDimensions.attributes;
                this.storedDimensions = attributesDimensions.dimensions;
            }

            this.setDefaultDataFromOrderToOrder(recommendation);
        }
    },
})
export class ReorderPurchaseOrderPanel extends ui.Page<GraphApi> {
    isFromSalesOrder = false;

    storedDimensions = '{}';

    storedAttributes = '{}';

    defaultShippingDate: string;

    purchaseUnits: string[];

    supplierMinimumQuantity = 0;

    supplierPurchaseUnit: ExtractEdgesPartial<UnitOfMeasure> | null = null;

    legalCompany: Omit<ExtractEdgesPartial<Company>, '_id' | 'priceScale'> & {
        _id: Company['_id'];
        priceScale: Company['priceScale']; // for grossPrice scale
    };

    @ui.decorators.pageAction<ReorderPurchaseOrderPanel>({
        title: 'Create',
        async onClick() {
            if (!this.supplier.value) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/page__reorder_purchase_order_panel__mandatory_supplier',
                        'You need to add a supplier.',
                    ),
                    { type: 'error' },
                );
                return;
            }
            const message = await this.$.page.validate();
            if (message.length > 0) {
                this.$.showToast(message.join(', '), { timeout: 0, type: 'error' });
                return;
            }
            this.$.loader.isHidden = false;
            const purchaseOrderCreated = (await this.$.graph
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.createPurchaseOrderReplenishment(
                    {
                        _id: true,
                        number: true,
                        lines: {
                            query: {
                                edges: {
                                    node: { _id: true, quantityInStockUnit: true, workInProgress: { _id: true } },
                                },
                            },
                        },
                    },
                    {
                        data: {
                            orderDate: this.date.value ?? '',
                            expectedReceiptDate: this.expectedReceiptDate.value ?? '',
                            quantity: this.purchaseQuantity.value ?? 0,
                            grossPrice: Number(this.grossPrice.value) ?? 0,
                            unit: this.unit.value?._id ?? '',
                            site: this.site.value?._id ?? 0,
                            stockSite: this.stockSite.value?._id ?? '',
                            item: this.item.value?._id ?? '',
                            supplier: this.supplier.value?._id ?? '',
                            priceOrigin: this.priceOrigin.value as PriceOrigin,
                            storedDimensions: this.storedDimensions,
                            storedAttributes: this.storedAttributes,
                            currency:
                                this.supplier.value.businessEntity?.currency?._id ?? this.supplier.value.currency?._id,
                        },
                    },
                )
                .execute()) as PurchaseOrderReturned;
            this.$.loader.isHidden = true;
            this.$.finish(purchaseOrderCreated);
        },
    })
    createPurchaseOrder: ui.PageAction;

    @ui.decorators.pageAction<ReorderPurchaseOrderPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancelPurchaseOrder: ui.PageAction;

    @ui.decorators.section<ReorderPurchaseOrderPanel>({})
    purchaseOrderSection: ui.containers.Section;

    @ui.decorators.block<ReorderPurchaseOrderPanel>({
        parent() {
            return this.purchaseOrderSection;
        },
    })
    purchaseOrderBlock: ui.containers.Block;

    @ui.decorators.referenceField<ReorderPurchaseOrderPanel, Supplier>({
        parent() {
            return this.purchaseOrderBlock;
        },
        title: 'Supplier',
        node: '@sage/xtrem-master-data/Supplier',
        tunnelPage: '@sage/xtrem-master-data/Supplier',
        valueField: { businessEntity: { name: true } },
        helperTextField: { businessEntity: { id: true } },
        isMandatory: true,
        isTransient: true,
        isDisabled: true,
        shouldSuggestionsIncludeColumns: true,
        columns: [
            ui.nestedFields.technical({ bind: { businessEntity: { name: true } } }),
            ui.nestedFields.technical({ bind: { businessEntity: { id: true } } }),
            ui.nestedFields.technical<ReorderPurchaseOrderPanel, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical<ReorderPurchaseOrderPanel, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
        ],
    })
    supplier: ui.fields.Reference<Supplier>;

    @ui.decorators.linkField<ReorderPurchaseOrderPanel>({
        parent() {
            return this.purchaseOrderBlock;
        },
        bind: 'supplierLink',
        isTransient: true,
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__select_supplier_link_text',
                'Select supplier',
            );
        },
        async onClick() {
            this.suppliers.value = await loadSuppliersLookUpData(
                this.$.graph,
                this.item.value?.id,
                this.site.value?.id,
            );
            this.supplierSelectionSection.isHidden = false;
            await this.$.dialog.custom('info', this.supplierSelectionSection).then(async () => {
                this.supplier.value = this.selectedSupplier.value;
                // make sure the dimensions will be defaulted from the correct supplier when coming from a sales order
                if (this.isFromSalesOrder) {
                    const attributesDimensions = await initDefaultDimensionsOrderToOrder({
                        page: this,
                        dimensionDefinitionLevel: 'purchasingOrderToOrder',
                        site: this.site.value,
                        supplier: this.supplier.value,
                        item: this.item.value,
                        storedDimensions: this.storedDimensions,
                        storedAttributes: this.storedAttributes,
                    });
                    this.storedAttributes = attributesDimensions.attributes;
                    this.storedDimensions = attributesDimensions.dimensions;
                }
                this._applyCurrency();
                await this.setPurchaseUnitsAndMinimumQuantity();
                await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                    grossPriceField: this.grossPrice,
                    priceOriginField: this.priceOrigin,

                    supplier: this.supplier.value,
                    purchasingSite: this.site.value,
                    item: this.item.value,
                    unit: this.unit.value,
                    purchaseQuantity: this.purchaseQuantity.value,
                    orderDate: this.date.value,
                });
            });
            this.selectedSupplier.value = null;
            this.supplierSelectionSection.isHidden = true;
            await purchasingCommon.setPurchaseUnitQuantity(this, {
                purchaseQuantityField: this.purchaseQuantity,

                stockUnit: this.stockUnit.value,
                unit: this.unit.value,
                stockQuantity: this.stockQuantity.value,

                item: this.item.value,
                supplier: this.supplier.value,

                supplierMinimumQuantity: this.supplierMinimumQuantity,
                supplierPurchaseUnit: this.supplierPurchaseUnit,
                formatToUnitDecimalDigits: true,
            });
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,
                priceOriginField: this.priceOrigin,

                supplier: this.supplier.value,
                purchasingSite: this.site.value,
                item: this.item.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.date.value,
            });
            await this.setPurchaseMinimumQuantity();
        },
    })
    supplierLink: ui.fields.Link;

    @ui.decorators.referenceField<ReorderPurchaseOrderPanel, Site>({
        parent() {
            return this.purchaseOrderBlock;
        },
        title: 'Purchase site',
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        valueField: 'name',
        helperTextField: 'id',
        isDisabled() {
            return !this.isFromSalesOrder;
        },
        isMandatory: true,
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isPurchase' }),
            ui.nestedFields.reference<ReorderPurchaseOrderPanel, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
            }),
        ],
        filter() {
            return this.legalCompany && this.isFromSalesOrder
                ? ({ isPurchase: true, legalCompany: { _id: this.legalCompany._id } } as Logical<Site>)
                : { isPurchase: true };
        },
        async onChange() {
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,
                priceOriginField: this.priceOrigin,

                supplier: this.supplier.value,
                purchasingSite: this.site.value,
                item: this.item.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.date.value,
            });
        },
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.separatorField<ReorderPurchaseOrderPanel>({
        parent() {
            return this.purchaseOrderBlock;
        },
        isFullWidth: false,
        isInvisible: true,
    })
    statusSeparator1: ui.fields.Separator;

    @ui.decorators.referenceField<ReorderPurchaseOrderPanel, Site>({
        parent() {
            return this.purchaseOrderBlock;
        },
        node: '@sage/xtrem-system/Site',
        tunnelPage: '@sage/xtrem-master-data/Site',
        title: 'Stock site',
        isTransient: true,
        lookupDialogTitle: 'Select stock site',
        valueField: 'name',
        helperTextField: 'id',
        minLookupCharacters: 0,
        isAutoSelectEnabled: true,
        isDisabled: true,
        isHidden() {
            return !this.isFromSalesOrder;
        },
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<ReorderPurchaseOrderPanel, Site, Company>({
                node: '@sage/xtrem-system/Company',
                tunnelPage: '@sage/xtrem-master-data/Company',
                title: 'Company',
                bind: 'legalCompany',
                valueField: 'name',
                helperTextField: 'id',
            }),
            ui.nestedFields.technical({ bind: 'description' }),
        ],
    })
    stockSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<ReorderPurchaseOrderPanel, Item>({
        parent() {
            return this.purchaseOrderBlock;
        },
        title: 'Item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        isDisabled: true,
        isTransient: true,
        columns: [
            ui.nestedFields.text({ bind: 'name', canFilter: false }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'description' }),
            ui.nestedFields.text({ title: 'Category', bind: { category: { name: true } } }),
            ui.nestedFields.technical<ReorderPurchaseOrderPanel, Item, UnitOfMeasure>({
                bind: 'stockUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
            ui.nestedFields.technical<ReorderPurchaseOrderPanel, Item, UnitOfMeasure>({
                bind: 'purchaseUnit',
                node: '@sage/xtrem-master-data/UnitOfMeasure',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.separatorField<ReorderPurchaseOrderPanel>({
        parent() {
            return this.purchaseOrderBlock;
        },
        isFullWidth: false,
        isInvisible: true,
    })
    statusSeparator2: ui.fields.Separator;

    @ui.decorators.numericField<ReorderPurchaseOrderPanel>({
        title: 'Purchase quantity',
        isTransient: true,
        isMandatory: true,
        parent() {
            return this.purchaseOrderBlock;
        },
        validation(val) {
            if (val === 0) {
                return ui.localize(
                    '@sage/xtrem-purchasing/page__reorder_purchase_order_panel__purchase_quantity_mandatory',
                    'Purchase quantity is mandatory.',
                );
            }
            return undefined;
        },
        async onChange() {
            purchasingCommon.checkPurchaseOrderLineMinimumQuantity(this, {
                supplierMinimumQuantity: this.supplierMinimumQuantity,
                supplierPurchaseUnit: this.supplierPurchaseUnit,

                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,
            });
            await purchasingCommon.setStockUnitQuantity(this, {
                stockQuantityField: this.stockQuantity,

                stockUnit: this.stockUnit.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,

                item: this.item.value,
                supplier: this.supplier.value,
                formatToUnitDecimalDigits: true,
            });
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,
                priceOriginField: this.priceOrigin,

                supplier: this.supplier.value,
                purchasingSite: this.site.value,
                item: this.item.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.date.value,
            });
        },
        scale() {
            return getScaleValue(2, this.unit.value?.decimalDigits);
        },
        postfix() {
            return this.unit.value?.symbol ?? '';
        },
    })
    purchaseQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<ReorderPurchaseOrderPanel, UnitOfMeasure>({
        parent() {
            return this.purchaseOrderBlock;
        },
        title: 'Purchase unit',
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        valueField: 'name',
        helperTextField: 'symbol',
        isMandatory: true,
        filter() {
            if (this.purchaseUnits && this.purchaseUnits.length) {
                return { id: { _in: this.purchaseUnits } } as Filter<UnitOfMeasure>;
            }
            return { isActive: true } as Filter<UnitOfMeasure>;
        },
        isTransient: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name', canFilter: false }),
            ui.nestedFields.text({ bind: 'id', isHidden: true }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
        ],
        async onChange() {
            await purchasingCommon.setPurchaseUnitQuantity(this, {
                purchaseQuantityField: this.purchaseQuantity,

                stockUnit: this.stockUnit.value,
                unit: this.unit.value,
                stockQuantity: this.stockQuantity.value,

                item: this.item.value,
                supplier: this.supplier.value,

                supplierMinimumQuantity: this.supplierMinimumQuantity,
                supplierPurchaseUnit: this.supplierPurchaseUnit,
                formatToUnitDecimalDigits: true,
            });
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,
                priceOriginField: this.priceOrigin,

                supplier: this.supplier.value,
                purchasingSite: this.site.value,
                item: this.item.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.date.value,
            });
        },
    })
    unit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<ReorderPurchaseOrderPanel>({
        title: 'Stock quantity',
        isTransient: true,
        isDisabled: true,
        parent() {
            return this.purchaseOrderBlock;
        },
        scale() {
            return this.stockUnit.value?.decimalDigits ?? 2;
        },
        postfix() {
            return this.stockUnit.value?.symbol ?? '';
        },
    })
    stockQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<ReorderPurchaseOrderPanel, UnitOfMeasure>({
        parent() {
            return this.purchaseOrderBlock;
        },
        title: 'Stock unit',
        node: '@sage/xtrem-master-data/UnitOfMeasure',
        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
        valueField: 'name',
        helperTextField: 'symbol',
        isDisabled: true,
        isTransient: true,
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name', canFilter: false }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'symbol', title: 'Symbol' }),
        ],
    })
    stockUnit: ui.fields.Reference<UnitOfMeasure>;

    @ui.decorators.numericField<ReorderPurchaseOrderPanel>({
        title: 'Gross price',
        isTransient: true,
        parent() {
            return this.purchaseOrderBlock;
        },
        scale() {
            return getCompanyPriceScale(this.legalCompany);
        },
        prefix() {
            return this.supplier.value?.businessEntity?.currency?.symbol ?? '';
        },
    })
    grossPrice: ui.fields.Numeric;

    private _applyCurrency() {
        this.grossPrice.prefix = this.supplier.value?.businessEntity?.currency?.symbol;
    }

    @ui.decorators.separatorField<ReorderPurchaseOrderPanel>({
        parent() {
            return this.purchaseOrderBlock;
        },
        isFullWidth: false,
        isInvisible: true,
    })
    statusSeparator3: ui.fields.Separator;

    @ui.decorators.dateField<ReorderPurchaseOrderPanel>({
        title: 'Order date',
        isTransient: true,
        parent() {
            return this.purchaseOrderBlock;
        },
        maxDate: DateValue.today().toString(),
        async onChange() {
            await purchasingCommon.setPurchaseGrossPriceAndPriceOrigin(this, {
                grossPriceField: this.grossPrice,
                priceOriginField: this.priceOrigin,

                supplier: this.supplier.value,
                purchasingSite: this.site.value,
                item: this.item.value,
                unit: this.unit.value,
                purchaseQuantity: this.purchaseQuantity.value,
                orderDate: this.date.value,
            });
        },
        isMandatory() {
            return this.isFromSalesOrder;
        },
    })
    date: ui.fields.Date;

    @ui.decorators.dateField<ReorderPurchaseOrderPanel>({
        title: 'Expected receipt date',
        isTransient: true,
        parent() {
            return this.purchaseOrderBlock;
        },
        minDate: DateValue.today().toString(),
        isMandatory: true,
        onChange() {
            if (
                this.isFromSalesOrder &&
                this.isFromSalesOrder === true &&
                this.expectedReceiptDate.value &&
                this.defaultShippingDate &&
                this.expectedReceiptDate.value > this.defaultShippingDate
            ) {
                this.$.showToast(
                    ui.localize(
                        '@sage/xtrem-purchasing/page__reorder_purchase_order_panel__expected_date_exceed',
                        'The expected date is later than the shipping date.',
                    ),
                    { type: 'warning' },
                );
            }
        },
    })
    expectedReceiptDate: ui.fields.Date;

    @ui.decorators.labelField<ReorderPurchaseOrderPanel>({
        title: 'Price origin',
        width: 'small',
        isHidden: true,
        isTransient: true,
        optionType: '@sage/xtrem-purchasing/PriceOrigin',
    })
    priceOrigin: ui.fields.Label;

    @ui.decorators.buttonField<ReorderPurchaseOrderPanel>({
        parent() {
            return this.purchaseOrderBlock;
        },
        map() {
            return ui.localize(
                '@sage/xtrem-purchasing/pages__reorder_purchase_order_panel__projected_stock_button_text',
                'Projected stock',
            );
        },
        width: 'small',
        isTransient: true,
        async onClick() {
            if (this.item.value && this.stockSite.value) {
                await this.$.dialog.page(
                    '@sage/xtrem-stock-data/ProjectedStockDialog',
                    {
                        item: JSON.stringify(this.item.value),
                        site: JSON.stringify(this.stockSite.value),
                        shipmentDate: this.defaultShippingDate,
                    },
                    { rightAligned: false, size: 'extra-large', resolveOnCancel: true },
                );
            }
        },
        isHidden() {
            return !this.isFromSalesOrder;
        },
    })
    projectedStock: ui.fields.Button;

    @ui.decorators.section<ReorderPurchaseOrderPanel>({
        isHidden: true,
        isTitleHidden: true,
    })
    supplierSelectionSection: ui.containers.Section;

    @ui.decorators.block<ReorderPurchaseOrderPanel>({
        parent() {
            return this.supplierSelectionSection;
        },
        isTitleHidden: true,
    })
    supplierSelectionBlock: ui.containers.Block;

    @ui.decorators.tableField<ReorderPurchaseOrderPanel, SupplierLookUpData>({
        parent() {
            return this.supplierSelectionBlock;
        },
        canSelect: false,
        title: 'Supplier',
        isTransient: true,
        isReadOnly: true,
        pageSize: 10,
        orderBy: { sortOrder: +1 },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.numeric({ title: 'Lead time', bind: 'purchaseLeadTime', canFilter: false }),
            ui.nestedFields.label({
                title: 'Type',
                bind: 'type',
                backgroundColor(value) {
                    switch (value) {
                        case ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_requisition__default_supplier',
                            'Default',
                        ):
                            return ui.tokens.colorsSemanticPositive500;
                        case ui.localize(
                            '@sage/xtrem-purchasing/pages__purchase_requisition__referenced_supplier',
                            'Referenced',
                        ):
                            return ui.tokens.colorsSemanticCaution500;
                        case ui.localize('@sage/xtrem-purchasing/pages__purchase_requisition__other_supplier', 'Other'):
                            return ui.tokens.colorsSemanticNegative500;
                        default:
                            return '#FFFFFF';
                    }
                },
            }),
            ui.nestedFields.technical({ bind: 'sortOrder' }),
            ui.nestedFields.technical<ReorderPurchaseOrderPanel, SupplierLookUpData, Currency>({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                ],
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
        ],

        async onRowClick(_rowId, rowData: SupplierLookUpData) {
            await suppliersLookUpOnRowClick(this, rowData.sortOrder, this.suppliers.value);
            this.$.loader.isHidden = false;
            this.selectedSupplier.value = rowData;
            this.$.loader.isHidden = true;
        },
    })
    suppliers: ui.fields.Table<SupplierLookUpData>;

    @ui.decorators.referenceField<ReorderPurchaseOrderPanel, Supplier>({
        parent() {
            return this.supplierSelectionBlock;
        },
        isReadOnly: true,
        node: '@sage/xtrem-master-data/Supplier',
        tunnelPage: '@sage/xtrem-master-data/Supplier',
        valueField: { businessEntity: { name: true } },
        isTransient: true,
        isFullWidth: true,
        columns: [
            ui.nestedFields.technical({ bind: { businessEntity: { name: true } } }),
            ui.nestedFields.technical({ bind: { businessEntity: { id: true } } }),
            ui.nestedFields.technical<ReorderPurchaseOrderPanel, Supplier, BusinessEntity>({
                bind: 'businessEntity',
                node: '@sage/xtrem-master-data/BusinessEntity',
                nestedFields: [
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical<ReorderPurchaseOrderPanel, BusinessEntity, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.technical({ bind: { internalNote: { value: true } } }),
        ],
    })
    selectedSupplier: ui.fields.Reference<Supplier>;

    async setPurchaseUnitsAndMinimumQuantity() {
        const purchaseUnits: string[] = [];
        if (this.item.value) {
            if (this.item.value.purchaseUnit?.id) {
                purchaseUnits.push(this.item.value.purchaseUnit.id);
            }
            if (this.item.value.stockUnit?.id) {
                purchaseUnits.push(this.item.value.stockUnit.id);
            }
            if (this.supplier.value) {
                const itemSuppliers = withoutEdges(
                    await this.$.graph
                        .node('@sage/xtrem-master-data/ItemSupplier')
                        .query(
                            ui.queryUtils.edgesSelector(
                                {
                                    purchaseUnitOfMeasure: {
                                        _id: true,
                                        id: true,
                                        name: true,
                                        symbol: true,
                                        decimalDigits: true,
                                    },
                                    minimumPurchaseQuantity: true,
                                },
                                { filter: { item: this.item.value._id, supplier: this.supplier.value._id } },
                            ),
                        )
                        .execute(),
                );
                if (itemSuppliers && itemSuppliers.length) {
                    purchaseUnits.push(itemSuppliers[0].purchaseUnitOfMeasure.id);
                    this.unit.value = itemSuppliers[0].purchaseUnitOfMeasure;
                    this.supplierPurchaseUnit = itemSuppliers[0].purchaseUnitOfMeasure;
                    this.supplierMinimumQuantity = Number(itemSuppliers[0].minimumPurchaseQuantity);
                } else {
                    this.unit.value = this.item.value.purchaseUnit!;
                    this.supplierPurchaseUnit = null;
                    this.supplierMinimumQuantity = 0;
                }
            }
        }
        this.purchaseUnits = purchaseUnits;
    }

    async setPurchaseMinimumQuantity() {
        this.supplierMinimumQuantity = 0;
        if (this.supplier.value && this.item.value) {
            const itemSuppliers = withoutEdges(
                await this.$.graph
                    .node('@sage/xtrem-master-data/ItemSupplier')
                    .query(
                        ui.queryUtils.edgesSelector(
                            { minimumPurchaseQuantity: true },
                            { filter: { item: this.item.value._id, supplier: this.supplier.value._id } },
                        ),
                    )
                    .execute(),
            );
            if (itemSuppliers && itemSuppliers.length) {
                this.supplierMinimumQuantity = Number(itemSuppliers[0].minimumPurchaseQuantity);
            }
        }
    }

    async setDefaultSupplier(recommendation: ReorderRecommendation) {
        if (recommendation.supplier) {
            this.supplier.value = recommendation.supplier;
            return;
        }
        if (recommendation.site?._id && recommendation.item?._id) {
            this.supplier.value = await this.$.graph
                .node('@sage/xtrem-master-data/Supplier')
                .queries.getDefaultSupplier(
                    {
                        _id: true,
                        id: true,
                        name: true,
                        businessEntity: {
                            id: true,
                            name: true,
                            currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                        },
                        currency: { _id: true },
                        internalNote: { value: true },
                    },
                    { item: recommendation.item._id, site: recommendation.site._id },
                )
                .execute();
        }
    }

    async setDefaultUnits(recommendation: ReorderRecommendation) {
        if (recommendation.item && recommendation.item._id) {
            const item = await this.$.graph
                .node('@sage/xtrem-master-data/Item')
                .read(
                    {
                        _id: true,
                        id: true,
                        name: true,
                        purchaseUnit: { _id: true, id: true, name: true, symbol: true, decimalDigits: true },
                        stockUnit: { _id: true, id: true, name: true, symbol: true, decimalDigits: true },
                    },
                    recommendation.item._id,
                )
                .execute();
            if (item) {
                this.unit.value = item.purchaseUnit;
                this.stockUnit.value = item.stockUnit;
                this.item.value = item;
            }
        }
    }

    setDefaultDates(recommendation: ReorderRecommendation) {
        this.date.value = recommendation.startDate;
        this.expectedReceiptDate.value = recommendation.endDate;
        if (this.isFromSalesOrder === true) {
            this.defaultShippingDate = recommendation.endDate;
            this.statusSeparator1.isHidden = true;
            this.statusSeparator2.isHidden = false;
        } else {
            this.statusSeparator1.isHidden = false;
            this.statusSeparator2.isHidden = false;
        }
    }

    setDefaultDataFromOrderToOrder(recommendation: ReorderRecommendation) {
        if (this.isFromSalesOrder === true && recommendation.stockSite) {
            this.stockSite.value = recommendation.stockSite;
        }
    }

    async setDefaultLegalCompany(recommendation: ReorderRecommendation) {
        let companyId = '';
        if (recommendation.site && recommendation.site.legalCompany && recommendation.site.legalCompany._id) {
            companyId = recommendation.site.legalCompany._id;
        } else if (recommendation.legalCompany && recommendation.legalCompany._id) {
            companyId = recommendation.legalCompany._id;
        }
        if (companyId) {
            // if the priceScale is missing in legalCompany properties => add it
            this.legalCompany = await this.$.graph
                .node('@sage/xtrem-system/Company')
                .read({ _id: true, priceScale: true }, companyId)
                .execute();
        }
    }
}
