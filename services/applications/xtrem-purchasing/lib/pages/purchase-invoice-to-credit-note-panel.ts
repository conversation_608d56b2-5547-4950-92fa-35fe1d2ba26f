import type { Currency, ReasonCode } from '@sage/xtrem-master-data-api';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<PurchaseInvoiceToCreditNotePanel>({
    title: 'Create purchase credit memo',
    mode: 'tabs',
    isTransient: true,
    module: 'xtrem-purchasing',
    access: { node: '@sage/xtrem-purchasing/PurchaseInvoice', bind: '$create' },
    businessActions() {
        return [this.cancel, this.create];
    },
    onLoad() {
        this.currency.value = this.$.queryParameters.currency
            ? JSON.parse(this.$.queryParameters.currency as string)
            : null;
    },
})
export class PurchaseInvoiceToCreditNotePanel extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<PurchaseInvoiceToCreditNotePanel>({
        title: 'Create',
        async onClick() {
            const validation = await this.$.page.validate();
            if (validation.length === 0) {
                this.$.finish({
                    reasonCreditNote: this.reasonCreditNote.value ? this.reasonCreditNote.value._id : '',
                    supplierDocumentDateCreditNote: this.supplierDocumentDateCreditNote.value,
                    totalAmountExcludingTaxCreditNote: this.totalAmountExcludingTaxCreditNote.value,
                });
            } else {
                this.$.showToast(validation.join('\n'), { type: 'error' });
            }
        },
    })
    create: ui.PageAction;

    @ui.decorators.pageAction<PurchaseInvoiceToCreditNotePanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<PurchaseInvoiceToCreditNotePanel>({ title: 'Create purchase credit memo' })
    creditNoteSection: ui.containers.Section;

    @ui.decorators.block<PurchaseInvoiceToCreditNotePanel>({
        parent() {
            return this.creditNoteSection;
        },
        width: 'extra-large',
    })
    creditNoteBlock: ui.containers.Block;

    @ui.decorators.referenceField<PurchaseInvoiceToCreditNotePanel, ReasonCode>({
        parent() {
            return this.creditNoteBlock;
        },
        title: 'Reason',
        lookupDialogTitle: 'Select reason',
        minLookupCharacters: 0,
        node: '@sage/xtrem-master-data/ReasonCode',
        tunnelPage: '@sage/xtrem-master-data/ReasonCode',
        valueField: 'name',
        fetchesDefaults: true,
        isMandatory: true,
        isTransient: true,
        columns: [ui.nestedFields.text({ bind: 'name' })],
    })
    reasonCreditNote: ui.fields.Reference<ReasonCode>;

    @ui.decorators.dateField<PurchaseInvoiceToCreditNotePanel>({
        parent() {
            return this.creditNoteBlock;
        },
        title: 'Supplier document date',
        isMandatory: true,
        isTransient: true,
        validation(value) {
            if (Date.parse(value) > Date.now()) {
                return ui.localize(
                    '@sage/xtrem-purchasing/pages__purchase_invoice__supplier_document_date__cannot__be__future',
                    'The supplier document date cannot be later than today.',
                );
            }

            return undefined;
        },
    })
    supplierDocumentDateCreditNote: ui.fields.Date;

    @ui.decorators.numericField<PurchaseInvoiceToCreditNotePanel>({
        parent() {
            return this.creditNoteBlock;
        },
        title: 'Total amount excluding tax',
        unit() {
            return this.currency?.value;
        },
        scale: null,
        isMandatory: true,
        isTransient: true,
    })
    totalAmountExcludingTaxCreditNote: ui.fields.Numeric;

    @ui.decorators.referenceField<PurchaseInvoiceToCreditNotePanel, Currency>({
        parent() {
            return this.creditNoteBlock;
        },
        title: 'Currency',
        lookupDialogTitle: 'Select currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.numeric({
                title: 'Decimal places',
                bind: 'decimalDigits',
                canFilter: false,
                isHidden: true,
            }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.checkbox({ bind: 'isActive', isHidden: true }),
        ],
        filter() {
            return { isActive: true };
        },
        minLookupCharacters: 1,
        width: 'small',
    })
    currency: ui.fields.Reference;
}
