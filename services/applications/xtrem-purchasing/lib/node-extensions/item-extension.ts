import type { Collection } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '..';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemExtension extends NodeExtension<xtremMasterData.nodes.Item> {
    @decorators.collectionProperty<ItemExtension, 'purchaseDocuments'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremPurchasing.nodes.PurchaseOrderLine,
        getFilter() {
            return { item: this._id, status: { _in: ['inProgress', 'draft', 'pending'] } };
        },
    })
    readonly purchaseDocuments: Collection<xtremPurchasing.nodes.PurchaseOrderLine>;
}

declare module '@sage/xtrem-master-data/lib/nodes/item' {
    interface Item extends ItemExtension {}
}
