import { NodeExtension, ValidationSeverity, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { doPurchaseDocumentsExist } from '../functions';

@decorators.nodeExtension<ItemSiteExtension>({
    extends: () => xtremMasterData.nodes.ItemSite,

    async controlDelete(cx) {
        if (
            !(await (await this.item).isStockManaged) &&
            (await doPurchaseDocumentsExist(this.$.context, await this.item, await this.site))
        ) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-purchasing/nodes__item-site__failed_deletion_impossible_if_documents',
                    'Delete not allowed. Purchasing documents exist for this item-site.',
                ),
            );
        }
    },
})
export class ItemSiteExtension extends NodeExtension<xtremMasterData.nodes.ItemSite> {}

declare module '@sage/xtrem-master-data/lib/nodes/item-site' {
    interface ItemSite extends ItemSiteExtension {}
}
