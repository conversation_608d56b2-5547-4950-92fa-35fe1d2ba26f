import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

@decorators.nodeExtension<SiteExtension>({ extends: () => xtremSystem.nodes.Site })
export class SiteExtension extends NodeExtension<xtremSystem.nodes.Site> {
    @decorators.booleanProperty<SiteExtension, 'isPurchaseRequisitionApprovalManaged'>({
        isPublished: true,
        isStored: true,
        defaultValue: true,
        lookupAccess: true,
        async control(cx, isManaged) {
            // If approval managed, no need to check for pending purchase requisitions.
            if (isManaged) {
                return;
            }
            await xtremPurchasing.events.controls.Requisition.checkPending(this.$.context, cx, this._id);
        },
    })
    readonly isPurchaseRequisitionApprovalManaged: Promise<boolean>;

    @decorators.referenceProperty<SiteExtension, 'purchaseRequisitionDefaultApprover'>({
        isPublished: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isStored: true,
        lookupAccess: true,
        filters: { lookup: xtremAuthorization.filters.user.interactiveUsers },
    })
    readonly purchaseRequisitionDefaultApprover: Reference<xtremSystem.nodes.User | null>;

    @decorators.referenceProperty<SiteExtension, 'purchaseRequisitionSubstituteApprover'>({
        isPublished: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isStored: true,
        lookupAccess: true,
        filters: { lookup: xtremAuthorization.filters.user.interactiveUsers },
    })
    readonly purchaseRequisitionSubstituteApprover: Reference<xtremSystem.nodes.User | null>;

    @decorators.booleanProperty<SiteExtension, 'isPurchaseOrderApprovalManaged'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        async control(cx, isManaged) {
            if (!isManaged) {
                await cx.error
                    .withMessage(
                        '@sage/xtrem-purchasing/node_site_extension_check_if_any_pending',
                        'You need to approve or reject pending documents before disabling the approval process.',
                    )
                    .if(
                        await this.$.context.queryCount(xtremPurchasing.nodes.PurchaseOrder, {
                            filter: { site: this._id, approvalStatus: 'pendingApproval' },
                        }),
                    )
                    .is.not.equal.to(0);
            }
        },
    })
    readonly isPurchaseOrderApprovalManaged: Promise<boolean>;

    @decorators.booleanProperty<SiteExtension, 'isPurchaseReturnApprovalManaged'>({
        isStored: true,
        isPublished: true,
        defaultValue: true,
        async control(cx, isManaged) {
            if (!isManaged) {
                await cx.error
                    .withMessage(
                        '@sage/xtrem-purchasing/node_site_extension_check_if_any_pending',
                        'You need to approve or reject pending documents before disabling the approval process.',
                    )
                    .if(
                        await this.$.context.queryCount(xtremPurchasing.nodes.PurchaseReturn, {
                            filter: { site: this._id, approvalStatus: 'pendingApproval' },
                        }),
                    )
                    .is.not.equal.to(0);
            }
        },
    })
    readonly isPurchaseReturnApprovalManaged: Promise<boolean>;

    @decorators.referenceProperty<SiteExtension, 'purchaseOrderDefaultApprover'>({
        isPublished: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isStored: true,
        lookupAccess: true,
        filters: { lookup: xtremAuthorization.filters.user.interactiveUsers },
    })
    readonly purchaseOrderDefaultApprover: Reference<xtremSystem.nodes.User | null>;

    @decorators.referenceProperty<SiteExtension, 'purchaseOrderSubstituteApprover'>({
        isPublished: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isStored: true,
        lookupAccess: true,
        filters: { lookup: xtremAuthorization.filters.user.interactiveUsers },
    })
    readonly purchaseOrderSubstituteApprover: Reference<xtremSystem.nodes.User | null>;
}
declare module '@sage/xtrem-system/lib/nodes/site' {
    export interface Site extends SiteExtension {}
}
