import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { Collection, Reference } from '@sage/xtrem-core';
import { SubNodeExtension1, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremPurchasing from '../index';

@decorators.subNodeExtension1<SupplierExtension>({ extends: () => xtremMasterData.nodes.Supplier })
export class SupplierExtension extends SubNodeExtension1<xtremMasterData.nodes.Supplier> {
    @decorators.collectionProperty<SupplierExtension, 'purchaseOrders'>({
        isPublished: true,
        node: () => xtremPurchasing.nodes.PurchaseOrder,
        reverseReference: 'businessRelation',
    })
    readonly purchaseOrders: Collection<xtremPurchasing.nodes.PurchaseOrder>;

    @decorators.referenceProperty<SupplierExtension, 'defaultBuyer'>({
        isPublished: true,
        node: () => xtremSystem.nodes.User,
        isNullable: true,
        isStored: true,
        lookupAccess: true,
        filters: { lookup: xtremAuthorization.filters.user.interactiveUsers },
    })
    readonly defaultBuyer: Reference<xtremSystem.nodes.User | null>;
}
declare module '@sage/xtrem-master-data/lib/nodes/supplier' {
    export interface Supplier extends SupplierExtension {}
}
