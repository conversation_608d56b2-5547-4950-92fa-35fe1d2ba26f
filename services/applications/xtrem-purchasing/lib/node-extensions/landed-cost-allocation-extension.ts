import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import type * as xtremPurchasing from '../index';

@decorators.nodeExtension<LandedCostAllocationExtension>({
    extends: () => xtremLandedCost.nodes.LandedCostAllocation,

    async controlEnd(cx) {
        if (
            this.$.status === 'added' &&
            (await this.allocatedDocumentType) === 'purchaseReceipt' &&
            (await ((await this.allocatedDocumentLine) as xtremPurchasing.nodes.PurchaseReceiptLine)
                .stockTransactionStatus) !== 'completed'
        ) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__landed_cost_allocation__stock_transaction_status_not_completed',
                'The stock status of all purchase receipt lines allocated to landed cost needs to be Completed.',
            );
        }
    },
})
export class LandedCostAllocationExtension extends NodeExtension<xtremLandedCost.nodes.LandedCostAllocation> {}

declare module '@sage/xtrem-landed-cost/lib/nodes/landed-cost-allocation' {
    interface LandedCostAllocation extends LandedCostAllocationExtension {}
}
