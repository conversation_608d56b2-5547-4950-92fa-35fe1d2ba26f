import type { date } from '@sage/xtrem-core';
import { NodeExtension, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '..';

@decorators.nodeExtension<PaymentTrackingExtension>({
    extends: () => xtremFinanceData.nodes.PaymentTracking,
})
export class PaymentTrackingExtension extends NodeExtension<xtremFinanceData.nodes.PaymentTracking> {
    @decorators.datePropertyOverride<PaymentTrackingExtension, 'discountPaymentBeforeDate'>({
        dependsOn: ['paymentTerm', 'document'],
        async defaultValue() {
            const paymentTerm = await this.paymentTerm;
            const discountDate = await paymentTerm?.discountDate;
            const document = await this.document;
            const baseDate = xtremPurchasing.functions.PaymentTracking.isPurchaseInvoiceOrPurchaseCreditMemo(document)
                ? await document.supplierDocumentDate
                : await document.date;

            return discountDate
                ? xtremMasterData.sharedFunctions.getDiscountPaymentBeforeDate({
                      discountType: (await paymentTerm?.discountFrom) ?? 'afterInvoiceDate',
                      discountDate,
                      baseDate,
                  })
                : null;
        },
        updatedValue: useDefaultValue,
    })
    readonly discountPaymentBeforeDate: Promise<date | null>;
}

declare module '@sage/xtrem-finance-data/lib/nodes/payment-tracking' {
    interface PaymentTracking extends PaymentTrackingExtension {}
}
