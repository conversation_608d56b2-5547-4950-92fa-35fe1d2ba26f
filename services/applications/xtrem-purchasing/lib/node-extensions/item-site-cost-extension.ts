import { NodeExtension, ValidationSeverity, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { doPurchaseDocumentsExist } from '../functions';

@decorators.nodeExtension<ItemSiteCostExtension>({
    extends: () => xtremMasterData.nodes.ItemSiteCost,

    async controlDelete(cx) {
        if (!this.allowDeletion) return; // Item-site-cost cannot be deleted because an error has already been detected.
        if ((await this.itemSite).deletionInProgress) return; // Item-site can be deleted. So can item-site-cost.

        if (
            !(await (await (await this.itemSite).item).isStockManaged) &&
            (await doPurchaseDocumentsExist(
                this.$.context,
                await (
                    await this.itemSite
                ).item,
                await (
                    await this.itemSite
                ).site,
            ))
        ) {
            cx.addDiagnose(
                ValidationSeverity.error,
                cx.localize(
                    '@sage/xtrem-purchasing/nodes__item-site-cost__failed_deletion_impossible_if_documents',
                    'Delete not allowed. Purchase documents exist for this item-site.',
                ),
            );
        }
    },
})
export class ItemSiteCostExtension extends NodeExtension<xtremMasterData.nodes.ItemSiteCost> {}

declare module '@sage/xtrem-master-data/lib/nodes/item-site-cost' {
    interface ItemSiteCost extends ItemSiteCostExtension {}
}
