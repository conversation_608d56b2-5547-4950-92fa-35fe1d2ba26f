/**  Purchase document */
const _purchaseDocumentFieldAndActions = {
    /** pending line : dimensions */
    editableLinePropertiesForPendingLineStatus: ['dimensions'],
    /** pending line : dimensions */
    availableLineActionsForPendingLineStatus: ['dimensions'],
    /** inProgress line :  */
    availableLineActionsForInProgressLineStatus: [''],
};

export function isPurchaseDocumentLinePropertyDisabled(
    orderStatus: string | null,
    lineStatus: string,
    propertyName: string,
) {
    const isDisabled = orderStatus === 'closed';

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !_purchaseDocumentFieldAndActions.editableLinePropertiesForPendingLineStatus.includes(propertyName)
            );
        case 'inProgress':
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

export function isPurchaseDocumentLineActionDisabled(
    orderStatus: string | null,
    lineStatus: string,
    actionName: string,
) {
    const isDisabled = orderStatus === 'closed';

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !_purchaseDocumentFieldAndActions.availableLineActionsForPendingLineStatus.includes(actionName)
            );
        case 'inProgress':
            return (
                isDisabled ||
                !_purchaseDocumentFieldAndActions.availableLineActionsForInProgressLineStatus.includes(actionName)
            );
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

// Purchase Requisition

const _purchaseRequisitionFieldAndActions = {
    editableLinePropertiesForPendingLineStatus: [
        'needByDate',
        'supplierLink',
        'supplier',
        'unit',
        'unitToStockUnitConversionFactor',
        'quantityInStockUnit',
    ],
    availableLineActionsForPendingLineStatus: ['close', 'dimensions', 'applyDefaultSuppliers', 'edit', 'delete'],
    availableLineActionsForInProgressLineStatus: ['close'],
};

export function isPurchaseRequisitionLinePropertyDisabled(
    requisitionStatus: string,
    lineStatus: string,
    propertyName: string,
    approvalStatus: string,
): boolean {
    const isDisabled = requisitionStatus === 'closed';

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !(
                    _purchaseRequisitionFieldAndActions.editableLinePropertiesForPendingLineStatus.includes(
                        propertyName,
                    ) || approvalStatus === 'confirmed'
                )
            );
        case 'inProgress':
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

export function isPurchaseRequisitionLineActionDisabled(
    requisitionStatus: string,
    lineStatus: string,
    actionName: string,
) {
    const isDisabled = requisitionStatus === 'closed' || (lineStatus === 'draft' && actionName === 'close');

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !_purchaseRequisitionFieldAndActions.availableLineActionsForPendingLineStatus.includes(actionName)
            );
        case 'inProgress':
            return (
                isDisabled ||
                !_purchaseRequisitionFieldAndActions.availableLineActionsForInProgressLineStatus.includes(actionName)
            );
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

// Purchase order
const _purchaseOrderFieldAndActions = {
    editableLinePropertiesForPendingLineStatus: ['expectedReceiptDate', 'dimensions'],
    availableLineActionsForPendingLineStatus: ['close', 'dimensions'],
    availableLineActionsForInProgressLineStatus: ['close'],
};

export function isPurchaseOrderLinePropertyDisabled(
    orderStatus: string,
    lineStatus: string,
    propertyName: string,
    approvalStatus: string,
    isRepost = false,
) {
    const isDisabled = orderStatus === 'closed' || isRepost;

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !(
                    _purchaseOrderFieldAndActions.editableLinePropertiesForPendingLineStatus.includes(propertyName) ||
                    approvalStatus === 'confirmed'
                )
            );
        case 'inProgress':
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

export function isPurchaseOrderLineActionDisabled(
    orderStatus: string,
    lineStatus: string,
    actionName: string,
    approvalStatus: string,
    isRepost = false,
) {
    const isDisabled = orderStatus === 'closed' || isRepost;

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !(
                    _purchaseOrderFieldAndActions.availableLineActionsForPendingLineStatus.includes(actionName) ||
                    approvalStatus === 'confirmed'
                )
            );
        case 'inProgress':
            return (
                isDisabled ||
                !_purchaseOrderFieldAndActions.availableLineActionsForInProgressLineStatus.includes(actionName)
            );
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

// Purchase receipt
const _purchaseReceiptFieldAndActions = {
    availableLineActionsForPendingLineStatus: ['close'],
    availableLineActionsForInProgressLineStatus: ['close'],
};

export function isPurchaseReceiptLinePropertyDisabled(receiptStatus: string, lineStatus: string) {
    const isDisabled = ['closed', 'inProgress'].includes(receiptStatus);

    switch (lineStatus) {
        case 'pending':
            return true;
        case 'inProgress':
            return true;
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

export function isPurchaseReceiptLineActionDisabled(receiptStatus: string, lineStatus: string, actionName: string) {
    const isDisabled = receiptStatus === 'closed';

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !_purchaseReceiptFieldAndActions.availableLineActionsForPendingLineStatus.includes(actionName)
            );
        case 'inProgress':
            return (
                isDisabled ||
                !_purchaseReceiptFieldAndActions.availableLineActionsForInProgressLineStatus.includes(actionName)
            );
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

// Purchase invoice
const _purchaseInvoiceFieldAndActions = {
    availableLineActionsForPendingLineStatus: ['close', 'dimensions'],
    availableLineActionsForInProgressLineStatus: ['close'],
};

export function isPurchaseInvoiceLinePropertyDisabled(invoiceStatus: string, lineStatus: string) {
    const isDisabled = ['closed', 'inProgress'].includes(invoiceStatus);

    switch (lineStatus) {
        case 'pending':
            return true;
        case 'inProgress':
            return true;
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

export function isPurchaseInvoiceLineActionDisabled(invoiceStatus: string, lineStatus: string, actionName: string) {
    const isDisabled = ['posted', 'inProgress', 'error'].includes(invoiceStatus);

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !_purchaseInvoiceFieldAndActions.availableLineActionsForPendingLineStatus.includes(actionName)
            );
        case 'inProgress':
            return (
                isDisabled ||
                !_purchaseInvoiceFieldAndActions.availableLineActionsForInProgressLineStatus.includes(actionName)
            );
        case 'posted':
            return true;
        default:
            return isDisabled;
    }
}

// Purchase credit memo
const _purchaseCreditMemoFieldAndActions = {
    availableLineActionsForPendingLineStatus: ['close', 'dimensions'],
    availableLineActionsForInProgressLineStatus: ['close'],
};

export function isPurchaseCreditMemoLinePropertyDisabled(creditMemoStatus: string, lineStatus: string) {
    const isDisabled = ['closed', 'inProgress'].includes(creditMemoStatus);

    switch (lineStatus) {
        case 'pending':
            return true;
        case 'inProgress':
            return true;
        case 'closed':
            return true;
        default:
            return isDisabled;
    }
}

export function isPurchaseCreditMemoLineActionDisabled(invoiceStatus: string, lineStatus: string, actionName: string) {
    const isDisabled = ['posted', 'inProgress', 'error'].includes(invoiceStatus);

    switch (lineStatus) {
        case 'pending':
            return (
                isDisabled ||
                !_purchaseCreditMemoFieldAndActions.availableLineActionsForPendingLineStatus.includes(actionName)
            );
        case 'inProgress':
            return (
                isDisabled ||
                !_purchaseCreditMemoFieldAndActions.availableLineActionsForInProgressLineStatus.includes(actionName)
            );
        case 'posted':
            return true;
        default:
            return isDisabled;
    }
}
