import { asyncArray } from '@sage/xtrem-async-helper';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import {
    getQueryParameters,
    openLandedCostDocumentLinePickList,
} from '@sage/xtrem-landed-cost/build/lib/client-functions/landed-cost-allocation-helpers';
import type { LandedCostAllocationPanel as LandedCostAllocationPanelPage } from '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-allocation-panel';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import type { StockDocumentTransactionStatus } from '@sage/xtrem-stock-data-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import * as ui from '@sage/xtrem-ui';
import * as displayButtons from '../client-functions/display-buttons-purchasing-landed-cost-allocation-panel';

@ui.decorators.pageExtension<LandedCostAllocationPanelExtension>({
    extends: '@sage/xtrem-landed-cost/LandedCostAllocationPanel',
    async onLoad() {
        this.initPurchaseExtension();
        this.manageDisplayButtonSelectFromReceiptAction();
        await this.setStockTransactionStatus();
        this.headerBusinessActions.push(this.selectFromPurchaseReceipt, this.selectFromPurchaseOrder);
    },
})
export class LandedCostAllocationPanelExtension extends ui.PageExtension<LandedCostAllocationPanelPage, GraphApi> {
    panelPurchaseExtensionParameters: LandedCostInterfaces.LandedCostAllocationParameters;

    @ui.decorators.pageAction<LandedCostAllocationPanelExtension>({
        icon: 'search',
        title: 'Add lines from receipts',
        async onClick() {
            await openLandedCostDocumentLinePickList(this, this.panelPurchaseExtensionParameters, 'purchaseReceipt');
            await this.setStockTransactionStatus();
        },
    })
    selectFromPurchaseReceipt: ui.PageAction;

    @ui.decorators.pageAction<LandedCostAllocationPanelExtension>({
        icon: 'search',
        title: 'Add lines from orders',
        async onClick() {
            await openLandedCostDocumentLinePickList(this, this.panelPurchaseExtensionParameters, 'purchaseOrder');
        },
    })
    selectFromPurchaseOrder: ui.PageAction;

    @ui.decorators.tableFieldOverride<LandedCostAllocationPanelExtension>({
        // TODO: XT-62301
        // replace the headerBusinessActions below with addItemActions
        // when we will be able to have both drop down action button + phantom row hidden
        // addItemActions() {
        //     return [this.selectFromPurchaseReceipt, this.selectFromPurchaseOrder];
        // },
        columns: [
            ui.nestedFieldExtensions.label({
                title: 'Stock status',
                bind: 'stockTransactionStatus',
                insertAfter: 'distributionValue',
                style: (_id, rowData) =>
                    PillColorStock.getLabelColorByStatus(
                        'StockDocumentTransactionStatus',
                        rowData?.stockTransactionStatus,
                    ),
            }),
        ],
    })
    lines: ui.fields.Table<
        LandedCostInterfaces.LandedCostAllocationPageBinding & {
            stockTransactionStatus?: StockDocumentTransactionStatus;
        }
    >;

    private initPurchaseExtension() {
        const queryParams = getQueryParameters(this.$.queryParameters.args);
        if (!queryParams) {
            throw new Error('Parameters are missing.');
        }
        this.panelPurchaseExtensionParameters = queryParams;
    }

    private manageDisplayButtonSelectFromReceiptAction() {
        this.selectFromPurchaseReceipt.isHidden = displayButtons.isHiddenButtonSelectFromReceiptAction({
            parameters: { isEditable: this.panelPurchaseExtensionParameters.isEditable },
        });
        this.selectFromPurchaseReceipt.isDisabled = displayButtons.isDisabledButtonSelectFromReceiptAction({
            parameters: { isEditable: this.panelPurchaseExtensionParameters.isEditable },
        });

        this.selectFromPurchaseOrder.isHidden = displayButtons.isHiddenButtonSelectFromOrderAction({
            parameters: { isEditable: this.panelPurchaseExtensionParameters.isEditable },
        });
        this.selectFromPurchaseOrder.isDisabled = displayButtons.isDisabledButtonSelectFromOrderAction({
            parameters: { isEditable: this.panelPurchaseExtensionParameters.isEditable },
        });
    }

    async setStockTransactionStatus() {
        await asyncArray(this.lines.value)
            .filter(line => line.allocatedDocumentType === 'purchaseReceipt' && !line.stockTransactionStatus)
            .forEach(async line => {
                const receiptLineId = line.allocatedDocumentLine?._id;
                if (receiptLineId) {
                    const receiptLine = await this.$.graph
                        .node('@sage/xtrem-purchasing/PurchaseReceiptLine')
                        .read({ stockTransactionStatus: true }, `${receiptLineId}`)
                        .execute();
                    if (receiptLine.stockTransactionStatus) {
                        line.stockTransactionStatus = receiptLine.stockTransactionStatus;
                        this.lines.addOrUpdateRecordValue(line);
                    }
                }
            });
    }
}

declare module '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-allocation-panel' {
    interface LandedCostAllocationPanel extends LandedCostAllocationPanelExtension {}
}
