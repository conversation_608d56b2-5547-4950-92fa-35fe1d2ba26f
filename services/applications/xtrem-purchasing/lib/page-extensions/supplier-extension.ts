import type { Supplier as SupplierNode } from '@sage/xtrem-master-data-api';
import type { Supplier } from '@sage/xtrem-master-data/build/lib/pages/supplier';
import type { GraphApi, PurchaseOrder } from '@sage/xtrem-purchasing-api';
import type { User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as PillColorPurchase from '../client-functions/pill-color';

@ui.decorators.pageExtension<SupplierExtension, SupplierNode>({
    extends: '@sage/xtrem-master-data/Supplier',
    navigationPanel: {
        listItem: {
            line11: ui.nestedFieldExtensions.reference({
                title: 'Default buyer',
                bind: { defaultBuyer: true },
                node: '@sage/xtrem-system/User',
                valueField: 'displayName',
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class SupplierExtension extends ui.PageExtension<Supplier, GraphApi> {
    @ui.decorators.section<SupplierExtension>({
        title: 'Purchasing',
        isTitleHidden: true,
        insertBefore() {
            return this.noteSection;
        },
    })
    purchasingSection: ui.containers.Section;

    @ui.decorators.block<SupplierExtension>({
        parent() {
            return this.purchasingSection;
        },
        title: 'Purchasing',
        isTitleHidden: true,
    })
    defaultBuyerBlock: ui.containers.Block;

    @ui.decorators.referenceField<SupplierExtension, User>({
        parent() {
            return this.defaultBuyerBlock;
        },
        title: 'Default buyer',
        node: '@sage/xtrem-system/User',
        valueField: 'displayName',
        lookupDialogTitle: 'Select default buyer',
        width: 'large',
        bind: { defaultBuyer: true },
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
    })
    defaultBuyer: ui.fields.Reference<User>;

    @ui.decorators.tableField<SupplierExtension, PurchaseOrder>({
        title: 'Orders',
        node: '@sage/xtrem-purchasing/PurchaseOrder',
        canUserHideColumns: false,
        canSelect: false,
        isHelperTextHidden: true,
        isReadOnly: true,
        bind: { purchaseOrders: true },
        parent() {
            return this.purchasingSection;
        },
        columns: [
            ui.nestedFields.text({ bind: 'number', title: 'Number', size: 'small' }),
            ui.nestedFields.label({
                bind: 'status',
                title: 'Status',
                size: 'small',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentStatus', rowData?.status),
            }),
            ui.nestedFields.label({
                bind: 'approvalStatus',
                title: 'Approval status',
                size: 'small',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentApprovalStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseDocumentApprovalStatus', rowData?.approvalStatus),
            }),
            ui.nestedFields.text<SupplierExtension>({ bind: { stockSite: { name: true } }, title: 'Stock site' }),
            ui.nestedFields.date({ bind: 'orderDate', title: 'Date' }),
            ui.nestedFields.text({ bind: { paymentTerm: { name: true } }, title: 'Payment term', size: 'small' }),
            ui.nestedFields.numeric({ bind: 'totalAmountExcludingTax', title: 'Amount', scale: 2 }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'View order',
                onClick(_id, order) {
                    this.$.router.goTo('@sage/xtrem-purchasing/PurchaseOrder', { _id: order._id });
                },
            },
        ],
    })
    purchaseOrders: ui.fields.Table<PurchaseOrder>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/supplier' {
    export interface Supplier extends SupplierExtension {}
}
