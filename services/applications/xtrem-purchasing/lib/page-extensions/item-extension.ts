import * as Pill<PERSON><PERSON><PERSON><PERSON><PERSON>mon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { Item as ItemPage } from '@sage/xtrem-master-data/build/lib/pages/item';
import type { GraphApi, PurchaseOrderLine } from '@sage/xtrem-purchasing-api';
import * as ui from '@sage/xtrem-ui';
import { isPurchaseOrderLine, isPurchaseOrderLineBinding } from '../client-functions/common';
import * as PillColorPurchase from '../client-functions/pill-color';

@ui.decorators.pageExtension<ItemExtension>({
    extends: '@sage/xtrem-master-data/Item',
    extensionAccessBinding: { node: '@sage/xtrem-purchasing/PurchaseOrder', bind: '$read' },
})
export class ItemExtension extends ui.PageExtension<ItemPage, GraphApi> {
    @ui.decorators.section<ItemExtension>({
        title: 'Documents',
        isLazyLoaded: true,
        isTitleHidden: true,
        insertBefore() {
            return this.allergenSection;
        },
        isHidden() {
            return !this.isBought.value;
        },
    })
    documentSection: ui.containers.Section;

    @ui.decorators.tableField<ItemExtension, PurchaseOrderLine>({
        node: '@sage/xtrem-purchasing/PurchaseOrderLine',
        title: 'Documents',
        isTitleHidden: true,
        canSelect: false,
        canExport: true,
        isReadOnly: true,
        isChangeIndicatorDisabled: true,
        pageSize: 10,
        bind: 'purchaseDocuments',
        parent() {
            return this.documentSection;
        },
        orderBy: { document: { orderDate: -1 }, stockSite: 1 },
        mobileCard: undefined,
        columns: [
            ui.nestedFields.technical<ItemExtension, PurchaseOrderLine>({ bind: { document: { _id: true } } }),
            ui.nestedFields.technical<ItemExtension, PurchaseOrderLine>({
                bind: 'item',
                nestedFields: [
                    ui.nestedFields.technical({ bind: { stockUnit: { id: true } } }),
                    ui.nestedFields.technical({ bind: { stockUnit: { symbol: true } } }),
                ],
            }),
            ui.nestedFields.date<ItemExtension, PurchaseOrderLine>({
                bind: { document: { orderDate: true } },
                title: 'Document date',
            }),
            ui.nestedFields.link({
                title: 'Document number',
                bind: { document: { number: true } },
                onClick(_rowId, rowData) {
                    if (isPurchaseOrderLineBinding(rowData)) {
                        return this.$.dialog.page(
                            '@sage/xtrem-purchasing/PurchaseOrder',
                            { _id: rowData.document._id },
                            { fullScreen: true, resolveOnCancel: true },
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.label<ItemExtension, PurchaseOrderLine>({
                bind: { document: { displayStatus: true } },
                optionType: '@sage/xtrem-purchasing/PurchaseOrderDisplayStatus',
                title: 'Document status',
                isHiddenOnMainField: true,
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.document?.displayStatus),
            }),
            ui.nestedFields.label<ItemExtension, PurchaseOrderLine>({
                title: 'Line status',
                bind: 'status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.status),
            }),
            ui.nestedFields.reference<ItemExtension, PurchaseOrderLine>({
                bind: { document: { site: true } },
                isHiddenOnMainField: true,
                title: 'Document site',
                valueField: 'name',
                node: '@sage/xtrem-system/Site',
            }),
            ui.nestedFields.reference<ItemExtension, PurchaseOrderLine>({
                bind: { document: { site: true } },
                isHiddenOnMainField: true,
                title: 'Document site ID',
                valueField: 'id',
                node: '@sage/xtrem-system/Site',
            }),
            ui.nestedFields.reference<ItemExtension, PurchaseOrderLine>({
                bind: 'stockSite',
                valueField: 'name',
                title: 'Stock site',
                node: '@sage/xtrem-system/Site',
            }),
            ui.nestedFields.reference<ItemExtension, PurchaseOrderLine>({
                bind: 'stockSite',
                isHiddenOnMainField: true,
                valueField: 'id',
                title: 'Stock site ID',
                node: '@sage/xtrem-system/Site',
            }),
            ui.nestedFields.reference<ItemExtension, PurchaseOrderLine>({
                bind: { document: { supplier: true } },
                title: 'Supplier',
                valueField: 'name',
                node: '@sage/xtrem-master-data/Supplier',
            }),
            ui.nestedFields.reference<ItemExtension, PurchaseOrderLine>({
                bind: { document: { supplier: true } },
                isHiddenOnMainField: true,
                title: 'Supplier ID',
                valueField: 'id',
                node: '@sage/xtrem-master-data/Supplier',
            }),
            ui.nestedFields.label<ItemExtension>({
                bind: '_constructor',
                map() {
                    return 'Purchase order';
                },
                backgroundColor: ui.tokens.colorsYang100,
                borderColor: ui.tokens.colorsSemanticNeutral500,
                title: 'Type',
            }),
            ui.nestedFields.numeric<ItemExtension, PurchaseOrderLine>({
                title: 'Ordered quantity',
                bind: 'quantity',
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.item.stockUnit.decimalDigits),
                postfix(_rowId, rowData) {
                    if (isPurchaseOrderLine(rowData)) {
                        return rowData.item.stockUnit?.symbol ?? '';
                    }
                    return '';
                },
                isHidden() {
                    return this.type.value === 'service';
                },
            }),
            ui.nestedFields.numeric<ItemExtension, PurchaseOrderLine>({
                title: 'Remaining quantity',
                bind: 'quantityToReceive',
                isReadOnly: true,
                scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.item.stockUnit.decimalDigits),
                postfix(_rowId, rowData) {
                    if (isPurchaseOrderLine(rowData)) {
                        return rowData.item.stockUnit?.symbol ?? '';
                    }
                    return '';
                },
                isHidden() {
                    return this.type.value === 'service';
                },
            }),
            ui.nestedFields.date<ItemExtension, PurchaseOrderLine>({
                bind: 'expectedReceiptDate',
                title: 'Expected receipt date',
            }),
            ui.nestedFields.label<ItemExtension, PurchaseOrderLine>({
                bind: 'lineReceiptStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PurchaseOrderReceiptStatus',
                title: 'Receipt status',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderReceiptStatus', rowData?.lineReceiptStatus),
            }),
            ui.nestedFields.label<ItemExtension, PurchaseOrderLine>({
                bind: 'lineInvoiceStatus',
                isHiddenOnMainField: true,
                optionType: '@sage/xtrem-purchasing/PurchaseOrderInvoiceStatus',
                title: 'Invoice status',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderInvoiceStatus', rowData?.lineInvoiceStatus),
            }),
        ],
        optionsMenu: [
            { title: 'All statuses', graphQLFilter: {} },
            { title: 'Draft', graphQLFilter: { status: { _eq: 'draft' } } },
            { title: 'Pending', graphQLFilter: { status: { _eq: 'pending' } } },
            { title: 'In progress', graphQLFilter: { status: { _eq: 'inProgress' } } },
        ],
    })
    itemDocuments: ui.fields.Table<PurchaseOrderLine>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/item' {
    interface Item extends ItemExtension {}
}
