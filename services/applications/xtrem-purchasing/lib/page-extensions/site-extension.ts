import type { Site } from '@sage/xtrem-master-data/build/lib/pages/site';
import type { GraphApi } from '@sage/xtrem-purchasing-api';
import type { User } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import {
    onChangeIsPurchaseOrderApprovalManaged,
    onChangeIsPurchaseRequisitionApprovalManaged,
    onChangeIsPurchaseReturnApprovalManaged,
} from '../client-functions/site';

@ui.decorators.pageExtension<SiteExtension>({
    extends: '@sage/xtrem-master-data/Site',
    navigationPanel: {
        listItem: {
            line11: ui.nestedFieldExtensions.reference({
                title: 'Default buyer',
                bind: { businessEntity: { supplier: { defaultBuyer: true } } },
                node: '@sage/xtrem-system/User',
                valueField: 'displayName',
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class SiteExtension extends ui.PageExtension<Site, GraphApi> {
    wasDirtyBeforeChanging: boolean;

    @ui.decorators.block<SiteExtension>({
        parent() {
            return this.managementSection;
        },
        title: 'Purchasing approval',
    })
    defaultApproverBlock: ui.containers.Block;

    @ui.decorators.switchField<SiteExtension>({
        title: 'Requisition',
        parent() {
            return this.defaultApproverBlock;
        },
        async onChange() {
            this.wasDirtyBeforeChanging = this.$.isDirty;
            await onChangeIsPurchaseRequisitionApprovalManaged(this, this.wasDirtyBeforeChanging);
        },
    })
    isPurchaseRequisitionApprovalManaged: ui.fields.Switch;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.defaultApproverBlock;
        },
        title: 'Approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        width: 'large',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        minLookupCharacters: 2,
        isReadOnly() {
            return !this.isPurchaseRequisitionApprovalManaged.value;
        },
    })
    purchaseRequisitionDefaultApprover: ui.fields.Reference<User>;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.defaultApproverBlock;
        },
        title: 'Substitute approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        width: 'large',
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        minLookupCharacters: 2,
        isReadOnly() {
            return !this.isPurchaseRequisitionApprovalManaged.value;
        },
    })
    purchaseRequisitionSubstituteApprover: ui.fields.Reference<User>;

    @ui.decorators.separatorField<SiteExtension>({
        parent() {
            return this.defaultApproverBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    approverSeparator: ui.fields.Separator;

    @ui.decorators.switchField<SiteExtension>({
        parent() {
            return this.defaultApproverBlock;
        },
        title: 'Order',
        onClick() {
            this.wasDirtyBeforeChanging = this.$.isDirty;
        },
        async onChange() {
            await onChangeIsPurchaseOrderApprovalManaged(this, this.wasDirtyBeforeChanging);
        },
    })
    isPurchaseOrderApprovalManaged: ui.fields.Switch;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.defaultApproverBlock;
        },
        title: 'Approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        width: 'large',
        isReadOnly() {
            return !this.isPurchaseOrderApprovalManaged.value;
        },
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        minLookupCharacters: 2,
    })
    purchaseOrderDefaultApprover: ui.fields.Reference<User>;

    @ui.decorators.referenceField<SiteExtension, User>({
        parent() {
            return this.defaultApproverBlock;
        },
        title: 'Substitute approver',
        node: '@sage/xtrem-system/User',
        tunnelPage: '@sage/xtrem-authorization/User',
        valueField: 'displayName',
        width: 'large',
        isReadOnly() {
            return !this.isPurchaseOrderApprovalManaged.value;
        },
        columns: [
            ui.nestedFields.text({ title: 'Last name', bind: 'lastName' }),
            ui.nestedFields.text({ title: 'First name', bind: 'firstName' }),
            ui.nestedFields.text({ title: 'Email', bind: 'email' }),
        ],
        minLookupCharacters: 2,
    })
    purchaseOrderSubstituteApprover: ui.fields.Reference<User>;

    @ui.decorators.separatorField<SiteExtension>({
        parent() {
            return this.defaultApproverBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    returnSeparator: ui.fields.Separator;

    @ui.decorators.switchField<SiteExtension>({
        parent() {
            return this.defaultApproverBlock;
        },
        title: 'Return',
        onClick() {
            this.wasDirtyBeforeChanging = this.$.isDirty;
        },
        async onChange() {
            if (this.isPurchaseReturnApprovalManaged.value === false) {
                await onChangeIsPurchaseReturnApprovalManaged(this, this.wasDirtyBeforeChanging);
            }
        },
    })
    isPurchaseReturnApprovalManaged: ui.fields.Switch;
}

declare module '@sage/xtrem-master-data/build/lib/pages/site' {
    export interface Site extends SiteExtension {}
}
