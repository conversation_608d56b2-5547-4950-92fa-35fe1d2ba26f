import type { Filter, Selector } from '@sage/xtrem-client';
import { extractEdges, querySelector } from '@sage/xtrem-client';
import type * as LandedCostInterfaces from '@sage/xtrem-landed-cost/build/lib/client-functions/interfaces';
import { clearNestedGrid } from '@sage/xtrem-landed-cost/build/lib/client-functions/landed-cost-allocation-helpers';
import type { LandedCostDocumentLinePickList as LandedCostDocumentLinePickListPage } from '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-document-line-pick-list';
import type { Supplier } from '@sage/xtrem-master-data-api';
import * as PillColorCommon from '@sage/xtrem-master-data/build/lib/client-functions/pill-color';
import * as MasterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type {
    GraphApi,
    PurchaseOrder,
    PurchaseOrderLine,
    PurchaseReceipt,
    PurchaseReceiptLine,
} from '@sage/xtrem-purchasing-api';
import * as PillColorStock from '@sage/xtrem-stock-data/build/lib/client-functions/pill-color';
import type { Country } from '@sage/xtrem-structure-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import type * as PurchaseClientInterfaces from '../client-functions/interfaces';
import * as PillColorPurchase from '../client-functions/pill-color';

@ui.decorators.pageExtension<LandedCostDocumentLinePickListExtension>({
    extends: '@sage/xtrem-landed-cost/LandedCostDocumentLinePickList',
    onLoad() {
        this.init();
    },
})
export class LandedCostDocumentLinePickListExtension extends ui.PageExtension<
    LandedCostDocumentLinePickListPage,
    GraphApi
> {
    isPurchaseSelectionActive: boolean;

    purchaseOrderFilter: Filter<PurchaseOrder>;

    purchaseReceiptFilter: Filter<PurchaseReceipt>;

    @ui.decorators.referenceField<LandedCostDocumentLinePickListExtension, PurchaseReceipt>({
        parent() {
            return this.lineBlock;
        },
        title: 'Purchase receipt',
        lookupDialogTitle: 'Select purchase receipt',
        node: '@sage/xtrem-purchasing/PurchaseReceipt',
        valueField: 'number',
        minLookupCharacters: 3,
        width: 'small',
        insertBefore() {
            return this.item ?? null;
        },
        isHidden() {
            return !this.isSelectingPurchaseReceipt();
        },
        onChange() {
            clearNestedGrid(this);
        },
        filter() {
            return this.purchaseReceiptFilter;
        },
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, PurchaseReceipt, Supplier>({
                title: 'Supplier',
                bind: 'supplier',
                node: '@sage/xtrem-master-data/Supplier',
                valueField: 'name',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
            }),
            ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseDocumentStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, PurchaseReceipt, Site>({
                title: 'Site',
                bind: 'site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
        ],
    })
    purchaseReceipt: ui.fields.Reference<PurchaseReceipt>;

    @ui.decorators.referenceField<LandedCostDocumentLinePickListExtension, PurchaseOrder>({
        parent() {
            return this.lineBlock;
        },
        title: 'Purchase order',
        lookupDialogTitle: 'Select purchase order',
        node: '@sage/xtrem-purchasing/PurchaseOrder',
        valueField: 'number',
        minLookupCharacters: 3,
        width: 'small',
        isHidden() {
            return !this.isPurchaseSelectionActive;
        },
        insertBefore() {
            return this.item ?? null;
        },
        onChange() {
            clearNestedGrid(this);
        },
        filter() {
            return this.purchaseOrderFilter;
        },
        columns: [
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, PurchaseOrder, Supplier>({
                title: 'Supplier',
                bind: 'supplier',
                node: '@sage/xtrem-master-data/Supplier',
                valueField: 'name',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
            }),
            ui.nestedFields.label({
                bind: 'receiptStatus',
                title: 'Receipt status',
                optionType: '@sage/xtrem-purchasing/PurchaseOrderReceiptStatus',
                style: (_id, rowData) =>
                    PillColorPurchase.getLabelColorByStatus('PurchaseOrderReceiptStatus', rowData?.receiptStatus),
            }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, PurchaseOrder, Site>({
                title: 'Site',
                bind: 'site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
        ],
    })
    purchaseOrder: ui.fields.Reference<PurchaseOrder>;

    @ui.decorators.referenceField<LandedCostDocumentLinePickListExtension, Supplier>({
        parent() {
            return this.lineBlock;
        },
        title: 'Supplier',
        node: '@sage/xtrem-master-data/Supplier',
        tunnelPage: '@sage/xtrem-master-data/Supplier',
        valueField: 'name',
        helperTextField: 'id',
        lookupDialogTitle: 'Select supplier',
        minLookupCharacters: 3,
        isAutoSelectEnabled: true,
        shouldSuggestionsIncludeColumns: true,
        width: 'small',
        isHidden() {
            return !this.isPurchaseSelectionActive;
        },
        insertAfter() {
            return this.item;
        },
        onChange() {
            clearNestedGrid(this);
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: { businessEntity: { name: true } } }),
            ui.nestedFields.text({ title: 'ID', bind: { businessEntity: { id: true } } }),
            ui.nestedFields.text({ title: 'Tax ID', bind: { businessEntity: { taxIdNumber: true } } }),
            ui.nestedFields.reference<LandedCostDocumentLinePickListExtension, Supplier, Country>({
                bind: { businessEntity: { country: true } },
            }),
        ],
    })
    supplier: ui.fields.Reference<Supplier>;

    @ui.decorators.referenceField<LandedCostDocumentLinePickListExtension, PurchaseReceipt>({
        parent() {
            return this.lineBlock;
        },
        title: 'Supplier packing slip',
        lookupDialogTitle: 'Select supplier packing slip',
        node: '@sage/xtrem-purchasing/PurchaseReceipt',
        valueField: 'supplierDocumentNumber',
        minLookupCharacters: 3,
        width: 'small',
        isHidden() {
            return !this.isSelectingPurchaseReceipt();
        },
        insertAfter() {
            return this.supplier ?? null;
        },
        onChange() {
            clearNestedGrid(this);
        },
        filter() {
            return {
                ...this.purchaseReceiptFilter,
                supplierDocumentNumber: { _ne: '' },
            };
        },
        columns: [
            ui.nestedFields.text({ title: 'Supplier packing slip', bind: 'supplierDocumentNumber' }),
            ui.nestedFields.text({ title: 'Number', bind: 'number' }),
            ui.nestedFields.reference<
                LandedCostDocumentLinePickListExtension,
                PurchaseReceipt,
                PurchaseReceipt['supplier']
            >({
                title: 'Supplier',
                bind: 'supplier',
                node: '@sage/xtrem-master-data/Supplier',
                valueField: 'name',
                tunnelPage: '@sage/xtrem-master-data/Supplier',
            }),
            ui.nestedFields.label({
                bind: 'displayStatus',
                title: 'Status',
                optionType: '@sage/xtrem-purchasing/PurchaseReceiptDisplayStatus',
                style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
            }),
            ui.nestedFields.reference<
                LandedCostDocumentLinePickListExtension,
                PurchaseReceipt,
                PurchaseReceipt['site']
            >({
                title: 'Site',
                bind: 'site',
                tunnelPage: '@sage/xtrem-master-data/Site',
            }),
        ],
    })
    purchaseReceiptPackingSlip: ui.fields.Reference<PurchaseReceipt>;

    @ui.decorators.buttonField<LandedCostDocumentLinePickListExtension>({
        parent() {
            return this.lineBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list__search', 'Search');
        },
        isHidden() {
            return !this.isPurchaseSelectionActive;
        },
        async onClick() {
            this.$.loader.isHidden = false;
            clearNestedGrid(this);
            await this.searchPurchaseDocument();
            this.$.loader.isHidden = true;
        },
    })
    searchPurchaseButton: ui.fields.Button;

    @ui.decorators.nestedGridFieldOverride<
        LandedCostDocumentLinePickListExtension,
        [PurchaseClientInterfaces.PurchaseReceiptNestedGrid, PurchaseClientInterfaces.PurchaseReceiptLineNestedGrid]
    >({
        levels: [
            {
                columns: [
                    ui.nestedFieldExtensions.reference<
                        LandedCostDocumentLinePickListExtension,
                        PurchaseClientInterfaces.PurchaseReceiptNestedGrid,
                        PurchaseReceipt['supplier']
                    >({
                        title: 'Supplier',
                        bind: 'supplier',
                        node: '@sage/xtrem-master-data/Supplier',
                        valueField: 'name',
                        tunnelPage: '@sage/xtrem-master-data/Supplier',
                        insertAfter: 'number',
                        isHidden() {
                            return !this.isPurchaseSelectionActive;
                        },
                    }),
                    ui.nestedFieldExtensions.text({
                        title: 'Supplier packing slip',
                        bind: 'supplierDocumentNumber',
                        insertBefore: 'supplier',
                        isHidden() {
                            return !this.isSelectingPurchaseReceipt();
                        },
                    }),
                    ui.nestedFieldExtensions.label({
                        title: 'Status',
                        bind: 'displayStatus',
                        insertAfter: 'number',
                        optionType: '@sage/xtrem-purchasing/PurchaseReceiptDisplayStatus',
                        isHidden() {
                            return !this.isSelectingPurchaseReceipt();
                        },
                        style: (_id, rowData) => PillColorCommon.getDisplayStatusPillFeatures(rowData?.displayStatus),
                    }),
                ],
                columnOverrides: [
                    ui.nestedFieldExtensions.text<LandedCostDocumentLinePickListExtension>({
                        title() {
                            if (this.isSelectingPurchaseReceipt() || this.isSelectingStockTransferReceipt()) {
                                return ui.localize(
                                    '@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__receipt_number',
                                    'Receipt number',
                                );
                            }
                            if (this.isSelectingPurchaseOrder()) {
                                return ui.localize(
                                    '@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__order_number',
                                    'Order number',
                                );
                            }
                            return '';
                        },
                        bind: 'number',
                        isReadOnly: true,
                    }),
                ],
            },
            {
                columns: [
                    ui.nestedFieldExtensions.numeric<
                        LandedCostDocumentLinePickListExtension,
                        PurchaseClientInterfaces.PurchaseReceiptLineNestedGrid
                    >({
                        title() {
                            if (this.isSelectingPurchaseReceipt() || this.isSelectingStockTransferReceipt()) {
                                return ui.localize(
                                    '@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__received_quantity',
                                    'Received quantity',
                                );
                            }
                            if (this.isSelectingPurchaseOrder()) {
                                return ui.localize(
                                    '@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list_extension__ordered_quantity',
                                    'Ordered quantity',
                                );
                            }
                            return '';
                        },
                        bind: 'quantity',
                        isReadOnly: true,
                        insertBefore: 'amountExcludingTax',
                        scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                        postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                    }),
                    ui.nestedFieldExtensions.numeric<
                        LandedCostDocumentLinePickListExtension,
                        PurchaseClientInterfaces.PurchaseReceiptLineNestedGrid
                    >({
                        title: 'Received quantity',
                        bind: 'receivedQuantity',
                        isReadOnly: true,
                        insertAfter: 'quantity',
                        isHidden() {
                            return !this.isSelectingPurchaseOrder();
                        },
                        scale: (_rowId, rowData) => MasterDataUtils.getScaleValue(2, rowData?.unit?.decimalDigits),
                        postfix: (_rowId, rowData) => rowData?.unit?.symbol ?? '',
                    }),
                    ui.nestedFieldExtensions.label({
                        title: 'Stock status',
                        bind: 'stockTransactionStatus',
                        insertAfter: 'amountExcludingTax',
                        optionType: '@sage/xtrem-stock-data/StockDocumentTransactionStatus',
                        isHidden() {
                            return !this.isSelectingPurchaseReceipt() && !this.isSelectingStockTransferReceipt();
                        },
                        style: (_id, rowData) =>
                            PillColorStock.getLabelColorByStatus(
                                'StockDocumentTransactionStatus',
                                rowData?.stockTransactionStatus,
                            ),
                    }),
                ],
            },
        ],
    })
    documents: ui.fields.NestedGrid<
        [LandedCostInterfaces.DocumentNestedGrid, LandedCostInterfaces.DocumentLineNestedGrid],
        LandedCostDocumentLinePickListPage
    >;

    isSelectingPurchaseReceipt() {
        return this.getThis().documentType.value === 'purchaseReceipt';
    }

    isSelectingPurchaseOrder() {
        return this.getThis().documentType.value === 'purchaseOrder';
    }

    init() {
        this.isPurchaseSelectionActive = this.isSelectingPurchaseReceipt() || this.isSelectingPurchaseOrder();

        const commonFilter = {
            site: { legalCompany: { _id: { _eq: this.getThis().companyId.value } } },
            lines: { _atLeast: 1 },
        };

        if (this.isSelectingPurchaseReceipt()) {
            this.purchaseOrderFilter = {
                ...commonFilter,
                status: { _in: ['inProgress', 'closed'] },
                receiptStatus: { _in: ['partiallyReceived', 'received'] },
            };
        } else if (this.isSelectingPurchaseOrder()) {
            this.purchaseOrderFilter = {
                ...commonFilter,
                status: { _in: ['pending', 'inProgress'] },
                receiptStatus: { _in: ['notReceived', 'partiallyReceived'] },
            };
        } else {
            this.purchaseOrderFilter = { ...commonFilter };
        }

        this.purchaseReceiptFilter = {
            ...commonFilter,
            status: { _ne: 'draft' },
        };
    }

    async searchPurchaseDocument() {
        const filteredPurchaseDocuments = await this.getPurchaseDocuments();

        this.documents.value = filteredPurchaseDocuments.filter(
            receipt => receipt.lines?.length > 0,
        ) as unknown as PurchaseClientInterfaces.PurchaseReceiptNestedGrid[];

        if (!filteredPurchaseDocuments.length) {
            this.$.showToast(
                ui.localize(
                    '@sage/xtrem-purchasing/pages__document_line_landed_cost_pick_list___no_results',
                    'No results found.',
                ),
                { type: 'warning' },
            );
        }
    }

    async getPurchaseDocuments() {
        const selector = this.getSelector();
        const headerFilter = this.isSelectingPurchaseReceipt()
            ? this.getReceiptHeaderFilter()
            : this.getOrderHeaderFilter();

        let nodeName: any = '@sage/xtrem-purchasing/PurchaseOrder';
        if (this.isSelectingPurchaseReceipt()) {
            nodeName = '@sage/xtrem-purchasing/PurchaseReceipt';
        }

        return extractEdges<Partial<PurchaseReceipt | PurchaseOrder>>(
            await this.$.graph
                .node(nodeName)
                .query(
                    ui.queryUtils.edgesSelector<PurchaseReceipt | PurchaseOrder>(selector, {
                        first: 1000,
                        filter: headerFilter,
                        orderBy: { number: +1 },
                    }),
                )
                .execute(),
        );
    }

    getCommonHeaderFilter(): Filter<PurchaseOrder | PurchaseReceipt> {
        if (this.supplier.value) {
            return { supplier: { _id: this.supplier.value._id } };
        }
        return {};
    }

    getOrderHeaderFilter(): Filter<PurchaseOrder> {
        if (!this.isSelectingPurchaseOrder()) {
            return {};
        }

        return {
            ...(this.getCommonHeaderFilter() as Filter<PurchaseOrder>),
            ...this.purchaseOrderFilter,
            ...(this.purchaseOrder.value ? { _id: this.purchaseOrder.value?._id } : {}),
        };
    }

    getReceiptHeaderFilter(): Filter<PurchaseReceipt> {
        if (!this.isSelectingPurchaseReceipt()) {
            return {};
        }

        return {
            ...(this.getCommonHeaderFilter() as Filter<PurchaseReceipt>),
            ...this.purchaseReceiptFilter,
            ...(this.purchaseReceipt.value ? { _id: this.purchaseReceipt.value._id } : {}),
            ...(this.purchaseReceiptPackingSlip.value
                ? { supplierDocumentNumber: this.purchaseReceiptPackingSlip.value.supplierDocumentNumber }
                : {}),
        };
    }

    getCommonLineFilter(): Filter<PurchaseOrderLine | PurchaseReceiptLine> {
        return {
            _id: { _nin: JSON.parse(this.getThis().alreadyPickedLineIds.value ?? '[]') },
            item: { isStockManaged: true },
            ...(this.getThis().item?.value ? { item: { _id: this.getThis().item?.value?._id ?? '' } } : {}),
        };
    }

    getOrderLineFilter(): Filter<PurchaseOrderLine> {
        if (!this.isSelectingPurchaseOrder()) {
            return {};
        }

        return {
            ...(this.getCommonLineFilter() as Filter<PurchaseOrderLine>),
            lineReceiptStatus: { _ne: 'received' },
        };
    }

    getReceiptLineFilter(): Filter<PurchaseReceiptLine> {
        if (!this.isSelectingPurchaseReceipt()) {
            return {};
        }

        return {
            ...(this.getCommonLineFilter() as Filter<PurchaseReceiptLine>),
            ...(this.purchaseOrder.value
                ? {
                      purchaseOrderLine: {
                          purchaseOrderLine: { document: { _id: { _eq: this.purchaseOrder.value?._id } } },
                      },
                  }
                : {}),
        };
    }

    getSelector(): Selector<PurchaseOrder | PurchaseReceipt> {
        const lineFilter = this.isSelectingPurchaseReceipt() ? this.getReceiptLineFilter() : this.getOrderLineFilter();

        return {
            _id: true,
            number: true,
            ...(this.isSelectingPurchaseReceipt() ? { supplierDocumentNumber: true, displayStatus: true } : {}),
            supplier: { name: true },
            totalAmountExcludingTax: true,
            currency: { id: true, symbol: true, decimalDigits: true },
            lines: querySelector(
                {
                    _id: true,
                    _sortValue: true,
                    item: {
                        id: true,
                        name: true,
                        weight: true,
                        volume: true,
                        stockUnit: { _id: true, id: true, symbol: true, decimalDigits: true },
                        weightUnit: { _id: true },
                        volumeUnit: { _id: true },
                    },
                    quantity: true,
                    ...(this.isSelectingPurchaseOrder() ? { receivedQuantity: true } : {}),
                    ...(this.isSelectingPurchaseReceipt() ? { stockTransactionStatus: true } : {}),
                    quantityInStockUnit: true,
                    unit: { id: true, symbol: true, decimalDigits: true },
                    amountExcludingTax: true,
                    currency: { id: true, symbol: true, decimalDigits: true },
                    document: { _id: true, number: true },
                },
                { first: 1000, filter: lineFilter },
            ),
        };
    }

    getThis() {
        return this as unknown as ExtensionMembers<
            LandedCostDocumentLinePickListExtension & LandedCostDocumentLinePickListPage
        >;
    }
}

declare module '@sage/xtrem-landed-cost/build/lib/pages/landed-cost-document-line-pick-list' {
    interface LandedCostDocumentLinePickList extends LandedCostDocumentLinePickListExtension {}
}
