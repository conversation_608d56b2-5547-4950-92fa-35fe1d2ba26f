import { RoundingMode, StringDataType, TextStreamDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export const supplierDocumentNumberDataType = new StringDataType({ maxLength: 20 });
export const purchasingAmountDataType = new xtremMasterData.dataTypes.PriceDataType({
    precision: 20,
    scale: 3,
    // TODO: We need to decide the appropriate rounding mode for amounts; previously was roundUp wich led amount like 124.992 to be rounded as 125
    roundingMode: RoundingMode.roundHalfUp,
});

export const purchasingPriceDataType = new xtremMasterData.dataTypes.PriceDataType({
    precision: 15,
    scale: 3,
    roundingMode: RoundingMode.roundHalfUp,
});

export const testTextStreamType = new TextStreamDataType({
    allowedContentTypes: ['text/xml', 'text/html', 'text/plain'],
});

export const propertyDataTypePurchasing = new StringDataType({ maxLength: 100 });
