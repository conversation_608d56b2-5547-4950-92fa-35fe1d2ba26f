import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';

/**
 * Purchase return delete controls :
 * - only if status is 'Draft' or 'Ready for processing'
 */
export async function controlDelete(
    document: xtremPurchasing.nodes.PurchaseReturn | xtremPurchasing.nodes.PurchaseRequisition,
    cx: ValidationContext,
) {
    const mayDeleteStatuses = ['draft', 'pending'];

    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_document__deletion_forbidden_reason_status',
            'The current purchase document cannot be deleted.',
        )
        .if(mayDeleteStatuses.includes(await document.status))
        .is.false();
}
