import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';
import { mayNotDeleteStatus } from './common';

export async function documentIsPosted(document: xtremPurchasing.nodes.BasePurchaseDocument, cx: ValidationContext) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_credit_memo__deletion_forbidden_reason_status',
            'A document with this status cannot be deleted: {{number}}, {{status}}',
            async () => ({
                number: await document.number,
                status: await document.status,
            }),
        )
        .if(mayNotDeleteStatus.includes(await document.status))
        .is.true();
}
