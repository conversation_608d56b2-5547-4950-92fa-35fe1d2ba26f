import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../..';

// Control deletion of a requisition line that has already been approved
export async function controlDeleteApproved(
    cx: ValidationContext,
    requisitionLine: xtremPurchasing.nodes.PurchaseRequisitionLine,
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_requisition_line_delete__line',
            'You cannot delete the purchase requisition line.',
        )
        .if(await requisitionLine.approvalStatus)
        .is.equal.to('approved');
}
