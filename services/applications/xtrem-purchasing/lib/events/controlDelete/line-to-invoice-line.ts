import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../..';
import { mayNotDeleteStatus } from './common';

/** Check if the invoice line is not in  mayNotDeleteStatus */
export async function checkInvoiceLineStatus(
    cx: ValidationContext,
    lineToInvoiceLine: xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine,
): Promise<void> {
    if (mayNotDeleteStatus.includes(await (await (await lineToInvoiceLine.purchaseInvoiceLine).document).status)) {
        cx.error.withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_receipt_purchase_invoice_line__deletion_forbidden_reason_status',
            'The purchase invoice line cannot be deleted. The invoice is already posted',
        );
    }
}
