import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremPurchasing from '../..';

// Control deletion of a purchase order line: there must be no linked invoices
export async function controlDeleteInvoiceLink(
    cx: ValidationContext,
    orderLine: xtremPurchasing.nodes.PurchaseOrderLine,
): Promise<void> {
    const messages = await xtremPurchasing.functions.PurchaseOrderLib.controlDeleteInvoiceLink(
        orderLine.$.context,
        orderLine,
    );
    messages.forEach(message => {
        cx.error.add(message);
    });
}
