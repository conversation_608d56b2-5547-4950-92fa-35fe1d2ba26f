import type { ValidationContext } from '@sage/xtrem-core';
import { ValidationSeverity } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';

export async function controlDelete(
    purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine,
    cx: ValidationContext,
) {
    if (['allocated', 'partiallyAllocated'].includes(await purchaseReturnLine.allocationStatus)) {
        cx.addDiagnose(
            ValidationSeverity.error,
            cx.localize(
                '@sage/xtrem-purchasing/nodes__purchase_return_line__allocated_exceeds_returned_quantity',
                'The allocated quantity must be equal or less than the returned stock quantity.',
            ),
        );
    }
}
