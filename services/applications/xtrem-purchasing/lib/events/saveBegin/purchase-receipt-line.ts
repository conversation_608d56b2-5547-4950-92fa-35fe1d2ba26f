import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremPurchasing from '../../index';

export async function saveBegin(purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine) {
    await purchaseReceiptLine.$.set({
        stockDetailStatus: await xtremStockData.functions.stockDetailLib.getStockDetailStatus({
            line: purchaseReceiptLine,
            quantityExpected: await purchaseReceiptLine.quantityInStockUnit,
            checkSerialNumbers: false,
        }),
    });
    await xtremPurchasing.functions.PurchaseReceiptLib.updateLineNotesOnCreation(purchaseReceiptLine);
}
