import * as xtremPurchasing from '../../index';

export async function saveBegin(purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine) {
    if (!(await (await purchaseReturnLine.item).isStockManaged) && (await purchaseReturnLine.status) === 'closed') {
        await purchaseReturnLine.$.set({ shippedStatus: 'shipped' });
    }
    await xtremPurchasing.functions.PurchaseReturnLib.updateLineNotesOnCreation(purchaseReturnLine);
}
