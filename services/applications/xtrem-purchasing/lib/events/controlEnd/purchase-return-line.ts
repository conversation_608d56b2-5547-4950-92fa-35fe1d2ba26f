import type { ValidationContext } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import type * as xtremPurchasing from '../../index';

export async function controlEnd(purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine, cx: ValidationContext) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_return_line__allocated_exceeds_returned_quantity',
            'The allocated quantity must be equal or less than the returned stock quantity.',
        )
        .if((await purchaseReturnLine.quantityAllocated) > (await purchaseReturnLine.quantityInStockUnit))
        .is.true();

    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_return_line__total_invoiced_exceeds_lines_returned_quantity',
            'The total of invoiced quantity must be equal or less than the returned stock quantity.',
        )
        .if(
            (await purchaseReturnLine.purchaseInvoiceLines.length) &&
                (await purchaseReturnLine.purchaseInvoiceLines.sum(line => line.invoicedQuantityInStockUnit)) >
                    (await purchaseReturnLine.quantityInStockUnit),
        )
        .is.true();

    const convertedQuantity = +new Decimal(
        (await purchaseReturnLine.quantity) * (await purchaseReturnLine.unitToStockUnitConversionFactor),
    ).toDecimalPlaces((await (await purchaseReturnLine.stockUnit)?.decimalDigits) ?? 0);

    const conversionFactors = {
        convertedQuantity,
        quantity: await purchaseReturnLine.quantity,
        unit: await (await purchaseReturnLine.unit)?.id,
        stockUnit: await (await purchaseReturnLine.stockUnit)?.id,
        quantityInStockUnit: (await purchaseReturnLine.quantityInStockUnit) ?? 0,
        conversionFactor: await purchaseReturnLine.unitToStockUnitConversionFactor,
    };

    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_return_line__bad_conversion_factor',
            `The quantity in stock unit ({{quantityInStockUnit}}) does not match the quantity converted from {{quantity}}
            {{unit}} to {{convertedQuantity}} {{stockUnit}} with a conversion factor of {{conversionFactor}}.`,
            () => conversionFactors,
        )
        .if(await purchaseReturnLine.quantityInStockUnit)
        .is.not.equal.to(convertedQuantity);
}
