import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { loggers } from '../../functions';
import type * as xtremPurchasing from '../../index';
import { sameCompanyForSiteAndStockSite } from '../controls/base-purchase-document';

async function checkStatusAfterBeingInProgress(
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
    cx: ValidationContext,
) {
    const oldStatus = await (await purchaseReturn.$.old).status;
    const currentStatus = await purchaseReturn.status;
    const oldApprovalStatus = await (await purchaseReturn.$.old).approvalStatus;
    const currentApprovalStatus = await purchaseReturn.approvalStatus;

    if (oldStatus === currentStatus && currentApprovalStatus === oldApprovalStatus) {
        return;
    }

    if (currentApprovalStatus === 'confirmed' && oldStatus === 'draft') {
        return;
    }

    const isApproved = currentStatus === 'inProgress' && oldStatus !== 'pending';
    const isSubmittedForApproval = oldStatus !== currentStatus && currentStatus === 'pending' && oldStatus !== 'draft';
    const isPendingApprovalStatusAndStatus =
        currentStatus === 'pending' && ['rejected', 'draft'].includes(currentApprovalStatus);
    const isClosed = currentStatus === 'closed';
    const isClosedWhileBeingApproved = isClosed && currentApprovalStatus === 'pendingApproval';
    const isRejectedAndNotClosed = currentStatus !== 'closed' && currentApprovalStatus === 'rejected';
    const isRejectedAndInProgress = oldStatus === 'inProgress' && currentApprovalStatus === 'rejected';

    loggers.return.debug(
        () => `isApproved: ${isApproved} ${currentStatus} ${oldStatus}
        OR isSubmittedForApproval: ${isSubmittedForApproval} ${currentStatus} ${oldStatus}
        OR isClosedWhileBeingApproved ${isClosedWhileBeingApproved} ${currentStatus} ${currentApprovalStatus} ${oldStatus}
        OR isRejectedAndNotClosed ${isRejectedAndNotClosed} ${currentStatus} ${currentApprovalStatus}
        OR isPendingApprovalStatusAndStatus ${isPendingApprovalStatusAndStatus} ${currentStatus} ${currentApprovalStatus}
        OR isRejectedAndInProgress ${isRejectedAndInProgress} ${oldStatus} ${currentApprovalStatus}
       `,
    );

    if (
        isApproved ||
        isSubmittedForApproval ||
        isClosedWhileBeingApproved ||
        isRejectedAndNotClosed ||
        isPendingApprovalStatusAndStatus ||
        isRejectedAndInProgress
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_return_status_inconsistent',
            'You cannot change the status of the purchase return to the status you selected.',
        );
    }
}

export async function controlEnd(purchaseReturn: xtremPurchasing.nodes.PurchaseReturn, cx: ValidationContext) {
    await xtremMasterData.events.control.baseDocumentControls.atLeastOneLine(cx, purchaseReturn);

    await sameCompanyForSiteAndStockSite(purchaseReturn, cx);

    if (purchaseReturn.$.status === NodeStatus.modified) {
        await checkStatusAfterBeingInProgress(purchaseReturn, cx);
    }
}
