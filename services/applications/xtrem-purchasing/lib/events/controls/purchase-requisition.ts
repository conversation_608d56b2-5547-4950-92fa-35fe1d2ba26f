import type { Context, ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremPurchasing from '../..';

/** check pendingApproval PurchaseRequisition for a site */
export async function checkPending(context: Context, cx: ValidationContext, siteId: number): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__site_extension__check_pending_purchase_requisitions',
            'Pending purchase requisitions need to be handled before disabling the approval process',
        )
        .if(
            await context.queryCount(xtremPurchasing.nodes.PurchaseRequisition, {
                filter: { site: siteId, approvalStatus: 'pendingApproval' },
            }),
        )
        .is.not.zero();
}

export async function checkApprovalStatusDraft(
    cx: ValidationContext,
    oldStatus: xtremPurchasing.enums.PurchaseDocumentStatus,
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_requisition__approval_status_change_forbidden',
            'The approval status can only be changed for "Draft" documents',
        )
        .if(oldStatus)
        .is.not.equal.to('draft');
}

export async function checkApprovalStatusPending(
    cx: ValidationContext,
    requisition: {
        isApprovalManaged: boolean;
        status: xtremPurchasing.enums.PurchaseDocumentStatus;
        approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus;
    },
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_requisition__cannot_update_incorrect_status_approval_workflow',
            'You cannot use this status if the approval workflow is disabled',
        )
        .if(
            !requisition.isApprovalManaged &&
                requisition.approvalStatus === 'pendingApproval' &&
                requisition.status !== 'closed',
        )
        .is.true();
}

export async function checkApprovalStatusConfirmed(
    cx: ValidationContext,
    requisition: {
        isApprovalManaged: boolean;
        status: xtremPurchasing.enums.PurchaseDocumentStatus;
        approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus;
    },
): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_requisition__cannot_update_incorrect_status_confirmation_workflow',
            'You cannot use this status if the approval workflow is enabled',
        )
        .if(
            requisition.isApprovalManaged &&
                requisition.approvalStatus === 'confirmed' &&
                requisition.status !== 'closed',
        )
        .is.true();
}

export async function checkApprovalStatus(
    cx: ValidationContext,
    purchaseRequisition: xtremPurchasing.nodes.PurchaseRequisition,
    approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
): Promise<void> {
    if (
        purchaseRequisition.$.status !== NodeStatus.modified ||
        approvalStatus === (await (await purchaseRequisition.$.old).approvalStatus)
    ) {
        return;
    }

    const oldStatus = await (await purchaseRequisition.$.old).status;
    const isApprovalManaged = await (await purchaseRequisition.site).isPurchaseRequisitionApprovalManaged;
    const status = await purchaseRequisition.status;

    xtremPurchasing.events.controls.checkApprovalStatusDraft(oldStatus, cx);
    await checkApprovalStatusPending(cx, { isApprovalManaged, approvalStatus, status });
    await checkApprovalStatusConfirmed(cx, { isApprovalManaged, approvalStatus, status });
}
