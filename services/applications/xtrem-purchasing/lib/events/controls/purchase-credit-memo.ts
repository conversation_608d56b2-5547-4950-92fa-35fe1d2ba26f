import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';

const postedInProgressError: xtremPurchasing.enums.PurchaseDocumentStatus[] = ['posted', 'inProgress', 'error'];

export async function addNewLinePostedStatus(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine,
    cx: ValidationContext,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_credit_memo_line__new_credit_memo_line_not_allowed',
            'You are not allowed to add new lines if credit memo is in a posted status.',
        )
        .if((await (await line.document).status) !== 'draft' && line.$.status === NodeStatus.added)
        .is.true();
}

export async function updateCreditMemoAlreadyPosted(
    line: xtremPurchasing.nodes.PurchaseCreditMemoLine,
    cx: ValidationContext,
) {
    if (
        line.$.status === NodeStatus.modified &&
        !(await line.forceUpdateForStock) &&
        postedInProgressError.includes(await (await line.document).status)
    ) {
        cx.error.withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_credit_memo__update_forbidden_credit_memo_posted',
            'You cannot update the credit memo. It is already posted.',
        );
    }
}
