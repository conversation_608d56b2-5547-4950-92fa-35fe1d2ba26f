import type { Context, Logger, ValidationContext } from '@sage/xtrem-core';
import { NodeStatus, date } from '@sage/xtrem-core';
import * as xtremTax from '@sage/xtrem-tax';
import { queryCountSupplierDocumentNumberBySupplier } from '../../functions/common';
import type * as xtremPurchasing from '../../index';

export async function documentIsPosted(document: xtremPurchasing.nodes.BasePurchaseDocument, cx: ValidationContext) {
    if (document.$.status === NodeStatus.modified) {
        if (
            (await document.forceUpdateForFinance) ||
            (await document.forceUpdateForStock) ||
            (await document.forceUpdateForResync)
        ) {
            return;
        }

        await cx.error
            .withMessage(
                '@sage/xtrem-purchasing/nodes__base_purchase_document__update_not_allowed_status_posted',
                'You cannot update a document that was posted: document {{ number}}.',
                async () => ({ number: await document.number }),
            )
            .if(['posted', 'inProgress', 'error'].includes(await (await document.$.old).status))
            .is.true();
    }
}

export function isNumberDeferred(document: xtremPurchasing.nodes.BasePurchaseDocument, logger: Logger) {
    if (document.$.status === NodeStatus.added) {
        if (!document.$.isValueDeferred('number')) {
            logger.warn(() =>
                document.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__base_purchase_document__id_already_exists',
                    'A sequence number cannot be allocated because the document number already exits: {{document}}',
                    () => ({ document: document.$.factory.getLocalizedTitle(document.$.context).toLowerCase() }),
                ),
            );
        }
    }
}

/** The site and the stock site need to have the same company. */
export async function sameCompanyForSiteAndStockSite(
    document: xtremPurchasing.nodes.BasePurchaseDocument,
    cx: ValidationContext,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document__same_company_site_stock_site',
            'The site and the stock site need to have the same company.',
        )
        .if((await (await document.site).legalCompany)._id)
        .is.not.equal.to((await (await document.stockSite).legalCompany)._id);
}

const mayDeleteStatuses: xtremPurchasing.enums.PurchaseDocumentStatus[] = ['draft', 'pending'];

export async function checkStatusDeletion(document: xtremPurchasing.nodes.BasePurchaseDocument, cx: ValidationContext) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document__deletion_forbidden_reason_status',
            'The current purchase document has the {{currentStatus}} status. You cannot delete it.',
            async () => {
                return { currentStatus: await document.status };
            },
        )
        .if(mayDeleteStatuses.includes(await document.status))
        .is.false();
}

export function checkApprovalStatusDraft(
    oldStatus: xtremPurchasing.enums.PurchaseDocumentStatus,
    cx: ValidationContext,
) {
    if (!['draft'].includes(oldStatus)) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__base_purchase_document__approval_status_change_forbidden',
            'You can only change the approval status for Draft documents.',
        );
    }
}

/** Checks if the document number already exists for the given supplier. */
export async function documentNumberExistsForSupplier(
    context: Context,
    cx: ValidationContext,
    document: xtremPurchasing.interfaces.DocumentForSupplierDocumentNumberValidation,
) {
    const { supplierDocumentNumber } = document;
    if (supplierDocumentNumber) {
        const supplierDocumentNumberCount = await queryCountSupplierDocumentNumberBySupplier(context, document);
        await cx.warn
            .withMessage(
                '@sage/xtrem-purchasing/nodes__base_purchase_document__supplier_document_number_already_exists',
                'Supplier document number already exists.',
            )
            .if(supplierDocumentNumberCount)
            .is.not.zero();
    }
}

/** Check if at least one of the given taxes has a wrong type. */
export async function checkWrongTaxType(cx: ValidationContext, document: xtremPurchasing.nodes.BasePurchaseDocument) {
    if (
        await xtremTax.functions.hasWrongTaxType({
            taxes: await document.taxes.toArray(),
            validTypes: ['purchasing', 'purchasingAndSales'],
        })
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__base_purchase_document__tax_type_validation',
            "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.",
        );
    }
}

export function dateValidation(
    document: xtremPurchasing.nodes.BasePurchaseDocument,
    documentDate: date,
    dateValue: date,
    cx: ValidationContext,
) {
    if (
        (document.$.status === NodeStatus.added ||
            (document.$.status === NodeStatus.modified && dateValue.compare(documentDate) !== 0)) &&
        dateValue.compare(date.today()) > 0
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_credit_memo__date',
            'The date cannot be later than the document date.',
        );
    }
}

export async function futureDateValidation(dateValue: date, cx: ValidationContext) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document__supplier_document_date',
            'The date cannot be later than the current date.',
        )
        .if(dateValue.compare(date.today()))
        .is.greater.than(0);
}
