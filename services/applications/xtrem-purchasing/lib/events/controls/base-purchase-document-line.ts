import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus, RoundingMode } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import { loggers } from '../../functions/loggers';
import type * as xtremPurchasing from '../../index';

/**  The line site and the header site need to be the same. */
export async function sameSiteForHeaderAndLine(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    await loggers.baseDocument.debugAsync(async () => `line.site: ${await (await line.site).id}`);
    await loggers.baseDocument.debugAsync(async () => `header.site: ${await (await (await line.document).site).id}`);
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line__same_site_header_and_line',
            'The line site and the header site need to be the same.',
        )
        .if((await line.site)._id)
        .is.not.equal.to((await (await line.document).site)._id);
}

/** The line site and the line stock site need to have the same company. */
export async function sameCompanyForSiteLineAndStockSiteLine(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line__same_company_site_stock_site',
            'The line site and the line stock site need to have the same company.',
        )
        .if((await (await line.site).legalCompany)._id)
        .is.not.equal.to((await (await line.stockSite).legalCompany)._id);
}

/** check if quantityInStockUnit is equal to quantity* */
export async function convertedQuantityInPurchaseUnitEqualquantityInStockUnit(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    /**  */
    const convertedQuantityInStockUnit = +new Decimal(
        (await line.quantity) * (await line.unitToStockUnitConversionFactor),
    ).toDecimalPlaces((await (await line.stockUnit)?.decimalDigits) || 0, RoundingMode.roundUp);
    if ((await line.quantityInStockUnit) !== convertedQuantityInStockUnit) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line__bad_conversion_factor',
            `The converted quantity: {{convertedQuantity}}, from quantity: {{quantity}} in unit: {{unit}}
             to quantity in stock unit: {{stockUnit}}, using the conversion factor: {{conversionFactor}},
             is different than the current quantity in stock unit: {{quantityInStockUnit}}.`,
            {
                convertedQuantity: convertedQuantityInStockUnit,
                quantity: await line.quantity,
                unit: await (await line.unit)?.id,
                stockUnit: await (await line.stockUnit)?.id,
                quantityInStockUnit: (await line.quantityInStockUnit) || 0,
                conversionFactor: await line.unitToStockUnitConversionFactor,
            },
        );
    }
}

/** The item must not be editable once the line has been created (so even before doc creation) */
export async function lineItemIsFrozen(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    if (line.$.status === NodeStatus.modified) {
        if ((await line.item)._id !== (await (await line.$.old).item)._id) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__base_purchase_document__line__item_modify',
                'The item: {{currentItem}}, cannot be edited.',
                async () => {
                    return {
                        currentItem: (await line.item)._id,
                    };
                },
            );
        }
    }
}

/** the receiving site belongs to the same legal company as the site on the header    */
export async function receivingSiteMustBelongToTheSameLegalCompany(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    const site = await line.site;
    const legalCompany = await (await (await line.document).site).legalCompany;
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document__reiving_site_legal_company',
            'The receiving site: {{site}}, does not belong to the same company as the header: {{company}}.',
            async () => {
                return {
                    site: await site.name,
                    company: await legalCompany.name,
                };
            },
        )
        .if((await site.legalCompany)._id)
        .is.not.equal.to(legalCompany._id);
}

/**  Item property controls that depends on stockSite, origin
 * and document's supplier (moved here from item control() event, to avoid circular dependencies) */
export async function itemMustHaveItemSiteAndMustBeStockManaged(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    const itemSite = await line.itemSite;
    await loggers.baseDocument.debugAsync(
        async () => `${itemSite?._id} ${await (await line.item).id} - ${await (await line.stockSite).id}`,
    );
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_site_record',
            'The item: {{currentItem}}, is not managed for the site: {{currentSite}}.',
            async () => {
                return {
                    currentItem: await (await line.item).id,
                    currentSite: await (await line.stockSite).id,
                };
            },
        )
        .if(!itemSite && !(await (await line.item).isStockManaged))
        .is.true();
}

/** The item: {{currentItem}}, is not managed for the site: {{currentSite}}.
 * /!\ This is the same message as the itemMustHaveItemSiteAndMustbeStockManaged  ??  */
export async function itemMustHaveItemSiteAndNotManagedWarning(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    const itemSite = await line.itemSite;
    await cx.warn
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_site_record',
            'The item: {{currentItem}}, is not managed for the site: {{currentSite}}.',
            async () => {
                return {
                    currentItem: await (await line.item).id,
                    currentSite: await (await line.stockSite).id,
                };
            },
        )
        .if(!itemSite && (await (await line.item).isStockManaged))
        .is.true();
}

/** The item: {{currentItem}}, is not managed for the supplier: {{currentSupplier}}. */
export async function itemMustHaveItemSupplier(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    const supplier = await (await line.document).businessRelation;
    const itemSupplier = await line.$.context.tryRead(xtremMasterData.nodes.ItemSupplier, {
        item: await line.item,
        supplier,
    });
    await cx.warn
        .withMessage(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_supplier_record',
            'The item: {{currentItem}}, is not managed for the supplier: {{currentSupplier}}.',
            async () => {
                return {
                    currentItem: await (await line.item).id,
                    currentSupplier: await (await supplier.businessEntity).id,
                };
            },
        )
        .if(!itemSupplier)
        .is.true();
}

/** The item:{{currentItem}}, is not managed for the site: {{currentSite}}, and supplier: {{currentSupplier}}. */
export async function itemMustBeManagedForSupplierAndSite(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    const supplier = await (await line.document).businessRelation;
    const itemSite = await line.$.context.tryRead(xtremMasterData.nodes.ItemSite, {
        item: await line.item,
        site: await line.stockSite,
    });

    const itemSupplier = await line.$.context.tryRead(xtremMasterData.nodes.ItemSupplier, {
        item: await line.item,
        supplier,
    });

    if (itemSupplier && itemSite) {
        const itemSiteSupplier = await line.$.context.tryRead(xtremMasterData.nodes.ItemSiteSupplier, {
            itemSite,
            supplier,
        });

        await cx.warn
            .withMessage(
                '@sage/xtrem-purchasing/nodes__base_purchase_document_line__no_item_supplier_site_record',
                'The item:{{currentItem}}, is not managed for the site: {{currentSite}}, and supplier: {{currentSupplier}}.',
                async () => {
                    return {
                        currentItem: await (await line.item).id,
                        currentSite: await (await line.stockSite).id,
                        currentSupplier: await (await supplier.businessEntity).id,
                    };
                },
            )
            .if(!itemSiteSupplier)
            .is.true();
    }
}

const mayNotDeleteStatus: xtremPurchasing.enums.PurchaseDocumentStatus[] = ['posted', 'inProgress', 'error'];

export async function checkLineStatusDeletion(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    const status = await (await line.document).status;
    if (mayNotDeleteStatus.includes(status)) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line_deletion_forbidden_reason_status',
            'The line cannot be deleted. The document status is: {{currentStatus}}.',
            { currentStatus: status },
        );
    }
}

export async function itemUpdateValidation(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    documentLineType: xtremPurchasing.interfaces.BaseDocumentLine,
    item: xtremMasterData.nodes.Item,
    cx: ValidationContext,
) {
    if (
        item &&
        ((await line.origin) === 'purchaseReturn' ||
            (await line.origin) === 'purchaseInvoice' ||
            (await line.origin) === 'purchaseReceipt') &&
        documentLineType &&
        (await documentLineType?.item)?._id !== item._id
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_document_line__item_must_be_same',
            'The item cannot be changed.',
        );
    }
}

export async function purchaseUnitUpdateValidation(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    documentLineType: xtremPurchasing.interfaces.BaseLineToLine,
    unit: xtremMasterData.nodes.UnitOfMeasure,
    cx: ValidationContext,
) {
    if (
        unit &&
        ((await line.origin) === 'purchaseReturn' ||
            (await line.origin) === 'purchaseInvoice' ||
            (await line.origin) === 'purchaseReceipt') &&
        documentLineType &&
        (await documentLineType?.unit)?._id !== unit._id
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_document_line__purchase_unit_must_be_same',
            'The purchase unit cannot be changed.',
        );
    }
}

export async function unitToStockUnitConversionFactorValidation(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    unit: xtremMasterData.nodes.UnitOfMeasure,
    cx: ValidationContext,
) {
    if (
        unit &&
        (await line.stockUnit) &&
        (await (await line.stockUnit).id) !== (await unit.id) &&
        (await xtremMasterData.functions.listOfPossibilities(unit)).length === 0
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_document_line__no_stock_unit_conversion_coefficient',
            'The current purchase unit has no conversion factor for the item stock unit.',
        );
    }
}

export async function unitToPreviousUnitConversionFactorValidation(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    unit: xtremMasterData.nodes.UnitOfMeasure,
    cx: ValidationContext,
) {
    if (
        unit &&
        line.$.status === NodeStatus.modified &&
        (await (await line.$.old).unit)._id !== unit._id &&
        !(await xtremMasterData.functions.listOfPossibilities(unit)).length
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_document_line__no_purchase_unit_conversion_coefficient',
            'The current purchase unit has no conversion factor for the previous purchase unit of the line.',
        );
    }
}

export async function recipientSiteValidation(recipientSite: xtremSystem.nodes.Site, cx: ValidationContext) {
    if (recipientSite && !((await recipientSite.isInventory) || (await recipientSite.isPurchase))) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_document_line__is_inventory_is_purchasing',
            'The receiving site needs to be either stock or purchasing.',
        );
    }
}

export async function matchingSiteValidation(
    line: xtremPurchasing.nodes.BasePurchaseDocumentLine | xtremPurchasing.nodes.PurchaseReceiptLine,
    recipientSite: xtremSystem.nodes.Site,
    receiptLine: xtremPurchasing.nodes.PurchaseReceiptLineToPurchaseInvoiceLine | null,
    cx: ValidationContext,
) {
    if (
        recipientSite &&
        (await line.origin) === 'purchaseReceipt' &&
        receiptLine &&
        recipientSite !== (await (await (await receiptLine.purchaseReceiptLine).document).stockSite)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_document_line__receipt_site_must_be_same_as_receipt',
            'The receiving site needs to be the same as the receipt site linked to invoice line.',
        );
    }
}

export async function landedCostItemValidation(
    item: xtremMasterData.nodes.Item | null,
    documentName: string,
    cx: ValidationContext,
) {
    if (item && (await item.type) === 'landedCost') {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line_item_may_not_have_type_landed_cost',
            'A landed cost item cannot be added to the document: {{documentName}}.',
            { documentName },
        );
    }
}

export async function approvalStatusValidation(
    line: xtremPurchasing.nodes.PurchaseRequisitionLine | xtremPurchasing.nodes.PurchaseOrderLine,
    currentStatus: string,
    cx: ValidationContext,
) {
    if (
        line.$.status === NodeStatus.modified &&
        currentStatus !== (await (await (await line.$.old).document).approvalStatus) &&
        !['draft'].includes(await (await line.$.old).lineStatus)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__base_purchase_document_line__approval_status_change_forbidden',
            'You can only change the approval status of Draft lines.',
        );
    }
}
