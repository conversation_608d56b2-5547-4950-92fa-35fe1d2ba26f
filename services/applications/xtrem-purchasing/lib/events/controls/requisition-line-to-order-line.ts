import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../..';
import { logNaturalKeyOfLine, loggers } from '../../functions';

/** Check if requisitionLine & orderLine have the same item / unit / supplier / currency  */

export async function samesProperties(
    cx: ValidationContext,
    requisitionLineToOrderLine: xtremPurchasing.nodes.PurchaseRequisitionLineToPurchaseOrderLine,
) {
    const { context } = requisitionLineToOrderLine.$;
    const differentProperties: string[] = [];
    const requisitionLine = await requisitionLineToOrderLine.purchaseRequisitionLine;
    const orderLine = await requisitionLineToOrderLine.purchaseOrderLine;

    if ((await orderLine.item)._id !== (await requisitionLine.item)?._id) {
        differentProperties.push(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_item',
                'item',
            ),
        );
        await loggers.same.debugAsync(
            async () => `Different item between requisition
        ${await (await requisitionLine.item).id} and order ${await (await orderLine.item).id}`,
        );
    }
    const requisitionLinePurchaseUnit = await requisitionLine.unit;
    if (requisitionLinePurchaseUnit && (await orderLine.unit)._id !== requisitionLinePurchaseUnit._id) {
        differentProperties.push(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_purchase_unit',
                'purchase unit',
            ),
        );
        await loggers.same.debugAsync(
            async () => `Different unit between requisition ${await (await requisitionLine.unit).id}
        and order ${await (await orderLine.unit).id}`,
        );
    }
    const requisitionLineSupplier = await requisitionLine.supplier;
    const orderLineDocument = await orderLine.document;
    if (requisitionLineSupplier && (await orderLineDocument.businessRelation)._id !== requisitionLineSupplier._id) {
        differentProperties.push(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_supplier',
                'supplier',
            ),
        );
        await loggers.same.debugAsync(
            async () => `Different supplier between requisition ${await (await requisitionLine.supplier)?.id}
            and order ${await (await orderLineDocument.supplier).id}`,
        );
    }
    const requisitionLineCurrency = await requisitionLine.currency;
    if (requisitionLineCurrency && (await orderLineDocument.currency)._id !== requisitionLineCurrency._id) {
        differentProperties.push(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_currency',
                'currency',
            ),
        );
    }
    if ((await orderLine.unitToStockUnitConversionFactor) !== (await requisitionLine.unitToStockUnitConversionFactor)) {
        differentProperties.push(
            context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__differentProperties_unit_conversion_factor',
                'unit conversion factor',
            ),
        );
    }
    if (differentProperties.length) {
        await logNaturalKeyOfLine(loggers.same, requisitionLine);
        await logNaturalKeyOfLine(loggers.same, orderLine);
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_requisition_to_purchase_order__same_properties_order_to_receipt',
            'The purchase order needs to have the same values for the following properties: {{differentProperties}}',
            { differentProperties: differentProperties.join(' ') },
        );
    }
}
