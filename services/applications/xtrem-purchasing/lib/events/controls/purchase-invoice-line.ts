import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';

export async function checkLineStatus(cx: ValidationContext, line: xtremPurchasing.nodes.PurchaseInvoiceLine) {
    if (line.$.status === NodeStatus.added) {
        if ((await (await line.document).status) !== 'draft') {
            cx.error.withMessage(
                '@sage/xtrem-purchasing/nodes_new_invoice_line_not_allowed',
                'You are not allowed to add new lines if invoice is in a posted status.',
            );
        }
    }
}
