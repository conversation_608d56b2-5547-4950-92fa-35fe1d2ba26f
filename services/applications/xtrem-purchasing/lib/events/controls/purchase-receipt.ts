import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../../index';

/** The total of returned quantity must be equal or less than the received stock quantity. */
export async function returnedQuantityMustBeEqualOrLessThanTheReceivedStockQuantity(
    line: xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    if (
        (await line.purchaseReturnLines.length) &&
        (await line.purchaseReturnLines.reduce(
            async (qty, returnLine) => qty + (await returnLine.returnedQuantityInStockUnit),
            0,
        )) > (await line.quantityInStockUnit)
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_receipt_line__total_returned_exceeds_lines_received_quantity',
            'The total of returned quantity must be equal or less than the received stock quantity.',
        );
    }
}

/** The total invoiced quantity is greater than the received stock quantity. */
export async function invoicedQuantityIsBiggerThanReceivedQuantity(
    line: xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    if (
        (await line.purchaseInvoiceLines.length) &&
        (await line.purchaseInvoiceLines.reduce(
            async (qty, InvoiceLine) => qty + (await InvoiceLine.invoicedQuantityInStockUnit),
            0,
        )) > (await line.quantityInStockUnit)
    ) {
        cx.warn.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_receipt_line__total_invoiced_exceeds_lines_received_quantity',
            'The total invoiced quantity is greater than the received stock quantity.',
        );
    }
}

export async function isPurchaseReceiptLineIncomplete(
    purchaseReceiptLine: xtremPurchasing.nodes.PurchaseReceiptLine,
    cx: ValidationContext,
) {
    await returnedQuantityMustBeEqualOrLessThanTheReceivedStockQuantity(purchaseReceiptLine, cx);

    await invoicedQuantityIsBiggerThanReceivedQuantity(purchaseReceiptLine, cx);

    const { purchaseReceiptLines } = (await (await purchaseReceiptLine.purchaseOrderLine)?.purchaseOrderLine) ?? {};

    if (purchaseReceiptLine.$.status === NodeStatus.added) {
        if (purchaseReceiptLines) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_completed',
                    'A purchase receipt line of the current purchase order line is completed. You cannot add additional receipt lines to this purchase order line.',
                )
                .if(await purchaseReceiptLines.some(async line => (await line.purchaseReceiptLine).completed))
                .is.true();

            await cx.error
                .withMessage(
                    '@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_draft',
                    'You need to post all receipt lines before you can add a complete receipt line.',
                )
                .if(
                    (await purchaseReceiptLines.some(
                        async line => (await (await line.purchaseReceiptLine).status) === 'draft',
                    )) && (await purchaseReceiptLine.completed),
                )
                .is.true();
        }
    }

    if (
        purchaseReceiptLine.$.status === NodeStatus.modified &&
        !(await (
            await purchaseReceiptLine.$.old
        ).completed) &&
        (await purchaseReceiptLine.completed)
    ) {
        if (purchaseReceiptLines) {
            await cx.error
                .withMessage(
                    '@sage/xtrem-purchasing/nodes__purchase_order_to_purchase_receipt__receipt_line_convert_draft',
                    'You need to post all receipt lines before you can define this line as complete.',
                )
                .if(
                    await purchaseReceiptLines.some(
                        async line =>
                            (await line.purchaseReceiptLine)._id !== purchaseReceiptLine._id &&
                            (await (await line.purchaseReceiptLine).status) === 'draft',
                    ),
                )
                .is.true();
        }
    }
}
