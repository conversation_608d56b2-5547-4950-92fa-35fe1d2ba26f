import type { date, ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremPurchasing from '../..';

export async function receivedQuantityGreaterThanTheOrderLineQuantity(
    line: xtremPurchasing.nodes.PurchaseOrderLine,
    cx: ValidationContext,
) {
    if (
        (await line.purchaseReceiptLines.length) &&
        (await line.purchaseReceiptLines.sum(receiptLines => receiptLines.receivedQuantityInStockUnit)) >
            (await line.quantityInStockUnit)
    ) {
        cx.warn.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_order_line__total_quantity_from_orders_equal_line_quantity',
            'The total received quantity is greater then the order line quantity.',
        );
    }
}

export async function receivingSiteValidation(line: xtremPurchasing.nodes.PurchaseOrderLine, cx: ValidationContext) {
    /**
     * When creating the item site must exist for the line item and the line receiving site
     */
    if (line.$.status === NodeStatus.added && (await (await line.item).isStockManaged)) {
        if (!(await line.itemSite)) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__base_purchase_document__line__no__receiving_site',
                'No {{siteId}} {{site}} receiving site for the {{item}} item site.',
                {
                    item: await (await line.item).name,
                    siteName: await (await line.stockSite).name,
                    siteId: await (await line.stockSite).id,
                },
            );
        }
    }
}

export async function updateLineStatus(line: xtremPurchasing.nodes.PurchaseOrderLine) {
    if ((await (await line.document).approvalStatus) === 'approved') {
        switch (await line.lineReceiptStatus) {
            case 'notReceived':
                return 'pending';
            case 'partiallyReceived':
                return 'inProgress';
            // The case 'received' returns 'closed' mustn't be managed here
            // It's managed by the node PurchaseOrderLineToPurchaseReceiptLine which should be the only to change the lineReceiptStatus
            default:
                return line.lineStatus;
        }
    }
    return line.lineStatus;
}

export async function expectedReceiptDateValidation(
    line: xtremPurchasing.nodes.PurchaseOrderLine,
    dateValue: date,
    cx: ValidationContext,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/nodes__purchase_order_line__expected_receipt_date_to_order_date',
            'The expected receipt date must be later than the order date.',
        )
        .if(dateValue.compare(await (await line.document).orderDate))
        .is.negative();
}
