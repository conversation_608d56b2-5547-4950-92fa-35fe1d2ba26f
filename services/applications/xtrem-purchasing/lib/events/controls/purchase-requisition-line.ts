import type { date, ValidationContext } from '@sage/xtrem-core';
import { NodeStatus, RoundingMode } from '@sage/xtrem-core';
import { Decimal } from '@sage/xtrem-decimal';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStructure from '@sage/xtrem-structure';
import type * as xtremPurchasing from '../..';
import { loggers } from '../../functions';

const allowedModifyFields = [
    'lineStatus', // old field status name ( keep because of the getValue )
    'status',
    'lineOrderStatus',
    'approvalStatus',
    'supplier',
    'itemSupplier',
    'itemSiteSupplier',
    'grossPrice',
    'priceOrigin',
    'discountCharges',
    'netPrice',
    'totalTaxExcludedAmount',
    'computedAttributes',
    'currency',
    'needByDate',
    // because when we change the supplier we change the itemSupplier so StockUnit can change
    'unitToStockUnitConversionFactor',
    'quantityInStockUnit',
    'quantityToOrderInStockUnit',
    'internalNote',
];

/** Purchase requisition approved: Don't allow editing, adding or deleting line anymore  */
export async function checkApprovedStatus(
    cx: ValidationContext,
    line: xtremPurchasing.nodes.PurchaseRequisitionLine,
): Promise<void> {
    const approvalStatus = await (await line.document).approvalStatus;
    if (approvalStatus !== 'approved') {
        return;
    }
    const changedFields =
        line.$.status === NodeStatus.added
            ? ['']
            : xtremStructure.functions.getNodeDiff(await line.$.payload(), await (await line.$.old).$.payload());

    const disallowedFields = changedFields.filter(field => !allowedModifyFields.includes(field));

    if (disallowedFields.length === 0) {
        return;
    }

    loggers.requisitionLine.error(() => `changed fields : ${changedFields.join(', ')}`);

    cx.path.push(...disallowedFields);
    cx.error.addLocalized(
        '@sage/xtrem-purchasing/nodes__purchase_requisition__approved_status__line_control',
        'The purchase requisition is approved. You cannot add or delete a line. You can only edit certain fields.',
    );
}

/** The total ordered quantity is greater then the requisition line quantity. */
export async function orderedQuantityGreaterThanTheRequisitionLineQuantity(
    line: xtremPurchasing.nodes.PurchaseRequisitionLine,
    cx: ValidationContext,
) {
    if (
        (await line.purchaseOrderLines.length) &&
        (await line.purchaseOrderLines.reduce(
            async (qty, orderLine) => qty + (await orderLine.orderedQuantityInStockUnit),
            0,
        )) > (await line.quantityInStockUnit)
    ) {
        cx.warn.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_requisition_line__total_quantity_from_orders_equal_line_quantity',
            'The total ordered quantity is greater then the requisition line quantity.',
        );
    }
}

/** The converted quantity {{convertedQuantity}} from {{quantity}} in {{unit}} to quantity in {{stockUnit}}
 * using the conversion factor {{conversionFactor}} is different than the current quantity in stock unit {{quantityInStockUnit}}. */
export async function convertedQuantityInPurchaseUnitDifferentThanQuantityInStockUnit(
    line: xtremPurchasing.nodes.PurchaseRequisitionLine,
    cx: ValidationContext,
) {
    if ((await line.item) && ((await line.quantityInStockUnit) ?? 0)) {
        const convertedQuantity = +new Decimal(
            (await line.quantity) * (await line.unitToStockUnitConversionFactor),
        ).toDecimalPlaces((await (await line.stockUnit)?.decimalDigits) || 0, RoundingMode.roundUp);
        if ((await line.quantityInStockUnit) !== convertedQuantity) {
            cx.error.addLocalized(
                '@sage/xtrem-purchasing/nodes__purchase_requisition_line__bad_conversion_factor',
                `The converted quantity {{convertedQuantity}} from {{quantity}} in {{unit}} to quantity in {{stockUnit}} using the conversion factor {{conversionFactor}} is different than the current quantity in stock unit {{quantityInStockUnit}}.`,
                {
                    convertedQuantity,
                    quantity: await line.quantity,
                    unit: await (await line.unit)?.id,
                    stockUnit: await (await line.stockUnit)?.id,
                    quantityInStockUnit: (await line.quantityInStockUnit) || 0,
                    conversionFactor: await line.unitToStockUnitConversionFactor,
                },
            );
        }
    }
}

export async function itemDescriptionValidation(
    line: xtremPurchasing.nodes.PurchaseRequisitionLine,
    itemDescription: string,
    cx: ValidationContext,
) {
    if (!(await line.item) && !itemDescription) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_requisition_line__requested_item_description_mandatory',
            'Requested item description is mandatory if no item provided!',
        );
    }
}

export async function needByDateValidation(
    line: xtremPurchasing.nodes.PurchaseRequisitionLine,
    dateValue: date,
    cx: ValidationContext,
) {
    if (
        (line.$.status === NodeStatus.added ||
            (line.$.status === NodeStatus.modified && dateValue.compare(await (await line.$.old).needByDate) !== 0)) &&
        dateValue.compare(await (await line.document).requestDate) < 0
    ) {
        cx.warn.add(
            line.$.context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_requisition_line__need_by_date_inferior_to_request_date',
                'The need by date should not be before the requisition request date',
            ),
        );
    }
}

export async function grossPriceCurrencyValidation(
    line: xtremPurchasing.nodes.PurchaseRequisitionLine,
    currency: xtremMasterData.nodes.Currency | null,
    cx: ValidationContext,
) {
    if ((await line.grossPrice) > 0 && !currency) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_requisition_line__gross_price_currency_mandatory',
            'The gross price currency is mandatory.',
        );
    }
}

export async function purchaseUnitValidation(
    line: xtremPurchasing.nodes.PurchaseRequisitionLine,
    purchaseUnit: xtremMasterData.nodes.UnitOfMeasure | null,
    cx: ValidationContext,
) {
    if (!purchaseUnit && !(await line.item)) {
        cx.error.addLocalized(
            '@sage/xtrem-purchasing/nodes__purchase_requisition_line__purchase_unit_mandatory_if_no_item',
            'The purchase unit is mandatory if no item is provided.',
        );
    }
}
