import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremPurchasing from '../../index';

export async function checkApprovalStatus(
    val: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
    cx: ValidationContext,
) {
    const isPurchaseOrderApprovalManaged = await (await purchaseOrder.site).isPurchaseOrderApprovalManaged;
    const status = await purchaseOrder.status;

    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_approval_workflow',
            'You cannot use this status if the approval workflow is disabled.',
        )
        .if(val === 'pendingApproval' && !isPurchaseOrderApprovalManaged && status !== 'closed')
        .is.true();

    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_confirmed_workflow',
            'You cannot use this status if the approval workflow is enabled.',
        )
        .if(val === 'confirmed' && isPurchaseOrderApprovalManaged && status !== 'closed')
        .is.true();
}

export async function checkAlreadyExistingOrderReferenceForGivenSupplier(
    purchaseOrder: xtremPurchasing.nodes.PurchaseOrder,
    cx: ValidationContext,
) {
    if (await purchaseOrder.supplierOrderReference) {
        const supplierOrderReference = await purchaseOrder.$.context.queryCount(xtremPurchasing.nodes.PurchaseOrder, {
            filter: {
                businessRelation: await purchaseOrder.businessRelation,
                supplierOrderReference: await purchaseOrder.supplierOrderReference,
            },
        });
        if (supplierOrderReference > 0) {
            cx.warn.add(
                purchaseOrder.$.context.localize(
                    '@sage/xtrem-purchasing/nodes__purchase_order__already_exist_order_with_same_supplier_order_reference',
                    'This order reference already exists for this supplier.',
                ),
            );
        }
    }
}
