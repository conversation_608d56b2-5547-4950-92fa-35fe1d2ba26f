import type { ValidationContext } from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremPurchasing from '../../index';

export async function controlStatus(
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
    cx: ValidationContext,
    newStatus: xtremPurchasing.enums.PurchaseDocumentStatus,
) {
    if (purchaseReturn.$.status === NodeStatus.added) return;
    if ((await (await purchaseReturn.$.old).status) !== 'draft') {
        await cx.error
            .withMessage(
                '@sage/xtrem-purchasing/nodes__purchase_return_status_draft',
                'Cannot revert purchase return status back to draft.',
            )
            .if(newStatus)
            .is.equal.to('draft');
    }
}

export async function modifyCreatedDocument(returnDoc: xtremPurchasing.nodes.PurchaseReturn) {
    if (
        returnDoc.$.status === NodeStatus.added ||
        (returnDoc.$.status === NodeStatus.modified &&
            !['closed', 'posted'].includes(await (await returnDoc.$.old).status))
    ) {
        const postResult = await xtremPurchasing.nodes.PurchaseReturn.financeIntegrationCheck(
            returnDoc.$.context,
            returnDoc,
        );

        if (postResult.message.length) {
            throw new BusinessRuleError(postResult.message);
        }
    }

    await xtremMasterData.functions.controlDocumentNumber(returnDoc);
    await xtremPurchasing.functions.PurchaseReturnLib.updateHeaderNotesOnCreation(returnDoc);
}

export async function checkApprovalStatus(
    approvalStatus: xtremPurchasing.enums.PurchaseDocumentApprovalStatus,
    purchaseReturn: xtremPurchasing.nodes.PurchaseReturn,
    cx: ValidationContext,
) {
    const isPurchaseReturnApprovalManaged = await (await purchaseReturn.site).isPurchaseReturnApprovalManaged;
    const status = await purchaseReturn.status;

    if (status === 'closed') {
        return;
    }
    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_approval_workflow',
            'You cannot use this status if the approval workflow is disabled.',
        )
        .if(approvalStatus === 'pendingApproval' && !isPurchaseReturnApprovalManaged)
        .is.true();

    await cx.error
        .withMessage(
            '@sage/xtrem-purchasing/node_purchase_order_cannot_update_incorrect_status_confirmed_workflow',
            'You cannot use this status if the approval workflow is enabled.',
        )
        .if(approvalStatus === 'confirmed' && isPurchaseReturnApprovalManaged)
        .is.true();
}
