import { loggers } from '../../functions';
import type * as xtremPurchasing from '../../index';

const mayDeleteStatuses: xtremPurchasing.enums.PurchaseDocumentStatus[] = ['draft', 'pending'];

export async function deleteBegin(purchaseReturn: xtremPurchasing.nodes.PurchaseReturn) {
    if (!mayDeleteStatuses.includes(await purchaseReturn.status)) {
        loggers.return.error(() =>
            purchaseReturn.$.context.localize(
                '@sage/xtrem-purchasing/nodes__purchase_return__deletion_forbidden_reason_status',
                'The current purchase return cannot be deleted.',
            ),
        );
    }
}
