import type { Reference } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremPurchasing from '../../index';

export async function saveEnd(purchaseReturnLine: xtremPurchasing.nodes.PurchaseReturnLine) {
    if (
        purchaseReturnLine.$.status === NodeStatus.modified &&
        (await (await purchaseReturnLine.$.old).status) !== (await purchaseReturnLine.status) &&
        (await purchaseReturnLine.status) === 'closed' &&
        (await purchaseReturnLine.quantityAllocated) > 0
    ) {
        const allocationsToBeDeleted: xtremStockData.interfaces.DataForUpdateAllocationActions[] =
            await purchaseReturnLine.stockAllocations
                .map(allocation => {
                    return {
                        action: 'delete',
                        allocationRecord: allocation,
                    } as unknown as xtremStockData.interfaces.DataForUpdateAllocationActions;
                })
                .toArray();

        await xtremStockData.nodes.Stock.updateAllocations(purchaseReturnLine.$.context, {
            documentLine:
                purchaseReturnLine as unknown as Reference<xtremStockData.interfaces.DocumentLineWithStockAllocation>,
            allocationUpdates: [...allocationsToBeDeleted],
        });
    }
}
