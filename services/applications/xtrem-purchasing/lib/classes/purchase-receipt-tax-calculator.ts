import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremPurchasing from '../index';
import { PurchasingTaxCalculator } from './purchasing-tax-calculator';

export class PurchaseReceiptTaxCalculator extends PurchasingTaxCalculator {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars, class-methods-use-this, require-await
    override async shouldOverwriteTax(lineInstance: xtremPurchasing.nodes.PurchaseReceiptLine): Promise<boolean> {
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    override async getTaxDeterminationDocumentLineParameters(
        lineInstance: xtremPurchasing.nodes.PurchaseReceiptLine,
    ): Promise<xtremTax.interfaces.TaxDeterminationDocumentLineParameters> {
        const lineInstanceDocument = await lineInstance.document;
        return {
            flow: 'purchasing',
            providerAddress: (await lineInstanceDocument.supplierAddress) as xtremMasterData.nodes.Address,
            consumerAddress: (await lineInstanceDocument.receivingAddress) as xtremMasterData.nodes.Address,
            item: await lineInstance.item,
            site: await lineInstanceDocument.stockSite,
            taxDate: await lineInstance.taxDate,
            legalEntity:
                (await (await (await lineInstanceDocument.billBySupplier)?.businessEntity)?.legalEntity) ??
                (await (
                    await (
                        await lineInstanceDocument.businessRelation
                    ).businessEntity
                ).legalEntity),
            taxSolution: await (await (await (await lineInstance.site).legalCompany).country).taxSolution,
        };
    }
}
