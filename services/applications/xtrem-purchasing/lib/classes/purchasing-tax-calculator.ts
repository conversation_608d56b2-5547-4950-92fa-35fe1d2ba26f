import type { Context } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremPurchasing from '../index';

export class PurchasingTaxCalculator extends xtremTax.classes.BaseTaxCalculator {
    // eslint-disable-next-line class-methods-use-this
    async shouldOverwriteTax(
        lineInstance:
            | xtremPurchasing.nodes.PurchaseInvoiceLine
            | xtremPurchasing.nodes.PurchaseReceiptLine
            | xtremPurchasing.nodes.PurchaseOrderLine,
    ): Promise<boolean> {
        if (lineInstance instanceof xtremPurchasing.nodes.PurchaseInvoiceLine) {
            return (await lineInstance.consumptionLinkedAddress)?.$.status === NodeStatus.modified;
        }
        if (lineInstance instanceof xtremPurchasing.nodes.PurchaseOrderLine) {
            return (await lineInstance.stockSiteAddress)?.$.status === NodeStatus.modified;
        }
        return false;
    }

    // eslint-disable-next-line class-methods-use-this
    async updatedValues(
        lineInstance:
            | xtremPurchasing.nodes.PurchaseInvoiceLine
            | xtremPurchasing.nodes.PurchaseCreditMemoLine
            | xtremPurchasing.nodes.PurchaseReceiptLine
            | xtremPurchasing.nodes.PurchaseOrderLine,
    ): Promise<void> {
        if ((await lineInstance.currency) && (await lineInstance.grossPrice) >= 0) {
            const quantity = await lineInstance.quantity;
            const company = await (await (await lineInstance.document).site).legalCompany;
            await lineInstance.$.set({
                netPrice: await xtremMasterData.functions.calculateNetPrice(
                    lineInstance.discountCharges,
                    await lineInstance.grossPrice,
                    quantity,
                    await xtremMasterData.functions.getCompanyPriceScale(company),
                ),
            });
            await lineInstance.$.set({ amountExcludingTax: quantity * (await lineInstance.netPrice) });
        }
    }

    // eslint-disable-next-line class-methods-use-this
    async getTaxDeterminationDocumentLineParameters(
        lineInstance:
            | xtremPurchasing.nodes.PurchaseInvoiceLine
            | xtremPurchasing.nodes.PurchaseCreditMemoLine
            | xtremPurchasing.nodes.PurchaseOrderLine
            | xtremPurchasing.nodes.PurchaseReceiptLine,
    ): Promise<xtremTax.interfaces.TaxDeterminationDocumentLineParameters> {
        if (lineInstance instanceof xtremPurchasing.nodes.PurchaseOrderLine) {
            const lineInstanceDocument = await lineInstance.document;
            return {
                flow: 'purchasing',
                providerAddress: (await lineInstanceDocument.supplierAddress) as xtremMasterData.nodes.Address,
                consumerAddress: (await lineInstance.stockSiteAddress) as xtremMasterData.nodes.Address,
                item: await lineInstance.item,
                site: await lineInstance.stockSite,
                taxDate: await lineInstance.taxDate,
                legalEntity:
                    (await (await (await lineInstanceDocument.billBySupplier)?.businessEntity)?.legalEntity) ??
                    (await (
                        await (
                            await lineInstanceDocument.businessRelation
                        ).businessEntity
                    ).legalEntity),
                taxSolution: await (await (await (await lineInstance.site).legalCompany).country).taxSolution,
            };
        }
        if (lineInstance instanceof xtremPurchasing.nodes.PurchaseReceiptLine) {
            const lineInstanceDocument = await lineInstance.document;
            return {
                flow: 'purchasing',
                providerAddress: (await lineInstanceDocument.supplierAddress) as xtremMasterData.nodes.Address,
                consumerAddress: (await lineInstanceDocument.receivingAddress) as xtremMasterData.nodes.Address,
                item: await lineInstance.item,
                site: await lineInstanceDocument.stockSite,
                taxDate: await lineInstance.taxDate,
                legalEntity:
                    (await (await (await lineInstanceDocument.billBySupplier)?.businessEntity)?.legalEntity) ??
                    (await (
                        await (
                            await lineInstanceDocument.businessRelation
                        ).businessEntity
                    ).legalEntity),
                taxSolution: await (await (await (await lineInstance.site).legalCompany).country).taxSolution,
            };
        }
        return {
            flow: 'purchasing',
            providerAddress: (await lineInstance.providerLinkedAddress) as xtremMasterData.nodes.Address,
            consumerAddress: (await lineInstance.consumptionLinkedAddress) as xtremMasterData.nodes.Address,
            item: await lineInstance.item,
            site: await lineInstance.recipientSite,
            taxDate: await lineInstance.taxDate,
            legalEntity: await (await (await (await lineInstance.document).billBySupplier).businessEntity).legalEntity,
            taxSolution: await (await (await (await lineInstance.site).legalCompany).country).taxSolution,
        };
    }

    includeAllTotals = false;

    override async calculateTaxData(
        headerInstance: xtremTax.interfaces.Document,
    ): Promise<xtremTax.interfaces.Document> {
        if (!this.isGenericTaxCalculationEnabled) {
            await headerInstance.$.set({ taxCalculationStatus: 'done' });
            return headerInstance;
        }
        return super.calculateTaxData(headerInstance);
    }

    protected constructor(public classContext: Context) {
        super();
    }

    protected async init(
        site: xtremSystem.nodes.Site,
        status:
            | xtremPurchasing.enums.PurchaseInvoiceStatus
            | xtremPurchasing.enums.PurchaseCreditMemoStatus
            | xtremPurchasing.enums.PurchaseDocumentStatus = 'draft',
        includeAllTotals = false,
        forceUpdateForFinance: boolean = false,
    ): Promise<this> {
        this.isGenericTaxCalculationEnabled =
            (await (await site.legalCompany).taxEngine) === 'genericTaxCalculation' ||
            (await (await (await site.legalCompany).legislation).id) === 'US';
        this.canEditTaxes = status === 'draft' || forceUpdateForFinance;
        this.includeAllTotals = includeAllTotals;
        return this;
    }

    static create(
        classContext: Context,
        site: xtremSystem.nodes.Site,
        status:
            | xtremPurchasing.enums.PurchaseInvoiceStatus
            | xtremPurchasing.enums.PurchaseCreditMemoStatus
            | xtremPurchasing.enums.PurchaseDocumentStatus = 'draft',
        includeAllTotals = false,
        forceUpdateForFinance?: boolean,
    ): Promise<PurchasingTaxCalculator> {
        return new PurchasingTaxCalculator(classContext).init(site, status, includeAllTotals, forceUpdateForFinance);
    }
}
