import { CustomSqlAction } from '@sage/xtrem-system';

export const baseOutBoundShipmentDocumentDeliveryMode = new CustomSqlAction({
    description: 'Add properties to the base outbound shipment document - delivery mode  ',
    fixes: {
        notNullableColumns: [{ table: 'base_outbound_shipment_document', column: 'delivery_mode' }],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            WITH CTE AS (
                 SELECT a._id, d.mode, a._tenant_id
                FROM ${helper.schemaName}.base_business_relation a
                LEFT OUTER JOIN ${helper.schemaName}.business_entity b ON a.business_entity = b._id and a._tenant_id = b._tenant_id
                LEFT OUTER JOIN (
                    SELECT *
                    FROM ${helper.schemaName}.business_entity_address
                    WHERE is_primary = true
                ) c ON b._id = c.business_entity and a._tenant_id = c._tenant_id
                LEFT OUTER JOIN ${helper.schemaName}.delivery_detail d ON c._id = d.address and a._tenant_id = d._tenant_id
                )
                UPDATE ${helper.schemaName}.base_outbound_shipment_document a
                    SET delivery_mode = (
                        SELECT _id
                        FROM CTE cte
                        WHERE cte._id = b.business_relation and cte._tenant_id = b._tenant_id
                    )
                FROM ${helper.schemaName}.base_distribution_document b
                WHERE a._id = b._id AND a._tenant_id = b._tenant_id
                AND delivery_mode IS NULL;
                END $$;`);
    },
});

export const baseOutBoundShipmentDocumentDeliveryLeadTime = new CustomSqlAction({
    description: 'Add properties to the base outbound shipment document - delivery lead time  ',
    fixes: {
        notNullableColumns: [{ table: 'base_outbound_shipment_document', column: 'delivery_lead_time' }],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.base_outbound_shipment_document a
            SET delivery_lead_time = 0
            FROM ${helper.schemaName}.base_document b
            WHERE a._id = b._id and a._tenant_id = b._tenant_id;
            END $$;`);
    },
});

export const baseOutBoundShipmentDocumentExptedDeliveryDate = new CustomSqlAction({
    description: 'Add properties to the base outbound order document - delivery date  ',
    fixes: {
        notNullableColumns: [{ table: 'base_outbound_shipment_document', column: 'delivery_date' }],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.base_outbound_shipment_document a
            SET delivery_date = b.date
            FROM ${helper.schemaName}.base_document b
            WHERE a._id = b._id and a._tenant_id = b._tenant_id;
            END $$;`);
    },
});
