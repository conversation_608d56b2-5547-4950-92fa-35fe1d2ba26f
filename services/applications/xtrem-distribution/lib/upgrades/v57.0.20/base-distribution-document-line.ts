import { CustomSqlAction } from '@sage/xtrem-system';

export const baseDistributionDocumentLineTaxProperties = new CustomSqlAction({
    description: 'Move tax properties from base purchase document line to base distribution document line',
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.base_distribution_document_line
            SET taxable_amount = COALESCE(b.taxable_amount, 0.00),
                tax_amount = COALESCE(b.tax_amount, 0.00),
                exempt_amount = COALESCE(b.exempt_amount, 0.00),
                tax_date = b.tax_date,
                text = COALESCE(b.text, '')
            FROM ${helper.schemaName}.base_purchase_document_line b
            WHERE base_distribution_document_line._constructor = b._constructor
            AND base_distribution_document_line._id = b._id
            AND base_distribution_document_line._tenant_id = b._tenant_id;
        END $$;`);
    },
});
