import { CustomSqlAction } from '@sage/xtrem-system';

export const copyCustomFieldsToBaseDistributionDocument = new CustomSqlAction({
    description: 'Copy custom fields from BasePurchaseDocument to BaseDistributionDocument',
    body: async helper => {
        await helper.executeSql(`
            WITH purchase_node AS (
                SELECT _id
                FROM ${helper.schemaName}.meta_node_factory
                WHERE name = 'BasePurchaseDocument'
            ),
            distribution_node AS (
                SELECT _id
                FROM ${helper.schemaName}.meta_node_factory
                WHERE name = 'BaseDistributionDocument'
            )
            INSERT INTO ${helper.schemaName}.custom_field (
                _tenant_id,
                node,
                name,
                title,
                data_type,
                target_node,
                enum_values,
                component_type,
                component_attributes,
                anchor_property,
                anchor_position,
                destination_types,
                is_active,
                _create_user,
                _update_user,
                _create_stamp,
                _update_stamp,
                _update_tick,
                _source_id,
                _custom_data
            )
            SELECT
                cf._tenant_id,
                (SELECT _id FROM distribution_node) as node,
                cf.name,
                cf.title,
                cf.data_type,
                cf.target_node,
                cf.enum_values,
                cf.component_type,
                cf.component_attributes,
                cf.anchor_property,
                cf.anchor_position,
                cf.destination_types,
                cf.is_active,
                cf._create_user,
                cf._update_user,
                cf._create_stamp,
                cf._update_stamp,
                cf._update_tick,
                cf._source_id,
                cf._custom_data
            FROM ${helper.schemaName}.custom_field cf
            WHERE cf.node = (SELECT _id FROM purchase_node)
            AND NOT EXISTS (
                SELECT 1
                FROM ${helper.schemaName}.custom_field cf2
                WHERE cf2.node = (SELECT _id FROM distribution_node)
                AND cf2.name = cf.name
                AND cf2._tenant_id = cf._tenant_id
            );`);
    },
});

export const copyCustomFieldsToBaseDistributionDocumentLine = new CustomSqlAction({
    description: 'Copy custom fields from BasePurchaseDocumentLine to BaseDistributionDocumentLine',
    body: async helper => {
        await helper.executeSql(`
            WITH purchase_node_line AS (
                SELECT _id
                FROM ${helper.schemaName}.meta_node_factory
                WHERE name = 'BasePurchaseDocumentLine'
            ),
            distribution_node_line AS (
                SELECT _id
                FROM ${helper.schemaName}.meta_node_factory
                WHERE name = 'BaseDistributionDocumentLine'
            )
            INSERT INTO ${helper.schemaName}.custom_field (
                _tenant_id,
                node,
                name,
                title,
                data_type,
                target_node,
                enum_values,
                component_type,
                component_attributes,
                anchor_property,
                anchor_position,
                destination_types,
                is_active,
                _create_user,
                _update_user,
                _create_stamp,
                _update_stamp,
                _update_tick,
                _source_id,
                _custom_data
            )
            SELECT
                cf._tenant_id,
                (SELECT _id FROM distribution_node_line) as node,
                cf.name,
                cf.title,
                cf.data_type,
                cf.target_node,
                cf.enum_values,
                cf.component_type,
                cf.component_attributes,
                cf.anchor_property,
                cf.anchor_position,
                cf.destination_types,
                cf.is_active,
                cf._create_user,
                cf._update_user,
                cf._create_stamp,
                cf._update_stamp,
                cf._update_tick,
                cf._source_id,
                cf._custom_data
            FROM ${helper.schemaName}.custom_field cf
            WHERE cf.node = (SELECT _id FROM purchase_node_line)
            AND NOT EXISTS (
                SELECT 1
                FROM ${helper.schemaName}.custom_field cf2
                WHERE cf2.node = (SELECT _id FROM distribution_node_line)
                AND cf2.name = cf.name
                AND cf2._tenant_id = cf._tenant_id
            );`);
    },
});
