import { CustomSqlAction } from '@sage/xtrem-system';

export const updateCustomDataInDistributionDocument = new CustomSqlAction({
    description:
        'Update _custom_data in base_distribution_document with merged boolean values from base_purchase_document',
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.base_distribution_document bd
            SET _custom_data = (
                SELECT jsonb_object_agg(
                    key,
                    CASE
                        WHEN jsonb_typeof(bd._custom_data::jsonb -> key) = 'boolean'
                             OR jsonb_typeof(bp._custom_data::jsonb -> key) = 'boolean'
                        THEN to_jsonb(
                            COALESCE((bd._custom_data::jsonb ->> key)::boolean, false)
                            OR
                            COALESCE((bp._custom_data::jsonb ->> key)::boolean, false)
                        )
                        ELSE COALESCE(bd._custom_data::jsonb -> key, bp._custom_data::jsonb -> key)
                    END
                )::json
                FROM (
                    SELECT DISTINCT key
                    FROM (
                        SELECT jsonb_object_keys(COALESCE(bd._custom_data::jsonb, '{}'::jsonb)) AS key
                        UNION
                        SELECT jsonb_object_keys(COALESCE(bp._custom_data::jsonb, '{}'::jsonb)) AS key
                    ) AS all_keys
                ) AS keys
            )
            FROM ${helper.schemaName}.base_purchase_document bp
            WHERE bd._id = bp._id
            AND bd._tenant_id = bp._tenant_id
            AND bp._constructor = 'PurchaseReceipt';
        END $$;`);
    },
});

export const updateCustomDataInDistributionDocumentLine = new CustomSqlAction({
    description:
        'Update _custom_data in base_distribution_document_line with merged boolean values from base_purchase_document_line',
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.base_distribution_document_line bdl
            SET _custom_data = (
                SELECT jsonb_object_agg(
                    key,
                    CASE
                        WHEN jsonb_typeof(bdl._custom_data::jsonb -> key) = 'boolean'
                             OR jsonb_typeof(bpl._custom_data::jsonb -> key) = 'boolean'
                        THEN to_jsonb(
                            COALESCE((bdl._custom_data::jsonb ->> key)::boolean, false)
                            OR
                            COALESCE((bpl._custom_data::jsonb ->> key)::boolean, false)
                        )
                        ELSE COALESCE(bdl._custom_data::jsonb -> key, bpl._custom_data::jsonb -> key)
                    END
                )::json
                FROM (
                    SELECT DISTINCT key
                    FROM (
                        SELECT jsonb_object_keys(COALESCE(bdl._custom_data::jsonb, '{}'::jsonb)) AS key
                        UNION
                        SELECT jsonb_object_keys(COALESCE(bpl._custom_data::jsonb, '{}'::jsonb)) AS key
                    ) AS all_keys
                ) AS keys
            )
            FROM ${helper.schemaName}.base_purchase_document_line bpl
            WHERE bdl._id = bpl._id
            AND bdl._tenant_id = bpl._tenant_id
            AND bpl._constructor = 'PurchaseReceiptLine';
        END $$;`);
    },
});
