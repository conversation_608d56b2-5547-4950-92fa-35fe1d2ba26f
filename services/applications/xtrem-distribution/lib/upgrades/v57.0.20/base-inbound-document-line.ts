import { CustomSqlAction } from '@sage/xtrem-system';

export const insertBaseInboundDocumentLine = new CustomSqlAction({
    description: 'Add purchase receipt lines to base inbound receipt document',
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            INSERT INTO ${helper.schemaName}.base_inbound_document_line
            (_tenant_id, _id, _constructor, _sort_value)
            SELECT
                b._tenant_id,
                b._id,
                b._constructor,
                b._sort_value
            FROM ${helper.schemaName}.base_purchase_document_line b
            WHERE b._constructor = 'PurchaseReceiptLine'
            AND NOT EXISTS (
                SELECT 1
                FROM ${helper.schemaName}.base_inbound_document_line bir
                WHERE bir._id = b._id
                AND bir._tenant_id = b._tenant_id
            );
        END $$;`);
    },
});
