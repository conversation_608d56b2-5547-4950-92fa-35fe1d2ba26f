import { UpgradeSuite } from '@sage/xtrem-system';
import { baseDistributionDocumentLineTaxProperties } from './base-distribution-document-line';
import { insertBaseInboundDocument } from './base-inbound-document';
import { insertBaseInboundDocumentLine } from './base-inbound-document-line';
import { insertBaseInboundReceiptDocument } from './base-inbound-receipt-document';
import {
    insertBaseInboundReceiptDocumentLine,
    updateBaseInboundReceiptDocumentLineStockTransactionStatus,
} from './base-inbound-receipt-document-line';
import {
    copyCustomFieldsToBaseDistributionDocument,
    copyCustomFieldsToBaseDistributionDocumentLine,
} from './custom-field-setup';
import { renameBasePurchaseDocumentLineDiscountCharge } from './rename-base-purchase-document-line-discount-charge';
import {
    updateCustomDataInDistributionDocument,
    updateCustomDataInDistributionDocumentLine,
} from './update-custom-data-in-distribution-document';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        insertBaseInboundDocument,
        insertBaseInboundDocumentLine,
        insertBaseInboundReceiptDocument,
        insertBaseInboundReceiptDocumentLine,
        updateBaseInboundReceiptDocumentLineStockTransactionStatus,
        baseDistributionDocumentLineTaxProperties,
        renameBasePurchaseDocumentLineDiscountCharge,
        updateCustomDataInDistributionDocument,
        updateCustomDataInDistributionDocumentLine,
        copyCustomFieldsToBaseDistributionDocument,
        copyCustomFieldsToBaseDistributionDocumentLine,
    ],
});
