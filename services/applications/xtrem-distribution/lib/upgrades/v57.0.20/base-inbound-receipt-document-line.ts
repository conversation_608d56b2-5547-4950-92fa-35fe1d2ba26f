import { CustomSqlAction } from '@sage/xtrem-system';

export const insertBaseInboundReceiptDocumentLine = new CustomSqlAction({
    description: 'Add purchase receipt lines to base inbound receipt document',
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            INSERT INTO ${helper.schemaName}.base_inbound_receipt_document_line
            (_tenant_id, _id, _constructor, _sort_value, stock_transaction_status, stock_cost_amount_in_company_currency)
            SELECT
                b._tenant_id,
                b._id,
                b._constructor,
                b._sort_value,
                'draft',
                0.00
            FROM ${helper.schemaName}.base_purchase_document_line b
            WHERE b._constructor = 'PurchaseReceiptLine'
            AND NOT EXISTS (
                SELECT 1
                FROM ${helper.schemaName}.base_inbound_receipt_document_line bir
                WHERE bir._id = b._id
                AND bir._tenant_id = b._tenant_id
            );
        END $$;`);
    },
});

export const updateBaseInboundReceiptDocumentLineStockTransactionStatus = new CustomSqlAction({
    description: 'Update base inbound receipt document line stock transaction status',
    body: async helper => {
        await helper.executeSql(`DO $$
         BEGIN
            UPDATE ${helper.schemaName}.base_inbound_receipt_document_line
            SET stock_transaction_status = b.stock_transaction_status
            FROM ${helper.schemaName}.purchase_receipt_line b
            WHERE base_inbound_receipt_document_line._constructor = 'PurchaseReceiptLine'
            AND base_inbound_receipt_document_line._id = b._id
            AND base_inbound_receipt_document_line._tenant_id = b._tenant_id;
        END $$;`);
    },
});
