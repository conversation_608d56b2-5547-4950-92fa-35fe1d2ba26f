import { CustomSqlAction } from '@sage/xtrem-system';

export const insertBaseInboundReceiptDocument = new CustomSqlAction({
    description: 'Add purchase receipt lines to base inbound receipt document',
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            INSERT INTO ${helper.schemaName}.base_inbound_receipt_document
            (_tenant_id, _id, _constructor)
            SELECT
                b._tenant_id,
                b._id,
                b._constructor
            FROM ${helper.schemaName}.base_purchase_document b
            WHERE b._constructor = 'PurchaseReceipt'
            AND NOT EXISTS (
                SELECT 1
                FROM ${helper.schemaName}.base_inbound_receipt_document bir
                WHERE bir._id = b._id
                AND bir._tenant_id = b._tenant_id
            );
        END $$;`);
    },
});
