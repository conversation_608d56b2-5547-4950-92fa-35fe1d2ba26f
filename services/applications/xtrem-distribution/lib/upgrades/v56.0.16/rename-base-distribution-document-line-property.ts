import { SchemaRenamePropertyAction } from '@sage/xtrem-system';
import { BaseDistributionDocumentLine } from '../../nodes';

export const renamePropertyBaseDistributionDocumentLineForLineAmountExcludingTax = new SchemaRenamePropertyAction({
    node: () => BaseDistributionDocumentLine,
    oldPropertyName: 'lineAmountExcludingTax',
    newPropertyName: 'amountExcludingTax',
});

export const renamePropertyBaseDistributionDocumentLineForLineAmountExcludingTaxInCompanyCurrency =
    new SchemaRenamePropertyAction({
        node: () => BaseDistributionDocumentLine,
        oldPropertyName: 'lineAmountExcludingTaxInCompanyCurrency',
        newPropertyName: 'amountExcludingTaxInCompanyCurrency',
    });
