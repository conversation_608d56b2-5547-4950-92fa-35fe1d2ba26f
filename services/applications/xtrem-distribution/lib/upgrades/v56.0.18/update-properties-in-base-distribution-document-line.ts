import { CustomSqlAction } from '@sage/xtrem-system';

export const baseDistributionDocumentLineTaxProperties = new CustomSqlAction({
    description:
        'Move amountIncludingTax, amountIncludingTaxInCompanyCurrency and taxAmountAdjusted values from base purchaseDocumentLine to baseDistributionDocumentLine',
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.base_distribution_document_line a
            SET
                amount_including_tax = b.amount_including_tax,
                amount_including_tax_in_company_currency = b.amount_including_tax_in_company_currency,
                tax_amount_adjusted = b.tax_amount_adjusted
            FROM ${helper.schemaName}.base_purchase_document_line b
            WHERE a._id = b._id AND a._constructor = b._constructor AND a._tenant_id = b._tenant_id;
        END $$;`);
    },
});
