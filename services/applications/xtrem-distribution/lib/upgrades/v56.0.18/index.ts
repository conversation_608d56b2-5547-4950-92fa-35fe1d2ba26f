import { UpgradeSuite } from '@sage/xtrem-system';
import { updateLineAmountExcludingTaxInCompanyCurrency } from './update-line-amount-excluding-tax-in-company-currency';
import { updateLineAmountIncludingTaxInCompanyCurrency } from './update-line-amount-including-tax-in-company-currency';
import { baseDistributionDocumentLineTaxProperties } from './update-properties-in-base-distribution-document-line';

export const upgradeSuite = new UpgradeSuite({
    actions: [
        // renamePurchaseDocumentTax, // commented following the move of the DocumentLineTax node to the xtrem-tax package
        // renamePurchaseDocumentLineTax, // commented following the move of the DocumentTax node to the xtrem-tax package
        baseDistributionDocumentLineTaxProperties,
        updateLineAmountIncludingTaxInCompanyCurrency,
        updateLineAmountExcludingTaxInCompanyCurrency,
    ],
});
