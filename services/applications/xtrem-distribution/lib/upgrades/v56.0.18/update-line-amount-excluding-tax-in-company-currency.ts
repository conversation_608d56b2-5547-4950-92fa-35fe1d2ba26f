import * as xtremMasterData from '@sage/xtrem-master-data';
import { DataUpdateAction } from '@sage/xtrem-system';
import { BaseDistributionDocumentLine } from '../../nodes/base-distribution-document-line';

export const updateLineAmountExcludingTaxInCompanyCurrency = new DataUpdateAction({
    description: 'Set lineAmountExcludingTaxInCompanyCurrency',
    node: () => BaseDistributionDocumentLine,
    set: {
        async amountExcludingTaxInCompanyCurrency() {
            const amount = await this.amountExcludingTax;
            const rateMultiplication = await (await this.document).companyFxRate;
            const rateDivision = await (await this.document).companyFxRateDivisor;
            const fromDecimals = await (await (await this.document).currency).decimalDigits;
            const toDecimals = await (await (await this.document).companyCurrency).decimalDigits;
            if (amount === 0 || rateMultiplication === 0 || rateDivision === 0) {
                return 0;
            }
            return xtremMasterData.sharedFunctions.convertAmount(
                amount,
                rateMultiplication,
                rateDivision,
                fromDecimals,
                toDecimals,
            );
        },
    },
});
