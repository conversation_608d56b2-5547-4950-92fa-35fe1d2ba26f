import { CustomSqlAction } from '@sage/xtrem-system';

export const baseOutBoundOrderDocumentLineQuantityInSalesUnit = new CustomSqlAction({
    description: 'Add properties to the base outbound order document - quantity in sales unit',
    fixes: {
        notNullableColumns: [{ table: 'base_outbound_order_document_line', column: 'quantity_in_sales_unit' }],
    },
    body: async helper => {
        await helper.executeSql(`DO $$
        BEGIN
            UPDATE ${helper.schemaName}.base_outbound_order_document_line a
            SET quantity_in_sales_unit = 0;
            END $$;`);
    },
});
