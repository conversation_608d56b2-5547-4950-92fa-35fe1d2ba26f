import type { Graph<PERSON><PERSON> } from '@sage/xtrem-distribution-api';
import type { BankAccount, BasePaymentDocument, PaymentDocumentLine } from '@sage/xtrem-finance-data-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageFragment<PaymentLine>({})
export class PaymentLine extends ui.PageFragment<GraphApi> {
    status: ui.fields.Label;

    @ui.decorators.tableField<PaymentLine, PaymentDocumentLine>({
        node: '@sage/xtrem-finance-data/PaymentDocumentLine',
        title: 'Payments',
        isTitleHidden: true,
        canSelect: false,
        canExport: true,
        isReadOnly: true,
        isChangeIndicatorDisabled: true,
        pageSize: 10,
        bind: { paymentTracking: { paymentLines: true } },
        isHidden() {
            return this.status.value !== 'posted' || !this.$.isServiceOptionEnabled('paymentTrackingOption');
        },
        orderBy: { document: { paymentDate: -1 } },
        columns: [
            ui.nestedFields.technical({
                bind: 'document',
                node: '@sage/xtrem-finance-data/BasePaymentDocument',
                nestedFields: [
                    ui.nestedFields.technical({ bind: '_id' }),
                    ui.nestedFields.technical<PaymentLine, BasePaymentDocument, Currency>({
                        bind: 'currency',
                        node: '@sage/xtrem-master-data/Currency',
                        nestedFields: [
                            ui.nestedFields.technical({ bind: 'name' }),
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                        ],
                    }),
                    ui.nestedFields.technical<PaymentLine, BasePaymentDocument, BankAccount>({
                        bind: 'bankAccount',
                        node: '@sage/xtrem-finance-data/BankAccount',
                        nestedFields: [ui.nestedFields.technical({ bind: 'name' })],
                    }),
                    ui.nestedFields.technical({ bind: 'paymentMethod' }),
                    ui.nestedFields.technical({ bind: 'reference' }),
                ],
            }),
            ui.nestedFields.date({ title: 'Payment date', bind: { document: { paymentDate: true } } }),
            ui.nestedFields.link({
                title: 'Payment number',
                width: 'small',
                bind: { document: { number: true } },
                isDisabled() {
                    return this.$.queryParameters.fromFinance?.toString() === 'true';
                },
                async onClick(_rowId, rowData: ui.PartialCollectionValue<PaymentDocumentLine>) {
                    await this.$.dialog.page(
                        `@sage/xtrem-finance/Payment`,
                        { _id: rowData.document?._id ?? '', fromSales: true },
                        { fullScreen: true, resolveOnCancel: true },
                    );
                    await this.$.router.refresh();
                },
            }),
            ui.nestedFields.numeric({
                title: 'Amount',
                bind: 'amount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Discount amount',
                bind: 'discountAmount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Penalty amount',
                bind: 'penaltyAmount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.numeric({
                title: 'Adjustment amount',
                bind: 'adjustmentAmount',
                unit: (_rowId, rowData) => rowData?.document?.currency,
                scale: null,
            }),
            ui.nestedFields.text({ title: 'Bank account', bind: { document: { bankAccount: { name: true } } } }),
            ui.nestedFields.dropdownList({
                bind: { document: { paymentMethod: true } },
                title: 'Payment method',
                optionType: '@sage/xtrem-master-data/PaymentMethod',
            }),
            ui.nestedFields.text({ title: 'Transaction information', bind: { document: { reference: true } } }),
            ui.nestedFields.checkbox({ title: 'Voided', bind: { document: { isVoided: true } } }),
        ],
    })
    payments: ui.fields.Table<PaymentDocumentLine>;
}
