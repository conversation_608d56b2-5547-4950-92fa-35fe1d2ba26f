import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremDistribution from '../..';

export async function checkIfSameStatus(
    cx: ValidationContext,
    line: xtremDistribution.nodes.BaseDistributionDocumentLine,
) {
    if ((await (await line.document).status) !== (await line.status)) {
        cx.error.addLocalized(
            '@sage/xtrem-distribution/nodes__base_distribution_document_line__line_must_have_the_same_status',
            'The line status needs to be the same as the transfer status.',
        );
    }
}

export async function checkIfSameStockSite(
    cx: ValidationContext,
    line: xtremDistribution.nodes.BaseDistributionDocumentLine,
) {
    if ((await (await line.document).stockSite) !== (await line.stockSite)) {
        cx.error.addLocalized(
            '@sage/xtrem-distribution/nodes__base_distribution_document_line__stock_site_must_have_the_same_value',
            'The stock site on the line needs to be the same as the stock site for the transfer.',
        );
    }
}

/**
 * Checks if a distribution document line can have a landed cost line.
 * Only purchase order line, purchase receipt line and stock transfer receipt line should have landed cost lines.
 */
export async function checkCanHaveLandedCostLine(
    cx: ValidationContext,
    line: xtremDistribution.nodes.BaseDistributionDocumentLine,
) {
    if (!(await line.canHaveLandedCostLine) && (await line.landedCostLines.length)) {
        cx.error.addLocalized(
            '@sage/xtrem-distribution/nodes__base-distribution-document-line__cannot_have_landed_cost_line',
            'This document line cannot have a landed cost line: {{documentLineType}}.',
            {
                documentLineType: line.$.factory.name,
            },
        );
    }
}

/**
 * Checks if a distribution document line can have a landed cost.
 * Only purchase invoice line and purchase credit memo line should have landed cost.
 */
export async function checkCanHaveLandedCost(
    cx: ValidationContext,
    line: xtremDistribution.nodes.BaseDistributionDocumentLine,
) {
    if (!(await line.canHaveLandedCost) && (await line.landedCost)) {
        cx.error.addLocalized(
            '@sage/xtrem-distribution/nodes__base-distribution-document-line__cannot_have_landed_cost',
            'This document line cannot have a landed cost: {{documentLineType}}.',
            {
                documentLineType: line.$.factory.name,
            },
        );
    }
}
