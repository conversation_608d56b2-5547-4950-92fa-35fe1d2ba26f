import type { ValidationContext } from '@sage/xtrem-core';
import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremDistribution from '../../index';

export async function checkIfSameShippingStatus(
    cx: ValidationContext,
    documentLine: xtremDistribution.nodes.BaseOutboundOrderDocumentLine,
): Promise<void> {
    const documentShippingStatus = await (await documentLine.document).shippingStatus;
    if (
        documentShippingStatus !== (await documentLine.shippingStatus) &&
        documentShippingStatus !== 'partiallyShipped'
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-distribution/nodes__base_outbound_order_document_line__sales_order_lines_shipping_status_must_have_the_same_shipping_status',
            'The Shipping status for the document order lines needs to be the same as the Shipping status in the document.',
        );
    }
}

export async function deleteNotAllowedIfShipped(
    cx: ValidationContext,
    documentLine: xtremDistribution.nodes.BaseOutboundOrderDocumentLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_outbound_order_document_line__deletion_forbidden_line_exists',
            'You can only delete the document order line if its Shipping status is Not shipped.',
        )
        .if(await documentLine.shippingStatus)
        .is.not.equal.to('notShipped');
}

export async function deleteNotAllowedIfStockAllocated(
    cx: ValidationContext,
    documentLine: xtremDistribution.nodes.BaseOutboundOrderDocumentLine,
) {
    const isStockAllocated =
        (await (await documentLine.item).isStockManaged) && (await documentLine.allocationStatus) !== 'notAllocated';
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_outbound_order_document_line__cannot_delete_line_quantity_allocated',
            'To delete the document order line, you need to remove the stock allocation.',
        )
        .if(isStockAllocated)
        .is.equal.to(true);
}

export async function deleteNotAllowedAutomaticAllocationInProgress(
    cx: ValidationContext,
    documentLine: xtremDistribution.nodes.BaseOutboundOrderDocumentLine,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_outbound_order_document_line__cannot_delete_line_allocation_request_in_progress',
            'To delete the stock transfer order line, you need to wait for the automatic allocation to finish.',
        )
        .if(await documentLine.allocationRequestStatus)
        .is.equal.to('inProgress');
}

export async function automaticAllocationInProgress(
    cx: ValidationContext,
    documentLine: xtremDistribution.nodes.BaseOutboundOrderDocumentLine,
) {
    // Automatic allocation in progress : we can't decrease the quantity
    const cannotDecreaseQuantity =
        documentLine.$.status === NodeStatus.modified &&
        (await documentLine.quantity) < (await (await documentLine.$.old).quantity) &&
        (await documentLine.allocationRequestStatus) === 'inProgress';
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_outbound_order_document_line__auto_allocation_cannot_decrease_quantity',
            'You can only reduce the quantity of the order line after the allocation request is complete.',
        )
        .if(cannotDecreaseQuantity)
        .is.equal.to(true);
}
