import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremDistribution from '../../index';

export function shippingStatusControl(cx: ValidationContext, shippingStatus: xtremDistribution.enums.ShippingStatus) {
    return cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_outbound_order_document__improper_shipping_status_during_creation',
            'To create the stock transfer order, you need to change the Shipping status to Not shipped.',
        )
        .if(shippingStatus)
        .is.not.equal.to('notShipped');
}

export async function cannotDeleteDocumentIfShippedOrPartiallyShipped(
    cx: ValidationContext,
    document: xtremDistribution.nodes.BaseOutboundOrderDocument,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_outbound_order_document__deletion_forbidden_line_exists',
            'You can only delete the document line if its Shipping status is Not shipped.',
        )
        .if(await document.shippingStatus)
        .is.not.equal.to('notShipped');
}
