import type { ValidationContext } from '@sage/xtrem-core';
import type * as xtremDistribution from '../../index';

export async function cannotUpdateDocumentShipToCustomer(
    cx: ValidationContext,
    document: xtremDistribution.nodes.BaseOutboundDocument,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__stock_transfer_order__header_ship_to_customer_not_updatable',
            'The Ship-to customer needs to be the same as the Receiving site for the document.',
        )
        .if(await (await document.$.old).shipToCustomer)
        .is.not.equal.to(await document.shipToCustomer);
}
