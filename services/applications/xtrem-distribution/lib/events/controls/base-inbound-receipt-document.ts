import type { ValidationContext } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import type * as xtremDistribution from '../../index';

/** Check if at least one of the given taxes has a wrong type. */
export async function checkWrongTaxType(
    cx: ValidationContext,
    document: xtremDistribution.nodes.BaseInboundReceiptDocument,
) {
    if (
        await xtremTax.functions.hasWrongTaxType({
            taxes: await document.taxes.toArray(),
            validTypes: ['purchasing', 'purchasingAndSales'],
        })
    ) {
        cx.error.addLocalized(
            '@sage/xtrem-distribution/nodes__base_inbound_receipt_document__tax_type_validation',
            "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.",
        );
    }
}

/** The site and the stock site need to have the same company. */
export async function sameCompanyForSiteAndStockSite(
    document: xtremDistribution.nodes.BaseInboundReceiptDocument,
    cx: ValidationContext,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_inbound_receipt_document__same_company_site_stock_site',
            'The site and the stock site need to have the same company.',
        )
        .if((await (await document.site).legalCompany)._id)
        .is.not.equal.to((await (await document.stockSite).legalCompany)._id);
}

const mayDeleteStatuses: xtremMasterData.enums.BaseStatus[] = ['draft', 'pending'];

export async function checkStatusDeletion(
    document: xtremDistribution.nodes.BaseInboundReceiptDocument,
    cx: ValidationContext,
) {
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_inbound_receipt_document__deletion_forbidden_reason_status',
            'The current document is at the status: {{currentStatus}}. You cannot delete it.',
            async () => {
                return { currentStatus: await document.status };
            },
        )
        .if(mayDeleteStatuses.includes(await document.status))
        .is.false();
}
