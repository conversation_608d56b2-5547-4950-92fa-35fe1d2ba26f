import type { Context, ValidationContext, decimal } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';

export async function companyFxRateControl(cx: ValidationContext, rate: decimal): Promise<void> {
    await cx.error
        .withMessage(
            '@sage/xtrem-distribution/nodes__base_distribution_document__fx_rate_not_found',
            'No exchange rate found.',
        )
        .if(rate)
        .is.zero();
}

async function getBusinessEntitySite(
    context: Context,
    businessEntity?: xtremMasterData.nodes.BusinessEntity,
): Promise<xtremSystem.nodes.Site | null> {
    if (!businessEntity || !(await businessEntity.isSite)) return null;
    const site = await context
        .query(xtremSystem.nodes.Site, {
            filter: { businessEntity: businessEntity._id },
            first: 1,
        })
        .at(0);
    return site || null;
}

/**
 * Controls the legal companies associated with the given site and business entity.
 * If the legal company of the business entity's site matches the legal company of the given site,
 * an error is added to the validation context.
 *
 * @param cx - The validation context to which errors can be added.
 * @param context - The context used to fetch data.
 * @param site - The site node to be validated.
 * @param businessEntity - The optional business entity node to be validated.
 * @returns A promise that resolves when the validation is complete.
 */
export async function controlSitesLegalCompanies(
    cx: ValidationContext,
    context: Context,
    site: xtremSystem.nodes.Site,
    businessEntity?: xtremMasterData.nodes.BusinessEntity,
): Promise<void> {
    if (!site) return;
    const businessEntitySite = await getBusinessEntitySite(context, businessEntity);
    if (!businessEntitySite) return;
    if ((await businessEntitySite.legalCompany)._id === (await site.legalCompany)._id) {
        cx.error.addLocalized(
            '@sage/xtrem-distribution/nodes__base_distribution_document__control_sites_legal_companies',
            'This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.',
        );
    }
}
