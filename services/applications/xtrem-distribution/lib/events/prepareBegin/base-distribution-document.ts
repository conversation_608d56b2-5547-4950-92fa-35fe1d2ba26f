import { NodeStatus } from '@sage/xtrem-core';
import type * as xtremDistribution from '../../../index';

export async function prepareBegin(document: xtremDistribution.nodes.BaseDistributionDocument): Promise<void> {
    if (
        ['PurchaseInvoice', 'PurchaseCreditmemo'].includes(document.$.factory.name) &&
        (document.$.status === NodeStatus.added ||
            (document.$.status === NodeStatus.modified &&
                (await (await document.$.old).paymentTerm) !== (await document.paymentTerm)))
    ) {
        await document.$.set({
            paymentTracking: {
                paymentTerm: await document.paymentTerm,
            },
        });
    }
}
