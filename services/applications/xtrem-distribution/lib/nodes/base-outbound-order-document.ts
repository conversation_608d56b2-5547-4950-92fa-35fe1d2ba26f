import type { Collection, date, integer, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDistribution from '..';
import {
    cannotDeleteDocumentIfShippedOrPartiallyShipped,
    shippingStatusControl,
} from '../events/controls/outbound-order-document';
import { BaseOutboundDocument } from './base-outbound-document';

@decorators.subNode<BaseOutboundOrderDocument>({
    isAbstract: true,
    isPublished: true,
    extends: () => BaseOutboundDocument,
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.added) {
            await shippingStatusControl(cx, await this.shippingStatus);
            await cannotDeleteDocumentIfShippedOrPartiallyShipped(cx, this);
        }
    },
})
export class BaseOutboundOrderDocument extends BaseOutboundDocument {
    @decorators.dateProperty<BaseOutboundOrderDocument, 'doNotShipBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly doNotShipBeforeDate: Promise<date | null>;

    @decorators.dateProperty<BaseOutboundOrderDocument, 'doNotShipAfterDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
    })
    readonly doNotShipAfterDate: Promise<date | null>;

    @decorators.dateProperty<BaseOutboundOrderDocument, 'requestedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
    })
    readonly requestedDeliveryDate: Promise<date>;

    @decorators.enumProperty<BaseOutboundOrderDocument, 'shippingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremDistribution.enums.shippingStatusDataType,
        defaultValue: 'notShipped',
        lookupAccess: true,
    })
    readonly shippingStatus: Promise<xtremDistribution.enums.ShippingStatus>;

    @decorators.referenceProperty<BaseOutboundOrderDocument, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.shipToCustomer)?.primaryShipToAddress;
        },
        filters: {
            control: {
                async businessEntity() {
                    return (await this.shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<BaseOutboundOrderDocument, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return (await this.shipToCustomerAddress)?.address;
        },
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<BaseOutboundOrderDocument, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.primaryContact)?.contact ?? null;
        },
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.dateProperty<BaseOutboundOrderDocument, 'shippingDate'>({
        isPublished: true,
        isStored: true,
        isRequired: true,
        lookupAccess: true,
    })
    readonly shippingDate: Promise<date>;

    @decorators.referenceProperty<BaseOutboundOrderDocument, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.DeliveryMode,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.mode ?? null;
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode>;

    // this field represents number of days.
    @decorators.integerProperty<BaseOutboundOrderDocument, 'deliveryLeadTime'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['shipToCustomerAddress'],
        lookupAccess: true,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.deliveryDetail)?.leadTime || 0;
        },
    })
    readonly deliveryLeadTime: Promise<integer>;

    @decorators.integerProperty<BaseOutboundOrderDocument, 'workDays'>({
        isPublished: true,
        excludedFromPayload: true,
        dependsOn: ['shipToCustomerAddress'],
        async computeValue() {
            const deliveryDetail = await (await this.shipToCustomerAddress).deliveryDetail;
            if (deliveryDetail) {
                return xtremDistribution.functions.computeWorkDays(deliveryDetail);
            }
            return 0;
        },
    })
    readonly workDays: Promise<integer>;

    @decorators.dateProperty<BaseOutboundOrderDocument, 'expectedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shippingDate', 'deliveryLeadTime', 'workDays'],
        async defaultValue() {
            return xtremDistribution.functions.addWorkDays(
                await this.shippingDate,
                await this.deliveryLeadTime,
                await this.workDays,
            );
        },
    })
    readonly expectedDeliveryDate: Promise<date>;

    @decorators.collectionPropertyOverride<BaseOutboundOrderDocument, 'lines'>({
        node: () => xtremDistribution.nodes.BaseOutboundOrderDocumentLine,
    })
    override readonly lines: Collection<xtremDistribution.nodes.BaseOutboundOrderDocumentLine>;
}
