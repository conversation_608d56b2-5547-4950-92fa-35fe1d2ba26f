import type { Collection, date, decimal, Reference } from '@sage/xtrem-core';
import { Decimal, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremDistribution from '..';
import { companyFxRateControl } from '../events/controls/distribution-document';

@decorators.subNode<BaseDistributionDocument>({
    isAbstract: true,
    isPublished: true,
    extends: () => xtremMasterData.nodes.BaseDocument,

    async prepareBegin() {
        await xtremDistribution.events.prepareBegin(this);
    },
})
export class BaseDistributionDocument extends xtremMasterData.nodes.BaseDocument {
    @decorators.referenceProperty<BaseDistributionDocument, 'businessRelation'>({
        isPublished: true,
        isRequired: true,
        isStored: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.BaseBusinessRelation,
    })
    businessRelation: Reference<xtremMasterData.nodes.BaseBusinessRelation>;

    @decorators.enumProperty<BaseDistributionDocument, 'invoiceStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremDistribution.enums.InvoiceStatusDataType,
        defaultValue: 'notInvoiced',
    })
    readonly invoiceStatus: Promise<xtremDistribution.enums.InvoiceStatus>;

    @decorators.dateProperty<BaseDistributionDocument, 'fxRateDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date'],
        defaultValue() {
            return this.date;
        },
    })
    readonly fxRateDate: Promise<date>;

    /** currency to company fx rate  */
    @decorators.decimalProperty<BaseDistributionDocument, 'companyFxRate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        updatedValue: useDefaultValue,
        isStoredOutput: true,
        dependsOn: ['currency', 'companyCurrency', 'fxRateDate', 'site'],
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        async defaultValue() {
            // eslint-disable-next-line @sage/xtrem/property-decorators-warnings
            const getRate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                await this.currency,
                await this.companyCurrency, // TODO: check diff between docs : some are using the site currency (po, preturn, preceipt)
                await this.fxRateDate,
            );
            return getRate.isRateFound ? getRate.rate : 1;
        },
        async control(cx, rate) {
            await companyFxRateControl(cx, rate);
        },
    })
    readonly companyFxRate: Promise<decimal>;

    @decorators.referenceProperty<BaseDistributionDocument, 'paymentTerm'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.PaymentTerm,
        dataType: () => xtremMasterData.dataTypes.paymentTerm,
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation).paymentTerm;
        },
        lookupAccess: true,
    })
    readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.referencePropertyOverride<BaseDistributionDocument, 'currency'>({
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await (await this.businessRelation)?.businessEntity)?.currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.decimalProperty<BaseDistributionDocument, 'companyFxRateDivisor'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => xtremMasterData.dataTypes.exchangeRate,
        dependsOn: ['currency', 'site', 'fxRateDate'],
        async defaultValue() {
            const currencyId = await (await this.currency).id;
            const destinationCurrencyId = await (await (await (await this.site).legalCompany).currency).id;
            // eslint-disable-next-line @sage/xtrem/property-decorators-warnings
            const rate = await xtremMasterData.functions.getRateOrReverseRate(
                this.$.context,
                currencyId,
                destinationCurrencyId,
                await this.fxRateDate,
            );
            return rate.isRateFound ? rate.divisor : 0;
        },
    })
    readonly companyFxRateDivisor: Promise<decimal>;

    @decorators.stringProperty<BaseDistributionDocument, 'rateDescription'>({
        isPublished: true,
        dependsOn: ['companyFxRate', 'companyFxRateDivisor'],
        async computeValue() {
            const currencyId = await (await this.currency).id;
            const destinationCurrencyId = await (await (await (await this.site).legalCompany).currency).id;
            return xtremMasterData.functions.rateDescription(
                currencyId,
                destinationCurrencyId,
                await this.companyFxRate,
                await this.companyFxRateDivisor,
            );
        },
    })
    readonly rateDescription: Promise<string>;

    @decorators.collectionPropertyOverride<BaseDistributionDocument, 'lines'>({
        node: () => xtremDistribution.nodes.BaseDistributionDocumentLine,
        dependsOn: ['companyFxRate'],
    })
    override readonly lines: Collection<xtremDistribution.nodes.BaseDistributionDocumentLine>;

    @decorators.decimalProperty<BaseDistributionDocument, 'totalAmountExcludingTax'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        defaultValue: 0,
        lookupAccess: true,
    })
    readonly totalAmountExcludingTax: Promise<decimal>;

    @decorators.decimalProperty<BaseDistributionDocument, 'totalAmountExcludingTaxInCompanyCurrency'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        defaultValue: 0,
    })
    readonly totalAmountExcludingTaxInCompanyCurrency: Promise<decimal>;

    @decorators.collectionProperty<BaseDistributionDocument, 'taxes'>({
        isVital: true,
        isPublished: true,
        reverseReference: 'document',
        node: () => xtremTax.nodes.DocumentTax,
        dependsOn: ['currency', 'status'],
        async isFrozen() {
            return (await this.status) === 'closed';
        },
    })
    readonly taxes: Collection<xtremTax.nodes.DocumentTax>;

    @decorators.decimalProperty<BaseDistributionDocument, 'totalTaxAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        async updatedValue() {
            return Decimal.roundAt(await this.totalTaxAmount, await (await this.currency).decimalDigits);
        },
    })
    readonly totalTaxAmount: Promise<decimal>;

    @decorators.decimalProperty<BaseDistributionDocument, 'totalTaxableAmount'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        defaultValue: 0,
    })
    readonly totalTaxableAmount: Promise<decimal>;

    @decorators.decimalProperty<BaseDistributionDocument, 'totalExemptAmount'>({
        isStored: true,
        isPublished: true,
        dependsOn: ['currency'],
        dataType: () => xtremMasterData.dataTypes.amountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        defaultValue: 0,
    })
    readonly totalExemptAmount: Promise<decimal>;

    @decorators.enumProperty<BaseDistributionDocument, 'taxCalculationStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.taxCalculationStatusDataType,
        defaultValue: 'notDone',
    })
    readonly taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;

    @decorators.enumProperty<BaseDistributionDocument, 'taxEngine'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.taxEngineDataType,
        async getValue() {
            return (await (await (await (await this.site).legalCompany).legislation).id) === 'US'
                ? 'genericTaxCalculation'
                : (await (await this.site).legalCompany).taxEngine;
        },
    })
    readonly taxEngine: Promise<xtremFinanceData.enums.TaxEngine | null>;

    @decorators.decimalProperty<BaseDistributionDocument, 'totalAmountIncludingTax'>({
        isNotZero: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType, // TODO: Has to have a computed length depending of the currency decimals !!!
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
        dependsOn: ['currency', 'totalAmountExcludingTax', 'totalTaxAmountAdjusted'],
        async getValue() {
            return Decimal.roundAt(
                (await this.totalAmountExcludingTax) + (await this.totalTaxAmountAdjusted),
                await (
                    await this.currency
                ).decimalDigits,
            );
        },
    })
    readonly totalAmountIncludingTax: Promise<decimal>;

    @decorators.decimalProperty<BaseDistributionDocument, 'totalTaxAmountAdjusted'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        dependsOn: ['currency'],
        async control(cx, val) {
            await cx.error.if(val).is.negative();
        },
    })
    readonly totalTaxAmountAdjusted: Promise<decimal>;

    @decorators.referenceProperty<BaseDistributionDocument, 'paymentTracking'>({
        isPublished: true,
        node: () => xtremFinanceData.nodes.PaymentTracking,
        lookupAccess: true,
        isVital: true,
        isNullable: true,
        reverseReference: 'document',
        dependsOn: ['date'],
    })
    readonly paymentTracking: Reference<xtremFinanceData.nodes.PaymentTracking | null>;

    // TODO: When the amountPaid will be stored instead of computed value, this (netBalance) property should be a getValue
    @decorators.decimalProperty<BaseDistributionDocument, 'netBalance'>({
        isPublished: true,
        isNullable: true,
        dataType: () => xtremMasterData.dataTypes.amountDataType,
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const amountPaid = (await (await this.paymentTracking)?.amountPaid) ?? 0;
            return (await this.totalAmountIncludingTax) - amountPaid;
        },
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
    })
    readonly netBalance: Promise<decimal | null>;

    /** depends on companyFxRate - companyFxRateDivisor - currency - companyCurrency */
    async convertAmountInCompanyCurrency(amount: decimal): Promise<number> {
        const companyFxRate = await this.companyFxRate;
        const companyFxRateDivisor = await this.companyFxRateDivisor;
        const currency = await this.currency;
        const companyCurrency = await this.companyCurrency;

        return xtremMasterData.sharedFunctions.convertAmount(
            amount,
            companyFxRate,
            companyFxRateDivisor,
            await currency.decimalDigits,
            await companyCurrency.decimalDigits,
        );
    }

    async getCompanyFxRate(): Promise<number> {
        return (await this.companyFxRate) !== 1.0 ? this.companyFxRate : 1.0 / (await this.companyFxRateDivisor);
    }
}
