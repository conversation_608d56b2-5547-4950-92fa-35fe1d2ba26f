import type { Collection, Context, Reference } from '@sage/xtrem-core';
import { decorators, TextStream, useDefaultValue } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import { noop } from 'lodash';
import * as xtremDistribution from '..';
import { isPostedErrorClosed } from '../functions/common';
import { BaseInboundDocument } from './base-inbound-document';

@decorators.subNode<BaseInboundReceiptDocument>({
    isAbstract: true,
    isPublished: true,
    extends: () => BaseInboundDocument,
    async controlBegin(cx) {
        await xtremDistribution.events.checkWrongTaxType(cx, this);
    },
    async controlEnd(cx) {
        await xtremDistribution.events.sameCompanyForSiteAndStockSite(this, cx);
    },
    async controlDelete(cx) {
        await xtremDistribution.events.checkStatusDeletion(this, cx);
    },
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentHeaderWithPostToStock>()
export class BaseInboundReceiptDocument
    extends BaseInboundDocument
    implements
        xtremFinanceData.interfaces.FinanceOriginDocumentHeader,
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentHeader<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    @decorators.enumProperty<BaseInboundReceiptDocument, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(
                this as unknown as xtremStockData.interfaces.DocumentHeaderWithStockPosting,
            );
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referencePropertyOverride<BaseInboundReceiptDocument, 'stockSite'>({
        /* isNullable: false,  */
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.referencePropertyOverride<BaseInboundReceiptDocument, 'businessRelation'>({
        node: () => xtremMasterData.nodes.Supplier,
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.textStreamPropertyOverride<BaseInboundReceiptDocument, 'internalNote'>({
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation).internalNote;
        },
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly internalNote: Promise<TextStream>;

    @decorators.textStreamPropertyOverride<BaseInboundReceiptDocument, 'externalNote'>({
        dependsOn: ['status'],
        duplicatedValue: useDefaultValue,
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly externalNote: Promise<TextStream>;

    @decorators.booleanPropertyOverride<BaseInboundReceiptDocument, 'isExternalNote'>({
        dependsOn: ['status'],
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isExternalNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<BaseInboundReceiptDocument, 'isTransferHeaderNote'>({
        dependsOn: ['status'],
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanPropertyOverride<BaseInboundReceiptDocument, 'isTransferLineNote'>({
        dependsOn: ['status'],
        async isFrozen() {
            return isPostedErrorClosed(await this.status);
        },
    })
    override readonly isTransferLineNote: Promise<boolean>;

    @decorators.collectionPropertyOverride<BaseInboundReceiptDocument, 'lines'>({
        node: () => xtremDistribution.nodes.BaseInboundReceiptDocumentLine,
    })
    override readonly lines: Collection<xtremDistribution.nodes.BaseInboundReceiptDocumentLine>;

    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    get landedCostAssignableLines() {
        return this.lines;
    }

    /**
     * Method that triggers the accounting engine for a given purchase receipt
     * @param context
     * @param reference to a purchase receipt
     */
    @decorators.mutation<typeof BaseInboundReceiptDocument, 'post'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'receipt',
                type: 'reference',
                isMandatory: true,
                node: () => BaseInboundReceiptDocument,
            },
        ],
        return: { type: 'boolean' },
    })
    static post(context: Context, receipt: BaseInboundReceiptDocument): Promise<boolean> {
        return receipt.executePost(context);
    }

    // eslint-disable-next-line class-methods-use-this
    executePost(this: BaseInboundReceiptDocument, context: Context): Promise<boolean> {
        noop(context);
        return Promise.resolve(true);
    }
}
