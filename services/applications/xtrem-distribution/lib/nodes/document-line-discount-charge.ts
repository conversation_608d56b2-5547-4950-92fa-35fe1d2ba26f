import type { decimal, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDistribution from '..';

@decorators.subNode<DocumentLineDiscountCharge>({
    isPublished: true,
    isVitalCollectionChild: true,
    canRead: true,
    extends: () => xtremMasterData.nodes.BaseLineDiscountCharge,
})
export class DocumentLineDiscountCharge extends xtremMasterData.nodes.BaseLineDiscountCharge {
    @decorators.referenceProperty<DocumentLineDiscountCharge, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremDistribution.nodes.BaseDistributionDocumentLine,
    })
    override readonly document: Reference<xtremDistribution.nodes.BaseDistributionDocumentLine>;

    @decorators.decimalPropertyOverride<DocumentLineDiscountCharge, 'basis'>({
        dependsOn: [{ document: ['grossPrice'] }],
        async defaultValue() {
            return (await this.document).grossPrice;
        },
        updatedValue: useDefaultValue,
    })
    override readonly basis: Promise<decimal>;

    override async calculateAmount(): Promise<number> {
        return super.calculateAmount(
            await xtremMasterData.functions.getCompanyPriceScale(await (await (await this.document).site).legalCompany),
        );
    }
}
