import type { Collection, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import type { decimal } from '@sage/xtrem-decimal';
import * as xtremLandedCost from '@sage/xtrem-landed-cost';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremDistribution from '..';
import { BaseInboundDocumentLine } from './base-inbound-document-line';

@decorators.subNode<BaseInboundReceiptDocumentLine>({
    isAbstract: true,
    extends: () => BaseInboundDocumentLine,
})
@xtremStockData.functions.staticImplements<xtremStockData.interfaces.StaticDocumentLineWithStockPosting>()
export class BaseInboundReceiptDocumentLine
    extends BaseInboundDocumentLine
    implements
        xtremLandedCost.interfaces
            .LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.transaction>
{
    @decorators.referencePropertyOverride<BaseInboundReceiptDocumentLine, 'document'>({
        node: () => xtremDistribution.nodes.BaseInboundReceiptDocument,
    })
    override readonly document: Reference<xtremDistribution.nodes.BaseInboundReceiptDocument>;

    @decorators.referencePropertyOverride<BaseInboundReceiptDocumentLine, 'stockSite'>({
        dependsOn: [{ document: ['status'] }, 'status', 'site'],
        updatedValue: useDefaultValue,
    })
    override readonly stockSite: Reference<xtremSystem.nodes.Site>;

    @decorators.enumProperty<BaseInboundReceiptDocumentLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue: 'draft',
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.decimalProperty<BaseInboundReceiptDocumentLine, 'stockCostAmountInCompanyCurrency'>({
        isStoredOutput: true,
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'item', { document: ['stockSite'] }],
        updatedValue: useDefaultValue,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        async computeValue() {
            return xtremDistribution.functions.getStockCostAmountInCompanyCurrency(
                await this.document,
                await this.item,
                await this.quantityInStockUnit,
            );
        },
    })
    readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<BaseInboundReceiptDocumentLine, 'stockCostAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: [
            'stockCostAmountInCompanyCurrency',
            {
                document: ['stockSite', 'currency', 'companyFxRateDivisor', 'companyFxRate'],
            },
        ],
        lookupAccess: true,
        async computeValue() {
            return xtremDistribution.functions.getStockCostAmount(
                await this.document,
                await this.stockCostAmountInCompanyCurrency,
            );
        },
    })
    readonly stockCostAmount: Promise<decimal>;

    @decorators.decimalProperty<BaseInboundReceiptDocumentLine, 'stockCostUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.orderCostDataType,
        dependsOn: ['quantityInStockUnit', 'stockCostAmount'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const quantityInStockUnit = await this.quantityInStockUnit;
            return quantityInStockUnit ? (await this.stockCostAmount) / quantityInStockUnit : 0;
        },
    })
    readonly stockCostUnit: Promise<decimal>;

    @decorators.referencePropertyOverride<BaseInboundReceiptDocumentLine, 'unit'>({
        dependsOn: ['item'],
        async defaultValue() {
            const itemSupplier = await this.itemSupplier;
            if (itemSupplier && (await itemSupplier.isActive)) {
                return itemSupplier.purchaseUnitOfMeasure;
            }
            const item = await this.item;
            return (await item?.purchaseUnit) ?? item.stockUnit;
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referenceProperty<BaseInboundReceiptDocumentLine, 'supplier'>({
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Supplier,
        dependsOn: [{ document: ['businessRelation'] }],
        async getValue() {
            return (await this.document).businessRelation;
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier | null>;

    private async computeNetPrice(): Promise<decimal> {
        const grossPrice = await this.grossPrice;
        const company = await (await this.site).legalCompany;
        return grossPrice > 0 && company
            ? xtremMasterData.functions.calculateNetPrice(
                  this.discountCharges,
                  grossPrice,
                  await this.quantity,
                  await xtremMasterData.functions.getCompanyPriceScale(company),
              )
            : 0;
    }

    @decorators.decimalPropertyOverride<BaseInboundReceiptDocumentLine, 'netPrice'>({
        dependsOn: ['grossPrice', 'quantity', 'discountCharges', { discountCharges: ['sign'] }, 'currency'],
        defaultValue() {
            return this.computeNetPrice();
        },
        duplicatedValue: useDefaultValue,
        updatedValue: useDefaultValue,
    })
    override readonly netPrice: Promise<decimal>;

    @decorators.referenceProperty<BaseInboundReceiptDocumentLine, 'purchaseUnit'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        getValue() {
            return this.unit;
        },
        async setValue(unit) {
            await this.$.set({ unit });
        },
    })
    readonly purchaseUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.referencePropertyOverride<BaseInboundReceiptDocumentLine, 'currency'>({
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<BaseInboundReceiptDocumentLine, 'companyCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await this.document).companyCurrency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<BaseInboundReceiptDocumentLine, 'company'>({
        isPublished: true,
        node: () => xtremSystem.nodes.Company,
        dependsOn: ['site'],
        async getValue() {
            return (await this.site).legalCompany;
        },
    })
    readonly company: Reference<xtremSystem.nodes.Company>;

    @decorators.decimalProperty<BaseInboundReceiptDocumentLine, 'amountForLandedCostAllocation'>({
        isPublished: true,
        dependsOn: ['amountExcludingTaxInCompanyCurrency'],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        getValue() {
            return this.amountExcludingTaxInCompanyCurrency;
        },
    })
    amountForLandedCostAllocation: Promise<decimal>;

    @decorators.decimalProperty<BaseInboundReceiptDocumentLine, 'quantityInStockUnitForLandedCostAllocation'>({
        dependsOn: ['quantityInStockUnit'],
        serviceOptions: () => [xtremMasterData.serviceOptions.landedCostOption],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return this.quantityInStockUnit;
        },
    })
    readonly quantityInStockUnitForLandedCostAllocation: Promise<decimal>;

    @decorators.collectionProperty<BaseInboundReceiptDocumentLine, 'stockTransactions'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;

    // eslint-disable-next-line class-methods-use-this
    getOrderDocumentLine(): Promise<
        | xtremLandedCost.interfaces.LandedCostAllocatedDocumentLine<xtremLandedCost.enums.LandedCostDocumentTypeEnum.order>
        | undefined
    > {
        return Promise.resolve(undefined);
    }
}
