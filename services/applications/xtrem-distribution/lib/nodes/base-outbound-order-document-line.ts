import type { Collection, date, decimal, integer, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremDistribution from '..';
import {
    automaticAllocationInProgress,
    checkIfSameShippingStatus,
    deleteNotAllowedAutomaticAllocationInProgress,
    deleteNotAllowedIfShipped,
    deleteNotAllowedIfStockAllocated,
} from '../events/controls/outbound-order-document-line';
import { BaseOutboundDocumentLine } from './base-outbound-document-line';

@decorators.subNode<BaseOutboundOrderDocumentLine>({
    isAbstract: true,
    extends: () => BaseOutboundDocumentLine,
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.added) {
            await checkIfSameShippingStatus(cx, this);
        }
        await automaticAllocationInProgress(cx, this);
    },
    async controlDelete(cx) {
        if ((await this.shippingStatus) !== 'notShipped') {
            await deleteNotAllowedIfShipped(cx, this);
            await deleteNotAllowedIfStockAllocated(cx, this);
            await deleteNotAllowedAutomaticAllocationInProgress(cx, this);
        }
    },
})
export class BaseOutboundOrderDocumentLine extends BaseOutboundDocumentLine {
    @decorators.referencePropertyOverride<BaseOutboundOrderDocumentLine, 'document'>({
        node: () => xtremDistribution.nodes.BaseOutboundOrderDocument,
    })
    override readonly document: Reference<xtremDistribution.nodes.BaseOutboundOrderDocument>;

    @decorators.referenceProperty<BaseOutboundOrderDocumentLine, 'stockSiteAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Address,
    })
    readonly stockSiteAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<BaseOutboundOrderDocumentLine, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['shipToCustomerAddress'] }],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.document).shipToCustomerAddress;
        },
        updatedValue: useDefaultValue,
        filters: {
            control: {
                async businessEntity() {
                    return (await (await this.document).shipToCustomer).businessEntity;
                },
                deliveryDetail: { _ne: null },
            },
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<BaseOutboundOrderDocumentLine, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Address,
        dependsOn: [{ document: ['shipToAddress'] }],
        async defaultValue() {
            return (await this.document)?.shipToAddress;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<BaseOutboundOrderDocumentLine, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.Contact,
        dependsOn: [{ document: ['shipToCustomerAddress', 'shipToContact'] }],
        async defaultValue() {
            return (await this.document)?.shipToContact;
        },
        updatedValue: useDefaultValue,
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.integerProperty<BaseOutboundOrderDocumentLine, 'deliveryLeadTime'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['deliveryLeadTime'] }],
        async defaultValue() {
            return (await this.document)?.deliveryLeadTime;
        },
        updatedValue: useDefaultValue,
    })
    readonly deliveryLeadTime: Promise<integer>;

    @decorators.dateProperty<BaseOutboundOrderDocumentLine, 'doNotShipBeforeDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: [{ document: ['doNotShipBeforeDate'] }],
        async defaultValue() {
            return (await this.document)?.doNotShipBeforeDate;
        },
        updatedValue: useDefaultValue,
    })
    readonly doNotShipBeforeDate: Promise<date | null>;

    @decorators.dateProperty<BaseOutboundOrderDocumentLine, 'doNotShipAfterDate'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: [{ document: ['doNotShipAfterDate'] }],
        async defaultValue() {
            return (await this.document)?.doNotShipAfterDate;
        },
        updatedValue: useDefaultValue,
    })
    readonly doNotShipAfterDate: Promise<date | null>;

    @decorators.dateProperty<BaseOutboundOrderDocumentLine, 'expectedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shippingDate', 'deliveryLeadTime', { document: ['workDays'] }],
        lookupAccess: true,
    })
    readonly expectedDeliveryDate: Promise<date>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'stockOnHand'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['itemSite'],
        async getValue() {
            const itemSite = await this.itemSite;
            return itemSite?.inStockQuantity ?? 0;
        },
    })
    readonly stockOnHand: Promise<decimal>;

    @decorators.enumProperty<BaseOutboundOrderDocumentLine, 'allocationRequestStatus'>({
        isPublished: true,
        isStored: true,
        defaultValue: 'noRequest',
        duplicatedValue: 'noRequest',
        dataType: () => xtremStockData.enums.allocationRequestStatusDataType,
        lookupAccess: true,
    })
    readonly allocationRequestStatus: Promise<xtremStockData.enums.AllocationRequestStatus>;

    @decorators.referenceProperty<BaseOutboundOrderDocumentLine, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['deliveryMode'] }],
        node: () => xtremMasterData.nodes.DeliveryMode,
        dataType: () => xtremMasterData.dataTypes.deliveryMode,
        async defaultValue() {
            return (await this.document)?.deliveryMode;
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode>;

    @decorators.dateProperty<BaseOutboundOrderDocumentLine, 'shippingDate'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        updatedValue: useDefaultValue,
    })
    readonly shippingDate: Promise<date>;

    @decorators.collectionProperty<BaseOutboundOrderDocumentLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'stockCostAmountInCompanyCurrency'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'item', { document: ['stockSite'] }],
        async computeValue() {
            return xtremDistribution.functions.getStockCostAmountInCompanyCurrency(
                await this.document,
                await this.item,
                await this.quantityInStockUnit,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'stockCostAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: [
            'stockCostAmountInCompanyCurrency',
            {
                document: ['stockSite', 'currency', 'companyFxRateDivisor', 'companyFxRate'],
            },
        ],
        lookupAccess: true,
        async computeValue() {
            return xtremDistribution.functions.getStockCostAmount(
                await this.document,
                await this.stockCostAmountInCompanyCurrency,
            );
        },
    })
    readonly stockCostAmount: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'stockCostUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.orderCostDataType,
        dependsOn: ['quantityInStockUnit', 'stockCostAmount'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const quantityInStockUnit = await this.quantityInStockUnit;
            return quantityInStockUnit ? (await this.stockCostAmount) / quantityInStockUnit : 0;
        },
    })
    readonly stockCostUnit: Promise<decimal>;

    @decorators.enumProperty<BaseOutboundOrderDocumentLine, 'shippingStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremDistribution.enums.shippingStatusDataType,
        defaultValue: 'notShipped',
        lookupAccess: true,
    })
    readonly shippingStatus: Promise<xtremDistribution.enums.ShippingStatus>;

    @decorators.dateProperty<BaseOutboundOrderDocumentLine, 'requestedDeliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: [{ document: ['requestedDeliveryDate'] }],
        async defaultValue() {
            return (await this.document)?.requestedDeliveryDate;
        },
    })
    readonly requestedDeliveryDate: Promise<date>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'availableQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockUnit', 'item', 'stockSite'],
        lookupAccess: true,
        async computeValue() {
            if ((await this.item) && (await this.stockSite)) {
                return xtremDistribution.functions.getItemSiteAvailableStockQuantity(
                    this.$.context,
                    await this.item,
                    (await this.stockSite) || (await this.site),
                );
            }
            return 0;
        },
    })
    readonly availableQuantityInStockUnit: Promise<decimal>;

    // TODO: quantityAllocated and remainingQuantityToAllocate are necessary to implement DocumentLineWithStockAllocation,
    // they are both already in StockUnit but their names are wrong
    // these 2 properties have to be removed when it will be fixed in the stock allocation
    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'quantityAllocated'>({
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityAllocatedInStockUnit'],
        getValue() {
            return this.quantityAllocatedInStockUnit;
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    // TODO: quantityAllocated and remainingQuantityToAllocate are necessary to implement DocumentLineWithStockAllocation,
    // they are both already in StockUnit but their names are wrong
    // these 2 properties have to be removed when it will be fixed in the stock allocation
    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'remainingQuantityToAllocate'>({
        dependsOn: ['remainingQuantityToAllocateInStockUnit'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return this.remainingQuantityToAllocateInStockUnit;
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'quantityAllocatedInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockAllocations'],
        lookupAccess: true,
        getValue() {
            return this.stockAllocations.sum(allocation => allocation.quantityInStockUnit);
        },
    })
    readonly quantityAllocatedInStockUnit: Promise<decimal>;

    @decorators.enumProperty<BaseOutboundOrderDocumentLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocateInStockUnit', 'quantityAllocatedInStockUnit', 'item'],
        async getValue() {
            const item = await this.item;
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocateInStockUnit,
                await this.quantityAllocatedInStockUnit,
                await item.isStockManaged,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'remainingQuantityToAllocateInStockUnit'>({
        isPublished: true,
        dependsOn: ['remainingQuantityToShipInStockUnit', 'quantityAllocatedInStockUnit'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.quantityInStockUnit) - (await this.quantityAllocatedInStockUnit);
        },
    })
    readonly remainingQuantityToAllocateInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'stockShortageInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['stockUnit', 'quantityInStockUnit', 'availableQuantityInStockUnit', 'quantityAllocatedInStockUnit'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const availableQuantityInStockUnit = await this.availableQuantityInStockUnit;
            const quantityInStockUnit = await this.quantityInStockUnit;
            const quantityAllocatedInStockUnit = await this.quantityAllocatedInStockUnit;
            if (availableQuantityInStockUnit + quantityAllocatedInStockUnit < quantityInStockUnit) {
                return quantityInStockUnit - (availableQuantityInStockUnit + quantityAllocatedInStockUnit);
            }
            return 0;
        },
    })
    readonly stockShortageInStockUnit: Promise<decimal>;

    @decorators.booleanProperty<BaseOutboundOrderDocumentLine, 'stockShortageStatus'>({
        isPublished: true,
        dependsOn: ['stockShortageInStockUnit'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            return (await this.stockShortageInStockUnit) > 0;
        },
    })
    readonly stockShortageStatus: Promise<boolean>;

    @decorators.decimalProperty<BaseOutboundOrderDocumentLine, 'remainingQuantityToShipInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        lookupAccess: true,
        getValue() {
            return 0;
        },
    })
    readonly remainingQuantityToShipInStockUnit: Promise<decimal>;
}
