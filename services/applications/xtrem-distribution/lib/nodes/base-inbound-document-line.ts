import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDistribution from '..';
import { BaseDistributionDocumentLine } from './base-distribution-document-line';

@decorators.subNode<BaseInboundDocumentLine>({
    isAbstract: true,
    isPublished: true,
    extends: () => BaseDistributionDocumentLine,
})
export class BaseInboundDocumentLine extends BaseDistributionDocumentLine {
    @decorators.referencePropertyOverride<BaseInboundDocumentLine, 'document'>({
        node: () => xtremDistribution.nodes.BaseInboundDocument,
    })
    override readonly document: Reference<xtremDistribution.nodes.BaseInboundDocument>;

    @decorators.referenceProperty<BaseInboundDocumentLine, 'itemSupplier'>({
        isPublished: true,
        dependsOn: ['item', { document: ['supplier'] }],
        isNullable: true,
        node: () => xtremMasterData.nodes.ItemSupplier,
        join: {
            item: 'item',
            async supplier() {
                return (await this.document).supplier;
            },
        },
    })
    readonly itemSupplier: Reference<xtremMasterData.nodes.ItemSupplier | null>;

    @decorators.referencePropertyOverride<BaseInboundDocumentLine, 'unit'>({
        dependsOn: ['item'],
        async defaultValue() {
            const itemSupplier = await this.itemSupplier;
            if (itemSupplier && (await itemSupplier.isActive)) {
                return itemSupplier.purchaseUnitOfMeasure;
            }
            const item = await this.item;
            return (await item?.purchaseUnit) ?? item.stockUnit;
        },
    })
    override readonly unit: Reference<xtremMasterData.nodes.UnitOfMeasure>;
}
