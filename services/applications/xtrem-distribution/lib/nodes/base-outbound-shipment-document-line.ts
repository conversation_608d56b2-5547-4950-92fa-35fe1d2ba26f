import type { Collection, decimal, Reference } from '@sage/xtrem-core';
import { Decimal, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremDistribution from '..';
import { BaseOutboundDocumentLine } from './base-outbound-document-line';

@decorators.subNode<BaseOutboundShipmentDocumentLine>({
    isAbstract: true,
    extends: () => BaseOutboundDocumentLine,
})
export class BaseOutboundShipmentDocumentLine extends BaseOutboundDocumentLine {
    @decorators.referencePropertyOverride<BaseOutboundShipmentDocumentLine, 'document'>({
        node: () => xtremDistribution.nodes.BaseOutboundShipmentDocument,
    })
    override readonly document: Reference<xtremDistribution.nodes.BaseOutboundShipmentDocument>;

    @decorators.collectionProperty<BaseOutboundShipmentDocumentLine, 'stockAllocations'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockAllocation,
    })
    readonly stockAllocations: Collection<xtremStockData.nodes.StockAllocation>;

    @decorators.collectionProperty<BaseOutboundShipmentDocumentLine, 'stockDetails'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.BaseStockDetail,
    })
    readonly stockDetails: Collection<xtremStockData.nodes.BaseStockDetail>;

    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'stockCostAmountInCompanyCurrency'>({
        isStoredOutput: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInCompanyCurrency,
        dependsOn: ['quantityInStockUnit', 'item', { document: ['stockSite'] }],
        async computeValue() {
            return xtremDistribution.functions.getStockCostAmountInCompanyCurrency(
                await this.document,
                await this.item,
                await this.quantityInStockUnit,
            );
        },
        updatedValue: useDefaultValue,
    })
    readonly stockCostAmountInCompanyCurrency: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'stockCostAmount'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.amountInTransactionCurrency,
        dependsOn: [
            'stockCostAmountInCompanyCurrency',
            {
                document: ['stockSite', 'currency', 'companyFxRateDivisor', 'companyFxRate'],
            },
        ],
        lookupAccess: true,
        async computeValue() {
            return xtremDistribution.functions.getStockCostAmount(
                await this.document,
                await this.stockCostAmountInCompanyCurrency,
            );
        },
    })
    readonly stockCostAmount: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'orderCost'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['itemSite', 'quantityInStockUnit'],
        dataType: () => xtremMasterData.dataTypes.costDataType,
        async control(ctx, val) {
            await ctx.error.if(val).is.negative();
        },
    })
    readonly orderCost: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'stockCostUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.orderCostDataType,
        dependsOn: ['quantityInStockUnit', 'stockCostAmount'],
        // eslint-disable-next-line @sage/xtrem/property-decorators-errors
        async computeValue() {
            const quantityInStockUnit = await this.quantityInStockUnit;
            return quantityInStockUnit ? (await this.stockCostAmount) / quantityInStockUnit : 0;
        },
    })
    readonly stockCostUnit: Promise<decimal>;

    @decorators.enumProperty<BaseOutboundShipmentDocumentLine, 'stockTransactionStatus'>({
        isStored: true,
        isPublished: true,
        defaultValue: 'draft',
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.enumProperty<BaseOutboundShipmentDocumentLine, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: ['remainingQuantityToAllocateInStockUnit', 'quantityAllocatedInStockUnit', 'item'],
        async getValue() {
            return xtremStockData.functions.allocationLib.getLineAllocationStatus(
                await this.remainingQuantityToAllocateInStockUnit,
                await this.quantityAllocatedInStockUnit,
                await (
                    await this.item
                ).isStockManaged,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'quantityAllocatedInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return this.stockAllocations.sum(allocation => allocation.quantityInStockUnit);
        },
    })
    readonly quantityAllocatedInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'remainingQuantityInStockUnit'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityInStockUnit', 'quantityAllocatedInStockUnit'],
        async computeValue() {
            return Number(Decimal.make(await this.quantityInStockUnit).sub(await this.quantityAllocatedInStockUnit));
        },
    })
    readonly remainingQuantityInStockUnit: Promise<decimal>;

    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'remainingQuantityToAllocateInStockUnit'>({
        isPublished: true,
        dependsOn: ['quantityInStockUnit', 'quantityAllocatedInStockUnit'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        async getValue() {
            return (await this.quantityInStockUnit) - (await this.quantityAllocatedInStockUnit);
        },
    })
    readonly remainingQuantityToAllocateInStockUnit: Promise<decimal>;

    // TODO: quantityAllocated and remainingQuantityToAllocate are necessary to implement DocumentLineWithStockAllocation,
    // they are both already in StockUnit but their names are wrong
    // these 2 properties have to be removed when it will be fixed in the stock allocation
    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'quantityAllocated'>({
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['quantityAllocatedInStockUnit'],
        getValue() {
            return this.quantityAllocatedInStockUnit;
        },
    })
    readonly quantityAllocated: Promise<decimal>;

    // TODO: quantityAllocated and remainingQuantityToAllocate are necessary to implement DocumentLineWithStockAllocation,
    // they are both already in StockUnit but their names are wrong
    // these 2 properties have to be removed when it will be fixed in the stock allocation
    @decorators.decimalProperty<BaseOutboundShipmentDocumentLine, 'remainingQuantityToAllocate'>({
        dependsOn: ['remainingQuantityToAllocateInStockUnit'],
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        getValue() {
            return this.remainingQuantityToAllocateInStockUnit;
        },
    })
    readonly remainingQuantityToAllocate: Promise<decimal>;

    @decorators.collectionProperty<BaseOutboundShipmentDocumentLine, 'stockMovements'>({
        isPublished: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockJournal,
    })
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;

    @decorators.collectionProperty<BaseOutboundShipmentDocumentLine, 'stockTransactions'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'documentLine',
        node: () => xtremStockData.nodes.StockTransaction,
    })
    readonly stockTransactions: Collection<xtremStockData.nodes.StockTransaction>;
}
