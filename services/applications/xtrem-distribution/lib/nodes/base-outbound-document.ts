import type { Collection, Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDistribution from '..';
import { cannotUpdateDocumentShipToCustomer } from '../events/controls/outbound-document';
import { BaseDistributionDocument } from './base-distribution-document';

/** sales document */
@decorators.subNode<BaseOutboundDocument>({
    isAbstract: true,
    isPublished: true,
    extends: () => BaseDistributionDocument,
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.modified) {
            await cannotUpdateDocumentShipToCustomer(cx, this);
        }
    },
})
export class BaseOutboundDocument extends BaseDistributionDocument {
    @decorators.referencePropertyOverride<BaseOutboundDocument, 'businessRelation'>({
        dependsOn: ['customer'],
        node: () => xtremMasterData.nodes.Customer,
        // isPublished:false, unpublish this property for all distribution documents
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referenceProperty<BaseOutboundDocument, 'customer'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Customer,
        dataType: () => xtremMasterData.dataTypes.customer,
        getValue() {
            return this.businessRelation;
        },
        async setValue(customer) {
            await this.$.set({ businessRelation: customer });
        },
    })
    readonly customer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.stringProperty<BaseOutboundDocument, 'customerNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly customerNumber: Promise<string | null>;

    @decorators.referenceProperty<BaseOutboundDocument, 'shipToCustomer'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['businessRelation'],
        node: () => xtremMasterData.nodes.Customer,
        defaultValue() {
            return this.businessRelation;
        },
        isFrozen() {
            return this.$.status === NodeStatus.modified;
        },
    })
    readonly shipToCustomer: Reference<xtremMasterData.nodes.Customer>;

    @decorators.referencePropertyOverride<BaseOutboundDocument, 'paymentTerm'>({
        filters: { control: { businessEntityType: { _in: ['customer', 'all'] } } },
    })
    override readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.referenceProperty<BaseOutboundDocument, 'incoterm'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.Incoterm,
        dataType: () => xtremMasterData.dataTypes.incoterm,
    })
    readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.collectionPropertyOverride<BaseOutboundDocument, 'lines'>({
        node: () => xtremDistribution.nodes.BaseOutboundDocumentLine,
    })
    override readonly lines: Collection<xtremDistribution.nodes.BaseOutboundDocumentLine>;
}
