import type { Collection, date, integer, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremDistribution from '..';
import { BaseOutboundDocument } from './base-outbound-document';

@decorators.subNode<BaseOutboundShipmentDocument>({
    isAbstract: true,
    isPublished: true,
    extends: () => BaseOutboundDocument,
})
export class BaseOutboundShipmentDocument extends BaseOutboundDocument {
    readonly linesWithStockTransactionStatusCollectionName = 'lines';

    @decorators.referenceProperty<BaseOutboundShipmentDocument, 'shipToCustomerAddress'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomer'],
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        async defaultValue() {
            return (await this.shipToCustomer)?.primaryShipToAddress;
        },
    })
    readonly shipToCustomerAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress>;

    @decorators.referenceProperty<BaseOutboundShipmentDocument, 'shipToAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        lookupAccess: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return (await this.shipToCustomerAddress)?.address;
        },
    })
    readonly shipToAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<BaseOutboundShipmentDocument, 'shipToContact'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Contact,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress)?.primaryContact)?.contact ?? null;
        },
    })
    readonly shipToContact: Reference<xtremMasterData.nodes.Contact | null>;

    @decorators.collectionPropertyOverride<BaseOutboundShipmentDocument, 'lines'>({
        node: () => xtremDistribution.nodes.BaseOutboundShipmentDocumentLine,
    })
    override readonly lines: Collection<xtremDistribution.nodes.BaseOutboundShipmentDocumentLine>;

    @decorators.referenceProperty<BaseOutboundShipmentDocument, 'deliveryMode'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.DeliveryMode,
        dataType: () => xtremMasterData.dataTypes.deliveryMode,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).deliveryDetail)?.mode || null;
        },
    })
    readonly deliveryMode: Reference<xtremMasterData.nodes.DeliveryMode>;

    @decorators.integerProperty<BaseOutboundShipmentDocument, 'deliveryLeadTime'>({
        isPublished: true,
        isStored: true,
        dependsOn: ['shipToCustomerAddress'],
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).deliveryDetail)?.leadTime ?? 0;
        },
    })
    readonly deliveryLeadTime: Promise<integer>;

    @decorators.integerProperty<BaseOutboundShipmentDocument, 'workDays'>({
        isPublished: true,
        excludedFromPayload: true,
        dependsOn: ['shipToCustomerAddress'],
        async computeValue() {
            const deliveryDetail = await (await this.shipToCustomerAddress).deliveryDetail;
            if (deliveryDetail) {
                return xtremDistribution.functions.computeWorkDays(deliveryDetail);
            }
            return 0;
        },
    })
    readonly workDays: Promise<integer>;

    @decorators.dateProperty<BaseOutboundShipmentDocument, 'deliveryDate'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dependsOn: ['date', 'deliveryLeadTime', 'workDays'],
        async defaultValue() {
            return xtremDistribution.functions.addWorkDays(
                await this.date,
                await this.deliveryLeadTime,
                await this.workDays,
            );
        },
    })
    readonly deliveryDate: Promise<date>;

    @decorators.enumProperty<BaseOutboundShipmentDocument, 'allocationStatus'>({
        isPublished: true,
        dataType: () => xtremStockData.enums.stockAllocationStatusDataType,
        dependsOn: [{ lines: ['allocationStatus'] }],
        computeValue() {
            return xtremStockData.functions.allocationLib.computeHeaderAllocationStatus(
                this as unknown as xtremStockData.interfaces.DocumentHeaderWithStockAllocation,
            );
        },
    })
    readonly allocationStatus: Promise<xtremStockData.enums.StockAllocationStatus>;

    @decorators.enumProperty<BaseOutboundShipmentDocument, 'stockTransactionStatus'>({
        isPublished: true,
        dependsOn: [{ lines: ['stockTransactionStatus'] }],
        dataType: () => xtremStockData.enums.StockDocumentTransactionStatusDataType,
        computeValue() {
            return xtremStockData.functions.stockTransactionLib.computeHeaderStatus(
                this as unknown as xtremStockData.interfaces.DocumentHeaderWithStockPosting,
            );
        },
    })
    readonly stockTransactionStatus: Promise<xtremStockData.enums.StockDocumentTransactionStatus>;

    @decorators.referencePropertyOverride<BaseOutboundShipmentDocument, 'incoterm'>({
        dependsOn: ['shipToCustomerAddress'],
        node: () => xtremMasterData.nodes.Incoterm,
        async defaultValue() {
            return (await (await this.shipToCustomerAddress).deliveryDetail)?.incoterm || null;
        },
    })
    override readonly incoterm: Reference<xtremMasterData.nodes.Incoterm | null>;

    @decorators.stringProperty<BaseOutboundShipmentDocument, 'trackingNumber'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.mediumString,
    })
    readonly trackingNumber: Promise<string>;
}
