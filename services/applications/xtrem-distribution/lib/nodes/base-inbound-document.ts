import type { Collection, Reference } from '@sage/xtrem-core';
import { decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDistribution from '..';
import { BaseDistributionDocument } from './base-distribution-document';

/** Purchase documents */
@decorators.subNode<BaseInboundDocument>({
    isAbstract: true,
    isPublished: true,
    extends: () => BaseDistributionDocument,
})
export class BaseInboundDocument extends BaseDistributionDocument {
    @decorators.referencePropertyOverride<BaseInboundDocument, 'businessRelation'>({
        dependsOn: ['supplier'],
        node: () => xtremMasterData.nodes.Supplier,
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<BaseInboundDocument, 'supplier'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        getValue() {
            return this.businessRelation;
        },
        async setValue(supplier) {
            await this.$.set({ businessRelation: supplier });
        },
    })
    readonly supplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<BaseInboundDocument, 'billBySupplier'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Supplier,
        lookupAccess: true,
        dependsOn: ['businessRelation'],
        async defaultValue() {
            return (await this.businessRelation).billBySupplier;
        },
        duplicatedValue: useDefaultValue,
    })
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.referenceProperty<BaseInboundDocument, 'supplierAddress'>({
        isPublished: true,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['businessRelation'],
        node: () => xtremMasterData.nodes.Address,
        async defaultValue() {
            return (await (await this.businessRelation).primaryAddress)?.address ?? null;
        },
        updatedValue: useDefaultValue,
    })
    readonly supplierAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referencePropertyOverride<BaseInboundDocument, 'paymentTerm'>({
        filters: { control: { businessEntityType: { _in: ['supplier', 'all'] } } },
    })
    override readonly paymentTerm: Reference<xtremMasterData.nodes.PaymentTerm>;

    @decorators.collectionPropertyOverride<BaseInboundDocument, 'lines'>({
        node: () => xtremDistribution.nodes.BaseInboundDocumentLine,
    })
    override readonly lines: Collection<xtremDistribution.nodes.BaseInboundDocumentLine>;
}
