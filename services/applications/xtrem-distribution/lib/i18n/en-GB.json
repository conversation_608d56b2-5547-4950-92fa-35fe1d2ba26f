{"@sage/xtrem-distribution/base_inbound_receipt_document_line__bad_conversion_factor": "The converted quantity: {{convertedQuantity}}, from quantity: {{quantity}} in unit: {{unit}}\n             to quantity in stock unit: {{stockUnit}}, using the conversion factor: {{conversionFactor}},\n             is different from the current quantity in stock unit: {{quantityInStockUnit}}.", "@sage/xtrem-distribution/base_inbound_receipt_document_line__same_company_site_stock_site": "The line site and the line stock site need to have the same company.", "@sage/xtrem-distribution/base_inbound_receipt_document_line__same_site_header_and_line": "The line site and the header site need to be the same.", "@sage/xtrem-distribution/data_types__base_price_origin_enum__name": "Base price origin enum", "@sage/xtrem-distribution/data_types__document_close_status_method_return_enum__name": "Document close status method return enum", "@sage/xtrem-distribution/data_types__document_confirm_status_method_return_enum__name": "Document confirm status method return enum", "@sage/xtrem-distribution/data_types__document_open_status_method_return_enum__name": "Document open status method return enum", "@sage/xtrem-distribution/data_types__inbound_display_status_enum__name": "Inbound display status enum", "@sage/xtrem-distribution/data_types__invoice_status_enum__name": "Invoice status enum", "@sage/xtrem-distribution/data_types__receiving_status_enum__name": "Receiving status enum", "@sage/xtrem-distribution/data_types__shipping_status_enum__name": "Shipping status enum", "@sage/xtrem-distribution/enums__base_price_origin__basePrice": "Base price", "@sage/xtrem-distribution/enums__base_price_origin__customerPriceList": "Customer price list", "@sage/xtrem-distribution/enums__base_price_origin__manual": "Manual", "@sage/xtrem-distribution/enums__base_price_origin__priceList": "Price list", "@sage/xtrem-distribution/enums__base_price_origin__supplierPriceList": "Supplier price list", "@sage/xtrem-distribution/enums__document_close_status_method_return__isAlreadyClosed": "Is already closed", "@sage/xtrem-distribution/enums__document_close_status_method_return__isNowClosed": "Is now closed", "@sage/xtrem-distribution/enums__document_close_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-distribution/enums__document_confirm_status_method_return__isConfirmed": "Is confirmed", "@sage/xtrem-distribution/enums__document_confirm_status_method_return__isNotDraft": "Is not draft", "@sage/xtrem-distribution/enums__document_confirm_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-distribution/enums__document_open_status_method_return__isAlreadyOpen": "Is already open", "@sage/xtrem-distribution/enums__document_open_status_method_return__isNowOpen": "Is now open", "@sage/xtrem-distribution/enums__document_open_status_method_return__isShipped": "Is shipped", "@sage/xtrem-distribution/enums__document_open_status_method_return__parametersAreIncorrect": "Parameters are incorrect", "@sage/xtrem-distribution/enums__inbound_display_status__approved": "Approved", "@sage/xtrem-distribution/enums__inbound_display_status__closed": "Closed", "@sage/xtrem-distribution/enums__inbound_display_status__confirmed": "Confirmed", "@sage/xtrem-distribution/enums__inbound_display_status__credited": "Credited", "@sage/xtrem-distribution/enums__inbound_display_status__draft": "Draft", "@sage/xtrem-distribution/enums__inbound_display_status__error": "Error", "@sage/xtrem-distribution/enums__inbound_display_status__invoiced": "Invoiced", "@sage/xtrem-distribution/enums__inbound_display_status__noVariance": "No variance", "@sage/xtrem-distribution/enums__inbound_display_status__ordered": "Ordered", "@sage/xtrem-distribution/enums__inbound_display_status__paid": "Paid", "@sage/xtrem-distribution/enums__inbound_display_status__partiallyCredited": "Partially credited", "@sage/xtrem-distribution/enums__inbound_display_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-distribution/enums__inbound_display_status__partiallyOrdered": "Partially ordered", "@sage/xtrem-distribution/enums__inbound_display_status__partiallyPaid": "Partially paid", "@sage/xtrem-distribution/enums__inbound_display_status__partiallyReceived": "Partially received", "@sage/xtrem-distribution/enums__inbound_display_status__partiallyReturned": "Partially returned", "@sage/xtrem-distribution/enums__inbound_display_status__pendingApproval": "Pending approval", "@sage/xtrem-distribution/enums__inbound_display_status__posted": "Posted", "@sage/xtrem-distribution/enums__inbound_display_status__postingError": "Posting error", "@sage/xtrem-distribution/enums__inbound_display_status__postingInProgress": "Posting in progress", "@sage/xtrem-distribution/enums__inbound_display_status__received": "Received", "@sage/xtrem-distribution/enums__inbound_display_status__rejected": "Rejected", "@sage/xtrem-distribution/enums__inbound_display_status__returned": "Returned", "@sage/xtrem-distribution/enums__inbound_display_status__stockError": "Stock error", "@sage/xtrem-distribution/enums__inbound_display_status__taxCalculationFailed": "Tax calculation failed", "@sage/xtrem-distribution/enums__inbound_display_status__variance": "<PERSON><PERSON><PERSON>", "@sage/xtrem-distribution/enums__inbound_display_status__varianceApproved": "<PERSON><PERSON><PERSON> approved", "@sage/xtrem-distribution/enums__invoice_status__invoiced": "Invoiced", "@sage/xtrem-distribution/enums__invoice_status__notInvoiced": "Not invoiced", "@sage/xtrem-distribution/enums__invoice_status__partiallyInvoiced": "Partially invoiced", "@sage/xtrem-distribution/enums__receiving_status__notReceived": "Not received", "@sage/xtrem-distribution/enums__receiving_status__partiallyReceived": "Partially received", "@sage/xtrem-distribution/enums__receiving_status__received": "Received", "@sage/xtrem-distribution/enums__shipping_status__notShipped": "Not shipped", "@sage/xtrem-distribution/enums__shipping_status__partiallyShipped": "Partially shipped", "@sage/xtrem-distribution/enums__shipping_status__shipped": "Shipped", "@sage/xtrem-distribution/function__sub_work_days__inconsistent_doNoShip_dates": "The do-not-ship-before date needs to be before the do-not-ship-after date.", "@sage/xtrem-distribution/function__sub_work_days__unable_to_find_a_work_day": "No shipping date found. You need to select a customer address with working days.", "@sage/xtrem-distribution/functions__date_check_control__discount_payment_before_date_later_than_due_date": "The discount date needs to be on or before the due date.", "@sage/xtrem-distribution/functions__date_check_control__validation_failed_safe_retry": "Safe retry validation failed. The discount date needs to be on or before the due date.", "@sage/xtrem-distribution/nodes__base_distribution_document__control_sites_legal_companies": "This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.", "@sage/xtrem-distribution/nodes__base_distribution_document__fx_rate_not_found": "No exchange rate found.", "@sage/xtrem-distribution/nodes__base_distribution_document__node_name": "Base distribution document", "@sage/xtrem-distribution/nodes__base_distribution_document__property__businessRelation": "Business relation", "@sage/xtrem-distribution/nodes__base_distribution_document__property__companyFxRate": "Company exchange rate", "@sage/xtrem-distribution/nodes__base_distribution_document__property__companyFxRateDivisor": "Company exchange rate divisor", "@sage/xtrem-distribution/nodes__base_distribution_document__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-distribution/nodes__base_distribution_document__property__fxRateDate": "Exchange rate date", "@sage/xtrem-distribution/nodes__base_distribution_document__property__invoiceStatus": "Invoice status", "@sage/xtrem-distribution/nodes__base_distribution_document__property__lines": "Lines", "@sage/xtrem-distribution/nodes__base_distribution_document__property__netBalance": "Net balance", "@sage/xtrem-distribution/nodes__base_distribution_document__property__paymentTerm": "Payment term", "@sage/xtrem-distribution/nodes__base_distribution_document__property__paymentTracking": "Payment tracking", "@sage/xtrem-distribution/nodes__base_distribution_document__property__rateDescription": "Rate description", "@sage/xtrem-distribution/nodes__base_distribution_document__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-distribution/nodes__base_distribution_document__property__taxEngine": "Tax engine", "@sage/xtrem-distribution/nodes__base_distribution_document__property__taxes": "Taxes", "@sage/xtrem-distribution/nodes__base_distribution_document__property__totalAmountExcludingTax": "Total amount excluding VAT", "@sage/xtrem-distribution/nodes__base_distribution_document__property__totalAmountExcludingTaxInCompanyCurrency": "Total amount excluding VAT in company currency", "@sage/xtrem-distribution/nodes__base_distribution_document__property__totalAmountIncludingTax": "Total amount including VAT", "@sage/xtrem-distribution/nodes__base_distribution_document__property__totalExemptAmount": "Total exempt amount", "@sage/xtrem-distribution/nodes__base_distribution_document__property__totalTaxableAmount": "Total taxable amount", "@sage/xtrem-distribution/nodes__base_distribution_document__property__totalTaxAmount": "Total VAT amount", "@sage/xtrem-distribution/nodes__base_distribution_document__property__totalTaxAmountAdjusted": "Total VAT amount adjusted", "@sage/xtrem-distribution/nodes__base_distribution_document_line__line_must_have_the_same_status": "The line status needs to be the same as the transfer status.", "@sage/xtrem-distribution/nodes__base_distribution_document_line__node_name": "Base distribution document line", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__amountExcludingTax": "Amount excluding VAT", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__amountExcludingTaxInCompanyCurrency": "Amount excluding VAT in company currency", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__amountIncludingTax": "Amount including VAT", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__amountIncludingTaxInCompanyCurrency": "Amount including VAT in company currency", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__canHaveLandedCost": "Can have landed cost", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__canHaveLandedCostLine": "Can have landed cost line", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__discountCharges": "Discount charges", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__document": "Document", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__exemptAmount": "Exempt amount", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__grossPrice": "Gross price", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__landedCost": "Landed cost", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__landedCostLines": "Landed cost lines", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__lineStatus": "Line status", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__netPrice": "Net price", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__priceOrigin": "Price origin", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__signedAmountExcludingTax": "Signed amount excluding VAT", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__signedAmountExcludingTaxInCompanyCurrency": "Signed amount excluding VAT in company currency", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__signedLineAmountExcludingTax": "Signed line amount excluding VAT", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__signedLineAmountExcludingTaxInCompanyCurrency": "Signed line amount excluding VAT in company currency", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__signedQuantity": "Signed quantity", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__taxableAmount": "Taxable amount", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__taxAmount": "VAT amount", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__taxAmountAdjusted": "VAT amount adjusted", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__taxCalculationStatus": "Tax calculation status", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__taxDate": "Tax date", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__taxes": "Taxes", "@sage/xtrem-distribution/nodes__base_distribution_document_line__property__text": "Text", "@sage/xtrem-distribution/nodes__base_distribution_document_line__stock_site_must_have_the_same_value": "The stock site on the line needs to be the same as the stock site for the transfer.", "@sage/xtrem-distribution/nodes__base_inbound_document__node_name": "Base inbound document", "@sage/xtrem-distribution/nodes__base_inbound_document__property__billBySupplier": "Bill-by supplier", "@sage/xtrem-distribution/nodes__base_inbound_document__property__businessRelation": "Business relation", "@sage/xtrem-distribution/nodes__base_inbound_document__property__lines": "Lines", "@sage/xtrem-distribution/nodes__base_inbound_document__property__paymentTerm": "Payment term", "@sage/xtrem-distribution/nodes__base_inbound_document__property__supplier": "Supplier", "@sage/xtrem-distribution/nodes__base_inbound_document__property__supplierAddress": "Supplier address", "@sage/xtrem-distribution/nodes__base_inbound_document_line__node_name": "Base inbound document line", "@sage/xtrem-distribution/nodes__base_inbound_document_line__property__document": "Document", "@sage/xtrem-distribution/nodes__base_inbound_document_line__property__itemSupplier": "Item supplier", "@sage/xtrem-distribution/nodes__base_inbound_document_line__property__unit": "Unit", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__deletion_forbidden_reason_status": "The current document is at the status: {{currentStatus}}. You cannot delete it.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__line__item_modify": "The item: {{currentItem}} cannot be edited.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__mutation__post": "Post", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__mutation__post__failed": "Post failed.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__mutation__post__parameter__receipt": "Receipt", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__node_name": "Base inbound receipt document", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__businessRelation": "Business relation", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__externalNote": "External note", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__internalNote": "Internal note", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__isExternalNote": "Is external note", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__isTransferHeaderNote": "Is transfer header note", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__isTransferLineNote": "Is transfer line note", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__lines": "Lines", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__stockSite": "Stock site", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__reiving_site_legal_company": "The receiving site: {{site}}, does not belong to the same company as the header: {{company}}.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__same_company_site_stock_site": "The site and the stock site need to have the same company.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document__tax_type_validation": "The tax type for all documents needs to be 'Purchasing' or 'Purchasing and sales'.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__is_inventory_is_purchasing": "The receiving site needs to be either stock or purchase managed.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__item_must_be_same": "The item cannot be changed.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__no_item_site_record": "The item: {{currentItem}}, is not managed for the site: {{currentSite}}.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__no_item_supplier_record": "The item: {{currentItem}}, is not managed for the supplier: {{currentSupplier}}.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__no_item_supplier_site_record": "The item:{{currentItem}}, is not managed for the site: {{currentSite}}, and supplier: {{currentSupplier}}.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__no_purchase_unit_conversion_coefficient": "The current purchase unit has no conversion factor for the previous purchase unit of the line.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__no_stock_unit_conversion_coefficient": "The current purchase unit has no conversion factor for the item stock unit.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__node_name": "Base inbound receipt document line", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__amountForLandedCostAllocation": "Amount for landed cost allocation", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__company": "Company", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__companyCurrency": "Company currency", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__document": "Document", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__netPrice": "Net price", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__purchaseUnit": "Purchase unit", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__quantityInStockUnitForLandedCostAllocation": "Quantity in stock unit for landed cost allocation", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__stockCostAmount": "Stock cost amount", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__stockCostUnit": "Stock cost unit", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__stockSite": "Stock site", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__supplier": "Supplier", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line__property__unit": "Unit", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line_deletion_forbidden_reason_status": "The line cannot be deleted. The document status is: {{currentStatus}}.", "@sage/xtrem-distribution/nodes__base_inbound_receipt_document_line_item_may_not_have_type_landed_cost": "A landed cost item cannot be added to the document: {{documentName}}.", "@sage/xtrem-distribution/nodes__base_outbound_document__node_name": "Base outbound document", "@sage/xtrem-distribution/nodes__base_outbound_document__property__businessRelation": "Business relation", "@sage/xtrem-distribution/nodes__base_outbound_document__property__customer": "Customer", "@sage/xtrem-distribution/nodes__base_outbound_document__property__customerNumber": "Customer number", "@sage/xtrem-distribution/nodes__base_outbound_document__property__incoterm": "Incoterms rule", "@sage/xtrem-distribution/nodes__base_outbound_document__property__lines": "Lines", "@sage/xtrem-distribution/nodes__base_outbound_document__property__paymentTerm": "Payment term", "@sage/xtrem-distribution/nodes__base_outbound_document__property__shipToCustomer": "Ship-to customer", "@sage/xtrem-distribution/nodes__base_outbound_document_line__node_name": "Base outbound document line", "@sage/xtrem-distribution/nodes__base_outbound_document_line__property__document": "Document", "@sage/xtrem-distribution/nodes__base_outbound_document_line__property__itemCustomer": "Item-customer", "@sage/xtrem-distribution/nodes__base_outbound_document_line__property__unit": "Unit", "@sage/xtrem-distribution/nodes__base_outbound_document_line__property__unitToStockUnitConversionFactor": "Unit to stock unit conversion factor", "@sage/xtrem-distribution/nodes__base_outbound_order_document__deletion_forbidden_line_exists": "You can only delete the document line if its Shipping status is Not shipped.", "@sage/xtrem-distribution/nodes__base_outbound_order_document__improper_shipping_status_during_creation": "To create the stock transfer order, you need to change the Shipping status to Not shipped.", "@sage/xtrem-distribution/nodes__base_outbound_order_document__node_name": "Base outbound order document", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__deliveryMode": "Delivery mode", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__doNotShipAfterDate": "Do-not-ship-after date", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__doNotShipBeforeDate": "Do-not-ship-before date", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__lines": "Lines", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__shippingDate": "Shipping date", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__shippingStatus": "Shipping status", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__shipToAddress": "Ship-to address", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__shipToContact": "Ship-to contact", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__shipToCustomerAddress": "Ship-to customer address", "@sage/xtrem-distribution/nodes__base_outbound_order_document__property__workDays": "Work days", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__auto_allocation_cannot_decrease_quantity": "You can only reduce the quantity of the order line after the allocation request is complete.", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__cannot_delete_line_allocation_request_in_progress": "To delete the stock transfer order line, you need to wait for the automatic allocation to finish.", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__cannot_delete_line_quantity_allocated": "To delete the document order line, you need to remove the stock allocation.", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__deletion_forbidden_line_exists": "You can only delete the document order line if its Shipping status is Not shipped.", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__node_name": "Base outbound order document line", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__allocationRequestStatus": "Allocation request status", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__allocationStatus": "Allocation status", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__availableQuantityInStockUnit": "Available quantity in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__deliveryMode": "Delivery mode", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__document": "Document", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__doNotShipAfterDate": "Do-not-ship-after date", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__doNotShipBeforeDate": "Do-not-ship-before date", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__expectedDeliveryDate": "Expected delivery date", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__quantityAllocatedInStockUnit": "Quantity allocated in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__remainingQuantityToAllocateInStockUnit": "Remaining quantity to allocate in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__remainingQuantityToShipInStockUnit": "Remaining quantity to ship in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__requestedDeliveryDate": "Requested delivery date", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__shippingDate": "Shipping date", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__shippingStatus": "Shipping status", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__shipToAddress": "Ship-to address", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__shipToContact": "Ship-to contact", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__shipToCustomerAddress": "Ship-to customer address", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockAllocations": "Stock allocations", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockCostAmount": "Stock cost amount", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockCostUnit": "Stock cost unit", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockOnHand": "Stock on hand", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockShortageInStockUnit": "Stock shortage in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockShortageStatus": "Stock shortage status", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__property__stockSiteAddress": "Stock site address", "@sage/xtrem-distribution/nodes__base_outbound_order_document_line__sales_order_lines_shipping_status_must_have_the_same_shipping_status": "The Shipping status for the document order lines needs to be the same as the Shipping status in the document.", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__node_name": "Base outbound shipment document", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__allocationStatus": "Allocation status", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__deliveryDate": "Delivery date", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__deliveryLeadTime": "Delivery lead time", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__deliveryMode": "Delivery mode", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__incoterm": "Incoterm", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__lines": "Lines", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__shipToAddress": "Ship-to address", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__shipToContact": "Ship-to contact", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__shipToCustomerAddress": "Ship-to customer address", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__trackingNumber": "Tracking number", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document__property__workDays": "Work days", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__node_name": "Base outbound shipment document line", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__allocationStatus": "Allocation status", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__document": "Document", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__orderCost": "Order cost", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__quantityAllocated": "Quantity allocated", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__quantityAllocatedInStockUnit": "Quantity allocated in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__remainingQuantityInStockUnit": "Remaining quantity in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__remainingQuantityToAllocate": "Remaining quantity to allocate", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__remainingQuantityToAllocateInStockUnit": "Remaining quantity to allocate in stock unit", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockAllocations": "Stock allocations", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockCostAmount": "Stock cost amount", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockCostAmountInCompanyCurrency": "Stock cost amount in company currency", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockCostUnit": "Stock cost unit", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockDetails": "Stock details", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockMovements": "Stock movements", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockTransactions": "Stock transactions", "@sage/xtrem-distribution/nodes__base_outbound_shipment_document_line__property__stockTransactionStatus": "Stock transaction status", "@sage/xtrem-distribution/nodes__base-distribution-document-line__cannot_have_landed_cost": "This document line cannot have a landed cost: {{documentLineType}}.", "@sage/xtrem-distribution/nodes__base-distribution-document-line__cannot_have_landed_cost_line": "This document line cannot have a landed cost line: {{documentLineType}}.", "@sage/xtrem-distribution/nodes__document_line_discount_charge__asyncMutation__asyncExport": "Export", "@sage/xtrem-distribution/nodes__document_line_discount_charge__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-distribution/nodes__document_line_discount_charge__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-distribution/nodes__document_line_discount_charge__node_name": "Document line discount charge", "@sage/xtrem-distribution/nodes__document_line_discount_charge__property__basis": "<PERSON><PERSON>", "@sage/xtrem-distribution/nodes__document_line_discount_charge__property__document": "Document", "@sage/xtrem-distribution/nodes__document_line_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-distribution/nodes__document_line_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-distribution/nodes__document_line_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-distribution/nodes__document_line_tax__node_name": "Document line tax", "@sage/xtrem-distribution/nodes__document_line_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-distribution/nodes__document_line_tax__property__document": "Document", "@sage/xtrem-distribution/nodes__document_line_tax__property__line": "", "@sage/xtrem-distribution/nodes__document_tax__asyncMutation__asyncExport": "Export", "@sage/xtrem-distribution/nodes__document_tax__asyncMutation__asyncExport__parameter__filter": "Filter", "@sage/xtrem-distribution/nodes__document_tax__asyncMutation__asyncExport__parameter__id": "ID", "@sage/xtrem-distribution/nodes__document_tax__node_name": "Document tax", "@sage/xtrem-distribution/nodes__document_tax__property__currency": "<PERSON><PERSON><PERSON><PERSON>", "@sage/xtrem-distribution/nodes__document_tax__property__document": "Document", "@sage/xtrem-distribution/nodes__stock_transfer_order__header_ship_to_customer_not_updatable": "The Ship-to customer needs to be the same as the Receiving site for the document.", "@sage/xtrem-distribution/package__name": "Distribution", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__adjustmentAmount": "Adjustment amount", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__amount": "Amount", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__discountAmount": "Discount amount", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__document__bankAccount__name": "Bank account", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__document__isVoided": "Voided", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__document__number": "Payment number", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__document__paymentDate": "Payment date", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__document__paymentMethod": "Payment method", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__document__reference": "Transaction information", "@sage/xtrem-distribution/page-fragments__payment_line__payments____columns__title__penaltyAmount": "Penalty amount", "@sage/xtrem-distribution/page-fragments__payment_line__payments____title": "Payments", "@sage/xtrem-distribution/pages__common__dialog_button__approve": "Approve", "@sage/xtrem-distribution/pages__common__dialog_button__cancel": "Cancel", "@sage/xtrem-distribution/pages__common__dialog_button__close": "Close", "@sage/xtrem-distribution/pages__common__dialog_button__confirm": "Confirm", "@sage/xtrem-distribution/pages__common__dialog_button__open": "Open", "@sage/xtrem-distribution/pages__common__dialog_button__overwrite_notes": "Use new notes", "@sage/xtrem-distribution/pages__common__dialog_button__reject": "Reject", "@sage/xtrem-distribution/pages__common__dialog_button__repeat_notes": "Repeat notes", "@sage/xtrem-distribution/pages__common__dialog_button__revert": "<PERSON><PERSON>", "@sage/xtrem-distribution/pages__common__dialog_content__approve_action": "You are about to approve this document.", "@sage/xtrem-distribution/pages__common__dialog_content__auto_allocate": "The allocation request was submitted.", "@sage/xtrem-distribution/pages__common__dialog_content__auto_deallocate": "The deallocation request was submitted.", "@sage/xtrem-distribution/pages__common__dialog_content__cannot_confirm_allocate_all_lines": "You need to allocate all lines before confirming.", "@sage/xtrem-distribution/pages__common__dialog_content__close_action_allowed_document_is_closed": "The document is closed.", "@sage/xtrem-distribution/pages__common__dialog_content__close_action_not_allowed_document_is_already_closed": "This document is already closed.", "@sage/xtrem-distribution/pages__common__dialog_content__close_action_not_allowed_incorrect_parameters": "You cannot close this document. The settings are incorrect.", "@sage/xtrem-distribution/pages__common__dialog_content__confirm": "You are about to set this document to 'Confirmed'.", "@sage/xtrem-distribution/pages__common__dialog_content__confirm_action": "You are about to set this document to 'Pending'.", "@sage/xtrem-distribution/pages__common__dialog_content__confirm_action_is_confirmed": "The document has been confirmed and set to 'Pending'.", "@sage/xtrem-distribution/pages__common__dialog_content__confirm_action_not_allowed_incorrect_parameters": "You cannot confirm this document. The settings are incorrect.", "@sage/xtrem-distribution/pages__common__dialog_content__confirm_action_status_is_not_draft": "The confirmation failed. The document is not 'Draft'.", "@sage/xtrem-distribution/pages__common__dialog_content__header_close_action": "You are about to close this document. You can reopen this document after you close it.", "@sage/xtrem-distribution/pages__common__dialog_content__header_open_action": "You are about to change the status of this order to Open.", "@sage/xtrem-distribution/pages__common__dialog_content__line_close_action": "You are about to close this document line.", "@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_allowed_header_closed": "The document line is closed.", "@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_allowed_line_closed": "The document line is closed.", "@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_not_allowed_incorrect_parameters": "You cannot close this line. The settings are incorrect.", "@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_not_allowed_line_already_closed": "This line is already closed.", "@sage/xtrem-distribution/pages__common__dialog_content__line_open_action": "You are about to open this document line.", "@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_allowed": "The document line is opened.", "@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_not_allowed_document_line_is_already_open": "This line is already open.", "@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_not_allowed_document_line_is_shipped": "This line is shipped. You cannot open it.", "@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_not_allowed_incorrect_parameters": "You cannot open this line. The settings are incorrect.", "@sage/xtrem-distribution/pages__common__dialog_content__open_action_allowed_header_open": "The document is opened.", "@sage/xtrem-distribution/pages__common__dialog_content__open_action_not_allowed_incorrect_parameters": "You cannot open this document. The settings are incorrect.", "@sage/xtrem-distribution/pages__common__dialog_content__open_action_not_allowed_the_document_is_already_open": "This document is already open.", "@sage/xtrem-distribution/pages__common__dialog_content__open_action_not_allowed_the_document_is_fully_shipped": "This document is fully shipped. You cannot open it.", "@sage/xtrem-distribution/pages__common__dialog_content__overwrite_notes": "You can repeat the notes from the source document or use the new notes you just entered.", "@sage/xtrem-distribution/pages__common__dialog_content__reject_action": "You are about to reject this document.", "@sage/xtrem-distribution/pages__common__dialog_content__revert": "You are about to set this document to 'Ready to process'.", "@sage/xtrem-distribution/pages__common__dialog_title__auto_allocate_message": "Allocation request submitted", "@sage/xtrem-distribution/pages__common__dialog_title__auto_deallocate": "Deallocation request submitted", "@sage/xtrem-distribution/pages__common__dialog_title__confirm_approval": "Confirm approval", "@sage/xtrem-distribution/pages__common__dialog_title__confirm_rejection": "Confirm rejection", "@sage/xtrem-distribution/pages__common__dialog_title__confirm_update": "Confirm update", "@sage/xtrem-distribution/pages__common__dialog_title__overwrite_notes": "Select notes to use", "@sage/xtrem-distribution/pages__common__step_sequence__approve": "Approve", "@sage/xtrem-distribution/pages__common__step_sequence__confirm": "Confirm", "@sage/xtrem-distribution/pages__common__step_sequence__creation": "Create", "@sage/xtrem-distribution/pages__common__step_sequence__post": "Post", "@sage/xtrem-distribution/pages__common__step_sequence__receive": "Receive", "@sage/xtrem-distribution/pages__common__step_sequence__ship": "Ship"}