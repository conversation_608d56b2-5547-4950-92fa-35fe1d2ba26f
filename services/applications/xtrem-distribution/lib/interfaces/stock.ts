import type * as xtremDistribution from '..';

export type DocumentWithCostAmount =
    | xtremDistribution.nodes.BaseOutboundOrderDocument
    | xtremDistribution.nodes.BaseOutboundShipmentDocument
    | xtremDistribution.nodes.BaseInboundReceiptDocument;

export interface StockRules {
    inboundDefaultLocation: number | null;
    outboundDefaultLocation: number | null;
    inboundDefaultQualityValue: number | null;
}
