import type { Dict, ExtractEdges, ExtractEdgesPartial, decimal } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import { DateValue } from '@sage/xtrem-date-time';
import type { BusinessEntityContact, ContactBase, GraphApi } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { ConfirmDialogWithAcceptButtonTextWidgetPageParameters } from './interfaces/common';

export function isNotWorkDay(workDays: number, day: DateValue): boolean {
    // check if weekDay is working day. After converting int to binary we have 0 and 1.
    // We are checking if bit is 1 on position which number is equal to week day.
    /* eslint no-bitwise: ["error", { "allow": ["<<", "&"] }] */
    return +(workDays & (1 << (6 - day.weekDay))) === 0;
}

/**
 * Load the default address contact detail
 * @param originContact
 * @param page
 */
export function assignEmailContactFrom(
    page: ui.Page<GraphApi> & {
        emailTitle: ui.fields.DropdownListControlObject;
        emailLastName: ui.fields.TextControlObject;
        emailFirstName: ui.fields.TextControlObject;
        emailAddress: ui.fields.TextControlObject;
    },
    originContact?: ExtractEdgesPartial<ContactBase> | null,
) {
    if (originContact?.title) page.emailTitle.value = originContact.title;
    page.emailLastName.value = originContact?.lastName ?? '';
    page.emailFirstName.value = originContact?.firstName ?? '';
    page.emailAddress.value = originContact?.email ?? '';
}

export async function contactsQuery(page: ui.Page, addressId: string): Promise<ExtractEdges<BusinessEntityContact>[]> {
    return extractEdges<BusinessEntityContact>(
        await page.$.graph
            .node('@sage/xtrem-master-data/BusinessEntityContact')
            .query(
                ui.queryUtils.edgesSelector<BusinessEntityContact>(
                    {
                        _id: true,
                        title: true,
                        firstName: true,
                        lastName: true,
                        email: true,
                        preferredName: true,
                        role: true,
                        position: true,
                        locationPhoneNumber: true,
                    },
                    {
                        filter: {
                            isActive: true,
                            address: addressId,
                        },
                    },
                ),
            )
            .execute(),
    );
}

/**
 * Load all contacts linked to this page address contact detail into the selection grid
 * @param contactDetail
 * @param page
 * @param addressId
 */
export async function loadContacts(
    page: ui.Page<GraphApi> & {
        contacts: ui.fields.TableControlObject;
        emailTitle: ui.fields.DropdownListControlObject;
        emailLastName: ui.fields.TextControlObject;
        emailFirstName: ui.fields.TextControlObject;
        emailAddress: ui.fields.TextControlObject;
    },
    addressId: string,
    contactDetail?: ExtractEdgesPartial<ContactBase> | null,
) {
    assignEmailContactFrom(page, contactDetail);
    page.contacts.value = await contactsQuery(page, addressId);
}

/**
 * Refresh the stock cost amount of a sales document line and calculates the gross profit amount + the total of the page
 * This function must be called after the update of amountExcludingTax
 * @param page
 * @param lineToUpdate
 */
export async function calculateStockCost(
    page: ui.Page,
    itemId: string,
    siteId: string,
    quantityInStockUnit: decimal,
): Promise<number> {
    const newCostInCompanyCurrency: decimal = (
        await page.$.graph
            .node('@sage/xtrem-master-data/ItemSite')
            .queries.getValuationCost(
                { amount: true },
                {
                    item: itemId,
                    site: siteId,
                    searchCriteria: {
                        quantity: quantityInStockUnit,
                        dateOfValuation: DateValue.today().format('YYYY-MM-DD'),
                    },
                },
            )
            .execute()
    ).amount;
    return newCostInCompanyCurrency;
}

export function confirmDialogWithAcceptButtonText(
    page: Dict<any>,
    title: string,
    message: string,
    acceptButtonText: string,
    cancelButtonText?: string,
) {
    const options = {
        acceptButton: {
            text: acceptButtonText,
        },
        cancelButton: {
            text:
                cancelButtonText ||
                ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__cancel', 'Cancel'),
        },
    };
    return page.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}

export function confirmDialogWithAcceptButtonTextWidgetPage(
    dialog: ui.Page['$']['dialog'],
    parameters: ConfirmDialogWithAcceptButtonTextWidgetPageParameters,
) {
    const options = {
        acceptButton: {
            text: parameters.acceptButtonText,
        },
        cancelButton: {
            text:
                parameters.cancelButtonText ||
                ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__cancel', 'Cancel'),
        },
    };
    return dialog
        .confirmation('warn', parameters.title, parameters.message, options)
        .then(() => true)
        .catch(() => false);
}

export function notesOverwriteWarning(page: ui.Page) {
    return confirmDialogWithAcceptButtonText(
        page,
        ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__overwrite_notes', 'Select notes to use'),
        ui.localize(
            '@sage/xtrem-distribution/pages__common__dialog_content__overwrite_notes',
            'You can repeat the notes from the source document or use the new notes you just entered.',
        ),
        ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__repeat_notes', 'Repeat notes'),
        ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__overwrite_notes', 'Use new notes'),
    );
}

// TODO: This is a hack. It can be removed when XT-23948 is ready.
// @testResult: a boolean value reflecting a test such as this.status.value === 'shipped'
export async function checkForUpdate(
    page: ui.Page<GraphApi> & {
        status: ui.fields.Label;
    },
    testResult: boolean,
) {
    let refreshCounter = 0;
    const checkForUpdateAction = async () => {
        try {
            await page.status.refresh();
            if (testResult) {
                await page.$.router.refresh(true); // true overrides the dirty checks
                await page.$.refreshNavigationPanel();
                page.$.loader.isHidden = true;
                return;
            }
            refreshCounter += 1;
            if (refreshCounter < 5) {
                setTimeout(() => checkForUpdateAction, 1000);
            } else {
                await page.$.router.refresh(true); // true overrides the dirty checks
                await page.$.refreshNavigationPanel();
                page.$.loader.isHidden = true;
            }
        } catch {
            page.$.loader.isHidden = true;
        }
    };
    await checkForUpdateAction();
}

export function isPostedInProgressError(status: string | null) {
    return ['posted', 'inProgress', 'error'].includes(status ?? '');
}
