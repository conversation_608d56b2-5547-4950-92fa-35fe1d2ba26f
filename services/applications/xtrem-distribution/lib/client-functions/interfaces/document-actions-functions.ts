import type { GraphApi } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface NotificationContentOptions {
    // TODO:
    // https://jira.sage.com/browse/XT-56067
    // option: NotificationOptions;
    content: string;
    severity: string;
}

export interface ConfirmActionDialog {
    pageInstance: ui.Page<GraphApi>;
    message: string;
}

export interface DocumentPageParameter {
    pageInstance: ui.Page<GraphApi>;
    recordNumber: string;
}

export interface CloseMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    recordId: string;
}

export interface CloseActionParameters {
    pageInstance: ui.Page<GraphApi>;
    recordNumber: string;
    mutation: () => Promise<string>;
    isCalledFromRecordPage: boolean;
}

export interface OpenMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    recordId: string;
}

export interface OpenActionParameters {
    pageInstance: ui.Page<GraphApi>;
    recordNumber: string;
    mutation: () => Promise<string>;
    isCalledFromRecordPage: boolean;
}
export interface CloseLineMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    lineId: string;
}

export interface CloseLineActionParameters {
    pageInstance: ui.Page<GraphApi>;
    lineId: string;
    mutation: () => Promise<string>;
}

export interface OpenLineMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    lineId: string;
}

export interface OpenLineActionParameters {
    pageInstance: ui.Page<GraphApi>;
    lineId: string;
    mutation: () => Promise<string>;
}

export interface ApproveRejectMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    recordId: string;
}

export interface ApproveRejectActionParameters {
    pageInstance: ui.Page<GraphApi>;
    recordId: string;
    mutation: () => Promise<string>;
}

export interface ConfirmOrderMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    recordId: string;
}

export interface ConfirmOrderActionParameters {
    pageInstance: ui.Page<GraphApi>;
    number: string;
    isCalledFromRecordPage: boolean;
    mutation: () => Promise<string>;
}

export interface ConfirmShipmentMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    number: string;
}

export interface ConfirmShipmentActionParameters {
    pageInstance: ui.Page<GraphApi>;
    number: string;
    isCalledFromRecordPage: boolean;
    mutation: () => Promise<string>;
    beforeMutation: () => boolean;
}

export interface RequestAllocationMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    _id: string;
    allocationType: string;
}

export interface RequestAllocationActionParameters {
    pageInstance: ui.Page<GraphApi>;
    allocationType: string;
    mutation: () => Promise<string>;
}

export interface RevertShipmentMutationParameters {
    pageInstance: ui.Page<GraphApi>;
    number: string;
}

export interface RevertShipmentActionParameters {
    pageInstance: ui.Page<GraphApi>;
    number: string;
    isCalledFromRecordPage: boolean;
    mutation: () => Promise<string>;
}
