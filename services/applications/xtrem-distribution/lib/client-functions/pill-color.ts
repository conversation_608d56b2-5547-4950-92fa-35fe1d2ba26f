import type { ReceivingStatus, ShippingStatus } from '@sage/xtrem-distribution-api';
import type { ApprovalStatus, BaseStatus } from '@sage/xtrem-master-data-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

function baseStatusColor(status: BaseStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'readyToProcess':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'readyToShip':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'shipped':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function approvalStatusColor(status: ApprovalStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pendingApproval':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'approved':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'rejected':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'changeRequested':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function shippingStatusColor(status: ShippingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notShipped':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'partiallyShipped':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'shipped':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function receivingStatusColor(status: ReceivingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notReceived':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'partiallyReceived':
            return colorfulPillPattern.outlinedCaution[coloredElement];
        case 'received':
            return colorfulPillPattern.filledPositive[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function lineStatusColor(status: BaseStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'closed':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'readyToProcess':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'readyToShip':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'shipped':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'received':
            return colorfulPillPattern.filledClosing[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?: BaseStatus | ApprovalStatus | ShippingStatus | ReceivingStatus | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'BaseStatus':
                return baseStatusColor(status as BaseStatus, coloredElement);
            case 'LineStatus':
                return lineStatusColor(status as BaseStatus, coloredElement);
            case 'ApprovalStatus':
                return approvalStatusColor(status as ApprovalStatus, coloredElement);
            case 'ShippingStatus':
                return shippingStatusColor(status as ShippingStatus, coloredElement);
            case 'ReceivingStatus':
                return receivingStatusColor(status as ReceivingStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };
    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

export function stockShortageStatusColor(status: boolean, coloredElement: ColoredElement) {
    switch (status) {
        case false:
            return colorfulPillPattern.filledPositive[coloredElement];
        case true:
            return colorfulPillPattern.filledCaution[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    if (booleanEntry === 'stockShortageStatus') {
        return stockShortageStatusColor(status, coloredElement);
    }
    return colorfulPillPatternDefaulted(coloredElement);
}
