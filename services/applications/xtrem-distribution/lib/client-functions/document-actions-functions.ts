import type {
    DocumentCloseStatusMethodReturn,
    DocumentCloseStatusMethodReturn$Enum,
    DocumentConfirmStatusMethodReturn,
    DocumentConfirmStatusMethodReturn$Enum,
    DocumentOpenStatusMethodReturn,
    DocumentOpenStatusMethodReturn$Enum,
} from '@sage/xtrem-distribution-api';
import * as ui from '@sage/xtrem-ui';
import { confirmDialogWithAcceptButtonText } from './common';
import type {
    ApproveRejectActionParameters,
    CloseActionParameters,
    CloseLineActionParameters,
    ConfirmOrderActionParameters,
    ConfirmShipmentActionParameters,
    NotificationContentOptions,
    OpenActionParameters,
    OpenLineActionParameters,
    RequestAllocationActionParameters,
    RevertShipmentActionParameters,
} from './interfaces/document-actions-functions';

function getReturnedMessageForLineCloseAction(
    returnStatus: DocumentCloseStatusMethodReturn,
): NotificationContentOptions {
    const listOfMethodReturn = {
        parametersAreIncorrect: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_not_allowed_incorrect_parameters',
                'You cannot close this line. The settings are incorrect.',
            ),
            severity: 'error',
        },
        isAlreadyClosed: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_not_allowed_line_already_closed',
                'This line is already closed.',
            ),
            severity: 'warning',
        },
        isNowClosed: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_allowed_line_closed',
                'The document line is closed.',
            ),
            severity: 'success',
        },
        isHeaderNowClosed: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_close_action_allowed_header_closed',
                'The document line is closed.',
            ),
            severity: 'success',
        },
    };
    return listOfMethodReturn[returnStatus];
}

export async function closeLineAction(parameters: CloseLineActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_update', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_close_action',
                'You are about to close this document line.',
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__close', 'Close'),
        )
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const returnStatus = await parameters.mutation();
        const returnValue = getReturnedMessageForLineCloseAction(
            returnStatus as keyof DocumentCloseStatusMethodReturn$Enum,
        );
        parameters.pageInstance.$.showToast(returnValue.content, {
            type: returnValue.severity as Required<Parameters<typeof parameters.pageInstance.$.showToast>>[1]['type'],
        });
        if (returnValue.severity === 'success') {
            await parameters.pageInstance.$.refreshNavigationPanel();
            await parameters.pageInstance.$.router.refresh();
        }
        parameters.pageInstance.$.loader.isHidden = true;
    }
}

function getReturnedMessageForLineOpenAction(returnStatus: DocumentOpenStatusMethodReturn): NotificationContentOptions {
    const listOfMethodReturn = {
        parametersAreIncorrect: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_not_allowed_incorrect_parameters',
                'You cannot open this line. The settings are incorrect.',
            ),
            severity: 'error',
        },
        isAlreadyOpen: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_not_allowed_document_line_is_already_open',
                'This line is already open.',
            ),
            severity: 'warning',
        },
        isShipped: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_not_allowed_document_line_is_shipped',
                'This line is shipped. You cannot open it.',
            ),
            severity: 'error',
        },
        isNowOpen: {
            content: ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_open_action_allowed',
                'The document line is opened.',
            ),
            severity: 'success',
        },
    };
    return listOfMethodReturn[returnStatus];
}

export async function openLineAction(parameters: OpenLineActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_update', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__line_open_action',
                'You are about to open this document line.',
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__open', 'Open'),
        )
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const returnStatus = await parameters.mutation();
        const returnValue = getReturnedMessageForLineOpenAction(
            returnStatus as keyof DocumentOpenStatusMethodReturn$Enum,
        );
        parameters.pageInstance.$.showToast(returnValue.content, {
            type: returnValue.severity as Required<Parameters<typeof parameters.pageInstance.$.showToast>>[1]['type'],
        });
        if (returnValue.severity === 'success') {
            await parameters.pageInstance.$.refreshNavigationPanel();
            await parameters.pageInstance.$.router.refresh();
        }
        parameters.pageInstance.$.loader.isHidden = true;
    }
}

function getReturnedMessageForOpenAction(returnStatus: DocumentOpenStatusMethodReturn): NotificationContentOptions {
    switch (returnStatus) {
        case 'parametersAreIncorrect':
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__open_action_not_allowed_incorrect_parameters',
                    'You cannot open this document. The settings are incorrect.',
                ),
                severity: 'error',
            };
        case 'isShipped':
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__open_action_not_allowed_the_document_is_fully_shipped',
                    'This document is fully shipped. You cannot open it.',
                ),
                severity: 'error',
            };
        case 'isAlreadyOpen':
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__open_action_not_allowed_the_document_is_already_open',
                    'This document is already open.',
                ),
                severity: 'warning',
            };
        default:
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__open_action_allowed_header_open',
                    'The document is opened.',
                ),
                severity: 'success',
            };
    }
}

export async function openAction(parameters: OpenActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_update', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__header_open_action',
                'You are about to change the status of this order to Open.',
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__open', 'Open'),
        )
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const returnStatus = await parameters.mutation();
        const returnValue = getReturnedMessageForOpenAction(returnStatus as keyof DocumentOpenStatusMethodReturn$Enum);
        parameters.pageInstance.$.showToast(returnValue.content, {
            type: returnValue.severity as Required<Parameters<typeof parameters.pageInstance.$.showToast>>[1]['type'],
        });
        if (returnValue.severity === 'success' && parameters.isCalledFromRecordPage) {
            await parameters.pageInstance.$.refreshNavigationPanel();
            await parameters.pageInstance.$.router.refresh();
        }
        parameters.pageInstance.$.loader.isHidden = true;
    }
}

function getReturnedMessageForCloseAction(returnStatus: DocumentCloseStatusMethodReturn): NotificationContentOptions {
    switch (returnStatus) {
        case 'parametersAreIncorrect':
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__close_action_not_allowed_incorrect_parameters',
                    'You cannot close this document. The settings are incorrect.',
                ),
                severity: 'error',
            };
        case 'isAlreadyClosed':
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__close_action_not_allowed_document_is_already_closed',
                    'This document is already closed.',
                ),
                severity: 'warning',
            };
        default:
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__close_action_allowed_document_is_closed',
                    'The document is closed.',
                ),
                severity: 'success',
            };
    }
}

export async function closeAction(parameters: CloseActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_update', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__header_close_action',
                'You are about to close this document. You can reopen this document after you close it.',
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__close', 'Close'),
        )
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const returnStatus = await parameters.mutation();
        const returnValue = getReturnedMessageForCloseAction(
            returnStatus as keyof DocumentCloseStatusMethodReturn$Enum,
        );
        parameters.pageInstance.$.showToast(returnValue.content, {
            type: returnValue.severity as Required<Parameters<typeof parameters.pageInstance.$.showToast>>[1]['type'],
        });
        if (returnValue.severity === 'success' && parameters.isCalledFromRecordPage) {
            await parameters.pageInstance.$.refreshNavigationPanel();
            await parameters.pageInstance.$.router.refresh();
        }
        parameters.pageInstance.$.loader.isHidden = true;
    }
}

function getReturnedMessageForConfirmOrderAction(
    returnStatus: DocumentConfirmStatusMethodReturn,
): NotificationContentOptions {
    switch (returnStatus) {
        case 'parametersAreIncorrect':
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__confirm_action_not_allowed_incorrect_parameters',
                    'You cannot confirm this document. The settings are incorrect.',
                ),
                severity: 'error',
            };
        case 'isConfirmed':
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__confirm_action_is_confirmed',
                    "The document has been confirmed and set to 'Pending'.",
                ),
                severity: 'success',
            };
        default:
            return {
                content: ui.localize(
                    '@sage/xtrem-distribution/pages__common__dialog_content__confirm_action_status_is_not_draft',
                    "The confirmation failed. The document is not 'Draft'.",
                ),
                severity: 'error',
            };
    }
}

export async function confirmOrderAction(parameters: ConfirmOrderActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_update', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__confirm_action',
                "You are about to set this document to 'Pending'.",
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__confirm', 'Confirm'),
        )
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const returnStatus = await parameters.mutation();
        const returnValue = getReturnedMessageForConfirmOrderAction(
            returnStatus as keyof DocumentConfirmStatusMethodReturn$Enum,
        );
        if (parameters.number) {
            parameters.pageInstance.$.showToast(returnValue.content, {
                type: returnValue.severity === 'error' ? 'error' : 'success',
            });
            await parameters.pageInstance.$.refreshNavigationPanel();
            await parameters.pageInstance.$.router.refresh();
        }
    }
}

export async function approveAction(parameters: ApproveRejectActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_approval', 'Confirm approval'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__approve_action',
                'You are about to approve this document.',
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__approve', 'Approve'),
        )
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const isApproved = await parameters.mutation();
        parameters.pageInstance.$.showToast(isApproved, { type: 'success' });
        await parameters.pageInstance.$.router.refresh();
        await parameters.pageInstance.$.refreshNavigationPanel();
        parameters.pageInstance.$.loader.isHidden = true;
    }
}

export async function rejectAction(parameters: ApproveRejectActionParameters) {
    if (
        await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_rejection', 'Confirm rejection'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__reject_action',
                'You are about to reject this document.',
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__reject', 'Reject'),
        )
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const isRejected = await parameters.mutation();
        parameters.pageInstance.$.showToast(isRejected, { type: 'success' });
        await parameters.pageInstance.$.router.refresh();
        await parameters.pageInstance.$.refreshNavigationPanel();
        parameters.pageInstance.$.loader.isHidden = true;
    }
}

export async function requestAllocationAction(parameters: RequestAllocationActionParameters) {
    parameters.pageInstance.$.loader.isHidden = false;
    await parameters.mutation();
    if (parameters.allocationType === 'deallocation') {
        await parameters.pageInstance.$.dialog.message(
            'info',
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_title__auto_deallocate',
                'Deallocation request submitted',
            ),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__auto_deallocate',
                'The deallocation request was submitted.',
            ),
        );
    } else {
        await parameters.pageInstance.$.dialog.message(
            'info',
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_title__auto_allocate_message',
                'Allocation request submitted',
            ),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__auto_allocate',
                'The allocation request was submitted.',
            ),
        );
    }
    await parameters.pageInstance.$.router.refresh();
    await parameters.pageInstance.$.refreshNavigationPanel();
    parameters.pageInstance.$.loader.isHidden = true;
}

export async function confirmShipmentAction(parameters: ConfirmShipmentActionParameters) {
    const { beforeMutation } = parameters;
    if (!beforeMutation()) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__cannot_confirm_allocate_all_lines',
                'You need to allocate all lines before confirming.',
            ),
        );
    }
    if (
        parameters.number &&
        (await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_update', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__confirm',
                "You are about to set this document to 'Confirmed'.",
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__confirm', 'Confirm'),
        ))
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const isConfirmed = await parameters.mutation();
        parameters.pageInstance.$.showToast(isConfirmed, { type: 'success' });
        await parameters.pageInstance.$.router.refresh();
        await parameters.pageInstance.$.refreshNavigationPanel();
        parameters.pageInstance.$.loader.isHidden = true;
    }
}

export async function revertAction(parameters: RevertShipmentActionParameters) {
    if (
        parameters.number &&
        (await confirmDialogWithAcceptButtonText(
            parameters.pageInstance,
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_title__confirm_update', 'Confirm update'),
            ui.localize(
                '@sage/xtrem-distribution/pages__common__dialog_content__revert',
                "You are about to set this document to 'Ready to process'.",
            ),
            ui.localize('@sage/xtrem-distribution/pages__common__dialog_button__revert', 'Revert'),
        ))
    ) {
        parameters.pageInstance.$.loader.isHidden = false;
        const result = await parameters.mutation();
        if (result) {
            parameters.pageInstance.$.showToast(String(result), { timeout: 5000, type: 'success' });
            await parameters.pageInstance.$.router.refresh();
            parameters.pageInstance.$.setPageClean();
            await parameters.pageInstance.$.refreshNavigationPanel();
            parameters.pageInstance.$.loader.isHidden = true;
        }
    }
}
