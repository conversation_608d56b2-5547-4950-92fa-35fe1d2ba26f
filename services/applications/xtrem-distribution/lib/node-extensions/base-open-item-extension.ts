import { decorators, NodeExtension, NodeStatus } from '@sage/xtrem-core';
import * as xtremFinanceData from '@sage/xtrem-finance-data';
import * as xtremDistribution from '..';

@decorators.nodeExtension<BaseOpenItemExtension>({
    extends: () => xtremFinanceData.nodes.BaseOpenItem,
    async saveBegin() {
        if (
            this.$.status === NodeStatus.added &&
            ['purchaseInvoice', 'purchaseCreditMemo'].includes(await this.documentType)
        ) {
            const document = await this.$.context.read(xtremDistribution.nodes.BaseDistributionDocument, {
                _id: await this.documentSysId,
            });
            if (document) {
                const paymentTracking = await document.paymentTracking;
                if (paymentTracking) {
                    const paymentTerm = await paymentTracking.paymentTerm;
                    await this.$.set({
                        discountFrom: await paymentTerm?.discountFrom,
                        discountDate: await paymentTerm?.discountDate,
                        discountType: await paymentTracking.discountPaymentType,
                        discountAmount: await paymentTracking.discountPaymentAmount,
                        discountPaymentBeforeDate: await paymentTracking.discountPaymentBeforeDate,
                        penaltyPaymentType: await paymentTracking.penaltyPaymentType,
                        penaltyAmount: await paymentTracking.penaltyPaymentAmount,
                    });
                }
            }
        }
    },
})
export class BaseOpenItemExtension extends NodeExtension<xtremFinanceData.nodes.BaseOpenItem> {}
declare module '@sage/xtrem-finance-data/lib/nodes/base-open-item' {
    export interface BaseOpenItem extends BaseOpenItemExtension {}
}
