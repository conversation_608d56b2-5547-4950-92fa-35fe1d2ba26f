import type { Context, date } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';

export function dateCheckControl(
    context: Context,
    parameters: {
        discountPaymentBeforeDate: date | null | undefined;
        dueDate: date;
        isSafeToRetry?: boolean;
    },
): boolean {
    if (parameters.discountPaymentBeforeDate && parameters.discountPaymentBeforeDate.compare(parameters.dueDate) > 0) {
        if (parameters.isSafeToRetry) {
            context.logger.warn(
                context.localize(
                    '@sage/xtrem-distribution/functions__date_check_control__validation_failed_safe_retry',
                    'Safe retry validation failed. The discount date needs to be on or before the due date.',
                ),
            );
            return true;
        }
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-distribution/functions__date_check_control__discount_payment_before_date_later_than_due_date',
                'The discount date needs to be on or before the due date.',
            ),
        );
    }
    return false;
}
