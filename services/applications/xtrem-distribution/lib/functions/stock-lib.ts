import type { Context, decimal } from '@sage/xtrem-core';
import { date } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type { DocumentWithCostAmount, StockRules } from '../interfaces';

export async function getStockCostAmount(document: DocumentWithCostAmount, stockCostAmountInCompanyCurrency: number) {
    // convert from company currency to transaction currency

    const stockSiteCompanyDecimalDigits = await (
        await (
            await (
                await document.stockSite
            )?.legalCompany
        )?.currency
    )?.decimalDigits;
    const siteCompanyDecimalDigits = await (await (await (await document.site).legalCompany).currency).decimalDigits;
    const companyDecimalDigits = stockSiteCompanyDecimalDigits || siteCompanyDecimalDigits;

    return xtremMasterData.sharedFunctions.convertAmount(
        stockCostAmountInCompanyCurrency,
        await document.companyFxRateDivisor,
        await document.companyFxRate,
        companyDecimalDigits,
        await (
            await document.currency
        ).decimalDigits,
    );
}

export async function getStockCostAmountInCompanyCurrency(
    document: DocumentWithCostAmount,
    item: xtremMasterData.nodes.Item,
    quantityInStockUnit: decimal,
) {
    const stockSite = await document?.stockSite;
    if (stockSite) {
        return (
            await xtremStockData.nodeExtensions.ItemSiteExtension.getValuationCost(
                document.$.context,
                item,
                stockSite,
                {
                    quantity: quantityInStockUnit,
                    dateOfValuation: date.today(),
                    valuationType: 'issue',
                },
            )
        ).amount;
    }
    return 0;
}

export async function getItemSiteAvailableStockQuantity(
    context: Context,
    item: xtremMasterData.nodes.Item,
    site: xtremSystem.nodes.Site,
): Promise<number> {
    if (item._id && site._id) {
        const itemSite = await context.tryRead(xtremMasterData.nodes.ItemSite, {
            item: item._id,
            site: site._id,
        });

        if (itemSite) {
            return (await itemSite.acceptedStockQuantity) - (await itemSite.allocatedQuantity);
        }
    }
    return 0;
}

export async function getDefaultStockRules(
    context: Context,
    itemId: number,
    siteId: number,
): Promise<StockRules | null> {
    if (!itemId || !siteId) return null;
    const itemSite = await context.tryRead(xtremMasterData.nodes.ItemSite, {
        item: itemId,
        site: siteId,
    });
    if (!itemSite) return null;
    return {
        inboundDefaultLocation: (await itemSite.inboundDefaultLocation)?._id ?? null,
        outboundDefaultLocation: (await itemSite.outboundDefaultLocation)?._id ?? null,
        inboundDefaultQualityValue: (await itemSite.inboundDefaultQualityValue)?._id ?? null,
    };
}
