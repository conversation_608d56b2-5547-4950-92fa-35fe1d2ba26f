import type { Context, DateValue, integer } from '@sage/xtrem-core';
import { BusinessRuleError, date } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { SubWorkDaysParams } from '../interfaces';

export async function computeWorkDays(address: xtremMasterData.nodes.DeliveryDetail): Promise<number> {
    const workDays = await Promise.all([
        address.isSundayWorkDay,
        address.isMondayWorkDay,
        address.isTuesdayWorkDay,
        address.isWednesdayWorkDay,
        address.isThursdayWorkDay,
        address.isFridayWorkDay,
        address.isSaturdayWorkDay,
    ]);
    const workDaysBinary = workDays.map(day => (day ? '1' : '0')).join('');
    return parseInt(workDaysBinary, 2);
}

function isNotWorkDay(workDays: integer, day: DateValue): boolean {
    // check if weekDay is working day. After converting int to binary we have 0 and 1.
    // We are checking if bit is 1 on position which number is equal to week day.
    /* eslint no-bitwise: ["error", { "allow": ["<<", "&"] }] */
    return +(workDays & (1 << (6 - day.weekDay))) === 0;
}

function isWorkDay(workDays: integer, day: DateValue): boolean {
    return !isNotWorkDay(workDays, day);
}

export function addWorkDays(sourceDate: date | null, toBeAdded: integer, workDays: integer): date {
    let baseDate = sourceDate?.addDays(toBeAdded) ?? date.today().addDays(toBeAdded);

    // workday value is greater than 0 if at least one workday is defined in customer delivery address table.
    if (workDays > 0) {
        while (isNotWorkDay(workDays, baseDate)) {
            baseDate = baseDate.addDays(1);
        }
    }
    return baseDate;
}

export async function addWorkDaysFromAddress(
    sourceDate: date | null,
    toBeAdded: integer,
    address: xtremMasterData.nodes.DeliveryDetail,
): Promise<date> {
    return addWorkDays(sourceDate, toBeAdded, await computeWorkDays(address));
}

export function maxDate(dates: date[]): date {
    return dates.reduce((max, current) => (current.value > max.value ? current : max), dates[0]);
}

export function subWorkDays(context: Context, data: SubWorkDaysParams): date {
    // check consistency of parameters doNotShipBeforeDate and doNotShipAfterDate
    if (
        data.doNotShipBeforeDate &&
        data.doNotShipAfterDate &&
        data.doNotShipBeforeDate.value > data.doNotShipAfterDate.value
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-distribution/function__sub_work_days__inconsistent_doNoShip_dates',
                'The do-not-ship-before date needs to be before the do-not-ship-after date.',
            ),
        );
    }

    // Compute the delivery delay to the requested date
    const baseDate = data.requestedDeliveryDate.addDays(-data.deliveryLeadTime);

    // Initialize the shipping date with the maximum of the base date and the order date
    let shippingDate = maxDate([
        baseDate,
        data.orderDate,
        ...(data.doNotShipBeforeDate ? [data.doNotShipBeforeDate] : []),
    ]);

    // We should be able to find a work day in the next 7 days
    for (let i = 0; i < 7; i += 1) {
        if (
            data.doNotShipAfterDate &&
            isWorkDay(data.workDaysMask, data.doNotShipAfterDate) &&
            shippingDate.value > data.doNotShipAfterDate.value
        ) {
            return data.doNotShipAfterDate;
        }

        if (isWorkDay(data.workDaysMask, shippingDate)) {
            return shippingDate;
        }
        shippingDate = shippingDate.addDays(1);
    }

    throw new BusinessRuleError(
        context.localize(
            '@sage/xtrem-distribution/function__sub_work_days__unable_to_find_a_work_day',
            'No shipping date found. You need to select a customer address with working days.',
        ),
    );
}
