import type { NodeCreateData, UpdateAction } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremDistribution from '../index';

/**
 * Returns the amount of a landed cost document line that must be spread on receipts and/or orders
 * @param documentLine a purchase document line that assigns landed costs to other documents (e.g. purchase invoice)
 * @returns
 */
export function getLandedCostAmountToAllocate(documentLine: xtremDistribution.nodes.BaseDistributionDocumentLine) {
    return documentLine.amountExcludingTax;
}

// This should be enhanced when BaseDistributionDocumentLine will be refactored with taxes included
// import type * as xtremLandedCost from '@sage/xtrem-landed-cost';
// import type * as xtremDistribution from '../index';

// /**
//  * Returns the amount of a landed cost document line that must be spread on receipts and/or orders
//  * @param documentLine a purchase document line that assigns landed costs to other documents (e.g. purchase invoice)
//  * @returns
//  */
// export function getLandedCostAmountToAllocate(
//     documentLine: xtremDistribution.nodes.BaseDistributionDocumentLine &
//         xtremLandedCost.interfaces.DocumentLineAllocatingLandedCost,
// ) {
//     return (
//         documentLine.amountExcludingTax +
//         (await documentLine.taxes.sum(async tax => (await tax.taxAmountAdjusted) - (await tax.deductibleTaxAmount)))
//     );
// }

export async function addNewDistributionDocumentLineTaxDependency(
    baseDistributionDocumentLine: xtremDistribution.nodes.BaseDistributionDocumentLine,
): Promise<
    NodeCreateData<
        xtremTax.nodes.DocumentLineTax & {
            _sortValue?: number;
            _action: UpdateAction;
        }
    >[]
> {
    return (
        (await baseDistributionDocumentLine.taxes
            .map(async tax => {
                return {
                    _action: 'create' as UpdateAction,
                    taxCategoryReference: (await tax.taxCategoryReference)?._id,
                    taxReference: (await tax.taxReference)?._id,
                    isSubjectToGlTaxExcludedAmount: await tax.isSubjectToGlTaxExcludedAmount,
                    isTaxMandatory: await tax.isTaxMandatory,
                    _sortValue: await tax._sortValue,
                    // taxRate is only to meet field requirements. It will be rewritten to the actual rate
                    // for the current date during BaseTaxCalculator execution.
                    taxRate: await tax.taxRate,
                };
            })
            .toArray()) || []
    );
}

export function isPostedErrorClosed(status: xtremMasterData.enums.BaseStatus | null) {
    return ['posted', 'error', 'closed'].includes(status || '');
}
