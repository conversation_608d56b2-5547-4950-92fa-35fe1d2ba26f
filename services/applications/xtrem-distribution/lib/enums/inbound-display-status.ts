import { EnumDataType } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';

export enum InboundDisplayStatusEnum {
    draft = xtremMasterData.enums.BaseDisplayStatusEnum.draft,
    pendingApproval = xtremMasterData.enums.BaseDisplayStatusEnum.pendingApproval,
    approved = xtremMasterData.enums.BaseDisplayStatusEnum.approved,
    rejected = xtremMasterData.enums.BaseDisplayStatusEnum.rejected,
    confirmed = xtremMasterData.enums.BaseDisplayStatusEnum.confirmed,
    received = xtremMasterData.enums.BaseDisplayStatusEnum.received,
    partiallyInvoiced = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyInvoiced,
    invoiced = xtremMasterData.enums.BaseDisplayStatusEnum.invoiced,
    closed = xtremMasterData.enums.BaseDisplayStatusEnum.closed,
    postingInProgress = xtremMasterData.enums.BaseDisplayStatusEnum.postingInProgress,
    error = xtremMasterData.enums.BaseDisplayStatusEnum.error,
    partiallyReceived = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyReceived,
    partiallyReturned = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyReturned,
    partiallyOrdered = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyOrdered,
    taxCalculationFailed = xtremMasterData.enums.BaseDisplayStatusEnum.taxCalculationFailed,
    postingError = xtremMasterData.enums.BaseDisplayStatusEnum.postingError,
    posted = xtremMasterData.enums.BaseDisplayStatusEnum.posted,
    stockError = xtremMasterData.enums.BaseDisplayStatusEnum.stockError,
    partiallyCredited = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyCredited,
    credited = xtremMasterData.enums.BaseDisplayStatusEnum.credited,
    noVariance = xtremMasterData.enums.BaseDisplayStatusEnum.noVariance,
    variance = xtremMasterData.enums.BaseDisplayStatusEnum.variance,
    varianceApproved = xtremMasterData.enums.BaseDisplayStatusEnum.varianceApproved,
    returned = xtremMasterData.enums.BaseDisplayStatusEnum.returned,
    ordered = xtremMasterData.enums.BaseDisplayStatusEnum.ordered,
    paid = xtremMasterData.enums.BaseDisplayStatusEnum.paid,
    partiallyPaid = xtremMasterData.enums.BaseDisplayStatusEnum.partiallyPaid,
}

export type InboundDisplayStatus = keyof typeof InboundDisplayStatusEnum;

export const inboundDisplayStatusDataType = new EnumDataType<InboundDisplayStatus>({
    enum: InboundDisplayStatusEnum,
    filename: __filename,
});
