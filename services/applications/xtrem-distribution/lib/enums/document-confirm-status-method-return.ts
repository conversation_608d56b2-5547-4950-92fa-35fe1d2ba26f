import { EnumDataType } from '@sage/xtrem-core';

export enum DocumentConfirmStatusMethodReturnEnum {
    parametersAreIncorrect,
    isNotDraft,
    isConfirmed,
}

export type DocumentConfirmStatusMethodReturn = keyof typeof DocumentConfirmStatusMethodReturnEnum;

export const documentConfirmStatusMethodReturnDataType = new EnumDataType<DocumentConfirmStatusMethodReturn>({
    enum: DocumentConfirmStatusMethodReturnEnum,
    filename: __filename,
});
