import { EnumDataType } from '@sage/xtrem-core';

export enum DocumentCloseStatusMethodReturnEnum {
    parametersAreIncorrect,
    isAlreadyClosed,
    isNowClosed,
}

export type DocumentCloseStatusMethodReturn = keyof typeof DocumentCloseStatusMethodReturnEnum;

export const documentCloseStatusMethodReturnDataType = new EnumDataType<DocumentCloseStatusMethodReturn>({
    enum: DocumentCloseStatusMethodReturnEnum,
    filename: __filename,
});
