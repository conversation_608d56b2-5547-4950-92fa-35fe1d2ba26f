import { EnumDataType } from '@sage/xtrem-core';

export enum DocumentOpenStatusMethodReturnEnum {
    parametersAreIncorrect,
    isShipped,
    isAlreadyOpen,
    isNowOpen,
}

export type DocumentOpenStatusMethodReturn = keyof typeof DocumentOpenStatusMethodReturnEnum;

export const documentOpenStatusMethodReturnDataType = new EnumDataType<DocumentOpenStatusMethodReturn>({
    enum: DocumentOpenStatusMethodReturnEnum,
    filename: __filename,
});
