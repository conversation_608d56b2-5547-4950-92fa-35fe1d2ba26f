import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { FakeDistributionDocument } from '../../fixtures/lib/nodes';

describe('Fake distribution document', () => {
    it('Create a new fake distribution document ', () =>
        Test.withContext(async context => {
            const customer01 = await context.read(xtremMasterData.nodes.Customer, { _id: '#US019' });
            const fakeDoc = await context.create(FakeDistributionDocument, {
                site: '#US001',
                businessRelation: customer01._id,
            });
            assert.isTrue(await fakeDoc.$.trySave());

            assert.equal(await (await fakeDoc.site).id, 'US001');
            /** Stock site default value is the stockSite of the legalCOmpany of the site  */
            assert.isTrue(await (await fakeDoc.site).isInventory);
            assert.equal(await (await fakeDoc.stockSite)?.id, 'US001');

            /** Default value of payment term comming from the businessRelation  */
            assert.equal(await (await customer01.paymentTerm).id, 'DUE_UPON_RECEIPT_ALL');
            assert.equal(await (await fakeDoc.paymentTerm).id, 'DUE_UPON_RECEIPT_ALL');

            /** Default value of currency comming from the businessRelation */
            assert.equal(await (await customer01.currency).id, 'USD');
            assert.equal(await (await fakeDoc.currency).id, 'USD');
        }));

    it('Read a existing fake distribution document ', () =>
        Test.withContext(async context => {
            const fakeDistriDoc = await context.read(FakeDistributionDocument, { number: 'fake-02' });
            assert.instanceOf(fakeDistriDoc, FakeDistributionDocument);
            assert.equal(await fakeDistriDoc.number, 'fake-02');
        }));

    it('Create a new fake distribution document with an item', () =>
        Test.withContext(async context => {
            const chair = await context.read(xtremMasterData.nodes.Item, { _id: '#Chair' });

            const stockUnit = await chair.stockUnit;

            const fakeDoc = await context.create(FakeDistributionDocument, {
                site: '#US001',
                businessRelation: '#The Golf Shack',
                lines: [{ item: '#Chair', quantity: 10 }],
            });
            await fakeDoc.$.save();

            assert.equal(stockUnit._id, (await (await fakeDoc.lines.elementAt(0)).stockUnit)._id);
        }));
});
