import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { FakeInboundDocument } from '../../fixtures/lib/nodes';

describe('Fake inbound document', () => {
    it('Create a new fake inbound document ', () =>
        Test.withContext(async context => {
            const fakeDoc = await context.create(FakeInboundDocument, { site: '#US001', supplier: '#US017' });

            const supplierPaymentTerm = await context.read(xtremMasterData.nodes.PaymentTerm, {
                _id: '#TEST_NET_30_SUPPLIER',
            });

            assert.equal(await supplierPaymentTerm.businessEntityType, 'supplier');

            await fakeDoc.$.set({ paymentTerm: '#TEST_NET_30_SUPPLIER' });

            assert.isTrue(await fakeDoc.$.trySave());

            await fakeDoc.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });

            assert.isTrue(await fakeDoc.$.trySave());

            /** The businessRelation is the customer  */
            assert.equal(await (await fakeDoc.businessRelation).id, 'US017');

            /** paymentTerm control  */

            assert.equal(await (await fakeDoc.billBySupplier).id, 'US017');
        }));

    it('Create a new fake inbound document with an item', () =>
        Test.withContext(async context => {
            const chair = await context.read(xtremMasterData.nodes.Item, { _id: '#Chair' });

            const stockUnit = await chair.stockUnit;

            const leclercChair = await chair.suppliers.find(async supplier => (await supplier.id) === 'LECLERC');

            assert.isNotNull(leclercChair);

            const fakeDoc = await context.create(FakeInboundDocument, {
                site: '#US001',
                supplier: '#LECLERC',
                lines: [{ item: '#Chair', quantity: 10 }],
            });
            await fakeDoc.$.save();

            assert.equal(stockUnit._id, (await (await fakeDoc.lines.elementAt(0)).stockUnit)._id);

            const chairLine = await fakeDoc.lines.elementAt(0);

            assert.equal(await (await chairLine.item).id, 'Chair');
            assert.equal(await (await chairLine.unit).id, 'BOX');
            assert.equal(await (await chairLine.stockUnit).id, 'EACH');
            assert.equal(await chairLine.quantity, 10); // 10 boxes
            assert.equal(await chairLine.quantityInStockUnit, 121); // 121 Chairs

            const itemSupplier = await chairLine.itemSupplier;

            assert.equal(await (await itemSupplier?.supplier)?.id, 'LECLERC');
        }));
});
