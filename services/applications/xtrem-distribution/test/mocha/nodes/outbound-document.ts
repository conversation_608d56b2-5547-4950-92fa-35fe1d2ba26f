import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { FakeOutboundDocument } from '../../fixtures/lib/nodes';

describe('Fake outbound document', () => {
    it('Create a new fake outbound document ', () =>
        Test.withContext(async context => {
            const fakeDoc = await context.create(FakeOutboundDocument, { site: '#US001', customer: '#US019' });

            const supplierPaymentTerm = await context.read(xtremMasterData.nodes.PaymentTerm, {
                _id: '#TEST_NET_30_SUPPLIER',
            });

            assert.equal(await supplierPaymentTerm.businessEntityType, 'supplier');

            await fakeDoc.$.set({ paymentTerm: '#TEST_NET_30_SUPPLIER' });

            assert.isTrue(await fakeDoc.$.trySave());

            await fakeDoc.$.set({ paymentTerm: '#TEST_NET_15_CUSTOMER' });

            assert.isTrue(await fakeDoc.$.trySave());

            /** The businessRelation is the customer  */
            assert.equal(await (await fakeDoc.businessRelation).id, 'US019');

            /** paymentTerm control  */

            assert.equal(await (await fakeDoc.shipToCustomer).id, 'US019');
        }));

    it('Create a new fake outbound document with an item', () =>
        Test.withContext(async context => {
            const aqua = await context.read(xtremMasterData.nodes.Item, { _id: '#Aqua' });

            const stockUnit = await aqua.stockUnit;

            const cust02Aqua = await aqua.customers.find(async customer => (await customer.id) === 'US020');

            assert.isNotNull(cust02Aqua);

            const fakeDoc = await context.create(FakeOutboundDocument, {
                site: '#US001',
                customer: '#US020',
                lines: [{ item: '#Aqua', quantity: 10 }],
            });
            assert.isTrue(
                await fakeDoc.$.trySave(),
                context.diagnoses.map(d => `${d.path} - ${d.message} `).join('\n'),
            );

            assert.equal(stockUnit._id, (await (await fakeDoc.lines.elementAt(0)).stockUnit)._id);

            const aquaLine = await fakeDoc.lines.elementAt(0);

            assert.equal(await (await aquaLine.item).id, 'Aqua');
            assert.equal(await (await aquaLine.unit).id, 'CUBIC_METER');
            assert.equal(await (await aquaLine.stockUnit).id, 'LITER');
            assert.equal(await aquaLine.quantity, 10);
            assert.equal(await aquaLine.quantityInStockUnit, 10000);

            const itemCustomer = await aquaLine.itemCustomer;

            assert.equal(await (await itemCustomer?.customer)?.id, 'US020');
        }));
});
