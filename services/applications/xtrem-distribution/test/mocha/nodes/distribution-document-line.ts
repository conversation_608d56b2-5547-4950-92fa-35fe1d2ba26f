import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import { FakeDistributionDocument } from '../../fixtures/lib/nodes';

describe('Fake distribution document line', () => {
    it('Read a existing fake distribution document ', () =>
        Test.withContext(async context => {
            const fakeDistriDoc = await context.read(FakeDistributionDocument, { number: 'fake-02' });
            assert.isNotNull(fakeDistriDoc);
            assert.equal(await fakeDistriDoc.lines?.length, 1);
            assert.equal(await (await (await fakeDistriDoc.lines.elementAt(0)).document).number, 'fake-02');
            assert.equal(await (await (await fakeDistriDoc.lines.elementAt(0)).item).name, 'STOFIFO');
        }));

    it('Create a new fake distribution document line', () =>
        Test.withContext(async context => {
            const fakeDistriDoc = await context.read(
                FakeDistributionDocument,
                { number: 'fake-02' },
                { forUpdate: true },
            );

            await fakeDistriDoc.lines.append({
                site: '#US001',
                item: '#Chair',
                quantity: 10,
                stockSiteLinkedAddress: '#US001|7100',
            });

            await fakeDistriDoc.$.save();
        }));

    it('Create a new fake distribution document line fails when status is different', () =>
        Test.withContext(async context => {
            const fakeDistriDoc = await context.read(
                FakeDistributionDocument,
                { number: 'fake-02' },
                { forUpdate: true },
            );

            await fakeDistriDoc.lines.append({
                site: '#US001',
                item: '#Chair',
                quantity: 10,
                stockSiteLinkedAddress: '#US001|7100',
                status: 'readyToShip',
            });

            await assert.isRejected(fakeDistriDoc.$.save(), 'The record was not updated.');

            assert.deepEqual(fakeDistriDoc.$.context.diagnoses, [
                {
                    message: 'The line status needs to be the same as the transfer status.',
                    path: ['lines', '-1000000001'],
                    severity: 3,
                },
            ]);
        }));

    it('Create a new fake distribution document line fails when stock site is different', () =>
        Test.withContext(async context => {
            const fakeDistriDoc = await context.read(
                FakeDistributionDocument,
                { number: 'fake-02' },
                { forUpdate: true },
            );

            await fakeDistriDoc.lines.append({
                site: '#CAS01',
                item: '#Chair',
                quantity: 10,
                stockSiteLinkedAddress: '#CAS01|10',
                stockSite: '#CAS01',
            });

            await assert.isRejected(fakeDistriDoc.$.save());

            assert.deepEqual(fakeDistriDoc.$.context.diagnoses, [
                {
                    message: 'The stock site on the line needs to be the same as the stock site for the transfer.',
                    path: ['lines', '-1000000001'],
                    severity: 3,
                },
                {
                    message: 'The record is not valid. You need to select a different record.',
                    path: ['lines', '-1000000001', 'stockSite'],
                    severity: 3,
                },
            ]);
        }));

    it('Create a new fake distribution document line with a landed cost item fails when adding landed cost', () =>
        Test.withContext(
            async context => {
                const fakeDistriDoc = await context.read(
                    FakeDistributionDocument,
                    { number: 'fake-02' },
                    { forUpdate: true },
                );

                await fakeDistriDoc.lines.append({
                    site: '#US001',
                    item: '#LandedCost001',
                    quantity: 1,
                    stockSiteLinkedAddress: '#US001|840500',
                    landedCost: {
                        allocationMethod: 'manual',
                    },
                });

                await assert.isRejected(fakeDistriDoc.$.save());

                assert.deepEqual(fakeDistriDoc.$.context.diagnoses, [
                    {
                        message: 'This document line cannot have a landed cost: FakeDocumentLine.',
                        path: ['lines', '-1000000001'],
                        severity: 3,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));

    it('Create a new fake distribution document line fails when adding landed cost line', () =>
        Test.withContext(
            async context => {
                const fakeDistriDoc = await context.read(
                    FakeDistributionDocument,
                    { number: 'fake-02' },
                    { forUpdate: true },
                );

                await fakeDistriDoc.lines.append({
                    site: '#US001',
                    item: '#STOFIFO',
                    quantity: 1,
                    stockSiteLinkedAddress: '#US001|840500',
                    landedCostLines: [
                        {
                            landedCost: { id: 'LandedCost001' },
                            actualCostAmountInCompanyCurrency: 105.88,
                            actualAllocatedCostAmountInCompanyCurrency: 0.0,
                            landedCostAllocation: {
                                line: { documentLine: { _id: 2 } },
                            },
                        },
                    ],
                });

                await assert.isRejected(fakeDistriDoc.$.save());

                assert.deepEqual(fakeDistriDoc.$.context.diagnoses, [
                    {
                        message: 'This document line cannot have a landed cost line: FakeDocumentLine.',
                        path: ['lines', '-1000000001'],
                        severity: 3,
                    },
                    {
                        message: 'The landed cost allocation is not consistent with the document line.',
                        path: ['lines', '-1000000001', 'landedCostLines', '-1000000004'],
                        severity: 3,
                    },
                ]);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.landedCostOption] },
        ));
});
