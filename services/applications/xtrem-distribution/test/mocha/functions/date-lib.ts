// tslint:disable:no-duplicate-string
import { date as Date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremDistribution from '../../../index';

describe('DateLib', () => {
    before(() => {});

    describe('addDaysSkipWeekEnd', () => {
        it('Should add 1 day to 2020-11-25 and return 2020-11-26', () => {
            const startDate = new Date(20201125);
            const endDate = new Date(20201126);
            const result = xtremDistribution.functions.addWorkDays(startDate, 1, 62); // work days only (0111110)
            assert.equal(result.valueOf(), endDate.valueOf());
        });

        it('Should add 2 days to 2020-11-25 and return 2020-11-27', () => {
            const startDate = new Date(20201125);
            const endDate = new Date(20201127);
            const result = xtremDistribution.functions.addWorkDays(startDate, 2, 62);
            assert.equal(result.valueOf(), endDate.valueOf());
        });

        it('Should add 3 days to 2020-11-25 and return 2020-11-30', () => {
            const startDate = new Date(20201125);
            const endDate = new Date(20201130);
            const result = xtremDistribution.functions.addWorkDays(startDate, 3, 62);
            assert.equal(result.valueOf(), endDate.valueOf());
        });

        it('Should add 4 days to 2020-11-25 and return 2020-11-30', () => {
            const startDate = new Date(20201125);
            const endDate = new Date(20201130);
            const result = xtremDistribution.functions.addWorkDays(startDate, 4, 62);
            assert.equal(result.valueOf(), endDate.valueOf());
        });

        it('Should add 5 days to 2020-11-25 and return 2020-11-30', () => {
            const startDate = new Date(20201125);
            const endDate = new Date(20201130);
            const result = xtremDistribution.functions.addWorkDays(startDate, 5, 62);
            assert.equal(result.valueOf(), endDate.valueOf());
        });

        it('Should add 5 days to 2020-11-25 and return 2020-11-30 null start date', () =>
            Test.withContext(
                () => {
                    const startDate = null;
                    const endDate = new Date(20201130);
                    const result = xtremDistribution.functions.addWorkDays(startDate, 5, 62);
                    assert.equal(result.valueOf(), endDate.valueOf());
                },
                { today: '2020-11-25' },
            ));
    });

    describe('subWorkDaysSkipWeekEnd', () => {
        it('Should subtract 1 day to 2020-11-25 and return 2020-11-24', () =>
            Test.withContext(context => {
                const requestedDeliveryDate = new Date(20201125);
                const orderDate = new Date(20201112);
                const doNotShipBeforeDate = new Date(20201112);
                const doNotShipAfterDate = new Date(20201129);
                const endDate = new Date(20201124);
                const result = xtremDistribution.functions.subWorkDays(context, {
                    requestedDeliveryDate,
                    orderDate,
                    doNotShipBeforeDate,
                    doNotShipAfterDate,
                    deliveryLeadTime: 1,
                    workDaysMask: 62,
                });
                assert.equal(result.valueOf(), endDate.valueOf());
            }));

        it('Should subtract 2 days to 2020-11-25 and return 2020-11-23', () =>
            Test.withContext(context => {
                const requestedDeliveryDate = new Date(20201125);
                const orderDate = new Date(20201112);
                const doNotShipBeforeDate = new Date(20201112);
                const doNotShipAfterDate = new Date(20201129);
                const endDate = new Date(20201123);
                const result = xtremDistribution.functions.subWorkDays(context, {
                    requestedDeliveryDate,
                    orderDate,
                    doNotShipBeforeDate,
                    doNotShipAfterDate,
                    deliveryLeadTime: 2,
                    workDaysMask: 62,
                });
                assert.equal(result.valueOf(), endDate.valueOf());
            }));

        it('Should subtract 3 days to 2020-11-25 and return 2020-11-23', () =>
            Test.withContext(context => {
                const requestedDeliveryDate = new Date(20201125);
                const orderDate = new Date(20201112);
                const doNotShipBeforeDate = new Date(20201112);
                const doNotShipAfterDate = new Date(20201129);
                const endDate = new Date(20201123);
                const result = xtremDistribution.functions.subWorkDays(context, {
                    requestedDeliveryDate,
                    orderDate,
                    doNotShipBeforeDate,
                    doNotShipAfterDate,
                    deliveryLeadTime: 3,
                    workDaysMask: 62,
                });
                assert.equal(result.valueOf(), endDate.valueOf());
            }));

        it('Should subtract 4 days to 2020-11-25 and return 2020-11-23', () =>
            Test.withContext(context => {
                const requestedDeliveryDate = new Date(20201125);
                const endDate = new Date(20201123);
                const orderDate = new Date(20201112);
                const doNotShipBeforeDate = new Date(20201112);
                const doNotShipAfterDate = new Date(20201129);
                const result = xtremDistribution.functions.subWorkDays(context, {
                    requestedDeliveryDate,
                    orderDate,
                    doNotShipBeforeDate,
                    doNotShipAfterDate,
                    deliveryLeadTime: 4,
                    workDaysMask: 62,
                });
                assert.equal(result.valueOf(), endDate.valueOf());
            }));

        it('Should subtract 5 days to 2020-11-25 and return 2020-11-20', () =>
            Test.withContext(context => {
                const requestedDeliveryDate = new Date(20201125);
                const endDate = new Date(20201120);
                const orderDate = new Date(20201112);
                const doNotShipBeforeDate = new Date(20201112);
                const doNotShipAfterDate = new Date(20201129);
                const result = xtremDistribution.functions.subWorkDays(context, {
                    requestedDeliveryDate,
                    orderDate,
                    doNotShipBeforeDate,
                    doNotShipAfterDate,
                    deliveryLeadTime: 5,
                    workDaysMask: 62,
                });
                assert.equal(result.valueOf(), endDate.valueOf());
            }));

        it('Should fail because of inconsistent do not ship dates', () =>
            Test.withContext(context => {
                const requestedDeliveryDate = new Date(20241018);
                const orderDate = new Date(20241007);
                const doNotShipBeforeDate = new Date(20241001);
                const doNotShipAfterDate = new Date(20240901);
                assert.throw(
                    () =>
                        xtremDistribution.functions.subWorkDays(context, {
                            requestedDeliveryDate,
                            orderDate,
                            doNotShipBeforeDate,
                            doNotShipAfterDate,
                            deliveryLeadTime: 5,
                            workDaysMask: 62,
                        }),
                    'The do-not-ship-before date needs to be before the do-not-ship-after date.',
                );
            }));

        it('Should fail because of impossibility to find a shipping date', () =>
            Test.withContext(context => {
                const requestedDeliveryDate = new Date(20241018);
                const orderDate = new Date(20241007);
                const doNotShipBeforeDate = new Date(20241001);
                const doNotShipAfterDate = new Date(20241031);
                assert.throw(
                    () =>
                        xtremDistribution.functions.subWorkDays(context, {
                            requestedDeliveryDate,
                            orderDate,
                            doNotShipBeforeDate,
                            doNotShipAfterDate,
                            deliveryLeadTime: 5,
                            workDaysMask: 0, // no work day at all
                        }),
                    'No shipping date found. You need to select a customer address with working days.',
                );
            }));
    });
});
