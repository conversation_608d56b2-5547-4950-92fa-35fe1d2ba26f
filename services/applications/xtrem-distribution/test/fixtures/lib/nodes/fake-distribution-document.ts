import type { Collection, Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDistribution from '../../../../lib';
import { FakeDocumentLine } from './index';

@decorators.subNode<FakeDistributionDocument>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseDistributionDocument,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class FakeDistributionDocument extends xtremDistribution.nodes.BaseDistributionDocument {
    @decorators.referencePropertyOverride<FakeDistributionDocument, 'businessRelation'>({
        node: () => xtremMasterData.nodes.Supplier,
    })
    override readonly businessRelation: Reference<xtremMasterData.nodes.Supplier>;

    @decorators.collectionPropertyOverride<FakeDistributionDocument, 'lines'>({
        node: () => FakeDocumentLine,
    })
    override readonly lines: Collection<FakeDocumentLine>;
}
