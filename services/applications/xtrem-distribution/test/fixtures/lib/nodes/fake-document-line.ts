import type { Reference } from '@sage/xtrem-core';
import { decorators, NodeStatus } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremDistribution from '../../../../lib';
import { FakeDistributionDocument } from './index';

@decorators.subNode<FakeDocumentLine>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseDistributionDocumentLine,
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.added) {
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStatus(cx, this);
            await xtremDistribution.events.DistributionDocumentLine.checkIfSameStockSite(cx, this);
        }
    },
})
export class FakeDocumentLine extends xtremDistribution.nodes.BaseDistributionDocumentLine {
    @decorators.referencePropertyOverride<FakeDocumentLine, 'document'>({
        node: () => FakeDistributionDocument,
    })
    override readonly document: Reference<FakeDistributionDocument>;

    @decorators.referenceProperty<FakeDocumentLine, 'companyCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        async getValue() {
            return (await this.document).companyCurrency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;
}
