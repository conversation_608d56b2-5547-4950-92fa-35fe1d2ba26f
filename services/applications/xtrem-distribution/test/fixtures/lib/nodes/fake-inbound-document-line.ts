import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremDistribution from '../../../../lib';
import { FakeInboundDocument } from './index';

@decorators.subNode<FakeInboundDocumentLine>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseInboundDocumentLine,
})
export class FakeInboundDocumentLine extends xtremDistribution.nodes.BaseInboundDocumentLine {
    @decorators.referencePropertyOverride<FakeInboundDocumentLine, 'document'>({
        node: () => FakeInboundDocument,
    })
    override readonly document: Reference<FakeInboundDocument>;
}
