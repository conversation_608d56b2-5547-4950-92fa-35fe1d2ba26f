import type { Collection } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremDistribution from '../../../../lib';
import { FakeOutboundDocumentLine } from './fake-outbound-document-line';

@decorators.subNode<FakeOutboundDocument>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseOutboundDocument,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class FakeOutboundDocument extends xtremDistribution.nodes.BaseOutboundDocument {
    @decorators.collectionPropertyOverride<FakeOutboundDocument, 'lines'>({
        node: () => FakeOutboundDocumentLine,
    })
    override readonly lines: Collection<FakeOutboundDocumentLine>;
}
