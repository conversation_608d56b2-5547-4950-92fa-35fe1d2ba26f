import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremDistribution from '../../../../lib';
import { FakeOutboundDocument } from './index';

@decorators.subNode<FakeOutboundDocumentLine>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseDistributionDocumentLine,
})
export class FakeOutboundDocumentLine extends xtremDistribution.nodes.BaseOutboundDocumentLine {
    @decorators.referencePropertyOverride<FakeOutboundDocumentLine, 'document'>({
        node: () => FakeOutboundDocument,
    })
    override readonly document: Reference<FakeOutboundDocument>;
}
