import { decorators } from '@sage/xtrem-core';
import * as xtremDistribution from '../../../../lib';

@decorators.subNode<FakeShipmentDocument>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseOutboundShipmentDocument,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class FakeShipmentDocument extends xtremDistribution.nodes.BaseOutboundShipmentDocument {}
