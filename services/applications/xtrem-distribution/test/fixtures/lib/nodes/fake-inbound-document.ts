import type { Collection } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremDistribution from '../../../../lib';
import { FakeInboundDocumentLine } from './fake-inbound-document-line';

@decorators.subNode<FakeInboundDocument>({
    isPublished: true,
    extends: () => xtremDistribution.nodes.BaseInboundDocument,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
})
export class FakeInboundDocument extends xtremDistribution.nodes.BaseInboundDocument {
    @decorators.collectionPropertyOverride<FakeInboundDocument, 'lines'>({
        node: () => FakeInboundDocumentLine,
    })
    override readonly lines: Collection<FakeInboundDocumentLine>;
}
