# The purpose of this test is to check on the creation of the sequence number will be added as part of a flow record

@manufacturing
Feature: manufacturing-crud-bom-revision

    Scenario: 01 - Activate bill of material revision service option
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
        Then the "Service options" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "billOfMaterialRevisionServiceOption"
        And the user selects the row 1 of the table field
        Then the user clicks the "Activate" dropdown action of the selected row of the table field
        Then a warn dialog appears on the main page
        When the user clicks the "OK" button of the Confirm dialog

    Scenario: 02 - Verify Payment tracking option is active in Service Options - required
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BomRevisionSequence"
        Then the "BOM revision sequence" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in header text fields
        And the user selects the "name" labelled text field on the main page
        And the user writes "BOM@001`" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "BOM Name / 0001" in the text field

        Then the user selects the "components" bound table field on the main page
        And the user clicks the "addComponent" bound action of the table field
        And the user selects the "components" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "type" bound nested field of the selected row in the table field
        And the user writes "Constant" in the "type" bound nested select field of the selected row in the table field
        And the user writes "3" in the "length" bound nested numeric field of the selected row in the table field
        And the user writes "BOM" in the "constant" bound nested text field of the selected row in the table field
        #alpha counter component
        And the user clicks the "addComponent" bound action of the table field
        And the user waits 5 seconds
        And the user selects the row 2 of the table field
        And the user clicks the "type" bound nested field of the selected row in the table field
        And the user writes "Sequence alpha" in the "type" bound nested select field of the selected row in the table field
        And the user writes "4" in the "length" bound nested numeric field of the selected row in the table field
        And the user writes "AAAA" in the "startValue" bound nested numeric field of the selected row in the table field
        And the user writes "ZZZZ" in the "endValue" bound nested numeric field of the selected row in the table field
        #numeric counter component
        And the user clicks the "addComponent" bound action of the table field
        And the user selects the row 3 of the table field
        And the user clicks the "type" bound nested field of the selected row in the table field
        And the user writes "Sequence number" in the "type" bound nested select field of the selected row in the table field
        And the user writes "5" in the "length" bound nested numeric field of the selected row in the table field
        And the user writes "00001" in the "startValue" bound nested numeric field of the selected row in the table field
        And the user writes "99999" in the "endValue" bound nested numeric field of the selected row in the table field
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: 03 Verify BOM data
        And the user selects the "Sequence length" labelled numeric field on the main page
        And the value of the numeric field is "12"

        Then the user selects the "components" bound table field on the main page

        And the user selects the row 1 of the table field
        And the value of the "Type" labelled nested text field of the selected row in the table field is "Constant"
        And the value of the "Constant" labelled nested text field of the selected row in the table field is "BOM"

        And the user selects the row 2 of the table field
        And the value of the "Type" labelled nested text field of the selected row in the table field is "Sequence alpha"
        And the value of the "Start value" labelled nested text field of the selected row in the table field is "AAAA"
        And the value of the "End value" labelled nested text field of the selected row in the table field is "ZZZZ"

        And the user selects the row 3 of the table field
        And the value of the "Type" labelled nested text field of the selected row in the table field is "Sequence number"
        And the value of the "Start value" labelled nested text field of the selected row in the table field is "00001"
        And the value of the "End value" labelled nested text field of the selected row in the table field is "99999"

    Scenario: 04 Duplicate record
        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header
        And the user selects the "ID *" labelled text field on a modal
        And the user writes "New ID@333" in the text field
        And the user clicks the "Duplicate" labelled business action button on a modal
        Then a toast containing text "Record was duplicated successfully." is displayed

    Scenario: 05 Verify duplicate record data
        When the user selects the "id" bound text field on the main page
        Then the value of the text field is "New ID@333"

        And the user selects the "Sequence length" labelled numeric field on the main page
        And the value of the numeric field is "12"

        Then the user selects the "components" bound table field on the main page

        And the user selects the row 1 of the table field
        And the value of the "Type" labelled nested text field of the selected row in the table field is "Constant"
        And the value of the "Constant" labelled nested text field of the selected row in the table field is "BOM"

        And the user selects the row 2 of the table field
        And the value of the "Type" labelled nested text field of the selected row in the table field is "Sequence alpha"
        And the value of the "Start value" labelled nested text field of the selected row in the table field is "AAAA"
        And the value of the "End value" labelled nested text field of the selected row in the table field is "ZZZZ"

        And the user selects the row 3 of the table field
        And the value of the "Type" labelled nested text field of the selected row in the table field is "Sequence number"
        And the value of the "Start value" labelled nested text field of the selected row in the table field is "00001"
        And the value of the "End value" labelled nested text field of the selected row in the table field is "99999"

    Scenario: 06 Update bom revision record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BomRevisionSequence"
        Then the "BOM revision sequence" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "ID" labelled column in the table field with value "New ID@333"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        And the user selects the "name" labelled text field on the main page
        And the user writes "BOM Name / 9999`" in the text field

        And the user selects the "Default" labelled checkbox field on the main page
        And the user ticks the checkbox field

        Then the user selects the "components" bound table field on the main page
        And the user clicks the "addComponent" bound action of the table field
        And the user selects the "components" bound table field on the main page
        And the user selects the row 4 of the table field
        And the user clicks the "type" bound nested field of the selected row in the table field
        And the user writes "Constant" in the "type" bound nested select field of the selected row in the table field
        And the user writes "2" in the "length" bound nested numeric field of the selected row in the table field
        And the user writes "YY" in the "constant" bound nested text field of the selected row in the table field

        # Add these lines when there is a step definition for delete button
        And the user selects the row with text "BOM" in the "Constant" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field

        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record updated" is displayed


        When the user selects the "name" bound text field on the main page
        Then the value of the text field is "BOM Name / 9999`"

        And the user selects the "Sequence length" labelled numeric field on the main page
        And the value of the numeric field is "11"

    Scenario: 07 Check bom default value in item page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "AR open item" in the navigation panel
        And the user clicks the record with the text "ID-091210" in the navigation panel

        When the user selects the "Manufactured" labelled switch field on the main page
        Then the user turns the switch field "ON"

        And selects the "Management" labelled navigation anchor on the main page
        Then the "Manufacturing" labelled block container on the main page is displayed

        # Verify Phantom switch field is displayed
        When the user selects the "Phantom" labelled switch field on the main page
        Then the "Phantom" labelled switch field on the main page is displayed
        And the switch field is set to "OFF"

        When the user selects the "BOM revision sequence number" labelled reference field on the main page
        Then the value of the reference field is "BOM Name / 9999`"
