# The goal of this test is to duplicate manufacturing features(Item, BOM, Routing, Work order)

@manufacturing
Feature: manufacturing-flow-duplication
    Scenario: 01 - Duplicate Item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel

        # Select the record
        And the user filters the "Name" labelled column in the table field with value "Pressure transducer"
        And the user selects the row with text "Pressure transducer" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field

        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header

        And the user selects the "Name" labelled text field on a modal
        And the user writes "Pressure transducer duplicate" in the text field

        And the user selects the "id" bound text field on a modal
        And the user writes "AUTO_TEST" in the text field

        And the user clicks the "Duplicate" labelled business action button on a modal
        Then a toast containing text "Record was duplicated successfully." is displayed

    Scenario: 02 - Duplicate BOM
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel

        # Select the record
        And the user filters the "Name" labelled column in the table field with value "Pressure transducer"
        When the user selects the row with text "Pressure transducer" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field

        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header

        # Select record created
        And the user selects the "Item" labelled reference field on a modal
        And the user writes "Pressure transducer duplicate" in the reference field
        And the user selects "Pressure transducer duplicate" in the reference field

        # Set Name value
        And the user selects the "Name" labelled text field on a modal
        And the user writes "Pressure transducer duplicate" in the text field

        # Duplicate the record
        And the user clicks the "Duplicate" labelled business action button on a modal
        Then a toast containing text "Record was duplicated successfully." is displayed

        # Verify Status value "Available to use"
        And the user selects the "Status" labelled dropdown-list field on the main page
        And the value of the dropdown-list field is "Available to use"

        # Verify Name value "Pressure transducer duplicate"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Pressure transducer duplicate"

    Scenario: 03 - Duplicate Routing
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel

        # Select the record
        And the user filters the "Name" labelled column in the table field with value "Pressure transducer"
        And the user selects the row with text "Pressure transducer" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field

        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header

        # Select record created
        And the user selects the "Item" labelled reference field on a modal
        And the user writes "Pressure transducer duplicate" in the reference field
        And the user selects "Pressure transducer duplicate" in the reference field

        # Set name value
        And the user selects the "Name" labelled text field on a modal
        And the user writes "Auto-Routing" in the text field

        # Duplicate the record
        And the user clicks the "Duplicate" labelled business action button on a modal
        Then a toast containing text "Record was duplicated successfully." is displayed

        # Verify Status value "Available to use"
        And the user selects the "Status" labelled dropdown-list field on the main page
        And the value of the dropdown-list field is "Available to use"

        # Verify Name value "Auto-Routing"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Auto-Routing"

        # Set a batch quantity
        When the user selects the "Batch quantity" labelled numeric field on the main page
        And the user writes "100" in the numeric field
        And the user presses Tab

        # Save changes
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: 04 - Duplicate Work Order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel

        # Select the record
        And the user filters the "Number" labelled column in the table field with value "TST_STD"
        And the user selects the row with text "TST_STD" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field

        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header

        # Set Work order number
        And the user selects the "Work order number" labelled text field on a modal
        And the user writes "Duplication-Test" in the text field

        # Duplicate the record
        And the user clicks the "Duplicate" labelled business action button on a modal
        Then a toast containing text "Record was duplicated successfully." is displayed

        # Verify Item value "Pressure transducer"
        And the user selects the "Item" labelled reference field on the main page
        And the value of the reference field is "Pressure transducer"

        # Selecting the Type using a dropdown-list field
        And the user selects the "type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Planned" in the dropdown-list field

        # Save changes
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
