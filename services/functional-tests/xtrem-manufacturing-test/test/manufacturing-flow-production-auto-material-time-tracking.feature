# The goal of this test is to use a given WO and test the production tracking, automatic material and time tracking
# to see if all the movements,issues and receipts are recorded correctly.
# Data
# Work Order Number : WO230002
# Items used         : Manufactured Item 2
#                      Material Item 2

@manufacturing
Feature: manufacturing-flow-production-auto-material-time-tracking

    Scenario: Create the production tracking with the automatic material and time tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Then the "Production tracking" titled page is displayed
        # Enter work order criteria
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "South Africa Headquarter & Warehouse" in the reference field
        And the user selects "South Africa Headquarter & Warehouse" in the reference field

        And the user selects the "From released item" labelled reference field on the main page
        And the user writes "Manufactured Item 2" in the reference field
        And the user selects "Manufactured Item 2" in the reference field

        # UPDATE WITH QA DATA
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "WO230002" in the reference field
        And the user selects "WO230002" in the reference field
        # UPDATE WITH QA DATA

        # UPDATE WITH QA DATA
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "WO230002" in the reference field
        And the user selects "WO230002" in the reference field
        # UPDATE WITH QA DATA
        And the user clicks in the "searchButton" bound button field on the main page

        And the user selects the "Work orders" labelled table field on the main page
        Then the table field is not empty


        # Select row
        # Dynamic row issue: Expected element not found when using the Name column to better select a row
        And the user selects the row with text "WO230002" in the "Number" labelled column header of the table field

        # Tick the left checkbox
        And the user ticks the main checkbox of the selected row in the table field
        # Get/Set the Actual qty from the Released qty
        And the user stores the value of the "Released qty." labelled nested numeric field of the selected row in the table field with the key "[ENV_Quantity]"
        And the user writes "[ENV_Quantity]" in the "Actual qty" labelled nested numeric field of the selected row in the table field
        # Tick the checkboxes for automatic material and time tracking
        And the user clicks the "Automatic material tracking" labelled nested field of the selected row in the table field
        And the user clicks the "Automatic time tracking" labelled nested field of the selected row in the table field

        # More actions - Stock detail
        And the user clicks the "Stock details" dropdown action of the selected row of the table field

        And the user selects the "Stock details" labelled table field on a modal
        Then the table field is empty

        When the user clicks the "Add a line" labelled header action button of the table field

        And the user selects the row with text "[ENV_Quantity]" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "[ENV_Quantity]" in the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field

        And the user writes "Bulk 01" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Bulk 01" in the "Location" labelled nested field of the selected row in the table field

        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field

        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast with text "Tracking records generated: 1" is displayed

    Scenario: Check the Work order main page details have been updated correctly
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "WO230002" in the navigation panel
        And the user clicks the record with the text "WO230002" in the navigation panel
        #Verify the status
        And the user selects the "Item" labelled reference field on the main page
        And the value of the reference field is "Manufactured Item 2"
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Completed quantity" labelled numeric field on the main page
        And the value of the numeric field is "24"

    Scenario: Check the Work order Operations nested grid details have been updated correctly
        # Check the Operations nested grid
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        #Verify Items details are updated correctly
        And the user selects row with text "1" in column with header "Operation number" in the nested grid field
        And the value of the "Status" labelled nested text field of the selected row in the nested grid field is "Completed"
        # Enhancement required
        # And the value of the "Completed quantity" labelled nested numeric field of the selected row in the nested grid field is "[ENV_Quantity]"
        And the value of the "Completed quantity" labelled nested numeric field of the selected row in the nested grid field is "24"
        And the value of the "Progress" labelled nested progress field of the selected row in the nested grid field is "100"
        And the value of the "Actual run time" labelled nested numeric field of the selected row in the nested grid field is "24.00 h"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the nested grid field is "R 250.0000"

    Scenario: Check the Work order components table field details have been updated correctly
        # Check the components table
        Given selects the "Components" labelled navigation anchor on the main page
        And the user selects the "Components" labelled table field on the main page
        #Verify Items details are updated correctly
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Completed"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "24 each"
        And the value of the "Progress" labelled nested progress field of the selected row in the table field is "100"

    Scenario: Check the Work order trackings table field details have been updated correctly
        # Check the trackings
        And selects the "Trackings" labelled navigation anchor on the main page
        And the user selects the "Trackings" labelled table field on the main page
        #Verify trackings are updated correctly
        And the user selects the row with text "Material tracking" in the "Document type" labelled column header of the table field

        And the user stores the value of the "Document number" labelled nested text field of the selected row in the table field with the key "[ENV_Tracking_document]"
        And the value of the "Document number" labelled nested link field of the selected row in the table field is "[ENV_Tracking_document]"
        And the value of the "Stock status" labelled nested text field of the selected row in the table field is "Completed"

        And the user selects the row with text "Production tracking" in the "Document type" labelled column header of the table field
        And the value of the "Document number" labelled nested link field of the selected row in the table field is "[ENV_Tracking_document]"
        And the value of the "Stock status" labelled nested text field of the selected row in the table field is "Completed"

        And the user selects the row with text "Time tracking" in the "Document type" labelled column header of the table field
        And the value of the "Document number" labelled nested link field of the selected row in the table field is "[ENV_Tracking_document]"
        And the user stores the value of the "Document number" labelled nested link field of the selected row in the table field with the key "[ENV_Tracking_document_link]"
        And the value of the "Stock status" labelled nested label field of the selected row in the table field is ""

    Scenario: Check the Stock journal inventory to verify the movements
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "South Africa Headquarter & Warehouse" in the filter of the table field
        And the user ticks the item with text "South Africa Headquarter & Warehouse" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter to get only items from WT document
        # Below is not working because Document has no filtering posibility
        # And the user filters the "Document" labelled column in the table field with value "[ENV_Tracking_document_link]"
        # Verify information for Item manufactured
        # Below might fail as we were not able to filter above to isolate from the stock receipt, To be fixed with above
        # And the value of the "Quantity" labelled nested numeric field of row 1 in the table field is "24 each"

        #Verify information for Material Item used
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Material Item 2" in the filter of the table field
        And the user ticks the item with text "Material Item 2" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

    # Below Will fail as we were not able to filter above to isolate from the stock receipt, To be fixed with above
    # And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-24 each"

    Scenario: Check Production receipt details have been updated correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTrackingInquiry"
        Then the "Production receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_Tracking_document]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # Verify
        And the user selects the "Work order" labelled reference field on the main page
        Then the value of the reference field is "WO230002"

        When the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # Verify Items table
        When the user selects the "Items" labelled table field on the main page
        And the user selects the row with text "Manufactured Item 2" in the "Item" labelled column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "24 each"

    Scenario: Check Material issue details have been updated correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/MaterialTrackingInquiry"
        Then the "Material issues" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Tracking_document]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Verify status for material issue
        And the user selects the "Stock status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Components" labelled table field on the main page
        #Verify quantity & details for item 1
        And the user selects the row with text "Material Item 2" in the "Item" labelled column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "24 each"
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "700" in the "Owner" labelled column header of the table field
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "24 each"
        And the user clicks the "Cancel" labelled business action button on a modal

    Scenario: Check Time tracking inquiry details have been updated correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTrackingInquiry"
        Then the "Time tracking inquiries" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        And the user selects the row with text "[ENV_Tracking_document]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Work order" labelled reference field on the main page
        And the value of the reference field is "WO230002"

        And the user selects the "Operations" labelled table field on the main page
        And the user selects the row with text "Labor Resource 1" in the "Resource  name" labelled column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "24 each"
        And the value of the "Run time" labelled nested numeric field of the selected row in the table field is "24.00 h"
# Check if this is an applicative bug or dev to be done?
# And the value of the "Completed" labelled nested checkbox field of the selected row in the table field is "true"
