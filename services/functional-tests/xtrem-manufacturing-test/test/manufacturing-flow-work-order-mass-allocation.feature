# This script does mass allocation on work orders, using various criteria and checking afterwards
# in the work order / allocation results function that the allocation / deallocation was correctly performed.
# Sites used = CHA-S01 & RUM-S01
# Existing work orders on site CHA-S01:
#   - TEST_MASS_ALLOC_1 (components = Raw material 1 & Raw material 2)
#   - TEST_MASS_ALLOC_2 (components = Raw material 3 & Raw material 4)
#   - TEST_MASS_ALLOC_5 (components = Raw material 1 & Raw material 4)
# Existing work orders on site RUM-S01:
#   - TEST_MASS_ALLOC_3 (components = Raw material 1 & Raw material 2)
#   - TEST_MASS_ALLOC_4 (components = Raw material 3 & Raw material 4)
#   - TEST_MASS_ALLOC_6 (components = Raw material 1 & Raw material 4)

@manufacturing @inventory

Feature: manufacturing-flow-work-order-mass-allocation

    Scenario: 01-Mass allocation - site = Site de Chavanod & item range = Raw material 1 to Raw material 2

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrderMassAllocation"

        Then the "Work order mass allocation" titled page is displayed

        ## Choose action = allocation ##
        When the user selects the "Action *" labelled select field on the main page
        And the user clicks in the select field
        And the user selects "Allocation" in the select field
        Then the value of the select field is "Allocation"

        ## Add meaningful description ##
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Filter items RAW1/RAW2" in the text field
        Then the value of the text field is "Filter items RAW1/RAW2"

        ## Enter company = Société S1 ##
        When the user selects the "Company *" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field
        Then the value of the reference field is "Société S1"

        ## Enter site = Site de Chavanod ##
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"

        ## Enter latest start date = today ##
        When the user selects the "Latest start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        ## Enter item range = RAW_MASS_ALLOC_1 to RAW_MASS_ALLOC_2 ##
        When the user selects the "From item" labelled reference field on the main page
        And the user writes "RAW_MASS_" in the reference field
        And the user selects "Raw material 1 - mass allocation" in the reference field
        Then the value of the reference field is "Raw material 1 - mass allocation"

        When the user selects the "To item" labelled reference field on the main page
        And the user writes "RAW_MASS_" in the reference field
        And the user selects "Raw material 2 - mass allocation" in the reference field
        Then the value of the reference field is "Raw material 2 - mass allocation"

        ## launch mass alocation ##
        And the user clicks the "Allocate" labelled business action button on the main page

        Then an info dialog appears on the main page
        And the dialog title is "Allocation request submitted" on the main page
        And the user clicks the "OK" button of the dialog

        And the user waits 2 seconds

    Scenario: 02-Check allocation status on dedicated work order components

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock-data/AllocationResult"

        Then the "Allocation results" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Filter items RAW1/RAW2" in the "description" bound column header of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"

        Given the user selects the "Details" labelled table field on the main page
        ## check Raw material 1 allocated on work order TEST_MASS_ALLOC_1 ##
        When the user selects the row with text "1 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 1 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        ## check Raw material 2 allocated on work order TEST_MASS_ALLOC_1 ##
        When the user selects the row with text "2 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 2 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        ## check Raw material 1 allocated on work order TEST_MASS_ALLOC_5 ##
        When the user selects the row with text "3 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 1 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"

    Scenario: 03-Mass allocation - site = Site de Rumilly & work order range = TEST_MASS_ALLOC_3 to TEST_MASS_ALLOC_6

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrderMassAllocation"

        Then the "Work order mass allocation" titled page is displayed

        ## Choose action = allocation ##
        When the user selects the "Action *" labelled select field on the main page
        And the user clicks in the select field
        And the user selects "Allocation" in the select field
        Then the value of the select field is "Allocation"

        ## Add meaningful description ##
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Filter work orders WO3/WO6" in the text field
        Then the value of the text field is "Filter work orders WO3/WO6"

        ## Enter company = Société S1 ##
        When the user selects the "Company *" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field
        Then the value of the reference field is "Société S1"

        ## Enter site = Site de Rumilly ##
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "Rum" in the reference field
        And the user selects "Site de Rumilly" in the reference field
        Then the value of the reference field is "Site de Rumilly"

        ## Enter latest start date = today ##
        When the user selects the "Latest start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        ## Enter work order range = TEST_MASS_ALLOC_3 to TEST_MASS_ALLOC_6 ##
        When the user selects the "From work order" labelled reference field on the main page
        And the user writes "TEST_MASS_" in the reference field
        And the user selects "TEST_MASS_ALLOC_3" in the reference field
        Then the value of the reference field is "TEST_MASS_ALLOC_3"

        When the user selects the "To work order" labelled reference field on the main page
        And the user writes "TEST_MASS_" in the reference field
        And the user selects "TEST_MASS_ALLOC_6" in the reference field
        Then the value of the reference field is "TEST_MASS_ALLOC_6"

        ## launch mass alocation ##
        And the user clicks the "Allocate" labelled business action button on the main page

        Then an info dialog appears on the main page
        And the dialog title is "Allocation request submitted" on the main page
        And the user clicks the "OK" button of the dialog

        And the user waits 2 seconds

    Scenario: 04-Check allocation status on dedicated work orders

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock-data/AllocationResult"

        Then the "Allocation results" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Filter work orders WO3/WO6" in the "description" bound column header of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"

        Given the user selects the "Details" labelled table field on the main page
        ## check allocations on work order TEST_MASS_ALLOC_3 ##
        And the user filters the "Document number" labelled column in the table field with value "TEST_MASS_ALLOC_3"
        When the user selects the row with text "10 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 1 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        When the user selects the row with text "20 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 2 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        ## check allocations on work order TEST_MASS_ALLOC_4 ##
        And the user filters the "Document number" labelled column in the table field with value "TEST_MASS_ALLOC_4"
        When the user selects the row with text "5 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 3 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        When the user selects the row with text "10 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 4 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        ## check allocations on work order TEST_MASS_ALLOC_6 ##
        And the user filters the "Document number" labelled column in the table field with value "TEST_MASS_ALLOC_6"
        When the user selects the row with text "4 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 1 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        When the user selects the row with text "8 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 4 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"

    Scenario: 05-Mass allocation - site = Site de Chavanod & released item range = Finished good - mass allocation 2 to Finished good - mass allocation 3

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrderMassAllocation"

        Then the "Work order mass allocation" titled page is displayed

        ## Choose action = allocation ##
        When the user selects the "Action *" labelled select field on the main page
        And the user clicks in the select field
        And the user selects "Allocation" in the select field
        Then the value of the select field is "Allocation"

        ## Add meaningful description ##
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Filter released items FG2/FG3" in the text field
        Then the value of the text field is "Filter released items FG2/FG3"

        ## Enter company = Société S1 ##
        When the user selects the "Company *" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field
        Then the value of the reference field is "Société S1"

        ## Enter site = Site de Chavanod ##
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"

        ## Enter latest start date = today ##
        When the user selects the "Latest start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        ## Enter released item range = FG_MASS_ALLOC_2 to FG_MASS_ALLOC_3 ##
        When the user selects the "From released item" labelled reference field on the main page
        And the user writes "FG_MASS_ALLOC_2" in the reference field
        And the user selects "Finished good - mass allocation 2" in the reference field
        Then the value of the reference field is "Finished good - mass allocation 2"

        When the user selects the "To released item" labelled reference field on the main page
        And the user writes "FG_MASS_ALLOC_3" in the reference field
        And the user selects "Finished good - mass allocation 3" in the reference field
        Then the value of the reference field is "Finished good - mass allocation 3"

        ## launch mass alocation ##
        And the user clicks the "Allocate" labelled business action button on the main page

        Then an info dialog appears on the main page
        And the dialog title is "Allocation request submitted" on the main page
        And the user clicks the "OK" button of the dialog

        And the user waits 2 seconds

    Scenario: 06-Check allocation status on dedicated work orders

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock-data/AllocationResult"

        Then the "Allocation results" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Filter released items FG2/FG3" in the "description" bound column header of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"

        Given the user selects the "Details" labelled table field on the main page
        ## check Raw material 3 allocated on work order TEST_MASS_ALLOC_2 ##
        When the user selects the row with text "2 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 3 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        ## check Raw material 4 allocated on work order TEST_MASS_ALLOC_2 ##
        When the user selects the row with text "4 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 4 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"
        ## check Raw material 4 allocated on work order TEST_MASS_ALLOC_5 ##
        When the user selects the row with text "6 each" in the "Quantity to process" labelled column header of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "Raw material 4 - mass allocation"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Work order"

    Scenario: 07-Check allocation status is "Completed" on each work order document

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"

        Then the "Work orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TEST_MASS_ALLOC" in the navigation panel
        And the user clicks the record with the text "TEST_MASS_ALLOC_1" in the navigation panel

        ## Verify allocation status on work order TEST_MASS_ALLOC_1 ##
        And the user selects the "Allocation status" labelled label field on the main page
        And the value of the label field is "Allocated"

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the record with the text "TEST_MASS_ALLOC_2" in the navigation panel

        ## Verify allocation status on work order TEST_MASS_ALLOC_2 ##
        When the user selects the "Allocation status" labelled label field on the main page
        Then the value of the label field is "Allocated"

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the record with the text "TEST_MASS_ALLOC_3" in the navigation panel

        ## Verify allocation status on work order TEST_MASS_ALLOC_3 ##
        When the user selects the "Allocation status" labelled label field on the main page
        Then the value of the label field is "Allocated"

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the record with the text "TEST_MASS_ALLOC_4" in the navigation panel

        ## Verify allocation status on work order TEST_MASS_ALLOC_4 ##
        When the user selects the "Allocation status" labelled label field on the main page
        Then the value of the label field is "Allocated"

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the record with the text "TEST_MASS_ALLOC_5" in the navigation panel

        ## Verify allocation status on work order TEST_MASS_ALLOC_5 ##
        When the user selects the "Allocation status" labelled label field on the main page
        Then the value of the label field is "Allocated"

        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the record with the text "TEST_MASS_ALLOC_6" in the navigation panel

        ## Verify allocation status on work order TEST_MASS_ALLOC_6 ##
        When the user selects the "Allocation status" labelled label field on the main page
        Then the value of the label field is "Allocated"
