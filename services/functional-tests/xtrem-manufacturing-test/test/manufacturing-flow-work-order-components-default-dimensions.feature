# The goal of this test is to verify the function of the set default dimensions dialog box of the components table

@manufacturing
Feature: manufacturing-flow-work-order-components-default-dimensions

    Scenario: 01 - Create direct work order and verify work order components inherited dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Site ZA1" in the reference field
        And the user selects "Site ZA1" in the reference field
        And the user selects the "Released item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Category" labelled reference field on the sidebar
        And the user selects the "Type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WOXT63310" in the text field
        And the user presses Tab
        And the user stores the value of the text field with the key "[ENV_WODI_NUM01]"
        And the user clicks the "Create" labelled business action button on the sidebar
        # Verify component dimensions
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Component description" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row 2 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row 3 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update components dimensions (Apply to new lines only) and verify components line dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_WODI_NUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user clicks the "Set default dimensions" labelled header action button of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "IT" in the reference field
        And the user selects "IT" in the reference field

        And the user selects the "Channel" labelled reference field on a modal
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field

        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add component line
        When the user selects the "Components" labelled table field on the main page
        And the user clicks the "Add" labelled header action button of the table field
        And the user selects the "Component number" labelled numeric field on the sidebar
        And the user writes "40" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the value of the numeric field is "10"
        Then the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify component dimensions (Pressure transmitter)
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Component description" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify component dimensions (Electrical connector)
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row 3 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update components dimensions (Apply to all lines) and verify components line dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_WODI_NUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user clicks the "Set default dimensions" labelled header action button of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        Then the user clicks the "Apply to all lines" labelled business action button on a modal
        # Add component line
        When the user selects the "Components" labelled table field on the main page
        And the user clicks the "Add" labelled header action button of the table field
        And the user selects the "Component number" labelled numeric field on the sidebar
        And the user writes "50" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the value of the numeric field is "10"
        Then the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify component dimensions (Pressure transducer)
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Component description" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is ""
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify component dimensions (Pressure transmitter)
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row 4 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify component dimensions (Electrical connector)
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row 3 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
