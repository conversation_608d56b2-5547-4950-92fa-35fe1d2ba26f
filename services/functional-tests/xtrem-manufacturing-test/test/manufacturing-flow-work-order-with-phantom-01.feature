# The goal of this test is to create a work order for a released item including phantom
# sub-assembly and to verify business rules
# site = Site de Chavanod CHA-S01
# items = Finished product = Finished good - with phantom B / phantom sub-assembly = PHANTOM_B
@manufacturing

Feature: manufacturing-flow-work-order-with-phantom-01

    Scenario: 1- Create work order WO_PHANTOM_01

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        ## Enter site = Site de Chavanod ##
        Given the user selects the "Site *" labelled reference field on the sidebar
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"
        ## Released item = Finished good - with phantom B ##
        Given the user selects the "Released item *" labelled reference field on the sidebar
        And the user writes "phantom" in the reference field
        And the user selects "Finished good - with phantom B" in the reference field
        Then the value of the reference field is "Finished good - with phantom B"
        ## Work order category = Normal ##
        Given the user selects the "Category *" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field
        Then the value of the reference field is "Normal"
        ## Type = Firm ##
        Given the user selects the "Type *" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        Then the value of the dropdown-list field is "Firm"
        ## Quantity = 10 ##
        And the user selects the "Quantity *" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        ## Requested start date = current date ##
        Given the user selects the "Requested start date *" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"
        ## Work order number = WO_PHANTOM_01 ##
        Given the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WO_PHANTOM_01" in the text field
        ## Create work order ##
        And the user clicks the "Create" labelled business action button on the sidebar

        And the "Work order WO_PHANTOM_01" titled page is displayed

    Scenario: 2- Check components' grid numbering & content

        # check re-numbering / components' description / required quantity #
        Given selects the "Components" labelled navigation anchor on the main page
        Given the user selects the "Components" labelled table field on the main page
        ## line 10 must mimic line 5 of the FG_PHANTOM_2 bill of material ##
        When the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B1"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "10.00 g"
        ## line 20 must mimic line 10 of the FG_PHANTOM_2 bill of material + include the scrap factor of 5% (20.00 g + 5% = 21.00 g) ##
        When the user selects the row with text "20" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B2"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "21.00 g"
        ## line 30 must mimic line 15 of the FG_PHANTOM_2 bill of material ##
        When the user selects the row with text "30" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B3"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "30.00 g"
        ## line 40 must mimic line 10 of the PHANTOM_B bill of material knowing that this BoM is for a base quantity of 1.00 kg and UOM link qtty = 10.00 kg for PHANTOM_B ##
        When the user selects the row with text "40" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B1"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "1,000.00 g"
        ## line 50 must mimic line 20 of the PHANTOM_B bill of material knowing that this BoM is for a base quantity of 1.00 kg and UOM link qtty = 10.00 kg for PHANTOM_B ##
        When the user selects the row with text "50" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B2"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "2,000.00 g"
        ## line 60 must mimic line 30 of the PHANTOM_B bill of material - attention: fixed quantiy type defined on this phantom component! ##
        When the user selects the row with text "60" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B3"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "300.00 g"

    Scenario: 3- Check work order can't be created for a phantom item

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        ## Enter site = Site de Chavanod ##
        Given the user selects the "Site *" labelled reference field on the sidebar
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"
        ## Released item = phantom items (that are manufactured) are excluded from the selection list ##
        Given the user selects the "Released item *" labelled reference field on the sidebar
        When the user clicks the lookup button of the reference field
        And the user selects the "bomCode" bound table field on a modal
        ## verify PHANTOM_A can't be selected as released item ##
        And the user filters the "Name" labelled column in the table field with value "Phantom A"
        Then the table field is empty
        ## verify PHANTOM_B can't be selected as released item ##
        And the user filters the "Name" labelled column in the table field with value "Phantom B"
        Then the table field is empty

        And the user clicks the Close button of the dialog

        And the user clicks the "Cancel" labelled business action button on the sidebar
