# The goal of this test is to verify the function of the set default dimensions dialog box of the opertaions grid

@manufacturing
Feature: manufacturing-flow-work-order-operations-default-dimensions

    Scenario: 01 - Create direct work order and verify work order operations inherited dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Site ZA2" in the reference field
        And the user selects "Site ZA2" in the reference field
        And the user selects the "Released item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Category" labelled reference field on the sidebar
        And the user selects the "Type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WOXT63320" in the text field
        And the user presses Tab
        And the user stores the value of the text field with the key "[ENV_WODI_NUM02]"
        And the user clicks the "Create" labelled business action button on the sidebar
        And the user waits 5 seconds
        # Verify operation resource dimensions
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "5" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update operations dimensions (Apply to new lines only) and verify operation resource line dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_WODI_NUM02]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "5" in column with header "Operation number" in the nested grid field
        And the user clicks the "Set default dimensions" labelled action of the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "IT" in the reference field
        And the user selects "IT" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add operation resource line
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user clicks the "Add" labelled action of the nested grid field
        And the user selects the "Operation number" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "OP20" in the text field
        And the user selects the "Capability level" labelled reference field on the sidebar
        And the user writes "Intermediate level of capability" in the reference field
        And the user selects "Intermediate level of capability" in the reference field
        And the user selects the "Setup time unit" labelled reference field on the sidebar
        And the user writes "Hour" in the reference field
        And the user selects "Hour" in the reference field
        And the user selects the "Run time unit" labelled reference field on the sidebar
        And the user writes "Hour" in the reference field
        And the user selects "Hour" in the reference field
        Then the user clicks the "OK" labelled business action button on the sidebar
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "20" in column with header "Operation number" in the nested grid field
        And the user clicks the "Add" action of the selected row in the nested grid field
        And the user selects the "Resource group" labelled reference field on the sidebar
        And the user writes "Labeler 15x12 1500 hour" in the reference field
        And the user selects "Labeler 15x12 1500 hour" in the reference field
        And the user selects the "Setup time" labelled numeric field on the sidebar
        And the user writes "0.5" in the numeric field
        And the user selects the "Run time" labelled numeric field on the sidebar
        And the user writes "1.5" in the numeric field
        Then the user clicks the "OK" labelled business action button on the sidebar
        # Verify operation resource dimensions (Operation number 20)
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "20" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "20" in column with header "Operation number" in the nested grid field
        And the user collapses the selected row of the nested grid field
        # Verify operation resource dimensions (Operation number 5)
        And the user selects row with text "5" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
