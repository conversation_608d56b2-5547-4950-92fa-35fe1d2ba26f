# The goals of this test are;
# 1. to verify that a new work order inherits dimensions, from the site and according to the company default
#    dimension rules,
# 2. to verify that when a new operation/component line is added to an existing work order without dimensions, the
#    new operation/component line dimensions are inherited from the work order released item

@manufacturing
Feature: manufacturing-flow-work-order-released-item-default-dimensions

    Scenario: 01 - Create direct work order and verify work order inherited dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Site ZA3" in the reference field
        And the user selects "Site ZA3" in the reference field
        And the user selects the "Released item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Category" labelled reference field on the sidebar
        And the user selects the "Type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "30" in the numeric field
        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WOXT63330" in the text field
        And the user presses Tab
        And the user stores the value of the text field with the key "[ENV_WODI_NUM03]"
        And the user clicks the "Create" labelled business action button on the sidebar
        And the user waits 5 seconds
        # Verify operation resource dimensions
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "5" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify component dimensions
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Component description" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify released item dimensions
        When the user clicks the "Dimensions" labelled business action button on the main page
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update released item dimensions - Apply to released to released only and verify dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_WODI_NUM03]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        When the user clicks the "Dimensions" labelled business action button on the main page
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "IT" in the reference field
        And the user selects "IT" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to released item only" labelled business action button on a modal
        # Add operation resource line
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user clicks the "Add" labelled action of the nested grid field
        And the user selects the "Operation number" labelled numeric field on the sidebar
        And the user writes "30" in the numeric field
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "OP30" in the text field
        And the user selects the "Capability level" labelled reference field on the sidebar
        And the user writes "Intermediate level of capability" in the reference field
        And the user selects "Intermediate level of capability" in the reference field
        And the user selects the "Setup time unit" labelled reference field on the sidebar
        And the user writes "Hour" in the reference field
        And the user selects "Hour" in the reference field
        And the user selects the "Run time unit" labelled reference field on the sidebar
        And the user writes "Hour" in the reference field
        And the user selects "Hour" in the reference field
        Then the user clicks the "OK" labelled business action button on the sidebar
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "30" in column with header "Operation number" in the nested grid field
        And the user clicks the "Add" action of the selected row in the nested grid field
        And the user selects the "Resource group" labelled reference field on the sidebar
        And the user writes "Labeler 15x12 1500 hour" in the reference field
        And the user selects "Labeler 15x12 1500 hour" in the reference field
        And the user selects the "Setup time" labelled numeric field on the sidebar
        And the user writes "0.5" in the numeric field
        And the user selects the "Run time" labelled numeric field on the sidebar
        And the user writes "1.5" in the numeric field
        And the user clicks in the "dimensionsButton" bound button field on the sidebar
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        And the user selects the "Resource for quantity produced" labelled checkbox field on the sidebar
        And the user ticks the checkbox field
        Then the user clicks the "OK" labelled business action button on the sidebar
        # Add component line
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user clicks the "Add" labelled header action button of the table field
        And the user selects the "Component number" labelled numeric field on the sidebar
        And the user writes "40" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the value of the numeric field is "30"
        And the user clicks in the "dimensionsButton" bound button field on the sidebar
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        Then the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify operation resource dimensions
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "30" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify component dimensions
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Component description" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update released item dimensions - Apply to all and verify dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_WODI_NUM03]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        When the user clicks the "Dimensions" labelled business action button on the main page
        Then the user clicks the "Apply to all" labelled business action button on a modal
        # Verify operation resource dimensions
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "5" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "5" in column with header "Operation number" in the nested grid field
        And the user collapses the selected row of the nested grid field
        And the user selects row with text "30" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal

        # Verify component dimensions
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Component description" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal

        When the user selects the "Components" labelled table field on the main page
        And the user selects the row 2 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
