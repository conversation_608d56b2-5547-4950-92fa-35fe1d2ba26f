# This test checks basic process for bill of material tracking: the bill is in status available to use, there is a valid
# routing for the released item and all the components have available stock.
# BOM used = BOM tracking FG1 / Site = CHA-S01

@manufacturing
Feature: manufacturing-flow-bom-tracking-with-routing-and-stock

    Scenario: 01 - Verify the user can access the BOM and track

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        # Search for bill of material
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "BOM tracking FG1" in the navigation panel
        And the user clicks the record with the text "BOM tracking FG1" in the navigation panel
        # Verify BOM tracking FG1 is correctly loaded and is in status "available to use"
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "BOM tracking FG1"
        Given the user selects the "Status" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Available to use"
        # Verify Track button appears on the page
        Then the "Track" labelled business action button on the main page is visible
        # Click on Track button
        Then the user clicks the "Track" labelled business action button on the main page

    Scenario: 02 - Verify stock receipt detail screen is displayed with correct values

        Then the dialog title is "Production tracking"

        # Check values and state of header fields
        Given the user selects the "Item" labelled reference field on a modal
        Then the value of the reference field is "BOM tracking FG1 - with routing"
        And the reference field is read-only
        Given the user selects the "Site" labelled reference field on a modal
        Then the value of the reference field is "Site de Chavanod"
        And the reference field is read-only
        Given the user selects the "Quantity to produce" labelled numeric field on a modal
        Then the value of the numeric field is "1"
        And the numeric field is enabled
        Given the user selects the "Date *" labelled date field on a modal
        Then the value of the date field is a generated date with value "T"
        And the date field is enabled
        Given the user selects the "Category *" labelled reference field on a modal
        Then the value of the reference field is "Normal"

    Scenario: 03 - Verify the user can view the component shortage grid

        # Click on Check stock button to display Component shortage grid
        Given the user clicks in the "stockCheck" bound button field on a modal
        Then the "Component shortage" labelled table field on a modal is valid
        Given the user selects the "Component shortage" labelled table field on a modal
        And the user selects the row with text "Component 1 - without lot" in the "Component description" labelled column header of the table field
        Then the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "100 each"
        Then the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "1 each"
        Then the value of the "Shortage" labelled nested numeric field of the selected row in the table field is "0"
        And the user selects the row with text "Component 2 - lot managed" in the "Component description" labelled column header of the table field
        Then the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "100 each"
        Then the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "2 each"
        Then the value of the "Shortage" labelled nested numeric field of the selected row in the table field is "0"

    Scenario: 04 - Enter the stock details to create the BOM tracking

        # Check stock details' grid is empty
        Given the user selects the "stockDetails" bound table field on a modal
        Then the table field is empty

        When the user clicks the "Add a line" labelled header action button of the table field

        And the user selects the row with text "1 each" in the "Quantity in stock unit" labelled column header of the table field
        Then the user writes "1" in the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field

        And the user writes "Internal 1" in the "Location" labelled nested reference field of the selected row in the table field
        Then the user selects "Internal 1" in the "Location" labelled nested field of the selected row in the table field

        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        Then the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field

        And the user writes "LOT1" in the "Lot" labelled nested reference field of the selected row in the table field
        Then the user selects "LOT1 (New)" in the "Lot" labelled nested field of the selected row in the table field

        And the user clicks the "OK" button of the Custom dialog

    Scenario: 05 - Verify trackings' tab of the BOM now includes tracking information

        # Force refresh of the bill of material to get the correct status
        And the user refreshes the screen
        And selects the "Trackings" labelled navigation anchor on the main page

        Given the user selects the "trackings" bound table field on the main page
        And the user selects the row with text "1" in the "Quantity" labelled column header of the table field
        Then the user clicks the "Open line panel" inline action button of the selected row in the table field

        Then the "Bill of material BOM tracking FG1" titled sidebar is displayed

        And selects the "Work order" labelled navigation anchor on the sidebar

        Given the user selects the "workOrder" bound table field on the sidebar
        When the user selects the row with text "CHA-S01" in the "Site" labelled column header of the table field
        And the user stores the value of the "number" bound nested link field of the selected row in the table field with the key "[ENV_WO1_Number]"

        And selects the "Material tracking" labelled navigation anchor on the sidebar

        Given the user selects the "materialTracking" bound table field on the sidebar
        When the user selects the row with text "CHA-S01" in the "Site" labelled column header of the table field
        And the user stores the value of the "number" bound nested link field of the selected row in the table field with the key "[ENV_MAT1_Number]"

        And selects the "Production tracking" labelled navigation anchor on the sidebar

        Given the user selects the "productionTracking" bound table field on the sidebar
        When the user selects the row with text "CHA-S01" in the "Site" labelled column header of the table field
        And the user stores the value of the "number" bound nested link field of the selected row in the table field with the key "[ENV_PROD1_Number]"

        And selects the "Time tracking" labelled navigation anchor on the sidebar

        Given the user selects the "timeTracking" bound table field on the sidebar
        When the user selects the row with text "CHA-S01" in the "Site" labelled column header of the table field
        And the user stores the value of the "number" bound nested link field of the selected row in the table field with the key "[ENV_TIM1_Number]"

        And the user clicks the "Cancel" button of the dialog on the sidebar

    Scenario: 06 - Verify work order is created as expected

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # Search for work order created using variable [ENV_WO1_Number]
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_WO1_Number]" in the navigation panel
        And the user clicks the record with the text "[ENV_WO1_Number]" in the navigation panel

        # Verify work order status = Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"
        # Verify progress status of production
        When the user selects the "productionCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "100"
        # Verify progress status of process (time tracking)
        When the user selects the "processCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "100"
        # Verify progress status of material
        When the user selects the "materialCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "100"

        # Verify trackings' tab is populated with the 3 trackings created from the BOM
        Given selects the "Trackings" labelled navigation anchor on the main page
        When the user selects the "Trackings" labelled table field on the main page
        Then the user selects the row with text "Material tracking" in the "Document type" labelled column header of the table field
        And the value of the "number" bound nested link field of the selected row in the table field is "[ENV_MAT1_Number]"
        And the user selects the row with text "Production tracking" in the "Document type" labelled column header of the table field
        And the value of the "number" bound nested link field of the selected row in the table field is "[ENV_PROD1_Number]"
        And the user selects the row with text "Time tracking" in the "Document type" labelled column header of the table field
        And  the value of the "number" bound nested link field of the selected row in the table field is "[ENV_TIM1_Number]"
