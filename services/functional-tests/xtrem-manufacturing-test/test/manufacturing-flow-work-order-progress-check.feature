# This test aims to verify the progress bar values in work order operation, work order operation resources and
# work order header when using different time units for setup and run and doing partial tracking (automatic or not)

@manufacturing
Feature: manufacturing-flow-work-order-progress-check

    Scenario: 01 - Production tracking creation with partial automatic time tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Then the "Production tracking" titled page is displayed
        # Enter work order criteria
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Site de Chavanod" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        # UPDATE WITH QA DATA
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "PROGRESS_01" in the reference field
        And the user selects "PROGRESS_01" in the reference field
        # UPDATE WITH QA DATA

        # UPDATE WITH QA DATA
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "PROGRESS_01" in the reference field
        And the user selects "PROGRESS_01" in the reference field
        # UPDATE WITH QA DATA
        And the user clicks in the "searchButton" bound button field on the main page

        And the user selects the "Work orders" labelled table field on the main page
        Then the table field is not empty

        # Select row
        # Dynamic row issue: Expected element not found when using the Name column to better select a row
        And the user selects the row with text "PROGRESS_01" in the "Number" labelled column header of the table field

        # Tick the left checkbox
        And the user ticks the main checkbox of the selected row in the table field
        # Set the Actual qty
        And the user writes "5 each" in the "Actual qty" labelled nested numeric field of the selected row in the table field

        # Tick the checkboxes for automatic material and time tracking
        And the user clicks the "Automatic material tracking" labelled nested field of the selected row in the table field
        And the user clicks the "Automatic time tracking" labelled nested field of the selected row in the table field

        # More actions - Stock detail
        And the user clicks the "Stock details" dropdown action of the selected row of the table field

        And the user selects the "Stock details" labelled table field on a modal
        Then the table field is empty

        When the user clicks the "Add a line" labelled header action button of the table field

        And the user selects the row with text "5 each" in the "Quantity in stock unit" labelled column header of the table field

        And the user writes "Internal 1" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Internal 1" in the "Location" labelled nested field of the selected row in the table field

        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field

        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast with text "Tracking records generated: 1" is displayed

    Scenario: 02 - Check all progress fields values in work order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "PROGRESS_01" in the navigation panel
        And the user clicks the record with the text "PROGRESS_01" in the navigation panel

        # Verify progress status of production
        When the user selects the "productionCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "50"
        # Verify progress status of process (time tracking)
        When the user selects the "processCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "51.22"
        # Verify progress status of material
        When the user selects the "materialCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "50"

        #Verify the status
        And the user selects the "Item" labelled reference field on the main page
        And the value of the reference field is "Item for progress bar testing"
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        And the user selects the "Completed quantity" labelled numeric field on the main page
        And the value of the numeric field is "5"

        #Verify component planned costs
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "Design" in column with header "Name" in the nested grid field
        And the value of the "Actual run time" labelled nested text field of the selected row in the nested grid field is "10.00 h"
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "51.21951219512195"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Operator - no group entered" in column with header "Name" in the nested grid field
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "51.21951219512195"
        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "Design" in column with header "Name" in the nested grid field
        And the user collapses the selected row of the nested grid field


        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "Assembly" in column with header "Name" in the nested grid field
        And the value of the "Actual run time" labelled nested text field of the selected row in the nested grid field is "10.00 h"
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "51.21951219512195"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Machine - no group entered" in column with header "Name" in the nested grid field
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "51.21951219512195"
        And the user selects row with text "Tool - no group entered" in column with header "Name" in the nested grid field
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "51.21951219512195"

    Scenario: 03 - time tracking creation with time higher or lower than the planned one
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTracking"
        Then the "Time tracking" titled page is displayed
        # Enter work order criteria
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Site de Chavanod" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        # UPDATE WITH QA DATA
        And the user selects the "From work order number" labelled reference field on the main page
        And the user writes "PROGRESS_01" in the reference field
        And the user selects "PROGRESS_01" in the reference field
        # UPDATE WITH QA DATA

        # UPDATE WITH QA DATA
        And the user selects the "To work order number" labelled reference field on the main page
        And the user writes "PROGRESS_01" in the reference field
        And the user selects "PROGRESS_01" in the reference field
        # UPDATE WITH QA DATA
        And the user clicks in the "searchButton" bound button field on the main page

        And the user selects the "workOrderOperations" labelled table field on the main page
        Then the table field is not empty

        When the user selects the row with text "OPE_GROUP_0" in the "Actual resource" labelled column header of the table field
        And the user writes "5" in the "Actual quantity" labelled nested numeric field of the selected row in the table field
        And the user writes "8" in the "Actual run time" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Completed" labelled nested field of the selected row in the table field

        When the user selects the row with text "MAC_GROUP_0" in the "Actual resource" labelled column header of the table field
        And the user writes "5" in the "Actual quantity" labelled nested numeric field of the selected row in the table field
        And the user writes "12" in the "Actual run time" labelled nested numeric field of the selected row in the table field

        When the user selects the row with text "TOOL_GROUP_0" in the "Actual resource" labelled column header of the table field
        And the user writes "12" in the "Actual run time" labelled nested numeric field of the selected row in the table field

        When the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated:" is displayed

        And the user selects the "workOrderOperations" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Completed"
        And the user selects the row 2 of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Completed"
        And the user selects the row 3 of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Completed"

    Scenario: 04 - check new values of progress fields in work order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "PROGRESS_01" in the navigation panel
        And the user clicks the record with the text "PROGRESS_01" in the navigation panel

        # Verify progress status of production
        When the user selects the "productionCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "50"
        # Verify progress status of process (time tracking)
        When the user selects the "processCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "100"
        # Verify progress status of material
        When the user selects the "materialCompletionPercentage" bound progress field on the main page
        Then the value of the progress field is "50"

        #Verify the status
        And the user selects the "Item" labelled reference field on the main page
        And the value of the reference field is "Item for progress bar testing"
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        And the user selects the "Completed quantity" labelled numeric field on the main page
        And the value of the numeric field is "5"

        #Verify component planned costs
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "Design" in column with header "Name" in the nested grid field
        And the value of the "Actual run time" labelled nested text field of the selected row in the nested grid field is "18.00 h"
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "90.2439024390244"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Operator - no group entered" in column with header "Name" in the nested grid field
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "90.2439024390244"
        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "Design" in column with header "Name" in the nested grid field
        And the user collapses the selected row of the nested grid field


        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "Assembly" in column with header "Name" in the nested grid field
        And the value of the "Actual run time" labelled nested text field of the selected row in the nested grid field is "22.00 h"
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "109.7560975609756"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Machine - no group entered" in column with header "Name" in the nested grid field
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "109.7560975609756"
        And the user selects row with text "Tool - no group entered" in column with header "Name" in the nested grid field
        Then the value of the "progress" labelled nested progress field of the selected row in the nested grid field is "109.7560975609756"
