
@manufacturing
Feature: manufacturing-flow-production-tracking

    Scenario: Verify user is able to create a production tracking (qty less than planned in WO)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        And the "Production tracking" titled page is displayed
        ##Selecting the site using a reference field##
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de Saint Denis" in the reference field
        ##Selecting existing WO##
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "WRKPRDTRC01" in the reference field
        And the user selects "WRKPRDTRC01" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Work orders" labelled table field on the main page
        And the user selects the row with text "WRKPRDTRC01" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user writes "3" in the "Actual qty." labelled nested numeric field of the selected row in the table field
        Then the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Required"
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "Stock details" labelled table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row 1 of the table field
        And the value of the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field is "3 each"
        And the user selects the row with text "3 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user selects the "Work orders " labelled table field on the main page
        And the user selects the row with text "WRKPRDTRC01" in the "Number" labelled column header of the table field
        And the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Entered"
        And the user clicks the "Generate" labelled business action button on the main page
        And a toast containing text "Tracking records generated:" is displayed

    Scenario: Verify the user is able to verify production receipt (qty less than planned in WO)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/ProductionTrackingInquiry"
        Then the "Production receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "WRKPRDTRC01" in the "workOrder__number " bound column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "workOrder__number" bound nested field of the selected row in the table field
        And the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_WTNUM01]"
        And the user selects the "Stock status" labelled label field on the main page
        And the value of the label field is "Completed"

        And the user refreshes the screen

        And the user selects the "Items" labelled table field on the main page
        And the user waits 5 second
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "3 each"

    Scenario: Verify the user is able to verify production tracking details on the work order (qty less than planned in WO)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "WRKPRDTRC01" in the navigation panel
        And the user clicks the record with the text "WRKPRDTRC01" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        ##Verify the completed quantity
        And the user selects the "totalActualQuantity" bound numeric field on the main page
        And the value of the numeric field is "3"
        ##Verify the completed percentage
        And the user selects the "productionCompletionPercentage" bound progress field on the main page
        And the value of the progress field is "60%"


    Scenario: Verify the user is able to verify details of the stock on the stock journal enquiry page (qty less than planned in WO)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Entrepot de  Saint  Denis" in the filter of the table field
        And the user ticks the item with text "Entrepot de  Saint  Denis" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure transducer" in the filter of the table field
        And the user ticks the item with text "Pressure transducer" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        ##Verifying the completed quantity
        When the user selects the row with text "[ENV_WTNUM01]" in the "Document" labelled column header of the table field
        Then the value of the "Quantity" labelled nested text field of the selected row in the table field is "3 each"
        ##Verifying the quantity and cost movement
        Then the value of the "Valued cost" labelled nested text field of the selected row in the table field is "€ 5.0000"
        And the value of the "Movement amount" labelled nested text field of the selected row in the table field is "€ 15.00"
        And the value of the "Cost variance" labelled nested text field of the selected row in the table field is "€ -5.0000"
        And the value of the "Amount variance" labelled nested text field of the selected row in the table field is "€ -15.00"
        And the value of the "Non-absorbed amount" labelled nested text field of the selected row in the table field is "€ -15.00"

    ############ Create a production tracking so that quantity of production exceeds than planned in WO ################

    Scenario: Verify user is able to create a production tracking (qty greater than planned in WO)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        And the "Production tracking" titled page is displayed
        ##Selecting the site using a reference field##
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de Saint Denis" in the reference field
        ##Selecting existing WO##
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "WRKPRDTRC01" in the reference field
        And the user selects "WRKPRDTRC01" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Work orders" labelled table field on the main page
        And the user selects the row with text "WRKPRDTRC01" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user writes "7" in the "Actual qty." labelled nested numeric field of the selected row in the table field
        # Verify Warning message displayed, and confirm
        Then the text in the body of the dialog contains "The total actual quantity will be greater than the released quantity." on the main page

        When the user clicks the "OK" button of the Message dialog on the main page
        Then the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Required"
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "Stock details" labelled table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row 1 of the table field
        And the value of the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field is "7 each"
        And the user selects the row with text "7 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user selects the "Work orders " labelled table field on the main page
        And the user selects the row with text "WRKPRDTRC01" in the "Number" labelled column header of the table field
        And the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Entered"
        And the user clicks the "Generate" labelled business action button on the main page
        And a toast containing text "Tracking records generated:" is displayed


    Scenario: Verify the user is able to verify production receipt (qty greater than planned in WO)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/ProductionTrackingInquiry"
        Then the "Production receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "WRKPRDTRC01" in the "Work order" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Work order" labelled nested field of the selected row in the table field
        And the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_WTNUM02]"
        And the user selects the "Stock status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Items" labelled table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "7 each"

    Scenario: Verify the user is able to verify production tracking details on the work order (qty greater than planned in WO)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "WRKPRDTRC01" in the navigation panel
        And the user clicks the record with the text "WRKPRDTRC01" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        ##Verify the completed quantity
        And the user selects the "totalActualQuantity" bound numeric field on the main page
        And the value of the numeric field is "10"
        ##Verify the completed percentage
        And the user selects the "productionCompletionPercentage" bound progress field on the main page
        And the value of the progress field is "200%"


    Scenario: Verify the user is able to verify details of the stock on the stock journal enquiry page (qty greater than planned in WO)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Entrepot de  Saint  Denis" in the filter of the table field
        And the user ticks the item with text "Entrepot de  Saint  Denis" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure transducer" in the filter of the table field
        And the user ticks the item with text "Pressure transducer" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        ##Verifying the completed quantity
        When the user selects the row with text "[ENV_WTNUM02]" in the "Document" labelled column header of the table field
        Then the value of the "Quantity" labelled nested text field of the selected row in the table field is "7 each"
        ##Verifying the quantity and cost movement
        Then the value of the "Valued cost" labelled nested text field of the selected row in the table field is "€ 5.0000"
        And the value of the "Movement amount" labelled nested text field of the selected row in the table field is "€ 35.00"
        And the value of the "Cost variance" labelled nested text field of the selected row in the table field is "€ -5.0000"
        And the value of the "Amount variance" labelled nested text field of the selected row in the table field is "€ -35.00"
        And the value of the "Non-absorbed amount" labelled nested text field of the selected row in the table field is "€ -35.00"
