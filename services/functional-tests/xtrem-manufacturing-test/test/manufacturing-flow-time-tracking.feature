# The goal of this test is to verify the time tracking process from end to finish without issues.
# Data
# Work Order Number : WORKORD001
# Item used         : Pressure Transducer

@manufacturing
Feature: manufacturing-flow-time-tracking

    Scenario: Verify the user can create a time tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTracking"
        Then the "Time tracking" titled page is displayed
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        When the user selects the "From released item" labelled reference field on the main page
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        When the user selects the "To released item" labelled reference field on the main page
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "workOrderOperations" bound table field on the main page
        Then the table field is not empty
        When the user selects the row with text "WORKORD001" in the "Number" labelled column header of the table field
        And the user writes "3" in the "Actual quantity" labelled nested numeric field of the selected row in the table field
        And the user writes "24" in the "Actual setup time" labelled nested numeric field of the selected row in the table field
        And the user writes "12" in the "Actual run time" labelled nested numeric field of the selected row in the table field
        When the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated:" is displayed

    Scenario: Verifify that the user can add a new operation to a Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTracking"
        Then the "Time tracking" titled page is displayed
        #Adding new opertaion
        When the user clicks the "addOperation" bound header action button on the main page
        And the user selects the "Work order number" labelled reference field on the sidebar
        And the user writes "WORKORD001" in the reference field
        And the user selects "WORKORD001" in the reference field
        And the user selects the "Operation description" labelled text field on the sidebar
        And the user writes "Testing" in the text field
        And the user selects the "Capability level" labelled reference field on the sidebar
        And the user writes "Intermediate level of capability" in the reference field
        And the user selects "Intermediate level of capability" in the reference field
        And the user selects the "Setup time unit" labelled reference field on the sidebar
        And the user writes "Hour" in the reference field
        And the user selects "Hour" in the reference field
        And the user selects the "Run time unit" labelled reference field on the sidebar
        And the user writes "Hour" in the reference field
        And the user selects "Hour" in the reference field
        And the user selects the "Resource" labelled reference field on the sidebar
        And the user writes "ETIQ1512-1560" in the reference field
        And the user selects "ETIQ1512-1560" in the reference field
        And the user presses Enter
        And the user selects the "Actual quantity *" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Actual setup time" labelled numeric field on the sidebar
        And the user writes "4" in the numeric field
        And the user selects the "Actual run time" labelled numeric field on the sidebar
        And the user writes "4" in the numeric field
        Then the user clicks the "OK" labelled business action button on the sidebar
        ##Table
        When the user selects the "workOrderOperations" bound table field on the main page
        And the user selects the row with text "Testing" in the "Description" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated:" is displayed

    Scenario: Verify time tracking of OP1 on Work order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "WORKORD001" in the navigation panel
        And the user clicks the record with the text "WORKORD001" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        And the user selects the "processCompletionPercentage" bound progress field on the main page
        And the value of the progress field is "146666.67"
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "OP1" in column with header "Name" in the nested grid field
        Then the value of the "Completed quantity" labelled nested text field of the selected row in the nested grid field is "3"
        And the value of the "Actual setup time" labelled nested text field of the selected row in the nested grid field is "24.00 h"
        And the value of the "Actual run time" labelled nested text field of the selected row in the nested grid field is "12.00 h"
        And the value of the "Progress" labelled nested progress field of the selected row in the nested grid field is "120000"

    Scenario: Verify time tracking of new operation
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "WORKORD001" in the navigation panel
        And the user clicks the record with the text "WORKORD001" in the navigation panel
        ## Verify new operation added
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "productionOperations" bound nested grid field on the main page
        And the user selects row with text "Testing" in column with header "Name" in the nested grid field
        Then the value of the "Completed quantity" labelled nested text field of the selected row in the nested grid field is "5"
        And the value of the "Actual setup time" labelled nested text field of the selected row in the nested grid field is "4.00 h"
        And the value of the "Actual run time" labelled nested text field of the selected row in the nested grid field is "4.00 h"
        Then the value of the "Progress" labelled nested progress field of the selected row in the nested grid field is "100"
