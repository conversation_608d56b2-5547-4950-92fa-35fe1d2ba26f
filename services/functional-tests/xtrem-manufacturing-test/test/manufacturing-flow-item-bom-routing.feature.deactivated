
NOTE: There is currently new development in progress causing this automation test to fail. Deactivating this test until dev completed
Refactoring required after dev is complete. https://confluence.sage.com/display/XTREEM/%5BV36%5D+-Requirements+-+Standard+cost+adjustments+and+roll+up



# The goal of this test is to check if a manufactured items cost in item-site gets updated through creating a bom and routing for it.
# It makes use of following Items : Material Item 1 [Material_Item__01] - Purchased item with cost 10
#                                 : Manufactured Item 1 [Manufactured_Item_01] - Manufactured item with calculated cost
# It makes use of following Resources : Labour resource 1 [LABOR_RES_01] - Cost R10 per hour
#                                     : Group resource 1 [GROUP_RES_01] with Machine Resource 1 [MACHINE_RES_01]
@manufacturing
Feature: manufacturing-flow-item-bom-routing
    Scenario: Verify the Manufactured item in master-data
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "Manufactured Item 1"
        And the user closes the custom reference filter of the "Name" labelled column in the table field
        When the user selects the row with text "Manufactured Item 1" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        #Verify the item is manufactured checked
        When the user selects the "Manufactured" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
 
        #Verify the item site
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "Item-sites" labelled table field on the main page
 
        # Select first row and check existence of the BOM and Routing columns, should be empty as we did not create BOM and Routing for it yet
        And the user selects the row with text "South Africa Headquarter & Warehouse" in the "Site" labelled column header of the table field
        Then the value of the "Bill of material" labelled nested link field of the selected row in the table field is ""
        Then the value of the "Routing" labelled nested link field of the selected row in the table field is ""
 
        And the user clicks the "Edit" dropdown action of row 1 of the table field
        # Side bar opens
        And the user selects the "Item name" labelled reference field on the sidebar
        Then the value of the reference field is "Manufactured Item 1"
    
        When the user selects the "Site" labelled reference field on the sidebar
        Then the value of the reference field is "South Africa Headquarter & Warehouse"
 
        When the user selects the "Valuation method" labelled dropdown-list field on the sidebar
        Then the value of the dropdown-list field is "Standard cost"
 
        # Move to Costs tab
        When selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "Costs" labelled table field on the sidebar
        And the user clicks the "Edit" dropdown action of row 1 of the table field
        # Side bar opens
        # Verify the details
        And the user selects the "Cost category" labelled reference field on the sidebar
        Then the value of the reference field is "Standard cost"
 
        When the user selects the "Quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "1"
        
        When the user selects the "Calculated" labelled switch field on the sidebar
        Then the switch field is set to "ON"
 
        # Return to main page
        And the user selects the "Cancel" labelled business action button on the sidebar
        And the user selects the "Cancel" labelled business action button on the sidebar
    
 
    Scenario: Verify the material item in master-data 
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "Material Item 1"
        And the user closes the custom reference filter of the "Name" labelled column in the table field
        When the user selects the row with text "Material Item 1" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
 
        #Verify the item is purchased checked
        When the user selects the "Purchased" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
 
        #Verify the item site
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "Item-sites" labelled table field on the main page
        And the user clicks the "Edit" dropdown action of row 1 of the table field
        # Side bar opens
        And the user selects the "Item name" labelled reference field on the sidebar
        Then the value of the reference field is "Material Item 1"
    
        When the user selects the "Site" labelled reference field on the sidebar
        Then the value of the reference field is "South Africa Headquarter & Warehouse"
 
        When the user selects the "Valuation method" labelled dropdown-list field on the sidebar
        Then the value of the dropdown-list field is "Standard cost"
 
        # Move to Costs tab
        When selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "Costs" labelled table field on the sidebar
        And the user clicks the "Edit" dropdown action of row 1 of the table field
        # Side bar opens
        # Verify the details
        And the user selects the "Cost category" labelled reference field on the sidebar
        Then the value of the reference field is "Standard cost"
 
        When the user selects the "Quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "1"
        
        When the user selects the "Material" labelled numeric field on the sidebar
        Then the value of the numeric field is "10.0000" 
 
        When the user selects the "Total cost" labelled numeric field on the sidebar
        Then the value of the numeric field is "10.0000" 
 
        
        # Return to main page
        And the user selects the "Cancel" labelled business action button on the sidebar
        And the user selects the "Cancel" labelled business action button on the sidebar
 
 
 
    Scenario: Create a BOM for the manufactured item 
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user selects the "Create" labelled business action button on the main page
        Then the "Bill of material" titled page is displayed
    
        # Fill in details
        And the user selects the "Status" labelled dropdown-list field on the main page
        And the user writes "Available to use" in the dropdown-list field 
        And the user selects "Available to use" in the dropdown-list field 
 
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Manufactured Item 1 BOM" in the text field
        And the user stores the value of the text field with the key "[ENV_BOM_Name]" 
 
        And the user selects the "Item" labelled reference field on the main page
        And the user writes "Manufactured Item 1" in the reference field 
        And the user selects "Manufactured Item 1" in the reference field 
 
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "South Africa Headquarter & Warehouse" in the reference field 
        And the user selects "South Africa Headquarter & Warehouse" in the reference field 
 
        And the user selects the "Base quantity" labelled numeric field on the main page
        And the user writes "1.00" in the numeric field 
 
        # Add components
        And the user selects the "components" bound table field on the main page
        #And the user clicks the "Add" labelled header action button of the table field
        And the user clicks the "addComponentLine" bound action of the table field
        
        # Side bar opens
        And the user selects the "Component number" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
 
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Material Item 1" in the reference field
        And the user selects "Material Item 1" in the reference field
 
        And the user selects the "UOM link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
 
        And the user selects the "Add" labelled business action button on the sidebar
        And the user selects the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
 
        # Test the Component line item Standard cost
        When the user selects the "Components" labelled table field on the main page
        Then the value of the "Standard cost" labelled nested numeric field of row 1 in the table field is "10.00000"
 
        # Test the Total standard cost
        When the user selects the "Total standard cost" labelled numeric field on the main page
        Then the value of the numeric field is "10.00" 
 
    Scenario: Test the updating of the BOM to see if it changes the standard cost and total standard cost
        When the user selects the "Components" labelled table field on the main page
        And the user clicks the "Edit" dropdown action of row 1 of the table field
        # Make changes
        And the user selects the "UOM link quantity" labelled numeric field on the sidebar
        And the user writes "2" in the numeric field
        # Update and Save
        And the user selects the "Update" labelled business action button on the sidebar
        And the user selects the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
 
        # Test the Component line item Standard cost
        When the user selects the "Components" labelled table field on the main page
        Then the value of the "Standard cost" labelled nested numeric field of row 1 in the table field is "20.00000"
 
        # Test the Total standard cost
        When the user selects the "Total standard cost" labelled numeric field on the main page
        Then the value of the numeric field is "20.00" 
 
    Scenario: Create a routing for the manufactured item
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        When the user selects the "Create" labelled business action button on the main page
 
        And the user selects the "Status" labelled dropdown-list field on the main page
        And the user writes "Available to use" in the dropdown-list field 
        And the user selects "Available to use" in the dropdown-list field 
 
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Manufactured Item 1 Routing" in the text field
        And the user stores the value of the text field with the key "[ENV_Routing_Name]"
 
        And the user selects the "Item" labelled reference field on the main page
        And the user writes "Manufactured Item 1" in the reference field 
        And the user selects "Manufactured Item 1" in the reference field 
 
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "South Africa Headquarter & Warehouse" in the reference field 
        And the user selects "South Africa Headquarter & Warehouse" in the reference field 
 
        And the user selects the "Time unit" labelled reference field on the main page
        And the user writes "Hour" in the reference field 
        And the user selects "Hour" in the reference field 
 
        And the user selects the "Batch quantity" labelled numeric field on the main page
        And the user writes "1.000" in the numeric field 
 
        # Add operations to the routing
        # And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user clicks the "Add" labelled action of the nested grid field
        
        And the user selects the "Operation number" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
 
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Make it 1" in the text field
 
        And the user selects the "Capability level" labelled reference field on the sidebar
        And the user writes "Low" in the reference field
        And the user selects "Low level of capability" in the reference field
 
        And the user selects the "Production step" labelled switch field on the sidebar
        And the switch field is set to "OFF"
        
        And the user selects the "Save" labelled business action button on the sidebar
 
        # Add the second operation
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user clicks the "Add" labelled action of the nested grid field
 
        And the user selects the "Operation number" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
 
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Make it 2" in the text field
 
        And the user selects the "Capability level" labelled reference field on the sidebar
        And the user writes "Low" in the reference field
        And the user selects "Low level of capability" in the reference field
 
        # Set this as the production step
        And the user selects the "Production step" labelled switch field on the sidebar
        And the switch field is set to "OFF"
        And the user turns the switch field "ON"
 
        And the user selects the "Save" labelled business action button on the sidebar
        And the user selects the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
 
 
    Scenario: Add the labor and group resource to the 2 operations
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "10" in column with header "Operation number" in the nested grid field
        # Add a resource to the operation
        And the user clicks the "Add" action of the selected row in the nested grid field
        Then the "Add resource or resource group" titled sidebar is displayed
 
        When the user selects the "Resource or resource group" labelled reference field on the sidebar
        And the user writes "Labor Resource 1" in the reference field
        And the user selects "Labor Resource 1" in the reference field
 
        When the user selects the "Setup time" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
 
        When the user selects the "Run time" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
 
        And the user selects the "Save" labelled business action button on the sidebar
        And the user selects the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
 
        # Add resource to the second operation
        And the user selects row with text "20" in column with header "Operation number" in the nested grid field
        And the user clicks the "Add" action of the selected row in the nested grid field
        Then the "Add resource or resource group" titled sidebar is displayed
 
        When the user selects the "Resource or resource group" labelled reference field on the sidebar
        And the user writes "Group Resource 1" in the reference field
        And the user selects "Group Resource 1" in the reference field
 
        When the user selects the "Setup time" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
 
        When the user selects the "Run time" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
 
        And the user selects the "Save" labelled business action button on the sidebar
        And the user selects the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
 
    Scenario: Test the deletion of a production step operation in the routing
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "20" in column with header "Operation number" in the nested grid field
        And the user clicks the "Delete" action of the selected row in the nested grid field
        And the user selects the "Save" labelled business action button on the main page
        # Bug here with warning dialog, Remove below 2 lines after it is fixed.
        Then a warn dialog appears on the main page
        And the user clicks the "Discard" button of the Confirm dialog
 
        # Check if the error notification dispalys
        Then a toast containing text "You must enter at least one production step." is displayed
        And the user dismisses all the notifications
    
    Scenario: Set operation 10 as production step and delete operation 20
 
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "10" in column with header "Operation number" in the nested grid field
        And the user clicks the "Edit" action of the selected row in the nested grid field
 
        # Update the production step switch
        And the user selects the "Production step" labelled switch field on the sidebar
        And the user turns the switch field "ON"
 
        And the user selects the "Save" labelled business action button on the sidebar
        And the user selects the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
 
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "20" in column with header "Operation number" in the nested grid field
        And the user clicks the "Delete" action of the selected row in the nested grid field
        And the user selects the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Go to the Item-site and cost tab, Click calculate and see if it has updated the manufactured item cost accordingly for material and labour
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "Manufactured Item 1"
        And the user closes the custom reference filter of the "Name" labelled column in the table field
        When the user selects the row with text "Manufactured Item 1" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "Item-sites" labelled table field on the main page



        And the user clicks the "Edit" dropdown action of row 1 of the table field
        # Move to Costs tab
        When selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "Costs" labelled table field on the sidebar
        And the user clicks the "Edit" dropdown action of row 1 of the table field
        # Check the current cost is 0
        When the user selects the "Material" labelled numeric field on the sidebar
        Then the value of the numeric field is "0.0000" 
        # if Labor vs Labour - Check locale
        When the user selects the "Labor" labelled numeric field on the sidebar
        Then the value of the numeric field is "0.0000"
        When the user selects the "Total cost" labelled numeric field on the sidebar
        Then the value of the numeric field is "0.0000" 

        # Calculate the new cost details
        And the user clicks in the "calculate" bound button field on the sidebar
        # Recheck amounts
        When the user selects the "Material" labelled numeric field on the sidebar
        Then the value of the numeric field is "20.0000" 
        When the user selects the "Labor" labelled numeric field on the sidebar
        Then the value of the numeric field is "20.0000"
        When the user selects the "Total cost" labelled numeric field on the sidebar
        Then the value of the numeric field is "40.0000" 

        # Return to main page
        And the user selects the "Cancel" labelled business action button on the sidebar
        And the user selects the "Cancel" labelled business action button on the sidebar


    Scenario: Check if the Bill of material is accessible from Item-Site
        
        # Select row
        And the user selects the "Item-sites" labelled table field on the main page
        And the user selects the row with text "South Africa Headquarter & Warehouse" in the "Site" labelled column header of the table field
        
        # Verify column is populated and click
        # [ENV_BOM_Name] = "Manufactured Item 1 BOM"
        Then the value of the "Bill of material" labelled nested link field of the selected row in the table field is "[ENV_BOM_Name]"
        And the user clicks the "Bill of material" labelled nested field of the selected row in the table field
       
        # Opens full screen dialog
        Then the text in the header of the dialog is "Bill of materials" on a full width modal
        And the "Bill of materials" labelled navigation anchor is selected

#      Enhancement request required to Interact with full screen dialog: https://jira.sage.com/browse/XT-57454
#      # Verify Status
#      When the user selects the "Status" labelled dropdown-list field on the full screen dialog
#      Then the value of the dropdown-list field is "Available to use"
#      # Verify BOM name
#      When the user selects the "Name" labelled text field on the full screen dialog
#      Then the value of the text field is "[ENV_BOM_Name]"
#      # Verify Item name
#      When the user selects the "Item" labelled reference field on the full screen dialog
#      Then the value of the reference field is "[ENV_Item_Name]"
       # Close page
        When the user clicks the Close button of the dialog on a full width modal

        # Should return back to Item-site
        Then the "Item Manufactured Item 1" titled page is displayed


    Scenario: Check if the Routing page is accessible from Item-Site
        
        # Select row
        And the user selects the "Item-sites" labelled table field on the main page
        And the user selects the row with text "South Africa Headquarter & Warehouse" in the "Site" labelled column header of the table field
        # [ENV_Routing_Name] = "Manufactured Item 1 Routing"
        Then the value of the "Routing" labelled nested link field of the selected row in the table field is "[ENV_Routing_Name]" 
        And the user clicks the "Routing" labelled nested field of the selected row in the table field

        # Opens full screen dialog
        Then the text in the header of the dialog is "Routing" on a full width modal
        


#      Enhancement request required to Interact with full screen dialog: https://jira.sage.com/browse/XT-57454
#       # Verify Status
        #When the user selects the "Status" labelled dropdown-list field on the full screen dialog
        #Then the value of the dropdown-list field is "Available to use"
#
#       # Verify BOM name
#       When the user selects the "Name" labelled text field on the full screen dialog
#       Then the value of the text field is "[ENV_Routing_Name]"
#
#       # Verify Item name
#       When the user selects the "Item" labelled reference field on the full screen dialog
#       Then the value of the reference field is "[ENV_Item_Name]"


        # Close page
        When the user clicks the Close button of the dialog on a full width modal

        # Should return back to Item-site
        Then the "Item Manufactured Item 1" titled page is displayed
