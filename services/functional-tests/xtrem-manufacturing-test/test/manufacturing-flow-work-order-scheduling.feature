# This script does basic test of the work order scheduling.
# For the moment, a fixed date is used as start and end time fields are label fields (generated date step definitions
# are only usable for date field)
@manufacturing

Feature: manufacturing-flow-work-order-scheduling

    Scenario: 01-Create work order without operations - verify the scheduling status is "Not managed"

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        ## Enter site = Site de Chavanod ##
        Given the user selects the "Site *" labelled reference field on the sidebar
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"
        ## Released item = WO planning item ##
        Given the user selects the "Released item *" labelled reference field on the sidebar
        And the user writes "WO scheduling" in the reference field
        And the user selects "WO scheduling item" in the reference field
        Then the value of the reference field is "WO scheduling item"
        ## Work order category = Assembly ##
        Given the user selects the "Category *" labelled reference field on the sidebar
        And the user writes "Assembly" in the reference field
        And the user selects "Assembly only" in the reference field
        Then the value of the reference field is "Assembly only"
        ## Type = Firm ##
        Given the user selects the "Type *" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        Then the value of the dropdown-list field is "Firm"
        ## Quantity = 1 ##
        And the user selects the "Quantity *" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        ## Requested start date = 10/26/2025 (daylight saving / France / at 3:00 it will be 2:00) ##
        Given the user selects the "Requested start date *" labelled date field on the sidebar
        And the user writes "10/26/2025" in the date field
        Then the value of the date field is "10/26/2025"
        ## Work order number = WO_SCHED_1 ##
        Given the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WO_SCHED_1" in the text field
        ## Create work order ##
        And the user clicks the "Create" labelled business action button on the sidebar

        And the "Work order WO_SCHED_1" titled page is displayed

        ## check scheduling status = "Not managed" as there is no operations on the work order ##
        When the user selects the "Scheduling status" labelled label field on the main page
        Then the value of the label field is "Not managed"

    Scenario: 02-Add operations to work order WO_SCHED_1 - verify the scheduling status is "Not scheduled"

        ## Use the corresponding checkbox to add operations on the previous work order ##
        Given the user selects the "Uses routing" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then an info dialog appears on the main page
        And the dialog title is "Add operations" on the main page
        And the user clicks the "Continue" button of the dialog

        And a toast with text "Operations added to work order WOSCHED1" is displayed
        And the user dismisses all the toasts

        ## check scheduling status has now changed to "Not scheduled" ##
        When the user selects the "Scheduling status" labelled label field on the main page
        Then the value of the label field is "Not scheduled"

        ## check operations' grid ##
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        ## Check Planned setup time and Planned run time values ##
        When the user selects row with text "10" in column with header "Operation number" in the nested grid field
        Then the value of the "Planned setup time" labelled nested numeric field of the selected row in the nested grid field is "30.00 min"
        And the value of the "Planned run time" labelled nested numeric field of the selected row in the nested grid field is "2.00 h"

    Scenario: 03-Run scheduling for work order WO_SCHED_1 - verify scheduling status is "Scheduled"

        Given selects the "General" labelled navigation anchor on the main page
        When the user clicks the "Schedule" labelled business action button on the main page

        And the "Work order WO_SCHED_1" titled page is displayed

        ## check scheduling status has now changed to "Scheduled" ##
        When the user selects the "Scheduling status" labelled label field on the main page
        Then the value of the label field is "Scheduled"

        ## check operations' grid ##
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        When the user selects row with text "10" in column with header "Operation number" in the nested grid field
        ## Check start and end dates are calculated correctly as scheduling has been run ##
        And the user expands the selected row of the nested grid field
        And the user selects row with text "MAC3" in column with header "ID" in the nested grid field
        Then the value of the "Start time" labelled nested label field of the selected row in the nested grid field is "2025-10-26 00:00:00"
        And the value of the "End time" labelled nested label field of the selected row in the nested grid field is "2025-10-26 02:30:00"

    Scenario: 04-Change released quantity on work order WO_SCHED_1 - verify scheduling status is "To reschedule"

        ## released quantity = 2 ##
        Given selects the "General" labelled navigation anchor on the main page
        When the user selects the "Released quantity" labelled numeric field on the main page
        When the user writes "2" in the numeric field
        Then the user presses Enter
        # Then the value of the numeric field is "2"
        And the user clicks the "Save" labelled business action button on the main page

        And the user waits 2 seconds

        Then a toast containing text "Record updated" is displayed

        And the user dismisses all the toasts

        ## check scheduling status has now changed to "To reschedule" ##
        When the user selects the "Scheduling status" labelled label field on the main page
        Then the value of the label field is "To reschedule"

        ## check operations' grid ##
        When selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        When the user selects row with text "10" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "MAC3" in column with header "ID" in the nested grid field
        ## Check start and end dates/times are the same as previously ##
        Then the value of the "Start time" labelled nested label field of the selected row in the nested grid field is "2025-10-26 00:00:00"
        And the value of the "End time" labelled nested label field of the selected row in the nested grid field is "2025-10-26 02:30:00"

    Scenario: 05-Run scheduling for work order WO_SCHED_1 - verify daylight saving is applied as expected

        Given selects the "General" labelled navigation anchor on the main page
        When the user clicks the "Schedule" labelled business action button on the main page

        And the "Work order WO_SCHED_1" titled page is displayed

        ## check scheduling status has now changed to "Scheduled" ##
        When the user selects the "Scheduling status" labelled label field on the main page
        Then the value of the label field is "Scheduled"

        ## check operations' grid ##
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        When the user selects row with text "10" in column with header "Operation number" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "MAC3" in column with header "ID" in the nested grid field
        ## Check Planned setup time and Planned run time values - total time = 4h30min ##
        Then the value of the "Planned setup time" labelled nested numeric field of the selected row in the nested grid field is "30.00 min"
        And the value of the "Planned run time" labelled nested numeric field of the selected row in the nested grid field is "4.00 h"
        ## Check end time is calculated removing 1 hour to the expected time ##
        Then the value of the "Start time" labelled nested label field of the selected row in the nested grid field is "2025-10-26 00:00:00"
        ### Note: start time = 00:00:00 production time = 04:30:00 (Planned setup time = 30 min + Planned run time = 4 hours) end time should be 04:30:00 - 1 hour ###
        And the value of the "End time" labelled nested label field of the selected row in the nested grid field is "2025-10-26 03:30:00"
