# This script tests that a work order number cannot be duplicated

@manufacturing
Feature: manufacturing-flow-work-order-unique-work-order-number

    Sc<PERSON>rio: 01 - Create first work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "site" labelled reference field on the sidebar
        And the user writes "Site ZA2" in the reference field
        And the user selects "Site ZA2" in the reference field
        And the user selects the "releasedItem" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "category" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field
        And the user selects the "type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user selects the "quantity" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And the user selects the "workOrderNumber" labelled text field on the sidebar
        And the user writes "WON70651" in the text field
        And the user stores the value of the text field with the key "[ENV_1ST_WON]"
        And the user clicks the "Create" labelled business action button on the sidebar
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Pending"


    Scenario: 02 - Create second work order with same work order number
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "site" labelled reference field on the sidebar
        And the user writes "Site ZA3" in the reference field
        And the user selects "Site ZA3" in the reference field
        And the user selects the "releasedItem" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "category" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field
        And the user selects the "type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user selects the "quantity" labelled numeric field on the sidebar
        And the user writes "30" in the numeric field
        And the user selects the "workOrderNumber" labelled text field on the sidebar
        And the user writes "WON70651" in the text field
        And the user clicks the "Create" labelled business action button on the sidebar
        And a error toast containing text "The work order number is already in use. Enter a unique work order reference." is displayed
