#This test can only be executed with sage.
#The goal of this test is to verify that the user can create Work order with pre generation of serial numbers
#Item used: Pressure Transducer strain gauge
@manufacturing
Feature: manufacturing-flow-wo-pre-generation-of-sn

    Scenario: 01 - Verify the user can create work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #creating work order
        #Site
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        #Item details
        And the user selects the "Released item" labelled reference field on the sidebar
        And the user writes "Pressure transducer strain gauge" in the reference field
        And the user selects "Pressure transducer strain gauge" in the reference field
        And the user selects the "Category" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field
        And the user selects the "Type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Planned" in the dropdown-list field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WOORDER_1" in the text field
        And the user stores the value of the text field with the key "[WONumber]"
        #Create
        Then the user clicks the "Create" labelled business action button on the sidebar

    Scenario: 02 - Verify the user can update the Work order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "501" in the "Site" labelled column header of the table field
        And the user clicks the "Site" labelled nested field of the selected row in the table field
        #Searching the WO number
        And the user searches for "[WONumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user clicks the "save" labelled business action button on the main page
        And the user refreshes the screen
        And the user clicks the "Pregenerate serial numbers" labelled business action button on the main page
        And the user clicks the "Confirm pregeneration" button of the Confirm dialog
        # check 5 serial numbers have been generated
        Then a toast containing text "5 serial numbers generated" is displayed

        And the user dismisses all the toasts

        # access serial numbers details to get the SN created and store values in keys
        When the user clicks the "View serial numbers" labelled business action button on the main page

        And the user selects the "serialNumbers" bound table field on the sidebar
        # store each SN in a key
        And the user selects the row 1 of the table field
        And the user stores the value of the "Serial number" labelled nested numeric field of the selected row in the table field with the key ""
        And the user selects the row 2 of the table field
        And the user stores the value of the "Serial number" labelled nested numeric field of the selected row in the table field with the key ""
        And the user selects the row 3 of the table field
        And the user stores the value of the "Serial number" labelled nested numeric field of the selected row in the table field with the key ""
        And the user selects the row 4 of the table field
        And the user stores the value of the "Serial number" labelled nested numeric field of the selected row in the table field with the key ""
        And the user selects the row 5 of the table field
        And the user stores the value of the "Serial number" labelled nested numeric field of the selected row in the table field with the key ""

        Then the user clicks the "OK" labelled business action button on the sidebar
        #Table selection and allocate stock
        #Item component 1
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        #And the user selects the row with text "959 each" in the "Available quantity" labelled column header of the table field
        And the user selects the row with text "23000004" in the "Lot" labelled column header of the table field
        And the user writes "5" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        Then the user clicks the "save" bound business action button on a modal
        #Item component 2
        When the user selects the "productionComponents" bound table field on the main page
        And the user selects the row with text "Transducer body 30x5" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "Accepted" in the "Quality control *" labelled column header of the table field
        And the user writes "5" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "save" bound business action button on a modal
        And the user waits 3 seconds
        #Item component 3
        When the user selects the "productionComponents" bound table field on the main page
        And the user selects the row with text "Electrical connector" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "23000003" in the "Lot" labelled column header of the table field
        And the user writes "5" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        Then the user clicks the "save" bound business action button on a modal
        And the user waits 3 seconds
        #Item component 4
        When the user selects the "productionComponents" bound table field on the main page
        And the user selects the row with text "Light emitting diode" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "1,000 each" in the "Available quantity" labelled column header of the table field
        And the user writes "50" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user selects the row with text "To be assigned" in the "Serial number" labelled column header of the table field
        Then the user clicks the "Serial number" labelled nested field of the selected row in the table field
        #Assigning serial numbers
        When the user selects the "allocationSerialNumbers" bound table field on a modal
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "SN001035" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "SN001035" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "50" in the "Quantity" labelled nested numeric field of the selected row in the table field
        Then the user clicks the "save" bound business action button on a modal

    Scenario: 03 - Verify the user can create Production tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Then the "Production tracking" titled page is displayed
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "From released item" labelled reference field on the main page
        And the user writes "Pressure transducer strain gauge" in the reference field
        And the user selects "Pressure transducer strain gauge" in the reference field
        Then the user clicks in the "searchButton" bound button field on the main page
        When the user selects the "workOrders" labelled table field on the main page
        And the user selects the row with text "[WONumber]" in the "Number" labelled column header of the table field
        #And the user selects the row with text "WOORDER_1" in the "Number" labelled column header of the table field

        And the user writes "5" in the "Actual qty" labelled nested numeric field of the selected row in the table field
        Then the value of the "Stock detail status" labelled nested numeric field of the selected row in the table field is "Required"
        #Adding stock
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user selects the row with text "To be assigned" in the "Serial number" labelled column header of the table field
        Then the user clicks the "Serial number" labelled nested field of the selected row in the table field
        #Assigning Serial numbers
        When the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user waits 5 seconds
        And the user selects the row 1 of the table field
        And the user writes "[ENV_SERIAL1]" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "[ENV_SERIAL1]" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "3" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        Then a toast containing text "Assign serial numbers to match the stock record quantity. Numbers to assign:" is displayed
        #Verify the partial assigned text
        When the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Accepted" in the "Quality control *" labelled column header of the table field
        And the value of the "Serial number" labelled nested text field of the selected row in the table field is "Partially assigned"
        And the user clicks the "OK" labelled business action button on a modal
        Then a toast containing text "Assign serial numbers to match the stock record quantity. Numbers to assign:" is displayed
        #Assigning right numbers of serial numbers
        When the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user writes "5" in the "Quantity" labelled nested numeric field of the selected row in the table field
        #Verify the "Entered" text in stock details
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Accepted" in the "Quality control *" labelled column header of the table field
        And the value of the "Serial number" labelled nested text field of the selected row in the table field is "Assigned"
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated: 1" is displayed

    Scenario: 04 - Verify the user user can create Production receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTrackingInquiry"
        Then the "Production receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[WONumber]" in the "Work order" labelled column header of the table field
        #And the user selects the row with text "WOORDER_1" in the "Work order" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer strain gauge" in the "Item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Assigned" in the "Serial number" labelled column header of the table field
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field
        And the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user selects the row with text "5" in the "Quantity" labelled column header of the table field
        And the value of the "From serial number" labelled nested numeric field of the selected row in the table field is "[ENV_SERIAL1]"
        Then the value of the "To serial number" labelled nested numeric field of the selected row in the table field is "[ENV_SERIAL5]"


    Scenario: 05 - Verify the user can create Stock detailed inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure transducer strain gauge" in the filter of the table field
        And the user ticks the item with text "Pressure transducer strain gauge" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # Verify available qty & serial numbers
        Then the user selects the row with text "Accepted" in the "Quality control" labelled column header of the table field
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "5 each"
    # TODO: Serial number values may be compared later when serial number stock inquiry is available, so we cannot check them now.
    #       For now let's keep the idea as a reminder not to be forced to invent the wheel twice.
    #
    # And the value of the "Starting serial number" labelled nested numeric field of the selected row in the table field is "[ENV_SERIAL1]"
    # And the value of the "Ending serial number" labelled nested numeric field of the selected row in the table field is "[ENV_SERIAL5]"
    # #Verify Serial numbers info
    # And the user selects the "lines" bound table field on the main page
    # And the user clicks the "Serial number information" dropdown action of row 1 of the table field
    # #Verify that the serial numbers amount correspond with available quantity, and also with Start and end serial numbers
    # And the user selects the "serialNumberLines" bound table field on the sidebar

    # And the value of the "Serial number" labelled nested numeric field of row 1 in the table field is "[ENV_SERIAL1]"
    # And the value of the "Serial number" labelled nested numeric field of row 2 in the table field is "[ENV_SERIAL2]"
    # And the value of the "Serial number" labelled nested numeric field of row 3 in the table field is "[ENV_SERIAL3]"
    # And the value of the "Serial number" labelled nested numeric field of row 4 in the table field is "[ENV_SERIAL4]"
    # Then the value of the "Serial number" labelled nested numeric field of row 5 in the table field is "[ENV_SERIAL5]"

    Scenario: 06 - Verify the user can update the Production Tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Then the "Production tracking" titled page is displayed
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "From released item" labelled reference field on the main page
        And the user writes "Pressure transducer strain gauge" in the reference field
        And the user selects "Pressure transducer strain gauge" in the reference field
        Then the user clicks in the "searchButton" bound button field on the main page
        When the user selects the "workOrders" labelled table field on the main page
        #And the user selects the row with text "[WONumber]" in the "Number" labelled column header of the table field
        And the user selects the row with text "WOORDER_1" in the "Number" labelled column header of the table field
        And the user writes "2" in the "Actual qty" labelled nested numeric field of the selected row in the table field
        And the text in the body of the dialog is "Pregenerate 2 serial numbers on the work order first." on the main page
        And the user clicks the "OK" button of the Confirm dialog
        And the text in the body of the dialog is "The total actual quantity will be greater than the released quantity." on the main page
        Then the user clicks the "OK" button of the Confirm dialog
    ######To be updated
    #And the text in the body of the dialog contains "The total actual quantity will be greater than the released quantity" on the main page
    # And the text in the body of the dialog is "Pregenerate 2 serial numbers on the work order first." on the main page
    # And the user clicks the "OK" button of the Confirm dialog


    Scenario: 07 - Adding additional serial numbers on work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "501" in the "Site" labelled column header of the table field
        And the user clicks the "Site" labelled nested field of the selected row in the table field
        #Searching the WO number
        And the user searches for "[WONumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Pregenerate additional serial numbers" labelled business action button on the main page
        And the user selects the "enterTheQuantityOfAdditionalSerialNumbersToPregenerate" labelled numeric field on a modal
        And the user writes "2" in the numeric field
        And the user blurs the numeric field
        And the user clicks in the "okButton" bound button field on a modal
        Then a toast containing text "serial numbers generated" is displayed
        When the user clicks the "View serial numbers" labelled business action button on the main page
        #Verifying that new additional SN's are flagged as "InStock"
        And the user selects the "serialNumbers" bound table field on the sidebar
        ####################################################
        #When The  bug is fixed undo the comment on the follwoing code
        And the user selects the row 1 of the table field
        And the value of the "inStock " labelled nested checkbox field of the selected row in the table field is "true"
        And the user selects the row 2 of the table field
        And the value of the "inStock " labelled nested checkbox field of the selected row in the table field is "true"
        And the user selects the row 3 of the table field
        And the value of the "inStock " labelled nested checkbox field of the selected row in the table field is "true"
        And the user selects the row 4 of the table field
        And the value of the "inStock " labelled nested checkbox field of the selected row in the table field is "true"
        And the user selects the row 5 of the table field
        And the value of the "inStock " labelled nested checkbox field of the selected row in the table field is "true"
        And the user selects the row 6 of the table field
        And the value of the "inStock " labelled nested checkbox field of the selected row in the table field is "false"
        And the user selects the row 6 of the table field
        And the user stores the value of the "Serial number" labelled nested numeric field of the selected row in the table field with the key ""
        And the user selects the row 7 of the table field
        And the value of the "inStock " labelled nested checkbox field of the selected row in the table field is "false"
        And the user stores the value of the "Serial number" labelled nested numeric field of the selected row in the table field with the key ""
        Then the user clicks the "OK" labelled business action button on the sidebar

    Scenario: 08 - Final Production Receipt page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Then the "Production tracking" titled page is displayed
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "From released item" labelled reference field on the main page
        And the user writes "Pressure transducer strain gauge" in the reference field
        And the user selects "Pressure transducer strain gauge" in the reference field
        Then the user clicks in the "searchButton" bound button field on the main page
        When the user selects the "workOrders" labelled table field on the main page
        And the user selects the row with text "[WONumber]" in the "Number" labelled column header of the table field
        And the user writes "2" in the "Actual qty" labelled nested numeric field of the selected row in the table field
        #Verifying the dialog message
        #Dialog message 1
        And the text in the body of the dialog contains "The total actual quantity will be greater than the released quantity" on the main page
        And the user clicks the "OK" button of the Confirm dialog
        #Adding stock
        And the user selects the "workOrders" bound table field on the main page
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user selects the row with text "To be assigned" in the "Serial number" labelled column header of the table field
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field
        #Serial numbers
        And the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "[ENV_SERIAL6]" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "[ENV_SERIAL6]" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "2" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated: " is displayed
