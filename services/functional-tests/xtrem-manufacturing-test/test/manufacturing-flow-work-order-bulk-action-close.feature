# The goal of this test is to verify wo closing using bulk action on the main list for WOs different status(Pending, Completed, Inprogress)
# WO used: Close1, 2, 3, 4, 5, 6, 7, and 8
@manufacturing
Feature: manufacturing-flow-work-order-bulk-action-close

    Scenario: 01- work order closing using bulk action for PENDING Work Orders
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "Close"

        #Closing the WOs
        And the user selects the row with text "Close8" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        And the user selects the row with text "Close7" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        # Clicking the bulk action button to close Work orders(Currently there's no special toast for bulk action WOs)
        And the user clicks the "Close orders" labelled bulk action button of the table field
        And the user selects the "Closing date " labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Generate" labelled business action button on a modal

        And the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed

        #Refresh the page, to verify that no closing is done on the WO
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "Close"
        And the user selects the row with text "Close8" in the "Number" labelled column header of the table field
        Then the value of the "Status" labelled nested text field of the selected row in the table field is "Pending"
        And the user selects the row with text "Close7" in the "Number" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Pending"

        # Verify the error status(The pending WOs can not be closed, so will check the error message on batch history)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-communication/SysNotificationState"
        Then the "Batch task history" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Close work order" in the "Operation" labelled column header of the table field
        Then the value of the "Status" labelled nested text field of the selected row in the table field is "Error"

    Scenario: 02- work order closing using bulk action for INPROGRESS Work Orders
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "Close"

        #Closing the WOs
        And the user selects the row with text "Close1" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        And the user selects the row with text "Close2" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        And the user selects the row with text "Close3" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        # Clicking the bulk action button to close Work orders(Currently there's no special toast for bulk action WOs)
        And the user clicks the "Close orders" labelled bulk action button of the table field
        And the user selects the "Closing date " labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Generate" labelled business action button on a modal
        And the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed

        #Refresh the page, to verify the status for the closed WO
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "Close"
        And the user selects the row with text "Close3" in the "Number" labelled column header of the table field
        Then the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
        And the user selects the row with text "Close2" in the "Number" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
        And the user selects the row with text "Close1" in the "Number" labelled column header of the table field
        Then the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"

    Scenario: 03- work order closing using bulk action for COMPLETED Work Orders
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "Close"

        #Closing the WOs
        And the user selects the row with text "Close4" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        And the user selects the row with text "Close5" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        And the user selects the row with text "Close6" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        # Clicking the bulk action button to close Work orders(Currently there's no special toast for bulk action WOs)
        And the user clicks the "Close orders" labelled bulk action button of the table field
        And the user selects the "Closing date " labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Generate" labelled business action button on a modal
        And the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed

        #Refresh the page, to verify the status for the closed WO
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "Close"
        And the user selects the row with text "Close6" in the "Number" labelled column header of the table field
        Then the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
        And the user selects the row with text "Close5" in the "Number" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
        And the user selects the row with text "Close4" in the "Number" labelled column header of the table field
        Then the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
