# The goal of this test is to update a work order for a released item including phantom
# sub-assembly and to verify business rules
# site = Site de Chavanod CHA-S01
# items = Finished product = Finished good - with phantom B / phantom sub-assembly = PHANTOM_B
@manufacturing

Feature: manufacturing-flow-work-order-with-phantom-02

    Scenario: 1- Create work order WO_PHANTOM_02

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        ## Enter site = Site de Chavanod ##
        Given the user selects the "Site *" labelled reference field on the sidebar
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"
        ## Released item = Finished good - with phantom B ##
        # select with item ID #
        Given the user selects the "Released item *" labelled reference field on the sidebar
        And the user clicks the lookup button of the reference field
        And the user selects the "bomCode" bound table field on a modal
        And the user selects the row with text "FG_PHANTOM_2" in the "ID" labelled column header of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        Then the value of the reference field is "Finished good - with phantom B"
        ## Work order category = Normal ##
        Given the user selects the "Category *" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field
        Then the value of the reference field is "Normal"
        ## Type = Firm ##
        Given the user selects the "Type *" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        Then the value of the dropdown-list field is "Firm"
        ## Quantity = 15 ##
        And the user selects the "Quantity *" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        ## Requested start date = current date ##
        Given the user selects the "Requested start date *" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"
        ## Work order number = WO_PHANTOM_02 ##
        Given the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WO_PHANTOM_02" in the text field
        ## Create work order ##
        And the user clicks the "Create" labelled business action button on the sidebar

        And the "Work order WO_PHANTOM_02" titled page is displayed

    Scenario: 2- Update work order released quantity

        ## check and update released quantity from 15 to 5 ##
        Given the user selects the "Released quantity" labelled numeric field on the main page
        Then the value of the numeric field is "15.00"
        And the user writes "5" in the numeric field
        Then the user presses Enter

        And the user clicks the "Save" labelled business action button on the main page

        And the user waits 2 seconds

        Then a toast containing text "Record updated" is displayed

        And the user dismisses all the toasts

        ## check components' grid ##

        # check required quantity has been updated accordingly #
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        ## line 10 must mimic line 5 of the FG_PHANTOM_2 bill of material ##
        When the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B1"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "5.00 g"
        ## line 20 must mimic line 10 of the FG_PHANTOM_2 bill of material + include the scrap factor of 5% (10.00 g + 5% = 10.50 g) ##
        When the user selects the row with text "20" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B2"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "10.50 g"
        ## line 30 must mimic line 15 of the FG_PHANTOM_2 bill of material (fixed quantity) ##
        When the user selects the row with text "30" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B3"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "30.00 g"
        ## line 40 must mimic line 10 of the PHANTOM_B bill of material knowing that this BoM is for a base quantity of 1.00 kg and UOM link qtty = 5.00 kg for PHANTOM_B ##
        When the user selects the row with text "40" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B1"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "500.00 g"
        ## line 50 must mimic line 20 of the PHANTOM_B bill of material knowing that this BoM is for a base quantity of 1.00 kg and UOM link qtty = 5.00 kg for PHANTOM_B ##
        When the user selects the row with text "50" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B2"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "1,000.00 g"
        ## line 60 must mimic line 30 of the PHANTOM_B bill of material - attention: fixed quantiy type defined on this phantom component! ##
        When the user selects the row with text "60" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B3"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "300.00 g"

    Scenario: 3- Allocate work order

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # Search for WO = WO_PHANTOM_02
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "WO_PHANTOM_02" in the navigation panel
        And the user clicks the record with the text "WO_PHANTOM_02" in the navigation panel
        # process full allocation of the work order
        And the user clicks the "Allocate stock" labelled more actions button in the header
        Then an info dialog appears on the main page
        And the text in the body of the dialog is "The allocation request was submitted." on the main page
        And the user clicks the "OK" button of the dialog

        And the "Work order WO_PHANTOM_02" titled page is displayed

        # Verify work order allocation status is "Allocated"
        When the user selects the "Allocation status" labelled label field on the main page
        Then the value of the label field is "Allocated"

    Scenario: 4- Create production tracking with automatic material and time tracking

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Then the "Production tracking" titled page is displayed
        # Enter work order criteria
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        # filter with site and work order number created previously = WO_PHANTOM_02
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "WO_PHANTOM_02" in the reference field
        And the user selects "WO_PHANTOM_02" in the reference field
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "WO_PHANTOM_02" in the reference field
        And the user selects "WO_PHANTOM_02" in the reference field

        And the user clicks in the "searchButton" bound button field on the main page

        And the user selects the "Work orders" labelled table field on the main page
        And the user selects the row with text "WO_PHANTOM_02" in the "Number" labelled column header of the table field

        # Tick the left checkbox
        And the user ticks the main checkbox of the selected row in the table field
        # enter actual quantity = 2
        And the user writes "2" in the "Actual qty" labelled nested numeric field of the selected row in the table field

        # Tick the checkboxes for automatic material and time tracking
        And the user clicks the "Automatic material tracking" labelled nested field of the selected row in the table field
        And the user clicks the "Automatic time tracking" labelled nested field of the selected row in the table field

        # Add stock details
        Given the user clicks the "Stock details" dropdown action of the selected row of the table field
        When the user selects the "Stock details" labelled table field on a modal
        Then the table field is empty
        When the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row with text "2.00 L" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "Internal 1" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Internal 1" in the "Location" labelled nested field of the selected row in the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        # validate stock details entry
        And the user clicks the "OK" labelled business action button on a modal
        # validate production tracking entry
        And the user clicks the "Generate" labelled business action button on the main page
        # check a single production tracking was generated
        Then a toast with text "Tracking records generated: 1" is displayed

        Then the user dismisses all the toasts

    Scenario: 5- Control work order was updated as expected

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # Search for WO = WO_PHANTOM_02
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "WO_PHANTOM_02" in the navigation panel
        And the user clicks the record with the text "WO_PHANTOM_02" in the navigation panel

        # Verify work order status is "In progress"
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "In progress"

        # control completed quantity = 2.00 #
        Given the user selects the "Completed quantity" labelled numeric field on the main page
        Then the value of the numeric field is "2.00"

        # verify status / allocation status on each component line

        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        ## line 10 must be in progress ##
        When the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B1"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "In progress"
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"
        ## line 20 must be in progress ##
        When the user selects the row with text "20" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B2"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "In progress"
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"
        ## line 30 must be completed (fixed quantity is fully tracked at once) & allocation status removed as the line is completed ##
        When the user selects the row with text "30" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B3"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is ""
        ## line 40 must be in progress ##
        When the user selects the row with text "40" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B1"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "In progress"
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"
        ## line 50 must be in progress ##
        When the user selects the row with text "50" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B2"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "In progress"
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"
        ## line 60 must be completed (fixed quantity is fully tracked at once) & allocation status removed as the line is completed ##
        When the user selects the row with text "60" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Phantom sub-level - B3"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is ""
