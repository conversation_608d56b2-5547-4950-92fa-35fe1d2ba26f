# This script tests the creation, update and deletion of a work order
# Read is explicitly tested
@manufacturing
@inventory
Feature: manufacturing-crud-work-order

    Scenario: Work order creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        ##Selecting the site using a reference field##
        And the user selects the "site" labelled reference field on the sidebar
        And the user writes "Chem. Boston" in the reference field
        And the user selects "Chem. Boston" in the reference field
        ##Selecting the Released item using a reference field##
        And the user selects the "releasedItem" labelled reference field on the sidebar
        And the user writes "Hydro alcoholic solution Formula 4" in the reference field
        And the user selects "Hydro alcoholic solution Formula 4" in the reference field
        ##Selecting the work order category using a reference field##
        And the user selects the "category" labelled reference field on the sidebar
        And the user writes "Assembly only" in the reference field
        And the user selects "Assembly only" in the reference field
        ##Selecting the Type using a dropdown-list field##
        And the user selects the "type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        ##Populating the Quantity using a numeric field##
        And the user selects the "quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        ##Populating the Work order number using a text field##
        And the user selects the "workOrderNumber" labelled text field on the sidebar
        And the user writes "QC Test Work order" in the text field
        And the user clicks the "Create" labelled business action button on the sidebar
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Pending"

    Scenario: Update Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # Search for WO
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC Test Work order" in the navigation panel
        And the user clicks the record with the text "QC Test Work order" in the navigation panel
        # Make changes to WO
        And the user selects the "type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Planned" in the dropdown-list field
        And the user selects the "type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Planned"
        # Save the changes
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Read Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # Search for WO
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC Test Work order" in the navigation panel
        And the user clicks the record with the text "QC Test Work order" in the navigation panel
        # Read the WO
        And the user selects the "site" labelled reference field on the main page
        Then the value of the reference field is "Chem. Boston"
        When the user selects the "type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Planned"
        When the user selects the "item" labelled reference field on the main page
        Then the value of the reference field is "Hydro-alcoholic gel for hand antisepsis"
        When the user selects the "releasedQuantity" labelled numeric field on the main page
        Then the value of the numeric field is "10.00"

    Scenario: Delete newly created Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # Search for WO
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC Test Work order" in the navigation panel
        And the user clicks the record with the text "QC Test Work order" in the navigation panel
        # Verify WO
        And the user selects the "item" labelled reference field on the main page
        Then the value of the reference field is "Hydro-alcoholic gel for hand antisepsis"
        # Delete WO
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
