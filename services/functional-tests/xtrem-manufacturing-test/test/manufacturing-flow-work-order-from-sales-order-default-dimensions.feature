# This script that checks the default dimensions of a sales and work order created using order to order
@manufacturing
@inventory

Feature: manufacturing-flow-work-order-from-sales-order-default-dimensions

    Scenario: 1 - Check dimensions for a recently created sales order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Sale order creation
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "85" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed

        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SOWO]"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field

        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"

    Scenario: 2 - Create Work Order from the recently created Sale Order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_SOWO]" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Create work order" dropdown action of the selected row of the table field

        Then the user selects the "Type *" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        Then the value of the dropdown-list field is "Firm"

        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WO_Dimensions" in the text field
        And the user stores the value of the text field with the key "[WO_Dimensions]"

        And the user clicks the "Create" labelled business action button on the sidebar
        And the user waits for 2 seconds
        And takes a screenshot

    Scenario: 3 - Check dimensions for the recently created Work Order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[WO_Dimensions]" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        #Check dimensions from Dimensions button
        When the user clicks the "Dimensions" labelled business action button on the main page
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"
        Then the user clicks the "Cancel" labelled business action button on a modal

        #Check dimensions for each component from components table
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        Then the user selects the row 1 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"
        Then the user clicks the "Cancel" labelled business action button on a modal

        When the user selects the "Components" labelled table field on the main page
        Then the user selects the row 2 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"
        Then the user clicks the "Cancel" labelled business action button on a modal

        When the user selects the "Components" labelled table field on the main page
        Then the user selects the row 3 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"
        Then the user clicks the "Cancel" labelled business action button on a modal

        #Check dimensions from operation nested grid
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "5" in column with header "Operation number" in the nested grid field
        And the user selects row with text "OP1" in column with header "Name" in the nested grid field

        And the user expands the selected row of the nested grid field
        And the user selects row with text "Labeler 15x12 1500 hour" in column with header "Name" in the nested grid field
        And the user clicks the "Dimensions" action of the selected row in the nested grid field
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"
        Then the user clicks the "Cancel" labelled business action button on a modal
