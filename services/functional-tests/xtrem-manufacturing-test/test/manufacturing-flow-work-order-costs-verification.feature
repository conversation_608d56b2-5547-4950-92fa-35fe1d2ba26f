# The goal of this test is to verify the work order costs.

@manufacturing
Feature: manufacturing-flow-work-order-costs-verification

    Scenario: 01 - Create and verify a Work order
        #Create
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "Released item" labelled reference field on the sidebar
        And the user writes "Wheel" in the reference field
        And the user selects "Wheel" in the reference field
        And the user selects the "Category" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field
        And the user selects the "Type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "WOXT52752_" in the text field
        And the user presses Tab
        And the user stores the value of the text field with the key "[ENV_WOCV52]"
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Wheel" in the text field
        And the user clicks the "Create" labelled business action button on the sidebar
        And the user waits 5 seconds
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Pending"
        #Verify component planned costs
        Given selects the "Components" labelled navigation anchor on the main page
        And the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the value of the "Component description" labelled nested text field of the selected row in the table field is "Rim"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "20 each"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the table field is "$ 220.0000"
        And the user selects the row with text "20" in the "Component number" labelled column header of the table field
        And the value of the "Component description" labelled nested text field of the selected row in the table field is "Tire"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "20 each"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the table field is "$ 340.0000"
        #Verify total planned costs
        When selects the "General" labelled navigation anchor on the main page
        And the user selects the "Planned versus actual" labelled summary table field on the main page
        And the user selects the row with text "Total" in the "Cost" labelled column header of the summary table field
        Then the value of the "Planned" labelled nested numeric field of the selected row in the summary table field is "$ 581.9400"


    Scenario: 02 - Create material tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/MaterialTracking"
        Then the "Material tracking" titled page is displayed
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "[ENV_WOCV52]" in the reference field
        And the user selects "[ENV_WOCV52]" in the reference field
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "[ENV_WOCV52]" in the reference field
        And the user selects "[ENV_WOCV52]" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Work order components" labelled table field on the main page
        And the user selects the row with text "Rim" in the "Component description" labelled column header of the table field
        And the user writes "3" in the "Actual quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "100 each" in the "Available quantity" labelled column header of the table field
        And the user writes "3" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Allocate" labelled business action button on a modal
        And the user selects the "Work order components" labelled table field on the main page
        And the user selects the row with text "Tire" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "100 each" in the "Available quantity" labelled column header of the table field
        And the user writes "4" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Allocate" labelled business action button on a modal
        And the user selects the "Work order components" labelled table field on the main page
        And the user selects the row with text "Tire" in the "Component description" labelled column header of the table field
        And the user writes "4" in the "Actual quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated: 2" is displayed


    Scenario: 03 - Create production tracking (Non-completed)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        And the "Production tracking" titled page is displayed
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "[ENV_WOCV52]" in the reference field
        And the user selects "[ENV_WOCV52]" in the reference field
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "[ENV_WOCV52]" in the reference field
        And the user selects "[ENV_WOCV52]" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Work orders" labelled table field on the main page
        And the user selects the row with text "[ENV_WOCV52]" in the "Number" labelled column header of the table field
        And the user writes "3" in the "Actual qty." labelled nested numeric field of the selected row in the table field
        Then the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Required"
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "Stock details" labelled table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row with text "3 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user selects the "Work orders" labelled table field on the main page
        And the user selects the row with text "[ENV_WOCV52]" in the "Number" labelled column header of the table field
        Then the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Entered"
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated: 1" is displayed


    Scenario: 04 - Create production tracking (Completed and Automatic material tracking)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        And the "Production tracking" titled page is displayed
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "[ENV_WOCV52]" in the reference field
        And the user selects "[ENV_WOCV52]" in the reference field
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "[ENV_WOCV52]" in the reference field
        And the user selects "[ENV_WOCV52]" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Work orders" labelled table field on the main page
        And the user selects the row with text "[ENV_WOCV52]" in the "Number" labelled column header of the table field
        And the user writes "2" in the "Actual qty." labelled nested numeric field of the selected row in the table field
        And the user clicks the "Completed" labelled nested field of the selected row in the table field
        And the user clicks the "Automatic material tracking" labelled nested field of the selected row in the table field
        Then the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Required"
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "Stock details" labelled table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row with text "2 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user selects the "Work orders" labelled table field on the main page
        And the user selects the row with text "[ENV_WOCV52]" in the "Number" labelled column header of the table field
        Then the value of the "Stock detail status" labelled nested text field of the selected row in the table field is "Entered"
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated: 1" is displayed


    Scenario: 05 - Verify actual costs of row 1 on the created Work order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_WOCV52]" in the navigation panel
        And the user clicks the record with the text "[ENV_WOCV52]" in the navigation panel
        #Verify component actual costs
        Given selects the "Components" labelled navigation anchor on the main page
        And the user selects the "Components" labelled table field on the main page
        ##Row 1
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Component description" labelled nested text field of the selected row in the table field is "Rim"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "5 each"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "$ 55.0000"
        #Verify total actual costs
        When selects the "General" labelled navigation anchor on the main page
        And the user selects the "Planned versus actual" labelled summary table field on the main page
        And the user selects the row with text "Total" in the "Cost" labelled column header of the summary table field
        Then the value of the "Actual" labelled nested numeric field of the selected row in the summary table field is "$ 157.0000"


    Scenario: 06 - Verify actual costs of row 2 on the created Work order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_WOCV52]" in the navigation panel
        And the user clicks the record with the text "[ENV_WOCV52]" in the navigation panel
        #Verify component actual costs
        When selects the "Components" labelled navigation anchor on the main page
        Then the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "20" in the "Component number" labelled column header of the table field
        And the user selects the row 2 of the table field
        And the value of the "Component description" labelled nested text field of the selected row in the table field is "Tire"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "6 each"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "$ 102.0000"
        #Verify total actual costs
        When selects the "General" labelled navigation anchor on the main page
        And the user selects the "Planned versus actual" labelled summary table field on the main page
        And the user selects the row with text "Total" in the "Cost" labelled column header of the summary table field
        Then the value of the "Actual" labelled nested numeric field of the selected row in the summary table field is "$ 157.0000"
        #Complete work order
        And the user clicks the "Close order" labelled business action button on the main page
        And the user clicks the "Continue" button of the Confirm dialog
        And the user waits 5 seconds
        And the user clicks the "Generate" button of the Confirm dialog
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

    Scenario: 07 - Verify quantity and cost in Item-site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ItemSite"
        Then the "Item-sites" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        Then the "Open column panel" labelled button of the table field is enabled
        And the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When searches for "Stock valuation at standard cost" in the lookup dialog on the sidebar
        Then the table column configuration with name "Stock valuation at standard cost" on the sidebar is unticked
        And the user ticks the table column configuration with "Stock valuation at standard cost" name on the sidebar
        Then the table column configuration with name "Stock valuation at standard cost" on the sidebar is ticked
        And the user clicks the Close button of the sidebar

        # Enter item criteria = Wheel
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Wheel" in the filter of the table field
        And the user ticks the item with text "Wheel" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user selects the row with text "Wheel" in the "Item" labelled column header of the table field
        And the value of the "Total stock" labelled nested numeric field of the selected row in the table field is "105"
        And the value of the "Stock valuation at standard cost" labelled nested numeric field of the selected row in the table field is "3,657"

        Then the "Open column panel" labelled button of the table field is enabled
        And the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When searches for "Valuation method" in the lookup dialog on the sidebar
        Then the table column configuration with name "Valuation method" on the sidebar is unticked
        And the user ticks the table column configuration with "Valuation method" name on the sidebar
        Then the table column configuration with name "Valuation method" on the sidebar is ticked
        And the user clicks the Close button of the sidebar

        And the value of the "Valuation method" labelled nested numeric field of the selected row in the table field is "Average unit cost"


    Scenario: 08 - Verify transaction in Stock journal inquiry
        #Store value of document number
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTrackingLineInquiry"
        Then the "Production tracking line inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Filter" button in the header of the table field
        And the user filters the "Work order link" labelled column in the table field with value "[ENV_WOCV52]"
        And the user selects the row with text "3 each" in the "Quantity" labelled column header of the table field
        And the user stores the value of the "Production tracking link" labelled nested text field of the selected row in the table field with the key "[ENV_PRDTRK01]"
        And the user selects the row with text "2 each" in the "Quantity" labelled column header of the table field
        Then the user stores the value of the "Production tracking link" labelled nested text field of the selected row in the table field with the key "[ENV_PRDTRK02]"
        #Verify Stock journal inquiry transactions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the navigation panel

        # Enter site criteria = TE Hampton
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "TE Hampton" in the filter of the table field
        And the user ticks the item with text "TE Hampton" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Enter item criteria = Wheel
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Wheel" in the filter of the table field
        And the user ticks the item with text "Wheel" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user selects the row with text "3 each" in the "Quantity" labelled column header of the table field
        And the value of the "Document" labelled nested reference field of the selected row in the table field is "[ENV_PRDTRK01]"
        And the user selects the row with text "2 each" in the "Quantity" labelled column header of the table field
        And the value of the "Document" labelled nested reference field of the selected row in the table field is "[ENV_PRDTRK02]"
        And the user clicks the "Filter" button in the header of the table field
        And the user filters the "Quantity" labelled column in the table field with value "0"
        And the user selects the row with text "[ENV_PRDTRK01]" in the "Document" labelled column header of the table field
        And the value of the "Item" labelled nested reference field of the selected row in the table field is "Wheel"
        And the user selects the row with text "[ENV_PRDTRK02]" in the "Document" labelled column header of the table field
        Then the value of the "Item" labelled nested reference field of the selected row in the table field is "Wheel"
