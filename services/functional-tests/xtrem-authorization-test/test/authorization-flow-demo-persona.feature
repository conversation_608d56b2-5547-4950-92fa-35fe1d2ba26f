@authorization
Feature: authorization-flow-demo-persona
    # This feature tests the authorization system by logging and select a demo persona , in order to verify the user access and display of the specific menu items and functions based on the personna previously selected

    <PERSON><PERSON><PERSON>: Select the demo persona
        Given the user opens the application on a HD desktop
        And the user opens the persona
        And the user selects the "Demo persona" labelled select field on a modal
        When the user clicks in the select field
        And the user selects "<PERSON><PERSON>, <PERSON><PERSON> (<EMAIL>)" in the select field
        And the user clicks the "Apply" button of the dialog

        #Sometimes the screen is not refreshed
        # may need to force the screen refresh
        And the user waits 3 seconds


    Sc<PERSON><PERSON>: Check the authorization
        Given the user maximizes the sitemap
        Then the "Items" menu item on the sitemap is displayed

        #Simulate a menu item not displayed → when the demo persona will be limited to certain function you can adapt the test accordingly
        Then the "NOT_DISPLAYED" menu item on the sitemap is hidden

        And the user clicks the "Items" menu item
        And the "Item" sub menu item on the sitemap is displayed
        And the "Item data" sub menu item on the sitemap is displayed
        And the user expands the "Item data" sub menu item
        And the "Item-site" sub menu item on the sitemap is displayed
        And the user clicks the "Item-site" sub menu item
        And the titled page containing "Item-sites" is displayed
        And takes a screenshot
