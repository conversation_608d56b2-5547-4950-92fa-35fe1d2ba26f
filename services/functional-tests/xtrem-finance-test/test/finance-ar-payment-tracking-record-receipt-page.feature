# The purpose of the test is to select for AR open item on the Record receipt page according to the search criteria entered resp.
# ensuring data integrity during the selection of the search criteria and the application of a payment amount.
# Prerequisites: prerequisites-flow-verify-options-status

@finance
Feature: finance-ar-payment-tracking-record-receipt-page

    Scenario: 01 - Record receipt page check
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/RecordReceipt"
        When the "Record receipt" titled page is displayed
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Customer *" labelled reference field on the main page
        And the user writes "Schmitt Apparatebau GmbH" in the reference field
        And the user selects "Schmitt Apparatebau GmbH" in the reference field
        And the user selects the "Bank account *" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And the user selects the "Date received *" labelled date field on the main page
        And the user writes "12/17/2024" in the date field
        When the user selects the "Payment method *" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "ACH" in the dropdown-list field
        And the user selects the "Payment amount *" labelled numeric field on the main page
        And the user writes "476" in the numeric field
        And the user presses Enter
        And the user clicks in the "searchButton" bound button field on the main page
        When the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        Then the value of the tile numeric field is "€476.00"
        When the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        Then the value of the tile numeric field is "€0.00"

        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SIDE240002" in the "Invoice Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€333.20"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€142.80"

        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SIDE240003" in the "Invoice Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€142.80"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€333.20"

        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SIDE240004" in the "Invoice Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€0.00"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€476.00"
