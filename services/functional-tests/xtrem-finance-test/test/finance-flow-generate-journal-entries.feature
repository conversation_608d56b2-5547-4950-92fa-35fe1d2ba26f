# The purpose of this test is to verify the fields are correctly set and we get a message

@finance
Feature: finance-flow-generate-journal-entries
    Scenario: Verify Generate journal entries values

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed

        #set values
        When the user selects the "Financial site" labelled reference field on the main page

        # And the user writes "DEP1-S02" in the reference field
        And the user writes "Entrepot de TOULOUSE  -  Sud Ouest" in the reference field
        # when XT-52862 is fixed replace with the step definition in comment
        Then the user selects "Entrepot de TOULOUSE - Sud Ouest" in the reference field
        #Then the user selects "Entrepot de TOULOUSE  -  Sud Ouest" in the reference field
        And the value of the reference field is "Entrepot de TOULOUSE  -  Sud Ouest"

        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+1"
        Then the value of the date field is a generated date with value "T+1"

        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "AP invoice" in the multi dropdown field
        Then the value of the multi dropdown field is "AP invoice"

        # Click Generate and check message
        When the user clicks in the "create" bound button field on the main page
        # Generate

        Then a toast containing text "No documents to be processed." is displayed
