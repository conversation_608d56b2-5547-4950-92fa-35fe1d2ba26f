
# The goal of this test is to Create, Update, Delete an account
@finance
Feature:  finance-crud-account
    # Create, Update, Delete an account
    Scenario: Account creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Account"
        Then the "Accounts" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        Given the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "ON"

        And the user selects the "ID" labelled text field on the main page
        And the user writes "110000" in the text field

        And the user selects the "Name" labelled text field on the main page
        And the user writes "FA In Process" in the text field

        And the user selects the "Chart of accounts" labelled reference field on the main page
        And the user writes "GB chart of accounts" in the reference field
        And the user selects "GB chart of accounts" in the reference field


        And the user selects the "Tax management *" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Other" in the dropdown-list field
        Then the value of the dropdown-list field is "Other"

        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

        #Update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Account"
        Then the "Accounts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "ID" labelled nested field of the selected row in the table field
        And the user searches for "110000" in the navigation panel
        And the user clicks the "first" navigation panel's row

        Given the user selects the "Direct entry forbidden" labelled switch field on the main page
        Then the user turns the switch field "ON"

        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        #Delete

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Account"
        Then the "Accounts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "ID" labelled nested field of the selected row in the table field
        And the user searches for "110000" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
