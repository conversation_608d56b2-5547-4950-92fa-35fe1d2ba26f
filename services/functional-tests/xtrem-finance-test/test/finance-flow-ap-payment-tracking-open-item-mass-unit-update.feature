# SDMO users may have gone live before AP payment tracking was introduced.
# In such cases, they need to update open items as partially or fully paid before using the AP payment tracking feature.
# To support this, we provide the 'Initialize Accounts Payable Payments' page for users who have been using SDMO and have fully or partially paid open items.
# A bulk action allows users to select multiple open items and mark them as 'fully paid.'
# After clicking the 'Close Open Item' button, an asynchronous process updates open items and purchases invoices, similar to recording a payment made ('Open item mass update').
# Once the process is complete, users receive a notification with links to the AP Open Items page and Batch Task History.
# In addition, the user can select a purchase invoice (resp. Open item) and partially or fully pay it on a dedicated page ('Open item unit update').
# The purpose of this automated test is to validate the different steps of this process.
# IMPORTANT: Intacct configuration status has to be not active (isIntacctServiceOptionActive in @sage/xtrem-structure/OptionManagementBase)
# IMPORTANT: Payment Tracking option status has to be active (paymentTrackingOption in @sage/xtrem-system/ServiceOptionState)
# Prerequisites: prerequisites-flow-verify-options-status

@finance
# @todo refactor file in order to have only 2 decimals after the issue with having 3 or 4 decimals is fixed - XT-94045
Feature: finance-flow-ap-payment-tracking-open-item-mass-unit-update
    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario Outline: 0.01 - Data creation - Purchase invoices
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes <Supplier> in the reference field
        And the user selects <Supplier> in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes <Quantity> in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Variance status updated" is displayed
        And the user dismisses all the toasts
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "No penalty no discount" in the reference field
        And the user selects "No penalty no discount" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes <AmountNoTax> in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes <Tax> in the numeric field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The purchase invoice was posted." is displayed
        Examples:
            | Quantity | EnvName             | AmountNoTax | Tax   | Supplier                  |
            | "300"    | "[ENV_PIAPT_NUM01]" | "3000"      | "570" | "MUHOMA Technology GmbH"  |
            | "200"    | "[ENV_PIAPT_NUM02]" | "2000"      | "380" | "MUHOMA Technology GmbH"  |
            | "100"    | "[ENV_PIAPT_NUM03]" | "1000"      | "190" | "MUHOMA Technology GmbH"  |
            | "400"    | "[ENV_PIAPT_NUM04]" | "4000"      | "0"   | "Stock Tranfer US Site 1" |

    Scenario Outline: 0.02 - Data creation - Purchase credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value <DocName>
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is <DocName>
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the <PageTitle> titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the dialog title is "Create purchase credit memo"
        And the user selects the "Reason *" labelled reference field on a modal
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field
        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes <AmountNoTax> in the numeric field
        And the user clicks the "Create" button of the dialog on the main page
        And a toast containing text "Purchase credit memo created." is displayed
        And the user selects the "Total credit memo tax" labelled numeric field on the main page
        And the user writes <Tax> in the numeric field
        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        And a toast containing text "The purchase credit memo was posted." is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        Examples:
            | EnvName              | DocName             | PageTitle                            | AmountNoTax | Tax   |
            | "[ENV_PCMAPT_NUM01]" | "[ENV_PIAPT_NUM03]" | "Purchase invoice [ENV_PIAPT_NUM03]" | "1000"      | "190" |


    Scenario: 01 - Verify Open item unit update (open item is in the same currency as the company currency) - purchase invoice partial payment
        # Enter partial payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM02]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Not paid"
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "1380" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "1,380.00"
        # Check 'Payment' tab on the Purchase invoice page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PIAPT_NUM02]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Partially paid"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is current
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "1,380.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "1,380.000"
        # Reset partial payment to 0
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM02]"
        And the user selects the row 1 of the table field
        And the value of the "Transaction amount due" labelled nested numeric field of the selected row in the table field is "€ 2,380.00"
        And the value of the "Transaction amount paid" labelled nested numeric field of the selected row in the table field is "€ 1,380.00"
        And the value of the "Remaining transaction amount" labelled nested numeric field of the selected row in the table field is "€ 1,000.00"
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.0000"
        # Check 'Payment' tab on the Purchase invoice page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PIAPT_NUM02]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the "Forced amount paid" labelled numeric field on the main page is hidden

    Scenario: 02 - Verify Open item unit update (open item is in a different currency from the company currency) - purchase invoice partial payment
        # Enter partial payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM04]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Not paid"
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "7500" in the numeric field
        And the user presses Tab
        And a validation error message is displayed containing text
            """
            The forced amount paid needs to be between 0 and 4000
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Forced amount paid: The forced amount paid needs to be between 0 and 4000." is displayed
        And the user dismisses all the toasts
        And the text in the header of the dialog is "Unsaved changes"
        And the text in the body of the dialog is "Leave and discard your changes?"
        And the user clicks the "Go back" button of the Message dialog
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "2500" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "2,500.00"
        # Check 'Payment' tab on the Purchase invoice page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PIAPT_NUM04]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Partially paid"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is current
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "2,500.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "2,500.000"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "displayStatus" bound nested label field of the card 1 in the table field is "Partially paid"
        # Reset partial payment to 0
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM04]"
        And the user selects the row 1 of the table field
        And the value of the "Transaction amount due" labelled nested numeric field of the selected row in the table field is "$ 4,000.00"
        And the value of the "Transaction amount paid" labelled nested numeric field of the selected row in the table field is "$ 2,500.00"
        And the value of the "Remaining transaction amount" labelled nested numeric field of the selected row in the table field is "$ 1,500.00"
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "status" bound nested label field of the card 1 in the table field is "Partially paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.0000"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "status" bound nested label field of the card 1 in the table field is "Not paid"
        # Check 'Payment' tab on the Purchase invoice page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PIAPT_NUM04]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the "Forced amount paid" labelled numeric field on the main page is hidden
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "displayStatus" bound nested label field of the card 1 in the table field is "Posted"

    Scenario: 03 - Verify Open item unit update  (open item is in the same currency as the company currency) - purchase credit memo partial payment
        # Enter partial payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PCMAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "500" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
            The forced amount paid needs to be negative.
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Forced amount paid: The forced amount paid needs to be negative." is displayed
        And the user dismisses all the toasts
        And the text in the header of the dialog is "Unsaved changes"
        And the text in the body of the dialog is "Leave and discard your changes?"
        And the user clicks the "Go back" button of the Message dialog
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "-500" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "-500.00"
        # Check 'Payment' tab on the Purchase credit memo page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PCMAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Partially paid"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is current
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "500.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "500.000"
        # Reset partial payment to 0
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PCMAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "Transaction amount due" labelled nested numeric field of the selected row in the table field is "€ -1,190.00"
        And the value of the "Transaction amount paid" labelled nested numeric field of the selected row in the table field is "€ -500.00"
        And the value of the "Remaining transaction amount" labelled nested numeric field of the selected row in the table field is "€ -690.00"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Partially paid"
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        # Check 'Payment' tab on the Purchase credit memo page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PCMAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        Then the "Forced amount paid" labelled numeric field on the main page is hidden

    Scenario: 04 - Verify Open item unit update (open item is in the same currency as the company currency) - purchase invoice full payment
        # Enter full payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "3570" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors The close reason is mandatory." is displayed
        And the user dismisses all the toasts
        And the text in the header of the dialog is "Unsaved changes"
        And the text in the body of the dialog is "Leave and discard your changes?"
        And the user clicks the "Go back" button of the Message dialog
        And the user selects the "Close reason" labelled reference field on the main page
        And the user writes "Forced close" in the reference field
        And the user selects "Forced close" in the reference field
        And the user selects the "Close text" labelled text field on the main page
        And the user writes "Automated test AP" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "3,570.0000"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "3,570.00"
        # Check 'Payment' tab on the Purchase invoice page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PIAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Paid"
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is complete
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "3,570.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "3,570.000"
        # Reset to partial payment of 2000,00 Euro
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "Transaction amount due" labelled nested numeric field of the selected row in the table field is "€ 3,570.00"
        And the value of the "Transaction amount paid" labelled nested numeric field of the selected row in the table field is "€ 3,570.00"
        And the value of the "Remaining transaction amount" labelled nested numeric field of the selected row in the table field is "€ 0.00"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "2000" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "2,000.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "2,000.0000"
        And the "Close reason" labelled reference field on the main page is hidden
        And the "Close text" labelled text field on the main page is hidden
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "status" bound nested label field of the card 1 in the table field is "Partially paid"
        # Check 'Payment' tab on the Purchase invoice page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Number" labelled column in the table field with value "[ENV_PIAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "Total including tax" labelled nested numeric field of the selected row in the table field is "€ 3,570.00"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Partially paid"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Partially paid"
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is current
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "2,000.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "2,000.000"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "displayStatus" bound nested label field of the card 1 in the table field is "Partially paid"

    Scenario: 05 - Verify Open item mass update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_PIAPT_NUM01]" in the "Document number" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Partially paid"
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPT_NUM02]" in the "Document number" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Not paid"
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPT_NUM03]" in the "Document number" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Not paid"
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPT_NUM04]" in the "Document number" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Not paid"
        When the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PCMAPT_NUM01]" in the "Document number" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Not paid"
        When the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Close open items" labelled bulk action button of the table field
        And a warn dialog appears on the main page
        And the text in the header of the dialog is "Close open items"
        And the text in the body of the dialog is "Perform this action on the selected items: 5"
        And the user clicks the "OK" button of the Confirm dialog
        And a toast containing text "Action started on the selected items." is displayed
        And the user clicks the "Refresh" labelled button of the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user selects the row 2 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user selects the row 3 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user selects the row 4 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user selects the row 5 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the "Initialize accounts payable payment [ENV_PIAPT_NUM01]" titled page is displayed
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Paid"
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "3,570.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "3,570.0000"
        And the user selects the "Close reason" labelled reference field on the main page
        And the value of the reference field is "Forced close"
        And the user selects the "Close text" labelled text field on the main page
    # @todo Find a way to validate that the property 'Close text' is populated with 'Forced close on <system date> by <email of active user>'

    Scenario: 06 - Reset payment to 0
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItemPay"
        Then the "Initialize accounts payable payments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Paid"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Total amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the user selects the "Forced amount paid" labelled numeric field on the main page
        And the value of the numeric field is "0.0000"
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Not paid"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Not paid"
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM02]"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM03]"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PIAPT_NUM04]"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PCMAPT_NUM01]"
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Paid"
