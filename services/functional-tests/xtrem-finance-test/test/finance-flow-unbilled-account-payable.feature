# This script is about to verify the filtered data displayed the correct results about the unbilled purchase receipt

@finance
@distribution
Feature: finance-flow-unbilled-account-payable

    Scenario: Add lock - LCK_CREATEAPC

        Given the user opens the application on a HD desktop
        And the user adds the lock entry "LCK_CREATEAPC"
    Scenario: Open the Unbilled accounts payable Page and create a record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/UnbilledAccountPayableInquiry"

        Then the "Unbilled accounts payable" titled page is displayed
        # Filtering Company field
        When the user selects the "Company" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Che" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        Then the value of the reference field is "Chem. Atlanta"
        When the user selects the "Site" labelled multi reference field on the main page
        And the user clears the multi reference field
        And the user writes "Che" in the multi reference field
        And the user selects "Chem. Irvine" in the multi reference field
        # Filtering From supplier field
        When the user selects the "From supplier" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "ind" in the reference field
        And the user selects "Independant Commodity Intelligence Services" in the reference field
        Then the value of the reference field is "Independant Commodity Intelligence Services"
        # Filtering To supplier field
        When the user selects the "To supplier" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "ind" in the reference field
        And the user selects "Independant Commodity Intelligence Services" in the reference field
        Then the value of the reference field is "Independant Commodity Intelligence Services"
        # Entering AS of Date
        When the user selects the "As of date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        # Validation to display the result
        And the user clicks the "Run" labelled business action button on the main page
        Then a toast containing text "Unbilled accounts payable request sent." is displayed
        And the user waits 5 seconds
        And a toast containing text "Unbilled accounts payable finished." is displayed
        # Verify value in the table field
        When the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "SCPECB100" in the "Item ID" labelled column header of the table field
        And the value of the "Bill-by supplier ID" labelled nested reference field of the selected row in the table field is "ICIS01"
        And the value of the "Bill-by supplier" labelled nested reference field of the selected row in the table field is "Independant Commodity Intelligence Services"
        And the value of the "Company ID" labelled nested reference field of the selected row in the table field is "US001"
        And the value of the "Financial site ID" labelled nested reference field of the selected row in the table field is "US002"
        And the value of the "Net price" labelled nested numeric field of the selected row in the table field is "€ 0.20"
        And the value of the "Unbilled amount" labelled nested numeric field of the selected row in the table field is "€ 3,100.00"
        And the value of the "Stock site" labelled nested reference field of the selected row in the table field is "Chem. Irvine"
        And the value of the "Receipt number" labelled nested link field of the selected row in the table field is "PRFRCOS2020100003"
        And the value of the "Received quantity" labelled nested numeric field of the selected row in the table field is "15,500 each"
        And the value of the "Returned quantity" labelled nested numeric field of the selected row in the table field is "0 each"
        And the value of the "Invoiced quantity" labelled nested numeric field of the selected row in the table field is "0 each"
        And the value of the "Credited quantity" labelled nested numeric field of the selected row in the table field is "0 each"
    Scenario: Delete previously created record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/UnbilledAccountPayableInquiry"

        Then the "Unbilled accounts payable" titled page is displayed

        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
    Scenario: Remove lock - LCK_CREATEAPC

        And the user removes the lock entry "LCK_CREATEAPC"
