# The purpose of this test is to create, read, update and delete a record on the statistical procedure page.
@finance
Feature: finance-crud-statistical-procedure-declaration

    Scenario: Open the Statistical procedures Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/StatisticalProcedure"
        Then the "Statistical procedures" titled page is displayed
        # Create the new record
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Statistical procedure" titled page is displayed
        When the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "ON"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Statistical procedure test" in the text field
        When the user selects the "ID" labelled text field on the main page
        And the user writes "SPT01" in the text field
        When the user selects the "Flow" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        And the user selects "Arrival" in the dropdown-list field
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed



    Scenario: Open the Statistical procedures Page and update the record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/StatisticalProcedure"
        Then the "Statistical procedures" titled page is displayed
        # Search and select the record
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "ID" labelled nested field of the selected row in the table field
        When the user searches for "SPT01" in the navigation panel
        And the user clicks the record with the text "SPT01" in the navigation panel
        # Read and make changes to the record
        When the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Statistical procedure test"
        And the user writes "Statistical procedure test edit" in the text field
        When the user selects the "Flow" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Arrival"
        When the user clicks in the dropdown-list field
        And the user selects "Dispatch" in the dropdown-list field
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Open the Statistical procedures Page and delete the record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/StatisticalProcedure"
        Then the "Statistical procedures" titled page is displayed
        # Search and select the record
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "ID" labelled nested field of the selected row in the table field
        When the user searches for "SPT01" in the navigation panel
        And the user clicks the record with the text "SPT01" in the navigation panel
        # Delete the record
        And the user clicks the "Delete" labelled more actions button in the header
        Then the text in the header of the dialog is "Delete record"
        When the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
