#This test can only be executed with sage.
#The goal of this test is to test the posting class defination for FR

@finance
Feature: finance-flow-fr-posting-class-definition-item

    Scenario: 01 - reading the posting class definition for FR Expense
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClassDefinition"
        Then the "Posting class definitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user filters the "Legislation" labelled column in the table field with value "FR"
        # Account type = Expense
        And the user selects the row with text "Expense" in the "Account type name" labelled column header of the table field
        And the user clicks the "Account type name" labelled nested field of the selected row in the table field
        Then the "Posting class definition Item" titled page is displayed
        And the user selects the "Additional criteria" labelled select field on the main page
        Then the value of the select field is ""
        And the user writes "Tax" in the select field
        And the user selects "Tax" in the select field
        Then the value of the select field is "Tax"




    Scenario: 02 - reading the posting class definition for FR Sales revenue
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClassDefinition"
        Then the "Posting class definitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user filters the "Legislation" labelled column in the table field with value "FR"
        # Account type = Sales revenue
        And the user selects the row with text "Sales revenue" in the "Account type name" labelled column header of the table field
        And the user clicks the "Account type name" labelled nested field of the selected row in the table field
        Then the "Posting class definition Item" titled page is displayed
        And the user selects the "Additional criteria" labelled select field on the main page
        Then the value of the select field is "Tax"

    Scenario: 03 - reading the posting class definition for FR Landed cost expense
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClassDefinition"
        Then the "Posting class definitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user filters the "Legislation" labelled column in the table field with value "FR"
        # Account type = Sales revenue
        And the user selects the row with text "Landed cost expense" in the "Account type name" labelled column header of the table field
        And the user clicks the "Account type name" labelled nested field of the selected row in the table field
        Then the "Posting class definition Item" titled page is displayed
        And the user selects the "Additional criteria" labelled select field on the main page
        Then the value of the select field is ""
        And the user writes "Tax" in the select field
        And the user selects "Tax" in the select field
        Then the value of the select field is "Tax"

    Scenario: 4.1 - reading the posting class for Expense
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
        Then the "Posting classes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user selects the row with text "Standard rate items and services" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Posting class Item-Standard rate items and services" titled page is displayed
        And the user selects the "Lines" labelled nested grid field on the main page


        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText                                                |
            | Chart of accounts | FR chart of accounts                                    |
            | Account type name | Expense                                                 |
            | Account           | Achats stockés de mat. premières TVA débits taux normal |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "********"
        And the user refreshes the screen



    Scenario: 4.2 - reading the posting class for Landed cost expense
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText             |
            | Chart of accounts | FR chart of accounts |
            | Account type name | Landed cost expense  |
            | Account           | Transport            |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "********"
        And the user refreshes the screen

    Scenario: 4.3 - reading the posting class for Sales revenue
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader        | cellText                                        |
            | Chart of accounts   | FR chart of accounts                            |
            | Account type name   | Sales revenue                                   |
            | Additional criteria | Tax                                             |
            | Account             | Ventes de produits finis TVA débits taux normal |
        And the user expands the selected row of the nested grid field

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                      |
            | Tax          | EU tax deductible on purchases, standard rate |
            | Account      | Ventes de produits finis TVA enc. taux normal |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "********"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                        |
            | Tax          | Tax collected on debits, reduced rate           |
            | Account      | Ventes de produits finis TVA débits taux réduit |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "********"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                        |
            | Tax          | Tax collected on debits, standard rate          |
            | Account      | Ventes de produits finis TVA débits taux normal |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "********"
