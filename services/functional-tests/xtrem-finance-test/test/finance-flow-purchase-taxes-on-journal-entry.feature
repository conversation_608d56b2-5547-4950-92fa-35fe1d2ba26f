#This test can only be executed with sage.
# Item used: Taxes on JE Item
# supplier used: Muhoma Technology GmbH
# Site used: Sandfeld
# The purpose of this test is to verify that there's a new icon(dropdown) on JE lines that opens the tax details

@finance
Feature: finance-flow-purchase-taxes-on-journal-entry

    Scenario: 01 -  Posting the Sales invoice
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PI240004"
        And the user selects the row with text "PI240004" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI240004" titled page is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "No variance"
        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted." is displayed

    Scenario: 02 -  Getting the the JE number
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user filters the "Number" labelled column in the table field with value "PI240004"
        And the user selects the row with text "PI240004" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "postingDetails" bound table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        Then the user stores the value of the "Document number" labelled nested text field of the selected row in the table field with the key "[ENV_JENumber]"

    Scenario: 03 - Verify the Journal Entry line tax details
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_JENumber]"
        And the user selects the row with text "[ENV_JENumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled nested grid field on the main page


        #line 1
        And the user selects row with text "1600 -- Verbindlichkeiten" in column with header "Account" in the nested grid field
        And the user clicks the "Tax details" action of the selected row in the nested grid field

        And the user selects the "Tax category" labelled text field on a modal
        And the value of the text field is "Value Added Tax"

        And the user selects the "Tax" labelled text field on a modal
        And the value of the text field is "Purchase Reduced Rate"

        And the user selects the "Total excluding tax" labelled numeric field on a modal
        And the value of the numeric field is "900.00"

        And the user selects the "Total tax" labelled numeric field on a modal
        And the value of the numeric field is "63.00"

        And the user selects the "Total including tax" labelled numeric field on a modal
        And the value of the numeric field is "963.00"

        And the user selects the "Taxable base" labelled numeric field on a modal
        And the value of the numeric field is "900.00"

        And the user selects the "Tax rate" labelled numeric field on a modal
        And the value of the numeric field is "7.00"

        And the user selects the "Deductible tax amount" labelled numeric field on a modal
        And the value of the numeric field is "63.00"

        And the user selects the "Deductible tax rate" labelled numeric field on a modal
        And the value of the numeric field is "100.00"
        Then the user clicks the "OK" labelled business action button on a modal


        #line 2
        And the user selects row with text "3300 -- Wareneingang 7 % Vorsteuer" in column with header "Account" in the nested grid field
        And the user clicks the "Tax details" action of the selected row in the nested grid field

        And the user selects the "Tax category" labelled text field on a modal
        And the value of the text field is "Value Added Tax"

        And the user selects the "Tax" labelled text field on a modal
        And the value of the text field is "Purchase Reduced Rate"

        And the user selects the "Total excluding tax" labelled numeric field on a modal
        And the value of the numeric field is "900.00"

        And the user selects the "Total tax" labelled numeric field on a modal
        And the value of the numeric field is "63.00"

        And the user selects the "Total including tax" labelled numeric field on a modal
        And the value of the numeric field is "963.00"

        And the user selects the "Taxable base" labelled numeric field on a modal
        And the value of the numeric field is "900.00"

        And the user selects the "Tax rate" labelled numeric field on a modal
        And the value of the numeric field is "7.00"

        And the user selects the "Deductible tax amount" labelled numeric field on a modal
        And the value of the numeric field is "63.00"

        And the user selects the "Deductible tax rate" labelled numeric field on a modal
        And the value of the numeric field is "100.00"
        Then the user clicks the "OK" labelled business action button on a modal


        #line 3
        And the user selects row with text "1571 -- Vorsteuer 7 %" in column with header "Account" in the nested grid field
        And the user clicks the "Tax details" action of the selected row in the nested grid field

        And the user selects the "Tax category" labelled text field on a modal
        And the value of the text field is "Value Added Tax"

        And the user selects the "Tax" labelled text field on a modal
        And the value of the text field is "Purchase Reduced Rate"

        And the user selects the "Total excluding tax" labelled numeric field on a modal
        And the value of the numeric field is "900.00"

        And the user selects the "Total tax" labelled numeric field on a modal
        And the value of the numeric field is "63.00"

        And the user selects the "Total including tax" labelled numeric field on a modal
        And the value of the numeric field is "963.00"

        And the user selects the "Taxable base" labelled numeric field on a modal
        And the value of the numeric field is "900.00"

        And the user selects the "Tax rate" labelled numeric field on a modal
        And the value of the numeric field is "7.00"

        And the user selects the "Deductible tax amount" labelled numeric field on a modal
        And the value of the numeric field is "63.00"

        And the user selects the "Deductible tax rate" labelled numeric field on a modal
        And the value of the numeric field is "100.00"
        Then the user clicks the "OK" labelled business action button on a modal
