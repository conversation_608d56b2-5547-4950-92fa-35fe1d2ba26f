# The goal of this test is to verify that a user can create, update, and delete finance attribute

@finance
Feature: finance-crud-attribute

    Scenario: Verify the user is able to create an attribute
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        #Creating an attribute
        And the user clicks the "Create" labelled business action button on the navigation panel
        #Checking the isActive switch
        When the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "ON"
        #Fill in the ID and Name field
        And the user selects the "ID" labelled text field on the main page
        Then the user writes "2000" in the text field
        And the user selects the "Name" labelled text field on the main page
        Then the user writes "New attribute" in the text field
        #Checking the current attribute type
        And the user selects the "Attribute type" labelled reference field on the main page
        Then the value of the reference field is "Project"
        #Verifying attribute creation
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify the user is able to update an attribute
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        #Searching and selecting the created attribute
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "2000" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "New attribute" in the navigation panel
        #Updating the selected attribute
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Update attribute" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        #Verifying if the attribute has been updated
        And a toast containing text "Record updated" is displayed
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Update attribute"

    Scenario: Verify the user is able to delete an attribute
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        #Searching and selecting the updated attribute
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "2000" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Update attribute" in the navigation panel
        #Deleting the attribute
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verifying if the attribute has been deleted
        Then a toast containing text "Record deleted" is displayed
