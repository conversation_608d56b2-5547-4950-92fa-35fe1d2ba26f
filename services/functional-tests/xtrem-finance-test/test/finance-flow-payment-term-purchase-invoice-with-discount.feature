# The purpose of this test is to verify that user is able to create purchase invoice and credit memo
# with payment term that has discount and penalties

@finance
@distribution
@TestAayu1
Feature: finance-flow-payment-term-purchase-invoice-with-discount

    Scenario: 01 - Verify the user is able to create a payment term

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        And the user selects the "Business entity type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "All" in the dropdown-list field

        And the user selects the "ID *" labelled text field on the main page
        Then the user writes "Net33D" in the text field

        And the user selects the "Name *" labelled text field on the main page
        Then the user writes "Net 33 days" in the text field

        And the user selects the "Description *" labelled text field on the main page
        Then the user writes "Net 33 days" in the text field

        And the user selects the "days" bound numeric field on the main page
        Then the user writes "33" in the numeric field

        And the user selects the "Type *" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "After invoice date" in the dropdown-list field

        And the user selects the "From" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "After invoice date" in the dropdown-list field

        And the user selects the "discountDate" bound numeric field on the main page
        And the user writes "5" in the numeric field

        And the user selects the "discountType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Percentage" in the dropdown-list field

        And the user selects the "discountAmount" bound numeric field on the main page
        And the user writes "2" in the numeric field

        And the user selects the "penaltyType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Percentage" in the dropdown-list field

        And the user selects the "penaltyAmount" bound numeric field on the main page
        And the user writes "5" in the numeric field

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed


    Scenario: 02 - Verify the user is able to post the purchase invoce

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Purchase invoice" titled page is displayed
        # Fill in the header fields on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        # Add lines
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Safety training" in the reference field
        And the user selects "Safety training" in the reference field
        And the user selects the "Recipient site *" labelled reference field on the sidebar
        And the value of the reference field is "Sandfeld"
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        When the user writes "11" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "11" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

        When selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the user writes "Net 33 days" in the reference field
        And the user selects "Net 33 days" in the reference field

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        When the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_PINVOICEDISCOUNT]"

        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the dialog title is "Accept variances"
        And the user clicks the "Accept" button of the dialog on the main page
        Then a toast containing text "Variance status updated." is displayed

        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "121.00" in the numeric field
        And the user selects the "Total supplier tax" labelled numeric field on the main page
        And the user writes "22.99" in the numeric field

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        And takes a screenshot
        And the user waits for 10 seconds

    Scenario: 03 - Verify the user is able to create sales credit memo with discount and penalty

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        # Fill in the header fields on main page
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PINVOICEDISCOUNT]" in the navigation panel
        And the user clicks the record with the text "MUHOMA Technology GmbH" in the navigation panel
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Safety training"
        And the value of the "Quantity in purchase unit" labelled nested text field of the selected row in the table field is "11.00 d"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 11.00000"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 121.00"
        And the value of the "Tax amount" labelled nested text field of the selected row in the table field is "€ 22.99"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 143.99"
        And the value of the "Tax amount adjusted" labelled nested text field of the selected row in the table field is "€ 22.99"

        When selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "Net 33 days"

        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the dialog title is "Create purchase credit memo"

        And the user selects the "Reason *" labelled reference field on a modal
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field

        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"

        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes "121" in the numeric field

        And the user clicks the "Create" button of the dialog on the main page
        Then a toast containing text "Purchase credit memo created." is displayed

        When the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_PCREDITMEMO]"

        And the user selects the "Total credit memo tax" labelled numeric field on the main page
        And the user writes "22.99" in the numeric field

        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        Then a toast containing text "The purchase credit memo was posted." is displayed

        And the user waits 2 seconds

        And the user refreshes the screen

        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

    Scenario: 04 - Verify created purchased credit memo details

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PCREDITMEMO]" in the navigation panel
        And the user clicks the record with the text "MUHOMA Technology GmbH" in the navigation panel
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Safety training"
        And the value of the "Quantity in purchase unit" labelled nested text field of the selected row in the table field is "11.00 d"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 11.00000"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 121.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "€ 22.99"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 143.99"
        And the value of the "Tax amount adjusted" labelled nested text field of the selected row in the table field is "€ 22.99"

        When selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "Net 33 days"
