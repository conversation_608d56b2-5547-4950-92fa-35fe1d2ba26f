
# In SDMO users can now enter a received payment for a sales invoice on the 'Record Receipt' page,
# manage these payments on a dedicated 'Receipt' page, and void payments when necessary.
# The purpose of this test is to validate that payments are correctly entered on the 'Record Receipt' page
# and that sales credit notes are properly accounted for in this process.
# Additionally, the test must verify the accuracy of the displayed statuses of sales invoices and sales credit notes
# after a payment has been received and after a payment receipt has been voided.
# IMPORTANT: Intacct configuration status has to be not active (isIntacctServiceOptionActive in @sage/xtrem-structure/OptionManagementBase)
# IMPORTANT: Payment Tracking option status has to be active (paymentTrackingOption in @sage/xtrem-system/ServiceOptionState)
# Prerequisites: prerequisites-flow-verify-options-status

@finance
Feature: finance-crud-ar-payment-tracking-record-receipt-create-void

    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario Outline: 0.01 - Data creation - Sales invoices
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the user writes "Schmitt Apparatebau GmbH" in the reference field
        And the user selects "Schmitt Apparatebau GmbH" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes <Quantity> in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Net 15 No Penalty" in the reference field
        And the user selects "Net 15 No Penalty" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales invoice was posted." is displayed
        Examples:
            | Quantity | EnvName            |
            | "300"    | "[ENV_SOAR_NUM01]" |
            | "200"    | "[ENV_SOAR_NUM02]" |
            | "100"    | "[ENV_SOAR_NUM03]" |

    Scenario: 0.02 - Data creation - Sales credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_SOAR_NUM03]"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SOAR_NUM03]"
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Sales invoice [ENV_SOAR_NUM03]" titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        And a toast with text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_CMAR_NUM01]"
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales credit memo was posted." is displayed

    Scenario: 01 - Enter a payment receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/RecordReceipt"
        Then the "Record receipt" titled page is displayed
        When the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Customer *" labelled reference field on the main page
        And the user writes "Schmitt Apparatebau GmbH" in the reference field
        And the user selects "Schmitt Apparatebau GmbH" in the reference field
        And the user selects the "Bank account *" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And the user selects the "Date received" labelled date field on the main page
        And the user selects the "Payment method *" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Currency *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        And the user selects the "Payment amount *" labelled numeric field on the main page
        And the user writes "4500" in the numeric field
        And the user selects the "Transaction information" labelled text field on the main page
        And the user writes "AR payment tracking" in the text field
        And the user clicks in the "searchButton" bound button field on the main page

    Scenario: 02 - Result grid
        And the user selects the "lines" bound table field on the main page
        And the table field is not empty

        Then the user opens the filter of the "Invoice number" labelled column in the table field
        And the user searches "[ENV_SOAR_NUM03]" in the filter of the table field
        And the user ticks the item with text "[ENV_SOAR_NUM03]" in the filter of the table field
        And the user searches "[ENV_CMAR_NUM01]" in the filter of the table field
        And the user ticks the item with text "[ENV_CMAR_NUM01]" in the filter of the table field
        And the user searches "[ENV_SOAR_NUM01]" in the filter of the table field
        And the user ticks the item with text "[ENV_SOAR_NUM01]" in the filter of the table field
        And the user searches "[ENV_SOAR_NUM02]" in the filter of the table field
        And the user ticks the item with text "[ENV_SOAR_NUM02]" in the filter of the table field
        Then the user closes the filter of the "Invoice number" labelled column in the table field

        And the user selects the row with text "[ENV_SOAR_NUM03]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_CMAR_NUM01]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_SOAR_NUM01]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_SOAR_NUM02]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€ 930.00"
        And the user clicks the "Generate" labelled business action button on the main page
        And the dialog title is "Receipts" on the main page
        And the user selects the "Financial site" labelled reference field on a modal
        And the value of the reference field is "Sandfeld"
        And the user selects the "Customer" labelled reference field on a modal
        And the value of the reference field is "Schmitt Apparatebau GmbH"
        And the user selects the "Bank account" labelled reference field on a modal
        And the value of the reference field is "Bank Germany EUR"
        And the user selects the "Date received" labelled date field on a modal
        And the value of the date field is a generated date with value "T"
        And the user selects the "Payment method" labelled dropdown-list field on a modal
        And the value of the dropdown-list field is "EFT"
        And the user selects the "Transaction information" labelled text field on a modal
        And the value of the text field is "AR payment tracking"
        And the user selects the "Currency" labelled reference field on a modal
        And the value of the reference field is "Euro"
        And the user selects the "Payment amount" labelled numeric field on a modal
        And the value of the numeric field is "4,500.00"
        And the user selects the "Lines" labelled table field on a modal
        And the user selects the row with text "[ENV_SOAR_NUM01]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 3,570.00"
        And the user selects the row with text "[ENV_SOAR_NUM02]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 930.00"
        And the user selects the row with text "[ENV_SOAR_NUM03]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 1,190.00"
        And the user selects the row with text "[ENV_CMAR_NUM01]" in the "Document number" labelled column header of the table field
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ -1,190.00"
        And the user clicks the "Confirm" labelled business action button on a modal
    # @todo uncomment next line when the issue with "has-pending-promises" on buttons is fixed
    # And a toast containing text "The following receipt was created:" is displayed

    Scenario: 03 - Verify and store the Receipt number
        # @todo remove next line when the issue with "has-pending-promises" on buttons is fixed
        And the user waits 3 seconds
        And the user refreshes the screen
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Receipt"
        Then the "Receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the option menu of the table field is "Posted receipts"
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AR payment tracking" in the "Reference" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested link field of the selected row in the table field with the key "[ENV_ReceiptNumber]"

    Scenario Outline: 04 - Check 'Payment' tab on the Sales invoice page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value <Number>
        And the user selects the row with text <Number> in the "Number" labelled column header of the table field
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is <Amount>
        And the value of the "displayStatus" bound nested label field of the selected row in the table field is <Status>
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the <PageTitle> titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is <Status>
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is <StepStatus>
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "receipts" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Receipt number" labelled nested link field of the selected row in the table field is "[ENV_ReceiptNumber]"
        And the user selects the row 1 of the table field
        And the user clicks the "Receipt number" labelled nested field of the selected row in the table field
        And an info dialog appears on a full width modal
        # @todo create ER to be able to use "Receipt [ENV_ReceiptNumber]"
        # And the text in the header of the dialog is "Receipt RCTDE250001" on a full width modal
        And the user clicks the Close button of the dialog on a full width modal
        And the <PageTitle> titled page is displayed
        Examples:
            | Number             | Status           | StepStatus | PageTitle                        | Amount       |
            | "[ENV_SOAR_NUM01]" | "Paid"           | complete   | "Sales invoice [ENV_SOAR_NUM01]" | "€ 3,570.00" |
            | "[ENV_SOAR_NUM02]" | "Partially paid" | current    | "Sales invoice [ENV_SOAR_NUM02]" | "€ 2,380.00" |
            | "[ENV_SOAR_NUM03]" | "Paid"           | complete   | "Sales invoice [ENV_SOAR_NUM03]" | "€ 1,190.00" |

    Scenario: 05 - Check 'Payment' tab on the Sales credit memo page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_CMAR_NUM01]"
        And the user selects the row with text "[ENV_CMAR_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Sales credit memo [ENV_CMAR_NUM01]" titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Paid"
        And the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Pay" item of the step-sequence is complete
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "receipts" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Receipt number" labelled nested link field of the selected row in the table field is "[ENV_ReceiptNumber]"
        And the user selects the row 1 of the table field
        And the user clicks the "Receipt number" labelled nested field of the selected row in the table field
        And an info dialog appears on a full width modal
        # @todo create ER to be able to use "Receipt [ENV_ReceiptNumber]"
        # And the text in the header of the dialog is "Receipt RCTDE250001" on a full width modal
        And the user clicks the Close button of the dialog on a full width modal
        And the "Sales credit memo [ENV_CMAR_NUM01]" titled page is displayed

    Scenario: 06 - Open the previously created receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Receipt"
        Then the "Receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the option menu of the table field is "Posted receipts"
        And the user selects the row with text "[ENV_ReceiptNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Receipt [ENV_ReceiptNumber]" titled page is displayed

    Scenario Outline: 07 - Check tunneling on the receipt
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text <Number> in the "Document number" labelled column header of the table field
        And the user clicks the "Document number" labelled nested field of the selected row in the table field
        And the user presses Enter
        And the user waits 3 seconds
        And an info dialog appears on a full width modal
        And the text in the header pill label of the dialog is <Status> on a full width modal
        And the user selects the "Number" labelled text field on a full width modal
        And the value of the text field is <Number>
        And the user clicks the Close button of the dialog on a full width modal
        And the "Receipt [ENV_ReceiptNumber]" titled page is displayed
        Examples:
            | Number             | Status           |
            | "[ENV_CMAR_NUM01]" | "Paid"           |
            | "[ENV_SOAR_NUM01]" | "Paid"           |
            | "[ENV_SOAR_NUM02]" | "Partially paid" |
            | "[ENV_SOAR_NUM03]" | "Paid"           |

    Scenario: 08 - Void receipt and check receipt page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Receipt"
        Then the "Receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the option menu of the table field is "Posted receipts"
        And the user selects the row with text "[ENV_ReceiptNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Receipt [ENV_ReceiptNumber]" titled page is displayed
        And the "Voided" labelled checkbox field on the main page is hidden
        And the "Voided on" labelled date field on the main page is hidden
        And the "Void text" labelled text field on the main page is hidden
        And the user selects the "Date received" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user clicks the "Void" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Void receipt" on the main page
        And the user selects the "Date *" labelled date field on a modal
        And the value of the date field is a generated date with value "T"
        And the user selects the "Text" labelled text field on a modal
        And the user writes "Void auto text" in the text field
        And the user clicks the "Confirm" button of the Confirm dialog
        And the "Voided" labelled checkbox field on the main page is displayed
        And the user selects the "isVoided" bound checkbox field on the main page
        # @todo atm it doesn't work to check that this specific field is checked, find a way
        # And the value of the checkbox field is "true"
        And the checkbox field is read-only
        And the "Voided on" labelled date field on the main page is displayed
        And the user selects the "Voided on" labelled date field on the main page
        And the date field is read-only
        And the value of the date field is a generated date with value "T"
        And the "Void text" labelled text field on the main page is displayed
        And the user selects the "Void text" labelled text field on the main page
        And the text field is read-only
        And the value of the text field is "Void auto text"
        And the "Void" labelled business action button on the main page is hidden

    Scenario Outline: 09 - Check 'Payment' tab on the Sales invoice page after void
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value <Number>
        And the user selects the row with text <Number> in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the <PageTitle> titled page is displayed
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is <Status>
        And the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the status of the "Credit" item of the step-sequence is <StepStatus>
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "receipts" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "voided" labelled nested checkbox field of the selected row in the table field is "true"
        Examples:
            | Number             | Status     | PageTitle                        | StepStatus |
            | "[ENV_SOAR_NUM01]" | "Posted"   | "Sales invoice [ENV_SOAR_NUM01]" | incomplete |
            | "[ENV_SOAR_NUM02]" | "Posted"   | "Sales invoice [ENV_SOAR_NUM02]" | incomplete |
            | "[ENV_SOAR_NUM03]" | "Credited" | "Sales invoice [ENV_SOAR_NUM03]" | complete   |

    Scenario: 10 - Check 'Payment' tab on the Sales credit memo page after void
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_CMAR_NUM01]"
        And the user selects the row with text "[ENV_CMAR_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Sales credit memo [ENV_CMAR_NUM01]" titled page is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Posted"
        # @todo Validate that only steps 'Create' and 'Post' are now active - maybe here is room for improvement
        # in order to make sure there is no other step present
        And the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "receipts" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "voided" labelled nested checkbox field of the selected row in the table field is "true"
