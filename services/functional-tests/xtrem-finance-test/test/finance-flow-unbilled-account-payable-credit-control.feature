# The purpose of this test is to verify that control is executed correctly on unbilled quantity for credit memo
# Unbilled quantity = (receivedQuantity - returnedQuantity) - (invoicedQuantity - creditedQuantity)
# only the credit memos linked to a return are considered in the equation
# data created on QA layer to verify the control are tests cases S1 & S4
# Link to test Suite : https://confluence.sage.com/x/urx3Fg


@finance
@distribution
Feature: finance-flow-unbilled-account-payable-credit-control

    Scenario: Add lock - LCK_CREATEAPC

        Given the user opens the application on a HD desktop
        And the user adds the lock entry "LCK_CREATEAPC"

    Scenario: Unbilled accounts payable verification for Receipt invoiced , not returned, & credited from invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/UnbilledAccountPayableInquiry"
        Then the "Unbilled accounts payable" titled page is displayed
        # Filtering Company field
        When the user selects the "Company" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "TE Connectivity" in the reference field
        And the user selects "TE Connectivity" in the reference field
        Then the value of the reference field is "TE Connectivity"
        When the user selects the "Site" labelled multi reference field on the main page
        And the user clears the multi reference field
        And the user writes "TE Hampton" in the multi reference field
        And the user selects "TE Hampton" in the multi reference field
        # Filtering From supplier field
        When the user selects the "From supplier" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Lenovo" in the reference field
        And the user selects "Lenovo" in the reference field
        Then the value of the reference field is "Lenovo"
        # Filtering To supplier field
        When the user selects the "To supplier" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Lenovo" in the reference field
        And the user selects "Lenovo" in the reference field
        # Entering AS of Date
        When the user selects the "As of date" labelled date field on the main page
        And the user writes "08/31/2023" in the date field
        Then the value of the date field is "08/31/2023"
        # Validation to display the result
        And the user clicks the "Run" labelled business action button on the main page
        Then a toast containing text "Unbilled accounts payable request sent." is displayed
        And the user waits 5 seconds
        And a toast containing text "Unbilled accounts payable finished." is displayed
        # Verify value in the table field
        When the user selects the "Results" labelled table field on the main page

        # Case S1: Verification of Receipt invoiced , not returned, & credited from invoice
        And the user selects the row with text "3847TY7" in the "Item ID" labelled column header of the table field
        And the value of the "Bill-by supplier ID" labelled nested reference field of the selected row in the table field is "20007"
        And the value of the "Bill-by supplier" labelled nested reference field of the selected row in the table field is "Lenovo"
        And the value of the "Company ID" labelled nested reference field of the selected row in the table field is "100"
        And the value of the "Financial site ID" labelled nested reference field of the selected row in the table field is "100"
        And the value of the "Unbilled quantity" labelled nested numeric field of the selected row in the table field is "500 each"
        And the value of the "Net price" labelled nested numeric field of the selected row in the table field is "$ 4.95"
        And the value of the "Unbilled amount" labelled nested numeric field of the selected row in the table field is "$ 2,475.00"
        And the value of the "Item ID" labelled nested reference field of the selected row in the table field is "3847TY7"
        And the value of the "Stock site" labelled nested reference field of the selected row in the table field is "TE Hampton"
        And the value of the "Receipt number" labelled nested link field of the selected row in the table field is "PR230024"
        And the value of the "Received quantity" labelled nested numeric field of the selected row in the table field is "1,000 each"
        And the value of the "Returned quantity" labelled nested numeric field of the selected row in the table field is "0 each"
        And the value of the "Invoiced quantity" labelled nested numeric field of the selected row in the table field is "500 each"
        And the value of the "Credited quantity" labelled nested numeric field of the selected row in the table field is "0 each"


    Scenario: Unbilled accounts payable verification for Receipt invoiced , returned, & credited from return
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/UnbilledAccountPayableInquiry"
        Then the "Unbilled accounts payable" titled page is displayed
        # Filtering Company field
        When the user selects the "Company" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "TE Connectivity" in the reference field
        And the user selects "TE Connectivity" in the reference field
        Then the value of the reference field is "TE Connectivity"
        When the user selects the "Site" labelled multi reference field on the main page
        And the user clears the reference field
        When the user selects the "From supplier" labelled reference field on the main page
        And the user clears the reference field
        # Filtering To supplier field
        When the user selects the "To supplier" labelled reference field on the main page
        And the user clears the reference field
        # Entering AS of Date
        When the user selects the "As of date" labelled date field on the main page
        And the user writes "08/31/2023" in the date field
        Then the value of the date field is "08/31/2023"
        # Validation to display the result
        And the user clicks the "Run" labelled business action button on the main page
        Then a toast containing text "Unbilled accounts payable request sent." is displayed
        And the user waits 5 seconds
        And a toast containing text "Unbilled accounts payable finished." is displayed
        # Verify value in the table field
        When the user selects the "Results" labelled table field on the main page
        # Case S4:  Verification of Receipt invoiced , returned, & credited from return
        And the user selects the row with text "E2GA7NM02" in the "Item ID" labelled column header of the table field
        And the value of the "Bill-by supplier ID" labelled nested reference field of the selected row in the table field is "20007"
        And the value of the "Bill-by supplier" labelled nested reference field of the selected row in the table field is "Lenovo"
        And the value of the "Company ID" labelled nested reference field of the selected row in the table field is "100"
        And the value of the "Financial site ID" labelled nested reference field of the selected row in the table field is "100"
        And the value of the "Unbilled quantity" labelled nested numeric field of the selected row in the table field is "480 each"
        And the value of the "Net price" labelled nested numeric field of the selected row in the table field is "$ 4.95"
        And the value of the "Unbilled amount" labelled nested numeric field of the selected row in the table field is "$ 2,376.00"
        And the value of the "Item ID" labelled nested reference field of the selected row in the table field is "E2GA7NM02"
        And the value of the "Stock site" labelled nested reference field of the selected row in the table field is "TE Hampton"
        And the value of the "Receipt number" labelled nested link field of the selected row in the table field is "PR230025"
        And the value of the "Received quantity" labelled nested numeric field of the selected row in the table field is "1,000 each"
        And the value of the "Returned quantity" labelled nested numeric field of the selected row in the table field is "100 each"
        And the value of the "Invoiced quantity" labelled nested numeric field of the selected row in the table field is "500 each"
        And the value of the "Credited quantity" labelled nested numeric field of the selected row in the table field is "80 each"
    Scenario: Delete previously created record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/UnbilledAccountPayableInquiry"
        Then the "Unbilled accounts payable" titled page is displayed

        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
    Scenario: Remove lock - LCK_CREATEAPC

        And the user removes the lock entry "LCK_CREATEAPC"
