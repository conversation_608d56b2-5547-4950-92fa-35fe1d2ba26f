#This test can only be executed with sage.
# The test will create a sales order, create a shipment from the sales order, create an invoice from the shipment and post the invoice
# The test will also verify that the tax is determined correctly
# Item used: DE-DE sales item
# Item Item tax group: Goods, standard VAT, so the on tax details the tax must be "Sale Standard Rate"
# On Acc receivable invoice, The account usedd must be " 8400 -- Erlose 19% USt"
# Customer used: Schmitt Apparatebau GmbH
# Site used: Sandfeld
# Important: The test will fail at scenario 1 and 2 because of  XT-77498, XT-77688

@finance

Feature: finance-flow-de-sales-domestic-customer-tax-determination

    Scenario: 01 - create the sales order flow and verify tax used
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "<PERSON>feld" in the reference field
        # Fill in Customer reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Schmitt Apparatebau GmbH" in the reference field
        And the user selects "Schmitt Apparatebau GmbH" in the reference field

        # Add a line for stock item
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DE-DE sales item" in the reference field
        And the user selects "DE-DE sales item" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.23" in the numeric field
        # And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 2 seconds
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed
        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SONUM]"
        ## Verify the tax used
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE sales item" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        And the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        Then the value of the "Tax" labelled nested text field of the selected row in the table field is "Sale Standard Rate"
        And the user clicks the "OK" labelled business action button on a modal
        #Save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        Then the user dismisses all the toasts

        # Confirm sales order details
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        # Verify Confirmation
        Then a toast containing text "The sales order was confirmed." is displayed
        And the user waits 2 seconds


    Scenario: 02 - create shipment from sales order
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Confirmed"
        ##Allocate stock
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE sales item" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "Stock allocation" labelled table field on a modal
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal
        ##Verify the allocation
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE sales item" in the "item" labelled column header of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"

        And the user clicks the "Create Shipment" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        And a toast containing text "1 shipment(s) created" is displayed
        And the user dismisses all the toasts
        ##Confirm the shipment
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And a toast containing text "Status updated." is displayed
        And the user dismisses all the toasts
        ##Post
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And a toast containing text "The sales shipment was posted" is displayed
        And the user refreshes the screen

    Scenario: 03 - create an invoice from sales shipment and verify tax used
        And the user clicks the "Create invoice" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        And a toast containing text "Record created" is displayed
        And the user waits 2 seconds
        ## Verify the tax used
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE sales item" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        And the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        Then the value of the "Tax" labelled nested text field of the selected row in the table field is "Sale Standard Rate"
        And the user clicks the "OK" labelled business action button on the main page
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        ##Post
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales invoice was posted." is displayed
        And the user refreshes the screen
        ## Storing the Account receivable invoice number
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "posting" labelled table field on the main page
        And the user selects the row with text "Accounts receivable invoice" in the "Document type" labelled column header of the table field
        Then the user stores the value of the "Document number" labelled nested text field of the selected row in the table field with the key "[ENV_ARNumber]"
        And the user selects the "posting" labelled table field on the main page
        And the user refreshes the screen

    Scenario: 04 - Saving the JE number
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "posting" labelled table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        Then the user stores the value of the "Document number" labelled nested text field of the selected row in the table field with the key "[ENV_JENumber]"


    Scenario: 05 - Verify the Accounts Receivable invoice Account
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/AccountsReceivableInvoice"
        Then the "Accounts receivable invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_ARNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user selects row with text "Goods" in column with header "Line type" in the nested grid field
        ## The account must be "8400 -- Erlöse 19 % USt"
        And the value of the "Account" labelled nested text field of the selected row in the nested grid field is "8400 -- Erlöse 19 % USt"

    Scenario: 06 - Verify the Journal Entry Accounts
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_JENumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user selects row with text "8400 -- Erlöse 19 % USt" in column with header "Contra account" in the nested grid field
        And the value of the "Account" labelled nested text field of the selected row in the nested grid field is "1400 -- Forderungen"
        And the value of the "Tax" labelled nested text field of the selected row in the nested grid field is ""

        And the user selects row with text "DE072 -- Schmitt Apparatebau GmbH" in column with header "Contra account" in the nested grid field
        And the value of the "Account" labelled nested text field of the selected row in the nested grid field is "8400 -- Erlöse 19 % USt"
        And the value of the "Tax" labelled nested text field of the selected row in the nested grid field is "Sale Standard Rate"

        And the user selects row with text "1776 -- Umsatzsteuer 19%" in column with header "Account" in the nested grid field
        And the value of the "Contra account" labelled nested text field of the selected row in the nested grid field is "DE072 -- Schmitt Apparatebau GmbH"
        And the value of the "Tax" labelled nested text field of the selected row in the nested grid field is "Sale Standard Rate"
