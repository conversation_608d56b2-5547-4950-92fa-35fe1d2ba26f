# The purpose of this test is to ensure the onhold behaviors for a customer work correctly according to analysis on the Sales Order page.
# Analysis : https://confluence.sage.com/display/XTREEM/Intacct+integration+-+Customer+on-hold+and+credit+limit+-+Analysis
# Intacct integration will not be used as the test will be manipulating the values from SDMO to test the various scenarios
# This requires that all Intacct tenants are switched OFF before this test runs
# Company: Company 01/Site 01
# Customer: Onhold_Customer_02
# Sales Orders: SO_SH_Blocking and SO_SH_Warning
@finance
@distribution
Feature: finance-flow-sales-shipment-on-hold-credit-limit

    Scenario: 0-1 Prerequisite - Set customer to Onhold

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        # Find the customer and verify readonly
        When the user selects the "Customers" labelled table field on the main page
        And the user selects the row with text "Onhold_Customer_02" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        When selects the "Financial" labelled navigation anchor on the main page

        # Verify Credit limit read-only
        And the user selects the "Credit limit" labelled numeric field on the main page
        And the user writes "500" in the numeric field
        And the user stores the value of the numeric field with the key "[ENV_Cus_Credit_Limit]"
        # Verify on hold read-only
        And the user presses Tab
        # Verify on hold read-only
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user waits for 2 seconds

        And the user clicks the "Put on hold" labelled more actions button in the header

        When the user selects the "On hold" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        # Verify Credit limit
        When the user selects the "Credit limit" labelled numeric field on the main page
        Then the value of the numeric field is "[ENV_Cus_Credit_Limit].000"


    Scenario: 0-2 Prerequisite - Set Company on hold behavior to Blocking

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user stores the value of the dropdown-list field with the key "[ENV_Cus_OH_Check_Value]"

        # Save
        #    When the user selects the "Save" labelled business action button on the main page
        #    Then a toast with text "The record has been updated" is displayed

        # Verify if the blocking is selected
        When the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "[ENV_Cus_OH_Check_Value]"

    Scenario: 1.1 - Create a Sales Shipment for an on hold customer with BLOCKING behavior
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Enter Stock site
        And the user selects the "Stock site" labelled reference field on the main page
        And the user writes "Site 01" in the reference field
        And the user selects "Site 01" in the reference field

        # Enter Sold-to customer
        And the user selects the "Ship-to customer" labelled reference field on the main page
        And the user writes "Onhold_Customer_02" in the reference field
        And the user selects "Onhold_Customer_02" in the reference field

        # Select another tab
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SS_OnHold_Warning" in the text field
        And the user stores the value of the text field with the key "[ENV_SS1_Number]"

        # Testing for this in this scenario
        # Verify that the warning icon is now displayed with text
        # TODO: Uncomment when bug is fixed : https://jira.sage.com/browse/XT-58528

        And the user selects the "Ship-to customer" labelled reference field on the main page
        # Then the ""Customer on hold Credit limit £ 0" validation warning message of the reference field is displayed
        # Then the "Customer on hold" validation warning message of the reference field is displayed

        # Add a line for non stock item
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from orders" labelled business action button of the table field

        # Verify Error validation message
        Then a error toast containing text "The sales order lines cannot be selected. The bill-to customer is on hold." is displayed

    Scenario: 1.2 - Post a Sales Shipment for an on hold customer with BLOCKING behavior
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "None" in the dropdown-list field
        And the user selects "None" in the dropdown-list field

        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        And the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Enter Sold-to customer
        And the user selects the "Ship-to customer" labelled reference field on the main page
        And the user writes "Onhold_Customer_02" in the reference field
        And the user selects "Onhold_Customer_02" in the reference field

        # Enter Stock site
        And the user selects the "Stock site" labelled reference field on the main page
        And the user writes "Site 01" in the reference field
        And the user selects "Site 01" in the reference field

        # Select another tab
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SS_OnHold_Blocking" in the text field
        And the user stores the value of the text field with the key "[ENV_SS1_Number]"

        # Testing for this in this scenario
        # Verify that the warning icon is now displayed with text
        # TODO: Uncomment when bug is fixed : https://jira.sage.com/browse/XT-58528

        And the user selects the "Ship-to customer" labelled reference field on the main page
        # Then the ""Customer on hold Credit limit £ 0" validation warning message of the reference field is displayed
        # Then the "Customer on hold" validation warning message of the reference field is displayed

        # Add a line for non stock item
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from orders" labelled business action button of the table field

        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "SO_SH_Blocking" in the "Document" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

        # Save the Sales Shipment number
        And the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SS1_Number]"

        # Confirm Sales Shipment details
        And the user clicks the "Confirm" labelled business action button on the main page
        Then the dialog title is "Confirm sales shipment"
        And the text in the body of the dialog is "You are about to set this sales shipment to 'Confirmed'." on the main page
        When the user clicks the "Confirm" button of the Confirm dialog

        #Verify Confirmation
        Then a toast containing text "Status updated." is displayed

        # Set the Company on hold parameter to blocking again
        And the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Blocking" in the dropdown-list field
        And the user selects "Blocking" in the dropdown-list field
        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        And the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed

        # Find the specific Sales shipment
        When the user selects the "Sales shipments" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user selects the "Sales shipments" labelled table field on the main page
        And the user selects the row with text "[ENV_SS1_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user clicks the "Post stock" labelled business action button on the main page
        And the text in the body of the dialog is "You are about to set this sales shipment to 'Shipped'." on the main page
        When the user clicks the "Confirm" button of the Confirm dialog

        # Verify Error validation message
        Then a error toast containing text "The bill-to customer is on hold. The shipment cannot be posted." is displayed

    Scenario: 1.3 - Create a Sales Invoice from a Sales Shipment for an on hold customer with BLOCKING behavior
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "None" in the dropdown-list field
        And the user selects "None" in the dropdown-list field

        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed

        # Find the specific shipment
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SS1_Number]"
        And the user selects the row with text "[ENV_SS1_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Post the sales shipment
        And the user clicks the "Post stock" labelled business action button on the main page
        And the text in the body of the dialog is "You are about to set this sales shipment to 'Shipped'." on the main page
        When the user clicks the "Confirm" button of the Confirm dialog

        # Save the Sales Shipment number
        And the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SS1_Number]"

        # Set the Company on hold parameter to blocking again
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Blocking" in the dropdown-list field
        And the user selects "Blocking" in the dropdown-list field
        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed

        # Find the specific shipment
        When the user selects the "Sales shipments" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user selects the "Sales shipments" labelled table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SS1_Number]"
        And the user selects the row with text "[ENV_SS1_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user clicks the "Create invoice" labelled business action button on the main page
        And the text in the body of the dialog is "You are about to create an invoice from this sales shipment." on the main page
        When the user clicks the "Create" button of the Confirm dialog

        # Verify validation message
        Then a toast containing text "Record created" is displayed

    Scenario: 2-0 Prerequisite - Set Company on hold behavior to None to create a sales order

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "None" in the dropdown-list field
        And the user selects "None" in the dropdown-list field
        And the user stores the value of the dropdown-list field with the key "[ENV_Cus_OH_Check_Value]"

        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # Verify update
        When the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "[ENV_Cus_OH_Check_Value]"

    Scenario: 2-1 Prerequisite - Set Company on hold behavior to Warning

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Warning" in the dropdown-list field
        And the user selects "Warning" in the dropdown-list field
        And the user stores the value of the dropdown-list field with the key "[ENV_Cus_OH_Check_Value]"

        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # Verify update
        When the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "[ENV_Cus_OH_Check_Value]"

    Scenario: 2.1 - Create a Sales Shipment for an on hold customer with WARNING behavior
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Enter Sold-to customer
        And the user selects the "Ship-to customer" labelled reference field on the main page
        And the user writes "Onhold_Customer_02" in the reference field
        And the user selects "Onhold_Customer_02" in the reference field

        # Enter Stock site
        And the user selects the "Stock site" labelled reference field on the main page
        And the user writes "Site 01" in the reference field
        And the user selects "Site 01" in the reference field

        # Select another tab
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SS_OnHold_Warning" in the text field
        And the user stores the value of the text field with the key "[ENV_SS2_Number]"

        # Testing for this in this scenario
        # Verify that the warning icon is now displayed with text
        # TODO: Uncomment when bug is fixed : https://jira.sage.com/browse/XT-58528

        And the user selects the "Ship-to customer" labelled reference field on the main page
        # Then the ""Customer on hold Credit limit £ 0" validation warning message of the reference field is displayed
        # Then the "Customer on hold" validation warning message of the reference field is displayed

        # Add a line for non stock item
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from orders" labelled business action button of the table field
        # Confirm customer on hold
        Then the dialog title is "Customer on hold"
        And the text in the body of the dialog is "You are about to select sales orders for an on hold customer." on the main page
        When the user clicks the "Continue" button of the Confirm dialog

        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "SO_SH_Warning" in the "Document" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

        # Save the Sales Shipment number
        And the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SS2_Number]"

        # Verify Error validation message
        Then a toast with text "Record created" is displayed

    Scenario: 2.2 - Post a Sales Shipment for an on hold customer with WARNING behavior
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed

        # Find the specific Sales shipment
        When the user selects the "Sales shipments" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user selects the "Sales shipments" labelled table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SS2_Number]"
        And the user selects the row with text "[ENV_SS2_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Confirm Sales Shipment details
        And the user clicks the "Confirm" labelled business action button on the main page
        Then the dialog title is "Confirm sales shipment"
        And the text in the body of the dialog is "You are about to set this sales shipment to 'Confirmed'." on the main page
        When the user clicks the "Confirm" button of the Confirm dialog
        #Verify Confirmation
        Then a toast containing text "Status updated." is displayed

        And the user clicks the "Post stock" labelled business action button on the main page
        And the text in the body of the dialog is "You are about to post the sales shipment for an on hold customer." on the main page
        When the user clicks the "Continue" button of the Confirm dialog

    # Verify validation message
    # comment this until this item is fixed: XT-76059
    # Then a toast containing text "The sales shipment has been posted." is displayed

    Scenario: 2.3 - Create a Sales invoice for a Sales Shipment with an on hold customer with WARNING behavior
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment/"
        Then the "Sales shipments" titled page is displayed

        # Find the specific Sales shipment
        When the user selects the "Sales shipments" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user selects the "Sales shipments" labelled table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SS2_Number]"
        And the user selects the row with text "[ENV_SS2_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Create the sales invoice
        And the user clicks the "Create invoice" labelled business action button on the main page
        And the text in the body of the dialog is "You are about to create an invoice from this sales shipment." on the main page
        When the user clicks the "Create" button of the Confirm dialog

        # Verify validation message
        Then a toast containing text "Record created" is displayed

    Scenario: 3.0 Cleanup to reset Onhold customer
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        # Find the customer and verify readonly
        When the user selects the "Customers" labelled table field on the main page
        And the user selects the row with text "Onhold_Customer_02" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        When selects the "Financial" labelled navigation anchor on the main page

        # Verify Credit limit read-only
        And the user selects the "Credit limit" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user stores the value of the numeric field with the key "[ENV_Cus_Credit_Limit]"
        And the user presses Tab

        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user waits for 2 seconds

        # Verify on hold read-only
        And the user clicks the "Remove on hold" labelled more actions button in the header
        Then the value of the checkbox field is "false"
        # Verify Credit limit
        When the user selects the "Credit limit" labelled numeric field on the main page
        Then the value of the numeric field is "[ENV_Cus_Credit_Limit].000"

    Scenario: 3.1 Set the Company on hold parameter to blocking again
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "Company 01" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company Company 01" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Blocking" in the dropdown-list field
        And the user selects "Blocking" in the dropdown-list field
        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
