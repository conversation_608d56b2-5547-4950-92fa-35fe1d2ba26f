#This test can only be executed with sage.
#The goal of this test is to CRUD the posting class and verify business rules


@finance
Feature: finance-crud-posting-class

   Scenario: 01 - create posting class
      Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
      Then the "Posting classes" titled page is displayed
      When the user clicks the "Create" labelled business action button on the navigation panel
      #Fill texts fields
      And the user selects the "ID" labelled text field on the main page
      And the user writes "ID-010" in the text field
      And the user selects the "Name" labelled text field on the main page
      And the user writes "New posting class" in the text field
      And the user selects the "Item type" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      And the user selects "Service item | Stock item | Landed cost item" in the multi dropdown field
      And the user clicks the "addLine" bound header action button on the main page
      And the user selects the "lines" bound nested grid field on the main page
      #landed cost line
      And the user selects row with text "" in column with header "Chart of accounts" in the nested grid field
      And the user writes "GB chart of accounts" in the "Chart of accounts" labelled nested reference field of the selected row in the nested grid field
      And the user writes "Landed cost accrual" in the "Account type name" labelled nested reference field of the selected row in the nested grid field
      And the user writes "Landed cost accrual" in the "Account" labelled nested reference field of the selected row in the nested grid field
      #service line
      And the user clicks the "addLine" bound header action button on the main page
      And the user selects the "lines" bound nested grid field on the main page
      And the user selects row with text "" in column with header "Chart of accounts" in the nested grid field
      And the user writes "FR chart of accounts" in the "Chart of accounts" labelled nested reference field of the selected row in the nested grid field
      And the user presses Enter
      And the user writes "Expense" in the "Account type name" labelled nested reference field of the selected row in the nested grid field
      And the user writes "Achats d'études et prest. de services TVA enc. taux normal" in the "Account" labelled nested reference field of the selected row in the nested grid field
      #Save
      And the user clicks the "Save" labelled business action button on the main page
      Then a toast containing text "Record created" is displayed

   Scenario: 02 - Read posting class
      Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
      Then the "Posting classes" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "New posting class"
      And the user selects the row with text "New posting class" in the "name" bound column header of the table field
      And the user clicks the "name" bound nested field of the selected row in the table field
      Then the "Posting class Item-New posting class" titled page is displayed
      #Reading
      When the user selects the "Name" labelled text field on the main page
      And the value of the text field is "New posting class"
      And the user selects the "lines" bound nested grid field on the main page
      And the user selects row with text "FR chart of accounts" in column with header "Chart of accounts" in the nested grid field
      And the value of the "Chart of accounts" labelled nested numeric field of the selected row in the nested grid field is "FR chart of accounts"
      And the value of the "Account type name" labelled nested numeric field of the selected row in the nested grid field is "Expense"
      Then the value of the "Account" labelled nested numeric field of the selected row in the nested grid field is "Achats d'études et prest. de services TVA enc. taux normal"


   Scenario: 03 - Update posting class - untick stock /tick non-stock
      Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
      Then the "Posting classes" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "New posting class"
      And the user selects the row with text "New posting class" in the "name" bound column header of the table field
      And the user clicks the "name" bound nested field of the selected row in the table field
      Then the "Posting class Item-New posting class" titled page is displayed
      #Updating
      And the user selects the "Item type" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      Then the value of the multi dropdown field is "Stock item, Service item, Landed cost item"
      And the user selects "Landed cost item" in the multi dropdown field
      And the user clicks the "Confirm update" button of the Confirm dialog
      And the user selects "Non-stock item" in the multi dropdown field
      When the user selects the "Name" labelled text field on the main page
      And the value of the text field is "New posting class"
      And the user selects the "Item type" labelled multi dropdown field on the main page
      Then the value of the multi dropdown field is "Stock item, Service item, Non-stock item"
      #Save
      And the user clicks the "Save" labelled business action button on the main page
      Then a toast containing text "Record updated" is displayed

   Scenario: 04 - Update posting class- Select lines via template
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
      Then the "Posting classes" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "New posting class"
      And the user selects the row with text "New posting class" in the "name" bound column header of the table field
      And the user clicks the "name" bound nested field of the selected row in the table field
      Then the "Posting class Item-New posting class" titled page is displayed
      #Selecting Lines
      And the user selects the "Item type" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      And the user selects "Landed cost item" in the multi dropdown field
      And the user clicks the "Select lines" labelled business action button on the main page
      And the user waits for 2 second
      And the user selects the "Posting class template" labelled reference field on a modal
      And the user waits for 2 second
      And the user writes "Standard rate items and services" in the reference field
      And the user selects "Standard rate items and services" in the reference field
      And the dialog title is "Select lines" on the main page
      And the user clicks the "ok" button of the Message dialog on the main page
      And the user selects the "Copy accounts" labelled checkbox field on a modal
      And the user ticks the checkbox field
      And the user selects the "Select all lines" labelled checkbox field on a modal
      And the user ticks the checkbox field
      And the user clicks the "Ok" button of the Custom dialog
      #Save
      And the user clicks the "Save" labelled business action button on the main page
      Then a toast containing text "Record updated" is displayed

   Scenario: 05 - Update posting class- untick item types to verify lines deletion via confirmation message
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
      Then the "Posting classes" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "New posting class"
      And the user selects the row with text "New posting class" in the "name" bound column header of the table field
      And the user clicks the "name" bound nested field of the selected row in the table field
      Then the "Posting class Item-New posting class" titled page is displayed
      # item type Business Rule verification
      And the user selects the "Item type" labelled multi dropdown field on the main page
      Then the value of the multi dropdown field is "Stock item, Non-stock item, Service item, Landed cost item"
      #unselecting non-stock type --> update must be possible with no confirmation message
      And the user selects the "Item type" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      And the user selects "Non-stock item" in the multi dropdown field
      And the user clicks the "Confirm update" button of the Confirm dialog
      When the user selects the "Name" labelled text field on the main page
      And the value of the text field is "New posting class"
      And the user selects the "Item type" labelled multi dropdown field on the main page
      Then the value of the multi dropdown field is "Stock item, Service item, Landed cost item"
      #unselecting Service type --> confirmation message expected
      And the user selects the "Item type" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      And the user selects "Service item" in the multi dropdown field
      And the user waits for 2 seconds
      #And the user clicks the "Confirm update" button of the Message dialog on the main page
      And the user clicks the "Confirm update" button of the Confirm dialog
      #unselecting Service type --> confirmation message expected
      And the user selects the "Item type" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      And the user selects "Stock item" in the multi dropdown field
      And the user waits for 2 seconds
      #And the user clicks the "Confirm update" button of the Message dialog on the main page
      And the user clicks the "Confirm update" button of the Confirm dialog
      # --> all lines except landed cost lines remain
      And the user selects the "Item type" labelled multi dropdown field on the main page
      And the user clicks in the multi dropdown field
      And the user selects "Landed cost item" in the multi dropdown field
      #And the user clicks the "Cancel" button of the Message dialog on the main page
      #And the user clicks the "Cancel" button of the Confirm dialog
      #Save
      And the user clicks the "Save" labelled business action button on the main page
      Then a toast containing text "Record updated" is displayed


   Scenario: 06 - Delete the line on the Posting class
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
      Then the "Posting classes" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "New posting class"
      And the user selects the row with text "New posting class" in the "name" bound column header of the table field
      And the user clicks the "name" bound nested field of the selected row in the table field
      Then the "Posting class Item-New posting class" titled page is displayed
      #Delete line 1
      And the user selects the "lines" bound nested grid field on the main page
      And the user selects row with text "FR chart of accounts" in column with header "Chart of accounts" in the nested grid field
      And the user clicks the "Delete" action of the selected row in the nested grid field
      #Save
      And the user clicks the "Save" labelled business action button on the main page
      Then a toast containing text "Record updated" is displayed

   Scenario: 07 - Delete posting class
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
      Then the "Posting classes" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "New posting class"
      And the user selects the row with text "New posting class" in the "name" bound column header of the table field
      And the user clicks the "name" bound nested field of the selected row in the table field
      Then the "Posting class Item-New posting class" titled page is displayed
      #Delete
      When the user clicks the "Delete" labelled more actions button in the header
      And the user clicks the "Delete" button of the Confirm dialog
      Then a toast containing text "Record deleted" is displayed
