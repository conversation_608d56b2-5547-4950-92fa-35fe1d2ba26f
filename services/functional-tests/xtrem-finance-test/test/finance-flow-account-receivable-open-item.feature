# Prerequisites: prerequisites-flow-verify-options-status

@finance
Feature:  finance-flow-account-receivable-open-item

    Scenario: 01 - Creates sales invoice from sales shipment and post it
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "SH240016"
        And the user selects the row with text "SH240016" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create invoice" labelled business action button on the main page
        And the user clicks the "Create" button of the Custom dialog
        Then a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        When the user clicks the "post" labelled business action button on the main page
        Then the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The sales invoice was posted." is displayed
        And the user dismisses all the toasts
        # Save the SI number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_INVNUM01]"

    Scenario: 02 - Create sales credit memo from sales invoice
        When the "Create credit memo" labelled business action button on the main page is visible
        And the user clicks the "Create credit memo" labelled business action button on the main page
        Then the dialog title is "Confirm creation"
        And the user clicks the "Create" button of the Confirm dialog
        And a toast with text "Record created" is displayed
        And the user dismisses all the toasts
        When the user clicks the "post" labelled business action button on the main page
        Then the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales credit memo was posted." is displayed
        # Save the SC number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCMUM01]"

    Scenario: 03 - Verify that the sales invoice record exist
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsReceivableOpenItem"
        Then the "Accounts receivable open items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Document number" labelled column in the table field with value "[ENV_INVNUM01]"
        And the user selects the row 1 of the table field
        And the user clicks the "Document number" labelled nested field of the selected row in the table field

        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Sandfeld"

        When the "Document number" labelled link field on the main page is displayed
        Then the user selects the "documentNumberLink" bound link field on the main page

        When the user selects the "Document type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Sales invoice"

        When the user selects the "Customer" labelled reference field on the main page
        Then the value of the reference field is "Schmitt Apparatebau GmbH"
        And the reference field tunnel link is displayed

        When the user selects the "Due date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T+30"

        When the user selects the "Transaction amount due" labelled numeric field on the main page
        Then the value of the numeric field is "2,975.00"

        When the user selects the "Company amount due" labelled numeric field on the main page
        Then the value of the numeric field is "2,975.00"

        When the user selects the "Discount payment before date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T+30"

        When the user selects the "Discount date" labelled numeric field on the main page
        Then the value of the numeric field is "30"

        When the user selects the "Discount type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Percentage"

        When the user selects the "Discount percent" labelled numeric field on the main page
        Then the value of the numeric field is "20.000"

    Scenario: 04 - Verify that the sales credit memo record exist
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsReceivableOpenItem"
        Then the "Accounts receivable open items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Document number" labelled column in the table field with value "[ENV_SCMUM01]"
        And the user selects the row 1 of the table field
        And the user clicks the "Document number" labelled nested field of the selected row in the table field

        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Sandfeld"

        ## Need the step definition to check if the link is filled/available
        Then the user selects the "documentNumberLink" bound link field on the main page

        When the user selects the "Document type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Sales credit memo"

        When the user selects the "Customer" labelled reference field on the main page
        Then the value of the reference field is "Schmitt Apparatebau GmbH"
        And the reference field tunnel link is displayed

        When the user selects the "Due date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T+30"

        When the user selects the "Transaction amount due" labelled numeric field on the main page
        Then the value of the numeric field is "-2,975.00"

        When the user selects the "Company amount due" labelled numeric field on the main page
        Then the value of the numeric field is "-2,975.00"

        When the user selects the "Discount payment before date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T+30"

        When the user selects the "Discount date" labelled numeric field on the main page
        Then the value of the numeric field is "30"

        When the user selects the "Discount type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Percentage"

        When the user selects the "Discount percent" labelled numeric field on the main page
        Then the value of the numeric field is "20.000"
