#This test can only be executed with sage.
# The test will read Journal entry type, post the Invoice, read <PERSON><PERSON> for the Posted invoice and read JE Inquiry
# Item used:
# Supplier used: MUHOMA Technology GmbH
# Site used: Sandfeld

Feature: finance-flow-contra-account

    Scenario: 01 - Read Journal entry type
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/JournalEntryType"
        Then the "Journal entry types" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "Germany" value in the option menu of the table field
        And the user filters the "Name" labelled column in the table field with value "Purchase receipt"
        And the user selects the row with text "Purchase receipt" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Journal entry type Purchase receipt" titled page is displayed
        When the user selects the "Line setup" labelled table field on the main page
        Then the user selects the row 1 of the table field
        And the value of the "Contra account type" labelled nested text field of the selected row in the table field is "Purchased item stock variation"
        Then the user selects the row 2 of the table field
        And the value of the "Contra account type" labelled nested text field of the selected row in the table field is "Stock"
        Then the user selects the row 3 of the table field
        And the value of the "Contra account type" labelled nested text field of the selected row in the table field is "Landed cost accrual"
        Then the user selects the row 4 of the table field
        And the value of the "Contra account type" labelled nested text field of the selected row in the table field is "Stock"
        Then the user selects the row 5 of the table field
        And the value of the "Contra account type" labelled nested text field of the selected row in the table field is "Non absorbed stock variance"
        Then the user selects the row 6 of the table field
        And the value of the "Contra account type" labelled nested text field of the selected row in the table field is "Non absorbed stock"

    Scenario: 02 - Post the Purchase invoice
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "PI240003" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI240003" titled page is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "No variance"
        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted." is displayed

    Scenario: 03 - Saving the JE number
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "PI240003" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        Then the user stores the value of the "Document number" labelled nested text field of the selected row in the table field with the key "[ENV_JENumber]"

    Scenario: 04 - Read the Journa Entry for the posted Purchase invoice
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "number" labelled column in the table field with value "[ENV_JENumber]"
        And the user selects the row with text "[ENV_JENumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled nested grid field on the main page

        And the user selects row with text "3300 -- Wareneingang 7 % Vorsteuer" in column with header "Account" in the nested grid field
        And the value of the "Contra account" labelled nested text field of the selected row in the nested grid field is "DE071 -- MUHOMA Technology GmbH"
        And the value of the "Business entity" labelled nested text field of the selected row in the nested grid field is ""

        And the user selects row with text "1600 -- Verbindlichkeiten" in column with header "Account" in the nested grid field
        And the value of the "Contra account" labelled nested text field of the selected row in the nested grid field is "3300 -- Wareneingang 7 % Vorsteuer"
        And the value of the "Business entity" labelled nested text field of the selected row in the nested grid field is "MUHOMA Technology GmbH"

        And the user selects row with text "1571 -- Vorsteuer 7 %" in column with header "Account" in the nested grid field
        And the value of the "Contra account" labelled nested text field of the selected row in the nested grid field is "DE071 -- MUHOMA Technology GmbH"
        Then the value of the "Business entity" labelled nested text field of the selected row in the nested grid field is ""

    Scenario: 05 - Read Journal Entry Inquiry
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/JournalEntryInquiry"
        Then the "Journal entry inquiry" titled page is displayed
        And the user selects the "companies" labelled multi reference field on the main page
        And the user writes "DE Automotive GmbH" in the multi reference field
        And the user selects "DE Automotive GmbH" in the multi reference field
        And the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/06/2024"
        And the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/06/2024"
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Lines" labelled table field on the main page

        And the user selects the row 1 of the table field
        And the value of the "Business entity name" labelled nested text field of the selected row in the table field is "MUHOMA Technology GmbH"
        Then the value of the "Contra account" labelled nested text field of the selected row in the table field is "3300 -- Wareneingang 7 % Vorsteuer"

        And the user selects the row 2 of the table field
        Then the value of the "Contra account" labelled nested text field of the selected row in the table field is "DE071 -- MUHOMA Technology GmbH"
        And the value of the "Business entity name" labelled nested text field of the selected row in the table field is ""
        And the user selects the row 3 of the table field
        And the value of the "Business entity name" labelled nested text field of the selected row in the table field is ""
        Then the value of the "Contra account" labelled nested text field of the selected row in the table field is "DE071 -- MUHOMA Technology GmbH"
