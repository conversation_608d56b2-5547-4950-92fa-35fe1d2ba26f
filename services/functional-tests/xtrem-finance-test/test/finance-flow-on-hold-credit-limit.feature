# This test file will be used to test all scenarios related to the on hold and credit limit feature
# Company: South Africa - ID: 700
# Customer: Adcock Ingram - ID: 17001
# Tenant: WD3


@finance
Feature: finance-flow-on-hold-credit-limit

        # TODO: Uncomment all Intacct scenarios once tenant is provisioned correctly and also uncomment the scenarios below

        # Scenario: Verify that the Customer credit limit and On hold is read-only
        #         Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        #         Then the "Customers" titled page is displayed
        #         # Find the customer and verify readonly
        #         When the user selects the "Customers" labelled table field on the main page
        #         And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        #         And the user clicks the "Name" labelled nested field of the selected row in the table field

        #         Then the "Customer ATSERMO" titled page is displayed
        #         When selects the "Financial" labelled navigation anchor on the main page

        #         # Verify Credit limit read-only
        #         And the user selects the "Credit limit" labelled numeric field on the main page
        #         Then the numeric field is disabled
        #         # Verify on hold read-only
        #         And the user selects the "On hold" labelled checkbox field on the main page
        #         Then the checkbox field is disabled

        Scenario: 01 - Verify that the Customer credit limit and on hold is not readonly and able to be edited
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
                Then the "Customers" titled page is displayed
                # Find the customer and verify readonly
                When the user selects the "Customers" labelled table field on the main page
                And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
                And the user clicks the "Name" labelled nested field of the selected row in the table field

                Then the "Customer ATSERMO" titled page is displayed
                When selects the "Financial" labelled navigation anchor on the main page

                # Verify Credit limit read-only
                And the user selects the "Credit limit" labelled numeric field on the main page
                And the user writes "500" in the numeric field
                And the user stores the value of the numeric field with the key "[ENV_Cus_Credit_Limit]"
                And the user presses Tab
                # Verify on hold read-only
                When the user clicks the "Save" labelled business action button on the main page
                Then a toast with text "Record updated" is displayed
                And the user waits for 2 seconds

                And the user clicks the "Put on hold" labelled more actions button in the header

                When the user selects the "On hold" labelled checkbox field on the main page
                Then the value of the checkbox field is "true"


                When the user selects the "Credit limit" labelled numeric field on the main page
                Then the value of the numeric field is "[ENV_Cus_Credit_Limit].000"


        Scenario: 02 - Verify that the Company credit limit and on hold is not readonly and able to be edited
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Company"
                Then the "Companies" titled page is displayed

                When the user selects the "Companies" labelled table field on the main page
                And the user selects the row with text "South Africa" in the "Name" labelled column header of the table field
                And the user clicks the "Name" labelled nested field of the selected row in the table field

                Then the "Company South Africa" titled page is displayed
                When selects the "Management" labelled navigation anchor on the main page

                # Verify Customer on hold check is editable
                And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user writes "None" in the dropdown-list field
                And the user selects "None" in the dropdown-list field
                And the user stores the value of the dropdown-list field with the key "[ENV_Cus_OH_Check_Value]"

                # Save
                When the user clicks the "Save" labelled business action button on the main page
                Then a toast with text "Record updated" is displayed

                # Verify update
                When the user selects the "Customer on hold check" labelled dropdown-list field on the main page
                Then the value of the dropdown-list field is "[ENV_Cus_OH_Check_Value]"
