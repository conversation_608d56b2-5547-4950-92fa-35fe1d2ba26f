
#This test can only be executed with sage.
# The test will create a purchase order, create a purchase receipt from the Purchase order,
# The test will also verify that the tax is determined correctly
# Item used: DE-DE purchase item
# Item Item tax group: Goods, reduced VAT, so the on tax details the tax must be "Purchase Reduced Rate"
# On Acc receivable invoice, The account usedd must be "3300 -- Wareneingang 7 % Vorsteuer"
# Supplier used: MUHOMA Technology GmbH
# Site used: Sandfeld
@finance
Feature:finance-flow-de-purchase-domestic-customer-tax-determination
    Scenario: 01 - create a Purchase order and verify tax used
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Fill in site reference field
        And the user selects the "purchasingSite" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        # Fill in Supplier reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        # Verify defaulted order date
        And the user selects the "Order date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        # Add a line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DE-DE purchase item" in the reference field
        And the user selects "DE-DE purchase item" in the reference field
        # Fill in Qty on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        # Fill in Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast with text "Record created" is displayed

        ## submit the purchase order
        When the user clicks the "Submit for approval" labelled business action button on the main page
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        Then a toast containing text "Email sent" is displayed
        Then the user dismisses all the toasts
        When the user clicks the "Approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        Then a toast containing text "Approval status updated" is displayed

        ## Verify the tax details
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE purchase item" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        And the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        Then the value of the "Tax" labelled nested text field of the selected row in the table field is "Purchase Reduced Rate"
        Then the value of the "Tax rate" labelled nested text field of the selected row in the table field is "7.00 %"
        And the user clicks the "OK" labelled business action button on the main page
        #Save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        Then the user dismisses all the toasts

    Scenario: 02 - create a purchase receipt from a purchase order and verify tax

        ## Create a purchase receipt
        And the user clicks the "Create receipt" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Receipts created: " is displayed

        ## Storing the value of the PR
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "purchaseReceiptLines" bound table field on the sidebar
        And the user selects the row with text "10 each" in the "Quantity in purchase unit" labelled column header of the table field
        Then the user stores the value of the "Receipt number" labelled nested text field of the selected row in the table field with the key "[ENV_PRNumber]"

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PRNumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE purchase item" in the "Item" labelled column header of the table field

        ##Stock allocation
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row with text "10 each" in the "quantityInStockUnit" bound column header of the table field
        And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on the main page

        ##save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        ## Verify the tax details
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE purchase item" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        And the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        Then the value of the "Tax" labelled nested text field of the selected row in the table field is "Purchase Reduced Rate"
        Then the value of the "Tax rate" labelled nested text field of the selected row in the table field is "7.00 %"
        And the user clicks the "OK" labelled business action button on the main page

        #Save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        ## Post the PR
        When the user clicks the "Post stock" labelled business action button on the main page
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Received"



    Scenario: 03 - create purchase invoice from Purchase receipt and verify tax used

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        #create a purchase invoice
        When the user clicks the "Create" labelled business action button on the navigation panel

        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        # Add lines from receipt
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from receipts" labelled add action of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "[ENV_PRNumber]" in the "Receipt number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        ##save
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Save the PI number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PINUM01]"
        # Verify the tax details
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "DE-DE purchase item" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        And the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        Then the value of the "Tax" labelled nested text field of the selected row in the table field is "Purchase Reduced Rate"
        Then the value of the "Tax rate" labelled nested text field of the selected row in the table field is "7.00 %"
        And the user clicks the "OK" labelled business action button on the main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # solving variences
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "100.00" in the numeric field
        And the user selects the "Total supplier tax" labelled numeric field on the main page
        And the user writes "7.00" in the numeric field
        #save
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        Then the user dismisses all the toasts
        #post the invoice
        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted." is displayed


    Scenario: 04 - Saving the JE number and AP Invoice number
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        When the user filters the "number" bound column in the table field with value "[ENV_PINUM01]"
        And the user selects the row with text "[ENV_PINUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        Then the user stores the value of the "Document number" labelled nested text field of the selected row in the table field with the key "[ENV_JENumber]"
        And the user selects the row with text "Accounts payable invoice" in the "Document type" labelled column header of the table field
        Then the user stores the value of the "Document number" labelled nested text field of the selected row in the table field with the key "[ENV_APNumber]"


    Scenario: 05 - Verify the Accounts Receivable invoice Account
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/AccountsPayableInvoice"
        Then the "Accounts payable invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_APNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user selects row with text "Goods" in column with header "Line type" in the nested grid field
        And the value of the "Account" labelled nested text field of the selected row in the nested grid field is "3300 -- Wareneingang 7 % Vorsteuer"

    Scenario: 06 - Verify the Journal Entry Accounts and Tax used
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_JENumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled nested grid field on the main page

        And the user selects row with text "3300 -- Wareneingang 7 % Vorsteuer" in column with header "Contra account" in the nested grid field
        And the value of the "Account" labelled nested text field of the selected row in the nested grid field is "1600 -- Verbindlichkeiten"
        And the value of the "Tax" labelled nested text field of the selected row in the nested grid field is ""

        And the user selects row with text "€ 100.00" in column with header "Transaction debit" in the nested grid field
        And the value of the "Account" labelled nested text field of the selected row in the nested grid field is "3300 -- Wareneingang 7 % Vorsteuer"
        And the value of the "Tax" labelled nested text field of the selected row in the nested grid field is "Purchase Reduced Rate"

        And the user selects row with text "€ 7.00" in column with header "Transaction debit" in the nested grid field
        And the value of the "Account" labelled nested text field of the selected row in the nested grid field is "1571 -- Vorsteuer 7 %"
        And the value of the "Tax" labelled nested text field of the selected row in the nested grid field is "Purchase Reduced Rate"
