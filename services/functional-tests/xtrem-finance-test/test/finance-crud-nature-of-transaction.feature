# Create update and delete a nature of transaction

@finance
Feature: finance-crud-nature-of-transaction

    Scenario: Open the Nature of transaction Page and create a record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/NatureOfTransaction"
        Then the "Natures of transaction" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "ON"
        # Add Name value
        When the user selects the "Name *" labelled text field on the main page
        And the user writes "TRANSTEST" in the text field
        Then the value of the text field is "TRANSTEST"
        # Add ID value
        When the user selects the "ID *" labelled text field on the main page
        And the user writes "77" in the text field
        Then the value of the text field is "77"
        # Save the creation
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Open the Nature of transaction Page and upadte a record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/NatureOfTransaction"
        Then the "Natures of transaction" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "ID" labelled nested field of the selected row in the table field
        # When the user opens the navigation panel
        When the user searches for "TRANSTEST" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Add Name value
        When the user selects the "Name *" labelled text field on the main page
        And the user writes "TRANSTEST1" in the text field
        Then the value of the text field is "TRANSTEST1"
        # Confirm Update
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Open the Nature of transaction Page and deleted a record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/NatureOfTransaction"
        Then the "Natures of transaction" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "ID" labelled nested field of the selected row in the table field
        # When the user opens the navigation panel
        When the user searches for "TRANSTEST1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Confirm Deletion
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
