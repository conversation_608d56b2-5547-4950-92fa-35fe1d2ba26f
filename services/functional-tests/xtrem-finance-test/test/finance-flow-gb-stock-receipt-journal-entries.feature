#This test can only be executed with sage.
#The goal of this test is to verify that the user can issue Journal entry with GB Intacct active
#Item used: Journal entry item 0002
@finance
@inventory
Feature: finance-flow-gb-stock-receipt-journal-entries
    Scenario: 01 - Posting stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "Stock receipts" labelled table field on the main page
        #############
        And the user filters the "Number" labelled column in the table field with value "SR230019"
        And the user selects the row with text "SR230019" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page
    #Then a toast containing text "Record created" is displayed

    Scenario: 02 - Verifying the status of the stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "Stock receipts" labelled table field on the main page
        #############
        And the user filters the "Number" labelled column in the table field with value "SR230019"
        And the user selects the row with text "SR230019" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        #Verify status

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal entry item 0002" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Received"

        When the user selects the "Display status" labelled label field on the main page
        Then the value of the label field is "Received"

    Scenario: 03 - Generate journal entry for the created stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        ##set values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        Then the user selects "UK LIMITED" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Miscellaneous stock receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Miscellaneous stock receipt"
        # Click Generate and check message
        When the user clicks in the "create" bound button field on the main page
        # Generate
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 04 - Notification page for stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        #When the user clicks on the "finance" bound header action button on the main page
        #And the user clicks on the "finance" labelled tab in the detail panel
        And selects the "Finance" labelled navigation anchor on the main page
        ##Selecting criteria
        And the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        #And the user writes "Miscellaneous stock receipt" in the multi dropdown field
        And the user selects "Miscellaneous stock receipt" in the multi dropdown field
        And the value of the multi dropdown field is "Miscellaneous stock receipt"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select status "Posted" only
        # And the user writes "Posted" in the multi dropdown field
        And the user selects "Posted" in the multi dropdown field
        ##Check correct value was selected
        And the value of the multi dropdown field is "Posted"

        # And the user writes "Recorded" in the multi dropdown field
        And the user selects "Recorded" in the multi dropdown field

        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SR230019" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_IJNumber]"



    Scenario: 05 - Verify that the journal entry is correct for stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed

        # Creating the journal entry inquiry
        And the user selects the "Journal entries" labelled table field on the main page
        And the user filters the "Description" labelled column in the table field with value "Miscellaneous stock receipt"
        And the user filters the "Number" labelled column in the table field with value "[ENV_IJNumber]"
        And the user selects the row with text "[ENV_IJNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        #Verify status and the reference field
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Stock receipt"
        #Checking that the information is correct on the grid
        When the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "13100 -- Inventory" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "500 -- UK LIMITED" in column with header "Financial site" in the nested grid field
        And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "£ 80.00"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Journal entry 0002 -- Journal entry item 0002"
    #Then the value of the "Stock site" labelled nested text field of the selected row in the nested grid field is "501 -- Swindon"

    Scenario: 06 - Posting stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed

        When the user selects the "Stock issues" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "SS230004"
        And the user selects the row with text "SS230004" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        #Assigning stock
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Journal entry item 0002" in the "item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "20 each" in the "Available quantity" labelled column header of the table field
        And the user writes "5" in the "Quantity to issue" labelled nested numeric field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "saveStockIssue" bound business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 07 - Verifying the status of the stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel

        When the user selects the "Stock issues" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "SS230004"
        And the user selects the row with text "SS230004" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verifying the display status of the posted sales issue (Issued)
        And the user selects the "Display status" labelled label field on the main page
        And the value of the label field is "Issued"

    Scenario: 08 - Generate journal entry for the created stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        # set the values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        Then the user selects "UK LIMITED" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Miscellaneous stock issue" in the multi dropdown field
        Then the value of the multi dropdown field is "Miscellaneous stock issue"
        # Clicks Generate and check message
        When the user clicks in the "create" bound button field on the main page
        # Generating a journal entry for the stock issue
        Then a toast containing text "Journals created: 1" is displayed


    Scenario: 09 - Notification page for stock Issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        #When the user clicks on the "finance" bound header action button on the main page
        #And the user clicks on the "finance" labelled tab in the detail panel
        And selects the "Finance" labelled navigation anchor on the main page
        ##Selecting criteria
        And the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        # And the user writes "Miscellaneous stock issue" in the multi dropdown field
        And the user selects "Miscellaneous stock issue" in the multi dropdown field
        And the value of the multi dropdown field is "Miscellaneous stock issue"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select status "Posted" only
        # And the user writes "Posted" in the multi dropdown field
        And the user selects "Posted" in the multi dropdown field
        ##Check correct value was selected
        And the value of the multi dropdown field is "Posted"
        #And the value of the multi dropdown field is "Posted"

        # And the user writes "Recorded" in the multi dropdown field
        And the user selects "Recorded" in the multi dropdown field

        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SS230004" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_IJNumber2]"

    Scenario: 10 - Verify that the journal entry for stock issue is correct
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        # Creating the journal entry inquiry
        And the user selects the "Journal entries" labelled table field on the main page
        And the user filters the "Description" labelled column in the table field with value "Miscellaneous stock issue"
        And the user filters the "Number" labelled column in the table field with value "[ENV_IJNumber2]"
        And the user selects the row with text "[ENV_IJNumber2]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field


        #Verify status and the reference field
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Stock issue"
        #Checking that the infomation is correct on the grid
        When the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "13100 -- Inventory" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "500 -- UK LIMITED" in column with header "Financial site" in the nested grid field
        And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "£ 20.00"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Journal entry 0002 -- Journal entry item 0002"
#And the value of the "Business site" labelled nested text field of the selected row in the nested grid field is "500 -- UK LIMITED"
#Then the value of the "Stock site" labelled nested text field of the selected row in the nested grid field is "501 -- Swindon"


# #### The following scenario works to check the link on the journal entry, but problem is the another tab will be opened which will cause the bot not to be able to execute
# #### other scenario that follows that is why it is at the end
# Scenario: Verify that the user can click on the journal entry link licked with stock receipt
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
#     Then the "Journal entries" titled page is displayed
#     #Creating the journal entry inquiry
#     And the user selects the "$navigationPanel" bound table field on the main page
#     ##########
#     And the user selects the row with text "[IJNumber]" in the "Number" labelled column header of the table field
#     And the user clicks the "Number" labelled nested field of the selected row in the table field
#     And the user searches for "[ENV_IJNumber]" in the navigation panel
#     And the user clicks on the "first" navigation panel's row
#     #Verify the link
#     And the user selects the "accountingIntegrationReference" labelled link field on the main page
#     And the user clicks in the link field
