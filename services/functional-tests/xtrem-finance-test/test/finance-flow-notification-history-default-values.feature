#The goal of this test is to verify all the default values of the notification history page
#Unable to competely verify the Avalara tab, due issues with the 'Topics' multi dropdown field...
#...An error pops up: "Cannot read properties of null (reading 'lenghth')"
#Script to be updated once issues are resolved

@finance
Feature: finance-flow-notification-history-default-values

    Scenario: 01 - Verify default values of the System tab
        #Verifying the default status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "System" labelled navigation anchor on the main page
        And the "System" labelled navigation anchor is selected
        And the user selects the "Status" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is "Error"
        And the user clears the multi dropdown field
        And the user clicks in the multi dropdown field
        And the user selects "Received | Sent | Success" in the multi dropdown field
        Then the value of the multi dropdown field is "Received, Sent, Success"

        When the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "System" labelled navigation anchor on the main page
        And the "System" labelled navigation anchor is selected
        And the user selects the "Status" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is "Error"
        #Verifying Search button
        And the "searchHistoryButton" bound button field on the main page is displayed


    Scenario: 02 - Verifying default values of the Stock tab
        #Verifying the default status
        And selects the "Stock" labelled navigation anchor on the main page
        And the "Stock" labelled navigation anchor is selected
        And the user selects the "Status" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is "In progress, Error"
        And the user clears the multi dropdown field
        And the user clicks in the multi dropdown field
        And the user selects "Completed" in the multi dropdown field
        Then the value of the multi dropdown field is "Completed"

        When the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed

        And selects the "Stock" labelled navigation anchor on the main page
        And the "Stock" labelled navigation anchor is selected
        And the user selects the "Status" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is "In progress, Error"
        #Verifying Search button
        And the "searchStock" bound button field on the main page is displayed


    Scenario: 03 - Verifying default values of the Finance tab
        #Verifying the default status
        And selects the "Finance" labelled navigation anchor on the main page
        And the "Finance" labelled navigation anchor is selected
        And the user selects the "Document type" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is ""
        ##select several document types
        And the user clicks in the multi dropdown field
        And the user selects "AP invoice | AR invoice | Purchase invoice | Sales invoice" in the multi dropdown field
        ##Check correct values were selected
        Then the value of the multi dropdown field is "AP invoice, AR invoice, Purchase invoice, Sales invoice"
        And the user selects the "Status" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is "To be recorded, Recording, Error, Not recorded, Failed"
        And the user clears the multi dropdown field
        And the user clicks in the multi dropdown field
        And the user selects "Pending | Posted | Submitted" in the multi dropdown field
        Then the value of the multi dropdown field is "Pending, Posted, Submitted"

        When the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed

        And selects the "Finance" labelled navigation anchor on the main page
        And the "Finance" labelled navigation anchor is selected
        And the user selects the "Document type" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is ""
        And the user selects the "Status" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is "To be recorded, Recording, Error, Not recorded, Failed"
        #Verifying Search button
        And the "searchFinance" bound button field on the main page is displayed


    Scenario: 04 - Verifying default values of the Avalara tab
        #Verifying the default status
        And selects the "Avalara" labelled navigation anchor on the main page
        And the "Avalara" labelled navigation anchor is selected
        And the user selects the "Document type" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is ""
        And the user clicks in the multi dropdown field
        And the user selects "Sales credit memo | Sales invoice" in the multi dropdown field
        Then the value of the multi dropdown field is "Sales credit memo, Sales invoice"
        And the user selects the "Latest notifications" labelled checkbox field on the main page
        And the value of the checkbox field is "true"
        And the user unticks the checkbox field
        And the value of the checkbox field is "false"
        And the user selects the "Status" labelled multi dropdown field on the main page
        And the value of the multi dropdown field is "Sent, Success, Error"
        And the user clears the multi dropdown field
        And the user clicks in the multi dropdown field
        And the user selects "Interrupted | Stop requested | Stopped" in the multi dropdown field
        Then the value of the multi dropdown field is "Interrupted, Stop requested, Stopped"
        # And the user selects the "Topics" labelled multi dropdown field on the main page
        # And the value of the multi dropdown field is "List entity use codes, List tax codes, Query companies, Create transaction"
        # And an error dialog appears on the main page
        # And the user selects the "Close" icon in the header on the main page
        # And the user clicks in the multi dropdown field
        # And at least the following list of options is displayed for the multi dropdown field: "List entity use codes | List tax codes"
        # And the user clears the multi dropdown field
        # And the user selects "Query companies" in the multi dropdown field
        # And at least the following list of options is displayed for the multi dropdown field: "Query companies"

        When the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed

        And selects the "Avalara" labelled navigation anchor on the main page
        And the "Avalara" labelled navigation anchor is selected
        # And the user selects the "Document type" labelled multi dropdown field on the main page
        # And the value of the multi dropdown field is ""
        # And the user selects the "Latest notifications" labelled checkbox field on the main page
        # And the value of the checkbox field is "true"
        # And the user selects the "Status" labelled multi dropdown field on the main page
        # And the value of the multi dropdown field is "Sent, Success, Error"
        # And the user selects the "Topics" labelled multi dropdown field on the main page
        # And at least the following list of options is displayed for the multi dropdown field: "List entity use codes | List tax codes"
        #Verifying Search button
        And the "avalaraSearchHistoryButton" bound button field on the main page is displayed
        And the "avalaraRevertCriteria" bound button field on the main page is displayed

## Scenario 5 removed as notification page has been refactored and Integration status moved to a new page @sage/xtrem-synchronization/Integration
# Scenario: 05 - Verifying default values of the Integration status tab
#     And selects the "Integration status" labelled navigation anchor on the main page
#     And the "Integration status" labelled navigation anchor is selected
#     And the user selects the "Status" labelled multi dropdown field on the main page
#     And the value of the multi dropdown field is "Desynchronized, Error, Not integrated"
#     And the user clears the multi dropdown field
#     And the user clicks in the multi dropdown field
#     And the user selects "Pending" in the multi dropdown field
#     And the user selects "Success" in the multi dropdown field
#     Then the value of the multi dropdown field is "Pending, Success"
#     And the user selects the "Nodes" labelled multi dropdown field on the main page
#     And the value of the multi dropdown field is ""
#     And the user clicks in the multi dropdown field
#     And the user selects "business_entity_address" in the multi dropdown field
#     And the user selects "customer" in the multi dropdown field
#     And the user selects "item" in the multi dropdown field
#     And the user selects "supplier" in the multi dropdown field
#     Then the value of the multi dropdown field is "business_entity_address, customer, item, supplier"
#     And the user selects the "Number of lines per node" labelled numeric field on the main page
#     And the value of the numeric field is "200"
#     And the user writes "10" in the numeric field
#     And the value of the numeric field is "10"

# When the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
# Then the "Notification history" titled page is displayed

# And selects the "Integration status" labelled navigation anchor on the main page
# And the "Integration status" labelled navigation anchor is selected
# And the user selects the "Status" labelled multi dropdown field on the main page
# And the value of the multi dropdown field is "Desynchronized, Error, Not integrated"
# And the user selects the "Nodes" labelled multi dropdown field on the main page
# And the value of the multi dropdown field is ""
# And the user selects the "Number of lines per node" labelled numeric field on the main page
# And the value of the numeric field is "200"
# #Verifying Search button
# And the "searchIntegrationState" bound button field on the main page is displayed
