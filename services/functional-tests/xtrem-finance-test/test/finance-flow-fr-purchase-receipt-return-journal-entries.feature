#This test can only be executed with sage.
#The goal of this test is to verify that the user can issue Journal entry
@finance
@distribution
Feature: finance-flow-fr-purchase-receipt-return-journal-entries

    Scenario: 01 - Verify that the user can post the purchase receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PR230019"
        And the user selects the row with text "PR230019" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 02 - Verifying the status of the Purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PR230019"
        And the user selects the row with text "PR230019" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        #Verify status
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Received"

    Scenario: 03 - Generate journal entry for the created Purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        ##set values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Siège social S01  PARIS" in the reference field
        And the user selects "Siège social S01 PARIS" in the reference field
        #And the user opens the lookup dialog in the "Financial site" labelled reference in the reference fiel
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/19/2023"
        Then the value of the date field is a generated date with value "09/19/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/19/2023"
        Then the value of the date field is a generated date with value "09/19/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Purchase receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Purchase receipt"
        # Click Generate
        And the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: " is displayed

    Scenario: 04 - Notification page for the posted purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page
        ##Selecting criteria
        When the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Purchase receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Purchase receipt"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select status "Recorded" only
        And the user clicks in the multi dropdown field
        And the user selects "Recorded" in the multi dropdown field
        ##Check correct value was selected
        And the value of the multi dropdown field is "Recorded"
        #And the value of the multi dropdown field is "Posted"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "PR230019" in the "Document number" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_UDNumber]"

    Scenario: 05 - Verify that the journal entry is correct for posted Purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        #Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        #########Using store
        When the user filters the "number" bound column in the table field with value "[ENV_UDNumber]"
        And the user selects the row with text "[ENV_UDNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_UDNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify status and the reference field
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Purchase receipt"
        #Checking that the infomation is correct on the grid
        When the user selects the "lines" bound nested grid field on the main page
        #Stock managed row
        And the user selects row with text "******** -- Stocks de matières premières" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "€ 16.00"
        And the value of the "Supplier" labelled nested text field of the selected row in the nested grid field is "BARRES -- BARRES"
        Then the value of the "Item" labelled nested text field of the selected row in the nested grid field is "stockFR -- FRItem01"
        #Non stock row
        And the user refreshes the screen
        Then the user opens the navigation panel
        And the user searches for "[ENV_UDNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify status and the reference field
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Purchase receipt"
        #Checking that the infomation is correct on the grid
        When the user selects the "lines" bound nested grid field on the main page
        When the user selects row with text "******** -- Variations des stocks de matières premières" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "€ 16.00"
        And the value of the "Supplier" labelled nested numeric field of the selected row in the nested grid field is "BARRES -- BARRES"
        Then the value of the "Item" labelled nested text field of the selected row in the nested grid field is "stockFR -- FRItem01"


    Scenario: 06 - Verify that the user can submit the purchase return
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PT230007"
        And the user selects the row with text "PT230007" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And the user clicks the "post stock" labelled business action button on the main page
        And the user clicks the "Continue" button of the Confirm dialog
        And the user waits for 5 seconds
        And the user refreshes the screen
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Returned"

    Scenario: 07 - Generate journal entry for the created Purchase return
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        ##set values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Siège social S01  PARIS" in the reference field
        Then the user selects "Siège social S01 PARIS" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/19/2023"
        Then the value of the date field is a generated date with value "09/19/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/19/2023"
        Then the value of the date field is a generated date with value "09/19/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Purchase return" in the multi dropdown field
        Then the value of the multi dropdown field is "Purchase return"
        # Click Generate and check message
        And the user clicks in the "create" bound button field on the main page
        #####Generate
        Then a toast containing text "Journals created: " is displayed

    Scenario: 08 - Notification page for stock return
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page
        ##Selecting criteria
        When the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Purchase return" in the multi dropdown field
        Then the value of the multi dropdown field is "Purchase return"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select status "Recorded" only
        And the user clicks in the multi dropdown field
        And the user selects "Recorded" in the multi dropdown field
        ##Check correct value was selected
        Then the value of the multi dropdown field is "Recorded"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "PT230007" in the "Document number" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_UNNumber2]"

    Scenario: 09 - Verify that the journal entry is correct for Purchase return
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        #Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_UNNumber2]"
        And the user selects the row with text "[ENV_UNNumber2]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_UNNumber2]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify status and the reference field
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Purchase return"
        #Checking that the infomation is correct on the grid
        When the user selects the "lines" bound nested grid field on the main page
        ##Stock managed row
        And the user selects row with text "******** -- Stocks de matières premières" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "€ 16.00"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "stockFR -- FRItem01"
        #Non stock row
        When the user selects row with text "******** -- Variations des stocks de matières premières" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "€ 16.00"
        And the value of the "Supplier" labelled nested reference field of the selected row in the nested grid field is "BARRES -- BARRES"
        Then the value of the "Item" labelled nested text field of the selected row in the nested grid field is "stockFR -- FRItem01"
