# the purpose of this test it to verify the journal entries created for gb sales shipment and sales return receipt (stock managed item - Apple juice)

@finance
@distribution
Feature: finance-flow-gb-sales-shipment-return-journal-entries

    Scenario: 01 - Posting sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SH230012" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales shipment was posted" is displayed

    Scenario: 02 - Generate journal entry for the created sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        # setting the values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        Then the user selects "UK LIMITED" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/15/2023"
        Then the value of the date field is a generated date with value "09/15/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/15/2023"
        Then the value of the date field is a generated date with value "09/15/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Sales shipment" in the multi dropdown field
        Then the value of the multi dropdown field is "Sales shipment"
        # Clicks Generate and check message
        When the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 03 - Notification page to store the generated journal entry ID for the sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page
        #Selecting criteria
        And the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Sales shipment" in the multi dropdown field
        And the value of the multi dropdown field is "Sales shipment"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select both statuses "Posted" & "Recorded"
        And the user clicks in the multi dropdown field
        #And the user writes "Posted" in the multi dropdown field
        And the user selects "Posted | Recorded" in the multi dropdown field
        ##Check correct values were selected
        And the value of the multi dropdown field is "Posted, Recorded"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SH230012" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_IJNumber]"

    Scenario: 04 - Verify that the values of lines in result grid correspond to values of the journal entry for sales shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        # Creating the journal entry inquiry
        And the user selects the "Journal entries" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_IJNumber]"
        And the user selects the row with text "[ENV_IJNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Verifying the correctness of text fields
        And the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "UK LIMITED"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Sales shipment"
        And the user selects the "Origin" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Sales"
        # Verify posting status field and accounting integration reference
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Accounting integration reference" labelled link field on the main page
        ## Need the step definition to check if the link is filled/available on the Accounting integration field ##
        # Verify the fields on the grid
        When the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "50100 -- COGS - Sales" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "500 -- UK LIMITED" in column with header "Financial site" in the nested grid field
        And the value of the "transactionAmount" labelled nested text field of the selected row in the nested grid field is "£ 750.00"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Apple Juice-00111 -- Apple Juice"
    # And the value of the "Business site" labelled nested text field of the selected row in the nested grid field is "501 -- Swindon"

    Scenario: 05 - Posting sales return receipts
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SRR230005" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 06 - Generate journal entry for the created sales return receipts
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        # setting the values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        Then the user selects "UK LIMITED" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/15/2023"
        Then the value of the date field is a generated date with value "09/15/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/15/2023"
        Then the value of the date field is a generated date with value "09/15/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Sales return receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Sales return receipt"
        # Clicks Generate and check message
        When the user clicks in the "create" bound button field on the main page

        # Generating a journal entry for the stock receipt
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 07 - Notification page to store the generated journal entry ID for the sales return receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page
        # Selecting criteria and storing target document number
        And the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user selects "Sales return receipt" in the multi dropdown field
        And the value of the multi dropdown field is "Sales return receipt"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select both statuses "Posted" & "Recorded"
        And the user selects "Posted | Recorded" in the multi dropdown field
        ##Check correct values were selected
        And the value of the multi dropdown field is "Posted, Recorded"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SRR230005" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_IJNumber]"

    Scenario: 08 - Verify that the values of lines in result grid correspond to values of the journal entry for sales return receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        # Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_IJNumber]"
        And the user selects the row with text "[ENV_IJNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_IJNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Verifying the correctness of text fields
        And the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "UK LIMITED"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Sales return receipt"
        And the user selects the "Origin" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Stock"
        # Verify posting status field and accounting integration reference
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Accounting integration reference" labelled link field on the main page
        ## Need the step definition to check if the link is filled/available on the Accounting integration field for the stock issue journal ##
        # Verify the fields on the grid
        When the user selects the "lines" bound nested grid field on the main page
        #And the user selects row with text "UK LIMITED" in column with header "Financial site" in the nested grid field
        And the user selects row with text "50100 -- COGS - Sales" in column with header "Account" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "500 -- UK LIMITED" in column with header "Financial site" in the nested grid field
        And the value of the "transactionAmount" labelled nested text field of the selected row in the nested grid field is "£ 650.00"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Apple Juice-00111 -- Apple Juice"
        And the value of the "Business site" labelled nested text field of the selected row in the nested grid field is "501 -- Swindon"
