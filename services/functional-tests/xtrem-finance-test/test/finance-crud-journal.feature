# The purpose of this test is to verify that user is able to create, update and delete a journal

@finance
Feature: finance-crud-journal

    Scenario: Verify the user is able to create a journal

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Journal"
        Then the "Journals" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "ID *" labelled text field on the main page
        Then the user writes "AJ" in the text field
        And the user selects the "Name *" labelled text field on the main page
        Then the user writes "New automatic journal" in the text field
        And the user selects the "Legislation *" labelled reference field on the main page
        Then the user writes "South Africa" in the reference field
        And the user selects "South Africa" in the reference field
        And the value of the reference field is "South Africa"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed


    Scenario: Verify the user is able to update a journal

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Journal"
        Then the "Journals" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "<PERSON>" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "New automatic journal" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name *" labelled text field on the main page
        And the user writes "Update automatic journal" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Verify the user is able to delete a journal

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Journal"
        Then the "Journals" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "AJ" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Update automatic journal" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
