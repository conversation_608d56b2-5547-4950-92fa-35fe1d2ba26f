
# The goal of this is to ensure that incorrect tax types cannot be selected at the transaction level
@finance
Feature:  finance-tax-not-editable

    Scenario: 01 - Prerequisites - Tax creation AUTO 01 PURCHASING NE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Tax category" labelled reference field on the main page
        And the user writes "Value Added Tax" in the reference field
        And the user selects "Value Added Tax" in the reference field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "AUTO 01 PURCHASING NE" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        And the user selects the "Primary external reference" labelled text field on the main page
        Then the user writes "AUTO 01 PURCHASING NE" in the text field
        When the user selects the "Tax type" labelled dropdown-list field on the main page
        And the user selects "Purchasing" in the dropdown-list field
        And the value of the dropdown-list field is "Purchasing"
        # Adding line
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 02 - Prerequisites - Tax creation AUTO 02 SALES NE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Tax category" labelled reference field on the main page
        And the user writes "Value Added Tax" in the reference field
        And the user selects "Value Added Tax" in the reference field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "AUTO 02 SALES NE" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        And the user selects the "Primary external reference" labelled text field on the main page
        Then the user writes "AUTO 02 SALES NE" in the text field
        When the user selects the "Tax type" labelled dropdown-list field on the main page
        And the user selects "Sales" in the dropdown-list field
        And the value of the dropdown-list field is "Sales"
        # Adding line
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 03 - Prerequisites - Tax creation AUTO 03 PURCHASING + SALES NE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Tax category" labelled reference field on the main page
        And the user writes "Value Added Tax" in the reference field
        And the user selects "Value Added Tax" in the reference field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "AUTO 03 PURCHASING + SALES NE" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        And the user selects the "Primary external reference" labelled text field on the main page
        Then the user writes "AUTO 03 PURCHASING + SALES NE" in the text field
        When the user selects the "Tax type" labelled dropdown-list field on the main page
        And the user selects "Purchasing and sales" in the dropdown-list field
        And the value of the dropdown-list field is "Purchasing and sales"
        # Adding line
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 04 - Create sales order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Create sales order
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Sold-to customer" labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 05 - Add tax details
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user filters the "name" labelled column in the table field with value "AUTO 02 SALES NE"
        And the user selects the row with text "AUTO 02 SALES NE" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user filters the "name" labelled column in the table field with value "AUTO 03 PURCHASING + SALES NE"
        And the user selects the row with text "AUTO 03 PURCHASING + SALES NE" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 06 - Purchase order creation in status draft
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "purchasingSite" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "7" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user filters the "name" labelled column in the table field with value "AUTO 01 PURCHASING NE"
        And the user selects the row with text "AUTO 01 PURCHASING NE" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 07 - Check tax type for AUTO 01 PURCHASING NE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "AUTO 01 PURCHASING NE"
        And the user selects the row with text "AUTO 01 PURCHASING NE" in the "name" bound column header of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        When the user selects the "Tax category" labelled reference field on the main page
        And the value of the reference field is "Value Added Tax"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "AUTO 01 PURCHASING NE"
        And the user selects the "Tax type" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is "Purchasing"

    Scenario: 08 - Check tax type for AUTO 02 SALES NE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "AUTO 02 SALES NE"
        And the user selects the row with text "AUTO 02 SALES NE" in the "name" bound column header of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        When the user selects the "Tax category" labelled reference field on the main page
        And the value of the reference field is "Value Added Tax"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "AUTO 02 SALES NE"
        And the user selects the "Tax type" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is "Sales"

    Scenario: 09 - Check tax type for AUTO 03 PURCHASING + SALES NE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "AUTO 03 PURCHASING + SALES NE"
        And the user selects the row with text "AUTO 03 PURCHASING + SALES NE" in the "name" bound column header of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        When the user selects the "Tax category" labelled reference field on the main page
        And the value of the reference field is "Value Added Tax"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "AUTO 03 PURCHASING + SALES NE"
        And the user selects the "Tax type" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is "Purchasing and sales"
