# The goal of this test is to check if the journals in the posting tab are created correctly for purchase invoice
# and purchase credit note/memo for Fr
# Tenant : None
# Site: Entrepot saint denis , Supplier: BARRES
# Items : Journal_Stock_Item_001
#         Journal_Service_Item_001
@finance
@distribution
@Stack_Overflow
Feature: finance-flow-fr-purchase-invoice-credit-memo-journal-entries

    Scenario: 01 - Find and Post the Purchase Invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        # Find the specific PI
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PI230003"
        And the user selects the row with text "PI230003" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Post the PI
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast with text "The purchase invoice was posted." is displayed

        # Store the values which will be used later
        When the user selects the "Financial site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"

        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PI_Number]"

        When the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Supplier]"

        When the user selects the "Invoice date" labelled date field on the main page
        And the user stores the value of the date field with the key "[ENV_Invoice_Date]"

    Scenario: 02 - Check the correctness of the posting tab of the Purchase Invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        # Find the specific PI
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user selects the "Purchase invoices" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_PI_Number]"
        And the user selects the row with text "[ENV_PI_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Verify it is posted
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"


        Then selects the "Posting" labelled navigation anchor on the main page
        When the "Posting" labelled navigation anchor is selected

        And the user selects the "Results" labelled table field on the main page
        # Select the AP row
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_PI_Number]"
        And the user selects the row with text "Accounts payable invoice" in the "Document type" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_PI_Number]"


    Scenario: 03 - Check the correctness of the Accounts payable invoice page
        # Go to accounts payable page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsPayableInvoice"
        Then the "Accounts payable invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        # Search for record
        # When the user selects the "Accounts payable invoices" labelled table field on the main page
        # CI bug loading taking too long
        And the user filters the "Number" labelled column in the table field with value "[ENV_PI_Number]"
        And the user selects the row with text "[ENV_PI_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Accounts payable invoice [ENV_PI_Number]" titled page is displayed
        # Verification steps
        # Number
        When the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_PI_Number]"
        # Type
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Purchase invoice"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"
        # Inv date
        # @TODO: This test is failing because record data is not being stored correctly?
        # When the user selects the "Invoice date" labelled date field on the main page
        # Then the value of the date field is "[ENV_Invoice_Date]"

        # Supplier
        # @TODO: This test is failing because it is selecting the wrong field below?
        #When the user selects the "Bill-by supplier" labelled reference field on the main page
        #Then the value of the date field is "[ENV_Supplier]"

        # Posting status
        When the user selects the "Posting status" labelled label field on the main page
        Then the value of the label field is "Posted"

    Scenario: 04 - Check the correctness of the Accounts payable invoice page Lines grid
        When the user selects the "Lines" labelled nested grid field on the main page
        # Check first item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                            |
            | Account              | ******** -- Achats stockés de mat. premières TVA débits taux normal |
            | Amount including tax | € 720.00                                                            |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Service_Item_001 -- Journal_Service_Item_001"
        # Unable to collapse field
        # And the user collapses the selected row of the nested grid field

        # Expand and check second Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                            |
            | Account              | ******** -- Achats stockés de mat. premières TVA débits taux normal |
            | Amount including tax | € 360.00                                                            |
        And the user expands the selected row of the nested grid field
        # Below is conflicting and selecting from first row above instead of next one
        # And the user selects row with text "DEP1-S01 -- Entrepot de  Saint  Denis"  in column with header "Financial site" in the nested grid field

        # Below might not work because Amount column is not in view, Robot not scrolling horizontally? Can use ultrawide desktop above to bypass issue locally
        # And the user selects row with text "€ 300.00" in column with header "Amount" in the nested grid field
        # Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Stock_Item_001 -- Journal_Stock_Item_001"

        # Click the link, Opens new tab, if empty should fail test?
        And the user selects the "accountingIntegrationReference" labelled link field on the main page
    # And the user clicks in the link field



    Scenario: 05 - Find, update and post the Purchase credit memo/note
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "Purchase credit memos" labelled table field on the main page
        # Find specific PCM
        And the user filters the "Number" labelled column in the table field with value "PC230003"
        And the user selects the row with text "PC230003" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Post the PCM
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast with text "The purchase credit memo was posted." is displayed

        # Store the values which will be used later
        When the user selects the "Financial site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"

        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PCM_Number]"

        When the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Supplier]"

        When the user selects the "Supplier document date" labelled date field on the main page
        And the user stores the value of the date field with the key "[ENV_Invoice_Date]"

    Scenario: 06 - Check the correctness of the posting tab of the purchase credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "Purchase credit memos" labelled table field on the main page

        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        # Find specific PCM
        And the user filters the "Number" labelled column in the table field with value "[ENV_PCM_Number]"
        And the user selects the row with text "[ENV_PCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Check that the status updated
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

        # Move to Posting tab
        When selects the "Posting" labelled navigation anchor on the main page
        When the "Posting" labelled navigation anchor is selected

        And the user selects the "Results" labelled table field on the main page
        # Select the AP row
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_PCM_Number]"


        And the user selects the row with text "Accounts payable invoice" in the "Document type" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_PCM_Number]"


    Scenario: 07 - Check the correctness of the Accounts payable invoice page for PCM
        # Go to accounts payable page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsPayableInvoice"
        Then the "Accounts payable invoices" titled page is displayed
        # Search for record
        When the user selects the "Accounts payable invoices" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_PCM_Number]"
        And the user selects the row with text "[ENV_PCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Accounts payable invoice [ENV_PCM_Number]" titled page is displayed
        # Verification steps
        # Number
        When the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_PCM_Number]"
        # Type
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Purchase credit memo"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"

        # Inv date
        # @TODO: This test is failing because record data is not being stored correctly?
        # When the user selects the "Invoice date" labelled date field on the main page
        # Then the value of the date field is "[ENV_Invoice_Date]"

        # Supplier
        # @TODO: This test is failing because it is selecting the wrong field below?
        #When the user selects the "Bill-by supplier" labelled reference field on the main page
        #Then the value of the date field is "[ENV_Supplier]"

        # Posting status
        When the user selects the "Posting status" labelled label field on the main page
        Then the value of the label field is "Posted"

    Scenario: 08 - Check the correctness of the Accounts payable invoice page Lines grid for PCM
        When the user selects the "Lines" labelled nested grid field on the main page
        # Check first item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                            |
            | Account              | ******** -- Achats stockés de mat. premières TVA débits taux normal |
            | Amount including tax | € 720.00                                                            |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Service_Item_001 -- Journal_Service_Item_001"
        # Unable to collapse field
        # And the user collapses the selected row of the nested grid field

        # Expand and check second Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                            |
            | Account              | ******** -- Achats stockés de mat. premières TVA débits taux normal |
            | Amount including tax | € 360.00                                                            |
        And the user expands the selected row of the nested grid field
        # Below is conflicting and selecting from first row above instead of next one
        # And the user selects row with text "DEP1-S01 -- Entrepot de  Saint  Denis"  in column with header "Financial site" in the nested grid field

        # Below might not work because Amount column is not in view, Robot not scrolling horizontally? Can Use ultrawide desktop above to bypass issue manually
        # And the user selects row with text "€ 300.00" in column with header "Amount" in the nested grid field
        # Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Stock_Item_001 -- Journal_Stock_Item_001"

        # Click the link, Opens new tab, if empty should fail test?
        And the user selects the "accountingIntegrationReference" labelled link field on the main page
# And the user clicks in the link field
