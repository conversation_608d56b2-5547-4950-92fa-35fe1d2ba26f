
# The Payment tracking capabilities of SDMO have been enhanced to better support the Accounts Receivable/Payables process and improve clarity
# and accuracy in payment reconciliation. With release V55 we provide our users enhanced controls of the logical consistency of Payment terms.
# This test outlines the need to develop automated tests for a 'Payment Term' dialog, which includes next to the payment term header
# the sections Due Date, Discount, and Penalty.
# Each section contains specific fields with defined input types, validation rules, and conditional behaviors.
# Additionally, two critical business rules must be enforced on save: alignment of Discount Type with Due date type,
# and ensuring Discount days do not exceed Due date days.
# These tests will ensure proper field validation, logical consistency, and accurate UI behavior.
# Additional note:
# In general, there are no specific requirements for testing or creating payment terms. However, when a Sage Intacct integration is active,
# payment terms that include discount and/or penalty conditions cannot be applied to invoices subject to VAT or GST. Consequently,
# it is impossible to create automated tests for the scenarios described below under UK, South African, French, or German legislation (or respective companies).
# Prequisites:
# Sage Intacct integration is deactivated in the option management

@finance
Feature: finance-payment-tracking-payment-term-controls

    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario: 0.00 - Data creation - Sales invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the user writes "Schmitt Apparatebau GmbH" in the reference field
        And the user selects "Schmitt Apparatebau GmbH" in the reference field
        And the user selects the "Invoice date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "100" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "DISAMT10D100EUR" in the reference field
        And the user selects "DISAMT10D100EUR" in the reference field
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+30"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM01]"

    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario: 0.01 - Data creation - Purchase invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        And the user selects the "Invoice date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "100" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Variance status updated" is displayed
        And the user dismisses all the toasts
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "DISAMT10D100EUR" in the reference field
        And the user selects "DISAMT10D100EUR" in the reference field
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+30"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "1000" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "190" in the numeric field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM02]"

    Scenario: 01 - Payment term control - Header section (Field-Level Validation)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Payment term" titled page is displayed
        And the user selects the "Business Entity Type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        # @todo create ER to be able to verify that dropdown includes only the values 'Customer', 'Supplier', and 'All'
        Then at least the following list of options is displayed for the dropdown-list field:"Customer | Supplier | All"
        Given the user selects the "ID" labelled text field on the main page
        And the user clears the text field
        When the user writes "" in the text field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
            You need to enter a value
            """
        Given the user selects the "Name" labelled text field on the main page
        When the user writes "" in the text field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           You need to enter a value
            """
        Given the user selects the "ID" labelled text field on the main page
        And the user clears the text field
        When the user writes "-3d?z" in the text field
        And the user presses Tab
        Then the text field is valid
        Given the user selects the "Name" labelled text field on the main page
        When the user writes "-3d?z" in the text field
        And the user presses Tab
        Then the text field is valid
        Given the user selects the "Description" labelled text field on the main page
        When the user writes "-3d?z" in the text field
        And the user presses Tab
        Then the text field is valid

    Scenario: 02 - Payment term control - Due date section (Field-Level Validation)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Payment term" titled page is displayed
        When the user selects the "Number of days" labelled numeric field on the main page
        And the user writes "z" in the numeric field
        And the user presses Tab
        Then the value of the numeric field is ""
        When the user selects the "Number of days" labelled numeric field on the main page
        And the user writes "-1" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Minimum value is 0
            """
        When the user selects the "Number of days" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "2.33" in the numeric field
        And the user presses Tab
        Then the numeric field is valid
        Then the value of the numeric field is "233"
        When the user selects the "Number of days" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "0" in the numeric field
        And the user presses Tab
        Then the numeric field is valid
        When the user selects the "Number of days" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "1" in the numeric field
        And the user presses Tab
        Then the numeric field is valid
        And the user selects the "dueDateType" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        # @todo create ER to be able to verify that dropdown includes only the values
        Then at least the following list of options is displayed for the dropdown-list field:"After invoice date | After the end of the month of invoice date | After invoice date and extended to end of month"

    Scenario: 03 - Payment term control - Discount section (Field-Level Validation)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Payment term" titled page is displayed
        And the user selects the "From" labelled dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        # @todo create ER to be able to verify that dropdown includes only the values
        Then at least the following list of options is displayed for the dropdown-list field:"After invoice date | After the end of the month of invoice date | After invoice date and extended to end of month"
        And the user selects the "discountType" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        # @todo create ER to be able to verify that dropdown includes only the values
        Then at least the following list of options is displayed for the dropdown-list field:"Percentage | Amount"
        When the user selects the "Day" labelled numeric field on the main page
        And the user writes "z" in the numeric field
        And the user presses Tab
        Then the value of the numeric field is ""
        When the user selects the "Day" labelled numeric field on the main page
        And the user writes "-1" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Minimum value is 0
            """
        When the user selects the "Day" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "2.33" in the numeric field
        And the user presses Tab
        Then the numeric field is valid
        Then the value of the numeric field is "233"
        When the user selects the "Day" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "0" in the numeric field
        And the user presses Tab
        Then the numeric field is valid
        When the user selects the "Day" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "1" in the numeric field
        And the user presses Tab
        Then the numeric field is valid

    Scenario: 04 - Payment term control - Discount section (Conditional UI Behavior)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Payment term" titled page is displayed
        And the user selects the "discountType" bound dropdown-list field on the main page
        And the user selects "Percentage" in the dropdown-list field
        Then the value of the dropdown-list field is "Percentage"
        Then the "Percentage" labelled numeric field on the main page is displayed
        Then the "Amount" labelled numeric field on the main page is hidden
        When the user selects the "Percentage" labelled numeric field on the main page
        And the user writes "-1" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Minimum value is 0
            """
        When the user selects the "Percentage" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "111" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Maximum value is 100
            """
        When the user selects the "Percentage" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "0.1" in the numeric field
        And the user presses Tab
        Then the numeric field is valid
        And the user selects the "discountType" bound dropdown-list field on the main page
        And the user selects "Amount" in the dropdown-list field
        Then the value of the dropdown-list field is "Amount"
        Then the "Amount" labelled numeric field on the main page is displayed
        Then the "Percentage" labelled numeric field on the main page is hidden
        When the user selects the "Amount" labelled numeric field on the main page
        And the user writes "-1" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Minimum value is 0
            """
        When the user selects the "Amount" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "1.333" in the numeric field
        And the user presses Tab
        Then the numeric field is valid

    Scenario: 05 - Payment term control - Penalty section (Field-Level Validation)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Payment term" titled page is displayed
        And the user selects the "penaltyType" bound dropdown-list field on the main page
        When the user clicks in the dropdown-list field
        # @todo create ER to be able to verify that dropdown includes only the values
        Then at least the following list of options is displayed for the dropdown-list field:"Percentage | Amount"

    Scenario: 06 - Payment term control - Penalty section (Conditional UI Behavior)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Payment term" titled page is displayed
        And the user selects the "penaltyType" bound dropdown-list field on the main page
        And the user selects "Percentage" in the dropdown-list field
        Then the value of the dropdown-list field is "Percentage"
        Then the "Percentage" labelled numeric field on the main page is displayed
        Then the "Amount" labelled numeric field on the main page is hidden
        When the user selects the "Percentage" labelled numeric field on the main page
        And the user writes "-1" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Minimum value is 0
            """
        When the user selects the "Percentage" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "111" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Maximum value is 100
            """
        When the user selects the "Percentage" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "0.1" in the numeric field
        And the user presses Tab
        Then the numeric field is valid
        And the user selects the "penaltyType" bound dropdown-list field on the main page
        And the user selects "Amount" in the dropdown-list field
        Then the value of the dropdown-list field is "Amount"
        Then the "Amount" labelled numeric field on the main page is displayed
        Then the "Percentage" labelled numeric field on the main page is hidden
        When the user selects the "Amount" labelled numeric field on the main page
        And the user writes "-1" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
           Minimum value is 0
            """
        When the user selects the "Amount" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "1.333" in the numeric field
        And the user presses Tab
        Then the numeric field is valid

    Scenario: 07 - Payment term control - Business Rule Validations (on Save) - Mismatch of Discount Type and Due date Type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the "Payment term" titled page is displayed
        And the user selects the "ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "AUTOMATIONTEST01" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user writes "AUTOMATIONTEST01" in the text field
        And the user selects the "Number of days" labelled numeric field on the main page
        And the user writes "30" in the numeric field
        And the user selects the "From" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "After the end of the month of invoice date" in the dropdown-list field
        And the user selects the "Day" labelled numeric field on the main page
        And the user writes "10" in the numeric field
        And the user selects the "discountType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Percentage" in the dropdown-list field
        And the user selects the "discountAmount" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "The Type for the Due date and the Type for the Discount need to be the same." is displayed

    Scenario: 08 - Payment term control - Business Rule Validations (on Save) - Discount Days bigger than Due Date Days
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the "Payment term" titled page is displayed
        And the user selects the "ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "AUTOMATIONTEST02" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user writes "AUTOMATIONTEST02" in the text field
        And the user selects the "Number of days" labelled numeric field on the main page
        And the user writes "30" in the numeric field
        And the user selects the "From" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "After the end of the month of invoice date" in the dropdown-list field
        And the user selects the "Day" labelled numeric field on the main page
        And the user writes "40" in the numeric field
        And the user selects the "discountType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Percentage" in the dropdown-list field
        And the user selects the "discountAmount" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "The discount date needs to be on or before the due date." is displayed

    Scenario: 09 - Payment term control - Business Rule Validations (on Save) - Valid save scenario
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
        Then the "Payment terms" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the "Payment term" titled page is displayed
        And the user selects the "ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "AUTOMATIONTEST03" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user writes "AUTOMATIONTEST03" in the text field
        And the user selects the "Number of days" labelled numeric field on the main page
        And the user writes "30" in the numeric field
        And the user selects the "dueDateType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "After invoice date" in the dropdown-list field
        And the user selects the "From" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "After invoice date" in the dropdown-list field
        And the user selects the "Day" labelled numeric field on the main page
        And the user writes "10" in the numeric field
        And the user selects the "discountType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Percentage" in the dropdown-list field
        And the user selects the "discountAmount" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: 10 - Sales Invoice - Invalid Due Date (Less than invDate + discountDays)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM01]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM01]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Sales invoice [ENV_DOC_NUM01]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+9"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a error toast containing text "The discount date needs to be on or before the due date" is displayed

    Scenario: 11 - Sales Invoice - Valid Due Date (Equal to invDate + discountDays)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM01]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM01]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Sales invoice [ENV_DOC_NUM01]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+10"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales invoice was posted." is displayed

    Scenario: 12 - Sales Invoice - Valid Due Date (Greater than invDate + discountDays)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the user writes "Schmitt Apparatebau GmbH" in the reference field
        And the user selects "Schmitt Apparatebau GmbH" in the reference field
        And the user selects the "Invoice date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "100" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "DISAMT10D100EUR" in the reference field
        And the user selects "DISAMT10D100EUR" in the reference field
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+30"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Payment term *" labelled reference field on the main page
        Then the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+11"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales invoice was posted." is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM05]"

    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario: 0.02 - Data creation - Sales credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM01]"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM01]"
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Sales invoice [ENV_DOC_NUM01]" titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        And a toast with text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM03]"

    Scenario: 13 - Sales Credit Memo - Invalid Due Date (Less than invDate + discountDays)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "[ENV_DOC_NUM03]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Sales credit memo [ENV_DOC_NUM03]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+9"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a error toast containing text "The discount date needs to be on or before the due date" is displayed

    Scenario: 14 - Sales Credit Memo - Valid Due Date (Equal to invDate + discountDays)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "[ENV_DOC_NUM03]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Sales credit memo [ENV_DOC_NUM03]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+10"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales credit memo was posted." is displayed

    Scenario: 15 - Sales Credit Memo - Valid Due Date (Greater than invDate + discountDays)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM05]"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM05]"
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Sales invoice [ENV_DOC_NUM05]" titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        And a toast with text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM06]"
        And the "Sales credit memo [ENV_DOC_NUM06]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+11"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The sales credit memo was posted." is displayed

    Scenario: 16 - Purchase Invoice - Invalid Due Date (Less than suppInvDate + discountDays)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM02]"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM02]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase invoice [ENV_DOC_NUM02]" titled page is displayed
        And the user selects the "Invoice date" labelled date field on the main page
        Given the value of the date field is a generated date with value "T"
        And the user selects the "Supplier document date" labelled date field on the main page
        Given the value of the date field is a generated date with value "T"
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        When the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+9"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a error toast containing text "The discount date needs to be on or before the due date" is displayed

    Scenario: 17 - Purchase Invoice - Valid Due Date (Equal to suppInvDate + discountDays)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM02]"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM02]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase invoice [ENV_DOC_NUM02]" titled page is displayed
        And the user selects the "Invoice date" labelled date field on the main page
        Given the value of the date field is a generated date with value "T"
        And the user selects the "Supplier document date" labelled date field on the main page
        Given the value of the date field is a generated date with value "T"
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        When the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+10"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The purchase invoice was posted." is displayed

    Scenario: 18 - Purchase Invoice - Valid Due Date (Greater than suppInvDate + discountDays)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        When the user selects the "Invoice date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "100" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Variance status updated" is displayed
        And the user dismisses all the toasts
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "DISAMT10D100EUR" in the reference field
        And the user selects "DISAMT10D100EUR" in the reference field
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+30"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "1000" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "190" in the numeric field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM07]"
        And the "Purchase invoice [ENV_DOC_NUM07]" titled page is displayed
        And the user selects the "Invoice date" labelled date field on the main page
        Given the value of the date field is a generated date with value "T"
        And the user selects the "Supplier document date" labelled date field on the main page
        Given the value of the date field is a generated date with value "T"
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        Then the value of the reference field is "DISAMT10D100EUR"
        When the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+11"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The purchase invoice was posted." is displayed

    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario: 0.03 - Data creation - Purchase credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM02]"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM02]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase invoice [ENV_DOC_NUM02]" titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the dialog title is "Create purchase credit memo"
        And the user selects the "Reason *" labelled reference field on a modal
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field
        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes "1000" in the numeric field
        And the user clicks the "Create" button of the dialog on the main page
        And a toast containing text "Purchase credit memo created." is displayed
        And the user dismisses all the toasts
        And the user selects the "Total credit memo tax" labelled numeric field on the main page
        And the user writes "190" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM04]"

    Scenario: 19 - Purchase credit memo - Invalid Due Date (Less than suppInvDate + discountDays)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "Purchase credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "[ENV_DOC_NUM04]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase credit memo [ENV_DOC_NUM04]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+9"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        And a error toast containing text "The discount date needs to be on or before the due date" is displayed

    Scenario: 20 - Purchase credit memo - Valid Due Date (Equal to suppInvDate + discountDays)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "Purchase credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "[ENV_DOC_NUM04]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase credit memo [ENV_DOC_NUM04]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+10"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        And a toast containing text "The purchase credit memo was posted." is displayed

    Scenario: 21 - Purchase credit memo - Valid Due Date (Greater than suppInvDate + discountDays)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value "[ENV_DOC_NUM07]"
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "[ENV_DOC_NUM07]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Purchase invoice [ENV_DOC_NUM07]" titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the dialog title is "Create purchase credit memo"
        And the user selects the "Reason *" labelled reference field on a modal
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field
        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes "1000" in the numeric field
        And the user clicks the "Create" button of the dialog on the main page
        And a toast containing text "Purchase credit memo created." is displayed
        And the user dismisses all the toasts
        And the user selects the "Total credit memo tax" labelled numeric field on the main page
        And the user writes "190" in the numeric field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_DOC_NUM08]"
        And the "Purchase credit memo [ENV_DOC_NUM08]" titled page is displayed
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the value of the reference field is "DISAMT10D100EUR"
        And the user selects the "Due date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+11"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        And a toast containing text "The purchase credit memo was posted." is displayed
