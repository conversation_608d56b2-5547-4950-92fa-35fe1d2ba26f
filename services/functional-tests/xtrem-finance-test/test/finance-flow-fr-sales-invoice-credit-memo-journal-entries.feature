# The goal of this test is to check if the journals in the posting tab are created correctly for sales invoice
# and sales credit note/memo for FR
# Intacct tenant : None
# Site: Siege Social S01 / Entrepot de Saint Denis
# Customer: ATSERMO
# Items : Journal_Stock_Item_002
#         Journal_Service_Item_002
# Sales Invoice SIED230009
# Sales Credit Memo SCED230003

@finance
@distribution
Feature: finance-flow-fr-sales-invoice-credit-memo-journal-entries

    Scenario: 01 - Find and post the Sales Invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        # Find the specific SI
        When the user selects the "Sales invoices" labelled table field on the main page
        # SIED when in Draft, SIPED when Posted
        And the user filters the "Number" labelled column in the table field with value "SIED230009"
        And the user selects the row with text "SIED230009" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Post the SI
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast with text "The sales invoice was posted." is displayed

        # Store the values which will be used later
        When the user selects the "Site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"

        # Wait so the page refreshes the Number field
        And the user refreshes the screen

        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SI_Number]"

        When the user selects the "Bill-to customer" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Customer]"

        When the user selects the "Invoice date" labelled date field on the main page
        And the user stores the value of the date field with the key "[ENV_Invoice_Date]"


    Scenario: 02 - Check the correctness of the posting tab of the Sales Invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        # Find the specific SI
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user selects the "Sales invoices" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_SI_Number]"
        And the user selects the row with text "[ENV_SI_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Verify it is posted
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

        Then selects the "Posting" labelled navigation anchor on the main page
        When the "Posting" labelled navigation anchor is selected

        And the user selects the "postingDetails" bound table field on the main page
        # Select the JE row
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field

        # Verify
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_SI_Number]"

        # Select the AR invoice row
        When the user selects the row with text "Accounts receivable invoice" in the "Document type" labelled column header of the table field
        # Verify
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_SI_Number]"


    Scenario: 03 - Check the correctness of the Accounts Receivable invoice page - 1
        # Go to accounts receivable page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsReceivableInvoice"
        Then the "Accounts receivable invoices" titled page is displayed
        # Search for record
        When the user selects the "Accounts receivable invoices" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_SI_Number]"
        And the user selects the row with text "[ENV_SI_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Accounts receivable invoice [ENV_SI_Number]" titled page is displayed
        # Verification steps
        # Number
        When the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SI_Number]"
        # Type
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Sales invoice"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Siège social S01  PARIS"
        And the user stores the value of the reference field with the key "[ENV_Financial_Site]"
        # Inv date
        # @TODO: This test is failing because record data is not being stored correctly?
        # When the user selects the "Invoice date" labelled date field on the main page
        # Then the value of the date field is "[ENV_Invoice_Date]"

        # Bill-to customer
        # @TODO: This test is failing because it is selecting the wrong field below?
        #When the user selects the "Bill-to customer" labelled reference field on the main page
        #Then the value of the date field is "[ENV_Customer]"

        # Posting status
        When the user selects the "Posting status" labelled label field on the main page
        Then the value of the label field is "Posted"


    Scenario: 04 - Check the correctness of the Accounts receivable invoice page Lines grid - 1
        When the user selects the "Lines" labelled nested grid field on the main page
        # Check first item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                    |
            | Account              | ******** -- Ventes de produits finis TVA débits taux normal |
            | Amount including tax | € 720.00                                                    |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial Site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Service_Item_002 -- Journal_Service_Item_002"

        When the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                    |
            | Account              | ******** -- Ventes de produits finis TVA débits taux normal |
            | Amount including tax | € 720.00                                                    |
        And the user collapses the selected row of the nested grid field

        # Expand and check second Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                    |
            | Account              | ******** -- Ventes de produits finis TVA débits taux normal |
            | Amount including tax | € 360.00                                                    |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "€ 300.00" in column with header "Amount" in the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Stock_Item_002 -- Journal_Stock_Item_002"



    Scenario: 05 - Check the correctness of the Journal entry page - 1
        # Go to Journal entries page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        # Search for record
        When the user selects the "Journal entries" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_SI_Number]"
        And the user selects the row with text "[ENV_SI_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Journal entry [ENV_SI_Number]" titled page is displayed
        # Verification
        # Number
        When the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SI_Number]"
        # Type
        When the user selects the "Journal" labelled reference field on the main page
        Then the value of the reference field is "Journal des ventes"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Financial_Site]"
        # Description
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "AR invoice"
        # Posting status
        When the user selects the "Posting status" labelled label field on the main page
        Then the value of the label field is "Posted"

    Scenario: 06 - Check the correctness of the Journal entry page Lines grid [Using stock item to see if amounts match]
        When the user selects the "Lines" labelled nested grid field on the main page
        # Check first item
        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText                            |
            | Account           | ******** -- Clients - Ventes France |
            | Transaction debit | € 360.00                            |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Stock_Item_002 -- Journal_Stock_Item_002"

        When the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText                            |
            | Account           | ******** -- Clients - Ventes France |
            | Transaction debit | € 360.00                            |
        And the user collapses the selected row of the nested grid field

        # Expand and check second Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText                                                    |
            | Account            | ******** -- Ventes de produits finis TVA débits taux normal |
            | Transaction credit | € 300.00                                                    |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Stock_Item_002 -- Journal_Stock_Item_002"
        And the value of the "Company amount" labelled nested numeric field of the selected row in the nested grid field is "€ 300.00"

        When the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText                                                    |
            | Account            | ******** -- Ventes de produits finis TVA débits taux normal |
            | Transaction credit | € 300.00                                                    |
        And the user collapses the selected row of the nested grid field

        # Expand to check tax of Stock Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText                                               |
            | Account            | ******** -- TVA collectée sur les débits - Taux normal |
            | Transaction credit | € 60.00                                                |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Stock_Item_002 -- Journal_Stock_Item_002"
        And the value of the "Company amount" labelled nested numeric field of the selected row in the nested grid field is "€ 60.00"

    Scenario: 07 - Find and post the Sales Credit Memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "Sales credit memos" labelled table field on the main page
        # Find specific PCM
        # SCED when in Draft, SCPED when Posted
        And the user filters the "Number" labelled column in the table field with value "SCED230003"
        And the user selects the row with text "SCED230003" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Update the document date to prevent validation error: Because previous above scenarios SCM got posted first with current date so
        # this one will have an older date which needs to be updated to conform to business rules.
        And the user selects the "Credit memo date" labelled date field on the main page
        And the user writes "09/20/2023" in the date field
        And the user presses Tab

        # Save
        And the user clicks the "Save" labelled business action button on the main page
        # Post the PCM
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast with text "The sales credit memo was posted." is displayed

        # Store the values which will be used later
        When the user selects the "Site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"

        # Wait few seconds so that the page refreshes the Number field before storing
        And the user refreshes the screen

        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCM_Number]"

        When the user selects the "Bill-to customer" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Customer]"

        When the user selects the "Credit memo date" labelled date field on the main page
        And the user stores the value of the date field with the key "[ENV_Credit_Date]"

    Scenario: 08 - Check the correctness of the posting tab of the Sales Credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed

        When the user selects the "Sales credit memos" labelled table field on the main page

        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        # Find specific PCM
        And the user filters the "Number" labelled column in the table field with value "[ENV_SCM_Number]"
        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Check that the status updated
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

        # Move to Posting tab
        When selects the "Posting" labelled navigation anchor on the main page
        When the "Posting" labelled navigation anchor is selected

        And the user selects the "postingDetails" bound table field on the main page
        # Select the JE row
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        # Verify
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_SCM_Number]"

        # Select the AR invoice row
        When the user selects the row with text "Accounts receivable invoice" in the "Document type" labelled column header of the table field
        # Verify
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"
        Then the value of the "Document number" labelled nested label field of the selected row in the table field is "[ENV_SCM_Number]"

    Scenario: 09 - Check the correctness of the Accounts Receivable invoice page - 2
        # Go to accounts receivable page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsReceivableInvoice"
        Then the "Accounts receivable invoices" titled page is displayed
        # Search for record
        When the user selects the "Accounts receivable invoices" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_SCM_Number]"
        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Accounts receivable invoice [ENV_SCM_Number]" titled page is displayed
        # Verification steps
        # Number
        When the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SCM_Number]"
        # Type
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Sales credit memo"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Financial_Site]"
        # Inv date
        # @TODO: This test is failing because record data is not being stored correctly?
        # When the user selects the "Invoice date" labelled date field on the main page
        # Then the value of the date field is "[ENV_Invoice_Date]"

        # Bill-to customer
        # @TODO: This test is failing because it is selecting the wrong field below?
        #When the user selects the "Bill-to customer" labelled reference field on the main page
        #Then the value of the date field is "[ENV_Customer]"

        # Posting status
        When the user selects the "Posting status" labelled label field on the main page
        Then the value of the label field is "Posted"


    Scenario: 10 - Check the correctness of the Accounts receivable invoice page Lines grid - 2
        When the user selects the "Lines" labelled nested grid field on the main page
        # Check first item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                    |
            | Account              | ******** -- Ventes de produits finis TVA débits taux normal |
            | Amount including tax | € 360.00                                                    |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "€ 300.00" in column with header "Amount" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Service_Item_002 -- Journal_Service_Item_002"

        When the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                    |
            | Account              | ******** -- Ventes de produits finis TVA débits taux normal |
            | Amount including tax | € 360.00                                                    |
        And the user collapses the selected row of the nested grid field

        # Expand and check second Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader         | cellText                                                    |
            | Account              | ******** -- Ventes de produits finis TVA débits taux normal |
            | Amount including tax | € 180.00                                                    |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        And the user selects row with text "€ 150.00" in column with header "Amount" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Stock_Item_002 -- Journal_Stock_Item_002"


    Scenario: 11 - Check the correctness of the Journal entry page - 2
        # Go to Journal entries page
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        # Search for record
        When the user selects the "Journal entries" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_SCM_Number]"
        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        Then the "Journal entry [ENV_SCM_Number]" titled page is displayed
        # Verification
        # Number
        When the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SCM_Number]"
        # Type
        When the user selects the "Journal" labelled reference field on the main page
        Then the value of the reference field is "Journal des ventes"
        # Document type
        When the user selects the "Document type" labelled text field on the main page
        Then the value of the text field is "AC"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Financial_Site]"
        # Description
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "AR invoice"
        # Posting status
        When the user selects the "Posting status" labelled label field on the main page
        Then the value of the label field is "Posted"

    Scenario: 12 - Check the correctness of the Journal entry page Lines grid [Using service item to see if amounts match]
        When the user selects the "Lines" labelled nested grid field on the main page
        # Check first item
        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText                            |
            | Account            | ******** -- Clients - Ventes France |
            | Transaction credit | € 360.00                            |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Service_Item_002 -- Journal_Service_Item_002"

        When the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText                            |
            | Account            | ******** -- Clients - Ventes France |
            | Transaction credit | € 360.00                            |
        And the user collapses the selected row of the nested grid field

        # Expand and check second Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText                                                    |
            | Account           | ******** -- Ventes de produits finis TVA débits taux normal |
            | Transaction debit | € 300.00                                                    |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Service_Item_002 -- Journal_Service_Item_002"
        And the value of the "Company amount" labelled nested numeric field of the selected row in the nested grid field is "€ 300.00"


        When the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText                                                    |
            | Account           | ******** -- Ventes de produits finis TVA débits taux normal |
            | Transaction debit | € 300.00                                                    |
        And the user collapses the selected row of the nested grid field

        # Expand to check tax of Stock Item
        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText                                                      |
            | Account           | ******** -- TVA collectée sur les encaissements - Taux normal |
            | Transaction debit | € 60.00                                                       |
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01  PARIS" in column with header "Financial site" in the nested grid field
        Then the value of the "Item" labelled nested reference field of the selected row in the nested grid field is "Journal_Service_Item_002 -- Journal_Service_Item_002"
        And the value of the "Company amount" labelled nested numeric field of the selected row in the nested grid field is "€ 60.00"
