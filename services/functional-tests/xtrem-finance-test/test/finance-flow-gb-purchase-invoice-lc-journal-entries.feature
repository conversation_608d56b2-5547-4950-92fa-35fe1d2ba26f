#  the goal of this test is to verify that landed cost purchase invoice can be created and posted following business rules
# in this test case we allocate landed cost purchase invoice to 2 different purchase receipts and one PO, verify warnings on allocation, delete allocation , generate journals for LC
#  We use supplier in Pinv different from supplier in P.Receipt
#  We also check if we can allocate all types of landed costs

@finance
@distribution @TestAayu1
Feature: finance-flow-gb-purchase-invoice-lc-journal-entries

    Scenario: 01 - Create Purchase Invoice for landed cost
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        #Fill in HEADER on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        And the user selects "UK LIMITED" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "Ind" in the reference field
        And the user selects "Independant Commodity Intelligence Services" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI-GB-LC001" in the text field
        And the user selects the "Supplier invoice reference" labelled text field on the main page
        And the user writes "test LC" in the text field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "1,684.50" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "336.90" in the numeric field
        And the user presses Enter

        #Adding Pinv Line
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "LC002" in the reference field
        And the user selects "LC002" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "150" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "11.23" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 02 - Verify If Alloction Works Using the Wrning Icon

        #Verification of warning message on table level
        And the user selects the "lines" bound table field on the main page
        And the table has "Refer to the warning on the line for details (1)." error

        #Click on icon in the grid
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field
        And the user clicks the " " labelled nested field of the selected row in the table field
        And the dialog title is "Allocated amount insufficient"
        And the text in the body of the dialog is "The total allocated amount (£0) cannot be less than the landed cost amount to allocate (£1684.5)."
        And the user clicks the "Allocate" button of the Confirm dialog

        #Allocation window is open
        When the user selects the "Allocated documents" labelled table field on a modal
        And the user clicks the "Add lines from orders" labelled multi action of the table field
        And the user selects the "Supplier" labelled reference field on a modal
        And the user clicks the "Search" button of the Custom dialog
        And the user selects the "documents" bound nested grid field on a modal
        And the user selects row with text "PO230001" in column with header "Order number" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user clicks the "Add" labelled business action button on a modal

        #Allocate an amount of Zero
        Then the user selects the row with text "PO230001" in the "Document number" labelled column header of the table field
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Purchase order"
        And the value of the "Allocated amount" labelled nested numeric field of the selected row in the table field is "£ 1,684.50"
        And the user writes "0.00" in the "Allocated Amount" labelled nested numeric field of the selected row in the table field
        And the user presses Enter
        And the user clicks the "Save" button of the Custom dialog
        And the text in the body of the dialog is "The total allocated amount is lower than the landed cost amount to allocate."
        And the user clicks the "Confirm" button of the Custom dialog

        #Verification of warning message on row level
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field
        And the user clicks the " " labelled nested field of the selected row in the table field
        And the dialog title is "Allocated amount missing"
        And the text in the body of the dialog is "You need to enter an allocated amount greater than zero for each allocated document."
        And the user clicks the "Cancel" button of the Custom dialog


    Scenario: 03 - Removing the PO allocated line
        #Select PINV Line
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field
        #Allocation of Landed costs to PO
        And the user clicks the "Landed cost" dropdown action of the selected row of the table field
        When the user selects the "Allocated documents" labelled table field on a modal
        Then the user selects the row with text "PO230001" in the "Document number" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        #Save Allocation
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "confirm" button of the Custom dialog

    Scenario: 04 - Allocation of landed cost to PO with zero amount

        #Open record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-GB-LC001" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-GB-LC001" titled page is displayed

        #Select PINV Line
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field

        #Allocation of Landed costs to PO
        And the user clicks the "Landed cost" dropdown action of the selected row of the table field
        When the user selects the "Allocated documents" labelled table field on a modal
        And the user clicks the "Add lines from orders" labelled business action button of the table field
        And the user selects the "Supplier" labelled reference field on a modal
        And the user clicks the "Search" button of the Custom dialog
        And the user selects the "documents" bound nested grid field on a modal
        And the user selects row with text "PO230001" in column with header "Order number" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user presses Enter
        And the user selects row with text "PO230001" in column with header "Order number" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user clicks the "Add" labelled business action button on a modal
        # update of allocation amount to zero
        Then the user selects the row with text "PO230001" in the "Document number" labelled column header of the table field
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Purchase order"
        And the user writes "0.00" in the "Allocated Amount" labelled nested numeric field of the selected row in the table field
        And the user presses Enter

        #Save Allocation
        And the user clicks the "Save" labelled business action button on the main page

        # Verification of warning message
        And a warn dialog appears
        And the dialog title is "Landed cost allocation"
        And the text in the body of the dialog contains "The total allocated amount is lower than the landed cost amount to allocate."
        And the user clicks the "confirm" button of the Custom dialog

    Scenario: 05 - Verification of Warning Icons for Zero Amount
        And the user selects the "lines" bound table field on the main page
        And the table has "(Refer to the warning on the line for details (1).)" error
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field
        And the user clicks the " " labelled nested field of the selected row in the table field
        Then the dialog title is "Allocated amount missing"
        And the user clicks the "Cancel" button of the Custom dialog

    Scenario: 06 - Removing the PO allocated line
        #Select PINV Line
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field
        #Allocation of Landed costs to PO
        And the user clicks the "Landed cost" dropdown action of the selected row of the table field
        When the user selects the "Allocated documents" labelled table field on a modal
        Then the user selects the row with text "PO230001" in the "Document number" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        #Save Allocation
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "confirm" button of the Custom dialog

    Scenario: 07 - Allocation of landed cost to Purchase receipts

        #Open record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-GB-LC001" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-GB-LC001" titled page is displayed

        #Select PINV Line
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field

        #Allocation of Landed costs
        And the user clicks the "Landed cost" dropdown action of the selected row of the table field
        When the user selects the "Allocated documents" labelled table field on a modal
        And the user clicks the "Add lines from receipts" labelled business action button of the table field
        And the user selects the "Supplier" labelled reference field on a modal
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field
        And the user clicks the "Search" button of the Custom dialog
        And the user selects the "documents" bound nested grid field on a modal
        And the user selects row with text "PR230031" in column with header "Receipt number" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user selects row with text "PR230030" in column with header "Receipt number" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user clicks the "Add" labelled business action button on a modal
        # Add allocation Amount
        And the user selects the "Allocated documents" labelled table field on a modal
        Then the user selects the row with text "PR230030" in the "Document number" labelled column header of the table field
        And the user writes "1123.00" in the "Allocated amount" labelled nested numeric field of the selected row in the table field
        Then the user selects the row with text "PR230031" in the "Document number" labelled column header of the table field
        And the user writes "561.50" in the "Allocated amount" labelled nested numeric field of the selected row in the table field
        And the user presses Enter
        #Save Allocation
        And the user clicks the "Save" labelled business action button on the main page


    Scenario: 08 - Verification of Landed Cost Allocation
        #Open record
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-GB-LC001" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-GB-LC001" titled page is displayed
        #Open Line Action
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field
        #Verify Allocation
        And the user clicks the "Landed cost" dropdown action of the selected row of the table field
        When the user selects the "Allocated documents" labelled table field on a modal
        # Line 1
        Then the user selects the row with text "PR230030" in the "Document number" labelled column header of the table field
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Purchase receipt"
        And the value of the "item__name" bound nested text field of the selected row in the table field is "Pressure sensor"
        And the value of the "Allocated amount" labelled nested text field of the selected row in the table field is "£ 1,123.00"
        And the value of the "Quantity in stock unit" labelled nested text field of the selected row in the table field is "100 each"
        # Line 2
        Then the user selects the row with text "PR230031" in the "Document number" labelled column header of the table field
        And the value of the "Document type" labelled nested text field of the selected row in the table field is "Purchase receipt"
        And the value of the "item__name" bound nested text field of the selected row in the table field is "Pressure transmitter"
        And the value of the "Allocated amount" labelled nested numeric field of the selected row in the table field is "£ 561.50"
        And the value of the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field is "50 each"

    Scenario: 09 - Variance acceptance and PINV Posting
        #Open record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-GB-LC001" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-GB-LC001" titled page is displayed
        #Accepting Variance
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        Then a toast containing text "Variance status updated." is displayed
        And the user dismisses all the toasts
        #Post an invoice
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted." is displayed

    Scenario: 10 - Verification of Account Payable Invoice
        #Open PINV
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-GB-LC001" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-GB-LC001" titled page is displayed
        #Posting Tab
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Accounts payable invoice" in the "Document type" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Posted"

        # Open AP-INV
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsPayableInvoice"
        Then the "Accounts payable invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-GB-LC001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # AP Invoice verification
        Then the "Accounts payable invoice PI-GB-LC001" titled page is displayed
        And the user selects the "Number" labelled text field on the main page
        And the value of the text field is "PI-GB-LC001"
        And the user selects the "Type" labelled dropdown-list field on the main page
        And the value of the dropdown-list field is "Purchase invoice"
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the value of the reference field is "Independant Commodity Intelligence Services"
        And the user selects the "Supplier invoice number" labelled text field on the main page
        And the value of the text field is "test LC"

        #Lines Verification
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "22701 -- Landed cost expense" in column with header "Account" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "22701 -- Landed cost expense"
        And the value of the "Amount excluding tax" labelled nested text field of the selected row in the nested grid field is "£ 1,684.50"
        And the value of the "Tax amount" labelled nested text field of the selected row in the nested grid field is "£ 336.90"
        And the value of the "Amount including tax" labelled nested text field of the selected row in the nested grid field is "£ 2,021.40"
        And the value of the "Line type" labelled nested text field of the selected row in the nested grid field is "Services"
        And the value of the "Financial site" labelled nested text field of the selected row in the nested grid field is "UK LIMITED"

    Scenario: 11 - Verification of Journal Entry
        #Open PINV
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-GB-LC001" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-GB-LC001" titled page is displayed
        #Verify Posting Tab
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Posted"
        And the user stores the value of the "Document number" labelled nested link field of the selected row in the table field with the key "[ENV_PI_LC_JE01]"

        # open JE
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        #Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PI_LC_JE01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PI_LC_JE01]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        #Verify Journal Entry automatically generated
        And the user selects the "Number" labelled text field on the main page
        And the value of the text field is "[ENV_PI_LC_JE01]"
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Journal" labelled reference field on the main page
        And the value of the reference field is "Unbilled Journal"
        And the user selects the "Reference" labelled text field on the main page
        And the value of the text field is "Purchase invoice stock variance"

        #Line 1 Verification
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "£ 561.50" in column with header "Transaction debit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "53100 -- Purchase variance"
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Purchase variance"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR230031"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "£ 561.50"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 GBP = 1 GBP"

        #Line 2 Verification
        And the user refreshes the screen
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "£ 561.50" in column with header "Transaction credit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "22700 -- Landed cost accrual"
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Landed cost accrual"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR230031"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "£ 561.50"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 GBP = 1 GBP"

        #Line 3 Verification
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "£ 1,123.00" in column with header "Transaction debit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "53100 -- Purchase variance"
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Purchase variance"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR230030"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "£ 1,123.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 GBP = 1 GBP"

        #Line 4 Verification
        And the user refreshes the screen
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "£ 1,123.00" in column with header "Transaction credit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "22700 -- Landed cost accrual"
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Landed cost accrual"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR230030"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "£ 0.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "£ 1,123.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 GBP = 1 GBP"

    Scenario: 12 - Create Purchase Invoice for all Types of Landed Costs
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        #Fill in HEADER on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Chem. Austin" in the reference field
        And the user selects "Chem. Austin" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "Laboratoires Industriels Pichot" in the reference field
        And the user selects "Laboratoires Industriels Pichot" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI-GB-LC001-ALL-Types" in the text field
        And the user selects the "Supplier invoice reference" labelled text field on the main page
        And the user writes "test LC all types" in the text field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "1,784.50" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "136.90" in the numeric field
        And the user presses Enter

        #Adding Pinv Line
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "LC/AMOUNT/001" in the reference field
        And the user selects "LC item with Amount name" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "11.23" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        #Adding Pinv Line
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "LC/VOLUME/001" in the reference field
        And the user selects "LC item with Volume name" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "11.23" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        #Adding Pinv Line
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "LC/QTY/001" in the reference field
        And the user selects "LC item with Qty name" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "11.23" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        #Adding Pinv Line
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "LC/WEIGHT/001" in the reference field
        And the user selects "LC item with Weight name" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "11.23" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
