#This test can only be executed with sage.
#The goal of this test is to test the posting class definition for DE

@finance
Feature: finance-flow-de-posting-class-definition-item

    Scenario: 01 - reading the posting class definition for DE Expense
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClassDefinition"
        Then the "Posting class definitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user filters the "Legislation" labelled column in the table field with value "DE"
        # Account type = Expense
        And the user selects the row with text "Expense" in the "Account type name" labelled column header of the table field
        And the user clicks the "Account type name" labelled nested field of the selected row in the table field
        Then the "Posting class definition Item" titled page is displayed
        And the user selects the "Additional criteria" labelled select field on the main page
        Then the value of the select field is "Tax"


    Scenario: 02 - reading the posting class definition for DE Sales revenue
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClassDefinition"
        Then the "Posting class definitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user filters the "Legislation" labelled column in the table field with value "DE"
        # Account type = Sales revenue
        And the user selects the row with text "Sales revenue" in the "Account type name" labelled column header of the table field
        And the user clicks the "Account type name" labelled nested field of the selected row in the table field
        Then the "Posting class definition Item" titled page is displayed
        And the user selects the "Additional criteria" labelled select field on the main page
        Then the value of the select field is "Tax"

    Scenario: 03 - reading the posting class definition for DE Landed cost expense
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClassDefinition"
        Then the "Posting class definitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user filters the "Legislation" labelled column in the table field with value "DE"
        # Account type = Sales revenue
        And the user selects the row with text "Landed cost expense" in the "Account type name" labelled column header of the table field
        And the user clicks the "Account type name" labelled nested field of the selected row in the table field
        Then the "Posting class definition Item" titled page is displayed
        And the user selects the "Additional criteria" labelled select field on the main page
        Then the value of the select field is "Tax"

    Scenario: 4.1 - reading the posting class for Expense
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance-data/PostingClass"
        Then the "Posting classes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "Item" value in the option menu of the table field
        And the user selects the row with text "Standard rate items and services" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Posting class Item-Standard rate items and services" titled page is displayed
        And the user selects the "Lines" labelled nested grid field on the main page


        And the user selects the row with the following content in the nested grid field
            | columnHeader        | cellText             |
            | Chart of accounts   | DE chart of accounts |
            | Account type name   | Expense              |
            | Additional criteria | Tax                  |
        And the user expands the selected row of the nested grid field

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                                            |
            | Tax          | EU Purchase Standard Rate                                           |
            | Account      | Innergemeinschaftlicher Erwerb 19 % Vorsteuer und 19 % Umsatzsteuer |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3425"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                                          |
            | Tax          | EU Purchase Reduced Rate                                          |
            | Account      | Innergemeinschaftlicher Erwerb 7 % Vorsteuer und 7 % Umsatzsteuer |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3420"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                    |
            | Tax          | EU Purchase Zero Rate                       |
            | Account      | Steuerfreier innergemeinschaftlicher Erwerb |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3550"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText              |
            | Tax          | Import Zero Rate      |
            | Account      | Steuerfreie Einfuhren |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3559"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                   |
            | Tax          | Purchase Reduced Rate      |
            | Account      | Wareneingang 7 % Vorsteuer |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3300"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                    |
            | Tax          | Purchase Standard Rate      |
            | Account      | Wareneingang 19 % Vorsteuer |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3400"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Tax          | Purchase Zero Rate |
            | Account      | Wareneingang       |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3200"

        And the user refreshes the screen

    Scenario: 4.2 - reading the posting class for Landed cost Expense
        And the user selects the "Lines" labelled nested grid field on the main page


        And the user selects the row with the following content in the nested grid field
            | columnHeader        | cellText             |
            | Chart of accounts   | DE chart of accounts |
            | Account type name   | Landed cost expense  |
            | Additional criteria | Tax                  |
        And the user expands the selected row of the nested grid field

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                 |
            | Tax          | EU Purchase Reduced Rate |
            | Account      | Bezugsnebenkosten        |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3800"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                  |
            | Tax          | EU Purchase Standard Rate |
            | Account      | Bezugsnebenkosten         |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3800"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText              |
            | Tax          | EU Purchase Zero Rate |
            | Account      | Bezugsnebenkosten     |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3800"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText              |
            | Tax          | Purchase Reduced Rate |
            | Account      | Bezugsnebenkosten     |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3800"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText               |
            | Tax          | Purchase Standard Rate |
            | Account      | Bezugsnebenkosten      |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3800"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Tax          | Purchase Zero Rate |
            | Account      | Bezugsnebenkosten  |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "3800"
        And the user refreshes the screen

    Scenario: 4.3 - reading the posting class for Sales revenue
        And the user selects the "Lines" labelled nested grid field on the main page
        And the user selects the row with the following content in the nested grid field
            | columnHeader        | cellText             |
            | Chart of accounts   | DE chart of accounts |
            | Account type name   | Sales revenue        |
            | Additional criteria | Tax                  |
        And the user expands the selected row of the nested grid field

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                                            |
            | Tax          | EU Sale Zero Rate                                                   |
            | Account      | Steuerfreie innergemeinschaftliche Lieferungen nach § 4 Nr. 1b UStG |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "8125"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText                                 |
            | Tax          | Export Zero Rate                         |
            | Account      | Steuerfreie Umsätze nach § 4 Nr. 1a UStG |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "8120"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText          |
            | Tax          | Sale Reduced Rate |
            | Account      | Erlöse 7 % USt    |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "8300"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText           |
            | Tax          | Sale Standard Rate |
            | Account      | Erlöse 19 % USt    |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "8400"

        And the user selects the row with the following content in the nested grid field
            | columnHeader | cellText       |
            | Tax          | Sale Zero Rate |
            | Account      | Erlöse 0 % Ust |
        Then the value of the "Account ID" labelled nested reference field of the selected row in the nested grid field is "8290"
