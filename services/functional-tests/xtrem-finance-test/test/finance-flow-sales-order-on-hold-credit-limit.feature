# The purpose of this test is to ensure the onhold behaviors for a customer work correctly according to analysis on the Sales Order page.
# Analysis : https://confluence.sage.com/display/XTREEM/Intacct+integration+-+Customer+on-hold+and+credit+limit+-+Analysis
# Intacct integration will not be used as the test will be manipulating the values from SDMO to test the various scenarios
# This requires that all Intacct tenants are switched OFF before this test runs
# Company: TE Headquarter/TE Hampton
# Customer: Onhold_Customer

@finance
@distribution

Feature: finance-flow-sales-order-on-hold-credit-limit

    Scenario: 1 Prerequisite - Set customer to Onhold

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        # Find the customer and verify readonly
        When the user selects the "Customers" labelled table field on the main page
        And the user selects the row with text "Onhold_Customer" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        When selects the "Financial" labelled navigation anchor on the main page

        # Verify Credit limit read-only
        And the user selects the "Credit limit" labelled numeric field on the main page
        And the user writes "500" in the numeric field
        And the user stores the value of the numeric field with the key "[ENV_Cus_Credit_Limit]"
        And the user presses Tab
        # Verify on hold read-only
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user waits for 2 seconds

        And the user clicks the "Put on hold" labelled more actions button in the header

        When the user selects the "On hold" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"

        When the user selects the "Credit limit" labelled numeric field on the main page
        Then the value of the numeric field is "[ENV_Cus_Credit_Limit].000"

    Scenario: 2 Prerequisite - Set Company on hold behavior to Warning

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "TE Connectivity" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company TE Connectivity" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Warning" in the dropdown-list field
        And the user selects "Warning" in the dropdown-list field
        And the user stores the value of the dropdown-list field with the key "[ENV_Cus_OH_Check_Value]"

        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # Verify update
        When the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "[ENV_Cus_OH_Check_Value]"

    Scenario: 3 - Create a Sales Order for an on hold customer with WARNING behavior while in Quote status

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Enter Sales site
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field

        # Enter Sold-to customer
        And the user selects the "Sold-to customer" labelled reference field on the main page
        And the user writes "Onhold_Customer" in the reference field
        And the user selects "Onhold_Customer" in the reference field

        # Select another tab
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SO_OnHold_Warning" in the text field
        And the user stores the value of the text field with the key "[ENV_SO1_Number]"

        # Testing for this in this scenario
        # Verify that the warning icon is now displayed with text
        # TODO: Uncomment when bug is fixed : https://jira.sage.com/browse/XT-58528

        And the user selects the "Sold-to customer" labelled reference field on the main page
        # Then the ""Customer on hold Credit limit £ 0" validation warning message of the reference field is displayed
        # Then the "Customer on hold" validation warning message of the reference field is displayed

        # Add a line for non stock item
        And the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Journal_Service_Item_002" in the reference field
        And the user selects "Journal_Service_Item_002" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user presses Enter

        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10.00" in the numeric field
        And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page

        # Verify Creation
        Then a toast containing text "Record created" is displayed

        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SO1_Number]"

    Scenario: 4 - Update a Sales Order for an on hold customer with WARNING behavior while in Quote status

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        And the user selects the "Sales orders" labelled table field on the main page
        # Find and Select Document
        And the user filters the "Number" labelled column in the table field with value "[ENV_SO1_Number]"
        And the user selects the row with text "[ENV_SO1_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Update by Adding a new line for stock item
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Journal_Stock_Item_002" in the reference field
        And the user selects "Journal_Stock_Item_002" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user presses Enter

        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "20.00" in the numeric field
        And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Testing for this in this scenario
        Then a toast with text "Record updated" is displayed

    Scenario: 5 - Confirm a Sales Order for an on hold customer with WARNING behavior

        # Confirm sales order details
        When the user clicks the "Confirm" labelled business action button on the main page

        # Testing for this in this scenario
        Then the dialog title is "Customer on hold"
        And the text in the body of the dialog is "You are about to confirm the sales order for an on hold customer." on the main page
        When the user clicks the "Continue" button of the Confirm dialog

        #Verify Confirmation
        Then a toast containing text "The sales order was confirmed." is displayed

    Scenario: 6 - Allocate Sales order line item stock for an on hold customer with WARNING behavior

        # Select stock item line
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal_Stock_Item_002" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field

        And the user selects the "Stock allocation" labelled table field on a modal
        And the user selects the row with text "0 each" in the "Quantity to allocate" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        Then the user clicks the "Save" labelled business action button on a modal

        # Testing for this in this scenario
        # Nothing different should happen

        # Testing for this as well in same scenario, User must still be able to Dellocate stock from Header action
        When the user clicks the "Deallocate stock" labelled more actions button in the header

        Then the dialog title is "Deallocation request submitted"
        And the text in the body of the dialog is "The deallocation request was submitted." on the main page
        When the user clicks the "OK" button of the dialog on the main page

    Scenario: 7 - Create WO and PO action from a Sales Order line for an on hold customer with WARNING behavior

        # Select stock item line
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal_Stock_Item_002" in the "Item" labelled column header of the table field

        # Testing for this in this scenario
        And the user clicks the "Create purchase order" dropdown action of the selected row of the table field
        Then the dialog title is "Confirm update"
        And the text in the body of the dialog is "You are about to create an assigned order for an on hold customer." on the main page
        When the user clicks the "Confirm" button of the Confirm dialog
        And the user waits 8 seconds
        Then the "New purchase order" titled sidebar is displayed
        And the user clicks the "Cancel" labelled business action button on the sidebar

    Scenario: 8 - Assign an order from a Sales Order line for an on hold customer with WARNING behavior

        # Select stock item line
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal_Stock_Item_002" in the "Item" labelled column header of the table field

        # Testing for this in this scenario
        And the user clicks the "Assign order" dropdown action of the selected row of the table field
        Then the dialog title is "Confirm update"
        And the text in the body of the dialog is "You are about to manage an assigned order for an on hold customer." on the main page
        When the user clicks the "Confirm" button of the Confirm dialog

    Scenario: 9 - On Ship Business action of Sales Order for an on hold customer with WARNING behavior

        # Testing for this in this scenario
        And the user refreshes the screen
        # Ship the Sales Order
        When the user clicks the "Create shipment" labelled business action button on the main page
        Then the dialog title is "Confirm shipment creation"
        And the text in the body of the dialog is "You are about to ship the sales order for an on hold customer." on the main page
        When the user clicks the "Create" button of the Confirm dialog

        # Verify Confirmation
        Then a toast containing text "1 shipment(s) created" is displayed

    Scenario: 10 - Prerequisite - Set Company on hold behavior to Blocking

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed

        When the user selects the "Companies" labelled table field on the main page
        And the user selects the row with text "TE Connectivity" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Company TE Connectivity" titled page is displayed
        When selects the "Management" labelled navigation anchor on the main page

        # Verify Customer on hold check is editable
        And the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Blocking" in the dropdown-list field
        And the user selects "Blocking" in the dropdown-list field
        And the user stores the value of the dropdown-list field with the key "[ENV_Cus_OH_Check_Value]"

        # Save
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # Verify update
        When the user selects the "Customer on hold check" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "[ENV_Cus_OH_Check_Value]"

    Scenario: 11 - Create/Update a Sales Order for an on hold customer with BLOCKING behavior while in Quote status

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Enter Sales site
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field

        # Enter Sold-to customer
        And the user selects the "Sold-to customer" labelled reference field on the main page
        And the user writes "Onhold_Customer" in the reference field
        And the user selects "Onhold_Customer" in the reference field

        # Select another tab
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SO_OnHold_Blocking" in the text field
        And the user stores the value of the text field with the key "[ENV_SO2_Number]"

        # Testing for this in this scenario
        # Verify that the warning icon is now displayed with text
        # TODO: Uncomment when bug is fixed : https://jira.sage.com/browse/XT-58528

        And the user selects the "Sold-to customer" labelled reference field on the main page
        # Then the ""Customer on hold Credit limit £ 0" validation warning message of the reference field is displayed
        # Then the "Customer on hold" validation warning message of the reference field is displayed

        # Add a line for non stock item
        And the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Journal_Service_Item_002" in the reference field
        And the user selects "Journal_Service_Item_002" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user presses Enter

        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10.00" in the numeric field
        And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page

        # Verify Creation
        Then a toast containing text "Record created" is displayed

        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SO2_Number]"

        # Update by Adding a new line for stock item
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Journal_Stock_Item_002" in the reference field
        And the user selects "Journal_Stock_Item_002" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user presses Enter

        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "20.00" in the numeric field
        And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Testing for this in this scenario
        Then a toast with text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 12 - Confirm a Sales Order for an on hold customer with BLOCKING behavior

        # Testing for this in this scenario
        # Confirm sales order details
        When the user clicks the "Confirm" labelled business action button on the main page
        #Verify Error validation message
        Then a error toast containing text "The bill-to customer is on hold." is displayed

    # Change the Customer to !onHold Confirm then put back on hold

    Scenario: 13 - Update a Sales Order for an on hold customer with BLOCKING behavior while in Confirmed status (Using another SO)

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        And the user selects the "Sales orders" labelled table field on the main page
        # Find and Select Document
        And the user filters the "Number" labelled column in the table field with value "SO_OnHold_Blocking_S03"
        And the user selects the row with text "SO_OnHold_Blocking_S03" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SO3_Number]"

        # Update by Adding a new line for stock item
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Journal_Stock_Item_002" in the reference field
        And the user selects "Journal_Stock_Item_002" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And the user presses Enter

        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "20.00" in the numeric field
        And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page

        # Testing for this in this scenario
        Then the dialog title is "Customer on hold"
        And the text in the body of the dialog is "You are about to update a confirmed sales order for an on hold customer." on the main page
        When the user clicks the "Continue" button of the Confirm dialog
        # Verify Confirmation
        Then a toast with text "Record updated" is displayed

    Scenario: 14 - Allocate Sales order line item stock for an on hold customer with BLOCKING behavior

        # Select stock item line
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal_Stock_Item_002" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field

        And the user selects the "Stock allocation" labelled table field on a modal
        And the user selects the row with text "0 each" in the "Quantity to allocate" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        Then the user clicks the "Save" labelled business action button on a modal

    # Testing for this in this scenario
    # Nothing different should happen, Should allow allocating

    Scenario: 15 - Create a PO action from a Sales Order line for an on hold customer with BLOCKING behavior

        # Select stock item line
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal_Stock_Item_002" in the "Item" labelled column header of the table field

        # Testing for this in this scenario
        And the user clicks the "Create purchase order" dropdown action of the selected row of the table field

        # Verify Blocking Error validation message
        Then a error toast containing text "The bill-to customer is on hold." is displayed

    Scenario: 16 - Assign an order from a Sales Order line for an on hold customer with BLOCKING behavior

        # Select stock item line
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal_Stock_Item_002" in the "Item" labelled column header of the table field

    # Testing for this in this scenario
    # TODO: ATP Enhancement request : https://jira.sage.com/browse/XT-58990
    # And the "Assign order" dropdown action of the selected row in the table field is hidden
    # And the "Assign order" dropdown action of the selected row in the table field is disabled

    Scenario: 17 - Allocate stock from the header of the Sales Order document for an on hold customer with BLOCKING behavior

        Given the user refreshes the screen

        # Testing for this in this scenario, User must still be able to Allocate stock from Header action
        When the user clicks the "Allocate stock" labelled more actions button in the header

        Then the dialog title is "Allocation request submitted"
        And the text in the body of the dialog is "The allocation request was submitted." on the main page
        When the user clicks the "OK" button of the dialog on the main page

        Then the "Sales order SO_OnHold_Blocking_S03" titled page is displayed

    Scenario: 18 - Deallocate stock from the header of the Sales Order document for an on hold customer with BLOCKING behavior

        Given the user refreshes the screen
        # Testing for this in this scenario, User must still be able to Dellocate stock from Header action
        When the user clicks the "Deallocate stock" labelled more actions button in the header

        Then the dialog title is "Deallocation request submitted"
        And the text in the body of the dialog is "The deallocation request was submitted." on the main page
        When the user clicks the "OK" button of the dialog on the main page

        Then the "Sales order SO_OnHold_Blocking_S03" titled page is displayed

    Scenario: 19 - On Ship Business action of Sales Order for an on hold customer with BLOCKING behavior

        Given the user refreshes the screen
        # Ship the Sales Order
        When the user clicks the "Create shipment" labelled business action button on the main page
        # Verify Blocking Error validation message
        Then a toast containing text "The bill-to customer is on hold." is displayed

    Scenario: 20 Cleanup to reset Onhold customer

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        # Find the customer and verify readonly
        When the user selects the "Customers" labelled table field on the main page
        And the user selects the row with text "Onhold_Customer" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        When selects the "Financial" labelled navigation anchor on the main page

        # Verify Credit limit read-only
        And the user selects the "Credit limit" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user stores the value of the numeric field with the key "[ENV_Cus_Credit_Limit]"
        And the user presses Tab
        # Verify on hold read-only
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user waits for 2 seconds

        And the user clicks the "Remove on hold" labelled more actions button in the header

        When the user selects the "On hold" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        # Verify Credit limit
        When the user selects the "Credit limit" labelled numeric field on the main page
        Then the value of the numeric field is "[ENV_Cus_Credit_Limit].000"
