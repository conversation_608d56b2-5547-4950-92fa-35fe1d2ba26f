
# When entering payment receipts for sales invoices and credit memos on the 'Record Receipt' page,
# users can input payments in a currency different from the bank account currency.
# They can now enter amounts in both the transaction and bank currencies.
# This test verifies that the 'Amount in Bank Currency' field is correctly displayed or hidden based on
# company, transaction (invoice), and bank currency combinations.
# It also ensures the system validates user-entered bank currency amounts against FX rate-based calculations
# and triggers information messages for discrepancies if applicable.
# IMPORTANT: Intacct configuration status has to be not active (isIntacctServiceOptionActive in @sage/xtrem-structure/OptionManagementBase)
# IMPORTANT: Payment Tracking option status has to be active (paymentTrackingOption in @sage/xtrem-system/ServiceOptionState)
# Prerequisites: prerequisites-flow-verify-options-status

@finance
Feature: finance-flow-ar-payment-tracking-payment-amount-line-in-bank-currency

    Scenario: 01 - Sales Invoices & Credit Memos in Company Currency - generate receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/RecordReceipt"
        Then the "Record receipt" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Customer" labelled reference field on the main page
        And the user writes "Schmitt Apparatebau GmbH" in the reference field
        And the user selects "Schmitt Apparatebau GmbH" in the reference field
        And the user selects the "Bank account" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And the "Amount in bank currency" labelled numeric field on the main page is hidden
        And the user selects the "Payment method" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "1800" in the numeric field
        And the user selects the "Transaction information" labelled text field on the main page
        And the user writes "AR bank currency" in the text field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SIDE250007" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "SCDE250003" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "SIDE250006" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Generate" labelled business action button on the main page
        And the text in the header of the dialog is "Receipts"
        And the "Amount in bank currency" labelled numeric field on a modal is hidden
        And the user clicks the "Confirm" labelled business action button on a modal
        And the user waits for 2 seconds
        Then a toast containing text "The following receipt was created:" is displayed

    Scenario: 02 - Sales Invoices & Credit Memos in Company Currency - verify receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Receipt"
        Then the "Receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AR bank currency" in the "Reference" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested link field of the selected row in the table field with the key "[ENV_ReceiptNumberARPT]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Receipt [ENV_ReceiptNumberARPT]" titled page is displayed
        And the "Amount in bank currency" labelled numeric field on the main page is hidden
        And the user selects the "lines" bound table field on the main page
        And the "Amount in bank currency" labelled column in the table field is hidden

    Scenario: 03 - Sales Invoices & Credit Memos in a Different Currency from Company Currency - generate receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/RecordReceipt"
        Then the "Record receipt" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Customer" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field
        And the user selects the "Bank account" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And a toast containing text "The chosen currency is different to the currency associated with the bank account." is displayed
        And the user dismisses all the toasts
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "Payment method" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "1800" in the numeric field
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1600" in the numeric field
        And a toast containing text "The bank currency and calculated amounts are different. Bank amount: €1600, calculated amount: (€1500)." is displayed
        And the user dismisses all the toasts
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1500" in the numeric field
        And no error toast or validation error message is displayed
        And the user selects the "Transaction information" labelled text field on the main page
        And the user writes "AR bank currency 2" in the text field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SIDE250008" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "SCDE250002" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "SIDE250005" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Generate" labelled business action button on the main page
        And the text in the header of the dialog is "Receipts"
        And the "Amount in bank currency" labelled numeric field on a modal is displayed
        And the user selects the "Amount in bank currency" labelled numeric field on a modal
        And the value of the numeric field is "1,500.00"
        And the user selects the "lines" bound table field on a modal
        # @todo "Amount in bank currency" value is affected by the penalty ammount. Refactor data to use a payment term with no penalty
        # And the user selects the row with text "SIDE250008" in the "Document number" labelled column header of the table field
        # And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "€ 1,500.00"
        # And the user selects the row with text "SCDE250002" in the "Document number" labelled column header of the table field
        # And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "€ -1,500.00"
        # And the user selects the row with text "SIDE250005" in the "Document number" labelled column header of the table field
        # And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "€ 1,500.00"
        And the user clicks the "Confirm" labelled business action button on a modal
        Then a toast containing text "The following receipt was created:" is displayed

    Scenario: 04 - Sales Invoices & Credit Memos in a Different Currency from Company Currency - verify receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/Receipt"
        Then the "Receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AR bank currency 2" in the "Reference" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested link field of the selected row in the table field with the key "[ENV_ReceiptNumberARPT2]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Receipt [ENV_ReceiptNumberARPT2]" titled page is displayed
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "lines" bound table field on the main page
        And the "Amount in bank currency" labelled column in the table field is displayed

    Scenario: 05 - Three Different Currencies (Invoice, Company, and Bank Account) - generate receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/RecordReceipt"
        Then the "Record receipt" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Customer" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field
        And the user selects the "Bank account" labelled reference field on the main page
        And the user writes "Bank Germany GBP" in the reference field
        And the user selects "Bank Germany GBP" in the reference field
        And a toast containing text "The chosen currency is different to the currency associated with the bank account." is displayed
        And the user dismisses all the toasts
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "Payment method" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "-1800" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
        The payment amount needs to be greater than or equal to 0.
            """
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user presses Tab
        Then a validation error message is displayed containing text
            """
        The amount in bank currency needs to be greater than 0.
            """
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page
        And the table field is empty
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "1800" in the numeric field
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1600" in the numeric field
        And a toast containing text "The bank currency and calculated amounts are different. Bank amount: £1600, calculated amount: (£1440)." is displayed
        And the user dismisses all the toasts
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1440" in the numeric field
        And no error toast or validation error message is displayed
        And the user selects the "Transaction information" labelled text field on the main page
        And the user writes "AR bank currency 3" in the text field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SIDE250008" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "SCDE250002" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "SIDE250005" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Generate" labelled business action button on the main page
        And the text in the header of the dialog is "Receipts"
        And the "Amount in bank currency" labelled numeric field on a modal is displayed
        And the user selects the "Amount in bank currency" labelled numeric field on a modal
        And the value of the numeric field is "1,440.00"
        And the user selects the "lines" bound table field on a modal
        # @todo "Amount in bank currency" value is affected by the penalty ammount. Refactor data to use a payment term with no penalty
        # And the user selects the row with text "SIDE250008" in the "Document number" labelled column header of the table field
        # And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "£ 1,440.00"
        # And the user selects the row with text "SCDE250002" in the "Document number" labelled column header of the table field
        # And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "£ -960.00"
        # And the user selects the row with text "SIDE250005" in the "Document number" labelled column header of the table field
        # And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "£ 960.00"
        And the user clicks the "Confirm" labelled business action button on a modal
        Then a toast containing text "The following receipt was created:" is displayed

    Scenario: 06 - Three Different Currencies (Invoice, Company, and Bank Account) - verify receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/Receipt"
        Then the "Receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AR bank currency 3" in the "Reference" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested link field of the selected row in the table field with the key "[ENV_ReceiptNumberARPT3]"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Receipt [ENV_ReceiptNumberARPT3]" titled page is displayed
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "lines" bound table field on the main page
        And the "Amount in bank currency" labelled column in the table field is displayed
