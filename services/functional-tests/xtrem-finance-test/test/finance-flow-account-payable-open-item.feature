# The goal of this test is to verify that a payment invoice and a credit memo created from payment invoice
# are displayed in accounts payable page
# Prerequisites: prerequisites-flow-verify-options-status

@finance
Feature: finance-flow-account-payable-open-item

    Scenario: 01 - Purchase invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in fields on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI AccountsR" in the text field

        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from receipts" labelled add action of the table field

        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "PR240015" in the "Receipt number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field

        And the user clicks the "Select" button of the Lookup dialog
        Then the user clicks the "Save" labelled business action button on the main page

        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI AccountsR" in the text field

        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "144" in the numeric field

        And the user selects the "Total supplier tax" labelled numeric field on the main page
        And the user writes "27.36" in the numeric field

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        # Save the PI number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_INVNUM01]"

        When the user clicks the "post" labelled business action button on the main page
        Then the user clicks the "Post" button of the Confirm dialog

        And the user waits 5 seconds

        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the dialog title is "Create purchase credit memo"

        And the user selects the "Reason *" labelled reference field on a modal
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field

        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"

        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes "144" in the numeric field

        And the user clicks the "Create" button of the dialog on the main page
        Then a toast containing text "Purchase credit memo created." is displayed

        And the user waits 2 seconds
        And the user selects the "Total credit memo tax" labelled numeric field on the main page
        And the user writes "27.36" in the numeric field

        And the user selects the "Supplier document date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        When the user clicks the "post" labelled business action button on the main page
        Then the user clicks the "Post" button of the Confirm dialog
        # Save the PC number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PCMUM01]"

    Scenario: 02 - Verify purchase invoice on account payable page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItem"
        Then the "Accounts payable open items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Document number" labelled column in the table field with value "[ENV_INVNUM01]"
        And the user filters the "Document type" labelled column in the table field with value "Purchase invoice"
        And the user selects the row 1 of the table field
        And the user clicks the "Document number" labelled nested field of the selected row in the table field

        When the user selects the "Document type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Purchase invoice"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Sandfeld"

        When the user selects the "Supplier" labelled reference field on the main page
        Then the value of the reference field is "MUHOMA Technology GmbH"

        When the user selects the "Transaction amount due" labelled numeric field on the main page
        Then the value of the numeric field is "171.36"

        When the user selects the "Company amount due" labelled numeric field on the main page
        Then the value of the numeric field is "171.36"

    Scenario: 03 - Verify credit memo on account payable page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsPayableOpenItem"
        Then the "Accounts payable open items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Document number" labelled column in the table field with value "[ENV_PCMUM01]"
        And the user filters the "Document type" labelled column in the table field with value "Purchase credit memo"
        And the user selects the row 1 of the table field
        And the user clicks the "Document number" labelled nested field of the selected row in the table field

        When the user selects the "Document type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Purchase credit memo"
        # Financial site
        When the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Sandfeld"

        When the user selects the "Supplier" labelled reference field on the main page
        Then the value of the reference field is "MUHOMA Technology GmbH"

        When the user selects the "Transaction amount due" labelled numeric field on the main page
        Then the value of the numeric field is "-171.36"

        When the user selects the "Company amount due" labelled numeric field on the main page
        Then the value of the numeric field is "-171.36"
