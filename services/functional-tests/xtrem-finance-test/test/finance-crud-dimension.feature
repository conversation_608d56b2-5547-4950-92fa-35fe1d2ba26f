# The purpose of this test is to verify that user is able to create, update and delete a dimension

@finance
Feature: finance-crud-dimension

    Scenario: Verify the user is able to create a dimension

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Dimension"
        Then the "Dimensions" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "ID *" labelled text field on the main page
        Then the user writes "600" in the text field
        And the user selects the "Name *" labelled text field on the main page
        Then the user writes "New dimension" in the text field
        And the user selects the "Dimension type" labelled reference field on the main page
        Then the value of the reference field is "Department"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify the user is able to update a dimension

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Dimension"
        Then the "Dimensions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "600" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "New dimension" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name *" labelled text field on the main page
        And the user writes "Update dimension" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Verify the user is able to delete a dimension

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Dimension"
        Then the "Dimensions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "600" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Update dimension" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
