# The purpose of this test is to ensure the creation and correct execution of
# the Job Scheduler operation 'Create journals from accounting staging job',
# thus ensuring that the operation defined by our users is executed without errors
# and converting transactions contained in the accounting staging table into (financial) journal entries.
# We aim to confirm that the Job scheduler operation 'Create journals from accounting staging job'
# can be entered and executed without an error and that the subsequent creation of journal entries is carried out successfully.

@finance
Feature: finance-flow-job-scheduler-create-journals-from-accounting-staging

    Scenario: 01 - Create Job schedule operation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-scheduler/SysJobSchedule"
        Then the "Jobs schedule" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the dialog title is "New batch task" on the main page
        And the user selects the "jobSchedule" bound radio field on a modal
        And the user selects the value "Run now" in the radio field
        And the value "Run now" of the radio field is selected
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "jobDefinition" labelled reference field on a modal
        And the user writes "Create journals from accounting staging job" in the reference field
        And the user selects "Create journals from accounting staging job" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Parameters" labelled table field on a modal
        And the user selects the row with text "journalsCreatedData" in the "Parameter" labelled column header of the table field
        And the user writes "true" in the "value" bound nested text field of the selected row in the table field
        And the user selects the row with text "filter" in the "Parameter" labelled column header of the table field
        And the user writes '{"_and":[{"documentDate":{"_gte":"2025-05-23"}},{"_or":[{"documentType":{"_eq":"miscellaneousStockReceipt"}}]},{"financialSite":{"id":{"_eq":"D1S"}}}]}' in the "value" bound nested text field of the selected row in the table field
        And the user clicks the "Finish" labelled business action button on a modal
        And a toast containing text "Batch task started" is displayed
    # it is not reliable to check the notification card because on some environments the notifications are disabled
    # Notification 'Journal entry generation batch task successful' appears

    Scenario: 02 -  Validate Job schedule operation
        Then the "Batch task history" titled page is displayed
        And the user refreshes the screen
        And the user selects the "status" bound label field on the main page
        And the value of the label field is "Success"
        And selects the "Logs" labelled navigation anchor on the main page
        And the user selects the "result" bound label field on the main page
        And the value of the label field is ""\"Journals created: 1 \\n \\n IJ250001\"""
