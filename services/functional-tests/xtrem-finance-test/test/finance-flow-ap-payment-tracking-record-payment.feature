# The purpose of the test is to select for AP open item on the Record payment page according to the search criteria entered
# resp. ensuring data integrity during the selection of the search criteria and the application of a payment amount.
# We aim to confirm that entering selection criteria, the subsequent open item selection and deselection
# and the calculation of amounts work as expected and follow the rules defined.
# - opening the Record payment page
# - selecting criteria and ensure its data integrity
# - presenting the correct open items that fullfill the search criteria in the result grid
# - selecting and unselecting AP open items in the result grid
# - entering 'Payment amount' of the header and applying to the 'Payment resp. Credit amount' fields of the lines
# - correctly calculating the 'Total payment applied' and 'Amount available to apply'
# IMPORTANT: Intacct configuration status has to be not active (isIntacctServiceOptionActive in @sage/xtrem-structure/OptionManagementBase)
# IMPORTANT: Payment Tracking option status has to be active (paymentTrackingOption in @sage/xtrem-system/ServiceOptionState)
# IMPORTANT: Bank account available for financial site of test company
# Prerequisites: prerequisites-flow-verify-options-status

@finance
Feature: finance-flow-ap-payment-tracking-record-payment

    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario Outline: 00 - Data creation -Purchase invoices
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes <Quantity> in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Variance status updated" is displayed
        And the user dismisses all the toasts
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "No penalty no discount" in the reference field
        And the user selects "No penalty no discount" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes <AmountNoTax> in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes <Tax> in the numeric field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The purchase invoice was posted." is displayed
        Examples:
            | Quantity | EnvName            | AmountNoTax | Tax  |
            | "30"     | "[ENV_PIAP_NUM01]" | "300"       | "57" |
            | "20"     | "[ENV_PIAP_NUM02]" | "200"       | "38" |
            | "40"     | "[ENV_PIAP_NUM03]" | "400"       | "76" |

    Scenario: 01 - Open the Record payment page and enter the selection criteria
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/RecordPayment"
        Then the "Record payment" titled page is displayed
        When the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Supplier *" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        And the user selects the "Bank account *" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And the user selects the "Payment method *" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Currency *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        And the user selects the "Payment amount *" labelled numeric field on the main page
        And the user writes "1000" in the numeric field
        And the user selects the "Transaction information" labelled text field on the main page
        And the user writes "AP payment tracking - Record payment" in the text field
        And the user clicks in the "searchButton" bound button field on the main page

    Scenario: 02 - Verify that the open items from the result grid fullfill the search criteria
        And the user selects the "lines" bound table field on the main page
        And the table field is not empty
        And the user selects the row with text "[ENV_PIAP_NUM01]" in the "Invoice number" labelled column header of the table field
        And the user selects the row with text "[ENV_PIAP_NUM02]" in the "Invoice number" labelled column header of the table field
        And the user selects the row with text "[ENV_PIAP_NUM03]" in the "Invoice number" labelled column header of the table field

    Scenario: 03 - Selecting and unselecting AP open items in the result grid
        And the user selects the "lines" bound table field on the main page
        And the table field is not empty
        And the user selects the row with text "[ENV_PIAP_NUM01]" in the "Invoice number" labelled column header of the table field
        And the selected row of the table field is unselected
        And the user ticks the main checkbox of the selected row in the table field
        And the selected row of the table field is selected
        And the user unticks the main checkbox of the selected row in the table field
        And the selected row of the table field is unselected

    Scenario: 04 - Verify the calculation of 'Total payment applied' and 'Amount available to apply' tiles and of amounts at the row level
        And the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€1,000.00"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€0.00"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "[ENV_PIAP_NUM01]" in the "Invoice number" labelled column header of the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 357.00"
        And the user ticks the main checkbox of the selected row in the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€ 357.00"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 357.00"
        And the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€643.00"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€357.00"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "[ENV_PIAP_NUM02]" in the "Invoice number" labelled column header of the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 238.00"
        And the user ticks the main checkbox of the selected row in the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€ 238.00"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 238.00"
        And the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€405.00"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€595.00"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "[ENV_PIAP_NUM03]" in the "Invoice number" labelled column header of the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 476.00"
        And the user ticks the main checkbox of the selected row in the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€ 405.00"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 476.00"
        And the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€0.00"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€1,000.00"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "[ENV_PIAP_NUM03]" in the "Invoice number" labelled column header of the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€ 405.00"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 476.00"
        And the user unticks the main checkbox of the selected row in the table field
        And the value of the "Payment amount" labelled nested numeric field of the selected row in the table field is "€"
        And the value of the "Total amount" labelled nested numeric field of the selected row in the table field is "€ 476.00"
        And the user selects the "AMOUNT AVAILABLE TO APPLY" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€405.00"
        And the user selects the "TOTAL PAYMENT APPLIED" labelled tile numeric field on the main page
        And the value of the tile numeric field is "€595.00"
