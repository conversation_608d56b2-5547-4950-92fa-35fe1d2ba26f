
# When entering payment made for purchase invoices and credit memos on the 'Record payment' page,
# users can input payments in a currency different from the bank account currency.
# They can now enter amounts in both the transaction and bank currencies.
# This test verifies that the 'Amount in Bank Currency' field is correctly displayed or hidden
# based on company, transaction (invoice), and bank currency combinations.
# It also ensures the system validates user-entered bank currency amounts against FX rate-based calculations
# and triggers information messages for discrepancies if applicable.
# IMPORTANT: Bank accounts in EUR and GBP available for financial site of test company
# IMPORTANT: Currency rate EUR to USD exists (1 EUR = 1.20 USD)
# IMPORTANT: Currency rate GBP to USD exists (1 GBP= 1.25 USD)
# IMPORTANT: Intacct configuration status has to be not active (isIntacctServiceOptionActive in @sage/xtrem-structure/OptionManagementBase)
# IMPORTANT: Payment Tracking option status has to be active (paymentTrackingOption in @sage/xtrem-system/ServiceOptionState)
# prerequisite: prerequisites-flow-verify-options-status

@finance
Feature: finance-flow-ap-payment-tracking-payment-amount-line-in-bank-currency
    # !IMPORTANT - because of some qa data layer issues with IDs constantly changing on layer reload,
    # we need to create the data from here, at least for the moment
    Scenario Outline: 0.01 - Data creation - Purchase invoices
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes <Supplier> in the reference field
        And the user selects <Supplier> in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DEItemAR" in the reference field
        And the user selects "DEItemAR" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes <Quantity> in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Variance status updated" is displayed
        And the user dismisses all the toasts
        And selects the "Payments" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "No penalty no discount" in the reference field
        And the user selects "No penalty no discount" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes <AmountNoTax> in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes <Tax> in the numeric field
        And the user presses Enter
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The purchase invoice was posted." is displayed
        Examples:
            | Quantity | EnvName              | AmountNoTax | Tax   | Supplier                  |
            | "300"    | "[ENV_PIAPBC_NUM01]" | "3000"      | "570" | "MUHOMA Technology GmbH"  |
            | "200"    | "[ENV_PIAPBC_NUM02]" | "2000"      | "380" | "MUHOMA Technology GmbH"  |
            | "100"    | "[ENV_PIAPBC_NUM03]" | "1000"      | "190" | "MUHOMA Technology GmbH"  |
            | "400"    | "[ENV_PIAPBC_NUM04]" | "4000"      | "0"   | "Stock Tranfer US Site 1" |
            | "300"    | "[ENV_PIAPBC_NUM05]" | "3000"      | "0"   | "Stock Tranfer US Site 1" |

    Scenario Outline: 0.02 - Data creation - Purchase credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "number" bound column in the table field with value <DocName>
        And the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is <DocName>
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the <PageTitle> titled page is displayed
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the dialog title is "Create purchase credit memo"
        And the user selects the "Reason *" labelled reference field on a modal
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field
        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes <AmountNoTax> in the numeric field
        And the user clicks the "Create" button of the dialog on the main page
        And a toast containing text "Purchase credit memo created." is displayed
        And the user selects the "Total credit memo tax" labelled numeric field on the main page
        And the user writes <Tax> in the numeric field
        And the user clicks the "Post" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        And a toast containing text "The purchase credit memo was posted." is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key <EnvName>
        Examples:
            | EnvName               | DocName              | PageTitle                             | AmountNoTax | Tax   |
            | "[ENV_PCMAPBC_NUM01]" | "[ENV_PIAPBC_NUM03]" | "Purchase invoice [ENV_PIAPBC_NUM03]" | "1000"      | "190" |
            | "[ENV_PCMAPBC_NUM02]" | "[ENV_PIAPBC_NUM05]" | "Purchase invoice [ENV_PIAPBC_NUM05]" | "3000"      | "0"   |

    Scenario: 01 - Purchase Invoices & Credit Memos in Company Currency - generate payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/RecordPayment"
        Then the "Record payment" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "MUHOMA Technology GmbH" in the reference field
        And the user selects "MUHOMA Technology GmbH" in the reference field
        And the user selects the "Bank account" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And the "Amount in bank currency" labelled numeric field on the main page is hidden
        And the user selects the "Payment method" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "1800" in the numeric field
        And the user selects the "transactionInformation" labelled text field on the main page
        And the user writes "AP bank currency 1" in the text field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page
        When the user filters the "Invoice number" labelled column in the table field with value "[ENV_PIAPBC_NUM03]"
        When the user filters the "Invoice number" labelled column in the table field with value "[ENV_PCMAPBC_NUM01]"
        When the user filters the "Invoice number" labelled column in the table field with value "[ENV_PIAPBC_NUM02]"
        And the user selects the row with text "[ENV_PIAPBC_NUM03]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PCMAPBC_NUM01]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPBC_NUM02]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Generate" labelled business action button on the main page
        And the text in the header of the dialog is "Payments"
        And the "Amount in bank currency" labelled numeric field on a modal is hidden
        And the user clicks the "Confirm" labelled business action button on a modal

    Scenario: 02 - Purchase Invoices & Credit Memos in Company Currency - verify payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/Payment"
        Then the "Payments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AP bank currency 1" in the "Reference" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Amount in bank currency" labelled numeric field on the main page is hidden
        And the user selects the "lines" bound table field on the main page
        And the "Amount in bank currency" labelled column in the table field is hidden

    Scenario: 03 - Purchase Invoices & Credit Memos in a Different Currency from Company Currency - generate payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/RecordPayment"
        Then the "Record payment" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "Stock Tranfer US Site 1" in the reference field
        And the user selects "Stock Tranfer US Site 1" in the reference field
        And the user selects the "Bank account" labelled reference field on the main page
        And the user writes "Bank Germany EUR" in the reference field
        And the user selects "Bank Germany EUR" in the reference field
        And a toast containing text "The chosen currency is different to the currency associated with the bank account." is displayed
        And the user dismisses all the toasts
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "Payment method" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "1800" in the numeric field
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1600" in the numeric field
        And a toast containing text "The bank currency and calculated amounts are different. Bank amount: €1600, calculated amount: (€1500)." is displayed
        And the user dismisses all the toasts
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1500" in the numeric field
        And no error toast or validation error message is displayed
        And the user selects the "transactionInformation" labelled text field on the main page
        And the user writes "AP bank currency 2" in the text field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page

        Then the user opens the filter of the "Invoice number" labelled column in the table field
        And the user searches "[ENV_PIAPBC_NUM04]" in the filter of the table field
        And the user ticks the item with text "[ENV_PIAPBC_NUM04]" in the filter of the table field
        And the user searches "[ENV_PCMAPBC_NUM02]" in the filter of the table field
        And the user ticks the item with text "[ENV_PCMAPBC_NUM02]" in the filter of the table field
        And the user searches "[ENV_PIAPBC_NUM05]" in the filter of the table field
        And the user ticks the item with text "[ENV_PIAPBC_NUM05]" in the filter of the table field
        Then the user closes the filter of the "Invoice number" labelled column in the table field

        And the user selects the row with text "[ENV_PIAPBC_NUM04]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PCMAPBC_NUM02]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPBC_NUM05]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Generate" labelled business action button on the main page
        And the text in the header of the dialog is "Payments"
        And the "Amount in bank currency" labelled numeric field on a modal is displayed
        And the user selects the "Amount in bank currency" labelled numeric field on a modal
        And the value of the numeric field is "1,500.00"
        And the user selects the "lines" bound table field on a modal
        And the user selects the row with text "[ENV_PIAPBC_NUM04]" in the "Document number" labelled column header of the table field
        And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "€ 1,500.00"
        And the user selects the row with text "[ENV_PCMAPBC_NUM02]" in the "Document number" labelled column header of the table field
        And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "€ -1,500.00"
        And the user selects the row with text "[ENV_PIAPBC_NUM05]" in the "Document number" labelled column header of the table field
        And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "€ 1,500.00"
        And the user clicks the "Confirm" labelled business action button on a modal

    Scenario: 04 - Purchase Invoices & Credit Memos in a Different Currency from Company Currency - verify payment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/Payment"
        Then the "Payments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AP bank currency 2" in the "Reference" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "lines" bound table field on the main page
        And the "Amount in bank currency" labelled column in the table field is displayed

    Scenario: 05 - Three Different Currencies (Invoice, Company, and Bank Account) - generate payment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/RecordPayment"
        Then the "Record payment" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "Stock Tranfer US Site 1" in the reference field
        And the user selects "Stock Tranfer US Site 1" in the reference field
        And the user selects the "Bank account" labelled reference field on the main page
        And the user writes "Bank Germany GBP" in the reference field
        And the user selects "Bank Germany GBP" in the reference field
        And a toast containing text "The chosen currency is different to the currency associated with the bank account." is displayed
        And the user dismisses all the toasts
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "Payment method" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "EFT" in the dropdown-list field
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "-1800" in the numeric field
        And the user presses Tab
        And a validation error message is displayed containing text
            """
        The payment amount needs to be greater than or equal to 0.
            """
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "0" in the numeric field
        And the user presses Tab
        And a validation error message is displayed containing text
            """
        The amount in bank currency needs to be greater than 0.
            """
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page
        And the table field is empty
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the user writes "1800" in the numeric field
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1600" in the numeric field
        And a toast containing text "The bank currency and calculated amounts are different. Bank amount: £1600, calculated amount: (£1440)." is displayed
        And the user dismisses all the toasts
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the user writes "1440" in the numeric field
        And no error toast or validation error message is displayed
        And the user selects the "transactionInformation" labelled text field on the main page
        And the user writes "AP bank currency 3" in the text field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "lines" bound table field on the main page

        Then the user opens the filter of the "Invoice number" labelled column in the table field
        And the user searches "[ENV_PIAPBC_NUM04]" in the filter of the table field
        And the user ticks the item with text "[ENV_PIAPBC_NUM04]" in the filter of the table field
        And the user searches "[ENV_PCMAPBC_NUM02]" in the filter of the table field
        And the user ticks the item with text "[ENV_PCMAPBC_NUM02]" in the filter of the table field
        And the user searches "[ENV_PIAPBC_NUM05]" in the filter of the table field
        And the user ticks the item with text "[ENV_PIAPBC_NUM05]" in the filter of the table field
        Then the user closes the filter of the "Invoice number" labelled column in the table field

        And the user selects the row with text "[ENV_PIAPBC_NUM04]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PCMAPBC_NUM02]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row with text "[ENV_PIAPBC_NUM05]" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Generate" labelled business action button on the main page
        And the text in the header of the dialog is "Payments"
        And the "Amount in bank currency" labelled numeric field on a modal is displayed
        And the user selects the "Amount in bank currency" labelled numeric field on a modal
        And the value of the numeric field is "1,440.00"
        And the user selects the "lines" bound table field on a modal
        And the user selects the row with text "[ENV_PIAPBC_NUM04]" in the "Document number" labelled column header of the table field
        And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "£ 1,440.00"
        And the user selects the row with text "[ENV_PCMAPBC_NUM02]" in the "Document number" labelled column header of the table field
        And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "£ -960.00"
        And the user selects the row with text "[ENV_PIAPBC_NUM05]" in the "Document number" labelled column header of the table field
        And the value of the "Amount in bank currency" labelled nested numeric field of the selected row in the table field is "£ 960.00"
        And the user clicks the "Confirm" labelled business action button on a modal

    Scenario: 06 - Three Different Currencies (Invoice, Company, and Bank Account) - verify payment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/Payment"
        Then the "Payments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Reference" name on the sidebar
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row with text "AP bank currency 3" in the "Reference" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Amount in bank currency" labelled numeric field on the main page is displayed
        And the user selects the "Amount in bank currency" labelled numeric field on the main page
        And the value of the numeric field is "1,440.00"
        And the "Payment amount" labelled numeric field on the main page is displayed
        And the user selects the "Payment amount" labelled numeric field on the main page
        And the value of the numeric field is "1,800.00"
        And the user selects the "lines" bound table field on the main page
        And the "Adjustment amount" labelled column in the table field is displayed
        And the "Amount in bank currency" labelled column in the table field is displayed
