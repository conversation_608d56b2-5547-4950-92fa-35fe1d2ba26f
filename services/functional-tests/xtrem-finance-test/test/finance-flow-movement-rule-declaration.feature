# The purpose of this test is to test the flow of the movement rule page. Movement rule are vendor protected.
# Therefore It is not possible for the user to create new movement rules, update or delete a movement rule.
# The only possiblity is to activate or deactivate a movement rule
@finance

Feature: finance-flow-movement-rule-declaration

    Scenario: Open the Movement rules Page and verify the record details
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/MovementRule"
        Then the "Movement rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        When the user searches for "Purchase receipt" in the navigation panel
        And the user clicks the record with the text "Purchase receipt" in the navigation panel
        # Verify record details is as expected
        Then the "Movement rule Purchase receipt" titled page is displayed
        When the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "ON"
        When the user selects the "Document type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Purchase receipt"
        When the user selects the "Statistical procedure" labelled reference field on the main page
        Then the value of the reference field is "Final arrivals"
        When the user selects the "Flow" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Arrival"



    Scenario: Check that the Movement rules page details is not able to be updated and changed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/MovementRule"
        Then the "Movement rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        When the user searches for "Purchase receipt" in the navigation panel
        And the user clicks the record with the text "Purchase receipt" in the navigation panel
        # Test if the record is not able to be updated or changed
        When the user selects the "Nature of transaction" labelled reference field on the main page
        And the user clears the reference field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "Nature of transaction" labelled reference field on the main page
        And the user clicks in the reference field
        Then the "MovementRule.natureOfTransaction: Vendor protected properties cannot be assigned a value." validation error message of the reference field is displayed



    Scenario: Check that the movement rule is able to be enabled and disabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/MovementRule"
        Then the "Movement rules" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        When the user searches for "Purchase receipt" in the navigation panel
        And the user clicks the record with the text "Purchase receipt" in the navigation panel
        When the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "ON"
        When the user turns the switch field "OFF"
        Then the switch field is set to "OFF"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: Confirm that the Movement rule was deactivated
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-declarations/MovementRule"
        Then the "Movement rules" titled page is displayed
        # Search for record
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Document type" labelled nested field of the selected row in the table field
        When the user searches for "Purchase receipt" in the navigation panel
        And the user clicks the record with the text "Purchase receipt" in the navigation panel
        # Verify record Active switch
        When the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "OFF"
        # Restore back to original value
        When the user turns the switch field "ON"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
