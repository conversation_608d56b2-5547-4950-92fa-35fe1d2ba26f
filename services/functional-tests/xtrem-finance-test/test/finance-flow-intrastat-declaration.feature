# The purpose of this test is to test whether the complete flow of an Intrastat declaration record is possible
# Full analysis: https://confluence.sage.com/pages/viewpage.action?pageId=309106974
@finance
Feature: finance-flow-intrastat-declaration

    Scenario: Open the Intrastat Declaration Page and create a new extraction criteria record
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-declarations/IntrastatDeclaration"
        Then the "Intrastat declarations" titled page is displayed
        When the user selects the "Intrastat declarations" labelled table field on the main page
        # This test requires that this record table is initially empty to potentially avoid test failure due to this bug for now: https://jira.sage.com/browse/XT-52835
        Then the table field is empty
        # Create the new record
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Intrastat declaration" titled page is displayed
        When the user selects the "Company" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field
        # Selects last day of the current system month dynamically (NB: There is a validation check on the Period that the date must always be the last date of any month)
        And the user selects the "Period" labelled date field on the main page
        # Date is specific to Intrastat records to be generated from preloaded QA data
        And the user writes a generated date in the date field with value "08/31/2023"
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Check that the status is correct, note there is no field label to read.
        When the user selects the "status" bound label field on the main page
        Then the value of the label field is "Recorded"
        # Test the flow of the Intrastat to see if records show up as from existing QA data
        When the user selects the "Results" labelled table field on the main page
        Then the table field is empty
        When the user clicks the "Generate" labelled business action button on the main page
        # Ensure that the table has been generated with records
        And the user selects the "Results" labelled table field on the main page
        Then the table field is not empty

    Scenario: Open the Intrastat declaration page and view/read the record
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-declarations/IntrastatDeclaration"
        Then the "Intrastat declarations" titled page is displayed
        # Search and select the record
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Company" labelled nested field of the selected row in the table field
        And the user searches for "Société S1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Check that user is in status able to be editing the rows
        And the user selects the "status" bound label field on the main page
        Then the value of the label field is "Draft"
        When the user selects the "Results" labelled table field on the main page

        # XT-53031
        # Filter to find and ensure correctness of record
        # Filter by Document type and commodity code, ATP bug error here as Document type column is far right of table and not on "screen"
        And the user filters the "Document type" labelled column in the table field with value "Purchase receipt"

        And the user opens the filter of the "Statistical procedure" labelled column in the table field
        And the user searches "Final arrivals" in the filter of the table field
        And the user ticks the item with text "Final arrivals" in the filter of the table field
        When the user closes the filter of the "Statistical procedure" labelled column in the table field

        And the user filters the "Commodity code" labelled column in the table field with value "8544 30 10"

        # Test the values against QA data
        And the user selects the row 1 of the table field
        Then the value of the "Document number" labelled nested text field of the selected row in the table field is "PR230001"
        And the value of the "Weight(kg)" labelled nested text field of the selected row in the table field is "160.000 kg"
        And the value of the "Nature of transaction" labelled nested text field of the selected row in the table field is "Normal Exchange of Goods"
        And the value of the "Statistical procedure" labelled nested text field of the selected row in the table field is "Final arrivals"


    Scenario: Editing/changing of the Intrastat declaration data
        # Store the statistical procedure value to retest after regenerating
        And the user selects the row 1 of the table field
        When the user stores the value of the "Nature of transaction" labelled nested text field of the selected row in the table field with the key "[ENV-NatureOfTransaction01]"
        # Change the value of the statistical procedure field
        And the user selects the row with text "Final arrivals" in the "Statistical procedure" labelled column header of the table field
        And the user writes "Other shipments" in the "Statistical procedure" labelled nested reference field of the selected row in the table field
        And the user selects "Other shipments" in the "Statistical procedure" labelled nested field of the selected row in the table field
        # Save the changes
        Then the "Save" labelled business action button on the main page is enabled
        And the "Save" labelled business action button on the main page is visible
        When the user clicks the "Save" labelled business action button on the main page
        Then the value of the toast is "Record updated"
        # Regenerate the table
        When the user clicks the "Regenerate" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog

    Scenario: Regenerate the Intrastat and test if it updates the table
        # Refresh of the page to Reset the horizontal scroll of the table to bypass bug https://jira.sage.com/browse/XT-53031
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-declarations/IntrastatDeclaration"
        Then the "Intrastat declarations" titled page is displayed
        # Search and select the record
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Company" labelled nested field of the selected row in the table field
        And the user searches for "Société S1" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # Filter to find line record
        And the user selects the "Results" labelled table field on the main page

        # XT-53031
        # Filter to find and ensure correctness of record
        # Filter by Document type and commodity code, ATP bug error here as Document type column is far right of table and not on "screen"
        And the user filters the "Document type" labelled column in the table field with value "Purchase receipt"

        And the user opens the filter of the "Statistical procedure" labelled column in the table field
        And the user searches "Final arrivals" in the filter of the table field
        And the user ticks the item with text "Final arrivals" in the filter of the table field
        When the user closes the filter of the "Statistical procedure" labelled column in the table field

        And the user filters the "Commodity code" labelled column in the table field with value "8544 30 10"
        # Test if the change was reverted back to its original value on Regenerate
        And the user selects the row 1 of the table field
        Then the value of the "Nature of transaction" labelled nested text field of the selected row in the table field is "[ENV-NatureOfTransaction01]"



    Scenario: Open the Intrastat declaration page and delete the record
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-declarations/IntrastatDeclaration"
        Then the "Intrastat declarations" titled page is displayed
        #Search and select the record
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "Company" labelled nested field of the selected row in the table field
        When the user searches for "Société S1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Delete the record
        When the user clicks the "Delete" labelled more actions button in the header
        # ATP bug: Robot not detecting header text correctly
        #Then the text in the header of the dialog is "Confirm deletion"
        When the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    Scenario: Create an Intrastat declaration and validate it
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-declarations/IntrastatDeclaration"
        Then the "Intrastat declarations" titled page is displayed
        When the user selects the "Intrastat declarations" labelled table field on the main page
        # This test requires that this record table is initially empty to potentially avoid test failure due to this bug for now: https://jira.sage.com/browse/XT-52835
        Then the table field is empty
        # Create the new record
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Intrastat declaration" titled page is displayed
        When the user selects the "Company" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field
        # Selects last day of the current system month dynamically (NB: There is a validation check on the Period that the date must always be the last date of any month)
        And the user selects the "Period" labelled date field on the main page
        # Date is specific to Intrastat records to be generated from preloaded QA data
        And the user writes a generated date in the date field with value "08/31/2023"
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        When the user clicks the "Generate" labelled business action button on the main page
        # Ensure that the table has been generated with records
        And the user selects the "Results" labelled table field on the main page
        Then the table field is not empty
        # Validate the Intrastat
        When the user clicks the "Validate" labelled business action button on the main page
        Then the text in the header of the dialog is "Confirm validation"
        When the user clicks the "Confirm" button of the Confirm dialog
        And the user selects the "status" bound label field on the main page
        Then the value of the label field is "Validated"
