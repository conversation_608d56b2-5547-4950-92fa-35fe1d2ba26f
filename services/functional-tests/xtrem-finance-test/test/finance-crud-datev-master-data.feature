
# SDMO supports users in Germany with a DATEV export interface.
# With this (csv) file-based interface, users can transfer structured transactions and master data to DATEV accounting.
# A set of automated test have to be created to validate the different steps of this export process and the related master data settings.
# This tests focuses on additions to the structure of SDMO master data to include information that supports
# and simplifies the subsequent data collection and DATEV file creation.
# Prerequisite: DATEV service option is active

@finance
Feature: finance-crud-datev-master-data

    Scenario: 01-1 - Prerequisite: Activate DATEV service option
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
        Then the "Service options" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "datevOption"
        And the user selects the row 1 of the table field
        And the value of the "Active" labelled nested checkbox field of the selected row in the table field is "false"
        And the user clicks the "Activate" dropdown action of the selected row of the table field
        And a warn dialog appears on the main page
        And the user clicks the "OK" button of the Confirm dialog

    Scenario: 01-2 - Prerequisite: DATEV Configuration has the correct default settings after activation
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance-data/DatevConfiguration"
        Then the "DATEV configuration" titled page is displayed
        When the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "DATEV configuration (Version 700)"
        And the user selects the "ID" labelled text field on the main page
        And the value of the text field is "DATEV"
        And the user selects the "Account length" labelled numeric field on the main page
        And the value of the numeric field is "4"
        And the user selects the "SKR" labelled text field on the main page
        And the value of the text field is "03"
        And the user selects the "Customer and supplier ID length" labelled numeric field on the main page
        And the value of the numeric field is "5"
        And the user selects the "Customer ID range" labelled text field on the main page
        And the value of the text field is "From 10000 to 69999"
        And the user selects the "Supplier ID range" labelled text field on the main page
        And the value of the text field is "From 70000 to 99999"

    Scenario: 02-1 - DATEV Configuration page
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance-data/DatevConfiguration"
        Then the "DATEV configuration" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the card 1 in the table field
        Then the value of the "id" bound nested text field of the card 1 in the table field is "DATEV"

    Scenario: 02-2 - DATEV Configuration page - Name field
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
            Mandatory field

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Name: Mandatory field" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Cancel" labelled business action button on the main page
        Then the text in the header of the dialog is "Unsaved changes"
        And the text in the body of the dialog is "Leave and discard your changes?"
        When the user clicks the "Discard" button of the Confirm dialog

    Scenario: 02-3 - DATEV Configuration page - ID field
        And the user selects the "ID" labelled text field on the main page
        Then the text field is read-only

    Scenario: 02-4 - DATEV Configuration page - Account length field
        # make sure improper values trigger error
        And the user selects the "Account length" labelled numeric field on the main page
        And the user writes "3" in the numeric field
        And the user blurs the numeric field
        Then the numeric field is invalid
        Then a validation error message is displayed with text
            """
           Enter a number between 4 and 8.

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Account length: Enter a number between 4 and 8." is displayed
        And the user dismisses all the toasts
        And the user selects the "Account length" labelled numeric field on the main page
        And the user writes "10" in the numeric field
        And the user blurs the numeric field
        Then the numeric field is invalid
        Then a validation error message is displayed with text
            """
           Enter a number between 4 and 8.

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Account length: Enter a number between 4 and 8." is displayed
        And the user dismisses all the toasts
        # make sure other fields get updated
        And the user selects the "Account length" labelled numeric field on the main page
        And the user writes "8" in the numeric field
        And the user selects the "Customer and supplier ID length" labelled numeric field on the main page
        And the value of the numeric field is "9"
        And the user selects the "Customer ID range" labelled text field on the main page
        And the value of the text field is "From ********* to *********"
        And the user selects the "Supplier ID range" labelled text field on the main page
        And the value of the text field is "From ********* to *********"
        # reset the dafault value
        And the user selects the "Account length" labelled numeric field on the main page
        And the user clears the numeric field
        And the user writes "4" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 02-5 - DATEV Configuration page - SKR field
        And the user selects the "SKR" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
            Mandatory field
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors SKR: Mandatory field" is displayed
        And the user dismisses all the toasts
        And the user selects the "SKR" labelled text field on the main page
        And the user writes "123" in the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
            Enter a number between 1 and 99.
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors SKR: Enter a number between 1 and 99." is displayed
        And the user dismisses all the toasts
        And the user selects the "SKR" labelled text field on the main page
        And the user writes "AB" in the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
            Enter a number between 1 and 99.
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors SKR: Enter a number between 1 and 99." is displayed
        And the user dismisses all the toasts
        And the user selects the "SKR" labelled text field on the main page
        And the user writes "3" in the text field
        And the user blurs the text field
        And the value of the text field is "03"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "SKR" labelled text field on the main page
        And the value of the text field is "03"

    Scenario: 02-6 - DATEV Configuration page - Customer and supplier ID length field
        And the user selects the "Customer and supplier ID length" labelled numeric field on the main page
        And the user writes "3" in the numeric field
        And the user blurs the numeric field
        Then a validation error message is displayed with text
            """
            Enter a number between 5 and 9.
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Customer and supplier ID length: Enter a number between 5 and 9." is displayed
        And the user dismisses all the toasts
        And the user selects the "Customer and supplier ID length" labelled numeric field on the main page
        And the user writes "10" in the numeric field
        And the user blurs the numeric field
        Then a validation error message is displayed with text
            """
            Enter a number between 5 and 9.
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Customer and supplier ID length: Enter a number between 5 and 9." is displayed
        And the user dismisses all the toasts
        And the user selects the "Customer and supplier ID length" labelled numeric field on the main page
        And the user writes "AB" in the numeric field
        And the user blurs the numeric field
        Then a validation error message is displayed with text
            """
            Enter a number between 5 and 9.
            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Customer and supplier ID length: Enter a number between 5 and 9." is displayed
        And the user dismisses all the toasts
        And the user selects the "Customer and supplier ID length" labelled numeric field on the main page
        And the user writes "9" in the numeric field
        And the user blurs the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "Account length" labelled numeric field on the main page
        And the value of the numeric field is "8"
        And the user selects the "Customer ID range" labelled text field on the main page
        And the value of the text field is "From ********* to *********"
        And the user selects the "Supplier ID range" labelled text field on the main page
        And the value of the text field is "From ********* to *********"

    Scenario: 02-7 - DATEV Configuration page - Reset the Account length field
        And the user selects the "Account length" labelled numeric field on the main page
        And the user writes "4" in the numeric field
        And the user blurs the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: 03-1 - Company page - Verify DATEV fields are not available in country-context other than 'Germany'
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "UK Limited"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Company UK Limited" titled page is displayed
        Then the "General" labelled navigation anchor is selected
        Then the "DATEV consultant number" labelled text field on the main page is hidden
        And the "DATEV customer number" labelled text field on the main page is hidden

    Scenario: 03-2 - Company page - Verify DATEV fields are available in country-context 'Germany'
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "DE Automotive GmbH"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Company DE Automotive GmbH" titled page is displayed
        Then the "General" labelled navigation anchor is selected
        Then the "DATEV consultant number" labelled text field on the main page is displayed
        And the "DATEV customer number" labelled text field on the main page is displayed

    Scenario Outline: 03-3 - Company page - DATEV fields - Make sure the fields don't accept improper values
        And the user selects the <Field> labelled text field on the main page
        And the user clears the text field
        And the user writes <Value> in the text field
        And the user blurs the text field
        Then a validation error message is displayed containing text
            """
            Enter a number between

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text <ToastMessage> is displayed
        And the user dismisses all the toasts
        Examples:
            | Field                     | Value      | ToastMessage                                                                          |
            | "DATEV consultant number" | "123"      | "Validation errors DATEV consultant number: Enter a number between 1001 and 9999999." |
            | "DATEV consultant number" | "12345678" | "Validation errors DATEV consultant number: Enter a number between 1001 and 9999999." |
            | "DATEV consultant number" | "ABCD"     | "Validation errors DATEV consultant number: Enter a number between 1001 and 9999999." |
            | "DATEV customer number"   | "0"        | "Validation errors DATEV customer number: Enter a number between 1 and 99999."        |
            | "DATEV customer number"   | "123456"   | "Validation errors DATEV customer number: Enter a number between 1 and 99999."        |
            | "DATEV customer number"   | "ABCD"     | "Validation errors DATEV customer number: Enter a number between 1 and 99999."        |


    Scenario: 03-4 - Company page - DATEV fields - Make sure the fields can be updated with a proper value
        And the user selects the "DATEV customer number" labelled text field on the main page
        And the user writes "12345" in the text field
        And the user blurs the text field
        And the user selects the "DATEV consultant number" labelled text field on the main page
        And the user clears the text field
        And the user writes "1234567" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 03-5 - Company page - DATEV fields - Make sure no empty value is accepted
        And the user selects the "DATEV customer number" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV customer number: If the legislation is Germany, this number is required." is displayed
        And the user dismisses all the toasts
        And the user selects the "DATEV consultant number" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV consultant number: If the legislation is Germany, this number is required." is displayed
        And the user dismisses all the toasts

    Scenario: 04-1 - Customer page - Verify DATEV fields are available
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "Schmitt Apparatebau GmbH"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Schmitt Apparatebau GmbH" titled page is displayed
        Then the "General" labelled navigation anchor is selected
        Then the "DATEV ID" labelled text field on the main page is displayed

    Scenario Outline: 04-2 - Customer page - DATEV fields - Make sure the fields don't accept improper values
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes <Value> in the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
            Enter a number between 10000 and 69999.

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV ID: Enter a number between 10000 and 69999." is displayed
        And the user dismisses all the toasts
        Examples:
            | Value    |
            | "1"      |
            | "123456" |
            | "70000"  |
            | "ABCDE"  |

    Scenario: 04-3 - Customer page - DATEV fields - Make sure a proper value is allowed
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "10000" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 04-4 - Customer page - DATEV fields - Make sure empty value is warned
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a warning toast containing text "You need to enter the DATEV ID before extracting the data when the DATEV integration is active." is displayed
        And the user dismisses all the toasts
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 04-5 - Customer page - DATEV fields - Verify uniqueness control
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "21223" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clears the search field in the navigation panel
        Then the user searches for "MK Manufacturing" in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the card 1 in the table field
        Then the "Customer MK Manufacturing" titled page is displayed
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "21223" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV ID: [Updating vital parent] The DATEV ID needs to be unique." is displayed
        And the user dismisses all the toasts
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "21225" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 05-1 - Supplier page - Verify DATEV fields are available
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "MUHOMA Technology GmbH"
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Supplier MUHOMA Technology GmbH" titled page is displayed
        Then the "General" labelled navigation anchor is selected
        Then the "DATEV ID" labelled text field on the main page is displayed

    Scenario Outline: 05-2 - Supplier page - DATEV fields - Make sure the fields don't accept improper values
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes <Value> in the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
            Enter a number between 70000 and 99999.

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV ID: Enter a number between 70000 and 99999" is displayed
        And the user dismisses all the toasts
        Examples:
            | Value    |
            | "7"      |
            | "789789" |
            | "69999"  |
            | "ABCDE"  |

    Scenario: 05-3 - Supplier page - DATEV fields - Make sure a proper value is allowed
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "70000" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 05-4 - Supplier page - DATEV fields - Verify uniqueness control
        And the user clears the search field in the navigation panel
        Then the user searches for "BARRES" in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the card 1 in the table field
        Then the "Supplier BARRES" titled page is displayed
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "70000" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV ID: [Updating vital parent] The DATEV ID needs to be unique." is displayed
        And the user dismisses all the toasts
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "70001" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 05-5 - Supplier page - DATEV fields - Make sure empty value is warned
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a warning toast containing text "You need to enter the DATEV ID before extracting the data when the DATEV integration is active." is displayed
        And the user dismisses all the toasts
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 06-1 - Accounts page - Verify 'DATEV' tab is not available for accounts outside the German chart of accounts
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance-data/Account"
        Then the "Accounts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "GB chart of accounts" dropdown option in the navigation panel
        And the user filters the "Name" labelled column in the table field with value "Inventory"
        And the user filters the "ID" labelled column in the table field with value "13100"
        And the user selects the row 1 of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        Then the "Account Inventory" titled page is displayed
    # @todo create ER in order to be able to verify that a tab is not present
    #Validate that tab 'DATEV' is not present on the 'Account' page

    Scenario: 06-2 - Accounts page - Verify 'DATEV' tab is only available for accounts of the German chart of accounts
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance-data/Account"
        Then the "Accounts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "DE chart of accounts" dropdown option in the navigation panel
        And the user filters the "Name" labelled column in the table field with value "Wareneingang 19 % Vorsteuer"
        And the user filters the "ID" labelled column in the table field with value "3400"
        And the user selects the row 1 of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        Then the "Account Wareneingang 19 % Vorsteuer" titled page is displayed
        And selects the "DATEV" labelled navigation anchor on the main page
        Then the "DATEV ID" labelled text field on the main page is displayed

    Scenario: 06-3 - Accounts page - DATEV fields - Verify that three preceding zeros are added to the digit '1'
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "1" in the text field
        And the user blurs the text field
        Then the value of the select field is "0001"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 06-4 - Accounts page - DATEV fields - Make sure no improper value is allowed
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "12345" in the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
            Enter a number between 1 and 9999.

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV ID: Enter a number between 1 and 9999." is displayed
        And the user dismisses all the toasts
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "ABCD" in the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
        Enter a number between 1 and 9999.

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a warning toast containing text "You need to enter the DATEV ID before extracting the data when the DATEV integration is active." is displayed
        And the user dismisses all the toasts
        And a error toast containing text "Validation errors DATEV ID: Enter a number between 1 and 9999." is displayed
        And the user dismisses all the toasts

    Scenario: 06-5 - Accounts page - DATEV fields - Make sure a proper value is allowed
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "3400" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 06-6 - Accounts page - DATEV fields - Verify uniqueness control
        And the user clears the search field in the navigation panel
        And the user clears the search field in the navigation panel
        Then the user searches for "3300" in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the card 1 in the table field
        Then the "Account Wareneingang 7 % Vorsteuer" titled page is displayed
        And selects the "DATEV" labelled navigation anchor on the main page
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "3400" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors DATEV ID: The DATEV ID needs to be unique." is displayed
        And the user dismisses all the toasts
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user writes "3300" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 06-7 - Accounts page - DATEV fields - Make sure empty value is warned
        And the user selects the "DATEV ID" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a warning toast containing text "You need to enter the DATEV ID before extracting the data when the DATEV integration is active." is displayed
        And the user dismisses all the toasts
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 06-8 - Accounts page - Automatic account + Tax
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance-data/Account"
        Then the "Accounts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "DE chart of accounts" dropdown option in the navigation panel
        And the user filters the "Name" labelled column in the table field with value "Wareneingang 19 % Vorsteuer"
        And the user filters the "ID" labelled column in the table field with value "3400"
        And the user selects the row 1 of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        Then the "Account Wareneingang 19 % Vorsteuer" titled page is displayed
        And selects the "DATEV" labelled navigation anchor on the main page
        # Select switch 'Automatic account' and set it to 'ON'
        And the user selects the "Automatic account" labelled switch field on the main page
        And the user turns the switch field "ON"
        And the user selects the "Tax" labelled reference field on the main page
        And the user clears the reference field
        Then the value of the reference field is ""
        And the user clicks the "Save" labelled business action button on the main page
        And a warning toast containing text "You need to enter the tax code when the Automatic account is enabled." is displayed
        And the user dismisses all the toasts
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        # Verify that no results are found for country 'France'
        And the user selects the "Tax" labelled reference field on the main page
        And the user clicks the lookup button of the reference field
        And the dialog title is "Tax"
        When the user selects the "tax" bound table field on a modal
        And the user filters the "Country" labelled column in the table field with value "France"
        Then the table field is empty
        And the user clicks the remove all filters button in the table field
        # Verify that results are found for country 'Germany'
        And the user filters the "Country" labelled column in the table field with value "Germany"
        And the user filters the "Name" labelled column in the table field with value "Purchase Standard Rate"
        When the user selects the row with text "Purchase Standard Rate" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Tax" labelled reference field on the main page
        Then the value of the reference field is "Purchase Standard Rate"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        # Select switch 'Automatic account' and set it to 'OFF'
        And the user selects the "Automatic account" labelled switch field on the main page
        And the switch field is set to "ON"
        And the user turns the switch field "OFF"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        # Validate that field 'Tax' is inactive
        And the user selects the "Tax" labelled reference field on the main page
        And the reference field is disabled

    Scenario: 07-1 - Tax page - DATEV fields - Verify DATEV fields are displayed
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Country" labelled column in the table field with value "Germany"
        And the user filters the "Name" labelled column in the table field with value "Purchase Standard Rate"
        When the user selects the row with text "Purchase Standard Rate" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Tax Purchase Standard Rate" titled page is displayed

    Scenario Outline: 07-2 - Tax page - DATEV fields - Make sure the fields don't accept improper values
        And the user selects the "Posting key" labelled text field on the main page
        And the user clears the text field
        And the user writes <Value> in the text field
        And the user blurs the text field
        Then a validation error message is displayed with text
            """
        Enter a number between 1 and 9999.

            """
        And the user clicks the "Save" labelled business action button on the main page
        And a error toast containing text "Validation errors Posting key: Enter a number between 1 and 9999." is displayed
        And the user dismisses all the toasts
        Examples:
            | Value   |
            | "0"     |
            | "ABC"   |
            | "12345" |

    Scenario: 07-3 - Tax page - DATEV fields - Make sure a proper value is allowed
        And the user selects the "Posting key" labelled text field on the main page
        And the user clears the text field
        And the user writes "9" in the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 07-4 - Tax page - DATEV fields - Make sure empty value is accepted
        And the user selects the "Posting key" labelled text field on the main page
        And the user clears the text field
        And the user blurs the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
