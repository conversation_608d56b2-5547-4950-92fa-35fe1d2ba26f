# the purpose of this test it to verify the journal entries created on stock receipt and issue for United state (US) sites
# Note there is currently no step definition to verify if there is a link on the Accounting integration reference

@finance
@inventory
Feature: finance-flow-us-stock-receipt-issue-journal-entries

    Scenario: 01 - Posting stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        And the user selects the "Stock receipts" labelled table field on the main page
        # Find and Select Document
        And the user filters the "Number" labelled column in the table field with value "SR230018"
        And the user selects the row with text "SR230018" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field


        And the user clicks the "Post stock" labelled business action button on the main page
        And the user waits 5 seconds
        #RELOADING THE PAGE BECAUSE OF THE POST STATUS THAT NEED TO CHANGE TO "POSTED"

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SR230018"
        And the user selects the row with text "SR230018" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # Verifying the display status of the posted sales receipt (Closed)

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Journal entry 0001" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Received"

        When the user selects the "Display status" labelled label field on the main page
        Then the value of the label field is "Received"

    Scenario: 02 - Generate journal entry for the created stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        # setting the values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        Then the user selects "TE Headquarter" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Miscellaneous stock receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Miscellaneous stock receipt"

        # Clicks Generate and check message
        When the user clicks in the "create" bound button field on the main page

        # Generating a journal entry for the stock receipt
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 03 - Notification page - 1
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page

        # Selecting criteria
        When the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        #And the user writes "Miscellaneous stock receipt" in the multi dropdown field
        And the user selects "Miscellaneous stock receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Miscellaneous stock receipt"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select statuses "Posted", "Recorded" & "Submitted"
        And the user clicks in the multi dropdown field
        And the user selects "Posted | Recorded | Submitted" in the multi dropdown field
        ##Check correct values were selected
        Then the value of the multi dropdown field is "Posted, Recorded, Submitted"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user waits 5 seconds
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SR230018" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" bound nested text field of the selected row in the table field with the key "[ENV_IJNumber]"
        And the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"

    Scenario: 04 - Verify that the values of lines in result grid correspond to values of the journal entry for stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed

        # Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_IJNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # Verifying the correctness of text fields
        And the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "TE Headquarter"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Stock receipt"
        And the user selects the "Origin" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Stock"

        # Verify posting status field and accounting integration reference
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Accounting integration reference" labelled link field on the main page
        ## Need the step definition to check if the link is filled/available on the Accounting integration field ##
        # Verify the fields on the grid
        When the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "TE Headquarter" in column with header "Financial site" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "100 -- TE Headquarter" in column with header "Financial site" in the nested grid field
        And the value of the "transactionAmount" labelled nested text field of the selected row in the nested grid field is "$ 80.00"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Journal entry 0001 -- Journal entry 0001"

    Scenario: 05 - Posting stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SS230006" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        #Assigning stock
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Journal entry 0001" in the "item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "saveStockIssue" bound business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user waits 5 seconds

        #RELOADING THE PAGE BECAUSE OF THE POST STATUS THAT NEED TO CHANGE TO "POSTED"
        And the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "SS230006" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verifying the display status of the posted sales issue (Issued)
        Then the "Stock issue SS230006" titled page is displayed
        And the user selects the "Display status" labelled label field on the main page
        And the value of the label field is "Issued"


    Scenario: 06 - Generate journal entry for the created stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        # set the values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        Then the user selects "TE Headquarter" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/04/2023"
        Then the value of the date field is a generated date with value "09/04/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Miscellaneous stock issue" in the multi dropdown field
        Then the value of the multi dropdown field is "Miscellaneous stock issue"

        # Clicks Generate and check message
        When the user clicks in the "create" bound button field on the main page

        # Generating a journal entry for the stock issue
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 07 - Notification page - 2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page

        # Selecting criteria
        When the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        #And the user writes "Miscellaneous stock issue" in the multi dropdown field
        And the user selects "Miscellaneous stock issue" in the multi dropdown field
        Then the value of the multi dropdown field is "Miscellaneous stock issue"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select statuses "Posted", "Recorded" & "Submitted"
        And the user clicks in the multi dropdown field
        And the user selects "Posted | Recorded | Submitted" in the multi dropdown field
        ##Check correct values were selected
        And the value of the multi dropdown field is "Posted, Recorded, Submitted"

        # And the value of the multi dropdown field is "Posted"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SS230006" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" bound nested text field of the selected row in the table field with the key "[ENV_IJNumber]"

    Scenario: 08 - Verify that the values of lines in result grid correspond to values of the journal entry for stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed

        # Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_IJNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # Verifying the correctness of text fields
        And the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "TE Headquarter"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Stock issue"
        And the user selects the "Origin" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Stock"
        # Verify posting status field and accounting integration reference
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Accounting integration reference" labelled link field on the main page
        ## Need the step definition to check if the link is filled/available on the Accounting integration field for the stock issue journal ##
        # Verify the fields on the grid
        And the user selects row with text "TE Headquarter" in column with header "Financial site" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "100 -- TE Headquarter" in column with header "Financial site" in the nested grid field
        And the value of the "transactionAmount" labelled nested text field of the selected row in the nested grid field is "$ 20.00"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Journal entry 0001 -- Journal entry 0001"
