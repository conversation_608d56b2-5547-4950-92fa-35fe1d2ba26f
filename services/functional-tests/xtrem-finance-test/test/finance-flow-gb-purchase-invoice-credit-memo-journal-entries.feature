
# The purpose of this test is to Check correctness of Accounts payable invoice for the following;
# Purchase Invoice
# Credit Memo

@finance
@distribution @TestAayu1
Feature: finance-flow-gb-purchase-invoice-credit-memo-journal-entries

    Scenario: 01 - Verifiying that the user can post a purchase invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PI20230001"
        And the user selects the row with text "PI20230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Add dimensions to the purchase invoice
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        #Post an invoice
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted" is displayed

    Scenario: 02 - Verifiying that the user can verify if the purchase invoice is posted
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user waits 5 seconds
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user clicks the option menu of the table field
        And the user clicks the "Posted" value in the option menu of the table field
        And the user filters the "Number" labelled column in the table field with value "PI20230001"
        And the user selects the row with text "PI20230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Posted"
        When selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Accounts payable invoice" in the "Document type" labelled column header of the table field
        And the value of the "Document number" labelled nested text field of the selected row in the table field is "PI20230001"
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Posted"

    Scenario: 03 - Verifiying that the user can verify the correctness of accounts payable invoice - 1
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsPayableInvoice"
        Then the "Accounts payable invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        # Check correctness of Accounts payable invoice (access AP from posting tab)
        And the user filters the "Number" labelled column in the table field with value "PI20230001"
        And the user selects the row with text "PI20230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "20680 -- GRNI" in column with header "Account" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "20680 -- GRNI"
        And the value of the "Amount excluding tax" labelled nested text field of the selected row in the nested grid field is "£ 100.00"
        And the value of the "Tax amount" labelled nested text field of the selected row in the nested grid field is "£ 20.00"
        And the value of the "Amount including tax" labelled nested text field of the selected row in the nested grid field is "£ 120.00"
        And the value of the "Line type" labelled nested text field of the selected row in the nested grid field is "Goods"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is ""
        And the user expands the selected row of the nested grid field
        And the user selects row with text "500 -- UK LIMITED" in column with header "Financial site" in the nested grid field
        And the value of the "Business site" labelled nested text field of the selected row in the nested grid field is "500 -- UK LIMITED"
        And the value of the "Stock site" labelled nested text field of the selected row in the nested grid field is "501 -- Swindon"
        And the value of the "Supplier" labelled nested text field of the selected row in the nested grid field is "20075 -- Lyreco"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "3847TY7 -- Pressure sensor"
        And the value of the "Amount" labelled nested text field of the selected row in the nested grid field is "£ 100.00"


    Scenario: 04 - Verifiying that the user can post a credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PC230001"
        And the user selects the row with text "PC230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Add dimentions to the purchase invoice
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Purchase invoice" in the "origin" bound column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        # Post an credit memo
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        And a toast containing text "The purchase credit memo was posted." is displayed


    Scenario: 05 - Verifiying that the user can verify if the purchase credit memo is posted
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        And the user waits 5 seconds
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user clicks the option menu of the table field
        And the user clicks the "Posted" value in the option menu of the table field
        And the user filters the "Number" labelled column in the table field with value "PC230001"
        And the user selects the row with text "PC230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" bound label field on the main page
        And the value of the label field is "Posted"
        When selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Accounts payable invoice" in the "Document type" labelled column header of the table field
        And the value of the "Document number" labelled nested text field of the selected row in the table field is "PC230001"
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Posted"


    Scenario: 06 - Verifiying that the user can verify the correctness of accounts payable invoice - 2
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/AccountsPayableInvoice"
        Then the "Accounts payable invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PC230001"
        And the user selects the row with text "PC230001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "20680 -- GRNI" in column with header "Account" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "20680 -- GRNI"
        And the value of the "Amount excluding tax" labelled nested text field of the selected row in the nested grid field is "£ 200.00"
        And the value of the "Tax amount" labelled nested text field of the selected row in the nested grid field is "£ 40.00"
        And the value of the "Amount including tax" labelled nested text field of the selected row in the nested grid field is "£ 240.00"
        And the value of the "Line type" labelled nested text field of the selected row in the nested grid field is "Goods"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is ""

        And the user expands the selected row of the nested grid field
        And the user selects row with text "500 -- UK LIMITED" in column with header "Financial site" in the nested grid field
        And the value of the "Business site" labelled nested text field of the selected row in the nested grid field is "500 -- UK LIMITED"
        And the value of the "Stock site" labelled nested text field of the selected row in the nested grid field is "500 -- UK LIMITED"
        And the value of the "Supplier" labelled nested text field of the selected row in the nested grid field is "20075 -- Lyreco"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "3847TY7 -- Pressure sensor"
        And the value of the "Department" labelled nested text field of the selected row in the nested grid field is "100 -- Sales"
        And the value of the "Channel" labelled nested text field of the selected row in the nested grid field is "300 -- Retail"
        And the value of the "Amount" labelled nested text field of the selected row in the nested grid field is "£ 200.00"

#    Scenario: Verify that non stock managed items are able to be posted
#
#        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
#        Then the "Purchase credit memos" titled page is displayed
#
#       # Find the specific PCM Document
#        When the user selects the "Purchase credit memos" labelled table field on the main page
#        And the user selects the row with text "PC240003" in the "Number" labelled column header of the table field
#        And the user clicks the "Number" labelled nested field of the selected row in the table field

#        # Verify existing status of the credit memo
#        When the user selects the "displayStatus" bound label field on the main page
#        Then the value of the label field is "Draft"

#       # Post the Purchase credit memo
#        And the user clicks the "Post" labelled business action button on the main page
#        And the user clicks the "Post" button of the Confirm dialog
#        And a toast containing text "The purchase credit memo was posted." is displayed

#        And the user waits 5 seconds
#        # And the user refreshes the screen

#       # Confirm staus
#        When the user selects the "displayStatus" bound label field on the main page
#        Then the value of the label field is "Posted"

#        # Confirm Posting tab
#        When selects the "Posting" labelled navigation anchor on the main page
#        And the user selects the "Results" labelled table field on the main page
#        And the user selects the row with text "Accounts payable invoice" in the "Document type" labelled column header of the table field
#        # And the value of the "Document number" labelled nested text field of the selected row in the table field is ""
#        And the value of the "Status" labelled nested text field of the selected row in the table field is "Posted"
