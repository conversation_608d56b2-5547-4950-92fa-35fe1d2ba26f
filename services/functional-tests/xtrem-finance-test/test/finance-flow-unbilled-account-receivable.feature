# This script verify that query of shipment not invoiced is run as expected on the Unbilled account Receivable page

@finance
@distribution
Feature: finance-flow-unbilled-account-receivable

    Scenario: Unbilled accounts payable verification for Receipt invoiced , not returned, & credited from invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/UnbilledAccountReceivableInquiry/"
        Then the "Unbilled accounts receivable" titled page is displayed
        # Filtering Company fieldc
        When the user selects the "Company" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field
        Then the value of the reference field is "Société S1"
        # Filtering From supplier field
        When the user selects the "From customer" labelled reference field on the main page
        And the user writes "ATSERMO" in the reference field
        And the user selects "ATSERMO" in the reference field
        Then the value of the reference field is "ATSERMO"
        # Filtering To supplier field
        When the user selects the "To customer" labelled reference field on the main page
        And the user writes "ATSERMO" in the reference field
        And the user selects "ATSERMO" in the reference field
        Then the value of the reference field is "ATSERMO"
        # Entering AS of Date
        When the user selects the "As of date" labelled date field on the main page
        And the user writes "08/31/2023" in the date field
        Then the value of the date field is "08/31/2023"
        # Validation to display the result
        And the user clicks the "Run" labelled business action button on the main page
        Then a toast containing text "Unbilled accounts receivable request sent." is displayed
        And the user waits 5 seconds
        And a toast containing text "Unbilled accounts receivable finished." is displayed

        # Verify value in the table field
        When the user selects the "Results" labelled table field on the main page

        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When searches for "Ship-to customer ID" in the lookup dialog on the sidebar
        And the user ticks the table column configuration with "Ship-to customer ID" name on the sidebar
        Then the table column configuration with name "Ship-to customer ID" on the sidebar is ticked
        And the user clicks the Close button of the sidebar

        # Verification of Sales Order Shipped , partially returned, & invoiced post "As of Date"
        And the user selects the row with text "ATSE00074" in the "Ship-to customer ID" labelled column header of the table field
        And the value of the "Bill-to customer" labelled nested reference field of the selected row in the table field is "ATSERMO"
        And the value of the "Company ID" labelled nested reference field of the selected row in the table field is "S1"
        And the value of the "Financial site ID" labelled nested reference field of the selected row in the table field is "ETS1-S01"
        And the value of the "Shipped not invoiced quantity" labelled nested numeric field of the selected row in the table field is "80 each"
        And the value of the "Net price" labelled nested numeric field of the selected row in the table field is "€ 56.15"
        And the value of the "Unbilled amount" labelled nested numeric field of the selected row in the table field is "€ 4,492.00"
        And the value of the "Item ID" labelled nested reference field of the selected row in the table field is "3847TY7"
        And the value of the "Stock site" labelled nested reference field of the selected row in the table field is "Entrepot de Saint Denis"
        And the value of the "Account item" labelled nested link field of the selected row in the table field is "Ventes de produits finis TVA débits taux normal"
        And the value of the "Account item ID" labelled nested link field of the selected row in the table field is "********"
        And the value of the "Shipment number" labelled nested link field of the selected row in the table field is "SH230034"
        And the value of the "Shipped quantity" labelled nested numeric field of the selected row in the table field is "100 each"
        And the value of the "Returned quantity" labelled nested numeric field of the selected row in the table field is "20 each"
    Scenario: Delete previously created record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/UnbilledAccountReceivableInquiry"

        Then the "Unbilled accounts receivable" titled page is displayed

        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
