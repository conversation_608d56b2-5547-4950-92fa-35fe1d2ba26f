
#The goal of this test is to verify the creation, update and deletion of the dimention type

@finance
Feature: finance-crud-dimension-type

    Scenario: Verify the user is able to create a dimension type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/DimensionType"
        Then the "Dimension types" titled page is displayed
        And the user selects the "dimensionTypes" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        Then the "Dimension type" titled page is displayed
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Dimension09" in the text field
        And the user selects the "Document property" labelled select field on the main page
        And the user writes "Dimension type 09" in the select field
        And the user selects "Dimension type 09" in the select field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify the user is able to update a dimension type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/DimensionType"
        Then the "Dimension types" titled page is displayed
        And the user selects the "Dimension types" labelled table field on the main page
        And the user selects the row with text "Dimension09" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Dimension09 - Update" in the text field
        And the user selects the "Document property" labelled select field on the main page
        And the user clicks in the select field
        And the user clicks the "Rename" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "Name" labelled text field on the main page
        ##Verify the updated value
        And the value of the text field is "Dimension09 - Update"

    Scenario: Verify the user is able to delete a dimension type
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/DimensionType"
        Then the "Dimension types" titled page is displayed
        And the user selects the "Dimension types" labelled table field on the main page
        And the user selects the row with text "Dimension09 - Update" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed
