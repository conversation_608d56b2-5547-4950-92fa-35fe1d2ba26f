# the purpose of this test it to verify the journal entries created for-fr-sales shipment and sales return receipt (non stock managed item and stock managed item)

@finance
@distribution
Feature: finance-flow-fr-sales-shipment-return-journal-entries

    Scenario: 01 - Posting sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "SH230002"
        And the user selects the row with text "SH230002" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales shipment was posted" is displayed

    Scenario: 02 - Generate journal entry for the created sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        # setting the values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Siège social S01  PARIS" in the reference field
        Then the user selects "Siège social S01 PARIS" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/06/2023"
        Then the value of the date field is a generated date with value "09/06/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/06/2023"
        Then the value of the date field is a generated date with value "09/06/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Sales shipment" in the multi dropdown field
        Then the value of the multi dropdown field is "Sales shipment"
        # Clicks Generate and check message
        When the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 03 - Notification page to store the generated journal entry ID for the sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page
        # Selecting criteria
        When the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Sales shipment" in the multi dropdown field
        Then the value of the multi dropdown field is "Sales shipment"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select both statuses "Posted" & "Recorded"
        And the user clicks in the multi dropdown field
        And the user selects "Posted | Recorded" in the multi dropdown field
        ##Check correct values were selected
        And the value of the multi dropdown field is "Posted, Recorded"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SH230002" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_IJNumber]"

    Scenario: 04 - Verify that the values of lines in result grid correspond to values of the journal entry for sales shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        # Creating the journal entry inquiry
        And the user selects the "Journal entries" labelled table field on the main page
        And the user filters the "Description" labelled column in the table field with value "Sales shipment"
        And the user filters the "Number" labelled column in the table field with value "[ENV_IJNumber]"
        And the user selects the row with text "[ENV_IJNumber]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Verifying the correctness of text fields
        And the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Siège social S01  PARIS"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Sales shipment"
        And the user selects the "Origin" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Sales"
        # Verify posting status field and accounting integration reference
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        # Verify the fields on the grid revenue recognition
        When the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the value of the "transactionAmount" labelled nested text field of the selected row in the nested grid field is "€ 34,444.10"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Sales work flow SM ID -- Sales work flow SM name"
        And the value of the "businessSite" labelled nested text field of the selected row in the nested grid field is "DEP1-S01 -- Entrepot de Saint Denis"

    Scenario: 05 - Posting sales return receipts
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "SRR00001"
        And the user selects the row with text "SRR00001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page

        And the user waits for 10 seconds

    Scenario: 06 - Generate journal entry for the created sales return receipts
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        # setting the values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Siège social S01  PARIS" in the reference field
        Then the user selects "Siège social S01 PARIS" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/06/2023"
        Then the value of the date field is a generated date with value "09/06/2023"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "09/06/2023"
        Then the value of the date field is a generated date with value "09/06/2023"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Sales return receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Sales return receipt"
        # Clicks Generate and check message
        When the user clicks in the "create" bound button field on the main page
        # Generating a journal entry for the stock receipt
        Then a toast containing text "Journals created: 1" is displayed
        And the user waits for 10 seconds

    Scenario: 07 - Notification page to store the generated journal entry ID for the sales return receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page
        # Selecting criteria and storing target document number
        When the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Sales return receipt" in the multi dropdown field
        Then the value of the multi dropdown field is "Sales return receipt"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Clear multi dropdown field before selecting criteria
        And the user clears the multi dropdown field
        ##Select both statuses "Posted" & "Recorded"
        And the user clicks in the multi dropdown field
        And the user selects "Posted | Pending | Recorded" in the multi dropdown field
        ##Check correct values were selected
        And the value of the multi dropdown field is "Posted, Pending, Recorded"
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "SRR00001" in the "documentNumber" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_IJNumber2]"

    Scenario: 08 - Verify that the values of lines in result grid correspond to values of the journal entry for sales return receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        # Creating the journal entry inquiry
        And the user selects the "Journal entries" labelled table field on the main page
        And the user filters the "Description" labelled column in the table field with value "Sales return receipt"
        And the user filters the "Number" labelled column in the table field with value "[ENV_IJNumber2]"
        And the user selects the row with text "[ENV_IJNumber2]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Verifying the correctness of text fields
        And the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Siège social S01  PARIS"
        And the user selects the "Reference" labelled text field on the main page
        Then the value of the text field is "Sales return receipt"
        And the user selects the "Origin" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Stock"
        # Verify posting status field and accounting integration reference
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        # Verify the fields on the grid reversal of revenue recognition and stock
        When the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "ETS1-S01 -- Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the value of the "transactionAmount" labelled nested text field of the selected row in the nested grid field is "€ 37,110.74"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "Sales work flow SM ID -- Sales work flow SM name"
        And the value of the "businessSite" labelled nested text field of the selected row in the nested grid field is "DEP1-S01 -- Entrepot de Saint Denis"
