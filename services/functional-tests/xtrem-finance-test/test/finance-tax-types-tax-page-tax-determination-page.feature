
# SDMO users can now define the type of tax at the tax code level, specifying whether it is input ('Purchasing'), output ('Sales'), or applicable to both ('Sales and Purchasing').
# The automated test(s) must verify that the controls integrated into the tax determination rules function correctly and are effectively applied to relevant business transactions,
# ensuring that incorrect tax types cannot be selected at the transaction level.
@finance
Feature: finance-tax-types-tax-page-tax-determination-page

    Scenario: 01 - Prerequisites - Tax creation AUTO 01 PURCHASING
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Tax category" labelled reference field on the main page
        And the user writes "Value Added Tax" in the reference field
        And the user selects "Value Added Tax" in the reference field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "AUTO 01 PURCHASING" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        And the user selects the "Primary external reference" labelled text field on the main page
        Then the user writes "AUTO 01 PURCHASING" in the text field
        When the user selects the "Tax type" labelled dropdown-list field on the main page
        And the user selects "Purchasing" in the dropdown-list field
        And the value of the dropdown-list field is "Purchasing"
        # Adding line
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
    Scenario: 02 - Prerequisites - Tax creation AUTO 02 SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Tax category" labelled reference field on the main page
        And the user writes "Value Added Tax" in the reference field
        And the user selects "Value Added Tax" in the reference field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "AUTO 02 SALES" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        And the user selects the "Primary external reference" labelled text field on the main page
        Then the user writes "AUTO 02 SALES" in the text field
        When the user selects the "Tax type" labelled dropdown-list field on the main page
        And the user selects "Sales" in the dropdown-list field
        And the value of the dropdown-list field is "Sales"
        # Adding line
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
    Scenario: 03 - Prerequisites - Tax creation AUTO 03 PURCHASING + SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Tax category" labelled reference field on the main page
        And the user writes "Value Added Tax" in the reference field
        And the user selects "Value Added Tax" in the reference field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "AUTO 03 PURCHASING + SALES" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        And the user selects the "Primary external reference" labelled text field on the main page
        Then the user writes "AUTO 03 PURCHASING + SALES" in the text field
        When the user selects the "Tax type" labelled dropdown-list field on the main page
        And the user selects "Purchasing and sales" in the dropdown-list field
        And the value of the dropdown-list field is "Purchasing and sales"
        # Adding line
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    ### currently there is an issue with the tax custom filter, so the following scenario is commented out
    # Scenario: 04 - Verify tax filter works properly
    #     Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
    #     When the "Tax determination rules" titled page is displayed
    #     And the user selects the "$navigationPanel" bound table field on the navigation panel
    #     And the user selects the row with text "United Kingdom tax solution" in the "Tax solution" labelled column header of the table field
    #     When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
    #     And the user selects the "lines" bound table field on the main page

    #     When the user opens the filter of the "Item tax group" labelled column in the table field
    #     And the user searches "Goods, exempt VAT" in the filter of the table field
    #     And the user ticks the item with text "Goods, exempt VAT" in the filter of the table field
    #     Then the user closes the filter of the "Item tax group" labelled column in the table field

    #     When the user opens the filter of the "Origin country" labelled column in the table field
    #     And the user searches "United Kingdom" in the filter of the table field
    #     And the user ticks the item with text "United Kingdom" in the filter of the table field
    #     Then the user closes the filter of the "Origin country" labelled column in the table field

    #     When the user opens the filter of the "Destination country" labelled column in the table field
    #     And the user searches "United Kingdom" in the filter of the table field
    #     And the user ticks the item with text "United Kingdom" in the filter of the table field
    #     Then the user closes the filter of the "Destination country" labelled column in the table field

    #     When the user selects the row with text "Sales" in the "Flow" labelled column header of the table field
    #     And the user clicks the "Tax" labelled nested field of the selected row in the table field
    #     And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field

    #     And the user selects the "tax" bound table field on a modal

    #     When the user opens the filter of the "Type" labelled column in the table field
    #     #And the user searches "Purchasing" in the filter of the table field
    #     And the user ticks the item with text "Purchasing" in the filter of the table field
    #     When the user closes the filter of the "Type" labelled column in the table field
    #     Then the table field is empty


    Scenario: 05 - Update a tax determination rule to use AUTO 01 PURCHASING
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "United Kingdom tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page

        When the user opens the filter of the "Item tax group" labelled column in the table field
        And the user searches "Goods, exempt VAT" in the filter of the table field
        And the user ticks the item with text "Goods, exempt VAT" in the filter of the table field
        Then the user closes the filter of the "Item tax group" labelled column in the table field

        When the user opens the filter of the "Origin country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Origin country" labelled column in the table field

        When the user opens the filter of the "Destination country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Destination country" labelled column in the table field

        When the user selects the row with text "Purchasing" in the "Flow" labelled column header of the table field
        And the user stores the value of the "Tax" labelled nested reference field of the selected row in the table field with the key "[ENV_TAX_01]"
        And the user writes "AUTO 01 PURCHASING" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "AUTO 01 PURCHASING" in the "Tax" labelled nested field of the selected row in the table field

        And the user clicks the "Save" labelled business action button on the main page


    Scenario: 06 - Update a tax determination rule to use AUTO 02 SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "United Kingdom tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page

        When the user opens the filter of the "Item tax group" labelled column in the table field
        And the user searches "Goods, exempt VAT" in the filter of the table field
        And the user ticks the item with text "Goods, exempt VAT" in the filter of the table field
        Then the user closes the filter of the "Item tax group" labelled column in the table field

        When the user opens the filter of the "Origin country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Origin country" labelled column in the table field

        When the user opens the filter of the "Destination country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Destination country" labelled column in the table field

        When the user selects the row with text "Sales" in the "Flow" labelled column header of the table field
        And the user stores the value of the "Tax" labelled nested reference field of the selected row in the table field with the key "[ENV_TAX_02]"
        And the user writes "AUTO 02 SALES" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "AUTO 02 SALES" in the "Tax" labelled nested field of the selected row in the table field

        And the user clicks the "Save" labelled business action button on the main page


    Scenario: 07 - Update a tax determination rule to use AUTO 03 PURCHASING + SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "United Kingdom tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page

        When the user opens the filter of the "Item tax group" labelled column in the table field
        And the user searches "Goods, exempt VAT" in the filter of the table field
        And the user ticks the item with text "Goods, exempt VAT" in the filter of the table field
        Then the user closes the filter of the "Item tax group" labelled column in the table field

        When the user opens the filter of the "Origin country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Origin country" labelled column in the table field

        When the user opens the filter of the "Destination country group" labelled column in the table field
        And the user searches "All countries" in the filter of the table field
        And the user ticks the item with text "All countries" in the filter of the table field
        Then the user closes the filter of the "Destination country group" labelled column in the table field

        When the user selects the row with text "Sales" in the "Flow" labelled column header of the table field
        And the user stores the value of the "Tax" labelled nested reference field of the selected row in the table field with the key "[ENV_TAX_03]"
        And the user writes "AUTO 03 PURCHASING + SALES" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "AUTO 03 PURCHASING + SALES" in the "Tax" labelled nested field of the selected row in the table field

        And the user clicks the "Save" labelled business action button on the main page

    Scenario: 08 - Check tax type for AUTO 01 PURCHASING
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "AUTO 01 PURCHASING"
        And the user selects the row with text "AUTO 01 PURCHASING" in the "name" bound column header of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        When the user selects the "Tax category" labelled reference field on the main page
        And the value of the reference field is "Value Added Tax"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "AUTO 01 PURCHASING"
        And the user selects the "Tax type" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is "Purchasing"

    Scenario: 09 - Check tax type for AUTO 02 SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "AUTO 02 SALES"
        And the user selects the row with text "AUTO 02 SALES" in the "name" bound column header of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        When the user selects the "Tax category" labelled reference field on the main page
        And the value of the reference field is "Value Added Tax"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "AUTO 02 SALES"
        And the user selects the "Tax type" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is "Sales"

    Scenario: 10 - Check tax type for AUTO 03 PURCHASING + SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "AUTO 03 PURCHASING + SALES"
        And the user selects the row with text "AUTO 03 PURCHASING + SALES" in the "name" bound column header of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        When the user selects the "Tax category" labelled reference field on the main page
        And the value of the reference field is "Value Added Tax"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "AUTO 03 PURCHASING + SALES"
        And the user selects the "Tax type" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is "Purchasing and sales"

    Scenario: 11 - Revert a tax determination rule to use the previous value AUTO 01 PURCHASING
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "United Kingdom tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page

        When the user opens the filter of the "Item tax group" labelled column in the table field
        And the user searches "Goods, exempt VAT" in the filter of the table field
        And the user ticks the item with text "Goods, exempt VAT" in the filter of the table field
        Then the user closes the filter of the "Item tax group" labelled column in the table field

        When the user opens the filter of the "Origin country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Origin country" labelled column in the table field

        When the user opens the filter of the "Destination country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Destination country" labelled column in the table field

        When the user selects the row with text "Purchasing" in the "Flow" labelled column header of the table field
        And the user writes "[ENV_TAX_01]" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "[ENV_TAX_01]" in the "Tax" labelled nested field of the selected row in the table field

        And the user clicks the "Save" labelled business action button on the main page


    Scenario: 12 - Revert a tax determination rule to use the previous value AUTO 02 SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "United Kingdom tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page

        When the user opens the filter of the "Item tax group" labelled column in the table field
        And the user searches "Goods, exempt VAT" in the filter of the table field
        And the user ticks the item with text "Goods, exempt VAT" in the filter of the table field
        Then the user closes the filter of the "Item tax group" labelled column in the table field

        When the user opens the filter of the "Origin country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Origin country" labelled column in the table field

        When the user opens the filter of the "Destination country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Destination country" labelled column in the table field

        When the user selects the row with text "Sales" in the "Flow" labelled column header of the table field
        And the user writes "[ENV_TAX_02]" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "[ENV_TAX_02]" in the "Tax" labelled nested field of the selected row in the table field

        And the user clicks the "Save" labelled business action button on the main page


    Scenario: 13 - Revert a tax determination rule to use the previous value AUTO 03 PURCHASING + SALES
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "United Kingdom tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page

        When the user opens the filter of the "Item tax group" labelled column in the table field
        And the user searches "Goods, exempt VAT" in the filter of the table field
        And the user ticks the item with text "Goods, exempt VAT" in the filter of the table field
        Then the user closes the filter of the "Item tax group" labelled column in the table field

        When the user opens the filter of the "Origin country" labelled column in the table field
        And the user searches "United Kingdom" in the filter of the table field
        And the user ticks the item with text "United Kingdom" in the filter of the table field
        Then the user closes the filter of the "Origin country" labelled column in the table field

        When the user opens the filter of the "Destination country group" labelled column in the table field
        And the user searches "All countries" in the filter of the table field
        And the user ticks the item with text "All countries" in the filter of the table field
        Then the user closes the filter of the "Destination country group" labelled column in the table field

        When the user selects the row with text "Sales" in the "Flow" labelled column header of the table field
        And the user writes "[ENV_TAX_03]" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "[ENV_TAX_03]" in the "Tax" labelled nested field of the selected row in the table field

        And the user clicks the "Save" labelled business action button on the main page
    Scenario: 14 - Cleanup
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "name" bound column in the table field with value "AUTO 01 PURCHASING"
        And the user selects the row with text "AUTO 01 PURCHASING" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "AUTO 01 PURCHASING" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "name" bound column in the table field with value "AUTO 02 SALES"
        And the user selects the row with text "AUTO 02 SALES" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "AUTO 02 SALES" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "name" bound column in the table field with value "AUTO 03 PURCHASING + SALES"
        And the user selects the row with text "AUTO 03 PURCHASING + SALES" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "AUTO 03 PURCHASING + SALES" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
