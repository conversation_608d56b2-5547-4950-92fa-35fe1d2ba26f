# The goal of this test is to verify that a user can activate and deactivate the attribute type employee.

@finance
Feature: finance-flow-activate-attribute-type

    Scenario: 01 - Activate the attribute type employee
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/AttributeType"
        Then the "Attribute types" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Employee" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "OFF"
        And the user turns the switch field "ON"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "ON"
        #Create attribute with attribute type employee
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        When the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "ON"
        And the user selects the "ID" labelled text field on the main page
        Then the user writes "59394" in the text field
        And the user stores the value of the text field with the key "[ENV_ATT_ID1]"
        And the user selects the "Name" labelled text field on the main page
        Then the user writes "Automation Tester" in the text field
        And the user selects the "Attribute type" labelled reference field on the main page
        And the user writes "Employee" in the reference field
        And the user selects "Employee" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        #Verify new attribute
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the value of the "Attribute type" labelled nested reference field of the selected row in the table field is "Employee"


    Scenario: 02 - Deactivate the attribute type employee
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/AttributeType"
        Then the "Attribute types" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Employee" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "ON"
        And the user turns the switch field "OFF"
        And the user clicks the "Deactivate" button of the Confirm dialog on the main page
        Then a toast containing text "Select 'Save' to apply your change." is displayed
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "OFF"
