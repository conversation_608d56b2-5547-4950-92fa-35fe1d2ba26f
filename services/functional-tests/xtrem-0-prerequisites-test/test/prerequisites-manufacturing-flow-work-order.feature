# The goal of this test is to create a WO and test the flow of the WO
# Data
# Items used         : Manufactured Item 2
#                      Material Item 2

@prerequisites
@manufacturing
Feature: prerequisites-manufacturing-flow-work-order

    Scenario: 01 - Create a Work order

        # Create the Work order
        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        # use ultrawide desktop allows to check planned cost on the operation line, otherwise the value is not found (probably because the robot
        # doesn't manage to scroll till this field)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed

        When the user clicks the "Create" labelled business action button on the main page
        Then the "New work order" titled sidebar is displayed

        When the user selects the "Site" labelled reference field on the sidebar
        And the user writes "South Africa Headquarter & Warehouse" in the reference field
        And the user selects "South Africa Headquarter & Warehouse" in the reference field

        And the user selects the "Released item" labelled reference field on the sidebar
        And the user writes "Manufactured Item 2 BOM" in the reference field
        And the user selects "Manufactured Item 2 BOM" in the reference field

        And the user selects the "Category" labelled reference field on the sidebar
        And the user writes "Normal" in the reference field
        And the user selects "Normal" in the reference field

        And the user selects the "Type" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Firm" in the dropdown-list field

        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field

        And the user selects the "Work order number" labelled text field on the sidebar
        And the user writes "Work_Order_Flow_Test_001" in the text field
        And the user presses Enter

        And the user stores the value of the text field with the key "[ENV_Work_Order_Number]"
        And the user clicks the "Create" labelled business action button on the sidebar
        Then the "Work order [ENV_Work_Order_Number]" titled page is displayed

    Scenario: 02 - Check if the BOM and Routing details are loaded correctly

        # Main page checks
        When the user selects the "Item" labelled reference field on the main page
        And the value of the reference field is "Manufactured Item 2"

        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Pending"

        And the user selects the "Uses routing" labelled checkbox field on the main page
        And the value of the checkbox field is "true"

        And the user selects the "Uses bill of material" labelled checkbox field on the main page
        And the value of the checkbox field is "true"

        # Check the Operations nested grid
        Given selects the "Operations" labelled navigation anchor on the main page
        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "1" in column with header "Operation number" in the nested grid field

        And the value of the "Name" labelled nested text field of the selected row in the nested grid field is "Operation 1"
        And the value of the "Status" labelled nested text field of the selected row in the nested grid field is "Pending"
        And the value of the "Planned  quantity" labelled nested numeric field of the selected row in the nested grid field is "10"
        And the value of the "Completed quantity" labelled nested numeric field of the selected row in the nested grid field is "0"

        And the value of the "Planned setup time" labelled nested numeric field of the selected row in the nested grid field is "1.00 h"
        And the value of the "Planned run time" labelled nested numeric field of the selected row in the nested grid field is "10.00 h"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the nested grid field is "R 110.0000"
        # Check the resource #1 of the operation
        And the user expands the selected row of the nested grid field
        And the user selects row with text "LABOR_RES_01" in column with header "ID" in the nested grid field
        # TODO: ATP Bug parent row with same column name, Parent row being read instead - bug seems to be fixed
        And the value of the "Name" labelled nested text field of the selected row in the nested grid field is "Labor Resource 1"
        # And the value of the "resource__name" bound nested text field of the selected row in the nested grid field is "Labor Resource 1"
        And the value of the "Status" labelled nested text field of the selected row in the nested grid field is "Pending"
        And the value of the "Planned setup time" labelled nested numeric field of the selected row in the nested grid field is "1.00 h"
        And the value of the "Planned run time" labelled nested numeric field of the selected row in the nested grid field is "10.00 h"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the nested grid field is "R 110.00"


    Scenario: 03 - Check the components table

        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        # use ultrawide desktop allows to check planned cost on the component line, otherwise the value is not found (probably because the robot
        # doesn't manage to scroll till this field)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Check the components table
        Given selects the "Components" labelled navigation anchor on the main page
        And the user selects the "Components" labelled table field on the main page
        # Add Item ID column to test if correct Items are in the table
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When searches for "Item ID" in the lookup dialog on the sidebar
        And the user ticks the table column configuration with "Item ID" name on the sidebar
        Then the table column configuration with name "Item ID" on the sidebar is ticked
        And the user clicks the Close button of the sidebar
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the value of the "Item ID" labelled nested text field of the selected row in the table field is "Material_Item_02"
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Pending"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "0 each"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the table field is "R 11.1100"
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Not allocated"

    Scenario: 04 - Update the operation labor resource and check the updated process costs

        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        # use ultrawide desktop allows to check planned cost on the operation line, otherwise the value is not found (probably because the robot
        # doesn't manage to scroll till this field)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Open side panel of nested grid operation and change run time value
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "Operation 1" in column with header "Name" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "LABOR_RES_01" in column with header "ID" in the nested grid field
        And the value of the "ID" labelled nested text field of the selected row in the nested grid field is "LABOR_RES_01"
        # And the user expands the selected row of the nested grid field
        And the user clicks the "Edit" action of the selected row in the nested grid field
        Then the "Edit work order operation resource" titled sidebar is displayed
        And the user selects the "Run time" labelled numeric field on the sidebar
        And the user writes "20.00" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar
        # Save
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        # Check Process cost not updated automatically
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "1" in column with header "Operation number" in the nested grid field
        Then the value of the "Planned run time" labelled nested numeric field of the selected row in the nested grid field is "20.00 h"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the nested grid field is "R 110.0000"
        # click button Update Planned cost and verify Planned cost on operation has now been updated
        Given selects the "General" labelled navigation anchor on the main page
        Given the user clicks the "updateCostsButton" bound button on the main page

        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "1" in column with header "Operation number" in the nested grid field
        Then the value of the "Planned cost" labelled nested numeric field of the selected row in the nested grid field is "R 210.0000"

        # Check Planned versus actual summary table for Process cost
        Given selects the "General" labelled navigation anchor on the main page
        When the user selects the "Planned versus actual" labelled summary table field on the main page
        When the user selects the row with text "Process cost" in the "Cost" labelled column header of the summary table field
        Then the value of the "Planned" labelled nested numeric field of the selected row in the summary table field is "R 210.0000"
        And the value of the "Actual" labelled nested numeric field of the selected row in the summary table field is ""

    Scenario: 05 - Update the component resource and check the updated material costs

        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        # use ultrawide desktop allows to check planned cost on the component line, otherwise the value is not found (probably because the robot
        # doesn't manage to scroll till this field)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # ATP BUG: Unable to select row within a nested grid and edit that row. Always referring to parent row?
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        Then the "Edit work order component" titled sidebar is displayed
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar
        # Save
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # Check material cost not updated automatically
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "20 each"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the table field is "R 11.1100"

        # click button Update Planned cost and verify Material cost (Planned) has now been updated
        Given selects the "General" labelled navigation anchor on the main page
        Given the user clicks the "updateCostsButton" bound button on the main page

        When selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "20 each"
        And the value of the "Planned cost" labelled nested numeric field of the selected row in the table field is "R 22.2200"

        # Check Planned versus actual summary table for Material cost
        When selects the "General" labelled navigation anchor on the main page
        Given the user selects the "Planned versus actual" labelled summary table field on the main page
        When the user selects the row with text "Material cost" in the "Cost" labelled column header of the summary table field
        Then the value of the "Planned" labelled nested numeric field of the selected row in the summary table field is "R 22.2200"
        And the value of the "Actual" labelled nested numeric field of the selected row in the summary table field is ""

    Scenario: 06 - Exclude component line and check the material costs update

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the user clicks the "Exclude" dropdown action of the selected row of the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Excluded"

        # Restore component
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        Then the user clicks the "Include" dropdown action of the selected row of the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Pending"

    Scenario: 07 - Update the released quantity and check the updated run time and required component quantity
        When selects the "General" labelled navigation anchor on the main page
        When the user selects the "Released quantity" labelled numeric field on the main page
        And the value of the numeric field is "10"
        And the user writes "25" in the numeric field
        And the user presses Enter
        And the user waits for 5 seconds
        # Check Operations
        Given selects the "Operations" labelled navigation anchor on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "Operation 1" in column with header "Name" in the nested grid field
        Then the value of the "Planned run time" labelled nested numeric field of the selected row in the nested grid field is "50.00 h"
        # Check Components
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "25 each"

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: 08 - Allocate components

        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        # use ultrawide desktop allows to check allocation status on the component line, otherwise the value is not found (probably because the robot
        # doesn't manage to scroll till this field)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "Stock allocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        Then the user ticks the main checkbox of the selected row in the table field
        And the selected row of the table field is selected
        And the value of the "Quantity to allocate" labelled nested numeric field of the selected row in the table field is "25 each"
        # Save
        And the user clicks the "Save" labelled business action button on a modal
        # Check that it is allocated
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"

    Scenario: 09 - Create production tracking with auto tracking for WO

        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/ProductionTracking"
        Then the "Production tracking" titled page is displayed
        # Enter work order criteria
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "South Africa Headquarter & Warehouse" in the reference field
        And the user selects "South Africa Headquarter & Warehouse" in the reference field

        And the user selects the "From released item" labelled reference field on the main page
        And the user writes "Manufactured Item 2" in the reference field
        And the user selects "Manufactured Item 2" in the reference field
        # filter with work order number created previously
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "[ENV_Work_Order_Number]" in the reference field
        And the user selects "[ENV_Work_Order_Number]" in the reference field
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "[ENV_Work_Order_Number]" in the reference field
        And the user selects "[ENV_Work_Order_Number]" in the reference field

        And the user clicks in the "searchButton" bound button field on the main page

        And the user selects the "Work orders" labelled table field on the main page
        Then the table field is not empty
        # Select row
        # Dynamic row issue: Expected element not found when using the Name column to better select a row
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "Number" labelled column header of the table field

        # Tick the left checkbox
        And the user ticks the main checkbox of the selected row in the table field
        # Get/Set the Actual qty from the Released qty
        And the user stores the value of the "Released qty." labelled nested numeric field of the selected row in the table field with the key "[ENV_Quantity]"
        And the user writes "[ENV_Quantity]" in the "Actual qty" labelled nested numeric field of the selected row in the table field
        # Tick the checkboxes for automatic material and time tracking
        And the user clicks the "Automatic material tracking" labelled nested field of the selected row in the table field
        And the user clicks the "Automatic time tracking" labelled nested field of the selected row in the table field
        # More actions - Stock detail
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "Stock details" labelled table field on a modal
        Then the table field is empty
        When the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row with text "[ENV_Quantity]" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "[ENV_Quantity]" in the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field
        And the user writes "Bulk 01" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Bulk 01" in the "Location" labelled nested field of the selected row in the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast with text "Tracking records generated: 1" is displayed

    Scenario: 10 - Close the WO and check it is completed amd business actions not visible

        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"

        # Check Planned versus actual summary table for Process cost / Material cost
        ## Process cost values ##
        Given the user selects the "Planned versus actual" labelled summary table field on the main page
        When the user selects the row with text "Process cost" in the "Cost" labelled column header of the summary table field
        Then the value of the "Planned" labelled nested numeric field of the selected row in the summary table field is "R 210.0000"
        And the value of the "Actual" labelled nested numeric field of the selected row in the summary table field is "R 510.0000"
        ## Material cost values ##
        When the user selects the row with text "Material cost" in the "Cost" labelled column header of the summary table field
        Then the value of the "Planned" labelled nested numeric field of the selected row in the summary table field is "R 22.2200"
        And the value of the "Actual" labelled nested numeric field of the selected row in the summary table field is "R 27.7800"
        And the user clicks the "Close order" labelled business action button on the main page
        And the user waits 5 seconds
        And the user clicks the "Generate" button of the Custom dialog

        # Refresh the page
        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_Work_Order_Number]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Closed"

        # Check that all these buttons are not available
        And the "Close" labelled business action button on the main page is hidden
        And the "Dimensions" labelled business action button on the main page is hidden
        And the "Save" labelled business action button on the main page is hidden
