#The goal of this test is to verify the flow of the customer dimensions.

@prerequisites
@reference_data
Feature: prerequisites-reference-data-flow-customer-dimensions

    Scenario: 01 - Setup customer dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "Customers" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "KLZA1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Kliënt ZA1"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Residential" in the reference field
        And the user selects "Residential" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
