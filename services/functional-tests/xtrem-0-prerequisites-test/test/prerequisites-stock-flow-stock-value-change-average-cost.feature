# The goal of this test is to:
# - create a stock value change for an item that is defined with a valuation method "Average unit cost",
# - post to stock,
# - control the stock journal inquiry,
# - control the new average unit cost on the item-site.
#  Item used is SVC-ITEM-AUC-2 / site is CHA-S01
#  Verify posting of stock value change generates the correct JE
@prerequisites
@inventory
Feature: prerequisites-stock-flow-stock-value-change-average-cost

    Scenario: Create stock value change for item
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueChange"
        Then the "Stock value changes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Stock value change" titled page is displayed
        # site = Site de Chavanod
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "<PERSON><PERSON><PERSON>" in the reference field
        Then the user selects "Site de Chavanod" in the reference field
        # check valuation method is read-only and empty before choosing item
        When the user selects the "Valuation method" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is ""
        # item = SVC-ITEM-AUC-2
        When the user selects the "Item" labelled reference field on the main page
        And the user writes "SVC-ITEM-AUC-2" in the reference field
        Then the user selects "Item 2 for stock value change - AUC" in the reference field
        # check valuation method is now defaulted with "Average unit cost"
        When the user selects the "Valuation method" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Average unit cost"
        # enter description
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Test SVC for Average unit cost" in the text field
        Then the value of the text field is "Test SVC for Average unit cost"
        # check date field is read-only and empty before posting the document
        When the user selects the "Date" labelled date field on the main page
        Then the date field is read-only
        And the value of the date field is ""
        # check stock status is Draft
        When the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Draft"

        # check grid is empty at the beginning - check "Add" button is enabled
        Given the user selects the "lines" bound table field on the main page
        Then the table field is empty
        Then the "Add" labelled header action button of the table field is enabled
        # create the stock value change line
        Given the user clicks the "Add" labelled header action button of the table field
        When the user selects the row with text "20" in the "Quantity" labelled column header of the table field
        # check unit cost
        Then the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "€ 15.11"
        # check new unit cost
        Then the value of the "New unit cost" labelled nested numeric field of the selected row in the table field is "€ 15.11"
        # check amount
        Then the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 302.20"
        # check new amount
        Then the value of the "New amount" labelled nested numeric field of the selected row in the table field is "€ 302.20"


        # Check business action buttons' state and Save document
        Then the "Cancel" labelled business action button on the main page is enabled
        And the "Save" labelled business action button on the main page is enabled
        And the "Post stock" labelled business action button on the main page is disabled
        # save stock value change document
        And the user clicks the "Save" labelled business action button on the main page
        # record created notification
        Then a toast with text "Record created" is displayed

    Scenario: Verify document status and action buttons of the stock value change document

        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SVC1_Number]"

        Given the user selects the "Site" labelled reference field on the main page
        Then the reference field is read-only

        Given the user selects the "Item" labelled reference field on the main page
        Then the reference field is read-only

        Given the user selects the "Date" labelled date field on the main page
        Then the value of the date field is ""

        When the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Draft"

        # check "Add" button is now disabled
        Given the user selects the "lines" bound table field on the main page
        Then the "Add" labelled header action button of the table field is disabled

        # Check business action buttons' state again
        Then the "Save" labelled business action button on the main page is hidden
        And the "Cancel" labelled business action button on the main page is hidden
        And the "Post stock" labelled business action button on the main page is enabled

    Scenario: Post stock value change for item SVC-ITEM-AUC-2

        # check error message in case the user wants to Post the document without any change in the current values
        Given the user clicks the "Post stock" labelled business action button on the main page
        Then a error toast containing text " the current and new amounts cannot be the same" is displayed
        And the user dismisses all the toasts

        # change new amount to € 307.78
        Given the user selects the "lines" bound table field on the main page
        When the user selects the row with text "20" in the "Quantity" labelled column header of the table field
        And the user writes "307.78" in the "New amount" labelled nested numeric field of the selected row in the table field
        Then the value of the "New unit cost" labelled nested numeric field of the selected row in the table field is "€ 15.39"

        # save new values
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # verify document can now be posted
        Given the user clicks the "Post stock" labelled business action button on the main page
        # And the user waits 5 seconds
        And the user refreshes the screen
        When the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Completed"
        When the user selects the "Date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"

        # Check business action buttons' state again
        Then the "Save" labelled business action button on the main page is hidden
        And the "Cancel" labelled business action button on the main page is hidden
        And the "Post stock" labelled business action button on the main page is hidden

    Scenario: Verify stock value change movement appears in stock journal inquiry

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the navigation panel

        # Enter site criteria = Site de Chavanod
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Site de Chavanod" in the filter of the table field
        And the user ticks the item with text "Site de Chavanod" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Enter item criteria = Item 2 for stock value change - AUC
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item 2 for stock value change - AUC" in the filter of the table field
        And the user ticks the item with text "Item 2 for stock value change - AUC" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user clicks the "Filter" button in the header of the table field
        And the user filters the "Quantity" labelled column in the table field with value "0"
        And the user selects the row 1 of the table field
        Then the value of the "Document" labelled nested link field of the selected row in the table field is "[ENV_SVC1_Number]"
        And the value of the "Date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Location" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Quality control" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "€ 0.2790"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "€ 5.58"

    Scenario: Control item cost has been updated correctly

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "Item 2 for stock value change - AUC" in the navigation panel
        And the user clicks the record with the text "SVC-ITEM-AUC-2" in the navigation panel
        #And the user clicks the record with the text "Item 2 for stock value change - AUC" in the navigation panel

        # edit item-site
        And selects the "Sites" labelled navigation anchor on the main page
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "Site de Chavanod" in the "Site" labelled column header of the table field
        And the user clicks the "Edit" dropdown action of the selected row of the table field

        Then the "Item-site Item SVC-ITEM-AUC-2-Site CHA-S01" titled sidebar is displayed

        And selects the "General" labelled navigation anchor on the sidebar

        When the user selects the "Average unit cost" labelled numeric field on the sidebar
        Then the value of the numeric field is "15.3890"
        When the user selects the "Stock valuation at average unit cost" labelled numeric field on the sidebar
        Then the value of the numeric field is "307.78"

        And the user clicks the "Cancel" button of the dialog on the sidebar

    Scenario: Generate the Journal Entry for Stock Value Change
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        ##set values
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Siège social S01  PARIS" in the reference field
        Then the user selects "Siège social S01  PARIS" in the reference field
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Stock value change" in the multi dropdown field
        Then the value of the multi dropdown field is "Stock value change"
        # Click Generate and check message
        And the user clicks in the "create" bound button field on the main page
        # Generate
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: Notification page for stock value change
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationHistory"
        Then the "Notification history" titled page is displayed
        And selects the "Finance" labelled navigation anchor on the main page
        ##Selecting criteria
        And the user selects the "financeTransactionDocumentType" bound multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Stock value change" in the multi dropdown field
        Then the value of the multi dropdown field is "Stock value change"
        And the user selects the "Status" labelled multi dropdown field on the main page
        ##Select status "Recorded" only
        And the user clicks in the multi dropdown field
        And the user selects "Recorded" in the multi dropdown field
        ##Check correct value was selected
        And the user clicks in the "searchFinance" bound button field on the main page
        And the user selects the "results" labelled table field on the main page
        And the user selects the row with text "[ENV_SVC1_Number]" in the "Document number" labelled column header of the table field
        Then the user stores the value of the "targetDocumentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_JENum003]"

    Scenario: Verify that the journal entry is correct for Stock Value Change
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        #Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        ## Using store value
        And the user selects the row with text "[ENV_JENum003]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_JENum003]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify status and the reference field
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Stock value change"
        #Checking that the information is correct on the grid
        When the user selects the "lines" bound nested grid field on the main page
        #Non stock row
        When the user selects row with text "******** -- Stocks de matières premières" in column with header "Account" in the nested grid field
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "€ 5.58"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "€ 5.58" in column with header "Transaction amount" in the nested grid field
        And the value of the "Financial site" labelled nested reference field of the selected row in the nested grid field is "ETS1-S01 -- Siège social S01 PARIS"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "SVC-ITEM-AUC-2 -- Item 2 for stock value change - AUC"
        When the user selects row with text "Siège social S01 PARIS" in column with header "Financial site" in the nested grid field
        And the user collapses the selected row of the nested grid field
        #Stock managed row
        And the user selects row with text "******** -- Variations des stocks de matières premières" in column with header "Account" in the nested grid field
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "€ 5.58"
        And the user expands the selected row of the nested grid field
        And the user selects row with text "€ 5.58" in column with header "Transaction amount" in the nested grid field
        And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "€ 5.58"
        And the value of the "Financial site" labelled nested numeric field of the selected row in the nested grid field is "ETS1-S01 -- Siège social S01 PARIS"
        And the value of the "Item" labelled nested text field of the selected row in the nested grid field is "SVC-ITEM-AUC-2 -- Item 2 for stock value change - AUC"
