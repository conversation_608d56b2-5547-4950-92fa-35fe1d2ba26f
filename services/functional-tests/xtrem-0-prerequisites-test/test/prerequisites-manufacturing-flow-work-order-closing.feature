# The goal of this test is to verify wo closing for Finished items with different valuation methods
# Valuation methods: standard | fifo | average
# WO used         : TST_STD | TST_AVG | TST_FIFO
# Variance Expected: AVG (negative actual adjustment) | FIFO (actual adjustment) | STD (negative variance)

@prerequisites
@manufacturing
Feature: prerequisites-manufacturing-flow-work-order-closing

    Scenario: 01- work order closing for finished good valued with average cost
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # When the user selects the "$navigationPanel" bound table field on the main page
        # And the user selects the row with text "TST_AVG" in the "number" bound column header of the table field
        # And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST_AVG" in the navigation panel
        And the user clicks the record with the text "TST_AVG" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        And the user clicks the "Close order" labelled business action button on the main page
        And the user waits 5 seconds
        And the user clicks the "Continue" button of the Custom dialog
        And the user waits 3 seconds
        And the user clicks the "Generate" button of the Custom dialog

        # Refresh the page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # When the user selects the "$navigationPanel" bound table field on the main page
        # And the user selects the row with text "TST_AVG" in the "number" bound column header of the table field
        # And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST_AVG" in the navigation panel
        And the user clicks the record with the text "TST_AVG" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Closed"

        # Check that all these buttons are not available
        And the "Close order" labelled business action button on the main page is hidden
        And the "Dimensions" labelled business action button on the main page is hidden
        And the "Save" labelled business action button on the main page is hidden

    Scenario: 02- wip transaction verification for finished good valued with average cost

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipTransactionInquiry"
        Then the "Work in progress transaction inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the user opens the filter of the "Work order" labelled column in the table field
        And the user searches "TST_AVG" in the filter of the table field
        And the user ticks the item with text "TST_AVG" in the filter of the table field
        And the user closes the filter of the "Work order" labelled column in the table field
        And the user selects the row with text "Work order negative actual cost adjustment" in the "Type" labelled column header of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested numeric field of the selected row in the table field is "TST_AVG"

    Scenario: 03- work order closing for finished good valued with fifo cost
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # When the user selects the "$navigationPanel" bound table field on the main page
        # And the user selects the row with text "TST_FIFO" in the "number" bound column header of the table field
        # And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST_FIFO" in the navigation panel
        And the user clicks the record with the text "TST_FIFO" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user clicks the "Close order" labelled business action button on the main page
        And the user waits 5 seconds
        And the user clicks the "Generate" button of the Custom dialog

        # Refresh the page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # When the user selects the "$navigationPanel" bound table field on the main page
        # And the user selects the row with text "TST_FIFO" in the "number" bound column header of the table field
        # And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST_FIFO" in the navigation panel
        And the user clicks the record with the text "TST_FIFO" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Closed"

    Scenario: 04- wip transaction verification for finished good valued with fifo cost

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipTransactionInquiry"
        Then the "Work in progress transaction inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the user opens the filter of the "Work order" labelled column in the table field
        And the user searches "TST_FIFO" in the filter of the table field
        And the user ticks the item with text "TST_FIFO" in the filter of the table field
        And the user closes the filter of the "Work order" labelled column in the table field
        And the user selects the row with text "Work order actual cost adjustment" in the "Type" labelled column header of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested numeric field of the selected row in the table field is "TST_FIFO"

    Scenario: 05- work order closing for finished good valued with standard cost
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # When the user selects the "$navigationPanel" bound table field on the main page
        # And the user selects the row with text "TST_STD" in the "number" bound column header of the table field
        # And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST_STD" in the navigation panel
        And the user clicks the record with the text "TST_STD" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        And the user clicks the "Close order" labelled business action button on the main page
        And the user waits 5 seconds
        And the user clicks the "Continue" button of the Custom dialog
        And the user waits 3 seconds
        And the user clicks the "Generate" button of the Custom dialog

    Scenario: 06- wip transaction verification for finished good valued with standard cost

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipTransactionInquiry"
        Then the "Work in progress transaction inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the user opens the filter of the "Work order" labelled column in the table field
        And the user searches "TST_STD" in the filter of the table field
        And the user ticks the item with text "TST_STD" in the filter of the table field
        And the user closes the filter of the "Work order" labelled column in the table field
        And the user selects the row with text "Work order variance" in the "Type" labelled column header of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested numeric field of the selected row in the table field is "TST_STD"
