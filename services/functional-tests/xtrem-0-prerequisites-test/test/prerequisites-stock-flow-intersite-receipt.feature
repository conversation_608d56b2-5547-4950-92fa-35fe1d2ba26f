# This test aims to validate the creation of a stock transfer receipt for supply chain. This will confirm stability on the new feature
# The focus will be on verifying field validations and creation of the Stock transfer receipt without issue

@prerequisites
@inventory
Feature: prerequisites-stock-flow-intersite-receipt
    Scenario: Add lock - LCK_FIR
        Given the user opens the application on a HD desktop
        And the user adds the lock entry "LCK_FIR"

    Scenario: 01 - Verify the user is able to revert the confirmed shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferShipment"
        Then the "Stock transfer shipments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TS250001" in the "Number" labelled column header of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "TS250001"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Revert" labelled business action button on the main page
        And the user clicks the "Revert" button of the Confirm dialog
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to process"
        When the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is current
        Then the status of the "Post" item of the step-sequence is incomplete
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Ready to process"
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Status" labelled label field on the sidebar
        And the value of the label field is "Ready to process"
        And the user refreshes the screen
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to ship"
        When the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is complete
        Then the status of the "Post" item of the step-sequence is current
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Ready to ship"
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Status" labelled label field on the sidebar
        And the value of the label field is "Ready to ship"

    Scenario: 02 - Verify the user can create a stock transfer receipt
        And the user refreshes the screen
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        # need to refresh because it remains stuck on the "Posting in progress" status
        And the user refreshes the screen
        When the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is complete
        Then the status of the "Post" item of the step-sequence is complete

    Scenario: 03 - Verify status on receipt
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "linkToStockTransferReceiptLine" bound table field on the sidebar
        And the user stores the value of the "Receipt number" labelled nested link field of the selected row in the table field with the key "[ENV_STR_01]"
        And the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferReceipt"
        And the "Stock transfer receipts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_STR_01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to process"
        And the user selects the "receiptStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is incomplete
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Ready to process"
        Then the value of the "Stock details" labelled nested label field of the selected row in the table field is "Required"

    Scenario: 04 - Verifying that when dimensions are added to the shipment, it is transferred to the receipt
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Task" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"

    Scenario: 05 - Verify that dimensions on receipt can be edited
        And the user selects the "Channel" labelled reference field on a modal
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Ok" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal

    Scenario: 06 - Verify notes can be added to the stock transfer receipt
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the user writes "this is an internal note @123 !" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_NOTETR]"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTETR]"

    Scenario: 07 - Verify that the stock details can be added too the receipt
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Stock details" labelled nested label field of the selected row in the table field is "Entered"

    Scenario: 08 - Post stock and verify statuses
        And the user dismisses all the toasts
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user clicks the "Confirm" button of the Confirm dialog
        And a toast containing text "The stock transfer receipt was posted." is displayed
        # need to refresh because it remains stuck on the "Posting in progress" status
        And the user refreshes the screen
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Received"
        And the user selects the "receiptStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Received"

    Scenario: 09 - Verify status on stock transfer shipment
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Origin" labelled navigation anchor on the sidebar
        And the user selects the "linkToStockTransferShipmentLine" bound table field on the sidebar
        And the user stores the value of the "Shipment number" labelled nested link field of the selected row in the table field with the key "[ENV_STS_01]"
        And the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferShipment"
        And the "Stock transfer shipments" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_STS_01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Shipped"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Shipped"
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Status" labelled label field on the sidebar
        And the value of the label field is "Shipped"
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "receivingStatus" labelled label field on the sidebar
        And the value of the label field is "Received"
        And the user selects the "linkToStockTransferReceiptLine" bound table field on the sidebar
        And the user selects the row 1 of the table field
        Then the value of the "Receipt status" labelled nested label field of the selected row in the table field is "Received"
        And selects the "Origin" labelled navigation anchor on the sidebar
        And the user selects the "linkToStockTransferOrderLine" bound table field on the sidebar
        And the user selects the row 1 of the table field
        Then the value of the "Order status" labelled nested label field of the selected row in the table field is "Closed"

    Scenario: 10 - Verify status on stock transfer order
        And the user stores the value of the "Order number" labelled nested link field of the selected row in the table field with the key "[ENV_ST0_01]"
        And the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        And the "Stock transfer orders" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_ST0_01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Received"
        And the user selects the "orderStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Ship" item of the step-sequence is complete
        And the status of the "Receive" item of the step-sequence is complete
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Status" labelled label field on the sidebar
        And the value of the label field is "Closed"
        And selects the "Delivery" labelled navigation anchor on the sidebar
        And the user selects the "shippingStatus" labelled label field on the sidebar
        And the value of the label field is "Shipped"
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "shippingStatus" labelled label field on the sidebar
        And the value of the label field is "Shipped"
        And the user selects the "receivingStatus" labelled label field on the sidebar
        And the value of the label field is "Received"

    Scenario: 11 - Verify update on Stock detail inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        When the user selects the "Including in-transit" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Site" labelled column in the table field with value "Stock Tranfer Uk Site 2"
        And the user filters the "Lot" labelled column in the table field with value "24000032"
        And the user selects the row with text "0 each" in the "In-transit quantity" labelled column header of the table field
        Then the value of the "On-hand quantity" labelled nested text field of the selected row in the table field is "20 each"
        Then the value of the "Available quantity" labelled nested text field of the selected row in the table field is "20 each"

    Scenario: 12 - Verify update on Stock inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockInquiryBound"
        Then the "Stock inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Site" labelled column in the table field with value "Stock Tranfer Uk Site 2"
        And the user filters the "Item" labelled column in the table field with value "Pressure transducer"
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Quantity on hand" labelled nested text field of the selected row in the table field is "1,027 each"
        And the value of the "Stock value" labelled nested text field of the selected row in the table field is "£ 103,165.54"

    Scenario: 13 - Verify the stock journal inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Site" labelled column in the table field with value "Stock Tranfer Uk Site 2"
        And the user filters the "Date" labelled column in the table field with value "02/03/2025"
        And the user selects the row with text "Stock transfer receipt line" in the "Document line type" labelled column header of the table field
        And the value of the "Quantity" labelled nested text field of the selected row in the table field is "20 each"
        And the value of the "Order cost" labelled nested text field of the selected row in the table field is "£ 100.8875"
        And the value of the "Order amount" labelled nested text field of the selected row in the table field is "£ 2,017.75"
        And the value of the "Movement amount" labelled nested text field of the selected row in the table field is "£ 2,009.07"

    Scenario: Remove lock - LCK_FIR
        And the user removes the lock entry "LCK_FIR"
