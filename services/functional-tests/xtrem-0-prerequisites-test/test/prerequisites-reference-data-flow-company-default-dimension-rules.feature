#The goal of this test is to verify the flow of the company default dimension rules.

@prerequisites
@reference_data
Feature: prerequisites-reference-data-flow-company-default-dimension-rules

    Scenario: 01 - Setup company default dimension rules (Company 1)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "ZA01" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Maatskappy ZA1"
        And selects the "Dimensions" labelled navigation anchor on the main page
        When the user selects the "Default dimension rules" labelled table field on the main page
        And the user clicks the "Add line" labelled header action button of the table field
        And the user selects the row with text " " in the "Document and origin" labelled column header of the table field
        And the user clicks the "Document and origin" labelled nested field of the selected row in the table field
        And the user selects "Purchasing direct" in the "Document and origin" labelled nested field of the selected row in the table field
        And the user clicks the "Project" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Project" labelled nested field of the selected row in the table field
        And the user clicks the "Department" labelled nested field of the selected row in the table field
        And the user selects "Supplier" in the "Department" labelled nested field of the selected row in the table field
        And the user clicks the "Channel" labelled nested field of the selected row in the table field
        And the user selects "Supplier" in the "Channel" labelled nested field of the selected row in the table field
        And the user clicks the "Add line" labelled header action button of the table field
        And the user selects the row with text " " in the "Document and origin" labelled column header of the table field
        And the user clicks the "Document and origin" labelled nested field of the selected row in the table field
        And the user selects "Manufacturing direct" in the "Document and origin" labelled nested field of the selected row in the table field
        And the user clicks the "Project" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Project" labelled nested field of the selected row in the table field
        And the user clicks the "Department" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Department" labelled nested field of the selected row in the table field
        And the user clicks the "Channel" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Channel" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 02 - Setup company default dimension rules (Company 2)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "ZA02" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Maatskappy ZA2"
        And selects the "Dimensions" labelled navigation anchor on the main page
        When the user selects the "Default dimension rules" labelled table field on the main page
        And the user clicks the "Add line" labelled header action button of the table field
        And the user selects the row with text " " in the "Document and origin" labelled column header of the table field
        And the user clicks the "Document and origin" labelled nested field of the selected row in the table field
        And the user selects "Sales direct" in the "Document and origin" labelled nested field of the selected row in the table field
        And the user clicks the "Project" labelled nested field of the selected row in the table field
        And the user selects "Customer" in the "Project" labelled nested field of the selected row in the table field
        And the user clicks the "Department" labelled nested field of the selected row in the table field
        And the user selects "Customer" in the "Department" labelled nested field of the selected row in the table field
        And the user clicks the "Channel" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Channel" labelled nested field of the selected row in the table field
        And the user clicks the "Add line" labelled header action button of the table field
        And the user selects the row with text " " in the "Document and origin" labelled column header of the table field
        And the user clicks the "Document and origin" labelled nested field of the selected row in the table field
        And the user selects "Manufacturing direct" in the "Document and origin" labelled nested field of the selected row in the table field
        And the user clicks the "Project" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Project" labelled nested field of the selected row in the table field
        And the user clicks the "Department" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Department" labelled nested field of the selected row in the table field
        And the user clicks the "Channel" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Channel" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 03 - Setup company default dimension rules (Company 3)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "ZA03" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Maatskappy ZA3"
        And selects the "Dimensions" labelled navigation anchor on the main page
        When the user selects the "Default dimension rules" labelled table field on the main page
        And the user clicks the "Add line" labelled header action button of the table field
        And the user selects the row with text " " in the "Document and origin" labelled column header of the table field
        And the user clicks the "Document and origin" labelled nested field of the selected row in the table field
        And the user selects "Stock direct" in the "Document and origin" labelled nested field of the selected row in the table field
        And the user clicks the "Project" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Project" labelled nested field of the selected row in the table field
        And the user clicks the "Department" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Department" labelled nested field of the selected row in the table field
        And the user clicks the "Channel" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Channel" labelled nested field of the selected row in the table field
        And the user clicks the "Add line" labelled header action button of the table field
        And the user selects the row with text " " in the "Document and origin" labelled column header of the table field
        And the user clicks the "Document and origin" labelled nested field of the selected row in the table field
        And the user selects "Manufacturing direct" in the "Document and origin" labelled nested field of the selected row in the table field
        And the user clicks the "Project" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Project" labelled nested field of the selected row in the table field
        And the user clicks the "Department" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Department" labelled nested field of the selected row in the table field
        And the user clicks the "Channel" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Channel" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
