# The goal of this test is to check status of all intacct integration is disabled
# Verify that the instance can be activated/deactivated

@prerequisites
@reference_data
Feature: prerequisites-flow-intacct-config

    <PERSON><PERSON><PERSON>: <Instance> - check status of all intacct integrations
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-intacct/Intacct"
        Then the "Sage Intacct configurations" titled page is displayed
        When the user selects the "Sage Intacct configurations" labelled table field on the main page
        And the user selects the row with text "<Instance>" in the "ID" labelled column header of the table field
        Then the "Active" labelled nested switch field of the selected row in the table field is set to "OFF"
        Examples:
            | Instance      |
            | DEMO          |
            | DEV           |
            | Martini Z<PERSON>    |
            | QA            |
            | XTREM         |
            | XTREM-GB      |
            | XTREM-ZA-DEMO |


    Scenario: Add/Remove non-legislative countries to the Intacct configuration defaults
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-intacct/Intacct"
        Then the "Sage Intacct configurations" titled page is displayed
        When the user selects the "Sage Intacct configurations" labelled table field on the main page
        And the user selects the row with text "XTREM-ZA-DEMO" in the "ID" labelled column header of the table field
        And the user clicks the "ID" labelled nested field of the selected row in the table field
        And selects the "Defaults" labelled navigation anchor on the main page
        And the user selects the "Legislation" labelled reference field on the main page
        And the value of the reference field is "South Africa"
        And the user selects the "Chart of accounts" labelled reference field on the main page
        And the value of the reference field is "ZA chart of accounts"
        And the user selects the "Transaction integration level" labelled dropdown-list field on the main page
        And the value of the dropdown-list field is "Top level"
        And the user clicks the "addLine" bound header action button on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "United Kingdom" in the "Country" labelled nested reference field of the selected row in the table field
        And the user selects "United Kingdom" in the "Country" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "United Kingdom" in the "Country" labelled column header of the table field
        And the value of the "SDMO tax solution" labelled nested reference field of the selected row in the table field is "United Kingdom tax solution"
        And the value of the "Tax category" labelled nested reference field of the selected row in the table field is "Value Added Tax"
        And the user clicks the "Delete line" dropdown action of the selected row of the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
