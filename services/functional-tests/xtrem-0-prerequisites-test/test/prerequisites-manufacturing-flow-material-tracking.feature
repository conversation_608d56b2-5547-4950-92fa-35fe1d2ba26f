#This test can only be executed with sage.
#The goal of this test is to verify material tracking
#Item used: Pressure Transducer
# Uncomment lines [20-26-32-84-90-96] when BUG is fixed:https://jira.sage.com/browse/XT-56897

@prerequisites
@manufacturing
Feature: prerequisites-manufacturing-flow-material-tracking

    Scenario: Verify that the information on Work order is correct
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # When the user selects the "$navigationPanel" bound table field on the main page
        # And the user selects the row with text "WO230001" in the "number" bound column header of the table field
        # And the user clicks the "site__id" bound nested field of the selected row in the table field
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "WO230001" in the navigation panel
        And the user clicks the record with the text "WO230001" in the navigation panel
        #Verify the status
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Pending"

        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        #Verify Items details for Item1
        And the user selects the row with text "Pressure sensor" in the "name" bound column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Pending"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "0 each"
        And the value of the "Progress" labelled nested progress field of the selected row in the table field is "0"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "£ 0.0000"
        #Verify Items details for Item2
        And the user selects the row with text "£ 44.0000" in the "plannedCost" bound column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Pending"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "0 each"
        And the value of the "Progress" labelled nested progress field of the selected row in the table field is "0"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "£ 0.0000"
        #Verify Items details for Item3
        And the user selects the row with text "£ 22.0000" in the "plannedCost" bound column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Pending"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "0 each"
        And the value of the "Progress" labelled nested progress field of the selected row in the table field is "0"
        Then the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "£ 0.0000"

    Scenario: Verifying that a user can do material tracking
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/MaterialTracking"
        Then the "Material tracking" titled page is displayed
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "WO230001" in the reference field
        And the user selects "WO230001" in the reference field
        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "WO230001" in the reference field
        And the user selects "WO230001" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "components" bound table field on the main page
        #Allocate stock for Item1
        And the user selects the row with text "Electrical connector" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "986 each" in the "Available quantity" labelled column header of the table field
        And the user writes "10" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Allocate" labelled business action button on a modal
        #Allocate stock for Item2
        And the user selects the "components" bound table field on the main page
        And the user selects the row with text "Transducer body 30x5" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "1,006 each" in the "Available quantity" labelled column header of the table field
        And the user writes "10" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Allocate" labelled business action button on a modal
        #Allocate stock for Item3
        And the user selects the "components" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Component description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "964 each" in the "Available quantity" labelled column header of the table field
        And the user writes "10" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Allocate" labelled business action button on a modal
        #Generate
        And the user clicks the "Generate" labelled business action button on the main page
        Then a toast containing text "Tracking records generated:" is displayed

    Scenario: Verify that the information on Work order is updated correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        # When the user selects the "$navigationPanel" bound table field on the main page
        # And the user selects the row with text "WO230001" in the "number" bound column header of the table field
        # And the user clicks the "site__id" bound nested field of the selected row in the table field
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "WO230001" in the navigation panel
        And the user clicks the record with the text "WO230001" in the navigation panel
        #Verify the status
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"

        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        #Verify Items details for Item1 are updated correctly
        And the user selects the row with text "Pressure sensor" in the "name" bound column header of the table field
        # And the value of the "Status" labelled nested text field of the selected row in the table field is "Completed"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the value of the "Progress" labelled nested progress field of the selected row in the table field is "100"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "£ 110.0000"
        #Verify Items details for Item2 are updated correctly
        And the user selects the row with text "£ 44.0000" in the "plannedCost" bound column header of the table field
        # And the value of the "Status" labelled nested text field of the selected row in the table field is "Completed"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the value of the "Progress" labelled nested progress field of the selected row in the table field is "100"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "£ 44.0000"
        #Verify Items details for Item3 are updated correctly
        And the user selects the row with text "£ 22.0000" in the "plannedCost" bound column header of the table field
        # And the value of the "Status" labelled nested text field of the selected row in the table field is "Completed"
        And the value of the "Consumed quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the value of the "Progress" labelled nested progress field of the selected row in the table field is "100"
        Then the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "£ 22.0000"

    Scenario: Verify the quantity of items on Material Issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/MaterialTrackingInquiry"
        Then the "Material issues" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "WO230001" in the "workOrder__number" bound column header of the table field
        And the user stores the value of the "number" bound nested text field of the selected row in the table field with the key "[EVN_TRCKNUM01]"
        And the user clicks the "Work order name" labelled nested field of the selected row in the table field
        #Verify status for material issue
        And the user selects the "Stock status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "lines" bound table field on the main page
        #Verify quantity & details for item 1
        And the user selects the row with text "Pressure sensor" in the "item__name" bound column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "501" in the "Owner" labelled column header of the table field
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "10 each"
        And the user clicks the "Cancel" labelled business action button on a modal
        #Verify quantity & details for item 2
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Transducer body 30x5" in the "item__name" bound column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "501" in the "Owner" labelled column header of the table field
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "10 each"
        And the user clicks the "Cancel" labelled business action button on a modal
        #Verify quantity & details for item 3
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Electrical connector" in the "item__name" bound column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "501" in the "Owner" labelled column header of the table field
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "10 each"
        Then the user clicks the "Cancel" labelled business action button on a modal

    Scenario: Verifying the movement on Stock journal inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        #Verify information for Item1
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Electrical connector" in the filter of the table field
        And the user ticks the item with text "Electrical connector" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field
        And the value of the "Document" labelled nested text field of the selected row in the table field is "[EVN_TRCKNUM01]"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-10 each"

        #Verify information for Item2
        Then the user opens the filter of the "Item" labelled column in the table field
        Then the user clicks the clear selected items link in the filter menu of the "Item" labelled column of the table field
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Transducer body 30x5" in the filter of the table field
        And the user ticks the item with text "Transducer body 30x5" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field
        And the value of the "Document" labelled nested text field of the selected row in the table field is "[EVN_TRCKNUM01]"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-10 each"

        #Verify information for Item3
        Then the user opens the filter of the "Item" labelled column in the table field
        Then the user clicks the clear selected items link in the filter menu of the "Item" labelled column of the table field
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure sensor" in the filter of the table field
        And the user ticks the item with text "Pressure sensor" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field
        And the value of the "Document" labelled nested text field of the selected row in the table field is "[EVN_TRCKNUM01]"
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-10 each"
