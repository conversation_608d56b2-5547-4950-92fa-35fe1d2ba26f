# The purpose of this test is to check the MRP calculation request with / without option "include sales quote" ticked.

@prerequisites
@inventory
Feature: prerequisites-stock-flow-mrp-calculation-includes-sales-quote
    Scenario: Add lock - LCK_CREATESO

        Given the user opens the application on a HD desktop
        And the user adds the lock entry "LCK_CREATESO"

    Scenario: 01- Update existing sales quote MRP_SALES_QUOTE_1 to today's date + 14

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        # Select sales order with number MRP_SALES_QUOTE_1
        And the user selects the row with text "MRP_SALES_QUOTE_1" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify the Sales order ID number
        Given the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "MRP_SALES_QUOTE_1"

        # Verify sales site = Site de Chavanod
        Given the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "Site de Chavanod"

        # Verify sold to customer = MRP first customer
        Given the user selects the "Sold-to customer *" labelled reference field on the main page
        Then the value of the reference field is "MRP first customer"

        # Change requested delivery date to T+14 on sales quote line
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "MRP Good 1 - Manuf Sales quote" in the "Item" labelled column header of the table field
        And the user writes a generated date with value "T+14" in the "Shipping date" labelled nested date field of the selected row in the table field
        And the user presses Enter

        # Save sales quote
        And the user clicks the "Save" labelled business action button on the main page

        And the user dismisses all the toasts

    Scenario: 02- Update existing sales quote MRP_SALES_QUOTE_2 to today's date + 14

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        # Select sales order with number MRP_SALES_QUOTE_2
        And the user selects the row with text "MRP_SALES_QUOTE_2" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify the Sales order ID number
        Given the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "MRP_SALES_QUOTE_2"

        # Verify sales site = Site de Chavanod
        Given the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "Site de Chavanod"

        # Verify sold to customer = MRP first customer
        Given the user selects the "Sold-to customer *" labelled reference field on the main page
        Then the value of the reference field is "MRP first customer"

        # Change requested delivery date to T+14 on sales quote line
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "MRP Good 2 - Purch Sales quote" in the "Item" labelled column header of the table field
        And the user writes a generated date with value "T+14" in the "Shipping date" labelled nested date field of the selected row in the table field
        And the user presses Enter

        # Save sales quote
        And the user clicks the "Save" labelled business action button on the main page

        And the user dismisses all the toasts

    Scenario: Remove lock - LCK_CREATESO

        And the user removes the lock entry "LCK_CREATESO"

    Scenario: 03- Launch MRP calculation request with option "Include sales quotes" ticked

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/MrpCalculationRequest"
        Then the "MRP calculation request" titled page is displayed

        # Enter description
        When the user selects the "Description" labelled text field on the main page
        And the user writes "MRP - Sales quote included" in the text field
        And the user stores the value of the text field with the key "[ENV_MRP_Description_1]"

        # Enter single site = Site de Chavanod
        When the user selects the "Site" labelled multi reference field on the main page
        And the user writes "Chavanod" in the multi reference field
        And the user selects "Site de Chavanod" in the multi reference field

        # Enter item range from MRP Good 1 to MRP Good 2
        When the user selects the "From item" labelled reference field on the main page
        And the user writes "MRP Good 1" in the reference field
        And the user selects "MRP Good 1 - Manuf Sales quote" in the reference field

        When the user selects the "To item" labelled reference field on the main page
        And the user writes "MRP Good 2" in the reference field
        And the user selects "MRP Good 2 - Purch Sales quote" in the reference field

        # Confirm value of period in weeks is set to 5
        When the user selects the "Period in weeks" labelled numeric field on the main page
        Then the value of the numeric field is "5"

        # Make sure checkbox "Explode bill of material" is unticked
        When the user selects the "Explode bill of material" labelled checkbox field on the main page
        And the user unticks the checkbox field
        Then the value of the checkbox field is "false"

        # Make sure checkbox "Include sales quote" is ticked
        When the user selects the "Include sales quotes" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # Submit and calculate the result
        When the user clicks the "Calculate" labelled business action button on the main page
        Then a toast containing text "MRP calculation request sent" is displayed

    Scenario: 04- Launch MRP calculation request without option "Include sales quote"

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/MrpCalculationRequest"
        Then the "MRP calculation request" titled page is displayed

        # Enter description
        When the user selects the "Description" labelled text field on the main page
        And the user writes "MRP - Sales quote excluded" in the text field
        And the user stores the value of the text field with the key "[ENV_MRP_Description_2]"

        # Enter single site = Site de Chavanod
        When the user selects the "Site" labelled multi reference field on the main page
        And the user writes "Chavanod" in the multi reference field
        And the user selects "Site de Chavanod" in the multi reference field

        # Enter item range from MRP Good 1 to MRP Good 2
        When the user selects the "From item" labelled reference field on the main page
        And the user writes "MRP Good 1" in the reference field
        And the user selects "MRP Good 1 - Manuf Sales quote" in the reference field

        When the user selects the "To item" labelled reference field on the main page
        And the user writes "MRP Good 2" in the reference field
        And the user selects "MRP Good 2 - Purch Sales quote" in the reference field

        # Confirm value of period in weeks is set to 5
        When the user selects the "Period in weeks" labelled numeric field on the main page
        Then the value of the numeric field is "5"

        # Make sure checkbox "Explode bill of material" is unticked
        When the user selects the "Explode bill of material" labelled checkbox field on the main page
        And the user unticks the checkbox field
        Then the value of the checkbox field is "false"

        # Make sure checkbox "Include sales quote" is unticked
        When the user selects the "Include sales quotes" labelled checkbox field on the main page
        And the user unticks the checkbox field
        Then the value of the checkbox field is "false"

        # Submit and calculate the result
        When the user clicks the "Calculate" labelled business action button on the main page
        Then a toast containing text "MRP calculation request sent" is displayed

    Scenario: 05- Check calculation results with option "Include sales quote"

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed

        # Search for corresponding request
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_MRP_Description_1]" in the "Description" labelled column header of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        Then the "MRP calculation result MRP - Sales quote included" titled page is displayed

        # Verify description = MRP - Sales quote included
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "MRP - Sales quote included"

        # Verify calculation date = today
        When the user selects the "Calculation date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"

        # Verify the table details
        Given the user selects the "Details" labelled table field on the main page

        # Filter to keep lines for MRP_GOOD_1_SALES_QUOTE item only
        And the user filters the "Quantity" labelled column in the table field with value "4"
        ## Check suggestion to cover safety stock for MRP_GOOD_1_SALES_QUOTE - replenishment method = Production
        When the user selects the row with text "MRP Good 1 - Manuf Sales quote" in the "Item" labelled column header of the table field
        Then the value of the "End date" labelled nested date field of the selected row in the table field is a generated date with value "T+7"
        And the value of the "Start date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Replenishment method" labelled nested text field of the selected row in the table field is "Production"

        ## Check suggestion to cover sales quote for MRP_GOOD_1_SALES_QUOTE - replenishment method = Production
        And the user filters the "Quantity" labelled column in the table field with value "20"
        When the user selects the row with text "MRP Good 1 - Manuf Sales quote" in the "Item" labelled column header of the table field
        Then the value of the "End date" labelled nested date field of the selected row in the table field is a generated date with value "T+14"
        And the value of the "Start date" labelled nested date field of the selected row in the table field is a generated date with value "T+7"
        And the value of the "Replenishment method" labelled nested text field of the selected row in the table field is "Production"
        # Filter to keep lines for MRP_GOOD_2_SALES_QUOTE item only
        Given the user selects the "Details" labelled table field on the main page
        When the user clicks the "Show table filters" labelled button of the table field
        Then the user clicks the remove all filters button in the table field
        When the user clicks the "Hide table filters" labelled button of the table field

        And the user filters the "Quantity" labelled column in the table field with value "10"
        When the user selects the row with text "MRP Good 2 - Purch Sales quote" in the "Item" labelled column header of the table field

        Then the value of the "End date" labelled nested date field of the selected row in the table field is a generated date with value "T+7"
        And the value of the "Start date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Replenishment method" labelled nested text field of the selected row in the table field is "Purchasing"

        ## Check suggestion to cover sales quote for MRP_GOOD_2_SALES_QUOTE - replenishment method = Purchasing
        And the user filters the "Quantity" labelled column in the table field with value "20"
        When the user selects the row with text "MRP Good 2 - Purch Sales quote" in the "Item" labelled column header of the table field

        Then the value of the "End date" labelled nested date field of the selected row in the table field is a generated date with value "T+14"
        And the value of the "Start date" labelled nested date field of the selected row in the table field is a generated date with value "T+7"
        And the value of the "Replenishment method" labelled nested text field of the selected row in the table field is "Purchasing"


    Scenario: 06- Check calculation results without option "Include sales quote"

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed

        # Search for corresponding request
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_MRP_Description_2]" in the "Description" labelled column header of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        Then the "MRP calculation result MRP - Sales quote excluded" titled page is displayed

        # Verify description = MRP - Sales quote excluded
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "MRP - Sales quote excluded"

        # Verify calculation date = today
        When the user selects the "Calculation date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"

        # Verify the table details
        Given the user selects the "Details" labelled table field on the main page

        # Filter on quantity = 20 to verify that the grid is empty (only sales quote have a quantity of 20)
        When the user filters the "Quantity" labelled column in the table field with value "20"
        Then the table field is empty
