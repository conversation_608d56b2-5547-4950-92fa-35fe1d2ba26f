

# The goal of this test is to verify that the user can create journal entries inquiry and filter it.
# The focus is for the test to verify that the user successfully creates a journal after posting a stock transfer shipment and stock transfer receipt.
# NOTES:
#  - A stock transfer shipment should be in shipped status
#  - Journals can only be generated once per instance


@prerequisites
@inventory
Feature: prerequisites-stock-flow-intersite-journal-entries-inquiry

    Scenario: 01 - Post the stock transfer shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferShipment"
        Then the "Stock transfer shipments" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the row with text "TS250010" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Stock transfer shipment TS250010" titled page is displayed
        When the user clicks the "Post Stock" labelled business action button on the main page
        Then the dialog title is "Confirm posting"
        When the user clicks the "Confirm" button of the dialog on the main page
        Then a toast containing text "The stock transfer shipment was posted" is displayed

    Scenario: 02 - Generate journal entry for the stock transfer shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Stock Tranfer DE Site 1" in the reference field
        And the user selects "Stock Tranfer DE Site 1" in the reference field
        And the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Stock transfer shipment" in the multi dropdown field
        And the value of the multi dropdown field is "Stock transfer shipment"
        When the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 03 - Verify the journal entry
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Description" labelled column in the table field with value "Stock transfer shipment"
        And the user filters the "Financial site" labelled column in the table field with value "Stock Tranfer DE Site 1"
        And the user selects the row with text "Stock transfer shipment" in the "Description" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested text field of the selected row in the table field with the key "[ENV_JESTS]"
        When the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Journal entry [ENV_JESTS]" titled page is displayed
        Then the "General" labelled navigation anchor is selected
        And the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_JESTS]"
        And the user selects the "Posting date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"
        And the user selects the "Financial site" labelled reference field on the main page
        Then the value of the reference field is "Stock Tranfer DE Site 1"
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Stock transfer shipment"
        And the user selects the "Posting status" labelled label field on the main page
        Then the value of the label field is "Posted"
        And the user selects the "lines" labelled nested grid field on the main page
        And the user selects row with text "1590 -- Durchlaufende Posten" in column with header "Account" in the nested grid field
        And the user selects row with text "3970 -- Roh-, Hilfs- und Betriebsstoffe (Bestand)" in column with header "Account" in the nested grid field
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "postingDetails" bound table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Document type" labelled nested reference field of the selected row in the table field is "Stock transfer shipment"
        Then the value of the "Document number" labelled nested reference field of the selected row in the table field is "TS250010"
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Posted"

    Scenario: 04 - Create stock transfer receipt from the previously posted stock transfer shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferShipment"
        Then the "Stock transfer shipments" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the row with text "TS250010" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Stock transfer shipment TS250010" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "linkToStockTransferReceiptLine" bound table field on the sidebar
        And the user stores the value of the "Receipt number" labelled nested link field of the selected row in the table field with the key "[ENV_STRfromSTS]"
        And the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferReceipt"
        And the "Stock transfer receipts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_STRfromSTS]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the "Stock transfer receipt [ENV_STRfromSTS]" titled page is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user clicks the "Post Stock" labelled business action button on the main page
        And the dialog title is "Confirm posting"
        And the user clicks the "Confirm" button of the dialog on the main page
        Then a toast containing text "The stock transfer receipt was posted" is displayed

    Scenario: 05 - Generate journal entry for the stock transfer receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Stock Tranfer DE Site 1" in the reference field
        And the user selects "Stock Tranfer DE Site 1" in the reference field
        And the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Stock transfer receipt" in the multi dropdown field
        And the value of the multi dropdown field is "Stock transfer receipt"
        When the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 06 - Verify that the journal entry was posted
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Description" labelled column in the table field with value "Stock transfer receipt"
        And the user filters the "Financial site" labelled column in the table field with value "Stock Tranfer DE Site 1"
        And the user selects the row with text "Stock transfer receipt" in the "Description" labelled column header of the table field
        Then the value of the "Posting status" labelled nested label field of the selected row in the table field is "Posted"
