#This test can only be executed with sage.
#The goal of this test is to verify that the user can create journal entries inquiry and filter it.

@prerequisites
@finance
Feature: prerequisites-finance-flow-journal-entries-inquiry

   Scenario: 01-Verify that the user can not see lines when using other site as filter
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntryInquiry"
      Then the "Journal entry inquiry" titled page is displayed
      #Creating the journal entry inquiry
      When the user selects the "companyFilter" bound multi reference field on the main page
      And the user writes "Chem.Chicago" in the multi reference field
      And the user selects "Chem.Chicago" in the multi reference field
      Then the user clicks in the "searchButton" bound button field on the main page
      And the user selects the "journalEntryLines" bound table field on the main page
      Then the table field is empty

   Scenario: 02-Verify that the user can not see lines when using correct site and end date -1 current date as filter
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntryInquiry"
      Then the "Journal entry inquiry" titled page is displayed
      #Creating the journal entry inquiry
      When the user selects the "companyFilter" bound multi reference field on the main page
      And the user writes "TE Connectivity" in the multi reference field
      And the user selects "TE Connectivity" in the multi reference field
      And the user selects the "End date" labelled date field on the main page
      And the user writes "09/03/2023" in the date field
      And the user presses Enter
      And the user clicks in the "searchButton" bound button field on the main page
      And the user selects the "journalEntryLines" bound table field on the main page
      Then the table field is empty

   Scenario: 03-Verify that the user can see lines when using correct start date
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntryInquiry"
      Then the "Journal entry inquiry" titled page is displayed
      #Creating the journal entry inquiry
      When the user selects the "Start date" labelled date field on the main page
      And the user writes "09/04/2023" in the date field
      And the user presses Enter
      And the user clicks in the "searchButton" bound button field on the main page
      And the user selects the "journalEntryLines" bound table field on the main page
      And the table field is not empty
      And the user selects the "journalEntryLines" bound table field on the main page
      When the user filters the "Number" labelled column in the table field with value "PI230004"
      And the user selects the row with text "PI230004" in the "Number" labelled column header of the table field
      Then the value of the "Amount" labelled nested numeric field of the selected row in the table field is "360"

   Scenario: 04-Verify that the values of lines in result grid correspond to values of the journal entry
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
      Then the "Journal entries" titled page is displayed
      #Creating the journal entry inquiry
      And the user selects the "$navigationPanel" bound table field on the main page
      And the user selects the row 1 of the table field
      And the user clicks the "Number" labelled nested field of the selected row in the table field
      And the user searches for "IJ230002" in the navigation panel
      And the user clicks the "first" navigation panel's row
      And the user selects the "lines" bound nested grid field on the main page
      And the user selects row with text "13100 -- Inventory" in column with header "Account" in the nested grid field
      Then the value of the "Transaction debit" labelled nested numeric field of the selected row in the nested grid field is "$ 8.00"
      #Checking that the value correspond with the value on the Journal Entry Inquiry.
      When the user selects the "lines" bound nested grid field on the main page
      And the user selects row with text "13100 -- Inventory" in column with header "Account" in the nested grid field
      And the user expands the selected row of the nested grid field
      And the user selects row with text "100 -- TE Headquarter" in column with header "Financial site" in the nested grid field
      And the value of the "Transaction amount" labelled nested numeric field of the selected row in the nested grid field is "$ 8.00"
      Then the value of the "Item" labelled nested text field of the selected row in the nested grid field is "ID-098 -- Journal entry Item"

###We don't have steps to interact with the multi action button therefore "Export" won't be tested
# Scenario: 05-Verify that the user can Export to excel
#    Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntryInquiry"
#    Then the "Journal entry inquiry" titled page is displayed
#    #Creating the journal entry inquiry
#    And the user selects the "journalEntryLines" bound table field on the main page
#    And the user selects the "Export" labelled business action button on the main page
#    And the user writes "08/28/2023" in the date field
#    And the user presses Enter
#    Then the user clicks in the "searchButton" bound button field on the main page
