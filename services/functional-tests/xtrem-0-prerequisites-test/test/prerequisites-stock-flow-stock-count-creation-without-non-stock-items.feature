# The purpose of this test is to validate that the user is able to create a stock count using the criteria that best meet his needs.
# has to run before stock-flow-stock-count-in-stock-only.feature

@inventory
Feature: prerequisites-stock-flow-stock-count-creation-without-non-stock-items
    Scenario: 01 - Stock count creation without any
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockCountCreation"
        Then the "Stock count creation" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "STO-S01" in the reference field
        And the user selects "Stock count site" in the reference field
        And the user selects the "description" bound text field on the main page
        And the user writes "No filter" in the text field
        Given the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T-2"
        Then the user clicks the "Save" labelled business action button on the main page
        ### line 1
        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - lot & sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user selects row with text "Internal 01" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "24000023"
        And the user selects row with text "Internal 02" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "24000024"
        And the user selects row with text "Internal 03" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "24000025"
        And the user selects row with text "Internal 04" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "24000026"

        # we need this to collapse the row until XT-77207 is done
        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - lot & sequence number 1" in column with header "Item" in the nested grid field
        And the user collapses the selected row of the nested grid field

        ### line 2
        And the user selects row with text "SC Item - lot w/o sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user selects row with text "Dock 01" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "LOT01"

        # we need this to collapse the row until XT-77207 is done
        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - lot w/o sequence number 1" in column with header "Item" in the nested grid field
        And the user collapses the selected row of the nested grid field

        ### line 3
        And the user selects row with text "SC Item - serial number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user selects row with text "Dock 02" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is ""

        # we need this to collapse the row until XT-77207 is done
        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - serial number 1" in column with header "Item" in the nested grid field
        And the user collapses the selected row of the nested grid field
        ### line 4
        And the user selects row with text "SC Item - sub-lot & sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user selects row with text "Dock 01" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "24000027"
        And the user selects row with text "Dock 02" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "24000028"
        And the user selects row with text "Dock 03" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "24000029"

        # we need this to collapse the row until XT-77207 is done
        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - sub-lot & sequence number 1" in column with header "Item" in the nested grid field
        And the user collapses the selected row of the nested grid field
        ### line 5
        And the user selects row with text "SC Item - sub-lot w/o sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field

        And the user selects row with text "Internal 01" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "LOTAA"
        And the user selects row with text "Internal 02" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "LOTBB"
        And the user selects row with text "Internal 03" in column with header "Location" in the nested grid field
        Then the value of the "Lot" labelled nested reference field of the selected row in the nested grid field is "LOTCC"

        # we need this to collapse the row until XT-77207 is done
        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - sub-lot w/o sequence number 1" in column with header "Item" in the nested grid field
        And the user collapses the selected row of the nested grid field
    Scenario: 02 - Update stock count creation page with filter on category
        Given the user selects the "Category" labelled multi reference field on the main page
        And the user writes "Food" in the multi reference field
        And the user selects "Food" in the multi reference field
        And the user selects the "description" bound text field on the main page
        And the user writes "Filter by category" in the text field
        Given the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T-2"
        Then the user clicks the "Save" labelled business action button on the main page

        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - lot & sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user collapses the selected row of the nested grid field
        And the user selects row with text "SC Item - lot w/o sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user collapses the selected row of the nested grid field
    Scenario: 03 - Update stock count creation with filter on zone
        Given the user selects the "Category" labelled multi reference field on the main page
        Then the user clears the multi reference field
        When the user selects the "Zone" labelled multi reference field on the main page
        And the user writes "Dock" in the multi reference field
        And the user selects "Dock" in the multi reference field
        And the user selects the "description" bound text field on the main page
        And the user writes "Filter by zone" in the text field
        Given the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T-2"
        Then the user clicks the "Save" labelled business action button on the main page

        When the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - lot w/o sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user collapses the selected row of the nested grid field
        And the user selects row with text "SC Item - serial number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user collapses the selected row of the nested grid field
        And the user selects row with text "SC Item - sub-lot & sequence number 1" in column with header "Item" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user collapses the selected row of the nested grid field
    Scenario: 04 - Create stock
        Given the user selects the "Select all" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then the user clicks the "Create" labelled business action button on the main page
        Then a toast containing text "The stock count was created successfully." is displayed
        And the user dismisses all the toasts
        # Verify status
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "To be counted"
        And the user selects the "number" bound text field on the main page
        Then the user stores the value of the text field with the key "[ENV_StockCOUNTNUM01]"
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        When the user selects the "stockCounts" labelled table field on the main page
        And the user selects the row with text "[ENV_StockCOUNTNUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Site" labelled nested field of the selected row in the table field
        And the user searches for "Filter by zone" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "number" bound text field on the main page
        And the value of the text field is "[ENV_StockCOUNTNUM01]"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Item ID" labelled nested text field of the selected row in the table field is "SC_LOT_NO_SEQ_NUMBER_1"
        And the value of the "Location" labelled nested text field of the selected row in the table field is "Dock 01"
        And the value of the "Quantity variance" labelled nested numeric field of the selected row in the table field is "0 each"

        And the user selects the row 2 of the table field
        And the value of the "Item ID" labelled nested text field of the selected row in the table field is "SC_SERIAL_1"
        And the value of the "Location" labelled nested text field of the selected row in the table field is "Dock 02"
        And the value of the "Quantity variance" labelled nested numeric field of the selected row in the table field is "0 each"

        And the user selects the row 3 of the table field
        And the value of the "Item ID" labelled nested text field of the selected row in the table field is "SC_SUBLOT_SEQ_NUMBER_1"
        And the value of the "Location" labelled nested text field of the selected row in the table field is "Dock 01"
        And the value of the "Quantity variance" labelled nested numeric field of the selected row in the table field is "0.00 g"

        And the user selects the row 4 of the table field
        And the value of the "Item ID" labelled nested text field of the selected row in the table field is "SC_SUBLOT_SEQ_NUMBER_1"
        And the value of the "Location" labelled nested text field of the selected row in the table field is "Dock 02"
        And the value of the "Quantity variance" labelled nested numeric field of the selected row in the table field is "0.00 g"

        And the user selects the row 5 of the table field
        And the value of the "Item ID" labelled nested text field of the selected row in the table field is "SC_SUBLOT_SEQ_NUMBER_1"
        And the value of the "Location" labelled nested text field of the selected row in the table field is "Dock 03"
        And the value of the "Quantity variance" labelled nested numeric field of the selected row in the table field is "0.00 g"

    Scenario: 05 - Modify stock count created
        And the user selects the "description" bound text field on the main page
        And the user writes "Filter by zone - modified" in the text field
        And the user selects the "counter" bound text field on the main page
        And the user writes "ATP user" in the text field
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 06 - Start stock count
        And the user waits for 5 seconds
        And the user refreshes the screen
        Then the user clicks the "Start" labelled business action button on the main page
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Count in progress"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "20" in the "Counted quantity" labelled nested numeric field of the selected row in the table field
        And the value of the "countStatus" labelled nested label field of the selected row in the table field is "Counted"

        And the user selects the row 2 of the table field
        And the user writes "15" in the "Counted quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Serial numbers" dropdown action of the selected row of the table field
        And the user selects the "serialNumberRanges" bound table field on the sidebar
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "SN005039" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "SN005039" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 2 of the table field
        And the value of the "serialNumber" labelled nested progress field of the selected row in the table field is "66.66666666666667"
        And the value of the "countStatus" labelled nested label field of the selected row in the table field is "Stock count in progress"

        And the user selects the row 3 of the table field
        And the user clicks the "Exclude" dropdown action of the selected row of the table field
        And the value of the "countStatus" labelled nested label field of the selected row in the table field is "Excluded"

        And the user selects the row 4 of the table field
        And the user clicks the "Exclude" dropdown action of the selected row of the table field
        And the value of the "countStatus" labelled nested label field of the selected row in the table field is "Excluded"

        And the user selects the row 5 of the table field
        And the user writes "22.55" in the "Counted quantity" labelled nested numeric field of the selected row in the table field
        And the value of the "countStatus" labelled nested label field of the selected row in the table field is "Counted"

        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts

        And the user selects the row 2 of the table field
        And the user clicks the "Serial numbers" dropdown action of the selected row of the table field
        And the user selects the "serialNumberRanges" bound table field on the sidebar
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "SN005049" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "SN005049" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "5" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 2 of the table field
        And the value of the "countStatus" labelled nested label field of the selected row in the table field is "Counted"

        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user waits 5 seconds
        Then the user refreshes the screen
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Counted"

    Scenario: 07 - Post stock count
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        When the user selects the "stockCounts" labelled table field on the main page
        And the user selects the row with text "[ENV_StockCOUNTNUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the user clicks the "Post stock" labelled business action button on the main page
        Then the user waits for 10 seconds
        Then the user refreshes the screen
        And the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Closed"
        And the user selects the "Stock Status" labelled label field on the main page
        Then the value of the label field is "Completed"
