# The goal of this test is to verify that the RC opens (click the Question mark icon) and each block inside also.

@prerequisites
Feature: prerequisites-flow-resource-center

        Scenario: 01-Verify that the user can click the question mark icon and open the RC.
                Given the user opens the application on a desktop
                When the help center icon is displayed
                And the user opens the help center
                Then the help center "Search for information" resource is displayed

        Scenario: 02-Verify that the user can click on each link of the RC menu
                And the user opens the help center "Search for information" resource
                When the user switches to the last opened tab
                Then the help center page contains "Help center"

                And the user switches to the first opened tab
                And the user opens the help center
                And the user opens the help center "Read the release notes" resource
                When the user switches to the last opened tab
                Then the help center page contains "What's new in Sage Distribution and Manufacturing Operations"

                Then the user switches to the first opened tab
# Then the user opens the help center "See current hotfixes" resource
