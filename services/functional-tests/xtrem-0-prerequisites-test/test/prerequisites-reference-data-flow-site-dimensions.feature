#The goal of this test is to verify the flow of the site dimensions.

@prerequisites
@reference_data
Feature: prerequisites-reference-data-flow-site-dimensions

    Scenario: Scenario: 01 - Setup site dimensions (Site 1)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed
        When the user selects the "Sites" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "ZS01" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Site ZA1"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed


    Scenario: Scenario: 02 - Setup site dimensions (Site 2)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed
        When the user selects the "Sites" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "ZS02" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Site ZA2"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed


    Scenario: Scenario: 03 - Setup site dimensions (Site 3)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed
        When the user selects the "Sites" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "ZS03" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Site ZA3"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
