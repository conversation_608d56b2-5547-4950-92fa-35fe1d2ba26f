# The goal of this test is to test the dashboard flow.

@prerequisites
Feature: prerequisites-flow-dashboard

    # Creation of a dashboard and adding of a widget to the dashboard
    Scenario: As a user I want to create the first dashboard using a blank template dashboard
        Given the user opens the application on a desktop
        Then the dashboard page is displayed
        # This requires that there is not already an existing dashboard on the page
        And the "Create a dashboard to get started." subtitled empty dashboard is displayed
        When the user clicks the create button on the dashboard
        Then the dashboard creation dialog is displayed
        And the "Blank template" template in the dashboard creation dialog is displayed
        # template 1 is the first template and refers to the empty template option
        When the user selects the template with title "Blank template" in the dashboard creation dialog
        And the user clicks the "next" button in the dashboard creation dialog
        Then the "New dashboard" titled dashboard in the dashboard editor is displayed

        # Input a name to the dashboard and save
        When the user clicks the edit dashboard title icon
        And the user writes "First dashboard" in the dashboard title
        And the user presses Enter
        And the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed

        # Test for reading the dashboard exists after above
        Then the "First dashboard" titled dashboard is displayed



    Sc<PERSON>rio: As a user I want to add a widget to the dashboard
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        And the "Widgets" titled header in the dashboard editor navigation panel is displayed
        And the "others" titled category in the dashboard editor navigation panel is displayed
        And the user searches for "System version" in the navigation panel
        When the user clicks the Add button of the "System version" titled widget card in the dashboard editor navigation panel
        Then the "System version" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "First dashboard" titled dashboard is displayed
        And the "System version" titled widget in the dashboard is displayed



    # Updating of a dashboard name and adding a second widget to a dashboard
    Scenario: As a user I want to rename a dashboard
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "First dashboard" titled dashboard in the dashboard editor is displayed
        When the user clicks the edit dashboard title icon
        And the user writes "Updated dashboard" in the dashboard title
        And the user presses Enter
        And the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "Updated dashboard" titled dashboard is displayed


    Scenario: As a user I want to add another widget to a dashboards
        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "Updated dashboard" titled dashboard in the dashboard editor is displayed
        And the user searches for "Sales process" in the navigation panel
        When the user clicks the Add button of the "Sales process" titled widget card in the dashboard editor navigation panel
        Then the "Sales process" titled widget in the dashboard editor is displayed
        When the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        Then the "Updated dashboard" titled dashboard is displayed
        And the "System version" titled widget in the dashboard is displayed
        And the "Sales process" titled widget in the dashboard is displayed


    # Deletion of a dashboard and widget from a dashboard
    Scenario: As as user I want to delete a widget from a dashboard

        When the user clicks the "Edit" labelled CRUD button in the dashboard action menu
        Then the "Updated dashboard" titled dashboard in the dashboard editor is displayed
        And the "System version" titled widget in the dashboard editor is displayed
        When the user selects the "System version" titled tile-indicator widget field in the dashboard editor
        And the user clicks the "Close" more actions button in the header of the tile-indicator widget field
        And the user clicks the "save" button in the dashboard editor footer
        Then a toast containing text "Dashboard saved." is displayed
        And the user dismisses all the toasts
        Then the "Updated dashboard" titled dashboard is displayed

    Scenario: As a user I want to delete a dashboard

        When the user clicks the "delete" labelled CRUD button in the dashboard action menu
        Then a warn dialog appears on the main page
        When the user clicks the "OK" button of the Confirm dialog on the main page
        Then a toast containing text "Dashboard deleted." is displayed
        # Requires that there was only 1 dashboard which got deleted in this scenario resulting in 0 dashboards existing
        And the "Create a dashboard to get started." subtitled empty dashboard is displayed
