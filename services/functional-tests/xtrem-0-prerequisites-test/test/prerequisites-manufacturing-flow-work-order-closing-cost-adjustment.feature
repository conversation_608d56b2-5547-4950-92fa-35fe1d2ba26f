# The goal of this test is to verify wo closing for Finished items with different valuation methods
# Valuation methods: standard | fifo | average
# WO used         : TST_STD | TST_AVG | TST_FIFO
# Variance Expected: AVG (negative actual adjustment) | FIFO (actual adjustment) | STD (negative variance)

@prerequisites
@manufacturing
@finance
Feature: prerequisites-manufacturing-flow-work-order-closing-cost-adjustment
    Scenario: Add lock - LCK_WI
        Given the user opens the application on a HD desktop
        And the user adds the lock entry "LCK_WI"

    Scenario: 01- FIFO | WIP | Work order closing
        #Close Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST004" in the navigation panel
        And the user clicks the record with the text "TST004" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        And the user clicks the "Close order" labelled business action button on the main page
        And the user waits 5 seconds
        And the user clicks the "Continue" button of the Custom dialog
        And the user waits 3 seconds
        And the user clicks the "Generate" button of the Custom dialog

    Scenario: 02- Verify transaction cost Inquiry of WIP | WIP detailed Inquiry | Generating JournalEntries | Verifying JE

        #Verify the WIP transaction Inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipTransactionInquiry"
        Then the "Work in progress transaction inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the user opens the filter of the "Work order" labelled column in the table field
        And the user searches "TST004" in the filter of the table field
        And the user ticks the item with text "TST004" in the filter of the table field
        And the user closes the filter of the "Work order" labelled column in the table field
        And the user selects the row with text "Work order negative actual cost adjustment" in the "Type" labelled column header of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested numeric field of the selected row in the table field is "TST004"

        #Verify the WIP Inquiry
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/WipInquiry"
        Then the "Work in progress inquiry" titled page is displayed
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field

        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "TST004" in the reference field
        And the user selects "TST004" in the reference field

        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "TST004" in the reference field
        And the user selects "TST004" in the reference field

        And the user clicks the "Run" labelled business action button on the main page
        Then a toast containing text "Work in progress inquiry request sent." is displayed
        Then a toast containing text "Work in progress inquiry finished." is displayed

        When the user selects the "Results" labelled table field on the main page

        And the user selects the row with text "FIFO_ITEM" in the "Item" labelled column header of the table field
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "$ 0.0000"

        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog

        #generate JE for WIP
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        Then the user selects "TE Headquarter" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Work in progress" in the multi dropdown field
        And the value of the multi dropdown field is "Work in progress"
        # Click Generate
        And the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

        #Verify generated JE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "WIP24000004" in the navigation panel
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"

    Scenario: 03- AVG | WIP | Work order closing
        #Close Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST002" in the navigation panel
        And the user clicks the record with the text "TST002" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "In progress"
        And the user clicks the "Close order" labelled business action button on the main page
        And the user waits 3 seconds
        And the user clicks the "Continue" button of the Custom dialog
        And the user waits 3 seconds
        And the user clicks the "Generate" button of the Custom dialog

    Scenario: 04- Verify transaction cost Inquiry of WIP | WIP detailed Inquiry | Generating JournalEntries | Verifying JE

        #Verify the WIP transaction Inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipTransactionInquiry"
        Then the "Work in progress transaction inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the user opens the filter of the "Work order" labelled column in the table field
        And the user searches "TST002" in the filter of the table field
        And the user ticks the item with text "TST002" in the filter of the table field
        And the user closes the filter of the "Work order" labelled column in the table field
        And the user selects the row with text "Work order negative actual cost adjustment" in the "Type" labelled column header of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested numeric field of the selected row in the table field is "TST002"

        #Verify the WIP Inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipInquiry"
        Then the "Work in progress inquiry" titled page is displayed
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field

        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "TST002" in the reference field
        And the user selects "TST002" in the reference field

        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "TST002" in the reference field
        And the user selects "TST002" in the reference field

        And the user clicks the "Run" labelled business action button on the main page
        And the user waits 10 seconds
        When the user selects the "Results" labelled table field on the main page

        And the user selects the row with text "AVG_ITEM" in the "Item" labelled column header of the table field
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 0.0000"

        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog

        #generate JE for WIP
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        Then the user selects "Sandfeld" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Work in progress" in the multi dropdown field
        And the value of the multi dropdown field is "Work in progress"
        # Click Generate
        And the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

        #Verify generated JE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "WIP24000005" in the navigation panel
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"

    Scenario: 05- FIFO | WIP | Work order closing

        #Close Work order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TST003" in the navigation panel
        And the user clicks the record with the text "TST003" in the navigation panel
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user clicks the "Close order" labelled business action button on the main page
        And the user waits 3 seconds
        And the user clicks the "Generate" button of the Custom dialog

    Scenario: 06- Verify transaction cost Inquiry of WIP | WIP detailed Inquiry | Generating JournalEntries | Verifying JE

        #Verify the WIP transaction Inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipTransactionInquiry"
        Then the "Work in progress transaction inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        Then the user opens the filter of the "Work order" labelled column in the table field
        And the user searches "TST003" in the filter of the table field
        And the user ticks the item with text "TST003" in the filter of the table field
        And the user closes the filter of the "Work order" labelled column in the table field
        And the user selects the row with text "Work order actual cost adjustment non absorbed" in the "Type" labelled column header of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested numeric field of the selected row in the table field is "TST003"

        #Verify the WIP Inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipInquiry"
        Then the "Work in progress inquiry" titled page is displayed
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field

        And the user selects the "From work order" labelled reference field on the main page
        And the user writes "TST003" in the reference field
        And the user selects "TST003" in the reference field

        And the user selects the "To work order" labelled reference field on the main page
        And the user writes "TST003" in the reference field
        And the user selects "TST003" in the reference field

        And the user clicks the "Run" labelled business action button on the main page
        When the user selects the "Results" labelled table field on the main page

        And the user selects the row with text "TST003" in the "Work order" labelled column header of the table field
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "$ 0.0000"

        #generate JE for WIP
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        Then the user selects "TE Headquarter" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Work in progress" in the multi dropdown field
        And the value of the multi dropdown field is "Work in progress"
        # Click Generate
        And the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

        #Verify generated JE
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "WIP24000004" in the navigation panel
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"

    Scenario: Remove lock - LCK_WI
        And the user removes the lock entry "LCK_WI"
