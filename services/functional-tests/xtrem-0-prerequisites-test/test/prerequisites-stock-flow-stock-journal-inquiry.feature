#The goal of this test is to verify the flow of the stock journal inquiry.
#There is currently a sorting enhancement XT-28347, that is in development.
#<PERSON>ript may be updated following the enhancement, if need be.

@prerequisites
@inventory
Feature: prerequisites-stock-flow-stock-journal-inquiry
    Scenario: 01 - Store value of document number
        #Stock receipt number
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "Stock receipts" labelled table field on the main page
        And the user filters the "number" bound column in the table field with value "SJI-SR-52262"
        And the user selects the row with text "SJI-SR-52262" in the "Number" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested text field of the selected row in the table field with the key "[ENV_SJISR01]"
        #Stock issue number
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "Stock issues" labelled table field on the main page
        And the user selects the row with text "SJI-SS-52262" in the "Number" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested text field of the selected row in the table field with the key "[ENV_SJISS02]"
        #Stock adjustment number
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "Stock adjustments" labelled table field on the main page
        And the user selects the row with text "SJI-SA-52262" in the "Number" labelled column header of the table field
        And the user stores the value of the "Number" labelled nested text field of the selected row in the table field with the key "[ENV_SJISA03]"

    Scenario: 02 - Verify stock journal inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed
        #Verify empty table field

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item for stock journal inquiry 1" in the filter of the table field
        And the user ticks the item with text "Item for stock journal inquiry 1" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        When the user selects the row with text "[ENV_SJISR01]" in the "Document" labelled column header of the table field
        Then the value of the "Quantity" labelled nested text field of the selected row in the table field is "200 each"
        And the value of the "Quality control" labelled nested numeric field of the selected row in the table field is "Accepted"
        And the value of the "Lot" labelled nested numeric field of the selected row in the table field is "LOT5226201"
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "£ 15.0000"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "£ 3,000.00"

        When the user selects the row with text "[ENV_SJISS02]" in the "Document" labelled column header of the table field
        And the value of the "Item" labelled nested reference field of the selected row in the table field is "Item for stock journal inquiry 1"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-65 each"
        And the value of the "Quality control" labelled nested numeric field of the selected row in the table field is "Accepted"
        And the value of the "Lot" labelled nested numeric field of the selected row in the table field is "LOT5226201"
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "£ 0.0000"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "£ 0.00"
        #Search with multiple items
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item for stock journal inquiry 2" in the filter of the table field
        And the user ticks the item with text "Item for stock journal inquiry 2" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        When the user selects the row with text "95 each" in the "Quantity" labelled column header of the table field
        Then the value of the "Document" labelled nested numeric field of the selected row in the table field is "[ENV_SJISR01]"
        And the value of the "Quality control" labelled nested numeric field of the selected row in the table field is "Accepted"
        And the value of the "Lot" labelled nested numeric field of the selected row in the table field is "LOT5226202"
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "£ 8.9500"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "£ 850.25"

        When the user selects the row with text "[ENV_SJISA03]" in the "Document" labelled column header of the table field
        Then the value of the "Item" labelled nested reference field of the selected row in the table field is "Item for stock journal inquiry 2"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-45 each"
        And the value of the "Quality control" labelled nested numeric field of the selected row in the table field is "Accepted"
        And the value of the "Lot" labelled nested numeric field of the selected row in the table field is "LOT5226202"
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "£ 0.0000"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "£ 0.00"
        And the value of the "Reason code" labelled nested reference field of the selected row in the table field is "Decrease quantity"
        #Search with site ONLY

        When the user opens the filter of the "Item" labelled column in the table field
        Then the user clicks the clear selected items link in the filter menu of the "Item" labelled column of the table field

        When the user filters the "Quantity" labelled column in the table field with value "-65"
        And the user selects the row with text "[ENV_SJISS02]" in the "Document" labelled column header of the table field
        Then the value of the "Item" labelled nested reference field of the selected row in the table field is "Item for stock journal inquiry 1"

        When the user filters the "Quantity" labelled column in the table field with value "-45"
        And the user selects the row with text "[ENV_SJISA03]" in the "Document" labelled column header of the table field
        Then the value of the "Item" labelled nested reference field of the selected row in the table field is "Item for stock journal inquiry 2"

        #Search without value still returns results


        When the user opens the filter of the "Site" labelled column in the table field
        Then the user clicks the clear selected items link in the filter menu of the "Site" labelled column of the table field

        And the table field is not empty
