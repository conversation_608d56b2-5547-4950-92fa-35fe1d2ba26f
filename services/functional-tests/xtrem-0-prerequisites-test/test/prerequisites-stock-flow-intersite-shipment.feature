# This test aims to validate the creation of a stock transfer shipment for supply chain. This will confirm stability on the new feature
# The focus will be on verifying field validations and creation of the Stock transfer receipt without issue

@prerequisites
@inventory
Feature: prerequisites-stock-flow-intersite-shipment

    Scenario: 01 - Verify the user is able to create a stock transfer shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        Then the "Stock transfer orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TO250001" in the "Number" labelled column header of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "TO250001"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Create shipment" labelled business action button on the main page
        And the user waits for 2 seconds
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Transfer shipment created" is displayed
        And the user waits for 2 seconds
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_STS01]"

    Scenario: 02 - Allocate stock on the stock transfer shipment
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"

    Scenario: 03 - Verify the dimensions on the shipment
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Task" labelled reference field on a modal
        And the value of the reference field is "Indirect"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"

    Scenario: 04 - Verify the shipment can be updated
        And the user clears the reference field
        Then the user clicks the "Ok" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is ""
        Then the user clicks the "Cancel" labelled business action button on a modal

    Scenario: 05 - Verify notes can be added to the stock transfer shipment
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the user writes "this is an internal note @123 !" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_NOTETS]"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTETS]"

    Scenario: 06 - Verify the user can not delete the shipment
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a validation error message is displayed containing text
            """"
            Validation errors
            Deletion impossible
            """
        And the user dismisses all the toasts
        And the user waits 5 seconds

    Scenario: 07 - Verify the user can confirm the shipment
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The stock transfer shipment is confirmed and set to 'Ready to ship'" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to ship"
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Ready to ship"
        And the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is current

    Scenario: 08 - Verify the user can print the shipment
        #remove step definition to refresh the screen when bug XT-88941 is fixed
        And the user refreshes the screen
        And the user clicks the "Print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast containing text "The stock transfer shipment was printed." is displayed
        And the user dismisses all the toasts
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Printed" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"

    Scenario: 09 - Verify the user can post the stock on the shipment
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user waits 5 seconds

        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The stock transfer shipment was posted." is displayed
        # need to refresh because it remains stuck on the "Posting in progress" status
        And the user refreshes the screen
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Shipped"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Shipped"
        And the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is complete

    Scenario: 10 - Verify update on Stock detail inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        When the user selects the "Including in-transit" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Site" labelled column in the table field with value "Stock Tranfer Uk Site 2"
        And the user filters the "Lot" labelled column in the table field with value "24000032"
        And the user selects the row with text "30 each" in the "In-transit quantity" labelled column header of the table field
        Then the value of the "On-hand quantity" labelled nested text field of the selected row in the table field is "0 each"
        Then the value of the "Available quantity" labelled nested text field of the selected row in the table field is "0 each"
        Then the value of the "Allocated quantity" labelled nested text field of the selected row in the table field is "0 each"

    Scenario: 11 - Verify update on Stock inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockInquiryBound"
        Then the "Stock inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Item" labelled column in the table field with value "Pressure transducer"
        And the user filters the "Site" labelled column in the table field with value "Stock Tranfer Uk Site 1"
        And the user selects the row 1 of the table field
        Then the value of the "Quantity on hand" labelled nested text field of the selected row in the table field is "1,923 each"
        Then the value of the "Accepted quantity" labelled nested text field of the selected row in the table field is "1,923 each"
        Then the value of the "Stock value" labelled nested text field of the selected row in the table field is "£ 194,007.05"

    Scenario: 12 - Verify the stock journal inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Date" labelled column in the table field with filter type "Current day"
        And the user filters the "Site" labelled column in the table field with value "Stock Tranfer Uk Site 1"
        And the user selects the row with text "[ENV_STS01]" in the "Document" labelled column header of the table field
        Then the value of the "Quantity" labelled nested text field of the selected row in the table field is "-30 each"
        Then the value of the "Order cost" labelled nested text field of the selected row in the table field is "£ 100.8877"
        Then the value of the "Movement amount" labelled nested text field of the selected row in the table field is "£ -3,026.63"
