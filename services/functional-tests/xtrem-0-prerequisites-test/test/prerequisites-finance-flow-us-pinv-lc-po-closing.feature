# The purpose of this test is to cover the Landed cost flow in case of PO closing
# we create a PO, create an invoice for landed costs allocated to the PO, we verify journals
# we partially receive the PO and generate JE, verify landed cost and JE generated for receipt
# we close the PO, verify landed cost and JE generated for receipt

@prerequisites
@finance
Feature: prerequisites-finance-flow-us-pinv-lc-po-closing

    Scenario: 01 Create Purchase Purchase Order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Fill in site reference field
        And the user selects the "purchasingSite" labelled reference field on the main page
        And the user writes "T<PERSON> Hampton" in the reference field
        And the user selects "T<PERSON> Hampton" in the reference field
        # Fill in Supplier reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "Lenovo" in the reference field
        And the user selects "Lenovo" in the reference field
        # Add Number for PO
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PO-US-LC002" in the text field
        # Add a line to the PO
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        # Fill in Qty on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        # Fill in Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast with text "Record created" is displayed
        # Approve Po
        And the user clicks the "submitForApproval" labelled business action button on the main page
        And the user selects the "to" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And a toast containing text "Email sent" is displayed
        And the user clicks the "approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        Then a toast containing text "Approval status updated" is displayed

    Scenario: 02 Create Purchase invoice for landed costs
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in HEADER on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        And the user selects "TE Headquarter" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "Covalent chemical" in the reference field
        And the user selects "Covalent chemical" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI-US-LC002" in the text field
        And the user selects the "Supplier invoice reference" labelled text field on the main page
        And the user writes "test LC PO Closing" in the text field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "120" in the numeric field
        And the user presses Enter
        #Adding Pinv Line
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "LC002" in the reference field
        And the user selects "LC002" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "120" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        #Allocation of Landed costs to PO
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC002" in the "Item" labelled column header of the table field
        And the user clicks the "Landed cost" dropdown action of the selected row of the table field
        When the user selects the "Allocated documents" labelled table field on a modal
        And the user clicks the "Add lines from orders" labelled multi action of the table field
        And the user selects the "Supplier" labelled reference field on a modal
        And the user clicks the "Search" button of the Custom dialog
        And the user selects the "documents" bound nested grid field on a modal
        And the user selects row with text "PO-US-LC002" in column with header "Order number" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user presses Enter
        And the user selects row with text "PO-US-LC002" in column with header "Order number" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field
        And the user clicks the "Add" labelled business action button on a modal
        #Save Allocation
        And the user clicks the "Save" labelled business action button on the main page
        #Accept Variance
        And the user clicks the "acceptAllVariances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog

    Scenario: 03 Verify JE generated for landed costs of PO
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel

        And the user selects the row with text "PI-US-LC002" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Post
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        #Open Posting tab
        And the user refreshes the screen
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Posted"
        And the user stores the value of the "Document number" labelled nested link field of the selected row in the table field with the key "[ENV_PI_LC_JE02]"

        # open JE
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        #Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PI_LC_JE02]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PI_LC_JE02]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        #Verify Journal Entry automatically generated
        And the user selects the "Number" labelled text field on the main page
        And the value of the text field is "[ENV_PI_LC_JE02]"
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Journal" labelled reference field on the main page
        And the value of the reference field is "Unbilled Journal"
        And the user selects the "Reference" labelled text field on the main page
        And the value of the text field is "Purchase invoice stock variance"

        #Line 1 Verification
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "$ 120.00" in column with header "Transaction debit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "13200 -- Goods in transit"
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Goods in transit"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PO-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 120.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

        # workaround due to a bug in ATP
        And the user refreshes the screen

        # #Line 2 Verification
        And the user selects row with text "$ 120.00" in column with header "Transaction credit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "22700 -- Landed cost accrual"
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Landed cost accrual"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PO-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 120.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"


    Scenario: 04 Purchase Receipt Creation (partial)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        #Create Receipt
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Receiving site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "Lenovo" in the reference field
        And the user selects "Lenovo" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PR-US-LC002" in the text field
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from orders" labelled add action of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "PO-US-LC002" in the "Document" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user writes "5" in the "Quantity to receive" labelled nested numeric field of the selected row in the table field
        And the user presses Enter
        And the user clicks the "OK" button of the Confirm dialog
        #Add stock
        When the user selects the "lines" labelled table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "item" labelled column header of the table field
        #Stock allocation
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row with text "5 each" in the "quantityInStockUnit" bound column header of the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # post stock
        And the user clicks the "Post Stock" labelled business action button on the main page
        # Posting Tab Verification
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "To be generated"

    Scenario: 05 Generate JE for partially received PO
        And the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        And the user selects "TE Headquarter" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Purchase receipt" in the multi dropdown field
        And the value of the multi dropdown field is "Purchase receipt"
        # Click Generate
        And the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: " is displayed

    Scenario: 06 Purchase Receipt Verification after JE Generation
        # Open Purchase Receipt record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR-US-LC002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # Posting Tab Verification on PR
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Posted"
        And the user stores the value of the "Document number" labelled nested link field of the selected row in the table field with the key "[ENV_PR_LC_POC01]"
        # Landed cost Tab verification on PR
        And selects the "Landed costs" labelled navigation anchor on the main page
        And the user selects the "Actual landed costs" labelled numeric field on the main page
        And the value of the numeric field is "60.00"
        And the user selects the "Summary by landed cost type" labelled table field on the main page
        And the user selects the row with text "Tariffs" in the "Type" labelled column header of the table field
        And the value of the "Actual cost amount in company currency" labelled nested text field of the selected row in the table field is "$ 60.00"

    Scenario: 07 Verify JE generated for partially received PO
        # JE Verification
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        #Creating the journal entry inquiry
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PR_LC_POC01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PR_LC_POC01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify Journal Entry
        And the user selects the "Number" labelled text field on the main page
        And the value of the text field is "[ENV_PR_LC_POC01]"
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Journal" labelled reference field on the main page
        And the value of the reference field is "Unbilled Journal"
        And the user selects the "Reference" labelled text field on the main page
        And the value of the text field is "Purchase receipt"

        #Line 1 Verification
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "$ 60.00" in column with header "Transaction credit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "13200 -- Goods in transit"
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Goods in transit"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PO-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 60.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

        # workaround due to a bug in ATP
        And the user refreshes the screen

        #Line 2 Verification
        And the user selects row with text "$ 60.00" in column with header "Transaction debit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "53100 -- Purchase variance"
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Purchase variance"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 60.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

        # workaround due to a bug in ATP
        And the user refreshes the screen

        #Line 3 Verification
        And the user selects row with text "$ 10.00" in column with header "Transaction credit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "20680 -- Inventory - GRNI"
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Goods received not invoiced"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 10.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

        # workaround due to a bug in ATP
        And the user refreshes the screen

        #Line 4 Verification
        And the user selects row with text "$ 10.00" in column with header "Transaction debit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "53100 -- Purchase variance"
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Purchase variance"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 10.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

        # workaround due to a bug in ATP
        And the user refreshes the screen

        #Line 5 Verification
        And the user selects row with text "$ 40.00" in column with header "Transaction credit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "20680 -- Inventory - GRNI"
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Goods received not invoiced"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 40.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

        # workaround due to a bug in ATP
        And the user refreshes the screen

        #Line 6 Verification
        And the user selects row with text "$ 40.00" in column with header "Transaction debit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "13100 -- Inventory"
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Stock"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 40.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

    Scenario: 08 PO Closing (verify LC tab)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PO-US-LC002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # Landed cost Tab verification on PO
        And selects the "Landed costs" labelled navigation anchor on the main page
        And the user selects the "Actual landed costs" labelled numeric field on the main page
        And the value of the numeric field is "120.00"
        And the user selects the "Summary by landed cost type" labelled table field on the main page
        And the user selects the row with text "Tariffs" in the "Type" labelled column header of the table field
        And the value of the "Actual cost amount in company currency" labelled nested text field of the selected row in the table field is "$ 120.00"
        And the value of the "Actual allocated cost amount in company currency" labelled nested text field of the selected row in the table field is "$ 60.00"
        # PO Closing
        And the user clicks the "Close order" labelled business action button on the main page
        Then the user clicks the "Close order" button of the Confirm dialog
        Then a toast containing text "The purchase order is closed." is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Closed"
        # Reverify Landed cost Tab
        And selects the "Landed costs" labelled navigation anchor on the main page
        And the user selects the "Actual landed costs" labelled numeric field on the main page
        And the value of the numeric field is "120.00"
        And the user selects the "Summary by landed cost type" labelled table field on the main page
        And the user selects the row with text "Tariffs" in the "Type" labelled column header of the table field
        And the value of the "Actual cost amount in company currency" labelled nested text field of the selected row in the table field is "$ 120.00"
        And the value of the "Actual allocated cost amount in company currency" labelled nested text field of the selected row in the table field is "$ 120.00"

    Scenario: 09 Verify Purchase receipt
        # Open Purchase Receipt record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR-US-LC002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # Verify the Landed Cost Tab on PR
        And selects the "Landed costs" labelled navigation anchor on the main page
        And the user selects the "Actual landed costs" labelled numeric field on the main page
        And the value of the numeric field is "120.00"
        And the user selects the "Summary by landed cost type" labelled table field on the main page
        And the user selects the row with text "Tariffs" in the "Type" labelled column header of the table field
        And the value of the "Actual cost amount in company currency" labelled nested text field of the selected row in the table field is "$ 120.00"
        # Verify the Posting Tab on PR
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "" in the "Document number" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "To be generated"

    Scenario: 10 Generate Journal entry for Purchase Receipt
        # Generate JE
        And the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        And the user selects "TE Headquarter" in the reference field
        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Purchase receipt" in the multi dropdown field
        And the value of the multi dropdown field is "Purchase receipt"
        # Click Generate
        And the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: " is displayed

    Scenario: 11 Verification of JE generated
        # Open Purchase Receipt record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR-US-LC002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # Verify the Posting Tab on PR
        And selects the "Posting" labelled navigation anchor on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Document number" labelled nested field of the selected row in the table field
        And the user stores the value of the "Document number" labelled nested link field of the selected row in the table field with the key "[ENV_PR_LC_POC02]"

        # Landed cost Tab verification on PR
        And selects the "Landed costs" labelled navigation anchor on the main page
        And the user selects the "Actual landed costs" labelled numeric field on the main page
        And the value of the numeric field is "120.00"
        And the user selects the "Summary by landed cost type" labelled table field on the main page
        And the user selects the row with text "Tariffs" in the "Type" labelled column header of the table field
        And the value of the "Actual cost amount in company currency" labelled nested text field of the selected row in the table field is "$ 120.00"

        # Open JE
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PR_LC_POC02]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PR_LC_POC02]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify Journal Entry
        And the user selects the "Number" labelled text field on the main page
        And the value of the text field is "[ENV_PR_LC_POC02]"
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"
        And the user selects the "Journal" labelled reference field on the main page
        And the value of the reference field is "Unbilled Journal"
        And the user selects the "Reference" labelled text field on the main page
        And the value of the text field is "Purchase receipt"

        #Line 1 Verification
        And the user selects the "lines" bound nested grid field on the main page
        And the user selects row with text "$ 60.00" in column with header "Transaction credit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "13200 -- Goods in transit"
        And the value of the "Transaction debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Goods in transit"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PO-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 60.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"

        # workaround due to a bug in ATP
        And the user refreshes the screen

        #Line 2 Verification
        And the user selects row with text "$ 60.00" in column with header "Transaction debit" in the nested grid field
        Then the value of the "Account" labelled nested text field of the selected row in the nested grid field is "53100 -- Purchase variance"
        And the value of the "Transaction credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Description" labelled nested text field of the selected row in the nested grid field is "Purchase variance"
        And the value of the "Common reference" labelled nested text field of the selected row in the nested grid field is "PR-US-LC002"
        And the value of the "Company debit" labelled nested text field of the selected row in the nested grid field is "$ 60.00"
        And the value of the "Company credit" labelled nested text field of the selected row in the nested grid field is "$ 0.00"
        And the value of the "Exchange rate" labelled nested text field of the selected row in the nested grid field is "1 USD = 1 USD"
