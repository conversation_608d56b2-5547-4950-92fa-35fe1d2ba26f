# The purpose of this test is to verify the supply purchase order planning page functionality
# Works as expected

@prerequisites
@distribution
@inventory
@manufacturing
Feature: prerequisites-distribution-flow-supply-planning-purchase-order
    Scenario: Run the MRP calculation request 01 - Manufactured item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed

        # Create
        When the user clicks the "Create" labelled business action button on the main page
        Then the dialog title is "MRP calculation request" on the main page

        # Enter criteria details
        When the user selects the "Description" labelled text field on a modal
        And the user writes "Supply_Planning_PO_MRP_Manufactured_Item_01" in the text field
        And the user stores the value of the text field with the key "[ENV_MRP_Description_MF_01]"
        And the user selects the "Site" labelled multi reference field on a modal

        # South Africa ZA
        And the user writes "South Africa Headquarter & Warehouse" in the multi reference field
        And the user selects "South Africa Headquarter & Warehouse" in the multi reference field

        # Sandfeld DE
        And the user writes "Sandfeld" in the multi reference field
        And the user selects "Sandfeld" in the multi reference field

        # Select same item for both from and to so that only 1 specific items MRP is generated
        And the user selects the "From item" labelled reference field on a modal
        And the user writes "Supply order planning Manufactured Item 001" in the reference field
        And the user selects "Supply order planning Manufactured Item 001" in the reference field
        And the user stores the value of the reference field with the key "[ENV_MRP_From_item_MF_01]"
        And the user selects the "To item" labelled reference field on a modal
        And the user writes "Supply order planning Manufactured Item 001" in the reference field
        And the user selects "Supply order planning Manufactured Item 001" in the reference field

        # Ensure the checkbox "Explode bill of material" is ticked
        When the user selects the "Explode bill of material" labelled checkbox field on a modal
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # Ensure the checkbox "Include sales quote" is ticked
        When the user selects the "Include sales quotes" labelled checkbox field on a modal
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # Submit and calculate the result
        When the user clicks the "Calculate" labelled business action button on a modal
        Then a toast containing text "MRP calculation request sent" is displayed

    Scenario: Run the MRP calculation request 02 - Purchased item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed

        # Create
        When the user clicks the "Create" labelled business action button on the main page
        Then the dialog title is "MRP calculation request" on the main page

        # Enter criteria details
        When the user selects the "Description" labelled text field on a modal
        And the user writes "Supply_Planning_PO_MRP_Purchased_Item_01" in the text field
        And the user stores the value of the text field with the key "[ENV_MRP_Description_Purch_01]"
        And the user selects the "Site" labelled multi reference field on a modal

        # South Africa ZA
        And the user writes "South Africa Headquarter & Warehouse" in the multi reference field
        And the user selects "South Africa Headquarter & Warehouse" in the multi reference field

        # Sandfeld DE
        And the user writes "Sandfeld" in the multi reference field
        And the user selects "Sandfeld" in the multi reference field

        # Select same item for both from and to so that only 1 specific items MRP is generated
        And the user selects the "From item" labelled reference field on a modal
        And the user writes "Supply order planning Purchased Item 001" in the reference field
        And the user selects "Supply order planning Purchased Item 001" in the reference field
        And the user stores the value of the reference field with the key "[ENV_MRP_From_item_Purch_01]"
        And the user selects the "To item" labelled reference field on a modal
        And the user writes "Supply order planning Purchased Item 001" in the reference field
        And the user selects "Supply order planning Purchased Item 001" in the reference field

        # Ensure the checkbox "Explode bill of material" is ticked
        When the user selects the "Explode bill of material" labelled checkbox field on a modal
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # Ensure the checkbox "Include sales quote" is ticked
        When the user selects the "Include sales quotes" labelled checkbox field on a modal
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # Submit and calculate the result
        When the user clicks the "Calculate" labelled business action button on a modal
        Then a toast containing text "MRP calculation request sent" is displayed

    Scenario: Run the MRP calculation request 03 - Purchased and Manufactured item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed

        # Create
        When the user clicks the "Create" labelled business action button on the main page
        Then the dialog title is "MRP calculation request" on the main page

        # Enter criteria details
        When the user selects the "Description" labelled text field on a modal
        And the user writes "Supply_Planning_PO_MRP_PurchManuf_Item_01" in the text field
        And the user stores the value of the text field with the key "[ENV_MRP_Description_PurchManuf_01]"
        And the user selects the "Site" labelled multi reference field on a modal

        # South Africa ZA
        And the user writes "South Africa Headquarter & Warehouse" in the multi reference field
        And the user selects "South Africa Headquarter & Warehouse" in the multi reference field

        # Sandfeld DE
        And the user writes "Sandfeld" in the multi reference field
        And the user selects "Sandfeld" in the multi reference field

        # Select same item for both from and to so that only 1 specific items MRP is generated
        And the user selects the "From item" labelled reference field on a modal
        And the user writes "Supply order planning PurchManuf Item 001" in the reference field
        And the user selects "Supply order planning PurchManuf Item 001" in the reference field
        And the user stores the value of the reference field with the key "[ENV_MRP_From_item_PurchManuf_01]"
        And the user selects the "To item" labelled reference field on a modal
        And the user writes "Supply order planning PurchManuf Item 001" in the reference field
        And the user selects "Supply order planning PurchManuf Item 001" in the reference field

        # Ensure the checkbox "Explode bill of material" is ticked
        When the user selects the "Explode bill of material" labelled checkbox field on a modal
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # Ensure the checkbox "Include sales quote" is ticked
        When the user selects the "Include sales quotes" labelled checkbox field on a modal
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # Submit and calculate the result
        When the user clicks the "Calculate" labelled business action button on a modal
        Then a toast containing text "MRP calculation request sent" is displayed

    Scenario: View and verify the Purchase order planning Page
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-supply-chain/PurchaseOrderPlanning"
        Then the "Purchase order planning" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        # TODO: TEMP work around : TO REMOVE AFTER wdio migration https://jira.sage.com/browse/XT-45515
        And the user clicks the "Hide table filters" labelled button of the table field

        # View - BOM component of Manufactured item 1 -> Supply order planning BOM Item 001
        # Filter by Company name
        When the user opens the filter of the "Company name" labelled column in the table field
        And the user searches "South Africa" in the filter of the table field
        And the user ticks the item with text "South Africa" in the filter of the table field
        Then the user closes the filter of the "Company name" labelled column in the table field

        # Filter by Purchase site name
        When the user opens the filter of the "Site name" labelled column in the table field
        And the user searches "South Africa Headquarter & Warehouse" in the filter of the table field
        And the user ticks the item with text "South Africa Headquarter & Warehouse" in the filter of the table field
        Then the user closes the filter of the "Site name" labelled column in the table field

        # Filter by Item name
        When the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Supply order planning BOM Item 001" in the filter of the table field
        And the user ticks the item with text "Supply order planning BOM Item 001" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # # Filter by Supplier - TODO: Item not being able to have set supplier issue, need to investigate
        # When the user opens the filter of the "Supplier" labelled column in the table field
        # And the user searches "Micron" in the filter of the table field
        # And the user ticks the item with text "Micron" in the filter of the table field
        # Then the user closes the filter of the "Supplier" labelled column in the table field

        # Select and click Item
        And the user selects the row with text "Supply order planning BOM Item 001" in the "Item" labelled column header of the table field
        And the user clicks the "Company name" labelled nested field of the selected row in the table field

        # Verify the details of the record
        # Default Supplier
        When the user selects the "Supplier" labelled reference field on the main page

        # Empty Supplier
        Then the value of the reference field is ""

        # Stock quantity
        When the user selects the "Stock quantity" labelled numeric field on the main page
        Then the value of the numeric field is "627"

        # Purchase quantity
        When the user selects the "Purchase quantity" labelled numeric field on the main page
        Then the value of the numeric field is "627"

        # Default Purchase unit
        When the user selects the "Purchase unit" labelled reference field on the main page
        Then the value of the reference field is "Each"

        # Order date - Current date
        When the user selects the "Order date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"

        # Default Gross price
        When the user selects the "Gross price" labelled numeric field on the main page

        # Default is 0 because unable to set supplier for this item site supplier pair
        Then the value of the numeric field is "0.00000"

        # Create order status
        When the user selects the "Create order status" labelled label field on the main page
        Then the value of the label field is "Pending"

    Scenario: Edit a record on the purchase order planning page
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-supply-chain/PurchaseOrderPlanning"
        Then the "Purchase order planning" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        # TODO: TEMP work around : TO REMOVE AFTER wdio migration https://jira.sage.com/browse/XT-45515
        And the user clicks the "Hide table filters" labelled button of the table field

        # View - Purchased Item Sandfeld -> Supply order planning Purchased Item 001
        # Filter by Company name
        When the user opens the filter of the "Company name" labelled column in the table field
        And the user searches "DE Automotive GmbH" in the filter of the table field
        And the user ticks the item with text "DE Automotive GmbH" in the filter of the table field
        Then the user closes the filter of the "Company name" labelled column in the table field

        # Filter by Purchase site name
        When the user opens the filter of the "Site name" labelled column in the table field
        And the user searches "Sandfeld" in the filter of the table field
        And the user ticks the item with text "Sandfeld" in the filter of the table field
        Then the user closes the filter of the "Site name" labelled column in the table field

        # Filter by Item name
        When the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Supply order planning Purchased Item 001" in the filter of the table field
        And the user ticks the item with text "Supply order planning Purchased Item 001" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # Filter by Supplier
        When the user opens the filter of the "Supplier" labelled column in the table field
        And the user searches "MUHOMA Technology GmbH" in the filter of the table field
        And the user ticks the item with text "MUHOMA Technology GmbH" in the filter of the table field
        Then the user closes the filter of the "Supplier" labelled column in the table field

        # Select and click Item
        And the user selects the row with text "Supply order planning Purchased Item 001" in the "Item" labelled column header of the table field
        And the user clicks the "Company name" labelled nested field of the selected row in the table field

        # Edit Supplier and save
        When the user selects the "Supplier" labelled reference field on the main page
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field

        # Edit Purchase quantity
        When the user selects the "Purchase quantity" labelled numeric field on the main page
        And the user writes "500" in the numeric field

        # Edit Order date T - 2
        When the user selects the "Order date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T-2"

        # Edit expected receipt date T + 2
        When the user selects the "Expected receipt date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+2"

        # Edit Gross price
        When the user selects the "Gross price" labelled numeric field on the main page
        And the user writes "35" in the numeric field

        # Save
        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: Create orders for multiple PO Suggestions
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-supply-chain/PurchaseOrderPlanning"
        Then the "Purchase order planning" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        # TODO: TEMP work around : TO REMOVE AFTER wdio migration https://jira.sage.com/browse/XT-45515
        And the user clicks the "Hide table filters" labelled button of the table field

        # Select all records with item filter
        # Filter by Item name
        When the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Supply order planning BOM Item 001" in the filter of the table field
        And the user ticks the item with text "Supply order planning BOM Item 001" in the filter of the table field
        When the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Supply order planning Purchased Item 001" in the filter of the table field
        And the user ticks the item with text "Supply order planning Purchased Item 001" in the filter of the table field
        When the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Supply order planning PurchManuf Item 001" in the filter of the table field
        And the user ticks the item with text "Supply order planning PurchManuf Item 001" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # Select bulk selection tick box
        When the user selects all rows of the table field
        Then the bulk action bar of the table field has 5 items
        Then the bulk action bar of the table field is displayed
        When the "Create order" labelled bulk action button of the table field is displayed
        And the user clicks the "Create order" labelled bulk action button of the table field
        Then the dialog title is "Create order" on the main page
        And the text in the body of the dialog is "Perform this action on the selected items: 5"
        When the user clicks the "OK" button of the Confirm dialog on the main page
        Then a toast with text "Action started on the selected items." is displayed
