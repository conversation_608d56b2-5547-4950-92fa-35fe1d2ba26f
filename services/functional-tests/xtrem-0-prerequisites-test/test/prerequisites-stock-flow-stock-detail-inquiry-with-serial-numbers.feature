#This test can only be executed with sage.
#The goal of this test is to run the stock detailed inquiry for the item & site
@inventory
Feature: prerequisites-stock-flow-stock-detail-inquiry-with-serial-numbers

    Scenario: stock check
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "SDI Item2" in the filter of the table field
        And the user ticks the item with text "SDI Item2" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        When the user selects the row with text "Accepted" in the "Quality control" labelled column header of the table field
        Then the value of the "Item ID" labelled nested text field of the selected row in the table field is "ID-987"
        And the value of the "On-hand quantity" labelled nested numeric field of the selected row in the table field is "5 each"
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "5 each"
        And the value of the "Allocated quantity" labelled nested numeric field of the selected row in the table field is "0 each"

# TODO: Serial number values may be compared later when serial number stock inquiry is available, so we cannot check them now.
#       For now let's keep the idea as a reminder not to be forced to invent the wheel twice.
#
# And the value of the "Starting serial number" labelled nested numeric field of the selected row in the table field is "SN000024"
# Then the value of the "Ending serial number" labelled nested numeric field of the selected row in the table field is "SN000028"
# #Serial number info
# When the user selects the "lines" bound table field on the main page
# And the user clicks the "Serial number information" dropdown action of row 1 of the table field
# #Verify that the serial numbers amount correspond with available quantity, and also with Start and end serial numbers
# And the user selects the "serialNumberLines" bound table field on the sidebar
# #Verify the serila numbers
# And the value of the "Serial number" labelled nested numeric field of row 1 in the table field is "SN000024"
# And the value of the "Serial number" labelled nested numeric field of row 2 in the table field is "SN000025"
# And the value of the "Serial number" labelled nested numeric field of row 3 in the table field is "SN000026"
# And the value of the "Serial number" labelled nested numeric field of row 4 in the table field is "SN000027"
# And the value of the "Serial number" labelled nested numeric field of row 5 in the table field is "SN000028"
# #Verifying that the serial numbers are not allocated
# And the value of the "Allocated" labelled nested checkbox field of row 1 in the table field is "false"
# And the value of the "Allocated" labelled nested checkbox field of row 2 in the table field is "false"
# And the value of the "Allocated" labelled nested checkbox field of row 3 in the table field is "false"
# And the value of the "Allocated" labelled nested checkbox field of row 4 in the table field is "false"
# And the value of the "Allocated" labelled nested checkbox field of row 5 in the table field is "false"
# #Checking that stock receipt number is displayed as the Last document
# #We checked only 1 since the other 4 are from Stock adjustment.
# And the value of the "Last document" labelled nested numeric field of row 1 in the table field is "SR230008"
# #Checking that the stock adjustment number is also displayed on other serial numbers.
# And the value of the "Last document" labelled nested numeric field of row 2 in the table field is "SA230004"
# And the value of the "Last document" labelled nested numeric field of row 3 in the table field is "SA230004"
# And the value of the "Last document" labelled nested numeric field of row 4 in the table field is "SA230004"
# And the value of the "Last document" labelled nested numeric field of row 5 in the table field is "SA230004"
