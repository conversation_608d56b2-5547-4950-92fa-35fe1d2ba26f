#The goal of this test is to verify that the user can create/delete tags from system tag page
@tag
Feature: prerequisites-tag-service
   Scenario: 00 - Add lock - LCK_TAGS
      Given the user opens the application on a HD desktop
      And the user adds the lock entry "LCK_TAGS"
   Scenario: 01 - Activate Service option tags
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
      Then the "Service options" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "tags"
      And the user selects the row 1 of the table field
      Then the user clicks the "Activate" dropdown action of the selected row of the table field
      Then a warn dialog appears on the main page
      When the user clicks the "OK" button of the Confirm dialog

      Then the "Service options" titled page is displayed
      And the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "tags"
      And the user selects the row 1 of the table field
      Then the value of the "Active" labelled nested checkbox field of the selected row in the table field is "true"

   Scenario: 02 - Verify tag creation
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/SysTag"
      Then the "Tag" titled page is displayed
      When the user clicks the "Create" labelled business action button on the navigation panel

      And the user selects the "Name" labelled text field on the main page
      And the user writes "TestTag1" in the text field
      And the user selects the "Description" labelled text field on the main page
      And the user writes "First test tag" in the text field
      And the user clicks the "Save" labelled business action button on the main page
      Then the value of the toast is "Record created"
      And the user waits 2 seconds

      Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/SysTag"
      Then the "Tag" titled page is displayed
      When the user clicks the "Create" labelled business action button on the navigation panel

      And the user selects the "Name" labelled text field on the main page
      And the user writes "TestTag2" in the text field
      And the user selects the "Description" labelled text field on the main page
      And the user writes "Second test tag" in the text field
      And the user clicks the "Save" labelled business action button on the main page
      Then the value of the toast is "Record created"
      And the user waits 2 seconds

      Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/SysTag"
      Then the "Tag" titled page is displayed
      When the user clicks the "Create" labelled business action button on the navigation panel

      And the user selects the "Name" labelled text field on the main page
      And the user writes "TestTag3" in the text field
      And the user selects the "Description" labelled text field on the main page
      And the user writes "Third test tag" in the text field
      And the user clicks the "Save" labelled business action button on the main page
      Then the value of the toast is "Record created"
      And the user waits 2 seconds

   Scenario: 03 - Verify tags values
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/SysTag"
      Then the "Tag" titled page is displayed

      And the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user selects the row 1 of the table field
      Then the value of the "Name" labelled nested text field of the selected row in the table field is "TestTag1"
      And the user selects the row 2 of the table field
      And the value of the "Name" labelled nested text field of the selected row in the table field is "TestTag2"
      And the user selects the row 3 of the table field
      And the value of the "Name" labelled nested text field of the selected row in the table field is "TestTag3"

      And the user selects the row 1 of the table field
      Then the value of the "Description" labelled nested text field of the selected row in the table field is "First test tag"
      And the user selects the row 2 of the table field
      And the value of the "Description" labelled nested text field of the selected row in the table field is "Second test tag"
      And the user selects the row 3 of the table field
      And the value of the "Description" labelled nested text field of the selected row in the table field is "Third test tag"

   Scenario: 04 - Verify tags deletion
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/SysTag"
      Then the "Tag" titled page is displayed

      And the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user selects the row 1 of the table field
      And the user clicks the "Name" labelled nested field of the selected row in the table field
      And the user selects the "Name" labelled text field on the main page
      Then the value of the text field is "TestTag1"

      And the user clicks the "Delete" labelled business action button on the main page
      When the user clicks the "Delete" button of the Confirm dialog on the main page

      Then the value of the toast is "Record deleted"
      And the user waits 2 seconds

      And the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user selects the row 1 of the table field
      And the user clicks the "Name" labelled nested field of the selected row in the table field
      And the user selects the "Name" labelled text field on the main page
      Then the value of the text field is "TestTag2"

      And the user clicks the "Delete" labelled business action button on the main page
      When the user clicks the "Delete" button of the Confirm dialog on the main page

      Then the value of the toast is "Record deleted"
      And the user waits 2 seconds

      And the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user selects the row 1 of the table field
      And the user clicks the "Name" labelled nested field of the selected row in the table field
      And the user selects the "Name" labelled text field on the main page
      Then the value of the text field is "TestTag3"

      And the user clicks the "Delete" labelled business action button on the main page
      When the user clicks the "Delete" button of the Confirm dialog on the main page

      Then the value of the toast is "Record deleted"
      And the user waits 2 seconds

   Scenario: 05 - Deactivate Service option tags
      Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
      Then the "Service options" titled page is displayed
      When the user selects the "$navigationPanel" bound table field on the navigation panel
      And the user filters the "Name" labelled column in the table field with value "tags"
      And the user selects the row 1 of the table field
      Then the user clicks the "Deactivate" dropdown action of the selected row of the table field
      Then a warn dialog appears on the main page
      When the user clicks the "OK" button of the Confirm dialog

   Scenario: 99 - Remove lock - LCK_TAGS
      Given the user opens the application on a HD desktop
      And the user removes the lock entry "LCK_TAGS"
