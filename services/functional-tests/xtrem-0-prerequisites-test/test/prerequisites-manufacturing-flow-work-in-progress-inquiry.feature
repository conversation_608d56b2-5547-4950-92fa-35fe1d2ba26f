# The purpose of this test is to verify the amounts in work in progress inquiry for a specific site and using different
# dates ("As of date" means = we rebuild the WIP amounts at this precise date).
# Site used = CHA-S01
# Work orders = WIP_TEST_1 and WIP_TEST_2

@prerequisites
@manufacturing
@Stack_Overflow
Feature: prerequisites-manufacturing-flow-work-in-progress-inquiry

    Scenario: 01-Verify default content of inquiry

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipInquiry"
        Then the "Work in progress inquiry" titled page is displayed
        And the user waits for 2 seconds

        # check Site is not defaulted
        When the user selects the "Site *" labelled reference field on the main page
        Then the value of the reference field is ""

        # check As of date is defaulted with today's date
        When the user selects the "As of date *" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"

        # check Search button is disabled as long as criteria have not been filled in correctly
        Then the "Run" labelled business action button on the main page is disabled

    Scenario: 02-Add values to the criteria and check again

        Given the user selects the "Site *" labelled reference field on the main page
        When the user writes "Rumi" in the reference field
        And the user selects "Site de Rumilly" in the reference field
        Then the value of the reference field is "Site de Rumilly"
        # check site is mandatory field
        Given the user clears the reference field
        Then the "You need to select or enter a value." validation error message of the reference field is displayed
        When the user writes "Chava" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"

        # check Run button is now enabled
        Then the "Run" labelled business action button on the main page is enabled

    Scenario: 03-Launch inquiry with site Chavanod and as of date 01/18/2024

        # change as of date = 01/18/2024
        When the user selects the "As of date *" labelled date field on the main page
        And the user writes "01/18/2024" in the date field
        Then the value of the date field is "01/18/2024"

        When the user clicks the "Run" labelled business action button on the main page
        And the user waits for 10 seconds

        # check column settings is now enabled
        When the user selects the "Results" labelled table field on the main page
        Then the "Open column panel" labelled button of the table field is enabled

        # verify default column settings
        And the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed

        When searches for "Planned" in the lookup dialog on the sidebar

        Then the table column configuration with name "Planned material cost" on the sidebar is ticked
        Then the table column configuration with name "Planned process cost" on the sidebar is ticked
        Then the table column configuration with name "Total planned cost" on the sidebar is ticked

        When searches for "Actual" in the lookup dialog on the sidebar

        Then the table column configuration with name "Actual material cost" on the sidebar is ticked
        Then the table column configuration with name "Actual process cost" on the sidebar is ticked
        Then the table column configuration with name "Total actual cost" on the sidebar is ticked

        When searches for "variance" in the lookup dialog on the sidebar

        Then the table column configuration with name "Material cost variance" on the sidebar is ticked
        Then the table column configuration with name "Process cost variance" on the sidebar is ticked
        Then the table column configuration with name "Closing variance" on the sidebar is unticked

        When searches for "work" in the lookup dialog on the sidebar

        Then the table column configuration with name "Work order" on the sidebar is ticked
        And the table column configuration with name "Work order" on the sidebar is locked
        Then the table column configuration with name "Work in progress total" on the sidebar is ticked

        And the user clicks the Close button of the sidebar

    Scenario: 04-Check values in grid using as of date = 01/18/2024

        When the user selects the "Results" labelled table field on the main page
        And the user waits for 10 seconds

        # check WIP costs for 1st work order = WIP_TEST_1
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_1"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_1"
        And the user selects the row with text "WIP_TEST_1" in the "Work order" labelled column header of the table field
        ## check planned costs (the ones calculated at the work order creation) ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs (the ones calculated after a tracking has been done) ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 20.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 267.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 287.5000"
        ## check cost variance (actual - planned) ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -80.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,347.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 287.5000"

        # check WIP costs for 2nd work order = WIP_TEST_2
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_2"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_2"
        And the user selects the row with text "WIP_TEST_2" in the "Work order" labelled column header of the table field
        ## check planned costs (the ones calculated at the work order creation) ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs (the ones calculated after a tracking has been done) ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 20.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 267.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 287.5000"
        ## check cost variance (actual - planned) ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -80.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,347.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 287.5000"

    Scenario: 05-Check values in grid using as of date = 01/25/2024 (WO end date of WIP_TEST_1 = 02/05/2024 & WIP_TEST_2 = 01/19/2024)

        # change as of date = 01/25/2024
        When the user selects the "As of date *" labelled date field on the main page
        And the user writes "01/25/2024" in the date field
        Then the value of the date field is "01/25/2024"

        When the user clicks the "Run" labelled business action button on the main page
        And the user waits for 10 seconds

        When the user selects the "Results" labelled table field on the main page

        # check WIP costs for 1st work order = WIP_TEST_1
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_1"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_1"
        And the user selects the row with text "WIP_TEST_1" in the "Work order" labelled column header of the table field
        ## check planned costs - they should remain the same as the ones of scenario 04 ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs - they should remain the same as the ones of scenario 04  ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 20.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 267.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 287.5000"
        ## check cost variance - they should remain the sames as the ones of scenario 04 ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -80.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,347.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 287.5000"

        # check WIP costs for 2nd work order = WIP_TEST_2 in this case (WO Closing date = 02/15/2024 > as of date = 01/25/2024)
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_2"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_2"
        And the user selects the row with text "WIP_TEST_2" in the "Work order" labelled column header of the table field
        ## check planned costs - they should remain the same as the ones of scenario 04 ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs - they should remain the same as the ones of scenario 04  ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 20.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 267.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 287.5000"
        ## check cost variance - they should remain the sames as the ones of scenario 04 ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -80.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,347.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 287.5000"


    Scenario: 06-Check values in grid using as of date = 02/05/2024 (2 additional trackings created on WO WIP_TEST_1)

        ## change as of date = 02/05/2024
        When the user selects the "As of date *" labelled date field on the main page
        And the user writes "02/05/2024" in the date field
        Then the value of the date field is "02/05/2024"

        When the user clicks the "Run" labelled business action button on the main page
        And the user waits for 10 seconds

        When the user selects the "Results" labelled table field on the main page

        # check WIP costs for 1st work order = WIP_TEST_1
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_1"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_1"
        And the user selects the row with text "WIP_TEST_1" in the "Work order" labelled column header of the table field
        ## check planned costs - they should remain the same as the ones of scenario 04 ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs - they must now include additional material tracking of € 30 and time tracking of € 120 ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 50.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 387.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 437.5000"
        ## check cost variance - they should now be decresed accordingly (actual - planned) ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -50.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,227.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 437.5000"

        # check WIP costs for 2nd work order = WIP_TEST_2 in this case (WO Closing date = 02/15/2024 > as of date = 02/05/2024)
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_2"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_2"
        And the user selects the row with text "WIP_TEST_2" in the "Work order" labelled column header of the table field
        ## check planned costs - they should remain the same as the ones of scenario 04 ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs - they should remain the same as the ones of scenario 04  ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 20.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 267.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 287.5000"
        ## check cost variance - they should remain the sames as the ones of scenario 04 ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -80.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,347.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 287.5000"

    Scenario: 07-Check values in grid using as of date = today's date

        # change as of date = today
        When the user selects the "As of date *" labelled date field on the main page
        And the user writes "12/31/2024" in the date field

        When the user clicks the "Run" labelled business action button on the main page
        And the user waits for 10 seconds

        When the user selects the "Results" labelled table field on the main page

        # check WIP costs for 1st work order = WIP_TEST_1 (work order still open )
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_1"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_1"
        And the user selects the row with text "WIP_TEST_1" in the "Work order" labelled column header of the table field
        ## check planned costs - they should remain the same as the ones of scenario 04 ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs - they must now include additional material tracking of € 30 and time tracking of € 120 ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 50.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 387.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 437.5000"
        ## check cost variance - they should now be decresed accordingly (actual - planned) ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -50.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,227.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 437.5000"

    Scenario: 08-Check values in grid using as of date = 02/15/2024 (WO end date of WIP_TEST_1 = 02/05/2024)
        # change as of date = 02/15/2024 (Date of closing of WIP_TEST_1)
        When the user selects the "As of date *" labelled date field on the main page
        And the user writes "02/15/2024" in the date field
        Then the value of the date field is "02/15/2024"

        When the user clicks the "Run" labelled business action button on the main page
        And the user waits for 10 seconds

        When the user selects the "Results" labelled table field on the main page

        # check WIP costs for 1st work order = WIP_TEST_1
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_1"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_1"
        And the user selects the row with text "WIP_TEST_1" in the "Work order" labelled column header of the table field
        ## check planned costs - they should remain the same as the ones of scenario 04 ##
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs - they must now include additional material tracking of € 30 and time tracking of € 120 ##
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 50.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 387.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 437.5000"
        ## check cost variance - they should now be decreased accordingly (actual - planned) ##
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ -50.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -2,227.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 437.5000"
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "In progress"


        # check WIP costs for 2nd work order = WIP_TEST_2(WO end date 01/19/2024 = closing date = 02/15/2024)
        When the user filters the "Work order" labelled column in the table field with value "WIP_TEST_2"
        And the user selects the row 1 of the table field
        Then the value of the "Work order" labelled nested text field of the selected row in the table field is "WIP_TEST_2"
        And the user selects the row with text "WIP_TEST_2" in the "Work order" labelled column header of the table field
        ## check planned costs
        Then the value of the "Planned material cost" labelled nested numeric field of the selected row in the table field is "€ 100.0000"
        Then the value of the "Planned process cost" labelled nested numeric field of the selected row in the table field is "€ 2,615.0000"
        Then the value of the "Total planned cost" labelled nested numeric field of the selected row in the table field is "€ 2,715.0000"
        ## check actual costs
        Then the value of the "Actual material cost" labelled nested numeric field of the selected row in the table field is "€ 120.0000"
        Then the value of the "Actual process cost" labelled nested numeric field of the selected row in the table field is "€ 2,537.5000"
        Then the value of the "Total actual cost" labelled nested numeric field of the selected row in the table field is "€ 2,657.5000"
        ## check cost variance
        Then the value of the "Material cost variance" labelled nested numeric field of the selected row in the table field is "€ 20.0000"
        Then the value of the "Process cost variance" labelled nested numeric field of the selected row in the table field is "€ -77.5000"
        Then the value of the "Work in progress total" labelled nested numeric field of the selected row in the table field is "€ 0.0000"
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Closed"

    Scenario: 09-Test the delete button
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
