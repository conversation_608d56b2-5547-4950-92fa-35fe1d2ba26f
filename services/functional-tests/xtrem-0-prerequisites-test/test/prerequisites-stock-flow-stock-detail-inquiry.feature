# The goal of this test is to check the flow of the stock detail inquiry page to ensure the filters work correctly
# and to ensure correctness and validity of the data gathered and reported

@inventory

Feature: prerequisites-stock-flow-stock-detail-inquiry

    Scenario: Create a search criteria for Item 1
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Sandfeld" in the filter of the table field
        And the user ticks the item with text "Sandfeld" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item for stock detail inquiry 1" in the filter of the table field
        And the user ticks the item with text "Item for stock detail inquiry 1" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

    Scenario: Verify the details of Item 1
        # Filter for avalable quantity
        When the user filters the "Available quantity" labelled column in the table field with value "975"
        And the user selects the row 1 of the table field
        Then the value of the "Quality control" labelled nested reference field of the selected row in the table field is "Accepted"
        And the value of the "Location" labelled nested reference field of the selected row in the table field is "Bulk 01"
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is "SDI_Lot_001"


    Scenario: Stock detail inquiry line item - Verify the lot information through sidebar
        # Filter for avalable quantity
        When the user filters the "Available quantity" labelled column in the table field with value "36"
        And the user opens the filter of the "Location" labelled column in the table field
        And the user searches "Bulk 02" in the filter of the table field
        And the user ticks the item with text "Bulk 02" in the filter of the table field
        When the user closes the filter of the "Location" labelled column in the table field
    # Not yet available:
    #     # Open sidebar for line item
    #     And the user clicks the "Lot information" dropdown action of row 1 of the table field
    #     Then the "Lot information - SDI_Lot_001" titled sidebar is displayed
    #     # Verify the sidebar Lot details
    #     When the user selects the "Creation date" labelled date field on the sidebar
    #     Then the value of the date field is "08/16/2023"
    #     When the user selects the "Quantity" labelled numeric field on the sidebar
    #     Then the value of the numeric field is "36"


    Scenario: Create a search criteria for Item 2 using Status and Lot
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Sandfeld" in the filter of the table field
        And the user ticks the item with text "Sandfeld" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item for stock detail inquiry 2" in the filter of the table field
        And the user ticks the item with text "Item for stock detail inquiry 2" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # Filter for status = Accepted
        Then the user opens the filter of the "Quality control" labelled column in the table field
        And the user searches "Accepted" in the filter of the table field
        And the user ticks the item with text "Accepted" in the filter of the table field
        Then the user closes the filter of the "Quality control" labelled column in the table field

        # Filter for Lot = SDI_Lot_003
        When the user filters the "Lot" labelled column in the table field with value "SDI_Lot_003"

        Then the table field is not empty

    Scenario: Verify Item 2 details
        # Filter the data to find 1 record
        And the user filters the "Available quantity" labelled column in the table field with value "465"

        # Verify the data
        And the user selects the row 1 of the table field
        Then the value of the "Quality control" labelled nested reference field of the selected row in the table field is "Accepted"
        And the value of the "Location" labelled nested reference field of the selected row in the table field is "Bulk 02"
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is "SDI_Lot_003"
        And the value of the "Allocated quantity" labelled nested reference field of the selected row in the table field is "0 each"
        And the value of the "Available quantity" labelled nested reference field of the selected row in the table field is "465 each"

    Scenario: Verify tunnelling to the serial numbers in stock
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Sandfeld" in the filter of the table field
        And the user ticks the item with text "Sandfeld" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item for stock issue with SN" in the filter of the table field
        And the user ticks the item with text "Item for stock issue with SN" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # Check that the item is serial number managed and the serial number stock inquiry opens
        Then the table field is not empty
        And the user selects the row 1 of the table field
        And the user clicks the "Serial number information" dropdown action of the selected row of the table field
