#This test can only be executed with sage.
#The goal of this test is to associate a new user to the current tenant and to verify it is possible to connect with it.
#This test can't be executed on in CI mode.

#To execute it from vscode you have to use parameters from and to add them to parameters-atp file.
# parameters-atp-sdmo-dev-sageid-template
# parameters-atp-sdmo-pp-sageid-template
# parameters-atp-sdmo-qa-sageid-template

#Dedicated users for each environment is defined here: https://confluence.sage.com/x/xBKgFw

@prerequisites
@xtrem_authorization
@distribution
@inventory
@manufacturing
@reference_data
@finance
Feature: prerequisites-flow-user-authentication-verification


    Scenario: Associate the secondary user to the tenant
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-authorization/User"
        Then the "Users" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "First name" labelled text field on the main page
        And the user writes "sageidatp" in the text field
        And the user selects the "Last name" labelled text field on the main page
        And the user writes "sdmo2" in the text field
        And the user selects the "Email" labelled text field on the main page
        And the user writes "param:loginUserName2" in the text field
        And the user selects the "Administrator" labelled switch field on the main page
        And the user turns the switch field "ON"
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record created" is displayed

    Scenario: Logout - 1
        When the user logs out of the system

    Scenario: reconnect with the secondary user and verify he can open a page
        When the user is logged into the system in desktop mode using the "param:loginUserName2" user name and "param:loginPassword2" password
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Allergen"
        Then the "Allergens" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel

    Scenario: Logout - 2
        When the user logs out of the system
