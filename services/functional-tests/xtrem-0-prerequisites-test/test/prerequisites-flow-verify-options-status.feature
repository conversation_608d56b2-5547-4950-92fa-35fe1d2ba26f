#This test can be executed only with sage.
#The goal of this test is to verify that certain options from Service Options and Option Management Base
#have the correct status for the test suite to run properly

@prerequisites
@distribution
@inventory
@manufacturing
@reference_data
@finance
Feature: prerequisites-flow-verify-options-status
    Scenario: 01 - Verify that the Serial Number option status is active
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
        Then the "Service options" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "serialNumberOption"
        And the user selects the row 1 of the table field
        Then the value of the "Active" labelled nested checkbox field of the selected row in the table field is "true"

    # temporary solution until we find a way to change it from csv without it being overrriten on layer extract/reload
    Scenario: Deactivate Intacct
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-structure/OptionManagementBase"
        Then the "Option management" titled page is displayed
        When the user selects the "isIntacctServiceOptionActive" bound switch field on the main page
        And the user turns the switch field "OFF"
        And the user clicks the "Save" labelled business action button on the main page
        And an info dialog appears on the main page
        And the user clicks the "OK" button of the Confirm dialog

    Scenario: 02 - Verify that the Intacct configuration status is not active
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-structure/OptionManagementBase"
        Then the "Option management" titled page is displayed
        When the user selects the "isIntacctServiceOptionActive" bound switch field on the main page
        And the switch field is set to "OFF"

    Scenario: 03 - Verify that the Payment Tracking option status is active
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
        Then the "Service options" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "paymentTrackingOption"
        And the user selects the row 1 of the table field
        And the value of the "Active" labelled nested checkbox field of the selected row in the table field is "true"
