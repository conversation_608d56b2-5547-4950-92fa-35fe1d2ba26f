# The goal of this test is to verify the value of the stock for an item after a partial issue as well as an adjustment#
# If script is failing, it might be because changes were made that refers to ER: XT-35035 / EPIC XT-49151#

@prerequisites
@inventory
Feature: prerequisites-stock-flow-stock-inquiry

        Scenario: Verify the user is able to create a Stock inquiry
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockInquiryBound"
                Then the "Stock inquiry" titled page is displayed
                When the user selects the "$navigationPanel" bound table field on the navigation panel
                # And the user clicks the "Show table filters" labelled button of the table field
                When the user filters the "Item" labelled column in the table field with value "Stock inquiry name"
                And the user selects the row with text "Stock inquiry name" in the "Item" labelled column header of the table field
                And the value of the "Quantity on hand" labelled nested text field of the selected row in the table field is "55 each"
