NOTE: Test deactivated for now, until tenant provisionning of service options is fixed.


#The goal of this test is to verify the status of the frp1000 is set to OFF
#It will then set the status to ON and OFF

@prerequisites
@distribution
@inventory
@manufacturing
@reference_data
@finance
Feature: prerequisites-crud-frp1000-config

    <PERSON><PERSON><PERSON>: Check the FRP1000 status is disabled
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-frp-1000/Frp1000Configuration"
        Then the "Sage FRP 1000 configuration" titled page is displayed
        When the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "OFF"
        When the user turns the switch field "ON"
        And the user selects the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        Then the switch field is set to "ON"
        When the user turns the switch field "OFF"
        And the user selects the "Save" labelled business action button on the main page
        Then the switch field is set to "OFF"
        Then a toast containing text "Record updated" is displayed
