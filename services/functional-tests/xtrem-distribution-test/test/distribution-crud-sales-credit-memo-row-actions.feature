# The purpose of this test is to verify row action (post,print... ) on the Sales credit memo page
# directly form navigation bar
@distribution
Feature: distribution-crud-sales-credit-memo-row-actions

    Scenario: 01 - Verify that the user can set dimensions in a sales credit memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SCED230001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SCED230001"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the "Apply to all lines" button of the Custom dialog
        Then a toast containing text "Dimensions applied." is displayed

    Scenario: 02 - Verify the user is able to print the credit memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SCED230001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SCED230001"
        And the user clicks the "Print" dropdown action of the selected row of the table field
        And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page

    Scenario: 03 - Verify the user is able to post the credit memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SCED230001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SCED230001"
        And the user clicks the "Post" dropdown action of the selected row of the table field
        And the user clicks the "Post" button of the dialog on the main page
        And a toast with text "The sales credit memo was posted." is displayed

    Scenario: 04 - Verify the user is able to sent the credit memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Sales invoice" titled page is displayed
        # Fill in the header fields on main page
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        # Add lines
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "RJournal entry item02" in the reference field
        And the user selects "RJournal entry item02" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        When the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Store the sales invoice Number
        When the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_SINVOICE]"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SINVOICE]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SINVOICE]"
        And the user clicks the "Post" dropdown action of the selected row of the table field
        And the user clicks the "Post" button of the dialog on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SINVOICE]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SINVOICE]"
        And the user clicks the "Create credit memo" dropdown action of the selected row of the table field
        And the user clicks the "Create" button of the dialog on the main page
        And the user selects the row with text "[ENV_SINVOICE]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "RJournal entry item02" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "salesCreditMemoLines" bound table field on the sidebar
        And the user selects the row with text "Draft" in the "Credit memo status" labelled column header of the table field
        And the user stores the value of the "Credit memo number" labelled nested link field of the selected row in the table field with the key "[ENV_SCM_Number]"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SCM_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SCM_Number]"
        And the user clicks the "Post" dropdown action of the selected row of the table field
        And the user clicks the "Post" button of the dialog on the main page
        And a toast with text "The sales credit memo was posted." is displayed
        Then the user refreshes the screen
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SCM_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SCM_Number]"
        And the user clicks the "Send" dropdown action of the selected row of the table field
        And the user selects the "Last name" labelled text field on a modal
        And the user writes "test" in the text field
        And the user selects the "Email" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And the user clicks the "Send" button of the Custom dialog
        Then a toast containing text "Sales credit memo <NAME_EMAIL>." is displayed

    Scenario: 05 - Verify that the user can delete a sales credit memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SCED230002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SCED230002"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Custom dialog
        Then a toast with text "Record deleted" is displayed
