# The goal of this test it to verify the automatic assignment of taxes based on the tax zone of the ship-to-address

@distribution

Feature:  distribution-flow-sales-order-tax-zone-assignment

    Scenario: 01 - Create Tax zone
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-tax/TaxZone"
        Then the "Tax zones" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        # Creating
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Colorado Tax Zone" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "COTAZ" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United States" in the reference field
        And the user selects "United States of America" in the reference field
        # Adding line
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "State tax" in the "Tax category" labelled column header of the table field
        And the user writes "Colorado state" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Colorado state sales tax" in the "Tax" labelled nested field of the selected row in the table field
        And the user stores the value of the "Tax" labelled nested reference field of the selected row in the table field with the key "[ENV_CTZ_SST01]"
        And the user selects the row with text "County tax" in the "Tax category" labelled column header of the table field
        And the user writes "Broomfield County" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Broomfield County sales tax" in the "Tax" labelled nested field of the selected row in the table field
        And the user stores the value of the "Tax" labelled nested reference field of the selected row in the table field with the key "[ENV_CTZ_CST02]"
        And the user selects the row with text "City tax" in the "Tax category" labelled column header of the table field
        And the user writes "Broomfield city" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Broomfield city tax" in the "Tax" labelled nested field of the selected row in the table field
        And the user stores the value of the "Tax" labelled nested reference field of the selected row in the table field with the key "[ENV_CTZ_CYT03]"
        And the user selects the row with text "Special tax" in the "Tax category" labelled column header of the table field
        And the user writes "Special tax Colorado" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Special tax Colorado" in the "Tax" labelled nested field of the selected row in the table field
        And the user stores the value of the "Tax" labelled nested reference field of the selected row in the table field with the key "[ENV_CTZ_STC04]"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed


    Scenario: 02 - Assign tax zone to a customer ship-to-address
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "MK Manufacturing" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And selects the "Address" labelled navigation anchor on the main page
        When the user selects the "addresses" bound pod collection field on the main page
        Then the selected pod collection field is enabled
        And the user selects the "MK Manufacturing" labelled pod collection item of the selected pod collection field
        And the user clicks the "Edit address" action of the selected pod collection item
        And the user selects the "Country" labelled reference field on the sidebar
        And the value of the reference field is "United States of America"
        And selects the "Information" labelled navigation anchor on the sidebar
        And the user selects the "Tax zone" labelled reference field on the sidebar
        And the user writes "Colorado Tax" in the reference field
        And the user selects "Colorado Tax Zone" in the reference field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 03 - Create sales order - Verify assignment of taxes
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Sale order creation
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Journal_Service_Item_002" in the reference field
        And the user selects "Journal_Service_Item_002" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "479.99" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Verify correct assignment of taxes
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Journal_Service_Item_002" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        Then the table field is not empty
        And the user selects the row with text "State tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_SST01]"
        And the user selects the row with text "County tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_CST02]"
        And the user selects the row with text "City tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_CYT03]"
        And the user selects the row with text "Special tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_STC04]"
        Then the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SO_NUM01]"


    Scenario: 04 - Create sale invoice - Verify assignment of taxes
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "date" bound nested field of the selected row in the table field
        And the user searches for "[ENV_SO_NUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Create shipment
        When the user clicks the "Create shipment" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "created" is displayed
        And the user dismisses all the toasts
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user dismisses all the toasts
        And the user waits 3 seconds
        When the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        # Then a toast containing text "The sales shipment has been posted." is displayed
        And the user dismisses all the toasts
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SH_NUM05]"
        # Create sales invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "shipToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SH_NUM05]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        When the user clicks the "Create invoice" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        # verify invoice
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "Site" labelled reference field on the main page
        And the value of the reference field is "TE Hampton"
        # Verify correct assignment of taxes
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Journal_Service_Item_002" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        Then the table field is not empty
        And the user selects the row with text "State tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_SST01]"
        And the user selects the row with text "County tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_CST02]"
        And the user selects the row with text "City tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_CYT03]"
        And the user selects the row with text "Special tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "[ENV_CTZ_STC04]"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # And the user clicks the "Save" labelled business action button on the main page
        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The sales invoice was posted." is displayed
