# This test can only be executed with sage.
# The purpose of this test is to verify the Currency conversion for the sale order
# Sales site : TE Hampton(United States)
# Customer Site: ATSERMO(France)
#  Exchange Rate : 1 EUR = 1,002 USD
# Item used: Conversion Item
@distribution
Feature:  distribution-flow-sales-order-currency-conversion
    Scenario: 01 - Verify the user can do Sales order creation with a currency different from the company currency
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "T<PERSON> Hampton" in the reference field
        And the user selects "T<PERSON> <PERSON>" in the reference field

        # Fill in Order date field
        And the user selects the "Order date" labelled date field on the main page
        And the user writes "10/10/2024" in the date field
        And the user presses Tab

        # Fill in Customer reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "ATSERMO" in the reference field
        And the user selects "ATSERMO" in the reference field

        # Add a line in panel
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Conversion item" in the reference field
        And the user selects "Conversion item" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field

        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "343" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Add tax detail
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "taxDetails" bound table field on a modal
        And the user selects the row with text "State tax" in the "category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user selects the row with text "Hampton State Tax" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed


        # Wait
        And the user waits 3 seconds


        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SO_NUM01]"

    Scenario: 02 - Verify currency conversion amounts on the Sales order created
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed

        When the user selects the "Sales orders" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        And the user selects the row with text "[ENV_SO_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # verify amounts on grid
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 343.00"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "€ 343.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 1,715.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "€ 171.50"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 1,886.50"

        # verify amounts on panel
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "1,715.00"
        And the user selects the "Total tax" labelled numeric field on the sidebar
        And the value of the numeric field is "171.50"
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "1,886.50"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "1,718.43"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "1,890.27"
        Then the user clicks the "Cancel" button of the dialog on the sidebar

        # verify tax detail
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Hampton State Tax" in the "Tax" labelled column header of the table field
        And the value of the "Taxable base" labelled nested reference field of the selected row in the table field is "€ 1,715.00"
        And the value of the "Tax rate" labelled nested reference field of the selected row in the table field is "10.00 %"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "€ 171.50"
        Then the user clicks the "ok" bound business action button on a modal

        # verify exchange rate
        When selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Transaction currency" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        Then the value of the text field is "1 EUR = 1.002 USD"

        # verify totals tab
        Then selects the "Totals" labelled navigation anchor on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "1,715.00"
        And the user selects the "tax" labelled numeric field on the main page
        And the value of the numeric field is "171.50"
        And the user selects the "Including tax" labelled numeric field on the main page
        Then the value of the numeric field is "1,886.50"

    Scenario: 03 - Confirm the sales order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_SO_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Confirm
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And a toast containing text "The sales order was confirmed." is displayed
        And the user selects the "displayStatus" labelled label field on the main page

        Then the value of the label field is "Confirmed"
        # Closing
        And the user clicks the "Close order" labelled business action button on the main page
        Then the user clicks the "Close order" button of the Confirm dialog
        Then a toast containing text "The sales order is closed." is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Closed"

    Scenario: 04 - Verify currency conversion amounts on the closed Sales order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_SO_NUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # verify amounts on grid
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 343.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 1,715.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "€ 171.50"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 1,886.50"

        # verify amounts on panel
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "1,715.00"
        And the user selects the "Total tax" labelled numeric field on the sidebar
        And the value of the numeric field is "171.50"
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "1,886.50"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "1,718.43"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "1,890.27"
        Then the user clicks the "Apply" button of the dialog on the sidebar

        # verify exchange rate
        When selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Transaction currency" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        Then the value of the text field is "1 EUR = 1.002 USD"

        # verify totals tab
        When selects the "Totals" labelled navigation anchor on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "1,715.00"
        And the user selects the "Tax" labelled numeric field on the main page
        And the value of the numeric field is "171.50"
        And the user selects the "Including tax" labelled numeric field on the main page
        Then the value of the numeric field is "1,886.50"

        # Verify the totals tab - Amounts in company currency
        When the user selects the "totalAmountExcludingTaxInCompanyCurrency" bound numeric field on the main page
        Then the value of the numeric field is "1,718.43"

        When the user selects the "totalAmountIncludingTaxInCompanyCurrency" bound numeric field on the main page
        Then the value of the numeric field is "1,890.27"
