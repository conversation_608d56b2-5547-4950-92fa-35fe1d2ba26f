#This test can only be executed with sage.
#The goal of this test is to verify that the user can do CRUD on Purchase receipt with serial numbers
@distribution
Feature: distribution-crud-purchase-receipt-with-sn

    Scenario: verify that the user can create a purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        # Create
        When the user clicks the "create" labelled business action button on the main page

        # Receiving site
        And the user selects the "Receiving site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        # Supplier
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "<PERSON>yre<PERSON>" in the reference field
        And the user selects "Lyreco" in the reference field

        # Number
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PR_002" in the text field

        # Add line through sidebar
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Item name
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Light emitting diode" in the reference field
        And the user selects "Light emitting diode" in the reference field

        # Quantity in purchase unit
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "4" in the numeric field

        # Move to price tab
        And selects the "Price" labelled navigation anchor on the sidebar

        # Gross price
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "4" in the numeric field

        # Discount
        And the user selects the "Discount" labelled numeric field on the sidebar
        And the user writes "1.00" in the numeric field

        # Charge
        And the user selects the "Charge" labelled numeric field on the sidebar
        And the user writes "1.00" in the numeric field

        # Apply line item to grid
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Select Line item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Light emitting diode" in the "Item" labelled column header of the table field

        # Allocate stock
        And the user clicks the "Stock details" dropdown action of the selected row of the table field

        # Add stock detail
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field

        # Select line and Add detail inline
        And the user selects the row with text "4 each" in the "quantityInStockUnit" bound column header of the table field

        # Status
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field

        # Open up the Serial number table
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field

        # Serial numbers allocation
        When the user selects the "stockDetailSerialNumbers" bound table field on a modal

        # Add line
        And the user clicks the "addSerialNumberRange" bound action of the table field

        And the user selects the row 1 of the table field
        And the user writes "SN_001" in the "fromSerialNumber" labelled nested reference field of the selected row in the table field
        And the user selects "SN_001 (New)" in the "From serial number" labelled nested field of the selected row in the table field
        # Quantity
        And the user writes "4" in the "Quantity" labelled nested numeric field of the selected row in the table field

        # Verifying the serial numbers status
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Accepted" in the "Quality Control" labelled column header of the table field
        And the value of the "Serial number" labelled nested text field of the selected row in the table field is "Assigned"
        # Save
        And the user clicks the "OK" labelled business action button on a modal
        Then the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify that the user can do Tax allocation on a line
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR_002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound table field on the main page

        And the user selects the row with text "Light emitting diode" in the "item" labelled column header of the table field

        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        And the user selects the "taxDetails" bound table field on a modal

        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        # Bug https://jira.sage.com/browse/XT-56119 : Unable to search for tax using keyboard. Have to do lookup manually
        #And the user writes "UK Purchase Goods Reduced Rate" in the "Tax" labelled nested reference field of the selected row in the table field
        #And the user selects "UK Purchase Goods Reduced Rate" in the "Tax" labelled nested field of the selected row in the table field

        # Click to show the lookup button
        # And the user waits 2 seconds
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        #Tax selection
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field

        And the user selects the "taxReference" bound table field on a modal
        And the user selects the row with text "UK Purchase Goods Reduced Rate" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "taxDetails" bound table field on a modal
        And the user selects the row with text "UK Purchase Goods Reduced Rate" in the "tax" labelled column header of the table field
        And the value of the "taxableBase" labelled nested numeric field of the selected row in the table field is "£ 16.00"
        And the value of the "taxRate" bound nested numeric field of the selected row in the table field is "5.00 %"
        And the value of the "taxAmount" bound nested numeric field of the selected row in the table field is "£ 0.80"
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed



    Scenario: Verify that the user can update a pending Purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR_002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Light emitting diode" in the "item" labelled column header of the table field
        ##Changing the number of quantity
        And the user writes "5" in the "quantity" bound nested numeric field of the selected row in the table field
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        ##Stock allocation
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Light emitting diode" in the "item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "4 each" in the "quantityInStockUnit" bound column header of the table field
        And the user writes "5" in the "quantityInStockUnit" bound nested numeric field of the selected row in the table field
        #Serial numbers allocation
        When the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user selects the row with text "SN_001" in the "fromSerialNumber" labelled column header of the table field
        And the user writes "5" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user presses Enter
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        ## Update Tax details
        ## Tax is UK Purchase Goods Standart Rate its % is 20% therefore 20% of 20 is  3,60
        ## The tax reference field is failing to find my tax group, therefore I will comment out
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Light emitting diode" in the "item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        And the user selects the "taxDetails" bound table field on a modal
        And the user selects the row with text "£ 20.00" in the "taxableAmount" bound column header of the table field
        And the user clicks the "tax" labelled nested field of the selected row in the table field
        #Tax update
        And the user opens the lookup dialog in the "tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user selects the row with text "UK Purchase Goods Standard Rate" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user selects the "taxDetails" bound table field on a modal
        And the user selects the row with text "£ 20.00" in the "taxableBase" labelled column header of the table field
        And the value of the "taxableBase" labelled nested numeric field of the selected row in the table field is "£ 20.00"
        And the value of the "taxRate" bound nested numeric field of the selected row in the table field is "20.00 %"
        And the value of the "taxAmount" bound nested numeric field of the selected row in the table field is "£ 4.00"
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify that the user can print a pending Purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR_002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
    #We don't want to click the link as it re direct to a page that the bot can not detect

    Scenario: Verify that the user can delete pending Purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR_002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
