# This test can only be executed with sage.
# The purpose of this test is to verify the Currency conversion for Purchase return
# Return site : TE Hampton(United States)
# Supplier: BARRES(France)
#  Exchange Rate : 1 EUR = 1,002 USD
# Line 46 to 51, and 54 will be commented out when bug is fixed XT-55002
@distribution
Feature:  distribution-flow-purchase-return-currency-conversion

    Scenario: 01 - Verify the user can do Purchase return creation with a currency different from the company currency from receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user clicks the "create" labelled business action button on the main page
        #create
        And the user selects the "Return site" labelled reference field on the main page
        And the user writes "T<PERSON> <PERSON>" in the reference field
        And the user selects "T<PERSON> Hampton" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field
        And the user selects the "Return request date" labelled date field on the main page
        And the user writes "10/10/2023" in the date field
        #Adding a new item in the receipt line by using the select from purchase order option
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from receipts" labelled business action button of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "PR230023" in the "Receipt number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        ##Adding reason
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Return reason" labelled reference field on the sidebar
        And the user writes "Consignment issue" in the reference field
        And the user selects "Consignment issue" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_ReturnNumber]"
    Scenario: 02 - Open the created purchase return and verify the data fields
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "number" bound column in the table field with value "[ENV_ReturnNumber]"
        And the user selects the row with text "[ENV_ReturnNumber]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ## Submit for approval
        And the user clicks the "Submit for approval" labelled business action button on the main page
        # And a toast containing text "Record updated" is displayed
        ##Verify the header
        And the user selects the "Return site" labelled reference field on the main page
        And the value of the reference field is "TE Hampton"
        And the user selects the "Supplier" labelled reference field on the main page
        Then the value of the reference field is "BARRES"
        # verify amounts on grid
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 343.00000"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "€ 343.00000"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 1,715.00"
        # verify amounts on panel
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the value of the numeric field is "343.00000"
        And the user selects the "Net price" labelled numeric field on the sidebar
        And the value of the numeric field is "343.00000"
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "1,715.00"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "1,718.43"
        Then the user clicks the "Apply" button of the dialog on the sidebar
        ##Verify information tab
        When selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Transaction currency" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "1 EUR = 1.002 USD"
        # verify totals tab
        And selects the "Totals" labelled navigation anchor on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "1,715.00"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        # Click Save Crud Button on main page

        And the user clicks the "Approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Approval status updated." is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Approved"
