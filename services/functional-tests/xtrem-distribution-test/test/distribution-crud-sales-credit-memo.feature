# The purpose of this test is to CRUD the Sales credit memo page.
# Sales Site: Entrepot De Saint Denis
# Customer: ATSERMO
# Items: Service Item - Sale_Service_Item_001
#        Non-Stock Item - Sale_Nonstock_Item_001
# Uses 1 x Sales Invoice (SIPED230004)
@distribution
Feature: distribution-crud-sales-credit-memo

    Scenario: 01 - Open and find the Sales invoice document and create credit memo from it
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        # Find the specific Sales invoice
        When the user selects the "Sales invoices" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        And the user selects the row with text "SIPED230004" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Verify
        Then the "Sales invoice SIPED230004" titled page is displayed

        # Main display status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

        # Step flow check
        When the user selects the "invoiceStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Post" item of the step-sequence is complete
        Then the status of the "Credit" item of the step-sequence is incomplete

        # Indicator bar check
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "2"

        # Store the values
        # Site
        When the user selects the "Site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"
        # Customer
        When the user selects the "Bill-to customer" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Customer]"

        # Click on the credit business action button
        When the "Create credit memo" labelled business action button on the main page is visible
        And the user clicks the "Create credit memo" labelled business action button on the main page
        Then the dialog title is "Confirm creation"
        And the user clicks the "Create" button of the Confirm dialog

        # Browser should redirect to newly generated sales credit memo page
        And a toast with text "Record created" is displayed

    Scenario: 02 - Verify Sales Credit Memo details
        # Wait for page to redirect
        And the user waits 5 seconds
        # Display main status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"

        # Step flow check
        When the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is current
        Then the status of the "Post" item of the step-sequence is incomplete

        # Site
        When the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"

        # Customer
        When the user selects the "Bill-to customer" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Customer]"

        # Store the value of the SCM Number
        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCM_Number]"
        # Credit memo date check
        When the user selects the "Credit memo date" labelled date field on the main page
        # T = Current date, because it was created above just now
        Then the value of the date field is a generated date with value "T"

        # Indicator bar tile check = 2 Items
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "2"

        # Footer Business action button checks
        And the "Post" labelled business action button on the main page is visible
    Scenario: 03 - Update the SCM
        When selects the "Information" labelled navigation anchor on the main page
        # Update the Reason reference field
        And the user selects the "Reason" labelled reference field on the main page
        And the user writes "Standard refund" in the reference field
        And the user selects "Standard refund" in the reference field
        And the value of the reference field is "Standard refund"

        # Save the update
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 04 - Update the SCM - through row sidebar
        # Move to Lines tab
        When selects the "Lines" labelled navigation anchor on the main page
        # Select the table and find row
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        # Item description update
        And the user selects the "Item description" labelled text field on the sidebar
        And the user writes "Updated description change" in the text field
        And the user stores the value of the text field with the key "[ENV_Updated_Description]"

        # Apply and Save the update
        And the user clicks the "Apply" button of the dialog on the sidebar
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Verify the update
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        And the user selects the "Item description" labelled text field on the sidebar
        Then the value of the text field is "[ENV_Updated_Description]"

        And the user clicks the "Cancel" button of the dialog on the sidebar



    Scenario: 05 - Update the SCM - through row inline
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Service_Item_001" in the "Item" labelled column header of the table field

        # Update Gross price
        And the user writes "30.00" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user stores the value of the "Gross price" labelled nested numeric field of the selected row in the table field with the key "[ENV_Updated_Gross_Price]"

        # Save the update
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Verify the update
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Service_Item_001" in the "Item" labelled column header of the table field
        Then the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "[ENV_Updated_Gross_Price]"
        And  the value of the "Total including tax" labelled nested numeric field of the selected row in the table field is "€ 432.00"


    Scenario: 06 - Print the Sales credit memo
        And the user clicks the "Print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast with text "The sales credit memo has been printed." is displayed

    Scenario: 07 - Delete a row in the SCM
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        # Find the specific Sales credit memo
        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field

        # Delete row
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Continue" button of the Confirm dialog
        # Save
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        # NOTE: If CI fails and says text is : "Record has been updated successfully" instead of "Record updated" or vice versa,
        # then it is an ATP/applicative bug. Robot is too fast, platform function not being overriden. Race condition issue?
        And the user waits 5 seconds
        Then a toast containing text "Record updated" is displayed

        # Indicator bar tile check = 1 Items
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "1"

    Scenario: 08 - Delete the sales credit memo
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        # Find the specific Sales credit memo
        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Display main status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"

        # Delete the document
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast with text "Record deleted" is displayed
