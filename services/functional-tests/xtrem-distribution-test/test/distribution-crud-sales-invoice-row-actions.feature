# The purpose of this test is to verify the creation, update, deletion and all the available row actions from the main list of sales invoice
# Financial site : Swindon
# Customer : Distributor

@distribution
@Stack_Overflow
Feature: distribution-crud-sales-invoice-row-actions

    Scenario: Verify the user can create sales invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Sales invoice" titled page is displayed
        # Fill in the header fields on main page
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        # Add lines
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "RJournal entry item02" in the reference field
        And the user selects "RJournal entry item02" in the reference field
        And the user selects the "Provider site" labelled reference field on the sidebar
        And the value of the reference field is "Swindon"
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        When the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "15.90" in the numeric field
        And the user selects the "Discount" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Net price" labelled numeric field on the sidebar
        And the value of the numeric field is "14.31"
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Store the sales invoice Number
        When the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_SINVOICE1]"

    Scenario: Verify that the user can print a sales invoice directly from the main list
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SINVOICE1]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SINVOICE1]"
        And the user clicks the "Print" dropdown action of the selected row of the table field
        And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast containing text "The sales invoice has been printed." is displayed

    Scenario: Verify that the user can send a sales invoice directly from the main list
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        # Filtering by "Posted" because the function has been changed as you should not be able to send a Draft invoice per email.
        When the user selects the "Posted" dropdown option in the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Send" dropdown action of the selected row of the table field
        And the user selects the "Title" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Dr." in the dropdown-list field
        And the user selects the "First name" labelled text field on a modal
        And the user writes "Recipient" in the text field
        And the user selects the "Last name" labelled text field on a modal
        And the user writes "Test" in the text field
        And the user selects the "Email" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        Then a toast containing text "Email sent" is displayed

    Scenario: Verify that the user can set dimensions in a sales invoice directly from the main list
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SINVOICE1]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SINVOICE1]"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        And the user clicks the Close button of the dialog on the main page

    Scenario: Verify that the user can delete a sales invoice directly from the main list
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SINVOICE1]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_SINVOICE1]"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        Then a toast containing text "Record deleted" is displayed

    Scenario: Verify that the user can post a sales invoice directly from the main list
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SISW240002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SISW240002"
        And the user clicks the "Post" dropdown action of the selected row of the table field
        And the dialog title is "Confirm posting"
        And the user clicks the "Post" button of the dialog on the main page
        Then a toast containing text "The sales invoice was posted." is displayed

    Scenario: Verify that the user can create a credit memo from a sales invoice directly from the main list
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SISW240002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SISW240002"
        And the user clicks the "Create credit memo" dropdown action of the selected row of the table field
        And the dialog title is "Confirm creation"
        And the user clicks the "Create" button of the dialog on the main page
        Then a toast containing text "Record created" is displayed
