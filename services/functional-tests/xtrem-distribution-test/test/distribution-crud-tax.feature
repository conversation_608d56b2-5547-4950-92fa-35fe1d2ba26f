#This test can only be executed with sage.
#The goal of this test is to verify the CRUD on Tax
@distribution
Feature:  distribution-crud-tax

    Scenario: Tax creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Tax category" labelled reference field on the main page
        And the user writes "Value Added Tax" in the reference field
        And the user selects "Value Added Tax" in the reference field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Let's Tax" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "South Africa" in the reference field
        And the user selects "South Africa" in the reference field
        And the user selects the "Primary external reference" labelled text field on the main page
        Then the user writes "Happy Tax" in the text field
        # Adding line
        When the user selects the "taxValues" bound table field on the main page
        And the user clicks the "addTaxValue" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "01/11/2024" in the "End date" labelled nested numeric field of the selected row in the table field
        And the user writes "15" in the "Rate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Tax update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "Let's Tax"
        And the user selects the row with text "Let's Tax" in the "name" bound column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user selects the "Primary external reference" labelled text field on the main page
        And the user writes "Happy Tax everyone" in the text field
        And the user selects the "taxValues" bound table field on the main page
        And the user selects the row with text "01/11/2024" in the "End date" labelled column header of the table field
        And the user writes "14" in the "Rate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Tax read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "Let's Tax"
        And the user selects the row with text "Let's Tax" in the "name" bound column header of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        When the user selects the "Tax category" labelled reference field on the main page
        And the value of the reference field is "Value Added Tax"
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Let's Tax"
        And the user selects the "Country" labelled reference field on the main page
        And the value of the reference field is "South Africa"
        And the user selects the "Primary external reference" labelled text field on the main page
        And the value of the text field is "Happy Tax everyone"
        And the user selects the "taxValues" bound table field on the main page
        And the user selects the row with text "01/11/2024" in the "End date" labelled column header of the table field
        Then the value of the "Rate" labelled nested numeric field of the selected row in the table field is "14"

    Scenario: Tax deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/Tax"
        Then the "Taxes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "name" bound column in the table field with value "Let's Tax"
        And the user selects the row with text "Let's Tax" in the "name" bound column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        #Delete
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
