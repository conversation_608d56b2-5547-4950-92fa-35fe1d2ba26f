# The purpose of this test is to test FLOW of the Sales credit memo page from return request(Return type: credit only).
# Sales Site: Entrepot De Saint Denis
# Customer: ATSERMO
# Items: Service Item - Sale_Service_Item_001
#        Non-Stock Item - Sale_Nonstock_Item_001
#        Stock Item - Sale_Stock_Item_001
# Uses 1 x Sales return request (ST230012)
@distribution
Feature: distribution-flow-sales-credit-memo-from-sales-return-request

    Scenario: Find and open the return request
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "ST230012" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Display main status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Approved"

        # Indicator bar tile check
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "3"

        # Store the values

        # Site
        When the user selects the "Site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"
        # Customer
        When the user selects the "Sold-to customer" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Customer]"



        When the user clicks the "Create credit memo" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast with text "Sales credit memo created" is displayed

    Scenario: Verify the Sales Credit memo
        # Wait for the page to redirect
        And the user waits 5 seconds
        # Step flow check
        When the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is current
        Then the status of the "Post" item of the step-sequence is incomplete

        # Site
        When the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"

        # Customer
        When the user selects the "Bill-to customer" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Customer]"

        # Store the value of the SCM Number
        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCM_Number]"

        # Credit memo date check
        When the user selects the "Credit memo date" labelled date field on the main page
        # T = Current date, because it was created above just now
        Then the value of the date field is a generated date with value "T"

        # Indicator tile check = 3 Items
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "3"

    Scenario: Post the Sales credit memo

        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast with text "The sales credit memo was posted." is displayed

        # Force screen revalidation to acquire the new credit memo reference
        And the user refreshes the screen

        # Store the new SCM number
        When the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SCM_Number1]"

    Scenario: Verify the posted Sales credit memo
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        # Find the specific Sales credit memo
        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user selects the row with text "[ENV_SCM_Number1]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Display main status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

        # Step flow check
        When the user selects the "creditMemoStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Post" item of the step-sequence is complete

        # Footer button visibility check
        Then the "Post" labelled business action button on the main page is hidden




    Scenario: Print the Sales credit memo
        And the user clicks the "Print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast containing text "The sales credit memo has been printed." is displayed

        # This check only works if document is printed when in posted status
        When selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Printed" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
