#This test can only be executed with sage.
#The goal of this test is to verify that the user can do CRUD on Sales shipment
@distribution
Feature: distribution-crud-sales-shipment

    Scenario: Verify that the user can open and update a Sales Shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "SH230024"
        And the user selects the row with text "SH230024" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Stock allocation
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row with text "0 each" in the "quantityToAllocate" bound column header of the table field
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the user clicks the "save" labelled business action button on a modal
        ##Taking away stock
        When the user selects the "lines" bound table field on the main page
        And the user waits 2 seconds
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row with text "3 each" in the "quantityToAllocate" bound column header of the table field
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the user clicks the "save" labelled business action button on a modal
        ##Confirm the stock is not allocated
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"

    Scenario: Verify that the user can delete a Shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SH230024" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        #Delete
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: Verify that the user can create a Sales Shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        ##Create
        When the user clicks the "create" labelled business action button on the main page
        And the user selects the "Stock site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "Ship-to customer *" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "Shipment_001" in the text field
        #Adding line by using Add liens from orders button
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from orders" labelled business action button of the table field

        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "SO230018" in the "Document" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify that the user can allocate stock on a Sales Shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Shipment_001" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Stock allocation
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "save" labelled business action button on a modal
        ##Confirm the allocation of stock
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"

    Scenario: Verify that the user can update a Sales Shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Shipment_001" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Stock allocation
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row with text "3 each" in the "quantityToAllocate" bound column header of the table field
        And the user writes "2" in the "quantityToAllocate" bound nested numeric field of the selected row in the table field
        And the user clicks the "Available quantity" labelled nested field of the selected row in the table field
        # And the user presses TAB
        Then the user clicks the "save" labelled business action button on a modal
        When the user selects the "lines" bound table field on the main page
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Partially allocated"
        And the user writes "2" in the "quantity" bound nested numeric field of the selected row in the table field
        And the user clicks the "Stock unit conversion factor" labelled nested field of the selected row in the table field
        And the value of the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field is "2 each"
        And the user clicks the "customSave" bound business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify that the user can confirm a Sales Shipment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Shipment_001" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Confirm
        And the user clicks the "confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        #Then a toast containing text "The sales shipment has been confirmed" is displayed
        Then a toast containing text "Status updated." is displayed
