# The goal of this test is to assign a tax solution to a country and verify that, that same country is displayed
# on the tax solution page
@distribution
Feature:  distribution-flow-tax-solution-countries
    Scenario: 01 - Assign tax solution to country
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Country"
        Then the "Countries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "United Kingdom" in the "name" labelled column header of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        Then the "Country United Kingdom" titled page is displayed
        And the user selects the "Legislation" labelled reference field on the main page
        And the value of the reference field is "United Kingdom"
        And the user selects the "Tax solution" labelled reference field on the main page
        And the value of the reference field is "United Kingdom tax solution"
        # When the user searches for "Australia" in the navigation panel
        # And the user clicks the "first" navigation panel's row
        # And the user selects the "Tax solution" labelled reference field on the main page
        # And the user writes "United Kingdom tax solution" in the reference field
        # And the user selects "United Kingdom tax solution" in the reference field
        # And a toast containing text "This tax solution is already assigned to GB country" is displayed
        # # validation on the 'Tax solution' field
        # And the user clicks the "Save" labelled business action button on the main page
        # And a validation error message is displayed with text
        #     """
        #     Validation errors
        #     You have 1 error in the following grid: Tax solution.
        #      You cannot enter a tax solution for a country without legislation.
        #     """
        # And the user waits for 10 seconds
        # And the user selects the "Legislation" labelled reference field on the main page
        # And the user writes "United Kingdom" in the reference field
        # And the user selects "United Kingdom" in the reference field
        # And the user clicks the "Save" labelled business action button on the main page
        # And a toast containing text "Record updated" is displayed
        When the user searches for "Northern Ireland" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Legislation" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        # validation on the 'Legislation' field
        And the user clicks the "Save" labelled business action button on the main page
        And a validation error message is displayed containing text
            """
            You need to enter a tax solution
            """
        And the user waits for 10 seconds
        And the user selects the "Tax solution" labelled reference field on the main page
        And the user writes "United Kingdom tax solution" in the reference field
        And the user selects "United Kingdom tax solution" in the reference field
        And a toast containing text "This tax solution is already assigned to GB country." is displayed
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed


    Scenario: 02 - Verify that country is displayed on the tax solution page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxSolution"
        Then the "Tax solutions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        # And the user selects the row with text "United Kingdom tax solution" in the "name" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "id" labelled nested field of the selected row in the table field
        When the user searches for "United Kingdom" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the "Tax solution United Kingdom tax solution" titled page is displayed
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "United Kingdom tax solution"
        And the user selects the "Countries" labelled multi reference field on the main page
        And the value of the multi reference field is "United Kingdom|Northern Ireland"
