# The goal of this test it to verify the creation, update and deletion of tax zones

@distribution
Feature:  distribution-crud-tax-zone

    Scenario: 01 - Tax zone creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxZone"
        Then the "Tax zones" titled page is displayed
        When the user clicks the "Create" labelled business action button on the main page
        #Creating
        And the user selects the "Name" labelled text field on the main page
        And the user writes "US State Tax Zone" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "COTAZ" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United States" in the reference field
        And the user selects "United States of America" in the reference field
        #Adding line
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "State tax" in the "Tax category" labelled column header of the table field
        And the user writes "Colorado state" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Colorado state sales tax" in the "Tax" labelled nested field of the selected row in the table field
        And the user selects the row with text "County tax" in the "Tax category" labelled column header of the table field
        And the user writes "Broomfield County" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Broomfield County sales tax" in the "Tax" labelled nested field of the selected row in the table field
        And the user selects the row with text "City tax" in the "Tax category" labelled column header of the table field
        And the user writes "Denver city" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Denver city tax" in the "Tax" labelled nested field of the selected row in the table field
        And the user selects the row with text "Special tax" in the "Tax category" labelled column header of the table field
        And the user writes "Special tax Colorado" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Special tax Colorado" in the "Tax" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed


    Scenario: 02 - Tax zone update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxZone"
        Then the "Tax zones" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "US State Tax Zone" in the "name" bound column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Colorado Tax zone" in the text field
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "City tax" in the "Tax category" labelled column header of the table field
        And the user writes "Broomfield city" in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects "Broomfield city tax" in the "Tax" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 03 - Tax read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxZone"
        Then the "Tax zones" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Colorado Tax zone" in the "name" bound column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        #Reading
        And the user selects the "Name" labelled text field on the main page
        And the value of the text field is "Colorado Tax zone"
        And the user selects the "ID" labelled text field on the main page
        And the value of the text field is "COTAZ"
        And the user selects the "Country" labelled reference field on the main page
        And the value of the reference field is "United States of America"
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "State tax" in the "Tax category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "Colorado state sales tax"
        And the user selects the row with text "County tax" in the "Tax category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "Broomfield County sales tax"
        And the user selects the row with text "City tax" in the "Tax category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "Broomfield city tax"
        And the user selects the row with text "Special tax" in the "Tax category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "Special tax Colorado"


    Scenario: Tax deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxZone"
        Then the "Tax zones" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Colorado Tax zone" in the "name" bound column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        #Delete
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
