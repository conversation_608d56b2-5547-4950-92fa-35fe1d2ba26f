#the purpose of this test is to verify creation of purchase credit memo from purchase invoice-PI230007 for with TE Hampton site and stawberry cup cake item.

@distribution
Feature: distribution-flow-purchase-credit-memo-from-invoice

    Scenario: Create purchase credit memo from purchase invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in fields on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "TE Headquarter" in the reference field
        And the user selects "TE Headquarter" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "Lenovo" in the reference field
        And the user selects "Lenovo" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PCM-PINV" in the text field
        And the user selects the "Reason *" labelled reference field on the main page
        And the user writes "Stock value correction" in the reference field
        And the user selects "Stock value correction" in the reference field
        And the user selects the "supplierCreditMemoReference" labelled text field on the main page
        And the user writes "SCMemo-PINV" in the text field
        And the user selects the "totalCreditMemoAmountExclTax" labelled numeric field on the main page
        And the user writes "325.00" in the numeric field
        And the user selects the "totalCreditMemoTax " labelled numeric field on the main page
        And the user writes "0.00" in the numeric field
        #Selects/adding the lines from purchase invoice
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "selectFromInvoice" bound add action of the table field

        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "PI230007" in the "Invoice number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog

        And the user clicks the "Save" labelled business action button on the main page
        #Verifying all field on purchase credit memo
        And the user selects the "Financial site *" labelled reference field on the main page
        And the value of the reference field is "TE Headquarter"
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the value of the reference field is "Lenovo"
        And the user selects the "reason" labelled reference field on the main page
        And the value of the reference field is "Stock value correction"
        And the user selects the "supplierCreditMemoReference" labelled text field on the main page
        And the value of the text field is "SCMemo-PINV"
        And the user selects the "totalCreditMemoAmountExclTax" labelled numeric field on the main page
        And the value of the numeric field is "325.00"
        And the user selects the "totalCreditMemoTax" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the user selects the "totalVarianceAmountExclTax" labelled tile numeric field on the main page
        And the value of the tile numeric field is "$0.00"
        And the user selects the "totalTaxVariance" labelled tile numeric field on the main page
        And the value of the tile numeric field is "$0.00"
        And the user selects the "totalVarianceAmountInclTax" labelled tile numeric field on the main page
        And the value of the tile numeric field is "$0.00"
        And the user clicks the "post" labelled business action button on the main page
        And the user clicks the "post" button of the Confirm dialog
