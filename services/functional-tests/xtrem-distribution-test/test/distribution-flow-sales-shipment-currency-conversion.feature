# This test can only be executed with sage.
# The purpose of this test is to verify the user can create shipment from Sales order with Currency conversion

@distribution
Feature:  distribution-flow-sales-shipment-currency-conversion

        Scenario: 01 - Verify the user can create shipmet from sales order
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
                Then the "Sales orders" titled page is displayed
                When the user selects the "All statuses" dropdown option in the navigation panel
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user filters the "Number" labelled column in the table field with value "SO230027"
                And the user selects the row with text "SO230027" in the "number" bound column header of the table field
                And the user clicks the "number" labelled nested field of the selected row in the table field
                And the user selects the "displayStatus" labelled label field on the main page
                Then the value of the label field is "Confirmed"
                ##Allocate stock
                And the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
                And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
                And the user selects the "Stock allocation" labelled table field on a modal
                And the user ticks the main checkbox of the selected row in the table field
                And the user clicks the "Save" labelled business action button on a modal
                ##Verify the allocation
                And the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Conversion item" in the "item" labelled column header of the table field
                Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"

                And the user clicks the "Create Shipment" labelled business action button on the main page
                And the user clicks the "Create" button of the Confirm dialog
                And a toast containing text "1 shipment(s) created" is displayed


        Scenario: 02 - Getting the number of the shipment
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
                Then the "Sales orders" titled page is displayed
                When the user selects the "All statuses" dropdown option in the navigation panel
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user filters the "Number" labelled column in the table field with value "SO230027"
                And the user selects the row with text "SO230027" in the "number" bound column header of the table field
                And the user clicks the "number" labelled nested field of the selected row in the table field
                ## Using progress tab to get the shipment number
                And the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
                And the user clicks the "Open line panel" inline action button of the selected row in the table field
                And selects the "Progress" labelled navigation anchor on the sidebar
                And the user selects the "shipmentLines" labelled table field on the sidebar
                And the user selects the row with text "5 each" in the "quantity" bound column header of the table field
                ##Storing the value of the Purchase return
                Then the user stores the value of the "shipmentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_ShipNumber]"


        Scenario:  03 - Verify that there are no changes
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
                Then the "Sales shipments" titled page is displayed
                When the user selects the "All statuses" dropdown option in the navigation panel
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row with text "[ENV_ShipNumber]" in the "number" bound column header of the table field
                And the user clicks the "number" labelled nested field of the selected row in the table field
                ##Verify
                And the user selects the "Stock site" labelled reference field on the main page
                And the value of the reference field is "TE Hampton"
                And the user selects the "Ship-to customer" labelled reference field on the main page
                Then the value of the reference field is "ATSERMO"
                When the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Conversion item" in the "Item" labelled column header of the table field
                And the value of the "Item" labelled nested text field of the selected row in the table field is "Conversion item"
                And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "5 each"

        Scenario: 04 - Post the shipment number
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
                Then the "Sales shipments" titled page is displayed
                When the user selects the "All statuses" dropdown option in the navigation panel
                When the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row with text "[ENV_ShipNumber]" in the "number" bound column header of the table field
                And the user clicks the "number" labelled nested field of the selected row in the table field
                ##Confirm the shipment
                And the user clicks the "Confirm" labelled business action button on the main page
                And the user clicks the "Confirm" button of the Confirm dialog
                And a toast containing text "Status updated" is displayed
                ##Post
                And the user waits 3 seconds
                And the user clicks the "Post stock" labelled business action button on the main page
                And the user clicks the "Confirm" button of the Confirm dialog
                And a toast containing text "The sales shipment was posted." is displayed
