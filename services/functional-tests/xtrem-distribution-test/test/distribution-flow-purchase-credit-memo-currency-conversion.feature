# the goal of this test is to verify that currency conversion is working as expected on Purchase credit memo
# when PCM is created directly from a purchase invoice with currency conversion
# We use a company with Leg=GB & a Supplier with Leg=FR
# Site= UK Limited | Supplier = BARRES | Exchange Rate : 1 EUR = 0.89673 GBP
# uncomment [18-36] &  [93-110] when XT-55842 is fixed
@distribution
Feature: distribution-flow-purchase-credit-memo-currency-conversion

    Scenario: 01 - Purchase Invoice amounts verification
        # open purchase invoice
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-PCM-CC01" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        Then the "Purchase invoice PI-PCM-CC01" titled page is displayed

        # verify amounts on panel
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "125.60"
        And the user selects the "Tax amount" labelled numeric field on the sidebar
        And the value of the numeric field is "0.00"
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "125.60"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "112.63"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "112.63"
        And the user clicks the "Apply" button of the dialog on the sidebar

        # verify exchange rate
        Then selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Transaction currency *" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "1 EUR = 0.89673 GBP"

        # verify totals tab
        Then selects the "Totals" labelled navigation anchor on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "125.60"
        And the user selects the "Tax" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the user selects the "Including tax" labelled numeric field on the main page
        And the value of the numeric field is "125.60"

    Scenario: 02 - Creating Directly the Purchase Credit Memo
        # open PCM
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        Then the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PI-PCM-CC01" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        #crediting
        When the user clicks the "Create credit memo" labelled business action button on the main page
        And the user selects the "reasonCreditNote" bound reference field on a modal

        And the user clicks the lookup button of the reference field
        And the user selects the "reasonCreditNote" bound table field on a modal
        And the user selects the row with text "Stock value correction" in the "name" labelled column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the value of the reference field is "Stock value correction"

        When the user selects the "supplierDocumentDate" labelled date field on a modal
        And the user writes "09/29/2023" in the date field
        Then the value of the date field is "09/29/2023"
        And the user selects the "totalAmountExcludingTaxCreditNote" bound numeric field on a modal
        And the user writes "125.60" in the numeric field
        And the user clicks the "Create" labelled business action button on a modal
        Then a toast with text "Purchase credit memo created." is displayed
        And the user waits for 5 seconds
        And the user refreshes the screen

        # storing number of PCM
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PCM_CC_NUM01]"

    Scenario: 03 - Purchase Credit Memo Amounts verification
        # Open PCM
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "Purchase credit memos" labelled table field on the main page
        # And the user selects the row with text "PC230006" in the "Number" labelled column header of the table field
        And the user selects the row with text "[ENV_PCM_CC_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Posting
        When the user clicks the "post" labelled business action button on the main page
        Then the user clicks the "Post" button of the Confirm dialog

        And the user waits 2 seconds
        And the user refreshes the screen

        # verify amounts on panel
        And the user selects the "Lines" labelled table field on the main page

        #And the user selects the row with text "Service" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Item *" labelled reference field on the sidebar
        And the value of the reference field is "Service"
        # Then the "Service - PUR-SERVICE" titled sidebar is displayed
        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "125.60"
        And the user selects the "Total tax" labelled numeric field on the sidebar
        And the value of the numeric field is "0.00"
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "125.60"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "112.63"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "112.63"
        And the user clicks the "Apply" button of the dialog on the sidebar

        # verify exchange rate
        Then selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Transaction currency *" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "1 EUR = 0.89673 GBP"

        # verify totals tab
        Then selects the "Totals" labelled navigation anchor on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "125.60"
        And the user selects the "Tax" labelled numeric field on the main page
        And the value of the numeric field is "0.00"
        And the user selects the "Including tax" labelled numeric field on the main page
        And the value of the numeric field is "125.60"
