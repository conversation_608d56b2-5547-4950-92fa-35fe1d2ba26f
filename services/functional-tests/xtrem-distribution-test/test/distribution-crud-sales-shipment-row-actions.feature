#This test can only be executed with sage.
#The goal of this test is to verify that the user can do a CRUD on Sales shipment navigation panel
@distribution
Feature: distribution-crud-sales-shipment-row-actions
    Scenario: Verify the user is able to set dimension from navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SH240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240001"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the "Apply to all lines" button of the Custom dialog
        Then a toast containing text "Dimensions applied." is displayed

    Scenario: Verify the user is able to print from navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SH240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240001"
        And the user clicks the "Print" dropdown action of the selected row of the table field
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast containing text "The sales shipment has been printed." is displayed

    Scenario: Verify that the user can confirm a Sales Shipment from navigation panel - 1
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SH240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240001"
        And the user clicks the "Confirm" dropdown action of the selected row of the table field
        ##Confirm
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "Status updated" is displayed

    Scenario: Verify that the user can post sales shipment from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SH240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240001"
        And the user clicks the "Post stock" dropdown action of the selected row of the table field
        And the user clicks the "Confirm" button of the Confirm dialog
        # Verify posting
        Then a toast with text "The sales shipment was posted." is displayed

    Scenario: Verify that the user can create a sales invoice from sales shipment from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SH240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240001"
        And the user clicks the "Create invoice" dropdown action of the selected row of the table field
        And the user clicks the "Create" button of the Confirm dialog
        # Verify posting
        Then a toast with text "Record created" is displayed

    Scenario: Verify that the user can create a return request from sales shipment from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SH240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240001"
        And the user clicks the "Create return request" dropdown action of the selected row of the table field
        And the user clicks the "Create" button of the Confirm dialog
        # Verify posting
        Then a toast with text "Sales return request created" is displayed

    #before we can revert a sales shipment we have to confirm one.
    Scenario: Verify that the user can confirm a Sales Shipment from navigation panel - 2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SH240002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240002"
        And the user clicks the "Confirm" dropdown action of the selected row of the table field
        ##Confirm
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "Status updated" is displayed

    Scenario: Verify that the user can revert a Sales Shipment from navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SH240002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240002"
        And the user clicks the "Revert" dropdown action of the selected row of the table field
        ##Confirm
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales shipment status has been reverted" is displayed

    Scenario: Verify that the user can delete a Sales Shipment from navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SH240003"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH240003"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        ##Confirm
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
