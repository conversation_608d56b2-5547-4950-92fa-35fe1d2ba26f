#The goal of this test is to duplicate purchase order and sales order

@distribution

Feature: distribution-flow-purchase-order-sales-order-duplication
    Scenario: 01 - Verify the user can duplicate Purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        # Select the record
        When the user selects the row with text "PO230001" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify Approved status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Approved"

        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header

        And the user selects the "Order date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Duplicate" labelled business action button on a modal


        # Verify duplication
        And a toast containing text "Record was duplicated successfully." is displayed
        Then the value of the label field is "Draft"

        # Add a line in panel
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field

        # Select Price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field

        # Apply changes
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Save changes
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        Then the value of the label field is "Draft"

    Scenario: 02 - Verify the user can duplicate Sales order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        # Select the record
        When the user selects the row with text "SO240019" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Check Confirmed status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Confirmed"

        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header


        # Verify duplication
        And a toast containing text "Record was duplicated successfully." is displayed

        # Make changes and verify the values on the record
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "LC Item 1 name" in the "Item" labelled column header of the table field
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "10.00 m3"
        And the user writes "20" in the "Quantity in sales unit" labelled nested text field of the selected row in the table field
        And the user writes "10" in the "Gross price" labelled nested text field of the selected row in the table field
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "$ 10.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "$ 200.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "$ 0.00"

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page

        # Verify update
        Then a toast containing text "Record updated" is displayed
        Then the value of the label field is "Quote"

        # Confirm the record
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed

        # Verify Confirmed status
        Then the value of the label field is "Confirmed"
