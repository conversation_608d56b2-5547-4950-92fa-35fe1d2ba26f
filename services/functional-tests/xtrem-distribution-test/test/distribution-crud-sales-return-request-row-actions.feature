# The purpose of this test is to CRUD the Sales return request main list.
@distribution
Feature: distribution-crud-sales-return-request-row-actions
  Scenario: 01 - Verify that the user can Confirm a Return request from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "ST240009"
    And the user selects the row with text "ST240009" in the "Number" labelled column header of the table field
    And the user clicks the "Confirm" dropdown action of the selected row of the table field
    And the user clicks the "Confirm" button of the Confirm dialog
    Then a toast containing text "Confirmation successful" is displayed

  Scenario: 02 - Verify that the user can delete a Return request from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "ST240002"
    And the user selects the row with text "ST240002" in the "Number" labelled column header of the table field
    And the user clicks the "Delete" dropdown action of the selected row of the table field
    And the user clicks the "Delete" button of the Confirm dialog
    Then a toast containing text "Record deleted" is displayed

  Scenario: 03 - Approve the Return request from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "ST240003"
    And the user selects the row with text "ST240003" in the "Number" labelled column header of the table field
    And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
    And the user selects the "To" labelled text field on a modal
    And the user writes "<EMAIL>" in the text field
    And the user clicks the "Send" button of the Custom dialog
    And the user clicks the "Send" button of the Confirm dialog
    And the user presses Tab
    And the user presses Tab
    And the user presses Tab
    And the user presses Enter
    Then a toast containing text "Email sent" is displayed
    # Refresh page
    When the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user clicks the option menu of the table field
    And the user clicks the "All statuses" value in the option menu of the table field
    And the user selects the row with text "ST240003" in the "Number" labelled column header of the table field
    And the user clicks the "Number" labelled nested field of the selected row in the table field
    # Main display status check
    And the user selects the "displayStatus" bound label field on the main page
    Then the value of the label field is "Pending approval"
    # Step flow check
    When the user selects the "returnRequestStepSequence" bound step-sequence field on the main page
    Then the status of the "Create" item of the step-sequence is complete
    Then the status of the "Approve" item of the step-sequence is current
    Then the status of the "Receive" item of the step-sequence is incomplete
    Then the status of the "Credit" item of the step-sequence is incomplete
    # Approve
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "ST240003"
    And the user selects the row with text "ST240003" in the "Number" labelled column header of the table field
    And the user clicks the "Approve" dropdown action of the selected row of the table field
    And the user clicks the "Approve" button of the Confirm dialog
    And a toast with text "The sales return request has been approved." is displayed
    # Refresh page
    When the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user clicks the option menu of the table field
    And the user clicks the "All statuses" value in the option menu of the table field
    And the user selects the row with text "ST240003" in the "Number" labelled column header of the table field
    And the user clicks the "Number" labelled nested field of the selected row in the table field
    # Verify status
    And the user selects the "displayStatus" bound label field on the main page
    Then the value of the label field is "Approved"
    # Step flow check
    When the user selects the "returnRequestStepSequence" bound step-sequence field on the main page
    Then the status of the "Create" item of the step-sequence is complete
    Then the status of the "Approve" item of the step-sequence is complete
    Then the status of the "Receive" item of the step-sequence is incomplete
    Then the status of the "Credit" item of the step-sequence is incomplete

  Scenario: 04 - Verify that the user can set dimensions in a sales return requests from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "ST240003"
    And the user selects the row with text "ST240003" in the "Number" labelled column header of the table field
    And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
    And the user waits 5 seconds
    And the dialog title is "Dimensions"
    And the user selects the "Project" labelled reference field on a modal
    And the user writes "General Overhead" in the reference field
    And the user selects "General Overhead" in the reference field
    And the user clicks the "Apply to all lines" button of the Confirm dialog
    Then a toast containing text "Dimensions applied." is displayed

  Scenario: 05 - Verify that the user can Close Return request using row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "ST240004"
    And the user selects the row with text "ST240004" in the "Number" labelled column header of the table field
    And the user clicks the "Close request" dropdown action of the selected row of the table field
    And the user clicks the "Close request" button of the Confirm dialog
    Then a toast with text "The sales return request is closed." is displayed

  Scenario: 06 - Verify that the user can Open Return request using row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user clicks the option menu of the table field
    And the user clicks the "All statuses" value in the option menu of the table field
    And the user filters the "Number" labelled column in the table field with value "ST240004"
    And the user selects the row with text "ST240004" in the "Number" labelled column header of the table field
    And the user clicks the "Open request" dropdown action of the selected row of the table field
    And the user clicks the "Reopen request" button of the Confirm dialog
    Then a toast with text "The sales return request is open." is displayed

  Scenario: 07 - Create a Credit memo from the Return request using row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the option menu of the table field is displayed
    And the user clicks the option menu of the table field
    And the user clicks the "All statuses" value in the option menu of the table field
    And the user filters the "Number" labelled column in the table field with value "ST240010"
    And the user selects the row with text "ST240010" in the "Number" labelled column header of the table field
    And the user clicks the "Create credit memo" dropdown action of the selected row of the table field
    And the user clicks the "Create" button of the Confirm dialog
    Then a toast with text "Sales credit memo created" is displayed

  #Reject
  Scenario: 08 - Find another Return request and Reject it
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "ST240006"
    And the user selects the row with text "ST240006" in the "Number" labelled column header of the table field
    And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
    And the user selects the "To" labelled text field on a modal
    And the user writes "<EMAIL>" in the text field
    And the user clicks the "Send" button of the Custom dialog
    And the user clicks the "Send" button of the Confirm dialog
    And the user presses Tab
    And the user presses Tab
    And the user presses Tab
    And the user presses Enter
    Then a toast containing text "Email sent" is displayed
    # Reject the SRR
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
    Then the "Sales return requests" titled page is displayed
    When the user selects the "Sales return requests" labelled table field on the main page
    And the user clicks the option menu of the table field
    And the user clicks the "All statuses" value in the option menu of the table field
    And the user filters the "Number" labelled column in the table field with value "ST240006"
    And the user selects the row with text "ST240006" in the "Number" labelled column header of the table field
    And the user clicks the "Reject" dropdown action of the selected row of the table field
    And the user clicks the "Reject" button of the Confirm dialog
    And a toast with text "The sales return request has been rejected." is displayed
