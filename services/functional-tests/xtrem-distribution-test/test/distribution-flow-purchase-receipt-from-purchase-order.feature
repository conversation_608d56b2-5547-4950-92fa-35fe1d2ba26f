#This test can only be executed with sage.
#The goal of this test is to verify that the user can create the purchase receipt from the purchase order
@distribution
Feature: distribution-flow-purchase-receipt-from-purchase-order
    Scenario: 01 - verify that the user can approve purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user filters the "Number" labelled column in the table field with value "PO230002"
        And the user selects the row with text "PO230002" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        Then a toast containing text "Approval status updated" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Approved"

    Scenario: 02 - verify that the user can create a purchase receipt from purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user filters the "Number" labelled column in the table field with value "PO230002"
        And the user selects the row with text "PO230002" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "createReceipt" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Receipts created" is displayed
        #Verify that the values on the line are correct
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Approved"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "In progress"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Receiving site" labelled nested text field of the selected row in the table field is "TE Hampton"
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "2 each"
        And the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "$ 20.00000"
        And the value of the "Total excluding tax" labelled nested numeric field of the selected row in the table field is "$ 40.00"
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "purchaseReceiptLines" bound table field on the sidebar
        And the user selects the row with text "2 each" in the "Quantity in purchase unit" labelled column header of the table field
        ##Storing the value of the PO
        Then the user stores the value of the "Receipt number" labelled nested text field of the selected row in the table field with the key "[ENV_PRNumber]"

    Scenario: 03 - Verify the Purchase order creation and correcteness
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        #########Using store

        And the user filters the "Number" labelled column in the table field with value "[ENV_PRNumber]"
        And the user selects the row with text "[ENV_PRNumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PRNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Draft"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "2 each"
        And the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "$ 20.00000"
        And the value of the "Total excluding tax" labelled nested numeric field of the selected row in the table field is "$ 40.00"
        And the value of the "Total including tax" labelled nested numeric field of the selected row in the table field is "$ 40.00"
        ##Stock allocation
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user selects the row with text "2 each" in the "quantityInStockUnit" bound column header of the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        #And the user presses Enter
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        When the user clicks the "post stock" labelled business action button on the main page
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Received"

    Scenario: 04 - Verify that the user can print a purchase receipt
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_PRNumber]"
        And the user selects the row with text "[ENV_PRNumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PRNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        Then the user clicks the Close button of the dialog on the main page
#We don't want to click the link as it re direct to a page that the robot can not detect
