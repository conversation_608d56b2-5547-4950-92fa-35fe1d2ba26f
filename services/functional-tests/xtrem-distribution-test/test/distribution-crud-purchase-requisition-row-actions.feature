# The purpose of this test is to verify row action (aprove,reject ) on the purchase requisition page
# directly form navigation bar
# Purchasing Site: Entrepot De Saint Denis
# Items: Stock Item - Purchase_Stock_Item_001 - Default supplier BARRES - Supplier price
@distribution
@Stack_Overflow
Feature: distribution-crud-purchase-requisition-row-actions

    Scenario: 01 - Verify the user is able to create a Purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Purchasing site
        And the user selects the "Purchasing site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de Saint Denis" in the reference field
        # Number
        And the user selects the "Number" labelled text field on the main page
        And the user writes "Pur_Req_Test_Row_Actions" in the text field
        And the user stores the value of the text field with the key "[ENV_PRQ_Number]"
        # Requester
        And the user selects the "Requester" labelled reference field on the main page
        And the user writes "<PERSON>a, <PERSON>min" in the reference field
        And the user selects "<PERSON>a, <PERSON>min" in the reference field

    Scenario: 02 - Purchase requisition creation - Adding a line using the sidebar
        # Add a line using sidebar
        When the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        Then the "Add new line" titled sidebar is displayed
        # Item name
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Purchase_Stock_Item_001" in the reference field
        And the user selects "Purchase_Stock_Item_001" in the reference field
        # Supplier
        And the user selects the "supplierLink" bound link field on the sidebar
        And the user clicks in the link field
        # Search for Supplier and Click on the row
        And the user selects the "Supplier" labelled table field on a modal
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        # Confirm this is the default supplier
        Then the value of the "Type" labelled nested text field of the selected row in the table field is "Default"
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        # Confirm it has been selected
        And the user selects the "selectedSupplier" bound reference field on a modal
        Then the value of the reference field is "BARRES"
        # Confirm dialog
        And the user clicks the "OK" button of the Custom dialog on the main page
        # Transaction currency
        And the user selects the "Transaction currency" labelled reference field on the sidebar
        Then the value of the reference field is "Euro"
        # Purchase unit - Defaulted value
        And the user selects the "Purchase unit" labelled reference field on the sidebar
        Then the value of the reference field is "Each"
        # Quantity in purchase unit
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user presses Enter
        # And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Apply" button of the dialog on the sidebar

    Scenario: 03 - Purchase requisition creation - Saving the document - 1
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 04 - Verify that the user can submit a purchase requisition for approval
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        # Verify sending
        Then a toast containing text "Email sent" is displayed

    Scenario: 05 - Verify that the user can duplicate a purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Duplicate" inline action button of the selected row in the table field
        And the user selects the "Requisition date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Duplicate" labelled business action button on a modal
        And a toast with text "Record was duplicated successfully." is displayed
    #  And the user clicks the "Save" labelled business action button on the main page
    # Verify duplication
    #Then a toast with text "Record created" is displayed

    Scenario: 06 - Verify that the user can set dimensions in a purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the "Apply to all lines" button of the Custom dialog

    Scenario: 07 - Verify that the user can approve a purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Approve" dropdown action of the selected row of the table field
        And the dialog title is "Confirm approval"
        And the user clicks the "Accept" button of the dialog on the main page
        # Verify Approval
        Then a toast with text "Approval status updated" is displayed


    Scenario: 08 - Verify that the user can reject a purchase requisition
        # First we need to submit for approval/rejection the record to enable the reject action
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PQ240002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PQ240002"
        And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        # And the user clicks the "Send" button of the dialog on the main page
        And the user clicks the "Send" button of the Custom dialog
        # Verify email sending
        Then a toast containing text "Email sent" is displayed
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PQ240002"
        Then the user selects the row 1 of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "PQ240002"
        And the user clicks the "Reject" dropdown action of the selected row of the table field
        And the dialog title is "Confirm rejection"
        And the user clicks the "Reject" button of the dialog on the main page
        # Verify rejection
        Then a toast with text "Approval status updated" is displayed

    Scenario: 09 - Verify that the user can create a purchase  requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Purchasing site
        And the user selects the "Purchasing site" labelled reference field on the main page
        And the user writes "Site de Chavanod" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        # Number
        And the user selects the "Number" labelled text field on the main page
        And the user writes "Purchase_Req_Confirm" in the text field
        And the user stores the value of the text field with the key "[ENV_PRQ_Number]"
        # Requester
        And the user selects the "Requester" labelled reference field on the main page
        And the user writes "Test, Unit" in the reference field
        And the user selects "Test, Unit" in the reference field

    Scenario: 10 - Purchase requisition creation - Adding a line using the sidebar
        # Add a line using sidebar
        When the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        Then the "Add new line" titled sidebar is displayed
        # Item name
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Item 1 for stock value change - FIFO" in the reference field
        And the user selects "Item 1 for stock value change - FIFO" in the reference field
        # Quantity in purchase unit
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user presses Enter
        # And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Apply" button of the dialog on the sidebar

    Scenario: 11 - Purchase requisition creation - Saving the document - 2
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 12 - Verify that the user can confirm a purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Confirm" dropdown action of the selected row of the table field
        And the dialog title is "Confirm purchase requisition"
        And the user clicks the "Confirm" button of the dialog on the main page
        # Verify deletion
        Then a toast with text "Status updated." is displayed

    Scenario: 13 - Verify the user can create a purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Purchasing site
        And the user selects the "Purchasing site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de Saint Denis" in the reference field
        # Number
        And the user selects the "Number" labelled text field on the main page
        And the user writes "Purchase_Req_Supplier" in the text field
        And the user stores the value of the text field with the key "[ENV_PRQ_Number]"
        # Requester
        And the user selects the "Requester" labelled reference field on the main page
        And the user writes "Test, Unit" in the reference field
        And the user selects "Test, Unit" in the reference field
        # Add a line using sidebar
        When the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        Then the "Add new line" titled sidebar is displayed
        # Item name
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Acrylates C10-30" in the reference field
        And the user selects "Acrylates C10-30" in the reference field
        # Quantity in purchase unit
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user presses Enter
        # And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Apply" button of the dialog on the sidebar

    Scenario: 14 - Purchase requisition creation - Saving the document - 3
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 15 - Verify that the user can apply default supplier
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Apply default supplier" dropdown action of the selected row of the table field
        # Verify Default supplier applied
        Then a toast with text "Default supplier applied" is displayed

    Scenario: 16 - Verify that the user can delete a purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_PRQ_Number]"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        # Verify deletion
        Then a toast with text "Record deleted" is displayed

    Scenario: 17 - Verify that the user can close a purchase requisition
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PQ230002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PQ230002"
        And the user clicks the "Close requisition" dropdown action of the selected row of the table field
        And the dialog title is "Confirm status change"
        And the user clicks the "Close requisition" button of the dialog on the main page
        # Verify closing
        Then a toast with text "Requisition status updated" is displayed
