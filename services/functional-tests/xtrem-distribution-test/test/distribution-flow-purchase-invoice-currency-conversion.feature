# the goal of this test is to verify that currency conversion is working as expected on Purchase invoice
# And that variance acceptance and posting has no impact on currency conversion of Purchase invoice
# We use a company with Leg=GB & a Supplier with Leg=FR
# Site= Swindon | Supplier = BARRES | Exchange Rate : 1 EUR = 0.89673 GBP

@distribution
Feature: distribution-flow-purchase-invoice-currency-conversion

    Scenario: Purchase invoice creation
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in fields on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        And the user selects "UK LIMITED" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field
        And the user selects the "Supplier invoice reference" labelled text field on the main page
        And the user writes "C.C test" in the text field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "240" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "48" in the numeric field
        #Adding adding line in a panel
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "Service" in the reference field
        And the user selects "Service" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar

        # Add tax detail
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user filters the "name" labelled column in the table field with value "UK Purchase Services Standard Rate"
        And the user selects the row with text "UK Purchase Services Standard Rate" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal

        # Enter amount details
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "240" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "48" in the numeric field

        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Save the PO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PI_CC_NUM01]"


    Scenario: Purchase invoice variance acceptance

        # open PO created
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_PI_CC_NUM01]"
        And the user selects the row with text "[ENV_PI_CC_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Accept Variance
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        #Verify Variance acceptance
        Then a toast containing text "Variance status updated" is displayed

    Scenario: Purchase invoice Posting
        # open PO created
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PI_CC_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # PI posting
        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted." is displayed


    Scenario: Verify Currency conversion data after Posting
        # open PO created
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_PI_CC_NUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # verify amounts on grid
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service" in the "Item" labelled column header of the table field
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 12.00000"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 240.00"
        And the value of the "Tax amount" labelled nested text field of the selected row in the table field is "€ 48.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 288.00"
        # verify amounts on panel
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "240.00"
        And the user selects the "Tax amount" labelled numeric field on the sidebar
        And the value of the numeric field is "48.00"
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "288.00"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "215.22"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "258.26"
        And the user clicks the "Apply" button of the dialog on the sidebar

        # verify exchange rate
        Then selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Transaction currency *" labelled reference field on the main page
        And the value of the reference field is "Euro"
        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "1 EUR = 0.89673 GBP"

        # verify totals tab
        Then selects the "Totals" labelled navigation anchor on the main page
        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "240.00"
        And the user selects the "Tax" labelled numeric field on the main page
        And the value of the numeric field is "48.00"
        And the user selects the "Including tax" labelled numeric field on the main page
        And the value of the numeric field is "288.00"
