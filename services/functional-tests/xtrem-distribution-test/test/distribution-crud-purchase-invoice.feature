#The goal of this test is to verify that the user can perform purchase invoice creation, update and deletion (CRUD)

@distribution
Feature: distribution-crud-purchase-invoice

    Scenario: Purchase invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in fields on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        And the user selects "UK LIMITED" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI Servise0001" in the text field
        And the user selects the "Supplier invoice reference" labelled text field on the main page
        And the user writes "test" in the text field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "350" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "80" in the numeric field
        #Adding item details or adding line in a panel
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "Service" in the reference field
        And the user selects "Service" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user waits 5 seconds
        And the user clicks the "Accept all variances" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        #Verify purchase invoice creation
        Then a toast containing text "Variance status updated." is displayed

    Scenario: Purchase invoice Update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "PI Servise0001" in the "Number" labelled column header of the table field
        #Updating purchase invoice fields
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Supplier invoice reference" labelled text field on the main page
        And the user writes "PI00001" in the text field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "240" in the numeric field
        And the user selects the " Total supplier tax " labelled numeric field on the main page
        And the user writes "48" in the numeric field
        #Save changes and verify purchase invoice update
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Purchase invoice Delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        #Searching for created purchase invoice
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "PI Servise0001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Delete creation
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
