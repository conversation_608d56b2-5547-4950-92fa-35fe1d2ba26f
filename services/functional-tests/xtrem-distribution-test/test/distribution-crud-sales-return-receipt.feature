#This test can only be executed with sage.
#The goal of this test is to verify that the user can do a CRUD on Sales return receipt
@distribution
Feature: distribution-crud-sales-return-receipt

    Scenario: Verify that the user can create a Sales return receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        ##Create
        When the user clicks the "create" labelled business action button on the main page
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "Ship-to customer *" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SalesRR_001" in the text field
        #Adding line by using Add liens from orders button
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from return requests" labelled business action button of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "ST240007" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        ##Update sales return receipt
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" labelled table field on a modal
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row with text "3 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user clicks the "ok" labelled business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify that the user can Delete a Sales Shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SalesRR_001" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Verify status
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        #Delete
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
