#This test can only be executed with sage.
#The goal of this test is to verify that the user can do CRUD on Purchase receipt main list actions
@distribution
Feature: distribution-crud-purchase-receipt-row-actions
  Scenario: 01 - Verify that the user can print a pending Purchase receipt from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    When the user selects the "Purchase receipts" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "PR_ROW_01"
    And the user selects the row with text "PR_ROW_01" in the "Number" labelled column header of the table field
    And the user clicks the "Print" dropdown action of the selected row of the table field
    And the user waits 10 seconds
    And the dialog title is "Print document"
  #We don't want to click the link as it re direct to a page that the bot can not detect

  Sc<PERSON>rio: 02 - Verify that the user can set dimensions in a purchase receipt from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    And the user filters the "Number" labelled column in the table field with value "PR_ROW_01"
    And the user selects the row with text "PR_ROW_01" in the "Number" labelled column header of the table field
    And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
    And the user waits 5 seconds
    And the dialog title is "Dimensions"
    And the user selects the "Project" labelled reference field on a modal
    And the user writes "General Overhead" in the reference field
    And the user selects "General Overhead" in the reference field
    And the user clicks the Close button of the dialog on the main page

  Scenario: 03 - Verify that the user can delete a Purchase receipt from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    When the user selects the "Purchase receipts" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "PR_ROW_01"
    And the user selects the row with text "PR_ROW_01" in the "Number" labelled column header of the table field
    And the user clicks the "Delete" dropdown action of the selected row of the table field
    And the user clicks the "Delete" button of the Confirm dialog
    Then a toast containing text "Record deleted" is displayed


  Scenario: 04 - Verify that the user can post a Purchase receipt from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    And the user selects the "$navigationPanel" bound table field on the navigation panel
    And the user filters the "Number" labelled column in the table field with value "PR_ROW_02"
    And the user selects the row with text "PR_ROW_02" in the "Number" labelled column header of the table field
    And the user clicks the "post stock" dropdown action of the selected row of the table field
    And the user waits for 5 seconds
    And the user clicks the "number" labelled nested field of the selected row in the table field
    And the user selects the "displayStatus" labelled label field on the main page
    Then the value of the label field is "Received"

  Scenario: 05 - verify that the user can create a purchase return on purchase receipt from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    When the user selects the "Purchase receipts" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "PR_ROW_02"
    And the user selects the row with text "PR_ROW_02" in the "Number" labelled column header of the table field
    And the user clicks the "createReturn" dropdown action of the selected row of the table field
    Then a toast containing text "Purchase returns created:" is displayed
