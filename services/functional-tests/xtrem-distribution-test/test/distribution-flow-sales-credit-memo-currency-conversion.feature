# The goal of this test is to verify that currency conversion is working as expected on Sales credit memo
# And that posting has no impact on currency conversion of Sales credit memo
# We use a site with Leg=FR & a Customer with Leg=GB
# Site= Entrepot de Saint Denis| Customer = Distributor | Exchange Rate : 1 EUR = 0.89673 GBP

@distribution
Feature: distribution-flow-sales-credit-memo-currency-conversion

    Scenario: 01 - Open Sales Invoice page

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        # Find the specific Sales invoice
        When the user selects the "Sales invoices" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        And the user selects the row with text "SIPED230006" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Verify document is correct
        Then the "Sales invoice SIPED230006" titled page is displayed

        # Main display status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"

    Scenario: 02 - Verify and Store the values from the Sales Invoice document

        # Site
        When the user selects the "Site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"

        # Customer
        When the user selects the "Bill-to customer" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Customer]"

        # Move to information tab and store those values
        When selects the "Information" labelled navigation anchor on the main page

        # Exchange rate
        And the user selects the "Exchange rate" labelled text field on the main page
        Then the value of the text field is "1 GBP = 1.1151628695 EUR"
        And the user stores the value of the text field with the key "[ENV_Exchange_Rate]"

        # Transaction currency
        And the user selects the "Transaction currency" labelled reference field on the main page
        Then the value of the reference field is "Pound Sterling"
        And the user stores the value of the reference field with the key "[ENV_Transaction_Currency]"

        # Move to totals tab and store these values
        When selects the "Totals" labelled navigation anchor on the main page

        # Customer amount - Excluding tax
        And the user selects the "Excluding tax" labelled numeric field on the main page
        Then the value of the numeric field is "1,200.00"
        And the user stores the value of the numeric field with the key "[ENV_CustomerTotal_Excl_Tax]"
        # Customer amount - Including tax
        And the user selects the "Including tax" labelled numeric field on the main page
        Then the value of the numeric field is "1,440.00"
        And the user stores the value of the numeric field with the key "[ENV_CustomerTotal_Incl_Tax]"

        # Company currency amount - Excluding tax

        And the user selects the "totalAmountExcludingTaxInCompanyCurrency" bound numeric field on the main page
        Then the value of the numeric field is "1,338.20"
        And the user stores the value of the numeric field with the key "[ENV_CompanyTotal_Excl_Tax]"

        # Company currency amount - Including tax

        And the user selects the "totalAmountIncludingTaxInCompanyCurrency" bound numeric field on the main page
        Then the value of the numeric field is "1,605.84"
        And the user stores the value of the numeric field with the key "[ENV_CompanyTotal_Incl_Tax]"


        # Move to lines table and store the values of line item Price tab
        When selects the "Lines" labelled navigation anchor on the main page
        # Select the table and find row
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field
        And the user stores the value of the "Item" labelled nested reference field of the selected row in the table field with the key "[ENV_LineItem_Name]"

        # Verify and store inline values
        # Gross price
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "£ 25.00"
        And the user stores the value of the "Gross price" labelled nested reference field of the selected row in the table field with the key "[ENV_LineItem_Inline_GrossPrice]"
        # Total excl tax
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "£ 300.00"
        And the user stores the value of the "Total excluding tax" labelled nested reference field of the selected row in the table field with the key "[ENV_LineItem_Inline_Total_Excl_Tax]"
        # Total tax
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "£ 60.00"
        And the user stores the value of the "Total tax" labelled nested reference field of the selected row in the table field with the key "[ENV_LineItem_Total_Inline_Tax]"
        # Total incl tax
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "£ 360.00"
        And the user stores the value of the "Total including tax" labelled nested reference field of the selected row in the table field with the key "[ENV_LineItem_Inline_Total_Incl_Tax]"


        # Side panel verification
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        # Open the Price tab
        When selects the "Price" labelled navigation anchor on the sidebar

        # Verify and Store the values

        # Line item Price - Total excluding tax
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "300.00"
        And the user stores the value of the numeric field with the key "[ENV_LineItem_SideBar_Total_Excl_Tax]"

        # Line item Price - Tax amount
        And the user selects the "Total tax" labelled numeric field on the sidebar
        And the value of the numeric field is "60.00"
        And the user stores the value of the numeric field with the key "[ENV_LineItem_SideBar_Total_Tax]"

        # Line item Price - Total including tax
        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "360.00"
        And the user stores the value of the numeric field with the key "[ENV_LineItem_SideBar_Incl_Tax]"

        # Line item Price - Total excluding tax company currency
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "334.55"
        And the user stores the value of the numeric field with the key "[ENV_LineItem_SideBar_CompanyTotal_Excl_Tax]"

        # Line item Price - Total including tax company currency
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "401.46"
        And the user stores the value of the numeric field with the key "[ENV_LineItem_SideBar_CompanyTotal_Incl_Tax]"


        And the user clicks the "Cancel" button of the dialog on the sidebar



    Scenario: 03 - Create Credit memo from the Sales Invoice
        # Click on the request return business action button
        When the "Create credit memo" labelled business action button on the main page is visible
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog

        # Browser should redirect to newly generated sales credit memo page
        And a toast with text "Record created" is displayed

    # Browser should redirect to Sales Credit Memo page here

    Scenario: 04 - Verify the Sales credit memo details

        # Site
        When the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"

        # Customer
        When the user selects the "Bill-to customer" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Customer]"

        # Store the SCM number
        When the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SCM_Number]"


        # Line item verify
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "[ENV_LineItem_Name]" in the "Item" labelled column header of the table field

        # Verify inline line item
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "[ENV_LineItem_Inline_GrossPrice]"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "[ENV_LineItem_Inline_Total_Excl_Tax]"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "[ENV_LineItem_Total_Inline_Tax]"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "[ENV_LineItem_Inline_Total_Incl_Tax]"

        # verify amounts on sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "[ENV_LineItem_Name]" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "[ENV_LineItem_SideBar_Total_Excl_Tax]"

        And the user selects the "Total tax" labelled numeric field on the sidebar
        And the value of the numeric field is "[ENV_LineItem_SideBar_Total_Tax]"

        And the user selects the "Total including tax" labelled numeric field on the sidebar
        And the value of the numeric field is "[ENV_LineItem_SideBar_Incl_Tax]"

        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "[ENV_LineItem_SideBar_CompanyTotal_Excl_Tax]"

        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "[ENV_LineItem_SideBar_CompanyTotal_Incl_Tax]"

        And the user clicks the "Apply" button of the dialog on the sidebar

        # verify information tab/exchange rate
        Then selects the "Information" labelled navigation anchor on the main page

        And the user selects the "Transaction currency" labelled reference field on the main page
        And the value of the reference field is "[ENV_Transaction_Currency]"

        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "[ENV_Exchange_Rate]"

        # verify totals tab
        Then selects the "Totals" labelled navigation anchor on the main page

        And the user selects the "Excluding tax" labelled numeric field on the main page
        And the value of the numeric field is "[ENV_CustomerTotal_Excl_Tax]"

        And the user selects the "Including Tax" labelled numeric field on the main page
        And the value of the numeric field is "[ENV_CustomerTotal_Incl_Tax]"

        # Verify company currency totals
        And the user selects the "totalAmountExcludingTaxInCompanyCurrency" bound numeric field on the main page
        And the value of the numeric field is "[ENV_CompanyTotal_Excl_Tax]"

        And the user selects the "totalAmountIncludingTaxInCompanyCurrency" bound numeric field on the main page
        Then the value of the numeric field is "[ENV_CompanyTotal_Incl_Tax]"


    Scenario: 05 - Post the Sales credit memo and check no error is displayed

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        # Find the specific Sales Credit memo
        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        And the user selects the row with text "[ENV_SCM_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field


        # Click on the post business action button
        When the "Post" labelled business action button on the main page is visible
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog

        # Browser should redirect to newly generated sales credit memo page
        And a toast with text "The sales credit memo was posted." is displayed

        # Wait so the Number field updates
        And the user refreshes the screen
        # Store the new SCM number
        When the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SCM_Number1]"

    # refresh the page and confirm status
    Scenario: 06 - Confirm the SCM was posted
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "Sales credit memos" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_SCM_Number1]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Verify document is correct
        Then the "Sales credit memo [ENV_SCM_Number1]" titled page is displayed
        # Main display status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"
