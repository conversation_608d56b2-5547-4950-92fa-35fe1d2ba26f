# The goals of this test are;
# 1. to verify that a new sales order inherits dimensions from the site, customer and according
#    to the company default dimension rules,
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly

@distribution
Feature: distribution-flow-sales-order-default-dimensions

    Scenario: 01 - Create sales order and verify sales order line dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Create sales order
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Site ZA2" in the reference field
        And the user selects "Site ZA2" in the reference field
        And the user selects the "Sold-to customer" labelled reference field on the main page
        And the user writes "Kliënt ZA1" in the reference field
        And the user selects "Kliënt ZA1" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "78.99" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SODD_NUM04]"
        # Verify sales order line dimensions after save
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update sales order dimensions (Apply to new lines only) and verify sales order line dimensions
        # Update dimension
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "date" bound nested field of the selected row in the table field
        And the user searches for "[ENV_SODD_NUM04]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add new sales order line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "30.70" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify new sales order line dimensions (Pressure transmitter)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify initial sales order line dimensions (Pressure transducer)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update sales order dimensions (Apply to all lines) and verify sales order line dimensions
        # Update dimension
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "date" bound nested field of the selected row in the table field
        And the user searches for "[ENV_SODD_NUM04]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "IT" in the reference field
        And the user selects "IT" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Residential" in the reference field
        And the user selects "Residential" in the reference field
        Then the user clicks the "Apply to all lines" labelled business action button on a modal
        # Add new sales order line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "16.45" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify new sales order line dimensions (Pressure sensor)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify second sales order line dimensions (Pressure transmitter)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify initial sales order line dimensions (Pressure transducer)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Delete sales order
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
