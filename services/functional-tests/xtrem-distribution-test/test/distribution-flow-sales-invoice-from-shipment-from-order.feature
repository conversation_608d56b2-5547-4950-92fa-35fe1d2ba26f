#This test can only be executed with sage.
#The goal of this test is to verify that the user can create sales invoice from shipment
@distribution
Feature: distribution-flow-sales-invoice-from-shipment-from-order

    Scenario: 01 - Verify the user is able to duplicate the sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO240013"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO240013"
        And the user clicks the "Duplicate" labelled button of the table field
        And a toast with text "Record was duplicated successfully." is displayed
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_salesOrder]"
        # Find the line item
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user writes "1" in the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user stores the value of the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field with the key "[ENV_Updated_Price]"
        And the user presses Control+Enter

    Scenario: 02 - Add the customer order reference in the created sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_salesOrder]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # Enter customer order reference
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user selects the "Customer order reference" labelled text field on the main page
        And the user writes "test cutomer number" in the text field
        And the user presses Tab
        #Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record updated" is displayed
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        ##Create invoice
        When the user clicks the "Create shipment" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_salesshipment]"
        # Find the line item
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user writes "1" in the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user stores the value of the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field with the key "[ENV_Updated_Price]"
        And the user presses Control+Enter

    Scenario: 03 - Getting the number of the shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_salesOrder]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ## Using progress tab to get the shipment number
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "shipmentLines" labelled table field on the sidebar
        And the user selects the row with text "1 each" in the "quantity" bound column header of the table field
        ##Storing the value of the Purchase return
        Then the user stores the value of the "shipmentNumber" labelled nested text field of the selected row in the table field with the key "[ENV_ShipNumber]"

    Scenario: 04 - check the customer order reference in the sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_ShipNumber]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # verify the customer order reference on shipment
        When the user selects the "Lines" labelled table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When searches for "Customer order" in the lookup dialog on the sidebar
        Then the table column configuration with name "Customer order reference" on the sidebar is unticked
        When the user ticks the table column configuration with "Customer order reference" name on the sidebar
        And the table column configuration with name "Customer order reference" on the sidebar is ticked
        When the user clicks the Close button of the dialog on the sidebar
        When the user selects the "Lines" labelled table field on the main page

        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Customer order reference" labelled nested text field of the selected row in the table field is "test cutomer number"
        ##Stock allocation
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "save" labelled business action button on a modal
        # confirm shipment
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "Status updated." is displayed
        And the user dismisses all the toasts
        # post stock
        When the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales shipment was posted." is displayed

        And the user refreshes the screen
        ##Create invoice
        When the user clicks the "Create invoice" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Record created" is displayed

    Scenario: 05 - Getting the number of the invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_ShipNumber]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        ## Using progress tab to get the shipment number
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "toInvoiceLines" bound table field on the sidebar
        And the user selects the row with text "1 each" in the "quantity" bound column header of the table field
        ##Storing the value of the Purchase return
        Then the user stores the value of the "invoiceNumber" labelled nested text field of the selected row in the table field with the key "[ENV_InvoiceNumber]"

    Scenario: 06 - check the customer order reference in the sales invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_InvoiceNumber]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # verify the customer order reference on shipment
        When the user selects the "Lines" labelled table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When searches for "Customer order" in the lookup dialog on the sidebar
        Then the table column configuration with name "Customer order reference" on the sidebar is unticked
        When the user ticks the table column configuration with "Customer order reference" name on the sidebar
        And the table column configuration with name "Customer order reference" on the sidebar is ticked
        When the user clicks the Close button of the dialog on the sidebar
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Customer order reference" labelled nested text field of the selected row in the table field is "test cutomer number"
