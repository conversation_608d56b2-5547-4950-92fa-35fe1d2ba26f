#This test can only be executed with sage.
#The goal of this test is to verify that the user can do a CRUD on Sales return receipt main list
@distribution
Feature: distribution-crud-sales-return-receipt-row-actions
  Scenario: Verify that the user can set dimensions in a sales return receipt from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
    Then the "Sales return receipts" titled page is displayed
    When the user selects the "Sales return receipts" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "SRR_ROW_001"
    And the user selects the row with text "SRR_ROW_001" in the "Number" labelled column header of the table field
    And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
    And the user waits 5 seconds
    And the dialog title is "Dimensions"
    And the user selects the "Project" labelled reference field on a modal
    And the user writes "General Overhead" in the reference field
    And the user selects "General Overhead" in the reference field
    And the user clicks the "Apply to all lines" button of the Confirm dialog
    Then a toast containing text "Dimensions applied." is displayed

  Scenario: Verify that the user can Delete a Sales return receipts
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
    Then the "Sales return receipts" titled page is displayed
    When the user selects the "Sales return receipts" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "SRR_ROW_001"
    And the user selects the row with text "SRR_ROW_001" in the "Number" labelled column header of the table field
    And the user clicks the "Delete" dropdown action of the selected row of the table field
    And the user clicks the "Delete" button of the Confirm dialog
    Then a toast containing text "Record deleted" is displayed

  Scenario: Verify that the user can post a Sales return receipt from row action
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
    Then the "Sales return receipts" titled page is displayed
    And the user selects the "All statuses" dropdown option in the navigation panel
    When the user selects the "Sales return receipts" labelled table field on the main page
    And the user filters the "Number" labelled column in the table field with value "SRR_ROW_002"
    And the user selects the row with text "SRR_ROW_002" in the "Number" labelled column header of the table field
    And the user clicks the "post stock" dropdown action of the selected row of the table field
    Then a toast containing text "Receipt posted" is displayed
    And the user clicks the "number" labelled nested field of the selected row in the table field
    And the user selects the "displayStatus" labelled label field on the main page
    Then the value of the label field is "Closed"
