#This test can only be executed with sage.
#The goal of this test is to verify that the user can create sales invoice from shipment
@distribution
Feature: distribution-flow-sales-invoice-from-shipment
    Scenario: 01 - Verify that the user can open the shipment and allocate the stock
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SH230026" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Allocate stock
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "save" labelled business action button on a modal
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to process"

    Scenario: 02 - Verify that the user can open the shipment and post the shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SH230026" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Confirm
        When the user clicks the "confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "Status updated" is displayed
        And the user dismisses all the toasts
        ##post
        When the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales shipment was posted" is displayed

    Scenario: 03 - Verify that the user can create sales invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user filters the "number" bound column in the table field with value "SH230026"
        And the user selects the row with text "SH230026" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Create invoice
        When the user clicks the "Create invoice" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Record created" is displayed
        ##Verify invoice created
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "Site" labelled reference field on the main page
        And the value of the reference field is "TE Hampton"
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the value of the reference field is "MK Manufacturing"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Origin" labelled nested text field of the selected row in the table field is "Shipment"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "7 each"
        Then the value of the "Gross price" labelled nested text field of the selected row in the table field is "$ 70.00"
        ##Post invoice
        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The sales invoice was posted" is displayed
