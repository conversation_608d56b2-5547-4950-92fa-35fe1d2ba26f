# The goal of this test it to verify the creation, update and deletion of tax determination rule

@distribution
Feature: distribution-crud-tax-determination-rule

    Scenario: Verify the user is able to create a tax determination rule
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        Then the "Tax determination rules" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Tax determination rule" titled page is displayed
        #Creating a tax determination rule
        When the user selects the "Tax solution" labelled reference field on the main page
        And the user writes "USA tax solution" in the reference field
        And the user selects "USA tax solution" in the reference field
        When the user selects the "Tax category" labelled reference field on the main page
        And the user writes "County tax" in the reference field
        And the user selects "County tax" in the reference field

    Scenario Outline: <Flow> - Populating lines
        And the user selects the "Lines" labelled table field on the main page
        And the user clicks the "Add" labelled button of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Flow" labelled nested field of the selected row in the table field
        And the user selects "<Flow>" in the "Flow" labelled nested field of the selected row in the table field
        And the user writes "<OriginCountry>" in the "Origin country" labelled nested reference field of the selected row in the table field
        And the user selects "<OriginCountry>" in the "Origin country" labelled nested field of the selected row in the table field
        And the user writes "<DestinationCountry>" in the "Destination country" labelled nested reference field of the selected row in the table field
        And the user selects "<DestinationCountry>" in the "Destination country" labelled nested field of the selected row in the table field
        And the user writes "<TransactionType>" in the "Transaction type" labelled nested reference field of the selected row in the table field
        And the user selects "<TransactionType>" in the "Transaction type" labelled nested field of the selected row in the table field
        And the user writes "<ItemTaxGroup>" in the "Item tax group" labelled nested reference field of the selected row in the table field
        And the user selects "<ItemTaxGroup>" in the "Item tax group" labelled nested field of the selected row in the table field

        Examples:
            | Flow       | OriginCountry            | DestinationCountry       | TransactionType | ItemTaxGroup                |
            | Purchasing | United States of America | United States of America | B2B             | Goods, standard VAT         |
            | Sales      | United States of America | United States of America | B2B and B2C     | Goods, reduced VAT          |
            | Purchasing | United States of America | United States of America | B2B and B2C     | Goods, zero VAT             |
            | Sales      | United States of America | United States of America | B2B             | Capital Goods, standard VAT |

    Scenario Outline: Saving the new tax determination rule
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify the user is able to update a tax determination rule
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "USA tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page

        And the user selects the row with text "Goods, standard VAT" in the "Item tax group" labelled column header of the table field
        And the user writes "Services, standard VAT" in the "Item tax group" labelled nested reference field of the selected row in the table field
        And the user selects "Services, standard VAT" in the "Item tax group" labelled nested field of the selected row in the table field
        Then the value of the "Item tax group" labelled nested text field of the selected row in the table field is "Services, standard VAT"
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed



    Scenario: Verify the user is able to delete a newly created tax determination rule
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxDetermination"
        When the "Tax determination rules" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "USA tax solution" in the "Tax solution" labelled column header of the table field
        When the user clicks the "Tax solution" labelled nested field of the selected row in the table field
        Then the "Tax determination rule County tax USA tax solution" titled page is displayed
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
