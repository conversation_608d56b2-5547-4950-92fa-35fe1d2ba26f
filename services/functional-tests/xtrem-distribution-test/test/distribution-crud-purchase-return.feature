#This test can only be executed with sage.
#The goal of this test is to verify that the user can do Purchase crud
##Line 24 can be updated when bug XT-55002 is fixed
@distribution
Feature: distribution-crud-purchase-return

    Scenario: Verify that the user can create a purchase return
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user clicks the "create" labelled business action button on the main page
        #create
        And the user selects the "Return site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "<PERSON>yre<PERSON>" in the reference field
        And the user selects "<PERSON>yreco" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PReturn_001" in the text field
        #Adding a new item in the receipt line by using the select from purchase order option
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from receipts" labelled business action button of the table field

        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "PR230005" in the "Receipt number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        ##Adding reason
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Return reason" labelled reference field on the sidebar
        And the user writes "Decrease" in the reference field
        And the user selects "Decrease quantity" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Return reason" labelled reference field on the sidebar
        And the user writes "Decrease" in the reference field
        And the user selects "Decrease quantity" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: Verify that the user can update a purchase return
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PReturn_001" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        ##Updating reason
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Return reason" labelled reference field on the sidebar
        And the user writes "Stock" in the reference field
        And the user selects "Stock value correction" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify that the user can delete pending Purchase Return
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PReturn_001" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    #Test for actions directly from navigation panel

    Scenario: Verify that the user can submit a purchase return for approval from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PT240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PT240001"
        And the user clicks the "Submit for approval" dropdown action of the selected row of the table field
        #Then a toast containing text "Record updated" is displayed
        Then a toast containing text "Submitted for approval" is displayed


    Scenario: Verify that the user can set dimensions in a purchase return from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PT240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PT240001"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the Close button of the dialog on the main page


    Scenario: Verify that the user can approve a purchase return from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PT240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PT240001"
        And the user clicks the "Approve" dropdown action of the selected row of the table field
        And the dialog title is "Confirm approval"
        And the user clicks the "Accept" button of the dialog on the main page
        # Verify Approval
        #Then a toast with text "Record updated." is displayed
        Then a toast with text "Approval status updated." is displayed

    Scenario: Verify that the user can close a purchase return from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PT240001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PT240001"
        And the user clicks the "Close return" dropdown action of the selected row of the table field
        And the dialog title is "Confirm status change"
        And the user clicks the "Close return" button of the dialog on the main page
        # Verify Approval
        #Then a toast with text "Record updated." is displayed
        Then a toast with text "Return status updated." is displayed

    Scenario: Verify the user is able to reject a Purchase return from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PT240002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PT240002"
        And the user clicks the "Reject" dropdown action of the selected row of the table field
        And the dialog title is "Confirm rejection"
        And the user clicks the "Reject" button of the dialog on the main page
        # Verify rejection
        Then a toast with text "Approval status updated." is displayed

    Scenario: Verify the user is able to delete a Purchase return from navigation panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PT240003"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PT240003"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Confirm dialog
        # Verify deletion
        #Then a toast with text "Record has been deleted succesfully" is displayed
        Then a toast with text "Record deleted" is displayed
