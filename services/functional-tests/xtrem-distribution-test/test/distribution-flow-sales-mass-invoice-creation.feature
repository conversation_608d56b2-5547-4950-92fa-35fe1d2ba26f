# The purpose of this test is to confirm that when there are multiple confirmed sales shipment ready to be invoiced then the
# mass invoice creation is able to create invoices for them based on supplied criteria

@distribution

Feature: distribution-flow-sales-mass-invoice-creation

    Scenario: Create Mass invoices
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipmentInvoicingMassProcess"
        Then the "Mass invoice creation" titled page is displayed

        # Set Company and site criteria
        When the user selects the "Company" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field

        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de  Saint  Denis" in the reference field

        When the user selects the "Invoice date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"

        When the user clicks the "Show advanced selection" labelled business action button on the main page


        When the user selects the "results" bound table field on the main page

        # Untick all the shipments
        When the user selects the "Select all" labelled checkbox field on the main page
        And the user unticks the checkbox field


        And the user filters the "Shipment number" labelled column in the table field with value "Mass_Invoice_Test_SH"

        And the user selects the row with text "Mass_Invoice_Test_SH_01" in the "Shipment number" labelled column header of the table field
        Then the value of the "Bill-to customer" labelled nested reference field of the selected row in the table field is "ATSERMO"
        Then the value of the "Shipping date" labelled nested date field of the selected row in the table field is "12/29/2023"
        And the user ticks the main checkbox of the selected row in the table field

        And the user selects the row with text "Mass_Invoice_Test_SH_02" in the "Shipment number" labelled column header of the table field
        Then the value of the "Bill-to customer" labelled nested reference field of the selected row in the table field is "Distributor"
        Then the value of the "Shipping date" labelled nested date field of the selected row in the table field is "12/29/2023"
        And the user ticks the main checkbox of the selected row in the table field


        And the user clicks the "Create" labelled business action button on the main page
        Then a toast with text "Sales invoices created: 2" is displayed
