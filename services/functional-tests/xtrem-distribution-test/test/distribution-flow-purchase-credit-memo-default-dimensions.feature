# The goals of this test are;
# 1. to verify that a new purchase credit-memo inherits dimensions from the site or supplier and according
#    to the company default dimension rules,
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly

@distribution
Feature: distribution-flow-purchase-credit-memo-default-dimensions

    Scenario: 01 - Create purchase credit-memo and verify purchase order line dimensions
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        # Create purchase credit-memo
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Site ZA1" in the reference field
        And the user selects "Site ZA1" in the reference field
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user writes "Verskaffer ZA1" in the reference field
        And the user selects "Verskaffer ZA1" in the reference field
        And the user selects the "Reason" labelled reference field on the main page
        And the user writes "Stock value correction" in the reference field
        And the user selects "Stock value correction" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Service" in the reference field
        And the user selects "Service" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "47.99" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_PCMDD_NUM04]"
        # Verify purchase credit-memo line dimensions after save
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update purchase credit-memo dimensions (Apply to new lines only) and verify purchase credit-memo line dimensions
        # Update dimension
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "site__name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_PCMDD_NUM04]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add new purchase credit-memo line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Safety training" in the reference field
        And the user selects "Safety training" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "23.70" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify new purchase credit-memo line dimensions (Safety training)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Safety training" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify initial purchase credit-memo line dimensions (Service)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update purchase credit-memo dimensions (Apply to all lines) and verify purchase credit-memo line dimensions
        # Update dimension
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "site__name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_PCMDD_NUM04]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "IT" in the reference field
        And the user selects "IT" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Residential" in the reference field
        And the user selects "Residential" in the reference field
        Then the user clicks the "Apply to all lines" labelled business action button on a modal
        # Add new purchase credit-memo line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Freight charges" in the reference field
        And the user selects "Freight charges" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "16.45" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify new purchase credit-memo line dimensions (Freight charges)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Freight charges" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify second purchase credit-memo line dimensions (Safety training)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Safety training" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify initial purchase credit-memo line dimensions (Service)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "IT"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Delete purchase credit memo
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
