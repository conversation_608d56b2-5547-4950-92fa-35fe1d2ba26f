# This test covers a new function in the kebab header of sales shipment page, able to access "Stock journal inquiry" action.

@distribution
Feature: distribution-crud-sales-shipment-stock-journal-inquiry

    Scenario: 01 - Verify functionality for the "Stock journal inquiry" top kebab action
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "SH250002"
        And the user selects the row with text "SH250002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Sales shipment SH250002" titled page is displayed
        And the user clicks the "Stock journal inquiry" labelled more actions button in the header
        And an info dialog appears on a full width modal
        And the dialog title is "Stock journal inquiry" on a full width modal
        And the user selects the "$navigationPanel" bound table field on a full width modal
        And the value of the option menu of the table field is "My selected data"
        And the user selects the row 1 of the table field
        And the value of the "Document line type" labelled nested text field of the selected row in the table field is "Sales shipment line"
        And the value of the "Document" labelled nested link field of the selected row in the table field is "SH250002"
        And the value of the "Item" labelled nested reference field of the selected row in the table field is "Pressure sensor"
        And the value of the "Site" labelled nested reference field of the selected row in the table field is "TE Hampton"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-1 each"
        And the user clicks the Close button of the dialog on a full width modal
        And the "Sales shipment SH250002" titled page is displayed
