# The purpose of this test is to verify row action (aprove,reject ) on the sales order page
# directly form navigation bar

@distribution

Feature:  distribution-crud-sales-order-row-actions

    Scenario: 01 - Verify the user is able to duplicate the sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO230028"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO230028"
        And the user clicks the "Duplicate" labelled button of the table field
        And a toast with text "Record was duplicated successfully." is displayed
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_salesOrder]"
        # Find the line item
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user writes "10" in the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user stores the value of the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field with the key "[ENV_Updated_Price]"
        And the user presses Control+Enter

    Scenario: 02 - Verify the user is able to confirm the created sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_salesOrder]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        # Verify the values of the stock item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Pressure transmitter"
        #Item description is no more defaulted to the page - XT-90623
        #And the value of the "Item description" labelled nested text field of the selected row in the table field is "Pressure transmitter"
        And the value of the "Sales unit" labelled nested text field of the selected row in the table field is "Each"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "10 each"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "$ 15.00"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "$ 15.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "$ 150.00"
        # Confirm sales order details
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Confirm" dropdown action of the selected row of the table field
        And the user clicks the "Confirm" button of the Confirm dialog
        #Verify Confirmation
        Then a toast containing text "The sales order was confirmed." is displayed

    Scenario: 03 - Verify the user is able to set dimension
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the "Apply to all lines" button of the Custom dialog
        Then a toast containing text "Dimensions applied." is displayed

    Scenario: 04 - Verify the user is able to print
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Print" dropdown action of the selected row of the table field
        And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast containing text "The sales order was printed" is displayed

    Scenario: 05 - Verify the user is able to close the order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Close order" dropdown action of the selected row of the table field
        And the user clicks the "Close order" button of the Confirm dialog
        Then a toast containing text "The sales order is closed." is displayed

    Scenario: 06 - Verify the user is able to open the order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Open order" dropdown action of the selected row of the table field
        And the user clicks the "Reopen order" button of the Confirm dialog
        Then a toast containing text "The open action is allowed. The sales order is open." is displayed

    Scenario: 07 - Verify the user is able to sent the order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Send" dropdown action of the selected row of the table field
        And the user selects the "Last name" labelled text field on a modal
        And the user writes "test" in the text field
        And the user selects the "Email" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And the user clicks the "Send" button of the Custom dialog
        Then a toast containing text "Sales order sent to: <EMAIL>" is displayed

    Scenario: 08 - Verify the user is able to allocate stock
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user clicks the "OK" button of the Custom dialog

    Scenario: 09 - Verify the user is able to deallocate stock
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Deallocate stock" dropdown action of the selected row of the table field
        And the user clicks the "OK" button of the Custom dialog

    Scenario: 10 - Verify the user is able to create proforma
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Create proforma invoice" dropdown action of the selected row of the table field
        And the user selects the "Expiration date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user clicks the "Generate" button of the Custom dialog
        Then a toast with text "Record created" is displayed

    Scenario: 11 - Verify the user is able to create shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder]"
        And the user clicks the "Create shipment" dropdown action of the selected row of the table field
        And the user clicks the "Create" button of the Custom dialog
        Then a toast containing text "1 shipment(s) created" is displayed

    Scenario: 12 - Verify the user is able to delete the sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO230028"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO230028"
        And the user clicks the "Duplicate" labelled button of the table field
        And a toast with text "Record was duplicated successfully." is displayed
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_salesOrder2]"
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user writes "10" in the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user stores the value of the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field with the key "[ENV_Updated_Price]"
        And the user presses Control+Enter
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_salesOrder2]"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "[ENV_salesOrder2]"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Custom dialog
        Then a toast with text "Record deleted" is displayed
