# This script that checks the default dimensions of a sales and purchase order created using order to order
@distribution

Feature: distribution-flow-purchase-order-from-sales-order-default-dimensions

    Scenario: 1 - Check dimensions for a recently created sales order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Sale order creation
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "85" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed

        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SOPO]"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field

        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"

    Scenario: 2 - Create Work Order from the recently created Sale Order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_SOPO]" in the "Number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Create purchase order" dropdown action of the selected row of the table field

        And the user selects the "supplierLink" bound link field on the sidebar
        And the user clicks in the link field
        # Search for Supplier and Click on the row
        And the user selects the "Supplier" labelled table field on a modal
        And the user selects the row with text "Lyreco" in the "Name" labelled column header of the table field
        # Confirm this is the default supplier
        Then the value of the "Type" labelled nested text field of the selected row in the table field is "Other"
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Continue" button of the Custom dialog on the main page
        # Confirm it has been selected
        And the user selects the "selectedSupplier" bound reference field on a modal
        Then the value of the reference field is "Lyreco"

        # Confirm dialog
        And the user clicks the "OK" button of the Custom dialog on the main page

        And the user clicks the "Create" labelled business action button on the sidebar

    Scenario: 3 - Check dimensions for the recently created Work Order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Supplier" labelled column in the table field with value "Lyreco"
        And the user filters the "Purchasing Site" labelled column in the table field with value "Swindon"

        And the user selects the row 1 of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        And the user selects the "Purchasing site" labelled reference field on the main page
        Then the value of the reference field is "Swindon"

        And the user selects the "Supplier *" labelled reference field on the main page
        Then the value of the reference field is "Lyreco"

        And the user selects the "Order date *" labelled date field on the main page
        And the value of the date field is a generated date with value "T"

        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Admin"
        Then the user clicks the "Cancel" labelled business action button on a modal
