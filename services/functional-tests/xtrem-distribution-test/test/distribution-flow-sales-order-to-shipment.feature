#The purpose of this test is to verify sales order to shipment creation from sales order (SO230023) and non-stock item (Strawberry cup cake) and stock-item (Je<PERSON> sweet)

@distribution
Feature: distribution-flow-sales-order-to-shipment

    Scenario: 01 - Opening sales order and confirm order and allocated stock
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        # Changing menu status
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field

        # Selecting the sales order line
        And the user filters the "Number" labelled column in the table field with value "SO230023"
        And the user selects the row with text "SO230023" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verifying the sales order allocated stock
        # Verify sales site
        And the user selects the "Site" labelled reference field on the main page
        And the value of the reference field is "TE Hampton"

        # Verify sold to customer
        And the user selects the "soldToCustomer" labelled reference field on the main page
        And the value of the reference field is "MK Manufacturing"

        # Verify the Sales order ID number, and order date
        When the user selects the "number" labelled text field on the main page
        Then the value of the text field is "SO230023"
        When the user selects the "orderDate" labelled date field on the main page
        Then the value of the date field is "09/26/2023"

        #Verify the tile fields
        And the user selects the "salesOrderLineCount" bound tile count field on the main page
        Then the value of the tile count field is "2"
        And the user selects the "expectedDeliveryDate" labelled tile date field on the main page
        Then the value of the tile date field is "10/02/2023"
        And the user selects the "totalExcludingTax" labelled tile aggregate field on the main page
        Then the value of the tile aggregate field is "$175.00"
        And the user selects the "totalIncludingTax" labelled tile aggregate field on the main page
        Then the value of the tile aggregate field is "$175.00"

        # Verify the items (Jelly sweet and Strawberry cup cake) details for on the table fields
        # Item Jelly sweet
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Jelly sweet" in the "Item" labelled column header of the table field
        #Item description is no more defaulted to the page - XT-90623
        #And the value of the "Item description" labelled nested text field of the selected row in the table field is "Jelly sweet Description"
        And the value of the "Sales unit" labelled nested text field of the selected row in the table field is "Each"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "5 each"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "$ 15.00"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "$ 15.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "$ 75.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "$ 0.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "$ 75.00"
        And the value of the "Stock cost amount" labelled nested text field of the selected row in the table field is "$ 25.00"
        And the value of the "stockSite" labelled nested text field of the selected row in the table field is "TE Hampton"

        # Item Strawberry cup cake
        And the user selects the row with text "Strawberry cup cake" in the "Item" labelled column header of the table field
        #Item description is no more defaulted to the page - XT-90623
        #And the value of the "Item description" labelled nested text field of the selected row in the table field is "Strawberry cup cake description"
        And the value of the "Sales unit" labelled nested text field of the selected row in the table field is "Each"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "5 each"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "$ 20.00"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "$ 20.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "$ 100.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "$ 0.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "$ 100.00"
        And the value of the "Stock cost amount" labelled nested text field of the selected row in the table field is "$ 30.00"
        And the value of the "stockSite" labelled nested text field of the selected row in the table field is "TE Hampton"

        # Verifying the display status (on Quote) of the sales shipment
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Quote"

        # Confirming the sales order
        And the user clicks the "confirm" labelled business action button on the main page
        And the user clicks the "confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed
        And the user dismisses all the toasts

        # Verifying the display status (on Confirmed) of the sales shipment
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"

        # Shipping and creating sales shipment
        And the user clicks the "Create shipment" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "1 shipment(s) created" is displayed

        # Verifying the display status of the sales shipment
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to process"

        # Allocate stock to item that is not allocated (Jelly sweet-stock managed)
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Jelly sweet" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user stores the value of the "quantityToAllocate" bound nested numeric field of the selected row in the table field with the key "[ENV_QUANTITYTOALLOCATE]"
        And the user clicks the "save" labelled business action button on a modal

        # Verifying if the stock is allocated and the allocated quantity
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Jelly sweet" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the value of the "Allocated quantity" labelled nested text field of the selected row in the table field is "[ENV_QUANTITYTOALLOCATE]"
        And the user clicks the "confirm" labelled business action button on the main page
        And the user clicks the "confirm" button of the Confirm dialog
        Then a toast containing text "Status updated." is displayed
        And the user dismisses all the toasts

        # Verifying the display status of the sales shipment (on Ready to ship)
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to ship"

        # Posting sales shipment and verifying shipment status (on Shipped)
        And the user clicks the "Post Stock" labelled business action button on the main page
        And the user clicks the "confirm" button of the Confirm dialog
        Then a toast containing text "The sales shipment was posted." is displayed
        And the user refreshes the screen
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Shipped"
