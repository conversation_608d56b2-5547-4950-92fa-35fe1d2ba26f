#This test can only be executed with sage.
#The goal of this test is to verify that the user can do CRUD on Purchase receipt
@distribution
Feature: distribution-crud-purchase-receipt

  Scenario: Verify that the user can create a Purchase receipt
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    When the user clicks the "create" labelled business action button on the main page
    #create
    And the user selects the "Receiving site" labelled reference field on the main page
    And the user writes "Swindon" in the reference field
    And the user selects "Swindon" in the reference field
    And the user selects the "Supplier" labelled reference field on the main page
    And the user writes "Lyreco" in the reference field
    And the user selects "<PERSON>yreco" in the reference field
    And the user selects the "Number" labelled text field on the main page
    And the user writes "PR_001" in the text field
    #Add stock
    When the user selects the "lines" labelled table field on the main page
    And the user adds a new table row to the table field using the sidebar
    And the user selects the "item" labelled reference field on the sidebar
    And the user writes "RJournal entry item01" in the reference field
    And the user selects "RJournal entry item01" in the reference field
    And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
    And the user writes "5" in the numeric field
    And selects the "Price" labelled navigation anchor on the sidebar
    And the user selects the "Gross price" labelled numeric field on the sidebar
    And the user writes "40" in the numeric field
    And the user selects the "Discount" labelled numeric field on the sidebar
    And the user writes "2.00" in the numeric field
    And the user selects the "Charge" labelled numeric field on the sidebar
    And the user writes "2.00" in the numeric field
    And the user clicks the "Apply" button of the dialog on the sidebar
    And the user selects the "lines" bound table field on the main page
    And the user selects the row with text "RJournal entry item01" in the "item" labelled column header of the table field
    #Stock allocation
    And the user clicks the "Stock details" dropdown action of the selected row of the table field
    And the user selects the "stockDetails" bound table field on a modal
    And the user clicks the "addStockDetail" bound action of the table field
    And the user selects the row with text "5 each" in the "quantityInStockUnit" bound column header of the table field
    And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
    And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
    #And the user presses Enter
    And the user clicks the "ok" bound business action button on a modal
    #add save to update the net price -> used to calculate the total excluding tax -> used to calculate the tax
    And the user clicks the "Save" labelled business action button on the main page
    Then a toast containing text "Record created" is displayed
    And the user dismisses all the toasts
    #Tax calculation
    #Tax is UK Purchase Goods Standard Rate,it's % is 20% therefore 20% of 200 is  40,00
    And the user selects the "lines" bound table field on the main page
    And the user selects the row with text "RJournal entry item01" in the "item" labelled column header of the table field
    And the user clicks the "Tax details" dropdown action of the selected row of the table field
    And the user selects the "taxDetails" bound table field on a modal
    And the user selects the row with text "£ 200.00" in the "taxableAmount" bound column header of the table field
    And the value of the "taxableAmount" bound nested numeric field of the selected row in the table field is "£ 200.00"
    And the value of the "taxRate" bound nested numeric field of the selected row in the table field is "20.00 %"
    And the value of the "taxAmount" bound nested numeric field of the selected row in the table field is "£ 40.00"
    And the user clicks the "ok" bound business action button on a modal
    And the user clicks the "save" labelled business action button on the main page
    Then a toast containing text "Record updated" is displayed
    And the user dismisses all the toasts


  Scenario: Verify that the user can update a pending Purchase receipt
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    When the user selects the "$navigationPanel" bound table field on the main page
    And the user selects the row with text "PR_001" in the "number" bound column header of the table field
    And the user clicks the "number" labelled nested field of the selected row in the table field
    And the user selects the "lines" bound table field on the main page
    And the user selects the row with text "RJournal entry item01" in the "item" labelled column header of the table field
    And the user writes "6" in the "quantity" bound nested numeric field of the selected row in the table field
    And the user clicks the "save" labelled business action button on the main page
    Then a toast containing text "Record updated" is displayed
    ##Stock allocation
    When the user selects the "lines" bound table field on the main page
    And the user selects the row with text "RJournal entry item01" in the "item" labelled column header of the table field
    And the user clicks the "Stock details" dropdown action of the selected row of the table field
    And the user selects the "stockDetails" bound table field on a modal
    And the user selects the row with text "5 each" in the "quantityInStockUnit" bound column header of the table field
    And the user writes "6" in the "quantityInStockUnit" bound nested numeric field of the selected row in the table field
    And the user clicks the "ok" bound business action button on a modal
    And the user clicks the "save" labelled business action button on the main page
    Then a toast containing text "Record updated" is displayed

  # this part is commented because of line 96 , select button not yet covered by framework as it is a new component
  # | Enhancement request to Create to ATP | Automation request to create to update this scenario and create new test to cover all actions of "addLinesFromOrdersLookup" feature

  # #Adding a new item in the receipt line by using the select from purchase order option
  # When the user selects the "lines" bound table field on the main page
  # And the user clicks the "addLinesFromOrdersLookup" labelled business action button of the table field
  # And the user selects the "$applicationCodeLookup" bound table field on a modal
  # And the user selects the row with text "RJournal entry item01" in the "Item" labelled column header of the table field
  # And the user ticks the main checkbox of the selected row in the table field
  # And the user clicks in the "select" bound button field on a modal
  # And the user clicks the "save" labelled business action button on the main page
  # Then a toast containing text "Record updated" is displayed
  # #Adding stock
  # When the user selects the "lines" labelled table field on the main page

  # And the user selects the row with text "2 each" in the "quantityInPurchaseUnit" labelled column header of the table field
  # #Stock allocation
  # And the user clicks the "Stock details" dropdown action of the selected row of the table field
  # And the user selects the "stockDetails" bound table field on a modal
  # And the user clicks the "addStockDetail" bound action of the table field
  # And the user selects the row with text "2 each" in the "quantityInStockUnit" bound column header of the table field
  # And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
  # And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
  # #And the user presses Enter
  # And the user clicks the "ok" bound business action button on a modal
  # And the user clicks the "save" labelled business action button on the main page
  # Then a toast containing text "Record updated" is displayed

  # ## Updating Tax details
  # ## Tax is UK Purchase Goods Reduced Rate, its % is 5% therefore 5% of 240 is  12,00
  # ## The tax reference field is failing to find my tax group, therefore I will comment out
  # When the user selects the "lines" bound table field on the main page
  # And the user selects the row with text "RJournal entry item01" in the "item" labelled column header of the table field
  # And the user clicks the "Tax details" dropdown action of the selected row of the table field
  # And the user selects the "taxDetails" bound table field on a modal
  # And the user selects the row with text "£ 240.00" in the "taxableAmount" bound column header of the table field
  # And the user clicks the "tax" labelled nested field of the selected row in the table field
  # #Tax update
  # And the user opens the lookup dialog in the "tax" labelled nested reference field of the selected row in the table field
  # And the user selects the "taxReference" bound table field on a modal
  # And the user selects the row with text "UK Purchase Goods Reduced Rate" in the "name" labelled column header of the table field
  # And the user clicks the "name" labelled nested field of the selected row in the table field
  # And the user selects the "taxDetails" bound table field on a modal
  # And the user selects the row with text "£ 240.00" in the "taxableAmount" bound column header of the table field
  # And the value of the "taxableAmount" bound nested numeric field of the selected row in the table field is "£ 240.00"
  # And the value of the "taxRate" bound nested numeric field of the selected row in the table field is "5.00 %"
  # And the value of the "taxAmount" bound nested numeric field of the selected row in the table field is "£ 12.00"
  # And the user clicks the "ok" bound business action button on a modal
  # And the user clicks the "save" labelled business action button on the main page
  # Then a toast containing text "Record updated" is displayed


  Scenario: Verify that the user can print a pending Purchase receipt
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    When the user selects the "$navigationPanel" bound table field on the main page
    And the user selects the row with text "PR_001" in the "number" bound column header of the table field
    And the user clicks the "number" labelled nested field of the selected row in the table field
    And the user clicks the "print" labelled button in the header
    # And the user waits 10 seconds
    And the dialog title is "Print document"
    And the user clicks the Close button of the dialog on the main page
  #We don't want to click the link as it re direct to a page that the bot can not detect

  Scenario: Verify that the user can delete pending Purchase receipt
    Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
    Then the "Purchase receipts" titled page is displayed
    When the user selects the "$navigationPanel" bound table field on the main page
    And the user selects the row with text "PR_001" in the "number" bound column header of the table field
    And the user clicks the "number" labelled nested field of the selected row in the table field
    And the user clicks the "Delete" labelled more actions button in the header
    And the user clicks the "Delete" button of the Confirm dialog
    Then a toast containing text "Record deleted" is displayed
