# The purpose of this test is to verify the creation, update and deletion of the sale order
# Site : Swindon
# Customer : Distributor

@distribution

Feature:  distribution-crud-sales-order

    Scenario: 01 - Verify the user is able to create the sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        # Fill in Customer reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        # Add a line for stock item
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        # And the user presses Enter
        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.23" in the numeric field
        # And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 2 seconds
        # Add line for non stock item
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "RJournal entry item02" in the reference field
        And the user selects "RJournal entry item02" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        # And the user presses Enter
        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10.00" in the numeric field
        # And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed
        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SONUM01]"


    Scenario: 02 - Verify the user is able to confirm the created sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SONUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Verify the values of the stock item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        #Item description is no more defaulted to the page - XT-90623
        #And the value of the "Item description" labelled nested text field of the selected row in the table field is "Pressure transmitter"
        And the value of the "Sales unit" labelled nested text field of the selected row in the table field is "Each"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "5 each"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "£ 12.23"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "£ 12.23"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "£ 61.15"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "£ 12.23"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "£ 73.38"
        And the value of the "Stock cost amount" labelled nested text field of the selected row in the table field is "£ 40.00"
        # Verify tthe values of the stock item
        And the user selects the row with text "RJournal entry item02" in the "Item" labelled column header of the table field
        #Item description is no more defaulted to the page - XT-90623
        #And the value of the "Item description" labelled nested text field of the selected row in the table field is "RJournal entry item02"
        And the value of the "Sales unit" labelled nested text field of the selected row in the table field is "Each"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "5 each"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "£ 10.00"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "£ 10.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "£ 50.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "£ 10.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "£ 60.00"
        And the value of the "Stock cost amount" labelled nested text field of the selected row in the table field is "£ 30.00"
        # Confirm sales order details
        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        #Verify Confirmation
        Then a toast containing text "The sales order was confirmed." is displayed


    Scenario: 03 - Verify the user is able to generate a proforma invoice of the sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SONUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        When the user clicks the "Create proforma invoice" labelled more actions button in the header
        And the user selects the "Expiration date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T+3"
        And the user selects the "Customer comment" labelled rich text field on a modal
        And the user writes "Test comment" in the rich text field
        And the user clicks the "Generate" button of the Custom dialog
        Then a toast containing text "Record created" is displayed
        # Verify new tab exists and correct table information
        When selects the "Proforma invoices" labelled navigation anchor on the main page
        And the user selects the "proformaInvoices" bound table field on the main page
        And the user selects the row with text "1" in the "Version" labelled column header of the table field
        Then the value of the "Issue date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        Then the value of the "Expiration date" labelled nested date field of the selected row in the table field is a generated date with value "T+3"


    Scenario: 04 - Verify the user is able to update the sales order item quantity
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SONUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "lines" bound table field on the main page
        # Update quantity via line panel
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        Then the text in the body of the dialog contains "The sales quantity has been updated. Do you want to recalculate prices, discounts and charges?" on the main page
        And the user clicks the "OK" button of the Confirm dialog
        # Update quantity via inline
        And the user selects the row with text "RJournal entry item02" in the "Item" labelled column header of the table field
        And the user writes "50" in the "quantity" bound nested numeric field of the selected row in the table field
        And the user presses Tab
        Then the text in the body of the dialog contains "The sales quantity has been updated. Do you want to recalculate prices, discounts and charges?" on the main page
        And the user clicks the "Cancel" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 05 - Verify the user is able to verify the updated item quantity
        #Verify Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SONUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "10 each"
        And the user selects the row with text "RJournal entry item02" in the "Item" labelled column header of the table field
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "50 each"


    Scenario: 06 - Confirm that a sales order cannot be deleted when there is a linked active proforma invoice (from page)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SONUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a validation error message is displayed containing text
            """"
            Validation errors
            You cannot delete a sales order when there are linked proforma invoices.
            """


    Scenario: 07 - Confirm that a sales order cannot be deleted when there is a linked active proforma invoice (from row action)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "[ENV_SONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Confirm dialog
        Then a validation error message is displayed containing text
            """"
            Validation errors
            You cannot delete a sales order when there are linked proforma invoices.
            """

# # @todo uncomment and ammend the bellow scenario when development is done
# # Verify Deletion
# # Note this action will fail as we have a linked active proforma invoice from an above scenario, we are awaiting further development which will allow the proforma to be manually set to
# # deleted/expired to then allow deletion of a sales order to be successful
# Scenario: 08 - Verify the user is able to delete a newly created sales order
#     Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
#     Then the "Sales orders" titled page is displayed
#     When the user selects the "$navigationPanel" bound table field on the main page
#     And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of row 1 in the table field
#     And the user selects the "All statuses" dropdown option in the navigation panel
#     And the user searches for "[ENV_SONUM01]" in the navigation panel
#     And the user clicks the "first" navigation panel's row
#     When the user clicks the "Delete" labelled more actions button in the header
#     And the user clicks the "Delete" button of the Confirm dialog
#     Then a toast containing text "Record deleted" is displayed
