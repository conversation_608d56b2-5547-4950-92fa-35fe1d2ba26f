# the goal of this test is to verify that currency conversion is working as expected on Purchase receipt
# when receipt is created directly from a purchase order with currency conversion
# We use a company with Leg=GB & a Supplier with Leg=FR
# Site= Swindon | Supplier = BARRES | Exchange Rate : 1 EUR = 0.89673 GBP

@distribution
Feature: distribution-flow-purchase-receipt-currency-conversion

        Scenario: 01 - Purchase order amounts verification
                # open purchase order
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
                Then the "Purchase orders" titled page is displayed
                When the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row with text "PO-PR-CC01" in the "number" labelled column header of the table field
                And the user clicks the "number" labelled nested field of the selected row in the table field

                # Verify amounts, currency, & exchange rate of the PO
                # verify amounts on grid
                And the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
                And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 12.56000"
                And the value of the "Net price" labelled nested text field of the selected row in the table field is "€ 12.56000"
                And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 125.60"
                And the value of the "Total tax" labelled nested text field of the selected row in the table field is "€ 6.28"
                And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 131.88"
                # verify amounts on panel - Price tab
                And the user clicks the "Open line panel" inline action button of the selected row in the table field
                And selects the "Price" labelled navigation anchor on the sidebar
                And the user selects the "Total excluding tax" labelled numeric field on the sidebar
                And the value of the numeric field is "125.60"
                And the user selects the "Total tax" labelled numeric field on the sidebar
                And the value of the numeric field is "6.28"
                And the user selects the "Total including tax" labelled numeric field on the sidebar
                And the value of the numeric field is "131.88"
                And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
                And the value of the numeric field is "112.63"
                And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
                And the value of the numeric field is "118.26"
                And the user clicks the "Apply" button of the dialog on the sidebar
                # verify exchange rate
                Then selects the "Information" labelled navigation anchor on the main page
                And the user selects the "Currency" labelled reference field on the main page
                And the value of the reference field is "Euro"
                And the user selects the "Exchange rate" labelled text field on the main page
                And the value of the text field is "1 EUR = 0.89673 GBP"
                # verify totals tab
                Then selects the "Totals" labelled navigation anchor on the main page
                And the user selects the "Excluding tax" labelled numeric field on the main page
                And the value of the numeric field is "125.60"
                And the user selects the "tax" labelled numeric field on the main page
                And the value of the numeric field is "6.28"
                And the user selects the "Including tax" labelled numeric field on the main page
                And the value of the numeric field is "131.88"
                And the user clicks the "Save" labelled business action button on the main page

        Scenario: 02 - Purchase receipt creation from Purchase order & posting
                # open purchase order
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
                Then the "Purchase orders" titled page is displayed
                When the user selects the "All statuses" dropdown option in the navigation panel
                When the user selects the "$navigationPanel" bound table field on the main page
                And the user filters the "number" bound column in the table field with value "PO-PR-CC01"
                And the user selects the row with text "PO-PR-CC01" in the "number" labelled column header of the table field
                And the user clicks the "number" labelled nested field of the selected row in the table field
                # Create the receipt from order
                And the user clicks the "createReceipt" labelled business action button on the main page
                And the user clicks the "Create" button of the Confirm dialog
                Then a toast containing text "Receipts created" is displayed
                # storing number of receipt created
                And the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
                And the user selects the row 1 of the table field
                And the user clicks the "Open line panel" inline action button of the selected row in the table field
                And selects the "Progress" labelled navigation anchor on the sidebar
                And the user selects the "purchaseReceiptLines" bound table field on the sidebar
                And the user selects the row with text "10 each" in the "Quantity in purchase unit" labelled column header of the table field
                Then the user stores the value of the "Receipt number" labelled nested text field of the selected row in the table field with the key "[ENV_PR_CC01]"
                # Opening purchase receipt
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
                Then the "Purchase receipts" titled page is displayed
                When the user selects the "All statuses" dropdown option in the navigation panel
                When the user selects the "$navigationPanel" bound table field on the main page
                And the user filters the "number" bound column in the table field with value "[ENV_PR_CC01]"
                And the user selects the row with text "[ENV_PR_CC01]" in the "number" bound column header of the table field
                And the user clicks the "number" labelled nested field of the selected row in the table field
                Then the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
                And the value of the "Item" labelled nested text field of the selected row in the table field is "Pressure sensor"
                And the user clicks the "Stock details" dropdown action of the selected row of the table field
                # Allocating Stock
                And the user selects the "stockDetails" bound table field on a modal
                And the user clicks the "addStockDetail" bound action of the table field
                And the user selects the row with text "10 each" in the "quantityInStockUnit" bound column header of the table field
                And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
                And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
                #And the user presses Enter
                And the user clicks the "OK" labelled business action button on a modal
                And the user clicks the "save" labelled business action button on the main page
                Then a toast containing text "Record updated" is displayed
                # Posting the receipt
                When the user clicks the "post stock" labelled business action button on the main page
                And the user selects the "displayStatus" labelled label field on the main page
                Then the value of the label field is "Received"

        Scenario: 03 - Purchase receipt Currency conversion verification
                # open PR created
                Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
                Then the "Purchase receipts" titled page is displayed
                When the user selects the "All statuses" dropdown option in the navigation panel
                When the user selects the "$navigationPanel" bound table field on the main page
                And the user filters the "number" bound column in the table field with value "[ENV_PR_CC01]"
                And the user selects the row with text "[ENV_PR_CC01]" in the "number" bound column header of the table field
                And the user clicks the "Number" labelled nested field of the selected row in the table field

                # verify amounts on grid
                And the user selects the "lines" bound table field on the main page
                And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
                And the value of the "Gross price" labelled nested text field of the selected row in the table field is "€ 12.56000"
                And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "€ 125.60"
                And the value of the "Total tax" labelled nested text field of the selected row in the table field is "€ 6.28"
                And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "€ 131.88"

                # verify amounts on panel
                And the user clicks the "Open line panel" inline action button of the selected row in the table field
                # select price tab
                And selects the "Price" labelled navigation anchor on the sidebar
                And the user selects the "Total excluding tax" labelled numeric field on the sidebar
                And the value of the numeric field is "125.60"
                And the user selects the "Total tax" labelled numeric field on the sidebar
                And the value of the numeric field is "6.28"
                And the user selects the "Total including tax" labelled numeric field on the sidebar
                And the value of the numeric field is "131.88"
                And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
                And the value of the numeric field is "112.63"
                And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
                And the value of the numeric field is "118.26"
                And the user clicks the "Apply" button of the dialog on the sidebar

                # verify exchange rate
                Then selects the "Information" labelled navigation anchor on the main page
                And the user selects the "Transaction currency *" labelled reference field on the main page
                And the value of the reference field is "Euro"
                And the user selects the "Exchange rate" labelled text field on the main page
                And the value of the text field is "1 EUR = 0.89673 GBP"

                # verify totals tab
                Then selects the "Totals" labelled navigation anchor on the main page
                And the user selects the "Excluding tax" labelled numeric field on the main page
                And the value of the numeric field is "125.60"
                And the user selects the "Tax" labelled numeric field on the main page
                And the value of the numeric field is "6.28"
                And the user selects the "Including tax" labelled numeric field on the main page
                And the value of the numeric field is "131.88"
