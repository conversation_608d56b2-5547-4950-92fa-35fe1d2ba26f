#This test can only be executed with sage.
#The goal of this test is to verify that the user can create sales invoice from shipment with currency different from the company currency

@distribution
Feature: distribution-flow-sales-invoice-from-sales-shipment-currency-conversion

    Scenario: 01 - Verify that the user can create sales invoice from sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user filters the "number" bound column in the table field with value "SH230030"
        And the user selects the row with text "SH230030" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        ##Create invoice
        When the user clicks the "Create invoice" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Record created" is displayed

        # Wait for page to redirect
        And the user waits 5 seconds

        ##Verify invoice created
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "Site" labelled reference field on the main page
        And the value of the reference field is "Swindon"
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the value of the reference field is "MK Manufacturing"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the value of the "Origin" labelled nested text field of the selected row in the table field is "Shipment"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Pressure transmitter"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "10 each"
        Then the value of the "Gross price" labelled nested text field of the selected row in the table field is "$ 15.00"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "$ 15.00"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "$ 150.00"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "$ 30.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "$ 180.00"
        And the value of the "Stock cost amount" labelled nested text field of the selected row in the table field is "$ 99.17"
        And the value of the "Gross profit amount" labelled nested text field of the selected row in the table field is "$ 50.83"

        # Verify the currency conversion record
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Total excluding tax" labelled numeric field on the sidebar
        And the value of the numeric field is "150.00"
        And the user selects the "Total excluding tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "121.01"
        And the user selects the "Total including tax company currency" labelled numeric field on the sidebar
        And the value of the numeric field is "145.21"
        # When the user selects the "Cancel" labelled business action button on the sidebar
        And the user clicks the "Cancel" button of the dialog on the sidebar

        # Verify the tax details
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the value of the "Tax" labelled nested reference field of the selected row in the table field is "UK Sale Goods Standard Rate"
        And the value of the "Taxable base" labelled nested reference field of the selected row in the table field is "$ 150.00"
        And the value of the "Tax rate" labelled nested reference field of the selected row in the table field is "20.00 %"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "$ 30.00"
        Then the user clicks the "ok" bound business action button on a modal

        # Verify the currency conversion record - Exchange rate
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Transaction currency" labelled reference field on the main page
        And the value of the reference field is "US Dollar"
        And the user selects the "Exchange rate" labelled text field on the main page
        And the value of the text field is "1 USD = 0.8067020809 GBP"
        And the user clicks the "Save" labelled business action button on the main page

        ##Post invoice
        When the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "Record updated" is displayed
