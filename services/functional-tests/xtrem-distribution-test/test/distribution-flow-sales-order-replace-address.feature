@distribution
Feature: distribution-flow-sales-order-replace-address
    Scenario: 01 - Verify the user is able to create the sales order
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        # Fill in Customer reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        # Add a line for stock item
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        # And the user presses Enter
        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.23" in the numeric field
        # And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 2 seconds
        # Add line for non stock item
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "RJournal entry item02" in the reference field
        And the user selects "RJournal entry item02" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        # And the user presses Enter
        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10.00" in the numeric field
        # And the user presses Enter
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed
        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SONUM01]"
    Scenario: 02 - Verify current address
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SONUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And selects the "Shipping" labelled navigation anchor on the main page
        And the user selects the "shipToAddress" bound pod field on the main page
        And the title of the pod field is "Ship-to address"
        Then the value of the "billToAddress " labelled nested text area field in the pod field is "Distribution Unit 5 Spen Valley Business Park Bradford BD1 3ED United Kingdom"
    Scenario: 03 - Change address
        And the user clicks the "replace" action of the pod field
        And the user selects the "shipToCustomerAddress" bound table field on a modal
        When the user selects the row with text "Additional Address" in the "name" bound column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Update" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
    Scenario: 04 - Verify new address
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user searches for "[ENV_SONUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And selects the "Shipping" labelled navigation anchor on the main page
        And the user selects the "shipToAddress" bound pod field on the main page
        And the title of the pod field is "Ship-to address"
        Then the value of the "billToAddress " labelled nested text area field in the pod field is "Additional Address Knowle Top Rd Halifax HX3 8SW United Kingdom"
    Scenario: 05 - Delete sales order
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
