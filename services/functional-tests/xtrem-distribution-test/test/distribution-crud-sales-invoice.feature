# The purpose of this test is to verify the creation, update and deletion of sales invoice
# Financial site : UK LIMITED
# Supplier : Lyreco

@distribution
Feature: distribution-crud-sales-invoice

    Scenario: Verify the user can create sales invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Sales invoice" titled page is displayed
        # Fill in the header fields on main page
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Bill-to customer" labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        # Add lines
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "RJournal entry item02" in the reference field
        And the user selects "RJournal entry item02" in the reference field
        And the user selects the "Provider site" labelled reference field on the sidebar
        And the value of the reference field is "Swindon"
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        When the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "15.90" in the numeric field
        And the user selects the "Discount" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Net price" labelled numeric field on the sidebar
        And the value of the numeric field is "14.31"
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save button on main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Store the sales invoice Number
        When the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_SINVOICE]"


    Scenario: Verify the user can update a created sales invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_SINVOICE]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Updating the sales invoice quantity
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "RJournal entry item02" in the "Item" labelled column header of the table field
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "10 each"
        And the user writes "15" in the "Quantity in sales unit" labelled nested text field of the selected row in the table field
        Then the text in the body of the dialog contains "The sales quantity has been updated. Do you want to recalculate prices, discounts and charges?" on the main page
        When the user clicks the "Cancel" button of the Confirm dialog
        # Save changes and verify sales invoice update
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: Verify the user can verify the updated record of the invoice - 1
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_SINVOICE]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Verify the tile indicator values
        And the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "1"
        And the user selects the "TOTAL EXCLUDING TAX" labelled tile aggregate field on the main page
        Then the value of the tile aggregate field is "£214.65"
        And the user selects the "TOTAL INCLUDING TAX" labelled tile aggregate field on the main page
        Then the value of the tile aggregate field is "£257.58"

        # Updating the sales invoice quantity
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        When the user selects the row with text "RJournal entry item02" in the "Item" labelled column header of the table field
        Then the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "15 each"
        And the value of the "Origin" labelled nested text field of the selected row in the table field is "Direct"
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "£ 15.90"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "£ 14.31"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "£ 214.65"
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "£ 42.93"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "£ 257.58"


    Scenario: Verify the user can verify the updated record of the invoice - 2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_SINVOICE]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Delete purchase sales order
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
