# The purpose of this test is to verify the flow of purchase invoice (partial) created from a purchase receipt
# Financial site : UK LIMITED
# Supplier : Lyreco
# Item : Pressure transmitter

@distribution
Feature: distribution-flow-purchase-invoice-from-receipt

    Scenario: 01-Verify the user can create a partial purchase invoice from purchase receipt

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Purchase invoice" titled page is displayed
        # Fill in fields on main page
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        And the user selects "UK LIMITED" in the reference field
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field
        # Add lines from receipt
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from receipts" labelled add action of the table field
        And takes a screenshot
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "PINTSHI202301" in the "Receipt number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        # Verify quantity in purchase unit = 5 each
        Given the user selects the "lines" bound table field on the main page
        When the user selects the row with text "Purchase receipt" in the "Origin" labelled column header of the table field
        Then the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "5 each"
        # Change quantity to 3 each using the sidebar
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "3" in the numeric field
        And the user presses Tab
        # Verify dialog "Confirm partial invoicing" is displayed
        Then an info dialog appears on the sidebar
        And the text in the header of the dialog is "Confirm partial invoicing"
        And the user clicks the "Continue" button of the dialog
        # Enter manual price as it is set back to 0 when changing the quantity (no existing supplier price)
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 3 seconds
        And takes a screenshot

        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        # Getting the purchase invoice number and store it
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PINUM01]"

    Scenario: 02-Verify the user can read the record of the purchase invoice

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        # Searching Creation
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_PINUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user waits 3 seconds
        And the user selects the row with text "Purchase receipt" in the "Origin" labelled column header of the table field
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Pressure transmitter"
        And the value of the "Recipient site" labelled nested text field of the selected row in the table field is "Swindon"
        And the value of the "Purchase unit" labelled nested text field of the selected row in the table field is "each"
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "3 each"


    Scenario: 03-Verify the user can remove variance amount on a purchase invoice

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        # Searching Creation
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_PINUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        # Balance the varience to 0
        And the user selects the "totalAmountExcludingTax" bound numeric field on the main page
        And the user writes "15.00" in the numeric field
        And the user selects the "totalTaxAmount" bound numeric field on the main page
        And the user writes "3.00" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record updated" is displayed

    Scenario: 04-Verify the user can accept all variances and post a partial purchase invoice

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        # Searching Creation
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_PINUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        # Verifying that purchase invoice status is "Variance"
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Variance"
        # Accept all variances
        When the user clicks the "Accept all variances" labelled business action button on the main page
        # Verify dialog "Accept all variances for this invoice" is displayed
        Then an info dialog appears on the main page
        And the dialog title is "Accept variances" on the main page
        And the user clicks the "Accept" button of the dialog
        And the user waits 3 seconds
        Then a toast with text "Variance status updated." is displayed
        And the user dismisses all the toasts
        # Verifying that purchase invoice status has changed to "Variance approved"
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Variance approved"
        # Post an invoice
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted" is displayed
        # Verifying that the purchase invoice status is now "Posted"
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Posted"
