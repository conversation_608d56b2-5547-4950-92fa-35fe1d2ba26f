# The purpose of this test is to confirm that notes are well propagated to subsequent documents for the sales flow

@distribution

Feature: distribution-flow-sales-notes-propagation

    Scenario: 01 - Create a Sales Order with notes and line notes
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        # Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        # Fill in Customer reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field
        # Add a line for stock item
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "jelly sweet" in the reference field
        And the user selects "Jelly sweet" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        # And the user presses Enter
        # Fill in Gross Price on sidebar
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.23" in the numeric field
        # Add notes
        And selects the "Line notes" labelled navigation anchor on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the user writes "this is an internal line note" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_LINE_NOTES01]"
        # And the user presses Enter

        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 2 seconds
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the user writes "this is an internal note" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_NOTES01]"
        And the user selects the "Repeat the document notes on new documents." labelled switch field on the main page
        And the user turns the switch field "ON"
        And the user selects the "Repeat all the line notes on new documents." labelled switch field on the main page
        And the user turns the switch field "ON"

        # Save the record
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast containing text "Record created" is displayed
        # Save the SO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SONUM01]"

        # Confirm the record
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed

    Scenario: 02 - Create a Sales Shipment from the Sales Order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SONUM01]"
        And the user selects the row with text "[ENV_SONUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Allocate stock
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal

        # Create shipment
        And the user clicks the "Create shipment" labelled business action button on the main page
        And the user clicks the "Create" button of the Custom dialog
        Then a toast containing text "1 shipment(s) created" is displayed

        # Verifying the display status of the sales shipment
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to process"

        # Save the SH number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SHNUM01]"

    Scenario: 03 - Verify the Sales shipment has the notes propagated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SHNUM01]"
        And the user selects the row with text "[ENV_SHNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field


        # Verify line notes
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Line notes" labelled navigation anchor on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the value of the rich text field is "[ENV_LINE_NOTES01]"
        And the user clicks the "Cancel" button of the dialog on the sidebar

        # Verify notes
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTES01]"
        And the user selects the "Repeat the document notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Repeat all the line notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
    Scenario: 04 - Create a Sales Invoice from the Sales Shipment
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SHNUM01]"
        And the user selects the row with text "[ENV_SHNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Confirm Sales Shipment
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast with text "Status updated." is displayed

        # Post stock
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast with text "The sales shipment was posted." is displayed


        # Create invoice
        And the user clicks the "Create invoice" labelled business action button on the main page
        And the user clicks the "Create" button of the Custom dialog
        Then a toast with text "Record created" is displayed

        # Verifying the display status of the sales invoice
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"

        # Save the SI number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SINUM01]"
    Scenario: 05 - Verify the Sales Invoice has the notes propagated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SINUM01]"
        And the user selects the row with text "[ENV_SINUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify line notes
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Line notes" labelled navigation anchor on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the value of the rich text field is "[ENV_LINE_NOTES01]"
        And the user clicks the "Cancel" button of the dialog on the sidebar

        # Verify notes
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTES01]"
        And the user selects the "Repeat the document notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Repeat all the line notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
    Scenario: 06 - Create a Sales Credit memo from the Sales Invoice
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SINUM01]"
        And the user selects the row with text "[ENV_SINUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Confirm Sales Invoice
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast with text "The sales invoice was posted." is displayed

        # workaround because of this issue: XT-79005
        And the user refreshes the screen

        # Create credit note
        And the user clicks the "Create credit memo" labelled business action button on the main page
        And the user clicks the "Create" button of the Custom dialog
        Then a toast with text "Record created" is displayed

        # Verifying the display status of the sales credit note
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"

        # Save the SC number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCMUM01]"
    Scenario: 07 - Verify the Sales Credit Memo has the notes propagated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesCreditMemo"
        Then the "Sales credit memos" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SCMUM01]"
        And the user selects the row with text "[ENV_SCMUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify line notes
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Line notes" labelled navigation anchor on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the value of the rich text field is "[ENV_LINE_NOTES01]"
        And the user clicks the "Cancel" button of the dialog on the sidebar

        # Verify notes
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTES01]"

        # Post the credit memo
        And the user clicks the "Post" labelled business action button on the main page
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast with text "The sales credit memo was posted." is displayed

    Scenario: 08 - Create a Sales Return from the Sales Shipment
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user selects the "All statuses" dropdown option in the navigation panel
        When the user filters the "number" bound column in the table field with value "[ENV_SHNUM01]"
        And the user selects the row with text "[ENV_SHNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Create return
        And the user clicks the "Create return request" labelled business action button on the main page
        And the user clicks the "Create" button of the Custom dialog
        Then a toast with text "Sales return request created" is displayed

        And the user refreshes the screen

        # Retrieve new document number of the Sales return request item sidebar progress tab
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Jelly sweet" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "toReturnRequestLines" bound table field on the sidebar

        # Store the Sales return request number
        And the user selects the row with text "Draft" in the "Return request status" labelled column header of the table field
        And the user stores the value of the "Return request number" labelled nested link field of the selected row in the table field with the key "[ENV_SRNUM01]"

    Scenario: 09 - Verify the Sales Return Request has the notes propagated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SRNUM01]"
        And the user selects the row with text "[ENV_SRNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Verify line notes
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Line notes" labelled navigation anchor on the sidebar
        And the user selects the "Internal line notes" labelled rich text field on the sidebar
        And the value of the rich text field is "[ENV_LINE_NOTES01]"
        And the user clicks the "Cancel" button of the dialog on the sidebar

        # Verify notes
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTES01]"
        And the user selects the "Repeat the document notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Repeat all the line notes on new documents." labelled switch field on the main page
        And the switch field is set to "ON"

    Scenario: 10 - Create a Sales Return Receipt from the Sales Return Request
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SRNUM01]"
        And the user selects the row with text "[ENV_SRNUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field

        # Create return receipt
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Custom dialog

        #Then a toast containing text "Confirmation sucessful" is displayed

        # Verifying the display status of the sales return receipt
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"

        # Save the SRR number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SRRNUM01]"
