#This test can only be executed with sage.
#The goal of this test is to verify that the user can add a second sales order to an existing sales shipment and post it.
@distribution
Feature: distribution-flow-adding-sales-order-to-shipment
    Scenario: Adding second line to a sales shipmentCreate sale invoice - Verify assignment of taxes
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "number" bound column in the table field with value "SH240014"
        And the user selects the row with text "SH240014" in the "number" bound column header of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        # Verifying the display status of the sales shipment
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to process"

        # Adding a second line of sales order to the sales shipment using Add lines from order button
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from orders" labelled business action button of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "SO240015" in the "Document" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        ##Stock allocation for the added line
        Then the user refreshes the screen
        And the user selects the "lines" bound table field on the main page
        When the user clicks the "Open column panel" labelled button of the table field
        Then the "Column settings" titled sidebar is displayed
        When searches for "Item description" in the lookup dialog on the sidebar
        And the user ticks the table column configuration with "Item description" name on the sidebar
        Then the table column configuration with name "Item description" on the sidebar is ticked
        And the user clicks the Close button of the sidebar
        And the user selects the row with text "working item2" in the "Item description" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal

        # Verifying if the stock is allocated and the allocated quantity
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "working item2" in the "Item description" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the value of the "Allocated quantity" labelled nested text field of the selected row in the table field is "15 each"

        #Confirm sales shipment
        And the user clicks the "confirm" labelled business action button on the main page
        And the user clicks the "confirm" button of the Confirm dialog
        Then a toast containing text "Status updated." is displayed
        And the user dismisses all the toasts

        # Verifying the display status of the sales shipment (on Ready to ship)
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to ship"

        # Posting sales shipment and verifying shipment status (on Shipped)
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "confirm" button of the Confirm dialog
        Then a toast containing text "The sales shipment was posted." is displayed
        And the user refreshes the screen
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Shipped"
