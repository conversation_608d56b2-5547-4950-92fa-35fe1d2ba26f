# The purpose of this test is to verify row action (accept all variances, post... ) on the purchase invoice page
# directly form navigation bar

@distribution
@Stack_Overflow
Feature: distribution-crud-purchase-invoice-row-actions

    Scenario: Purchase invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in fields on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "PI RowAction" in the text field
        And the user selects the "Supplier invoice reference" labelled text field on the main page
        And the user writes "test" in the text field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "200" in the numeric field
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "Service" in the reference field
        And the user selects "Service" in the reference field
        And the user selects the "Quantity in purchase unit *" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "PI RowAction"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PI RowAction"

    Scenario: Verify that the user can set dimensions in a purchase invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PI RowAction"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PI RowAction"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 10 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the "Apply to all lines" button of the Custom dialog

    Scenario: Verify that the user can accept variance
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "PI RowAction"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PI RowAction"
        And the user clicks the "Accept all variances" dropdown action of the selected row of the table field
        And the user clicks the "Accept" button of the Confirm dialog
        Then a toast containing text "Variance status updated." is displayed

    Scenario: Verify that the user can send for matching
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "PI RowAction"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PI RowAction"
        And the user clicks the "Send for matching" dropdown action of the selected row of the table field
        And the user selects the "to" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And a toast containing text "Email sent" is displayed

    Scenario: Verify that the user can post
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "PI RowAction"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PI RowAction"
        And the user clicks the "Post" dropdown action of the selected row of the table field
        And the user clicks the "Post" button of the Confirm dialog
        Then a toast containing text "The purchase invoice was posted." is displayed


    Scenario: Verify that the user can create credit memo
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "PI RowAction"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PI RowAction"
        And the user clicks the "Create credit memo" dropdown action of the selected row of the table field
        And the user selects the "Reason" labelled reference field on a modal
        And the user clicks the lookup button of the reference field
        And the user selects the "reasonCreditNote" bound table field on a modal
        And the user selects the row with text "Stock value correction" in the "name" labelled column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the value of the reference field is "Stock value correction"
        And the user selects the "Supplier document date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Total amount excluding tax" labelled numeric field on a modal
        And the user writes "200" in the numeric field
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast containing text "Purchase credit memo created." is displayed

    Scenario: Verify that the user can delete invoice
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the option menu of the table field is displayed
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "PIIVN202301"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "PIIVN202301"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
