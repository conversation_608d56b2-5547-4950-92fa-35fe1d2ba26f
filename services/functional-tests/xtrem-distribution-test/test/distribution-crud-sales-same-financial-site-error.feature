# Verify that a user cannot select two sites from the same financial site on the following sales documents: Sales order, Sales invoice
# The focus is for the user to get an error message when trying to add two sites linked to the same financial site on certain sales documents

@distribution
Feature: distribution-crud-sales-same-financial-site-error

    Scenario: 01 - Sales order creation
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 2" in the reference field
        And the user selects "Stock Tranfer Uk Site 2" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Pressure transducer" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Pressure transducer" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user writes "200" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 1 error in the following grid: Sold-to customer.
             This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.
            """

    Scenario: 02 - Sales invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "Bill-to customer " labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 2" in the reference field
        And the user selects "Stock Tranfer Uk Site 2" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Service" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Service" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user writes "200" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 1 error in the following grid: Bill-to customer.
             This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.
            """
