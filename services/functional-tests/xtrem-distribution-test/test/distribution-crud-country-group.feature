# The goal of this test is to Create, Update, Delete a country group
@distribution
Feature:  distribution-crud-country-group
    # Create, Update, Delete a ountry group
    Scenario: country group Creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/CountryGroup"
        Then the "Country groups" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "ON"
        Then the user turns the switch field "OFF"

        Then the user selects the "Name" labelled text field on the main page
        And the user writes "NewGroup" in the text field

        When the user selects the "ID" labelled text field on the main page
        Then the user writes "NewGroupCRUD" in the text field

        When the user selects the "Description" labelled text field on the main page
        Then the user writes "New group country" in the text field

        When the user selects the "Countries" labelled multi reference field on the main page
        Then the user writes "France" in the multi reference field
        And the user selects "France" in the multi reference field
        Then the user writes "Spain" in the multi reference field
        And the user selects "Spain" in the multi reference field

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Country group Update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/CountryGroup"
        Then the "Country groups" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "NewGroupCRUD" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "NewGroup" in the navigation panel
        And the user clicks the "first" navigation panel's row

        Then the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "OFF"
        Then the user turns the switch field "ON"

        And the user selects the "Description" labelled text field on the main page
        And the user writes "Update group country" in the text field

        When the user selects the "Countries" labelled multi reference field on the main page
        Then the value of the multi reference field is "Spain|France"

        And the user clears the multi reference field
        Then the user writes "Germany" in the multi reference field
        And the user selects "Germany" in the multi reference field

        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Country group Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/CountryGroup"
        Then the "Country groups" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "NewGroup" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
