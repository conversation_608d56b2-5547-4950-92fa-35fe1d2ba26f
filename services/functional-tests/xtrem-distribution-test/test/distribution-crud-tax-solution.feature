# The goal of this test is to <PERSON>reate, Update, Delete a tax solution
@distribution
Feature:  distribution-crud-tax-solution
    # Create, Update, Delete a tax solution
    Scenario: Tax solution Creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxSolution"
        Then the "Tax solutions" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "ON"
        Then the user turns the switch field "OFF"

        Then the user selects the "Name *" labelled text field on the main page
        And the user writes "NewTaxSolution" in the text field

        When the user selects the "ID *" labelled text field on the main page
        Then the user writes "TaxSolCRUD" in the text field

        When the user selects the "Description" labelled text field on the main page
        Then the user writes "New tax solution" in the text field


        And the user selects the "lines" bound table field on the main page
        And the user clicks the "addTaxSolutionLine" bound action of the table field

        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "Value Added Tax" in the "Tax category" labelled nested reference field of the selected row in the table field
        And the user selects "Value Added Tax" in the "Tax category" labelled nested field of the selected row in the table field

        And the user selects the row 1 of the table field
        And the user writes "1" in the "Display order" labelled nested reference field of the selected row in the table field
        And the user clicks the "Mandatory tax" labelled nested field of the selected row in the table field

        #https://jira.sage.com/browse/XT-52663
        #needed to tick the Mandatory tax checkbox in tax categories checkbox

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Tax solution Update

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxSolution"
        Then the "Tax solutions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TaxSolCRUD" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "NewTaxSolution" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "Description" labelled text field on the main page
        And the user writes "Update Tax solution" in the text field
        And the user presses Tab

        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Tax solution Deletion

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxSolution"
        Then the "Tax solutions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TaxSolCRUD" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "NewTaxSolution" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
