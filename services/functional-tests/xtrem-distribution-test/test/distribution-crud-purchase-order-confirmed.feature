# This test aims to check purchase order CRUD actions without the approval process
# (create P<PERSON>, add line when the PO still in draft, confirm the PO, verify you can still add a line,
# modify an existing line, add lines from requisitions and delete the purchase order).
# Focus on purchase order statuses and on toasts generated (Record created / Record updated / Status updated)
# Make sure that the user can process all these actions on a confirmed purchase order.

@distribution
Feature: distribution-crud-purchase-order-confirmed
    Scenario: 01 - Purchase order creation in status draft
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "purchasingSite" labelled reference field on the main page
        And the user writes "Site de Chavanod" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record created" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[NEWPONUM01]"

    Scenario: 02 - Adding line in purchase order in draft
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[NEWPONUM01]"
        And the user selects the row with text "[NEWPONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        When the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Pressure transmitter" in the "Item" labelled nested reference field of the selected row in the table field
        And the user writes "1" in the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field
        And the user writes "20" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"

    Scenario: 03 - Confirm purchase order and verify a line can still be added
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[NEWPONUM01]"
        And the user selects the row with text "[NEWPONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "Status updated" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "21" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: 04 - Modifying an existing line of confirmed purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[NEWPONUM01]"
        And the user selects the row with text "[NEWPONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "10 each" in the "Quantity in purchase unit" labelled column header of the table field
        And the user writes "11" in the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Cancel" button of the Message dialog
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: 05 - Adding line from purchase requisitions in confirmed purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[NEWPONUM01]"
        And the user selects the row with text "[NEWPONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "selectFromRequisition" bound business action button of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Purchase requisition" in the "Origin" labelled column header of the table field
        And the user writes "25" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 06 - Deleting a confirmed purchase order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[NEWPONUM01]"
        And the user selects the row with text "[NEWPONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And a toast containing text "Record deleted" is displayed
