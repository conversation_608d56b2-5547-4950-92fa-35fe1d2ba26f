#This test can only be executed with sage.
#The goal of this test is to verify that the user can create the purchase return from the purchase receipt
@distribution
Feature: distribution-flow-purchase-return-from-purchase-receipt
    Scenario: 01 - verify that the user can post a purchase receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "number" bound column in the table field with value "PR230022"
        And the user selects the row with text "PR230022" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Received"

    Scenario: 02 - verify that the user can create a purchase return from purchase receipt
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "number" bound column in the table field with value "PR230022"
        And the user selects the row with text "PR230022" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "createReturn" labelled business action button on the main page
        Then a toast containing text "Purchase returns created:" is displayed


        And the user waits 5 seconds

        # Refresh to update the main status of the Purchase receipt
        And the user refreshes the screen
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Returned"
        #Verify that the values on the line are correct

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "USItem1" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Closed"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "USItem1"
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "2 each"
        And the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "$ 20.00000"
        And the value of the "Total excluding tax" labelled nested numeric field of the selected row in the table field is "$ 40.00"
        And the user selects the row 1 of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "purchaseReturnLines" bound table field on the sidebar
        And the user selects the row with text "2.00" in the "returnedQuantity" bound column header of the table field
        ##Storing the value of the Purchase return
        Then the user stores the value of the "returnNumber" labelled nested text field of the selected row in the table field with the key "[ENV_PRNumber]"



    Scenario: 03 - Verify the Purchase return creation and correcteness
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReturn"
        Then the "Purchase returns" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "number" bound column in the table field with value "[ENV_PRNumber]"
        ########Using store
        And the user selects the row with text "[ENV_PRNumber]" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PRNumber]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "lines" bound table field on the main page
        ##Updating reason
        And the user selects the row 1 of the table field
        When the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Item" labelled reference field on the sidebar
        And the value of the reference field is "USItem1"
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        Then the value of the numeric field is "2"
        When selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the value of the numeric field is "20.00000"
        Then the user clicks the "Apply" button of the dialog on the sidebar
        When the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user clicks the "submitForApproval" labelled business action button on the main page
        And a toast containing text "Submitted for approval" is displayed
        And the user clicks the "approve" labelled business action button on the main page
        And the user clicks the "Accept" button of the Confirm dialog
        And a toast containing text "Approval status updated" is displayed
        ##Stock allocation
        And the user selects the "lines" bound table field on the main page
        And the user waits 3 seconds
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "save" labelled business action button on a modal
        And the user clicks the "post stock" labelled business action button on the main page
        And the user clicks the "Continue" button of the Confirm dialog
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Returned"
