# The purpose of this test is to verify creation, update, printing and deletion of the purchase order
# It also ensures Purchase Order can be created using a CSV file import

# Site : Swindon
# Supplier : Lyreco

@distribution
Feature: distribution-crud-purchase-order

    Scenario: 01 - Verify the user is able to create a Purchase order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # Fill in site reference field
        And the user selects the "purchasingSite" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        # Fill in Supplier reference field
        And the user selects the "supplier" labelled reference field on the main page
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field
        # Verify defaulted order date
        And the user selects the "Order date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        # Add a line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        # Fill in Qty on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        # select price tab
        And selects the "Price" labelled navigation anchor on the sidebar
        # Fill in Price on sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Discount" labelled numeric field on the sidebar
        And the user writes "2" in the numeric field
        And the user selects the "Charge" labelled numeric field on the sidebar
        And the user writes "2" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Creation
        Then a toast with text "Record created" is displayed
        # Save the PO number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[PONUM01]"

    Scenario: 02 - Verify the user is able to update a Purchase order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        # Update the Qty on sidebar
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        # Update the expected receipt date on the sidebar
        And the user selects the "Expected receipt date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "T+5"
        Then the value of the date field is a generated date with value "T+5"
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Click Save Crud Button on main page
        And the user clicks the "Save" labelled business action button on the main page
        # Verify Update
        Then a toast with text "Record updated" is displayed
        # Add Dimensions on the line
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "department" labelled reference field on a modal
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user selects the "channel" labelled reference field on a modal
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    # Update tax details on the line
    Scenario: 03 - Verify the user is able to update the tax details
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user filters the "name" labelled column in the table field with value "UK Purchase Goods Reduced Rate"
        And the user selects the row with text "UK Purchase Goods Reduced Rate" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        # Check the tax details are correct
        When the user selects the "Taxes" labelled table field on a modal
        And the value of the "Tax rate" labelled nested reference field of the selected row in the table field is "5.00 %"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "£ 5.00"
        And the value of the "Deductible amount" labelled nested reference field of the selected row in the table field is "£ 5.00"
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    # Add line from requisition
    Scenario: 04 - Verify the user is able add lines from a purchase requisition
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "selectFromRequisition" bound business action button of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        # 2 items with same name display here
        And the user selects the row with text "10 each" in the "Quantity to order" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        # Verify if the new line is added on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the value of the "Origin" labelled nested text field of the selected row in the table field is "Purchase requisition"
        # Wait so that any other toast disappears
        And the user waits for 5 seconds
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 05 - Verify the user can read the record of the purchase order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        #Searching Creation
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "number" bound column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Reading
        And the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[PONUM01]"
        When the user selects the "Purchasing site" labelled reference field on the main page
        Then the value of the reference field is "Swindon"
        When the user selects the "Supplier" labelled reference field on the main page
        Then the value of the reference field is "Lyreco"
        And the user selects the "Order date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the value of the "Gross price" labelled nested text field of the selected row in the table field is "£ 10.00000"
        And the value of the "Net price" labelled nested text field of the selected row in the table field is "£ 10.00000"
        And the value of the "Total excluding tax" labelled nested text field of the selected row in the table field is "£ 100.00"
        # Value of the total tax should still be 5, already verified in a scenario above
        And the value of the "Total tax" labelled nested text field of the selected row in the table field is "£ 5.00"
        And the value of the "Total including tax" labelled nested text field of the selected row in the table field is "£ 105.00"
        And the value of the "Expected receipt date" labelled nested date field of the selected row in the table field is a generated date with value "T+5"

    Scenario: 06 - Verify that the user can print a purchase order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Print" labelled button in the header
        # And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page
        Then a toast containing text "The purchase order was printed." is displayed

    Scenario: 07 - Verify that the "Projected stock" action is available when the item is stock managed
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the "Projected stock" dropdown action of the selected row in the table field is enabled
        And the user clicks the "Projected stock" dropdown action of the selected row of the table field
        And the text in the header of the dialog is "Projected stock"
        And the user selects the "Stock site" labelled reference field on a modal
        And the reference field is read-only
        And the user selects the "Item" labelled reference field on a modal
        And the reference field is read-only
        And the user selects the "Stock available" labelled numeric field on a modal
        And the numeric field is read-only
        And the user selects the "Frequency" labelled radio field on a modal
        And the value "day" of the radio field is selected
        Then the value "week" of the radio field is not selected
        And the user selects the value "week" in the radio field
        And the value "week" of the radio field is selected
        Then the value "day" of the radio field is not selected
        And the user clicks the "OK" button of the Confirm dialog
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Projected stock" dropdown action of the selected row of the table field
        And the user selects the "Frequency" labelled radio field on a modal
        And the value "day" of the radio field is selected

    Scenario: 08 - Verify that the "Projected stock" action is hidden when the item is not stock managed
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Freight charges" in the reference field
        And the user selects "Freight charges" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        When the user writes "1" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Freight charges" in the "Item" labelled column header of the table field
        And the "Projected stock" dropdown action of the selected row in the table field is hidden

    Scenario: 09 - Verify the user can delete the purchase order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        #Searching Creation
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[PONUM01]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion
        Then a toast containing text "Record deleted" is displayed

    Scenario: 10 - Validate creation of "Purchase Order" is possible using import templates
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportData"
        Then the "Data import" titled page is displayed
        When the user selects the "Select file" labelled file deposit field on the main page
        And the user adds the file "./import/46-Purchase-Order.csv" to the file deposit field
        And the user selects the "Insert" labelled checkbox field on the main page
        And the value of the checkbox field is "true"
        And the user selects the "Update" labelled checkbox field on the main page
        And the value of the checkbox field is "false"
        And the user selects the "Test import" labelled checkbox field on the main page
        And the value of the checkbox field is "false"
        And the user selects the "Continue and ignore the error" labelled checkbox field on the main page
        And the value of the checkbox field is "true"
        And the user selects the "Files to import" labelled table field on the main page
        And the user selects the row with text "46-Purchase-Order.csv" in the "File name" labelled column header of the table field
        And the user writes "PurchaseOrder" in the "Template" labelled nested reference field of the selected row in the table field
        And the user selects "PurchaseOrder" in the "Template" labelled nested field of the selected row in the table field
        And the user presses Tab
        And the user clicks the "Import" labelled business action button on the main page
        And the text in the body of the dialog is "Do you want to import the file?"
        And the user clicks the "OK" button of the Confirm dialog
        And a toast with text "46-Purchase-Order.csv has been submitted for processing." is displayed
        And the user dismisses all the toasts
        And selects the "Import results" labelled navigation anchor on the main page
        #Make the robot wait for the sync
        And the user waits 10 seconds
        And the user selects the "importResults" bound table field on the main page
        And the user clicks the "Refresh" labelled header action button of the table field
        And the user selects the row 1 of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"
        And the value of the "File name" labelled nested label field of the selected row in the table field is "46-Purchase-Order.csv"
        And the value of the "Rows processed" labelled nested label field of the selected row in the table field is "4"
        And the value of the "Number of errors" labelled nested label field of the selected row in the table field is "0"
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Purchasing site" labelled column in the table field with value "Site de Rumilly"
        ## there is no support for column filtering with T
        # And the user filters the "Order date" labelled column in the table field with value "T"
        And the user selects the row 1 of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "lines" bound table field on the main page
        And the value of the "Item" labelled nested label field of the selected row in the table field is "Stock valuation report STD - Item 2"
        And the user selects the row 2 of the table field
        And the value of the "Item" labelled nested label field of the selected row in the table field is "Raw material 4 - mass allocation"
        And the user selects the row 3 of the table field
        And the value of the "Item" labelled nested label field of the selected row in the table field is "Stock valuation report FIFO - Item 1"
        And the user selects the row 4 of the table field
        And the value of the "Item" labelled nested label field of the selected row in the table field is "Item test aloc transfer sales"
