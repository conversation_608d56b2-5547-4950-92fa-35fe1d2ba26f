#This test can only be executed with sage.
#The goal of this test is to verify that the user can create a Sales return receipt with serial numbers
@distribution
Feature: distribution-flow-sales-return-receipt-with-sn

    Scenario: Verify that the user can create a Sales return receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        ##Create
        When the user clicks the "create" labelled business action button on the main page
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And the user selects the "Ship-to customer *" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field
        And the user selects the "Number" labelled text field on the main page
        And the user writes "SalesRR_002" in the text field
        #Adding line by using Add liens from orders button
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add lines from return requests" labelled business action button of the table field
        And the user selects the "$applicationCodeLookup" bound table field on a modal
        And the user selects the row with text "ST240008" in the "Number" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Select" button of the Lookup dialog
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        ##Add stock
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Laptop device" in the "Item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" labelled table field on a modal
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row with text "3 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        ##Serial numbers allocation
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field
        And the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "SN004042" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user waits 20 seconds
        And the user selects "SN004042" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "3" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "ok" labelled business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify data of the record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "SalesRR_002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Verify the data
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "Site" labelled reference field on the main page
        And the value of the reference field is "TE Hampton"
        And the user selects the "Ship-to customer" labelled reference field on the main page
        And the value of the reference field is "MK Manufacturing"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Laptop device" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Origin" labelled nested text field of the selected row in the table field is "Return"
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Laptop device"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "3 each"

    Scenario: Verify that the user can post the Sales return receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "SalesRR_002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Post sales return receipt
        Then the user clicks the "Post stock" labelled business action button on the main page

    Scenario: Verify the document status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesReturnReceipt"
        Then the "Sales return receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "SalesRR_002" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        ##Verify status of document
        When the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Closed"
        ##Verify SN and stock status in stock detail
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Laptop device" in the "Item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" labelled table field on a modal
        And the user selects the row with text "3 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "Quantity in stock unit" labelled nested text field of the selected row in the table field is "3 each"
        And the value of the "Serial number" labelled nested text field of the selected row in the table field is "Assigned"
