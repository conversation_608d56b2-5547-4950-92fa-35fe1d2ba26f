# The goal of this test is to Create, Update, Delete a tax category
@distribution
Feature:  distribution-crud-tax-category
    # Create, Update, Delete an account
    Scenario: Tax category Creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxCategory"
        Then the "Tax categories" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the user selects the "Active" labelled switch field on the main page
        # And the switch field is set to "ON"
        Then the user turns the switch field "OFF"

        Then the user selects the "Name" labelled text field on the main page
        And the user writes "NewTaxCategory" in the text field

        When the user selects the "ID" labelled text field on the main page
        Then the user writes "TaxCatCRUD" in the text field

        When the user selects the "Description" labelled text field on the main page
        Then the user writes "New tax category" in the text field

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Tax category Update

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxCategory"
        Then the "Tax categories" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TaxCatCRUD" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "NewTaxCategory" in the navigation panel
        And the user clicks the "first" navigation panel's row

        Then the user selects the "Active" labelled switch field on the main page
        # And the switch field is set to "OFF"
        Then the user turns the switch field "ON"

        And the user selects the "Description" labelled text field on the main page
        And the user writes "Update Tax category" in the text field
        And the user presses Tab

        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Tax category Deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-tax/TaxCategory"
        Then the "Tax categories" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "NewTaxCategory" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
