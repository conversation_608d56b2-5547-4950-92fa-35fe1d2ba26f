# Verify that a user cannot select two sites from the same financial site on the following sales documents:
# Purchase requisition, Purchase order, Purchase invoice, Purchase credit memo
# The focus is for the user to get an error message when trying to add two sites linked to the same financial site on certain purchase documents

@distribution
Feature: distribution-crud-purchase-same-financial-site-error
    Scenario: 01 - Purchase requisition creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Purchasing site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the "Add new line" titled sidebar is displayed
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "200" in the numeric field
        And selects the "Information" labelled navigation anchor on the sidebar
        And the user selects the "supplierLink" bound link field on the sidebar
        And the user clicks in the link field
        And the user selects the "suppliers" bound table field on a modal
        And the user selects the row with text "Stock Tranfer Uk Site 2" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Continue" button of the Confirm dialog
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 1 error in the following grid: Lines.
            _id: -2 - Supplier: This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.
            """

    Scenario: 02 - Purchase order creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Purchasing site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 2" in the reference field
        And the user selects "Stock Tranfer Uk Site 2" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Pressure transducer" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Pressure transducer" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field
        And the user writes "200" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 1 error in the following grid: Supplier.
             This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.
            """

    Scenario: 03 - Purchase invoice creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 2" in the reference field
        And the user selects "Stock Tranfer Uk Site 2" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Service" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Service" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field
        And the user writes "200" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 1 error in the following grid: Bill-by supplier.
             This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.
            """

    Scenario: 04 - Purchase credit memo creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseCreditMemo"
        Then the "Purchase credit memos" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "Bill-by supplier" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 2" in the reference field
        And the user selects "Stock Tranfer Uk Site 2" in the reference field
        And the user selects the "Reason" labelled reference field on the main page
        And the user writes "Donations or free products" in the reference field
        And the user selects "Donations or free products" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Service" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Service" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field
        And the user writes "200" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            You have 1 error in the following grid: Bill-by supplier.
             This value is linked to a stock transfer document and cannot be used for this document. You need to select a different value.
            """
