@distribution
Feature: distribution-flow-sales-order-mass-allocation

    Scenario: 1 - Verify that user can manually alocate/dealocate stock
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page
        # Find and Select Document
        And the user filters the "Number" labelled column in the table field with value "SO240016"
        And the user selects the row with text "SO240016" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user clicks the "Continue" button of the Confirm dialog

        And the user selects the "Stock allocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "Stock allocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"

    Scenario: 2 - Mass allocation of stock
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrderMassAllocation"
        Then the "Sales order mass allocation" titled page is displayed

        When the user selects the "Action" labelled select field on the main page
        When the user clicks in the select field
        And the user selects "Allocation" in the select field

        And the user selects the "Company" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field

        And the user selects the "site" labelled reference field on the main page
        And the user writes "Chem. Austin" in the reference field
        And the user selects "Chem. Austin" in the reference field

        And the user selects the "From sold-to customer" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field

        And the user selects the "To sold-to customer" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field

        And the user selects the "Incoterms® rule" labelled reference field on the main page
        And the user writes "Carriage and Insurance Paid To" in the reference field
        And the user selects "Carriage and Insurance Paid To" in the reference field

        Then the user clicks the "Allocate" labelled business action button on the main page

        Then the dialog title is "Allocation request submitted"
        And the text in the body of the dialog is "The allocation request was submitted." on the main page
        And the user clicks the "OK" button of the dialog on the main page
        And the user waits for 5 seconds


    Scenario: 3 - Verify sales order status is allocatated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page

        And the user filters the "Number" labelled column in the table field with value "SO240016"
        And the user selects the row with text "SO240016" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field

        And the user filters the "Number" labelled column in the table field with value "SO240017"
        And the user selects the row with text "SO240017" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field

        And the user filters the "Number" labelled column in the table field with value "SO240018"
        And the user selects the row with text "SO240018" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field

        And the user filters the "Number" labelled column in the table field with value "SO240019"
        And the user selects the row with text "SO240019" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field

    Scenario: 4 - Mass deallocation of stock
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrderMassAllocation"
        Then the "Sales order mass allocation" titled page is displayed

        When the user selects the "Action" labelled select field on the main page
        When the user clicks in the select field
        And the user selects "Deallocation" in the select field

        And the user selects the "Company" labelled reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field

        And the user selects the "site" labelled reference field on the main page
        And the user writes "Chem. Austin" in the reference field
        And the user selects "Chem. Austin" in the reference field

        And the user selects the "From sold-to customer" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field

        And the user selects the "To sold-to customer" labelled reference field on the main page
        And the user writes "MK Manufacturing" in the reference field
        And the user selects "MK Manufacturing" in the reference field

        And the user selects the "Incoterms® rule" labelled reference field on the main page
        And the user writes "Carriage and Insurance Paid To" in the reference field
        And the user selects "Carriage and Insurance Paid To" in the reference field

        Then the user clicks the "Deallocate" labelled business action button on the main page

        # Needs to be changed to deallocation when https://jira.sage.com/browse/XT-78130 is fixed
        Then the dialog title is "Deallocation request submitted"
        And the text in the body of the dialog is "The deallocation request was submitted." on the main page
        And the user clicks the "OK" button of the dialog on the main page
        And the user waits for 5 seconds

    Scenario: 5 - Verify sales order status is not allocatated
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page

        And the user filters the "Number" labelled column in the table field with value "SO240016"
        And the user selects the row with text "SO240016" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field

        And the user filters the "Number" labelled column in the table field with value "SO240017"
        And the user selects the row with text "SO240017" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field

        And the user filters the "Number" labelled column in the table field with value "SO240018"
        And the user selects the row with text "SO240018" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field

        And the user filters the "Number" labelled column in the table field with value "SO240019"
        And the user selects the row with text "SO240019" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the user clicks the "Close record" icon in the header on the main page
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the remove all filters button in the table field
