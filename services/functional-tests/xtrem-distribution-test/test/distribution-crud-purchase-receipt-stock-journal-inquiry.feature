
# This test covers a new function in the kebab header of purchase receipt page, able to access "Stock journal inquiry" action.
# Notes: Make sure the purchase receipt document status is completed and the allocation is completed to be able to see the new action at the top kebab menu.

@distribution
Feature: distribution-crud-purchase-receipt-stock-journal-inquiry

    Scenario: 01 - Verify functionality for the "Stock journal inquiry" top kebab action
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user filters the "number" labelled column in the table field with value "PR240015"
        And the user selects the row with text "PR240015" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Purchase receipt PR240015" titled page is displayed
        And the user clicks the "Stock journal inquiry" labelled more actions button in the header
        And an info dialog appears on a full width modal
        And the dialog title is "Stock journal inquiry" on a full width modal
        And the user selects the "$navigationPanel" bound table field on a full width modal
        And the value of the option menu of the table field is "My selected data"
        And the user selects the row 1 of the table field
        And the value of the "Document line type" labelled nested text field of the selected row in the table field is "Purchase receipt line"
        And the value of the "Document" labelled nested link field of the selected row in the table field is "PR240015"
        And the user clicks the Close button of the dialog on a full width modal
        And the "Purchase receipt PR240015" titled page is displayed
