# The purpose of this test is to CRUD the purchase requisition page
# Purchasing Site: Entrepot De Saint Denis
# Items: Stock Item - Purchase_Stock_Item_001 - Default supplier BARRES - Supplier price
#        Service Item - Purchase_Service_Item_001 - Default supplier - Manual price
#        Non-Stock Item - Purchase_Nonstock_Item_001 - Non Default supplier - Supplier price
@distribution
Feature: distribution-crud-purchase-requisition

    Scenario: 01 - Purchase requisition creation - Main page
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Purchasing site
        And the user selects the "Purchasing site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de Saint Denis" in the reference field

        # Number
        And the user selects the "Number" labelled text field on the main page
        And the user writes "Purchase_Req_Test" in the text field


        # Requester
        And the user selects the "Requester" labelled reference field on the main page
        And the user writes "Persona, Admin" in the reference field
        And the user selects "Persona, Admin" in the reference field

        # Status Checks when in Draft Main Status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"

        # Step flow check
        When the user selects the "requisitionStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is current
        Then the status of the "Approve" item of the step-sequence is incomplete
        Then the status of the "Order" item of the step-sequence is incomplete



    Scenario: 02 - Purchase requisition creation - Adding a line using the sidebar
        # Add a line using sidebar
        When the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        Then the "Add new line" titled sidebar is displayed

        # Item name
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Purchase_Stock_Item_001" in the reference field
        And the user selects "Purchase_Stock_Item_001" in the reference field

        # Supplier

        And the user selects the "supplierLink" bound link field on the sidebar
        And the user clicks in the link field
        # Search for Supplier and Click on the row
        And the user selects the "Supplier" labelled table field on a modal
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        # Confirm this is the default supplier
        Then the value of the "Type" labelled nested text field of the selected row in the table field is "Default"
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        # Confirm it has been selected
        And the user selects the "selectedSupplier" bound reference field on a modal
        Then the value of the reference field is "BARRES"
        # Confirm dialog
        And the user clicks the "OK" button of the Custom dialog on the main page


        # Transaction currency
        And the user selects the "Transaction currency" labelled reference field on the sidebar
        Then the value of the reference field is "Euro"

        # Purchase unit - Defaulted value
        And the user selects the "Purchase unit" labelled reference field on the sidebar
        Then the value of the reference field is "Each"


        # Quantity in purchase unit
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user presses Enter


        # And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Apply and add new" button of the dialog on the sidebar

        # Add second item

        # Item name
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Purchase_Nonstock_Item_001" in the reference field
        And the user selects "Purchase_Nonstock_Item_001" in the reference field


        # Supplier

        And the user selects the "supplierLink" bound link field on the sidebar
        And the user clicks in the link field
        # Search for Supplier and Click on the row
        And the user selects the "Supplier" labelled table field on a modal
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        # Confirm this is the referenced non default supplier
        Then the value of the "Type" labelled nested text field of the selected row in the table field is "Referenced"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        # Confirm it has been selected
        And the user selects the "selectedSupplier" bound reference field on a modal
        Then the value of the reference field is "BARRES"
        # Confirm dialog
        And the user clicks the "OK" button of the Custom dialog on the main page


        # Transaction currency
        And the user selects the "Transaction currency" labelled reference field on the sidebar
        Then the value of the reference field is "Euro"

        # Purchase unit - Defaulted value
        And the user selects the "Purchase unit" labelled reference field on the sidebar
        Then the value of the reference field is "Each"


        # Quantity in purchase unit
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user presses Tab


        And the user clicks the "Apply" button of the dialog on the sidebar



    Scenario: 04 - Purchase requisition creation - Adding a line using the floating/phantom row
        # Select the table field
        When the user selects the "Lines" labelled table field on the main page
        # Add a line using floating/phantom row
        When the user adds a new table row to the table field
        #And the user selects the floating row with text "Draft" in the "Status" labelled column header of the table field
        # Item name
        # And the user clicks the "Item" labelled nested field of the floating row in the table field
        And the user selects the floating row of the table field
        And the user writes "Purchase_Service_Item_001" in the "Item" labelled nested reference field of the selected row in the table field
        And the user opens the lookup dialog in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects the "item" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Purchase_Service_Item_001"
        And the user selects the row with text "Purchase_Service_Item_001" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        # Set reference context
        When the user selects the "Lines" labelled table field on the main page

        # Transaction Currency
        And the user selects the floating row of the table field
        And the user clicks the "Transaction currency" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Transaction currency" labelled nested reference field of the selected row in the table field
        And the user selects the "currency" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Euro"
        And the user selects the row with text "Euro" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled table field on the main page

        # Quantity
        And the user selects the floating row of the table field
        And the user writes "12" in the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field

        # Gross price
        And the user writes "50" in the "Gross price" labelled nested numeric field of the selected row in the table field

        And the user presses Control+Enter

    Scenario: 05 - Purchase requisition creation - Saving the document

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Re confirm that it is still in Draft
        # Status Checks when in Draft Main Status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"

        # Store the number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PRQ_Number]"


    Scenario: 06 - Purchase Requisition Update - Update line item through sidebar
        # Open the PR document
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed

        When the user selects the "Purchase requisitions" labelled table field on the main page
        And the user filters the "Number" labelled column in the table field with value "[ENV_PRQ_Number]"
        And the user selects the row with text "[ENV_PRQ_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Find the line item
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Purchase_Nonstock_Item_001" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        # Update the Quantity
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "6" in the numeric field
        And the user presses Enter
        And the user stores the value of the numeric field with the key "[ENV_Updated_Quantity]"


        # Update the required date
        And the user selects the "Required date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "T+1"
        # Date field cannot be stored, variable remains empty
        # And the user stores the value of the date field with the key "[ENV_Updated_Date]"

        # Apply and Save the update
        And the user clicks the "Apply" button of the dialog on the sidebar
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Verify the Quantity change in line
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Purchase_Nonstock_Item_001" in the "Item" labelled column header of the table field
        # Hard coded "each" below. When there is ER completed for helper text storage then we can store the unit from the sidebar Purchase unit helper text and use below in concatanation.
        And the value of the "Quantity in purchase unit" labelled nested numeric field of the selected row in the table field is "[ENV_Updated_Quantity] each"
        # TODO: REFACTOR PRICE, SET DEFAULT via Data creation?
        And the value of the "Total excluding tax" labelled nested numeric field of the selected row in the table field is "€ 300.00"

        # Verify the Date change in sidebar
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Required date" labelled date field on the sidebar
        Then the value of the date field is a generated date with value "T+1"


    Scenario: 07 - Update line item through row edit
        # Open the PR document
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_PRQ_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Find the line item
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Purchase_Service_Item_001" in the "Item" labelled column header of the table field
        # Update the price
        And the user writes "100" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user stores the value of the "Gross price" labelled nested numeric field of the selected row in the table field with the key "[ENV_Updated_Price]"
        # Save the update
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user refreshes the screen
        # Verify the change
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Purchase_Service_Item_001" in the "Item" labelled column header of the table field

        And the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "€ 100.00000"
        And the value of the "Gross price" labelled nested numeric field of the selected row in the table field is "[ENV_Updated_Price]"
        And the value of the "Total excluding tax" labelled nested numeric field of the selected row in the table field is "€ 1,200.00"


        # Re confirm that it is still in Draft
        # Status Checks when in Draft Main Status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"



    Scenario: 08 - Purchase requisition - Delete line item through inline row action
        # Open the document
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "[ENV_PRQ_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Find row
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Purchase_Nonstock_Item_001" in the "Item" labelled column header of the table field
        # Delete row
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Continue" button of the Confirm dialog

        # Save
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Re confirm that it is still in Draft
        # Status Checks when in Draft Main Status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"


    Scenario: 09 - Purchase requisition - Delete document
        # Find and open the document
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-purchasing/PurchaseRequisition"
        Then the "Purchase requisitions" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_PRQ_Number]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Delete the document
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast with text "Record deleted" is displayed
