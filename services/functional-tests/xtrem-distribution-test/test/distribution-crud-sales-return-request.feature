# The purpose of this test is to CRUD the Sales return request page.
# Sales Site: Entrepot De Saint Denis
# Customer: ATSERMO
# Items: Stock Item - Sale_Stock_Item_001
#        Service Item - Sale_Service_Item_001
#        Non-Stock Item - Sale_Nonstock_Item_001
@distribution
Feature: distribution-crud-sales-return-request

    Scenario: Open and find the Sales shipment document and create return request from it
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        # Find the specific Sales shipment
        When the user selects the "Sales shipments" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SH230022"
        When the user selects the "Sales shipments" labelled table field on the main page
        And the user selects the row with text "SH230022" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Verify
        Then the "Sales shipment SH230022" titled page is displayed

        # Main display status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Invoiced"

        # Step flow check
        When the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is complete
        Then the status of the "Post" item of the step-sequence is complete
        Then the status of the "Invoice" item of the step-sequence is complete
        Then the status of the "Request return" item of the step-sequence is incomplete
        Then the status of the "Receive return" item of the step-sequence is incomplete

        # Indicator bar tile check
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "3"

        # Store the values

        # Site
        When the user selects the "Stock site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"
        # Customer
        When the user selects the "Ship-to customer" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Customer]"

        # Click on the request return business action button
        When the "Create return request" labelled business action button on the main page is visible
        And the user clicks the "Create return request" labelled business action button on the main page
        Then the dialog title is "Confirm creation"
        And the user clicks the "Create" button of the Confirm dialog

        And a toast with text "Sales return request created" is displayed

        # Retrieve new document number of the Sales return request item sidebar progress tab
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Stock_Item_001" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And selects the "Progress" labelled navigation anchor on the sidebar
        And the user selects the "toReturnRequestLines" bound table field on the sidebar
        And the user selects the row with text "Draft" in the "Return request status" labelled column header of the table field
        And the user stores the value of the "Return request number" labelled nested link field of the selected row in the table field with the key "[ENV_SRR_Number_01]"

    # Below step not redirecting page
    # And the user clicks the "Return request number" labelled nested field of the selected row in the table field


    Scenario: Verify the details of the Return request

        # Navigate to and open the SRR page
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SRR_Number_01]"
        And the user selects the row with text "[ENV_SRR_Number_01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Display main status
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"

        # Site
        When the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"

        # Customer
        When the user selects the "Sold-to customer" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Customer]"

        # Store the value of the Return request number
        When the user selects the "Number" labelled text field on the main page
        Then the value of the text field is "[ENV_SRR_Number_01]"

        # Step flow check
        When the user selects the "returnRequestStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is current
        Then the status of the "Approve" item of the step-sequence is incomplete
        Then the status of the "Receive" item of the step-sequence is incomplete
        Then the status of the "Credit" item of the step-sequence is incomplete

        # Indicator tile check = 3 Items
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "3"

        # Footer Business action button checks
        And the "Submit for approval" labelled business action button on the main page is visible
        And the "Close request" labelled business action button on the main page is visible



    Scenario: Update the Return type of the Return request - Required for Credit memo Scenario below

        When selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Return type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user writes "Credit memo" in the dropdown-list field
        And the user selects "Credit memo" in the dropdown-list field
        And the value of the dropdown-list field is "Credit memo"

        # Save the update
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Update the return request through sidebar
        # Move to Lines tab
        When selects the "Lines" labelled navigation anchor on the main page
        # Select the table and find row
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        # Reason
        And the user selects the "Reason" labelled reference field on the sidebar
        And the user writes "Other reason" in the reference field
        And the user selects "Other reason" in the reference field
        And the user stores the value of the reference field with the key "[ENV_Updated_Reason_01]"

        # Apply and Save the update
        And the user clicks the "Apply" button of the dialog on the sidebar
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Verify the update
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        And the user selects the "Reason" labelled reference field on the sidebar
        Then the value of the reference field is "[ENV_Updated_Reason_01]"

        And the user clicks the "Cancel" button of the dialog on the sidebar


    Scenario: Update the return request through inline
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Stock_Item_001" in the "Item" labelled column header of the table field

        # Update Reason
        And the user clicks the "Reason" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Reason" labelled nested reference field of the selected row in the table field
        And the user selects the "reason" bound table field on a modal
        And the user filters the "Name" labelled column in the table field with value "Incorrect item sent"
        And the user selects the row with text "Incorrect item sent" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        # Set reference context again
        When the user selects the "Lines" labelled table field on the main page

        And the user writes "Incorrect item sent" in the "Reason" labelled nested reference field of the selected row in the table field
        And the user selects "Incorrect item sent" in the "Reason" labelled nested field of the selected row in the table field
        And the user stores the value of the "Reason" labelled nested reference field of the selected row in the table field with the key "[ENV_Updated_Reason_02]"

        # Update Dimension
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Department" labelled reference field on a modal
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        And the user clicks the "OK" button of the Custom dialog

        # Save the update
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Verify the update
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Stock_Item_001" in the "Item" labelled column header of the table field
        Then the value of the "Reason" labelled nested reference field of the selected row in the table field is "[ENV_Updated_Reason_02]"



    Scenario: Delete a line item from the Sales return request
        # Find row
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field
        # Delete row
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Continue" button of the Confirm dialog
        # Save
        Then the "Save" labelled business action button on the main page is visible
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Indicator tile check = 2 Items
        When the user selects the "NUMBER OF ITEMS" labelled tile count field on the main page
        Then the value of the tile count field is "2"

    Scenario: Approve the Return request
        When the user dismisses all the toasts
        When the "Submit for approval" labelled business action button on the main page is visible
        And the user clicks the "Submit for approval" labelled business action button on the main page

        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field

        And the user clicks the "Send" button of the Custom dialog
        # Below does not work due to https://jira.sage.com/browse/XT-53998 possibly
        And the user clicks the "Send" button of the Confirm dialog
        # TODO: Workaround until above delivered
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Enter


        Then a toast containing text "Email sent" is displayed

        # Refresh page
        When the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SRR_Number_01]"
        And the user selects the row with text "[ENV_SRR_Number_01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Main display status check
        And the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Pending approval"

        # Step flow check
        When the user selects the "returnRequestStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Approve" item of the step-sequence is current
        Then the status of the "Receive" item of the step-sequence is incomplete
        Then the status of the "Credit" item of the step-sequence is incomplete

        And the user clicks the "Approve" labelled business action button on the main page
        And the user clicks the "Approve" button of the Confirm dialog
        And a toast with text "The sales return request has been approved." is displayed

        # Refresh page
        When the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SRR_Number_01]"
        And the user selects the row with text "[ENV_SRR_Number_01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Verify status
        And the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Approved"

        # Step flow check
        When the user selects the "returnRequestStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Approve" item of the step-sequence is complete
        Then the status of the "Receive" item of the step-sequence is incomplete
        Then the status of the "Credit" item of the step-sequence is incomplete

        # Footer button check
        Then the "Create credit memo" labelled business action button on the main page is visible
        Then the "Close request" labelled business action button on the main page is visible

    Scenario: Create a Credit memo from the Return request
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "[ENV_SRR_Number_01]"
        And the user selects the row with text "[ENV_SRR_Number_01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        When the user clicks the "Create credit memo" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        Then a toast with text "Sales credit memo created" is displayed

        # Wait for page to load
        And the user waits 5 seconds
        When the user selects the "Credit memo date" labelled date field on the main page
        # T = Current date, because it was created above just now
        Then the value of the date field is a generated date with value "T"


    Scenario: Find another Return request and Reject it
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "ST230008"
        And the user selects the row with text "ST230008" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        # Submit for approval
        When the "Submit for approval" labelled business action button on the main page is visible
        And the user clicks the "Submit for approval" labelled business action button on the main page

        And the user selects the "To" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field

        And the user clicks the "Send" button of the Custom dialog
        # Below does not work due to https://jira.sage.com/browse/XT-53998 possibly
        And the user clicks the "Send" button of the Confirm dialog
        # TODO: Workaround until above delivered
        And the user presses Tab
        And the user presses Tab
        And the user presses Tab
        And the user presses Enter

        Then a toast containing text "Email sent" is displayed

        # Refresh page
        When the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "ST230008"
        And the user selects the row with text "ST230008" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Reject the SRR
        And the user clicks the "Reject" labelled business action button on the main page
        And the user clicks the "Reject" button of the Confirm dialog
        And a toast with text "The sales return request has been rejected." is displayed

        # Refresh page
        When the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "ST230008"
        And the user selects the row with text "ST230008" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Check status
        And the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Rejected"

        # Step flow check
        When the user selects the "returnRequestStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Approve" item of the step-sequence is complete
        Then the status of the "Receive" item of the step-sequence is incomplete
        Then the status of the "Credit" item of the step-sequence is incomplete

        # Line item check - Status must be closed
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Sale_Nonstock_Item_001" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Closed"

        # Footer button check
        Then the "Credit" labelled business action button on the main page is hidden
        Then the "Close request" labelled business action button on the main page is hidden
        Then the "Approve" labelled business action button on the main page is hidden
        Then the "Reject" labelled business action button on the main page is hidden
        Then the "Send for approval" labelled business action button on the main page is hidden





    Scenario: Delete a Return request
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-sales/SalesReturnRequest"
        Then the "Sales return requests" titled page is displayed
        When the user selects the "Sales return requests" labelled table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "ST230011"
        And the user selects the row with text "ST230011" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        # Display main status check
        When the user selects the "displayStatus" bound label field on the main page
        Then the value of the label field is "Draft"

        # Delete the document
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast with text "Record deleted" is displayed
