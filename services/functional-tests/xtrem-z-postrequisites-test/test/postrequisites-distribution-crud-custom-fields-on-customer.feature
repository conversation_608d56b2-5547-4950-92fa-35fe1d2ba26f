#The purpose of this test is to do crud operations using custom fields on customer

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-crud-custom-fields-on-customer

    Scenario: 01 - Create custom text field
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Customer" in the reference field
        And the user selects "Customer" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "CF CRUD Text Customer" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "cfCrudTextCustomer" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Text field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        # "Page" option is prepopulated by default
        And the user selects "Navigation bar" in the multi dropdown field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Text helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Text info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Text warning message" in the text field
        And the user selects the "Prefix" labelled text field on a modal
        And the user writes "<" in the text field
        And the user selects the "Postfix" labelled text field on a modal
        And the user writes ">" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Minimum length" labelled numeric field on a modal
        And the user writes "3" in the numeric field
        And the user selects the "Maximum length" labelled numeric field on a modal
        And the user writes "20" in the numeric field
        And the user clicks the "Finish" labelled business action button on a modal


    Scenario: 02 - Check the min limit of the custom text field
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        And the user writes "aa" in the text field
        And the user presses Tab
        Then the "Minimum length is 3" validation error message of the text field is displayed

    Scenario: 03 - Check the max limit of the custom text field
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        And the user writes "Too long to handle because we have a max limit" in the text field
        And the user presses Tab
        Then the "Maximum length is 20" validation error message of the text field is displayed

    Scenario: 04 - Enter valid data in the custom text field, save record, check if data is displayed on detail record and main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        And the user writes "From ATSERMO" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Check field value on the record details page
        And the user refreshes the screen
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        And the value of the text field is "From ATSERMO"
        # Check field value on the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "CF CRUD Text Customer" labelled column in the table field with value "From ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        # Make sure no other customer is affected
        And the user filters the "Name" labelled column in the table field with value "MRP first customer"
        When the user selects the "$navigationPanel" bound table field on the main page
        And the table field is empty

    Scenario: 05 - Enter data on second customer, check if data is saved, updated and removed correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "MRP first customer"
        And the user selects the row with text "MRP first customer" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        And the user writes "From MRP" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Check if data is saved correctly
        And the user refreshes the screen
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        And the value of the text field is "From MRP"
        # Update custom field data
        And the user writes "From MRP updated" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Check if data is updated correctly
        And the user refreshes the screen
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        And the value of the text field is "From MRP updated"
        # Remove value, save and check if correct
        And the user clears the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user refreshes the screen
        Given the user selects the "CF CRUD Text Customer" labelled text field on the main page
        Then the value of the text field is ""

    Scenario: 06 - Cleanup - delete the custom field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "Customer"
        And the user selects the row with text "cfCrudTextCustomer" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
