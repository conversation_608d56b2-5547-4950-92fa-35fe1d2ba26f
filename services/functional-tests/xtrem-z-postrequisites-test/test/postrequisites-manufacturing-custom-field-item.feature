#The purpose of this test is to check item update after adding custom fields

@postrequisites
@custom_fields
Feature: postrequisites-manufacturing-custom-field-item

    Scenario: 01 - Create custom checkbox field on item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "name" bound column in the table field with value "Apple Juice"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field

        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Item" in the reference field
        And the user selects "Item" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Checkbox custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customCheckboxITEM" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Checkbox helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Checkbox info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Checkbox warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Item Apple Juice" titled page is displayed
        Then the user selects the "Checkbox custom field" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the value of the checkbox field is "true"

    Scenario: 02 - Verify user add new site to item sites
        # SITES TAB SECTION OK
        And selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        And the user selects the "item" bound reference field on the sidebar
        Then the value of the reference field is "Apple Juice"
        ## reference field ##
        When the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Chem. Chicago" in the reference field
        And the user selects "Chem. Chicago" in the reference field
        Then the value of the reference field is "Chem. Chicago"
        ## General tab##
        And the user selects the "Specific item tax group" labelled checkbox field on the sidebar
        And the user clicks in the checkbox field
        When the user selects the "Item tax group" labelled reference field on the sidebar
        And the user writes "Goods, standard VAT" in the reference field
        And the user selects "Goods, standard VAT" in the reference field
        ## Replenishment tab##
        And selects the "Replenishment" labelled navigation anchor on the sidebar
        And the user selects the "Replenishment method" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "By reorder point" in the dropdown-list field
        Then the value of the dropdown-list field is "By reorder point"
        And the user selects the "Preferred process" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Purchasing" in the dropdown-list field
        Then the value of the dropdown-list field is "Purchasing"
        And the user selects the "Production lead time" labelled numeric field on the sidebar
        And the user writes "8" in the numeric field
        And the user selects the "Purchase lead time" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Safety stock" labelled numeric field on the sidebar
        And the user writes "500" in the numeric field
        And the user selects the "Reorder point" labelled numeric field on the sidebar
        And the user writes "200" in the numeric field
        And the user selects the "Economic order quantity" labelled numeric field on the sidebar
        And the user writes "14" in the numeric field
        And the user selects the "Batch quantity" labelled numeric field on the sidebar
        And the user writes "7" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: 03 - Verify user add new supplier to item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "name" bound column in the table field with value "Apple Juice"
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        # SITES TAB SECTION OK
        And selects the "Suppliers" labelled navigation anchor on the main page
        And the user selects the "suppliers" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        ## reference field ##
        When the user selects the "Supplier" labelled reference field on the sidebar
        And the user writes "BARRES" in the reference field
        And the user selects "BARRES" in the reference field
        Then the value of the reference field is "BARRES"

        When the user selects the "Purchase unit" labelled reference field on the sidebar
        And the user writes "Each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

    Scenario: Verify item creation with custom field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # HEADER#
        And the user selects the "Name" labelled text field on the main page
        And the user writes "ItemCustomField" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "ItemCustomField" in the text field

        When the user selects the "Purchased" labelled switch field on the main page
        Then the user turns the switch field "ON"

        When the user selects the "Manufactured" labelled switch field on the main page
        Then the user turns the switch field "ON"

        When the user selects the "Sold" labelled switch field on the main page
        Then the user turns the switch field "ON"

        # INFORMATION TAB SECTION #
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "My item #1" in the text area field

        When selects the "Management" labelled navigation anchor on the main page
        When the user selects the "Lot management" labelled dropdown-list field on the main page
        Then the user clicks in the dropdown-list field
        And the user selects "Lot" in the dropdown-list field
        Then the value of the dropdown-list field is "Lot"

        When the user selects the "Expiration date management" labelled checkbox field on the main page
        Then the user ticks the checkbox field

        And the user selects the "Lot sequence number" labelled reference field on the main page
        And the user writes "MATLOT" in the reference field
        And the user selects "Material lot number" in the reference field
        Then the value of the reference field is "Material lot number"

        When the user selects the "serialNumberManagement" bound dropdown-list field on the main page
        Then the user clicks in the dropdown-list field
        And the user selects "Managed" in the dropdown-list field
        Then the value of the dropdown-list field is "Managed"

        And the user selects the "Serial number sequence" labelled reference field on the main page
        And the user writes "ItemSerial" in the reference field
        And the user selects "Item serial number" in the reference field
        Then the value of the reference field is "Item serial number"

        # UNITS TAB SECTION #
        When selects the "Units" labelled navigation anchor on the main page
        And the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        And the user selects the "Purchase unit" labelled reference field on the main page
        And the user writes "Each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        And the user selects the "Sales unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # SITES TAB SECTION #
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        Then the "Item-sites" titled sidebar is displayed

        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field

        When the user selects the "Valuation method" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "Average unit cost" in the dropdown-list field
        Then the value of the dropdown-list field is "Average unit cost"

        When selects the "Replenishment" labelled navigation anchor on the sidebar

        When the user selects the "Replenishment method" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "By MRP" in the dropdown-list field
        Then the value of the dropdown-list field is "By MRP"

        When the user selects the "Preferred process" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "Purchasing" in the dropdown-list field
        Then the value of the dropdown-list field is "Purchasing"

        And the user selects the "Purchase lead time" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field

        And the user selects the "Safety stock" labelled numeric field on the sidebar
        And the user writes "50" in the numeric field

        And the user selects the "Batch quantity" labelled numeric field on the sidebar
        And the user writes "30" in the numeric field

        And the user selects the "Economic order quantity" labelled numeric field on the sidebar
        And the user writes "60" in the numeric field

        # When the user clicks the "Cancel" button of the dialog on the sidebar
        When the user clicks the "OK" labelled business action button on the sidebar
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
