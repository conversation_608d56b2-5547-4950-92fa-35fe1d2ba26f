#The purpose of this test is to verify that the user can create and interact with the newly created cutom fields on a customer

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-flow-customer-custom-field-user-interaction
    Scenario: 01 - Create and interact with custom checkbox field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Customer" in the reference field
        And the user selects "Customer" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Checkbox custom field customer" in the text field
        # Use the default generated technical name
        And the user selects the "Technical name *" labelled text field on a modal
        And the user clicks in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Checkbox helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Checkbox info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Checkbox warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Customer ATSERMO" titled page is displayed
        Then the user selects the "Checkbox custom field customer" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the value of the checkbox field is "true"

    Scenario: 02 - Create and interact with custom date field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Customer" in the reference field
        And the user selects "Customer" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Date custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customDateCustomer" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Date" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Date helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Date info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Date warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Customer ATSERMO" titled page is displayed
        Then the user selects the "Date custom field" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

    Scenario: 03 - Create and interact with custom numeric field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Customer" in the reference field
        And the user selects "Customer" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Numeric custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customNumericCustomer" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Numeric field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        And the user selects "Navigation bar" in the multi dropdown field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Numeric helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Numeric info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Numeric warning message" in the text field
        And the user selects the "Prefix" labelled text field on a modal
        And the user writes "<" in the text field
        And the user selects the "Postfix" labelled text field on a modal
        And the user writes ">" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Scale" labelled numeric field on a modal
        And the user writes "4" in the numeric field
        And the user selects the "Minimum value" labelled numeric field on a modal
        And the user writes "3" in the numeric field
        And the user selects the "Maximum value" labelled numeric field on a modal
        And the user writes "13" in the numeric field
        And the user clicks the "Finish" labelled business action button on a modal

    Scenario: 04 - Check if the field appears on the navigation bar
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Numeric custom field" labelled column in the table field with value "1"

    Scenario: 05 - Create and interact with custom switch field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Customer" in the reference field
        And the user selects "Customer" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Switch custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customSwitchCustomer" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "On/Off switch" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        And the user selects "Navigation bar" in the multi dropdown field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Switch helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Switch info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Switch warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Customer ATSERMO" titled page is displayed
        Then the user selects the "Switch custom field" labelled switch field on the main page
        And the user turns the switch field "ON"
        And the user turns the switch field "OFF"
        Then the switch field is set to "OFF"

    Scenario: 06 - Create and interact with custom text field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Customer" in the reference field
        And the user selects "Customer" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Text custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customTextCustomer" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Text field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        And the user selects "Lookup" in the multi dropdown field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Text helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Text info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Text warning message" in the text field
        And the user selects the "Prefix" labelled text field on a modal
        And the user writes "<" in the text field
        And the user selects the "Postfix" labelled text field on a modal
        And the user writes ">" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Minimum length" labelled numeric field on a modal
        And the user writes "3" in the numeric field
        And the user selects the "Maximum length" labelled numeric field on a modal
        And the user writes "11" in the numeric field
        And the user clicks the "Finish" labelled business action button on a modal

    Scenario: 07 - Check if the field appears on the lookup
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        And the user selects "UK LIMITED" in the reference field
        And the user selects the "Sold-to customer *" labelled reference field on the main page
        When the user clicks the lookup button of the reference field
        And the user selects the "soldToCustomer" bound table field on a modal
        And the user clicks the "Text custom field" labelled column of the table field

    Scenario: 08 - Cleanup - delete custom fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "Customer"
        And the user selects the row with text "customNumericCustomer" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "checkboxCustomFieldCustomer" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "customDateCustomer" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "customSwitchCustomer" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "customTextCustomer" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
