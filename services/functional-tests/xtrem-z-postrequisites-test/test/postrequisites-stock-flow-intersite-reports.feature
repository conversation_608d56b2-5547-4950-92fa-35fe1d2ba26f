# This test aims to confirm the information on the two reports generated on the Stock transfer shipment page
# The focus will be on verifying the correct information is printed on the Pick list and Packing slip reports
# The two reports are both printed from the Stock transfer shipment page and by using the print button.
# The only difference is the status.
# When the status is 'Ready to ship' the print action results in the 'Packing slip'.
# When the status is 'Ready to process' the print action results in the 'Pick list'.

@inventory
Feature: postrequisites-stock-flow-intersite-reports

    Scenario: 01 - Allocate stock on the stock transfer shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferShipment"
        Then the "Stock transfer shipments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TS250006" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_ST_SHIPMENT]"
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item for reports" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "stockAllocation" labelled table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item for reports" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"

    Scenario: 02 - Verify the Pick List report (shipment report) can be printed and the information verified (shipment in 'Ready to process')
        And the user clicks the "print" labelled button in the header
        And the user waits 5 seconds
        And the dialog title is "Print document"
        And the user stores the file name from the preview toolbar with the key "[ENV_PL_PDF_NAME]"
        And the user clicks the "Download" action in the preview toolbar
        And the user clicks the Close button of the dialog on the main page
        And a toast containing text "The stock transfer shipment was printed." is displayed
        And the user dismisses all the toasts
        And the user renames the file "pick-list-report-[ENV_ST_SHIPMENT].pdf" with name containing "[ENV_PL_PDF_NAME]"
        And the user sets the pdf vertical alignment to bottom
        And the user reads the "pick-list-report-[ENV_ST_SHIPMENT].pdf" pdf file
        # @todo create ER to be able to read dinamically the Date at the bottom of the page
        Then the user verifies the pdf report contains
            """
            Stock transfer shipment pick list
            Stock transfer shipment: TS250006
            Shipping date: 04/18/2025
            Receiving site id: 61
            Delivery mode: Air freight
            Ship-to address
            Stock Tranfer DE Site 2
            century roard
            abc
            Germany
            Item
            ITEM_TEST_REPORTS_01
            Description
            Item for report testing
            with decimals
            Stock transfer order
            TO250007
            Quantity to ship in stock unit
            1.42 Liter
            Allocated
            quantity
            1.42
            Page 1 of 1
            """

    Scenario: 03 - Verify the user can confirm the shipment
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And a toast containing text "The stock transfer shipment is confirmed and set to 'Ready to ship'" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to ship"
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item for reports" in the "Item" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Ready to ship"
        And the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        And the status of the "Create" item of the step-sequence is complete
        And the status of the "Confirm" item of the step-sequence is complete
        And the status of the "Post" item of the step-sequence is current

    Scenario: 04 - Verify the Packing Slip report (shipment report) can be printed and the information verified (shipment in 'Ready to ship')
        And the user clicks the "Print" labelled button in the header
        And the user waits 5 seconds
        And the dialog title is "Print document"
        And the user stores the file name from the preview toolbar with the key "[ENV_PS_PDF_NAME]"
        And the user clicks the "Download" action in the preview toolbar
        And the user clicks the Close button of the dialog on the main page
        And a toast containing text "The stock transfer shipment was printed." is displayed
        And the user dismisses all the toasts
        And the user renames the file "packing-slip-report-[ENV_ST_SHIPMENT].pdf" with name containing "[ENV_PS_PDF_NAME]"
        And the user sets the pdf vertical alignment to bottom
        And the user reads the "packing-slip-report-[ENV_ST_SHIPMENT].pdf" pdf file
        # @todo create ER to be able to read dinamically the Date at the bottom of the page
        Then the user verifies the pdf report contains
            """
            Stock transfer packing slip
            Stock Tranfer DE Site 1
            century roard
            xyz
            Germany
            Tax id number: 965-2345678
            Number: TS250006
            Shipping date: 04/18/2025
            Ship-to address
            Stock Tranfer DE Site 2
            century roard
            abc
            Germany
            Delivery date Delivery mode
            04/18/2025 Air
            Order number Item Description Unit Quantity
            TO250007 ITEM_TEST_REPORTS_01 Item for report testing with decimals Liter 1.42
            DE Automotive GmbH
            Am Sandfeld 15a Karlsruhe Germany
            """
