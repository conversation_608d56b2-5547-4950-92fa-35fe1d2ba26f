#The purpose of this test is to verify that the user can create and interact with the newly created cutom fields on a purchase order

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-flow-purchase-order-custom-field-user-interaction-on-page-display

    Scenario: 01 - Create and interact with custom checkbox field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PO240006"
        And the user selects the row with text "PO240006" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Purchase order" in the reference field
        And the user selects "Purchase order" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Checkbox custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customCheckboxPOPD" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Number" in the reference field
        And the user selects "Number" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Checkbox helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Checkbox info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Checkbox warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Purchase order PO240006" titled page is displayed
        Then the user selects the "Checkbox custom field" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the value of the checkbox field is "true"

    Scenario: 02 - Create and interact with custom date field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PO240006"
        And the user selects the row with text "PO240006" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Purchase order" in the reference field
        And the user selects "Purchase order" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Date custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customDatePOPD" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Date" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Number" in the reference field
        And the user selects "Number" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Date helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Date info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Date warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Purchase order PO240006" titled page is displayed
        Then the user selects the "Date custom field" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

    Scenario: 03 - Create and interact with custom numeric field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PO240006"
        And the user selects the row with text "PO240006" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Purchase order" in the reference field
        And the user selects "Purchase order" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Numeric custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customNumericPOPD" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Numeric field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Number" in the reference field
        And the user selects "Number" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Numeric helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Numeric info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Numeric warning message" in the text field
        And the user selects the "Prefix" labelled text field on a modal
        And the user writes "<" in the text field
        And the user selects the "Postfix" labelled text field on a modal
        And the user writes ">" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Scale" labelled numeric field on a modal
        And the user writes "4" in the numeric field
        And the user selects the "Minimum value" labelled numeric field on a modal
        And the user writes "3" in the numeric field
        And the user selects the "Maximum value" labelled numeric field on a modal
        And the user writes "13" in the numeric field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Purchase order PO240006" titled page is displayed
        And the user selects the "Numeric custom field" labelled numeric field on the main page
        And the user writes "11" in the numeric field
        Then the value of the numeric field is "11.0000"

    Scenario: 04 - Create and interact with custom switch field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PO240006"
        And the user selects the row with text "PO240006" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Purchase order" in the reference field
        And the user selects "Purchase order" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Switch custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customSwitchPOPD" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "On/Off switch" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Number" in the reference field
        And the user selects "Number" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Switch helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Switch info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Switch warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Purchase order PO240006" titled page is displayed
        Then the user selects the "Switch custom field" labelled switch field on the main page
        And the user turns the switch field "ON"
        And the user turns the switch field "OFF"
        Then the switch field is set to "OFF"

    Scenario: 05 - Create and interact with custom text field
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "PO240006"
        And the user selects the row with text "PO240006" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Purchase order" in the reference field
        And the user selects "Purchase order" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Text custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customTextPOPD" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Text field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Number" in the reference field
        And the user selects "Number" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Text helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Text info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Text warning message" in the text field
        And the user selects the "Prefix" labelled text field on a modal
        And the user writes "<" in the text field
        And the user selects the "Postfix" labelled text field on a modal
        And the user writes ">" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Minimum length" labelled numeric field on a modal
        And the user writes "3" in the numeric field
        And the user selects the "Maximum length" labelled numeric field on a modal
        And the user writes "11" in the numeric field
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Purchase order PO240006" titled page is displayed
        And the user selects the "Text custom field" labelled text field on the main page
        And the user writes "test" in the text field
        Then the value of the text field is "test"

    # # bug - uncomment and refine when XT-77463 is fixed
    # Scenario: Create and interact with custom drop-down list field
    # Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
    # Then the "Purchase orders" titled page is displayed
    # When the user selects the "$navigationPanel" bound table field on the main page
    # And the user filters the "Number" labelled column in the table field with value "PO240006"
    # And the user selects the row with text "PO240006" in the "number" labelled column header of the table field
    # And the user clicks the "number" labelled nested field of the selected row in the table field
    # And the user clicks the "Create field" labelled more actions button in the header
    # And the user selects the "Record type" labelled reference field on a modal
    # And the user writes "Purchase order" in the reference field
    # And the user selects "Purchase order" in the reference field
    # And the user clicks the "Next" labelled business action button on a modal
    # And the user selects the "Field label *" labelled text field on a modal
    # And the user writes "Drop-down list custom field" in the text field
    # And the user selects the "Technical name *" labelled text field on a modal
    # And the user writes "customDropdownListPOPD" in the text field
    # And the user selects the "Field type *" labelled dropdown-list field on a modal
    # And the user clicks in the dropdown-list field
    # And the user selects "Drop-down list" in the dropdown-list field
    # And the user clicks the "Next" labelled business action button on a modal
    # And the user selects the "Anchor field *" labelled reference field on a modal
    # And the user writes "Number" in the reference field
    # And the user selects "Number" in the reference field
    # And the user clicks the "Next" labelled business action button on a modal
    # And the user selects the "Helper text" labelled text field on a modal
    # And the user writes "Drop-down list helper text" in the text field
    # And the user selects the "Info message" labelled text field on a modal
    # And the user writes "Drop-down list info message" in the text field
    # And the user selects the "Warning message" labelled text field on a modal
    # And the user writes "Drop-down list warning message" in the text field
    # And the user clicks the "Next" labelled business action button on a modal

    Scenario: 06 - Cleanup - delete custom fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "PurchaseOrder"
        And the user selects the row with text "customNumericPOPD" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "customCheckboxPOPD" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "customDatePOPD" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "customSwitchPOPD" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "customTextPOPD" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
