#The purpose of this test is to do crud operations using custom fields on item

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-crud-custom-fields-on-item
    Scenario: 01 - Create custom checkbox field
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC Item 1 name"
        And the user selects the row with text "LC Item 1 name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Item" in the reference field
        And the user selects "Item" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "CF CRUD Checkbox Item" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user clicks in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Type" in the reference field
        And the user selects "Type" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        # "Page" option is prepopulated by default
        And the user selects "Navigation bar" in the multi dropdown field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Checkbox helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Checkbox info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Checkbox warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal

    Scenario: 02 - Tick the custom checkbox field, save and check if data is correct on detail page and main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC Item 1 name"
        And the user selects the row with text "LC Item 1 name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Item LC Item 1 name" titled page is displayed
        Then the user selects the "CF CRUD Checkbox Item" labelled checkbox field on the main page
        And the user ticks the checkbox field
        # Save record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Check field value on the record details page
        And the user refreshes the screen
        Then the user selects the "CF CRUD Checkbox Item" labelled checkbox field on the main page
        And the value of the checkbox field is "true"
        # Check field value on the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC"
        And the user presses Escape
        And the user selects the row with text "LC Item 1 name" in the "Name" labelled column header of the table field
        Then the value of the "CF CRUD Checkbox Item" labelled nested checkbox field of the selected row in the table field is "true"
        # Make sure no other item is affected
        And the user selects the row with text "LC Item 2 name" in the "Name" labelled column header of the table field
    #  possibly bug here
    # Then the value of the "CF CRUD Checkbox Item" labelled nested checkbox field of the selected row in the table field is "false"

    Scenario: 03 - Tick on second item, check if data is saved and updated correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC Item 2 name"
        And the user selects the row with text "LC Item 2 name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Item LC Item 2 name" titled page is displayed
        Then the user selects the "CF CRUD Checkbox Item" labelled checkbox field on the main page
        And the user ticks the checkbox field
        # Save record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Check if data is saved correctly
        And the user refreshes the screen
        Then the user selects the "CF CRUD Checkbox Item" labelled checkbox field on the main page
        And the value of the checkbox field is "true"
        # Update custom field data
        Then the user selects the "CF CRUD Checkbox Item" labelled checkbox field on the main page
        And the user unticks the checkbox field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Check if data is updated correctly
        And the user refreshes the screen
        Then the user selects the "CF CRUD Checkbox Item" labelled checkbox field on the main page
        And the value of the checkbox field is "false"

    Scenario: 04 - Test duplication with custom field data on item
        # Open detail record with the checkbox ticked
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC Item 1 name"
        And the user selects the row with text "LC Item 1 name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Item LC Item 1 name" titled page is displayed
        # Duplicate the record
        And the "Duplicate" labelled button in the header is displayed
        And the user clicks the "Duplicate" labelled button in the header
        And the user selects the "Name" labelled text field on a modal
        And the user writes "LC Item 1 name duplicate" in the text field
        And the user selects the "id" bound text field on a modal
        And the user writes "AUTO_TEST_CF" in the text field
        And the user clicks the "Duplicate" labelled business action button on a modal
        Then a toast containing text "Record was duplicated successfully." is displayed
        # Check that the value of the custom checkbox field was duplicated
        Then the "Item LC Item 1 name duplicate" titled page is displayed
        And the user selects the "CF CRUD Checkbox Item" labelled checkbox field on the main page
        And the value of the checkbox field is "true"

    Scenario: 05 - Cleanup - delete the custom field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "Item"
        And the user selects the row with text "cfCrudCheckboxItem" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
