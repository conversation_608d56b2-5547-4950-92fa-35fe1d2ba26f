
#The purpose of this test is to verify that the user can create and interact with the newly created cutom fields on an item

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-flow-item-custom-field-user-interaction
    Scenario: 01 - Create and interact with custom checkbox field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC Item 1 name"
        And the user selects the row with text "LC Item 1 name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Item" in the reference field
        And the user selects "Item" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Checkbox custom field on item" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user clicks in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Type" in the reference field
        And the user selects "Type" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        # "Page" option is prepopulated by default
        And the user selects "Lookup | Navigation bar" in the multi dropdown field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Checkbox helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Checkbox info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Checkbox warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal

    Scenario: 02 - Check if field appears on page
        Then the "Item LC Item 1 name" titled page is displayed
        Then the user selects the "Checkbox custom field on item" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the value of the checkbox field is "true"

    Scenario: 03 - Check if field appears on the navigation bar
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the "Checkbox custom field on item" labelled column of the table field

    Scenario: 04 - Check if field appears on lookup
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        And the user selects "UK LIMITED" in the reference field
        And the user selects the "Sold-to customer *" labelled reference field on the main page
        And the user writes "ATSERMO" in the reference field
        And the user selects "ATSERMO" in the reference field
        When the user selects the "Lines" labelled table field on the main page
        And the user selects the floating row of the table field
        And the user clicks the "Item" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects the "item" bound table field on a modal
        And the user clicks the "Checkbox custom field on item" labelled column of the table field

    Scenario: 05 - Cleanup - delete custom fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "Item"
        And the user selects the row with text "checkboxCustomFieldOnItem" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
