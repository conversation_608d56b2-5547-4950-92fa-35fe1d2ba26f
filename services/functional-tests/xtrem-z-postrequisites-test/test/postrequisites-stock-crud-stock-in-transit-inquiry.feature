# This test aims to validate the running of the 'In transit inquiry' and confirming that the expected values are returned
# The focus will be on verifying the results of the inquiry

@inventory
Feature: postrequisites-stock-crud-stock-in-transit-inquiry

    Scenario: 01 - Verifying that field selections can be made
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferInTransitInquiry"
        Then the "Stock in transit inquiry" titled page is displayed
        And the user selects the "Results" labelled table field on the main page
        And the table field is empty
        And the user selects the "User" labelled reference field on the main page
        And the reference field is read-only
        And the user selects the "Company" labelled reference field on the main page
        And the user writes "UK Limited" in the reference field
        And the user selects "UK Limited" in the reference field
        And the user selects the "Shipping site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "From item" labelled reference field on the main page
        And the user writes "Item for inquiry" in the reference field
        And the user selects "Item for inquiry" in the reference field
        And the user selects the "To item" labelled reference field on the main page
        And the user writes "Item for inquiry" in the reference field
        And the user selects "Item for inquiry" in the reference field
        And the user selects the "Item category" labelled reference field on the main page
        And the user writes "Chemical" in the reference field
        And the user selects "Chemical" in the reference field
        And the user selects the "Commodity code" labelled text field on the main page
        And the user writes "Code 2/002" in the text field
        And the user selects the "Posting class" labelled reference field on the main page
        And the user writes "Standard rate items and services" in the reference field
        And the user selects "Standard rate items and services" in the reference field
        And the user selects the "Date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user writes a generated date in the date field with value "T-1"
        And the value of the date field is a generated date with value "T-1"
        And the user writes a generated date in the date field with value "02/13/2025"
        And the value of the date field is a generated date with value "02/13/2025"
        And the user clicks the "Run" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        And the user selects the "Results" labelled table field on the main page
        And the table field is not empty
        And the user selects the "inTransitStockValue" bound tile aggregate field on the main page
        And the value of the tile aggregate field is "£2,002.20"
        And the user selects the "Results" labelled table field on the main page
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the user ticks the table column configuration with "Commodity code" name on the sidebar
        And the table column configuration with name "Commodity code" on the sidebar is ticked
        And the user clicks the Close button of the sidebar
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Item for inquiry"
        And the value of the "Shipping site" labelled nested text field of the selected row in the table field is "Stock Tranfer Uk Site 1"
        And the value of the "Receiving site" labelled nested text field of the selected row in the table field is "Stock Tranfer Uk Site 2"
        And the value of the "Company" labelled nested text field of the selected row in the table field is "500"
        And the value of the "Quantity in stock unit" labelled nested text field of the selected row in the table field is "20 each"
        And the value of the "Unit cost" labelled nested text field of the selected row in the table field is "£ 100.11"
        And the value of the "Stock value in transit" labelled nested text field of the selected row in the table field is "£ 2,002.20"
        And the value of the "Cost type" labelled nested text field of the selected row in the table field is "Standard cost"
        And the value of the "Item category" labelled nested text field of the selected row in the table field is "Chemical"
        And the value of the "Posting class" labelled nested text field of the selected row in the table field is "Standard rate items and services"
        And the value of the "Commodity code" labelled nested text field of the selected row in the table field is "Code 2/002"

    Scenario: 02 - Confirm a future date can not be selected
        Given the user selects the "Date" labelled date field on the main page
        Then the user writes a generated date in the date field with value "T"
        And the date equal to "T-1" is enabled
        And the date equal to "T" is enabled
        And the date equal to "T+1" is disabled
        And the user writes a generated date in the date field with value "02/13/2025"
        And the value of the date field is a generated date with value "02/13/2025"

    Scenario: 03 - Confirming the stock in transit result with only Company as filter
        And the user selects the "Company" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "UK Limited" in the reference field
        And the user selects "UK Limited" in the reference field
        And the user selects the "Shipping site" labelled reference field on the main page
        And the user clears the reference field
        And the user selects the "From item" labelled reference field on the main page
        And the user clears the reference field
        And the user selects the "To item" labelled reference field on the main page
        And the user clears the reference field
        And the user selects the "Item category" labelled reference field on the main page
        And the user clears the reference field
        And the user selects the "Commodity code" labelled text field on the main page
        And the user clears the text field
        And the user selects the "Posting class" labelled reference field on the main page
        And the user clears the reference field
        And the user clicks the "Run" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "inTransitStockValue" bound tile aggregate field on the main page
        And the value of the tile aggregate field is "£4,019.95"
        And the user selects the "Results" labelled table field on the main page
        And the table field is not empty
        And the user selects the row 1 of the table field
        And the value of the "Item" labelled nested text field of the selected row in the table field is "Item for inquiry"
        And the value of the "Shipping site" labelled nested text field of the selected row in the table field is "Stock Tranfer Uk Site 1"
        And the value of the "Receiving site" labelled nested text field of the selected row in the table field is "Stock Tranfer Uk Site 2"
        And the value of the "Company" labelled nested text field of the selected row in the table field is "500"
        And the value of the "Quantity in stock unit" labelled nested text field of the selected row in the table field is "20 each"
        And the value of the "Unit cost" labelled nested text field of the selected row in the table field is "£ 100.11"
        And the value of the "Stock value in transit" labelled nested text field of the selected row in the table field is "£ 2,002.20"
        And the value of the "Cost type" labelled nested text field of the selected row in the table field is "Standard cost"
        And the value of the "Item category" labelled nested text field of the selected row in the table field is "Chemical"
        And the value of the "Posting class" labelled nested text field of the selected row in the table field is "Standard rate items and services"
        And the value of the "Commodity code" labelled nested text field of the selected row in the table field is "Code 2/002"

    Scenario: 04 - Confirm that the right information is filtered when we have two items and we filter only on one
        And the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferInTransitInquiry"
        And the user selects the "Company" labelled reference field on the main page
        And the user writes "UK Limited" in the reference field
        And the user selects "UK Limited" in the reference field
        And the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "02/13/2025"
        And the value of the date field is a generated date with value "02/13/2025"
        And the user clicks the "Run" labelled business action button on the main page
        And the user selects the "inTransitStockValue" bound tile aggregate field on the main page
        And the value of the tile aggregate field is "£4,019.95"
        And the user selects the "Commodity code" labelled text field on the main page
        And the user writes "Code 2/002" in the text field
        And the user clicks the "Run" labelled business action button on the main page
        And the user selects the "inTransitStockValue" bound tile aggregate field on the main page
        And the value of the tile aggregate field is "£2,002.20"

    Scenario: 05 - Confirm posting of shipment will increase value of in transit stock
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferShipment"
        Then the "Stock transfer shipments" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TS250002" in the "Number" labelled column header of the table field
        And the value of the "number" bound nested text field of the selected row in the table field is "TS250002"
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferInTransitInquiry"
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user selects the "Company" labelled reference field on the main page
        And the user writes "UK Limited" in the reference field
        And the user selects "UK Limited" in the reference field
        And the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "02/13/2025"
        And the value of the date field is a generated date with value "02/13/2025"
        And the user clicks the "Run" labelled business action button on the main page
        And the user selects the "inTransitStockValue" bound tile aggregate field on the main page
        And the value of the tile aggregate field is "£6,037.70"

    # @todo ammend the scenario when the story mentioned in the comments of this bug is done https://jira.sage.com/browse/XT-91677
    # @todo adapt the data and the scenario to make possible to check thet confirming posting of receipt makes no difference to the value
    Scenario: 06 - Confirm posting of receipt makes no difference to the value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferReceipt"
        Then the "Stock transfer receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "TR250001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Post stock" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        And the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/StockTransferInTransitInquiry"
        And the user selects the "Company" labelled reference field on the main page
        And the user writes "UK Limited" in the reference field
        And the user selects "UK Limited" in the reference field
        And the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "02/13/2025"
        And the value of the date field is a generated date with value "02/13/2025"
        And the user clicks the "Run" labelled business action button on the main page
        And the user selects the "inTransitStockValue" bound tile aggregate field on the main page
        And the value of the tile aggregate field is "£6,037.70"
