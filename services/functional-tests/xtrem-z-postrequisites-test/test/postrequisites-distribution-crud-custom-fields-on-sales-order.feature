#The purpose of this test is to do crud operations using custom fields on sales order

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-crud-custom-fields-on-sales-order
    Scenario: 01 - Create custom date field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "SO240016"
        And the user selects the row with text "SO240016" in the "number" labelled column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Sales order" in the reference field
        And the user selects "Sales order" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "CF CRUD Date SO" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Date" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Number" in the reference field
        And the user selects "Number" in the reference field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        # "Page" option is prepopulated by default
        And the user selects "Navigation bar" in the multi dropdown field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Date helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Date info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Date warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal

    Scenario: 02 - Create a new sales order, populate the custom field before initial saving and save
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.23" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 2 seconds
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "RJournal entry item02" in the reference field
        And the user selects "RJournal entry item02" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "10.00" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Populate the custom field
        Given the user selects the "CF CRUD Date SO" labelled date field on the main page
        And the user writes "11/12/2024" in the date field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 03 - Change the value of the custom field and save SO
        Given the user selects the "CF CRUD Date SO" labelled date field on the main page
        Then the value of the date field is "11/12/2024"
        And the user clears the date field
        And the user writes "12/13/2024" in the date field
        And the user presses Escape
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SONUM01_CF]"

    Scenario: 04 - Reload SO and check changed value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SONUM01_CF]"
        And the user selects the row with text "[ENV_SONUM01_CF]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "CF CRUD Date SO" labelled date field on the main page
        Then the value of the date field is "12/13/2024"

    Scenario: 05 - Duplicate SO and check if the value of the custom field was duplicated
        And the user clicks the "Duplicate" labelled button in the header
        And a toast containing text "Record was duplicated successfully." is displayed
        And the user selects the "CF CRUD Date SO" labelled date field on the main page
        Then the value of the date field is "12/13/2024"

    Scenario: 06 - Change value on duplicated SO and Save
        And the user clears the date field
        And the user writes "12/13/2023" in the date field
        And the user presses Escape
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SONUM01_DUPLICATED_CF]"

    Scenario: 07 - Reload duplicated SO and check changed value
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SONUM01_DUPLICATED_CF]"
        And the user selects the row with text "[ENV_SONUM01_DUPLICATED_CF]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "CF CRUD Date SO" labelled date field on the main page
        Then the value of the date field is "12/13/2023"

    Scenario: 08 - Navigate to the first SO and chack if the old value of the custome field is the same
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SONUM01_CF]"
        And the user selects the row with text "[ENV_SONUM01_CF]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "CF CRUD Date SO" labelled date field on the main page
        Then the value of the date field is "12/13/2024"

    Scenario: 09 - Empty the value of the custom field, save and make sure it stays with empty value
        And the user clears the date field
        And the user presses Escape
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "[ENV_SONUM01_CF]"
        And the user selects the row with text "[ENV_SONUM01_CF]" in the "number" bound column header of the table field
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the user selects the "CF CRUD Date SO" labelled date field on the main page
        Then the value of the date field is ""

    Scenario: 10 - Cleanup - delete the custom field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "SalesOrder"
        And the user selects the row with text "cfCrudDateSo" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
