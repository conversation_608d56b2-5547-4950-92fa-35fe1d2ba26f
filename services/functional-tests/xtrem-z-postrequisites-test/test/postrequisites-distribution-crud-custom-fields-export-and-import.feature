#The purpose of this test is to export custom field data and to import custom field data

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-crud-custom-fields-export-and-import

    Scenario: 01 - Create custom checkbox field on Item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC Item 1 name"
        And the user selects the row with text "LC Item 1 name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Item" in the reference field
        And the user selects "Item" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "CF Export Checkbox Item" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user clicks in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Type" in the reference field
        And the user selects "Type" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Checkbox helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Checkbox info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Checkbox warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal

    Scenario: 02 - Create new export template
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-import-export/ImportExportTemplate"
        Then the "Import and export templates" titled page is displayed
        And the user clicks the "Create" labelled business action button on the main page
        And the user selects the "Node name *" labelled select field on the main page
        And the user writes "Item" in the select field
        And the user selects "Item" in the select field
        And the user selects the "Name *" labelled text field on the main page
        And the user clears the text field
        And the user writes "Item custom field" in the text field
        And the user selects the "id" bound text field on the main page
        And the user clears the text field
        And the user writes "itemCfExport" in the text field
        And the user selects the "templateUse" bound dropdown-list field on the main page
        And the user selects "Import and export" in the dropdown-list field
        When the user selects the "columnsTable" bound table field on the main page
        And the user selects the row with text "cfExportCheckboxItem" in the "Field" labelled column header of the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 03 - Prepare Item data for export
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC item with Amount name"
        And the user selects the row with text "LC item with Amount name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Item LC item with Amount name" titled page is displayed
        Then the user selects the "CF Export Checkbox Item" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user refreshes the screen
        Then the user selects the "CF Export Checkbox Item" labelled checkbox field on the main page
        And the value of the checkbox field is "true"

    Scenario: 04 - Export the Item
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC item with Amount name"
        And the user selects the row with text "LC item with Amount name" in the "Name" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        Then the bulk action bar of the table field has 1 items
        When the user clicks the "Export" labelled bulk action button of the table field
        Then a warn dialog appears on the main page
        And the text in the header of the dialog is "Select export template"
        And the user selects the "Export template *" labelled reference field on a modal
        When the user clears the reference field
        When the user writes "Item custom field" in the reference field
        And the user selects "Item custom field" in the reference field
        And the user clicks the "Confirm" labelled business action button on a modal
        Then a warn dialog appears on the main page
        And the text in the header of the dialog is "Export"
        And the text in the body of the dialog is "Perform this action on the selected items: 1"
        When the user clicks the "OK" button of the Confirm dialog
        Then a toast containing text "Action started on the selected items." is displayed

    Scenario: 05 - Check that the export worked
        And the user waits 5 seconds
        When the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationState"
        Then the "Batch task history" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Export" in the "Operation" labelled column header of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And the user selects the "Parameters" labelled table field on the main page
        And the user selects the row 2 of the table field
        And the value of the "Value" labelled nested numeric field of the selected row in the table field is "itemCfExport"
        And selects the "Logs" labelled navigation anchor on the main page
        And the user selects the "Logs" labelled table field on the main page
        And the user selects the row with text "Result" in the "Level" labelled column header of the table field
        Then the value of the "Message" labelled nested text field of the selected row in the table field is "Export completed"

    Scenario: 06 - Download the export
        And the user clicks the "Link" labelled nested field of the selected row in the table field
    # When XT-85367 ER is done, open CSV file and check if column "_customData(cfExportCheckboxItem)" contains value "true" for all lines

    Scenario: 07 - Import the file with the modified custom field data for Item
        # Column "_customData(cfExportCheckboxItem)" was modified to contain values "false" for all lines
        Then the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportData"
        And the "Data import" titled page is displayed
        And the user selects the "Select file" labelled file deposit field on the main page
        And the user adds the file "./import/import-item-custom-field-data.csv" to the file deposit field
        Then the user selects the "Insert" labelled checkbox field on the main page
        And the user unticks the checkbox field
        And the value of the checkbox field is "false"
        Then the user selects the "Update" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the value of the checkbox field is "true"
        Then the user selects the "Test import" labelled checkbox field on the main page
        And the value of the checkbox field is "false"
        Then the user selects the "Continue and ignore the error" labelled checkbox field on the main page
        And the value of the checkbox field is "true"
        When the user selects the "Files to import" labelled table field on the main page
        And the user selects the row with text "import-item-custom-field-data.csv" in the "File name" labelled column header of the table field
        And the user writes "itemCfExport" in the "Template" labelled nested reference field of the selected row in the table field
        And the user selects "itemCfExport" in the "Template" labelled nested field of the selected row in the table field
        And the user presses Tab
        And the user clicks the "Import" labelled business action button on the main page
        Then the text in the body of the dialog is "Do you want to import the file?"
        And the user clicks the "OK" button of the Confirm dialog
        Then a toast with text "import-item-custom-field-data.csv has been submitted for processing." is displayed
        And the user dismisses all the toasts

    Scenario: 08 - Check import results to make sure that the import was successful
        And selects the "Import results" labelled navigation anchor on the main page
        #Make the robot wait for the sync
        And the user waits 10 seconds
        And the user selects the "importResults" bound table field on the main page
        When the user clicks the "Refresh" labelled header action button of the table field
        And the user selects the row 1 of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Completed"

    Scenario: 09 - Check the imported item to make sure the value of the custom field was modified
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "LC item with Amount name"
        And the user selects the row with text "LC item with Amount name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Item LC item with Amount name" titled page is displayed
        Then the user selects the "CF Export Checkbox Item" labelled checkbox field on the main page
        And the value of the checkbox field is "false"

    Scenario: 10 - Cleanup - delete the custom field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "Item"
        And the user selects the row with text "cfExportCheckboxItem" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
