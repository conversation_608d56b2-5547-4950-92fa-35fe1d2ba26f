#This test can only be executed with sage.
#The goal of this test is to verify that the user can create a mass sales shipment
@postrequisites
@distribution
Feature: postrequisites-distribution-crud-sales-mass-shipment

    Scenario: Verify user can create a mass sales shipment creation
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrderShippingMassProcess"
        Then the "Mass shipment creation" titled page is displayed

        And the user selects the "Company *" labelled reference field on the main page
        And the user writes "Chem. Irvine" in the reference field
        And the user selects "Chem. Irvine" in the reference field

        And the user selects the "Stock site *" labelled reference field on the main page
        And the user writes "Chem. Chicago" in the reference field
        And the user selects "Chem. Chicago" in the reference field

        Then the user clicks the "Show advanced selection" labelled business action button on the main page
        And the user selects the "Results" labelled table field on the main page
        And the user selects the row with text "SO_Allocated" in the "Order number" labelled column header of the table field
        And the value of the "Available stock" labelled nested text field of the selected row in the table field is "850.00 m3"
        And the value of the "Allocated quantity" labelled nested text field of the selected row in the table field is "100.00 m3"

        And the user selects the row with text "SO_NAllocated" in the "Order number" labelled column header of the table field
        And the value of the "Available stock" labelled nested text field of the selected row in the table field is "850.00 m3"
        And the value of the "Allocated quantity" labelled nested text field of the selected row in the table field is "0.00 m3"

        And the user selects the row with text "SO_PAllocated" in the "Order number" labelled column header of the table field
        And the value of the "Available stock" labelled nested text field of the selected row in the table field is "850.00 m3"
        And the value of the "Allocated quantity" labelled nested text field of the selected row in the table field is "50.00 m3"

        And the user clicks the "Create" labelled business action button on the main page
        Then a toast containing text "Sales shipments created: 1" is displayed

    Scenario: Verify the newly created mass sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        When the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Chem. Chicago" in the filter of the table field
        And the user ticks the item with text "Chem. Chicago" in the filter of the table field
        And the user closes the filter of the "Site" labelled column in the table field

        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        #Delete
        Then the user selects the "lines" bound table field on the main page

        And the user selects the row with text "100.00 m3" in the "Quantity in sales unit" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"

        And the user selects the row with text "150.00 m3" in the "Quantity in sales unit" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Partially allocated"

        And the user selects the row with text "200.00 m3" in the "Quantity in sales unit" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
