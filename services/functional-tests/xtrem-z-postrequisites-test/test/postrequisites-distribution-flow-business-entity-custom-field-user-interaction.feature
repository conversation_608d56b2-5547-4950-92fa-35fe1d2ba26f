#The purpose of this test is to verify that the user can create and interact with the newly created cutom fields on a business entity

@postrequisites
@distribution
@custom_fields
Feature: postrequisites-distribution-flow-business-entity-custom-field-user-interaction
    Scenario: 01 - Create and interact with custom numeric field
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Name" labelled column in the table field with value "ATSERMO"
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Business entity" in the reference field
        And the user selects "Business entity" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Business entity number" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customNumericBE" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Numeric field" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Name" in the reference field
        And the user selects "Name" in the reference field
        And the user selects the "Display on *" labelled multi dropdown field on a modal
        And the user clicks in the multi dropdown field
        # "Page" option is prepopulated by default
        And the user selects "Lookup | Navigation bar" in the multi dropdown field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Numeric helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Numeric info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Numeric warning message" in the text field
        And the user selects the "Prefix" labelled text field on a modal
        And the user writes "<" in the text field
        And the user selects the "Postfix" labelled text field on a modal
        And the user writes ">" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Scale" labelled numeric field on a modal
        And the user writes "4" in the numeric field
        And the user selects the "Minimum value" labelled numeric field on a modal
        And the user writes "3" in the numeric field
        And the user selects the "Maximum value" labelled numeric field on a modal
        And the user writes "13" in the numeric field
        And the user clicks the "Finish" labelled business action button on a modal

    Scenario: 02 - Check if field appears on page
        Then the "Business entity ATSERMO" titled page is displayed
        And the user selects the "Business entity number" labelled numeric field on the main page
        And the user writes "11" in the numeric field
        And the value of the numeric field is "11.0000"

    Scenario: 03 - Check if field appears on the navigation bar
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Business entity number" labelled column in the table field with value "0"

    Scenario: 04 - Cleanup - delete custom fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-customization/CustomField"
        Then the "Custom fields" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "recordType" labelled column in the table field with value "BusinessEntity"
        And the user selects the row with text "customNumericBE" in the "technicalName" labelled column header of the table field
        And the user clicks the "recordType" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
