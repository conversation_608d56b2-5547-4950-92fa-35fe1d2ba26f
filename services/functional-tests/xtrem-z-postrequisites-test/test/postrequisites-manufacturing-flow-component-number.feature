@manufacturing

Feature: postrequisites-manufacturing-flow-component-number

    Scenario: Add component number for an existing work order

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "WRKPRDTRC02" in the navigation panel
        And the user clicks the record with the text "WRKPRDTRC02" in the navigation panel

        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New work order component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "40"

        And the user writes "42" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Buck<PERSON>" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar

        When the user waits 2 seconds

        When the user selects the row with text "42" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New work order component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "50"

        And the user writes "55" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Bucket" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar

        When the user waits 2 seconds
        When the user selects the row with text "55" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New work order component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "60"

        And the user writes "69" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Bucket" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar

        When the user waits 2 seconds
        When the user selects the row with text "69" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New work order component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "70"

        And the user writes "71" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Bucket" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Required quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar

        When the user waits 2 seconds
        When the user selects the row with text "71" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed


    Scenario: Add component number for an existing bill of materials

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Wheel" in the navigation panel
        And the user clicks the record with the text "Wheel" in the navigation panel

        When the user selects the "Components" labelled table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "10"

        And the user writes "12" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Bucket" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "UOM link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "Add" labelled business action button on the sidebar

        When the user waits 2 seconds

        When the user selects the row with text "12" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "20"

        And the user writes "25" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Bucket" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "UOM link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "Add" labelled business action button on the sidebar

        When the user waits 2 seconds
        When the user selects the row with text "25" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "30"

        And the user writes "39" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Bucket" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "UOM link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "Add" labelled business action button on the sidebar

        When the user waits 2 seconds
        When the user selects the row with text "39" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        When the user clicks the "addComponentLine" bound action of the table field

        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        Then the value of the numeric field is "40"

        And the user writes "41" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Bucket" in the reference field
        And the user selects "Bucket" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "UOM link quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "Add" labelled business action button on the sidebar

        When the user waits 2 seconds
        When the user selects the row with text "41" in the "Component number" labelled column header of the table field
        Then the value of the "Component description" labelled nested text field of the selected row in the table field is "Bucket"

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
