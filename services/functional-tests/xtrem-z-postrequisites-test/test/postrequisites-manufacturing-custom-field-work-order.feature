#The purpose of this test is to check work order update after adding custom fields

@postrequisites
@custom_fields
Feature: postrequisites-manufacturing-custom-field-work-order

    Scenario: 01 - Create custom checkbox field on work order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "Shopfloor_W003" in the navigation panel
        And the user clicks the record with the text "Shopfloor_W003" in the navigation panel

        And the user clicks the "Create field" labelled more actions button in the header
        And the user selects the "Record type" labelled reference field on a modal
        And the user writes "Work order" in the reference field
        And the user selects "Work order" in the reference field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Field label *" labelled text field on a modal
        And the user writes "Checkbox custom field" in the text field
        And the user selects the "Technical name *" labelled text field on a modal
        And the user writes "customCheckboxWO" in the text field
        And the user selects the "Field type *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "Checkbox" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Anchor field *" labelled reference field on a modal
        And the user writes "Category" in the reference field
        And the user selects "Category" in the reference field
        And the user selects the "Position relative to anchor field *" labelled dropdown-list field on a modal
        And the user clicks in the dropdown-list field
        And the user selects "After" in the dropdown-list field
        And the user clicks the "Next" labelled business action button on a modal
        And the user selects the "Helper text" labelled text field on a modal
        And the user writes "Checkbox helper text" in the text field
        And the user selects the "Info message" labelled text field on a modal
        And the user writes "Checkbox info message" in the text field
        And the user selects the "Warning message" labelled text field on a modal
        And the user writes "Checkbox warning message" in the text field
        And the user clicks the "Next" labelled business action button on a modal
        And the user clicks the "Finish" labelled business action button on a modal
        Then the "Work order Shopfloor_W003" titled page is displayed
        Then the user selects the "Checkbox custom field" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the value of the checkbox field is "true"

    Scenario: 02 - Verify user can exclude row from component table
        Given selects the "Components" labelled navigation anchor on the main page
        And the user selects the "Components" labelled table field on the main page
        And the user selects the row with text "10" in the "Component number" labelled column header of the table field
        And the user clicks the "Exclude" dropdown action of the selected row of the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        And the value of the "Status" labelled nested text field of the selected row in the table field is "Excluded"

    Scenario: 03 - Verify user can exclude row from component table
        Given selects the "Components" labelled navigation anchor on the main page
        When the user selects the "Components" labelled table field on the main page
        Then the user selects the row with text "20" in the "Component number" labelled column header of the table field
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        When the user selects the "Operation" labelled reference field on the sidebar
        And the user writes "OP1" in the reference field
        And the user selects "OP1" in the reference field

        And the user selects the "instruction" bound rich text field on the sidebar
        And the user clears the rich text field
        And the user writes "test value for component" in the rich text field
        And the user clicks the "Ok" labelled business action button on the sidebar

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
