{"extends": "../../tsconfig-base.json", "compilerOptions": {"composite": true, "outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts"], "exclude": ["**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../../platform/system/xtrem-authorization"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/back-end/xtrem-core"}, {"path": "../../../platform/shared/xtrem-decimal"}, {"path": "../../applications/xtrem-declarations"}, {"path": "../../applications/xtrem-finance"}, {"path": "../../../platform/system/xtrem-mailer"}, {"path": "../../applications/xtrem-manufacturing"}, {"path": "../../shared/xtrem-master-data"}, {"path": "../../applications/xtrem-purchasing"}, {"path": "../../../platform/system/xtrem-reporting"}, {"path": "../../applications/xtrem-sales"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../../shared/xtrem-structure"}, {"path": "../../../platform/system/xtrem-system"}, {"path": "../../shared/xtrem-technical-data"}, {"path": "../../../platform/front-end/xtrem-ui"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/cli/xtrem-cli"}]}