@inventory
@accessibility
Feature: accessibility-flow-stock-change
    # This feature tests accessibility compliance of the Stock Change page, scanning the page to search for any accessibility issue

    Scenario: Access StockChange Page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockChange"
        Then the "Stock changes" titled page is displayed

    Scenario: Create Stock Change Record
        And the user clicks the "Create" labelled business action button on the navigation panel

        #Creating
        And the user selects the "Number" labelled text field on the main page
        And the user writes "STKCG01" in the text field
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Item" labelled reference field on the main page
        And the user writes "Item for stock change" in the reference field
        And the user selects "Item for stock change" in the reference field

        #Searching
        And the user clicks in the "stockSearch" bound button field on the main page
        And the user selects the "stockRecord" bound table field on a modal
        And the user selects the row with text "Bulk 01" in the "Location" labelled column header of the table field
        And the value of the "Location" labelled nested text field of the selected row in the table field is "Bulk 01"
        And the user clicks the "Location" labelled nested field of the selected row in the table field

        #Add line
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "addLine" bound header action button of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "quantity" labelled nested field of the selected row in the table field
        And the user writes "20" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user writes "Bulk 02" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Bulk 02" in the "Location" labelled nested field of the selected row in the table field
        And the user writes "Quality control" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Quality control" in the "Quality control" labelled nested field of the selected row in the table field

        #Save and post
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Scan StockChange page

        And the user executes an accessibility tests scan

    Scenario: Delete stock change record
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
