@finance
@accessibility
Feature: accessibility-flow-finance-journal-entries-inquiry
    # This feature tests accessibility compliance of the Journal Entry Inquiry page, scanning the page for any accessiblity issue

    Scenario: Access JournalEntryInquiry Page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntryInquiry/"
        Then the "Journal entry inquiry" titled page is displayed

        And the user selects the "Companies" labelled multi reference field on the main page
        And the user writes "UK" in the multi reference field
        And the user selects "UK Limited" in the multi reference field

        And the user selects the "Start date" labelled date field on the main page
        And the user clears the date field

        And the user clicks in the "searchButton" bound button field on the main page

    Scenario: Scan JournalEntryInquiry page
        And the user executes an accessibility tests scan
