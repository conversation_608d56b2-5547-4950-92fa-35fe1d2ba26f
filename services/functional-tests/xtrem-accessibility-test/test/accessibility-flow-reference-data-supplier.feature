@reference-data
@accessibility
Feature: accessibility-flow-reference-data-supplier
    # This feature tests accessibility compliance of the Supplier page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access Supplier page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Carlson Filtration" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Supplier Carlson Filtration" titled page is displayed

    Scenario: Scan Supplier page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Address Tab
        And selects the "Address" labelled navigation anchor on the main page
        Then the "Address" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Contacts Tab
        And selects the "Contacts" labelled navigation anchor on the main page
        Then the "Contacts" labelled navigation anchor is selected

        And the user selects the "contacts" bound pod collection field on the main page
        When the user clicks the "Add contact" button of the selected pod collection field

        Then the "New contact" titled sidebar is displayed
        And the user selects the "isActive" bound switch field on the sidebar
        And the user clicks in the switch field
        And the user selects the "Address" labelled reference field on the sidebar
        And the user writes "Carlson Filtration" in the reference field
        And the user selects "Carlson Filtration" in the reference field
        And the user selects the "Title" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Mr." in the dropdown-list field
        And the user selects the "First name" labelled text field on the sidebar
        And the user writes "Carlson" in the text field
        And the user selects the "Last name" labelled text field on the sidebar
        And the user writes "Filtration" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar

        Then the "Contacts" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Commercial Tab
        And selects the "Commercial" labelled navigation anchor on the main page
        Then the "Commercial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Certificates Tab
        And selects the "Certificates" labelled navigation anchor on the main page
        Then the "Certificates" labelled navigation anchor is selected

        And the user selects the "certificates" bound table field on the main page
        And the user clicks the "Add" labelled button of the table field

        Then the "New supplier certificate" titled sidebar is displayed
        And the user selects the "Certificate reference" labelled text field on the sidebar
        And the user writes "CERT00001" in the text field
        And the user selects the "Standard" labelled reference field on the sidebar
        And the user writes "BRC" in the reference field
        And the user selects "BRC" in the reference field
        And the user selects the "Original certification date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "01/01/Y"
        And the user selects the "Certification date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "01/02/Y"
        And the user selects the "Validity end date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "12/31/Y"
        And the user clicks the "OK" labelled business action button on the sidebar

        Then the "Certificates" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Items Tab
        And selects the "Items" labelled navigation anchor on the main page
        Then the "Items" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Purchasing Tab
        And selects the "Purchasing" labelled navigation anchor on the main page
        Then the "Purchasing" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Supplier page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
