@administration
@accessibility
Feature: accessibility-flow-administration-report-template
    # This feature tests accessibility compliance of the Report Template page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access ReportTemplate page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-reporting/ReportTemplate"
        Then the "Report templates" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "activeUsers" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Report template activeUsers" titled page is displayed

    Scenario: Scan ReportTemplate page - Basic details Tab
        And selects the "Basic details" labelled navigation anchor on the main page
        Then the "Basic details" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ReportTemplate page - Data query Tab
        And selects the "Data query" labelled navigation anchor on the main page
        Then the "Data query" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ReportTemplate page - Code Tab
        And selects the "Code" labelled navigation anchor on the main page
        Then the "Code" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ReportTemplate page - HTML template Tab
        And selects the "HTML template" labelled navigation anchor on the main page
        Then the "HTML template" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ReportTemplate page - Header and footer Tab
        And selects the "Header and footer" labelled navigation anchor on the main page
        Then the "Header and footer" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ReportTemplate page - Attachment Tab
        And selects the "Attachment" labelled navigation anchor on the main page
        Then the "Attachment" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ReportTemplate page - Style sheet Tab
        And selects the "Style sheet" labelled navigation anchor on the main page
        Then the "Style sheet" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ReportTemplate page - Translations Tab
        And selects the "Translations" labelled navigation anchor on the main page
        Then the "Translations" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
