@reference-data
@accessibility
Feature: accessibility-flow-reference-data-customer
    # This feature tests accessibility compliance of the Customer page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access Customer page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "ATSERMO" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Customer ATSERMO" titled page is displayed

    Scenario: Scan Customer page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Customer page - Address Tab
        And selects the "Address" labelled navigation anchor on the main page
        Then the "Address" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Customer page - Contacts Tab
        And selects the "Contacts" labelled navigation anchor on the main page
        Then the "Contacts" labelled navigation anchor is selected

        And the user selects the "contacts" bound pod collection field on the main page
        When the user clicks the "Add contact" button of the selected pod collection field

        Then the "New contact" titled sidebar is displayed
        And the user selects the "isActive" bound switch field on the sidebar
        And the user clicks in the switch field
        And the user selects the "Address" labelled reference field on the sidebar
        And the user writes "ATSERMO" in the reference field
        And the user selects "ATSERMO" in the reference field
        And the user selects the "Title" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Mr." in the dropdown-list field
        And the user selects the "First name" labelled text field on the sidebar
        And the user writes "ATSERMO" in the text field
        And the user selects the "Last name" labelled text field on the sidebar
        And the user writes "SA" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar

        Then the "Contacts" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Customer page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Customer page - Items Tab
        And selects the "Items" labelled navigation anchor on the main page
        Then the "Items" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Customer page - Item prices Tab
        And selects the "Item prices" labelled navigation anchor on the main page
        Then the "Item prices" labelled navigation anchor is selected

        And the user selects the "itemPrices" bound table field on the main page
        And the user clicks the "Add" labelled button of the table field

        Then the "New sales price" titled sidebar is displayed
        And the user selects the "isActive" bound switch field on the sidebar
        And the user clicks in the switch field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Apple Juice" in the reference field
        And the user selects "Apple Juice" in the reference field
        And the user selects the "Price reason" labelled reference field on the sidebar
        And the user writes "Customer pricing" in the reference field
        And the user selects "Customer pricing" in the reference field
        And the user selects the "Unit of measure" labelled reference field on the sidebar
        And the user writes "Each" in the reference field
        And the user selects "Each" in the reference field
        And the user selects the "Currency" labelled reference field on the sidebar
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        And the user selects the "Price" labelled numeric field on the sidebar
        And the user writes "2" in the numeric field

        And the user clicks the "Save" labelled business action button on the sidebar

        Then the "Item prices" labelled navigation anchor is selected
        And the user executes an accessibility tests scan


    Scenario: Scan Customer page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
