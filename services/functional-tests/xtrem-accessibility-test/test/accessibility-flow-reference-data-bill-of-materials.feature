@reference-data
@accessibility
Feature: accessibility-flow-reference-data-bill-of-materials
    # This feature tests accessibility compliance of the Bill of Materials page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access BillOfMaterial page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "BOM tracking FG1" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Bill of material BOM tracking FG1" titled page is displayed

    Scenario: Scan BillOfMaterial page - Components Tab
        And selects the "Components" labelled navigation anchor on the main page
        Then the "Components" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan BillOfMaterial page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan BillOfMaterial page - Multilevel view Tab
        And selects the "Multilevel view" labelled navigation anchor on the main page
        Then the "Multilevel view" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan BillOfMaterial page - Trackings Tab
        And selects the "Trackings" labelled navigation anchor on the main page
        Then the "Trackings" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
