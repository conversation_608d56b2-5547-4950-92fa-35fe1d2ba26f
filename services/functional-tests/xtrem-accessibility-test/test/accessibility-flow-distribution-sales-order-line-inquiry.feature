@distribution
@accessibility
Feature: accessibility-flow-distribution-sales-order-line-inquiry
    # This feature tests accessibility compliance of the Sales Order Line Inquiry page, scanning the page to search for any accessibility issue


    Scenario: Access SalesOrderLineInquiry page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrderLineInquiry"
        Then the "Sales order line" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Company" labelled column in the table field
        And the user searches "UK Limited" in the filter of the table field
        And the user ticks the item with text "UK Limited" in the filter of the table field
        Then the user closes the filter of the "Company" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure sensor" in the filter of the table field
        And the user ticks the item with text "Pressure sensor" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        When the user selects the row with text "Invoiced" in the "Invoice status" labelled column header of the table field
        Then the value of the "Net price" labelled nested numeric field of the selected row in the table field is "£ 10.00"

    Scenario: Scan SalesOrderLineInquiry page
        And the user executes an accessibility tests scan
