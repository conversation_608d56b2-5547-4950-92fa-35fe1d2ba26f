@administration
@accessibility
Feature: accessibility-flow-administration-import-template
    # This feature tests accessibility compliance of the Import Export Template page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access ImportExportTemplate page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-import-export/ImportExportTemplate"
        Then the "Import and export templates" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Account" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Import and export templates" titled page is displayed

    Scenario: Scan ImportExportTemplate page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan ImportExportTemplate page - Import history Tab
        And selects the "Import history" labelled navigation anchor on the main page
        Then the "Import history" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
