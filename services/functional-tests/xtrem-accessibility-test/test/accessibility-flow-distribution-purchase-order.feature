@distribution
@accessibility
Feature: accessibility-flow-distribution-purchase-order
    # This feature tests accessibility compliance of the Purchase Order page, scanning the page on all tabs to search for any accessibility issue

    Scenario: Access PurchaseOrder page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseOrder"
        Then the "Purchase orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PO240006" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Purchase order PO240006" titled page is displayed

    Scenario: Scan PurchaseOrder page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseOrder page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    # Scenario: Scan PurchaseOrder page - Requested changes Tab
    #     And selects the "Requested changes" labelled navigation anchor on the main page
    #     Then the "Requested changes" labelled navigation anchor is selected
    #     And the user executes an accessibility tests scan

    Scenario: Scan PurchaseOrder page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseOrder page - Totals Tab
        And selects the "Totals" labelled navigation anchor on the main page
        Then the "Totals" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseOrder page - Landed costs Tab
        And selects the "Landed costs" labelled navigation anchor on the main page
        Then the "Landed costs" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseOrder page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
