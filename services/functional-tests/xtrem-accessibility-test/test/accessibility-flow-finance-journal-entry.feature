@finance
@accessibility
Feature: accessibility-flow-finance-journal-entry
    # This feature tests accessibility compliance of the Journal Entry page, scanning the page on all tabs to search for any accessibility issue

    Scenario: Access JournalEntry Page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SJ240001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Journal entry SJ240001" titled page is displayed

    Scenario: Scan JournalEntry page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan JournalEntry page - Posting Tab
        And selects the "Posting" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
