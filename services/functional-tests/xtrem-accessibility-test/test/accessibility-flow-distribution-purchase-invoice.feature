@distribution
@accessibility
Feature: accessibility-flow-distribution-purchase-invoice
    # This feature tests accessibility compliance of the Purchase Invoice page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access PurchaseInvoice page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "PI240001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Purchase invoice PI240001" titled page is displayed

    Scenario: Scan PurchaseInvoice page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseInvoice page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseInvoice page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseInvoice page - Totals Tab
        And selects the "Totals" labelled navigation anchor on the main page
        Then the "Totals" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseInvoice page - Posting Tab
        And selects the "Posting" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseInvoice page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
