@distribution
@accessibility
Feature: accessibility-flow-distribution-sales-order
    # This feature tests accessibility compliance of the Sales Order page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access SalesOrder page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "MRP_SALES_QUOTE_2" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Sales order MRP_SALES_QUOTE_2" titled page is displayed

    Scenario: Scan SalesOrder page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesOrder page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesOrder page - Shipping Tab
        And selects the "Shipping" labelled navigation anchor on the main page
        Then the "Shipping" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesOrder page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesOrder page - Totals Tab
        And selects the "Totals" labelled navigation anchor on the main page
        Then the "Totals" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesOrder page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
