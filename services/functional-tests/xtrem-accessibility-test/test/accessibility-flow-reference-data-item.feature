@reference-data
@accessibility
Feature: accessibility-flow-reference-data-item
    # This feature tests accessibility compliance of the Item page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access Item page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Apple Juice" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Item Apple Juice" titled page is displayed

    Scenario: Scan Item page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Management Tab
        And selects the "Management" labelled navigation anchor on the main page
        Then the "Management" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Units Tab
        And selects the "Units" labelled navigation anchor on the main page
        Then the "Units" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Sites Tab
        And selects the "Sites" labelled navigation anchor on the main page
        Then the "Sites" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Suppliers Tab
        And selects the "Suppliers" labelled navigation anchor on the main page
        Then the "Suppliers" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Supplier prices Tab
        And selects the "Supplier prices" labelled navigation anchor on the main page
        Then the "Supplier prices" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Sales prices Tab
        And selects the "Sales prices" labelled navigation anchor on the main page
        Then the "Sales prices" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Customers Tab
        And selects the "Customers" labelled navigation anchor on the main page
        Then the "Customers" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Manufacturing Tab
        And selects the "Manufacturing" labelled navigation anchor on the main page
        Then the "Manufacturing" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan Item page - Documents Tab
        And selects the "Documents" labelled navigation anchor on the main page
        Then the "Documents" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
