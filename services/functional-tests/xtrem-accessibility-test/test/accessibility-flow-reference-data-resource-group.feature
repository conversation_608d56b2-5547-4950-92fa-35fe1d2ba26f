@reference-data
@accessibility
Feature: accessibility-flow-reference-data-resource-group
    # This feature tests accessibility compliance of the Resource Group page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access GroupResource page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/GroupResource"
        Then the "Resource groups" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Capping machines" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Resource group Capping machines" titled page is displayed

    Scenario: Scan GroupResource page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan GroupResource page - Cost Tab
        And selects the "Cost" labelled navigation anchor on the main page
        Then the "Cost" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
