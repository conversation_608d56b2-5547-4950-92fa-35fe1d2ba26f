@manufacturing
@accessibility
Feature: accessibility-flow-manufacturing-work-order-mass-allocation
    # This feature tests accessibility compliance of the Work Order Mass Allocation page, scanning the page to search for any accessibility issue

    Scenario: Access WorkOrderMassAllocation page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrderMassAllocation"

        Then the "Work order mass allocation" titled page is displayed

        ## Choose action = allocation ##
        When the user selects the "Action *" labelled select field on the main page
        And the user clicks in the select field
        And the user selects "Allocation" in the select field
        Then the value of the select field is "Allocation"

        ## Add meaningful description ##
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Filter items RAW1/RAW2" in the text field
        Then the value of the text field is "Filter items RAW1/RAW2"

        ## Enter company = Société S1 ##
        When the user selects the "Company *" labelled reference field on the main page
        And the user writes "Société S1" in the reference field
        And the user selects "Société S1" in the reference field
        Then the value of the reference field is "Société S1"

        ## Enter site = Site de Chavanod ##
        When the user selects the "Site *" labelled reference field on the main page
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"

        ## Enter latest start date = today ##
        When the user selects the "Latest start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        ## Enter item range = RAW_MASS_ALLOC_1 to RAW_MASS_ALLOC_2 ##
        When the user selects the "From item" labelled reference field on the main page
        And the user writes "RAW_MASS_" in the reference field
        And the user selects "Raw material 1 - mass allocation" in the reference field
        Then the value of the reference field is "Raw material 1 - mass allocation"

        When the user selects the "To item" labelled reference field on the main page
        And the user writes "RAW_MASS_" in the reference field
        And the user selects "Raw material 2 - mass allocation" in the reference field
        Then the value of the reference field is "Raw material 2 - mass allocation"

        ## launch mass alocation ##
        And the user clicks the "Allocate" labelled business action button on the main page

        Then an info dialog appears on the main page
        And the dialog title is "Allocation request submitted" on the main page
        And the user clicks the "OK" button of the dialog


    Scenario: scan WorkOrderMassAllocation Page
        And the user executes an accessibility tests scan


    Scenario: Access AllocationResult page

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock-data/AllocationResult"
        Then the "Allocation results" titled page is displayed

    Scenario: scan AllocationResult Page
        And the user executes an accessibility tests scan


    Scenario: Access AllocationResult page resource
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        Then the "Allocation results" titled page is displayed


    Scenario: scan AllocationResult Page resource
        And the user executes an accessibility tests scan
