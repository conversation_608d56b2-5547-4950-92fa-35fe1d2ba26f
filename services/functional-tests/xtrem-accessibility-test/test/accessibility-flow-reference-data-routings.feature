@reference-data
@accessibility
Feature: accessibility-flow-reference-data-routings
    # This feature tests accessibility compliance of the Routings page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access Routing page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "BOM tracking FG1 routing" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Routing BOM tracking FG1 routing" titled page is displayed


    Scenario: Scan Routing page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
