@finance
@accessibility
Feature: accessibility-flow-finance-accounts-payable
    # This feature tests accessibility compliance of the Accounts Payable Invoice page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access AccountsPayableInvoice Page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/AccountsPayableInvoice"
        Then the "Accounts payable invoices" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PC230005" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Accounts payable invoice PC230005" titled page is displayed

    Scenario: Scan AccountsPayableInvoice page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan AccountsPayableInvoice page - Totals Tab
        And selects the "Totals" labelled navigation anchor on the main page
        Then the "Totals" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
