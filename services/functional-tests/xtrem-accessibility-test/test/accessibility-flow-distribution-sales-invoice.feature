@distribution
@accessibility
Feature: accessibility-flow-distribution-sales-invoice
    # This feature tests accessibility compliance of the Sales Invoice page, scanning the page on all tabs to search for any accessibility issue

    Scenario: Access SalesInvoice page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesInvoice"
        Then the "Sales invoices" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user selects the row with text "SIDE240004" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Sales invoice SIDE240004" titled page is displayed

    Scenario: Scan SalesInvoice page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesInvoice page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesInvoice page - Totals Tab
        And selects the "Totals" labelled navigation anchor on the main page
        Then the "Totals" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesInvoice page - Posting Tab
        And selects the "Posting" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesInvoice page - Payments Tab
        And selects the "Payments" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesInvoice page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
