@inventory
@accessibility
Feature: accessibility-flow-stock-issue
    # This feature tests accessibility compliance of the Stock Issue page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access StockIssue page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SS230007" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Stock issue SS230007" titled page is displayed

    Scenario: Scan StockIssue page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
