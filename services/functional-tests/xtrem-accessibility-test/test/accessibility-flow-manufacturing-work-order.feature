@manufacturing
@accessibility
Feature: accessibility-flow-manufacturing-work-order
    # This feature tests accessibility compliance of the Work Order page, scanning the page on all tabs to search for any accessibility issue

    Scenario: Access WorkOrder page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WorkOrder"
        Then the "Work orders" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "Number" labelled column in the table field with value "Close6"
        And the user selects the row 1 of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Work order Close6" titled page is displayed


    Scenario: Scan WorkOrder page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan WorkOrder page - Trackings Tab
        And selects the "Trackings" labelled navigation anchor on the main page
        Then the "Trackings" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan WorkOrder page - Posting Tab
        And selects the "Posting" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan WorkOrder page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
