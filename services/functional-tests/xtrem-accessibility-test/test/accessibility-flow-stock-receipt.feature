@inventory
@accessibility
Feature: accessibility-flow-stock-receipt
    # This feature tests accessibility compliance of the Stock Receipt page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access StockReceipt page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        And the user filters the "Number" labelled column in the table field with value "SR240003"
        And the user selects the row with text "SR240003" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Stock receipt SR240003" titled page is displayed

    Scenario: Scan <PERSON>Receipt page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan StockReceipt page - Posting Tab
        And selects the "Posting" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
