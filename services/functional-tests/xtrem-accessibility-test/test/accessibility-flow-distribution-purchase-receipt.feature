@distribution
@accessibility
Feature: accessibility-flow-distribution-purchase-receipt
    # This feature tests accessibility compliance of the Purchase Receipt page, scanning the page on all tabs to search for any accessibility issue

    Scenario: Access PurchaseReceipt page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "PR240004" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Purchase receipt PR240004" titled page is displayed

    Scenario: Scan PurchaseReceipt page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseReceipt page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseReceipt page - Receiving Tab
        And selects the "Receiving" labelled navigation anchor on the main page
        Then the "Receiving" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseReceipt page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseReceipt page - Totals Tab
        And selects the "Totals" labelled navigation anchor on the main page
        Then the "Totals" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseReceipt page - Landed costs Tab
        And selects the "Landed costs" labelled navigation anchor on the main page
        Then the "Landed costs" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseReceipt page - Returns Tab
        And selects the "Returns" labelled navigation anchor on the main page
        Then the "Returns" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan PurchaseReceipt page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
