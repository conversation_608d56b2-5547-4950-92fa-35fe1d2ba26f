@distribution
@accessibility
Feature: accessibility-flow-distribution-purchase-receipt-line-inquiry
    # This feature tests accessibility compliance of the Purchase Receipt Line Inquiry page, scanning the page to search for any accessibility issue

    Scenario: Access PurchaseReceiptLineInquiry page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceiptLineInquiry"
        Then the "Purchase receipt line" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Company" labelled column in the table field
        And the user searches "TE Connectivity" in the filter of the table field
        And the user ticks the item with text "TE Connectivity" in the filter of the table field
        Then the user closes the filter of the "Company" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Conversion item" in the filter of the table field
        And the user ticks the item with text "Conversion item" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        When the user selects the row with text "Pending" in the "Status" labelled column header of the table field
        Then the value of the "Net price" labelled nested numeric field of the selected row in the table field is "€ 343.00"

    Scenario: Scan PurchaseReceiptLineInquiry page
        And the user executes an accessibility tests scan
