@manufacturing
@accessibility
Feature: accessibility-flow-manufacturing-work-in-progress-inquiry
    # This feature tests accessibility compliance of the Work in Progress Inquiry page, scanning the page to search for any accessibility issue

    Scenario: Access WipInquiry Page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/WipInquiry"
        Then the "Work in progress inquiry" titled page is displayed

        Given the user selects the "Site" labelled reference field on the main page
        When the user writes "Chava" in the reference field
        And the user selects "Site de Chavanod" in the reference field
        Then the value of the reference field is "Site de Chavanod"

        When the user clicks the "Run" labelled business action button on the main page
        And the user waits for 10 seconds
        And the user selects the "Results" labelled table field on the main page
        Then the table field is not empty

    Scenario: scan WipInquiry Page
        And the user executes an accessibility tests scan
