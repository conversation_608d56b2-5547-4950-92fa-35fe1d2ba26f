@reference-data
@accessibility
Feature: accessibility-flow-reference-data-labor-resource
    # This feature tests accessibility compliance of the Labor Resource page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access LaborResource page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LaborResource"
        Then the "Labor resources" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Antoinette Marie" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        Then the "Labor resource Antoinette Marie" titled page is displayed

    Scenario: Scan LaborResource page - General Tab
        And selects the "General" labelled navigation anchor on the main page
        Then the "General" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan LaborResource page - Capabilities Tab
        And selects the "Capabilities" labelled navigation anchor on the main page
        Then the "Capabilities" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan LaborResource page - Cost Tab
        And selects the "Cost" labelled navigation anchor on the main page
        Then the "Cost" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
