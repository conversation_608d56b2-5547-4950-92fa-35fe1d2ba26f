@distribution
@accessibility
Feature: accessibility-flow-distribution-sales-shipment
    # This feature tests accessibility compliance of the Sales Shipment page, scanning the page on all the tabs to search for any accessibility issue

    Scenario: Access SalesShipment page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed

        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "SH240003" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        Then the "Sales shipment SH240003" titled page is displayed

    Scenario: Scan SalesShipment page - Lines Tab
        And selects the "Lines" labelled navigation anchor on the main page
        Then the "Lines" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesShipment page - Information Tab
        And selects the "Information" labelled navigation anchor on the main page
        Then the "Information" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesShipment page - Shipping Tab
        And selects the "Shipping" labelled navigation anchor on the main page
        Then the "Shipping" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesShipment page - Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        Then the "Financial" labelled navigation anchor is selected
        And the user executes an accessibility tests scan

    Scenario: Scan SalesShipment page - Notes Tab
        And selects the "Notes" labelled navigation anchor on the main page
        Then the "Notes" labelled navigation anchor is selected
        And the user executes an accessibility tests scan
