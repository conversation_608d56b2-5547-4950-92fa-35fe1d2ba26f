#This test can only be executed with sage.
#The goal of this test is to create stock receipt with serial numbers.
#Note the Serial number option is activiated on a pre-requisite test within the pipeline, to execute this test manually-
#on QA layer you need to activate the serial number option manually in your environment

@inventory
Feature: stock-flow-stock-receipt-with-serial-number

    Scenario: create stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Giving the stock receiptt number and Site
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        When the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        #Adding stock to the line
        When the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "Light emitting diode" in the reference field
        And the user selects "Light emitting diode" in the reference field
        Then the value of the reference field is "Light emitting diode"
        When the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        When the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        Then the value of the reference field is "Accepted"
        #Accept
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Saving
        And the user clicks the "Save" labelled business action button on the main page
        #The notification moves too fast and the bot can't read the notification, that's why I commented the below line.
        #Then a toast containing text "Record updated" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SRNUM]"

    Scenario: Adding stock details
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the main page
        And the user selects the row with text "[ENV_SRNUM]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        #Verifying that stock details was entered
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Light emitting diode" in the "Item" labelled column header of the table field
        #Verifying that serial numbers was entered
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Accepted" in the "Quality control *" labelled column header of the table field
        And the value of the "Serial number" labelled nested text field of the selected row in the table field is "Assigned"
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field
        And the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user selects the row with text "5" in the "Quantity" labelled column header of the table field
        And the user waits 1 seconds
        And the user clicks the "OK" labelled business action button on a modal
        #Saving and Posting
        When the user clicks the "Save" labelled business action button on the main page
        Then the user clicks the "Post stock" labelled business action button on the main page

    #Verifying that the Stock Receipt was closed
    Scenario: Verify status of stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "stockReceipts" labelled table field on the main page
        And the user selects the row with text "[ENV_SRNUM]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Verifying the allocation of serial number after the stock receipt is closed
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Accepted" in the "Quality control *" labelled column header of the table field
        And the value of the "Serial number" labelled nested text field of the selected row in the table field is "Assigned"
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field
        And the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user selects the row with text "5" in the "Quantity" labelled column header of the table field
        And the user waits 1 seconds
        And the user clicks the "Cancel" labelled business action button on a modal
