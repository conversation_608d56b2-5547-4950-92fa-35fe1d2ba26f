#This test can only be executed with sage.
#The goal of this test is to verify the stock count flow with serial numbers
@inventory
Feature: stock-flow-stock-count-with-serial-numbers

    Scenario: 01 - Verify the information on the stock detailed inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Sandfeld" in the filter of the table field
        And the user ticks the item with text "Sandfeld" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Stock Count Item" in the filter of the table field
        And the user ticks the item with text "Stock Count Item" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        When the user selects the row with text "Bulk 01" in the "Location" labelled column header of the table field
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "4 each"
        # Not yet available:
        # And the value of the "Starting serial number" labelled nested text field of the selected row in the table field is "SN000029"
        # Then the value of the "Ending serial number" labelled nested text field of the selected row in the table field is "SN000032"

        #stock check for 2nd line
        When the user selects the row with text "Bulk 02" in the "Location" labelled column header of the table field
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "2 each"
    # Not yet available:
    # And the value of the "Starting serial number" labelled nested text field of the selected row in the table field is "SN000033"
    # Then the value of the "Ending serial number" labelled nested text field of the selected row in the table field is "SN000034"

    Scenario: 02 - Verify the user is able to create a stock count
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockCountCreation"
        Then the "Stock count creation" titled page is displayed

        When the user clicks the "Create" labelled business action button on the main page
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        Then the user selects "Sandfeld" in the reference field
        When the user selects the "From item" labelled reference field on the main page
        And the user writes "Stock Count Item" in the reference field
        Then the user selects "Stock Count Item" in the reference field
        When the user selects the "To item" labelled reference field on the main page
        And the user writes "Stock Count Item" in the reference field
        Then the user selects "Stock Count Item" in the reference field
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Stock count" in the text field
        #Storing Stock count description
        Then the user stores the value of the text field with the key "[ENV_SCount1]"

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        Then the user selects the "Select all" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then the user clicks the "Create" labelled business action button on the main page
        Then a toast containing text "The stock count was created successfully." is displayed

    Scenario: 03 - Doing the stock count
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        When the user selects the "stockCounts" labelled table field on the main page
        And the user selects the row with text "D1S" in the "Site" labelled column header of the table field
        And the user clicks the "Site" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_SCount1]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Selecting and storing a Stock Count number
        And the user selects the "number" bound text field on the main page
        Then the user stores the value of the text field with the key "[ENV_StockCOUNTNUM01]"
        #Starting the count for location, BULK 01
        When the user clicks the "Start" labelled business action button on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "4 each" in the "Stock quantity" labelled column header of the table field
        Then the user writes "3" in the "Counted quantity" labelled nested numeric field of the selected row in the table field
        #open serial numbers info
        When the user selects the "lines" bound table field on the main page
        And the user waits 2 seconds
        And the user selects the row 1 of the table field
        And the user clicks the "Serial numbers" dropdown action of the selected row of the table field
        And the user selects the "serialNumberRanges" bound table field on the sidebar
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user writes "SN000029" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "SN000029" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "2" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on the sidebar
        Then the user clicks the "Save" labelled business action button on the main page
        #Checking the status & bar percentage for partially counted items
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "ID1212" in the "Item ID" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "countedSerialNumberPercentage" bound nested progress field of the selected row in the table field is "66.66666666666667"
        Then the value of the "status" bound nested text field of the selected row in the table field is "Stock count in progress"
        #Counting all stock
        When the user selects the "lines" bound table field on the main page
        And the user waits 2 seconds
        And the user selects the row 1 of the table field
        And the user clicks the "Serial numbers" dropdown action of the selected row of the table field
        And the user selects the "serialNumberRanges" bound table field on the sidebar
        And the user selects the row with text "SN000029" in the "From serial number" labelled column header of the table field
        And the user writes "3" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        #Checking the status & bar percentage for completed count of items
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "ID1212" in the "Item ID" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the value of the "countedSerialNumberPercentage" bound nested progress field of the selected row in the table field is "100"
        Then the value of the "status" bound nested text field of the selected row in the table field is "Counted"
        #Stock count for location:Bulk 02
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "2 each" in the "Stock quantity" labelled column header of the table field
        And the user writes "2" in the "Counted quantity" labelled nested numeric field of the selected row in the table field
        #open serial numbers
        And the user selects the row 2 of the table field
        And the user clicks the "Serial numbers" dropdown action of the selected row of the table field
        And the user selects the "serialNumberRanges" bound table field on the sidebar
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "SN000033" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "SN000033" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "2" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on the sidebar
        Then the user clicks the "Save" labelled business action button on the main page
        And the user waits 5 seconds
        #Checking the status & bar percentage for completed count of items
        When the user selects the "lines" bound table field on the main page
        And the user selects the row with text "ID1212" in the "Item ID" labelled column header of the table field
        And the user selects the row 2 of the table field
        And the value of the "countedSerialNumberPercentage" bound nested progress field of the selected row in the table field is "100"
        And the user selects the row 1 of the table field
        And the value of the "status" bound nested text field of the selected row in the table field is "Counted"
        Then the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 04 - Verify the status of the stock count
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        When the user selects the "stockCounts" labelled table field on the main page
        And the user selects the row with text "D1S" in the "Site" labelled column header of the table field
        And the user clicks the "Site" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_SCount1]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify status
        When the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Closed"
        And the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Completed"

    Scenario: 05 - Verify that the information is updated on the stock detailed inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Sandfeld" in the filter of the table field
        And the user ticks the item with text "Sandfeld" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Stock Count Item" in the filter of the table field
        And the user ticks the item with text "Stock Count Item" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user selects the row with text "Bulk 01" in the "Location" labelled column header of the table field
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "3 each"
        # Not yet available:
        # And the value of the "Starting serial number" labelled nested text field of the selected row in the table field is "SN000029"
        # Then the value of the "Ending serial number" labelled nested text field of the selected row in the table field is "SN000031"
        #stock check for 2nd line
        When the user selects the row with text "Bulk 02" in the "Location" labelled column header of the table field
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "2 each"
# Not yet available:
# And the value of the "Starting serial number" labelled nested text field of the selected row in the table field is "SN000033"
# Then the value of the "Ending serial number" labelled nested text field of the selected row in the table field is "SN000034"
