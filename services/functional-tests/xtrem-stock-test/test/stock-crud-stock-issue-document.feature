
# This test aims to validate the stock issue document UI last updates.
# Focus on verifying the new added components that changes the way the user create, update and delete lines.
# A new split button is added at the top of the grid to enable the user to pick between two options to create new lines:
#     - Directly from a phantom row in the grid or;
#     - Open the new right sidebar component to create or modify the line.
# Also, it is necessary to cover the line actions directly in the modified grid and inside the sidebar at the top kebab button,
# in order to ensure all the actions are working in the same correct way in both cases.
# Additionally, a confirmation delete dialog is also implemented, both in the grid line and in the sidebar.

@inventory
Feature: stock-crud-stock-issue-document
    Scenario: 01 - Create new stock issue record and fill mandatory header fields
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "reason" labelled reference field on the main page
        And the user writes "Theft or loss" in the reference field
        And the user selects "Theft or loss" in the reference field

    Scenario: 02 - Create a line directly from the grid phantom row
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Electrical connector" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Electrical connector" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user selects the row with text "10 each" in the "Quantity" labelled column header of the table field

    Scenario: 03 - Create a line from the side panel using the "Apply" button (right sidebar closes)
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Electrical connector" in the reference field
        And the user selects "Electrical connector" in the reference field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "11" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "11 each" in the "Quantity" labelled column header of the table field

    Scenario: 04 - Create a line from the side panel using the "Apply and add new" button (right sidebar remains open)
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Electrical connector" in the reference field
        And the user selects "Electrical connector" in the reference field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        And the user clicks the "Apply and add new" button of the dialog on the sidebar
        And the "Add new line" titled sidebar is displayed
        And the user clicks the Close button of the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "12 each" in the "Quantity" labelled column header of the table field

    Scenario: 05 - Modify the line in the grid directly
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "12 each" in the "Quantity" labelled column header of the table field
        And the user writes "13" in the "Quantity" labelled nested reference field of the selected row in the table field
        And the user presses Tab
        And the user selects the row with text "13 each" in the "Quantity" labelled column header of the table field

    Scenario: 06 - Modify a line from the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "11 each" in the "Quantity" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "16" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "16 each" in the "Quantity" labelled column header of the table field

    Scenario: 07 - Check "Stock details" line action from the grid
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record created" is displayed
        And the user dismisses all the toasts
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "11 each" in the "Quantity" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Details required"
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "11 each" in the "Quantity" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Details entered"

    Scenario: 08 - Check "Stock details" line action from the sidebar
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user clicks the "Stock details" labelled more actions button in the sidebar header
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "OK" button of the Confirm dialog
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        And the user dismisses all the toasts
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "11 each" in the "Quantity" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Details required"

    Scenario: 09 - Check "Delete" line action new dialog from the grid
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "11 each" in the "Quantity" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        And the user dismisses all the toasts

    Scenario: 10 - Check "Delete" line action new dialog from from the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "13 each" in the "Quantity" labelled column header of the table field
        And the user clicks the "Open line panel" inline action button of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the sidebar header
        And the user clicks the "Delete" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        And a toast with text "Record updated" is displayed
        And the user dismisses all the toasts
