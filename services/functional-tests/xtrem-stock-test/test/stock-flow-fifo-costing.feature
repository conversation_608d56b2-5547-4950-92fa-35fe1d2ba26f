
# The purpose of this test is to check the flow of fifo costing through Stock Receipt -> Purchase Receipt -> Purchase Invoice
@inventory
Feature: stock-flow-fifo-costing

    Scenario: 1 - Create Stock Receipt for "MyFIFO05" item with Quantitity = 10 and Actual Cost = 10
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed

        # make sure company is empty
        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        And the user selects the "Date" labelled date field on the main page
        And the user stores the value of the date field with the key "[ENV_Receipt_Date]"

        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        Then the user selects the "Item" labelled reference field on the sidebar
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field

        Then the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field

        Then the user selects the "orderCost" bound numeric field on the sidebar
        And the user writes "10" in the numeric field

        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        When the user selects the "Stock details" labelled table field on a modal
        #Adding the line and selecting the existing lot number
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row with text "10 each" in the "Quantity in stock unit" labelled column header of the table field

        And the user clicks the "OK" labelled business action button on a modal
        #Save and posting stock receipt
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 2 - Create Stock Receipt for "MyFIFO05" item with Quantitity = 10 and Actual Cost = 15

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed

        # make sure company is empty
        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        And the user selects the "Date" labelled date field on the main page
        And the user stores the value of the date field with the key "[ENV_Receipt_Date2]"

        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        Then the user selects the "Item" labelled reference field on the sidebar
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field

        Then the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field

        Then the user selects the "orderCost" bound numeric field on the sidebar
        And the user writes "15" in the numeric field

        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        When the user selects the "Stock details" labelled table field on a modal
        #Adding the line and selecting the existing lot number
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row with text "10 each" in the "Quantity in stock unit" labelled column header of the table field

        And the user clicks the "OK" labelled business action button on a modal
        #Save and posting stock receipt
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 3 - Select FIFO cost tiers for item-site
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockFifoValuationInquiry"
        Then the "FIFO cost tier" titled page is displayed

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        Then the user selects the "Item" labelled reference field on the main page
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        Then the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Results" labelled table field on the main page

        And the user selects the row 1 of the table field
        And the value of the "Receipt date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "£ 10.0000"
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "£ 100.00"

        And  the value of the "Receipt date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the user selects the row 2 of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "£ 15.0000"
        And the value of the "Amount" labelled nested numeric field of the selected row in the table field is "£ 150.00"

    Scenario: 4 - Create/post an issue for the item: FIFO-CR
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed

        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field

        And the user selects the floating row of the table field
        And the user writes "12" in the "quantityInStockUnit" bound nested numeric field of the selected row in the table field
        And the user writes "MyFIFO05" in the "item__name" bound nested reference field of the selected row in the table field
        And the user selects "MyFIFO05" in the "item__name" bound nested field of the selected row in the table field
        And the user presses Control+Enter

        And the user selects the row 1 of the table field
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        When the user selects the "Stock details" labelled table field on a modal

        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "saveStockIssue" bound business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 5 - Select FIFO cost tiers for item-site
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockFifoValuationInquiry"
        Then the "FIFO cost tier" titled page is displayed

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        Then the user selects the "Item" labelled reference field on the main page
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        Then the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Results" labelled table field on the main page

        And the user selects the row 1 of the table field
        And the value of the "Receipt date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Sequence" labelled nested reference field of the selected row in the table field is "2"
        And the value of the "Quantity" labelled nested reference field of the selected row in the table field is "8 each"
        And the value of the "Original quantity" labelled nested reference field of the selected row in the table field is "10 each"
        And the value of the "Unit cost" labelled nested reference field of the selected row in the table field is "£ 15.0000"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "£ 120.00"


    Scenario: 6 - Select stock journal transactions for item-site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed
        #Verify empty table field

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "MyFIFO05" in the filter of the table field
        And the user ticks the item with text "MyFIFO05" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user selects the row with text "Stock issue line" in the "Document line type" labelled column header of the table field

        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-12 each"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "£ -130.00"
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "£ 10.8333"
        And the value of the "Valued cost" labelled nested numeric field of the selected row in the table field is "£ 10.8333"
        And the value of the "Movement amount" labelled nested numeric field of the selected row in the table field is "£ -130.00"

    Scenario: 7 - Create/post a (purchase) receipt for the item FIFO-CR
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseReceipt"
        Then the "Purchase receipts" titled page is displayed
        #Verify empty table field

        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Receiving site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Supplier" labelled reference field on the main page
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field

        # Add line through sidebar
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar

        # Item
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        # Quantity in purchase unit
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field

        # Move to price tab
        And selects the "Price" labelled navigation anchor on the sidebar

        # Gross price
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field

        And the user clicks the "Apply" button of the dialog on the sidebar

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "MyFIFO05" in the "Item" labelled column header of the table field

        # Allocate stock
        And the user clicks the "Stock details" dropdown action of the selected row of the table field

        # Add stock detail
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field

        # Select line and Add detail inline
        And the user selects the row with text "5 each" in the "quantityInStockUnit" bound column header of the table field

        # Status
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "MyFIFO05" in the "Item" labelled column header of the table field

        And the user clicks the "Tax details" dropdown action of the selected row of the table field
        When the user selects the "Taxes" labelled table field on a modal
        And the user selects the row with text "Value Added Tax" in the "Category" labelled column header of the table field
        And the user clicks the "Tax" labelled nested field of the selected row in the table field
        And the user opens the lookup dialog in the "Tax" labelled nested reference field of the selected row in the table field
        And the user selects the "taxReference" bound table field on a modal
        And the user selects the row with text "UK Purchase Goods Standard Rate" in the "Name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        # Save
        And the user clicks the "save" labelled business action button on the main page

        Then a toast containing text "Record created" is displayed

        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 8 - Select FIFO cost tiers for item-site
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockFifoValuationInquiry"
        Then the "FIFO cost tier" titled page is displayed

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        Then the user selects the "Item" labelled reference field on the main page
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        Then the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Results" labelled table field on the main page

        And the user selects the row 2 of the table field
        And the value of the "Receipt date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Sequence" labelled nested reference field of the selected row in the table field is "3"
        And the value of the "Quantity" labelled nested reference field of the selected row in the table field is "5 each"
        And the value of the "Original quantity" labelled nested reference field of the selected row in the table field is "5 each"
        And the value of the "Unit cost" labelled nested reference field of the selected row in the table field is "£ 20.0000"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "£ 100.00"

    Scenario: 9 - Create/post an issue for the item: MyFIFO05
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed

        When the user clicks the "Create" labelled business action button on the navigation panel

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field

        And the user selects the floating row of the table field
        And the user writes "10" in the "quantityInStockUnit" bound nested numeric field of the selected row in the table field
        And the user writes "MyFIFO05" in the "item__name" bound nested reference field of the selected row in the table field
        And the user selects "MyFIFO05" in the "item__name" bound nested field of the selected row in the table field
        And the user presses Control+Enter

        And the user selects the row 1 of the table field
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        When the user selects the "Stock details" labelled table field on a modal

        And the user selects the row 1 of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "saveStockIssue" bound business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 10 - Select FIFO cost tiers for item-site
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockFifoValuationInquiry"
        Then the "FIFO cost tier" titled page is displayed

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        Then the user selects the "Item" labelled reference field on the main page
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        Then the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Results" labelled table field on the main page

        And the user selects the row 1 of the table field
        And the value of the "Receipt date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Sequence" labelled nested reference field of the selected row in the table field is "3"
        And the value of the "Quantity" labelled nested reference field of the selected row in the table field is "3 each"
        And the value of the "Original quantity" labelled nested reference field of the selected row in the table field is "5 each"
        And the value of the "Unit cost" labelled nested reference field of the selected row in the table field is "£ 20.0000"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "£ 60.00"

    # TODO - Enable this scenarios after https://jira.sage.com/browse/XT-75382 is fixed.
    Scenario: 11 - Purchase invoice creation - MyFIFO05
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-purchasing/PurchaseInvoice"
        Then the "Purchase invoices" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in fields on main page
        And the user selects the "Financial site *" labelled reference field on the main page
        And the user writes "UK LIMITED" in the reference field
        And the user selects "UK LIMITED" in the reference field
        And the user selects the "Bill-by supplier *" labelled reference field on the main page
        And the user writes "Lyreco" in the reference field
        And the user selects "Lyreco" in the reference field
        And the user selects the "Total supplier amount excl. tax *" labelled numeric field on the main page
        And the user writes "125" in the numeric field
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "Service" in the reference field
        And the user selects "Service" in the reference field
        And the user selects the "Quantity in purchase unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "25" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 12 - Select FIFO cost tiers for item-site
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockFifoValuationInquiry"
        Then the "FIFO cost tier" titled page is displayed

        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        Then the user selects the "Item" labelled reference field on the main page
        And the user writes "MyFIFO05" in the reference field
        And the user selects "MyFIFO05" in the reference field

        Then the user clicks in the "searchButton" bound button field on the main page
        And the user selects the "Results" labelled table field on the main page

        And the user selects the row 1 of the table field
        And the value of the "Receipt date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Sequence" labelled nested reference field of the selected row in the table field is "3"
        And the value of the "Quantity" labelled nested reference field of the selected row in the table field is "3 each"
        And the value of the "Original quantity" labelled nested reference field of the selected row in the table field is "5 each"
        And the value of the "Unit cost" labelled nested reference field of the selected row in the table field is "£ 20.0000"
        And the value of the "Amount" labelled nested reference field of the selected row in the table field is "£ 60.00"
        And the value of the "Non-absorbed amount" labelled nested reference field of the selected row in the table field is "£ 0.00"

    Scenario: 13 - Select stock journal transactions for item-site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed
        #Verify empty table field

        And the user selects the "$navigationPanel" bound table field on the main page

        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "MyFIFO05" in the filter of the table field
        And the user ticks the item with text "MyFIFO05" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user selects the row with text "Stock issue line" in the "Document line type" labelled column header of the table field

        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "-12 each"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "£ -130.00"
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "£ 10.8333"
        And the value of the "Valued cost" labelled nested numeric field of the selected row in the table field is "£ 10.8333"
        And the value of the "Movement amount" labelled nested numeric field of the selected row in the table field is "£ -130.00"
        And the user selects the row 1 of the table field
        And the value of the "Non-absorbed amount" labelled nested reference field of the selected row in the table field is "£ 0.00"
