# The goals of this test are;
# 1. to verify that a new stock value-change inherits dimensions from the site and according
#    to the company default dimension rules,
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly

@inventory
Feature: stock-flow-stock-value-change-default-dimensions

    Scenario: 01 - Create a stock value change and verify dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueChange"
        Then the "Stock value changes" titled page is displayed
        # Create stock value change
        When the user clicks the "Create" labelled business action button on the navigation panel
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Site ZA3" in the reference field
        And the user selects "Site ZA3" in the reference field
        And the user selects the "Item" labelled reference field on the main page
        And the user writes "Value item" in the reference field
        And the user selects "Value item" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add" labelled header action button of the table field
        And the table field is not empty
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        # Verify dimensions
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update dimensions (Apply to new lines only) and verify stock value change line dimensions
        # Update dimensions
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        When the user clicks the "Set default dimensions" labelled header action button of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add new stock value change line
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Add" labelled header action button of the table field
        And the table field is not empty
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify updated stock count line dimensions
        And the user selects the row 1 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        And the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update dimensions (Apply to all lines) and verify stock value change line dimensions
        # Update dimension
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Set default dimensions" labelled header action button of the table field

        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Residential" in the reference field
        And the user selects "Residential" in the reference field
        And the user stores the value of the reference field with the key "Residential"

        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user stores the value of the reference field with the key "Operations"

        Then the user clicks the "Apply to all lines" labelled business action button on a modal

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Verify updated stock count line dimensions
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"

        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal

        # Delete stock value change
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
