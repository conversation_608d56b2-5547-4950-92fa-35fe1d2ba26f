# The goal of this test is to check whether the TOTAL STOCK VALUE field is calculated / displayed
# or not depending on selected criteria (single-site / multiple sites from different companies / multiple sites
# belonging to the same company)
# Script to be amemded as soon as enhancement request https://jira.sage.com/browse/XT-62319 is done

@inventory
Feature: stock-flow-stock-valuation-report-total-stock-value-check

    Scenario: Add lock - LCK_VAL1

        Given the user opens the application on a HD desktop
        And the user adds the lock entry "LCK_VAL1"

    Scenario: Launch stock valuation report for a single company

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockValuation"
        Then the "Stock valuation" titled page is displayed

        # select company S1
        Given the user selects the "Company" labelled reference field on the main page
        And the user writes "S1" in the reference field
        And the user selects "Société S1" in the reference field

        # check site is disabled / empty
        When the user selects the "Site" labelled multi reference field on the main page
        Then the multi reference field is disabled
        And the value of the multi reference field is ""

        # from item = Stock valuation report AUC - Item 1
        Given the user selects the "From item" labelled reference field on the main page
        And the user writes "Stock valuation report AUC" in the reference field
        And the user selects "Stock valuation report AUC - Item 1" in the reference field

        # to item = Stock valuation report AUC - Item 1
        Given the user selects the "To item" labelled reference field on the main page
        And the user writes "Stock valuation report AUC" in the reference field
        And the user selects "Stock valuation report AUC - Item 1" in the reference field

        # date = current date
        Given the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"

        # tick checkbox display zero values
        Given the user selects the "Display zero values" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # control TOTAL STOCK VALUE is displayed on top of the grid
        When the user selects the "TOTAL STOCK VALUE" labelled tile aggregate field on the main page
        Then the tile aggregate field is enabled
        And the value of the tile aggregate field is "€3,300.00"

        # check values in the table
        Given the user selects the "itemSites" bound table field on the main page
        # filter on site = Site de Chavanod

        And the user filters the "site" labelled column in the table field with value "Site de Chavanod"
        When the user selects the row with text "SVR-ITEM-AUC-1" in the "Item ID" labelled column header of the table field
        Then the value of the "Item ID" labelled nested reference field of the selected row in the table field is "SVR-ITEM-AUC-1"
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "300.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "15.00 each"

        And the user filters the "site" labelled column in the table field with value "Site de Rumilly"
        When the user selects the row with text "SVR-ITEM-AUC-1" in the "Item ID" labelled column header of the table field
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "3,000.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "15.00 each"

    Scenario: Launch stock valuation report for a selection of sites - same company

        # remove company S1
        Given the user selects the "Company" labelled reference field on the main page
        And the user clears the reference field

        # check site is enabled / empty
        When the user selects the "Site" labelled multi reference field on the main page
        Then the multi reference field is enabled
        And the user writes "Site de" in the multi reference field

        And the user selects "Site de Chavanod|Site de Rumilly" in the multi reference field

        Then the value of the multi reference field is "Site de Chavanod|Site de Rumilly"

        # from item = Stock valuation report AUC - Item 1
        Given the user selects the "From item" labelled reference field on the main page
        And the user writes "Stock valuation report AUC" in the reference field
        And the user selects "Stock valuation report AUC - Item 1" in the reference field

        # to item = Stock valuation report AUC - Item 2
        Given the user selects the "To item" labelled reference field on the main page
        And the user writes "Stock valuation report AUC" in the reference field
        And the user selects "Stock valuation report AUC - Item 2" in the reference field

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # control TOTAL STOCK VALUE is hidden on top of the grid
        When the user selects the "totalStockValue" bound tile aggregate field on the main page

        Then the "totalStockValue" bound tile aggregate field is hidden


    Scenario: Launch stock valuation report for a selection of sites - various companies

        # check site is enabled / empty
        When the user selects the "Site" labelled multi reference field on the main page
        Then the user clears the multi reference field
        And the user writes "Swindon" in the multi reference field
        And the user selects "Swindon" in the multi reference field
        And the user writes "Hampton" in the multi reference field
        And the user selects "TE Hampton" in the multi reference field
        And the user writes "Site de" in the multi reference field
        And the user selects "Site de Chavanod|Site de Rumilly" in the multi reference field

        Then the value of the multi reference field is "Swindon|TE Hampton|Site de Chavanod|Site de Rumilly"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # control TOTAL STOCK VALUE is displayed on top of the grid should fail until
        # https://jira.sage.com/browse/XT-98655 is fixed
        When the user selects the "totalStockValue" bound tile aggregate field on the main page

        Then the "totalStockValue" bound tile aggregate field is displayed

    Scenario: Check behavior of checkbox "Display zero values"

        # change site selection to "Site de Rumilly" only
        When the user selects the "Site" labelled multi reference field on the main page
        Then the user clears the multi reference field
        And the user writes "Site de" in the multi reference field
        And the user selects "Site de Rumilly" in the multi reference field

        Then the value of the multi reference field is "Site de Rumilly"

        # from item = Stock valuation report AUC - Item 2
        Given the user selects the "From item" labelled reference field on the main page
        And the user writes "Stock valuation report AUC" in the reference field
        And the user selects "Stock valuation report AUC - Item 2" in the reference field

        # to item = Stock valuation report AUC - Item 2
        Given the user selects the "To item" labelled reference field on the main page
        And the user writes "Stock valuation report AUC" in the reference field
        And the user selects "Stock valuation report AUC - Item 2" in the reference field

        # ensure checkbox "display zero values" is ticked
        Given the user selects the "Display zero values" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # control TOTAL STOCK VALUE is displayed on top of the grid should fail until
        # https://jira.sage.com/browse/XT-98655 is fixed
        When the user selects the "totalStockValue" bound tile aggregate field on the main page

        Then the "totalStockValue" bound tile aggregate field is displayed

        # check values in the table
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "SVR-ITEM-AUC-2" in the "Item ID" labelled column header of the table field
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "0.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "0.00 each"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "0.00 each"

        # untick checkbox "display zero values"
        Given the user selects the "Display zero values" labelled checkbox field on the main page
        And the user unticks the checkbox field
        Then the value of the checkbox field is "false"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # check the grid is now displayed empty
        Given the user selects the "itemSites" bound table field on the main page
        Then the table field is empty

    Scenario: Remove lock - LCK_VAL1

        And the user removes the lock entry "LCK_VAL1"
