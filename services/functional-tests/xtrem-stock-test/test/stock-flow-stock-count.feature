# The goal of this test is to verify the stock count creation flow is working properly as expected.

@inventory
Feature: stock-flow-stock-count

    Scenario: Verify the user is able to create a stock count
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockCountCreation"

        Then the "Stock count creation" titled page is displayed

        When the user clicks the "Create" labelled business action button on the main page
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        When the user selects the "From item" labelled reference field on the main page
        And the user writes "Item to be counted" in the reference field
        And the user selects "Item to be counted" in the reference field
        When the user selects the "To item" labelled reference field on the main page
        And the user writes "Item to be counted" in the reference field
        And the user selects "Item to be counted" in the reference field
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Stock Count 1" in the text field
        And the user stores the value of the text field with the key "[ENV_SCOUNTDESC02]"
        And the user waits for 10 seconds

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user dismisses all the toasts
        Then the user selects the "Select all" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then the user clicks the "Create" labelled business action button on the main page
        Then a toast containing text "The stock count was created successfully." is displayed

    Scenario: Verify the user is able to enter the counted stock on a stock count
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        When the user selects the "stockCounts" labelled table field on the navigation panel
        And the user selects the row with text "110" in the "Site" labelled column header of the table field
        When the user clicks the "Site" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_SCOUNTDESC02]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        ##Selecting and storing a SC number
        And the user selects the "Site" labelled reference field on the main page
        And the value of the reference field is "TE Hampton"
        And the user selects the "Description" labelled text field on the main page
        And the value of the text field is "[ENV_SCOUNTDESC02]"
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "To be counted"
        And the user refreshes the screen
        And the user selects the "Stock status" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user selects the "From item" labelled reference field on the main page
        And the value of the reference field is "Item to be counted"
        And the user selects the "To item" labelled reference field on the main page
        And the value of the reference field is "Item to be counted"
        And the user selects the "number" bound text field on the main page
        And the user stores the value of the text field with the key "[ENV_COUNTNUM01]"
        And the user clicks the "Start" labelled business action button on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "ID-03" in the "Item ID" labelled column header of the table field
        And the user writes "30" in the "Counted quantity" labelled nested numeric field of the selected row in the table field
        When the user selects the row with text "Quality control" in the "stockStatus__name" bound column header of the table field
        And the user selects the row 2 of the table field
        And the user clicks the "Exclude" dropdown action of the selected row of the table field
        Then the value of the "status" bound nested text field of the selected row in the table field is "Excluded"
        And the value of the "Counted quantity" labelled nested text field of the selected row in the table field is "0 each"
        When the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        When the user clicks the "Post stock" labelled business action button on the main page

    Scenario: Verify the user is able to check the status of the stock count after posting
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        When the user selects the "stockCounts" labelled table field on the navigation panel
        And the user selects the row with text "110" in the "Site" labelled column header of the table field
        When the user clicks the "Site" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_SCOUNTDESC02]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Status" labelled label field on the main page
        ##Verify stock status
        Then the value of the label field is "Closed"

    Scenario: Verify the user is able to verify details of the stock count on the stock details page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page

        And the user opens the filter of the "Site" labelled column in the table field
        And the user searches "TE Hampton" in the filter of the table field
        And the user ticks the item with text "TE Hampton" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item to be counted" in the filter of the table field
        And the user ticks the item with text "Item to be counted" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        When the user selects the row with text "Accepted" in the "Quality Control" labelled column header of the table field
        Then the value of the "On-hand quantity" labelled nested numeric field of the selected row in the table field is "30 each"
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "30 each"
        When the user selects the row with text "Quality control" in the "Quality Control" labelled column header of the table field
        Then the value of the "On-hand quantity" labelled nested numeric field of the selected row in the table field is "15 each"
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "15 each"

    Scenario: Verify the user is able to verify details of the stock count on the stock journal enquiry page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel

        # Enter site criteria = TE Hampton
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "TE Hampton" in the filter of the table field
        And the user ticks the item with text "TE Hampton" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Enter item criteria = Item to be counted
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item to be counted" in the filter of the table field
        And the user ticks the item with text "Item to be counted" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        ##Verifying the counted quantity for excluded item did not change
        When the user selects the row with text "Quality control" in the "Quality control" labelled column header of the table field
        Then the value of the "Quantity" labelled nested text field of the selected row in the table field is "15 each"
        ##Verifying the increased quantity
        When the user selects the row with text "Increase quantity" in the "Reason code" labelled column header of the table field
        Then the value of the "Quantity" labelled nested text field of the selected row in the table field is "5 each"
        And the value of the "movementAmount" bound nested text field of the selected row in the table field is "$ 80.00"
        When the user selects the row with text "25 each" in the "Quantity" labelled column header of the table field
        And the value of the "Quantity" labelled nested text field of the selected row in the table field is "25 each"
