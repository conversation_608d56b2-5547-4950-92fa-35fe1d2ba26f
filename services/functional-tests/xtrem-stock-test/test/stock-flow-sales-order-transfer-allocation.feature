# This test aims to verify more actions menu "Manage allocations"
# and the ability to transfer allocations from sales document (only sales order and sales shipment)

@inventory

Feature:  stock-flow-sales-order-transfer-allocation
    Scenario: 01 - Turn on allocationTransferOption
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
        Then the "Service options" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "allocationTransferOption"
        And the user selects the row 1 of the table field
        Then the user clicks the "Activate" dropdown action of the selected row of the table field
        Then a warn dialog appears on the main page
        When the user clicks the "OK" button of the Confirm dialog

        Then the "Service options" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "allocationTransferOption"
        And the user selects the row 1 of the table field
        Then the value of the "Active" labelled nested checkbox field of the selected row in the table field is "true"

    Scenario: 02 - Verify SO_TEST_ALLOC_1 items enabled/disabled buttons
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO_TEST_ALLOC_1"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO_TEST_ALLOC_1"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        # Find the line item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        Then the "Allocate stock" dropdown action of the selected row in the table field is enabled
        Then the "Manage allocations" dropdown action of the selected row in the table field is disabled

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service transfer alloc" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not managed"
        Then the "Manage allocations" dropdown action of the selected row in the table field is hidden
        And the "Allocate stock" dropdown action of the selected row in the table field is hidden

    Scenario: 03 - Verify SO_TEST_ALLOC_2 items enabled/disabled buttons
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO_TEST_ALLOC_2"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO_TEST_ALLOC_2"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Quote"
        # Find the line item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        Then the "Allocate stock" dropdown action of the selected row in the table field is disabled
        Then the "Manage allocations" dropdown action of the selected row in the table field is disabled

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service transfer alloc" in the "Item" labelled column header of the table field
        Then the "Manage allocations" dropdown action of the selected row in the table field is hidden
        And the "Allocate stock" dropdown action of the selected row in the table field is hidden

    Scenario: 04 - Verify SO_TEST_ALLOC_1 items enabled/disabled buttons
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO_TEST_ALLOC_3"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO_TEST_ALLOC_3"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        # Find the line item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        Then the "Allocate stock" dropdown action of the selected row in the table field is enabled
        Then the "Manage allocations" dropdown action of the selected row in the table field is enabled

    Scenario: 05 - Confirm and save SO_TEST_ALLOC_2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO_TEST_ALLOC_2"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO_TEST_ALLOC_2"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Quote"

        When the user clicks the "Confirm" labelled business action button on the main page
        And the user waits 5 seconds
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed

        And the user waits 2 seconds

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        # Find the line item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "25000031" in the "Lot" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal

        # Find the line item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        Then the "Allocate stock" dropdown action of the selected row in the table field is enabled
        Then the "Manage allocations" dropdown action of the selected row in the table field is disabled

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Service transfer alloc" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not managed"
        Then the "Manage allocations" dropdown action of the selected row in the table field is hidden
        And the "Allocate stock" dropdown action of the selected row in the table field is hidden

    Scenario: 05 - View and transfer allocation on SO_TEST_ALLOC_3
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO_TEST_ALLOC_3"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO_TEST_ALLOC_3"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        # Find the line item
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the user clicks the "Manage allocations" dropdown action of the selected row of the table field

        Given the user selects the "Required quantity" labelled numeric field on a modal
        Then the value of the text field is "7"
        And the user selects the "Allocated quantity" labelled numeric field on a modal
        Then the value of the text field is "0"
        And the user selects the "Stock on hand" labelled numeric field on a modal
        Then the value of the text field is "100"
        And the user selects the "Stock available" labelled numeric field on a modal
        Then the value of the text field is "89"

        And the user selects the "stockAllocations" bound table field on a modal
        And the user selects the row with text "SO_TEST_ALLOC_1" in the "Document" labelled column header of the table field
        And the user clicks the "Transfer allocation" dropdown action of the selected row of the table field
        And the user clicks the "Transfer" button of the Confirm dialog
        Then a toast with text "Allocation transferred." is displayed

        And the user waits 5 seconds

        Given the user selects the "Required quantity" labelled numeric field on a modal
        Then the value of the text field is "7"
        And the user selects the "Allocated quantity" labelled numeric field on a modal
        Then the value of the text field is "5"
        And the user selects the "Stock on hand" labelled numeric field on a modal
        Then the value of the text field is "100"
        And the user selects the "Stock available" labelled numeric field on a modal
        Then the value of the text field is "89"

        And the user clicks the "Ok" labelled business action button on a modal

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Partially allocated"
        And the user clicks the "Manage allocations" dropdown action of the selected row of the table field

        And the user selects the "stockAllocations" bound table field on a modal
        And the user selects the row with text "SO_TEST_ALLOC_2" in the "Document" labelled column header of the table field
        And the user clicks the "Transfer allocation" dropdown action of the selected row of the table field
        And the user clicks the "Transfer" button of the Confirm dialog
        Then a toast with text "Allocation transferred." is displayed

        And the user waits 5 seconds

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"
        Then the "Manage allocations" dropdown action of the selected row in the table field is disabled

    Scenario: 06 - Check remaining allocations on sales orders SO_TEST_ALLOC_1

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO_TEST_ALLOC_1"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO_TEST_ALLOC_1"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        And selects the "Stock" labelled navigation anchor on the sidebar
        And the user selects the "Quantity to allocate" labelled numeric field on the sidebar
        Then the value of the numeric field is "5"
        And the user selects the "Allocated quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "0"
        And the user selects the "Remaining quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "5"

    Scenario: 07 - Check remaining allocations on sales orders SO_TEST_ALLOC_2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO_TEST_ALLOC_2"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO_TEST_ALLOC_2"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Partially allocated"
        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        And selects the "Stock" labelled navigation anchor on the sidebar
        And the user selects the "Quantity to allocate" labelled numeric field on the sidebar
        Then the value of the numeric field is "6"
        And the user selects the "Allocated quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "4"
        And the user selects the "Remaining quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "2"

    Scenario: 08 - Confirm sales order SO25003 and transfer allocations from sales shipment
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO250003"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO250003"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Quote"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "5 each"

        When the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The sales order was confirmed." is displayed

        And the user waits 2 seconds

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"

        And the user clicks the "Manage allocations" dropdown action of the selected row of the table field

        Given the user selects the "Required quantity" labelled numeric field on a modal
        Then the value of the text field is "5"
        And the user selects the "Allocated quantity" labelled numeric field on a modal
        Then the value of the text field is "0"
        And the user selects the "Stock on hand" labelled numeric field on a modal
        Then the value of the text field is "100"
        And the user selects the "Stock available" labelled numeric field on a modal
        Then the value of the text field is "93"

        And the user selects the "stockAllocations" bound table field on a modal
        And the user selects the row with text "SH250001" in the "Document" labelled column header of the table field
        And the user clicks the "Transfer allocation" dropdown action of the selected row of the table field
        And the user clicks the "Transfer" button of the Confirm dialog
        Then a toast with text "Allocation transferred." is displayed

        And the user waits 2 seconds

        Given the user selects the "Required quantity" labelled numeric field on a modal
        Then the value of the text field is "5"
        And the user selects the "Allocated quantity" labelled numeric field on a modal
        Then the value of the text field is "2"
        And the user selects the "Stock on hand" labelled numeric field on a modal
        Then the value of the text field is "100"
        And the user selects the "Stock available" labelled numeric field on a modal
        Then the value of the text field is "93"

        And the user selects the "stockAllocations" bound table field on a modal
        And the user selects the row with text "SO250002" in the "Document" labelled column header of the table field
        And the user clicks the "Transfer allocation" dropdown action of the selected row of the table field
        And the user clicks the "Transfer" button of the Confirm dialog
        Then a toast with text "Allocation transferred." is displayed

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Allocated"

    Scenario: 09 - Check remaining allocations on sales orders SO250002
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SO250002"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SO250002"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Partially allocated"

        And the user clicks the "Open line panel" inline action button of the selected row in the table field

        And selects the "Stock" labelled navigation anchor on the sidebar
        And the user selects the "Quantity to allocate" labelled numeric field on the sidebar
        Then the value of the numeric field is "5"
        And the user selects the "Allocated quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "2"
        And the user selects the "Remaining quantity" labelled numeric field on the sidebar
        Then the value of the numeric field is "3"

    Scenario: 10 - Check allocation on sales shipment SH250001
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesShipment"
        Then the "Sales shipments" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user clicks the option menu of the table field
        And the user clicks the "All statuses" value in the option menu of the table field
        When the user filters the "number" bound column in the table field with value "SH250001"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SH250001"
        And the user clicks the "Number" labelled nested field of the selected row in the table field

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item test aloc transfer sales" in the "Item" labelled column header of the table field
        And the value of the "Allocation status" labelled nested text field of the selected row in the table field is "Not allocated"
        And the value of the "Quantity in sales unit" labelled nested text field of the selected row in the table field is "2 each"
