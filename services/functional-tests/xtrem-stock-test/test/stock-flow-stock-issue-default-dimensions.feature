# The goals of this test are;
# 1. to verify that a new stock issue inherits dimensions from the site and according
#    to the company default dimension rules,
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly
@inventory
Feature: stock-flow-stock-issue-default-dimensions

    Scenario: 01 - Create stock issue and verify stock issue line dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        #Set dimensions from main list
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "number" bound column in the table field with value "SS230005"
        And the user selects the row 1 of the table field
        Then the value of the "number" bound nested text field of the selected row in the table field is "SS230005"
        And the user clicks the "Set dimensions" dropdown action of the selected row of the table field
        And the user waits 5 seconds
        And the dialog title is "Dimensions"
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the "Apply to all lines" button of the Custom dialog
        Then a toast containing text "Dimensions applied." is displayed
        # Create stock issue
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Site ZA3" in the reference field
        And the user selects "Site ZA3" in the reference field
        And the user selects the "Date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Pressure sensor" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Pressure sensor" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity" labelled nested numeric field of the selected row in the table field
        # And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        # And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user presses Control+Enter
        # Verify new stock issue line dimensions
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update stock issue dimensions (Apply to new lines only) and verify stock issue line dimensions
        # Update dimension
        When the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add new stock issue line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Pressure transmitter" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Pressure transmitter" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "50" in the "Quantity" labelled nested numeric field of the selected row in the table field
        # And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        # And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user presses Control+Enter
        # Verify new stock issue line dimensions (Pressure transmitter)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock issue line dimensions (Pressure sensor)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update stock issue dimensions (Apply to all lines) and verify stock issue line dimensions
        # Update dimension
        When the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        Then the user clicks the "Apply to all lines" labelled business action button on a modal
        # Add new stock issue line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Electrical connector" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Electrical connector" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "85" in the "Quantity" labelled nested numeric field of the selected row in the table field
        # And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        # And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user presses Control+Enter
        # Verify new stock issue line dimensions (Electrical connector)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Electrical connector" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is ""
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock issue line dimensions (Pressure transmitter)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock issue line dimensions (Pressure sensor)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
