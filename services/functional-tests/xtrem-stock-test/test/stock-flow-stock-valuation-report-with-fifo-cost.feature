# The goal of this test is to check that the stock valuation report shows the correct
# values in case of the item is managed with FIFO valuation method.
# Site used = Site de Chavanod
# Item used = SVR-ITEM-FIFO-1
# Existing stock journals
#   - 12/01/2023 | SR230042 | 10 each | order cost = € 5.00 | movement amount = € 50.00
#   - 12/02/2023 | PR230032 | 10 each | order cost = € 10.00 | movement amount = € 100.00
#   - 12/02/2023 | PR230032 | 0 each | order cost = € 4.00 | movement amount = € 40.00 | non-absorbed amount = € 10.00
#   - 12/03/2023 | PT230009 | - 2 each | order cost = € 10.00 | movement amount = € 20.00
#   - 12/04/2023 | SR230043 | 10 each | order cost = € 7.00 | movement amount = € 70.00

@inventory
Feature: stock-flow-stock-valuation-report-with-fifo-cost

    Scenario: Add lock - LCK_VAL1

        Given the user opens the application on a HD desktop
        And the user adds the lock entry "LCK_VAL1"

    Scenario: 0-Launch stock valuation report function and enter main criteria

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockValuation"
        Then the "Stock valuation" titled page is displayed

        # make sure company is empty
        Given the user selects the "company" labelled reference field on the main page
        And the user clears the reference field
        Then the value of the reference field is ""

        # Select site = Site de Chavanod
        Given the user selects the "Site" labelled multi reference field on the main page
        Then the multi reference field is enabled
        And the user clears the multi reference field
        When the user writes "Site de" in the multi reference field
        And the user selects "Site de Chavanod" in the multi reference field
        Then the value of the multi reference field is "Site de Chavanod"

        # from item = Stock valuation report FIFO - Item 1
        Given the user selects the "From item" labelled reference field on the main page
        And the user writes "Stock valuation report FIFO" in the reference field
        And the user selects "Stock valuation report FIFO - Item 1" in the reference field

        # to item = Stock valuation report FIFO - Item 1
        Given the user selects the "To item" labelled reference field on the main page
        And the user writes "Stock valuation report FIFO" in the reference field
        And the user selects "Stock valuation report FIFO - Item 1" in the reference field

    Scenario: 1-Launch first calculation on December 1st 2023

        # date = 12/01/2023 (only one stock receipt exists with quantity = 10 each and unit cost = 5 €)
        Given the user selects the "Date" labelled date field on the main page
        When the user writes "12/01/2023" in the date field
        Then the value of the date field is "12/01/2023"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds
        And the user refreshes the screen

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # check results in the grid
        Given the user selects the "itemSites" bound table field on the main page
        # And the user waits 15 seconds
        When the user selects the row with text "SVR-ITEM-FIFO-1" in the "itemId " labelled column header of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "10.00 each"
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "50.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "5.00 each"

    Scenario: 2-Launch second calculation on December 2nd 2023

        # date = 12/02/2023 (additional receipt of 10 + cost correction due to invoice including variance)
        Given the user selects the "Date" labelled date field on the main page
        When the user writes "12/02/2023" in the date field
        Then the value of the date field is "12/02/2023"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # check results in the grid
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "SVR-ITEM-FIFO-1" in the "itemId" labelled column header of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "20.00 each"
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "190.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "9.50 each"

    Scenario: 3-Launch third calculation on December 3rd 2023

        # date = 12/03/2023 (purchase return of 2 posted)
        Given the user selects the "Date" labelled date field on the main page
        When the user writes "12/03/2023" in the date field
        Then the value of the date field is "12/03/2023"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # check results in the grid
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "SVR-ITEM-FIFO-1" in the "itemId" labelled column header of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "18.00 each"
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "170.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "9.44 each"

    Scenario: 4-Launch fourth calculation on December 4th 2023

        # date = 12/04/2023 (additional receipt of 10)
        Given the user selects the "Date" labelled date field on the main page
        When the user writes "12/04/2023" in the date field
        Then the value of the date field is "12/04/2023"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # check results in the grid
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "SVR-ITEM-FIFO-1" in the "itemId" labelled column header of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "28.00 each"
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "240.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "8.57 each"

    Scenario: 5-Launch fifth calculation with current date

        # date = current date (nothing has changed from the 12/04/2023 - results should be the same)
        Given the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # check results in the grid
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "SVR-ITEM-FIFO-1" in the "itemId" labelled column header of the table field
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "28.00 each"
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "240.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "8.57 each"

    Scenario: 6-Launch sixth calculation earlier than 12/01/2023

        # date = 11/30/2023 (no existing stock journals - grid should display zero values)
        Given the user selects the "Date" labelled date field on the main page
        When the user writes "11/30/2023" in the date field
        Then the value of the date field is "11/30/2023"

        # ensure checkbox "display zero values" is ticked
        Given the user selects the "Display zero values" labelled checkbox field on the main page
        When the user ticks the checkbox field
        Then the value of the checkbox field is "true"

        # click Run button to calculate results
        And the user clicks the "Run" labelled business action button on the main page

        # wait the process to be finished
        And the user waits 5 seconds

        # check stock status is Completed
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        # check values in the table
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "SVR-ITEM-FIFO-1" in the "itemId" labelled column header of the table field
        Then the value of the "Stock value" labelled nested numeric field of the selected row in the table field is "0.00 €"
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "0.00 each"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "0.00 each"

    Scenario: Remove lock - LCK_VAL1

        And the user removes the lock entry "LCK_VAL1"
