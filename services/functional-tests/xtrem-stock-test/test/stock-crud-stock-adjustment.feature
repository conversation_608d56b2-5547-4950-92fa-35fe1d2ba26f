#This test can only be executed with sage.
#The goal of this test is to verify the CRUD for stock adjustment
@inventory
Feature: stock-crud-stock-adjustment

    Scenario: 01 - Create stock adjustment for decrease
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the "Stock adjustment" titled page is displayed
        #Creating
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Adjustment reason" labelled reference field on the main page
        And the user writes "Decrease quantity" in the reference field
        And the user selects "Decrease quantity" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Item for Adjustment" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Item for Adjustment" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Adjustment quantity" labelled nested numeric field of the selected row in the table field
        # And the user writes "Bulk 01" in the "Location" labelled nested reference field of the selected row in the table field
        # And the user selects "Bulk 01" in the "Location" labelled nested field of the selected row in the table field
        # And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        # Then the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user presses Control+Enter
        #Stock
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row 1 of the table field
        And the user writes "10" in the "Quantity to issue" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        #Save
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user clicks the "Post stock" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user stores the value of the text field with the key "[ENV_STKADJ01]"
    Scenario: 02 - Verify Data and Status - 1
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        When the user selects the "stockAdjustments" labelled table field on the main page
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_STKADJ01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Verify status
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Adjusted"
        #Verify Data
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item for Adjustment" in the "Item" labelled column header of the table field
        And the value of the "New stock quantity" labelled nested text field of the selected row in the table field is "90 each"
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "D1S" in the "Owner" labelled column header of the table field
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "10 each"

    # Scenario Update stock adjustment location commented as it is forbidden to update an already posted document (status = "Completed")
    # TODO: Keep the document in status "Draft" and try to do a change in the stock adjustment
    # currently not working because of this bug = https://jira.sage.com/browse/XT-69843

    # Scenario: Update stock adjustment location
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
    #     Then the "Stock adjustments" titled page is displayed
    #     When the user selects the "stockAdjustments" labelled table field on the main page
    #     And the user selects the row with text "[ENV_STKADJ01]" in the "Number" labelled column header of the table field
    #     And the user clicks the "Number" labelled nested field of the selected row in the table field
    #     #Update
    #     And the user selects the "lines" bound table field on the main page
    #     And the user selects the row with text "Accepted" in the "Quality Control" labelled column header of the table field
    #     And the user writes "Bulk 02" in the "Location" labelled nested reference field of row 1 in the table field
    #     And the user selects "Bulk 02" in the "Location" labelled nested field of row 1 in the table field
    #     #Save
    #     And the user clicks the "Save" labelled business action button on the main page
    #     Then a toast containing text "Record updated" is displayed

    # Scenario Delete stock adjustment location commented as it is forbidden to delete an already posted document (status = "Completed")
    # TODO: Keep the document in status "Draft" and try to delete the stock adjustment document
    # currently not working because of this bug = https://jira.sage.com/browse/XT-69843

    # Scenario: Delete the stock adjustment
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
    #     Then the "Stock adjustments" titled page is displayed
    #     When the user selects the "stockAdjustments" labelled table field on the main page
    #     And the user selects the row with text "[ENV_STKADJ01]" in the "Number" labelled column header of the table field
    #     And the user clicks the "Number" labelled nested field of the selected row in the table field
    #     #Delete
    #     When the user clicks the "Delete" labelled more actions button in the header
    #     And the user clicks the "Delete" button of the Confirm dialog
    #     Then a toast containing text "Record deleted" is displayed

    Scenario: 03 - Create stock adjustment for increase
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Creating
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        And the user selects the "Adjustment reason" labelled reference field on the main page
        And the user writes "Increase quantity" in the reference field
        And the user selects "Increase quantity" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Item for Adjustment" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Item for Adjustment" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "20" in the "Adjustment quantity" labelled nested numeric field of the selected row in the table field
        And the user writes "Bulk 01" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Bulk 01" in the "Location" labelled nested field of the selected row in the table field
        And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        Then the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        And the user presses Control+Enter
        #Adding stock
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user clicks the "OK" labelled business action button on a modal
        #Save & post
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        Then the user clicks the "Post stock" labelled business action button on the main page
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_STKADJ02]"


    Scenario: 04 - Verify Data and Status - 2
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        When the user selects the "stockAdjustments" labelled table field on the main page
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the row with text "[ENV_STKADJ02]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        #Verify status
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Adjusted"
        #Verify data
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Item for Adjustment" in the "Item" labelled column header of the table field
        And the value of the "New stock quantity" labelled nested text field of the selected row in the table field is "110 each"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Bulk 01" in the "Location" labelled column header of the table field
        And the value of the "Quantity in stock unit" labelled nested text field of the selected row in the table field is "20 each"
