#The goal of this test is to verify the flow of creating a new lot
#Unable to verify checkbox status of 'New lot' column due to bug XT-52663
#<PERSON><PERSON><PERSON> to be updated following bug fix

@inventory
Feature:  stock-flow-stock-receipt-lot

    Scenario: 01 - Create stock receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        Then the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        Then the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "100" in the numeric field
        Then the user selects the "Actual cost" labelled numeric field on the sidebar
        And the user writes "21.99" in the numeric field
        Then the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PRECEIPT01]"


    Scenario: 02 - Update stock details
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT01]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PRECEIPT01]" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When the user selects the "Lines" labelled table field on the main page
        Then the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "Stock details" labelled table field on a modal
        #Add line 1
        And the user clicks the "addALine" labelled header action button of the table field
        And the user selects the row with text "100 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "41" in the "quantityInStockUnit" bound nested numeric field of the selected row in the table field
        And the user writes "Accepted" in the "status__name" bound nested reference field of the selected row in the table field
        And the user writes "LOT2023001" in the "Lot" labelled nested filter select field of the selected row in the table field
        And the user waits 1 second
        And the user presses ArrowDown
        And the user presses Enter
        # #Add line 2
        And the user clicks the "addALine" labelled header action button of the table field
        And the user selects the row with text "59 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "35" in the "quantityInStockUnit" bound nested numeric field of the selected row in the table field
        And the user writes "Quality" in the "status__name" bound nested reference field of the selected row in the table field
        And the user waits 1 second
        And the user selects "Quality control" in the "status__name" bound nested field of the selected row in the table field
        And the user writes "LOT2023002" in the "Lot" labelled nested filter select field of the selected row in the table field
        And the user waits 1 second
        And the user presses ArrowDown
        And the user presses Enter
        # #Add line 3
        And the user clicks the "addALine" labelled header action button of the table field
        And the user selects the row with text "24 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "24" in the "quantityInStockUnit" bound nested numeric field of the selected row in the table field
        And the user writes "Accepted" in the "status__name" bound nested reference field of the selected row in the table field
        And the user waits 1 second
        And the user selects "Accepted" in the "status__name" bound nested field of the selected row in the table field
        And the user writes "LOT2023003" in the "Lot" labelled nested filter select field of the selected row in the table field
        And the user waits 1 second
        And the user presses ArrowDown
        And the user presses Enter
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 03 - Verify New lot status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT01]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field

        When the user selects the "Lines" labelled table field on the main page
        Then the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "Stock details" labelled table field on a modal
        And the user selects the row with text "41 each" in the "Quantity in stock unit" labelled column header of the table field
        And the value of the "Lot" labelled nested filter select field of the selected row in the table field is "LOT2023001"
        And the user selects the row with text "35 each" in the "Quantity in stock unit" labelled column header of the table field
        And the value of the "Lot" labelled nested filter select field of the selected row in the table field is "LOT2023002"
        And the user selects the row with text "24 each" in the "Quantity in stock unit" labelled column header of the table field
        And the value of the "Lot" labelled nested filter select field of the selected row in the table field is "LOT2023003"
        And the user clicks the "Cancel" labelled business action button on a modal
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 04 - Verify stock details

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure sensor" in the filter of the table field
        And the user ticks the item with text "Pressure sensor" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user selects the row with text "41 each" in the "Available quantity" labelled column header of the table field
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is "LOT2023001"
        And the user selects the row with text "35 each" in the "Available quantity" labelled column header of the table field
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is "LOT2023002"
        And the user selects the row with text "24 each" in the "Available quantity" labelled column header of the table field
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is "LOT2023003"
