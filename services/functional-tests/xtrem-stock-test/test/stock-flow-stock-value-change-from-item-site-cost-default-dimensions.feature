# The goals of this test are;
# 1. to verify that stock value change inherits dimensions from the item site cost
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly

@inventory
Feature: stock-flow-stock-value-change-from-item-site-cost-default-dimensions

    Scenario: 01 - Add an item site cost and verify dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ItemSite"
        Then the "Item-sites" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "inStockQuantity" bound nested field of the selected row in the table field
        And the user searches for "TI65450" in the navigation panel
        And the user clicks the "2" navigation panel's row
        And selects the "Costs" labelled navigation anchor on the main page
        And the user selects the "Costs" labelled table field on the main page
        # And the user selects the "costSection" bound nested grid field on the main page
        And the user clicks the "Add" labelled header action button of the table field
        And the user selects the "Cost category" labelled reference field on the sidebar
        And the user writes "Standard cost" in the reference field
        And the user selects "Standard cost" in the reference field
        And the user selects the "Start date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "M/T/Y"
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user selects the "Material" labelled numeric field on the sidebar
        And the user writes "57.99" in the numeric field
        # Verify default dimensions
        And the user clicks in the "dimensionsButton" bound button field on the sidebar
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the sidebar


    Scenario: 02 - Open stock value change and verify stock value change line dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueChange"
        Then the "Stock value changes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "TI65450" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Item" labelled reference field on the main page
        And the value of the reference field is "Test Item 65450"
        And the user selects the "Description" labelled text field on the main page
        And the value of the text field is "Stock value change from item-site cost."
        And the user selects the "Stock status" labelled label field on the main page
        And the value of the label field is "Completed"
        # Verify stock value change line dimensions
        And the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal
