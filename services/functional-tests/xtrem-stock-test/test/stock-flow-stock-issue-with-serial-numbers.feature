#This test can only be executed with sage.
#The goal of this test is to do a stock issue with serial numbers
@inventory
Feature: stock-flow-stock-issue-with-serial-numbers
    Scenario: 01 - Creating stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the "Stock issue" titled page is displayed
        #Fill in the Site text field
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Sandfeld" in the reference field
        And the user selects "Sandfeld" in the reference field
        #Add a line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Item for stock issue with SN" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Item for stock issue with SN" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        #Adding stock detail
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "0 each" in the "Quantity to issue" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user selects the row 1 of the table field
        And the user writes "10" in the "Quantity to issue" labelled nested numeric field of the selected row in the table field
        And the user selects the row with text "To be assigned" in the "Serial number" labelled column header of the table field
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field
        #Serial numbers
        And the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user clicks the "addSerialNumberRange" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "SN000001" in the "From serial number" labelled nested reference field of the selected row in the table field
        And the user selects "SN000001" in the "From serial number" labelled nested field of the selected row in the table field
        And the user writes "9" in the "Quantity" labelled nested numeric field of the selected row in the table field
        #Verify the partial assigned text
        When the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Partially assigned" in the "Serial number" labelled column header of the table field
        And the value of the "Serial number" labelled nested text field of the selected row in the table field is "Partially assigned"
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            """
        #Making serial number quantity correspond with the stock quantity
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "Partially assigned" in the "Serial number" labelled column header of the table field
        And the user clicks the "Serial number" labelled nested field of the selected row in the table field
        And the user selects the "stockDetailSerialNumbers" bound table field on a modal
        And the user selects the row with text "9" in the "Quantity" labelled column header of the table field
        And the user selects the row 1 of the table field
        And the user writes "10" in the "Quantity" labelled nested numeric field of the selected row in the table field
        And the user clicks the "OK" labelled business action button on a modal
        #Saving and Posting
        When the user clicks the "Save" labelled business action button on the main page
        Then the user clicks the "Post stock" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_STKISSNUM01]"

    Scenario: 02 - verifying that the stock issue was closed and completed.
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_STKISSNUM01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Issued"

    Scenario: 03 - Running stock detailed inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Sandfeld" in the filter of the table field
        And the user ticks the item with text "Sandfeld" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item for stock issue with SN" in the filter of the table field
        And the user ticks the item with text "Item for stock issue with SN" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        #Issued stock has decreased the available quantity & serial numbers linked to the issued stock is no longer available
        And the user selects the row with text "Accepted" in the "Quality Control" labelled column header of the table field
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "10 each"
# Not yet available:
# And the value of the "Starting serial number" labelled nested numeric field of the selected row in the table field is "SN000011"
# Then the value of the "Ending serial number" labelled nested numeric field of the selected row in the table field is "SN000020"
