# The goals of this test are;
# 1. to verify that a new stock receipt inherits dimensions from the site and according
#    to the company default dimension rules,
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly

@inventory
Feature: stock-flow-stock-receipt-default-dimensions

    Scenario: 01 - Create stock receipt and verify stock receipt line dimensions
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        # Create stock receipt
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Site ZA3" in the reference field
        And the user selects "Site ZA3" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Actual cost" labelled numeric field on the sidebar
        And the user writes "47.99" in the numeric field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row 1 of the table field
        And the value of the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field is "5 each"
        And the value of the "Quality control" labelled nested reference field of the selected row in the table field is "Accepted"
        Then the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SRDD_NUM04]"
        # Verify stock receipt line dimensions after save
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 02 - Update stock receipt dimensions (Apply to new lines only) and verify stock receipt line dimensions
        # Update dimension
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "[ENV_SRDD_NUM04]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Delete" button of the Confirm dialog
        And the table field is empty
        And the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add new stock receipt line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transmitter" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Actual cost" labelled numeric field on the sidebar
        And the user writes "23.70" in the numeric field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Verify new stock receipt line dimensions (Pressure transmitter)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update stock receipt dimensions (Apply to all lines) and verify stock receipt line dimensions
        # Update dimension
        When the user selects the "lines" bound table field on the main page

        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row 1 of the table field
        And the value of the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field is "10 each"
        And the value of the "Quality control" labelled nested reference field of the selected row in the table field is "Accepted"
        Then the user clicks the "OK" labelled business action button on a modal

        And the user clicks the "Set dimensions" labelled more actions button in the header

        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        Then the user clicks the "Apply to all lines" labelled business action button on a modal
        # Add new stock receipt line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And the user selects the "Actual cost" labelled numeric field on the sidebar
        And the user writes "16.45" in the numeric field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        # Verify new stock receipt line dimensions (Pressure sensor)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is ""
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify second stock receipt line dimensions (Pressure transmitter)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transmitter" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        # Delete stock receipt
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
