#This test can only be executed with sage.
#The goal of this test is to create a stock count for non stock items
#When the corresponding switch field "In stock only" is set to "OFF"
@inventory
Feature: stock-flow-stock-count-in-stock-only

    Scenario: 01 - Verify the user is able to create a stock count
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockCountCreation"
        Then the "Stock count creation" titled page is displayed

        When the user clicks the "Create" labelled business action button on the main page
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Stock count site" in the reference field
        Then the user selects "Stock count site" in the reference field
        When the user selects the "From item" labelled reference field on the main page
        And the user writes "SC Item - lot w/o sequence number 1" in the reference field
        Then the user selects "SC Item - lot w/o sequence number 1" in the reference field
        When the user selects the "To item" labelled reference field on the main page
        And the user writes "SC Item - sub-lot & sequence number 2" in the reference field
        Then the user selects "SC Item - sub-lot & sequence number 2" in the reference field
        Then the user selects the "In stock only" labelled switch field on the main page
        # And the switch field is set to "OFF"
        Then the user turns the switch field "OFF"
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Test without stock count" in the text field

        #Storing Stock count description
        Then the user stores the value of the text field with the key "[SC_ISOnly]"

        Given the user selects the "Date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user dismisses all the toasts

        #When the user selects the "itemSites" bound table field on the main page

        And the user selects the "itemSites" bound nested grid field on the main page
        And the user selects row with text "SC Item - lot & sequence number 2" in column with header "Item" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field

        And the user selects row with text "SC Item - lot w/o sequence number 2" in column with header "Item" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field

        And the user selects row with text "SC Item - sub-lot & sequence number 2" in column with header "Item" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field

        And the user selects row with text "SC Item - sub-lot w/o sequence number 2" in column with header "Item" in the nested grid field
        And the user ticks the main checkbox of the selected row in the nested grid field

        And the user waits 2 seconds
        #And the user selects the row with text "SC Item - lot & sequence number 2" in the "Item" labelled column header of the table field

        Then the user clicks the "Create" labelled business action button on the main page
        Then a toast containing text "The stock count was created successfully." is displayed

    Scenario: 02 - Doing the stock count
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "Test without stock count" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When the user selects the "Status" labelled label field on the main page
        And the value of the label field is "To be counted"
        And the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Draft"

        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SC Item - lot & sequence number 2" in the "Item" labelled column header of the table field
        Then the value of the "status" bound nested text field of the selected row in the table field is "To be counted"
        And the value of the "Counted quantity" labelled nested text field of the selected row in the table field is "0 each"
        And the value of the "Stock quantity" labelled nested text field of the selected row in the table field is "0 each"

        And the user selects the row with text "SC Item - lot w/o sequence number 2" in the "Item" labelled column header of the table field
        Then the value of the "status" bound nested text field of the selected row in the table field is "To be counted"
        And the value of the "Counted quantity" labelled nested text field of the selected row in the table field is "0 each"
        And the value of the "Stock quantity" labelled nested text field of the selected row in the table field is "0 each"

        And the user selects the row with text "SC Item - sub-lot & sequence number 2" in the "Item" labelled column header of the table field
        Then the value of the "status" bound nested text field of the selected row in the table field is "To be counted"
        And the value of the "Counted quantity" labelled nested text field of the selected row in the table field is "0.00 g"
        And the value of the "Stock quantity" labelled nested text field of the selected row in the table field is "0.00 g"

        And the user selects the row with text "SC Item - sub-lot w/o sequence number 2" in the "Item" labelled column header of the table field
        Then the value of the "status" bound nested text field of the selected row in the table field is "To be counted"
        And the value of the "Counted quantity" labelled nested text field of the selected row in the table field is "0.00 g"
        And the value of the "Stock quantity" labelled nested text field of the selected row in the table field is "0.00 g"

    Scenario: 03 - Start the stock count
        When the user clicks the "Start" labelled business action button on the main page
        And the user waits 2 seconds

        When the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Count in progress"

        And the user selects the "lines" bound table field on the main page
        And the table has "Check your unit cost. A value of 0 can affect the stock value." error

    Scenario: 04 - Create stock details on an existing stock count line

        Then the user selects the row with text "SC Item - sub-lot w/o sequence number 2" in the "Item" labelled column header of the table field
        And the user writes "Internal 01" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Internal 01" in the "Location" labelled nested field of the selected row in the table field

        And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field

        Then the user clicks the "Save" labelled business action button on the main page

        Then a validation error message is displayed containing text
            """
            Document lines: You need to enter a sublot.
            """
        And the user dismisses the validation error message

        And the user writes "0001" in the "Sublot number" labelled nested numeric field of the selected row in the table field
        And the user presses Enter

        Then the user clicks the "Save" labelled business action button on the main page

        Then a validation error message is displayed containing text
            """
            Document lines: You need to enter an expiration date.
            """
        And the user dismisses the validation error message

        And the user writes "12/31/2026" in the "Expiration date" labelled nested date field of the selected row in the table field

        Then the user clicks the "Save" labelled business action button on the main page

        Then a validation error message is displayed containing text
            """
            You need to assign a lot number to the item 'SC Item - sub-lot w/o sequence number 2'.
            """
        And the user dismisses the validation error message

        And the user writes "LOTNEW01" in the "Lot" labelled nested reference field of the selected row in the table field
        And the user presses Enter

        And the user writes "15.75" in the "Counted quantity" labelled nested numeric field of the selected row in the table field
        And the user presses Enter

        Then the user clicks the "Save" labelled business action button on the main page
        Then the value of the "Count status" labelled nested text field of the selected row in the table field is "Counted"

        And the user writes "10.25" in the "Unit cost" labelled nested numeric field of the selected row in the table field
        And the user presses Enter

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 05 - Add line to the stock count - using phantom row
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "Test without stock count" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "lines" bound table field on the main page

        When the user adds a new table row to the table field

        And the user selects the floating row of the table field
        And the user writes "SC Item - lot & sequence number 1" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "SC Item - lot & sequence number 1" in the "Item" labelled nested field of the selected row in the table field

        And the user writes "Quality control" in the "Quality control" labelled nested reference field of the selected row in the table field
        And the user selects "Quality control" in the "Quality control" labelled nested field of the selected row in the table field

        And the user writes "Internal 04" in the "Location" labelled nested reference field of the selected row in the table field
        And the user selects "Internal 04" in the "Location" labelled nested field of the selected row in the table field

        And the user writes "3" in the "Counted quantity" labelled nested numeric field of the selected row in the table field


        And the user presses Control+Enter
        And the user waits 2 seconds

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        And the user refreshes the screen
        And the user selects the "lines" bound table field on the main page

        Then the user selects the row with text "SC Item - lot & sequence number 1" in the "Item" labelled column header of the table field
        Then the value of the "Count status" labelled nested text field of the selected row in the table field is "Counted"

    Scenario: 06 - Add line to the stock count - using side-panel
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "Test without stock count" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar

        When the user selects the "Item" labelled reference field on the sidebar
        And the user writes "SC Item - lot & sequence number 1" in the reference field
        Then the user selects "SC Item - lot & sequence number 1" in the reference field

        When the user selects the "Location" labelled reference field on the sidebar
        And the user writes "Dock 01" in the reference field
        And the user selects "Dock 01" in the reference field

        When the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Quality control" in the reference field
        And the user selects "Quality control" in the reference field

        When the user selects the "Counted quantity" labelled numeric field on the sidebar
        And the user writes "2" in the numeric field

        When the user selects the "Unit Cost" labelled numeric field on the sidebar
        And the user writes "10.00" in the numeric field

        When the user clicks the "Apply" button of the dialog on the sidebar

        Then the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        And the user refreshes the screen
        And the user selects the "lines" bound table field on the main page

        Then the user selects the row with text "SC Item - lot & sequence number 1" in the "Item" labelled column header of the table field
        Then the value of the "Count status" labelled nested text field of the selected row in the table field is "Counted"

    Scenario: 07 - Confirm 0 quantity on item without stock record
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "SC Item - lot w/o sequence number 2" in the "Item" labelled column header of the table field
        And the user clicks the "Confirm zero quantity" dropdown action of the selected row of the table field
        And the user waits 2 seconds
        Then the value of the "Count status" labelled nested text field of the selected row in the table field is "Counted"
        Then the value of the "Counted quantity" labelled nested numeric field of the selected row in the table field is "0 each"

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"

        Then the "Stock counts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "Test without stock count" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "lines" bound table field on the main page
        And the user filters the "Lot" labelled column in the table field with value "Lot"
        And the user selects the row 1 of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "SC Item - sub-lot w/o sequence number 2"
        Then the value of the "Lot" labelled nested text field of the selected row in the table field is "LOTNEW01"

        And the user clicks the "Confirm zero quantities" labelled more actions button in the header

        Then a toast containing text "Lines updated: 3." is displayed
        And the user waits 2 seconds

        When the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Counted"

        And the "Post stock" labelled business action button on the main page is enabled

    Scenario: 08 - Post stock
        Given the user clicks the "Post stock" labelled business action button on the main page
        And the user refreshes the screen

        When the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Closed"

        When the user selects the "Stock status" labelled label field on the main page
        And the value of the label field is "Completed"

        And the user selects the "lines" bound table field on the main page
        And the user filters the "Lot" labelled column in the table field with value "Lot"
        And the user selects the row 1 of the table field
        Then the value of the "Item" labelled nested text field of the selected row in the table field is "SC Item - sub-lot w/o sequence number 2"
        Then the value of the "Lot" labelled nested text field of the selected row in the table field is "LOTNEW01"

        And selects the "Posting" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected

        And the user selects the "postingDetails" bound table field on the main page

        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        And the value of the "Document number" labelled nested text field of the selected row in the table field is ""
        And the value of the "Status" labelled nested text field of the selected row in the table field is "To be generated"

    Scenario: 09 - generate journal entries
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance/GenerateJournalEntries"
        Then the "Generate journal entries" titled page is displayed

        When the user selects the "Financial site" labelled reference field on the main page
        And the user writes "Siège social S01  PARIS" in the reference field
        And the user selects "Siège social S01 PARIS" in the reference field

        When the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T"
        Then the value of the date field is a generated date with value "T"

        When the user selects the "End date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+1"
        Then the value of the date field is a generated date with value "T+1"

        When the user selects the "Document type" labelled multi dropdown field on the main page
        And the user clicks in the multi dropdown field
        And the user selects "Stock count" in the multi dropdown field
        Then the value of the multi dropdown field is "Stock count"

        And the user clicks in the "create" bound button field on the main page
        Then a toast containing text "Journals created: 1" is displayed

    Scenario: 10 - Verify journals using stock count - posting tab
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "Test without stock count" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And selects the "Posting" labelled navigation anchor on the main page
        Then the "Posting" labelled navigation anchor is selected

        And the user selects the "postingDetails" bound table field on the main page

        And the user selects the row with text "Journal entry" in the "Document type" labelled column header of the table field
        And the user clicks the "targetDocumentNumber" bound nested field of the selected row in the table field
        And the user stores the value of the "Document number" labelled nested link field of the selected row in the table field with the key "[Journal_ISO_Number]"

    Scenario: 11 - Verify journals entry
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-finance/JournalEntry"
        Then the "Journal entries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "OD250001" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Posting status" labelled label field on the main page
        And the value of the label field is "Posted"

        And the user selects the "Lines" labelled nested grid field on the main page

        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText |
            | Transaction debit | € 161.44 |
        Then the value of the "Account" labelled nested reference field of the selected row in the nested grid field is "******** -- Stocks de matières premières"

        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText |
            | Transaction credit | € 161.44 |
        Then the value of the "Account" labelled nested reference field of the selected row in the nested grid field is "******** -- Variations des stocks de matières premières"

        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText |
            | Transaction debit | € 20.00  |
        Then the value of the "Account" labelled nested reference field of the selected row in the nested grid field is "******** -- Stocks de matières premières"

        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText |
            | Transaction credit | € 20.00  |
        Then the value of the "Account" labelled nested reference field of the selected row in the nested grid field is "******** -- Variations des stocks de matières premières"

        And the user selects the row with the following content in the nested grid field
            | columnHeader      | cellText |
            | Transaction debit | € 30.00  |
        Then the value of the "Account" labelled nested reference field of the selected row in the nested grid field is "******** -- Stocks de matières premières"

        And the user selects the row with the following content in the nested grid field
            | columnHeader       | cellText |
            | Transaction credit | € 30.00  |
        Then the value of the "Account" labelled nested reference field of the selected row in the nested grid field is "******** -- Variations des stocks de matières premières"
