# The goals of this test are;
# 1. to verify that a new stock count inherits dimensions from the site and according
#    to the company default dimension rules,
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly

@inventory
Feature: stock-flow-stock-count-default-dimensions

    Scenario: 01 - Create a stock count
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockCountCreation"
        Then the "Stock count creation" titled page is displayed

        When the user clicks the "Create" labelled business action button on the main page
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Site ZA3" in the reference field
        And the user selects "Site ZA3" in the reference field
        And the user selects the "From item" labelled reference field on the main page
        And the user writes "Electrical connector" in the reference field
        And the user selects "Electrical connector" in the reference field
        And the user selects the "To item" labelled reference field on the main page
        And the user writes "Electrical connector" in the reference field
        And the user selects "Electrical connector" in the reference field
        And the user selects the "description" bound text field on the main page
        And the user writes "XT654500" in the text field
        And the user stores the value of the text field with the key "[ENV_SCDD_DSC11]"

        Then the user clicks the "Save" labelled business action button on the main page
        Then the user selects the "Select all" labelled checkbox field on the main page
        And the user ticks the checkbox field
        Then the user clicks the "Create" labelled business action button on the main page
        Then a toast containing text "The stock count was created successfully." is displayed


    Scenario: 02 - Open the stock count and verify the dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockCount"
        Then the "Stock counts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "[ENV_SCDD_DSC11]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Start" labelled business action button on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Electrical connector" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 03 - Update stock count dimensions (Apply to new lines only) and verify stock count line dimensions
        # Update dimensions
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Set default dimensions" labelled header action button of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add new stock count line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "3847TY7" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Quality control" in the reference field
        And the user selects "Quality control" in the reference field
        And the user selects the "Lot" labelled filter select field on the sidebar
        And the user writes "23000005" in the filter select field
        And the user selects "23000005" in the filter select field
        And the user selects the "Counted quantity" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Unit cost" labelled numeric field on the sidebar
        And the user writes "20" in the numeric field
        Then the user clicks the "Apply" button of the Confirm dialog on the sidebar
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        # Verify new stock count line dimensions (Pressure sensor)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock count line dimensions (Electrical connector)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Electrical connector" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal


    Scenario: 04 - Update stock count dimensions (Apply to all lines) and verify stock count line dimensions
        # Update dimension
        When the user selects the "lines" bound table field on the main page
        And the user clicks the "Set default dimensions" labelled header action button of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user stores the value of the reference field with the key "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Residential" in the reference field
        And the user selects "Residential" in the reference field
        And the user stores the value of the reference field with the key "Residential"
        Then the user clicks the "Apply to all lines" labelled business action button on a modal
        # Add new stock count line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "E2GA7NM02" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Quality control" in the reference field
        And the user selects "Quality control" in the reference field
        And the user selects the "Lot" labelled filter select field on the sidebar
        And the user writes "24000022" in the filter select field
        And the user selects "24000022" in the filter select field
        And the user selects the "Counted quantity" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Unit cost" labelled numeric field on the sidebar
        And the user writes "12" in the numeric field
        Then the user clicks the "Apply" button of the Confirm dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        # Verify new stock count line dimensions (Pressure transducer)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock count line dimensions (Pressure sensor)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock count line dimensions (Electrical connector)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Electrical connector" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
