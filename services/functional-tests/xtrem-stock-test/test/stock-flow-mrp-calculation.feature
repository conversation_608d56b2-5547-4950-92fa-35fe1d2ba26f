# The purpose of this test is to test the flow of the MRP calculation requestion and verify the MRP result
@inventory

Feature: stock-flow-mrp-calculation

    Scenario: Generate the MRP request
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/MrpCalculationRequest"
        Then the "MRP calculation request" titled page is displayed
        # Enter criteria details
        When the user selects the "Description" labelled text field on the main page
        And the user writes "MRP_Flow_test" in the text field
        And the user stores the value of the text field with the key "[ENV_MRP_Description]"

        And the user selects the "Site" labelled multi reference field on the main page
        And the user writes "T<PERSON> Hampton" in the multi reference field
        And the user selects "TE Hampton" in the multi reference field
        And the user stores the value of the multi reference field with the key "[ENV_MRP_Site]"

        And the user selects the "From item" labelled reference field on the main page
        And the user writes "Item for MRP flow" in the reference field
        And the user selects "Item for MRP flow" in the reference field
        And the user stores the value of the reference field with the key "[ENV_MRP_From_item]"

        And the user selects the "To item" labelled reference field on the main page
        And the user writes "Item for MRP flow" in the reference field
        And the user selects "Item for MRP flow" in the reference field
        And the user stores the value of the reference field with the key "[ENV_MRP_To_item]"


        And the user selects the "Period in weeks" labelled numeric field on the main page
        # Default value is 5
        Then the value of the numeric field is "5"
        And the user stores the value of the numeric field with the key "[ENV_MRP_Period_in_weeks]"


        # Submit and calculate the result
        When the user clicks the "Calculate" labelled business action button on the main page
        Then a toast containing text "MRP calculation request sent" is displayed




    Scenario: Check the MRP calculation result
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed

        # Search for created request
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "[ENV_MRP_Description]" in the "Description" labelled column header of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        Then the "MRP calculation result MRP_Flow_test" titled page is displayed

        # Verify the page details
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "[ENV_MRP_Description]"
        When the user selects the "Sites" labelled multi reference field on the main page
        Then the value of the multi reference field is "[ENV_MRP_Site]"
        When the user selects the "From item" labelled text field on the main page
        Then the value of the text field is "[ENV_MRP_From_item]"
        When the user selects the "To item" labelled text field on the main page
        Then the value of the text field is "[ENV_MRP_To_item]"

        # Test with todays date
        When the user selects the "Start date" labelled date field on the main page
        Then the value of the date field is a generated date with value "M/T/Y"

        When the user selects the "Period in weeks" labelled numeric field on the main page
        Then the value of the numeric field is "[ENV_MRP_Period_in_weeks]"
        When the user selects the "Status" labelled label field on the main page
        Then the value of the label field is "Completed"

        #Verify the table details
        When the user selects the "Details" labelled table field on the main page
        Then the table field is not empty
        # Filter to retrieve single line
        And the user selects the row with text "Item for MRP flow" in the "Item" labelled column header of the table field
        # Verify
        And the user selects the row 1 of the table field
        Then the value of the "Site name" labelled nested text field of the selected row in the table field is "[ENV_MRP_Site]"
        Then the value of the "Item" labelled nested link field of the selected row in the table field is "[ENV_MRP_From_item]"
        Then the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "60 each"
        Then the value of the "Replenishment method" labelled nested select field of the selected row in the table field is "Purchasing"
        Then the value of the "Suggestion status" labelled nested text field of the selected row in the table field is "Suggestion"

        # check suggestion start date is today's date
        Then the value of the "Start date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        # check suggestion end date is today's date + 5 (end date = start date + purchase lead time of 5 days)
        Then the value of the "End date" labelled nested date field of the selected row in the table field is a generated date with value "T+5"
