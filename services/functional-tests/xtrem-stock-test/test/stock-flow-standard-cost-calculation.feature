# This feature is using testUser static parameter defined in parameters-atp file
# Make sure parameters-atp file exist and add required value for testUser parameter depending of the environment

@inventory
Feature: stock-flow-standard-cost-calculation
    Scenario: 1 - Verify standard cost values for item 1 without routing
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StandardCostCalculation"
        Then the "Standard cost calculations" titled page is displayed

        # comment as the step below is dependent on the environment
        #Change value of testUser parameter according to the environment in parameters-atp file
        When the user selects the "Calculation user" labelled reference field on the main page
        Then the value of the reference field is "param:testUser"

        #Select site on reference field
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        #Fill in items field
        And the user selects the "Items" labelled multi reference field on the main page
        And the user clears the multi reference field
        And the user writes "Standard Cost Item 1" in the multi reference field
        And the user selects "Standard Cost Item 1" in the multi reference field

        And the user selects the "Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field

        Then the user clicks the "Calculate" labelled business action button on the main page

        And an info dialog appears on the main page
        And the dialog title is "Wait for the calculation to complete" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Standard cost calculation finished" is displayed

        And the user selects the "Result lines" labelled table field on the main page

        And the user selects the row with text "Standard_Cost_Item_03" in the "Item ID" labelled column header of the table field
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 299,999.7333"
        And the value of the "Machine cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 299,999.7333"

        And the user selects the row with text "Standard_Cost_Item_01" in the "Item ID" labelled column header of the table field
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 533,431.8667"
        And the value of the "Machine cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 533,431.8667"

    Scenario: 2 - Verify standard cost values for item 1 with routing
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StandardCostCalculation"
        Then the "Standard cost calculations" titled page is displayed

        # comment as the step below is dependent on the environment
        # #Check header status
        # When the user selects the "Calculation user" labelled reference field on the main page
        # #Change this step definition while executing it in local / CI
        # #CI execution
        # Then the value of the reference field is "sdmodev1, sageidatp"
        # #local execution
        # #Then the value of the reference field is "Test, Unit"

        #Select site on reference field
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field

        #Fill in items field
        And the user selects the "Items" labelled multi reference field on the main page
        And the user clears the multi reference field
        And the user writes "Standard Cost Item 1" in the multi reference field
        And the user selects "Standard Cost Item 1" in the multi reference field

        And the user selects the "Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field

        And the user selects the "Include routing" labelled checkbox field on the main page
        And the user ticks the checkbox field

        And the user clicks the "Calculate" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the calculation to complete" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Standard cost calculation finished" is displayed

        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"

        And the user selects the "Result lines" labelled table field on the main page

        And the user selects the row with text "Standard_Cost_Item_03" in the "Item ID" labelled column header of the table field
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 299,999.7333"
        And the value of the "Machine cost" labelled nested text field of the selected row in the table field is "£ 3,936.0000"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 303,935.7333"

        And the user selects the row with text "Standard_Cost_Item_01" in the "Item ID" labelled column header of the table field
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 533,431.8667"
        And the value of the "Machine cost" labelled nested text field of the selected row in the table field is "£ 6,672.0000"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 540,103.8667"

    Scenario: 3 - Verify values with "Component standard cost" ticked / unticked
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StandardCostCalculation"
        Then the "Standard cost calculations" titled page is displayed
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Items" labelled multi reference field on the main page
        And the user clears the multi reference field
        And the user writes "Bike" in the multi reference field
        And the user selects "Bike" in the multi reference field
        And the user selects the "Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "Include routing" labelled checkbox field on the main page
        And the user unticks the checkbox field
        And the user clicks the "Calculate" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the calculation to complete" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Standard cost calculation finished" is displayed
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Result lines" labelled table field on the main page
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 122.4100"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 122.4100"
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 1,644.8200"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 1,644.8200"
        And the user selects the "Component standard cost" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Draft"
        And the user clicks the "Calculate" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the calculation to complete" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Standard cost calculation finished" is displayed
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Result lines" labelled table field on the main page
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "Current material cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 86.6000"
        And the value of the "Current machine cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Machine cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Current labor cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Labor cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 86.6000"
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "Current material cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Material cost" labelled nested text field of the selected row in the table field is "£ 2,173.2000"
        And the value of the "Current machine cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Machine cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Current labor cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Labor cost" labelled nested text field of the selected row in the table field is "£ 0.0000"
        And the value of the "Total cost" labelled nested text field of the selected row in the table field is "£ 2,173.2000"

    Scenario: 4 - Verify "Select all" option and create item-site costs
        And the user selects the "Component standard cost" labelled checkbox field on the main page
        And the user unticks the checkbox field
        And the user selects the "Include routing" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks the "Calculate" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the calculation to complete" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Standard cost calculation finished" is displayed
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Result lines" labelled table field on the main page
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the selected row of the table field is unselected
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the selected row of the table field is unselected
        And the user selects the "Select all result lines" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Result lines" labelled table field on the main page
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the selected row of the table field is selected
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Pending"
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the selected row of the table field is selected
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Pending"
        And the user clicks the "createItemSiteCost" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the item-site cost records to be created" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Item-site cost creation finished." is displayed
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Created"
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Created"

    Scenario: 5 - Verify the values of the newly item-site costs records are created as expected
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "ID" labelled column in the table field with value "CCZ"
        And the user selects the row with text "FRAME_CCZ" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user selects the row with text "Average unit cost" in the "Cost type" labelled column header of the table field
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "£ 1,400.0000"

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "ID" labelled column in the table field with value "CCZ"
        And the user selects the row with text "WHEEL_CCZ" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user selects the row with text "Standard cost" in the "Cost type" labelled column header of the table field
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "£ 122.4100"
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        And selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "costs" bound table field on the sidebar
        And the user selects the row with text "Standard cost" in the "Cost category" labelled column header of the table field
        And the value of the "Calculated" labelled nested checkbox field of the selected row in the table field is "true"

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "ID" labelled column in the table field with value "CCZ"
        And the user selects the row with text "BIKE_CCZ" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user selects the row with text "Standard cost" in the "Cost type" labelled column header of the table field
        And the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "£ 1,656.4033"
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        And selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "costs" bound table field on the sidebar
        And the user selects the row with text "Standard cost" in the "Cost category" labelled column header of the table field
        And the value of the "Calculated" labelled nested checkbox field of the selected row in the table field is "true"
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        And the user selects the "Material" labelled numeric field on the sidebar
        And the value of the numeric field is "1,644.8200"
        And the user selects the "Machine" labelled numeric field on the sidebar
        And the value of the numeric field is "11.5833"
        And the user selects the "Total cost" labelled numeric field on the sidebar
        And the value of the numeric field is "1,656.4033"

    Scenario: 6 - Check error provided when user tries to create another cost with the same date (only one cost per day is allowed)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StandardCostCalculation"
        Then the "Standard cost calculations" titled page is displayed
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Items" labelled multi reference field on the main page
        And the user clears the multi reference field
        And the user writes "Bike" in the multi reference field
        And the user selects "Bike" in the multi reference field
        And the user selects the "Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "Component standard cost" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Include routing" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks the "Calculate" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the calculation to complete" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Standard cost calculation finished" is displayed
        And the user selects the "Select all result lines" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks the "createItemSiteCost" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the item-site cost records to be created" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Item-site cost creation finished." is displayed
        And the user selects the "Result lines" labelled table field on the main page
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Error"
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Error"
        And the user opens the application on a desktop using the following link: "@sage/xtrem-communication/SysNotificationState"
        And the "Batch task history" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Create item site costs from cost roll up results" in the "Operation" labelled column header of the table field
        And the user clicks the "Status" labelled nested field of the selected row in the table field
        And selects the "Logs" labelled navigation anchor on the main page
        And the user selects the "Logs" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Level" labelled nested select field of the selected row in the table field is "Error"
        And the value of the "Message" labelled nested text field of the selected row in the table field is "Item-site cost exists and cannot be updated: [Item: BIKE_CCZ - Site: 501]"
        And the user selects the row 2 of the table field
        And the value of the "Level" labelled nested select field of the selected row in the table field is "Error"
        And the value of the "Message" labelled nested text field of the selected row in the table field is "Item-site cost exists and cannot be updated: [Item: WHEEL_CCZ - Site: 501]"

    Scenario: 7 - Check that an additiona cost can be created with a future date
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StandardCostCalculation"
        Then the "Standard cost calculations" titled page is displayed
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Items" labelled multi reference field on the main page
        And the user clears the multi reference field
        And the user writes "Bike" in the multi reference field
        And the user selects "Bike" in the multi reference field
        And the user selects the "Start date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+1"
        And the user selects the "Quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field
        And the user selects the "Component standard cost" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user selects the "Include routing" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks the "Calculate" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the calculation to complete" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Standard cost calculation finished" is displayed
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Result lines" labelled table field on the main page
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Pending"
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Pending"
        And the user selects the "Select all result lines" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks the "createItemSiteCost" labelled business action button on the main page
        And an info dialog appears on the main page
        And the dialog title is "Wait for the item-site cost records to be created" on the main page
        And the user clicks the "Wait" button of the Confirm dialog
        And the user waits 2 seconds
        And a toast containing text "Item-site cost creation finished." is displayed
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Result lines" labelled table field on the main page
        And the user selects the row with text "WHEEL_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Created"
        And the user selects the row with text "BIKE_CCZ" in the "Item ID" labelled column header of the table field
        And the value of the "creationStatus" bound nested label field of the selected row in the table field is "Created"

    Scenario: 8 - Verify the values of the newly item-site costs records are created as expected (and the unit cost shown on the item is the one currently valid)
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "ID" labelled column in the table field with value "CCZ"
        And the user selects the row with text "BIKE_CCZ" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Unit cost" labelled nested select field of the selected row in the table field is "£ 1,656.4033"
        And the user stores the value of the "Unit cost" labelled nested text field of the selected row in the table field with the key ""
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        And selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "costs" bound table field on the sidebar
        And the user selects the row 2 of the table field
        And the value of the "Calculated" labelled nested checkbox field of the selected row in the table field is "true"
        And the value of the "Total cost" labelled nested select field of the selected row in the table field is "£ 2,184.7833"
        And the value of the "Unit cost" labelled nested select field of the selected row in the table field is "£ 2,184.7833"
        And the value of the "Unit cost" labelled nested select field of the selected row in the table field is "[ENV_PrevCostBike]"

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "ID" labelled column in the table field with value "CCZ"
        And the user selects the row with text "WHEEL_CCZ" in the "ID" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Unit cost" labelled nested select field of the selected row in the table field is "£ 122.4100"
        And the user stores the value of the "Unit cost" labelled nested text field of the selected row in the table field with the key ""
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        And selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "costs" bound table field on the sidebar
        And the user selects the row 2 of the table field
        And the value of the "Calculated" labelled nested checkbox field of the selected row in the table field is "true"
        And the value of the "Total cost" labelled nested select field of the selected row in the table field is "£ 86.6000"
        And the value of the "Unit cost" labelled nested select field of the selected row in the table field is "£ 86.6000"
        And the user selects the row 1 of the table field
        And the value of the "Unit cost" labelled nested select field of the selected row in the table field is "[ENV_PrevCostWheel]"
