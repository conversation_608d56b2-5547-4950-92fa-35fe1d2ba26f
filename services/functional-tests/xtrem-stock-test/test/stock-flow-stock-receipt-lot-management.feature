#This test can only be executed with sage.
# The goal of this test is to verify stock receipt lot flow creation

@inventory
Feature: stock-flow-stock-receipt-lot-management
    Scenario: Checking the quantity on the current stock detailed enquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure sensor" in the filter of the table field
        And the user ticks the item with text "Pressure sensor" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # verifying the current item quantity on the existing lot number
        # LOT001 is missing on the results
        And the user selects the row with text "LOT001" in the "Lot" labelled column header of the table field
        Then the value of the "Lot" labelled nested numeric field of the selected row in the table field is "LOT001"
        And the value of the "On-hand quantity" labelled nested numeric field of the selected row in the table field is "10 each"

    Scenario: Create Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        #Click Create business action
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill in a text field
        And the user selects the "Number" labelled text field on the main page
        #Select site on reference field
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        #Adding a line to grid
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        #Select item on sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        #Fill in Quantity and Actual cost on sidebar
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Actual cost" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        #Select Status on sidebar
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        #Click Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed
        #Select Stock details
        And the user selects the row 1 of the table field
        When the user clicks the "Stock details" dropdown action of the selected row of the table field
        When the user selects the "Stock details" labelled table field on a modal
        #Adding the line and selecting the existing lot number
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user selects the row with text "5 each" in the "Quantity in stock unit" labelled column header of the table field
        And the user writes "LOT001" in the "Lot" labelled nested filter select field of the selected row in the table field
        And the user presses ArrowDown
        And the user waits 1 second
        And the user presses Enter
        And the user waits 1 second
        And the user clicks the "OK" labelled business action button on a modal
        #Save and posting stock receipt
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: Verify the lot number on stock detailed inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockDetailedInquiry"
        Then the "Stock detailed inquiry" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the main page

        # Filter for site
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Swindon" in the filter of the table field
        And the user ticks the item with text "Swindon" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Filter for item
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Pressure sensor" in the filter of the table field
        And the user ticks the item with text "Pressure sensor" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        # verifying the added item quantity on the existing lot number
        And the user selects the row with text "LOT001" in the "Lot" labelled column header of the table field
        Then the value of the "Lot" labelled nested numeric field of the selected row in the table field is "LOT001"
        And the value of the "On-hand quantity" labelled nested numeric field of the selected row in the table field is "15 each"
