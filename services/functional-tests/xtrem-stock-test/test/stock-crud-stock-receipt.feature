#The goal of this test is to verify the creation, update and deletion of Stock Receipt.
#Data Used : Item - non-serialized and non-lotManaged.

@inventory
Feature: stock-crud-stock-receipt

    Scenario: 01 - Verify the user is able to create a Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de Saint Denis" in the reference field
        ##Adding new lines in the table field
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Transducer body 30x5" in the reference field
        And the user selects "Transducer body 30x5" in the reference field
        And the user selects the "quantityInStockUnit" bound numeric field on the sidebar
        And the user writes "100" in the numeric field
        And the user selects the "orderCost" bound numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        ##Selecting and storing a PR number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PRECEIPT01]"

    Scenario: 02 - Verify the user is able to add the stock details line on a Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT01]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PRECEIPT01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Details required"
        ##Adding stock details and save the record
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Transducer body 30x5" in the "Item" labelled column header of the table field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 03 - Verify the user is able to update record of a Stock Receipt
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT01]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Details entered"
        ##Updating stock quantity
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Transducer body 30x5" in the "Item" labelled column header of the table field
        And the user writes "120" in the "Quantity" labelled nested text field of the selected row in the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user selects the "lines" bound table field on the main page
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "120 each"

    Scenario: 04 - Verify the user is able to add a new line item on a Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT01]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        ##Add a new line item
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer strain gauge" in the reference field
        And the user selects "Pressure transducer strain gauge" in the reference field
        And the user selects the "quantityInStockUnit" bound numeric field on the sidebar
        And the user writes "100" in the numeric field
        And the user selects the "orderCost" bound numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 05 - Verify the user is able to delete a newly created Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT01]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    ##Creating another stock receipt
    Scenario: 06 - Verify the user is able to create a second Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Entrepot de  Saint  Denis" in the reference field
        And the user selects "Entrepot de Saint Denis" in the reference field
        ##Adding new lines in the table field
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Transducer body 30x5" in the reference field
        And the user selects "Transducer body 30x5" in the reference field
        And the user selects the "quantityInStockUnit" bound numeric field on the sidebar
        And the user writes "100" in the numeric field
        And the user selects the "orderCost" bound numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        And the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        ##Selecting and storing a PR number
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_PRECEIPT02]"

    Scenario: 07 - Verify the user is able to add the stock details line on the second Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT02]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "[ENV_PRECEIPT02]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        ##Adding stock details and save the record
        When the user selects the "lines" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user clicks the "addStockDetail" bound action of the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Transducer body 30x5" in the "Item" labelled column header of the table field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 08 - Verify the user is able to post a Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT02]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        ##Posting the stock
        And the user clicks the "Post stock" labelled business action button on the main page

    Scenario: 09 - Verify the user is able to check the status of the Stock Receipt
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "stockReceipts" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_PRECEIPT02]" in the "Number" labelled column header of the table field
        When the user clicks the "Number" labelled nested field of the selected row in the table field
        ##Verify the Stock receipt status is closed.
        When the user selects the "Display status" labelled label field on the main page
        Then the value of the label field is "Received"
