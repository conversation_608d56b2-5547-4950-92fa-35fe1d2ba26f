
# The purpose of this test is to test the flow of the MRP calculation request and verify the MRP result

@inventory
Feature: stock-flow-intersite-shipment-mrp-calculation-adjustment-on-stock-transfer-shipment

    Scenario: 01 - Generate the MRP request
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the dialog title is "MRP calculation request"
        And the user selects the "Description" labelled text field on a modal
        And the user writes "MRP Stock Transfer Shipment" in the text field
        And the user selects the "Site" labelled multi reference field on a modal
        And the user writes "Stock Tranfer DE Site 1" in the multi reference field
        And the user selects "Stock Tranfer DE Site 1" in the multi reference field
        And the user writes "Stock Tranfer DE Site 2" in the multi reference field
        And the user selects "Stock Tranfer DE Site 2" in the multi reference field
        And the user selects the "From item" labelled reference field on a modal
        And the user writes "Item for MRP flow" in the reference field
        And the user selects "Item for MRP flow" in the reference field
        And the user selects the "To item" labelled reference field on a modal
        And the user writes "Item for MRP flow" in the reference field
        And the user selects "Item for MRP flow" in the reference field
        And the user selects the "Start date" labelled date field on a modal
        And the value of the date field is a generated date with value "T"
        And the user selects the "Period in weeks" labelled numeric field on a modal
        And the value of the numeric field is "5"
        And the user clicks the "Calculate" labelled business action button on a modal
        And a toast containing text "MRP calculation request sent" is displayed

    Scenario: 02 - Check the MRP calculation result
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/MrpCalculation"
        Then the "MRP calculation results" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "MRP Stock Transfer Shipment" in the "Description" labelled column header of the table field
        And the user clicks the "Description" labelled nested field of the selected row in the table field
        And the "MRP calculation result MRP Stock Transfer Shipment" titled page is displayed
        And the user selects the "Description" labelled text field on the main page
        And the text field is read-only
        And the value of the text field is "MRP Stock Transfer Shipment"
        And the user selects the "Companies" labelled multi reference field on the main page
        And the value of the multi reference field is ""
        And the user selects the "Sites" labelled multi reference field on the main page
        And the value of the multi reference field is "Stock Tranfer DE Site 1|Stock Tranfer DE Site 2"
        And the user selects the "From item" labelled text field on the main page
        And the value of the text field is "Item for MRP flow"
        And the user selects the "To item" labelled text field on the main page
        And the value of the text field is "Item for MRP flow"
        And the user selects the "Start date" labelled date field on the main page
        And the date field is read-only
        And the value of the date field is a generated date with value "T"
        And the user selects the "Period in weeks" labelled numeric field on the main page
        And the value of the numeric field is "5"
        And the user selects the "Status" labelled label field on the main page
        And the value of the label field is "Completed"
        And the user selects the "Details" labelled table field on the main page
        And the table field is not empty
        And the user selects the row 1 of the table field
        And the value of the "Company name" labelled nested text field of the selected row in the table field is "DE Automotive GmbH"
        And the value of the "Site name" labelled nested text field of the selected row in the table field is "Stock Tranfer DE Site 2"
        And the value of the "Item" labelled nested link field of the selected row in the table field is "Item for MRP flow"
        And the value of the "Item ID" labelled nested text field of the selected row in the table field is "MRP_Item_01"
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "5 each"
        And the value of the "Replenishment method" labelled nested select field of the selected row in the table field is "Production"
        And the value of the "Suggestion status" labelled nested text field of the selected row in the table field is "Suggestion"
        And the value of the "First suggestion date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Start date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "End date" labelled nested date field of the selected row in the table field is a generated date with value "T+5"
