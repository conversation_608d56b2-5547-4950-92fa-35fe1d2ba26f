# This test aims to validate the creation of a stock transfer order for supply chain. This will confirm stability on the new feature
# The focus will be on verifying field validations and creation of the Stock transfer shipment without issue
# Checking status and step sequence updates throughout the process as we've had some issues with this

@inventory
Feature: stock-crud-intersite-order

    Scenario: 01 - Verify the user is able to create the stock transfer order (using direct entry)
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        Then the "Stock transfer orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the "Stock transfer order" titled page is displayed
        And the user selects the "Shipping site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "Receiving site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 2" in the reference field
        And the user selects "Stock Tranfer Uk Site 2" in the reference field
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the floating row of the table field
        And the user writes "Pressure transducer" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Pressure transducer" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "Quantity in stock unit" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"

    Scenario: 02 - Verify the user is able to delete the stock transfer order
        And the user waits 3 seconds
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: 03 - Verify the user is able to create (using the panel) and confirm a stock transfer order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        Then the "Stock transfer orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the "Stock transfer order" titled page is displayed
        And the user selects the "Shipping site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 1" in the reference field
        And the user selects "Stock Tranfer Uk Site 1" in the reference field
        And the user selects the "Receiving site" labelled reference field on the main page
        And the user writes "Stock Tranfer Uk Site 2" in the reference field
        And the user selects "Stock Tranfer Uk Site 2" in the reference field
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in stock unit *" labelled numeric field on the sidebar
        And the user writes "30" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Draft"
        When the user selects the "orderStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is current
        Then the status of the "Ship" item of the step-sequence is incomplete
        Then the status of the "Receive" item of the step-sequence is incomplete
        Then a toast containing text "Record created" is displayed
        And the user clicks the "Confirm" labelled business action button on the main page
        And the user clicks the "Confirm" button of the Confirm dialog
        Then a toast containing text "The document has been confirmed and set to 'Pending" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        When the user selects the "orderStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is complete
        Then the status of the "Ship" item of the step-sequence is current
        Then the status of the "Receive" item of the step-sequence is incomplete
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SCO_NMB]"

    Scenario: 04 - Verify the user is able to update the stock transfer order
        And the user selects the "Order date" labelled date field on the main page
        And the user writes a generated date in the date field with value "T+1"
        # check the other date fields when the bug is fixed
        And selects the "Shipping" labelled navigation anchor on the main page
        Then the "Shipping" labelled navigation anchor is selected
        And the user selects the "Delivery lead time" labelled numeric field on the main page
        And the user writes "4" in the numeric field
        And the user presses Tab
        And the user clicks the "Update" button of the Confirm dialog
        And the user selects the "Expected delivery date *" labelled date field on the main page
        And the value of the date field is a generated date with value "T+5"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 05 - Verify the user can set and then confirm dimensions
        And selects the "Lines" labelled navigation anchor on the main page
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user clicks the "ok" bound business action button on a modal
        And the user clicks the "save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user clicks the "Cancel" labelled business action button on a modal

    Scenario: 06 - Verify stock can be allocated
        Given the user refreshes the screen
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table field
        And the user selects the "allocations" bound table field on a modal
        And the user selects the row with text "24000032" in the "Lot" labelled column header of the table field
        And the user writes "30" in the "Quantity to allocate" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on a modal
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Allocation status" labelled nested label field of the selected row in the table field is "Allocated"

    Scenario: 07 - Verify the user is able to create a stock transfer shipment
        Given the user refreshes the screen
        And the user clicks the "Create Shipment" labelled business action button on the main page
        And the user clicks the "Create" button of the Confirm dialog
        And a toast containing text "Transfer shipment created" is displayed
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Ready to process"
        When the user selects the "shipmentStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is current
        Then the status of the "Post" item of the step-sequence is incomplete
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "Ready to process"

    Scenario: 08 - Verify the status on the stock transfer order
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-supply-chain/StockTransferOrder"
        Then the "Stock transfer orders" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "[ENV_SCO_NMB]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Confirmed"
        When the user selects the "orderStepSequence" bound step-sequence field on the main page
        Then the status of the "Create" item of the step-sequence is complete
        Then the status of the "Confirm" item of the step-sequence is complete
        Then the status of the "Ship" item of the step-sequence is current
        Then the status of the "Receive" item of the step-sequence is incomplete
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        Then the value of the "Status" labelled nested label field of the selected row in the table field is "In progress"

    Scenario: 09 - Verify notes can be added to the stock transfer order
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the user writes "this is an internal note @123 !" in the rich text field
        And the user stores the value of the rich text field with the key "[ENV_NOTETO]"
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And selects the "Notes" labelled navigation anchor on the main page
        And the user selects the "Internal notes" labelled rich text field on the main page
        And the value of the rich text field is "[ENV_NOTETO]"
