# The goals of this test are;
# 1. to verify that a new stock adjustment inherits dimensions from the site and according
#    to the company default dimension rules,
# 2. to verify that the 'Apply to new lines only' and 'Apply to all lines' functions of the Set dimensions modal, work
#    accordingly

@inventory
Feature: stock-flow-stock-adjustment-default-dimensions

    Scenario: 01 - Create stock adjustment and verify stock adjustment line dimensions
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockAdjustment"
        Then the "Stock adjustments" titled page is displayed
        # Create stock adjustment
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Site ZA3" in the reference field
        And the user selects "Site ZA3" in the reference field
        And the user selects the "Adjustment reason" labelled reference field on the main page
        And the user writes "Decrease quantity" in the reference field
        And the user selects "Decrease quantity" in the reference field
        And the user selects the "Date" labelled date field on the main page
        And the value of the date field is a generated date with value "T"
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Integrated circuit amplifier" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Integrated circuit amplifier" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "200" in the "Adjustment quantity" labelled nested numeric field of the selected row in the table field
        #And the user selects the row 1 of the table field
        #And the user writes "Quality control" in the "Quality control" labelled nested reference field of the selected row in the table field
        #And the user selects "Quality control" in the "Quality control" labelled nested field of the selected row in the table field
        #And the value of the "New stock quantity" labelled nested numeric field of the selected row in the table field is "-200 each"
        And the user presses Control+Enter
        # Verify new stock adjustment line dimensions
        And the user selects the row with text "Integrated circuit amplifier" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal

    Scenario: 02 - Update stock adjustment dimensions (Apply to new lines only) and verify stock adjustment line dimensions
        # Update dimension
        When the user clicks the "Set dimensions" labelled more actions button in the header
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field
        Then the user clicks the "Apply to new lines only" labelled business action button on a modal
        # Add new stock adjustment line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Housing" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Housing" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "75" in the "Adjustment quantity" labelled nested numeric field of the selected row in the table field
        #And the user selects the row 1 of the table field
        #And the user writes "Rejected" in the "Quality control" labelled nested reference field of the selected row in the table field
        #And the user selects "Rejected" in the "Quality control" labelled nested field of the selected row in the table field
        #And the value of the "New stock quantity" labelled nested numeric field of the selected row in the table field is "-75 each"
        And the user presses Control+Enter
        # Verify new stock adjustment line dimensions (Housing)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Housing" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Retail"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock adjustment line dimensions (Integrated circuit amplifier)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Integrated circuit amplifier" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Sales"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Commercial"
        Then the user clicks the "Cancel" labelled business action button on a modal

    Scenario: 03 - Update stock adjustment dimensions (Apply to all lines) and verify stock adjustment line dimensions
        # Update dimension
        When the user clicks the "Set dimensions" labelled more actions button in the header
        When the user selects the "lines" bound table field on the main page
        And the user selects the "Department" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Operations" in the reference field
        And the user selects "Operations" in the reference field
        And the user selects the "Channel" labelled reference field on a modal
        And the user clears the reference field
        And the user writes "Residential" in the reference field
        And the user selects "Residential" in the reference field
        Then the user clicks the "Apply to all lines" labelled business action button on a modal
        # Add new stock adjustment line
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Pressure transducer strain gauge" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Pressure transducer strain gauge" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "-10" in the "Adjustment quantity" labelled nested numeric field of the selected row in the table field
        #And the user selects the row 1 of the table field
        #And the user writes "Accepted" in the "Quality Control" labelled nested reference field of the selected row in the table field
        #And the user selects "Accepted" in the "Quality Control" labelled nested field of the selected row in the table field
        #And the value of the "New stock quantity" labelled nested numeric field of the selected row in the table field is "10 each"
        And the user presses Control+Enter
        # Verify new stock adjustment line dimensions (Pressure transducer strain gauge)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer strain gauge" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock adjustment line dimensions (Housing)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Housing" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
        # Verify new stock adjustment line dimensions (Integrated circuit amplifier)
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Integrated circuit amplifier" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead-Current"
        And the user selects the "Department" labelled reference field on a modal
        And the value of the reference field is "Operations"
        And the user selects the "Channel" labelled reference field on a modal
        And the value of the reference field is "Residential"
        Then the user clicks the "Cancel" labelled business action button on a modal
