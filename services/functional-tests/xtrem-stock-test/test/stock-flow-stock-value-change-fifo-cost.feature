# The goal of this test is to:
# - create a stock value change for an item that is defined with a valuation method "FIFO cost",
# - post to stock,
# - control the stock journal inquiry,
# - control the new FIFO cost on the item-site.
#  Item used is SVC-ITEM-FIFO-1 / site is CHA-S01

@inventory
Feature: stock-flow-stock-value-change-fifo-cost

    Scenario: Create stock value change for item SVC-ITEM-FIFO-1
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueChange"
        Then the "Stock value changes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Stock value change" titled page is displayed
        # site = Site de Chavanod
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "<PERSON><PERSON><PERSON>" in the reference field
        Then the user selects "Site de Chavanod" in the reference field
        # check valuation method is read-only and empty before choosing item
        When the user selects the "Valuation method" labelled dropdown-list field on the main page
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is ""
        # item = SVC-ITEM-FIFO-1
        When the user selects the "Item" labelled reference field on the main page
        And the user writes "SVC-ITEM-FIFO-1" in the reference field
        Then the user selects "Item 1 for stock value change - FIFO" in the reference field
        # check valuation method is now defaulted with "Average unit cost"
        When the user selects the "Valuation method" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "FIFO cost"
        # enter description
        When the user selects the "Description" labelled text field on the main page
        And the user writes "Test SVC for FIFO cost" in the text field
        Then the value of the text field is "Test SVC for FIFO cost"
        # check date field is read-only and empty before posting the document
        When the user selects the "Date" labelled date field on the main page
        Then the date field is read-only
        And the value of the date field is ""
        # check stock status is Draft
        When the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Draft"

        # check grid is empty at the beginning
        Given the user selects the "lines" bound table field on the main page
        Then the table field is empty
        # add records from FIFO stack
        Given the user clicks the "addFromFifoStack" labelled header action button of the table field
        And the user waits 5 seconds
        Then the dialog title is "Add from FIFO stack"
        # verify records displayed for the selection and pick last one
        Given the user selects the "fifoValuationTierLines" bound table field on a modal
        And the user selects the row with text "PR230026" in the "Document number" labelled column header of the table field
        Then the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "5.11"
        And the user selects the row with text "PR230027" in the "Document number" labelled column header of the table field
        Then the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "6.22"
        And the user selects the row with text "PR230028" in the "Document number" labelled column header of the table field
        Then the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "7.33"
        And the user selects the row with text "PR230029" in the "Document number" labelled column header of the table field
        Then the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "8.44"
        Then the user ticks the main checkbox of the selected row in the table field

        And the user clicks the "Add" labelled business action button on a modal

        Then the user selects the "lines" bound table field on the main page
        When the user selects the row with text "PR230029" in the "Receipt" labelled column header of the table field
        # check unit cost
        Then the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "€ 8.44"
        # check new unit cost
        Then the value of the "New unit cost" labelled nested numeric field of the selected row in the table field is "€ 8.44"
        # check amount
        Then the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 67.52"
        # check new amount
        Then the value of the "New amount" labelled nested numeric field of the selected row in the table field is "€ 67.52"
        # change new unit cost to € 8.88
        And the user writes "8.88" in the "New unit cost" labelled nested numeric field of the selected row in the table field
        Then the value of the "New amount" labelled nested numeric field of the selected row in the table field is "€ 71.04"

        # save stock value change document
        And the user clicks the "Save" labelled business action button on the main page
        # record created notification
        Then a toast with text "Record created" is displayed

    Scenario: Verify the user can add another line to the stock value change document

        # check there are only 3 remaining records displayed in the FIFO stack
        Given the user selects the "lines" bound table field on the main page
        When the user clicks the "Add from FIFO stack" labelled header action button of the table field
        And the user waits 5 seconds
        Then the dialog title is "Add from FIFO stack"

        # verify records displayed for the selection and pick last one
        Given the user selects the "fifoValuationTierLines" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "Document number" labelled nested link field of the selected row in the table field is "PR230028"
        And the user selects the row 2 of the table field
        Then the value of the "Document number" labelled nested link field of the selected row in the table field is "PR230027"
        And the user selects the row 3 of the table field
        Then the value of the "Document number" labelled nested link field of the selected row in the table field is "PR230026"
        And the user selects the row with text "PR230026" in the "Document number" labelled column header of the table field
        Then the user ticks the main checkbox of the selected row in the table field

        And the user clicks the "Add" labelled business action button on a modal

        Then the user selects the "lines" bound table field on the main page

        When the user selects the row with text "PR230026" in the "Receipt" labelled column header of the table field
        # check unit cost
        Then the value of the "Unit cost" labelled nested numeric field of the selected row in the table field is "€ 5.11"
        # check new unit cost
        Then the value of the "New unit cost" labelled nested numeric field of the selected row in the table field is "€ 5.11"
        # check amount
        Then the value of the "Amount" labelled nested numeric field of the selected row in the table field is "€ 25.55"
        # check new amount
        Then the value of the "New amount" labelled nested numeric field of the selected row in the table field is "€ 25.55"
        # change new unit cost to € 5.55
        And the user writes "5.55" in the "New unit cost" labelled nested numeric field of the selected row in the table field
        Then the value of the "New amount" labelled nested numeric field of the selected row in the table field is "€ 27.75"

        # save new values
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # verify document can now be posted
        Given the user clicks the "Post stock" labelled business action button on the main page
        And the user waits 5 seconds
        And the user refreshes the screen
        When the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Completed"
        When the user selects the "Date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_SVC2_Number]"

    Scenario: Verify stock value change movement appears in stock journal inquiry

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed


        And the user selects the "$navigationPanel" bound table field on the navigation panel

        # Enter site criteria = Site de Chavanod
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Site de Chavanod" in the filter of the table field
        And the user ticks the item with text "Site de Chavanod" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Enter item criteria = Item 1 for stock value change - FIFO
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Item 1 for stock value change - FIFO" in the filter of the table field
        And the user ticks the item with text "Item 1 for stock value change - FIFO" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user filters the "Movement amount" labelled column in the table field with value "2.20"

        And the user filters the "Quantity" labelled column in the table field with value "0"

        Then the user closes the filter of the "Quantity" labelled column in the table field
        # first stock value change line
        And the user selects the row 1 of the table field
        Then the value of the "Document" labelled nested link field of the selected row in the table field is "[ENV_SVC2_Number]"
        And the value of the "Date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Location" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Quality control" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "€ 0.4400"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "€ 2.20"
        # second stock value change line
        And the user filters the "Movement amount" labelled column in the table field with value "3.52"
        Then the user selects the row 1 of the table field
        And the value of the "Document" labelled nested link field of the selected row in the table field is "[ENV_SVC2_Number]"
        And the value of the "Date" labelled nested date field of the selected row in the table field is a generated date with value "T"
        And the value of the "Location" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Quality control" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Lot" labelled nested reference field of the selected row in the table field is ""
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "€ 0.4400"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "€ 3.52"

    Scenario: Control item cost has been updated correctly

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "Item 1 for stock value change - FIFO" in the navigation panel
        And the user clicks the record with the text "SVC-ITEM-FIFO-1" in the navigation panel

        # edit item-site
        And selects the "Sites" labelled navigation anchor on the main page
        Given the user selects the "itemSites" bound table field on the main page
        When the user selects the row with text "Site de Chavanod" in the "Site" labelled column header of the table field
        And the user clicks the "Edit" dropdown action of the selected row of the table field

        Then the "Item-site Item SVC-ITEM-FIFO-1-Site CHA-S01" titled sidebar is displayed

        And selects the "General" labelled navigation anchor on the sidebar

        When the user selects the "FIFO unit cost" labelled numeric field on the sidebar

        Then the value of the numeric field is "5.5500"

        And the user clicks the "Cancel" button of the dialog on the sidebar
