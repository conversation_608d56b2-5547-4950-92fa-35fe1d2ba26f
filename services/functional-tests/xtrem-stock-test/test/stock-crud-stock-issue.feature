#The goal of this test is to verify the creation, update and deletion of stock issue.

@inventory
Feature: stock-crud-stock-issue

    Scenario: 01 - Stock issue creation (No posting)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Item to be issued" in the "Item" labelled nested reference field of the selected row in the table field
        And the user waits 1 second
        And the user selects "Item to be issued" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "15" in the "Quantity" labelled nested numeric field of the selected row in the table field
        # And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        # And the user waits 1 second
        # And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "501" in the "Owner" labelled column header of the table field
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "0 each"
        #  When the user ticks the main checkbox of row 1 in the table field
        And the user writes "7" in the "Quantity to issue" labelled nested numeric field of the selected row in the table field
        And the user presses Tab
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "7 each"
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_STKISS01]"


    Scenario: 02 - Stock issue update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "stockIssues" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_STKISS01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        When the user unticks the main checkbox of the selected row in the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user presses Tab
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "15 each"
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 03 - Stock issue read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "stockIssues" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_STKISS01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "Number" labelled text field on the main page
        And the value of the text field is "[ENV_STKISS01]"
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Details entered"
        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row with text "Item to be issued" in the "Item" labelled column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "15 each"
        And the value of the "Actual cost" labelled nested numeric field of the selected row in the table field is "£ 12.00"
        And the value of the "Status" labelled nested numeric field of the selected row in the table field is "Details entered"
    # And the value of the "Quality control" labelled nested reference field of the selected row in the table field is "Accepted"


    Scenario: 04 - Stock issue deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "stockIssues" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_STKISS01]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed


    Scenario: 05 - Stock issue creation and posting
        #Creating stock issue
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Lines" labelled table field on the main page
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Item to be issued" in the "Item" labelled nested reference field of the selected row in the table field
        And the user waits 1 second
        And the user selects "Item to be issued" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "15" in the "Quantity" labelled nested numeric field of the selected row in the table field
        # And the user writes "Accepted" in the "Quality control" labelled nested reference field of the selected row in the table field
        # And the user waits 1 second
        # And the user selects "Accepted" in the "Quality control" labelled nested field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        And the user selects the "stockDetails" bound table field on a modal
        And the user selects the row with text "501" in the "Owner" labelled column header of the table field
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "0 each"
        And the user selects the row 1 of the table field
        When the user ticks the main checkbox of the selected row in the table field
        And the user presses Tab
        And the value of the "Quantity to issue" labelled nested numeric field of the selected row in the table field is "15 each"
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed
        And the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_STKISS02]"
        #Posting stock issue
        And the user clicks the "Post stock" labelled business action button on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockIssue"
        Then the "Stock issues" titled page is displayed
        When the user selects the "All statuses" dropdown option in the navigation panel
        When the user selects the "stockIssues" labelled table field on the navigation panel
        And the user selects the row with text "[ENV_STKISS02]" in the "Number" labelled column header of the table field
        And the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "Issued"
