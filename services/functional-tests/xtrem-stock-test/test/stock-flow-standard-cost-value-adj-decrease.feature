# The goal of this test is to:
# - create a stock value change for an item that is defined with a valuation method "Standard cost",
# - should be automatically posted,
# - check the stock journal inquiry values,
# - control the new average unit cost on the item-site.
#  Item used is Standard cost Item name / site is Boston
# - Script to be updated as soon as bug: https://jira.sage.com/browse/XT-58269 is fixed as the lines are currently not available

@inventory
Feature: stock-flow-standard-cost-value-adj-decrease

    Scenario: 01 Add a new Item-site-cost record
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Standard cost item name" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # Editing Item-Site tab
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        Then the user selects the row with text "Chem. Austin" in the "Site" labelled column header of the table field
        And the user clicks the "Edit" dropdown action of the selected row of the table field

        # Adding a record to the 'Costs' tab
        And selects the "Costs" labelled navigation anchor on the sidebar
        And the user selects the "costs" bound table field on the sidebar
        And the user clicks the "addItemSiteCost" bound action of the table field
        Then the user selects the "Cost category" labelled reference field on the sidebar
        And the user writes "Standard cost" in the reference field
        And the user selects "Standard cost" in the reference field
        And the user selects the "Start date" labelled date field on the sidebar
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "100" in the numeric field

        And the user selects the "Material" labelled numeric field on the sidebar
        And the user writes "800.8888" in the numeric field
        And the user selects the "Machine" labelled numeric field on the sidebar
        And the user writes "1900.9999" in the numeric field
        And the user selects the "Labor" labelled numeric field on the sidebar
        And the user writes "2000.2222" in the numeric field
        And the user selects the "Tool" labelled numeric field on the sidebar
        And the user writes "3100.3333" in the numeric field
        And the user clicks the "Save" labelled business action button on the sidebar

    Scenario: 02 - Verify stock value change for Item Standard cost item id
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueChange"
        Then the "Stock value changes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the user clicks the "Number" labelled nested field of the selected row in the table field
        And the user searches for "US003" in the navigation panel
        And the user clicks the "first" navigation panel's row
        When the user selects the "Number" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_SVCSTD1_Number]"

        #Verify values
        When the user selects the "Site" labelled reference field on the main page
        Then the reference field is read-only
        When the user selects the "Item" labelled reference field on the main page
        Then the reference field is read-only
        When the user selects the "Date" labelled date field on the main page
        Then the value of the date field is a generated date with value "T"
        When the user selects the "Stock status" labelled label field on the main page
        Then the value of the label field is "Completed"

    Scenario: 03 - Verify stock journal inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockJournalInquiry"
        Then the "Stock journal inquiry" titled page is displayed

        And the user selects the "$navigationPanel" bound table field on the navigation panel

        # Enter site criteria = Chem. Austin
        Then the user opens the filter of the "Site" labelled column in the table field
        And the user searches "Chem. Austin" in the filter of the table field
        And the user ticks the item with text "Chem. Austin" in the filter of the table field
        Then the user closes the filter of the "Site" labelled column in the table field

        # Enter item criteria = Standard cost Item name
        Then the user opens the filter of the "Item" labelled column in the table field
        And the user searches "Standard cost Item name" in the filter of the table field
        And the user ticks the item with text "Standard cost Item name" in the filter of the table field
        Then the user closes the filter of the "Item" labelled column in the table field

        And the user selects the row with text "[ENV_SVCSTD1_Number]" in the "Document" labelled column header of the table field
        And the value of the "Quantity" labelled nested numeric field of the selected row in the table field is "0.00 m3"
        And the value of the "Order cost" labelled nested numeric field of the selected row in the table field is "$ 0.0000"
        And the value of the "Order amount" labelled nested numeric field of the selected row in the table field is "$ 0.00"
        And the value of the "Valued cost" labelled nested numeric field of the selected row in the table field is "$ -21.9866"
        And the value of the "Movement amount" labelled nested numeric field of the selected row in the table field is "$ -37,377.26"
        And the value of the "Cost variance" labelled nested numeric field of the selected row in the table field is "$ 21.9866"
        And the value of the "Amount variance" labelled nested numeric field of the selected row in the table field is "$ 37,377.26"
        And the value of the "Non-absorbed amount" labelled nested numeric field of the selected row in the table field is "$ 0.00"
