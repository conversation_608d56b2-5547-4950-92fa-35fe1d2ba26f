# This test covers a new function in the kebab header of material issue page, able to access "Stock journal inquiry" action.
# Notes: Make sure the material issue document status is "Completed" in order to be able to see the new action at the top kebab menu.

@inventory
Feature: stock-crud-material-issue-stock-journal-inquiry

    Scenario: 01 - Verify functionality for the "Stock journal inquiry" top kebab action
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-manufacturing/MaterialTrackingInquiry"
        Then the "Material issues" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user filters the "number" labelled column in the table field with value "WT24000037"
        And the user selects the row with text "WT24000037" in the "number" bound column header of the table field
        And the value of the "Stock status" labelled nested label field of the selected row in the table field is "Completed"
        And the user clicks the "number" labelled nested field of the selected row in the table field
        And the "Material issue WT24000037" titled page is displayed
        And the user clicks the "Stock journal inquiry" labelled more actions button in the header
        And an info dialog appears on a full width modal
        And the dialog title is "Stock journal inquiry" on a full width modal
        And the user selects the "$navigationPanel" bound table field on a full width modal
        And the value of the option menu of the table field is "My selected data"
        And the user selects the row 1 of the table field
        And the value of the "Document line type" labelled nested text field of the selected row in the table field is "Material tracking line"
        And the value of the "Document" labelled nested link field of the selected row in the table field is "WT24000037"
        And the user clicks the Close button of the dialog on a full width modal
        And the "Material issue WT24000037" titled page is displayed
