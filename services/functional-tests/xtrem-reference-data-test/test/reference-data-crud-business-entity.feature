# Create update and delete a business entity

@reference_data
Feature: reference-data-crud-business-entity

    Scenario: Open the Business Entity Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel

    Scenario: Business Entity creation

        # Company information section
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Isle of man" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "IOF_01" in the text field
        # Reference field
        And the user selects the "Country" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "United States of America" in the reference field
        And the user selects "United States of America" in the reference field
        # Reference field
        And the user selects the "Currency" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "US Dollar" in the reference field
        And the user selects "US Dollar" in the reference field
        # Dropdown list
        And the user selects the "Legal entity" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Corporation" in the dropdown-list field
        Then the value of the dropdown-list field is "Corporation"

        And the user selects the "Tax id" labelled text field on the main page
        And the user writes "321-54-9688" in the text field
        And the user selects the "website" bound text field on the main page
        And the user writes "<EMAIL>" in the text field

        ## Selecting the Parent using a reference field ##
        And the user selects the "Parent business entity" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Covalent chemical" in the reference field
        And the user selects "Covalent chemical" in the reference field

        # Address section

        And selects the "Addresses" labelled navigation anchor on the main page

        # Call pod collaction add new item
        And the user selects the "addresses" bound pod collection field on the main page
        And the user clicks the "Add address" button of the selected pod collection field

        ## Sidebar section ##
        And the user selects the "Active" labelled switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Sage Annecy" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "1 Rue Callisto" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "1er étage" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Chavanod" in the text field
        And the user selects the "isPrimary" bound checkbox field on the sidebar
        And the user clicks in the checkbox field

        ## Reference field ##
        And the user selects the "Country" labelled reference field on the sidebar
        Then the value of the reference field is "United States of America"
        And the user selects the "region" bound text field on the sidebar
        And the user writes "Haute-savoie" in the text field
        And the user selects the "postcode" bound text field on the sidebar
        And the user writes "74650" in the text field
        And the user selects the "Phone number" labelled text field on the sidebar
        And the user writes "0687988765" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar

        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: Business Entity update

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field

        When the user searches for "Isle of man" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"

        # Reference field
        And the user selects the "Tax id" labelled text field on the main page
        And the user writes "123456789012" in the text field
        When the user selects the "Country" labelled reference field on the main page
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field

        # Reference field
        And the user selects the "Currency *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Pound Sterling" in the reference field
        And the user selects "Pound Sterling" in the reference field
        And the user selects the "website" bound text field on the main page
        And the user writes "<EMAIL>" in the text field
        And selects the "Addresses" labelled navigation anchor on the main page

        #Pod field
        And the user selects the "addresses" bound pod collection field on the main page
        And the user selects the "Sage Annecy" labelled pod collection item of the selected pod collection field
        And the user clicks the "Edit address" action of the selected pod collection item

        ## Sidebar section ##
        And the user selects the "Active" labelled switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Sage Lyon" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "1 Rue de Lyon" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "1er étage" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Limonest" in the text field

        ## Reference field ##
        And the user selects the "Country" labelled reference field on the sidebar
        And the user writes "United Kingdom" in the reference field
        And the user selects "United Kingdom" in the reference field
        Then the value of the reference field is "United Kingdom"
        And the user selects the "region" bound text field on the sidebar
        And the user writes "Haute-savoie" in the text field
        And the user selects the "postcode" bound text field on the sidebar
        And the user writes "74650" in the text field
        And the user selects the "Phone number" labelled text field on the sidebar
        And the user writes "0687988765" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar

        And the user selects the "addresses" bound pod collection field on the main page
        And the user selects the "Sage Lyon" labelled pod collection item of the selected pod collection field
        And the user clicks the "Add contact" action of the selected pod collection item


        #Reference field
        When the user selects the "Address" labelled reference field on the sidebar
        #And the user clears the reference field
        And the user writes "Sage Lyon" in the reference field
        And the user selects "Sage Lyon" in the reference field


        # Dropdown list
        And the user selects the "Title *" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Dr." in the dropdown-list field
        Then the value of the dropdown-list field is "Dr."

        And the user selects the "First name" labelled text field on the sidebar
        And the user writes "François" in the text field
        And the user selects the "Last name" labelled text field on the sidebar
        And the user writes "Dupont" in the text field

        # Dropdown list
        And the user selects the "Role" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Main contact" in the dropdown-list field
        Then the user selects the "Role" labelled dropdown-list field on the sidebar
        Then the value of the dropdown-list field is "Main contact"


        And the user clicks the "OK" labelled business action button on the sidebar

        # And the user selects the "displayAddresses" bound pod collection field on the main page
        # And the user selects the "Sage Lyon" labelled pod collection item of the selected pod collection field
        # And the user selects the "Contacts" action of the selected pod collection item

        # Call pod collaction add new item
        # And the user selects the "contacts" bound pod collection field on a modal
        # And the user selects the "Add contact" button of the selected pod collection field

        # # Dropdown list
        # And the user clicks in the "Title *" labelled dropdown-list field on the sidebar
        # And selects "Ms" in the "Title *" labelled dropdown-list field on the sidebar
        # Then the value of the "Title *" labelled dropdown-list field on the sidebar is "Ms"

        # And the user writes "Françoise" to the "First name *" labelled text field on the sidebar
        # And the user writes "Duponte" to the "Last name *" labelled text field on the sidebar

        # # Dropdown list
        # And the user clicks in the "Role" labelled dropdown-list field on the sidebar
        # And selects "Financial contact" in the "Role" labelled dropdown-list field on the sidebar
        # Then the value of the "Role" labelled dropdown-list field on the sidebar is "Financial contact"

        # And the user selects the "OK" labelled business action button on the sidebar
        # And the user selects the "OK" labelled business action button on a modal

        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed


    Scenario: Business Entity read

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field

        When the user searches for "Isle of man" in the navigation panel
        And the user clicks the "first" navigation panel's row
        Then the user selects the "Name *" labelled text field on the main page
        Then the value of the text field is "Isle of man"
        And the user selects the "id" bound text field on the main page
        Then the value of the text field is "IOF_01"
        And the user selects the "Tax ID" labelled text field on the main page
        Then the value of the text field is "123456789012"
        And the user selects the "Country" labelled reference field on the main page
        Then the value of the reference field is "United Kingdom"
        And the user selects the "Currency *" labelled reference field on the main page
        Then the value of the reference field is "Pound Sterling"
        And the user selects the "website" bound text field on the main page
        Then the value of the text field is "<EMAIL>"

        And selects the "Addresses" labelled navigation anchor on the main page
        And the value of the "concatenatedAddressWithoutName" bound nested text area field of the selected pod collection item is "1 Rue de Lyon 1er étage Limonest Haute-savoie 74650 United Kingdom"

    Scenario: Business Entity delete

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field

        When the user searches for "Isle of man" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And a toast containing text "Record deleted" is displayed
