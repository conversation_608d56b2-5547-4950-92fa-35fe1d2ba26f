# Duplicate and delete an item directly from the main list

@reference_data
Feature: reference-data-crud-item-main-list

    Scenario: Create a new Item
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed

        When the user clicks the "Create" labelled business action button on the navigation panel

        And the user selects the "Name" labelled text field on the main page
        And the user writes "ITEM-A" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "ID-A" in the text field

        And the user selects the "category" bound reference field on the main page
        And the user writes "Other" in the reference field
        And the user selects "Other" in the reference field
        Then the value of the reference field is "Other"
        When the user selects the "Type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Good" in the dropdown-list field
        Then the value of the dropdown-list field is "Good"

        # INFORMATION TAB SECTION #
        When selects the "Information" labelled navigation anchor on the main page
        And the user selects the "description" bound text area field on the main page
        And the user writes "Item A" in the text area field
        And the user selects the "status" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Active" in the dropdown-list field
        And the value of the dropdown-list field is "Active"
        And the user selects the "GTIN-13" labelled text field on the main page
        And the user writes "1234567890123" in the text field
        # Back on the main page
        And the user selects the "Purchased" labelled switch field on the main page
        Then the user turns the switch field "ON"
        # UNITS TAB SECTION #
        And selects the "Units" labelled navigation anchor on the main page
        ## reference field ##
        When the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "Gram" in the reference field
        And the user selects "Gram" in the reference field
        Then the value of the reference field is "Gram"
        ## reference field ##
        When the user selects the "Purchase unit" labelled reference field on the main page
        And the user writes "Gram" in the reference field
        And the user selects "Gram" in the reference field
        Then the value of the reference field is "Gram"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record created" is displayed
        And the user selects the "ID" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_001]"

    Scenario: Verify that the user can duplicate an item directly from the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        # When the user filters the "id" bound column in the table field with value "[ENV_001]"
        # Then the value of the "id" bound nested text field of row 1 in the table field is "[ENV_001]"
        When the user filters the "id" bound column in the table field with value "ID-A"
        And the user selects the row 1 of the table field
        Then the value of the "id" bound nested text field of the selected row in the table field is "ID-A"
        And the user clicks the "Duplicate" labelled button of the table field
        And a toast with text "Save to create duplicate." is displayed
        And the user selects the "Name" labelled text field on a modal
        And the user writes "ITEM-B" in the text field
        And the user selects the "id" bound text field on a modal
        And the user writes "ID-123" in the text field
        And the user clicks the "Duplicate" labelled business action button on the main page
        Then a toast with text "Record was duplicated successfully." is displayed
        # Save the item name
        And the user selects the "ID" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_002]"

    Scenario: Verify that the user can delete an item directly from the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        When the user filters the "id" bound column in the table field with value "[ENV_002]"
        And the user selects the row 1 of the table field
        Then the value of the "id" bound nested text field of the selected row in the table field is "[ENV_002]"
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        # Verify deletion
        Then a toast with text "Record deleted" is displayed
