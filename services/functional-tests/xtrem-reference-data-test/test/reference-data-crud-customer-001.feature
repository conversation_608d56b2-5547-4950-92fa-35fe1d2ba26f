# The goal of this test is to Create, Update, Delete the US002 customer using a Business entity
@reference_data
@distribution
Feature: reference-data-crud-customer-001
        Scenario: Customer creation from business entity
                # General Tab Information
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
                Then the "Customers" titled page is displayed
                When the user clicks the "Create from business entity" labelled multi action button on the navigation panel

                Given the user selects the "businessEntity" bound reference field on a modal
                Then the user writes "Chem. Irvine" in the reference field
                And the user selects "Chem. Irvine" in the reference field
                And the user clicks the "Next" labelled business action button on a modal

                When the user selects the "addresses" bound pod collection field on a modal
                And the user selects the "US002" labelled pod collection item of the selected pod collection field
                When the user clicks the "Edit address" action of the selected pod collection item
                And selects the "Information" labelled navigation anchor on the sidebar
                And the user selects the "isShippingAddress" bound switch field on the sidebar
                And the user clicks in the switch field
                And the user selects the "isActiveShippingAddress" bound switch field on the sidebar
                And the user clicks in the switch field
                And the user selects the "Primary ship-to address" labelled checkbox field on the sidebar
                And the user ticks the checkbox field
                And the user selects the "Shipping site" labelled reference field on the sidebar
                And the user writes "Chem. Irvine" in the reference field
                And the user selects "Chem. Irvine" in the reference field
                And the user selects the "Delivery mode" labelled reference field on the sidebar
                And the user writes "Road" in the reference field
                And the user selects "Road" in the reference field
                And the user selects the "Delivery lead time" labelled numeric field on the sidebar
                And the user writes "10" in the numeric field
                And the user selects the "Incoterms rule" labelled reference field on the sidebar
                And the user writes "Carriage Paid To" in the reference field
                And the user selects "Carriage Paid To" in the reference field
                And the user clicks the "OK" labelled business action button on the sidebar
                And the user clicks the "Next" labelled business action button on a modal

                And the user selects the "Payment term" labelled reference field on a modal
                And the user writes "Net 15" in the reference field
                And the user selects "Net 15" in the reference field

                And the user clicks the "next" bound business action button on a modal
                Then a toast containing text "Record updated" is displayed

                And the user selects the "Name" labelled text field on the main page
                Then the value of the text field is "Chem. Irvine"
                And the user selects the "ID" labelled text field on the main page
                Then the value of the text field is "US002"
                And the user selects the "Country" labelled reference field on the main page
                Then the value of the reference field is "United States of America"
                And the user selects the "Currency" labelled reference field on the main page
                Then the value of the reference field is "US Dollar"
                And the user selects the "Tax ID" labelled text field on the main page
                And the user writes "965-85-6598" in the text field

                # Shipping Tab
                Given selects the "Address" labelled navigation anchor on the main page
                # Call pod collection add new item
                When the user selects the "addresses" bound pod collection field on the main page
                Then the selected pod collection field is enabled

                And the user selects the "US002" labelled pod collection item of the selected pod collection field
                And the value of the "isActive" bound nested label field of the selected pod collection item is "Active"
                #And the value of the "isPrimary" bound nested label field of the selected pod collection item is "Primary"
                And the value of the "concatenatedAddressWithoutName" bound nested text area field of the selected pod collection item is "US Chemicals Irvine Irvine Irvine 75016 France"
                And the value of the "Shipment site" labelled nested reference field of the selected pod collection item is "Chem. Irvine"
                And the value of the "Delivery mode" labelled nested reference field of the selected pod collection item is "Road"
                And the value of the "Delivery lead time" labelled nested numeric field of the selected pod collection item is "10 day(s)"
                And the value of the "Incoterms rule" labelled nested reference field of the selected pod collection item is "Carriage Paid To"

        Scenario: Customer US002 update
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
                Then the "Customers" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row with text "US002" in the "ID" labelled column header of the table field
                And the user clicks the "ID" labelled nested field of the selected row in the table field

                And selects the "Financial" labelled navigation anchor on the main page
                And the user selects the "Payment term" labelled reference field on the main page
                And the user writes "Net 60" in the reference field
                And the user selects "Net 60" in the reference field
                And the user selects the "Project" labelled reference field on the main page
                And the user writes "General Overhead-Current" in the reference field
                And the user selects "General Overhead-Current" in the reference field
                And the user selects the "Channel" labelled reference field on the main page
                And the user writes "Retail" in the reference field
                And the user selects "Retail" in the reference field

                # Save
                And the user clicks the "Save" labelled business action button on the main page
                Then a toast containing text "Record updated" is displayed

        Scenario: Customer US002 Delete
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
                Then the "Customers" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row with text "US002" in the "ID" labelled column header of the table field
                And the user clicks the "ID" labelled nested field of the selected row in the table field
                And the user clicks the "Delete" labelled more actions button in the header
                And the user clicks the "Delete" button of the Confirm dialog
                Then a toast containing text "Record deleted" is displayed
