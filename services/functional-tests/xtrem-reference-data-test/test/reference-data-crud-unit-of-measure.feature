# Confirmation that a unit of measure record can be created/updated and deleted
# Testing interaction with text fields/dropdown list/table field components
# Text fields allow for special characters as well as numerics

@reference_data
@manufacturing
Feature: reference-data-crud-unit-of-measure

    Scenario: Unit of measure General tab creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/UnitOfMeasure"
        Then the "Units of measure" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Unit description/1" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Unit name/1" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "UOM/1" in the text field
        And the user selects the "Symbol" labelled text field on the main page
        And the user writes "@" in the text field
        And the user selects the "Decimal digits" labelled numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "Unit type" labelled dropdown-list field on the main page
        And the user writes "Each" in the dropdown-list field

    Scenario: Unit of measure Conversion tab creation
        And selects the "Conversion" labelled navigation anchor on the main page
        And the user selects the "Conversion" labelled table field on the main page
        And the user clicks the "addConversion" bound action of the table field
        And the user selects the row 1 of the table field
        And the user writes "Count" in the "Base unit" labelled nested reference field of the selected row in the table field
        And the user selects "Count" in the "Base unit" labelled nested field of the selected row in the table field
        Then the value of the "Base unit" labelled nested reference field of the selected row in the table field is "Count"
        And the user writes "1.001" in the "Factor" labelled nested numeric field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Read Unit of Measure information in navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/UnitOfMeasure"
        Then the "Units of measure" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Unit name/1" in the navigation panel
        And the value of the "description" bound nested text field on the "first" navigation panel's row is "Unit description/1"
        And the value of the "name" bound nested text field on the "first" navigation panel's row is "Unit name/1"

    Scenario: Read Unit of Measure information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/UnitOfMeasure"
        Then the "Units of measure" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Unit name/1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "description" labelled text field on the main page
        Then the value of the text field is "Unit description/1"
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "Unit name/1"
        And the user selects the "id" bound text field on the main page
        Then the value of the text field is "UOM/1"
        And the user selects the "Decimal digits" labelled numeric field on the main page
        Then the value of the numeric field is "2"
        And the user selects the "Unit type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Each"

    Scenario: Update Unit of Measure information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/UnitOfMeasure"
        Then the "Units of measure" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Unit name/1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "isActive" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "description" labelled text field on the main page
        And the user writes "Unit description/1 update" in the text field
        And the user selects the "name" labelled text field on the main page
        And the user writes "Unit name/1 update" in the text field
        And the user selects the "Decimal digits" labelled numeric field on the main page
        And the user writes "3" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Read updated Unit of Measure information in navigation panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/UnitOfMeasure"
        Then the "Units of measure" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Unit name/1 update" in the navigation panel
        And the value of the "description" bound nested text field on the "first" navigation panel's row is "Unit description/1 update"
        And the value of the "name" bound nested text field on the "first" navigation panel's row is "Unit name/1 update"

    Scenario: Delete a Unit of Measure
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/UnitOfMeasure"
        Then the "Units of measure" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Unit name/1 update" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
