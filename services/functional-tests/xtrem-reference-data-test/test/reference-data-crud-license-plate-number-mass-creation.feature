# Create update and delete a license plate number mass creation

@reference_data
Feature: reference-data-crud-license-plate-number-mass-creation

    Scenario: Display License plate number Page

        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LicensePlateNumberMassCreation"
        Then the "LPN mass creation" titled page is displayed

    Scenario: License plate number mass creation

        ##lookup##
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LicensePlateNumberMassCreation"
        Then the "LPN mass creation" titled page is displayed
        Given the user selects the "container" bound reference field on the main page
        And the user clicks the lookup button of the reference field
        And the user selects the "container" bound table field on a modal
        And the user selects the row 1 of the table field
        Then the value of the "id" labelled nested text field of the selected row in the table field is "TestId"
        And the user selects the "container" bound table field on a modal
        And the user selects the row 1 of the table field
        When the user clicks the "id" labelled nested field of the selected row in the table field
        And the user selects the "container" bound reference field on the main page
        Then the value of the reference field is "TestName"
        ##checkboxes##
        And the user selects the "Single item" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Single lot" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "location" labelled reference field on the main page
        And the user writes "location 8" in the reference field
        And the user selects "location 8" in the reference field
        And the user selects the "lpNsToGenerate" labelled numeric field on the main page
        And the user writes "3" in the numeric field
        And the user clicks the "searchButton" bound button on the main page
        And the user clicks the "saveLicensePlateNumbers" bound business action button on the main page
