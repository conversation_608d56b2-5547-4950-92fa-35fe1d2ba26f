# Create update and delete a cost category

@reference_data
Feature: reference-data-crud-cost-category

    Scenario: Display Cost category Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/CostCategory/$new"
        When the "Cost category" titled page is displayed

    Scenario: Cost category creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/CostCategory"
        When the "Cost categories" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "id" bound text field on the main page
        And the user writes "Budgeted" in the text field
        And the user selects the "name" bound text field on the main page
        And the user writes "Budgeted costing" in the text field
        And the user selects the "costCategoryType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Budgeted" in the dropdown-list field
        Then the value of the dropdown-list field is "Budgeted"
        And the user selects the "isMandatory" bound switch field on the main page
        And the user clicks in the switch field
        And the switch field is set to "ON"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Cost category update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/CostCategory"
        When the "Cost categories" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Budgeted costing" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # And the user writes "Budgeted1" to the "id" bound text field on the main page
        And the user selects the "name" bound text field on the main page
        And the user writes "Budgeted costing1" in the text field
        And the user selects the "isMandatory" bound switch field on the main page
        And the user clicks in the switch field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Cost category read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/CostCategory"
        When the "Cost categories" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Budgeted costing1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "id" bound text field on the main page
        Then the value of the text field is "Budgeted"
        And the user selects the "name" bound text field on the main page
        Then the value of the text field is "Budgeted costing1"
        And the user selects the "costCategoryType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Budgeted" in the dropdown-list field
        Then the value of the dropdown-list field is "Budgeted"
        And the user selects the "isMandatory" bound switch field on the main page
        And the switch field is set to "OFF"

    Scenario: Cost category delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/CostCategory"
        When the "Cost categories" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Budgeted costing1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
