# The purpose of this script is to cover the functionalities in the customer main list page

@reference_data
@distribution
Feature:  reference-data-crud-customer-main-list-001

    Scenario: 01 - Verify that the “Create order” action on the main list kebab menu is working correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the user clicks the "Create order" dropdown action of the selected row of the table field
        And the user selects the "Site" labelled reference field on a full width modal
        And the user writes "T<PERSON> Hampton" in the reference field
        And the user selects "T<PERSON> <PERSON>" in the reference field
        And selects the "Lines" labelled navigation anchor on a full width modal
        And the user selects the "lines" bound table field on a full width modal
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "Strawberry cup cake" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "Strawberry cup cake" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "*Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on a full width modal
        And a toast with text "Record created" is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Main List Automation" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "TE Hampton" and column header "Site" of the table widget field
        And the user clicks the link of the cell with column header "Number" of the row of the table widget field
        And the user selects the "Sold-to customer" labelled reference field on a full width modal
        And the value of the reference field is "Main List Automation"

    Scenario: 02 - Verify that the “Put on hold” action on the main list kebab menu is working correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Active"
        And the "Remove on hold" dropdown action of the selected row in the table field is hidden
        And the "Put on hold" dropdown action of the selected row in the table field is enabled
        And the user clicks the "Put on hold" dropdown action of the selected row of the table field
        And a toast containing text "Customer put on hold" is displayed
        And the value of the "Status" labelled nested label field of the selected row in the table field is "On hold"
        And the "Put on hold" dropdown action of the selected row in the table field is hidden
        And the "Remove on hold" dropdown action of the selected row in the table field is displayed

    Scenario: 03 - Verify that the “Remove on hold” action on the main list kebab menu is working correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the value of the "Status" labelled nested label field of the selected row in the table field is "On hold"
        And the "Put on hold" dropdown action of the selected row in the table field is hidden
        And the "Remove on hold" dropdown action of the selected row in the table field is enabled
        And the user clicks the "Remove on hold" dropdown action of the selected row of the table field
        And a toast containing text "Customer hold removed" is displayed
        And the value of the "Status" labelled nested label field of the selected row in the table field is "Active"
        And the "Remove on hold" dropdown action of the selected row in the table field is hidden
        And the "Put on hold" dropdown action of the selected row in the table field is displayed

    Scenario: 04 - Verify that the “Delete” action on the main list kebab menu is working correctly
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the dialog title is "Confirm delete"
        And the user clicks the "Cancel" button of the dialog on the main page
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        Then a validation error message is displayed containing text
            """
            Validation errors
            Deletion impossible: Blocked by property SalesOrder.billToCustomer
            """
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Main List Automation" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "TE Hampton" and column header "Site" of the table widget field
        And the user clicks the "Delete" dropdown action of the selected row of the table widget field
        And the dialog title is "Delete sales order"
        And the user clicks the "Continue" button of the dialog on the main page
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        Then a toast with text "Record deleted" is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field
        Then the total count of records in the table widget fields is "0"
        And the user clicks the "Close record" icon in the header on the main page
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Main List Automation" in the "name" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        Then a toast with text "Record deleted" is displayed
