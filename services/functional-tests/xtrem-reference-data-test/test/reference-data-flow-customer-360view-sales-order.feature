
# This test aims to validate the customer 360 view "In progress Sales order" widget functionalities.
# Focus on verifying the correct behavior and data displayed within the "In progress Sales order" widget placed in the customer 360 view.
# The main functionalities that we need to check are:
# - Correct filtering by the current customer in context.
# - Verification of the content and filtering logic for each of the available views:
#     - Open sales orders (displaying not closed sales orders)
#     - On time sales orders (shipping date >= dayDate)
#     - Late sales orders (shipping date < dayDate)
# - Verification of the functionality of relevant kebab menu actions depending on the sales order status.
# - The top kebab "Refresh" button refresh the widget data and return to "Open sales orders" section by default.
# - Verification of the availability and functionality of "See more" footer action.
# - Verification of the availability and functionality of "Create order" footer action.

@reference_data
@distribution

Feature: reference-data-flow-customer-360view-sales-order

    # !!!IMPORTANT
    # revisit this file when the ER are done:
    # https://jira.sage.com/browse/XT-96143
    # https://jira.sage.com/browse/XT-96144

    Scenario: 01 - Widget loads correctly for a customer with relevant documents - no documents available
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the total count of records in the table widget fields is "0"

    Scenario: 02 - Verification of the availability and functionality of "Create order" footer action
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user clicks the "Create order" button of the table widget field
        And the user waits 3 seconds
        And the user selects the "Sold-to customer" labelled reference field on a full width modal
        And the value of the reference field is "Automation Contact Widget"
        And the reference field is disabled
        And the user selects the "Site" labelled reference field on a full width modal
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And selects the "Lines" labelled navigation anchor on a full width modal
        And the user selects the "lines" bound table field on a full width modal
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "AP Open Item" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "AP Open Item" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "*Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user writes "10" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on a full width modal
        And a toast with text "Record created" is displayed

    Scenario: 03 - Widget loads correctly for a customer with relevant documents - one order available
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "TE Hampton" and column header "Site" of the table widget field
        And the user clicks the link of the cell with column header "Number" of the row of the table widget field
        And the user selects the "Sold-to customer" labelled reference field on a full width modal
        And the value of the reference field is "Automation Contact Widget"

    Scenario: 04 - Verify the availability of the kebab menu actions before confirming - sales order in "Quote" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "Quote" and column header "Status" of the table widget field
    # And the "Create proforma invoice" dropdown action of the selected row in the table field is hidden
    # And the "Allocate stock" dropdown action of the selected row in the table field is hidden
    # And the "Print" dropdown action of the selected row in the table field is enabled
    # And the "Send" dropdown action of the selected row in the table field is enabled
    # And the "Delete" dropdown action of the selected row in the table field is enabled
    # And the "Confirm" dropdown action of the selected row in the table field is enabled

    Scenario: 05 - Verify the functionality of the "Print" kebab menu actions - sales order in "Quote" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "Quote" and column header "Status" of the table widget field
        And the user clicks the "Print" dropdown action of the selected row of the table widget field
        And the user waits 10 seconds
        And the dialog title is "Print document"
        And the user clicks the Close button of the dialog on the main page

    Scenario: 06 - Verify the functionality of the "Send" kebab menu actions - sales order in "Quote" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "TE Hampton" and column header "Site" of the table widget field
        And the user clicks the "Send" dropdown action of the selected row of the table widget field
        And the dialog title is "Send sales quote"
        And the user selects the "Title" labelled dropdown-list field on a modal
        And the value of the dropdown-list field is "Dr."
        And the user selects the "First name" labelled text field on a modal
        And the value of the text field is "Contact 3"
        And the user selects the "Last name" labelled text field on a modal
        And the value of the text field is "for address 3"
        And the user selects the "Email" labelled text field on a modal
        And the user writes "<EMAIL>" in the text field
        And the user clicks the "Send" button of the Custom dialog
        And the user clicks the "Send" button of the Custom dialog
        And no error toast or validation error message is displayed
        And the user waits 10 seconds
        And a toast containing text "Sales quote sent to: <EMAIL>" is displayed
        And the user dismisses all the toasts

    Scenario: 07 - Verify the functionality of the "Delete" kebab menu actions - sales order in "Quote" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "TE Hampton" and column header "Site" of the table widget field
        And the user clicks the "Delete" dropdown action of the selected row of the table widget field
        And the dialog title is "Delete sales order"
        And the user clicks the "Continue" button of the dialog on the main page
        And the dialog title is "Confirm delete"
        And the user clicks the "Delete" button of the dialog on the main page
        And a toast with text "Record deleted" is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user clicks the "Refresh" more actions button in the header of the table widget field
        And the total count of records in the table widget fields is "0"

    Scenario: 08 - Verify the and functionality of the "Confirm" kebab menu actions - sales order in "Quote" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user clicks the "Create order" button of the table widget field
        And the user waits 3 seconds
        And the user selects the "Sold-to customer" labelled reference field on a full width modal
        And the value of the reference field is "Automation Contact Widget"
        And the reference field is disabled
        And the user selects the "Site" labelled reference field on a full width modal
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        And selects the "Lines" labelled navigation anchor on a full width modal
        And the user selects the "lines" bound table field on a full width modal
        And the user adds a new table row to the table field
        And the user selects the floating row of the table field
        And the user writes "AP Open Item" in the "Item" labelled nested reference field of the selected row in the table field
        And the user selects "AP Open Item" in the "Item" labelled nested field of the selected row in the table field
        And the user writes "10" in the "*Quantity in sales unit" labelled nested numeric field of the selected row in the table field
        And the user writes "10" in the "Gross price" labelled nested numeric field of the selected row in the table field
        And the user presses Control+Enter
        And the user clicks the "Save" labelled business action button on a full width modal
        And a toast with text "Record created" is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "Quote" and column header "Status" of the table widget field
        And the user clicks the "Confirm" dropdown action of the selected row of the table widget field
        And the dialog title is "Confirm update"
        And the user clicks the "Confirm" button of the dialog on the main page
        And a toast containing text "The sales order was confirmed." is displayed
        And the user dismisses all the toasts
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "Confirmed" and column header "Status" of the table widget field

    Scenario: 09 - Verify the availability of the kebab menu actions after confirming - sales order in "Confirmed" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "Confirmed" and column header "Status" of the table widget field
    # And the "Confirm" dropdown action of the selected row in the table field is hidden
    # And the "Print" dropdown action of the selected row in the table field is enabled
    # And the "Send" dropdown action of the selected row in the table field is enabled
    # And the "Delete" dropdown action of the selected row in the table field is enabled
    # And the "Create proforma invoice" dropdown action of the selected row in the table field is enabled
    # And the "Allocate stock" dropdown action of the selected row in the table field is enabled

    Scenario: 10 - Verify the availability and functionality of the "Allocate stock" kebab menu actions - sales order in "Confirmed" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "TE Hampton" and column header "Site" of the table widget field
        And the user clicks the "Allocate stock" dropdown action of the selected row of the table widget field
        And the dialog title is "Allocation request submitted"
        And the user clicks the "OK" button of the dialog on the main page
    # And the "Allocate stock" dropdown action of the selected row in the table field is hidden

    Scenario: 11 - Verify the functionality of the "Create proforma invoice" kebab menu actions - sales order in "Confirmed" status
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the user selects the row with text "TE Hampton" and column header "Site" of the table widget field
        And the user clicks the "Create proforma invoice" dropdown action of the selected row of the table widget field
        And the dialog title is "Generate proforma"
        And the user selects the "Expiration date" labelled date field on a modal
        And the user writes a generated date in the date field with value "T"
        And the user selects the "Customer comment" labelled rich text field on a modal
        And the user writes "Test comment" in the rich text field
        And the user clicks the "Generate" button of the dialog on the main page
        And a toast containing text "Record created" is displayed
        And the user dismisses all the toasts

    Scenario: 12 - Verify the "Refresh" top kebab button functionality
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
    # Then the "Open sales orders" tab is selected
    # And selects the "On-time sales orders" tab
    # Then the "On-time sales orders" tab is selected
    # And the user clicks the "Refresh" more actions button in the header of the table widget field
    # Then the "Open sales orders" tab is selected

    Scenario: 13 - Verification of the availability and functionality of "See more" footer action
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "In progress sales orders" titled widget in the dashboard editor is displayed
        And the user selects the "In progress sales orders" titled table widget field in the dashboard
        And the total count of records in the table widget fields is "1"
        And the user clicks the "See more" button of the table widget field
        And the user waits 2 seconds
        And the "Sales orders" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "TE Hampton" in the "Site" labelled column header of the table field

# Scenario: 14 - "Open sales orders" view displays the correct records based on its criteria
# Scenario: 15 - "On time sales orders" view filters records correctly based on shipping date
# Scenario: 16 - "Late sales orders" view filters records correctly based on shipping date
#     And the "return" navigation arrow button in the header on the main page is displayed
#     When the user clicks the "return" navigation arrow button in the header on the main page
#     Then the "Showcase dashboard" titled dashboard is displayed
#     And the 360 view switch in the header is ON
