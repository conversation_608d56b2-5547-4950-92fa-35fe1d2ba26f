Feature: reference-data-crud-tool-resource

    Scenario: Read tool resource information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ToolResource"
        When the "Tool resource" titled page is displayed
        Then searches for "MANSCOT" in the navigation panel
        And the user clicks on the "first" navigation panel's row
        And the "Tool resource - Manual taping machine" titled page is displayed
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Manual taping machine"
        And the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "US002"
        And the user selects the "Quantity" labelled numeric field on the main page
        Then the value of the numeric field is "0.00"
        And the user selects the "Weekly shift" labelled reference field on the main page
        Then the value of the reference field is "5 days 2 x 4 hours"

        And the user selects the "weeklyDetails" bound table field on the main page
        And the value of the "day" bound nested text field of row 1 in the table field is "Monday"
        And the value of the "shift2" bound nested text field of row 1 in the table field is "14:00-18:00"

        And selects the "Cost" labelled navigation anchor on the main page
        And the user selects the "resourceCostCategories" bound table field on the main page
        And the value of the "setupCost" bound nested numeric field of row 1 in the table field is "12.00"
        And the value of the "runCost" bound nested numeric field of row 1 in the table field is "12.00"

    Scenario: Tool resource creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ToolResource"
        When selects the "General" labelled navigation anchor on the main page
        Then the user clicks on the create CRUD button on the main page
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Test tool name" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "Test tool ID" in the text field
        And the user selects the "Site" labelled reference field on the main page
        And the user clicks the lookup button of the reference field
        And the user selects the "site" bound table field on a modal
        And the user clicks the "ID" labelled nested field of row 1 in the table field

        And the user selects the "Weekly shift" labelled reference field on the main page
        And the user clicks the lookup button of the reference field
        And the user selects the "weeklyShift" bound table field on a modal
        And the user clicks the "Name" labelled nested field of row 2 in the table field

        And the user clicks on the save CRUD button on the main page

    Scenario: Update tool resource information on the main page
        # After creating a record the navigation panel is collapsed so need to reopen the page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ToolResource"
        When searches for "Test tool ID" in the navigation panel
        And the user clicks on the "first" navigation panel's row
        And the user selects the "ID" labelled text field on the main page
        And the user writes "Test tool ID updated" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Test tool name updated" in the text field

        # Add a this resource to a resource group
        And the user selects the "Resource group" labelled reference field on the main page
        And the user clicks the lookup button of the reference field
        And the user selects the "resourceGroup" bound table field on a modal
        And the user clicks the "Name" labelled nested field of row 1 in the table field

        # Add a cost record (table reference field steps not available at the moment)
        # When selects the "Cost" labelled navigation anchor
        # And the user clicks the "addCostCategory" bound action of the "resourceCostCategories" bound table field on the main page
        # Then the user writes "Standard cost" in the "costCategory" bound nested reference field of row 1 in the "resourceCostCategories" bound table field in the main page
        # Then the user writes "12.00" in the "setupCost" bound nested numeric field of row 1 in the "resourceCostCategories" bound table field in the main page
        # Then the user writes "25.12" in the "runCost" bound nested numeric field of row 1 in the "resourceCostCategories" bound table field in the main page
        # Then the user clicks on the save CRUD button on the main page

    Scenario: Delete a tool resource
        # Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ToolResource"
        Given searches for "Test tool ID updated" in the navigation panel
        Then the user clicks on the delete CRUD button on the main page
        And the user clicks the "Delete" button of the Confirm dialog
