# The goal of this test is to check creation / update / deletion of standard BoMs
# including phantom components

@reference_data

Feature: reference-data-flow-standard-bom-with-phantom

    Scenario: 1 - Create a new finished product FG_PHANTOM_3 for the standard BoM - item & item-site created
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Header information
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Finished good - phantom test 3" in the text field
        When the user selects the "id" bound text field on the main page
        And the user writes "FG_PHANTOM_3" in the text field
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Good"

        # Manufacturing switch field turned to "ON"
        When the user selects the "Manufactured" labelled switch field on the main page
        Then the user turns the switch field "ON"
        Then the switch field is set to "ON"

        # Sold switch field turned to "ON"
        When the user selects the "Sold" labelled switch field on the main page
        Then the user turns the switch field "ON"
        Then the switch field is set to "ON"

        # select units' tab
        And selects the "Units" labelled navigation anchor on the main page

        # enter stock unit
        When the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # enter sales unit
        When the user selects the "Sales unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # add item-site record
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        Then the "Item-sites" titled sidebar is displayed

        When selects the "General" labelled navigation anchor on the sidebar
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        When selects the "Replenishment" labelled navigation anchor on the sidebar

        And the user selects the "Replenishment method" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "By MRP" in the dropdown-list field
        Then the value of the dropdown-list field is "By MRP"

        When the user clicks the "OK" labelled business action button on the sidebar

        # create record
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: 2 - Create a new component PHANT_COMP_TEST for the standard BoM - item created
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Header information
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Phantom component - Test" in the text field
        When the user selects the "id" bound text field on the main page
        And the user writes "PHANT_COMP_TEST" in the text field
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Good"

        # Manufacturing switch field is turned "OFF"
        When the user selects the "Manufactured" labelled switch field on the main page
        Then the switch field is set to "OFF"

        # Purchased switch field turned to "ON"
        When the user selects the "Purchased" labelled switch field on the main page
        Then the user turns the switch field "ON"
        Then the switch field is set to "ON"

        # Sold switch field turned to "ON"
        When the user selects the "Sold" labelled switch field on the main page
        Then the user turns the switch field "ON"
        Then the switch field is set to "ON"

        # select units' tab
        And selects the "Units" labelled navigation anchor on the main page

        # enter stock unit
        When the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # enter purchase unit
        When the user selects the "Purchase unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # enter sales unit
        When the user selects the "Sales unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # create record
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: 3 - Create corresponding BOM for FG_PHANTOM_3 - status "In development"
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Bill of material" titled page is displayed

        # Check default value of BOM status
        And selects the "Information" labelled navigation anchor on the main page
        When the user selects the "Status" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "In development"

        And selects the "Components" labelled navigation anchor on the main page
        Then the user selects the "name" labelled text field on the main page
        And the user writes "Finished good 3 - with phantom A" in the text field

        # select phantom item created previously #
        Then the user selects the "Item" labelled reference field on the main page
        And the user writes "Finished good - pha" in the reference field
        And the user selects "Finished good - phantom test 3" in the reference field

        # select site = site de Chavanod #
        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        # enter base quantity = 1 each #
        Then the user selects the "Base quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field

        # add components to the BOM #
        Given the user selects the "components" bound table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field

        # component number 5 - Phantom component A1
        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Phantom component - A1" in the reference field
        And the user selects "Phantom component - A1" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "add" labelled business action button on the sidebar

        # component number 10 - Phantom A
        Given the user selects the "components" bound table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field
        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Phantom A" in the reference field
        And the user selects "Phantom A" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "add" labelled business action button on the sidebar

        # check components' lines
        ## first component is a normal component, new Phantom checkbox is unticked
        Given the user selects the "components" bound table field on the main page
        When the user selects the row with text "5" in the "Component number" labelled column header of the table field
        Then the value of the "Phantom" labelled nested checkbox field of the selected row in the table field is "false"
        ## second component is a phantom, corresponding checkbox is ticked
        When the user selects the row with text "10" in the "Component number" labelled column header of the table field
        Then the value of the "Phantom" labelled nested checkbox field of the selected row in the table field is "true"

        # save record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 4 - Verify component PHANT_COMP_TEST can be added to the BoM
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Finished good 3 - with phantom A" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Finished good 3 - with phantom A"

        # add components to the BOM #
        Given the user selects the "components" bound table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field

        # component number 15 - Phantom component - Test
        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Phantom component - Test" in the reference field
        And the user selects "Phantom component - Test" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "1" in the numeric field
        And the user clicks the "add" labelled business action button on the sidebar

        # check line added
        Given the user selects the "components" bound table field on the main page
        When the user selects the row with text "15" in the "Component number" labelled column header of the table field
        Then the value of the "Phantom" labelled nested checkbox field of the selected row in the table field is "false"

        # save record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 5 - Check standard BOM status can't be changed to "Available to use"
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Finished good 3 - with phantom A" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # select status and try to change it to "Available to use"
        And selects the "Information" labelled navigation anchor on the main page
        When the user selects the "Status" labelled dropdown-list field on the main page
        Then the user clicks in the dropdown-list field
        And the user selects "Available to use" in the dropdown-list field
        Then the value of the dropdown-list field is "Available to use"

        # save record - check an error is displayed
        Then the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """"
            Validation errors
            The PHANT_COMP_TEST item does not exist at the CHA-S01 site.
            """

        And the user clicks the "Cancel" labelled business action button on the main page

    Scenario: 6 - Delete a standard BoM with phantom item
        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Finished good 3 - with phantom A" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Finished good 3 - with phantom A"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
