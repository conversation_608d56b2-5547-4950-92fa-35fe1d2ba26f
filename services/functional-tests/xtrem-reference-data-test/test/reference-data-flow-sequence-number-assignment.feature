# This tests the flow of reading the default assigned sequence numbers.

@reference_data
Feature: reference-data-flow-sequence-number-assignment


    Scenario: Read the result of a record to ensure that the default sequence numbers are active
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/SequenceNumberAssignmentSetup"
        Then the "Sequence number assignment" titled page is displayed

        When the user selects the "results" labelled nested grid field on the main page
        And the user selects row with text "Sales" in column with header "Menu" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Sales order" in column with header "Document" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Sales order" in column with header "Sequence number" in the nested grid field
        Then the value of the "Sequence number" labelled nested text field of the selected row in the nested grid field is "Sales order"
        And the value of the "Default" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And the value of the "Active" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And takes a screenshot

    Scenario: Filter by France legislation and ensure the nested grid displays the correct records for this legislation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/SequenceNumberAssignmentSetup"
        Then the "Sequence number assignment" titled page is displayed

        When the user selects the "Legislation" labelled reference field on the main page
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        And the user clicks in the "searchButton" bound button field on the main page
        When the user selects the "results" labelled nested grid field on the main page
        And the user selects row with text "Sales" in column with header "Menu" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Sales invoice" in column with header "Document" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Assign on posting: Posted sales invoice" in column with header "Sequence number" in the nested grid field
        Then the value of the "Legislation" labelled nested text field of the selected row in the nested grid field is "France"
        And the value of the "Default" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And the value of the "Active" labelled nested checkbox field of the selected row in the nested grid field is "true"

        And the user selects row with text "Sales invoice" in column with header "Document" in the nested grid field
        And the user collapses the selected row of the nested grid field
        And the user selects row with text "Sales credit memo" in column with header "Document" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Assign on posting: Posted sales credit memo" in column with header "Sequence number" in the nested grid field
        Then the value of the "Legislation" labelled nested text field of the selected row in the nested grid field is "France"
        And the value of the "Default" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And the value of the "Active" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And takes a screenshot

    Scenario: Filter by France legislation and all default values and ensure the nested grid displays the records for France as well as default
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/SequenceNumberAssignmentSetup"
        Then the "Sequence number assignment" titled page is displayed

        When the user selects the "Legislation" labelled reference field on the main page
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        And the user selects the "Include default values" labelled checkbox field on the main page
        And the user ticks the checkbox field
        And the user clicks in the "searchButton" bound button field on the main page
        When the user selects the "results" labelled nested grid field on the main page
        And the user selects row with text "Sales" in column with header "Menu" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Sales invoice" in column with header "Document" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user selects row with text "Assign on posting: Posted sales invoice" in column with header "Sequence number" in the nested grid field
        Then the value of the "Legislation" labelled nested text field of the selected row in the nested grid field is "France"
        And the value of the "Default" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And the value of the "Active" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And the user selects row with text "Sales invoice" in column with header "Document" in the nested grid field
        And the user collapses the selected row of the nested grid field
        And the user selects row with text "Sales" in column with header "Menu" in the nested grid field
        And the user collapses the selected row of the nested grid field
        # Check other default sequence numbers
        And the user selects row with text "Finance" in column with header "Menu" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user waits 6 seconds
        And the user selects row with text "Payment" in column with header "Document" in the nested grid field
        And the user expands the selected row of the nested grid field
        And the user waits 6 seconds
        And the user selects row with text "Payment" in column with header "Sequence number" in the nested grid field
        # Test that the legislation is empty/default
        Then the value of the "Legislation" labelled nested text field of the selected row in the nested grid field is ""
        And the value of the "Default" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And the value of the "Active" labelled nested checkbox field of the selected row in the nested grid field is "true"
        And takes a screenshot
