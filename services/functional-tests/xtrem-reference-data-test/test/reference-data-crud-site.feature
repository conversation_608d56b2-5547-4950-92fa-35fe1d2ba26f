# Goal is to test that a Site record can be created/updated and deleted
# Read is explicitly tested in the read scenario
@reference_data
@finance
@distribution
@inventory
@manufacturing
Feature: reference-data-crud-site

    Scenario: Site creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed

        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the user selects the "isActive" bound switch field on the main page
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"

        And the user selects the "Name" labelled text field on the main page
        And the user writes "QC Test S01" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "QCS01" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "QC Test Site 01" in the text field
        And the user selects the "Legal company" labelled reference field on the main page
        And the user writes "Société S2" in the reference field
        And the user selects "Société S2" in the reference field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        And the user selects the "Tax ID" labelled text field on the main page
        And the user writes "FR00000989999" in the text field
        And the user presses Enter
        And the user selects the "SIRET" labelled text field on the main page
        And the user writes "00000034254999" in the text field
        And the user presses Enter

        # Addresses tab
        And selects the "Address" labelled navigation anchor on the main page
        And the user selects the "addresses" bound pod collection field on the main page
        And the user clicks the "Add address" button of the selected pod collection field

        # New site address sidebar
        And the user selects the "Primary address" labelled checkbox field on the sidebar
        And the user clicks in the checkbox field
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "QC Address" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "11 Silwer-Eike" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "Heuweloord" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Paris" in the text field
        And the user selects the "Department" labelled text field on the sidebar
        And the user writes "France" in the text field
        And the user selects the "Postal code" labelled text field on the sidebar
        And the user writes "0010" in the text field
        And the user selects the "Country" labelled reference field on the sidebar
        And the value of the reference field is "France"
        And the user selects the "Phone number" labelled text field on the sidebar
        And the user writes "***********" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar

        # Financial tab
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Admin" in the reference field
        And the user selects "Admin" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field

        And selects the "Management" labelled navigation anchor on the main page
        Then the user selects the "Finance" labelled switch field on the main page
        And the switch field is set to "ON"

        Then the user selects the "Purchase" labelled switch field on the main page
        And the switch field is set to "ON"

        Then the user selects the "Stock" labelled switch field on the main page
        And the switch field is set to "ON"

        Then the user selects the "Sales" labelled switch field on the main page
        And the switch field is set to "ON"

        Then the user selects the "Manufacturing" labelled switch field on the main page
        And the switch field is set to "ON"

        # Save the new Site
        And the user waits 2 seconds
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record created" is displayed

    Scenario: Read site information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed

        # Search for Site
        When the user selects the "Sites" labelled table field on the main page
        And the user filters the "ID" labelled column in the table field with value "QCS01"
        And the user selects the row with text "QCS01" in the "ID" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field

        # Read site
        And the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "ON"
        When the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "QC Test S01"
        When the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "QCS01"
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "QC Test Site 01"
        When the user selects the "Legal company" labelled reference field on the main page
        Then the value of the reference field is "Société S2"

    Scenario: Update site information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed

        # Search for Site
        When the user selects the "Sites" labelled table field on the main page
        And the user filters the "ID" labelled column in the table field with value "QCS01"
        And the user selects the row with text "QCS01" in the "ID" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field

        # Make changes to the Site
        And the user selects the "isActive" bound switch field on the main page
        And the user clicks in the switch field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "QC Test S01E" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "QC Test Site 01E" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed

        # Verify updated information
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "QC Test S01E"
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "QC Test Site 01E"

    Scenario: Delete a site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed

        # Search for Site
        When the user selects the "Sites" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "QC Test S01E"
        And the user selects the row with text "QC Test S01E" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field

        # Delete the site
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast with text "Record deleted" is displayed

    # Delete business entity for above created site to clean up data completely and keep QA data consistent
    Scenario: Delete business entity for related site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed

        # Search for BE
        When the user selects the "Business entities" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "QC Test S01E"
        And the user selects the row with text "QC Test S01E" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field

        # Delete the BE
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast with text "Record deleted" is displayed
