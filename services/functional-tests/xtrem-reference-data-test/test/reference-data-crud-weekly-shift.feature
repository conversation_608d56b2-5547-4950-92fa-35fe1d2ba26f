#The goal of this test is to verify the creation, update and deletion of a weekely shift

@reference-data
@manufacturing
Feature: reference-data-crud-weekly-shift

    Scenario: Verify the user is able to create a Weekly Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/WeeklyShift"
        Then the "Weekly shifts" titled page is displayed
        When the user selects the "weeklyShifts" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        When the user selects the "id" labelled text field on the main page
        And the user writes "QC TEST hours ID" in the text field
        Then the user selects the "name" labelled text field on the main page
        And the user writes "QC TEST hours name" in the text field
        ##Select Monday shift##
        And the user selects the "mondayShift" labelled reference field on the main page
        And the user writes "QA_TWENTY_FOUR_HOURS_SHIFT" in the reference field
        And the user selects "QA_TWENTY_FOUR_HOURS_SHIFT" in the reference field
        ##Select Tuesday shift##
        And the user selects the "tuesdayShift" labelled reference field on the main page
        And the user writes "QA_DUAL_SHIFT_EIGHT_HOURS" in the reference field
        And the user selects "QA_DUAL_SHIFT_EIGHT_HOURS" in the reference field
        ##Select Wednesday shift##
        And the user selects the "wednesdayShift" labelled reference field on the main page
        And the user writes "QA_SINGLE_SHIFT_EIGHT_HOURS" in the reference field
        And the user selects "QA_SINGLE_SHIFT_EIGHT_HOURS" in the reference field
        ##Select Thursday shift##
        And the user selects the "thursdayShift" labelled reference field on the main page
        And the user writes "QA_TRIPLE_SHIFT_SIX_HOURS" in the reference field
        And the user selects "QA_TRIPLE_SHIFT_SIX_HOURS" in the reference field
        ##Select Friday shift##
        And the user selects the "fridayShift" labelled reference field on the main page
        And the user writes "QA_TWENTY_FOUR_HOURS_SHIFT" in the reference field
        And the user selects "QA_TWENTY_FOUR_HOURS_SHIFT" in the reference field
        ##Select Saturday shift##
        And the user selects the "saturdayShift" labelled reference field on the main page
        And the user writes "QA_DUAL_SHIFT_EIGHT_HOURS" in the reference field
        And the user selects "QA_DUAL_SHIFT_EIGHT_HOURS" in the reference field
        ##lookup Sunday shift##
        And the user selects the "sundayShift" labelled reference field on the main page
        And the user writes "QA_SINGLE_SHIFT_EIGHT_HOURS" in the reference field
        And the user selects "QA_SINGLE_SHIFT_EIGHT_HOURS" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify the user is able to update a Weekly Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/WeeklyShift"
        Then the "Weekly shifts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC TEST hours ID" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC TEST hours name" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        And the user writes "QC TEST hours name edit" in the text field
        ##Select Wednesday shift##
        And the user selects the "wednesdayShift" labelled reference field on the main page
        And the user writes "QA_TWENTY_FOUR_HOURS_SHIFT" in the reference field
        And the user selects "QA_TWENTY_FOUR_HOURS_SHIFT" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify the user is able to read details of a Weekly Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/WeeklyShift"
        Then the "Weekly shifts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC TEST hours ID" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC TEST hours name edit" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC TEST hours name edit"
        And the user selects the "id" labelled text field on the main page
        Then the value of the text field is "QC TEST hours ID"
        And the user selects the "wednesdayShift" labelled reference field on the main page
        Then the value of the reference field is "QA_TWENTY_FOUR_HOURS_SHIFT"
        And the user selects the "sundayShift" labelled reference field on the main page
        Then the value of the reference field is "QA_SINGLE_SHIFT_EIGHT_HOURS"

    Scenario: Verify the user is able to update a Weekly Shift to a Full Week Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/WeeklyShift"
        Then the "Weekly shifts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC TEST hours ID" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC TEST hours name edit" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC TEST hours name edit"
        And the user selects the "fullWeek" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify the user is able to delete a Weekly Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/WeeklyShift"
        Then the "Weekly shifts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC TEST hours ID" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC TEST hours name edit" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC TEST hours name edit"
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
