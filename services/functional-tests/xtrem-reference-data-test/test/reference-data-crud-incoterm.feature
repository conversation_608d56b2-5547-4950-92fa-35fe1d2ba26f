#The goal of this test is to verify the creation, update and deletion of the incoterm rules

@reference-data
@manufacturing
Feature: reference-data-crud-incoterm

    Scenario: Verify the user is able to create an Incoterm rule
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Incoterm"
        Then the "Incoterms® rules" titled page is displayed
        When the user selects the "incotermsRules" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        And the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "ON"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Cost and Freight 2.0" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "CFR 2.0" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Cost and Freight 2.0" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify the user is able to update an Incoterm rule
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Incoterm"
        Then the "Incoterms® rules" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "CFR 2.0" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Cost and Freight 2.0" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Cost and Freight 3.0" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Cost and Freight 3.0" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify the user is able to read details of an Incoterm rule
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Incoterm"
        Then the "Incoterms® rules" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Cost and Freight 3.0" in the "Name" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Cost and Freight 3.0" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Cost and Freight 3.0"
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Cost and Freight 3.0"

    Scenario: Verify the user is able to delete an Incoterm rule
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Incoterm"
        Then the "Incoterms® rules" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Cost and Freight 3.0" in the "Name" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Cost and Freight 3.0" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
