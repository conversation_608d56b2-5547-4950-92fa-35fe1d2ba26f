#The goal of this test is to verify the creation, update and deletion of a supplier (without business entity)

@reference_data
@distribution
Feature:  reference-data-crud-supplier-without-be

    Scenario: 01 - Supplier creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        When the user clicks the "Create" labelled multi action button on the navigation panel
        Then the user selects the "isActive" bound switch field on the main page
        When the user clicks in the switch field
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        Then the user writes "NoBESup-126" in the text field
        And the user selects the "ID" labelled text field on the main page
        Then the user writes "XT52126" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "United States" in the reference field
        Then the user selects "United States of America" in the reference field
        And the user selects the "currency" labelled reference field on the main page
        Then the value of the reference field is "US Dollar"
        And the user selects the "Tax ID" labelled text field on the main page
        Then the user writes "126-52-0002" in the text field
        #Adding Address 1 (Primary)
        And selects the "Address" labelled navigation anchor on the main page
        And the user selects the "Addresses" labelled pod collection field on the main page
        When the user clicks the "Add address" button of the selected pod collection field
        And the user selects the "isActive" bound switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Name *" labelled text field on the sidebar
        And the user writes "ADDR001" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "52 This Street" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "That Place" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "TheCity" in the text field
        And the user selects the "postcode" bound text field on the sidebar
        And the user writes "0002" in the text field
        And the user selects the "Country" labelled reference field on the sidebar
        And the user writes "United States" in the reference field
        And the user selects "United States of America" in the reference field
        And the user selects the "Phone number" labelled text field on the sidebar
        And the user writes "0123456789" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar
        #Adding Address 2
        And the user selects the "Addresses" labelled pod collection field on the main page
        When the user clicks the "Add address" button of the selected pod collection field
        And the user selects the "isActive" bound switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Primary address" labelled checkbox field on the sidebar
        And the value of the checkbox field is "false"
        And the user selects the "Name *" labelled text field on the sidebar
        And the user writes "ADDR002" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "126 That Road" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "This Area" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "TheCity" in the text field
        And the user selects the "postcode" bound text field on the sidebar
        And the user writes "0002" in the text field
        And the user selects the "Country" labelled reference field on the sidebar
        And the user writes "United States" in the reference field
        And the user selects "United States of America" in the reference field
        And the user selects the "Phone number" labelled text field on the sidebar
        And the user writes "0987654321" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar
        #Capturing Financial data
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        And the user writes "Net 45" in the reference field
        And the user selects "Net 45" in the reference field
        And the user selects the "Posting class" labelled reference field on the main page
        And the user writes "Material suppliers" in the reference field
        And the user selects "Material suppliers" in the reference field
        And the user selects the "Payment method" labelled text field on the main page
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Admin" in the reference field
        And the user selects "Admin" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 02 - Supplier update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "XT52126" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Payment method" labelled text field on the main page
        And the user selects the "Primary bill-by address" labelled pod field on the main page
        And the user clicks the "Replace" action of the pod field
        And the user selects the "billByAddressLookup" bound table field on a modal
        And the user selects the row with text "ADDR002" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        #Capturing Item data
        And selects the "Items" labelled navigation anchor on the main page
        And the user selects the "items" bound table field on the main page
        And the user clicks the "add" labelled action of the table field
        And the user selects the "isActive" bound switch field on the sidebar
        And the user clicks in the switch field
        And the user selects the "Item *" labelled reference field on the sidebar
        And the user writes "Propanediol" in the reference field
        And the user selects "Propanediol" in the reference field
        And the user selects the "Item-supplier ID" labelled text field on the sidebar
        And the user writes "69801-A" in the text field
        And the user selects the "Item-supplier name" labelled text field on the sidebar
        And the user writes "69801-A" in the text field
        And the user selects the "Default supplier" labelled checkbox field on the sidebar
        And the user clicks in the checkbox field
        And the user selects the "Unit *" labelled reference field on the sidebar
        And the user writes "Cubic meter" in the reference field
        And the user selects "Cubic meter" in the reference field
        And the user clicks the "OK" labelled business action button on the sidebar
        #Capturing Certificates data
        And selects the "Certificates" labelled navigation anchor on the main page
        And the user selects the "certificates" bound table field on the main page
        And the user clicks the "addCertificate" bound action of the table field
        And the user selects the "certificateReference" labelled text field on the sidebar
        And the user writes "ISO TSTMKH Certificate" in the text field
        And the user selects the "standard" labelled reference field on the sidebar
        And the user writes "ISO" in the reference field
        And the user selects "ISO 9001" in the reference field
        And the user selects the "Original certification date" labelled date field on the sidebar
        And the user writes "03/30/2021" in the date field
        And the user selects the "Certification date" labelled date field on the sidebar
        And the user writes "05/30/2021" in the date field
        And the user selects the "Validity end date" labelled date field on the sidebar
        And the user writes "04/30/2024" in the date field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 03 - Supplier read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "XT52126" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "NoBESup-126"
        And the user selects the "Tax ID" labelled text field on the main page
        Then the value of the text field is "126-52-0002"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Payment term *" labelled reference field on the main page
        Then the value of the reference field is "Net 45"
        And selects the "Certificates" labelled navigation anchor on the main page
        And the user selects the "certificates" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Valid until" labelled nested date field of the selected row in the table field is "2024-04-30"

    Scenario: Supplier Delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "XT52126" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: Deleting the business entity
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        And the user selects the "Business entities" labelled table field on the main page
        And the user filters the "ID" labelled column in the table field with value "XT52126"
        And the user selects the row with text "NoBESup-126" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
