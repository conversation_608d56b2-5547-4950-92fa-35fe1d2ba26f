# the aim is to verify that the creation of a landed cost item is possible
# we verify the business rules regarding LC tab, posting class

@reference_data
Feature: reference-data-crud-item-lc

    Scenario: Item creation - Landed cost - LC001
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        # HEADER SECTION #
        And the user selects the "Name" labelled text field on the main page
        And the user writes "LC001" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "LC001" in the text field
        When the user selects the "Type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        Then the user selects "Landed cost" in the dropdown-list field

        #Verify disabled fields
        And the user selects the "Stock management" labelled switch field on the main page
        And the switch field is read-only
        And the user selects the "Purchased" labelled switch field on the main page
        And the switch field is read-only
        And the user selects the "Manufactured" labelled switch field on the main page
        And the switch field is read-only
        And the user selects the "Sold" labelled switch field on the main page
        And the switch field is read-only

        # INFORMATION TAB SECTION #
        When selects the "Information" labelled navigation anchor on the main page
        And scrolls to the "Management" labelled block
        And the user selects the "description" bound text area field on the main page
        And the user writes "Landed cost Item 001" in the text area field

        #fill in unit tab
        When selects the "Units" labelled navigation anchor on the main page
        And the user selects the "stockUnit" bound reference field on the main page
        And the user writes "eac" in the reference field
        And the user selects "Each" in the reference field

        #fill in the sites tab
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        Then the "Item-sites" titled sidebar is displayed
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field
        When the user clicks the "OK" labelled business action button on the sidebar

        #verify that the tab has been added
        When selects the "Management" labelled navigation anchor on the main page
        And scrolls to the "Landed costs" labelled block
        When the user selects the "landedCostType" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        Then the user selects "Tariffs" in the dropdown-list field
        And the user selects the "Allocation rule " labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        Then the user selects "By quantity" in the dropdown-list field

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: verify that the fields are still disabled after saving
        # open Record  LC001
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "LC001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Verify disabled fields
        And the user selects the "Stock management" labelled switch field on the main page
        And the switch field is read-only
        And the user selects the "Purchased" labelled switch field on the main page
        And the switch field is read-only
        And the user selects the "Manufactured" labelled switch field on the main page
        And the switch field is read-only
        And the user selects the "Sold" labelled switch field on the main page
        Then the switch field is read-only


    Scenario: Financial tab update & posting class verification - 1
        # open Record  LC001
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "LC001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Financial tab update
        And selects the "Financial" labelled navigation anchor on the main page
        When the user selects the "Posting class" labelled reference field on the main page
        And the user writes "Standard rate items and services" in the reference field
        And the user selects "Standard rate items and services" in the reference field

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed
        Then a toast containing text "Record updated" is displayed


    Scenario: Financial tab update & posting class verification - 2
        # open Record LC001
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "LC001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #delete the record
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "LC001"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
