# Update an item to lot managed , create stock, verify control on item.

@reference_data
@inventory
Feature: reference-data-flow-item-lot-update

    Scenario: Item selection in order to update the lot management
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        # Filtering the data
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        When the user searches for "Glycerin" in the navigation panel
        Then the user clicks the "first" navigation panel's row
        # Stock section: enabling lot management
        When selects the "Management" labelled navigation anchor on the main page
        When the user selects the "Lot management" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Lot" in the dropdown-list field
        Then the value of the dropdown-list field is "Lot"
        # Lot sequence number reference field
        When the user selects the "Lot sequence number" labelled reference field on the main page
        And the user clicks the lookup button of the reference field
        And the user selects the "lotSequenceNumber" bound table field on a modal
        And the user selects the row with text "Material lot number" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        # Save the update
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Creation of a stock receipt with a lot assigment
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        # Creation of a stock receipt
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Chem. Boston" in the reference field
        Then the user selects "Chem. Boston" in the reference field
        # Add a line in the table
        When the user selects the "lines" labelled table field on the main page
        And the user adds a new table row to the table field using the sidebar
        # Enter data in the sidebar
        When the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Glycerin" in the reference field
        Then the user selects "Glycerin" in the reference field
        # Quantity field in the sidebar
        When the user selects the "Quantity" labelled numeric field on the sidebar
        And the user writes "100" in the numeric field
        Then the value of the numeric field is "100.00"
        # Choose the Status
        When the user selects the "Quality control" labelled reference field on the sidebar
        And the user writes "Accepted" in the reference field
        Then the user selects "Accepted" in the reference field
        And the user clicks the "Apply" button of the dialog on the sidebar
        Then the user clicks the "Save" labelled business action button on the main page
        # Entering stock details
        When the user selects the "lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Stock details" dropdown action of the selected row of the table field
        # Adding stock details in the table in the modal
        When the user selects the "Stock details" labelled table field on a modal
        And the user clicks the "Add a line" labelled header action button of the table field
        And the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        Then the user clicks the "Post stock" labelled business action button on the main page


    Scenario: Verification of stock receipt status
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-stock/StockReceipt"
        Then the "Stock receipts" titled page is displayed
        # Filtering the data and checking status

        And the user selects the "All statuses" dropdown option in the navigation panel

        And the user selects the "$navigationPanel" bound table field on the navigation panel

        And the user selects the row 1 of the table field
        When the user clicks the "stockSite__name" bound nested field of the selected row in the table field

        And the user selects the "Lines" labelled table field on the main page
        And the user selects the row 1 of the table field
        Then the value of the "Status" labelled nested text field of the selected row in the table field is "Received"

    Scenario: Verification of Item business rules are rights
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        # Filtering the data
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        When the user searches for "Glycerin" in the navigation panel
        Then the user clicks the "first" navigation panel's row
        # Stock section: changing lot management value wrong to validate the business rules
        When selects the "Management" labelled navigation anchor on the main page
        When the user selects the "Lot management" labelled dropdown-list field on the main page
        And the value of the dropdown-list field is "Lot"
        And the user clicks in the dropdown-list field
        And the user selects "None" in the dropdown-list field
        Then the value of the dropdown-list field is "None"
        And the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """"
            Validation errors
            You can only change lot management when the stock value is zero.
            """
