# Create update and delete a location zone

@reference_data
Feature: reference-data-crud-location-zone

    # Question. This test has no assertions. What is being tested here?
    Scenario: Display Storage zone page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationZone"
        Then the "Storage zones" titled page is displayed

    # Question. The only assertion is that the dropdown selects the correct value. What is being tested here?
    Scenario: Storage zone creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationZone"
        Then the "Storage zones" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Storage zone" titled page is displayed
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Test@Name_Storage zone" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "Test@ID_Storage zone" in the text field
        And the user selects the "site" bound reference field on the main page
        And the user writes "Chem. Atlanta" in the reference field
        And the user selects "Chem. Atlanta" in the reference field
        And the user selects the "Type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Hazard" in the dropdown-list field
        Then the value of the dropdown-list field is "Hazard"
        And the user clicks the "Save" labelled business action button on the main page

    # Question. The only assertion is that the dropdown selects the correct value. What is being tested here?
    Scenario: Storage zone update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationZone"
        Then the "Storage zones" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "id" bound nested field of the selected row in the table field
        When the user searches for "Test@ID_Storage zone" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user writes "New_Test@NameOfLocation" in the text field
        And the user selects the "Type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Chemical" in the dropdown-list field
        Then the value of the dropdown-list field is "Chemical"
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Storage zone read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationZone"
        Then the "Storage zones" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        When the user searches for "Test@ID_Storage zone" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "New_Test@NameOfLocation"
        And the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "Test@ID_Storage zone"
        And the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "Chem. Atlanta"
        And the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Chemical"

    Scenario: Storage zone delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationZone"
        Then the "Storage zones" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        When the user searches for "Test@ID_Storage zone" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
