# Purpose of this test is to verify the flow from phantom item creation till BOM creation in both statuses
# from "In development" to "Available to use" including BOM update and BOM deletion.
@reference-data

Feature: reference-data-flow-phantom-bom

    <PERSON><PERSON><PERSON>: 1- Create a new phantom item PHANTOM_D without item-site record

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Header information
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Phantom D/BOM creation" in the text field
        When the user selects the "id" bound text field on the main page
        And the user writes "<PERSON>HA<PERSON><PERSON>_D" in the text field
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Good"

        # Manufacturing switch field turned to "ON"
        When the user selects the "Manufactured" labelled switch field on the main page
        Then the user turns the switch field "ON"
        Then the switch field is set to "ON"

        # Phantom switch field turned to "ON"
        And selects the "Management" labelled navigation anchor on the main page
        Then the user selects the "Phantom" labelled switch field on the main page
        And the user turns the switch field "ON"

        # select units' tab
        And selects the "Units" labelled navigation anchor on the main page

        # enter stock unit
        When the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # create record
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: 2- Create corresponding BOM for PHANTOM_D - status "In development"

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Bill of material" titled page is displayed

        # Check default value of BOM status
        And selects the "Information" labelled navigation anchor on the main page
        When the user selects the "Status" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "In development"

        And selects the "Components" labelled navigation anchor on the main page
        Then the user selects the "name" labelled text field on the main page
        And the user writes "Phantom D Test BOM" in the text field

        # select phantom item created previously #
        Then the user selects the "Item" labelled reference field on the main page
        And the user writes "Phantom D" in the reference field
        And the user selects "Phantom D/BOM creation" in the reference field

        # select site = site de Chavanod #
        Then the user selects the "Site" labelled reference field on the main page
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        # enter base quantity = 10 each #
        Then the user selects the "Base quantity" labelled numeric field on the main page
        And the user writes "10" in the numeric field

        # add components to the BOM #
        Given the user selects the "components" bound table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field

        # component number 5 - Phantom component A1
        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Phantom component - A1" in the reference field
        And the user selects "Phantom component - A1" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "add" labelled business action button on the sidebar

        # component number 510 - Phantom component A2
        Given the user selects the "components" bound table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field
        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Phantom component - A2" in the reference field
        And the user selects "Phantom component - A2" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "add" labelled business action button on the sidebar

        # save record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: 3- Check phantom BOM status can't be changed to "Available to use"

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Phantom D Test BOM" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # select status and try to change it to "Available to use"
        And selects the "Information" labelled navigation anchor on the main page
        When the user selects the "Status" labelled dropdown-list field on the main page
        Then the user clicks in the dropdown-list field
        And the user selects "Available to use" in the dropdown-list field
        Then the value of the dropdown-list field is "Available to use"

        # save record - check an error is displayed
        Then the user clicks the "Save" labelled business action button on the main page
        Then a validation error message is displayed containing text
            """"
            Validation errors

            Status: You need to add an item-site record before you can set the status to Available to use.
            """

    Scenario: 4- Create item-site record for PHANTOM_D

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Phantom D/BOM creation" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # verify item ID
        When the user selects the "id" bound text field on the main page
        Then the value of the text field is "PHANTOM_D"

        # add item-site record
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        Then the "Item-sites" titled sidebar is displayed

        When selects the "General" labelled navigation anchor on the sidebar
        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        When selects the "Replenishment" labelled navigation anchor on the sidebar

        And the user selects the "Replenishment method" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "By MRP" in the dropdown-list field
        Then the value of the dropdown-list field is "By MRP"

        When the user clicks the "OK" labelled business action button on the sidebar

        # save record
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 5- Check phantom BOM status can now be changed to "Available to use"

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Phantom D Test BOM" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # select status and change it to "Available to use"
        And selects the "Information" labelled navigation anchor on the main page
        When the user selects the "Status" labelled dropdown-list field on the main page
        Then the user clicks in the dropdown-list field
        And the user selects "Available to use" in the dropdown-list field
        Then the value of the dropdown-list field is "Available to use"

        # save record - no error is displayed
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 6- Verify phantom BOM in status "Available to use" can be updated

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Phantom D Test BOM" in the navigation panel
        And the user clicks the "first" navigation panel's row

        # add component to the BOM #
        Given the user selects the "components" bound table field on the main page
        When the user clicks the "addComponentLine" bound action of the table field

        # component number 15 - Phantom component A3
        Then the "New component" titled sidebar is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "15" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Phantom component - A3" in the reference field
        And the user selects "Phantom component - A3" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "10" in the numeric field
        And the user clicks the "add" labelled business action button on the sidebar

        # save record - no error is displayed
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 7- Delete phantom BOM

        Given the user opens the application on a HD desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Phantom D Test BOM" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Phantom D Test BOM"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
