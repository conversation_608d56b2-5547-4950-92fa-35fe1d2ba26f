# Confirmation that a credit memo reason record can be created/updated and deleted
# Testing interaction with text fields
# Text fields allow for special characters as well as numerics

@reference_data
@distribution
Feature: reference-data-crud-credit-memo-reason

    Scenario: Credit memo reason creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemoReason"
        Then the "Credit memo reasons" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Credit memo reason name@01" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Credit memo reasons description@02" in the text field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Credit memo update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemoReason"
        Then the "Credit memo reasons" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Credit memo reason name@01" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Credit memo update name@01" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Credit memo update description@02" in the text field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Credit memo delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-sales/SalesCreditMemoReason"
        Then the "Credit memo reasons" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Credit memo update name@01" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
