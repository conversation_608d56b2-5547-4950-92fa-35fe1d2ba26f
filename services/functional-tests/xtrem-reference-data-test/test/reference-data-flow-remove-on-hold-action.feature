# The goal of this test is to verify that the new top kebab menu action "Remove on hold" has the expected behavior.


@reference_data
@distribution
Feature:  reference-data-flow-remove-on-hold-action

    Scenario: 01 - Validate that the "Put on hold" action is available in the kebab menu when the customer is not currently on hold and is active
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Menu Action Automation" in the "name" labelled column header of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        And the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Active"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "On hold" labelled checkbox field on the main page
        And the value of the checkbox field is "false"
        And the user clicks the "Put on hold" labelled more actions button in the header
        And a toast containing text "Customer put on hold" is displayed

    Scenario: 02 - Validate that the "Put on hold" action is hidden when the customer is already on hold
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "On hold"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "On hold" labelled checkbox field on the main page
        And the value of the checkbox field is "true"
        And the user clicks the more actions button in the header
        Then the header actions dropdown menu elements are:
            | Remove on hold |
            | divider        |
            | Create field   |
            | divider        |
            | Delete         |

    Scenario: 03 - Validate that the "Remove on hold" action is available in the kebab menu when the customer is currently on hold and is active
        And selects the "General" labelled navigation anchor on the main page
        And the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "displayStatus" labelled label field on the main page
        And the value of the label field is "On hold"
        And the user clicks the "Remove on hold" labelled more actions button in the header
        And a toast containing text "Customer hold removed" is displayed

    Scenario: 04 - Validate that the "Remove on hold" action is hidden when the customer is not on hold
        And the user selects the "displayStatus" labelled label field on the main page
        Then the value of the label field is "Active"
        And the user clicks the more actions button in the header
        Then the header actions dropdown menu elements are:
            | Create order |
            | divider      |
            | Put on hold  |
            | divider      |
            | Create field |
            | divider      |
            | Delete       |

    Scenario: 05 - If the customer is inactive the "Put on hold"/"Remove on hold" options shouldn't be visible
        And the user selects the "Active" labelled switch field on the main page
        Then the user turns the switch field "OFF"
        And the user clicks the "Save" labelled business action button on the main page
        And the user clicks the more actions button in the header
        Then the header actions dropdown menu elements are:
            | Create field |
            | divider      |
            | Delete       |
        And the user selects the "Active" labelled switch field on the main page
        Then the user turns the switch field "ON"
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: 06 - Verify that the isOnHold property is displayed as read-only on the customer page (Financial tab)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Menu Action Automation" in the "name" labelled column header of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        When selects the "Financial" labelled navigation anchor on the main page
        Given the user selects the "On hold" labelled checkbox field on the main page
        Then the checkbox field is disabled
