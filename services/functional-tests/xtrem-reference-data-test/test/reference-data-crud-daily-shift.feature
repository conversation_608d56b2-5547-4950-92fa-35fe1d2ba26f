#The goal of this test is to verify the creation, update and deletion of a daily shift

@reference-data
@manufacturing
Feature: reference-data-crud-daily-shift

    Scenario: Verify the user is able to create a Daily Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DailyShift"
        Then the "Daily shifts" titled page is displayed
        When the user selects the "dailyShifts" labelled table field on the main page
        And the user clicks the "Create" labelled business action button of the table field
        And the user selects the "id" labelled text field on the main page
        And the user writes "QC TEST daily ID" in the text field
        And the user selects the "name" labelled text field on the main page
        And the user writes "QC TEST daily name" in the text field
        And the user selects the "fullDay" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Verify the user is able to update a Daily Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DailyShift"
        Then the "Daily shifts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC TEST daily ID" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "QC TEST daily name" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        And the user writes "QC TEST daily name edit" in the text field
        And the user selects the "id" labelled text field on the main page
        And the user writes "QC TEST daily ID edit" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Verify the user is able to read details of a Daily Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DailyShift"
        Then the "Daily shifts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC TEST daily ID edit" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "QC TEST daily name" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC TEST daily name edit"
        And the user selects the "id" labelled text field on the main page
        Then the value of the text field is "QC TEST daily ID edit"

    Scenario: Verify the user is able to delete a newly created Daily Shift
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DailyShift"
        Then the "Daily shifts" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC TEST daily ID edit" in the "ID" labelled column header of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "QC TEST daily name" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC TEST daily name edit"
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
