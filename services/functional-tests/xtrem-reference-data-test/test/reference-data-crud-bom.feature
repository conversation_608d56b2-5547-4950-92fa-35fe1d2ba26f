# The goal of this test is to create a BOM, update it and delete it.
# Read is explicitly tested

@manufacturing
@inventory
@reference_data
Feature: reference-data-crud-bom

    Scenario: Bill of Material creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Bill of material" titled page is displayed

        When the user selects the "name" labelled text field on the main page
        And the user writes "QC Test BOM" in the text field

        ## Selecting Item using a reference field ##
        And the user selects the "Item" labelled reference field on the main page
        And the user writes "Bottle 100 ml Hydro-alcoholic gel formula 2 hand antisepsis" in the reference field
        And the user selects "Bottle 100 ml Hydro-alcoholic gel formula 2 hand antisepsis" in the reference field

        ## Selecting the Parent using a reference field ##
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Chem. Austin" in the reference field
        And the user selects "Chem. Austin" in the reference field

        # Dropdown list
        And selects the "Information" labelled navigation anchor on the main page
        When the user selects the "Status" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Available to use" in the dropdown-list field
        Then the value of the dropdown-list field is "Available to use"

        And selects the "Components" labelled navigation anchor on the main page
        And the user selects the "components" bound table field on the main page
        And the user clicks the "addComponentLine" bound action of the table field
        # And the "New component" titled side panel is displayed
        And the user selects the "componentNumber" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Acrylates C10-30" in the reference field
        And the user selects "Acrylates C10-30" in the reference field
        And the user selects the "linkQuantity" bound numeric field on the sidebar
        And the user writes "72.300" in the numeric field
        And the user clicks the "add" labelled business action button on the sidebar

        ##lookup end##
        And the user selects the "Base quantity" labelled numeric field on the main page
        And the user writes "100" in the numeric field
        # Save the new BOM
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Update Bill of Material
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        # Search creation
        And the user searches for "QC Test BOM" in the navigation panel
        And the user clicks the record with the text "QC Test BOM" in the navigation panel
        # Edit the BOM
        And the user selects the "name" labelled text field on the main page
        And the user writes "QC Test BOM edit" in the text field
        And the user selects the "Base quantity" labelled numeric field on the main page
        And the user writes "222" in the numeric field
        # Save the changes
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Read Bill of Material
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        # Search creation
        And the user searches for "QC Test BOM" in the navigation panel
        And the user clicks the record with the text "QC Test BOM edit" in the navigation panel
        # Read the BOM
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Status" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Available to use" in the dropdown-list field
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC Test BOM edit"
        When the user selects the "Item" labelled reference field on the main page
        Then the value of the reference field is "Bottle 100 ml Hydro-alcoholic gel formula 2 hand antisepsis"
        When the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "Chem. Austin"
        When the user selects the "Base quantity" labelled numeric field on the main page
        Then the value of the numeric field is "222"

    Scenario: Verify check status page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        # Search creation
        And the user searches for "QC Test BOM" in the navigation panel
        And the user clicks the record with the text "QC Test BOM edit" in the navigation panel
        # Read the BOM
        When the user clicks the "Check stock" labelled more actions button in the header
        Then an info dialog appears on the main page
        And the text in the header of the dialog is "Check stock"
        When the user selects the "Quantity to produce" labelled numeric field on a modal
        Then the value of the numeric field is "222"

        When the user selects the "componentShortage" bound table field on a modal
        Then the user selects the row with text "Acrylates C10-30" in the "Component description" labelled column header of the table field
        And the value of the "Available quantity" labelled nested text field of the selected row in the table field is "0"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "72 L"
        And the value of the "Shortage" labelled nested reference field of the selected row in the table field is "72 L"

        When the user selects the "Quantity to produce" labelled numeric field on a modal
        When the user writes "100" in the numeric field

        When the user clicks the "stockCheck" bound button on a modal
        Then the value of the numeric field is "100"

        When the user selects the "componentShortage" bound table field on a modal
        Then the user selects the row with text "Acrylates C10-30" in the "Component description" labelled column header of the table field
        And the value of the "Available quantity" labelled nested numeric field of the selected row in the table field is "0"
        And the value of the "Required quantity" labelled nested numeric field of the selected row in the table field is "33 L"
        And the value of the "Shortage" labelled nested reference field of the selected row in the table field is "33 L"

    Scenario: Check the "Stock" new tab is correctly added in the "Edit" panel
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row 1 of the table field
        And the user clicks the "name" labelled nested field of the selected row in the table field
        # Search creation
        Then the user searches for "QC Test BOM edit" in the navigation panel
        When the user clicks the record with the text "QC Test BOM edit" in the navigation panel
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC Test BOM edit"

        And selects the "Components" labelled navigation anchor on the main page
        And the user selects the "components" bound table field on the main page
        Then the user selects the row with text "Acrylates C10-30" in the "Component description" labelled column header of the table field

        And the user selects the row 1 of the table field
        And the user clicks the "Edit" dropdown action of the selected row of the table field
        And selects the "Stock" labelled navigation anchor on the sidebar

        And the user selects the "Stock shortage status" labelled label field on the sidebar
        And the value of the label field is "Available stock"

    Scenario: Delete newly created Bill of Material
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        # Search creation
        Then the user searches for "QC Test BOM edit" in the navigation panel
        When the user clicks the record with the text "QC Test BOM edit" in the navigation panel
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC Test BOM edit"
        # Delete the BOM
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
