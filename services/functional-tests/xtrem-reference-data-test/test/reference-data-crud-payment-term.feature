#The goal of this test is to verify the creation, update and deletion of a payment term

@reference_data
@finance
Feature: reference-data-crud-payment-term

        Scenario: 01 - Payment term creation
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
                Then the "Payment terms" titled page is displayed
                And the user clicks the "Create" labelled business action button on the navigation panel
                And the user selects the "isActive" bound switch field on the main page
                And the switch field is set to "ON"
                And the user selects the "Business entity type" labelled dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "Supplier" in the dropdown-list field
                And the user selects the "Name" labelled text field on the main page
                And the user writes "QC" in the text field
                And the user selects the "Description" labelled text field on the main page
                And the user writes "Description test" in the text field
                And the user selects the "days" bound numeric field on the main page
                And the user writes "30" in the numeric field
                And the user selects the "dueDateType" bound dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "After invoice date" in the dropdown-list field
                And the user selects the "From" labelled dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "After invoice date" in the dropdown-list field
                And the user selects the "Day" labelled numeric field on the main page
                And the user writes "10" in the numeric field
                And the user selects the "discountType" bound dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "Percentage" in the dropdown-list field
                And the user selects the "discountAmount" bound numeric field on the main page
                And the user writes "12" in the numeric field
                And the user selects the "penaltyType" bound dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "Amount" in the dropdown-list field
                And the user selects the "penaltyAmount" bound numeric field on the main page
                And the user writes "135" in the numeric field
                And the user clicks the "Save" labelled business action button on the main page
                And a toast containing text "Record created" is displayed


        Scenario: 02 - Payment term update
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
                Then the "Payment terms" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row 1 of the table field
                And the user clicks the "Name" labelled nested field of the selected row in the table field
                And the user searches for "QC" in the navigation panel
                And the user clicks the "first" navigation panel's row
                And the user selects the "Name" labelled text field on the main page
                And the user writes "QC-Mod" in the text field
                And the user selects the "Description" labelled text field on the main page
                And the user writes "Description Edit" in the text field
                And the user clicks the "Save" labelled business action button on the main page
                And a toast containing text "Record updated" is displayed


        Scenario: 03 - Payment term read
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
                Then the "Payment terms" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row 1 of the table field
                And the user clicks the "Name" labelled nested field of the selected row in the table field
                And the user searches for "QC-Mod" in the navigation panel
                And the user clicks the "first" navigation panel's row
                And the user selects the "Business entity type" labelled dropdown-list field on the main page
                Then the value of the dropdown-list field is "Supplier"
                And the user selects the "Name" labelled text field on the main page
                Then the value of the text field is "QC-Mod"
                And the user selects the "Description" labelled text field on the main page
                Then the value of the text field is "Description Edit"


        Scenario: 04 - Payment term deletion
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/PaymentTerm"
                Then the "Payment terms" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row 1 of the table field
                And the user clicks the "Name" labelled nested field of the selected row in the table field
                And the user searches for "QC-Mod" in the navigation panel
                And the user clicks the "first" navigation panel's row
                And the user clicks the "Delete" labelled more actions button in the header
                And the user clicks the "Delete" button of the Confirm dialog
                And a toast containing text "Record deleted" is displayed
