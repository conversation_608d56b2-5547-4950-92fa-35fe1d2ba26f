# The goal of this test is to create a routing, update it, Duplicate it and delete it.
# Read is explicitly tested
# Routing used: Manufactured Item 2 Routing


@manufacturing
@reference_data
Feature: reference-data-crud-routing


    Scenario: Routing creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Dropdown list
        And the user selects the "Status" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Available to use" in the dropdown-list field

        And the user selects the "name" labelled text field on the main page
        And the user writes "QC Test Routing" in the text field

        ##Select start##
        And the user selects the "Item" labelled reference field on the main page
        And the user writes "Hydro-alcoholic gel for hand antisepsis" in the reference field
        And the user selects "Hydro-alcoholic gel for hand antisepsis" in the reference field
        Then the value of the reference field is "Hydro-alcoholic gel for hand antisepsis"
        When the user selects the "Site" labelled reference field on the main page
        And the user writes "Chem. Boston" in the reference field
        And the user selects "Chem. Boston" in the reference field
        Then the value of the reference field is "Chem. Boston"

        When the user selects the "batchQuantity" labelled numeric field on the main page
        And the user writes "10.50" in the numeric field
        ##Select end##

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Update Routing name
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        # Search for the routing
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC Test Routing" in the navigation panel
        And the user clicks the record with the text "QC Test Routing" in the navigation panel
        # Update the routing
        And the user selects the "name" labelled text field on the main page
        And the user writes "QC Test Routing edit" in the text field
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC Test Routing edit"
        # Save the change
        When the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Read Routing
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        # Search for the routing
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC Test Routing edit" in the navigation panel
        And the user clicks the record with the text "QC Test Routing edit" in the navigation panel
        # Read the routing
        And the user selects the "Status" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Available to use"
        When the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC Test Routing edit"
        When the user selects the "Item" labelled reference field on the main page
        Then the value of the reference field is "Hydro-alcoholic gel for hand antisepsis"
        When the user selects the "site" labelled reference field on the main page
        Then the value of the reference field is "Chem. Boston"

    Scenario: Delete newly created Routing
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        # Search for the routing
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC Test Routing edit" in the navigation panel
        And the user clicks the record with the text "QC Test Routing edit" in the navigation panel
        # Verify it is the correct routing to delete
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC Test Routing edit"
        # Delete the routing
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    Scenario: Duplicate an existing routing - 1
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        And the user selects the "Routings" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "Manufactured Item 2 Routing"
        And the user selects the row with text "Manufactured Item 2 Routing" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field

        # Store the main page data
        And the user selects the "Status" labelled dropdown-list field on the main page
        And the user stores the value of the dropdown-list field with the key "[ENV_Status]"

        And the user selects the "Name" labelled text field on the main page
        And the user stores the value of the text field with the key "[ENV_Name]"

        And the user selects the "Item" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Item]"

        And the user selects the "Site" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Site]"

        And the user selects the "Time unit" labelled reference field on the main page
        And the user stores the value of the reference field with the key "[ENV_Time_Unit]"


        # Store the nested grid operation data

        And the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "1" in column with header "Operation number" in the nested grid field

        # TODO: ER required to store the value of the nested grid field value: https://jira.sage.com/browse/XT-60056
        # And the user stores the value of the "Name" labelled nested text field of the selected row in the nested grid field with the key "[ENV_Operation_Name]"
        # And the user stores the value of the "Setup time" labelled nested numeric field of the selected row in the nested grid field with the key "[ENV_Setup_Time]"

        # And the user stores the value of the "Run time" labelled nested text field of the selected row in the nested grid field with the key "[ENV_Run_Time]"
        # And the user stores the value of the "Capability level name" labelled nested text field of the selected row in the nested grid field with the key "[ENV_Capability level]"
        # And the user stores the value of the "Operation cost" labelled nested numeric field of the selected row in the nested grid field with the key "[ENV_Operation_Cost]"
        # And the user stores the value of the "Production step" labelled nested checkbox field of the selected row in the nested grid field with the key "[ENV_Production_Step]"

        And the user expands the selected row of the nested grid field
        And the user selects row with text "LABOR_RES_01" in column with header "ID" in the nested grid field

        # TODO: BUG LOG - Unable to Select nested row column with same name.
        # And the user stores the value of the "Name" labelled nested text field of the selected row in the nested grid field with the key "[ENV_Resource_Name]"
        # And the user stores the value of the "ID" labelled nested text field of the selected row in the nested grid field with the key "[ENV_Resource_ID]"
        # And the user stores the value of the "Type" labelled nested text field of the selected row in the nested grid field with the key "[ENV_Resource_Type]"





        # Click Duplicate action
        And the user clicks the "Duplicate" labelled button in the header


        # Verify the data is the same when in creation
        When the user selects the "Status" labelled dropdown-list field on a modal
        Then the value of the dropdown-list field is "[ENV_Status]"

        When the user selects the "Name" labelled text field on a modal
        ## when duplicating an existing routing, the duplication window opens with:
        ##  - item field = empty
        ##  - name field = empty
        ##  - status field = the one of the original routing
        #Then the value of the text field is "[ENV_Name]"

        # Change the name of the routing
        And the user writes "Duplicate routing 001" in the text field
        And the user stores the value of the text field with the key "[ENV_Duplicate_Routing_Name]"

        # Set a new item
        When the user selects the "Item" labelled reference field on a modal
        And the user writes "Manufactured Item 1" in the reference field
        And the user selects "Manufactured Item 1" in the reference field
        And the user stores the value of the reference field with the key "[ENV_Duplicate_Routing_Item]"
        And the user clicks the "Duplicate" labelled business action button on a modal
        Then a toast containing text "Record was duplicated successfully." is displayed


        When the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"

        When the user selects the "Time unit" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Time_Unit]"

        # Set a batch quantity
        When the user selects the "Batch quantity" labelled numeric field on the main page
        And the user writes "1" in the numeric field


        # Verify the Operation grid

        #When the user selects the "operations" bound table field on the main page
        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "1" in column with header "Operation number" in the nested grid field


        # Verify
        # TODO: ER required https://jira.sage.com/browse/XT-60135
        # Then the value of the "Name" labelled nested text field of the selected row in the nested grid field is "[ENV_Operation_Name]"


        Then the value of the "Name" labelled nested text field of the selected row in the nested grid field is "Operation 1"
        Then the value of the "Setup time" labelled nested numeric field of the selected row in the nested grid field is "1.00 h"
        Then the value of the "Capability level name" labelled nested text field of the selected row in the nested grid field is "Low level of capability"
        # LOG ATP BUG - Scrolling issue on nested grid
        #Then the value of the "Operation cost" labelled nested numeric field of the selected row in the nested grid field is "20.00"
        #Then the value of the "Production step" labelled nested checkbox field of the selected row in the nested grid field is "true"


        # Duplicate
        # I will confirm about the relevance of the two commented lines below
        # When the user clicks the "Duplicate" labelled business action button on the sidebar
        # Then the dialog title is "Duplicate routing" on the main page

        # And the user clicks the "Create duplicate" button of the Confirm dialog on the main page


        # Verify the duplicated values on main page of new routing

        And the user selects the "Status" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "[ENV_Status]"

        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "[ENV_Duplicate_Routing_Name]"

        And the user selects the "Item" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Duplicate_Routing_Item]"

        And the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Site]"

        And the user selects the "Time unit" labelled reference field on the main page
        Then the value of the reference field is "[ENV_Time_Unit]"

        # Verify the duplicated values of the nested operations grid of the new routing

        When the user selects the "Operations" labelled nested grid field on the main page
        And the user selects row with text "1" in column with header "Operation number" in the nested grid field

        # TODO: ER required to store the value of the nested grid field value: https://jira.sage.com/browse/XT-60056
        # Refactor below after above

        Then the value of the "Name" labelled nested text field of the selected row in the nested grid field is "Operation 1"
        Then the value of the "Setup time" labelled nested numeric field of the selected row in the nested grid field is "1.00 h"

        Then the value of the "Run time" labelled nested text field of the selected row in the nested grid field is "1.00 h"
        Then the value of the "Capability level name" labelled nested text field of the selected row in the nested grid field is "Low level of capability"
        Then the value of the "Operation cost" labelled nested numeric field of the selected row in the nested grid field is "20.00"
        Then the value of the "Production step" labelled nested checkbox field of the selected row in the nested grid field is "true"

        And the user expands the selected row of the nested grid field
        And the user selects row with text "LABOR_RES_01" in column with header "ID" in the nested grid field

        # TODO: BUG LOG - Same column names issue
        # Then the value of the "Name" labelled nested text field of the selected row in the nested grid field is "[ENV_Resource_Name]"
        Then the value of the "Type" labelled nested text field of the selected row in the nested grid field is "LaborResource"


    Scenario: Duplicate an existing routing - 2
        # Delete the duplicated routing
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-technical-data/Routing"
        Then the "Routings" titled page is displayed
        And the user selects the "Routings" labelled table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Duplicate routing 001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog on the main page
