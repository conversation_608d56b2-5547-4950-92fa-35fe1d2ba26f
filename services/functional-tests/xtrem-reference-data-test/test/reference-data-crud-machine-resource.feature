#The goal of this test is to verify the creation, update and deletion of a machine resource

@reference_data
@manufacturing
Feature: reference-data-crud-machine-resource

    Scenario: 01 - Machine resource creation
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/MachineResource"
        Then the "Machine resources" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        And the user turns the switch field "OFF"
        And the switch field is set to "OFF"
        And the user turns the switch field "ON"
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Test Machine Name" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "Test Machine ID" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Test Machine Description" in the text field
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Chem. Seattle" in the reference field
        And the user selects "Chem. Seattle" in the reference field
        And the user selects the "Active from" labelled date field on the main page
        And the user writes "10/01/2021" in the date field
        And the user selects the "Active from" labelled date field on the main page
        And the user blurs the date field
        And the user selects the "Active to" labelled date field on the main page
        And the user blurs the date field
        And the user selects the "Capability level" labelled reference field on the main page
        And the user writes "" in the reference field
        And the user selects "Expert level of capability" in the reference field
        And the user selects the "Machine model" labelled text field on the main page
        And the user writes "Model 1234" in the text field
        And the user selects the "Machine serial number" labelled text field on the main page
        And the user writes "Serial 1234" in the text field
        And the user selects the "Weekly shift" labelled reference field on the main page
        And the user writes "4 days mixed hours" in the reference field
        And the user selects "4 days mixed hours" in the reference field
        And the user selects the "Efficiency" labelled numeric field on the main page
        And the user writes "90" in the numeric field
        And selects the "Contract" labelled navigation anchor on the main page
        And the user selects the "ID" labelled text field on the main page
        And the user writes "Contract 1234" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Contract Name" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed


    Scenario: 02 - Machine resource update
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/MachineResource"
        Then the "Machine resources" titled page is displayed
        And the user selects the "Machine resources" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "Test Machine Name"
        And the user selects the row with text "Test Machine Name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Description Machine EDIT" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed


    Scenario: 03 - Machine resource read
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/MachineResource"
        Then the "Machine resources" titled page is displayed
        And the user selects the "Machine resources" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "Test Machine Name"
        And the user selects the row with text "Test Machine Name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Machine resource Test Machine Name" titled page is displayed
        Then the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Description Machine EDIT"
        And the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "Chem. Seattle"
        And the user selects the "Active from" labelled date field on the main page
        Then the value of the date field is "10/01/2021"
        And the user selects the "Machine model" labelled text field on the main page
        Then the value of the text field is "Model 1234"
        And the user selects the "Weekly shift" labelled reference field on the main page
        Then the value of the reference field is "4 days mixed hours"


    Scenario: 04 - Machine resource deletion
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/MachineResource"
        Then the "Machine resources" titled page is displayed
        And the user selects the "Machine resources" labelled table field on the main page
        And the user filters the "Name" labelled column in the table field with value "Test Machine Name"
        And the user selects the row with text "Test Machine Name" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And a toast containing text "Record deleted" is displayed
