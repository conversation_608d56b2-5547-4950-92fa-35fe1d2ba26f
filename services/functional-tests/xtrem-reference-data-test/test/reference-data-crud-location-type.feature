# Create update and delete a location type

@reference_data
Feature: reference-data-crud-location-type

    Scenario: Display Location type Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationType/$new"
        Then the "Location type" titled page is displayed

    Scenario: Location type creation (Detail)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationType"
        Then the "Location types" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "ID *" labelled text field on the main page
        And the user writes "Test@ID_Location type" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Test@NameOfLocation_Location type" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Test@Description of the Location_Location type" in the text field
        And the user selects the "Category" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Dock" in the dropdown-list field
        Then the value of the dropdown-list field is "Dock"

    Scenario: Location type creation (Stock)
        And the user selects the "Weight" labelled numeric field on the main page
        And the user writes "15.00" in the numeric field
        And the user selects the "Volume" labelled numeric field on the main page
        And the user writes "18.00" in the numeric field
        And the user selects the "Capacity" labelled numeric field on the main page
        And the user writes "50.00" in the numeric field
        And the user selects the "Accepted" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Quality control" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Rejected" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Accepted" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Location type update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationType"
        Then the "Location types" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the "Location type Bulk Storage" titled page is displayed
        Then the user searches for "Test@ID_Location type" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user writes "New_Test@NameOfLocation" in the text field
        And the user selects the "Category" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Customer" in the dropdown-list field
        Then the value of the dropdown-list field is "Customer"
        And the user selects the "Quality control" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Location type read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LocationType"
        Then the "Location types" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the "Location type Bulk Storage" titled page is displayed
        Then the user searches for "Test@ID_Location type" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "New_Test@NameOfLocation"
        And the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "Test@ID_Location type"
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Test@Description of the Location_Location type"
        And the user selects the "Category" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Customer"

    Scenario: Location type delete
        When the user searches for "Test@ID_Location type" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
