# This test aims to validate the consistency in the way that reference Items throughout the application is improved.
# The focus of the test will be validating whether we are showing the Item ID or the Item Name in correct way
# and both the Item ID and Item Name is always displayed by default. Below pages are impacted:
# - Purchase> Purchase order planning
# - Manufacturing > Bill of Materials
# - Stock> Stock value change
# - Stock> Stock change
# - Manufacturing> Production receipt inquiry
# - Manufacturing> Material issues inquiry
# - Manufacturing > Time tracking inquiry
# - Manufacturing> Time tracking line inquiry
# - Customer > Items tab
# - Customer > Item prices
# - Sales > Mass shipment creation – Advanced selection listing



@reference_data
@inventory
@manufacturing
@distribution

Feature: reference-data-flow-improve-item-reference-consistency

    Scenario: 01 - Purchase > Purchase order planning
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-supply-chain/PurchaseOrderPlanning"
        Then the "Purchase order planning" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Item ID" labelled column in the table field is displayed

    Scenario: 02 - Manufacturing > Bill of Materials
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-technical-data/BillOfMaterial"
        Then the "Bills of material" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Name" labelled column in the table field is displayed
        And the "Item name" labelled column in the table field is displayed
        And the "Site name" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Item ID" on the sidebar is unticked
        And the table column configuration with name "Site ID" on the sidebar is unticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "Name" labelled nested text field of row 1 in the table field is "AVG_FG"
        And the value of the "Item name" labelled nested text field of row 1 in the table field is "AVG_ITEM"
        And the user clicks the "Name" labelled nested field of row 1 in the table field
        And the "Bill of material AVG_FG" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "name" bound nested text field of the card 1 in the table field is "AVG_FG"
        And the value of the "item" bound nested reference field of the card 1 in the table field is "AVG_ITEM"
        And the "image" bound nested image field of the card 1 in the table field is undefined

    Scenario: 03 - Stock > Stock value change
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockValueChange"
        Then the "Stock value changes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Item name" labelled column in the table field is displayed
        And the "Site name" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Item ID" on the sidebar is unticked
        And the table column configuration with name "Site ID" on the sidebar is unticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "Number" labelled nested text field of row 1 in the table field is "SVC250003"
        And the value of the "Date" labelled nested date field of row 1 in the table field is a generated date with value "T"
        And the value of the "Number" labelled nested text field of row 3 in the table field is "SVC250001"
        And the value of the "Date" labelled nested date field of row 3 in the table field is "01/27/2025"
        And the user clicks the "Number" labelled nested field of row 2 in the table field
        And the "Stock value change SVC250002" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "number" bound nested text field of the card 2 in the table field is "SVC250002"
        And the value of the "stockTransactionStatus" bound nested label field of the card 2 in the table field is "Completed"
        And the value of the "site" bound nested reference field of the card 2 in the table field is "Stock Tranfer Uk Site 1"
        And the value of the "postedDate" bound nested date field of the card 2 in the table field is "01/27/2025"
        And the value of the "item" bound nested reference field of the card 2 in the table field is "Pressure transducer"

    Scenario: 04 - Stock > Stock change
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-stock/StockChange"
        Then the "Stock changes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Item ID" labelled column in the table field is displayed
        And the "Item name" labelled column in the table field is displayed
        And the "Site name" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Site ID" on the sidebar is unticked

    Scenario: 05 - Manufacturing > Production receipts inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/ProductionTrackingInquiry"
        Then the "Production receipts" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Item name" labelled column in the table field is displayed
        And the "Site name" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Site ID" on the sidebar is unticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "Number" labelled nested text field of row 1 in the table field is "WT24000038"
        And the value of the "Date" labelled nested date field of row 1 in the table field is "11/12/2024"
        And the value of the "Number" labelled nested text field of row 5 in the table field is "WT24000030"
        And the value of the "Date" labelled nested date field of row 5 in the table field is "05/17/2024"
        And the value of the "Item name" labelled nested reference field of row 1 in the table field is "FIFO_ITEM"
        And the user clicks the "Item name" labelled column of the table field
        And the value of the "Item name" labelled nested reference field of row 1 in the table field is "AVG_ITEM"
        And the value of the "Item ID" labelled nested reference field of row 3 in the table field is "FIFO_ITEM"
        And the user clicks the "Item ID" labelled column of the table field
        And the value of the "Item ID" labelled nested reference field of row 3 in the table field is "E2GA7NM02"
        And the user filters the "Item ID" labelled column in the table field with value "ITEM"
        And the value of the "Item ID" labelled nested text field of row 1 in the table field is "AVG_ITEM"
        And the user filters the "Item name" labelled column in the table field with value "FIFO_ITEM"
        And the value of the "Item name" labelled nested text field of row 1 in the table field is "FIFO_ITEM"
        And the user clicks the "Number" labelled nested field of row 1 in the table field
        And the "Production receipt WT24000018" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "number" bound nested text field of the card 1 in the table field is "WT24000018"
        And the value of the "stockTransactionStatus" bound nested label field of the card 1 in the table field is "Completed"
        And the value of the "workOrder" bound nested reference field of the card 1 in the table field is "TST_FIFO"
        And the value of the "effectiveDate" bound nested date field of the card 1 in the table field is "03/19/2024"
        And the value of the "itemName" labelled nested reference field of the card 1 in the table field is "FIFO_ITEM"
        And the value of the "site" bound nested reference field of the card 1 in the table field is "Swindon"
        And the user selects the "Item name" labelled reference field on the main page
        And the reference field is disabled
        And the reference field tunnel link is displayed
        And the user selects the "Items" labelled table field on the main page
        And the "Item name" labelled column in the table field is displayed
        And the "Item ID" labelled column in the table field is displayed

    Scenario: 06 - Manufacturing > Material issues inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/MaterialTrackingInquiry"
        Then the "Material issues" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Item name" labelled column in the table field is displayed
        And the "Site name" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Site ID" on the sidebar is unticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "Number" labelled nested text field of row 1 in the table field is "WT24000037"
        And the value of the "Date" labelled nested date field of row 1 in the table field is "11/12/2024"
        And the value of the "Number" labelled nested text field of row 2 in the table field is "WT24000035"
        And the value of the "Date" labelled nested date field of row 2 in the table field is "11/08/2024"
        And the value of the "Item name" labelled nested reference field of row 1 in the table field is "FIFO_ITEM"
        And the user clicks the "Item name" labelled column of the table field
        And the value of the "Item name" labelled nested reference field of row 1 in the table field is "AVG_ITEM"
        And the value of the "Item ID" labelled nested reference field of row 4 in the table field is "FIFO_ITEM"
        And the user clicks the "Item ID" labelled column of the table field
        And the value of the "Item ID" labelled nested reference field of row 4 in the table field is "E2GA7NM02"
        And the user filters the "Item ID" labelled column in the table field with value "ITEM"
        And the value of the "Item ID" labelled nested text field of row 1 in the table field is "AVG_ITEM"
        And the user filters the "Item name" labelled column in the table field with value "FIFO_ITEM"
        And the value of the "Item name" labelled nested text field of row 1 in the table field is "FIFO_ITEM"
        And the user clicks the "Number" labelled nested field of row 1 in the table field
        And the "Material issue WT24000014" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "number" bound nested text field of the card 1 in the table field is "WT24000014"
        And the value of the "stockTransactionStatus" bound nested label field of the card 1 in the table field is "Completed"
        And the value of the "workOrder" bound nested reference field of the card 1 in the table field is "TST_FIFO"
        And the value of the "effectiveDate" bound nested date field of the card 1 in the table field is "03/19/2024"
        And the value of the "itemName" labelled nested reference field of the card 1 in the table field is "FIFO_ITEM"
        And the value of the "siteName" labelled nested reference field of the card 1 in the table field is "Swindon"
        And the user selects the "Item name" labelled reference field on the main page
        And the reference field is disabled
        And the reference field tunnel link is displayed
        And the user selects the "Components" labelled table field on the main page
        # And the "Item name" labelled column in the table field is displayed
        # atm appears as "Item" not "Item name"
        And the "Item ID" labelled column in the table field is displayed

    Scenario: 07 - Manufacturing > Time tracking inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTrackingInquiry"
        Then the "Time tracking inquiries" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Item name" labelled column in the table field is displayed
        And the "Site name" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Site ID" on the sidebar is unticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "Number" labelled nested text field of row 1 in the table field is "WT24000024"
        And the value of the "Date" labelled nested date field of row 1 in the table field is "05/17/2024"
        And the value of the "Number" labelled nested text field of row 7 in the table field is "WT24000018"
        And the value of the "Date" labelled nested date field of row 7 in the table field is "03/19/2024"
        And the value of the "Item name" labelled nested reference field of row 1 in the table field is "Pressure transducer"
        And the user clicks the "Item name" labelled column of the table field
        And the value of the "Item name" labelled nested reference field of row 1 in the table field is "AVG_ITEM"
        And the value of the "Item ID" labelled nested reference field of row 2 in the table field is "FIFO_ITEM"
        And the user clicks the "Item ID" labelled column of the table field
        And the value of the "Item ID" labelled nested reference field of row 2 in the table field is "E2GA7NM02"
        And the user filters the "Item ID" labelled column in the table field with value "ITEM"
        And the value of the "Item ID" labelled nested text field of row 1 in the table field is "AVG_ITEM"
        And the user filters the "Item name" labelled column in the table field with value "FIFO_ITEM"
        And the value of the "Item name" labelled nested text field of row 1 in the table field is "FIFO_ITEM"
        And the user clicks the "Number" labelled nested field of row 1 in the table field
        And the "Time tracking inquiry WT24000018" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "number" bound nested text field of the card 1 in the table field is "WT24000018"
        And the value of the "workOrder" bound nested reference field of the card 1 in the table field is "TST_FIFO"
        And the value of the "effectiveDate" bound nested date field of the card 1 in the table field is "03/19/2024"
        And the value of the "itemName" labelled nested reference field of the card 1 in the table field is "FIFO_ITEM"
        And the value of the "siteName" labelled nested reference field of the card 1 in the table field is "Swindon"
        And the user selects the "Item name" labelled reference field on the main page
        And the reference field is disabled
        And the reference field tunnel link is displayed
        And the user selects the "Operations" labelled table field on the main page
        And the "Resource name" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Resource ID" on the sidebar is unticked
        And the table column configuration with name "Resource type" on the sidebar is unticked

    Scenario: 08 - Manufacturing > Time tracking line inquiry
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTrackingLineInquiry"
        Then the "Time tracking line inquiry" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Time tracking" labelled column in the table field is displayed
        And the "Routing name" labelled column in the table field is displayed
        And the "Site name" labelled column in the table field is displayed
        And the "Work order" labelled column in the table field is displayed
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Routing ID" on the sidebar is unticked
        And the table column configuration with name "Site ID" on the sidebar is unticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "Time tracking" labelled nested text field of row 1 in the table field is "WT25000014"
        And the value of the "Time tracking" labelled nested text field of row 2 in the table field is "WT25000013"
        And the value of the "Time tracking" labelled nested text field of row 3 in the table field is "WT25000013"
        And the value of the "Resource type" labelled nested text field of row 1 in the table field is "Machine resource"
        And the user clicks the "Resource type" labelled column of the table field
        And the value of the "Resource type" labelled nested reference field of row 1 in the table field is ""
        And the user opens the filter of the "Resource Type" labelled column in the table field
        And the user filters the "Resource Type" labelled column in the table field with value "Labor"
        And the value of the "Resource Type" labelled nested reference field of row 1 in the table field is "labor"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTrackingLineInquiry"
        Then the "Time tracking line inquiry" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Time tracking" labelled nested field of row 1 in the table field
        And an info dialog appears on a full width modal
        And the dialog title is "Time tracking inquiry WT25000014" on a full width modal
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-manufacturing/TimeTrackingLineInquiry"
        Then the "Time tracking line inquiry" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Work order" labelled nested field of row 1 in the table field
        And the user presses Enter
        And the user waits 2 seconds
        And an info dialog appears on a full width modal
        And the dialog title is "Work order WO_PHANTOM_02" on a full width modal
