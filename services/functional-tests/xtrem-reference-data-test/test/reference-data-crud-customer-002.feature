# The goal is to Create, Update, Delete the Sage customer without using a Business entity
@reference_data
@distribution
Feature:  reference-data-crud-customer-002

    Scenario: Customer creation without business entity
        # General Tab Information
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user clicks the "Create" labelled multi action button on the navigation panel
        # GENERAL TAB SECTION #
        Then selects the "General" labelled navigation anchor on the main page
        When the user selects the "Active" labelled switch field on the main page
        Then the user turns the switch field "OFF"
        # Adding customer details
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Sage Pastel" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "FR123" in the text field
        And the user selects the "Country" labelled reference field on the main page
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        Then the value of the reference field is "France"
        And the user selects the "Tax ID" labelled text field on the main page
        And the user writes "FR12345670000" in the text field
        And the user selects the "SIRET" labelled text field on the main page
        And the user writes "31396612900816" in the text field
        #Save the details and confirm the error message
        And the user clicks the "Save" labelled business action button on the main page
        # And a toast containing text "Validation errors" is displayed
        Then a validation error message is displayed containing text
            """
            Validation errors

            Payment term: You need to select or enter a value.
            """


        # Addresses- adding ship-to-address
        When selects the "Address" labelled navigation anchor on the main page
        # Call pod collection add new item
        Then the user selects the "addresses" bound pod collection field on the main page
        And the user clicks the "Add address" button of the selected pod collection field
        And the user selects the "Active" labelled switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Primary address" labelled checkbox field on the sidebar
        And the user clicks in the checkbox field
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Chavanod" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "1 RUE CALLISTO" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "PARC ALTAIS" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Chavanod" in the text field
        When the user selects the "postcode" bound text field on the sidebar
        And the user writes "74650" in the text field
        And the user selects the "Country" labelled reference field on the sidebar
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        Then the value of the reference field is "France"

        #Information tab
        When selects the "Information" labelled navigation anchor on the sidebar
        And the user selects the "Ship-to address" labelled switch field on the sidebar
        And the user clicks in the switch field
        Then the switch field is set to "ON"
        And the user selects the "Active" labelled switch field on the sidebar
        Then the switch field is set to "OFF"
        Then the user turns the switch field "ON"
        And the user selects the "Primary ship-to address" labelled checkbox field on the sidebar
        Then the user clicks in the checkbox field
        # Adding delivery mode
        And the user selects the "Delivery mode" labelled reference field on the sidebar
        And the user writes "Road" in the reference field
        And the user selects "Road" in the reference field
        Then the user clicks the "OK" labelled business action button on the sidebar

        # New primary contact
        When selects the "Contacts" labelled navigation anchor on the main page
        And the user selects the "contacts" bound pod collection field on the main page
        And the user clicks the "Add contact" button of the selected pod collection field
        And the user selects the "Address" labelled reference field on the sidebar
        And the user writes "Chavanod" in the reference field
        And the user selects "Chavanod" in the reference field
        And the user selects the "Title" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Mr." in the dropdown-list field
        Then the value of the dropdown-list field is "Mr."
        And the user selects the "First name" labelled text field on the sidebar
        And the user writes "The" in the text field
        And the user selects the "Last name" labelled text field on the sidebar
        And the user writes "Dahu" in the text field
        And the user selects the "Role" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Main contact" in the dropdown-list field
        Then the value of the dropdown-list field is "Main contact"
        Then the user clicks the "OK" labelled business action button on the sidebar

        # Financial Tab
        When selects the "Financial" labelled navigation anchor on the main page
        Then the user selects the "Payment term" labelled reference field on the main page
        And the user writes "Net 45" in the reference field
        And the user selects "Net 45" in the reference field
        Then the user selects the "Posting class" labelled reference field on the main page
        And the user writes "Customers" in the reference field
        And the user selects "Customers" in the reference field
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Admin" in the reference field
        And the user selects "Admin" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

        # Add another address
        When selects the "Address" labelled navigation anchor on the main page
        When the user selects the "addresses" bound pod collection field on the main page
        Then the user clicks the "Add address" button of the selected pod collection field
        And the user selects the "Active" labelled switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Annecy le Vieux" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "6 avenue du Pre Felin" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "Zi des Glaisins" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Annecy le Vieux" in the text field
        When the user selects the "postcode" bound text field on the sidebar
        And the user writes "74944" in the text field
        And the user selects the "Country" labelled reference field on the sidebar
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        Then the value of the reference field is "France"
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Customer without business entity Update
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Sage Pastel" in the "name" labelled column header of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        And the user searches for "Sage Pastel" in the navigation panel
        And the user clicks the "first" navigation panel's row

        #Add new address
        Then selects the "Address" labelled navigation anchor on the main page
        And the user selects the "addresses" bound pod collection field on the main page
        And the user clicks the "Add address" button of the selected pod collection field
        Then the user selects the "Active" labelled switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Rumilly" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "rue des 3 chateaux" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Rumilly" in the text field
        When the user selects the "postcode" bound text field on the sidebar
        And the user writes "74150" in the text field
        And the user selects the "Country" labelled reference field on the sidebar
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        Then the value of the reference field is "France"

        When selects the "Information" labelled navigation anchor on the sidebar
        And the user selects the "Ship-to address" labelled switch field on the sidebar
        And the user clicks in the switch field
        And the switch field is set to "ON"
        And the user selects the "Active" labelled switch field on the sidebar
        Then the switch field is set to "OFF"
        And the user selects the "Delivery mode" labelled reference field on the sidebar
        And the user writes "Road" in the reference field
        And the user selects "Road" in the reference field
        And the user selects the "Working days" labelled multi dropdown field on the sidebar
        # Todo : fix this
        Then the value of the multi dropdown field is "Monday, Tuesday, Wednesday, Thursday, Friday"
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

        # Financial Tab
        When selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "billToLinkedAddress" bound pod field on the main page
        Then the user clicks the "Replace" action of the pod field
        And the user selects the "billToAddress" bound table field on a modal
        # When the user filters the "Name" labelled column in the table field with value "Chavanod"
        When the user selects the row with text "Chavanod" in the "name" bound column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Retail" in the reference field
        And the user selects "Retail" in the reference field

        # Items
        When selects the "Items" labelled navigation anchor on the main page
        And the user selects the "items" bound table field on the main page
        And the user clicks the "addItem" bound action of the table field
        Then the "Add item-customer" titled sidebar is displayed
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "DIS-9542" in the reference field
        And the user selects "Pressure transmitter" in the reference field
        Then the value of the reference field is "Pressure transmitter"
        And the user clicks the "OK" labelled business action button on the sidebar

        # Item prices
        When selects the "Item prices" labelled navigation anchor on the main page
        And the user selects the "itemPrices" bound table field on the main page
        And the user clicks the "addItemPriceLine" bound action of the table field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Delete Customer without a business entity and the new created business entity
        #Delete Customer
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Sage Pastel" in the "name" labelled column header of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed

    #Delete business entity
    Scenario: Delete the new created business entity
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/BusinessEntity"
        Then the "Business entities" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field

        When the user searches for "Sage Pastel" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
