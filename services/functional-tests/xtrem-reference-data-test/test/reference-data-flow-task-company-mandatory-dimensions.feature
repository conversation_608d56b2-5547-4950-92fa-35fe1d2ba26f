# Goals are to verify that;
# - when the task attribute is set to mandatory on the company, while the project is not, an information message
#   is displayed'
# - when the task attribute is mandatory on the company and the project is not, then when document is created,
#   the control is triggered only if the project is filled in as well.

@finance
@reference_data
@distribution
Feature: reference-data-flow-task-company-mandatory-dimensions

    Scenario: 01 - Set Task mandatory on company
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "500" in the navigation panel
        And the user clicks the record with the text "UK Limited" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And selects the "Dimensions" labelled navigation anchor on the main page
        And the user selects the "Required dimensions" labelled table field on the main page
        And the user clicks the "Mandatory dimension and attributes" labelled header action button of the table field
        And the user selects the "Required dimensions" labelled table field on a modal
        And the user selects the row with text "Task" in the "Name" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "OK" button of the Custom dialog
        And the user clicks the "Apply" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        And a warning toast containing text "The Task is required when you enter a Project." is displayed
        And a toast containing text "Record updated" is displayed


    Scenario: 02 - Create sales order and define ONLY the project attribute
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Swindon" in the reference field
        And the user selects "Swindon" in the reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Distributor" in the reference field
        And the user selects "Distributor" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure transducer" in the reference field
        And the user selects "Pressure transducer" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.34" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 1 seconds
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        Then the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed
        Then a toast containing text "You need to select the Company dimension [Task] for this item: Pressure transducer on document: Unsaved ." is displayed
        And the user dismisses all the toasts
        And the user selects the "Number" labelled text field on the main page
        Then the user stores the value of the text field with the key "[ENV_RDOC_NUM01]"


    Scenario: 03 - Update required dimensions on company
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "500" in the navigation panel
        And the user clicks the record with the text "UK Limited" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And selects the "Dimensions" labelled navigation anchor on the main page
        And the user selects the "Required dimensions" labelled table field on the main page
        And the user clicks the "Mandatory dimension and attributes" labelled header action button of the table field
        And the user selects the "Required dimensions" labelled table field on a modal
        And the user selects the row with text "Project" in the "Name" labelled column header of the table field
        And the user ticks the main checkbox of the selected row in the table field
        And the user clicks the "OK" button of the Custom dialog
        And the user clicks the "Apply" button of the Confirm dialog
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed


    Scenario: 04 - Update sales order and define ONLY the project attribute
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "soldToCustomer__businessEntity__name" bound nested field of the selected row in the table field
        And the user searches for "[ENV_RDOC_NUM01]" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is "General Overhead"
        Then the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        Then a toast containing text "You need to select the Company dimension [Task] for this item: Pressure transducer on document: Unsaved ." is displayed
        And the user dismisses all the toasts
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user clears the reference field
        Then the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
        Then a toast containing text "You need to select the Company dimension [Project] for this item: Pressure transducer on document: Unsaved ." is displayed
        And the user dismisses all the toasts
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure transducer" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Task" labelled reference field on a modal
        And the user writes "Professional Development" in the reference field
        And the user selects "Professional Development" in the reference field
        Then the user clicks the "OK" labelled business action button on a modal
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed


    Scenario: 05 - Delete required dimensions on company
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "500" in the navigation panel
        And the user clicks the record with the text "UK Limited" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And selects the "Dimensions" labelled navigation anchor on the main page
        And the user selects the "Required dimensions" labelled table field on the main page
        And the user selects the row with text "Project" in the "Name" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user selects the row with text "Task" in the "Name" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed
