
# This test aims to validate the customer 360 view functionalities for the contact widget.

@reference_data
@distribution
Feature: reference-data-flow-customer-360view-contact

    Scenario: 01 - Verify that the address count value and the contact count value are correct
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the value of the basic widget field contains
            """
            4 Contacts
            3 Addresses
            """

    Scenario: 02 - Verify the contact dropdown list behavior
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the user selects "Contact 3 for address 3" in the "Select contact" filter dropdown of the contact-card widget field
        And the value of the basic widget field contains
            """
            Contact: Contact 3 for address 3
            Address: Address line 1 Address line 1 city3 dep3 pc3 France
            """
        And the user selects "Contact 1 for address 1" in the "Select contact" filter dropdown of the contact-card widget field
        And the value of the basic widget field contains
            """
            Contact:
            Contact 1 for address 1
            Primary contact
            Address:
            Address line 1 Address line 2 city dep pc France
            Primary address
            """

    Scenario: 03 - Verify the address dropdown list behavior
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the user clicks the "Switch to site view" toggle button in the header of the contact-card widget field
        And the user selects "Address line 1 Address line 1 city3 dep3 pc3 France" in the "Select address" filter dropdown of the contact-card widget field
        And the value of the basic widget field contains
            """
            Address name: Address 3
            Address: Address line 1 Address line 1 city3 dep3 pc3 France
            Contact: Contact 3 for address 3
            """
        And the user selects "Address line 1 Address line 2 city dep pc France" in the "Select address" filter dropdown of the contact-card widget field
        And the value of the basic widget field contains
            """
            Address name: Address 1
            Address: Address line 1 Address line 2 city dep pc France
            Primary address
            Contact: Contact 1 for address 1
            Primary contact
            """

    Scenario: 04 - Verify the toggle selection button behavior
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the user clicks the "Switch to site view" toggle button in the header of the contact-card widget field
        And the user clicks the "Switch to contact view" toggle button in the header of the contact-card widget field
        And the "Select address" filter dropdown of the contact-card widget field is hidden
        And the value of the "Select contact" filter dropdown of the contact-card widget field is "Contact 1 for address 1"
        And the value of the basic widget field contains
            """
            Contact:
            Contact 1 for address 1
            Primary contact
            Address:
            Address line 1 Address line 2 city dep pc France
            Primary address
            """
        And the user clicks the "Switch to site view" toggle button in the header of the contact-card widget field
        And the "Select contact" filter dropdown of the contact-card widget field is displayed
        And the value of the "Select address" filter dropdown of the contact-card widget field is "Address line 1Address line 2citydeppcFrance"
        And the value of the basic widget field contains
            """
            Address name:
            Address 1
            Address:
            Address line 1 Address line 2 city dep pc France
            Primary address
            Contact:
            Contact 1 for address 1
            Primary contact
            """

    Scenario: 05 - Verify the contact dropdown list behavior inside address toggle option
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the user clicks the "Switch to site view" toggle button in the header of the contact-card widget field
        And the user selects "Address line 1 Address line 2 city2 dep2 pc2 France" in the "Select address" filter dropdown of the contact-card widget field
        And the value of the "Select contact" filter dropdown of the contact-card widget field is "Contact 2 for address 2"
        And the value of the basic widget field contains
            """
            Address name: Address 2
            Address: Address line 1 Address line 2 city2 dep2 pc2 France
            Contact: Contact 2 for address 2
            """
        And the user selects "family contact" in the "Select contact" filter dropdown of the contact-card widget field
        And the value of the basic widget field contains
            """
            Address name: Address 2
            Address: Address line 1 Address line 2 city2 dep2 pc2 France
            Contact: family contact
            """

    Scenario: 06 - Verify the "See contacts" button
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the user clicks the "See contacts" button of the basic widget field
        And the user waits 3 seconds
        And the "Contacts" labelled navigation anchor is selected

    Scenario: 07 - Verify the "See addresses" button
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the user clicks the "See addresses" button of the basic widget field
        And the user waits 3 seconds
        And the "Address" labelled navigation anchor is selected

    Scenario: 08 - Verify the top kebab "Refresh" button works and the primary contact is displayed by default in the widget after refresh
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Automation Contact Widget" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Automation Contact Widget" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed
        And the "customerContacts" titled widget in the dashboard editor is displayed
        And the user selects the "customerContacts" titled contact-card widget field in the dashboard
        And the user selects "Contact 3 for address 3" in the "Select contact" filter dropdown of the contact-card widget field
        And the value of the "Select contact" filter dropdown of the contact-card widget field is "Contact 3 for address 3"
        And the user clicks the "Refresh" more actions button in the header of the basic widget field
        And the value of the "Select contact" filter dropdown of the contact-card widget field is "Contact 1 for address 1"
