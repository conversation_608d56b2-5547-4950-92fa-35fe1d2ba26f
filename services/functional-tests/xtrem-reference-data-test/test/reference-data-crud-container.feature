# Create update and delete a container


@reference_data
Feature: reference-data-crud-container

    Scenario: Display Container Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Container/$new"
        Then the "Container" titled page is displayed

    Scenario: Container creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Container"
        Then the "Containers" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "ON"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Pallet-01" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "0001" in the text field
        # Dropdown list
        And the user selects the "Container type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Pallet" in the dropdown-list field
        Then the value of the dropdown-list field is "Pallet"
        ## Selecting the Sequence number using a reference field ##
        When the user selects the "sequenceNumber" labelled reference field on the main page
        And the user writes "Miscellaneous stock issue" in the reference field
        And the user selects "Miscellaneous stock issue" in the reference field
        And the user selects the "Required storage capacity" labelled numeric field on the main page
        And the user writes "1000" in the numeric field
        And the user selects the "Label format" labelled text field on the main page
        And the user writes "FR" in the text field
        And the user selects the "Single item" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Single lot" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Storage capacity" labelled numeric field on the main page
        And the user writes "78990" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
    # Then a toast containing text "Record created" is displayed

    Scenario: Container update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Container"
        Then the "Containers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        Then the "Container 2-Way pallet" titled page is displayed
        When the user searches for "Pallet-01" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Pallet-02" in the text field
        And the user selects the "Container type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Other" in the dropdown-list field
        Then the value of the dropdown-list field is "Other"
        ## Selecting the Sequence number using a reference field ##
        When the user selects the "sequenceNumber " labelled reference field on the main page
        And the user clears the reference field
        And the user writes "Miscellaneous stock receipt" in the reference field
        And the user selects "Miscellaneous stock receipt" in the reference field
        And the user selects the "Single item" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Storage capacity" labelled numeric field on the main page
        And the user writes "98990" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Container read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Container"
        Then the "Containers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        Then the "Container 2-Way pallet" titled page is displayed
        When the user searches for "Pallet-02" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Pallet-02"
        And the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "0001"
        And the user selects the "Container type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Other" in the dropdown-list field
        And the user selects the "Internal" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "Required storage capacity" labelled numeric field on the main page
        Then the value of the numeric field is "1,000"
        And the user selects the "Label format" labelled text field on the main page
        Then the value of the text field is "FR"
        And the user selects the "Single item" labelled checkbox field on the main page
        Then the value of the checkbox field is "false"
        And the user selects the "Single lot" labelled checkbox field on the main page
        Then the value of the checkbox field is "true"
        And the user selects the "Storage capacity" labelled numeric field on the main page
        Then the value of the numeric field is "98,990"

    Scenario: Container delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Container"
        Then the "Containers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        Then the "Container 2-Way pallet" titled page is displayed
        When the user searches for "Pallet-02" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
