
# This test aims to validate the Customer 360 view functionality for Customer header page.
# Focus on verifying header page Single status, Image, Switch button and Service option.

@reference_data
@distribution
Feature: reference-data-flow-customer-360view-header

    Scenario: 01 - Verify that status label, customer name and image area are displayed
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Menu Action Automation" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        Then the "Customer Menu Action Automation" titled page is displayed
        Then the "displayStatus" bound label field on the main page is displayed
        And the user selects the "image" bound image field on the main page
        And the "image" bound image field on the main page is displayed

    Scenario: 02 - Verify that clicking the 360 view switch button redirects the user from the customer page to the 360 view page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Menu Action Automation" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Menu Action Automation" titled page is displayed
        And the user clicks the 360 view switch in the header
        And the "Customer 360 view" titled dashboard is displayed

    Scenario: 03 - Verify that the customer 360 view button is not accessible (deactivated) when the customer page is in dirty mode (update in progress)
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Menu Action Automation" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Menu Action Automation" titled page is displayed
        And the user selects the "Legal entity" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Corporation" in the dropdown-list field
        Then the value of the dropdown-list field is "Corporation"
        And the 360 view switch in the header is disabled

    Scenario: 04 - Verify that "360 view" switch button is visible for existing customers on Customer page when "Customer 360 view option" is active in service options
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-system/ServiceOptionState"
        Then the "Service options" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user filters the "Name" labelled column in the table field with value "customer360ViewOption"
        And the user selects the row 1 of the table field
        And the value of the "Active" labelled nested checkbox field of the selected row in the table field is "true"
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the main page
        And the user selects the row with text "Menu Action Automation" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Customer Menu Action Automation" titled page is displayed
        And the 360 view switch in the header is OFF

# @todo atm it is not visible, revisit after collecting more info XT-93594
# Scenario: 05 - Verify that "360 view" switch button is visible while creating new customer when "Customer 360 view option" is active in service options

# @todo step definition needed in order to be able to implement the following scenarios
# Scenario: 06 - Verify that "360 view" switch button is hidden when creating a new customer when "Customer 360 view option" is inactive in service options
# Scenario: 07 - Verify that 360 view switch button is hidden for existing customers when "Customer 360 view option" is inactive in service options
