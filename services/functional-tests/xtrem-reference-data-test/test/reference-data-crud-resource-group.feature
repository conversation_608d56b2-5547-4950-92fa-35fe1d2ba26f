# Confirmation that a resource group record can be created/updated and deleted
# Testing interaction with text fields/dropdown list/table field components
# Text fields allow for special characters as well as numerics

@reference_data
@manufacturing
Feature: reference-data-crud-resource-group

    Scenario: Resource group General creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/GroupResource"
        Then the "Resource groups" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Test name/001" in the text field
        And the user selects the "id" labelled text field on the main page
        And the user writes "Test_ID 0001" in the text field
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "Chem. Austin" in the reference field
        And the user selects "Chem. Austin" in the reference field
        And the user selects the "Capability level" labelled reference field on the main page
        And the user writes "Expert level of capability" in the reference field
        And the user selects "Expert level of capability" in the reference field
        And the user selects the "Resource type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Machine" in the dropdown-list field
        And the user selects the "Weekly shift" labelled reference field on the main page
        And the user writes "5 days 2 x 4 hours" in the reference field
        And the user selects "5 days 2 x 4 hours" in the reference field


    Scenario: Resource group Cost tab creation
        And selects the "Cost" labelled navigation anchor on the main page
        And the user selects the "resourceCostCategories" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "Standard cost" in the "Cost category" labelled nested reference field of the selected row in the table field
        And the user selects "Standard cost" in the "Cost category" labelled nested field of the selected row in the table field
        And the user writes "10.23" in the "Setup" labelled nested numeric field of the selected row in the table field
        And the user writes "55.00" in the "Run" labelled nested numeric field of the selected row in the table field
        And the user writes "Hour" in the "Cost unit" labelled nested reference field of the selected row in the table field
        And the user selects "Hour" in the "Cost unit" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed


    Scenario: Update group resource information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/GroupResource"
        Then the "Resource groups" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Test_ID 0001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "id" labelled text field on the main page
        And the user writes "Test ID updated" in the text field
        And the user selects the "name" labelled text field on the main page
        And the user writes "Test_name_updated" in the text field
        And the user clicks the "Save" labelled business action button on the main page
        And the user selects the "resources" bound table field on the main page
        And the user clicks the "addResource" bound action of the table field
        And the user selects the "name" labelled text field on the sidebar
        And the user writes "Detailed_test_name" in the text field
        And the user selects the "id" labelled text field on the sidebar
        And the user writes "Detailed_test_ID" in the text field
        And the user selects the "machineModel" labelled text field on the sidebar
        And the user writes "Model 1234" in the text field
        And the user selects the "machineSerialNumber" labelled text field on the sidebar
        And the user writes "Serial 1234" in the text field
        And the user clicks the "Save" labelled business action button on the sidebar
        Then a toast containing text "Record created" is displayed
        When selects the "Cost" labelled navigation anchor on the main page
        And the user selects the "resourceCostCategories" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user writes "15.00" in the "Setup" labelled nested numeric field of the selected row in the table field
        And the user writes "Day" in the "Cost unit" labelled nested reference field of the selected row in the table field
        And the user selects "Day" in the "Cost unit" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: Read group resource information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/GroupResource"
        Then the "Resource groups" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Test ID updated" in the navigation panel
        And the user clicks the "first" navigation panel's row
        ## Reading the General tab ##
        And the "Resource group Test_name_updated" titled page is displayed
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Test_name_updated"
        And the user selects the "Site" labelled reference field on the main page
        Then the value of the reference field is "Chem. Austin"
        And the user selects the "Weekly shift" labelled reference field on the main page
        Then the value of the reference field is "5 days 2 x 4 hours"
        And the user selects the "Efficiency" labelled numeric field on the main page
        Then the value of the numeric field is "100.00"
        And the user selects the "weeklyDetails" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "day" bound nested text field of the selected row in the table field is "Monday"
        And the value of the "dailyShift" labelled nested text field of the selected row in the table field is "QA_DUAL_SHIFT_EIGHT_HOURS"
        And the user selects the "resources" bound table field on the main page
        And the value of the "id" bound nested text field of the selected row in the table field is "Detailed_test_ID"
        ## Reading the Cost tab ##
        And selects the "Cost" labelled navigation anchor on the main page
        And the user selects the "resourceCostCategories" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "setup" labelled nested numeric field of the selected row in the table field is "$ 15.00"
        And the value of the "run" labelled nested numeric field of the selected row in the table field is "$ 55.00"


    Scenario: Delete the associated machine resource
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/MachineResource"
        Then the "Machine resources" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Detailed_test_ID" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog


    Scenario: Delete the main group resource
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/GroupResource"
        Then the "Resource groups" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        Then the user searches for "Test ID updated" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
