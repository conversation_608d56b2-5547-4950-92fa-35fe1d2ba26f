# Goal is to test that a Company record can be created/updated and deleted
# Read is explicitly tested in the read scenario
@reference_data
@finance
@distribution
@inventory
@manufacturing
Feature: reference-data-crud-company

    Scenario: Company creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        # This should be Off since is the creation of a company
        Then the switch field is set to "ON"
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Sage cie" in the text field
        And the user selects the "ID" labelled text field on the main page
        And the user writes "US005" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Sage company" in the text field
        # Reference field
        And the user selects the "Legislation *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        # Reference field
        And the user selects the "Country *" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        # Reference field
        And the user selects the "Currency *" labelled reference field on the main page
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        And the user selects the "SIREN *" labelled text field on the main page
        And the user writes "*********" in the text field
        And the user selects the "NAF (APE) *" labelled text field on the main page
        And the user writes "77.74Z" in the text field
        And the user selects the "RCS *" labelled text field on the main page
        And the user writes "RCS Annecy A *********" in the text field
        # Dropdown list
        And the user selects the "Legal form *" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "SARL" in the dropdown-list field
        Then the value of the dropdown-list field is "SARL"
        # Company Addresses section
        When selects the "Addresses" labelled navigation anchor on the main page
        # Call pod collaction add new item
        And the user selects the "addresses" bound pod collection field on the main page
        And the user clicks the "Add address" button of the selected pod collection field
        And the user selects the "Active" labelled switch field on the sidebar
        Then the switch field is set to "ON"
        When the user selects the "Primary address" labelled checkbox field on the sidebar
        And the user clicks in the checkbox field
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Stock" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "Hollywood Blvd" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "2100" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Los Angeles" in the text field
        And the user selects the "region" bound text field on the sidebar
        And the user writes "Californie" in the text field
        ## Sidebar Reference field ##
        And the user selects the "Country" labelled reference field on the sidebar
        And the user writes "United States of America" in the reference field
        And the user selects "United States of America" in the reference field
        Then the value of the reference field is "United States of America"
        When the user selects the "postcode" bound text field on the sidebar
        And the user writes "90001" in the text field
        And the user selects the "Phone number" labelled text field on the sidebar
        And the user writes "0102030405" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar
        # Company Dimensions section
        And selects the "Dimensions" labelled navigation anchor on the main page
        When the user selects the "Default dimension rules" labelled table field on the main page
        And the user clicks the "Add line" labelled header action button of the table field
        And the user selects the row 1 of the table field
        And the user clicks the "Document and origin" labelled nested field of the selected row in the table field
        And the user selects "Manufacturing direct" in the "Document and origin" labelled nested field of the selected row in the table field
        And the user clicks the "Project" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Project" labelled nested field of the selected row in the table field
        And the user clicks the "Department" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Department" labelled nested field of the selected row in the table field
        And the user clicks the "Channel" labelled nested field of the selected row in the table field
        And the user selects "Site" in the "Channel" labelled nested field of the selected row in the table field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Company update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "US005" in the navigation panel
        And the user clicks the record with the text "Sage cie" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Sage" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Sage XtreeM" in the text field
        And selects the "Addresses" labelled navigation anchor on the main page
        #Pod field
        And the user selects the "addresses" bound pod collection field on the main page
        And the user selects the "Stock" labelled pod collection item of the selected pod collection field
        And the user clicks the "Edit address" action of the selected pod collection item
        ## Sidebar section ##
        And the user selects the "Active" labelled switch field on the sidebar
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the sidebar
        And the user writes "Sage Lyon" in the text field
        And the user selects the "Address line 1" labelled text field on the sidebar
        And the user writes "1 Rue de Lyon" in the text field
        And the user selects the "Address line 2" labelled text field on the sidebar
        And the user writes "1er étage" in the text field
        And the user selects the "City" labelled text field on the sidebar
        And the user writes "Limonest" in the text field
        ## Reference field ##
        When the user selects the "region" bound text field on the sidebar
        And the user writes "Haute-savoie" in the text field
        And the user selects the "postcode" bound text field on the sidebar
        And the user writes "74650" in the text field
        And the user selects the "Phone number" labelled text field on the sidebar
        And the user writes "0687988765" in the text field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Company read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        # Accessing navigation panel
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "US005" in the navigation panel
        And the user clicks the record with the text "Sage" in the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        Then the switch field is set to "ON"
        When the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Sage"
        When the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "US005"
        When the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Sage XtreeM"
        When the user selects the "Legislation *" labelled reference field on the main page
        Then the value of the reference field is "France"
        When the user selects the "Country" labelled reference field on the main page
        Then the value of the reference field is "France"
        When the user selects the "Currency *" labelled reference field on the main page
        Then the value of the reference field is "Euro"
        When the user selects the "SIREN *" labelled text field on the main page
        Then the value of the text field is "*********"
        When the user selects the "NAF (APE) *" labelled text field on the main page
        Then the value of the text field is "77.74Z"
        When the user selects the "RCS *" labelled text field on the main page
        Then the value of the text field is "RCS Annecy A *********"
        #Read the address tab
        When selects the "Addresses" labelled navigation anchor on the main page
        Then the value of the "concatenatedAddressWithoutName" bound nested text area field of the selected pod collection item is "1 Rue de Lyon 1er étage Limonest Haute-savoie 74650 United States of America"

    Scenario: Company delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Company"
        Then the "Companies" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "US005" in the navigation panel
        And the user clicks the record with the text "Sage" in the navigation panel
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Sage"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
