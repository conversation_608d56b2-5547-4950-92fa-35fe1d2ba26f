# Create update and delete an item

@reference_data
Feature: reference-data-crud-item

    Scenario: Create a new Item - COMP-A
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed

        When the user clicks the "Create" labelled business action button on the navigation panel

        And the user selects the "Name" labelled text field on the main page
        And the user writes "COMP-A" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "0001" in the text field
        And the user selects the "category" bound reference field on the main page
        And the user writes "Other" in the reference field
        And the user selects "Other" in the reference field
        Then the value of the reference field is "Other"
        When the user selects the "Type" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Good" in the dropdown-list field
        Then the value of the dropdown-list field is "Good"
        And the user selects the "Purchased" labelled switch field on the main page
        Then the user turns the switch field "ON"
        And the user selects the "Manufactured" labelled switch field on the main page
        Then the user turns the switch field "ON"
        And the user selects the "Sold" labelled switch field on the main page
        Then the user turns the switch field "ON"

        # INFORMATION TAB SECTION #
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "Composant A" in the text field
        And the user selects the "status" bound dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Active" in the dropdown-list field
        And the value of the dropdown-list field is "Active"
        And the user selects the "GTIN-13" labelled text field on the main page
        And the user writes "1234567890123" in the text field

        # Stock section
        When selects the "Management" labelled navigation anchor on the main page
        When the user selects the "Lot management" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Lot" in the dropdown-list field
        Then the value of the dropdown-list field is "Lot"
        ## reference field ##
        And the user selects the "Lot sequence number" labelled reference field on the main page
        And the user writes "Material lot number" in the reference field
        And the user selects "Material lot number" in the reference field
        Then the value of the reference field is "Material lot number"
        # UNITS TAB SECTION #
        And selects the "Units" labelled navigation anchor on the main page
        ## reference field ##
        When the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "Gram" in the reference field
        And the user selects "Gram" in the reference field
        Then the value of the reference field is "Gram"
        ## reference field ##
        When the user selects the "Purchase unit" labelled reference field on the main page
        And the user writes "Gram" in the reference field
        And the user selects "Gram" in the reference field
        Then the value of the reference field is "Gram"
        # "Stock unit conversion factor" value is forced for unit gram to 1

        ## reference field ##
        When the user selects the "Sales unit" labelled reference field on the main page
        And the user writes "Gram" in the reference field
        And the user selects "Gram" in the reference field
        Then the value of the reference field is "Gram"
        # Storage information section
        ## reference field ##
        When the user selects the "Weight unit" labelled reference field on the main page
        And the user writes "Kilogram" in the reference field
        And the user selects "Kilogram" in the reference field
        Then the value of the reference field is "Kilogram"
        And the user selects the "Weight" labelled numeric field on the main page
        And the user writes "12" in the numeric field
        ## reference field ##
        When the user selects the "Volume unit" labelled reference field on the main page
        And the user writes "Liter" in the reference field
        And the user selects "Liter" in the reference field
        Then the value of the reference field is "Liter"
        And the user selects the "Volume" labelled numeric field on the main page
        And the user writes "12" in the numeric field
        And the user selects the "Density" labelled numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "Capacity" labelled numeric field on the main page
        And the user writes "167" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record created" is displayed


    Scenario: Update an item - Add item sites
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "COMP-A" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        # SITES TAB SECTION OK
        And selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        And the user selects the "item" bound reference field on the sidebar
        Then the value of the reference field is "COMP-A"
        ## reference field ##
        When the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Chem. Chicago" in the reference field
        And the user selects "Chem. Chicago" in the reference field
        Then the value of the reference field is "Chem. Chicago"
        ## General tab##
        And the user selects the "Specific item tax group" labelled checkbox field on the sidebar
        And the user clicks in the checkbox field
        When the user selects the "Item tax group" labelled reference field on the sidebar
        And the user writes "Goods, standard VAT" in the reference field
        And the user selects "Goods, standard VAT" in the reference field
        ## Replenishment tab##
        And selects the "Replenishment" labelled navigation anchor on the sidebar
        And the user selects the "Replenishment method" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "By reorder point" in the dropdown-list field
        Then the value of the dropdown-list field is "By reorder point"
        And the user selects the "Preferred process" labelled dropdown-list field on the sidebar
        And the user clicks in the dropdown-list field
        And the user selects "Purchasing" in the dropdown-list field
        Then the value of the dropdown-list field is "Purchasing"
        And the user selects the "Production lead time" labelled numeric field on the sidebar
        And the user writes "8" in the numeric field
        And the user selects the "Purchase lead time" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And the user selects the "Safety stock" labelled numeric field on the sidebar
        And the user writes "500" in the numeric field
        And the user selects the "Reorder point" labelled numeric field on the sidebar
        And the user writes "200" in the numeric field
        And the user selects the "Economic order quantity" labelled numeric field on the sidebar
        And the user writes "14" in the numeric field
        And the user selects the "Batch quantity" labelled numeric field on the sidebar
        And the user writes "7" in the numeric field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed


    Scenario: Update item - Add item sales data
        # Sales BLOCK OK
        And selects the "Management" labelled navigation anchor on the main page
        And the user selects the "Minimum quantity" labelled numeric field on the main page
        And the user writes "12" in the numeric field
        And the user selects the "Maximum quantity" labelled numeric field on the main page
        And the user writes "12345" in the numeric field
        ## reference field ##
        When the user selects the "Currency" labelled reference field on the main page
        And the user writes "US Dollar" in the reference field
        And the user selects "US Dollar" in the reference field
        Then the value of the reference field is "US Dollar"
        And the user selects the "Base price" labelled numeric field on the main page
        And the user writes "35" in the numeric field
        And the user selects the "Minimum price" labelled numeric field on the main page
        And the user writes "27" in the numeric field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed


    Scenario: Update item - Add item financials
        # FINANCIAL BLOCK
        ## reference field ##
        And selects the "Financial" labelled navigation anchor on the main page
        When the user selects the "Posting class" labelled reference field on the main page
        And the user writes "Standard rate items and services" in the reference field
        And the user selects "Standard rate items and services" in the reference field
        When the user selects the "Item tax group" labelled reference field on the main page
        And the user writes "Goods, standard VAT" in the reference field
        And the user selects "Goods, standard VAT" in the reference field
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Admin" in the reference field
        And the user selects "Admin" in the reference field
        And the user selects the "Channel" labelled reference field on the main page
        And the user writes "Commercial" in the reference field
        And the user selects "Commercial" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast with text "Record updated" is displayed


    Scenario: Deleting an Item
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "COMP-A" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        # Delete the item
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
