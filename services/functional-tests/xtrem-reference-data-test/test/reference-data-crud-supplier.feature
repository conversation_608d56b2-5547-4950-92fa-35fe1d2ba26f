#The goal of this test is to verify the creation, update and deletion of a supplier

@reference_data
@distribution
Feature:  reference-data-crud-supplier

    Scenario: 01 - Supplier creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed

        When the user clicks the "Create from business entity" labelled multi action button on the navigation panel

        Given the user selects the "businessEntity" bound reference field on a modal
        Then the user writes "Chem. Irvine" in the reference field
        And the user selects "Chem. Irvine" in the reference field
        And the user clicks the "Next" labelled business action button on a modal

        And the user selects the "Payment term" labelled reference field on a modal
        And the user writes "Net 30" in the reference field
        And the user selects "Net 30" in the reference field

        And the user clicks the "next" bound business action button on a modal
        Then a toast containing text "Record updated" is displayed

    Scenario: 02 - Supplier update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "US002" in the navigation panel
        And the user clicks the "first" navigation panel's row
        # Financial Tab
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        And the user writes "Net 60" in the reference field
        And the user selects "Net 60" in the reference field
        And the user selects the "Department" labelled reference field on the main page
        And the user writes "Sales" in the reference field
        And the user selects "Sales" in the reference field
        # Certificates Tab
        And selects the "Certificates" labelled navigation anchor on the main page
        And the user selects the "certificates" bound table field on the main page
        And the user clicks the "addCertificate" bound action of the table field
        And the user selects the "certificateReference" labelled text field on the sidebar
        And the user writes "ISO TSTMKH Certificate" in the text field
        And the user selects the "standard" labelled reference field on the sidebar
        And the user writes "ISO" in the reference field
        And the user selects "ISO 9001" in the reference field
        And the user selects the "Original certification date" labelled date field on the sidebar
        And the user writes "03/30/2021" in the date field
        And the user selects the "Certification date" labelled date field on the sidebar
        And the user writes "05/30/2021" in the date field
        And the user selects the "Validity end date" labelled date field on the sidebar
        And the user writes "04/30/2024" in the date field
        And the user clicks the "ok" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed


    Scenario: 03 - Supplier read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "US002" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Chem. Irvine"
        And the user selects the "Tax ID" labelled text field on the main page
        Then the value of the text field is "965-85-6598"
        And selects the "Financial" labelled navigation anchor on the main page
        And the user selects the "Payment term" labelled reference field on the main page
        Then the value of the reference field is "Net 60"
        And selects the "Certificates" labelled navigation anchor on the main page
        And the user selects the "certificates" bound table field on the main page
        And the user selects the row 1 of the table field
        And the value of the "Valid until" labelled nested date field of the selected row in the table field is "2024-04-30"


    Scenario: Supplier Delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "BARRES" in the "Name" labelled column header of the table field
        When the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "US002" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
