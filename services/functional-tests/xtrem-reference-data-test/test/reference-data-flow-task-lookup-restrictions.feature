# Goals are to verify that;
# - when entering a task on a site, customer, supplier or sales document, the look up and authorized values are
#   restricted to the project previously set
# - when no project is set, then no task is available

@finance
@reference_data
@distribution
Feature: reference-data-flow-task-lookup-restrictions

    Scenario: 01 - Check for available tasks and the project they are restricted to
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-finance-data/Attribute"
        Then the "Attributes" titled page is displayed
        And the user selects the "Task" dropdown option in the navigation panel
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "300" in the "Name" labelled column header of the table field
        And the value of the "Restricted to name" labelled nested reference field of the selected row in the table field is "General Overhead"
        And the user stores the value of the "Name" labelled nested text field of the selected row in the table field with the key "[ENV_P10000_300]"
        And the user selects the row with text "Holidays" in the "Name" labelled column header of the table field
        And the value of the "Restricted to name" labelled nested reference field of the selected row in the table field is "General Overhead-Current"
        And the user stores the value of the "Name" labelled nested text field of the selected row in the table field with the key "[ENV_P10025_HOL]"
        And the user selects the row with text "Indirect" in the "Name" labelled column header of the table field
        And the value of the "Restricted to name" labelled nested reference field of the selected row in the table field is "General Overhead"
        And the user stores the value of the "Name" labelled nested text field of the selected row in the table field with the key "[ENV_P10000_IND]"
        And the user selects the row with text "Professional Development" in the "Name" labelled column header of the table field
        And the value of the "Restricted to name" labelled nested reference field of the selected row in the table field is "General Overhead"
        And the user stores the value of the "Name" labelled nested text field of the selected row in the table field with the key "[ENV_P10000_PDE]"
        And the user selects the row with text "Paid Time Off" in the "Name" labelled column header of the table field
        And the value of the "Restricted to name" labelled nested reference field of the selected row in the table field is "General Overhead-Current"
        And the user stores the value of the "Name" labelled nested text field of the selected row in the table field with the key "[ENV_P10025_PTO]"



    Scenario: 02 - Enter a task on a Site
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Site"
        Then the "Sites" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "700" in the navigation panel
        And the user clicks the record with the text "South Africa Headquarter & Warehouse" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And selects the "Financial" labelled navigation anchor on the main page
        # Project is empty, thus task is unavailable
        And the user selects the "Project" labelled reference field on the main page
        And the value of the reference field is ""
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is disabled
        # Project is populated, thus only relative project-restricted tasks are listed
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "300" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_300]"
        And the user selects the row with text "IND" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_IND]"
        And the user selects the row with text "PDE" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_PDE]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "HOL" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_HOL]"
        And the user selects the row with text "PTO" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_PTO]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Cancel" labelled business action button on the main page
        And the user clicks the "Discard" button of the Confirm dialog on the main page


    Scenario: 03 - Enter a task on a Customer
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Customer"
        Then the "Customers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "17001" in the navigation panel
        #And the user clicks the record with the text "Adcock Ingram" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And selects the "Financial" labelled navigation anchor on the main page
        # Project is empty, thus task is unavailable
        And the user selects the "Project" labelled reference field on the main page
        And the value of the reference field is ""
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is disabled
        # Project is populated, thus only relative project-restricted tasks are listed
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "300" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_300]"
        And the user selects the row with text "IND" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_IND]"
        And the user selects the row with text "PDE" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_PDE]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "HOL" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_HOL]"
        And the user selects the row with text "PTO" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_PTO]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Cancel" labelled business action button on the main page
        And the user clicks the "Discard" button of the Confirm dialog on the main page


    Scenario: 04 - Enter a task on a Suppplier
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Supplier"
        Then the "Suppliers" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user searches for "20071" in the navigation panel
        And the user clicks the record with the text "Microns Consulting" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And selects the "Financial" labelled navigation anchor on the main page
        # Project is empty, thus task is unavailable
        And the user selects the "Project" labelled reference field on the main page
        And the value of the reference field is ""
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is disabled
        # Project is populated, thus only relative project-restricted tasks are listed
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "300" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_300]"
        And the user selects the row with text "IND" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_IND]"
        And the user selects the row with text "PDE" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_PDE]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Project" labelled reference field on the main page
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Task" labelled reference field on the main page
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "HOL" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_HOL]"
        And the user selects the row with text "PTO" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_PTO]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Cancel" labelled business action button on the main page
        And the user clicks the "Discard" button of the Confirm dialog on the main page


    Scenario: 05 - Enter a task on a Sales order
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-sales/SalesOrder"
        Then the "Sales orders" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Site" labelled reference field on the main page
        And the user writes "South Africa Headquarter & Warehouse" in the reference field
        And the user selects "South Africa Headquarter & Warehouse" in the reference field
        And the user selects the "Sold-to customer " labelled reference field on the main page
        And the user writes "Adcock Ingram" in the reference field
        And the user selects "Adcock Ingram" in the reference field
        And the user selects the "lines" bound table field on the main page
        And the user adds a new table row to the table field using the sidebar
        And the user selects the "Item" labelled reference field on the sidebar
        And the user writes "Pressure sensor" in the reference field
        And the user selects "Pressure sensor" in the reference field
        And the user selects the "Quantity in sales unit" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field
        And selects the "Price" labelled navigation anchor on the sidebar
        And the user selects the "Gross price" labelled numeric field on the sidebar
        And the user writes "12.34" in the numeric field
        And the user clicks the "Apply" button of the dialog on the sidebar
        And the user waits 1 seconds
        And the user selects the "lines" bound table field on the main page
        And the user selects the row with text "Pressure sensor" in the "Item" labelled column header of the table field
        And the user clicks the "Dimensions" dropdown action of the selected row of the table field
        # Project is empty, thus task is unavailable
        And the user selects the "Project" labelled reference field on a modal
        And the value of the reference field is ""
        And the user selects the "Task" labelled reference field on a modal
        And the reference field is disabled
        # Project is populated, thus only relative project-restricted tasks are listed
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead" in the reference field
        And the user selects "General Overhead" in the reference field
        And the user selects the "Task" labelled reference field on a modal
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "300" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_300]"
        And the user selects the row with text "IND" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_IND]"
        And the user selects the row with text "PDE" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10000_PDE]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user selects the "Project" labelled reference field on a modal
        And the user writes "General Overhead-Current" in the reference field
        And the user selects "General Overhead-Current" in the reference field
        And the user selects the "Task" labelled reference field on a modal
        And the reference field is enabled
        And the user clicks the lookup button of the reference field
        And the user selects the "task" bound table field on a modal
        And the table field is not empty
        And the user selects the row with text "HOL" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_HOL]"
        And the user selects the row with text "PTO" in the "ID" labelled column header of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "[ENV_P10025_PTO]"
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the user clicks the "Cancel" labelled business action button on a modal
        And the user clicks the "Cancel" labelled business action button on the main page
        And the user clicks the "Discard" button of the Confirm dialog on the main page
