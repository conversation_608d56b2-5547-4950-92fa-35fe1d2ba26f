# The goal of this test is to create, read, update, delete labor resource

@reference_data
@manufacturing
Feature: reference-data-crud-labor-resource

    Scenario: Labor resource creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LaborResource"
        Then the "Labor resources" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        And the user clicks in the switch field
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "OFF"
        ## Populating the Name, ID and Description text fields ##
        And the user selects the "name" labelled text field on the main page
        And the user writes "QC Test Labor Resource" in the text field
        And the user selects the "id" labelled text field on the main page
        And the user writes "Labor resource 001" in the text field
        And the user selects the "description" labelled text field on the main page
        And the user writes "Test labor description" in the text field
        ## Selecting the site using a reference field ##
        And the user selects the "site" labelled reference field on the main page
        And the user writes "Chem. Irvine" in the reference field
        And the user selects "Chem. Irvine" in the reference field
        ## Move the focus out of the active from date ##
        And the user selects the "activeFrom" labelled date field on the main page
        And the user blurs the date field
        ## Move the focus out of the active to date ##
        And the user selects the "activeTo" labelled date field on the main page
        And the user blurs the date field
        ## Selecting the Resource group using a reference field ##
        And the user selects the "Resource group" labelled reference field on the main page
        And the user writes "Chemical Operators" in the reference field
        And the user selects "Chemical Operators" in the reference field
        ## Selecting the Weekly shift using a reference field ##
        And the user selects the "weeklyShift" labelled reference field on the main page
        And the user writes "4 days mixed hours" in the reference field
        And the user selects "4 days mixed hours" in the reference field
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record created" is displayed

    Scenario: Update labor resource information on the main page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LaborResource"
        Then the "Labor resources" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC Test Labor Resource" in the "Name" labelled column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        ## Updating the General tab ##
        And the user selects the "description" labelled text field on the main page
        And the user writes "Description labor updated" in the text field
        And the user selects the "efficiency" labelled numeric field on the main page
        And the user writes "90" in the numeric field
        ## Updating the Capabilities tab ##
        And selects the "Capabilities" labelled navigation anchor on the main page
        And the user selects the "capabilities" bound table field on the main page
        And the user clicks the "addCapability" bound action of the table field
        And the user selects the "id" labelled text field on the sidebar
        And the user writes "New capability ID" in the text field
        And the user selects the "name" labelled text field on the sidebar
        And the user writes "New capability name" in the text field
        And the user selects the "capabilityLevel" labelled reference field on the sidebar
        And the user writes "Expert level of capability" in the reference field
        And the user selects "Expert level of capability" in the reference field
        And the user clicks the "OK" labelled business action button on the sidebar
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Read labor resource information on the main page
        ## After creating a record the navigation panel is collapsed so need to reopen the page ##
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LaborResource"
        Then the "Labor resources" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC Test Labor Resource" in the "Name" labelled column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the "Labor resource QC Test Labor Resource" titled page is displayed
        ## Reading the General tab ##
        And the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "OFF"
        And the user selects the "name" labelled text field on the main page
        Then the value of the text field is "QC Test Labor Resource"
        And the user selects the "site" labelled reference field on the main page
        Then the value of the reference field is "Chem. Irvine"
        And the user selects the "weeklyShift" labelled reference field on the main page
        Then the value of the reference field is "4 days mixed hours"
        And the user selects the "weeklyDetails" bound table field on the main page
        ## Reading the Capabilities tab ##
        And selects the "Capabilities" labelled navigation anchor on the main page
        And the user selects the "capabilities" bound table field on the main page
        And the user selects the row with text "New capability ID" in the "id" bound column header of the table field
        And the value of the "id" bound nested text field of the selected row in the table field is "New capability ID"
        ## Reading the Cost tab ##
        And selects the "Cost" labelled navigation anchor on the main page
        And the user selects the "resourceCostCategories" bound table field on the main page

    Scenario: Delete a labor resource
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LaborResource"
        Then the "Labor resources" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "QC Test Labor Resource" in the "Name" labelled column header of the table field
        And the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QC Test Labor Resource" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
