# Goal is to test that a Sequence number record can be created/updated and deleted
# Read is implicitly tested within the Create/Update/Delete
@reference_data
@inventory
@manufacturing
@distribution
Feature: reference-data-crud-sequence-number


        Scenario: Sequence number creation
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/SequenceNumber"
                Then the "Sequence numbers" titled page is displayed
                When the user clicks the "create" labelled business action button on the main page
                And the user selects the "ID" labelled text field on the main page
                And the user writes "QCTID" in the text field
                And the user selects the "Name" labelled text field on the main page
                And the user writes "QC TEST 123456789-NAME" in the text field

                And the user selects the "Definition level" labelled dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "Application" in the dropdown-list field
                And the user selects the "Reset frequency" labelled dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "Monthly" in the dropdown-list field
                And the user selects the "Type" labelled dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "Alphanumeric" in the dropdown-list field
                And the user selects the "Chronological control" labelled checkbox field on the main page
                And the user clicks in the checkbox field

                # Add components to sequence number
                And the user selects the "components" bound table field on the main page
                # Note how to access a drop down menu inside a table for the type column
                And the user clicks the "addComponent" bound action of the table field
                And the user selects the row 1 of the table field
                And the user clicks the "Type" labelled nested field of the selected row in the table field
                And the user selects "Constant" in the "Type" labelled nested field of the selected row in the table field
                And the user writes "TT" in the "Constant" labelled nested reference field of the selected row in the table field

                And the user clicks the "addComponent" bound action of the table field
                And the user selects the row 1 of the table field
                And the user clicks the "Type" labelled nested field of the selected row in the table field
                And the user selects "Year" in the "Type" labelled nested field of the selected row in the table field
                And the user writes "4" in the "Length" labelled nested reference field of the selected row in the table field

                And the user clicks the "addComponent" bound action of the table field
                And the user selects the row 1 of the table field
                And the user clicks the "Type" labelled nested field of the selected row in the table field
                And the user selects "Month" in the "Type" labelled nested field of the selected row in the table field
                And the user writes "2" in the "Length" labelled nested reference field of the selected row in the table field

                And the user clicks the "addComponent" bound action of the table field
                And the user selects the row 1 of the table field
                And the user clicks the "Type" labelled nested field of the selected row in the table field
                And the user selects "Sequence number" in the "Type" labelled nested field of the selected row in the table field
                And the user writes "4" in the "Length" labelled nested reference field of the selected row in the table field

                And the user clicks the "save" bound business action button on the main page
                Then the value of the toast is "Record created"


        Scenario: Update a Sequence number
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/SequenceNumber"
                Then the "Sequence numbers" titled page is displayed
                When the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row 1 of the table field
                And the user clicks the "id" bound nested field of the selected row in the table field
                And the user searches for "QCTID" in the navigation panel
                And the user clicks the record with the text "QC TEST 123456789-NAME" in the navigation panel
                Then the "Sequence number QC TEST 123456789-NAME" titled page is displayed

                When the user selects the "Reset frequency" labelled dropdown-list field on the main page
                And the user clicks in the dropdown-list field
                And the user selects "Yearly" in the dropdown-list field
                And the user clicks the "save" bound business action button on the main page
                Then the value of the toast is "Record updated"

                # Refresh and read that the field that was updated
                When the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/SequenceNumber"
                Then the "Sequence numbers" titled page is displayed
                And the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row 1 of the table field
                And the user clicks the "id" bound nested field of the selected row in the table field
                And the user searches for "QCTID" in the navigation panel
                And the user clicks the record with the text "QC TEST 123456789-NAME" in the navigation panel
                And the user selects the "Reset frequency" labelled dropdown-list field on the main page
                Then the value of the dropdown-list field is "Yearly"



        Scenario: Delete newly created Sequence number
                Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/SequenceNumber"
                Then the "Sequence numbers" titled page is displayed
                When the user selects the "$navigationPanel" bound table field on the main page
                And the user selects the row 1 of the table field
                And the user clicks the "id" bound nested field of the selected row in the table field
                And the user searches for "QC TEST 123456789-NAME" in the navigation panel
                And the user clicks the record with the text "QC TEST 123456789-NAME" in the navigation panel
                # Confirmation that we are deleting the correct line item
                And the user selects the "Name" labelled text field on the main page
                Then the value of the text field is "QC TEST 123456789-NAME"
                When the user selects the "ID" labelled text field on the main page
                Then the value of the text field is "QCTID"
                # Delete using the header more actions kebab menu
                When the user clicks the "Delete" labelled more actions button in the header
                And the user clicks the "Delete" button of the Custom dialog
                Then the value of the toast is "Record deleted"
