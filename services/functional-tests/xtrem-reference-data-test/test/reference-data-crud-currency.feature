#This test can only be executed with sage.
#The goal of this test is to check the CRUD for currency.

@reference_data
@finance
Feature: reference-data-crud-currency

    Scenario: Create currency
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        #Fill texts fields
        And the user selects the "Name *" labelled text field on the main page
        And the user writes "Swis Franc" in the text field
        And the user selects the "iso4217" labelled text field on the main page
        And the user writes "CHF" in the text field
        And the user selects the "Symbol *" labelled text field on the main page
        And the user writes "Frr" in the text field
        And the user selects the "decimalDigits" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "rounding" bound numeric field on the main page
        And the user writes "0" in the numeric field
        #Click on Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record created" is displayed

    Scenario: Update currency
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "AUD" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Australian Dollar" titled page is displayed
        #Searching Creation
        When the user searches for "Swis Franc" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Modify Creation
        And the user selects the "Name *" labelled text field on the main page
        And the user writes "Swiss Franc" in the text field
        #Frozen properties : this line cannot be assigned a value
        And the user selects the "Symbol *" labelled text field on the main page
        And the user writes "Fr" in the text field
        And the user selects the "decimalDigits" bound numeric field on the main page
        And the user writes "2" in the numeric field
        And the user selects the "rounding" bound numeric field on the main page
        And the user writes "0" in the numeric field
        And the user presses Tab
        #Click on Save Crud Button
        And the user clicks the "Save" labelled business action button on the main page
        #Verify Creation
        Then a toast containing text "Record updated" is displayed

    Scenario: Create exchange rate
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "AUD" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Australian Dollar" titled page is displayed
        #Searching Creation
        When the user searches for "Swiss Franc" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Go to exchange rates
        And selects the "Exchange rates" labelled navigation anchor on the main page
        #Enter values
        And the user clicks the "addExchangeRate" bound header action button on the main page
        And the user selects the "Destination currency" labelled reference field on the sidebar
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        Then the value of the reference field is "Euro"
        And the user selects the "Rate date" labelled date field on the sidebar
        And the user writes "06/23/2022" in the date field
        And the user selects the "Rate" labelled numeric field on the sidebar
        And the user writes "0.9" in the numeric field
        #Accept
        And the user clicks the "OK" button of the Custom dialog on the sidebar

    Scenario: Second exchange rate
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "AUD" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Australian Dollar" titled page is displayed
        #Searching Creation
        And the user searches for "Swiss Franc" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Go to exchange rates
        And selects the "Exchange rates" labelled navigation anchor on the main page
        #Enter values
        And the user clicks the "addExchangeRate" bound header action button on the main page
        And the user selects the "Destination currency" labelled reference field on the sidebar
        And the user writes "" in the reference field
        And the user writes "EURO" in the reference field
        And the user selects "Euro" in the reference field
        Then the value of the reference field is "Euro"
        And the user selects the "Rate date" labelled date field on the sidebar
        And the user writes "06/23/2022" in the date field
        And the user selects the "Rate" labelled numeric field on the sidebar
        And the user writes "0.8" in the numeric field
        #Accept
        And the user clicks the "OK" button of the Custom dialog on the sidebar

    Scenario: Third exchange rate
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "EUR" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Euro" titled page is displayed
        #Go to exchange rates
        And selects the "Exchange rates" labelled navigation anchor on the main page
        #Enter values
        And the user clicks the "addExchangeRate" bound header action button on the main page
        And the user selects the "Destination currency" labelled reference field on the sidebar
        And the user writes "US Dollar" in the reference field
        And the user selects "US Dollar" in the reference field
        Then the value of the reference field is "US Dollar"
        And the user selects the "Rate date" labelled date field on the sidebar
        And the user writes "08/01/2023" in the date field
        And the user selects the "Rate" labelled numeric field on the sidebar
        And the user writes "1" in the numeric field
        #Accept
        And the user clicks the "OK" button of the Custom dialog on the sidebar


    Scenario: Currency read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "AUD" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Australian Dollar" titled page is displayed
        #Searching Creation
        When the user searches for "Swiss Franc" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name *" labelled text field on the main page
        Then the value of the text field is "Swiss Franc"
        And the user selects the "iso4217" labelled text field on the main page
        Then the value of the text field is "CHF"
        And the user selects the "Symbol *" labelled text field on the main page
        Then the value of the text field is "Fr"
        #Go to exchange rates
        And selects the "Exchange rates" labelled navigation anchor on the main page
        #Verify
        And the user selects the "exchangeRates" bound table field on the main page
        And the user selects the row 1 of the table field
        When the value of the "Currency" labelled nested text field of the selected row in the table field is "EUR"

    Scenario: Delete exchange rate
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "AUD" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Australian Dollar" titled page is displayed
        #Searching Creation
        When the user searches for "Swiss Franc" in the navigation panel
        And the user clicks the "first" navigation panel's row
        #Go to exchange ratess
        And selects the "Exchange rates" labelled navigation anchor on the main page
        #Delete
        And the user selects the "exchangeRates" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        #Confirming deletion of exchange rate in first dialog.
        And the user clicks the "Delete" button of the Confirm dialog
        #Confirming deletion of inverse rate in second dialog
        And the user clicks the "Delete" button of the Confirm dialog

    Scenario: Delete US Dollar exchange rate in Euro
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "EUR" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Euro" titled page is displayed
        #Go to exchange rates
        And selects the "Exchange rates" labelled navigation anchor on the main page
        #Go to exchange ratess
        And selects the "Exchange rates" labelled navigation anchor on the main page
        #Delete
        And the user selects the "exchangeRates" bound table field on the main page
        And the user selects the row 1 of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field
        #Confirming deletion of exchange rate in first dialog
        And the user clicks the "Delete" button of the Confirm dialog
        #Confirming deletion of inverse rate in second dialog
        And the user clicks the "Delete" button of the Confirm dialog

    #Turn off the isActive switch and save
    Scenario: turning off isActive switch
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "EUR" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Euro" titled page is displayed
        When the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "ON"
        Then the user turns the switch field "OFF"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: turning the isActive switch back on
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "EUR" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Euro" titled page is displayed
        When the user selects the "isActive" bound switch field on the main page
        Then the switch field is set to "OFF"
        Then the user turns the switch field "ON"
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Currency deletion
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Currency"
        Then the "Currencies" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        When the user selects the row with text "AUD" in the "ISO 4217" labelled column header of the table field
        And the user clicks the "ISO 4217" labelled nested field of the selected row in the table field
        Then the "Currency Australian Dollar" titled page is displayed
        #Searching Creation
        When the user searches for "Swiss Franc" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        #Verify Deletion.
        Then a toast containing text "Record deleted" is displayed
