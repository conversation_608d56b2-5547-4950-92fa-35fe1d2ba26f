# Create update and delete a country

@reference_data
Feature: reference-data-crud-country

    Scenario: Display Country Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Country/$new"
        Then the "Country" titled page is displayed

    Scenario: Country creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Country"
        Then the "Countries" titled page is displayed
        And the user clicks the "Create" labelled business action button on the navigation panel
        Then the "Country" titled page is displayed

        When the user selects the "ISO 3166-1 alpha-2" labelled text field on the main page
        And the user writes "JP" in the text field
        And the user selects the "ISO 3166-1 alpha-3" labelled text field on the main page
        And the user writes "JPN" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Japan" in the text field

        ## Reference field ##
        And the user selects the "Currency" labelled reference field on the main page
        And the user writes "Euro" in the reference field
        And the user selects "Euro" in the reference field
        Then the value of the reference field is "Euro"

        # Dropdown list
        And the user selects the "continent" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Asia" in the dropdown-list field
        Then the value of the dropdown-list field is "Asia"

        And the user selects the "legislation" labelled reference field on the main page
        And the user writes "France" in the reference field
        And the user selects "France" in the reference field
        Then the value of the reference field is "France"

        And the user selects the "Label for region" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "State" in the dropdown-list field
        Then the value of the dropdown-list field is "State"

        And the user selects the "Label for postal code" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Postal code" in the dropdown-list field
        Then the value of the dropdown-list field is "Postal code"

        # reference list (tax solution)
        And the user selects the "tax solution" labelled reference field on the main page
        And the user writes "French" in the reference field
        And the user selects "French tax solution" in the reference field
        Then the value of the reference field is "French tax solution"

        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: Country update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Country"
        Then the "Countries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        Then the "Country Australia" titled page is displayed

        When the user searches for "Japan" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "currency" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "US D" in the reference field
        And the user selects "US Dollar" in the reference field

        # reference list
        And the user selects the "legislation" labelled reference field on the main page
        And the user clears the reference field
        And the user writes "United States of America" in the reference field
        And the user selects "United States of America" in the reference field
        Then the value of the reference field is "United States of America"

        And the user selects the "tax solution" labelled reference field on the main page
        And the user writes "USA" in the reference field
        And the user selects "USA tax solution" in the reference field
        Then the value of the reference field is "USA tax solution"

        # Dropdown list
        And the user selects the "Label for region" labelled dropdown-list field on the main page
        And the user clicks in the dropdown-list field
        And the user selects "Department" in the dropdown-list field
        Then the value of the dropdown-list field is "Department"

        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: Country Read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Country"
        Then the "Countries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        Then the "Country Australia" titled page is displayed

        When the user searches for "Japan" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Japan"
        And the user selects the "ISO 3166-1 alpha-2" labelled text field on the main page
        Then the value of the text field is "JP"
        And the user selects the "ISO 3166-1 alpha-3" labelled text field on the main page
        Then the value of the text field is "JPN"
        And the user selects the "Currency" labelled reference field on the main page
        Then the value of the reference field is "US Dollar"


    Scenario: Country delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Country"
        Then the "Countries" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" labelled nested field of the selected row in the table field
        Then the "Country Australia" titled page is displayed

        When the user searches for "Japan" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And a toast containing text "Record deleted" is displayed
