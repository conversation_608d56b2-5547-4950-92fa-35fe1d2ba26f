# Confirmation that a delivery mode record can be created/updated and deleted
# Testing interaction with text fields/dropdown list
# Text fields allow for special characters as well as numerics
# Validate the improved UI of Delivery mode page

@reference_data
@manufacturing
Feature: reference-data-crud-delivery-mode

    Scenario: 01 - Delivery mode creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "Active" labelled switch field on the main page
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Drone_1" in the text field
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Air Drone_1" in the text field
        And the user selects the "Intrastat transport mode" labelled dropdown-list field on the main page
        And the user writes "Air transport" in the dropdown-list field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: 02 - Delivery mode update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Drone_1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Description" labelled text field on the main page
        And the user writes "Air Drone project_2" in the text field
        Then the value of the text field is "Air Drone project_2"
        And the user selects the "Intrastat transport mode" labelled dropdown-list field on the main page
        And the user writes "Own propulsion" in the dropdown-list field
        And the user selects "Own propulsion" in the dropdown-list field
        And the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record updated" is displayed

    Scenario: 03 - Delivery mode read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Drone_1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Drone_1"
        And the user selects the "Description" labelled text field on the main page
        Then the value of the text field is "Air Drone project_2"
        And the user selects the "Intrastat transport mode" labelled dropdown-list field on the main page
        And the value of the dropdown-list field is "Own propulsion"

    Scenario: 04 - Delivery mode delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Drone_1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        And a toast containing text "Record deleted" is displayed

    Scenario: 05 - Verify that the "Intrastat transport mode" label appears correctly in the main list columns
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Intrastat transport mode" on the sidebar is unticked
        And the user ticks the table column configuration with "Intrastat transport mode" name on the sidebar
        And the table column configuration with name "Intrastat transport mode" on the sidebar is ticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Intrastat transport mode" labelled column in the table field is displayed

    Scenario: 06 - Verify that ID and Intrastat transport mode columns are added in the main list of Delivery mode page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Intrastat transport mode" on the sidebar is unticked
        And the user ticks the table column configuration with "Intrastat transport mode" name on the sidebar
        And the table column configuration with name "Intrastat transport mode" on the sidebar is ticked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "Intrastat transport mode" labelled column in the table field is displayed
        And the "ID" labelled column in the table field is displayed

    Scenario: 07 - Verify that Name column has Ascending sorting, is Locked in column setting panel and has link which when clicked opens a split-view
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "Air"
        And the user selects the row 2 of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "Mail"
        And the user selects the row 3 of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "Rail"
        And the user selects the row 4 of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "Road"
        And the user selects the row 5 of the table field
        And the value of the "Name" labelled nested text field of the selected row in the table field is "Sea"
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "Name" on the sidebar is locked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Delivery mode Air" titled page is displayed

    Scenario: 08 - Verify that ID column is present and locked in the column setting panel, is displayed by default in the main list and has Ascending sorting
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the "ID" labelled column in the table field is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user clicks the "Open column panel" labelled button of the table field
        And the "Column settings" titled sidebar is displayed
        And the table column configuration with name "ID" on the sidebar is locked
        And the user clicks the Close button of the dialog on the sidebar
        And the user selects the row 1 of the table field
        And the value of the "ID" labelled nested text field of the selected row in the table field is "AIR"
        And the user selects the row 2 of the table field
        And the value of the "ID" labelled nested text field of the selected row in the table field is "MAIL"
        And the user selects the row 3 of the table field
        And the value of the "ID" labelled nested text field of the selected row in the table field is "RAIL"
        And the user selects the row 4 of the table field
        And the value of the "ID" labelled nested text field of the selected row in the table field is "ROAD"
        And the user selects the row 5 of the table field
        And the value of the "ID" labelled nested text field of the selected row in the table field is "SEA"

    Scenario: 09 - Verify that Duplicate and Delete actions are present for each row actions of the main list
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        And the "Duplicate" inline action button of the selected row in the table field is displayed
        And the user selects the row 2 of the table field
        And the "Duplicate" inline action button of the selected row in the table field is displayed
        And the user selects the row 3 of the table field
        And the "Duplicate" inline action button of the selected row in the table field is displayed
        And the user selects the row 4 of the table field
        And the "Duplicate" inline action button of the selected row in the table field is displayed
        And the user selects the row 5 of the table field
        And the "Duplicate" inline action button of the selected row in the table field is displayed
        And the user selects the row 1 of the table field
        And the "Delete" dropdown action of the selected row in the table field is displayed
        And the user selects the row 2 of the table field
        And the "Delete" dropdown action of the selected row in the table field is displayed
        And the user selects the row 3 of the table field
        And the "Delete" dropdown action of the selected row in the table field is displayed
        And the user selects the row 4 of the table field
        And the "Delete" dropdown action of the selected row in the table field is displayed
        And the user selects the row 5 of the table field
        And the "Delete" dropdown action of the selected row in the table field is displayed

    # Scenario: 10 - Verify that Duplicate action works directly from the row action
    # # ATP bug - button is clicked but fails as if not found.
    # Uncomment current scenario and adapt the bellow scenario related to the deletion when https://jira.sage.com/browse/XT-88332 is fixed
    # Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
    # Then the "Delivery modes" titled page is displayed
    # When the user selects the "$navigationPanel" bound table field on the navigation panel
    # And the user selects the row with text "Air" in the "Name" labelled column header of the table field
    # And the user clicks the "Duplicate" inline action button of the selected row in the table field
    # And the user waits 2 seconds
    # And the dialog title is "Duplicate record"
    # And the user selects the "Name" labelled text field on a modal
    # And the user writes "Air duplicated" in the text field
    # And the user selects the "ID" labelled text field on a modal
    # And the user writes "AIRDUPLICATED" in the text field
    # And the user clicks the "Duplicate" labelled business action button on a modal
    # And the "Delivery mode Air duplicated" titled page is displayed
    # And a success toast containing text "Record was duplicated successfully." is displayed

    Scenario: 11 - Verify that Duplicate action works from the detailed view
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Rail" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Delivery mode Rail" titled page is displayed
        And the user clicks the "Duplicate" labelled button in the header
        And the dialog title is "Duplicate record"
        And the user selects the "Name" labelled text field on a modal
        And the user clears the text field
        And the user writes "Rail duplicated" in the text field
        And the user selects the "ID" labelled text field on a modal
        And the user writes "RAILDUPLICATED" in the text field
        And the user clicks the "Duplicate" labelled business action button on a modal
        And the "Delivery mode Rail duplicated" titled page is displayed
        And a success toast containing text "Record was duplicated successfully." is displayed

    # Scenario: 12 - Verify that the confirmation dialogue is displayed over the main list when the "Delete" row action is clicked (If deletion is confirmed, the record is removed)
    #     # ATP bug - uncomment when https://jira.sage.com/browse/XT-88332 is fixed
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
    #     Then the "Delivery modes" titled page is displayed
    #     When the user selects the "$navigationPanel" bound table field on the navigation panel
    #     And the value of the "Name" labelled nested text field of row 4 in the table field is "Rail duplicated"
    #     And the user clicks the "Delete" dropdown action of row 4 of the table field
    #     And the user clicks the "Delete" button of the Confirm dialog
    #     And a success toast containing text "Record deleted" is displayed
    #     And the user selects the "$navigationPanel" bound table field on the navigation panel
    #     And the value of the "Name" labelled nested text field of row 4 in the table field is "Road"

    # Scenario: 13 - Verify that the confirmation dialogue is displayed over the main list when the "Delete" row action is clicked (If deletion is canceled, nothing is done)
    #     # ATP bug - uncomment when https://jira.sage.com/browse/XT-88332 is fixed
    #     Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
    #     Then the "Delivery modes" titled page is displayed
    #     And the user selects the "$navigationPanel" bound table field on the navigation panel
    #     And the value of the "Name" labelled nested text field of row 1 in the table field is "Air"
    #     And the user clicks the "Delete" dropdown action of row 1 of the table field
    #     And the user clicks the "Cancel" labelled business action button on a modal
    #     And the user selects the "$navigationPanel" bound table field on the navigation panel
    #     And the value of the "Name" labelled nested text field of row 1 in the table field is "Air"

    Scenario: 14 - Verify that the ID is present on the left side card in card view
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Air" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Delivery mode Air" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the value of the "id" bound nested text field of the card 1 in the table field is "AIR"

    Scenario: 15 - Verify that the record information is displayed on the screen
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Air" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Delivery mode Air" titled page is displayed
        And the user selects the "Active" labelled switch field on the main page
        And the "Active" labelled switch field on the main page is displayed
        And the switch field is set to "ON"
        And the user selects the "Name" labelled text field on the main page
        And the "Name" labelled text field on the main page is displayed
        And the value of the text field is "Air"
        And the user selects the "ID" labelled text field on the main page
        And the "ID" labelled text field on the main page is displayed
        And the value of the text field is "AIR"
        And the user selects the "Description" labelled text field on the main page
        And the "Description" labelled text field on the main page is displayed
        And the value of the text field is "Air freight"
        And the user selects the "Intrastat transport mode" labelled dropdown-list field on the main page
        And the "Intrastat transport mode" labelled dropdown-list field on the main page is displayed
        And the value of the dropdown-list field is "Air transport"

    Scenario: 16 - Verify that Save and Cancel buttons appear on the page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/DeliveryMode"
        Then the "Delivery modes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row with text "Air" in the "Name" labelled column header of the table field
        And the user clicks the "Name" labelled nested field of the selected row in the table field
        And the "Delivery mode Air" titled page is displayed
        And the user selects the "Description" labelled text field on the main page
        And the user writes "trigger update" in the text field
        And the user presses Tab
        Then the "Cancel" labelled business action button on the main page is visible
        Then the "Save" labelled business action button on the main page is visible
