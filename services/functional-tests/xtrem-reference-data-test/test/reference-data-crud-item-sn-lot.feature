# The goal of this test is to Create/Update/Delete an item with SN/LOT information
@reference_data
Feature: reference-data-crud-item-sn-lot

    Scenario: Item creation - SN/LOT - MyItem01
        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # HEADER#
        And the user selects the "Name" labelled text field on the main page
        And the user writes "MyItem01" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "MYITEM01" in the text field

        When the user selects the "Purchased" labelled switch field on the main page
        Then the user turns the switch field "ON"

        When the user selects the "Manufactured" labelled switch field on the main page
        Then the user turns the switch field "ON"

        When the user selects the "Sold" labelled switch field on the main page
        Then the user turns the switch field "ON"

        # INFORMATION TAB SECTION #
        And selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Description" labelled text area field on the main page
        And the user writes "My item #1" in the text area field

        When selects the "Management" labelled navigation anchor on the main page
        When the user selects the "Lot management" labelled dropdown-list field on the main page
        Then the user clicks in the dropdown-list field
        And the user selects "Lot" in the dropdown-list field
        Then the value of the dropdown-list field is "Lot"

        When the user selects the "Expiration date management" labelled checkbox field on the main page
        Then the user ticks the checkbox field

        And the user selects the "Lot sequence number" labelled reference field on the main page
        And the user writes "MATLOT" in the reference field
        And the user selects "Material lot number" in the reference field
        Then the value of the reference field is "Material lot number"

        When the user selects the "serialNumberManagement" bound dropdown-list field on the main page
        Then the user clicks in the dropdown-list field
        And the user selects "Managed" in the dropdown-list field
        Then the value of the dropdown-list field is "Managed"

        And the user selects the "Serial number sequence" labelled reference field on the main page
        And the user writes "ItemSerial" in the reference field
        And the user selects "Item serial number" in the reference field
        Then the value of the reference field is "Item serial number"

        # UNITS TAB SECTION #
        When selects the "Units" labelled navigation anchor on the main page
        And the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        And the user selects the "Purchase unit" labelled reference field on the main page
        And the user writes "Each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        And the user selects the "Sales unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # SITES TAB SECTION #
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        Then the "Item-sites" titled sidebar is displayed

        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "TE Hampton" in the reference field
        And the user selects "TE Hampton" in the reference field

        When the user selects the "Valuation method" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "Average unit cost" in the dropdown-list field
        Then the value of the dropdown-list field is "Average unit cost"

        When selects the "Replenishment" labelled navigation anchor on the sidebar

        When the user selects the "Replenishment method" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "By MRP" in the dropdown-list field
        Then the value of the dropdown-list field is "By MRP"

        When the user selects the "Preferred process" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "Purchasing" in the dropdown-list field
        Then the value of the dropdown-list field is "Purchasing"

        And the user selects the "Purchase lead time" labelled numeric field on the sidebar
        And the user writes "5" in the numeric field

        And the user selects the "Safety stock" labelled numeric field on the sidebar
        And the user writes "50" in the numeric field

        And the user selects the "Batch quantity" labelled numeric field on the sidebar
        And the user writes "30" in the numeric field

        And the user selects the "Economic order quantity" labelled numeric field on the sidebar
        And the user writes "60" in the numeric field

        # When the user clicks the "Cancel" button of the dialog on the sidebar
        When the user clicks the "OK" labelled business action button on the sidebar
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: Item Update -  MyItem01

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "MyItem01" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When selects the "Information" labelled navigation anchor on the main page
        And the user selects the "Commodity code" labelled text field on the main page
        And the user writes "XXX" in the text field
        And the user presses Tab

        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: Item Delete - MyItem01

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "MyItem01" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "MyItem01"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
