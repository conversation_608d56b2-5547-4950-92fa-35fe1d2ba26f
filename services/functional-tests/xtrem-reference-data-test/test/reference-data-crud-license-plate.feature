# Create update and delete a license plate

@reference_data
Feature: reference-data-crud-license-plate

    Scenario: License plate number creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LicensePlateNumber"
        Then the "License plate numbers" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "number" labelled text field on the main page
        And the user writes "LPN0001" in the text field
        ##lookup container##
        And the user selects the "container" bound reference field on the main page
        And the user writes "Small box" in the reference field
        And the user selects "Small box" in the reference field
        ##lookup location##
        And the user selects the "location" bound reference field on the main page
        And the user writes "location 2" in the reference field
        And the user selects "location 2" in the reference field
        ##checkboxes##
        And the user selects the "Single item" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Single lot" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user clicks the "Save" labelled business action button on the main page
    #the following step definition is added only to over pass bug XT-12337
    #And the user selects the "Save" labelled business action button on the main page

    Scenario: License plate number update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LicensePlateNumber"
        Then the "License plate numbers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "LPN0001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Single item" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user selects the "Single lot" labelled checkbox field on the main page
        And the user clicks in the checkbox field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: License plate number delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/LicensePlateNumber"
        Then the "License plate numbers" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "number" bound nested field of the selected row in the table field
        And the user searches for "LPN0001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
