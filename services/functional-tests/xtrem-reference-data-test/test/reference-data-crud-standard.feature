# Create update and delete a standard

@reference_data
Feature: reference-data-crud-standard

    Scenario: Display Standard unit Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Standard/$new"
        Then the "Standard" titled page is displayed

    Scenario: Standard unit page creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Standard"
        Then the "Standards" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        Then the user selects the "id" bound text field on the main page
        And the user writes "QA standard ID" in the text field
        And the user selects the "Standard" labelled text field on the main page
        And the user writes "Standard@SDO_1" in the text field
        And the user selects the "Code" labelled text field on the main page
        And the user writes "Code_StandardTest1" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Name_StandardTest1" in the text field
        And the user selects the "Version" labelled text field on the main page
        And the user writes "2021" in the text field
        And the user selects the "Sector" labelled text field on the main page
        And the user writes "Sector@Standard_1" in the text field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Standard unit page update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Standard"
        Then the "Standards" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QA standard ID" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user writes "New@NameOfStandard" in the text field
        And the user selects the "Version" labelled text field on the main page
        And the user clears the text field
        And the user writes "2020" in the text field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Standard unit page read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Standard"
        Then the "Standards" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "QA standard ID" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "id" bound text field on the main page
        Then the value of the text field is "QA standard ID"
        And the user selects the "Standard" labelled text field on the main page
        Then the value of the text field is "Standard@SDO_1"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "New@NameOfStandard"
        And the user selects the "Version" labelled text field on the main page
        Then the value of the text field is "2020"
        And the user selects the "Sector" labelled text field on the main page
        Then the value of the text field is "Sector@Standard_1"

    Scenario: Standard unit page delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Standard"
        Then the "Standards" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "New@NameOfStandard" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
