# Confirmation that a reason code record can be created/updated and deleted
# Testing interaction with text fields and radio buttons
# Text fields allow for special characters as well as numerics

@reference_data
@distribution
@inventory
Feature: reference-data-crud-reason-code

    Scenario: Reason code creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ReasonCode"
        Then the "Reason codes" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "id" labelled text field on the main page
        And the user writes "Reason code_ID 0001" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "  Reason code name_001" in the text field
        When the user selects the "Functionality" labelled radio field on the main page
        When the user selects the label "Quantity increase adjustment" in the radio field
        And the user selects the "Default" labelled checkbox field on the main page
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Reason code update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ReasonCode"
        Then the "Reason codes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Reason code_ID 0001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user writes "Reason code update_001" in the text field
        And the user selects the "Functionality" labelled radio field on the main page
        And the user selects the label "Quantity decrease adjustment" in the radio field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Reason code delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ReasonCode"
        Then the "Reason codes" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Reason code update_001" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
