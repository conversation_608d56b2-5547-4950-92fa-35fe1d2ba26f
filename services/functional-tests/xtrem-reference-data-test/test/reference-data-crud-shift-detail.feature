# Create update and delete a shift detail


@reference_data
Feature: reference-data-crud-shift-detail

    Scenario: Display Shift detail Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ShiftDetail/$new"
        Then the "Shift detail" titled page is displayed

    Scenario: Shift detail page creation
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ShiftDetail"
        Then the "Shift details" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel
        And the user selects the "ID" labelled text field on the main page
        And the user writes "QAShiftdetail1234567890" in the text field
        And the user selects the "Name" labelled text field on the main page
        And the user writes "ShiftNameTest1234567890" in the text field
        And the user selects the "Start time *" labelled text field on the main page
        And the user writes "08:00" in the text field
        And the user selects the "End time *" labelled text field on the main page
        And the user writes "16:35" in the text field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Shift detail page update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ShiftDetail"
        Then the "Shift details" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "ShiftNameTest1234567890" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "Name" labelled text field on the main page
        And the user clears the text field
        And the user writes "NewTestNameOfShiftDetail1" in the text field
        And the user selects the "End time *" labelled text field on the main page
        And the user clears the text field
        And the user writes "18:30" in the text field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Shift detail page read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/ShiftDetail"
        Then the "Shift details" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "NewTestNameOfShiftDetail1" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "ID" labelled text field on the main page
        Then the value of the text field is "QAShiftdetail1234567890"
        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "NewTestNameOfShiftDetail1"
        And the user selects the "Start time *" labelled text field on the main page
        Then the value of the text field is "08:00"
        And the user selects the "End time *" labelled text field on the main page
        Then the value of the text field is "18:30"

    Scenario: Shift detail page delete
        Given the user searches for "NewTestNameOfShiftDetail1" in the navigation panel
        When the user clicks the "first" navigation panel's row
        Then the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
