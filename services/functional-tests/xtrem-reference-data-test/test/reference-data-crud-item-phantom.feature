# The goal of this test is to Create/Update/Delete a phantom item
@reference_data

Feature: reference-data-crud-item-phantom

    Scenario: 1- Check default values of switch fields

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

        # Header information
        When the user selects the "Name" labelled text field on the main page
        And the user writes "Phantom Creation" in the text field
        When the user selects the "id" bound text field on the main page
        And the user writes "PHANTOM_C" in the text field
        When the user selects the "Type" labelled dropdown-list field on the main page
        Then the value of the dropdown-list field is "Good"

        # check defaulted values of header switch fields
        When the user selects the "Stock management" labelled switch field on the main page
        Then the switch field is set to "ON"

        When the user selects the "Purchased" labelled switch field on the main page
        Then the switch field is set to "OFF"

        When the user selects the "Manufactured" labelled switch field on the main page
        Then the switch field is set to "OFF"

        When the user selects the "Sold" labelled switch field on the main page
        Then the switch field is set to "OFF"

        # check Management tab
        And selects the "Management" labelled navigation anchor on the main page

        # Verify no Phantom switch field is displayed
        Then the "Stock" labelled block container on the main page is displayed
        # TODO: add test step to verify that the Manufacturing block is hidden => need
        # of enhancement https://jira.sage.com/browse/XT-75826
        Then the "Manufacturing" labelled block container on the main page is hidden

    Scenario: 2- Change switch fields values and check business rules applied

        When the user selects the "Manufactured" labelled switch field on the main page
        Then the user turns the switch field "ON"

        # check Management tab
        And selects the "Management" labelled navigation anchor on the main page
        Then the "Manufacturing" labelled block container on the main page is displayed

        # Verify Phantom switch field is displayed
        When the user selects the "Phantom" labelled switch field on the main page
        Then the "Phantom" labelled switch field on the main page is displayed
        And the switch field is set to "OFF"

        # Verify switch field can be turned ON
        Then the user turns the switch field "ON"
        And the switch field is set to "ON"

        # verify header switch fields values when phantom turned ON
        When the user selects the "Stock management" labelled switch field on the main page
        Then the switch field is disabled
        And the switch field is set to "ON"

        When the user selects the "Purchased" labelled switch field on the main page
        Then the switch field is disabled
        And the switch field is set to "OFF"

        When the user selects the "Manufactured" labelled switch field on the main page
        Then the switch field is disabled
        Then the switch field is set to "ON"

        When the user selects the "Sold" labelled switch field on the main page
        Then the switch field is disabled
        And the switch field is set to "OFF"

    Scenario: 3- Create phantom

        # select units' tab
        And selects the "Units" labelled navigation anchor on the main page

        # enter stock unit
        When the user selects the "Stock unit" labelled reference field on the main page
        And the user writes "each" in the reference field
        And the user selects "Each" in the reference field
        Then the value of the reference field is "Each"

        # add item-site record
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user clicks the "addItemSite" bound action of the table field
        Then the "Item-sites" titled sidebar is displayed

        And the user selects the "Site" labelled reference field on the sidebar
        And the user writes "Chav" in the reference field
        And the user selects "Site de Chavanod" in the reference field

        # valuation method must be forced to "Standard cost"
        When the user selects the "Valuation method" labelled dropdown-list field on the sidebar
        Then the dropdown-list field is read-only
        And the value of the dropdown-list field is "Standard cost"

        When selects the "Replenishment" labelled navigation anchor on the sidebar

        When the user selects the "Replenishment method" labelled dropdown-list field on the sidebar
        Then the user clicks in the dropdown-list field
        And the user selects "By MRP" in the dropdown-list field
        Then the value of the dropdown-list field is "By MRP"

        When the user selects the "Preferred process" labelled dropdown-list field on the sidebar
        Then the dropdown-list field is disabled
        And the value of the dropdown-list field is "Production"

        When the user clicks the "OK" labelled business action button on the sidebar
        Then the user clicks the "Save" labelled business action button on the main page
        And a toast containing text "Record created" is displayed

    Scenario: 4- Read and update phantom item - PHANTOM_C

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Phantom creation" in the navigation panel
        And the user clicks the "first" navigation panel's row

        When selects the "Management" labelled navigation anchor on the main page
        And the user selects the "Phantom" labelled switch field on the main page
        Then the switch field is disabled
        And the switch field is set to "ON"

        # delete item-site record
        When selects the "Sites" labelled navigation anchor on the main page
        And the user selects the "itemSites" bound table field on the main page
        And the user selects the row with text "Site de Chavanod" in the "Site" labelled column header of the table field
        And the user clicks the "Delete" dropdown action of the selected row of the table field

        # check Phantom switch is now enabled (because no existing item-site)
        When selects the "Management" labelled navigation anchor on the main page
        And the user selects the "Phantom" labelled switch field on the main page
        Then the switch field is enabled
        And the switch field is set to "ON"

        # Update record
        And the user clicks the "Save" labelled business action button on the main page
        Then a toast containing text "Record updated" is displayed

    Scenario: 5- Delete phantom item - PHANTOM_C

        Given the user opens the application on a ultrawide desktop using the following link: "@sage/xtrem-master-data/Item"
        Then the "Items" titled page is displayed
        When the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        Then the user clicks the "name" bound nested field of the selected row in the table field
        And the user searches for "Phantom Creation" in the navigation panel
        And the user clicks the "first" navigation panel's row

        And the user selects the "Name" labelled text field on the main page
        Then the value of the text field is "Phantom Creation"
        When the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
        Then a toast containing text "Record deleted" is displayed
