# Create update and delete an allergen

@reference_data
Feature: reference-data-crud-allergen

    Scenario: Open the Address Page
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Allergen"
        Then the "Allergens" titled page is displayed
        When the user clicks the "Create" labelled business action button on the navigation panel

    Scenario: Allergen creation
        # Company information section
        #Then the user clicks in the "Active" labelled switch field on the main page
        When the user selects the "isActive" bound switch field on the main page
        And the switch field is set to "ON"
        Then the user selects the "name" labelled text field on the main page
        And the user writes "Penicillin" in the text field
        And the user selects the "id" bound text field on the main page
        And the user writes "PEN" in the text field
        And the user clicks the "Save" labelled business action button on the main page

    Scenario: Allergen update
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Allergen"
        Then the "Allergens" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "pictogram" labelled nested field of the selected row in the table field
        Then the "Allergen Celery" titled page is displayed
        When the user searches for "Penicillin" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" labelled text field on the main page
        And the user writes "Penicilline" in the text field
        And the user presses Tab
        And the user clicks the "Save" labelled business action button on the main page
    #And the user selects the "Save" labelled business action button on the main page

    Scenario: Allergen read
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Allergen"
        Then the "Allergens" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "pictogram" labelled nested field of the selected row in the table field
        Then the "Allergen Celery" titled page is displayed
        When the user searches for "Penicilline" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user selects the "name" bound text field on the main page
        Then the value of the text field is "Penicilline"
        And the user selects the "id" bound text field on the main page
        Then the value of the text field is "PEN"

    Scenario: Allergen delete
        Given the user opens the application on a desktop using the following link: "@sage/xtrem-master-data/Allergen"
        Then the "Allergens" titled page is displayed
        And the user selects the "$navigationPanel" bound table field on the navigation panel
        And the user selects the row 1 of the table field
        When the user clicks the "pictogram" labelled nested field of the selected row in the table field
        Then the "Allergen Celery" titled page is displayed
        When the user searches for "Penicilline" in the navigation panel
        And the user clicks the "first" navigation panel's row
        And the user clicks the "Delete" labelled more actions button in the header
        And the user clicks the "Delete" button of the Confirm dialog
