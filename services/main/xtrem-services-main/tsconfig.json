{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "composite": true}, "include": ["index.ts"], "references": [{"path": "../../adapters/xtrem-ap-automation"}, {"path": "../../adapters/xtrem-avalara-gateway"}, {"path": "../../../platform/cli/xtrem-cli"}, {"path": "../../../platform/shared/xtrem-date-time"}, {"path": "../../applications/xtrem-declarations"}, {"path": "../../adapters/xtrem-intacct-gateway"}, {"path": "../../../platform/system/xtrem-integration"}, {"path": "../../../platform/system/xtrem-interop"}, {"path": "../../../platform/system/xtrem-metadata"}, {"path": "../../../platform/system/xtrem-routing"}, {"path": "../../adapters/xtrem-sage-network"}, {"path": "../../../platform/system/xtrem-scheduler"}, {"path": "../../adapters/xtrem-service-fabric"}, {"path": "../../../platform/front-end/xtrem-standalone"}, {"path": "../../applications/xtrem-stock"}, {"path": "../../mashups/xtrem-stock-blend-po"}, {"path": "../../mashups/xtrem-stock-blend-po-so"}, {"path": "../../applications/xtrem-supply-chain"}, {"path": "../../../platform/system/xtrem-workflow"}]}