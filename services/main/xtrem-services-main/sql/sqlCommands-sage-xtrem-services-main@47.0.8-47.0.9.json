{"fromVersion": "47.0.8", "toVersion": "47.0.9", "gitHead": "ef3d054e1bbf7b3e4c9c99344fae9fd2e6775ed5", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN ", "    \tSELECT trigger_name, event_object_table ", "    \tFROM information_schema.triggers ", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN ", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN ", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_synchronization_state_ind0;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_synchronization_state ADD COLUMN IF NOT EXISTS remote_app INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_synchronization_state.remote_app IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"sys_app\",", "  \"isSelfReference\": false", "}';"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_outbound_order_document_line ADD COLUMN IF NOT EXISTS quantity_in_sales_unit NUMERIC(28,10);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_outbound_order_document_line.quantity_in_sales_unit IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": false,", "  \"precision\": 20,", "  \"scale\": 5", "}';"]}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            WITH CTE AS (", "                 SELECT a._id, d.mode, a._tenant_id", "                FROM %%SCHEMA_NAME%%.base_business_relation a", "                LEFT OUTER JOIN %%SCHEMA_NAME%%.business_entity b ON a.business_entity = b._id and a._tenant_id = b._tenant_id", "                LEFT OUTER JOIN (", "                    SELECT *", "                    FROM %%SCHEMA_NAME%%.business_entity_address", "                    WHERE is_primary = true", "                ) c ON b._id = c.business_entity and a._tenant_id = c._tenant_id", "                LEFT OUTER JOIN %%SCHEMA_NAME%%.delivery_detail d ON c._id = d.address and a._tenant_id = d._tenant_id", "                )", "                UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document a", "                    SET delivery_mode = (", "                        SELECT _id", "                        FROM CTE cte", "                        WHERE cte._id = b.business_relation and cte._tenant_id = b._tenant_id", "                    )", "                FROM %%SCHEMA_NAME%%.base_distribution_document b", "                WHERE a._id = b._id AND a._tenant_id = b._tenant_id", "                AND delivery_mode IS NULL;", "                END $$;"], "actionDescription": "Add properties to the base outbound shipment document - delivery mode  "}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document a", "            SET delivery_lead_time = 0", "            FROM %%SCHEMA_NAME%%.base_document b", "            WHERE a._id = b._id and a._tenant_id = b._tenant_id;", "            END $$;"], "actionDescription": "Add properties to the base outbound shipment document - delivery lead time  "}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document a", "            SET delivery_date = b.date", "            FROM %%SCHEMA_NAME%%.base_document b", "            WHERE a._id = b._id and a._tenant_id = b._tenant_id;", "            END $$;"], "actionDescription": "Add properties to the base outbound order document - delivery date  "}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            UPDATE %%SCHEMA_NAME%%.base_outbound_order_document_line a", "            SET quantity_in_sales_unit = 0;", "            END $$;"], "actionDescription": "Add properties to the base outbound order document - quantity in sales unit"}, {"isSysPool": false, "sql": "DELETE FROM %%SCHEMA_NAME%%.sys_synchronization_state;", "actionDescription": "Resets the sys_synchronization_state table"}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            WITH CTE AS (", "                SELECT DISTINCT ON (a._tenant_id) a._id, a._tenant_id", "                FROM %%SCHEMA_NAME%%.site a", "                WHERE a.is_inventory = True AND a.is_active = true", "                LIMIT 1", "            )", "            UPDATE %%SCHEMA_NAME%%.stock_transfer_shipment a", "            SET receiving_site = cte._id", "            FROM CTE cte", "            WHERE a._tenant_id = cte._tenant_id;", "        END $$;"], "actionDescription": "Add properties to the stock transfer shipment - receiving site"}, {"isSysPool": false, "sql": ["DO $$", "            BEGIN", "                WITH CTE AS (", "                    SELECT DISTINCT ON (a._tenant_id) a._id, a._tenant_id", "                    FROM %%SCHEMA_NAME%%.supplier a", "                    INNER JOIN %%SCHEMA_NAME%%.base_business_relation b", "                    ON a._id = b._id AND a._tenant_id = b._tenant_id", "                    WHERE b.is_active = True", "                    LIMIT 1", "                )", "                UPDATE %%SCHEMA_NAME%%.stock_transfer_shipment a", "                SET supplier = cte._id", "                FROM CTE cte", "                WHERE a._tenant_id = cte._tenant_id;", "            END $$;"], "actionDescription": "Add properties to the stock transfer shipment - supplier"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_outbound_order_document_line ALTER COLUMN quantity_in_sales_unit SET NOT NULL;"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_synchronization_state ADD CONSTRAINT \"sys_synchronization_state_remote_app_fk\" FOREIGN KEY(_tenant_id,remote_app) REFERENCES %%SCHEMA_NAME%%.sys_app(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sys_synchronization_state_remote_app_fk ON %%SCHEMA_NAME%%.sys_synchronization_state IS '{", "  \"targetTableName\": \"sys_app\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"remote_app\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_synchronization_state ALTER COLUMN remote_app SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_synchronization_state_ind0 ON %%SCHEMA_NAME%%.sys_synchronization_state(_tenant_id ASC,remote_app ASC,node ASC);"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", false, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00D6MB-64202\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "SysJobSchedule"}}, {"action": "reload_setup_data", "args": {"factory": "SequenceNumber"}}, {"action": "reload_setup_data", "args": {"factory": "SequenceNumberComponent"}}, {"action": "reload_setup_data", "args": {"factory": "SequenceNumberAssignmentDocumentType"}}, {"action": "reload_setup_data", "args": {"factory": "SequenceNumberAssignment"}}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.sys_synchronization_state IS '{", "  \"isSharedByAllTenants\": false,", "  \"naturalKey\": [", "    \"remoteApp\",", "    \"node\"", "  ]", "}';;COMMENT ON CONSTRAINT sys_synchronization_state_remote_app_fk ON %%SCHEMA_NAME%%.sys_synchronization_state IS '{", "  \"targetTableName\": \"sys_app\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"remote_app\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"SysJobSchedule": {"metadata": {"rootFactoryName": "SysJobSchedule", "name": "SysJobSchedule", "naturalKeyColumns": ["_tenant_id", "id", "execution_user"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "operation", "type": "reference", "targetFactoryName": "MetaNodeOperation"}, {"name": "description", "type": "string"}, {"name": "is_active", "type": "boolean"}, {"name": "start_stamp", "type": "datetime", "isNullable": true}, {"name": "end_stamp", "type": "datetime", "isNullable": true}, {"name": "execution_user", "type": "reference", "targetFactoryName": "User"}, {"name": "execution_locale", "type": "string"}, {"name": "parameter_values", "type": "json"}, {"name": "cron_schedule", "type": "string"}, {"name": "time_zone", "type": "string"}, {"name": "id", "type": "string"}]}, "rows": [["sage", "SysNotificationState|purgeHistory|start", "Purge history - 3 months", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"months\",\"duration\":\"3\"}", "0 1 1 * *", "Europe/Paris", "purgeHistory_1"], ["sage", "SysNotificationState|purgeHistory|start", "Purge sync history - 2 weeks", "Y", null, null, "<EMAIL>", "en-US", "{\n    \"unit\": \"weeks\",\n    \"duration\": \"2\",\n    \"operationNames\": [\"SysSynchronizationTarget.synchronize\"]\n}\n", "30 1 * * *", "Europe/Paris", "purgeSyncHistory_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"info\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"success\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"success\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_2"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"warning\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"warning\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_3"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"error\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"error\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_4"], ["sage", "SysChore|purgeContentAddressableTables|start", "Content-addressable tables are automatically purged once a day", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"1\"}", "0 1 * * *", "Europe/Paris", "purgeContentAddressableTables"], ["sage", "Attachment|purge|start", "Purge attachments and association attachments", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"1\"}", "0 0 * * *", "Europe/Paris", "purgeAttachment_"], ["sage", "Company|syncCompanyOnHold|start", "Update company on hold check from Intacct", "Y", null, null, "<EMAIL>", "en-US", "{\"isAllCompanies\":\"true\"}", "0 3 * * *", "Europe/Paris", "companyOnHold_1"], ["sage", "IntacctMap|updateCustomMapping|start", "Update the custom mapping", "Y", null, null, "<EMAIL>", "en-US", "{\"filter\":\"{_id:{_nin:[]}}\"}", "0 8 * * 6", "Europe/Paris", "updateCustomMapping"], ["sage", "Attribute|updateRecordNoOnEmployee|start", "Update recordNo on employee", "Y", null, null, "<EMAIL>", "en-US", "{\n    \"filter\": \"{attributeType:{id:'employee'},uIntacctId:{_ne: ''}}\"\n}\n", "0 2,14 * * 3", "Europe/Paris", "updateRecordNoOnEmployee"], ["sage", "ItemSiteCost|syncStockValueChange|start", "Daily stock value change from item site costs", "Y", null, null, "<EMAIL>", "en-US", null, "1 0 * * *", "Europe/Paris", "syncStockValueChange_1"]]}, "SequenceNumber": {"metadata": {"rootFactoryName": "SequenceNumber", "name": "SequenceNumber", "naturalKeyColumns": ["_tenant_id", "id", "legislation"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "definition_level", "type": "enum", "enumMembers": ["application", "company", "site"]}, {"name": "rtz_level", "type": "enum", "enumMembers": ["noReset", "yearly", "monthly"]}, {"name": "is_cleared_by_reset", "type": "boolean"}, {"name": "type", "type": "enum", "enumMembers": ["alphanumeric", "numeric"]}, {"name": "is_chronological", "type": "boolean"}, {"name": "legislation", "type": "reference", "isNullable": true, "targetFactoryName": "Legislation"}]}, "rows": [["", "ACH", "{\"en\":\"Purchasing\",\"de-DE\":\"Ein<PERSON>uf\",\"en-US\":\"Purchasing\",\"fr-FR\":\"Achats\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "ItemlLot", "{\"en\":\"Item lot number\",\"de-DE\":\"Chargennummer Artikel\",\"en-US\":\"Item lot number\",\"fr-FR\":\"Numéro lot article\"}", "application", "noReset", "N", "alphanumeric", "N", ""], ["", "ItemSerial", "{\"en\":\"Item serial number\",\"de-DE\":\"Seriennummer Artikel\",\"en-US\":\"Item serial number\",\"fr-FR\":\"Numéro série article\"}", "application", "noReset", "N", "alphanumeric", "N", ""], ["", "MATLOT", "{\"en\":\"Material lot number\",\"de-DE\":\"Chargennummer Material\",\"en-US\":\"Material lot number\",\"fr-FR\":\"Numéro lot matière\"}", "application", "noReset", "N", "alphanumeric", "N", ""], ["", "MiscStockIssue", "{\"en\":\"Miscellaneous stock issue\",\"de-DE\":\"Sonstiger Bestandsabgang\",\"en-US\":\"Miscellaneous stock issue\",\"fr-FR\":\"Sortie de stock diverse\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "MiscStockReceipt", "{\"en\":\"Miscellaneous stock receipt\",\"de-DE\":\"Sonstiger Bestandseingang\",\"en-US\":\"Miscellaneous stock receipt\",\"fr-FR\":\"Entrée de stock diverse\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "OD", "{\"en\":\"Miscellaneous operations\",\"de-DE\":\"Sonstige Vorgänge\",\"en-US\":\"Miscellaneous operations\",\"fr-FR\":\"Opérations Diverses\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PostedSalesCreditMemo", "{\"en\":\"Posted sales credit memo\",\"de-DE\":\"Gebuchte Verkaufsgutschrift\",\"en-US\":\"Posted sales credit memo\",\"fr-FR\":\"Avoir de vente comptabilisé\"}", "site", "yearly", "N", "alphanumeric", "Y", ""], ["", "PostedSalesInvoice", "{\"en\":\"Posted sales invoice\",\"de-DE\":\"Gebuchte Verkaufsrechnung\",\"en-US\":\"Posted sales invoice\",\"fr-FR\":\"Facture de vente comptabilisée\"}", "site", "yearly", "N", "alphanumeric", "Y", ""], ["", "PurchaseCreditMemo", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"en-US\":\"Purchase credit memo\",\"fr-FR\":\"Avoir d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseInvoice", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"en-US\":\"Purchase invoice\",\"fr-FR\":\"Facture d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseOrder", "{\"en\":\"Purchase order\",\"de-DE\":\"Bestellung\",\"en-US\":\"Purchase order\",\"fr-FR\":\"Commande d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseOrderSuggestion", "{\"en\":\"Purchase order suggestion\",\"de-DE\":\"Bestellvorschlag\",\"en-US\":\"Purchase order suggestion\",\"fr-FR\":\"Commande d'achat suggérée\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseReceipt", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"en-US\":\"Purchase receipt\",\"fr-FR\":\"Réception d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseRequisition", "{\"en\":\"Purchase requisition\",\"de-DE\":\"Bestellanforderung\",\"en-US\":\"Purchase requisition\",\"fr-FR\":\"Demand<PERSON> d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseReturn", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"en-US\":\"Purchase return\",\"fr-FR\":\"Retour d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesCreditMemo", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"en-US\":\"Sales credit memo\",\"fr-FR\":\"Avoir de vente\"}", "site", "yearly", "Y", "alphanumeric", "Y", ""], ["", "SalesInvoice", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"en-US\":\"Sales invoice\",\"fr-FR\":\"Facture de vente\"}", "site", "yearly", "Y", "alphanumeric", "Y", ""], ["", "SalesOrder", "{\"en\":\"Sales order\",\"de-DE\":\"Verkaufsauftrag\",\"en-US\":\"Sales order\",\"fr-FR\":\"Commande de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesReturnReceipt", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"en-US\":\"Sales return receipt\",\"fr-FR\":\"Réception retour de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesReturnRequest", "{\"en\":\"Sales return request\",\"de-DE\":\"Verkaufsretourenanforderung\",\"en-US\":\"Sales return request\",\"fr-FR\":\"Demande retour de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesShipment", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"en-US\":\"Sales shipment\",\"fr-FR\":\"Expédition de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockAdjustment", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockChange", "{\"en\":\"Stock change\",\"de-DE\":\"Bestandsänderung\",\"en-US\":\"Stock change\",\"fr-FR\":\"Changement stock\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockCount", "{\"en\":\"Stock count\",\"de-DE\":\"Inventur\",\"en-US\":\"Stock count\",\"fr-FR\":\"Inventaire\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockCountList", "{\"en\":\"Stock count list\",\"de-DE\":\"Inventurliste\",\"en-US\":\"Stock count list\",\"fr-FR\":\"Liste d'inventaire\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "StockCountSession", "{\"en\":\"Stock count session\",\"de-DE\":\"Inventurvorgang\",\"en-US\":\"Stock count session\",\"fr-FR\":\"Session d'inventaire\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "StockJournal", "{\"en\":\"Stock journal\",\"de-DE\":\"Bestandsjournal\",\"en-US\":\"Stock journal\",\"fr-FR\":\"Journal de stock\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "UnbilledJournal", "{\"en\":\"Unbilled journal\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten Buchaltung\",\"en-US\":\"Unbilled journal\",\"fr-FR\":\"Journal non facturé\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "VEN", "{\"en\":\"Sales\",\"de-DE\":\"Verkauf\",\"en-US\":\"Sales\",\"fr-FR\":\"Ventes\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "WorkInProgressJournal", "{\"en\":\"Work in progress Journal\",\"de-DE\":\"Work-In-Progress-Journal\",\"en-US\":\"Work in progress Journal\",\"fr-FR\":\"Journal en-cours\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "WorkOrder", "{\"en\":\"Work order\",\"de-DE\":\"Fertigungsauftrag\",\"en-US\":\"Work order\",\"fr-FR\":\"Ordre de fabrication\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "WorkOrderSuggestion", "{\"en\":\"Work order suggestion\",\"de-DE\":\"Vorschlag Fertigungsauftrag\",\"en-US\":\"Work order suggestion\",\"fr-FR\":\"Ordre de fabrication suggéré\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "WorkOrderTracking", "{\"en\":\"Work order tracking\",\"de-DE\":\"Rückmeldung Fertigungsaufträge\",\"en-US\":\"Work order tracking\",\"fr-FR\":\"Suivi d’ordre de fabrication\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesInvoiceUSA", "{\"en\":\"Sales invoice USA\",\"de-DE\":\"Verkaufsrechnung USA\",\"en-US\":\"Sales invoice USA\",\"fr-FR\":\"Facture de vente USA\"}", "site", "yearly", "Y", "alphanumeric", "Y", "US"], ["", "CashDisbursementsJournal", "{\"en\":\"Cash disbursements journal\",\"de-DE\":\"Zahlungsausgangsbeleg Bank Manager\",\"en-US\":\"Cash disbursements journal\",\"fr-FR\":\"Journal des décaissements\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "CashReceiptsJournal", "{\"en\":\"Cash receipts journal\",\"de-DE\":\"Zahlungseingangsbeleg Bank Manager\",\"en-US\":\"Cash receipts journal\",\"fr-FR\":\"Journal des encaissements\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "ArAdvance", "{\"en\":\"Accounts receivable advance\",\"de-DE\":\"Vorauszahlung Debitorenbuchhaltung Bank Manager\",\"en-US\":\"Accounts receivable advance\",\"fr-FR\":\"Encaissement\"}", "site", "yearly", "Y", "alphanumeric", "N", ""], ["", "ArPayment", "{\"en\":\"Accounts receivable payment\",\"de-DE\":\"Zahlung Debitorenbuchhaltung Bank Manager\",\"en-US\":\"Accounts receivable payment\",\"fr-FR\":\"Encaissement\"}", "site", "yearly", "Y", "alphanumeric", "N", ""], ["", "EREC", "{\"en\":\"Purchasing\",\"de-DE\":\"Ein<PERSON>uf\",\"en-US\":\"Purchasing\",\"fr-FR\":\"Achats\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "VREC", "{\"en\":\"Sales\",\"de-DE\":\"Verkauf\",\"en-US\":\"Sales\",\"fr-FR\":\"Ventes\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockValueChange", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"en-US\":\"Stock value change\",\"fr-FR\":\"Changement de la valeur de stock\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "StockTransferOrder", "{\"en\":\"Stock transfer order\",\"de-DE\":\"\",\"en-US\":\"Stock transfer order\",\"fr-FR\":\"\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "SalesJournal", "{\"en\":\"Sales journal\",\"de-DE\":\"Verkaufsbeleg Buchhaltung\",\"en-US\":\"Sales journal\",\"fr-FR\":\"Journal de vente\"}", "application", "yearly", "Y", "alphanumeric", null, ""]]}, "SequenceNumberComponent": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SequenceNumberComponent", "name": "SequenceNumberComponent", "naturalKeyColumns": ["_tenant_id", "sequence_number", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "sequence_number", "type": "reference", "targetFactoryName": "SequenceNumber"}, {"name": "type", "type": "enum", "enumMembers": ["constant", "year", "month", "week", "day", "company", "site", "sequenceNumber"]}, {"name": "length", "type": "short"}, {"name": "constant", "type": "string"}], "vitalParentColumn": {"name": "sequence_number", "type": "reference", "targetFactoryName": "SequenceNumber"}}, "rows": [["", "100", "ItemSerial|", "constant", "2", "SN"], ["", "200", "ItemSerial|", "sequenceNumber", "6", ""], ["", "100", "ItemlLot|", "year", "2", ""], ["", "200", "ItemlLot|", "sequenceNumber", "6", ""], ["", "100", "MATLOT|", "year", "2", ""], ["", "200", "MATLOT|", "sequenceNumber", "6", ""], ["", "100", "MiscStockIssue|", "constant", "2", "SS"], ["", "200", "MiscStockIssue|", "year", "2", ""], ["", "300", "MiscStockIssue|", "sequenceNumber", "4", ""], ["", "100", "MiscStockReceipt|", "constant", "2", "SR"], ["", "200", "MiscStockReceipt|", "year", "2", ""], ["", "300", "MiscStockReceipt|", "sequenceNumber", "4", ""], ["", "100", "PurchaseCreditMemo|", "constant", "2", "PC"], ["", "200", "PurchaseCreditMemo|", "year", "2", ""], ["", "300", "PurchaseCreditMemo|", "sequenceNumber", "4", ""], ["", "100", "PurchaseInvoice|", "constant", "2", "PI"], ["", "200", "PurchaseInvoice|", "year", "2", ""], ["", "300", "PurchaseInvoice|", "sequenceNumber", "4", ""], ["", "100", "PurchaseOrder|", "constant", "2", "PO"], ["", "200", "PurchaseOrder|", "year", "2", ""], ["", "300", "PurchaseOrder|", "sequenceNumber", "4", ""], ["", "100", "PurchaseOrderSuggestion|", "constant", "3", "POS"], ["", "200", "PurchaseOrderSuggestion|", "year", "2", ""], ["", "300", "PurchaseOrderSuggestion|", "sequenceNumber", "6", ""], ["", "100", "PurchaseReceipt|", "constant", "2", "PR"], ["", "200", "PurchaseReceipt|", "year", "2", ""], ["", "300", "PurchaseReceipt|", "sequenceNumber", "4", ""], ["", "100", "PurchaseRequisition|", "constant", "2", "PQ"], ["", "200", "PurchaseRequisition|", "year", "2", ""], ["", "300", "PurchaseRequisition|", "sequenceNumber", "4", ""], ["", "100", "PurchaseReturn|", "constant", "2", "PT"], ["", "200", "PurchaseReturn|", "year", "2", ""], ["", "300", "PurchaseReturn|", "sequenceNumber", "4", ""], ["", "100", "SalesCreditMemo|", "constant", "2", "SC"], ["", "200", "SalesCreditMemo|", "site", "2", ""], ["", "300", "SalesCreditMemo|", "year", "2", ""], ["", "400", "SalesCreditMemo|", "sequenceNumber", "4", ""], ["", "100", "SalesInvoice|", "constant", "2", "SI"], ["", "200", "SalesInvoice|", "site", "2", ""], ["", "300", "SalesInvoice|", "year", "2", ""], ["", "400", "SalesInvoice|", "sequenceNumber", "4", ""], ["", "100", "SalesOrder|", "constant", "2", "SO"], ["", "200", "SalesOrder|", "year", "2", ""], ["", "300", "SalesOrder|", "sequenceNumber", "4", ""], ["", "100", "SalesReturnReceipt|", "constant", "3", "SRR"], ["", "200", "SalesReturnReceipt|", "year", "2", ""], ["", "300", "SalesReturnReceipt|", "sequenceNumber", "4", ""], ["", "100", "SalesReturnRequest|", "constant", "2", "ST"], ["", "200", "SalesReturnRequest|", "year", "2", ""], ["", "300", "SalesReturnRequest|", "sequenceNumber", "4", ""], ["", "100", "SalesShipment|", "constant", "2", "SH"], ["", "200", "SalesShipment|", "year", "2", ""], ["", "300", "SalesShipment|", "sequenceNumber", "4", ""], ["", "100", "StockAdjustment|", "constant", "3", "SA"], ["", "200", "StockAdjustment|", "year", "2", ""], ["", "300", "StockAdjustment|", "sequenceNumber", "4", ""], ["", "100", "StockChange|", "constant", "2", "SG"], ["", "200", "StockChange|", "year", "2", ""], ["", "300", "StockChange|", "sequenceNumber", "4", ""], ["", "100", "StockCount|", "constant", "2", "CS"], ["", "200", "StockCount|", "year", "2", ""], ["", "300", "StockCount|", "sequenceNumber", "4", ""], ["", "100", "StockCountList|", "constant", "2", "CL"], ["", "200", "StockCountList|", "year", "2", ""], ["", "300", "StockCountList|", "sequenceNumber", "4", ""], ["", "100", "StockCountSession|", "constant", "2", "CS"], ["", "200", "StockCountSession|", "year", "2", ""], ["", "300", "StockCountSession|", "sequenceNumber", "4", ""], ["", "100", "StockJournal|", "constant", "2", "IJ"], ["", "200", "StockJournal|", "year", "2", ""], ["", "300", "StockJournal|", "sequenceNumber", "4", ""], ["", "100", "UnbilledJournal|", "constant", "2", "UN"], ["", "200", "UnbilledJournal|", "year", "2", ""], ["", "300", "UnbilledJournal|", "sequenceNumber", "4", ""], ["", "100", "WorkInProgressJournal|", "constant", "3", "WIP"], ["", "200", "WorkInProgressJournal|", "year", "2", ""], ["", "300", "WorkInProgressJournal|", "sequenceNumber", "6", ""], ["", "100", "WorkOrder|", "constant", "2", "WO"], ["", "200", "WorkOrder|", "year", "2", ""], ["", "300", "WorkOrder|", "sequenceNumber", "4", ""], ["", "100", "WorkOrderSuggestion|", "constant", "3", "WOS"], ["", "200", "WorkOrderSuggestion|", "year", "2", ""], ["", "300", "WorkOrderSuggestion|", "sequenceNumber", "6", ""], ["", "100", "WorkOrderTracking|", "constant", "2", "WT"], ["", "200", "WorkOrderTracking|", "year", "2", ""], ["", "300", "WorkOrderTracking|", "sequenceNumber", "6", ""], ["", "100", "OD|", "constant", "2", "OD"], ["", "200", "OD|", "year", "2", ""], ["", "300", "OD|", "sequenceNumber", "4", ""], ["", "100", "ACH|", "constant", "2", "AC"], ["", "200", "ACH|", "year", "2", ""], ["", "300", "ACH|", "sequenceNumber", "4", ""], ["", "100", "VEN|", "constant", "2", "VE"], ["", "200", "VEN|", "year", "2", ""], ["", "300", "VEN|", "sequenceNumber", "4", ""], ["", "100", "PostedSalesInvoice|", "constant", "3", "SIP"], ["", "200", "PostedSalesInvoice|", "site", "2", ""], ["", "300", "PostedSalesInvoice|", "year", "2", ""], ["", "400", "PostedSalesInvoice|", "sequenceNumber", "4", ""], ["", "100", "PostedSalesCreditMemo|", "constant", "3", "SCP"], ["", "200", "PostedSalesCreditMemo|", "site", "2", ""], ["", "300", "PostedSalesCreditMemo|", "year", "2", ""], ["", "400", "PostedSalesCreditMemo|", "sequenceNumber", "4", ""], ["", "100", "SalesInvoiceUSA|US", "constant", "3", "SIT"], ["", "200", "SalesInvoiceUSA|US", "site", "2", ""], ["", "300", "SalesInvoiceUSA|US", "year", "2", ""], ["", "400", "SalesInvoiceUSA|US", "sequenceNumber", "4", ""], ["", "100", "CashDisbursementsJournal|", "constant", "3", "CDJ"], ["", "200", "CashDisbursementsJournal|", "year", "2", ""], ["", "300", "CashDisbursementsJournal|", "sequenceNumber", "4", ""], ["", "100", "CashReceiptsJournal|", "constant", "3", "CRJ"], ["", "200", "CashReceiptsJournal|", "year", "2", ""], ["", "300", "CashReceiptsJournal|", "sequenceNumber", "4", ""], ["", "100", "ArPayment|", "constant", "3", "ARP"], ["", "200", "ArPayment|", "site", "2", ""], ["", "300", "ArPayment|", "year", "2", ""], ["", "400", "ArPayment|", "sequenceNumber", "4", ""], ["", "100", "ArAdvance|", "constant", "3", "ARA"], ["", "200", "ArAdvance|", "site", "2", ""], ["", "300", "ArAdvance|", "year", "2", ""], ["", "400", "ArAdvance|", "sequenceNumber", "4", ""], ["", "100", "EREC|", "constant", "2", "AC"], ["", "200", "EREC|", "year", "2", ""], ["", "300", "EREC|", "sequenceNumber", "4", ""], ["", "100", "VREC|", "constant", "2", "VE"], ["", "200", "VREC|", "year", "2", ""], ["", "300", "VREC|", "sequenceNumber", "4", ""], ["", "100", "StockValueChange|", "constant", "3", "SVC"], ["", "200", "StockValueChange|", "year", "2", ""], ["", "300", "StockValueChange|", "sequenceNumber", "4", ""], ["", "100", "StockTransferOrder|", "constant", "2", "TO"], ["", "200", "StockTransferOrder|", "year", "2", ""], ["", "300", "StockTransferOrder|", "sequenceNumber", "4", ""], ["", "100", "SalesJournal|", "constant", "2", "SJ"], ["", "200", "SalesJournal|", "year", "2", ""], ["", "300", "SalesJournal|", "sequenceNumber", "4", ""]]}, "SequenceNumberAssignmentDocumentType": {"metadata": {"rootFactoryName": "SequenceNumberAssignmentDocumentType", "name": "SequenceNumberAssignmentDocumentType", "naturalKeyColumns": ["_tenant_id", "sequence_number_assignment_module", "node_factory"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "sequence_number_assignment_module", "type": "reference", "targetFactoryName": "SequenceNumberAssignmentModule"}, {"name": "node_factory", "type": "reference", "targetFactoryName": "MetaNodeFactory"}, {"name": "display_order", "type": "integer"}]}, "rows": [["sage", "Finance", "AccountsReceivablePayment", "400"], ["sage", "Finance", "AccountsReceivableAdvance", "400"], ["sage", "Purchasing", "PurchaseOrder", "800"], ["sage", "Purchasing", "PurchaseCreditMemo", "600"], ["sage", "Purchasing", "PurchaseInvoice", "500"], ["sage", "Purchasing", "PurchaseReceipt", "300"], ["sage", "Purchasing", "PurchaseRequisition", "100"], ["sage", "Purchasing", "PurchaseReturn", "400"], ["sage", "Sales", "SalesInvoice", "300"], ["sage", "Sales", "SalesOrder", "100"], ["sage", "Sales", "SalesShipment", "200"], ["sage", "Sales", "SalesReturnReceipt", "500"], ["sage", "Sales", "SalesReturnRequest", "400"], ["sage", "Sales", "SalesCreditMemo", "600"], ["sage", "Manufacturing", "WorkOrder", "100"], ["sage", "Manufacturing", "MaterialTracking", "300"], ["sage", "Manufacturing", "OperationTracking", "400"], ["sage", "Manufacturing", "ProductionTracking", "200"], ["sage", "Stock", "StockValueChange", "700"], ["sage", "Stock", "StockAdjustment", "200"], ["sage", "Stock", "StockCount", "600"], ["sage", "Stock", "StockChange", "400"], ["sage", "Stock", "StockIssue", "300"], ["sage", "Stock", "StockReceipt", "100"], ["sage", "Stock", "StockTransferOrder", "700"]]}, "SequenceNumberAssignment": {"metadata": {"rootFactoryName": "SequenceNumberAssignment", "name": "SequenceNumberAssignment", "naturalKeyColumns": ["_tenant_id", "sequence_number_assignment_document_type", "site", "company", "legislation", "is_assign_on_posting", "is_default_assignment", "sequence_number"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "site", "type": "reference", "isNullable": true, "targetFactoryName": "Site"}, {"name": "company", "type": "reference", "isNullable": true, "targetFactoryName": "Company"}, {"name": "legislation", "type": "reference", "isNullable": true, "targetFactoryName": "Legislation"}, {"name": "sequence_number", "type": "reference", "isOwnedByCustomer": true, "targetFactoryName": "SequenceNumber"}, {"name": "sequence_number_assignment_document_type", "type": "reference", "targetFactoryName": "SequenceNumberAssignmentDocumentType"}, {"name": "is_default_assignment", "type": "boolean"}, {"name": "is_assign_on_posting", "type": "boolean"}]}, "rows": [["sage", "", "", "", "ArPayment|", "Finance|AccountsReceivablePayment", "Y", "N"], ["sage", "", "", "", "ArAdvance|", "Finance|AccountsReceivableAdvance", "Y", "N"], ["sage", "", "", "", "PurchaseOrder|", "Purchasing|PurchaseOrder", "Y", "N"], ["sage", "", "", "", "PurchaseCreditMemo|", "Purchasing|PurchaseCreditMemo", "Y", "N"], ["sage", "", "", "", "PurchaseInvoice|", "Purchasing|PurchaseInvoice", "Y", "N"], ["sage", "", "", "", "PurchaseOrderSuggestion|", "Purchasing|PurchaseOrder", "Y", "N"], ["sage", "", "", "", "PurchaseReceipt|", "Purchasing|PurchaseReceipt", "Y", "N"], ["sage", "", "", "", "PurchaseRequisition|", "Purchasing|PurchaseRequisition", "Y", "N"], ["sage", "", "", "", "PurchaseReturn|", "Purchasing|PurchaseReturn", "Y", "N"], ["sage", "", "", "", "SalesInvoice|", "Sales|SalesInvoice", "Y", "N"], ["sage", "", "", "US", "SalesInvoiceUSA|US", "Sales|SalesInvoice", "Y", "N"], ["sage", "", "", "", "SalesOrder|", "Sales|SalesOrder", "Y", "N"], ["sage", "", "", "", "SalesShipment|", "Sales|SalesShipment", "Y", "N"], ["sage", "", "", "", "SalesReturnReceipt|", "Sales|SalesReturnReceipt", "Y", "N"], ["sage", "", "", "", "SalesReturnRequest|", "Sales|SalesReturnRequest", "Y", "N"], ["sage", "", "", "", "SalesCreditMemo|", "Sales|SalesCreditMemo", "Y", "N"], ["sage", "", "", "FR", "PostedSalesInvoice|", "Sales|SalesInvoice", "Y", "Y"], ["sage", "", "", "FR", "PostedSalesCreditMemo|", "Sales|SalesCreditMemo", "Y", "Y"], ["sage", "", "", "ZA", "PostedSalesInvoice|", "Sales|SalesInvoice", "Y", "Y"], ["sage", "", "", "ZA", "PostedSalesCreditMemo|", "Sales|SalesCreditMemo", "Y", "Y"], ["sage", "", "", "", "WorkOrder|", "Manufacturing|WorkOrder", "Y", "N"], ["sage", "", "", "", "WorkOrderTracking|", "Manufacturing|MaterialTracking", "Y", "N"], ["sage", "", "", "", "WorkOrderTracking|", "Manufacturing|OperationTracking", "Y", "N"], ["sage", "", "", "", "WorkOrderTracking|", "Manufacturing|ProductionTracking", "Y", "N"], ["sage", "", "", "", "StockAdjustment|", "Stock|StockAdjustment", "Y", "N"], ["sage", "", "", "", "StockCount|", "Stock|StockCount", "Y", "N"], ["sage", "", "", "", "StockChange|", "Stock|StockChange", "Y", "N"], ["sage", "", "", "", "MiscStockIssue|", "Stock|StockIssue", "Y", "N"], ["sage", "", "", "", "MiscStockReceipt|", "Stock|StockReceipt", "Y", "N"], ["sage", "", "", "", "StockValueChange|", "Stock|StockValueChange", "Y", "N"], ["sage", "", "", "", "StockTransferOrder|", "Stock|StockTransferOrder", "Y", "N"]]}}}