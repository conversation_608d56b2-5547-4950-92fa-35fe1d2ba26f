{"fromVersion": "55.0.13", "toVersion": "55.0.14", "gitHead": "06e70b1cd57e09762949c08e204507ab1e5c401f", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='sequence_counter_definition_level_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.sequence_counter_definition_level_enum AS ENUM('application','company','site');", "                        END IF;", "                    END", "                $$;", "                 ", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='sequence_number_reset_frequency_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.sequence_number_reset_frequency_enum AS ENUM('noReset','yearly','monthly');", "                        END IF;", "                    END", "                $$;", "                 ", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='sequence_number_type_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.sequence_number_type_enum AS ENUM('alphanumeric','numeric');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.base_sequence_number (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, _constructor VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, id VARCHAR(24) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, name JSONB NOT NULL, definition_level %%SCHEMA_NAME%%.sequence_counter_definition_level_enum NOT NULL, rtz_level %%SCHEMA_NAME%%.sequence_number_reset_frequency_enum NOT NULL, is_cleared_by_reset BOOL NOT NULL, type %%SCHEMA_NAME%%.sequence_number_type_enum NOT NULL, is_chronological BOOL NOT NULL, legislation INT8, _vendor INT8, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"base_sequence_number_PK\" PRIMARY KEY(_tenant_id,_id));CREATE UNIQUE INDEX base_sequence_number_ind0 ON %%SCHEMA_NAME%%.base_sequence_number(_tenant_id ASC,_constructor ASC,id ASC,COALESCE(legislation, (0)::bigint) ASC);", "COMMENT ON TABLE %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"isSharedByAllTenants\": false,", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"_constructor\",", "    \"id\",", "    \"legislation\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._constructor IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.id IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 24", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.name IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"isLocalized\": true,", "  \"maxLength\": 4000", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.definition_level IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"sequence_counter_definition_level_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.rtz_level IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"sequence_number_reset_frequency_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.is_cleared_by_reset IS '{", "  \"type\": \"boolean\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"sequence_number_type_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.is_chronological IS '{", "  \"type\": \"boolean\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number.legislation IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"legislation\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._vendor IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"sys_vendor\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.base_sequence_number", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.base_sequence_number", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.base_sequence_number to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.base_sequence_number__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.range_sequence_number (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id INT8 NOT NULL, _constructor VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"range_sequence_number_PK\" PRIMARY KEY(_tenant_id,_id));", "COMMENT ON TABLE %%SCHEMA_NAME%%.range_sequence_number IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_sequence_number\",", "  \"rootTable\": \"base_sequence_number\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"_constructor\",", "    \"id\",", "    \"legislation\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_number._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_number._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_number._constructor IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_number._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.range_sequence_number_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_sequence_number WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.range_sequence_number", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.range_sequence_number_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.range_sequence_number to xtrem"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN name DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN definition_level DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN rtz_level DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN is_cleared_by_reset DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN type DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN is_chronological DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _create_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _update_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _create_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _update_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _update_tick DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _source_id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number DROP CONSTRAINT IF EXISTS sequence_number_legislation_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number DROP CONSTRAINT IF EXISTS sequence_number_legislation_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number DROP CONSTRAINT IF EXISTS sequence_number__vendor_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number DROP CONSTRAINT IF EXISTS sequence_number__vendor_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number DROP CONSTRAINT IF EXISTS sequence_number__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number DROP CONSTRAINT IF EXISTS sequence_number__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number DROP CONSTRAINT IF EXISTS sequence_number__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number DROP CONSTRAINT IF EXISTS sequence_number__update_user_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sequence_number_ind0;"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.bom_revision_sequence (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id INT8 NOT NULL, _custom_data JSONB,CONSTRAINT \"bom_revision_sequence_PK\" PRIMARY KEY(_tenant_id,_id));", "COMMENT ON TABLE %%SCHEMA_NAME%%.bom_revision_sequence IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"range_sequence_number\",", "  \"rootTable\": \"base_sequence_number\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"id\",", "    \"legislation\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bom_revision_sequence._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bom_revision_sequence._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bom_revision_sequence._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.bom_revision_sequence_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.range_sequence_number WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.bom_revision_sequence", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.bom_revision_sequence_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.bom_revision_sequence to xtrem"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN name DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _create_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _update_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _create_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _update_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _update_tick DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _source_id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number DROP CONSTRAINT IF EXISTS location_sequence__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.range_sequence_number DROP CONSTRAINT IF EXISTS location_sequence__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence DROP CONSTRAINT IF EXISTS location_sequence__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number DROP CONSTRAINT IF EXISTS location_sequence__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.range_sequence_number DROP CONSTRAINT IF EXISTS location_sequence__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence DROP CONSTRAINT IF EXISTS location_sequence__update_user_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.location_sequence_ind0;"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='base_sequence_number_component_type_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.base_sequence_number_component_type_enum AS ENUM('constant','year','month','week','day','company','site','sequenceNumber','sequenceAlpha');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.base_sequence_number_component (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, _constructor VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _sort_value INT8 DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.base_sequence_number_component'::text, '_id'::text))::regclass) * 100) NOT NULL, sequence_number INT8 NOT NULL, type %%SCHEMA_NAME%%.base_sequence_number_component_type_enum NOT NULL, constant VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, length INT4 NOT NULL, _vendor INT8, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"base_sequence_number_component_PK\" PRIMARY KEY(_tenant_id,_id));CREATE UNIQUE INDEX base_sequence_number_component_ind0 ON %%SCHEMA_NAME%%.base_sequence_number_component(_tenant_id ASC,sequence_number ASC,_sort_value ASC);", "COMMENT ON TABLE %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"isSharedByAllTenants\": false,", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"sequenceNumber\",", "    \"_sortValue\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._constructor IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._sort_value IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component.sequence_number IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"base_sequence_number\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component.type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"base_sequence_number_component_type_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component.constant IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component.length IS '{", "  \"type\": \"short\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._vendor IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"sys_vendor\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_sequence_number_component._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.base_sequence_number_component", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.base_sequence_number_component", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.base_sequence_number_component to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.base_sequence_number_component__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.range_sequence_component (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id INT8 NOT NULL, _constructor VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _sort_value INT8 DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.base_sequence_number_component'::text, '_id'::text))::regclass) * 100) NOT NULL, start_value VARCHAR(10) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, end_value VARCHAR(10) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"range_sequence_component_PK\" PRIMARY KEY(_tenant_id,_id));", "COMMENT ON TABLE %%SCHEMA_NAME%%.range_sequence_component IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_sequence_number_component\",", "  \"rootTable\": \"base_sequence_number_component\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"sequenceNumber\",", "    \"_sortValue\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_component._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_component._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_component._constructor IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_component._sort_value IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_component.start_value IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 10", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_component.end_value IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 10", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.range_sequence_component._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.range_sequence_component_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_sequence_number_component WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.range_sequence_component", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.range_sequence_component_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.range_sequence_component to xtrem"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN sequence_number DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN type DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN length DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN constant DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _create_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _update_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _create_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _update_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _update_tick DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _source_id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _sort_value SET DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.sequence_number_component'::text, '_id'::text))::regclass) * 100);"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component_sequence_number_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component_sequence_number_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component__vendor_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component__vendor_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component DROP CONSTRAINT IF EXISTS sequence_number_component__update_user_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sequence_number_component_ind0;"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.bom_revision_sequence_component (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id INT8 NOT NULL, _sort_value INT8 DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.base_sequence_number_component'::text, '_id'::text))::regclass) * 100) NOT NULL, _custom_data JSONB,CONSTRAINT \"bom_revision_sequence_component_PK\" PRIMARY KEY(_tenant_id,_id));", "COMMENT ON TABLE %%SCHEMA_NAME%%.bom_revision_sequence_component IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"range_sequence_component\",", "  \"rootTable\": \"base_sequence_number_component\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"sequenceNumber\",", "    \"_sortValue\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bom_revision_sequence_component._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bom_revision_sequence_component._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bom_revision_sequence_component._sort_value IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bom_revision_sequence_component._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.bom_revision_sequence_component_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.range_sequence_component WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.bom_revision_sequence_component", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.bom_revision_sequence_component_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.bom_revision_sequence_component to xtrem"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN location_sequence DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN type DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN constant DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN length DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN start_value DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN end_value DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _create_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _update_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _create_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _update_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _update_tick DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _source_id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _sort_value SET DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.location_sequence_component'::text, '_id'::text))::regclass) * 100);"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component DROP CONSTRAINT IF EXISTS location_sequence_component_location_sequence_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.range_sequence_component DROP CONSTRAINT IF EXISTS location_sequence_component_location_sequence_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component DROP CONSTRAINT IF EXISTS location_sequence_component_location_sequence_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component DROP CONSTRAINT IF EXISTS location_sequence_component__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.range_sequence_component DROP CONSTRAINT IF EXISTS location_sequence_component__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component DROP CONSTRAINT IF EXISTS location_sequence_component__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component DROP CONSTRAINT IF EXISTS location_sequence_component__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.range_sequence_component DROP CONSTRAINT IF EXISTS location_sequence_component__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component DROP CONSTRAINT IF EXISTS location_sequence_component__update_user_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.location_sequence_component_ind0;"}, {"isSysPool": true, "sql": ["", "             DO $$", "             DECLARE", "                pRecord RECORD;", "                last_id %%SCHEMA_NAME%%.base_sequence_number._id%TYPE :=1;", "", "                start_time TIMESTAMP;", "                end_time TIMESTAMP;", "                elapsed_time_base_sequence_insert BIGINT :=0;", "                elapsed_time_sequence_number BIGINT :=0;", "", "                number_base_sequence BIGINT :=0;", "", "            BEGIN", "                RAISE WARNING 'Create BaseSequenceNumber for SequenceNumber started';", "", "                FOR pRecord IN ( SELECT sn._id, sn._tenant_id, sn._create_user, sn._create_stamp,", "                    sn._update_user, sn._update_stamp, sn._vendor, sn._source_id,", "                    sn.id, sn.name,  sn.definition_level, sn.rtz_level, sn.is_cleared_by_reset, sn.type, sn.is_chronological, sn.legislation", "                    FROM %%SCHEMA_NAME%%.sequence_number sn)", "", "                LOOP", "                    start_time := clock_timestamp();", "                    INSERT INTO %%SCHEMA_NAME%%.base_sequence_number(_constructor, _tenant_id, _id, _create_user, _create_stamp,", "                     _update_user, _update_stamp, _vendor, _source_id,", "                        id, name,  definition_level, rtz_level, is_cleared_by_reset, type, is_chronological, legislation)", "", "                        VALUES ( 'SequenceNumber' , pRecord._tenant_id, pRecord._id, pRecord._create_user, pRecord._create_stamp,", "                        pRecord._update_user, pRecord._update_stamp, pRecord._vendor, pRecord._source_id,", "                        pRecord.id, pRecord.name,  pRecord.definition_level, pRecord.rtz_level, pRecord.is_cleared_by_reset, pRecord.type, pRecord.is_chronological, pRecord.legislation);", "                    end_time := clock_timestamp();", "                    elapsed_time_base_sequence_insert := elapsed_time_base_sequence_insert + (extract(milliseconds from end_time - start_time));", "                    number_base_sequence := number_base_sequence + 1;", "", "                END LOOP;", "", "                -- We are setting the sequence to the max id of the base_sequence_number_component if it is greater than the current sequence value", "                last_id  = (SELECT COALESCE(MAX(_id), 0) FROM %%SCHEMA_NAME%%.base_sequence_number);", "                IF last_id > COALESCE((SELECT last_value FROM %%SCHEMA_NAME%%.base_sequence_number__id_seq) , 0) THEN", "                    RAISE WARNING 'new base_sequence_number__id_seq value: %', last_id+1;", "                    EXECUTE 'ALTER SEQUENCE %%SCHEMA_NAME%%.base_sequence_number__id_seq RESTART WITH ' || last_id+1;", "                    RAISE WARNING 'ALTER done';", "                END IF;", "", "                RAISE WARNING 'Create BaseSequenceNumber for SequenceNumber completed';", "                RAISE WARNING 'Elapsed time for base_sequence_number insert: %', elapsed_time_base_sequence_insert;", "                RAISE WARNING 'Number of base_sequence_number inserted: %', number_base_sequence;", "", "            END $$;", "        "], "actionDescription": "Upgrade SequenceNumber to BaseSequenceNumber"}, {"isSysPool": true, "sql": ["", "             DO $$", "                DECLARE", "                    pRecord RECORD;", "                    base_id %%SCHEMA_NAME%%.base_sequence_number._id%TYPE :=1;", "                    start_id %%SCHEMA_NAME%%.base_sequence_number._id%TYPE :=1;", "", "                    start_time TIMESTAMP;", "                    end_time TIMESTAMP;", "                    elapsed_time_base_sequence_insert BIGINT :=0;", "                    elapsed_time_range_sequence_number_insert BIGINT :=0;", "                    elapsed_time_location_sequence BIGINT :=0;", "                    elapsed_time_location_sequence_component BIGINT :=0;", "", "                    interval_time TIMESTAMP;", "                    interval_time_end TIMESTAMP;", "", "                    nb_update BIGINT :=0;", "                    number_base_sequence BIGINT :=0;", "                    number_sequence_component_updated BIGINT :=0;", "", "                BEGIN", "                    start_time := clock_timestamp();", "                    -- There we are dropping the constraints to avoid the constraint violation", "                    SET CONSTRAINTS ALL DEFERRED;", "", "                    start_id  = (SELECT COALESCE(MAX(_id), 0) FROM %%SCHEMA_NAME%%.location_sequence);", "", "                    -- We are setting the sequence to the max id of the location_sequence if it is greater than the current sequence value", "                    IF start_id > COALESCE((SELECT last_value FROM %%SCHEMA_NAME%%.base_sequence_number__id_seq) , 0) THEN", "                        RAISE WARNING 'new base_sequence_number__id_seq value: %', start_id+1;", "                        EXECUTE 'ALTER SEQUENCE %%SCHEMA_NAME%%.base_sequence_number__id_seq RESTART WITH ' || start_id+1;", "                        RAISE WARNING 'ALTER done';", "                    END IF;", "                    end_time := clock_timestamp();", "", "                    RAISE WARNING 'Elapsed time for setting sequence and deferred: %', (extract(milliseconds from end_time - start_time));", "", "                    RAISE WARNING 'Upgrade LocationSequence to BaseSequenceNumber started';", "", "                    FOR pRecord IN ( SELECT ls._id, ls._tenant_id, ls._create_user, ls._create_stamp, ls._update_user, ls._update_stamp, ls._source_id,", "                        ls.id, ls.name", "                        FROM %%SCHEMA_NAME%%.location_sequence ls)", "", "                    LOOP", "", "                        start_time := clock_timestamp();", "                        INSERT INTO %%SCHEMA_NAME%%.base_sequence_number(_constructor, _tenant_id, _create_user, _create_stamp,", "                            _update_user, _update_stamp, _vendor, _source_id,", "                            id, name,  definition_level, rtz_level, is_cleared_by_reset, type, is_chronological, legislation)", "", "                            VALUES ( 'LocationSequence' , pRecord._tenant_id, pRecord._create_user, pRecord._create_stamp,", "                            pRecord._update_user, pRecord._update_stamp, NULL, pRecord._source_id,", "                            pRecord.id,", "                            jsonb_build_object('en', pRecord.name, '', pRecord.name),", "                            CAST('application' AS %%SCHEMA_NAME%%.sequence_counter_definition_level_enum),", "                            CAST('noReset' AS %%SCHEMA_NAME%%.sequence_number_reset_frequency_enum),", "                            false,", "                            CAST('alphanumeric' AS %%SCHEMA_NAME%%.sequence_number_type_enum),", "                            false,", "                            NULL)", "                            RETURNING _id INTO base_id;", "                        end_time := clock_timestamp();", "                        elapsed_time_base_sequence_insert := elapsed_time_base_sequence_insert + (extract(milliseconds from end_time - start_time));", "                        number_base_sequence := number_base_sequence + 1;", "", "", "                        start_time := clock_timestamp();", "                        INSERT INTO %%SCHEMA_NAME%%.range_sequence_number(_constructor, _id, _tenant_id)", "", "                            VALUES ( 'LocationSequence' , base_id, pRecord._tenant_id);", "                        end_time := clock_timestamp();", "                        elapsed_time_range_sequence_number_insert := elapsed_time_range_sequence_number_insert + (extract(milliseconds from end_time - start_time));", "", "", "                        start_time := clock_timestamp();", "                        UPDATE %%SCHEMA_NAME%%.location_sequence SET _id = base_id", "                            WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id ;", "                        end_time := clock_timestamp();", "                        elapsed_time_location_sequence := elapsed_time_location_sequence + (extract(milliseconds from end_time - start_time));", "", "                        start_time := clock_timestamp();", "                        UPDATE %%SCHEMA_NAME%%.location_sequence_component SET location_sequence = base_id", "                            WHERE location_sequence = pRecord._id AND _tenant_id = pRecord._tenant_id;", "                        end_time := clock_timestamp();", "                        GET DIAGNOSTICS nb_update = ROW_COUNT;", "                        number_sequence_component_updated := number_sequence_component_updated + nb_update;", "                        elapsed_time_location_sequence_component := elapsed_time_location_sequence_component + (extract(milliseconds from end_time - start_time));", "", "                    END LOOP;", "", "                    RAISE WARNING 'LocationSequence upgrade completed  ';", "                    RAISE WARNING 'Elapsed time for base_sequence_number insert: %', elapsed_time_base_sequence_insert;", "                    RAISE WARNING 'Number of base_sequence_number inserted: %', number_base_sequence;", "                    RAISE WARNING 'Elapsed time for location_sequence update: %', elapsed_time_location_sequence;", "                    RAISE WARNING 'Elapsed time for location_sequence_component update: %', elapsed_time_location_sequence_component;", "                    RAISE WARNING 'Number of location_sequence_component updated: %', number_sequence_component_updated;", "                END $$;", "        "], "actionDescription": "Upgrade LocationSequence to BaseSequenceNumber"}, {"isSysPool": true, "sql": ["", "                DO $$", "                DECLARE", "                    pRecord RECORD;", "                    last_id %%SCHEMA_NAME%%.base_sequence_number_component._id%TYPE :=1;", "", "                    start_time TIMESTAMP;", "                    end_time TIMESTAMP;", "                    elapsed_time_base_sequence_number_component_insert BIGINT :=0;", "                    elapsed_time_sequence_number_component BIGINT :=0;", "", "                    number_base_sequence_number_component_insert BIGINT :=0;", "", "                BEGIN", "                    RAISE WARNING 'Upgrade SequenceNumberComponent to BaseSequenceNumberComponent started';", "", "                    FOR pRecord IN ( SELECT snc._id, snc._tenant_id, snc._sort_value, snc._create_user,", "                        snc._create_stamp, snc._update_user, snc._update_stamp, snc._vendor, snc._source_id,", "                        snc.sequence_number, snc.type,  snc.length, snc.constant", "                        FROM %%SCHEMA_NAME%%.sequence_number_component snc)", "", "                    LOOP", "", "                        start_time := clock_timestamp();", "                        INSERT INTO %%SCHEMA_NAME%%.base_sequence_number_component (_constructor, _tenant_id, _id, _sort_value, _create_user,", "                            _create_stamp, _update_user, _update_stamp, _vendor, _source_id,", "                            sequence_number, type, length, constant)", "                        VALUES ( 'SequenceNumberComponent' , pRecord._tenant_id, pRecord._id, pRecord._sort_value, pRecord._create_user,", "                            pRecord._create_stamp, pRecord._update_user, pRecord._update_stamp, pRecord._vendor, pRecord._source_id,", "                            pRecord.sequence_number,", "                            pRecord.type::text::%%SCHEMA_NAME%%.base_sequence_number_component_type_enum,", "                            pRecord.length,", "                            pRecord.constant);", "                        end_time := clock_timestamp();", "                        elapsed_time_base_sequence_number_component_insert := elapsed_time_base_sequence_number_component_insert + (extract(milliseconds from end_time - start_time));", "                        number_base_sequence_number_component_insert := number_base_sequence_number_component_insert + 1;", "                    END LOOP;", "", "                    -- We are setting the sequence to the max id of the base_sequence_number_component if it is greater than the current sequence value", "                    last_id  = (SELECT COALESCE(MAX(_id), 0) FROM %%SCHEMA_NAME%%.base_sequence_number_component);", "                    IF last_id > COALESCE((SELECT last_value FROM %%SCHEMA_NAME%%.base_sequence_number_component__id_seq) , 0) THEN", "                        RAISE WARNING 'new base_sequence_number_component__id_seq value: %', last_id+1;", "                        EXECUTE 'ALTER SEQUENCE %%SCHEMA_NAME%%.base_sequence_number_component__id_seq RESTART WITH ' || last_id+1;", "                        RAISE WARNING 'ALTER done';", "                    END IF;", "", "                    RAISE WARNING 'Upgrade SequenceNumberComponent to BaseSequenceNumberComponent completed';", "                    RAISE WARNING 'Elapsed time for base_sequence_number_component insert: %', elapsed_time_base_sequence_number_component_insert;", "                    RAISE WARNING 'Number of base_sequence_number_component inserted: %', number_base_sequence_number_component_insert;", "", "                END $$;", "            "], "actionDescription": "Upgrade SequenceNumberComponent to BaseSequenceNumberComponent"}, {"isSysPool": true, "sql": ["", "                DO $$", "                DECLARE", "                    pRecord RECORD;", "                    base_id %%SCHEMA_NAME%%.base_sequence_number_component._id%TYPE :=1;", "                    start_id %%SCHEMA_NAME%%.base_sequence_number_component._id%TYPE :=1;", "", "                    start_time TIMESTAMP;", "                    end_time TIMESTAMP;", "                    elapsed_time_base_sequence_number_component_insert BIGINT :=0;", "                    elapsed_time_range_sequence_component_insert BIGINT :=0;", "                    elapsed_time_location_sequence_component BIGINT :=0;", "", "                    interval_time TIMESTAMP;", "                    interval_time_end TIMESTAMP;", "", "                    nb_update BIGINT :=0;", "                    number_base_sequence_number_component_insert BIGINT :=0;", "                    number_sequence_component_updated BIGINT :=0;", "", "                BEGIN", "", "                    start_time := clock_timestamp();", "                    -- There we are dropping the constraints to avoid the constraint violation", "                    SET CONSTRAINTS ALL DEFERRED;", "", "                    start_id  = (SELECT COALESCE(MAX(_id), 0) FROM %%SCHEMA_NAME%%.location_sequence_component);", "", "                    -- We are setting the sequence to the max id of the location_sequence_component if it is greater than the current sequence value", "                    IF start_id > COALESCE((SELECT last_value FROM %%SCHEMA_NAME%%.base_sequence_number_component__id_seq) , 0) THEN", "                        RAISE WARNING 'new base_sequence_number_component__id_seq value: %', start_id+1;", "                        EXECUTE 'ALTER SEQUENCE %%SCHEMA_NAME%%.base_sequence_number_component__id_seq RESTART WITH ' || start_id+1;", "                        RAISE WARNING 'ALTER done';", "                    END IF;", "                    end_time := clock_timestamp();", "", "                    RAISE WARNING 'Elapsed time for setting sequence and deferred: %', (extract(milliseconds from end_time - start_time));", "", "                    RAISE WARNING 'Upgrade SequenceNumberComponent to BaseSequenceNumberComponent started';", "", "                    FOR pRecord IN ( SELECT lsc._id, lsc._tenant_id, lsc._sort_value, lsc._create_user,", "                        lsc._create_stamp, lsc._update_user, lsc._update_stamp, lsc._source_id,", "                        lsc.location_sequence, lsc.type,  lsc.length, lsc.constant, lsc.start_value, lsc.end_value", "                        FROM %%SCHEMA_NAME%%.location_sequence_component lsc)", "", "                    LOOP", "                        start_time := clock_timestamp();", "                        INSERT INTO %%SCHEMA_NAME%%.base_sequence_number_component (_constructor, _tenant_id, _sort_value, _create_user,", "                            _create_stamp, _update_user, _update_stamp, _vendor, _source_id,", "                            sequence_number, type, length, constant)", "", "                        VALUES ( 'LocationSequenceComponent' , pRecord._tenant_id, pRecord._sort_value, pRecord._create_user,", "                            pRecord._create_stamp, pRecord._update_user, pRecord._update_stamp, NULL, pRecord._source_id,", "                            pRecord.location_sequence,", "                            CASE  WHEN pRecord.type='numerical' THEN 'sequenceNumber'::text::%%SCHEMA_NAME%%.base_sequence_number_component_type_enum", "                                WHEN pRecord.type='alphabetical' THEN 'sequenceAlpha'::text::%%SCHEMA_NAME%%.base_sequence_number_component_type_enum", "                                ELSE 'constant'::text::%%SCHEMA_NAME%%.base_sequence_number_component_type_enum", "                            END,", "                            pRecord.length,", "                            pRecord.constant)", "                        RETURNING _id INTO base_id;", "                        end_time := clock_timestamp();", "                        elapsed_time_base_sequence_number_component_insert := elapsed_time_base_sequence_number_component_insert + (extract(milliseconds from end_time - start_time));", "                        number_base_sequence_number_component_insert := number_base_sequence_number_component_insert + 1;", "", "                        start_time := clock_timestamp();", "                        INSERT INTO %%SCHEMA_NAME%%.range_sequence_component (_constructor, _id, _tenant_id, _sort_value,", "                            start_value, end_value)", "", "                        VALUES ( 'LocationSequenceComponent' , base_id, pRecord._tenant_id, pRecord._sort_value,", "                            pRecord.start_value, pRecord.end_value);", "                        end_time := clock_timestamp();", "                        elapsed_time_range_sequence_component_insert := elapsed_time_range_sequence_component_insert + (extract(milliseconds from end_time - start_time));", "", "                        start_time := clock_timestamp();", "                        UPDATE %%SCHEMA_NAME%%.location_sequence_component SET _id = base_id", "                            WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id ;", "                        end_time := clock_timestamp();", "                        GET DIAGNOSTICS nb_update = ROW_COUNT;", "                        number_sequence_component_updated := number_sequence_component_updated + nb_update;", "                        elapsed_time_location_sequence_component := elapsed_time_location_sequence_component + (extract(milliseconds from end_time - start_time));", "", "                    END LOOP;", "", "                    RAISE WARNING 'Upgrade SequenceNumberComponent to BaseSequenceNumberComponent completed';", "                    RAISE WARNING 'Elapsed time for base_sequence_number_component insert: %', elapsed_time_base_sequence_number_component_insert;", "                    RAISE WARNING 'Number of base_sequence_number_component inserted: %', number_base_sequence_number_component_insert;", "                    RAISE WARNING 'Elapsed time for range_sequence_component insert: %', elapsed_time_range_sequence_component_insert;", "                    RAISE WARNING 'Elapsed time for location_sequence_component update: %', elapsed_time_location_sequence_component;", "                    RAISE WARNING 'Number of location_sequence_component updated: %', number_sequence_component_updated;", "", "                END $$;", "            "], "actionDescription": "Upgrade SequenceNumberComponent to BaseSequenceNumberComponent"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _id DROP DEFAULT", "actionDescription": "LocationSequenceComponent: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "DROP SEQUENCE IF EXISTS %%SCHEMA_NAME%%.location_sequence_component__id_seq", "actionDescription": "LocationSequenceComponent: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _id TYPE INT8;COMMENT ON COLUMN %%SCHEMA_NAME%%.location_sequence_component._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;"], "actionDescription": "LocationSequenceComponent: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _id DROP DEFAULT", "actionDescription": "SequenceNumberComponent: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "DROP SEQUENCE IF EXISTS %%SCHEMA_NAME%%.sequence_number_component__id_seq", "actionDescription": "SequenceNumberComponent: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _id TYPE INT8;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number_component._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;"], "actionDescription": "SequenceNumberComponent: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _id DROP DEFAULT", "actionDescription": "LocationSequence: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "DROP SEQUENCE IF EXISTS %%SCHEMA_NAME%%.location_sequence__id_seq", "actionDescription": "LocationSequence: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _id TYPE INT8;COMMENT ON COLUMN %%SCHEMA_NAME%%.location_sequence._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;"], "actionDescription": "LocationSequence: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _id DROP DEFAULT", "actionDescription": "SequenceNumber: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "DROP SEQUENCE IF EXISTS %%SCHEMA_NAME%%.sequence_number__id_seq", "actionDescription": "SequenceNumber: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _id TYPE INT8;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;"], "actionDescription": "SequenceNumber: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS insert_table ON %%SCHEMA_NAME%%.sequence_number;", "DROP TRIGGER IF EXISTS update_table ON %%SCHEMA_NAME%%.sequence_number;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.sequence_number_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_sequence_number WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.sequence_number", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.sequence_number_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS insert_table ON %%SCHEMA_NAME%%.location_sequence;", "DROP TRIGGER IF EXISTS update_table ON %%SCHEMA_NAME%%.location_sequence;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.location_sequence_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.range_sequence_number WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.location_sequence", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.location_sequence_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS insert_table ON %%SCHEMA_NAME%%.sequence_number_component;", "DROP TRIGGER IF EXISTS update_table ON %%SCHEMA_NAME%%.sequence_number_component;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.sequence_number_component_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_sequence_number_component WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.sequence_number_component", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.sequence_number_component_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS insert_table ON %%SCHEMA_NAME%%.location_sequence_component;", "DROP TRIGGER IF EXISTS update_table ON %%SCHEMA_NAME%%.location_sequence_component;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.location_sequence_component_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.range_sequence_component WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.location_sequence_component", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.location_sequence_component_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ADD CONSTRAINT \"location_sequence_component__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.range_sequence_component(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT location_sequence_component__id_fk ON %%SCHEMA_NAME%%.location_sequence_component IS '{", "  \"targetTableName\": \"range_sequence_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component DROP COLUMN IF EXISTS location_sequence, DROP COLUMN IF EXISTS type, DROP COLUMN IF EXISTS constant, DROP COLUMN IF EXISTS length, DROP COLUMN IF EXISTS start_value, DROP COLUMN IF EXISTS end_value, DROP COLUMN IF EXISTS _create_user, DROP COLUMN IF EXISTS _update_user, DROP COLUMN IF EXISTS _create_stamp, DROP COLUMN IF EXISTS _update_stamp, DROP COLUMN IF EXISTS _update_tick, DROP COLUMN IF EXISTS _source_id;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _sort_value SET DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.base_sequence_number_component'::text, '_id'::text))::regclass) * 100);"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.bom_revision_sequence_component ADD CONSTRAINT \"bom_revision_sequence_component__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.range_sequence_component(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT bom_revision_sequence_component__id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence_component IS '{", "  \"targetTableName\": \"range_sequence_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.bom_revision_sequence_component ADD CONSTRAINT \"bom_revision_sequence_component__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT bom_revision_sequence_component__tenant_id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence_component IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ADD CONSTRAINT \"sequence_number_component__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_sequence_number_component(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sequence_number_component__id_fk ON %%SCHEMA_NAME%%.sequence_number_component IS '{", "  \"targetTableName\": \"base_sequence_number_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component DROP COLUMN IF EXISTS sequence_number, DROP COLUMN IF EXISTS type, DROP COLUMN IF EXISTS length, DROP COLUMN IF EXISTS constant, DROP COLUMN IF EXISTS _vendor, DROP COLUMN IF EXISTS _create_user, DROP COLUMN IF EXISTS _update_user, DROP COLUMN IF EXISTS _create_stamp, DROP COLUMN IF EXISTS _update_stamp, DROP COLUMN IF EXISTS _update_tick, DROP COLUMN IF EXISTS _source_id;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _sort_value SET DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.base_sequence_number_component'::text, '_id'::text))::regclass) * 100);"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.range_sequence_component ADD CONSTRAINT \"range_sequence_component__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_sequence_number_component(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT range_sequence_component__id_fk ON %%SCHEMA_NAME%%.range_sequence_component IS '{", "  \"targetTableName\": \"base_sequence_number_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.range_sequence_component ADD CONSTRAINT \"range_sequence_component__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT range_sequence_component__tenant_id_fk ON %%SCHEMA_NAME%%.range_sequence_component IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component ADD CONSTRAINT \"base_sequence_number_component__vendor_fk\" FOREIGN KEY(_vendor) REFERENCES %%SCHEMA_NAME%%.sys_vendor(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number_component__vendor_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component ADD CONSTRAINT \"base_sequence_number_component__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number_component__update_user_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component ADD CONSTRAINT \"base_sequence_number_component__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number_component__create_user_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component ADD CONSTRAINT \"base_sequence_number_component_sequence_number_fk\" FOREIGN KEY(_tenant_id,sequence_number) REFERENCES %%SCHEMA_NAME%%.base_sequence_number(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number_component_sequence_number_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"base_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"sequence_number\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number_component ADD CONSTRAINT \"base_sequence_number_component__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number_component__tenant_id_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.location_sequence ADD CONSTRAINT \"location_sequence__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.range_sequence_number(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT location_sequence__id_fk ON %%SCHEMA_NAME%%.location_sequence IS '{", "  \"targetTableName\": \"range_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.location_sequence DROP COLUMN IF EXISTS id, DROP COLUMN IF EXISTS name, DROP COLUMN IF EXISTS _create_user, DROP COLUMN IF EXISTS _update_user, DROP COLUMN IF EXISTS _create_stamp, DROP COLUMN IF EXISTS _update_stamp, DROP COLUMN IF EXISTS _update_tick, DROP COLUMN IF EXISTS _source_id;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.bom_revision_sequence ADD CONSTRAINT \"bom_revision_sequence__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.range_sequence_number(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT bom_revision_sequence__id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence IS '{", "  \"targetTableName\": \"range_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.bom_revision_sequence ADD CONSTRAINT \"bom_revision_sequence__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT bom_revision_sequence__tenant_id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sequence_number ADD CONSTRAINT \"sequence_number__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_sequence_number(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sequence_number__id_fk ON %%SCHEMA_NAME%%.sequence_number IS '{", "  \"targetTableName\": \"base_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number DROP COLUMN IF EXISTS id, DROP COLUMN IF EXISTS name, DROP COLUMN IF EXISTS definition_level, DROP COLUMN IF EXISTS rtz_level, DROP COLUMN IF EXISTS is_cleared_by_reset, DROP COLUMN IF EXISTS type, DROP COLUMN IF EXISTS is_chronological, DROP COLUMN IF EXISTS legislation, DROP COLUMN IF EXISTS _vendor, DROP COLUMN IF EXISTS _create_user, DROP COLUMN IF EXISTS _update_user, DROP COLUMN IF EXISTS _create_stamp, DROP COLUMN IF EXISTS _update_stamp, DROP COLUMN IF EXISTS _update_tick, DROP COLUMN IF EXISTS _source_id;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.range_sequence_number ADD CONSTRAINT \"range_sequence_number__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_sequence_number(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT range_sequence_number__id_fk ON %%SCHEMA_NAME%%.range_sequence_number IS '{", "  \"targetTableName\": \"base_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.range_sequence_number ADD CONSTRAINT \"range_sequence_number__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT range_sequence_number__tenant_id_fk ON %%SCHEMA_NAME%%.range_sequence_number IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number ADD CONSTRAINT \"base_sequence_number__vendor_fk\" FOREIGN KEY(_vendor) REFERENCES %%SCHEMA_NAME%%.sys_vendor(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number__vendor_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number ADD CONSTRAINT \"base_sequence_number__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number__update_user_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number ADD CONSTRAINT \"base_sequence_number__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number__create_user_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number ADD CONSTRAINT \"base_sequence_number_legislation_fk\" FOREIGN KEY(_tenant_id,legislation) REFERENCES %%SCHEMA_NAME%%.legislation(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number_legislation_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"legislation\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"legislation\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_sequence_number ADD CONSTRAINT \"base_sequence_number__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_sequence_number__tenant_id_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentification feature", "experimental", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tags (not yet released)", "experimental", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "experimental", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "experimental", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "workInProgress", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Open item page display", "released", true, "@sage/xtrem-structure", false, "openItemPageOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Bill of material revision", "workInProgress", false, "@sage/xtrem-master-data", false, "billOfMaterialRevisionServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "released", false, "@sage/xtrem-master-data", true, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "landedCostStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "released", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Discount payment tracking option", "workInProgress", false, "@sage/xtrem-finance-data", false, "discountPaymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Payment tracking option", "released", false, "@sage/xtrem-finance-data", false, "paymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation option", "released", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "workInProgress", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "workInProgress", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Network", "workInProgress", false, "@sage/xtrem-sage-network", true, "sageNetworkOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00KMIL-75964\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona, operator_id", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories Role,RoleActivity,SequenceNumber,SysNodeTransformation,SequenceNumberComponent,SysEnumTransformation,SysNodeMapping,SysEnumMapping"}, {"action": "reload_setup_data", "args": {"factory": "Role"}}, {"action": "reload_setup_data", "args": {"factory": "RoleActivity"}}, {"action": "reload_setup_data", "args": {"factory": "SequenceNumber"}}, {"action": "reload_setup_data", "args": {"factory": "SequenceNumberComponent"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT base_sequence_number__vendor_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number__update_user_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number__create_user_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number_legislation_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"legislation\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"legislation\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number__tenant_id_fk ON %%SCHEMA_NAME%%.base_sequence_number IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT range_sequence_number__id_fk ON %%SCHEMA_NAME%%.range_sequence_number IS '{", "  \"targetTableName\": \"base_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT range_sequence_number__tenant_id_fk ON %%SCHEMA_NAME%%.range_sequence_number IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.sequence_number IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_sequence_number\",", "  \"rootTable\": \"base_sequence_number\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"id\",", "    \"legislation\"", "  ]", "}';;COMMENT ON CONSTRAINT sequence_number__id_fk ON %%SCHEMA_NAME%%.sequence_number IS '{", "  \"targetTableName\": \"base_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT bom_revision_sequence__id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence IS '{", "  \"targetTableName\": \"range_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT bom_revision_sequence__tenant_id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.location_sequence IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"range_sequence_number\",", "  \"rootTable\": \"base_sequence_number\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"id\",", "    \"legislation\"", "  ]", "}';;COMMENT ON CONSTRAINT location_sequence__id_fk ON %%SCHEMA_NAME%%.location_sequence IS '{", "  \"targetTableName\": \"range_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number_component__vendor_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number_component__update_user_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number_component__create_user_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number_component_sequence_number_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"base_sequence_number\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"sequence_number\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_sequence_number_component__tenant_id_fk ON %%SCHEMA_NAME%%.base_sequence_number_component IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT range_sequence_component__id_fk ON %%SCHEMA_NAME%%.range_sequence_component IS '{", "  \"targetTableName\": \"base_sequence_number_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT range_sequence_component__tenant_id_fk ON %%SCHEMA_NAME%%.range_sequence_component IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.sequence_number_component IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_sequence_number_component\",", "  \"rootTable\": \"base_sequence_number_component\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"sequenceNumber\",", "    \"_sortValue\"", "  ]", "}';;COMMENT ON CONSTRAINT sequence_number_component__id_fk ON %%SCHEMA_NAME%%.sequence_number_component IS '{", "  \"targetTableName\": \"base_sequence_number_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT bom_revision_sequence_component__id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence_component IS '{", "  \"targetTableName\": \"range_sequence_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT bom_revision_sequence_component__tenant_id_fk ON %%SCHEMA_NAME%%.bom_revision_sequence_component IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.location_sequence_component IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"range_sequence_component\",", "  \"rootTable\": \"base_sequence_number_component\",", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"sequenceNumber\",", "    \"_sortValue\"", "  ]", "}';;COMMENT ON CONSTRAINT location_sequence_component__id_fk ON %%SCHEMA_NAME%%.location_sequence_component IS '{", "  \"targetTableName\": \"range_sequence_component\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';"]}], "data": {"Role": {"metadata": {"rootFactoryName": "Role", "name": "Role", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "is_active", "type": "boolean"}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "description", "type": "string"}, {"name": "id", "type": "string"}, {"name": "is_billing_role", "type": "boolean"}]}, "rows": [["sage", "Y", "{\"en\":\"IT manager\"}", null, "100", "N"], ["sage", "Y", "{\"en\":\"Sales manager\"}", null, "200", "N"], ["sage", "Y", "{\"en\":\"Sales administrator\"}", null, "300", "N"], ["sage", "Y", "{\"en\":\"Purchasing manager\"}", null, "400", "N"], ["sage", "Y", "{\"en\":\"Buyer\"}", null, "500", "N"], ["sage", "Y", "{\"en\":\"Inventory manager\"}", null, "600", "N"], ["sage", "Y", "{\"en\":\"Inventory operator\"}", null, "700", "N"], ["sage", "Y", "{\"en\":\"Design/Production engineer\"}", null, "800", "N"], ["sage", "Y", "{\"en\":\"Production manager\"}", null, "900", "N"], ["sage", "Y", "{\"en\":\"Production operator\"}", null, "1000", "N"], ["sage", "Y", "{\"en\":\"Administrator - Technical\"}", null, "Admin - Technical", "N"], ["sage", "Y", "{\"en\":\"Administrator\"}", null, "Admin", "N"], ["sage", "Y", "{\"en\":\"Support Access Read-only\"}", null, "Support User Read-only", "N"], ["sage", "Y", "{\"en\":\"Support Access\"}", null, "Support User", "N"], ["sage", "Y", "{\"en\":\"Operational User\"}", null, "Operational User", "Y"], ["sage", "Y", "{\"en\":\"Business User\"}", null, "Business User", "Y"], ["sage", "Y", "{\"en\":\"Controller/Finance manager\",\"base\":\"Controller/Finance manager\",\"en-US\":\"Controller/Finance manager\"}", null, "1100", "N"]]}, "RoleActivity": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "RoleActivity", "name": "RoleActivity", "naturalKeyColumns": ["_tenant_id", "role", "activity"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "role", "type": "reference", "targetFactoryName": "Role"}, {"name": "activity", "type": "reference", "targetFactoryName": "Activity"}, {"name": "has_all_permissions", "type": "boolean"}, {"name": "permissions", "type": "stringArray"}, {"name": "is_active", "type": "boolean"}], "vitalParentColumn": {"name": "role", "type": "reference", "targetFactoryName": "Role"}}, "rows": [["sage", "15600", "100", "groupRoleSite", "Y", "[]", "Y"], ["sage", "200", "100", "user", "Y", "[]", "Y"], ["sage", "15600", "100", "siteGroup", "Y", "[]", "Y"], ["sage", "15700", "100", "site", "Y", "[]", "Y"], ["sage", "15600", "100", "company", "Y", "[]", "Y"], ["sage", "3700", "200", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "3800", "200", "user", "Y", "[]", "Y"], ["sage", "3900", "200", "site", "N", "[\"read\"]", "Y"], ["sage", "3500", "200", "company", "N", "[\"read\"]", "Y"], ["sage", "3600", "200", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3400", "300", "company", "N", "[\"read\"]", "Y"], ["sage", "3900", "400", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "4000", "400", "site", "N", "[\"read\"]", "Y"], ["sage", "3700", "400", "company", "N", "[\"read\"]", "Y"], ["sage", "3800", "400", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "2500", "500", "company", "N", "[\"read\"]", "Y"], ["sage", "6200", "600", "company", "N", "[\"read\"]", "Y"], ["sage", "6300", "700", "company", "N", "[\"read\"]", "Y"], ["sage", "6400", "600", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6500", "600", "user", "Y", "[]", "Y"], ["sage", "6600", "600", "site", "N", "[\"read\"]", "Y"], ["sage", "6300", "600", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "6500", "800", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6600", "800", "user", "Y", "[]", "Y"], ["sage", "6700", "800", "site", "N", "[\"read\"]", "Y"], ["sage", "6400", "800", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3500", "900", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6700", "900", "user", "Y", "[]", "Y"], ["sage", "6800", "900", "site", "N", "[\"read\"]", "Y"], ["sage", "3400", "900", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "100", "Support User", "role", "Y", "[]", "Y"], ["sage", "200", "Support User", "user", "Y", "[]", "Y"], ["sage", "300", "Support User", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Support User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Support User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Support User", "site", "Y", "[]", "Y"], ["sage", "21400", "Support User", "company", "Y", "[]", "Y"], ["sage", "21500", "Support User", "tenant", "Y", "[]", "Y"], ["sage", "21400", "Support User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "21500", "Support User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "22200", "Support User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "22300", "Support User", "sysTag", "Y", "[]", "Y"], ["sage", "100", "Support User Read-only", "role", "N", "[\"read\"]", "Y"], ["sage", "200", "Support User Read-only", "user", "N", "[\"read\"]", "Y"], ["sage", "300", "Support User Read-only", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "400", "Support User Read-only", "groupRoleSite", "N", "[\"read\"]", "Y"], ["sage", "500", "Support User Read-only", "supportAccessHistory", "N", "[\"read\"]", "Y"], ["sage", "600", "Support User Read-only", "site", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "company", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "tenant", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "sysNotificationState", "N", "[\"read\"]", "Y"], ["sage", "22200", "Support User Read-only", "serviceOptionState", "N", "[\"read\"]", "Y"], ["sage", "22300", "Support User Read-only", "sysTag", "N", "[\"read\"]", "Y"], ["sage", "100", "Admin", "role", "Y", "[]", "Y"], ["sage", "200", "Admin", "user", "Y", "[]", "Y"], ["sage", "300", "Admin", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Admin", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Admin", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Admin", "site", "Y", "[]", "Y"], ["sage", "700", "Admin", "tenant", "Y", "[]", "Y"], ["sage", "12400", "Admin", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "12200", "Admin", "company", "Y", "[]", "Y"], ["sage", "12500", "Admin", "sysNotificationState", "Y", "[]", "Y"], ["sage", "13800", "Admin", "serviceOptionState", "Y", "[]", "Y"], ["sage", "13900", "Admin", "sysTag", "Y", "[]", "Y"], ["sage", "13500", "Business User", "role", "Y", "[]", "Y"], ["sage", "13600", "Business User", "user", "Y", "[]", "Y"], ["sage", "13700", "Business User", "siteGroup", "Y", "[]", "Y"], ["sage", "13800", "Business User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "13900", "Business User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "14200", "Business User", "tenant", "Y", "[]", "Y"], ["sage", "14100", "Business User", "company", "Y", "[]", "Y"], ["sage", "14150", "Business User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "14170", "Business User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "14180", "Business User", "site", "Y", "[]", "Y"], ["sage", "530", "1100", "company", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "15700", "100", "customField", "Y", "[]", "Y"], ["sage", "4300", "200", "customField", "Y", "[]", "Y"], ["sage", "4400", "400", "customField", "Y", "[]", "Y"], ["sage", "7000", "600", "customField", "Y", "[]", "Y"], ["sage", "7100", "800", "customField", "Y", "[]", "Y"], ["sage", "7200", "900", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User Read-only", "customField", "N", "[\"read\"]", "Y"], ["sage", "13000", "Admin", "customField", "Y", "[]", "Y"], ["sage", "900", "Business User", "customField", "Y", "[]", "Y"], ["sage", "15500", "100", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "3100", "200", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2100", "300", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3300", "400", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2200", "500", "dashboardActivity", "Y", "[]", "Y"], ["sage", "5900", "600", "dashboardActivity", "Y", "[]", "Y"], ["sage", "6000", "700", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3500", "800", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3000", "900", "dashboardActivity", "N", "[]", "Y"], ["sage", "1900", "1000", "dashboardActivity", "N", "[]", "Y"], ["sage", "80", "Operational User", "dashboardActivity", "Y", "[\"read\"]", "Y"], ["sage", "700", "Admin", "dashboardActivity", "Y", "[]", "Y"], ["sage", "21100", "Support User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "1000", "Business User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "10", "1100", "dashboardActivity", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "100", "Admin", "workflowDefinition", "Y", null, "Y"], ["sage", "3000", "200", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3200", "400", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "5400", "600", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3400", "800", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "2900", "900", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "700", "Admin", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "1500", "Admin - Technical", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "21100", "Support User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "1700", "Business User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "3900", "200", "reportResource", "Y", "[]", "Y"], ["sage", "4000", "200", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4100", "200", "reportTemplate", "Y", "[]", "Y"], ["sage", "4200", "200", "report", "Y", "[]", "Y"], ["sage", "3500", "300", "reportResource", "Y", "[]", "Y"], ["sage", "3600", "300", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "3700", "300", "reportTemplate", "Y", "[]", "Y"], ["sage", "3800", "300", "report", "Y", "[]", "Y"], ["sage", "4000", "400", "reportResource", "Y", "[]", "Y"], ["sage", "4100", "400", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4200", "400", "reportTemplate", "Y", "[]", "Y"], ["sage", "4300", "400", "report", "Y", "[]", "Y"], ["sage", "2600", "500", "reportResource", "Y", "[]", "Y"], ["sage", "2700", "500", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2800", "500", "reportTemplate", "Y", "[]", "Y"], ["sage", "2900", "500", "report", "Y", "[]", "Y"], ["sage", "6600", "600", "reportResource", "Y", "[]", "Y"], ["sage", "6700", "600", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6800", "600", "reportTemplate", "Y", "[]", "Y"], ["sage", "6900", "600", "report", "Y", "[]", "Y"], ["sage", "6400", "700", "reportResource", "Y", "[]", "Y"], ["sage", "6500", "700", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6600", "700", "reportTemplate", "Y", "[]", "Y"], ["sage", "6700", "700", "report", "Y", "[]", "Y"], ["sage", "6700", "800", "reportResource", "Y", "[]", "Y"], ["sage", "6800", "800", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6900", "800", "reportTemplate", "Y", "[]", "Y"], ["sage", "7000", "800", "report", "Y", "[]", "Y"], ["sage", "6800", "900", "reportResource", "Y", "[]", "Y"], ["sage", "6900", "900", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "7000", "900", "reportTemplate", "Y", "[]", "Y"], ["sage", "7100", "900", "report", "Y", "[]", "Y"], ["sage", "2200", "1000", "reportResource", "Y", "[]", "Y"], ["sage", "2300", "1000", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2400", "1000", "reportTemplate", "Y", "[]", "Y"], ["sage", "2500", "1000", "report", "Y", "[]", "Y"], ["sage", "20000", "Support User", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "20100", "Support User", "reportTemplate", "Y", "[]", "Y"], ["sage", "20200", "Support User", "report", "Y", "[]", "Y"], ["sage", "21300", "Support User", "reportResource", "Y", "[]", "Y"], ["sage", "20000", "Support User Read-only", "reportStyleVariable", "N", "[\"read\"]", "Y"], ["sage", "20100", "Support User Read-only", "reportTemplate", "N", "[\"read\"]", "Y"], ["sage", "20200", "Support User Read-only", "report", "N", "[\"read\"]", "Y"], ["sage", "21100", "Support User Read-only", "reportResource", "N", "[\"read\"]", "Y"], ["sage", "12600", "Admin", "reportResource", "Y", "[]", "Y"], ["sage", "12700", "Admin", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "12800", "Admin", "reportTemplate", "Y", "[]", "Y"], ["sage", "12900", "Admin", "report", "Y", "[]", "Y"], ["sage", "5200", "Admin", "sysEmailConfig", "Y", null, "Y"], ["sage", "8800", "100", "legislation", "Y", "[]", "Y"], ["sage", "8850", "100", "country", "Y", "[]", "Y"], ["sage", "10400", "Support User", "baseOptionManagement", "Y", "[]", "Y"], ["sage", "10600", "Support User", "country", "Y", "[]", "Y"], ["sage", "10700", "Support User", "legislation", "Y", "[]", "Y"], ["sage", "10400", "Support User Read-only", "baseOptionManagement", "N", "[\"read\"]", "Y"], ["sage", "10600", "Support User Read-only", "country", "N", "[\"read\"]", "Y"], ["sage", "10700", "Support User Read-only", "legislation", "N", "[\"read\"]", "Y"], ["sage", "5500", "Admin", "baseOptionManagement", "Y", "[]", "Y"], ["sage", "5600", "Admin", "country", "Y", "[]", "Y"], ["sage", "5700", "Admin", "legislation", "Y", "[]", "Y"], ["sage", "12300", "Business User", "baseOptionManagement", "Y", "[]", "Y"], ["sage", "12400", "Business User", "country", "Y", "[]", "Y"], ["sage", "12500", "Business User", "legislation", "Y", "[]", "Y"], ["sage", "520", "1100", "legislation", "N", "[\"read\",\"update\"]", "Y"], ["sage", "5400", "100", "allergen", "Y", "[]", "Y"], ["sage", "5500", "100", "businessEntity", "Y", "[]", "Y"], ["sage", "5900", "100", "currency", "Y", "[]", "Y"], ["sage", "6000", "100", "customerPriceReason", "Y", "[]", "Y"], ["sage", "6100", "100", "dailyShift", "Y", "[]", "Y"], ["sage", "6200", "100", "deliveryMode", "Y", "[]", "Y"], ["sage", "6500", "100", "incoterm", "Y", "[]", "Y"], ["sage", "6600", "100", "indirectCostOrigin", "N", "[\"read\"]", "Y"], ["sage", "6700", "100", "indirectCostSection", "N", "[\"read\"]", "Y"], ["sage", "6800", "100", "item", "Y", "[]", "Y"], ["sage", "6900", "100", "itemSite", "Y", "[]", "Y"], ["sage", "7200", "100", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "7300", "100", "location", "Y", "[]", "Y"], ["sage", "7400", "100", "locationSequence", "Y", "[]", "Y"], ["sage", "7500", "100", "locationType", "Y", "[]", "Y"], ["sage", "7600", "100", "locationZone", "N", "[\"read\"]", "Y"], ["sage", "7700", "100", "machineResource", "Y", "[]", "Y"], ["sage", "7800", "100", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "7900", "100", "reasonCode", "Y", "[]", "Y"], ["sage", "8000", "100", "sequenceNumber", "Y", "[]", "Y"], ["sage", "8200", "100", "standard", "N", "[\"read\"]", "Y"], ["sage", "8400", "100", "toolResource", "Y", "[]", "Y"], ["sage", "8500", "100", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "8600", "100", "weeklyShift", "N", "[\"read\"]", "Y"], ["sage", "8700", "100", "itemCategory", "Y", "[]", "Y"], ["sage", "8900", "100", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "16800", "100", "customer", "Y", "[]", "Y"], ["sage", "16900", "100", "supplier", "Y", "[]", "Y"], ["sage", "17110", "100", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "1400", "200", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "1500", "200", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1550", "200", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "1560", "200", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "2700", "200", "businessEntity", "Y", "[]", "Y"], ["sage", "2800", "200", "customerPriceReason", "Y", "[]", "Y"], ["sage", "2900", "200", "incoterm", "Y", "[]", "Y"], ["sage", "4400", "200", "customer", "Y", "[]", "Y"], ["sage", "4500", "200", "supplier", "Y", "[]", "Y"], ["sage", "1000", "300", "businessEntity", "Y", "[]", "Y"], ["sage", "1200", "300", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1300", "300", "item", "N", "[\"read\"]", "Y"], ["sage", "1400", "300", "itemSite", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1500", "300", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "3900", "300", "customer", "Y", "[]", "Y"], ["sage", "4000", "300", "supplier", "Y", "[]", "Y"], ["sage", "710", "400", "businessEntity", "Y", "[]", "Y"], ["sage", "900", "400", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "950", "400", "incoterm", "Y", "[]", "Y"], ["sage", "1000", "400", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1100", "400", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "1400", "400", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "1450", "400", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "4500", "400", "customer", "N", "[\"read\"]", "Y"], ["sage", "4600", "400", "supplier", "N", "[\"read\"]", "Y"], ["sage", "700", "500", "businessEntity", "Y", "[]", "Y"], ["sage", "800", "500", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "900", "500", "item", "N", "[\"read\"]", "Y"], ["sage", "1000", "500", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "3000", "500", "customer", "N", "[\"read\"]", "Y"], ["sage", "3100", "500", "supplier", "N", "[\"read\"]", "Y"], ["sage", "1700", "700", "itemSite", "Y", "[]", "Y"], ["sage", "2000", "700", "locationType", "N", "[\"read\"]", "Y"], ["sage", "5800", "700", "allergen", "N", "[\"read\"]", "Y"], ["sage", "5900", "700", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1050", "800", "allergen", "Y", "[]", "Y"], ["sage", "1060", "800", "capabilityLevel", "Y", "[]", "Y"], ["sage", "1070", "800", "container", "Y", "[]", "Y"], ["sage", "1080", "800", "costCategory", "Y", "[]", "Y"], ["sage", "1100", "800", "dailyShift", "N", "[\"read\"]", "Y"], ["sage", "1200", "800", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1300", "800", "groupResource", "Y", "[]", "Y"], ["sage", "1350", "800", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "1360", "800", "indirectCostSection", "Y", "[]", "Y"], ["sage", "1400", "800", "item", "Y", "[]", "Y"], ["sage", "1500", "800", "itemSite", "Y", "[]", "Y"], ["sage", "1700", "800", "labourResource", "Y", "[]", "Y"], ["sage", "1750", "800", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "1800", "800", "machineResource", "Y", "[]", "Y"], ["sage", "1900", "800", "shiftDetail", "Y", "[]", "Y"], ["sage", "2000", "800", "standard", "Y", "[]", "Y"], ["sage", "2200", "800", "toolResource", "Y", "[]", "Y"], ["sage", "2300", "800", "weeklyShift", "N", "[\"read\"]", "Y"], ["sage", "2400", "800", "itemCategory", "Y", "[]", "Y"], ["sage", "7200", "800", "customer", "N", "[\"read\"]", "Y"], ["sage", "7300", "800", "supplier", "N", "[\"read\"]", "Y"], ["sage", "17110", "800", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "950", "900", "capabilityLevel", "Y", "[]", "Y"], ["sage", "960", "900", "container", "Y", "[]", "Y"], ["sage", "1000", "900", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1100", "900", "groupResource", "Y", "[]", "Y"], ["sage", "1200", "900", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1300", "900", "itemSite", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1500", "900", "labourResource", "Y", "[]", "Y"], ["sage", "1600", "900", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "1700", "900", "machineResource", "Y", "[]", "Y"], ["sage", "1800", "900", "standard", "Y", "[]", "Y"], ["sage", "2000", "900", "toolResource", "Y", "[]", "Y"], ["sage", "2050", "900", "location", "Y", "[]", "Y"], ["sage", "2060", "900", "weeklyShift", "Y", "[]", "Y"], ["sage", "7300", "900", "customer", "N", "[\"read\"]", "Y"], ["sage", "7400", "900", "supplier", "N", "[\"read\"]", "Y"], ["sage", "17110", "900", "bomRevisionSequence", "N", "[\"read\"]", "Y"], ["sage", "1200", "1000", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "1400", "1000", "licensePlateNumber", "N", "[\"read\"]", "Y"], ["sage", "1500", "1000", "machineResource", "N", "[\"read\"]", "Y"], ["sage", "17110", "1000", "bomRevisionSequence", "N", "[\"read\"]", "Y"], ["sage", "6300", "Support User", "allergen", "Y", "[]", "Y"], ["sage", "6400", "Support User", "businessEntity", "Y", "[]", "Y"], ["sage", "6500", "Support User", "capabilityLevel", "Y", "[]", "Y"], ["sage", "6700", "Support User", "container", "Y", "[]", "Y"], ["sage", "6800", "Support User", "costCategory", "Y", "[]", "Y"], ["sage", "6900", "Support User", "currency", "Y", "[]", "Y"], ["sage", "7000", "Support User", "customerPriceReason", "Y", "[]", "Y"], ["sage", "7200", "Support User", "dailyShift", "Y", "[]", "Y"], ["sage", "7300", "Support User", "deliveryMode", "Y", "[]", "Y"], ["sage", "7400", "Support User", "ghsClassification", "Y", "[]", "Y"], ["sage", "7500", "Support User", "groupResource", "Y", "[]", "Y"], ["sage", "7600", "Support User", "incoterm", "Y", "[]", "Y"], ["sage", "7700", "Support User", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "7800", "Support User", "indirectCostSection", "Y", "[]", "Y"], ["sage", "8100", "Support User", "itemSite", "Y", "[]", "Y"], ["sage", "8200", "Support User", "item", "Y", "[]", "Y"], ["sage", "8300", "Support User", "labourResource", "Y", "[]", "Y"], ["sage", "8400", "Support User", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "8600", "Support User", "locationType", "Y", "[]", "Y"], ["sage", "8700", "Support User", "locationZone", "Y", "[]", "Y"], ["sage", "8800", "Support User", "location", "Y", "[]", "Y"], ["sage", "8900", "Support User", "machineResource", "Y", "[]", "Y"], ["sage", "9000", "Support User", "paymentTerm", "Y", "[]", "Y"], ["sage", "9100", "Support User", "reasonCode", "Y", "[]", "Y"], ["sage", "9200", "Support User", "sequenceNumberAssignment", "Y", "[]", "Y"], ["sage", "9300", "Support User", "sequenceNumber", "Y", "[]", "Y"], ["sage", "9400", "Support User", "shiftDetail", "Y", "[]", "Y"], ["sage", "9500", "Support User", "standard", "Y", "[]", "Y"], ["sage", "9700", "Support User", "toolResource", "Y", "[]", "Y"], ["sage", "9800", "Support User", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "9900", "Support User", "weeklyShift", "Y", "[]", "Y"], ["sage", "9950", "Support User", "locationSequence", "Y", "[]", "Y"], ["sage", "9960", "Support User", "itemCategory", "Y", "[]", "Y"], ["sage", "9970", "Support User", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "21700", "Support User", "customer", "Y", "[]", "Y"], ["sage", "21800", "Support User", "supplier", "Y", "[]", "Y"], ["sage", "17110", "Support User", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "22300", "Support User", "supplierCertificate", "Y", "[]", "Y"], ["sage", "6300", "Support User Read-only", "allergen", "N", "[\"read\"]", "Y"], ["sage", "6400", "Support User Read-only", "businessEntity", "N", "[\"read\"]", "Y"], ["sage", "6500", "Support User Read-only", "capabilityLevel", "N", "[\"read\"]", "Y"], ["sage", "6700", "Support User Read-only", "container", "N", "[\"read\"]", "Y"], ["sage", "6800", "Support User Read-only", "costCategory", "N", "[\"read\"]", "Y"], ["sage", "6900", "Support User Read-only", "currency", "N", "[\"read\"]", "Y"], ["sage", "7000", "Support User Read-only", "customerPriceReason", "N", "[\"read\"]", "Y"], ["sage", "7200", "Support User Read-only", "dailyShift", "N", "[\"read\"]", "Y"], ["sage", "7300", "Support User Read-only", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "7400", "Support User Read-only", "ghsClassification", "N", "[\"read\"]", "Y"], ["sage", "7500", "Support User Read-only", "groupResource", "N", "[\"read\"]", "Y"], ["sage", "7600", "Support User Read-only", "incoterm", "N", "[\"read\"]", "Y"], ["sage", "7700", "Support User Read-only", "indirectCostOrigin", "N", "[\"read\"]", "Y"], ["sage", "7800", "Support User Read-only", "indirectCostSection", "N", "[\"read\"]", "Y"], ["sage", "8100", "Support User Read-only", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "8200", "Support User Read-only", "item", "N", "[\"read\"]", "Y"], ["sage", "8300", "Support User Read-only", "labourResource", "N", "[\"read\"]", "Y"], ["sage", "8400", "Support User Read-only", "licensePlateNumber", "N", "[\"read\"]", "Y"], ["sage", "8600", "Support User Read-only", "locationType", "N", "[\"read\"]", "Y"], ["sage", "8700", "Support User Read-only", "locationZone", "N", "[\"read\"]", "Y"], ["sage", "8800", "Support User Read-only", "location", "N", "[\"read\"]", "Y"], ["sage", "8900", "Support User Read-only", "machineResource", "N", "[\"read\"]", "Y"], ["sage", "9000", "Support User Read-only", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "9100", "Support User Read-only", "reasonCode", "N", "[\"read\"]", "Y"], ["sage", "9200", "Support User Read-only", "sequenceNumberAssignment", "N", "[\"read\"]", "Y"], ["sage", "9300", "Support User Read-only", "sequenceNumber", "N", "[\"read\"]", "Y"], ["sage", "9400", "Support User Read-only", "shiftDetail", "N", "[\"read\"]", "Y"], ["sage", "9500", "Support User Read-only", "standard", "N", "[\"read\"]", "Y"], ["sage", "9700", "Support User Read-only", "toolResource", "N", "[\"read\"]", "Y"], ["sage", "9800", "Support User Read-only", "unitOfMeasure", "N", "[\"read\"]", "Y"], ["sage", "9900", "Support User Read-only", "weeklyShift", "N", "[\"read\"]", "Y"], ["sage", "9950", "Support User Read-only", "locationSequence", "Y", "[]", "Y"], ["sage", "9960", "Support User Read-only", "itemCategory", "Y", "[\"read\"]", "Y"], ["sage", "21700", "Support User Read-only", "customer", "N", "[\"read\"]", "Y"], ["sage", "21800", "Support User Read-only", "supplier", "N", "[\"read\"]", "Y"], ["sage", "17110", "Support User Read-only", "bomRevisionSequence", "N", "[\"read\"]", "Y"], ["sage", "22300", "Support User Read-only", "supplierCertificate", "N", "[\"read\"]", "Y"], ["sage", "7200", "Admin", "allergen", "Y", "[]", "Y"], ["sage", "7300", "Admin", "businessEntity", "Y", "[]", "Y"], ["sage", "7400", "Admin", "capabilityLevel", "Y", "[]", "Y"], ["sage", "7500", "Admin", "container", "Y", "[]", "Y"], ["sage", "7600", "Admin", "costCategory", "Y", "[]", "Y"], ["sage", "7700", "Admin", "currency", "Y", "[]", "Y"], ["sage", "7800", "Admin", "customerPriceReason", "Y", "[]", "Y"], ["sage", "7900", "Admin", "dailyShift", "Y", "[]", "Y"], ["sage", "8000", "Admin", "deliveryMode", "Y", "[]", "Y"], ["sage", "8100", "Admin", "ghsClassification", "Y", "[]", "Y"], ["sage", "8200", "Admin", "groupResource", "Y", "[]", "Y"], ["sage", "8300", "Admin", "incoterm", "Y", "[]", "Y"], ["sage", "8400", "Admin", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "8500", "Admin", "indirectCostSection", "Y", "[]", "Y"], ["sage", "8600", "Admin", "itemSite", "Y", "[]", "Y"], ["sage", "8700", "Admin", "item", "Y", "[]", "Y"], ["sage", "8800", "Admin", "labourResource", "Y", "[]", "Y"], ["sage", "8900", "Admin", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "9000", "Admin", "locationType", "Y", "[]", "Y"], ["sage", "9100", "Admin", "locationZone", "Y", "[]", "Y"], ["sage", "9200", "Admin", "location", "Y", "[]", "Y"], ["sage", "9300", "Admin", "machineResource", "Y", "[]", "Y"], ["sage", "9400", "Admin", "paymentTerm", "Y", "[]", "Y"], ["sage", "9500", "Admin", "reasonCode", "Y", "[]", "Y"], ["sage", "9600", "Admin", "sequenceNumberAssignment", "Y", "[]", "Y"], ["sage", "9700", "Admin", "sequenceNumber", "Y", "[]", "Y"], ["sage", "9800", "Admin", "shiftDetail", "Y", "[]", "Y"], ["sage", "9900", "Admin", "standard", "Y", "[]", "Y"], ["sage", "10000", "Admin", "toolResource", "Y", "[]", "Y"], ["sage", "10100", "Admin", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "10200", "Admin", "weeklyShift", "Y", "[]", "Y"], ["sage", "10300", "Admin", "locationSequence", "Y", "[]", "Y"], ["sage", "10400", "Admin", "itemCategory", "Y", "[]", "Y"], ["sage", "10500", "Admin", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "13100", "Admin", "customer", "Y", "[]", "Y"], ["sage", "13200", "Admin", "supplier", "Y", "[]", "Y"], ["sage", "17110", "Admin", "bomRevisionSequence", "Y", "[]", "Y"], ["sage", "13900", "Admin", "supplierCertificate", "Y", "[]", "Y"], ["sage", "8100", "Business User", "allergen", "Y", "[]", "Y"], ["sage", "8200", "Business User", "businessEntity", "Y", "[]", "Y"], ["sage", "8300", "Business User", "capabilityLevel", "Y", "[]", "Y"], ["sage", "8400", "Business User", "container", "Y", "[]", "Y"], ["sage", "8500", "Business User", "costCategory", "Y", "[]", "Y"], ["sage", "8600", "Business User", "currency", "Y", "[]", "Y"], ["sage", "8700", "Business User", "customerPriceReason", "Y", "[]", "Y"], ["sage", "9000", "Business User", "dailyShift", "Y", "[]", "Y"], ["sage", "9100", "Business User", "deliveryMode", "Y", "[]", "Y"], ["sage", "9200", "Business User", "ghsClassification", "Y", "[]", "Y"], ["sage", "9300", "Business User", "groupResource", "Y", "[]", "Y"], ["sage", "9400", "Business User", "incoterm", "Y", "[]", "Y"], ["sage", "9500", "Business User", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "9600", "Business User", "indirectCostSection", "Y", "[]", "Y"], ["sage", "9700", "Business User", "itemSite", "Y", "[]", "Y"], ["sage", "9800", "Business User", "item", "Y", "[]", "Y"], ["sage", "9900", "Business User", "labourResource", "Y", "[]", "Y"], ["sage", "10000", "Business User", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "10100", "Business User", "locationType", "Y", "[]", "Y"], ["sage", "10200", "Business User", "locationZone", "Y", "[]", "Y"], ["sage", "10300", "Business User", "location", "Y", "[]", "Y"], ["sage", "10400", "Business User", "machineResource", "Y", "[]", "Y"], ["sage", "10500", "Business User", "paymentTerm", "Y", "[]", "Y"], ["sage", "10600", "Business User", "reasonCode", "Y", "[]", "Y"], ["sage", "10700", "Business User", "sequenceNumberAssignment", "Y", "[]", "Y"], ["sage", "10800", "Business User", "sequenceNumber", "Y", "[]", "Y"], ["sage", "10900", "Business User", "shiftDetail", "Y", "[]", "Y"], ["sage", "11000", "Business User", "standard", "Y", "[]", "Y"], ["sage", "11100", "Business User", "toolResource", "Y", "[]", "Y"], ["sage", "11200", "Business User", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "11300", "Business User", "weeklyShift", "Y", "[]", "Y"], ["sage", "11400", "Business User", "locationSequence", "Y", "[]", "Y"], ["sage", "11500", "Business User", "itemCategory", "Y", "[]", "Y"], ["sage", "11600", "Business User", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "11650", "Business User", "customer", "Y", "[]", "Y"], ["sage", "11700", "Business User", "supplier", "Y", "[]", "Y"], ["sage", "17110", "Business User", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "290", "1100", "businessEntity", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "300", "1100", "costCategory", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "310", "1100", "currency", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "320", "1100", "customerPriceReason", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "330", "1100", "customerSupplierCategory", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "340", "1100", "indirectCostOrigin", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "350", "1100", "indirectCostSection", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "360", "1100", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "370", "1100", "itemSite", "N", "[\"read\",\"update\",\"delete\"]", "Y"], ["sage", "380", "1100", "paymentTerm", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "390", "1100", "sequenceNumber", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "400", "1100", "sequenceNumberAssignment", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "2200", "200", "stock", "N", "[\"read\"]", "Y"], ["sage", "2000", "300", "stock", "N", "[\"read\"]", "Y"], ["sage", "2700", "400", "stock", "N", "[\"read\"]", "Y"], ["sage", "2100", "500", "stock", "N", "[\"read\"]", "Y"], ["sage", "2800", "600", "stockStatus", "Y", "[]", "Y"], ["sage", "1000", "600", "stock", "N", "[\"read\"]", "Y"], ["sage", "1050", "600", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "2400", "700", "stockStatus", "N", "[\"read\"]", "Y"], ["sage", "2600", "700", "stock", "N", "[\"read\"]", "Y"], ["sage", "2700", "700", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "800", "900", "stock", "N", "[\"read\"]", "Y"], ["sage", "800", "1000", "stock", "N", "[\"read\"]", "Y"], ["sage", "10100", "Support User", "stockJournal", "Y", "[]", "Y"], ["sage", "10200", "Support User", "stockStatus", "Y", "[]", "Y"], ["sage", "10300", "Support User", "stock", "Y", "[]", "Y"], ["sage", "10350", "Support User", "allocationResult", "Y", "[]", "Y"], ["sage", "10100", "Support User Read-only", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "10200", "Support User Read-only", "stockStatus", "N", "[\"read\"]", "Y"], ["sage", "10300", "Support User Read-only", "stock", "N", "[\"read\"]", "Y"], ["sage", "10350", "Support User Read-only", "allocationResult", "N", "[\"read\"]", "Y"], ["sage", "250", "Operational User", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "255", "Operational User", "stock", "N", "[\"read\"]", "Y"], ["sage", "5800", "Admin", "stockStatus", "Y", "[]", "Y"], ["sage", "5900", "Admin", "stockJournal", "Y", "[]", "Y"], ["sage", "6000", "Admin", "stock", "Y", "[]", "Y"], ["sage", "6100", "Admin", "allocationResult", "Y", "[]", "Y"], ["sage", "11900", "Business User", "stockStatus", "Y", "[]", "Y"], ["sage", "12000", "Business User", "stockJournal", "Y", "[]", "Y"], ["sage", "12100", "Business User", "stock", "Y", "[]", "Y"], ["sage", "12200", "Business User", "allocationResult", "Y", "[]", "Y"], ["sage", "3400", "200", "synchronizationState", "N", "[\"read\"]", "Y"], ["sage", "3600", "400", "synchronizationState", "N", "[\"read\"]", "Y"], ["sage", "21200", "Support User", "synchronizationState", "Y", "[]", "Y"], ["sage", "21000", "Support User Read-only", "synchronizationState", "N", "[\"read\"]", "Y"], ["sage", "12100", "Admin", "synchronizationState", "Y", "[]", "Y"], ["sage", "1800", "Business User", "synchronizationState", "Y", "[]", "Y"], ["sage", "8900", "100", "countryGroup", "Y", "[]", "Y"], ["sage", "9000", "100", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "9100", "100", "tax", "N", "[\"read\",\"create\",\"update\",\"delete\",\"getTaxValues\"]", "Y"], ["sage", "9200", "100", "taxCategory", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "9300", "100", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "10000", "100", "taxSolution", "Y", "[]", "Y"], ["sage", "10100", "100", "taxZone", "Y", "[]", "Y"], ["sage", "1700", "200", "tax", "N", "[\"read\"]", "Y"], ["sage", "1800", "200", "taxCategory", "N", "[\"read\"]", "Y"], ["sage", "1900", "200", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "2000", "200", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "2100", "200", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "2200", "400", "tax", "N", "[\"read\"]", "Y"], ["sage", "2300", "400", "taxCategory", "N", "[\"read\"]", "Y"], ["sage", "2400", "400", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "2500", "400", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "2600", "400", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "5600", "700", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "5700", "700", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "10800", "Support User", "countryGroup", "Y", "[]", "Y"], ["sage", "10900", "Support User", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "11000", "Support User", "taxCategory", "Y", "[]", "Y"], ["sage", "11100", "Support User", "taxDetermination", "Y", "[]", "Y"], ["sage", "11200", "Support User", "taxSolution", "Y", "[]", "Y"], ["sage", "11300", "Support User", "tax", "Y", "[]", "Y"], ["sage", "11350", "Support User", "taxZone", "Y", "[]", "Y"], ["sage", "10800", "Support User Read-only", "countryGroup", "N", "[\"read\"]", "Y"], ["sage", "10900", "Support User Read-only", "itemTaxGroup", "N", "[\"read\"]", "Y"], ["sage", "11000", "Support User Read-only", "taxCategory", "N", "[\"read\"]", "Y"], ["sage", "11100", "Support User Read-only", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "11200", "Support User Read-only", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "11300", "Support User Read-only", "tax", "N", "[\"read\"]", "Y"], ["sage", "11350", "Support User Read-only", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "4700", "Admin", "countryGroup", "Y", "[]", "Y"], ["sage", "4800", "Admin", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "4900", "Admin", "tax", "Y", "[]", "Y"], ["sage", "5000", "Admin", "taxCategory", "Y", "[]", "Y"], ["sage", "5100", "Admin", "taxDetermination", "Y", "[]", "Y"], ["sage", "5200", "Admin", "taxSolution", "Y", "[]", "Y"], ["sage", "5200", "Admin", "taxZone", "Y", "[]", "Y"], ["sage", "12600", "Business User", "countryGroup", "Y", "[]", "Y"], ["sage", "12700", "Business User", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "12800", "Business User", "tax", "Y", "[]", "Y"], ["sage", "12900", "Business User", "taxCategory", "Y", "[]", "Y"], ["sage", "13000", "Business User", "taxDetermination", "Y", "[]", "Y"], ["sage", "13100", "Business User", "taxSolution", "Y", "[]", "Y"], ["sage", "13200", "Business User", "taxZone", "Y", "[]", "Y"], ["sage", "540", "1100", "countryGroup", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "550", "1100", "itemTaxGroup", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "560", "1100", "tax", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "570", "1100", "taxCategory", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "580", "1100", "taxDetermination", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "590", "1100", "taxSolution", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "600", "1100", "taxZone", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "3900", "100", "account", "Y", "[]", "Y"], ["sage", "4100", "100", "attribute", "Y", "[]", "Y"], ["sage", "4200", "100", "attributeType", "Y", "[]", "Y"], ["sage", "4250", "100", "datevConfiguration", "Y", "[]", "Y"], ["sage", "4300", "100", "dimension", "Y", "[]", "Y"], ["sage", "4400", "100", "dimensionType", "Y", "[]", "Y"], ["sage", "4000", "100", "journalEntryType", "Y", "[]", "Y"], ["sage", "4500", "100", "journal", "Y", "[]", "Y"], ["sage", "4600", "100", "postingClass", "Y", "[]", "Y"], ["sage", "4700", "100", "postingClassDefinition", "N", "[\"read\",\"update\"]", "Y"], ["sage", "4750", "100", "bankAccount", "Y", "[]", "Y"], ["sage", "2300", "200", "attribute", "N", "[\"read\"]", "Y"], ["sage", "2400", "200", "dimension", "N", "[\"read\"]", "Y"], ["sage", "2500", "200", "journal", "N", "[\"read\"]", "Y"], ["sage", "2600", "200", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "2800", "400", "attribute", "N", "[\"read\"]", "Y"], ["sage", "2900", "400", "dimension", "N", "[\"read\"]", "Y"], ["sage", "3000", "400", "journal", "N", "[\"read\"]", "Y"], ["sage", "3100", "400", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "5500", "600", "attribute", "N", "[\"read\"]", "Y"], ["sage", "5600", "600", "dimension", "N", "[\"read\"]", "Y"], ["sage", "5700", "600", "journal", "N", "[\"read\"]", "Y"], ["sage", "5800", "600", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "2500", "900", "attribute", "N", "[\"read\"]", "Y"], ["sage", "2600", "900", "dimension", "N", "[\"read\"]", "Y"], ["sage", "2700", "900", "journal", "N", "[\"read\"]", "Y"], ["sage", "2800", "900", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "5400", "Support User", "account", "Y", "[]", "Y"], ["sage", "5500", "Support User", "attributeType", "Y", "[]", "Y"], ["sage", "5600", "Support User", "attribute", "Y", "[]", "Y"], ["sage", "5650", "Support User", "datevConfiguration", "Y", "[]", "Y"], ["sage", "5700", "Support User", "dimensionType", "Y", "[]", "Y"], ["sage", "5800", "Support User", "dimension", "Y", "[]", "Y"], ["sage", "5900", "Support User", "journalEntryType", "Y", "[]", "Y"], ["sage", "6000", "Support User", "journal", "Y", "[]", "Y"], ["sage", "6100", "Support User", "postingClassDefinition", "Y", "[]", "Y"], ["sage", "6200", "Support User", "postingClass", "Y", "[]", "Y"], ["sage", "6250", "Support User", "bankAccount", "Y", "[]", "Y"], ["sage", "5400", "Support User Read-only", "account", "N", "[\"read\"]", "Y"], ["sage", "5500", "Support User Read-only", "attributeType", "N", "[\"read\"]", "Y"], ["sage", "5600", "Support User Read-only", "attribute", "N", "[\"read\"]", "Y"], ["sage", "5650", "Support User Read-only", "datevConfiguration", "N", "[\"read\"]", "Y"], ["sage", "5700", "Support User Read-only", "dimensionType", "N", "[\"read\"]", "Y"], ["sage", "5800", "Support User Read-only", "dimension", "N", "[\"read\"]", "Y"], ["sage", "5900", "Support User Read-only", "journalEntryType", "N", "[\"read\"]", "Y"], ["sage", "6000", "Support User Read-only", "journal", "N", "[\"read\"]", "Y"], ["sage", "6100", "Support User Read-only", "postingClassDefinition", "N", "[\"read\"]", "Y"], ["sage", "6200", "Support User Read-only", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "6250", "Support User Read-only", "bankAccount", "N", "[\"read\"]", "Y"], ["sage", "1800", "Operational User", "account", "N", "[\"read\"]", "Y"], ["sage", "6200", "Admin", "account", "Y", "[]", "Y"], ["sage", "6300", "Admin", "attribute", "Y", "[]", "Y"], ["sage", "6400", "Admin", "attributeType", "Y", "[]", "Y"], ["sage", "6450", "Admin", "datevConfiguration", "Y", "[]", "Y"], ["sage", "6500", "Admin", "dimension", "Y", "[]", "Y"], ["sage", "6600", "Admin", "dimensionType", "Y", "[]", "Y"], ["sage", "6700", "Admin", "journalEntryType", "Y", "[]", "Y"], ["sage", "6800", "Admin", "journal", "Y", "[]", "Y"], ["sage", "6900", "Admin", "postingClass", "Y", "[]", "Y"], ["sage", "7000", "Admin", "postingClassDefinition", "Y", "[]", "Y"], ["sage", "7100", "Admin", "bankAccount", "Y", "[]", "Y"], ["sage", "7100", "Business User", "account", "Y", "[]", "Y"], ["sage", "7200", "Business User", "attribute", "Y", "[]", "Y"], ["sage", "7300", "Business User", "attributeType", "Y", "[]", "Y"], ["sage", "7400", "Business User", "dimension", "Y", "[]", "Y"], ["sage", "7500", "Business User", "dimensionType", "Y", "[]", "Y"], ["sage", "7600", "Business User", "journalEntryType", "Y", "[]", "Y"], ["sage", "7700", "Business User", "journal", "Y", "[]", "Y"], ["sage", "7800", "Business User", "postingClass", "Y", "[]", "Y"], ["sage", "7900", "Business User", "postingClassDefinition", "Y", "[]", "Y"], ["sage", "8000", "Business User", "bankAccount", "Y", "[]", "Y"], ["sage", "120", "1100", "account", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "130", "1100", "attribute", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "140", "1100", "attributeType", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "150", "1100", "companyDefaultAttribute", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "160", "1100", "companyDefaultDimension", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "170", "1100", "datevConfiguration", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "180", "1100", "dimension", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "190", "1100", "dimensionDefinitionLevelAndDefault", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "200", "1100", "dimensionType", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "210", "1100", "journal", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "220", "1100", "journalEntryType", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "230", "1100", "postingClass", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "240", "1100", "postingClassDefinition", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "8000", "1100", "bankAccount", "Y", "[]", "Y"], ["sage", "8000", "Admin - Technical", "bankAccount", "Y", "[]", "Y"], ["sage", "15800", "100", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "15900", "100", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "16000", "100", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "16100", "100", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "16150", "100", "datevExport", "Y", "[]", "Y"], ["sage", "16200", "100", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "16300", "100", "journalEntry", "Y", "[]", "Y"], ["sage", "16400", "100", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "16450", "100", "payment", "Y", "[]", "Y"], ["sage", "16500", "100", "receipt", "Y", "[]", "Y"], ["sage", "3100", "100", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "2700", "100", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "1400", "200", "accountsReceivableInvoice", "N", "[\"read\"]", "Y"], ["sage", "1500", "200", "accountsReceivablePayment", "N", "[\"read\"]", "Y"], ["sage", "1600", "200", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "1700", "200", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "1800", "400", "accountsPayableInvoice", "N", "[\"read\"]", "Y"], ["sage", "1900", "400", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "1700", "400", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "1900", "500", "accountsPayableInvoice", "N", "[\"read\"]", "Y"], ["sage", "5400", "700", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "5500", "700", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "2300", "900", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "2400", "900", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "2400", "Support User", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "2500", "Support User", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "2600", "Support User", "journalEntry", "Y", "[]", "Y"], ["sage", "2700", "Support User", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "2800", "Support User", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "2850", "Support User", "datevExport", "Y", "[]", "Y"], ["sage", "2900", "Support User", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "3000", "Support User", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "3050", "Support User", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "3100", "Support User", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "3150", "Support User", "payment", "Y", "[]", "Y"], ["sage", "3200", "Support User", "receipt", "Y", "[]", "Y"], ["sage", "3300", "Support User", "accountingInterfaceListener", "Y", "[]", "Y"], ["sage", "2400", "Support User Read-only", "accountsPayableInvoice", "N", "[\"read\"]", "Y"], ["sage", "2500", "Support User Read-only", "accountsReceivableInvoice", "N", "[\"read\"]", "Y"], ["sage", "2550", "Support User Read-only", "datevExport", "N", "[\"read\"]", "Y"], ["sage", "2600", "Support User Read-only", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "2650", "Support User Read-only", "accountsPayableOpenItem", "N", "[\"read\"]", "Y"], ["sage", "2700", "Support User Read-only", "accountsReceivableOpenItem", "N", "[\"read\"]", "Y"], ["sage", "2800", "Support User Read-only", "payment", "N", "[\"read\"]", "Y"], ["sage", "2900", "Support User Read-only", "receipt", "N", "[\"read\"]", "Y"], ["sage", "1100", "Operational User", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "1200", "Operational User", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "1300", "Operational User", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "1400", "Operational User", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "1500", "Operational User", "journalEntry", "Y", "[]", "Y"], ["sage", "1600", "Operational User", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "4000", "Admin", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "4100", "Admin", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "4200", "Admin", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "4300", "Admin", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "4350", "Admin", "datevExport", "Y", "[]", "Y"], ["sage", "4400", "Admin", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "4500", "Admin", "journalEntry", "Y", "[]", "Y"], ["sage", "4600", "Admin", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "4650", "Admin", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "4700", "Admin", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "4750", "Admin", "payment", "Y", "[]", "Y"], ["sage", "4800", "Admin", "receipt", "Y", "[]", "Y"], ["sage", "4900", "Admin", "accountingInterfaceListener", "Y", "[]", "Y"], ["sage", "3500", "Business User", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "3600", "Business User", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "3700", "Business User", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "3800", "Business User", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "3900", "Business User", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "4000", "Business User", "journalEntry", "Y", "[]", "Y"], ["sage", "4100", "Business User", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "60", "1100", "accountsPayableInvoice", "N", "[\"read\",\"post\"]", "Y"], ["sage", "70", "1100", "accountsReceivableInvoice", "N", "[\"read\",\"post\",\"updateOpenItemFromIntacct\"]", "Y"], ["sage", "80", "1100", "datevExport", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "90", "1100", "generateJournalEntries", "N", "[\"accountingIntegration\"]", "Y"], ["sage", "100", "1100", "journalEntry", "N", "[\"read\",\"manage\",\"post\"]", "Y"], ["sage", "110", "1100", "journalEntryInquiry", "N", "[\"read\"]", "Y"], ["sage", "111", "1100", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "112", "1100", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "120", "1100", "payment", "Y", "[]", "Y"], ["sage", "125", "1100", "receipt", "Y", "[]", "Y"], ["sage", "130", "1100", "accountingInterfaceListener", "Y", "[]", "Y"], ["sage", "112", "Admin - Technical", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "115", "Admin - Technical", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "120", "Admin - Technical", "payment", "Y", "[]", "Y"], ["sage", "130", "Admin - Technical", "receipt", "Y", "[]", "Y"], ["sage", "16600", "100", "purchaseInvoice", "Y", "[\"read\",\"post\"]", "Y"], ["sage", "16700", "100", "purchaseOrder", "Y", "[]", "Y"], ["sage", "1100", "200", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "1200", "200", "purchaseOrder", "N", "[\"read\"]", "Y"], ["sage", "1700", "300", "purchaseRequisition", "N", "[\"read\"]", "Y"], ["sage", "1800", "300", "purchaseOrder", "N", "[\"read\"]", "Y"], ["sage", "100", "400", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "200", "400", "purchaseReturn", "Y", "[]", "Y"], ["sage", "300", "400", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "400", "400", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "500", "400", "purchaseOrder", "Y", "[]", "Y"], ["sage", "600", "400", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "100", "500", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "200", "500", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "300", "500", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "400", "500", "purchaseOrder", "Y", "[]", "Y"], ["sage", "500", "500", "purchaseReturn", "Y", "[]", "Y"], ["sage", "600", "500", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "600", "600", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "700", "600", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "750", "600", "purchaseReturn", "Y", "[]", "Y"], ["sage", "600", "700", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "700", "700", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "750", "700", "purchaseReturn", "Y", "[]", "Y"], ["sage", "600", "800", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "600", "900", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "3400", "Support User", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "3500", "Support User", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "3600", "Support User", "purchaseOrder", "Y", "[]", "Y"], ["sage", "3700", "Support User", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "3800", "Support User", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "3900", "Support User", "purchaseReturn", "Y", "[]", "Y"], ["sage", "3400", "Support User Read-only", "purchaseCreditMemo", "N", "[\"read\"]", "Y"], ["sage", "3500", "Support User Read-only", "purchaseInvoice", "N", "[\"read\"]", "Y"], ["sage", "3600", "Support User Read-only", "purchaseOrder", "N", "[\"read\"]", "Y"], ["sage", "3700", "Support User Read-only", "purchaseReceipt", "N", "[\"read\"]", "Y"], ["sage", "3800", "Support User Read-only", "purchaseRequisition", "N", "[\"read\"]", "Y"], ["sage", "3900", "Support User Read-only", "purchaseReturn", "N", "[\"read\"]", "Y"], ["sage", "85", "Operational User", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "300", "Operational User", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "400", "Operational User", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "500", "Operational User", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "1100", "Admin", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "1200", "Admin", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "1300", "Admin", "purchaseOrder", "Y", "[]", "Y"], ["sage", "1400", "Admin", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "1500", "Admin", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "1600", "Admin", "purchaseReturn", "Y", "[]", "Y"], ["sage", "4800", "Business User", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "4900", "Business User", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "5000", "Business User", "purchaseOrder", "Y", "[]", "Y"], ["sage", "5100", "Business User", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "5200", "Business User", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "5300", "Business User", "purchaseReturn", "Y", "[]", "Y"], ["sage", "410", "1100", "purchaseCreditMemo", "N", "[\"read\",\"post\",\"manage\"]", "Y"], ["sage", "420", "1100", "purchaseInvoice", "N", "[\"read\",\"manage\",\"acceptAllVariances\",\"post\"]", "Y"], ["sage", "3100", "100", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "3200", "100", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "3300", "100", "salesInvoice", "Y", "[]", "Y"], ["sage", "3400", "100", "salesOrder", "Y", "[]", "Y"], ["sage", "3500", "100", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "3600", "100", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "3700", "100", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "3800", "100", "salesShipment", "Y", "[]", "Y"], ["sage", "3850", "100", "proformaInvoice", "Y", "[]", "Y"], ["sage", "200", "200", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "300", "200", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "400", "200", "salesInvoice", "Y", "[]", "Y"], ["sage", "500", "200", "salesOrder", "Y", "[]", "Y"], ["sage", "600", "200", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "700", "200", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "800", "200", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "900", "200", "salesShipment", "Y", "[]", "Y"], ["sage", "950", "200", "proformaInvoice", "Y", "[]", "Y"], ["sage", "200", "300", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "300", "300", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "400", "300", "salesInvoice", "Y", "[]", "Y"], ["sage", "500", "300", "salesOrder", "Y", "[]", "Y"], ["sage", "600", "300", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "700", "300", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "800", "300", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "900", "300", "salesShipment", "Y", "[]", "Y"], ["sage", "950", "300", "proformaInvoice", "Y", "[]", "Y"], ["sage", "1600", "400", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "1700", "500", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "780", "600", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "800", "600", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "900", "600", "salesShipment", "Y", "[]", "Y"], ["sage", "950", "600", "salesReturnRequest", "N", "[\"read\"]", "Y"], ["sage", "800", "700", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "900", "700", "salesShipment", "Y", "[]", "Y"], ["sage", "1000", "800", "salesShipment", "N", "[\"read\"]", "Y"], ["sage", "1050", "800", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "700", "900", "salesShipment", "N", "[\"read\"]", "Y"], ["sage", "750", "900", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "4000", "Support User", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "4100", "Support User", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "4200", "Support User", "salesInvoice", "Y", "[]", "Y"], ["sage", "4300", "Support User", "salesOrder", "Y", "[]", "Y"], ["sage", "4400", "Support User", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "4500", "Support User", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "4600", "Support User", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "20300", "Support User", "salesShipment", "Y", "[]", "Y"], ["sage", "20350", "Support User", "proformaInvoice", "Y", "[]", "Y"], ["sage", "4000", "Support User Read-only", "salesCreditMemoReason", "N", "[\"read\"]", "Y"], ["sage", "4100", "Support User Read-only", "salesCreditMemo", "N", "[\"read\"]", "Y"], ["sage", "4200", "Support User Read-only", "salesInvoice", "N", "[\"read\"]", "Y"], ["sage", "4300", "Support User Read-only", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "4400", "Support User Read-only", "salesReturnReceipt", "N", "[\"read\"]", "Y"], ["sage", "4500", "Support User Read-only", "salesReturnRequestReason", "N", "[\"read\"]", "Y"], ["sage", "4600", "Support User Read-only", "salesReturnRequest", "N", "[\"read\"]", "Y"], ["sage", "20300", "Support User Read-only", "salesShipment", "N", "[\"read\"]", "Y"], ["sage", "20350", "Support User Read-only", "proformaInvoice", "N", "[\"read\"]", "Y"], ["sage", "600", "Operational User", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "700", "Operational User", "salesShipment", "Y", "[]", "Y"], ["sage", "800", "Operational User", "salesOrder", "N", "[\"read\",\"print\"]", "Y"], ["sage", "900", "Operational User", "salesInvoice", "Y", "[]", "Y"], ["sage", "1000", "Operational User", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "1700", "Admin", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "1800", "Admin", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "1900", "Admin", "salesInvoice", "Y", "[]", "Y"], ["sage", "2000", "Admin", "salesOrder", "Y", "[]", "Y"], ["sage", "2100", "Admin", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "2200", "Admin", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "2300", "Admin", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "2400", "Admin", "salesShipment", "Y", "[]", "Y"], ["sage", "2500", "Admin", "proformaInvoice", "Y", "[]", "Y"], ["sage", "5400", "Business User", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "5500", "Business User", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "5600", "Business User", "salesInvoice", "Y", "[]", "Y"], ["sage", "5700", "Business User", "salesOrder", "Y", "[]", "Y"], ["sage", "5800", "Business User", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "5900", "Business User", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "6000", "Business User", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "6100", "Business User", "salesShipment", "Y", "[]", "Y"], ["sage", "6200", "Business User", "proformaInvoice", "Y", "[]", "Y"], ["sage", "430", "1100", "proformaInvoice", "N", "[\"print\"]", "Y"], ["sage", "440", "1100", "salesCreditMemo", "N", "[\"read\",\"manage\",\"print\",\"post\"]", "Y"], ["sage", "450", "1100", "salesCreditMemoReason", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "460", "1100", "salesInvoice", "N", "[\"read\",\"manage\",\"post\",\"print\"]", "Y"], ["sage", "17000", "100", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "17100", "100", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "13300", "Admin", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "13400", "Admin", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "1600", "Admin - Technical", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "1700", "Admin - Technical", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "21900", "Support User", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "22000", "Support User", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "21900", "Support User Read-only", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "22000", "Support User Read-only", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "700", "1100", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "710", "1100", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "800", "100", "avalaraCompany", "Y", "[]", "Y"], ["sage", "900", "100", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "1000", "100", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "1100", "100", "entityUse", "Y", "[]", "Y"], ["sage", "200", "Admin - Technical", "avalaraCompany", "Y", "[]", "Y"], ["sage", "300", "Admin - Technical", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "400", "Admin - Technical", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "500", "Admin - Technical", "entityUse", "Y", "[]", "Y"], ["sage", "600", "Support User", "avalaraCompany", "Y", "[]", "Y"], ["sage", "700", "Support User", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "800", "Support User", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "900", "Support User", "entityUse", "Y", "[]", "Y"], ["sage", "600", "Support User Read-only", "avalaraCompany", "N", "[\"read\"]", "Y"], ["sage", "700", "Support User Read-only", "avalaraConfiguration", "N", "[\"read\"]", "Y"], ["sage", "800", "Support User Read-only", "avalaraItemTax", "N", "[\"read\"]", "Y"], ["sage", "900", "Support User Read-only", "entityUse", "N", "[\"read\"]", "Y"], ["sage", "10600", "Admin", "avalaraCompany", "Y", "[]", "Y"], ["sage", "10700", "Admin", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "10800", "Admin", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "10900", "Admin", "entityUse", "Y", "[]", "Y"], ["sage", "1900", "Business User", "avalaraCompany", "Y", "[]", "Y"], ["sage", "2000", "Business User", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "2100", "Business User", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "2200", "Business User", "entityUse", "Y", "[]", "Y"], ["sage", "15300", "100", "cakeHrConfiguration", "Y", "[]", "Y"], ["sage", "600", "Admin - Technical", "cakeHrConfiguration", "Y", "[]", "Y"], ["sage", "1000", "Support User", "cakeHrConfiguration", "Y", "[]", "Y"], ["sage", "1000", "Support User Read-only", "cakeHrConfiguration", "N", "[\"read\"]", "Y"], ["sage", "11000", "Admin", "cakeHrConfiguration", "Y", "[]", "Y"], ["sage", "2300", "Business User", "cakeHrConfiguration", "Y", "[]", "Y"], ["sage", "9600", "100", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "9700", "100", "movementRule", "Y", "[]", "Y"], ["sage", "9800", "100", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "9900", "100", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "1900", "Support User", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "2000", "Support User", "movementRule", "Y", "[]", "Y"], ["sage", "2100", "Support User", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "2200", "Support User", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "1900", "Support User Read-only", "intrastatDeclaration", "N", "[\"read\"]", "Y"], ["sage", "2000", "Support User Read-only", "movementRule", "N", "[\"read\"]", "Y"], ["sage", "2100", "Support User Read-only", "natureOfTransaction", "N", "[\"read\"]", "Y"], ["sage", "2200", "Support User Read-only", "statisticalProcedure", "N", "[\"read\"]", "Y"], ["sage", "4700", "Admin", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "4800", "Admin", "movementRule", "Y", "[]", "Y"], ["sage", "4900", "Admin", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "5000", "Admin", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "3100", "Business User", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "3200", "Business User", "movementRule", "Y", "[]", "Y"], ["sage", "3300", "Business User", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "3400", "Business User", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "20", "1100", "intrastatDeclaration", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "30", "1100", "movementRule", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "40", "1100", "natureOfTransaction", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "50", "1100", "statisticalProcedure", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "700", "Admin - Technical", "frp1000Configuration", "Y", "[]", "Y"], ["sage", "1100", "Support User", "frp1000Configuration", "Y", "[]", "Y"], ["sage", "1100", "Support User Read-only", "frp1000Configuration", "N", "[\"read\"]", "Y"], ["sage", "11100", "Admin", "frp1000Configuration", "Y", "[]", "Y"], ["sage", "2400", "Business User", "frp1000Configuration", "Y", "[]", "Y"], ["sage", "9400", "100", "billOfMaterial", "Y", "[]", "Y"], ["sage", "2900", "600", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "5400", "600", "routing", "N", "[\"read\"]", "Y"], ["sage", "2500", "700", "billOfMaterial", "N", "[\"read\"]", "Y"], ["sage", "2400", "800", "billOfMaterial", "Y", "[]", "Y"], ["sage", "2500", "800", "routing", "Y", "[]", "Y"], ["sage", "2100", "900", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "1800", "1000", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "11400", "Support User", "billOfMaterial", "Y", "[]", "Y"], ["sage", "11600", "Support User", "routing", "Y", "[]", "Y"], ["sage", "11400", "Support User Read-only", "billOfMaterial", "N", "[\"read\"]", "Y"], ["sage", "11600", "Support User Read-only", "routing", "N", "[\"read\"]", "Y"], ["sage", "1700", "Operational User", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "5300", "Admin", "billOfMaterial", "Y", "[]", "Y"], ["sage", "5400", "Admin", "routing", "Y", "[]", "Y"], ["sage", "13300", "Business User", "billOfMaterial", "Y", "[]", "Y"], ["sage", "13400", "Business User", "routing", "Y", "[]", "Y"], ["sage", "16400", "100", "productionTracking", "Y", "[]", "Y"], ["sage", "16500", "100", "workInProgressCost", "Y", "[]", "Y"], ["sage", "1300", "200", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "1900", "300", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "1700", "400", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "1800", "500", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "100", "600", "workOrder", "N", "[\"read\",\"tracking\"]", "Y"], ["sage", "200", "600", "materialTracking", "Y", "[]", "Y"], ["sage", "300", "600", "operationTracking", "N", "[\"read\"]", "Y"], ["sage", "400", "600", "productionTracking", "N", "[\"read\"]", "Y"], ["sage", "550", "600", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "100", "700", "workOrder", "N", "[\"read\",\"tracking\"]", "Y"], ["sage", "200", "700", "materialTracking", "Y", "[]", "Y"], ["sage", "100", "800", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "200", "800", "materialTracking", "N", "[\"read\"]", "Y"], ["sage", "300", "800", "operationTracking", "N", "[\"read\"]", "Y"], ["sage", "400", "800", "productionTracking", "N", "[\"read\"]", "Y"], ["sage", "450", "800", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "100", "900", "workOrder", "Y", "[]", "Y"], ["sage", "300", "900", "materialTracking", "Y", "[]", "Y"], ["sage", "400", "900", "operationTracking", "Y", "[]", "Y"], ["sage", "500", "900", "productionTracking", "Y", "[]", "Y"], ["sage", "550", "900", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "650", "900", "workOrderCategory", "Y", "[]", "Y"], ["sage", "200", "1000", "materialTracking", "Y", "[]", "Y"], ["sage", "300", "1000", "operationTracking", "Y", "[]", "Y"], ["sage", "400", "1000", "productionTracking", "Y", "[]", "Y"], ["sage", "500", "1000", "workOrder", "N", "[\"read\",\"manage\",\"close\",\"repost\",\"tracking\"]", "Y"], ["sage", "650", "1000", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "2700", "Support User", "materialTracking", "Y", "[]", "Y"], ["sage", "2800", "Support User", "operationTracking", "Y", "[]", "Y"], ["sage", "2900", "Support User", "productionTracking", "Y", "[]", "Y"], ["sage", "3000", "Support User", "workInProgressCost", "Y", "[]", "Y"], ["sage", "3100", "Support User", "workOrderCategory", "Y", "[]", "Y"], ["sage", "3300", "Support User", "workOrder", "Y", "[]", "Y"], ["sage", "2700", "Support User Read-only", "materialTracking", "N", "[\"read\"]", "Y"], ["sage", "2800", "Support User Read-only", "operationTracking", "N", "[\"read\"]", "Y"], ["sage", "2900", "Support User Read-only", "productionTracking", "N", "[\"read\"]", "Y"], ["sage", "3000", "Support User Read-only", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "3100", "Support User Read-only", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "3300", "Support User Read-only", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "20", "Operational User", "materialTracking", "Y", "[]", "Y"], ["sage", "30", "Operational User", "operationTracking", "Y", "[]", "Y"], ["sage", "40", "Operational User", "productionTracking", "Y", "[]", "Y"], ["sage", "260", "Operational User", "workOrder", "N", "[\"read\",\"manage\",\"close\",\"repost\",\"tracking\"]", "Y"], ["sage", "270", "Operational User", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "2600", "Admin", "materialTracking", "Y", "[]", "Y"], ["sage", "2700", "Admin", "operationTracking", "Y", "[]", "Y"], ["sage", "2800", "Admin", "productionTracking", "Y", "[]", "Y"], ["sage", "2900", "Admin", "workInProgressCost", "Y", "[]", "Y"], ["sage", "3000", "Admin", "workOrderCategory", "Y", "[]", "Y"], ["sage", "3100", "Admin", "workOrder", "Y", "[]", "Y"], ["sage", "4200", "Business User", "materialTracking", "Y", "[]", "Y"], ["sage", "4300", "Business User", "operationTracking", "Y", "[]", "Y"], ["sage", "4400", "Business User", "productionTracking", "Y", "[]", "Y"], ["sage", "4500", "Business User", "workInProgressCost", "Y", "[]", "Y"], ["sage", "4600", "Business User", "workOrderCategory", "Y", "[]", "Y"], ["sage", "4700", "Business User", "workOrder", "Y", "[]", "Y"], ["sage", "280", "1100", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "1300", "100", "frp1000Map", "N", "[\"read\",\"create\",\"update\",\"request\",\"delete\"]", "Y"], ["sage", "800", "Admin - Technical", "frp1000Map", "N", "[\"read\",\"create\",\"update\",\"request\",\"delete\"]", "Y"], ["sage", "1200", "Support User", "frp1000Map", "Y", "[]", "Y"], ["sage", "1200", "Support User Read-only", "frp1000Map", "N", "[\"read\"]", "Y"], ["sage", "3200", "200", "importData", "Y", "[]", "Y"], ["sage", "2200", "300", "importData", "Y", "[]", "Y"], ["sage", "3400", "400", "importData", "Y", "[]", "Y"], ["sage", "2300", "500", "importData", "Y", "[]", "Y"], ["sage", "6000", "600", "importData", "Y", "[]", "Y"], ["sage", "6100", "700", "importData", "Y", "[]", "Y"], ["sage", "3600", "800", "importData", "Y", "[]", "Y"], ["sage", "3100", "900", "importData", "N", "[]", "Y"], ["sage", "2000", "1000", "importData", "N", "[]", "Y"], ["sage", "15500", "100", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3300", "200", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2300", "300", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3500", "400", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2400", "500", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6100", "600", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6200", "700", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6300", "800", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3200", "900", "importExportTemplate", "N", "[]", "Y"], ["sage", "2100", "1000", "importExportTemplate", "N", "[]", "Y"], ["sage", "11800", "Support User", "importData", "Y", "[]", "Y"], ["sage", "12900", "Support User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "11800", "Support User Read-only", "importData", "N", "[\"read\"]", "Y"], ["sage", "12900", "Support User Read-only", "importExportTemplate", "N", "[\"read\"]", "Y"], ["sage", "11900", "Admin", "importData", "Y", "[]", "Y"], ["sage", "12000", "Admin", "importExportTemplate", "Y", "[]", "Y"], ["sage", "1100", "Business User", "importData", "Y", "[]", "Y"], ["sage", "1200", "Business User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "1400", "100", "intacct", "Y", "[]", "Y"], ["sage", "900", "Admin - Technical", "intacct", "Y", "[]", "Y"], ["sage", "1300", "Support User", "intacct", "Y", "[]", "Y"], ["sage", "1300", "Support User Read-only", "intacct", "N", "[\"read\"]", "Y"], ["sage", "11200", "Admin", "intacct", "Y", "[]", "Y"], ["sage", "2500", "Business User", "intacct", "Y", "[]", "Y"], ["sage", "250", "1100", "intacct", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "1500", "100", "intacctMap", "Y", "[]", "Y"], ["sage", "15400", "100", "financeListener", "Y", "[]", "Y"], ["sage", "1000", "Admin - Technical", "intacctMap", "Y", "[]", "Y"], ["sage", "1400", "Support User", "intacctBankAccountTransactionFeed", "Y", "[]", "Y"], ["sage", "1500", "Support User", "intacctMap", "Y", "[]", "Y"], ["sage", "1400", "Support User Read-only", "intacctBankAccountTransactionFeed", "N", "[\"read\"]", "Y"], ["sage", "1500", "Support User Read-only", "intacctMap", "N", "[\"read\"]", "Y"], ["sage", "11300", "Admin", "intacctMap", "Y", "[]", "Y"], ["sage", "11400", "Admin", "intacctBankAccountTransactionFeed", "Y", "[]", "Y"], ["sage", "11800", "Admin", "financeListener", "Y", "[]", "Y"], ["sage", "2700", "Business User", "intacctBankAccountTransactionFeed", "Y", "[]", "Y"], ["sage", "2800", "Business User", "financeListener", "Y", "[]", "Y"], ["sage", "260", "1100", "financeListener", "N", "[\"retryFinanceDocument\"]", "Y"], ["sage", "270", "1100", "intacctMap", "N", "[\"read\",\"manage\",\"synchronizationWithSageIntacct\"]", "Y"], ["sage", "1100", "Admin - Technical", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "1200", "Admin - Technical", "taxRateRepository", "Y", "[]", "Y"], ["sage", "1600", "Support User", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "1700", "Support User", "taxRateRepository", "Y", "[]", "Y"], ["sage", "1600", "Support User Read-only", "organisationServiceFabric", "N", "[\"manage\"]", "Y"], ["sage", "1700", "Support User Read-only", "taxRateRepository", "N", "[\"manage\"]", "Y"], ["sage", "11500", "Admin", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "11600", "Admin", "taxRateRepository", "Y", "[]", "Y"], ["sage", "2900", "Business User", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "3000", "Business User", "taxRateRepository", "Y", "[]", "Y"], ["sage", "470", "1100", "organisationServiceFabric", "N", "[\"manage\"]", "Y"], ["sage", "480", "1100", "taxRateRepository", "N", "[\"manage\"]", "Y"], ["sage", "2000", "400", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "400", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "2000", "500", "mrpCalculation", "N", "[\"read\"]", "Y"], ["sage", "2000", "600", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "600", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "2000", "800", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "800", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "2000", "900", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "900", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "10000", "Support User", "mrpCalculation", "Y", "[]", "Y"], ["sage", "10100", "Support User", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "10000", "Support User Read-only", "mrpCalculation", "N", "[\"read\"]", "Y"], ["sage", "10100", "Support User Read-only", "mrpSynchronization", "N", "[\"read\"]", "Y"], ["sage", "2000", "Admin", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "Admin", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "11700", "Business User", "mrpCalculation", "Y", "[]", "Y"], ["sage", "11800", "Business User", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "3000", "600", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "3100", "600", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "3400", "600", "stockAdjustment", "Y", "[]", "Y"], ["sage", "4900", "600", "stockChange", "Y", "[]", "Y"], ["sage", "5000", "600", "stockCount", "Y", "[]", "Y"], ["sage", "5100", "600", "stockIssue", "Y", "[]", "Y"], ["sage", "5200", "600", "stockReceipt", "Y", "[]", "Y"], ["sage", "5300", "600", "stockValueChange", "Y", "[]", "Y"], ["sage", "1000", "700", "stockIssue", "Y", "[]", "Y"], ["sage", "3500", "700", "stockAdjustment", "Y", "[]", "Y"], ["sage", "5100", "700", "stockCount", "Y", "[]", "Y"], ["sage", "5200", "700", "stockReceipt", "Y", "[]", "Y"], ["sage", "5300", "700", "stockValueChange", "Y", "[]", "Y"], ["sage", "3200", "800", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "3300", "800", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "4700", "Support User", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "4800", "Support User", "stockAdjustment", "Y", "[]", "Y"], ["sage", "4900", "Support User", "stockChange", "Y", "[]", "Y"], ["sage", "5000", "Support User", "stockCount", "Y", "[]", "Y"], ["sage", "5100", "Support User", "stockIssue", "Y", "[]", "Y"], ["sage", "5200", "Support User", "stockReceipt", "Y", "[]", "Y"], ["sage", "5250", "Support User", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "5300", "Support User", "stockValueChange", "Y", "[]", "Y"], ["sage", "22400", "Support User", "stockReorder", "Y", "[]", "Y"], ["sage", "4700", "Support User Read-only", "stockValuationInputSet", "N", "[\"read\"]", "Y"], ["sage", "4800", "Support User Read-only", "stockAdjustment", "N", "[\"read\"]", "Y"], ["sage", "4900", "Support User Read-only", "stockChange", "N", "[\"read\"]", "Y"], ["sage", "5000", "Support User Read-only", "stockCount", "N", "[\"read\"]", "Y"], ["sage", "5100", "Support User Read-only", "stockIssue", "N", "[\"read\"]", "Y"], ["sage", "5200", "Support User Read-only", "stockReceipt", "N", "[\"read\"]", "Y"], ["sage", "190", "Operational User", "stockAdjustment", "Y", "[]", "Y"], ["sage", "210", "Operational User", "stockCount", "Y", "[]", "Y"], ["sage", "220", "Operational User", "stockIssue", "Y", "[]", "Y"], ["sage", "230", "Operational User", "stockReceipt", "Y", "[]", "Y"], ["sage", "235", "Operational User", "stockValueChange", "Y", "[]", "Y"], ["sage", "3200", "Admin", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "3250", "Admin", "stockCount", "Y", "[]", "Y"], ["sage", "3300", "Admin", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "3400", "Admin", "stockAdjustment", "Y", "[]", "Y"], ["sage", "3500", "Admin", "stockChange", "Y", "[]", "Y"], ["sage", "3700", "Admin", "stockReceipt", "Y", "[]", "Y"], ["sage", "3800", "Admin", "stockValueChange", "Y", "[]", "Y"], ["sage", "3900", "Admin", "stockIssue", "Y", "[]", "Y"], ["sage", "14000", "Admin", "stockReorder", "Y", "[]", "Y"], ["sage", "6300", "Business User", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "6400", "Business User", "stockCount", "Y", "[]", "Y"], ["sage", "6500", "Business User", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "6600", "Business User", "stockAdjustment", "Y", "[]", "Y"], ["sage", "6700", "Business User", "stockChange", "Y", "[]", "Y"], ["sage", "6800", "Business User", "stockReceipt", "Y", "[]", "Y"], ["sage", "6900", "Business User", "stockValueChange", "Y", "[]", "Y"], ["sage", "7000", "Business User", "stockIssue", "Y", "[]", "Y"], ["sage", "14300", "Business User", "stockReorder", "Y", "[]", "Y"], ["sage", "490", "1100", "costRollUpInputSet", "N", "[\"manage\"]", "Y"], ["sage", "500", "1100", "stockValuationInputSet", "N", "[\"read\",\"manageCost\"]", "Y"], ["sage", "510", "1100", "stockValueChange", "N", "[\"read\",\"manage\",\"post\"]", "Y"], ["sage", "4600", "200", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "4700", "200", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "48001", "200", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "200", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "4700", "400", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "4800", "400", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "4900", "400", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "400", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "3200", "500", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "3300", "500", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "3400", "500", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "500", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "7100", "600", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "7200", "600", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "7300", "600", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "600", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "6800", "700", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "6900", "700", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "7000", "700", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "700", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "7500", "900", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "7600", "900", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "7700", "900", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "900", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "2600", "1000", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "2700", "1000", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "2800", "1000", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "1000", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "13400", "Admin", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "13500", "Admin", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "13600", "Admin", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "Admin", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "13700", "Admin", "supplyPlanning", "Y", "[]", "Y"], ["sage", "21900", "Support User Read-only", "stockTransferOrder", "N", "[\"read\"]", "Y"], ["sage", "22000", "Support User Read-only", "stockTransferShipment", "N", "[\"read\"]", "Y"], ["sage", "22100", "Support User Read-only", "stockTransferReceipt", "N", "[\"read\"]", "Y"], ["sage", "5000", "Support User Read-only", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "22200", "Support User Read-only", "supplyPlanning", "N", "[\"read\"]", "Y"], ["sage", "21900", "Support User", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "22000", "Support User", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "22100", "Support User", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "Support User", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "22200", "Support User", "supplyPlanning", "Y", "[]", "Y"], ["sage", "1900", "Operational User", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "2000", "Operational User", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "2100", "Operational User", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "Operational User", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "5100", "Operational User", "supplyPlanning", "Y", "[]", "Y"]]}, "SequenceNumber": {"metadata": {"rootFactoryName": "BaseSequenceNumber", "name": "SequenceNumber", "baseFactoryName": "BaseSequenceNumber", "naturalKeyColumns": ["_tenant_id", "id", "legislation"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "definingFactory": "BaseSequenceNumber", "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string", "definingFactory": "BaseSequenceNumber"}, {"name": "name", "type": "string", "isLocalized": true, "definingFactory": "BaseSequenceNumber"}, {"name": "definition_level", "type": "enum", "definingFactory": "BaseSequenceNumber", "enumMembers": ["application", "company", "site"]}, {"name": "rtz_level", "type": "enum", "definingFactory": "BaseSequenceNumber", "enumMembers": ["noReset", "yearly", "monthly"]}, {"name": "is_cleared_by_reset", "type": "boolean", "definingFactory": "BaseSequenceNumber"}, {"name": "type", "type": "enum", "definingFactory": "BaseSequenceNumber", "enumMembers": ["alphanumeric", "numeric"]}, {"name": "is_chronological", "type": "boolean", "definingFactory": "BaseSequenceNumber"}, {"name": "legislation", "type": "reference", "isNullable": true, "definingFactory": "BaseSequenceNumber", "targetFactoryName": "Legislation"}]}, "rows": [["", "ACH", "{\"en\":\"Purchasing\",\"de-DE\":\"Ein<PERSON>uf\",\"en-US\":\"Purchasing\",\"fr-FR\":\"Achats\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "ItemlLot", "{\"en\":\"Item lot number\",\"de-DE\":\"Chargennummer Artikel\",\"en-US\":\"Item lot number\",\"fr-FR\":\"Numéro lot article\"}", "application", "noReset", "N", "alphanumeric", "N", ""], ["", "ItemSerial", "{\"en\":\"Item serial number\",\"de-DE\":\"Seriennummer Artikel\",\"en-US\":\"Item serial number\",\"fr-FR\":\"Numéro série article\"}", "application", "noReset", "N", "alphanumeric", "N", ""], ["", "MATLOT", "{\"en\":\"Material lot number\",\"de-DE\":\"Chargennummer Material\",\"en-US\":\"Material lot number\",\"fr-FR\":\"Numéro lot matière\"}", "application", "noReset", "N", "alphanumeric", "N", ""], ["", "MiscStockIssue", "{\"en\":\"Miscellaneous stock issue\",\"de-DE\":\"Sonstiger Bestandsabgang\",\"en-US\":\"Miscellaneous stock issue\",\"fr-FR\":\"Sortie de stock diverse\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "MiscStockReceipt", "{\"en\":\"Miscellaneous stock receipt\",\"de-DE\":\"Sonstiger Bestandseingang\",\"en-US\":\"Miscellaneous stock receipt\",\"fr-FR\":\"Entrée de stock diverse\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "OD", "{\"en\":\"Miscellaneous operations\",\"de-DE\":\"Sonstige Vorgänge\",\"en-US\":\"Miscellaneous operations\",\"fr-FR\":\"Opérations Diverses\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PostedSalesCreditMemo", "{\"en\":\"Posted sales credit memo\",\"de-DE\":\"Gebuchte Verkaufsgutschrift\",\"en-US\":\"Posted sales credit memo\",\"fr-FR\":\"Avoir de vente comptabilisé\"}", "site", "yearly", "N", "alphanumeric", "Y", ""], ["", "PostedSalesInvoice", "{\"en\":\"Posted sales invoice\",\"de-DE\":\"Gebuchte Verkaufsrechnung\",\"en-US\":\"Posted sales invoice\",\"fr-FR\":\"Facture de vente comptabilisée\"}", "site", "yearly", "N", "alphanumeric", "Y", ""], ["", "PurchaseCreditMemo", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"en-US\":\"Purchase credit memo\",\"fr-FR\":\"Avoir d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseInvoice", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"en-US\":\"Purchase invoice\",\"fr-FR\":\"Facture d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseOrder", "{\"en\":\"Purchase order\",\"de-DE\":\"Bestellung\",\"en-US\":\"Purchase order\",\"fr-FR\":\"Commande d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseOrderSuggestion", "{\"en\":\"Purchase order suggestion\",\"de-DE\":\"Bestellvorschlag\",\"en-US\":\"Purchase order suggestion\",\"fr-FR\":\"Commande d'achat suggérée\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseReceipt", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"en-US\":\"Purchase receipt\",\"fr-FR\":\"Réception d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseRequisition", "{\"en\":\"Purchase requisition\",\"de-DE\":\"Bestellanforderung\",\"en-US\":\"Purchase requisition\",\"fr-FR\":\"Demand<PERSON> d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "PurchaseReturn", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"en-US\":\"Purchase return\",\"fr-FR\":\"Retour d'achat\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesCreditMemo", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"en-US\":\"Sales credit memo\",\"fr-FR\":\"Avoir de vente\"}", "site", "yearly", "Y", "alphanumeric", "Y", ""], ["", "SalesInvoice", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"en-US\":\"Sales invoice\",\"fr-FR\":\"Facture de vente\"}", "site", "yearly", "Y", "alphanumeric", "Y", ""], ["", "SalesOrder", "{\"en\":\"Sales order\",\"de-DE\":\"Verkaufsauftrag\",\"en-US\":\"Sales order\",\"fr-FR\":\"Commande de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesReturnReceipt", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"en-US\":\"Sales return receipt\",\"fr-FR\":\"Réception retour de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesReturnRequest", "{\"en\":\"Sales return request\",\"de-DE\":\"Verkaufsretourenanforderung\",\"en-US\":\"Sales return request\",\"fr-FR\":\"Demande retour de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesShipment", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"en-US\":\"Sales shipment\",\"fr-FR\":\"Expédition de vente\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockAdjustment", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockChange", "{\"en\":\"Stock change\",\"de-DE\":\"Bestandsänderung\",\"en-US\":\"Stock change\",\"fr-FR\":\"Changement stock\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockCount", "{\"en\":\"Stock count\",\"de-DE\":\"Inventur\",\"en-US\":\"Stock count\",\"fr-FR\":\"Inventaire\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockCountList", "{\"en\":\"Stock count list\",\"de-DE\":\"Inventurliste\",\"en-US\":\"Stock count list\",\"fr-FR\":\"Liste d'inventaire\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "StockCountSession", "{\"en\":\"Stock count session\",\"de-DE\":\"Inventurvorgang\",\"en-US\":\"Stock count session\",\"fr-FR\":\"Session d'inventaire\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "StockJournal", "{\"en\":\"Stock journal\",\"de-DE\":\"Bestandsjournal\",\"en-US\":\"Stock journal\",\"fr-FR\":\"Journal de stock\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "UnbilledJournal", "{\"en\":\"Unbilled journal\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten Buchaltung\",\"en-US\":\"Unbilled journal\",\"fr-FR\":\"Journal non facturé\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "VEN", "{\"en\":\"Sales\",\"de-DE\":\"Verkauf\",\"en-US\":\"Sales\",\"fr-FR\":\"Ventes\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "WorkInProgressJournal", "{\"en\":\"Work in progress Journal\",\"de-DE\":\"Work-In-Progress-Journal\",\"en-US\":\"Work in progress Journal\",\"fr-FR\":\"Journal en-cours\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "WorkOrder", "{\"en\":\"Work order\",\"de-DE\":\"Fertigungsauftrag\",\"en-US\":\"Work order\",\"fr-FR\":\"Ordre de fabrication\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "WorkOrderSuggestion", "{\"en\":\"Work order suggestion\",\"de-DE\":\"Vorschlag Fertigungsauftrag\",\"en-US\":\"Work order suggestion\",\"fr-FR\":\"Ordre de fabrication suggéré\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "WorkOrderTracking", "{\"en\":\"Work order tracking\",\"de-DE\":\"Rückmeldung Fertigungsaufträge\",\"en-US\":\"Work order tracking\",\"fr-FR\":\"Suivi d’ordre de fabrication\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesInvoiceUSA", "{\"en\":\"Sales invoice USA\",\"de-DE\":\"Verkaufsrechnung USA\",\"en-US\":\"Sales invoice USA\",\"fr-FR\":\"Facture de vente USA\"}", "site", "yearly", "Y", "alphanumeric", "Y", "US"], ["", "CashDisbursementsJournal", "{\"en\":\"Cash disbursements journal\",\"de-DE\":\"Zahlungsausgangsbeleg Bank Manager\",\"en-US\":\"Cash disbursements journal\",\"fr-FR\":\"Journal des décaissements\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "CashReceiptsJournal", "{\"en\":\"Cash receipts journal\",\"de-DE\":\"Zahlungseingangsbeleg Bank Manager\",\"en-US\":\"Cash receipts journal\",\"fr-FR\":\"Journal des encaissements\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "EREC", "{\"en\":\"Purchasing\",\"de-DE\":\"Ein<PERSON>uf\",\"en-US\":\"Purchasing\",\"fr-FR\":\"Achats\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "VREC", "{\"en\":\"Sales\",\"de-DE\":\"Verkauf\",\"en-US\":\"Sales\",\"fr-FR\":\"Ventes\"}", "application", "yearly", "Y", "alphanumeric", "N", ""], ["", "StockValueChange", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"en-US\":\"Stock value change\",\"fr-FR\":\"Changement de la valeur de stock\"}", "application", "yearly", "N", "alphanumeric", "N", ""], ["", "Receipt", "{\"en\":\"Receipt\",\"de-DE\":\"Zahlungseingang\",\"en-US\":\"Receipt\",\"fr-FR\":\"Réception du paiement\"}", "site", "yearly", "Y", "alphanumeric", "N", ""], ["", "Payment", "{\"en\":\"Payment\",\"de-DE\":\"Zahlungsausgang\",\"en-US\":\"Payment\",\"fr-FR\":\"Emission du paiement\"}", "site", "yearly", "Y", "alphanumeric", "N", ""], ["", "SalesJournal", "{\"en\":\"Sales journal\",\"de-DE\":\"Verkaufsbeleg Buchhaltung\",\"en-US\":\"Sales journal\",\"fr-FR\":\"Journal de vente\"}", "application", "yearly", "Y", "alphanumeric", null, ""], ["", "StockTransferOrder", "{\"en\":\"Stock transfer order\",\"de-DE\":\"\",\"en-US\":\"Stock transfer order\",\"fr-FR\":\"\"}", "application", "yearly", null, "alphanumeric", null, ""], ["", "StockTransferShipment", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"\",\"en-US\":\"Stock transfer shipment\",\"fr-FR\":\"\"}", "application", "yearly", null, "alphanumeric", null, ""], ["", "StockTransferReceipt", "{\"en\":\"Stock transfer receipt\",\"de-DE\":\"\",\"en-US\":\"Stock transfer receipt\",\"fr-FR\":\"\"}", "application", "yearly", null, "alphanumeric", null, ""]]}, "SequenceNumberComponent": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "BaseSequenceNumberComponent", "name": "SequenceNumberComponent", "baseFactoryName": "BaseSequenceNumberComponent", "naturalKeyColumns": ["_tenant_id", "sequence_number", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "definingFactory": "BaseSequenceNumberComponent", "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer", "definingFactory": "BaseSequenceNumberComponent"}, {"name": "sequence_number", "type": "reference", "definingFactory": "BaseSequenceNumberComponent", "targetFactoryName": "SequenceNumber", "targetRootFactoryName": "BaseSequenceNumber"}, {"name": "type", "type": "enum", "definingFactory": "BaseSequenceNumberComponent", "enumMembers": ["constant", "year", "month", "week", "day", "company", "site", "sequenceNumber", "sequenceAlpha"]}, {"name": "constant", "type": "string", "definingFactory": "BaseSequenceNumberComponent"}, {"name": "length", "type": "short", "definingFactory": "BaseSequenceNumberComponent"}], "vitalParentColumn": {"name": "sequence_number", "type": "reference", "definingFactory": "BaseSequenceNumberComponent", "targetFactoryName": "SequenceNumber", "targetRootFactoryName": "BaseSequenceNumber"}}, "rows": [["", "100", "ItemSerial|", "constant", "SN", "2"], ["", "200", "ItemSerial|", "sequenceNumber", "", "6"], ["", "100", "ItemlLot|", "year", "", "2"], ["", "200", "ItemlLot|", "sequenceNumber", "", "6"], ["", "100", "MATLOT|", "year", "", "2"], ["", "200", "MATLOT|", "sequenceNumber", "", "6"], ["", "100", "MiscStockIssue|", "constant", "SS", "2"], ["", "200", "MiscStockIssue|", "year", "", "2"], ["", "300", "MiscStockIssue|", "sequenceNumber", "", "4"], ["", "100", "MiscStockReceipt|", "constant", "SR", "2"], ["", "200", "MiscStockReceipt|", "year", "", "2"], ["", "300", "MiscStockReceipt|", "sequenceNumber", "", "4"], ["", "100", "PurchaseCreditMemo|", "constant", "PC", "2"], ["", "200", "PurchaseCreditMemo|", "year", "", "2"], ["", "300", "PurchaseCreditMemo|", "sequenceNumber", "", "4"], ["", "100", "PurchaseInvoice|", "constant", "PI", "2"], ["", "200", "PurchaseInvoice|", "year", "", "2"], ["", "300", "PurchaseInvoice|", "sequenceNumber", "", "4"], ["", "100", "PurchaseOrder|", "constant", "PO", "2"], ["", "200", "PurchaseOrder|", "year", "", "2"], ["", "300", "PurchaseOrder|", "sequenceNumber", "", "4"], ["", "100", "PurchaseOrderSuggestion|", "constant", "POS", "3"], ["", "200", "PurchaseOrderSuggestion|", "year", "", "2"], ["", "300", "PurchaseOrderSuggestion|", "sequenceNumber", "", "6"], ["", "100", "PurchaseReceipt|", "constant", "PR", "2"], ["", "200", "PurchaseReceipt|", "year", "", "2"], ["", "300", "PurchaseReceipt|", "sequenceNumber", "", "4"], ["", "100", "PurchaseRequisition|", "constant", "PQ", "2"], ["", "200", "PurchaseRequisition|", "year", "", "2"], ["", "300", "PurchaseRequisition|", "sequenceNumber", "", "4"], ["", "100", "PurchaseReturn|", "constant", "PT", "2"], ["", "200", "PurchaseReturn|", "year", "", "2"], ["", "300", "PurchaseReturn|", "sequenceNumber", "", "4"], ["", "100", "SalesCreditMemo|", "constant", "SC", "2"], ["", "200", "SalesCreditMemo|", "site", "", "2"], ["", "300", "SalesCreditMemo|", "year", "", "2"], ["", "400", "SalesCreditMemo|", "sequenceNumber", "", "4"], ["", "100", "SalesInvoice|", "constant", "SI", "2"], ["", "200", "SalesInvoice|", "site", "", "2"], ["", "300", "SalesInvoice|", "year", "", "2"], ["", "400", "SalesInvoice|", "sequenceNumber", "", "4"], ["", "100", "SalesOrder|", "constant", "SO", "2"], ["", "200", "SalesOrder|", "year", "", "2"], ["", "300", "SalesOrder|", "sequenceNumber", "", "4"], ["", "100", "SalesReturnReceipt|", "constant", "SRR", "3"], ["", "200", "SalesReturnReceipt|", "year", "", "2"], ["", "300", "SalesReturnReceipt|", "sequenceNumber", "", "4"], ["", "100", "SalesReturnRequest|", "constant", "ST", "2"], ["", "200", "SalesReturnRequest|", "year", "", "2"], ["", "300", "SalesReturnRequest|", "sequenceNumber", "", "4"], ["", "100", "SalesShipment|", "constant", "SH", "2"], ["", "200", "SalesShipment|", "year", "", "2"], ["", "300", "SalesShipment|", "sequenceNumber", "", "4"], ["", "100", "StockAdjustment|", "constant", "SA", "2"], ["", "200", "StockAdjustment|", "year", "", "2"], ["", "300", "StockAdjustment|", "sequenceNumber", "", "4"], ["", "100", "StockChange|", "constant", "SG", "2"], ["", "200", "StockChange|", "year", "", "2"], ["", "300", "StockChange|", "sequenceNumber", "", "4"], ["", "100", "StockCount|", "constant", "CS", "2"], ["", "200", "StockCount|", "year", "", "2"], ["", "300", "StockCount|", "sequenceNumber", "", "4"], ["", "100", "StockCountList|", "constant", "CL", "2"], ["", "200", "StockCountList|", "year", "", "2"], ["", "300", "StockCountList|", "sequenceNumber", "", "4"], ["", "100", "StockCountSession|", "constant", "CS", "2"], ["", "200", "StockCountSession|", "year", "", "2"], ["", "300", "StockCountSession|", "sequenceNumber", "", "4"], ["", "100", "StockJournal|", "constant", "IJ", "2"], ["", "200", "StockJournal|", "year", "", "2"], ["", "300", "StockJournal|", "sequenceNumber", "", "4"], ["", "100", "UnbilledJournal|", "constant", "UN", "2"], ["", "200", "UnbilledJournal|", "year", "", "2"], ["", "300", "UnbilledJournal|", "sequenceNumber", "", "4"], ["", "100", "WorkInProgressJournal|", "constant", "WIP", "3"], ["", "200", "WorkInProgressJournal|", "year", "", "2"], ["", "300", "WorkInProgressJournal|", "sequenceNumber", "", "6"], ["", "100", "WorkOrder|", "constant", "WO", "2"], ["", "200", "WorkOrder|", "year", "", "2"], ["", "300", "WorkOrder|", "sequenceNumber", "", "4"], ["", "100", "WorkOrderSuggestion|", "constant", "WOS", "3"], ["", "200", "WorkOrderSuggestion|", "year", "", "2"], ["", "300", "WorkOrderSuggestion|", "sequenceNumber", "", "6"], ["", "100", "WorkOrderTracking|", "constant", "WT", "2"], ["", "200", "WorkOrderTracking|", "year", "", "2"], ["", "300", "WorkOrderTracking|", "sequenceNumber", "", "6"], ["", "100", "OD|", "constant", "OD", "2"], ["", "200", "OD|", "year", "", "2"], ["", "300", "OD|", "sequenceNumber", "", "4"], ["", "100", "ACH|", "constant", "AC", "2"], ["", "200", "ACH|", "year", "", "2"], ["", "300", "ACH|", "sequenceNumber", "", "4"], ["", "100", "VEN|", "constant", "VE", "2"], ["", "200", "VEN|", "year", "", "2"], ["", "300", "VEN|", "sequenceNumber", "", "4"], ["", "100", "PostedSalesInvoice|", "constant", "SIP", "3"], ["", "200", "PostedSalesInvoice|", "site", "", "2"], ["", "300", "PostedSalesInvoice|", "year", "", "2"], ["", "400", "PostedSalesInvoice|", "sequenceNumber", "", "4"], ["", "100", "PostedSalesCreditMemo|", "constant", "SCP", "3"], ["", "200", "PostedSalesCreditMemo|", "site", "", "2"], ["", "300", "PostedSalesCreditMemo|", "year", "", "2"], ["", "400", "PostedSalesCreditMemo|", "sequenceNumber", "", "4"], ["", "100", "SalesInvoiceUSA|US", "constant", "SIT", "3"], ["", "200", "SalesInvoiceUSA|US", "site", "", "2"], ["", "300", "SalesInvoiceUSA|US", "year", "", "2"], ["", "400", "SalesInvoiceUSA|US", "sequenceNumber", "", "4"], ["", "100", "CashDisbursementsJournal|", "constant", "CDJ", "3"], ["", "200", "CashDisbursementsJournal|", "year", "", "2"], ["", "300", "CashDisbursementsJournal|", "sequenceNumber", "", "4"], ["", "100", "CashReceiptsJournal|", "constant", "CRJ", "3"], ["", "200", "CashReceiptsJournal|", "year", "", "2"], ["", "300", "CashReceiptsJournal|", "sequenceNumber", "", "4"], ["", "100", "EREC|", "constant", "AC", "2"], ["", "200", "EREC|", "year", "", "2"], ["", "300", "EREC|", "sequenceNumber", "", "4"], ["", "100", "VREC|", "constant", "VE", "2"], ["", "200", "VREC|", "year", "", "2"], ["", "300", "VREC|", "sequenceNumber", "", "4"], ["", "100", "StockValueChange|", "constant", "SVC", "3"], ["", "200", "StockValueChange|", "year", "", "2"], ["", "300", "StockValueChange|", "sequenceNumber", "", "4"], ["", "100", "Receipt|", "constant", "RCT", "3"], ["", "200", "Receipt|", "site", "", "2"], ["", "300", "Receipt|", "year", "", "2"], ["", "400", "Receipt|", "sequenceNumber", "", "4"], ["", "100", "Payment|", "constant", "PAY", "3"], ["", "200", "Payment|", "site", "", "2"], ["", "300", "Payment|", "year", "", "2"], ["", "400", "Payment|", "sequenceNumber", "", "4"], ["", "100", "SalesJournal|", "constant", "SJ", "2"], ["", "200", "SalesJournal|", "year", "", "2"], ["", "300", "SalesJournal|", "sequenceNumber", "", "4"], ["", "100", "StockTransferOrder|", "constant", "TO", "2"], ["", "200", "StockTransferOrder|", "year", "", "2"], ["", "300", "StockTransferOrder|", "sequenceNumber", "", "4"], ["", "100", "StockTransferShipment|", "constant", "TS", "2"], ["", "200", "StockTransferShipment|", "year", "", "2"], ["", "300", "StockTransferShipment|", "sequenceNumber", "", "4"], ["", "100", "StockTransferReceipt|", "constant", "TR", "2"], ["", "200", "StockTransferReceipt|", "year", "", "2"], ["", "300", "StockTransferReceipt|", "sequenceNumber", "", "4"]]}}}