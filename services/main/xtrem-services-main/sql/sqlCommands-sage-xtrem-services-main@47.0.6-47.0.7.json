{"fromVersion": "47.0.6", "toVersion": "47.0.7", "gitHead": "3ef82a1c4cd2bbee374ead7ae932e9d11ae7b0c1", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN ", "    \tSELECT trigger_name, event_object_table ", "    \tFROM information_schema.triggers ", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN ", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN ", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", ""]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.unit_of_measure ADD COLUMN IF NOT EXISTS _sync_tick NUMERIC(28,10);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.unit_of_measure._sync_tick IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": true,", "  \"precision\": 28,", "  \"scale\": 0", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.unit_of_measure AS t0 SET _sync_tick=$1 WHERE ((t0._sync_tick IS NULL))", "args": [0], "actionDescription": "Auto data action for property UnitOfMeasure._syncTick"}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$NODE.UnitOfMeasure\\\"}\",\"containerId\":\"x3-devops00D38V-72987\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.item ADD COLUMN IF NOT EXISTS _sync_tick NUMERIC(28,10);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.item._sync_tick IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": true,", "  \"precision\": 28,", "  \"scale\": 0", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.item AS t0 SET _sync_tick=$1 WHERE ((t0._sync_tick IS NULL))", "args": [0], "actionDescription": "Auto data action for property Item._syncTick"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.company ADD COLUMN IF NOT EXISTS _sync_tick NUMERIC(28,10);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.company._sync_tick IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": true,", "  \"precision\": 28,", "  \"scale\": 0", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.company AS t0 SET _sync_tick=$1 WHERE ((t0._sync_tick IS NULL))", "args": [0], "actionDescription": "Auto data action for property Company._syncTick"}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$NODE.Company\\\"}\",\"containerId\":\"x3-devops00D38V-72987\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.site ADD COLUMN IF NOT EXISTS _sync_tick NUMERIC(28,10);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.site._sync_tick IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": true,", "  \"precision\": 28,", "  \"scale\": 0", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.site AS t0 SET _sync_tick=$1 WHERE ((t0._sync_tick IS NULL))", "args": [0], "actionDescription": "Auto data action for property Site._syncTick"}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$NODE.Site\\\"}\",\"containerId\":\"x3-devops00D38V-72987\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_resource ADD COLUMN IF NOT EXISTS _sync_tick NUMERIC(28,10);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_resource._sync_tick IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": true,", "  \"precision\": 28,", "  \"scale\": 0", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.base_resource AS t0 SET _sync_tick=$1 WHERE ((t0._sync_tick IS NULL))", "args": [0], "actionDescription": "Auto data action for property BaseResource._syncTick"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.employee ADD COLUMN IF NOT EXISTS name VARCHAR(80) COLLATE \"und-x-icu\" DEFAULT '', ADD COLUMN IF NOT EXISTS _sync_tick NUMERIC(28,10);", "COMMENT ON COLUMN %%SCHEMA_NAME%%.employee.name IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 80", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.employee._sync_tick IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": true,", "  \"precision\": 28,", "  \"scale\": 0", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.employee AS t0 SET _sync_tick=$1 WHERE ((t0._sync_tick IS NULL))", "args": [0], "actionDescription": "Auto data action for property Employee._syncTick"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='stock_issue_display_status_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.stock_issue_display_status_enum AS ENUM('detailsRequired','detailsEntered','stockPostingInProgress','stockPostingError','issued');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_issue ADD COLUMN IF NOT EXISTS display_status %%SCHEMA_NAME%%.stock_issue_display_status_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_issue.display_status IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"stock_issue_display_status_enum\"", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.stock_issue AS t0 SET display_status=$1 WHERE ((t0.display_status IS NULL))", "args": ["detailsRequired"], "actionDescription": "Auto data action for property StockIssue.displayStatus"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='stock_issue_line_display_status_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.stock_issue_line_display_status_enum AS ENUM('detailsRequired','detailsEntered','stockPostingInProgress','stockPostingError','issued');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_issue_line ADD COLUMN IF NOT EXISTS display_status %%SCHEMA_NAME%%.stock_issue_line_display_status_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_issue_line.display_status IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"stock_issue_line_display_status_enum\"", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.employee AS t0 SET \"name\"=CONCAT($1::TEXT,t0.first_name,$2::TEXT,t0.last_name,$1::TEXT) WHERE (TRUE)", "args": ["", " "], "actionDescription": "Set name from first<PERSON><PERSON> and last<PERSON>ame"}, {"isSysPool": false, "sql": ["", "            UPDATE %%SCHEMA_NAME%%.stock_issue_line as t0", "            SET display_status =", "            ( CASE", "            WHEN (t0.stock_transaction_status = 'completed') THEN 'issued'::%%SCHEMA_NAME%%.stock_issue_line_display_status_enum", "            WHEN (t0.stock_transaction_status = 'error') THEN 'stockPostingError'::%%SCHEMA_NAME%%.stock_issue_line_display_status_enum", "            WHEN (t0.stock_transaction_status = 'inProgress') THEN 'stockPostingInProgress'::%%SCHEMA_NAME%%.stock_issue_line_display_status_enum", "            WHEN (t0.stock_detail_status = 'required') THEN 'detailsRequired'::%%SCHEMA_NAME%%.stock_issue_line_display_status_enum", "            ELSE 'detailsEntered'::%%SCHEMA_NAME%%.stock_issue_line_display_status_enum", "            END );", "", "            Do $$ DECLARE", "            srd RECORD;", "            begin", "            FOR srd IN (", "                SELECT sr._id, sr._tenant_id", "                FROM", "                    %%SCHEMA_NAME%%.stock_issue sr)", "            loop", "                UPDATE %%SCHEMA_NAME%%.stock_issue t0", "                SET display_status=", "                ( CASE", "                    WHEN ((select count (display_status) from %%SCHEMA_NAME%%.stock_issue_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'stockPostingError') > 0)", "                        THEN 'stockPostingError'::%%SCHEMA_NAME%%.stock_issue_display_status_enum", "                    WHEN ((select count (display_status) from %%SCHEMA_NAME%%.stock_issue_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'stockPostingInProgress') > 0)", "                        THEN 'stockPostingInProgress'::%%SCHEMA_NAME%%.stock_issue_display_status_enum", "                    WHEN ((select count (display_status) from %%SCHEMA_NAME%%.stock_issue_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'detailsRequired') > 0)", "                        THEN 'detailsRequired'::%%SCHEMA_NAME%%.stock_issue_display_status_enum", "                    WHEN ((select count (display_status) from %%SCHEMA_NAME%%.stock_issue_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.display_status = 'detailsEntered') > 0)", "                        THEN 'detailsEntered'::%%SCHEMA_NAME%%.stock_issue_display_status_enum", "                    else 'issued'::%%SCHEMA_NAME%%.stock_issue_display_status_enum", "                END)", "                WHERE t0._id = srd._id and t0._tenant_id = srd._tenant_id;", "            end loop;", "        end $$"], "actionDescription": "Set the value of StockIssue.displayStatus"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_node_mapping ALTER COLUMN remote_property TYPE VARCHAR(1024) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_node_mapping.remote_property IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 1024", "}';;"], "actionDescription": "SysNodeMapping: retype remote_property from string to string' (string->string)"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sys_node_mapping ALTER COLUMN local_property TYPE VARCHAR(1024) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_node_mapping.local_property IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 1024", "}';;"], "actionDescription": "SysNodeMapping: retype local_property from string to string' (string->string)"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.stock_issue_line ALTER COLUMN display_status SET NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.stock_issue ALTER COLUMN display_status SET NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.employee ALTER COLUMN name SET NOT NULL, ALTER COLUMN _sync_tick SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX employee_ind0 ON %%SCHEMA_NAME%%.employee(_tenant_id ASC,name ASC);"}, {"isSysPool": true, "sql": "CREATE  INDEX employee_ind1 ON %%SCHEMA_NAME%%.employee(_tenant_id ASC,_sync_tick ASC);"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_resource ALTER COLUMN _sync_tick SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE  INDEX base_resource_ind1 ON %%SCHEMA_NAME%%.base_resource(_tenant_id ASC,_sync_tick ASC);"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.site ALTER COLUMN _sync_tick SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE  INDEX site_ind2 ON %%SCHEMA_NAME%%.site(_tenant_id ASC,_sync_tick ASC);"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.company ALTER COLUMN _sync_tick SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE  INDEX company_ind2 ON %%SCHEMA_NAME%%.company(_tenant_id ASC,_sync_tick ASC);"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.item ALTER COLUMN _sync_tick SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE  INDEX item_ind1 ON %%SCHEMA_NAME%%.item(_tenant_id ASC,_sync_tick ASC);"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.unit_of_measure ALTER COLUMN _sync_tick SET NOT NULL;"}, {"isSysPool": true, "sql": "CREATE  INDEX unit_of_measure_ind1 ON %%SCHEMA_NAME%%.unit_of_measure(_tenant_id ASC,_sync_tick ASC);"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", false, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00D38V-72987\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.employee IS '{", "  \"isSharedByAllTenants\": false,", "  \"naturalKey\": [", "    \"name\"", "  ]", "}';"]}], "data": {}}