{"fromVersion": "48.0.27", "toVersion": "48.0.28", "gitHead": "60a8578086081f1c74abb3b77ceeca0391eaf868", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        is_audit_enabled BOOLEAN;", "        p_root_table_name VARCHAR;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        p_root_table_name := TG_ARGV[0];", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = COALESCE(NEW._id, OLD._id)", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_audit_log_ind0;"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='tax_type_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.tax_type_enum AS ENUM('purchasing','sales','purchasingAndSales');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.tax ADD COLUMN IF NOT EXISTS tax_type %%SCHEMA_NAME%%.tax_type_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.tax.tax_type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"tax_type_enum\"", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.tax AS t0 SET tax_type=$1 WHERE ((t0.tax_type IS NULL))", "args": ["purchasingAndSales"], "actionDescription": "Auto data action for property Tax.taxType"}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$NODE.Tax\\\"}\",\"containerId\":\"x3-devops00EMSF-58227\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_order_line ADD COLUMN IF NOT EXISTS receiving_site INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transfer_order_line.receiving_site IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"site\",", "  \"isSelfReference\": false", "}';"]}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_order_line ADD CONSTRAINT \"stock_transfer_order_line_receiving_site_fk\" FOREIGN KEY(_tenant_id,receiving_site) REFERENCES %%SCHEMA_NAME%%.site(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT stock_transfer_order_line_receiving_site_fk ON %%SCHEMA_NAME%%.stock_transfer_order_line IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"receiving_site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.tax ALTER COLUMN tax_type SET NOT NULL;"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_audit_log_ind0 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,record_id ASC,COALESCE(new_update_tick, (- ((2)::bigint ^ (62)::bigint))::bigint) ASC);"}, {"action": "reload_setup_data", "args": {"factory": "PostingClassDefinition"}}, {"action": "reload_setup_data", "args": {"factory": "SysApp"}}, {"action": "reload_setup_data", "args": {"factory": "SysNodeTransformation"}}, {"action": "reload_setup_data", "args": {"factory": "Tax"}}, {"action": "reload_setup_data", "args": {"factory": "JournalEntryType"}}, {"action": "reload_setup_data", "args": {"factory": "SysEnumTransformation"}}, {"action": "reload_setup_data", "args": {"factory": "SysNodeMapping"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT stock_transfer_order_line_receiving_site_fk ON %%SCHEMA_NAME%%.stock_transfer_order_line IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"receiving_site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';"]}], "data": {"PostingClassDefinition": {"metadata": {"rootFactoryName": "PostingClassDefinition", "name": "PostingClassDefinition", "naturalKeyColumns": ["_tenant_id", "legislation", "posting_class_type", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "legislation", "type": "reference", "targetFactoryName": "Legislation"}, {"name": "id", "type": "string"}, {"name": "posting_class_type", "type": "enum", "enumMembers": ["item", "supplier", "customer", "tax", "company", "header", "line", "resource"]}, {"name": "account_type_name", "type": "string", "isLocalized": true}, {"name": "is_detailed", "type": "boolean", "isOwnedByCustomer": true}, {"name": "is_stock_item_allowed", "type": "boolean"}, {"name": "is_non_stock_item_allowed", "type": "boolean"}, {"name": "is_service_item_allowed", "type": "boolean"}, {"name": "is_landed_cost_item_allowed", "type": "boolean"}, {"name": "can_have_additional_criteria", "type": "boolean"}, {"name": "additional_criteria", "type": "enum", "isNullable": true, "isOwnedByCustomer": true, "enumMembers": ["item", "supplier", "customer", "tax", "company", "header", "line", "resource"]}]}, "rows": [["sage", "FR", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "FR", "ArGsni", "customer", "{\"en\":\"Accounts receivable - GSNI\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable - GSNI\",\"fr-FR\":\"Collectif client FAE\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "Y", null], ["sage", "FR", "PurchaseExpense", "item", "{\"en\":\"Expense\",\"de-DE\":\"Aufwand\",\"en-US\":\"Expense\",\"fr-FR\":\"Charges - Achats\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "FR", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "FR", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "StockVariation", "item", "{\"en\":\"Purchased item stock variation\",\"de-DE\":\"Bestandsabweichung Einkaufsartikel\",\"en-US\":\"Purchased item stock variation\",\"fr-FR\":\"Variation de stock article acheté\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "StockVariationRevenue", "item", "{\"en\":\"Sold item stock variation\",\"de-DE\":\"Bestandsabweichung verkaufte Artikel\",\"en-US\":\"Sold item stock variation\",\"fr-FR\":\"Variation de stock article vendu\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "NonAbsorbedStock", "item", "{\"en\":\"Non absorbed stock\",\"de-DE\":\"Nicht absorbierter Bestand\",\"en-US\":\"Non absorbed stock\",\"fr-FR\":\"Stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "FR", "NonAbsorbedStockVariance", "item", "{\"en\":\"Non absorbed stock variance\",\"de-DE\":\"Nicht absorbierte Bestandsabweichung\",\"en-US\":\"Non absorbed stock variance\",\"fr-FR\":\"Variation de stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "FR", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "FR", "ApGrni", "supplier", "{\"en\":\"Accounts payable - GRNI\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable - GRNI\",\"fr-FR\":\"Collectif fournisseur FAR\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "ReverseChargeVat", "tax", "{\"en\":\"Reverse charge VAT\",\"de-DE\":\"Reverse Charge (Steuer EU-Erwerb/§13b)\",\"en-US\":\"Reverse charge VAT\",\"fr-FR\":\"Autoliquidation TVA sur achats\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "Vat", "tax", "{\"en\":\"VAT\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"VAT\",\"fr-FR\":\"TVA\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "US", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance US\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance US\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance US\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance US\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "US", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "US", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit des ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "US", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "US", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "US", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress\",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "US", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "FR", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "US", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "US", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de la lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "GB", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "GB", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "GB", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "GB", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "GB", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "GB", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "GB", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "GB", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "GB", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "GB", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "GB", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "US", "SalesTaxPayable", "tax", "{\"en\":\"Tax payable\",\"de-DE\":\"Vorsteuer\",\"en-US\":\"Tax payable\",\"fr-FR\":\"Taxe sur ventes\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "TaxReceivable", "tax", "{\"en\":\"Tax receivable\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"Tax receivable\",\"fr-FR\":\"Taxe sur achats\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "ZA", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "ZA", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "ZA", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "ZA", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "ZA", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "ZA", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "ZA", "SalesTaxPayable", "tax", "{\"en\":\"Tax payable\",\"de-DE\":\"Vorsteuer\",\"en-US\":\"Tax payable\",\"fr-FR\":\"Taxe sur ventes\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "TaxReceivable", "tax", "{\"en\":\"Tax receivable\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"Tax receivable\",\"fr-FR\":\"Taxe sur achats\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de la ligne de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Client\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "Y", null], ["sage", "DE", "PurchaseExpense", "item", "{\"en\":\"Expense\",\"de-DE\":\"Aufwand\",\"en-US\":\"Expense\",\"fr-FR\":\"Charge - Achats\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "DE", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "DE", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "StockVariation", "item", "{\"en\":\"Purchased item stock variation\",\"de-DE\":\"Bestandsabweichung Einkaufsartikel\",\"en-US\":\"Purchased item stock variation\",\"fr-FR\":\"Variation de stock article acheté\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "StockVariationSales", "item", "{\"en\":\"Sold item stock variation\",\"de-DE\":\"Bestandsabweichung verkaufte Artikel\",\"en-US\":\"Sold item stock variation\",\"fr-FR\":\"Variation de stock article vendu\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Fertigungsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "NonAbsorbedStock", "item", "{\"en\":\"Non absorbed stock\",\"de-DE\":\"Nicht absorbierter Bestand\",\"en-US\":\"Non absorbed stock\",\"fr-FR\":\"Stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "NonAbsorbedStockVariance", "item", "{\"en\":\"Non absorbed stock variance\",\"de-DE\":\"Nicht absorbierte Bestandsabweichung\",\"en-US\":\"Non absorbed stock variance\",\"fr-FR\":\"Variation de stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Fournisseur\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "Vat", "tax", "{\"en\":\"VAT\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"VAT\",\"fr-FR\":\"TVA\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "ReverseChargeVat", "tax", "{\"en\":\"Reverse charge VAT\",\"de-DE\":\"Reverse Charge (Steuer EU-Erwerb/§13b)\",\"en-US\":\"Reverse charge VAT\",\"fr-FR\":\"Autoliquidation TVA sur achats\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "AU", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "AU", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "AU", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "AU", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "AU", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "AU", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "AU", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "AU", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "AU", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "AU", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "AU", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "AU", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "FR", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null]]}, "SysApp": {"metadata": {"rootFactoryName": "SysApp", "name": "SysApp", "naturalKeyColumns": ["_tenant_id", "name"], "columns": [{"name": "name", "type": "string"}, {"name": "version", "type": "string"}, {"name": "title", "type": "string", "isLocalized": true}, {"name": "is_connector", "type": "boolean"}, {"name": "interop_package", "type": "string"}, {"name": "is_active", "type": "boolean"}]}, "rows": []}, "SysNodeTransformation": {"metadata": {"rootFactoryName": "SysNodeTransformation", "name": "SysNodeTransformation", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "remote_app", "type": "reference", "targetFactoryName": "SysApp"}, {"name": "remote_app_version", "type": "string"}, {"name": "remote_node_name", "type": "string"}, {"name": "local_node", "type": "reference", "targetFactoryName": "MetaNodeFactory"}, {"name": "local_node_full_name", "type": "string"}, {"name": "filter", "type": "json", "isNullable": true}, {"name": "version", "type": "string"}, {"name": "last_error", "type": "date", "isNullable": true}, {"name": "last_sync", "type": "date", "isNullable": true}]}, "rows": []}, "Tax": {"metadata": {"rootFactoryName": "Tax", "name": "Tax", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "primary_external_reference", "type": "string"}, {"name": "secondary_external_reference", "type": "string"}, {"name": "tax_category", "type": "reference", "targetFactoryName": "TaxCategory"}, {"name": "country", "type": "reference", "targetFactoryName": "Country"}, {"name": "is_reverse_charge", "type": "boolean"}, {"name": "jurisdiction_name", "type": "string"}, {"name": "legal_mention", "type": "string", "isLocalized": true}, {"name": "tax_type", "type": "enum", "enumMembers": ["purchasing", "sales", "purchasingAndSales"]}, {"name": "posting_class", "type": "reference", "isNullable": true, "targetFactoryName": "PostingClass"}, {"name": "posting_key", "type": "integer", "isNullable": true}, {"name": "is_intacct", "type": "boolean"}, {"name": "u_intacct_id", "type": "string"}, {"name": "record_no", "type": "integer", "isNullable": true}, {"name": "intacct_tax_type", "type": "string"}, {"name": "intacct_account", "type": "string"}, {"name": "is_intacct_reverse_charge", "type": "boolean"}, {"name": "rate", "type": "decimal", "isNullable": true}, {"name": "intacct_secondary_external_reference", "type": "reference", "isNullable": true, "targetFactoryName": "Tax"}, {"name": "intacct_solution_id", "type": "string"}, {"name": "service_fabric_id", "type": "string"}, {"name": "legislation", "type": "reference", "isNullable": true, "targetFactoryName": "Legislation"}]}, "rows": [["", "FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT", "Y", "{\"en\":\"EU tax deductible on fixed assets, standard rate\",\"en-US\":\"EU tax deductible on fixed assets, standard rate\",\"fr-FR\":\"TVA à déduire sur immobilisations, taux normal\"}", "TVA déductible intracomm. taux normal - Acquisition d'immobilisations", "TVA due intracomm. taux normal - Acquisition d'immobilisations", "VAT", "FR", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_INTRASTAT", "Y", "{\"en\":\"EU tax deductible on purchases, standard rate\",\"en-US\":\"EU tax deductible on purchases, standard rate\",\"fr-FR\":\"TVA à déduire sur achats, taux normal\"}", "TVA déductible intracomm. taux normal", "TVA due intracomm. taux normal", "VAT", "FR", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, intermediate rate\",\"de-DE\":\"Vors<PERSON>uer, teilreduzier<PERSON> (Ist)\",\"en-US\":\"Tax deductible on payments, intermediate rate\",\"fr-FR\":\"Taux intermédiaire déductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, reduced rate\",\"de-DE\":\"<PERSON><PERSON><PERSON><PERSON>, erm<PERSON><PERSON><PERSON><PERSON> (Ist)\",\"en-US\":\"Tax deductible on payments, reduced rate\",\"fr-FR\":\"Taux réduit déductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, standard rate\",\"de-DE\":\"Vorsteuer, Regelsatz (Ist)\",\"en-US\":\"Tax deductible on payments, standard rate\",\"fr-FR\":\"Taux normal déductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, intermediate rate\",\"de-DE\":\"Vorsteuer, teilreduzierter Satz\",\"en-US\":\"Tax deductible on debits, intermediate rate\",\"fr-FR\":\"Taux intermédiaire déductible sur les débits\"}", "FR -BS TVA Déductible 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, reduced rate\",\"de-DE\":\" <PERSON><PERSON><PERSON>uer, ermäß<PERSON><PERSON>tz\",\"en-US\":\"Tax deductible on debits, reduced rate\",\"fr-FR\":\"Taux réduit déductible sur les débits\"}", "FR -BS TVA Déductible 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, standard rate\",\"de-DE\":\" Vorsteuer, Regelsatz\",\"en-US\":\"Tax deductible on debits, standard rate\",\"fr-FR\":\"Taux normal déductible sur les débits\"}", "FR -BS TVA Déductible 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, exempt rate\",\"de-DE\":\"Vorsteuer, mehrwertsteuerfrei\",\"en-US\":\"Tax deductible on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération déductible sur les débits\"}", "FR -BS Exempté de TVA Déductible 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_DEBITS_INTRASTAT", "Y", "{\"en\":\"EU tax deductible on debits, exempt rate\",\"en-US\":\"EU tax deductible on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération déductible sur les débits DEB\"}", "Déductible débits exonéré de TVA intracommunautaire", "", "VAT", "FR", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, standard rate\",\"de-DE\":\"Vorsteuer Anlagen, Regelsatz\",\"en-US\":\"Tax deductible on fixed assets, standard rate\",\"fr-FR\":\"Taux normal déductible sur les immobilisations\"}", "FR -Immos TVA Déductible 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, intermediate rate\",\"de-DE\":\"Umsatzsteuer, teilreduzierter <PERSON> (Ist)\",\"en-US\":\"Tax collected on payments, intermediate rate\",\"fr-FR\":\"Taux intermédiaire collecté sur les encaissements\"}", "FR -TVA Enc -BS Collectée 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, reduced rate\",\"de-DE\":\"Umsatzsteuer, ermäßig<PERSON> (Ist)\",\"en-US\":\"Tax collected on payments, reduced rate\",\"fr-FR\":\"Taux réduit collecté sur les encaissements\"}", "FR -TVA Enc -BS Collectée 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, standard rate\",\"de-DE\":\"Umsatzsteuer, Regelsatz (Ist)\",\"en-US\":\"Tax collected on payments, standard rate\",\"fr-FR\":\"Taux normal collecté sur les encaissements\"}", "FR -TVA Enc -BS Collectée 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, intermediate rate\",\"de-DE\":\"UUmsatzsteuer, teilreduzierter Satz\",\"en-US\":\"Tax collected on debits, intermediate rate\",\"fr-FR\":\"Taux intermédiaire collecté sur les débits\"}", "FR -BS TVA Collectée 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, reduced rate\",\"de-DE\":\"Umsatzsteuer, ermäßigter Satz\",\"en-US\":\"Tax collected on debits, reduced rate\",\"fr-FR\":\"Taux réduit collecté sur les débits\"}", "FR -BS TVA Collectée 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, standard rate\",\"en-US\":\"Tax collected on debits, standard rate\",\"fr-FR\":\"Taux normal collecté sur les débits\"}", "FR -BS TVA Collectée 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, exempt rate\",\"en-US\":\"Tax collected on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération collecté sur les débits\"}", "FR -BS Exempté de TVA Collectée 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_COLLECTED_ON_DEBITS_INTRASTAT", "Y", "{\"en\":\"EU tax collected on debits, exempt rate\",\"en-US\":\"EU tax collected on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération collecté sur les débits DEB\"}", "Collectée débits exonéré de TVA intracommunautaire", "", "VAT", "FR", "Y", null, "{\"en\":\"Tax exemption, article 262 ter-1 of the general tax code\",\"en-US\":\"Tax exemption, article 262 ter-1 of the general tax code\",\"fr-FR\":\"exonération de TVA, article 262 ter-1 du code général des impôts\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, intermediate rate\",\"base\":\"Tax deductible on fixed assets, intermediate rate\",\"en-US\":\"Tax deductible on fixed assets, intermediate rate\",\"fr-FR\":\"Taux intermédiaire déductible sur les immobilisations\"}", "FR -Immos TVA Déductible 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, reduced rate\",\"base\":\"Tax deductible on fixed assets, reduced rate\",\"en-US\":\"Tax deductible on fixed assets, reduced rate\",\"fr-FR\":\"Taux réduit déductible sur les immobilisations\"}", "FR -Immos TVA Déductible TVA 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, exempt rate\",\"base\":\"Tax deductible on fixed assets, exempt rate\",\"en-US\":\"Tax deductible on fixed assets, exempt rate\",\"fr-FR\":\"Taux d'exonération déductible sur les immobilisations\"}", "FR -Immos TVA Déductible Exonéré 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, exempt rate\",\"base\":\"Tax deductible on payments, exempt rate\",\"en-US\":\"Tax deductible on payments, exempt rate\",\"fr-FR\":\"Taux d'exonérationdéductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible Exonéré 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, exempt rate\",\"base\":\"Tax collected on payments, exempt rate\",\"en-US\":\"Tax collected on payments, exempt rate\",\"fr-FR\":\"Taux normal collectée sur les encaissements\"}", "FR -TVA Enc -BS Collectée Exonéré 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK Sale Goods Standard Rate\",\"en-US\":\"UK Sale Goods Standard Rate\",\"fr-FR\":\"UK ventes de biens, taux standard\"}", "UK Sale Goods Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK Sale Goods Reduced Rate\",\"en-US\":\"UK Sale Goods Reduced Rate\",\"fr-FR\":\"UK Ventes de biens, taux réduit\"}", "UK Sale Goods Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK Sale Goods Zero Rate\",\"en-US\":\"UK Sale Goods Zero Rate\",\"fr-FR\":\"UK Ventes de biens, taux zéro\"}", "UK Sale Goods Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK Sale Goods Exempt Rate\",\"en-US\":\"UK Sale Goods Exempt Rate\",\"fr-FR\":\"UK ventes de biens, taux d’exonération\"}", "UK Sale Goods Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK Sale Services Standard Rate\",\"en-US\":\"UK Sale Services Standard Rate\",\"fr-FR\":\"UK ventes de service, taux standard\"}", "UK Sale Services Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK Sale Services Reduced Rate\",\"en-US\":\"UK Sale Services Reduced Rate\",\"fr-FR\":\"UK Ventes de services, taux réduit\"}", "UK Sale Services Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK Sale Services Zero Rate\",\"en-US\":\"UK Sale Services Zero Rate\",\"fr-FR\":\"UK Ventes de services, taux zéro\"}", "UK Sale Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK Sale Services Exempt Rate\",\"en-US\":\"UK Sale Services Exempt Rate\",\"fr-FR\":\"UK ventes de services, taux d’exonération\"}", "UK Sale Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK General Export Standard Rate\",\"en-US\":\"UK General Export Standard Rate\",\"fr-FR\":\"UK Export de biens, taux normal\"}", "UK General Export Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK General Export Reduced Rate\",\"en-US\":\"UK General Export Reduced Rate\",\"fr-FR\":\"UK Export de biens, taux réduit\"}", "UK General Export Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK General Export Zero Rate\",\"en-US\":\"UK General Export Zero Rate\",\"fr-FR\":\"UK Export de biens, taux zéro\"}", "UK General Export Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK General Export Exempt Rate\",\"en-US\":\"UK General Export Exempt Rate\",\"fr-FR\":\"UK export, taux d’exonération\"}", "UK General Export Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK General Export Services Standard Rate\",\"en-US\":\"UK General Export Services Standard Rate\",\"fr-FR\":\"UK export de services, taux standard\"}", "UK General Export Services Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK General Export Services Reduced Rate\",\"en-US\":\"UK General Export Services Reduced Rate\",\"fr-FR\":\"UK Export de services, taux réduit\"}", "UK General Export Services Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK General Export Services Zero Rate\",\"en-US\":\"UK General Export Services Zero Rate\",\"fr-FR\":\"UK Export de services, taux zéro\"}", "UK General Export Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK General Export Services, Exempt Rate\",\"en-US\":\"UK General Export Services, Exempt Rate\",\"fr-FR\":\"UK export de services, taux d’exonération\"}", "UK General Export Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_NO_VAT_INPUT", "Y", "{\"en\":\"No VAT Output\",\"en-US\":\"No VAT Output\",\"fr-FR\":\"Ventes sans taux\"}", "No VAT Output", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK Purchase Goods Standard Rate\",\"en-US\":\"UK Purchase Goods Standard Rate\",\"fr-FR\":\"UK Achats de biens, taux standard\"}", "UK Purchase Goods Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK Purchase Goods Reduced Rate\",\"en-US\":\"UK Purchase Goods Reduced Rate\",\"fr-FR\":\"UK Achats de biens, taux réduit\"}", "UK Purchase Goods Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK Purchase Goods Zero Rate\",\"en-US\":\"UK Purchase Goods Zero Rate\",\"fr-FR\":\"UK Achats de biens, taux zéro\"}", "UK Purchase Goods Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK Purchase Goods Exempt Rate\",\"en-US\":\"UK Purchase Goods Exempt Rate\",\"fr-FR\":\"UK achats de biens, taux d’exonération\"}", "UK Purchase Goods Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK Purchase Services Standard Rate\",\"en-US\":\"UK Purchase Services Standard Rate\",\"fr-FR\":\"UK Achats de services, taux standard\"}", "UK Purchase Services Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK Purchase Services Reduced Rate\",\"en-US\":\"UK Purchase Services Reduced Rate\",\"fr-FR\":\"UK Achats de services, taux réduit\"}", "UK Purchase Services Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK Purchase Services Zero Rate\",\"en-US\":\"UK Purchase Services Zero Rate\",\"fr-FR\":\"UK Achats de services, taux zéro\"}", "UK Purchase Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK Purchase Services Exempt Rate\",\"en-US\":\"UK Purchase Services Exempt Rate\",\"fr-FR\":\"UK achats de services, taux d’exonération\"}", "UK Purchase Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK Import Goods Standard Rate\",\"en-US\":\"UK Import Goods Standard Rate\",\"fr-FR\":\"UK Import de biens, taux standard\"}", "UK Import Goods Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK Import Goods Reduced Rate\",\"en-US\":\"UK Import Goods Reduced Rate\",\"fr-FR\":\"UK Import de biens, taux réduit\"}", "UK Import Goods Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK Import Goods Zero Rate\",\"en-US\":\"UK Import Goods Zero Rate\",\"fr-FR\":\"UK Import de biens, taux zéro\"}", "UK Import Goods Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK Import Goods Exempt Rate\",\"en-US\":\"UK Import Goods Exempt Rate\",\"fr-FR\":\"UK import de biens, taux d’exonération\"}", "UK Import Goods Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK Import Services Standard Rate\",\"en-US\":\"UK Import Services Standard Rate\",\"fr-FR\":\"UK Import de services, taux standard\"}", "UK Import Services Standard Rate", "UK Import Services Standard Rate Reverse Charge", "VAT", "GB", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK Import Services Reduced Rate\",\"en-US\":\"UK Import Services Reduced Rate\",\"fr-FR\":\"UK Import de services, taux réduit\"}", "UK Import Services Reduced Rate", "UK Import Services Reduced Rate Reverse Charge", "VAT", "GB", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK Import Services Zero Rate\",\"en-US\":\"UK Import Services Zero Rate\",\"fr-FR\":\"UK Import de services, taux zéro\"}", "UK Import Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK Import Services Exempt Rate\",\"en-US\":\"UK Import Services Exempt Rate\",\"fr-FR\":\"UK import de services, taux d’exonération\"}", "UK Import Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_NO_VAT_INPUT", "Y", "{\"en\":\"No VAT Input\",\"en-US\":\"No VAT Input\",\"fr-FR\":\"Achats sans taux\"}", "No VAT Input", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_PVA_STANDARD_RATE", "Y", "{\"en\":\"UK PVA Import Goods Standard Rate Input\",\"en-US\":\"UK PVA Import Goods Standard Rate Input\",\"fr-FR\":\"UK Import de biens, taux standard reporté\"}", "UK PVA Import Goods Standard Rate Input", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_PVA_REDUCED_RATE", "Y", "{\"en\":\"UK PVA Import Goods Reduced Rate Input\",\"en-US\":\"UK PVA Import Goods Reduced Rate Input\",\"fr-FR\":\"UK Import de biens, taux réduit reporté\"}", "UK PVA Import Goods Reduced Rate Input", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_HOME_GOODS", "Y", "{\"en\":\"Standard Rate Output\",\"en-US\":\"Standard Rate Output\",\"fr-FR\":\"Ventes de biens\"}", "Standard Rate Output", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_HOME_CAPITAL_GOODS", "Y", "{\"en\":\"Standard Rate (Capital Goods) Output\",\"en-US\":\"Standard Rate (Capital Goods) Output\",\"fr-FR\":\"Ventes de biens d'équipement\"}", "Standard Rate (Capital Goods) Output", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_HOME_ZERO_RATE", "Y", "{\"en\":\"Zero Rate (Excluding Goods Exported)\",\"en-US\":\"Zero Rate (Excluding Goods Exported)\",\"fr-FR\":\"Ventes taux zéro\"}", "Zero Rate (Excluding Goods Exported)", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_EXEMPT_RATE", "Y", "{\"en\":\"Exempt and Non-Supplies\",\"en-US\":\"Exempt and Non-Supplies\",\"fr-FR\":\"Ventes taux exonéré\"}", "Exempt and Non-Supplies", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_EXPORT_ZERO_RATE", "Y", "{\"en\":\"Zero Rate (Only Goods Exported)\",\"en-US\":\"Zero Rate (Only Goods Exported)\",\"fr-FR\":\"Export de biens\"}", "Zero Rate (Only Goods Exported)", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_HOME_GOODS", "Y", "{\"en\":\"Standard Rate Input\",\"en-US\":\"Standard Rate Input\",\"fr-FR\":\"Achats de biens\"}", "Standard Rate Input", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_HOME_CAPITAL_GOODS", "Y", "{\"en\":\"Standard Rate (Capital Goods) Input\",\"en-US\":\"Standard Rate (Capital Goods) Input\",\"fr-FR\":\"Achats de biens d'équipement\"}", "Standard Rate (Capital Goods) Input", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_HOME_NO_INPUT", "Y", "{\"en\":\"No Input VAT\",\"en-US\":\"No Input VAT\",\"fr-FR\":\"Achats taux zéro\"}", "No Input VAT", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_IMPORT_GOODS", "Y", "{\"en\":\"Other Goods Imported (Not Capital Goods)\",\"en-US\":\"Other Goods Imported (Not Capital Goods)\",\"fr-FR\":\"Import de biens\"}", "Other Goods Imported (Not Capital Goods)", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_IMPORT_SERVICES", "Y", "{\"en\":\"Other Output Tax Adjustments\",\"en-US\":\"Other Output Tax Adjustments\",\"fr-FR\":\"Import de services\"}", "Other Output Tax Adjustments", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_IMPORT_CAPITAL_GOODS", "Y", "{\"en\":\"Capital Goods Imported\",\"en-US\":\"Capital Goods Imported\",\"fr-FR\":\"Import de biens d'équipement\"}", "Capital Goods Imported", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_STANDARD_RATE", "Y", "{\"en\":\"Sale Standard Rate\",\"de-DE\":\"Umsatzsteuer Regelsatz\",\"en-US\":\"Sale Standard Rate\",\"fr-FR\":\"Ventes taux normal\"}", "Sale Standard Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_REDUCED_RATE", "Y", "{\"en\":\"Sale Reduced Rate\",\"de-DE\":\"Umsatzsteuer ermäßigter Satz\",\"en-US\":\"Sale Reduced Rate\",\"fr-FR\":\"Ventes taux réduit\"}", "Sale Reduced Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_ZERO_RATE", "Y", "{\"en\":\"Sale Zero Rate\",\"de-DE\":\"Umsatzsteuerfrei\",\"en-US\":\"Sale Zero Rate\",\"fr-FR\":\"Ventes taux zéro\"}", "Sale Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_MISC_RATE", "Y", "{\"en\":\"Sale Miscellaneous Rate\",\"de-DE\":\"Umsatzsteuer sonstiger Satz\",\"en-US\":\"Sale Miscellaneous Rate\",\"fr-FR\":\"Ventes taux divers\"}", "Sale Miscellaneous Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_STANDARD_RATE", "Y", "{\"en\":\"Purchase Standard Rate\",\"de-DE\":\"Vorsteuer Regelsatz\",\"en-US\":\"Purchase Standard Rate\",\"fr-FR\":\"Achats taux normal\"}", "Purchase Standard Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_REDUCED_RATE", "Y", "{\"en\":\"Purchase Reduced Rate\",\"de-DE\":\"Vorsteuer ermäßigter Satz\",\"en-US\":\"Purchase Reduced Rate\",\"fr-FR\":\"Achats taux réduit\"}", "Purchase Reduced Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_ZERO_RATE", "Y", "{\"en\":\"Purchase Zero Rate\",\"de-DE\":\"Vorsteuerfrei\",\"en-US\":\"Purchase Zero Rate\",\"fr-FR\":\"Achats taux zéro\"}", "Purchase Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_MISC_RATE", "Y", "{\"en\":\"Purchase Miscellaneous Rate\",\"de-DE\":\"Vorsteuer sonstiger Satz\",\"en-US\":\"Purchase Miscellaneous Rate\",\"fr-FR\":\"Achats taux divers\"}", "Purchase Miscellaneous Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_INTRASTAT_ZERO_RATE", "Y", "{\"en\":\"EU Sale Zero Rate\",\"de-DE\":\"Umsatzsteuer innergem. Lieferungen\",\"en-US\":\"EU Sale Zero Rate\",\"fr-FR\":\"Ventes CEE taux zéro\"}", "EU Sale Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_STANDARD_RATE", "Y", "{\"en\":\"EU Purchase Standard Rate\",\"de-DE\":\"Vorsteuer innergem. Erwerb Regelsatz\",\"en-US\":\"EU Purchase Standard Rate\",\"fr-FR\":\"Achats CEE taux normal\"}", "EU Purchase Standard Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_REDUCED_RATE", "Y", "{\"en\":\"EU Purchase Reduced Rate\",\"de-DE\":\"Vorsteuer innergem. Erwerb ermäßigter Satz\",\"en-US\":\"EU Purchase Reduced Rate\",\"fr-FR\":\"Achats CEE taux réduit\"}", "EU Purchase Reduced Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_ZERO_RATE", "Y", "{\"en\":\"EU Purchase Zero Rate\",\"de-DE\":\"Vorsteuer frei EU\",\"en-US\":\"EU Purchase Zero Rate\",\"fr-FR\":\"Achats CEE taux zéro\"}", "EU Purchase Zero Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_MISC_RATE", "Y", "{\"en\":\"EU Purchase Miscellaneous Rate\",\"de-DE\":\"Vorsteuer inergem. Erwerb sonstige Sätze\",\"en-US\":\"EU Purchase Miscellaneous Rate\",\"fr-FR\":\"Achats CEE taux divers\"}", "EU Purchase Miscellaneous Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_ROW_ZERO_RATE", "Y", "{\"en\":\"Export Zero Rate\",\"de-DE\":\"Umsatzsteuer frei Drittland\",\"en-US\":\"Export Zero Rate\",\"fr-FR\":\"Export taux zéro\"}", "Export Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_ROW_ZERO_RATE", "Y", "{\"en\":\"Import Zero Rate\",\"de-DE\":\"Vorsteuer frei Drittland\",\"en-US\":\"Import Zero Rate\",\"fr-FR\":\"Import taux zéro\"}", "Import Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_13B_RATE", "Y", "{\"en\":\"Purchase Standard Rate section 13b\",\"de-DE\":\"Abz. Vorsteuer §13b UStG Regelsatz\",\"en-US\":\"Purchase reverse charge Standard Rate section 13b\",\"fr-FR\":\"Achats taux normal section 13b\"}", "Purchase reverse charge Standard Rate section 13b", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_CAPITAL_STANDARD_RATE", "Y", "{\"en\":\"G15 Capital Purchases for Private Use\",\"base\":\"G15 Capital Purchases for Private Use\",\"en-US\":\"G15 Capital Purchases for Private Use\"}", "G15 Capital Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_CAPITAL_ZERO_RATE", "Y", "{\"en\":\"G15 GST Free Capital Purchases for Private Use\",\"base\":\"G15 GST Free Capital Purchases for Private Use\",\"en-US\":\"G15 GST Free Capital Purchases for Private Use\"}", "G15 GST Free Capital Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_GOODS_ZERO_RATE", "Y", "{\"en\":\"G15 GST Free Purchases for Private Use\",\"base\":\"G15 GST Free Purchases for Private Use\",\"en-US\":\"G15 GST Free Purchases for Private Use\"}", "G15 GST Free Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_STANDARD_RATE", "Y", "{\"en\":\"G15 Purchases for Private Use\",\"base\":\"G15 Purchases for Private Use\",\"en-US\":\"G15 Purchases for Private Use\"}", "G15 Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_1F_LUXURY_CAR_ZERO_RATE", "Y", "{\"en\":\"1F Luxury Car Tax Refundable\",\"base\":\"1F Luxury Car Tax Refundable\",\"en-US\":\"1F Luxury Car Tax Refundable\"}", "1F Luxury Car Tax Refundable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G11_OTHER_STANDARD_RATE", "Y", "{\"en\":\"G11 Other Acquisition\",\"base\":\"G11 Other Acquisition\",\"en-US\":\"G11 Other Acquisition\"}", "G11 Other Acquisition", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G10_CAPITAL_STANDARD_RATE", "Y", "{\"en\":\"G10 Capital Acquisition\",\"base\":\"G10 Capital Acquisition\",\"en-US\":\"G10 Capital Acquisition\"}", "G10 Capital Acquisition", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_1F_WINE_ZERO_RATE", "Y", "{\"en\":\"1D Wine Equalisation Tax Refundable\",\"base\":\"1D Wine Equalisation Tax Refundable\",\"en-US\":\"1D Wine Equalisation Tax Refundable\"}", "1D Wine Equalisation Tax Refundable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G10_MOTOR_VEHICLE_STANDARD_RATE", "Y", "{\"en\":\"G10 Motor Vehicle Acquisition\",\"base\":\"G10 Motor Vehicle Acquisition\",\"en-US\":\"G10 Motor Vehicle Acquisition\"}", "G10 Motor Vehicle Acquisition", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_CAPITAL_STANDARD_RATE", "Y", "{\"en\":\"G13 Capital Purchases for Input Tax Sales\",\"base\":\"G13 Capital Purchases for Input Tax Sales\",\"en-US\":\"G13 Capital Purchases for Input Tax Sales\"}", "G13 Capital Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_CAPITAL_ZERO_RATE", "Y", "{\"en\":\"G13 GST Free Capital Purchases for Input Tax Sales\",\"base\":\"G13 GST Free Capital Purchases for Input Tax Sales\",\"en-US\":\"G13 GST Free Capital Purchases for Input Tax Sales\"}", "G13 GST Free Capital Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_ZERO_RATE", "Y", "{\"en\":\"G13 GST Free Purchases for Input Tax Sales\",\"base\":\"G13 GST Free Purchases for Input Tax Sales\",\"en-US\":\"G13 GST Free Purchases for Input Tax Sales\"}", "G13 GST Free Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_STANDARD_RATE", "Y", "{\"en\":\"G13 Purchases for Input Tax Sales\",\"base\":\"G13 Purchases for Input Tax Sales\",\"en-US\":\"G13 Purchases for Input Tax Sales\"}", "G13 Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G14_CAPITAL_ZERO_RATE", "Y", "{\"en\":\"G14 GST Free Capital Purchases\",\"base\":\"G14 GST Free Capital Purchases\",\"en-US\":\"G14 GST Free Capital Purchases\"}", "G14 GST Free Capital Purchases", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G14_ZERO_RATE", "Y", "{\"en\":\"G14 GST Free Non-Capital Purchases\",\"base\":\"G14 GST Free Non-Capital Purchases\",\"en-US\":\"G14 GST Free Non-Capital Purchases\"}", "G14 GST Free Non-Capital Purchases", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G18_INPUT_TAX_ZERO_RATE", "Y", "{\"en\":\"G18 Input Tax Credit Adjustment\",\"base\":\"G18 Input Tax Credit Adjustment\",\"en-US\":\"G18 Input Tax Credit Adjustment\"}", "G18 Input Tax Credit Adjustment", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_W4_WITHOLDING_TAX_ZERO_RATE", "Y", "{\"en\":\"W4 Withholding Tax\",\"base\":\"W4 Withholding Tax\",\"en-US\":\"W4 Withholding Tax\"}", "W4 Withholding Tax", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_1C_WINE_ZERO_RATE", "Y", "{\"en\":\"1C Wine Equalisation Tax Payable\",\"base\":\"1C Wine Equalisation Tax Payable\",\"en-US\":\"1C Wine Equalisation Tax Payable\"}", "1C Wine Equalisation Tax Payable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_1E_LUXURY_CAR_ZERO_RATE", "Y", "{\"en\":\"1E Luxury Car Tax Payable\",\"base\":\"1E Luxury Car Tax Payable\",\"en-US\":\"1E Luxury Car Tax Payable\"}", "1E Luxury Car Tax Payable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G1_GOODS_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"G1 Goods and Services Tax\",\"base\":\"G1 Goods and Services Tax\",\"en-US\":\"G1 Goods and Services Tax\"}", "G1 Goods and Services Tax", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G1_GOODS_SERVICES_ZERO_RATE", "Y", "{\"en\":\"G1 Goods and Services Tax (GST Free)\",\"base\":\"G1 Goods and Services Tax (GST Free)\",\"en-US\":\"G1 Goods and Services Tax (GST Free)\"}", "G1 Goods and Services Tax (GST Free)", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G2_EXPORT_ZERO_RATE", "Y", "{\"en\":\"G2 Exports (GST Free)\",\"base\":\"G2 Exports (GST Free)\",\"en-US\":\"G2 Exports (GST Free)\"}", "G2 Exports (GST Free)", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G3_OTHER_ZERO_RATE", "Y", "{\"en\":\"G3 Other GST Free Sales\",\"base\":\"G3 Other GST Free Sales\",\"en-US\":\"G3 Other GST Free Sales\"}", "G3 Other GST Free Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G4_INPUT_TAX_ZERO_RATE", "Y", "{\"en\":\"G4 Input Taxed Sales\",\"base\":\"G4 Input Taxed Sales\",\"en-US\":\"G4 Input Taxed Sales\"}", "G4 Input Taxed Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G7_GST_PAYABLE_TAX_ZERO_RATE", "Y", "{\"en\":\"G7 GST Payable Adjustment\",\"base\":\"G7 GST Payable Adjustment\",\"en-US\":\"G7 GST Payable Adjustment\"}", "G7 GST Payable Adjustment", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasingAndSales", null, null, null, null, null, null, null, null, null, null, null, null, null]]}, "JournalEntryType": {"metadata": {"rootFactoryName": "JournalEntryType", "name": "JournalEntryType", "naturalKeyColumns": ["_tenant_id", "legislation", "document_type", "target_document_type"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "legislation", "type": "reference", "targetFactoryName": "Legislation"}, {"name": "document_type", "type": "enum", "enumMembers": ["miscellaneousStockReceipt", "miscellaneousStockIssue", "purchaseReceipt", "purchaseInvoice", "purchaseCreditMemo", "salesInvoice", "salesCreditMemo", "stockAdjustment", "salesShipment", "workInProgress", "apInvoice", "arInvoice", "purchaseReturn", "salesReturnReceipt", "bankReconciliationWithdrawal", "bankReconciliationDeposit", "stockCount", "stockValueChange", "stockTransferShipment"]}, {"name": "target_document_type", "type": "enum", "enumMembers": ["journalEntry", "accountsReceivableInvoice", "accountsPayableInvoice", "accountsReceivableAdvance", "accountsReceivablePayment"]}, {"name": "immediate_posting", "type": "boolean", "isOwnedByCustomer": true}, {"name": "header_journal", "type": "reference", "isNullable": true, "isOwnedByCustomer": true, "targetFactoryName": "Journal"}, {"name": "header_posting_date", "type": "enum", "isNullable": true, "isOwnedByCustomer": true, "enumMembers": ["documentDate", "endOfMonth"]}, {"name": "header_description", "type": "enum", "isOwnedByCustomer": true, "enumMembers": ["documentType", "documentNumber", "transactionDescription"]}, {"name": "header_account_type", "type": "reference", "isNullable": true, "targetFactoryName": "PostingClassDefinition"}, {"name": "header_amount_type", "type": "enum", "isNullable": true, "enumMembers": ["amount", "varianceAmount", "amountIncludingTax", "deductibleTaxAmount", "nonDeductibleTaxAmount", "reverseChargeDeductibleTaxAmount", "reverseChargeNonDeductibleTaxAmount", "amountExcludingTax", "taxAmount", "adjustmentAmount", "adjustmentNonabsorbedAmount", "landedCostAdjustmentAmount", "landedCostAdjustmentNonabsorbedAmount", "landedCostStockInTransitAmount", "landedCostStockInTransitAdjustmentAmount", "landedCostStockInTransitAdjustmentNonabsorbedAmount", "inTransitAmount", "inTransitVarianceAmount"]}]}, "rows": [["sage", "Y", "{\"en\":\"AP invoice\",\"de-DE\":\"Eingangsrechnung\",\"fr-FR\":\"Facture comptable fournisseur\"}", "FR", "apInvoice", "journalEntry", "Y", "FR|ACH", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"AR invoice\",\"de-DE\":\"Ausgangsrechnung\",\"fr-FR\":\"Facture comptable client\"}", "FR", "arInvoice", "journalEntry", "Y", "FR|VEN", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "FR", "miscellaneousStockIssue", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "FR", "miscellaneousStockReceipt", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "FR", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "FR|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "FR", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "FR|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "FR", "purchaseInvoice", "journalEntry", "Y", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "FR", "purchaseReceipt", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "FR", "purchaseCreditMemo", "journalEntry", "Y", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "FR", "purchaseReturn", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir vente\"}", "FR", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "FR|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "FR", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "FR|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "FR", "salesReturnReceipt", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "FR", "salesShipment", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "FR", "stockAdjustment", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "FR", "stockCount", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "FR", "stockValueChange", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "GB", "salesCreditMemo", "journalEntry", "Y", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "GB", "miscellaneousStockIssue", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "GB", "miscellaneousStockReceipt", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "GB", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "GB|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "GB", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "GB|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "GB", "purchaseInvoice", "journalEntry", "Y", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "GB", "purchaseReceipt", "journalEntry", "N", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "GB", "purchaseCreditMemo", "journalEntry", "Y", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "GB", "purchaseReturn", "journalEntry", "N", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "GB", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "GB|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "GB", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "GB|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "GB", "salesInvoice", "journalEntry", "Y", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "GB", "salesReturnReceipt", "journalEntry", "N", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "GB", "salesShipment", "journalEntry", "N", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "GB", "stockAdjustment", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "GB", "workInProgress", "journalEntry", "N", "GB|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "GB", "stockCount", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "GB", "stockValueChange", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "US", "salesCreditMemo", "journalEntry", "Y", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "US", "miscellaneousStockIssue", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "US", "miscellaneousStockReceipt", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "US", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "US|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "US", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "US|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "US", "purchaseInvoice", "journalEntry", "Y", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "US", "purchaseReceipt", "journalEntry", "N", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "US", "purchaseCreditMemo", "journalEntry", "Y", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "US", "purchaseReturn", "journalEntry", "N", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "US", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "US|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "US", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "US|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "US", "salesInvoice", "journalEntry", "Y", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "US", "salesReturnReceipt", "journalEntry", "N", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "US", "salesShipment", "journalEntry", "N", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "US", "stockAdjustment", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "US", "workInProgress", "journalEntry", "N", "US|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "US", "stockCount", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "US", "stockValueChange", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Bank reconciliation deposit\",\"de-DE\":\"Bankabstimmung Einzahlungen\",\"fr-FR\":\"Sortie de compte de rapprochement bancaire\"}", "ZA", "bankReconciliationDeposit", "journalEntry", "Y", "ZA|CRJ", "documentDate", "transactionDescription", "", ""], ["sage", "Y", "{\"en\":\"Bank reconciliation withdrawal\",\"de-DE\":\"Bankabstimmung Auszahlungen\",\"fr-FR\":\"Dépôt sur compte de rapprochement bancaire \"}", "ZA", "bankReconciliationWithdrawal", "journalEntry", "Y", "ZA|CDJ", "documentDate", "transactionDescription", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "ZA", "salesCreditMemo", "journalEntry", "Y", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "ZA", "miscellaneousStockIssue", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "ZA", "miscellaneousStockReceipt", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "ZA", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "ZA|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "ZA", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "ZA|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "ZA", "purchaseInvoice", "journalEntry", "Y", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "ZA", "purchaseReceipt", "journalEntry", "N", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "ZA", "purchaseCreditMemo", "journalEntry", "Y", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "ZA", "purchaseReturn", "journalEntry", "N", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "ZA", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "ZA|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "ZA", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "ZA|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "ZA", "salesInvoice", "journalEntry", "Y", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "ZA", "salesReturnReceipt", "journalEntry", "N", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "ZA", "salesShipment", "journalEntry", "N", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "ZA", "stockAdjustment", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "ZA", "workInProgress", "journalEntry", "N", "ZA|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "ZA", "stockCount", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "ZA", "stockValueChange", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "DE", "miscellaneousStockReceipt", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "DE", "miscellaneousStockIssue", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "DE", "stockAdjustment", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "DE", "stockCount", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "DE", "purchaseReceipt", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour achat\"}", "DE", "purchaseReturn", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "DE", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "DE|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "DE", "purchaseInvoice", "journalEntry", "Y", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir achat\"}", "DE", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "DE|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "DE", "salesShipment", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "DE", "salesReturnReceipt", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir vente\"}", "DE", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "DE|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "DE", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "DE|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"AP invoice\",\"de-DE\":\"Eingangsrechnung\",\"fr-FR\":\"Facture fournisseur\"}", "DE", "apInvoice", "journalEntry", "Y", "DE|EREC", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"AR invoice\",\"de-DE\":\"Ausgangsrechnung\",\"fr-FR\":\"Facture client\"}", "DE", "arInvoice", "journalEntry", "Y", "DE|VREC", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"En cours de production\"}", "DE", "workInProgress", "journalEntry", "N", "DE|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "DE", "purchaseCreditMemo", "journalEntry", "Y", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "DE", "stockValueChange", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "AU", "salesCreditMemo", "journalEntry", "Y", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "AU", "miscellaneousStockIssue", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "AU", "miscellaneousStockReceipt", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "AU", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "AU|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "AU", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "AU|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "AU", "purchaseInvoice", "journalEntry", "Y", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "AU", "purchaseReceipt", "journalEntry", "N", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "AU", "purchaseCreditMemo", "journalEntry", "Y", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "AU", "purchaseReturn", "journalEntry", "N", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "AU", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "AU|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "AU", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "AU|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "AU", "salesInvoice", "journalEntry", "Y", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "AU", "salesReturnReceipt", "journalEntry", "N", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "AU", "salesShipment", "journalEntry", "N", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "AU", "stockAdjustment", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "AU", "workInProgress", "journalEntry", "N", "AU|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "AU", "stockCount", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "AU", "stockValueChange", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "GB", "stockTransferShipment", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "ZA", "stockTransferShipment", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "US", "stockTransferShipment", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "AU", "stockTransferShipment", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "FR", "stockTransferShipment", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "DE", "stockTransferShipment", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""]]}, "SysEnumTransformation": {"metadata": {"rootFactoryName": "SysEnumTransformation", "name": "SysEnumTransformation", "naturalKeyColumns": ["_tenant_id", "local_enum", "remote_app", "remote_app_version", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "local_enum", "type": "reference", "targetFactoryName": "MetaDataType"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "remote_app", "type": "reference", "targetFactoryName": "SysApp"}, {"name": "remote_app_version", "type": "string"}, {"name": "remote_enum", "type": "string"}]}, "rows": []}, "SysNodeMapping": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SysNodeMapping", "name": "SysNodeMapping", "naturalKeyColumns": ["_tenant_id", "transform", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}, {"name": "local_property", "type": "string"}, {"name": "remote_property", "type": "string"}, {"name": "kind", "type": "enum", "enumMembers": ["path_to_path", "path_to_constant", "path_to_function", "constant_to_path", "function_to_path"]}], "vitalParentColumn": {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}}, "rows": []}}}