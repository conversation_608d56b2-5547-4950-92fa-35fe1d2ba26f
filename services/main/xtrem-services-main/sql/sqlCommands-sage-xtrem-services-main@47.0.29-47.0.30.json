{"fromVersion": "47.0.29", "toVersion": "47.0.30", "gitHead": "dddb749620a6bd99f6072f22c88648191343ca5c", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        is_audit_enabled BOOLEAN;", "        p_root_table_name VARCHAR;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        p_root_table_name := TG_ARGV[0];", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = COALESCE(NEW._id, OLD._id)", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_audit_log_ind0;"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='preferred_process_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.preferred_process_enum AS ENUM('purchasing','production');", "                        END IF;", "                    END", "                $$;", "                 ", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='stock_valuation_status_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.stock_valuation_status_enum AS ENUM('inProgress','completed','draft','error');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, \"user\" INT8 NOT NULL, company INT8, sites _INT8 NOT NULL, from_item INT8, to_item INT8, reorder_type %%SCHEMA_NAME%%.preferred_process_enum, end_date DATE NOT NULL, status %%SCHEMA_NAME%%.stock_valuation_status_enum NOT NULL, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"stock_reorder_calculation_input_set_PK\" PRIMARY KEY(_tenant_id,_id));CREATE UNIQUE INDEX stock_reorder_calculation_input_set_ind0 ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set(_tenant_id ASC,\"user\" ASC);", "COMMENT ON TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"isSharedByAllTenants\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.company IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"company\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.sites IS '{", "  \"type\": \"referenceArray\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.from_item IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"item\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.to_item IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"item\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.reorder_type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"preferred_process_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.end_date IS '{", "  \"type\": \"date\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set.status IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"stock_valuation_status_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_input_set._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.stock_reorder_calculation_input_set to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='preferred_process_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.preferred_process_enum AS ENUM('purchasing','production');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, _sort_value INT8 DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.stock_reorder_calculation_result_line'::text, '_id'::text))::regclass) * 100) NOT NULL, input_set INT8 NOT NULL, item_site INT8 NOT NULL, supplier INT8, order_date DATE NOT NULL, reorder_type %%SCHEMA_NAME%%.preferred_process_enum, stock_unit INT8 NOT NULL, purchase_unit INT8, currency INT8 NOT NULL, quantity NUMERIC(28,10) NOT NULL, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"stock_reorder_calculation_result_line_PK\" PRIMARY KEY(_tenant_id,_id));CREATE UNIQUE INDEX stock_reorder_calculation_result_line_ind0 ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line(_tenant_id ASC,input_set ASC,_sort_value ASC);", "COMMENT ON TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"isSharedByAllTenants\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._sort_value IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.input_set IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"stock_reorder_calculation_input_set\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.item_site IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"item_site\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.supplier IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"supplier\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.order_date IS '{", "  \"type\": \"date\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.reorder_type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"preferred_process_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.stock_unit IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"unit_of_measure\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.purchase_unit IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"unit_of_measure\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.currency IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"currency\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line.quantity IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": false,", "  \"precision\": 20,", "  \"scale\": 5", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_reorder_calculation_result_line._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.stock_reorder_calculation_result_line to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line__update_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line__create_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line_input_set_fk\" FOREIGN KEY(_tenant_id,input_set) REFERENCES %%SCHEMA_NAME%%.stock_reorder_calculation_input_set(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_input_set_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"stock_reorder_calculation_input_set\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"input_set\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line_item_site_fk\" FOREIGN KEY(_tenant_id,item_site) REFERENCES %%SCHEMA_NAME%%.item_site(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_item_site_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"item_site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"item_site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line_supplier_fk\" FOREIGN KEY(_tenant_id,supplier) REFERENCES %%SCHEMA_NAME%%.supplier(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_supplier_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"supplier\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"supplier\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line_stock_unit_fk\" FOREIGN KEY(_tenant_id,stock_unit) REFERENCES %%SCHEMA_NAME%%.unit_of_measure(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_stock_unit_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"unit_of_measure\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"stock_unit\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line_purchase_unit_fk\" FOREIGN KEY(_tenant_id,purchase_unit) REFERENCES %%SCHEMA_NAME%%.unit_of_measure(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_purchase_unit_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"unit_of_measure\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"purchase_unit\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line_currency_fk\" FOREIGN KEY(_tenant_id,currency) REFERENCES %%SCHEMA_NAME%%.currency(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_currency_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"currency\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"currency\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_result_line ADD CONSTRAINT \"stock_reorder_calculation_result_line__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_result_line__tenant_id_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set ADD CONSTRAINT \"stock_reorder_calculation_input_set__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_input_set__update_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set ADD CONSTRAINT \"stock_reorder_calculation_input_set__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_input_set__create_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set ADD CONSTRAINT \"stock_reorder_calculation_input_set_user_fk\" FOREIGN KEY(_tenant_id,\"user\") REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set ADD CONSTRAINT \"stock_reorder_calculation_input_set_company_fk\" FOREIGN KEY(_tenant_id,company) REFERENCES %%SCHEMA_NAME%%.company(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_company_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set ADD CONSTRAINT \"stock_reorder_calculation_input_set_from_item_fk\" FOREIGN KEY(_tenant_id,from_item) REFERENCES %%SCHEMA_NAME%%.item(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_from_item_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"item\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"from_item\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set ADD CONSTRAINT \"stock_reorder_calculation_input_set_to_item_fk\" FOREIGN KEY(_tenant_id,to_item) REFERENCES %%SCHEMA_NAME%%.item(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_to_item_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"item\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"to_item\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_reorder_calculation_input_set ADD CONSTRAINT \"stock_reorder_calculation_input_set__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_reorder_calculation_input_set__tenant_id_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_audit_log_ind0 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,record_id ASC,COALESCE(new_update_tick, (- ((2)::bigint ^ (62)::bigint))::bigint) ASC);"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00DWLZ-64335\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "Report"}}, {"action": "reload_setup_data", "args": {"factory": "ReportVariable"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT stock_reorder_calculation_input_set__update_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_input_set__create_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_company_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_from_item_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"item\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"from_item\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_input_set_to_item_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"item\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"to_item\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_input_set__tenant_id_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_input_set IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line__update_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line__create_user_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_input_set_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"stock_reorder_calculation_input_set\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"input_set\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_item_site_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"item_site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"item_site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_supplier_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"supplier\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"supplier\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_stock_unit_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"unit_of_measure\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"stock_unit\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_purchase_unit_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"unit_of_measure\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"purchase_unit\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line_currency_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"currency\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"currency\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_reorder_calculation_result_line__tenant_id_fk ON %%SCHEMA_NAME%%.stock_reorder_calculation_result_line IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"Report": {"metadata": {"rootFactoryName": "Report", "name": "Report", "naturalKeyColumns": ["_tenant_id", "name"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "name", "type": "string"}, {"name": "description", "type": "string"}, {"name": "parent_package", "type": "string"}, {"name": "active_template", "type": "reference", "isNullable": true, "isOwnedByCustomer": true, "targetFactoryName": "ReportTemplate"}, {"name": "report_type", "type": "enum", "enumMembers": ["printedDocument", "email"]}, {"name": "is_factory", "type": "boolean"}]}, "rows": [["sage", "activeUsersListing", "List all active users", "xtrem-system", "", "printedDocument", "Y"], ["sage", "usersByType", "List users by type", "xtrem-system", "usersByType", "printedDocument", "Y"], ["sage", "reportDefinitions", "List contents of reports and their active templates", "xtrem-reporting", "", "printedDocument", "Y"], ["sage", "onboarding_tenant", "Onboarding email when new tenant created", "xtrem-system", "onboarding_tenant", "email", "Y"], ["sage", "onboarding_user", "Onboarding email when new user created", "xtrem-system", "onboarding_user", "email", "Y"], ["sage", "purchaseOrder", "Purchase order template setup", "xtrem-purchasing", "purchaseOrderTemplate", "printedDocument", "Y"], ["sage", "purchaseOrdersByStatus", "Purchase order report by status", "xtrem-purchasing", "purchaseOrdersByStatus", "printedDocument", "Y"], ["sage", "purchase_requisition_approval_mail", "purchase_requisition_approval_mail", "xtrem-purchasing", "purchase_requisition_approval_mail", "email", "Y"], ["sage", "purchase_requisition_request_changes_mail", "purchase_requisition_request_changes_mail", "xtrem-purchasing", "purchase_requisition_request_changes_mail", "email", "Y"], ["sage", "purchase_order_approval_mail", "purchase_order_approval_mail", "xtrem-purchasing", "purchase_order_approval_mail", "email", "Y"], ["sage", "purchase_order_request_changes_mail", "purchase_order_request_changes_mail", "xtrem-purchasing", "purchase_order_request_changes_mail", "email", "Y"], ["sage", "purchase_invoice_buyer_notification_mail", "purchase_invoice_buyer_notification_mail", "xtrem-purchasing", "purchase_invoice_buyer_notification_mail", "email", "Y"], ["sage", "purchase_credit_memo_buyer_notification_mail", "purchase_credit_memo_buyer_notification_mail", "xtrem-purchasing", "purchase_credit_memo_buyer_notification_mail", "email", "Y"], ["sage", "purchase_order_send", "purchase_order_send", "xtrem-purchasing", "purchase_order_send", "email", "Y"], ["sage", "purchaseReceipt", "Purchase receipt template setup", "xtrem-purchasing", "purchaseReceiptTemplate", "printedDocument", "Y"], ["sage", "packingSlip", "Packing slip", "xtrem-sales", "packingSlip", "printedDocument", "Y"], ["sage", "salesInvoice", "Sales invoice", "xtrem-sales", "salesInvoice", "printedDocument", "Y"], ["sage", "salesCreditMemo", "Sales credit memo", "xtrem-sales", "salesCreditMemo", "printedDocument", "Y"], ["sage", "sales_return_request_approval_mail", "sales_return_request_approval_mail", "xtrem-sales", "sales_return_request_approval_mail", "email", "Y"], ["sage", "sales_invoice_send", "sales_invoice_send", "xtrem-sales", "sales_invoice_send", "email", "Y"], ["sage", "sales_credit_memo_send", "sales_credit_memo_send", "xtrem-sales", "sales_credit_memo_send", "email", "Y"], ["sage", "sales_order_send", "sales_order_send", "xtrem-sales", "sales_order_send", "email", "Y"], ["sage", "salesOrder", "Sales order", "xtrem-sales", "salesOrder", "printedDocument", "Y"], ["sage", "salesShipmentPickList", "Sales shipment pick list", "xtrem-sales", "salesShipmentPickList", "printedDocument", "Y"], ["sage", "salesOrderQuote", "Sales order quote", "xtrem-sales", "salesOrderQuote", "printedDocument", "Y"], ["sage", "sales_order_quote_send", "sales_order_quote_send", "xtrem-sales", "sales_order_quote_send", "email", "Y"], ["sage", "proformaInvoice", "Proforma invoice", "xtrem-sales", "proformaInvoice", "printedDocument", "Y"], ["sage", "bomMultiLevel", "Bill of material with its multilevel components list. It can be restricted to a single level components list.", "xtrem-technical-data", "bomMultiLevel", "printedDocument", "Y"], ["sage", "sageJobTraveler", "Work order job traveler", "xtrem-manufacturing", "sageJobTravelerTemplate", "printedDocument", "Y"], ["sage", "workOrderPickList", "Work order pick list", "xtrem-manufacturing", "workOrderPickList", "printedDocument", "Y"], ["sage", "stockTransferOrderApprovalMail", "Stock transfer order approval mail", "xtrem-supply-chain", "stockTransferOrderApprovalMail", "email", "Y"]]}, "ReportVariable": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "ReportVariable", "name": "ReportVariable", "naturalKeyColumns": ["_tenant_id", "report", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "name", "type": "string"}, {"name": "title", "type": "string", "isLocalized": true}, {"name": "is_mandatory", "type": "boolean", "isNullable": true}, {"name": "type", "type": "enum", "enumMembers": ["boolean", "string", "byte", "short", "integer", "decimal", "float", "double", "enum", "date", "time", "datetime", "uuid", "binaryStream", "textStream", "json", "reference", "collection", "jsonReference", "integerArray", "enumA<PERSON>y", "referenceArray", "stringArray", "integerRange", "decimalRange", "date<PERSON><PERSON><PERSON>", "datetime<PERSON><PERSON><PERSON>"]}, {"name": "data_type", "type": "reference", "isNullable": true, "targetFactoryName": "MetaDataType"}, {"name": "report", "type": "reference", "targetFactoryName": "Report"}], "vitalParentColumn": {"name": "report", "type": "reference", "targetFactoryName": "Report"}}, "rows": [["sage", "100", "includeSystemUsers", "{\"base\":\"Include system users\"}", null, "boolean", null, "usersByType"], ["sage", "100", "userName", "{\"base\":\"User name\"}", null, "string", null, "onboarding_user"], ["sage", "100", "order", "{\"base\":\"Order\"}", null, "reference", "purchaseOrder", "purchaseOrder"], ["sage", "200", "receipt", "{\"base\":\"Receipt\"}", null, "reference", "purchaseReceipt", "purchaseReceipt"], ["sage", "100", "shipment", "{\"base\":\"Shipment\"}", null, "reference", "salesShipment", "packingSlip"], ["sage", "100", "invoice", "{\"base\":\"Invoice\"}", null, "reference", "salesInvoice", "salesInvoice"], ["sage", "100", "creditMemo", "{\"base\":\"Credit memo\"}", null, "reference", "salesCreditMemo", "salesCreditMemo"], ["sage", "100", "order", "{\"base\":\"Order\"}", null, "reference", "salesOrder", "salesOrder"], ["sage", "100", "shipment", "{\"base\":\"Shipment\"}", null, "reference", "salesShipment", "salesShipmentPickList"], ["sage", "100", "proforma", "{\"base\":\"Order\"}", null, "reference", "salesOrder", "proformaInvoice"], ["sage", "100", "order", "{\"base\":\"Order\"}", null, "reference", "salesOrder", "salesOrderQuote"], ["sage", "100", "salesOrder", "{\"base\":\"Order\"}", null, "reference", "salesOrder", "sales_order_send"], ["sage", "200", "contact", "{\"base\":\"Contact\"}", null, "reference", "contact", "sales_order_send"], ["sage", "300", "site", "{\"base\":\"Site\"}", null, "reference", "site", "sales_order_send"], ["sage", "10", "bomPrintoutId", "{\"base\":\"BOM printout id\"}", null, "string", null, "bomMultiLevel"], ["sage", "20", "singleLevel", "{\"base\":\"Single level\"}", null, "boolean", null, "bomMultiLevel"], ["sage", "30", "omitCost", "{\"base\":\"Omit cost\"}", null, "boolean", null, "bomMultiLevel"], ["sage", "40", "omitInstruction", "{\"base\":\"Omit instruction\"}", null, "boolean", null, "bomMultiLevel"], ["sage", "100", "order", "{\"base\":\"Order\"}", null, "reference", "workOrder", "sageJobTraveler"], ["sage", "100", "order", "{\"base\":\"Order\"}", null, "reference", "workOrder", "workOrderPickList"]]}}}