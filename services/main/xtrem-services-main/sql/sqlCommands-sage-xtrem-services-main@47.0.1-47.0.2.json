{"fromVersion": "47.0.1", "toVersion": "47.0.2", "gitHead": "7a89ea2a2d83aead0de3b05b02a2414d09277c94", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN ", "    \tSELECT trigger_name, event_object_table ", "    \tFROM information_schema.triggers ", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN ", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN ", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", ""]}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.base_status_enum ADD VALUE IF NOT EXISTS 'readyToProcess'   ;"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.base_status_enum ADD VALUE IF NOT EXISTS 'readyToShip'   ;"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.base_status_enum ADD VALUE IF NOT EXISTS 'shipped'   ;"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='base_display_status_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.base_display_status_enum AS ENUM('draft','pendingApproval','approved','rejected','changeRequested','confirmed','readyToProcess','readyToShip','partiallyShipped','shipped','received','partiallyInvoiced','invoiced','closed','postingInProgress','error');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_document ADD COLUMN IF NOT EXISTS display_status %%SCHEMA_NAME%%.base_display_status_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_document.display_status IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"base_display_status_enum\"", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.base_document AS t0 SET display_status=$1 WHERE ((t0.display_status IS NULL))", "args": ["draft"], "actionDescription": "Auto data action for property BaseDocument.displayStatus"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_outbound_shipment_document ADD COLUMN IF NOT EXISTS tracking_number VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '', ADD COLUMN IF NOT EXISTS delivery_mode INT8, ADD COLUMN IF NOT EXISTS delivery_lead_time INT8, ADD COLUMN IF NOT EXISTS delivery_date DATE;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_outbound_shipment_document.tracking_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_outbound_shipment_document.delivery_mode IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"delivery_mode\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_outbound_shipment_document.delivery_lead_time IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_outbound_shipment_document.delivery_date IS '{", "  \"type\": \"date\",", "  \"isSystem\": false", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document AS t0 SET tracking_number=$1 WHERE (((t0.tracking_number IS NULL) OR (t0.tracking_number = $2)))", "args": ["", ""], "actionDescription": "Auto data action for property BaseOutboundShipmentDocument.trackingNumber"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='stock_transfer_shipment_type_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.stock_transfer_shipment_type_enum AS ENUM('stockTransfer','companyTransfer','serviceTransfer');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_shipment ADD COLUMN IF NOT EXISTS type %%SCHEMA_NAME%%.stock_transfer_shipment_type_enum, ADD COLUMN IF NOT EXISTS supplier INT8, ADD COLUMN IF NOT EXISTS receiving_site INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transfer_shipment.type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"stock_transfer_shipment_type_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transfer_shipment.supplier IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"supplier\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transfer_shipment.receiving_site IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"site\",", "  \"isSelfReference\": false", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.stock_transfer_shipment AS t0 SET \"type\"=$1 WHERE ((t0.type IS NULL))", "args": ["stockTransfer"], "actionDescription": "Auto data action for property StockTransferShipment.type"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_outbound_shipment_document_line ADD COLUMN IF NOT EXISTS stock_transaction_status %%SCHEMA_NAME%%.stock_document_transaction_status_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_outbound_shipment_document_line.stock_transaction_status IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"stock_document_transaction_status_enum\"", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document_line AS t0 SET stock_transaction_status=$1 WHERE ((t0.stock_transaction_status IS NULL))", "args": ["draft"], "actionDescription": "Auto data action for property BaseOutboundShipmentDocumentLine.stockTransactionStatus"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.base_line_to_base_outbound_line (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, _constructor VARCHAR(100) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, quantity_in_stock_unit NUMERIC(28,10) NOT NULL, quantity NUMERIC(28,10) NOT NULL, amount NUMERIC(28,10), line INT8 NOT NULL, outbound_line INT8 NOT NULL, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"base_line_to_base_outbound_line_PK\" PRIMARY KEY(_tenant_id,_id));", "COMMENT ON TABLE %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"isSharedByAllTenants\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._constructor IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 100", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line.quantity_in_stock_unit IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": false,", "  \"precision\": 20,", "  \"scale\": 5", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line.quantity IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": false,", "  \"precision\": 20,", "  \"scale\": 5", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line.amount IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": false,", "  \"precision\": 10,", "  \"scale\": 3", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line.line IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"base_distribution_document_line\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line.outbound_line IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"base_outbound_document_line\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_base_outbound_line._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.base_line_to_base_outbound_line to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.base_line_to_base_outbound_line__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id INT8 NOT NULL, _custom_data JSONB,CONSTRAINT \"stock_transfer_order_line_to_stock_transfer_shipment_line_PK\" PRIMARY KEY(_tenant_id,_id));", "COMMENT ON TABLE %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_line_to_base_outbound_line\",", "  \"rootTable\": \"base_line_to_base_outbound_line\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.stock_6b3d5505431ded9f82c3ffe7b6c8781e_hipment_line_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_line_to_base_outbound_line WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.stock_6b3d5505431ded9f82c3ffe7b6c8781e_hipment_line_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line to xtrem"}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            WITH CTE AS (", "                 SELECT a._id, d.mode, a._tenant_id", "                FROM %%SCHEMA_NAME%%.base_business_relation a", "                LEFT OUTER JOIN %%SCHEMA_NAME%%.business_entity b ON a.business_entity = b._id and a._tenant_id = b._tenant_id", "                LEFT OUTER JOIN (", "                    SELECT *", "                    FROM %%SCHEMA_NAME%%.business_entity_address", "                    WHERE is_primary = true", "                ) c ON b._id = c.business_entity and a._tenant_id = c._tenant_id", "                LEFT OUTER JOIN %%SCHEMA_NAME%%.delivery_detail d ON c._id = d.address and a._tenant_id = d._tenant_id", "                )", "                UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document a", "                    SET delivery_mode = (", "                        SELECT _id", "                        FROM CTE cte", "                        WHERE cte._id = b.business_relation and cte._tenant_id = b._tenant_id", "                    )", "                FROM %%SCHEMA_NAME%%.base_distribution_document b", "                WHERE a._id = b._id AND a._tenant_id = b._tenant_id", "                AND delivery_mode IS NULL;", "                END $$;"], "actionDescription": "Add properties to the base outbound shipment document - delivery mode  "}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document a", "            SET delivery_lead_time = 0", "            FROM %%SCHEMA_NAME%%.base_document b", "            WHERE a._id = b._id and a._tenant_id = b._tenant_id;", "            END $$;"], "actionDescription": "Add properties to the base outbound shipment document - delivery lead time  "}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            UPDATE %%SCHEMA_NAME%%.base_outbound_shipment_document a", "            SET delivery_date = b.date", "            FROM %%SCHEMA_NAME%%.base_document b", "            WHERE a._id = b._id and a._tenant_id = b._tenant_id;", "            END $$;"], "actionDescription": "Add properties to the base outbound order document - delivery date  "}, {"isSysPool": false, "sql": ["DO $$", "        BEGIN", "            WITH CTE AS (", "                SELECT DISTINCT ON (a._tenant_id) a._id, a._tenant_id", "                FROM %%SCHEMA_NAME%%.site a", "                WHERE a.is_inventory = True AND a.is_active = true", "                LIMIT 1", "            )", "            UPDATE %%SCHEMA_NAME%%.stock_transfer_shipment a", "            SET receiving_site = cte._id", "            FROM CTE cte", "            WHERE a._tenant_id = cte._tenant_id;", "        END $$;"], "actionDescription": "Add properties to the stock transfer shipment - receiving site"}, {"isSysPool": false, "sql": ["DO $$", "            BEGIN", "                WITH CTE AS (", "                    SELECT DISTINCT ON (a._tenant_id) a._id, a._tenant_id", "                    FROM %%SCHEMA_NAME%%.supplier a", "                    INNER JOIN %%SCHEMA_NAME%%.base_business_relation b", "                    ON a._id = b._id AND a._tenant_id = b._tenant_id", "                    WHERE b.is_active = True", "                    LIMIT 1", "                )", "                UPDATE %%SCHEMA_NAME%%.stock_transfer_shipment a", "                SET supplier = cte._id", "                FROM CTE cte", "                WHERE a._tenant_id = cte._tenant_id;", "            END $$;"], "actionDescription": "Add properties to the stock transfer shipment - supplier"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line ADD CONSTRAINT \"stock_381a4827d302a58b6113b4146ae57bf4_fer_shipment_line__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_line_to_base_outbound_line(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_381a4827d302a58b6113b4146ae57bf4_fer_shipment_line__id_fk ON %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line IS '{", "  \"targetTableName\": \"base_line_to_base_outbound_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line ADD CONSTRAINT \"stock_420d7b49ff679792d9d6ecfb19a6c8ca_pment_line__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_420d7b49ff679792d9d6ecfb19a6c8ca_pment_line__tenant_id_fk ON %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_line_to_base_outbound_line ADD CONSTRAINT \"base_line_to_base_outbound_line__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_line_to_base_outbound_line__update_user_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_line_to_base_outbound_line ADD CONSTRAINT \"base_line_to_base_outbound_line__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_line_to_base_outbound_line__create_user_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_line_to_base_outbound_line ADD CONSTRAINT \"base_line_to_base_outbound_line_line_fk\" FOREIGN KEY(_tenant_id,line) REFERENCES %%SCHEMA_NAME%%.base_distribution_document_line(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_line_to_base_outbound_line_line_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"base_distribution_document_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"line\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_line_to_base_outbound_line ADD CONSTRAINT \"base_line_to_base_outbound_line_outbound_line_fk\" FOREIGN KEY(_tenant_id,outbound_line) REFERENCES %%SCHEMA_NAME%%.base_outbound_document_line(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_line_to_base_outbound_line_outbound_line_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"base_outbound_document_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"outbound_line\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_line_to_base_outbound_line ADD CONSTRAINT \"base_line_to_base_outbound_line__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_line_to_base_outbound_line__tenant_id_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_outbound_shipment_document_line ALTER COLUMN stock_transaction_status SET NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_distribution_document_line DROP COLUMN IF EXISTS line_status;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_shipment ADD CONSTRAINT \"stock_transfer_shipment_supplier_fk\" FOREIGN KEY(_tenant_id,supplier) REFERENCES %%SCHEMA_NAME%%.supplier(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_transfer_shipment_supplier_fk ON %%SCHEMA_NAME%%.stock_transfer_shipment IS '{", "  \"targetTableName\": \"supplier\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"supplier\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_shipment ADD CONSTRAINT \"stock_transfer_shipment_receiving_site_fk\" FOREIGN KEY(_tenant_id,receiving_site) REFERENCES %%SCHEMA_NAME%%.site(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT stock_transfer_shipment_receiving_site_fk ON %%SCHEMA_NAME%%.stock_transfer_shipment IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"receiving_site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.stock_transfer_shipment ALTER COLUMN type SET NOT NULL, ALTER COLUMN supplier SET NOT NULL, ALTER COLUMN receiving_site SET NOT NULL;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_outbound_shipment_document ADD CONSTRAINT \"base_outbound_shipment_document_delivery_mode_fk\" FOREIGN KEY(_tenant_id,delivery_mode) REFERENCES %%SCHEMA_NAME%%.delivery_mode(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_outbound_shipment_document_delivery_mode_fk ON %%SCHEMA_NAME%%.base_outbound_shipment_document IS '{", "  \"targetTableName\": \"delivery_mode\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"delivery_mode\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_outbound_shipment_document ALTER COLUMN tracking_number SET NOT NULL, ALTER COLUMN delivery_mode SET NOT NULL, ALTER COLUMN delivery_lead_time SET NOT NULL, ALTER COLUMN delivery_date SET NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document ALTER COLUMN display_status SET NOT NULL;"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", false, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00CX9C-63896\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT base_outbound_shipment_document_delivery_mode_fk ON %%SCHEMA_NAME%%.base_outbound_shipment_document IS '{", "  \"targetTableName\": \"delivery_mode\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"delivery_mode\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_transfer_shipment_supplier_fk ON %%SCHEMA_NAME%%.stock_transfer_shipment IS '{", "  \"targetTableName\": \"supplier\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"supplier\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_transfer_shipment_receiving_site_fk ON %%SCHEMA_NAME%%.stock_transfer_shipment IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"receiving_site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_line_to_base_outbound_line__update_user_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_line_to_base_outbound_line__create_user_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_line_to_base_outbound_line_line_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"base_distribution_document_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"line\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_line_to_base_outbound_line_outbound_line_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"base_outbound_document_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"outbound_line\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT base_line_to_base_outbound_line__tenant_id_fk ON %%SCHEMA_NAME%%.base_line_to_base_outbound_line IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_381a4827d302a58b6113b4146ae57bf4_fer_shipment_line__id_fk ON %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line IS '{", "  \"targetTableName\": \"base_line_to_base_outbound_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT stock_420d7b49ff679792d9d6ecfb19a6c8ca_pment_line__tenant_id_fk ON %%SCHEMA_NAME%%.stock_transfer_order_line_to_stock_transfer_shipment_line IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {}}