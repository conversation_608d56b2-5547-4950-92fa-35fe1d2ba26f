{"fromVersion": "47.0.16", "toVersion": "47.0.17", "gitHead": "4447e5d2bc3e87155b82874e39975126916d8dd0", "commands": [{"action": "system_upgrade", "args": {"version": "47.0.17"}}, {"isSysPool": true, "sql": "ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.account ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.account_attribute_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.account_dimension_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounting_staging ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounting_staging_amount ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounting_staging_document_tax ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounting_staging_line_tax ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_payable_invoice ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_payable_invoice_line_dimension ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_payable_invoice_line_staging ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_receivable_advance ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_receivable_invoice ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_receivable_invoice_line_dimension ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_receivable_invoice_line_staging ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.accounts_receivable_payment ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.activity ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.address ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.address_base ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.allergen ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.allocation_queue ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.allocation_result ALTER COLUMN creation_datetime TYPE TIMESTAMPTZ(3) USING creation_datetime,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.allocation_result_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.analytical_data ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.attachment ALTER COLUMN last_modified TYPE TIMESTAMPTZ(3) USING last_modified,ALTER COLUMN last_download_date TYPE TIMESTAMPTZ(3) USING last_download_date,ALTER COLUMN orphaned_date TYPE TIMESTAMPTZ(3) USING orphaned_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.attachment_association ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.attribute ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.attribute_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.avalara_company ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.avalara_configuration ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.avalara_item_tax ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.bank_account ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_business_relation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_capability ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_certificate ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_document ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_document_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_finance_document ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_finance_line_dimension ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_line_discount_charge ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_line_to_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_line_to_sales_document_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_mapping ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_open_item ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_option_management ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_resource ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_stock_detail ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.base_tax ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.bill_of_material ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.bill_of_material_printout ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.bill_of_material_tracking ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.business_entity ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.cake_hr_configuration ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.capability_level ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.chart_of_account ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.company ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.company_attribute_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.company_default_attribute ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.company_default_dimension ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.company_dimension_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.component ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.component_printout ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.contact ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.contact_base ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.container ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.cost_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.cost_roll_up_input_set ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.cost_roll_up_result_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.cost_roll_up_sub_assembly ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.country ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.country_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.country_group_to_country ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.currency ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.custom_field ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.customer_price_reason ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.customer_supplier_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.daily_shift ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.daily_shift_detail ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dashboard ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dashboard_item ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.datev_configuration ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.datev_export ALTER COLUMN time_stamp TYPE TIMESTAMPTZ(3) USING time_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.datev_export_account ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.datev_export_business_relation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.delivery_detail ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.delivery_mode ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dev_tools ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dimension ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dimension_definition_level_and_default ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.dimension_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.employee ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.entity_use ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.exchange_rate ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.fifo_valuation_issue ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.fifo_valuation_tier ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.finance_transaction ALTER COLUMN last_status_update TYPE TIMESTAMPTZ(3) USING last_status_update,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.finance_transaction_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.formula ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.formula_component ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.formula_step ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.frp_1000_api_history ALTER COLUMN stamp TYPE TIMESTAMPTZ(3) USING stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.frp_1000_configuration ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.frp_1000_map ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.frp_1000_map_property ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.ghs_classification ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.group_role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.group_role_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.group_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.import_export_template ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.import_result ALTER COLUMN start_stamp TYPE TIMESTAMPTZ(3) USING start_stamp,ALTER COLUMN end_stamp TYPE TIMESTAMPTZ(3) USING end_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.incoterm ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.indirect_cost_origin ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.indirect_cost_section ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.indirect_cost_section_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct ALTER COLUMN session_expiration TYPE TIMESTAMPTZ(3) USING session_expiration,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_accounts_payable_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_accounts_receivable_advance_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_accounts_receivable_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_accounts_receivable_payment_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_bank_account_matching ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_import_session ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_journal_entry_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intacct_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intrastat_declaration ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intrastat_declaration_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intrastat_declaration_line_to_purchase_credit_memo_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intrastat_declaration_line_to_purchase_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intrastat_declaration_line_to_sales_credit_memo_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.intrastat_declaration_line_to_sales_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_allergen ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_classifications ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_customer ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_customer_price ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_site_cost ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_site_supplier ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_supplier ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_supplier_price ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.item_tax_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal_entry ALTER COLUMN frp_1000_last_integration_date TYPE TIMESTAMPTZ(3) USING frp_1000_last_integration_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal_entry_inquiry ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal_entry_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal_entry_line_dimension ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal_entry_line_staging ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal_entry_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.journal_entry_type_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.landed_cost_allocation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.landed_cost_base_document ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.landed_cost_document_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.landed_cost_item ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.landed_cost_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.legislation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.license_plate_number ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.locale ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.location ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.location_sequence ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.location_sequence_component ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.location_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.location_zone ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.lot ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.map_company ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.map_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.map_property ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.material_tracking ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_activity ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_activity_permission ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_data_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_node_factory ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_node_operation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_node_property ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_package ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.meta_service_option ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.movement_rule ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_bom ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_bom_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_calculation ALTER COLUMN calculation_date TYPE TIMESTAMPTZ(3) USING calculation_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_input ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_input_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_input_set ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_result_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_synchronization ALTER COLUMN last_sync_date_time TYPE TIMESTAMPTZ(3) USING last_sync_date_time,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.mrp_work_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.nature_of_transaction ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.operation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.operation_resource ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.operation_resource_detail ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.operation_tracking ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.order_assignment ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.organisation_service_fabric ALTER COLUMN token_creation TYPE TIMESTAMPTZ(3) USING token_creation,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.payment_term ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.posting_class ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.posting_class_definition ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.posting_class_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.posting_class_line_detail ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.production_tracking ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.proforma_invoice ALTER COLUMN link_expiration_date_time TYPE TIMESTAMPTZ(3) USING link_expiration_date_time,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_invoice_line_to_purchase_credit_memo_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_invoice_matching ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_order_line_to_purchase_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_order_line_to_purchase_receipt_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_receipt_line_to_purchase_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_receipt_line_to_purchase_return_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_receipt_matching ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_requisition ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_requisition_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_requisition_line_to_purchase_order_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_return_line_to_purchase_credit_memo_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_return_line_to_purchase_invoice_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.purchase_return_matching ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.reason_code ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_resource ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_style_variable ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_template ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_translatable_text ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_variable ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.report_wizard ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.resource_cost_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.resource_group_replacement ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.restricted_node ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.restricted_node_user_grant ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.role_activity ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.role_to_role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.routing ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_credit_memo ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_credit_memo_reason ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_invoice ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_order ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_return_receipt ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_return_request ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_return_request_reason ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sales_shipment ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sequence_number ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sequence_number_assignment ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sequence_number_assignment_document_type ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sequence_number_assignment_module ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sequence_number_assignment_setup ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sequence_number_component ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sequence_number_value ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.serial_number ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.shift_detail ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.shopify_configuration ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site_group_to_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.site_group_to_site_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.standard ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.standard_industrial_classification ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.statistical_procedure ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_adjustment ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_allocation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_change ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_count ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_count_line_serial_number ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_detail_lot ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_detail_serial_number ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_issue ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_journal ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_journal_serial_number ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_receipt ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_status ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_transaction ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_valuation_input_set ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_valuation_input_set_to_site ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_valuation_result_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_value_change ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.stock_value_correction ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.supply_planning ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.support_access_history ALTER COLUMN start_time TYPE TIMESTAMPTZ(3) USING start_time,ALTER COLUMN end_time TYPE TIMESTAMPTZ(3) USING end_time,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.synchronization_state ALTER COLUMN creation_stamp TYPE TIMESTAMPTZ(3) USING creation_stamp,ALTER COLUMN update_stamp TYPE TIMESTAMPTZ(3) USING update_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_app ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_changelog ALTER COLUMN change_date TYPE TIMESTAMPTZ(3) USING change_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_client_notification ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_client_notification_action ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_client_user_settings ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_csv_checksum ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_custom_record ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_custom_sql_history ALTER COLUMN start_date_time TYPE TIMESTAMPTZ(3) USING start_date_time,ALTER COLUMN end_date_time TYPE TIMESTAMPTZ(3) USING end_date_time,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_customer ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_enum_mapping ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_enum_transformation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_global_lock ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_job_schedule ALTER COLUMN start_stamp TYPE TIMESTAMPTZ(3) USING start_stamp,ALTER COLUMN end_stamp TYPE TIMESTAMPTZ(3) USING end_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_message ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_message_history ALTER COLUMN send_stamp TYPE TIMESTAMPTZ(3) USING send_stamp,ALTER COLUMN received_stamp TYPE TIMESTAMPTZ(3) USING received_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_node_mapping ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_node_transformation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification_history ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification_log_entry ALTER COLUMN timestamp TYPE TIMESTAMPTZ(3) USING timestamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_notification_state ALTER COLUMN time_started TYPE TIMESTAMPTZ(3) USING time_started,ALTER COLUMN time_ended TYPE TIMESTAMPTZ(3) USING time_ended,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_operation_transformation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_pack_allocation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_pack_version ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_patch_history ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_service_option ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_service_option_state ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_service_option_to_service_option ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_synchronization_state ALTER COLUMN succcess_stamp TYPE TIMESTAMPTZ(3) USING succcess_stamp,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_tenant ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_upgrade ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.sys_vendor ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_determination ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_determination_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_rate_repository ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_solution ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_solution_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_value ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_zone ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.tax_zone_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.team ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.third_party_application ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.unbilled_account_payable_input_set ALTER COLUMN execution_date TYPE TIMESTAMPTZ(3) USING execution_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.unbilled_account_payable_result_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.unbilled_account_receivable_input_set ALTER COLUMN execution_date TYPE TIMESTAMPTZ(3) USING execution_date,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.unbilled_account_receivable_result_line ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.unit_conversion_factor ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.unit_of_measure ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.uploaded_file ALTER COLUMN last_modified TYPE TIMESTAMPTZ(3) USING last_modified,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_billing_role ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_group ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_navigation ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.user_preferences ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.version_information ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.weekly_shift ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.widget_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_in_progress ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_in_progress_cost ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_order ALTER COLUMN start_datetime TYPE TIMESTAMPTZ(3) USING start_datetime,ALTER COLUMN end_datetime TYPE TIMESTAMPTZ(3) USING end_datetime,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_order_category ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_order_operation ALTER COLUMN start_datetime TYPE TIMESTAMPTZ(3) USING start_datetime,ALTER COLUMN end_datetime TYPE TIMESTAMPTZ(3) USING end_datetime,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_order_operation_resource ALTER COLUMN start_datetime TYPE TIMESTAMPTZ(3) USING start_datetime,ALTER COLUMN end_datetime TYPE TIMESTAMPTZ(3) USING end_datetime,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_order_operation_resource_detail ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_order_serial_number ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.work_order_view ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.workflow_definition ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.workflow_diagram ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp;ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.workflow_process ALTER COLUMN started_at TYPE TIMESTAMPTZ(3) USING started_at,ALTER COLUMN completed_at TYPE TIMESTAMPTZ(3) USING completed_at,ALTER COLUMN _create_stamp TYPE TIMESTAMPTZ(3) USING _create_stamp,ALTER COLUMN _update_stamp TYPE TIMESTAMPTZ(3) USING _update_stamp", "args": []}, {"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", ""]}, {"isSysPool": false, "sql": ["", "            DELETE FROM %%SCHEMA_NAME%%.sys_notification_state st", "                USING %%SCHEMA_NAME%%.sys_job_schedule sched", "                WHERE", "                    st._tenant_id = sched._tenant_id", "                AND st.schedule = sched._id", "                AND sched.id = 'purgeContentAddressableTables';", "", "            DELETE FROM %%SCHEMA_NAME%%.sys_job_schedule sched", "                WHERE sched.id = 'purgeContentAddressableTables';", "            "], "actionDescription": "Delete schedule entries for garbage collection of  content addressable tables"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00DHPA-64352\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "SysJobSchedule"}}, {"action": "reload_setup_data", "args": {"factory": "SysServiceOptionState"}}], "data": {"SysJobSchedule": {"metadata": {"rootFactoryName": "SysJobSchedule", "name": "SysJobSchedule", "naturalKeyColumns": ["_tenant_id", "id", "execution_user"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "operation", "type": "reference", "targetFactoryName": "MetaNodeOperation"}, {"name": "description", "type": "string"}, {"name": "is_active", "type": "boolean"}, {"name": "start_stamp", "type": "datetime", "isNullable": true}, {"name": "end_stamp", "type": "datetime", "isNullable": true}, {"name": "execution_user", "type": "reference", "targetFactoryName": "User"}, {"name": "execution_locale", "type": "string"}, {"name": "parameter_values", "type": "json"}, {"name": "cron_schedule", "type": "string"}, {"name": "time_zone", "type": "string"}, {"name": "id", "type": "string"}]}, "rows": [["sage", "SysNotificationState|purgeHistory|start", "Purge history - 3 months", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"months\",\"duration\":\"3\"}", "0 1 1 * *", "Europe/Paris", "purgeHistory_1"], ["sage", "SysNotificationState|purgeHistory|start", "Purge sync history - 2 weeks", "Y", null, null, "<EMAIL>", "en-US", "{\n    \"unit\": \"weeks\",\n    \"duration\": \"2\",\n    \"operationNames\": [\"SysSynchronizationTarget.synchronize\"]\n}\n", "30 1 * * *", "Europe/Paris", "purgeSyncHistory_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"info\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_1"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"success\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"success\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_2"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"warning\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"warning\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_3"], ["sage", "SysClientNotification|purgeSysClientNotification|start", "\"error\" client notification will automatically be deleted after seven days", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"error\",\"duration\":\"7\"}", "0 0 * * *", "Europe/Paris", "purgeSysClientNotification_4"], ["sage", "Attachment|purge|start", "Purge attachments and association attachments", "Y", null, null, "<EMAIL>", "en-US", "{\"unit\":\"days\",\"level\":\"info\",\"duration\":\"1\"}", "0 0 * * *", "Europe/Paris", "purgeAttachment_"], ["sage", "Company|syncCompanyOnHold|start", "Update company on hold check from Intacct", "Y", null, null, "<EMAIL>", "en-US", "{\"isAllCompanies\":\"true\"}", "0 3 * * *", "Europe/Paris", "companyOnHold_1"], ["sage", "IntacctMap|updateCustomMapping|start", "Update the custom mapping", "Y", null, null, "<EMAIL>", "en-US", "{\"filter\":\"{_id:{_nin:[]}}\"}", "0 8 * * 6", "Europe/Paris", "updateCustomMapping"], ["sage", "Attribute|updateRecordNoOnEmployee|start", "Update recordNo on employee", "Y", null, null, "<EMAIL>", "en-US", "{\n    \"filter\": \"{attributeType:{id:'employee'},uIntacctId:{_ne: ''}}\"\n}\n", "0 2,14 * * 3", "Europe/Paris", "updateRecordNoOnEmployee"], ["sage", "ItemSiteCost|syncStockValueChange|start", "Daily stock value change from item site costs", "Y", null, null, "<EMAIL>", "en-US", null, "1 0 * * *", "Europe/Paris", "syncStockValueChange_1"]]}, "SysServiceOptionState": {"metadata": {"rootFactoryName": "SysServiceOptionState", "name": "SysServiceOptionState", "naturalKeyColumns": ["_tenant_id", "service_option"], "columns": [{"name": "is_activable", "type": "boolean"}, {"name": "is_active", "type": "boolean"}, {"name": "service_option", "type": "reference", "targetFactoryName": "SysServiceOption"}]}, "rows": [["Y", null, "devTools"], ["Y", null, "isDemoTenant"], ["Y", null, "changelog"], ["Y", null, "notificationCenter"], ["Y", null, "authorizationServiceOption"], ["Y", null, "workflowOption"], ["Y", null, "frp1000ActivationOption"], ["Y", null, "intacctActivationOption"], ["Y", null, "serialNumberOption"], ["Y", null, "orderToOrderOption"], ["Y", null, "landedCostOption"], ["Y", null, "fifoValuationMethodOption"], ["Y", null, "allocationTransferOption"], ["Y", null, "landedCostOrderOption"], ["Y", null, "datevOption"], ["Y", null, "phantomItemOption"], ["Y", null, "intersiteStockTransferOption"], ["Y", null, "avalaraOption"], ["Y", "Y", "hrOption"], ["Y", "Y", "frp1000Option"], ["Y", "Y", "intacctOption"], ["Y", null, "intacctCashbookManagement"], ["Y", null, "synchronizationServiceOption"], ["Y", "Y", "serviceFabricTaxRepository"], ["Y", "Y", "serviceFabricTaxId"], ["Y", "Y", "serviceFabricOption"], ["Y", "Y", "shopifyOption"]]}}}