{"fromVersion": "51.0.29", "toVersion": "51.0.30", "gitHead": "d391ecb4bfac2e2939f5008b967ffea823cc4e0c", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.employee ALTER COLUMN is_active DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.employee DROP CONSTRAINT IF EXISTS employee_resource_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document ALTER COLUMN posting_date DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN number DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN display_status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN stock_site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN currency DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN text DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN internal_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN is_external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN is_transfer_header_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN is_transfer_line_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _create_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _update_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _create_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _update_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _update_tick DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _source_id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document_site_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document_site_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document_business_entity_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document_business_entity_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document_currency_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document_currency_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document__update_user_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.base_purchase_document_ind0;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order ALTER COLUMN approval_status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order ALTER COLUMN is_sent DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order ALTER COLUMN is_printed DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_return ALTER COLUMN approval_status DROP NOT NULL;"}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.sage_network_company (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, company INT8 NOT NULL, external_id VARCHAR(36) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, company_id VARCHAR(36) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, token VARCHAR(8033) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, token_creation TIMESTAMPTZ(3), _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"sage_network_company_PK\" PRIMARY KEY(_tenant_id,_id));CREATE UNIQUE INDEX sage_network_company_ind0 ON %%SCHEMA_NAME%%.sage_network_company(_tenant_id ASC,company ASC);", "COMMENT ON TABLE %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"isSharedByAllTenants\": false,", "  \"naturalKey\": [", "    \"company\"", "  ]", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company.company IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"company\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company.external_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 36", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company.company_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 36", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company.token IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"isEncrypted\": true,", "  \"maxLength\": 4000", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company.token_creation IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_network_company._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.sage_network_company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.sage_network_company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.sage_network_company to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.sage_network_company__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_count ADD COLUMN IF NOT EXISTS stock_transaction_status %%SCHEMA_NAME%%.stock_document_transaction_status_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_count.stock_transaction_status IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"stock_document_transaction_status_enum\"", "}';"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN document DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN site_linked_address DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN internal_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN is_external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN item DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN item_description DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN stock_site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN stock_site_linked_address DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN stock_unit DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN unit DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN unit_to_stock_unit_conversion_factor DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN quantity DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN quantity_in_stock_unit DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_document_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_document_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_document_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_item_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_item_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_item_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_stock_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line_unit_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.base_purchase_document_line_ind0;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_credit_memo_line ALTER COLUMN analytical_data DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS purchase_credit_memo_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS purchase_credit_memo_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS purchase_credit_memo_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_credit_memo_line DROP CONSTRAINT IF EXISTS purchase_credit_memo_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_invoice_line ALTER COLUMN analytical_data DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS purchase_invoice_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS purchase_invoice_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS purchase_invoice_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_invoice_line DROP CONSTRAINT IF EXISTS purchase_invoice_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order_line ALTER COLUMN analytical_data DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS purchase_order_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS purchase_order_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS purchase_order_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order_line DROP CONSTRAINT IF EXISTS purchase_order_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_receipt_line ALTER COLUMN analytical_data DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS purchase_receipt_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS purchase_receipt_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS purchase_receipt_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_receipt_line DROP CONSTRAINT IF EXISTS purchase_receipt_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_return_line ALTER COLUMN analytical_data DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS purchase_return_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS purchase_return_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS purchase_return_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_return_line DROP CONSTRAINT IF EXISTS purchase_return_line_analytical_data_fk;"}, {"isSysPool": false, "sql": ["DELETE FROM %%SCHEMA_NAME%%.employee", "        "], "actionDescription": "Delete employee records"}, {"isSysPool": true, "sql": ["", "            DO $$ DECLARE", "                pRecord RECORD;", "                base_id %%SCHEMA_NAME%%.base_document._id%TYPE :=1;", "                start_id %%SCHEMA_NAME%%.base_document._id%TYPE :=1;", "                financial_site_id %%SCHEMA_NAME%%.site._id%TYPE;", "                documentType varchar(255):= '';", "                start_time TIMESTAMP;", "                end_time TIMESTAMP;", "                elapsed_time_document_insert BIGINT :=0;", "                elapsed_time_purchase_doc BIGINT :=0;", "                elapsed_time_purchase_doc_line BIGINT :=0;", "                elapsed_time_purchase_doc_line_tax BIGINT :=0;", "                elapsed_time_purchase_doc_line_final BIGINT :=0;", "                elapsed_time_purchase_doc_finance BIGINT :=0;", "                elapsed_time_doc_association BIGINT :=0;", "", "                elapsed_time_finance_transaction BIGINT :=0;", "                elapsed_time_finance_transaction_line BIGINT :=0;", "                elapsed_time_finance_transaction_source BIGINT :=0;", "                elapsed_time_accounting_staging BIGINT :=0;", "                elapsed_time_base_open_item BIGINT :=0;", "                interval_time TIMESTAMP;", "                interval_time_end TIMESTAMP;", "", "                BEGIN", "", "                    start_time := clock_timestamp();", "                        -- There we are dropping the constraints to avoid the constraint violation", "                        SET CONSTRAINTS ALL DEFERRED;", "", "                        start_id  = (SELECT COALESCE(MAX(_id), 0) FROM %%SCHEMA_NAME%%.base_purchase_document);", "", "                        -- We are setting the sequence to the max id of the base_purchase_document if it is greater than the current sequence value", "                        IF start_id > COALESCE((SELECT last_value FROM %%SCHEMA_NAME%%.base_document__id_seq) , 0) THEN", "                            EXECUTE 'ALTER SEQUENCE %%SCHEMA_NAME%%.base_document__id_seq RESTART WITH ' || start_id+1;", "                        END IF;", "                    end_time := clock_timestamp();", "", "                    RAISE WARNING 'Elapsed time for setting sequence and deferred: %', (extract(milliseconds from end_time - start_time));", "", "                 RAISE WARNING 'BasePurchaseDocument upgrade started  ';", "", "                 FOR pRecord IN ( SELECT doc._constructor, doc._id, doc._tenant_id, doc._create_user, doc._create_stamp, doc._update_user, doc._update_stamp, doc.number , doc.status , doc.display_status,", "                    doc.site, doc.stock_site,  doc.currency, doc.internal_note, doc.external_note,", "                    doc.is_external_note, doc.is_transfer_header_note, doc.is_transfer_line_note,", "                    site.is_finance , site.financial_site, doc.site_address, doc.business_entity_address,", "                    COALESCE(porder.approval_status, return.approval_status, 'draft') as approval_status,", "                    COALESCE(porder.order_date, return.return_request_date , pcm.credit_memo_date, invoice.invoice_date, receipt.receipt_date ) as date,", "                    COALESCE(porder.is_printed, false) as is_printed,", "                    COALESCE(porder.is_sent, false) as is_sent,", "                    COALESCE(doc.text, '') as text", "", "                    FROM %%SCHEMA_NAME%%.base_purchase_document doc", "                        inner join %%SCHEMA_NAME%%.site as site on site._id = doc.site", "                            and site._tenant_id = doc._tenant_id", "                        left join %%SCHEMA_NAME%%.purchase_order as porder on porder._id = doc._id", "                            and porder._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseOrder'", "                        left join %%SCHEMA_NAME%%.purchase_return as return on return._id = doc._id", "                            and return._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReturn'", "                        left join %%SCHEMA_NAME%%.purchase_credit_memo as pcm on pcm._id = doc._id", "                            and pcm._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseCreditMemo'", "                        left join %%SCHEMA_NAME%%.purchase_invoice as invoice on invoice._id = doc._id", "                            and invoice._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseInvoice'", "                        left join %%SCHEMA_NAME%%.purchase_receipt as receipt on receipt._id = doc._id", "                            and receipt._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReceipt' )", "                LOOP", "", "                    financial_site_id := case when pRecord.is_finance = true then pRecord.site else pRecord.financial_site end;", "", "                    start_time := clock_timestamp();", "                    INSERT INTO %%SCHEMA_NAME%%.base_document(_constructor, _tenant_id, _create_user, _create_stamp, _update_user, _update_stamp,", "                        number, status, display_status, approval_status, date , is_printed, is_sent, site, stock_site, currency, financial_site,", "                        site_address, business_entity_address,", "                        internal_note, external_note, is_external_note, is_transfer_header_note, is_transfer_line_note, text )", "", "                        VALUES ( pRecord._constructor , pRecord._tenant_id, pRecord._create_user, pRecord._create_stamp, pRecord._update_user, pRecord._update_stamp,", "                        pRecord.number,", "                        pRecord.status::text::%%SCHEMA_NAME%%.base_status_enum,", "                        pRecord.display_status::text::%%SCHEMA_NAME%%.base_display_status_enum,", "                        pRecord.approval_status::text::%%SCHEMA_NAME%%.approval_status_enum,", "                        pRecord.date, pRecord.is_printed, pRecord.is_sent, pRecord.site, pRecord.stock_site, pRecord.currency, financial_site_id ,", "                        pRecord.site_address, pRecord.business_entity_address, pRecord.internal_note, pRecord.external_note,", "                        pRecord.is_external_note,  pRecord.is_transfer_header_note, pRecord.is_transfer_line_note, pRecord.text", "", "                        )", "                        RETURNING _id INTO base_id;", "                    end_time := clock_timestamp();", "                    elapsed_time_document_insert := elapsed_time_document_insert + (extract(milliseconds from end_time - start_time));", "", "                    start_time := clock_timestamp();", "                    UPDATE %%SCHEMA_NAME%%.base_purchase_document SET _id = base_id", "                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id ;", "                    end_time := clock_timestamp();", "                    elapsed_time_purchase_doc := elapsed_time_purchase_doc + (extract(milliseconds from end_time - start_time));", "", "                    start_time := clock_timestamp();", "                    UPDATE %%SCHEMA_NAME%%.base_purchase_document_line SET document = base_id", "                        WHERE document = pRecord._id AND _tenant_id = pRecord._tenant_id;", "                    end_time := clock_timestamp();", "                    elapsed_time_purchase_doc_line := elapsed_time_purchase_doc_line + (extract(milliseconds from end_time - start_time));", "", "                    start_time := clock_timestamp();", "                    UPDATE %%SCHEMA_NAME%%.attachment_association set source_node_id = base_id", "                            where source_node_id = pRecord._id AND source_node_name = pRecord._constructor AND _tenant_id = pRecord._tenant_id;", "                    end_time := clock_timestamp();", "                    elapsed_time_doc_association := elapsed_time_doc_association + (extract(milliseconds from end_time - start_time));", "", "                    start_time := clock_timestamp();", "                    UPDATE %%SCHEMA_NAME%%.purchase_document_tax SET document = base_id", "                        WHERE document = pRecord._id AND _tenant_id = pRecord._tenant_id;", "", "                    end_time := clock_timestamp();", "                    elapsed_time_purchase_doc_line_tax := elapsed_time_purchase_doc_line_tax + (extract(milliseconds from end_time - start_time));", "", "                    start_time := clock_timestamp();", "", "                    IF (pRecord._constructor='PurchaseOrder') THEN", "                        UPDATE %%SCHEMA_NAME%%.purchase_order SET _id = base_id", "                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;", "", "                        documentType= 'purchaseOrder';", "", "                    ELSIF (pRecord._constructor='PurchaseInvoice') THEN", "                        UPDATE %%SCHEMA_NAME%%.purchase_invoice SET _id = base_id", "                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;", "", "                       documentType= 'purchaseInvoice';", "", "                    ELSIF (pRecord._constructor='PurchaseCreditMemo') THEN", "                        UPDATE %%SCHEMA_NAME%%.purchase_credit_memo SET _id = base_id", "                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;", "", "                        documentType= 'purchaseCreditMemo';", "", "                    ELSIF (pRecord._constructor='PurchaseReceipt') THEN", "                        UPDATE %%SCHEMA_NAME%%.purchase_receipt SET _id = base_id", "                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;", "", "                        UPDATE %%SCHEMA_NAME%%.unbilled_account_payable_result_line SET receipt_internal_id = base_id", "                            WHERE receipt_internal_id = pRecord._id AND _tenant_id = pRecord._tenant_id;", "", "", "                        documentType= 'purchaseReceipt';", "", "                    ELSIF (pRecord._constructor='PurchaseReturn') THEN", "                        UPDATE %%SCHEMA_NAME%%.purchase_return SET _id = base_id", "                        WHERE _id = pRecord._id AND _tenant_id = pRecord._tenant_id;", "", "                        documentType= 'purchaseReturn';", "", "                    ELSE", "                        RAISE EXCEPTION 'Unknown constructor: %', pRecord._constructor;", "                    END IF;", "                    end_time := clock_timestamp();", "                    elapsed_time_purchase_doc_line_final := elapsed_time_purchase_doc_line_final + (extract(milliseconds from end_time - start_time));", "", "", "                    start_time := clock_timestamp();", "", "                        UPDATE %%SCHEMA_NAME%%.finance_transaction SET source_document_sys_id = base_id", "                        WHERE source_document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id", "                        AND source_document_type = documentType::%%SCHEMA_NAME%%.source_document_type_enum;", "", "                    interval_time := clock_timestamp();", "                    elapsed_time_finance_transaction_source := elapsed_time_finance_transaction_source + (extract(milliseconds from interval_time - start_time));", "", "                        UPDATE %%SCHEMA_NAME%%.finance_transaction_line SET source_document_sys_id = base_id", "                        WHERE source_document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id", "                        AND source_document_type = documentType::%%SCHEMA_NAME%%.source_document_type_enum;", "                    interval_time_end := clock_timestamp();", "                    elapsed_time_finance_transaction_line := elapsed_time_finance_transaction_line + (extract(milliseconds from interval_time_end - interval_time));", "", "", "                    IF (documentType<>'purchaseOrder') THEN", "                        interval_time := clock_timestamp();", "                            UPDATE %%SCHEMA_NAME%%.accounting_staging SET document_sys_id = base_id", "                            WHERE document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id", "                            AND document_type = documentType::%%SCHEMA_NAME%%.finance_document_type_enum;", "                        interval_time_end := clock_timestamp();", "                        elapsed_time_accounting_staging := elapsed_time_accounting_staging + (extract(milliseconds from interval_time_end - interval_time));", "", "                         interval_time := clock_timestamp();", "                            UPDATE %%SCHEMA_NAME%%.finance_transaction SET document_sys_id = base_id", "                            WHERE document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id", "                            AND document_type = documentType::%%SCHEMA_NAME%%.finance_document_type_enum;", "                        interval_time_end := clock_timestamp();", "                        elapsed_time_finance_transaction := elapsed_time_finance_transaction + (extract(milliseconds from interval_time_end - interval_time));", "", "                        interval_time := clock_timestamp();", "                            UPDATE %%SCHEMA_NAME%%.base_open_item SET document_sys_id = base_id", "                            WHERE document_sys_id = pRecord._id AND _tenant_id = pRecord._tenant_id", "                            AND document_type = documentType::%%SCHEMA_NAME%%.finance_document_type_enum;", "                        interval_time_end := clock_timestamp();", "                        elapsed_time_base_open_item := elapsed_time_base_open_item + (extract(milliseconds from interval_time_end - interval_time));", "", "                    END IF;", "                    end_time := clock_timestamp();", "                    elapsed_time_purchase_doc_finance := elapsed_time_purchase_doc_finance + (extract(milliseconds from end_time - start_time));", "", "                 END LOOP;", "", "                RAISE WARNING 'BasePurchaseDocument upgrade completed  ';", "                RAISE WARNING 'Elapsed time for document insert: %', elapsed_time_document_insert;", "                RAISE WARNING 'Elapsed time for purchase_doc update: %', elapsed_time_purchase_doc;", "                RAISE WARNING 'Elapsed time for purchase_doc_line update: %', elapsed_time_purchase_doc_line;", "                RAISE WARNING 'Elapsed time for purchase_doc_line_tax update: %', elapsed_time_purchase_doc_line_tax;", "                RAISE WARNING 'Elapsed time for purchase_doc_line_final update: %', elapsed_time_purchase_doc_line_final;", "                RAISE WARNING 'Elapsed time for doc_association update: %', elapsed_time_doc_association;", "", "                RAISE WARNING 'Elapsed time for purchase_doc_finance update: %', elapsed_time_purchase_doc_finance;", "                RAISE WARNING ' -- Elapsed time for finance_transaction_source update: %', elapsed_time_finance_transaction_source;", "                RAISE WARNING ' -- Elapsed time for finance_transaction_line update: %', elapsed_time_finance_transaction_line;", "                RAISE WARNING ' -- Elapsed time for accounting_staging update: %', elapsed_time_accounting_staging;", "                RAISE WARNING ' -- Elapsed time for finance_transaction update: %', elapsed_time_finance_transaction;", "                RAISE WARNING ' -- Elapsed time for base_open_item update: %', elapsed_time_base_open_item;", "            END $$;", "", "        "], "actionDescription": "Upgrade BasePurchaseDocuments to BaseDocument"}, {"isSysPool": false, "sql": ["", "                   INSERT INTO %%SCHEMA_NAME%%.base_document_item_line (_constructor, _id, _sort_value, _tenant_id,", "                        document, status, site, site_linked_address, item,", "                        item_description, stock_site, stock_site_linked_address, stock_unit,", "                        quantity_in_stock_unit, unit_to_stock_unit_conversion_factor,", "                        unit, quantity, internal_note, external_note, is_external_note, analytical_data , stored_dimensions, stored_attributes)", "", "                        SELECT line._constructor, line._id,line._sort_value, line._tenant_id, line.document,", "                            line.status::text::%%SCHEMA_NAME%%.base_status_enum,", "                            line.site,", "                            line.site_linked_address, line.item, line.item_description, line.stock_site, line.stock_site_linked_address,", "                            line.stock_unit, line.quantity_in_stock_unit, line.unit_to_stock_unit_conversion_factor, line.unit,", "                            line.quantity, line.internal_note, line.external_note, line.is_external_note,", "                            COALESCE( porderLine.analytical_data, returnLine.analytical_data, pcmLine.analytical_data, invoiceLine.analytical_data, receiptLine.analytical_data) as analytical_data,", "                            COALESCE( porderLine.stored_dimensions, returnLine.stored_dimensions, pcmLine.stored_dimensions, invoiceLine.stored_dimensions, receiptLine.stored_dimensions) as stored_dimensions,", "                            COALESCE( porderLine.stored_attributes, returnLine.stored_attributes, pcmLine.stored_attributes, invoiceLine.stored_attributes, receiptLine.stored_attributes) as stored_attributes", "                            FROM %%SCHEMA_NAME%%.base_purchase_document_line line", "                                left join %%SCHEMA_NAME%%.purchase_order_line as porderLine on porderLine._id = line._id", "                                    and porderLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseOrderLine'", "                                left join %%SCHEMA_NAME%%.purchase_return_line as returnLine on returnLine._id = line._id", "                                    and returnLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseReturnLine'", "                                left join %%SCHEMA_NAME%%.purchase_credit_memo_line  as pcmLine on pcmLine._id = line._id", "                                    and pcmLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseCreditMemoLine'", "                                left join %%SCHEMA_NAME%%.purchase_invoice_line as invoiceLine on invoiceLine._id = line._id", "                                    and invoiceLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseInvoiceLine'", "                                left join %%SCHEMA_NAME%%.purchase_receipt_line as receiptLine on receiptLine._id = line._id", "                                    and receiptLine._tenant_id = line._tenant_id and line._constructor = 'PurchaseReceiptLine'", "            "], "actionDescription": "Move some line properties from purchase to base document item line"}, {"isSysPool": false, "sql": ["", "            Do $$ DECLARE", "            srd RECORD;", "            begin", "            FOR srd IN (", "                SELECT sr._id, sr._tenant_id", "                FROM", "                    %%SCHEMA_NAME%%.stock_count sr)", "            loop", "                UPDATE %%SCHEMA_NAME%%.stock_count t0", "                SET stock_transaction_status=", "                ( CASE", "                    /* need to find out if any stock receipt lines have a stock transaction status of 'error' */", "                    WHEN ((select count (stock_transaction_status) from %%SCHEMA_NAME%%.stock_count_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'error') > 0)", "                        THEN 'error'::%%SCHEMA_NAME%%.stock_document_transaction_status_enum", "                    /* need to get find out if any stock receipt lines have a stock transaction status of 'inProgress' */", "                    WHEN ((select count (stock_transaction_status) from %%SCHEMA_NAME%%.stock_count_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'inProgress') > 0)", "                        THEN 'inProgress'::%%SCHEMA_NAME%%.stock_document_transaction_status_enum", "                    /* need to get find out if any stock receipt lines have a stock transaction status of 'draft' */", "                    WHEN ((select count (stock_transaction_status) from %%SCHEMA_NAME%%.stock_count_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'draft') > 0)", "                        THEN 'draft'::%%SCHEMA_NAME%%.stock_document_transaction_status_enum", "                    /* need to get find out if any stock receipt lines have a stock transaction status of 'completed' */", "                    WHEN ((select count (stock_transaction_status) from %%SCHEMA_NAME%%.stock_count_line srl", "                            where srd._id =srl.document and srd._tenant_id = srl._tenant_id and srl.stock_transaction_status = 'completed') > 0)", "                        THEN 'completed'::%%SCHEMA_NAME%%.stock_document_transaction_status_enum", "                    else 'draft'::%%SCHEMA_NAME%%.stock_document_transaction_status_enum", "                END)", "                WHERE t0._id = srd._id and t0._tenant_id = srd._tenant_id;", "            end loop;", "        end $$"], "actionDescription": "Set the value of StockCount.stockTransactionStatus"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _id DROP DEFAULT", "actionDescription": "BasePurchaseDocument: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "DROP SEQUENCE IF EXISTS %%SCHEMA_NAME%%.base_purchase_document__id_seq", "actionDescription": "BasePurchaseDocument: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN _id TYPE INT8;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_purchase_document._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;"], "actionDescription": "BasePurchaseDocument: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS audit_table ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS insert_table ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS update_table ON %%SCHEMA_NAME%%.base_purchase_document;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_document', '');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_document WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS audit_table ON %%SCHEMA_NAME%%.purchase_credit_memo;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_credit_memo", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_document', 'PurchaseCreditMemo');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS audit_table ON %%SCHEMA_NAME%%.purchase_invoice;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_invoice", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_document', 'PurchaseInvoice');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS audit_table ON %%SCHEMA_NAME%%.purchase_order;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_document', 'PurchaseOrder');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS audit_table ON %%SCHEMA_NAME%%.purchase_receipt;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_receipt", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_document', 'PurchaseReceipt');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS audit_table ON %%SCHEMA_NAME%%.purchase_return;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_return", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_document', 'PurchaseReturn');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_delete ON %%SCHEMA_NAME%%.base_purchase_document_line;", "DROP FUNCTION IF EXISTS %%SCHEMA_NAME%%.base_purchase_document_line_base_delete();"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_line_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_document_item_line WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_line_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_return_line DROP COLUMN IF EXISTS stored_dimensions, DROP COLUMN IF EXISTS stored_attributes, DROP COLUMN IF EXISTS analytical_data;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_receipt_line DROP COLUMN IF EXISTS stored_dimensions, DROP COLUMN IF EXISTS stored_attributes, DROP COLUMN IF EXISTS analytical_data;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order_line DROP COLUMN IF EXISTS stored_dimensions, DROP COLUMN IF EXISTS stored_attributes, DROP COLUMN IF EXISTS analytical_data;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_invoice_line DROP COLUMN IF EXISTS stored_dimensions, DROP COLUMN IF EXISTS stored_attributes, DROP COLUMN IF EXISTS analytical_data;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_credit_memo_line DROP COLUMN IF EXISTS stored_dimensions, DROP COLUMN IF EXISTS stored_attributes, DROP COLUMN IF EXISTS analytical_data;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line__id_fk;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ADD CONSTRAINT \"base_purchase_document_line__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_document_item_line(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_purchase_document_line__id_fk ON %%SCHEMA_NAME%%.base_purchase_document_line IS '{", "  \"targetTableName\": \"base_document_item_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP COLUMN IF EXISTS document, DROP COLUMN IF EXISTS status, DROP COLUMN IF EXISTS site, DROP COLUMN IF EXISTS site_linked_address, DROP COLUMN IF EXISTS internal_note, DROP COLUMN IF EXISTS external_note, DROP COLUMN IF EXISTS is_external_note, DROP COLUMN IF EXISTS item, DROP COLUMN IF EXISTS item_description, DROP COLUMN IF EXISTS stock_site, DROP COLUMN IF EXISTS stock_site_linked_address, DROP COLUMN IF EXISTS stock_unit, DROP COLUMN IF EXISTS unit, DROP COLUMN IF EXISTS unit_to_stock_unit_conversion_factor, DROP COLUMN IF EXISTS quantity, DROP COLUMN IF EXISTS quantity_in_stock_unit;"}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_line_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_document_item_line WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_line_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.stock_count ALTER COLUMN stock_transaction_status SET NOT NULL;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sage_network_company ADD CONSTRAINT \"sage_network_company__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sage_network_company__update_user_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sage_network_company ADD CONSTRAINT \"sage_network_company__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sage_network_company__create_user_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sage_network_company ADD CONSTRAINT \"sage_network_company_company_fk\" FOREIGN KEY(_tenant_id,company) REFERENCES %%SCHEMA_NAME%%.company(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sage_network_company_company_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sage_network_company ADD CONSTRAINT \"sage_network_company__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sage_network_company__tenant_id_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_return DROP COLUMN IF EXISTS approval_status;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order DROP COLUMN IF EXISTS approval_status, DROP COLUMN IF EXISTS is_sent, DROP COLUMN IF EXISTS is_printed;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ADD CONSTRAINT \"base_purchase_document__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_document(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_purchase_document__id_fk ON %%SCHEMA_NAME%%.base_purchase_document IS '{", "  \"targetTableName\": \"base_document\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP COLUMN IF EXISTS number, DROP COLUMN IF EXISTS status, DROP COLUMN IF EXISTS display_status, DROP COLUMN IF EXISTS site, DROP COLUMN IF EXISTS site_address, DROP COLUMN IF EXISTS business_entity_address, DROP COLUMN IF EXISTS stock_site, DROP COLUMN IF EXISTS currency, DROP COLUMN IF EXISTS text, DROP COLUMN IF EXISTS internal_note, DROP COLUMN IF EXISTS external_note, DROP COLUMN IF EXISTS is_external_note, DROP COLUMN IF EXISTS is_transfer_header_note, DROP COLUMN IF EXISTS is_transfer_line_note, DROP COLUMN IF EXISTS _create_user, DROP COLUMN IF EXISTS _update_user, DROP COLUMN IF EXISTS _create_stamp, DROP COLUMN IF EXISTS _update_stamp, DROP COLUMN IF EXISTS _update_tick, DROP COLUMN IF EXISTS _source_id;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP COLUMN IF EXISTS posting_date;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.employee DROP COLUMN IF EXISTS is_active, DROP COLUMN IF EXISTS resource;"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "experimental", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "experimental", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": ["INSERT INTO %%SCHEMA_NAME%%.sys_service_option", "(_update_tick,_source_id,package,option_name,description,status,is_hidden,is_active_by_default)", "VALUES ($1,$2,$3,$4,$5,$6,$7,$8)", "RETURNING _create_stamp,_update_stamp,_id"], "args": [1, "", "@sage/xtrem-reporting", "reportAssignment", "enable ReportAssignment feature", "workInProgress", false, false]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "workInProgress", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Open item page display", "released", true, "@sage/xtrem-structure", false, "openItemPageOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "workInProgress", false, "@sage/xtrem-master-data", false, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "released", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Payment tracking option", "workInProgress", false, "@sage/xtrem-finance-data", false, "paymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation option", "released", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "workInProgress", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "workInProgress", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Network", "workInProgress", false, "@sage/xtrem-sage-network", true, "sageNetworkOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00HKQH-79842\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "SysServiceOptionState"}}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.base_purchase_document IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_document\",", "  \"rootTable\": \"base_document\",", "  \"naturalKey\": [", "    \"_constructor\",", "    \"number\"", "  ]", "}';;COMMENT ON CONSTRAINT base_purchase_document__id_fk ON %%SCHEMA_NAME%%.base_purchase_document IS '{", "  \"targetTableName\": \"base_document\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.purchase_credit_memo IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_purchase_document\",", "  \"rootTable\": \"base_document\",", "  \"hasAttachments\": true,", "  \"naturalKey\": [", "    \"number\"", "  ]", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.purchase_invoice IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_purchase_document\",", "  \"rootTable\": \"base_document\",", "  \"hasAttachments\": true,", "  \"naturalKey\": [", "    \"number\"", "  ]", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.purchase_order IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_purchase_document\",", "  \"rootTable\": \"base_document\",", "  \"hasAttachments\": true,", "  \"naturalKey\": [", "    \"number\"", "  ]", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.purchase_receipt IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_purchase_document\",", "  \"rootTable\": \"base_document\",", "  \"hasAttachments\": true,", "  \"naturalKey\": [", "    \"number\"", "  ]", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.purchase_return IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_purchase_document\",", "  \"rootTable\": \"base_document\",", "  \"hasAttachments\": true,", "  \"naturalKey\": [", "    \"number\"", "  ]", "}';;COMMENT ON CONSTRAINT sage_network_company__update_user_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sage_network_company__create_user_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sage_network_company_company_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sage_network_company__tenant_id_fk ON %%SCHEMA_NAME%%.sage_network_company IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.base_purchase_document_line IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_document_item_line\",", "  \"rootTable\": \"base_document_line\",", "  \"naturalKey\": [", "    \"document\",", "    \"_sortValue\"", "  ]", "}';;COMMENT ON CONSTRAINT base_purchase_document_line__id_fk ON %%SCHEMA_NAME%%.base_purchase_document_line IS '{", "  \"targetTableName\": \"base_document_item_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';"]}], "data": {"SysServiceOptionState": {"metadata": {"rootFactoryName": "SysServiceOptionState", "name": "SysServiceOptionState", "naturalKeyColumns": ["_tenant_id", "service_option"], "columns": [{"name": "is_activable", "type": "boolean"}, {"name": "is_active", "type": "boolean"}, {"name": "service_option", "type": "reference", "targetFactoryName": "SysServiceOption"}]}, "rows": [["Y", null, "devTools"], ["Y", null, "isDemoTenant"], ["Y", null, "changelog"], ["Y", null, "notificationCenter"], ["Y", null, "authorizationServiceOption"], ["Y", null, "auditingOption"], ["Y", null, "auditing"], ["Y", null, "workflow"], ["Y", null, "workflowAdvanced"], ["N", null, "workflowOption"], ["Y", null, "reportAssignment"], ["Y", null, "frp1000ActivationOption"], ["Y", null, "intacctActivationOption"], ["Y", null, "openItemPageOption"], ["Y", null, "serialNumberOption"], ["Y", null, "orderToOrderOption"], ["Y", null, "landedCostOption"], ["Y", null, "fifoValuationMethodOption"], ["Y", null, "allocationTransferOption"], ["Y", null, "landedCostOrderOption"], ["Y", null, "datevOption"], ["Y", null, "phantomItemOption"], ["Y", null, "customer360ViewOption"], ["Y", null, "paymentTrackingOption"], ["Y", null, "apAutomationOption"], ["Y", null, "avalaraOption"], ["Y", "Y", "hrOption"], ["Y", "Y", "frp1000Option"], ["Y", "Y", "intacctOption"], ["Y", null, "intacctCashbookManagement"], ["Y", null, "synchronizationServiceOption"], ["Y", null, "sageNetworkOption"], ["Y", "Y", "serviceFabricTaxRepository"], ["Y", "Y", "serviceFabricTaxId"], ["Y", "Y", "serviceFabricOption"]]}}}