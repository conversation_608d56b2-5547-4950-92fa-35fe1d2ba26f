{"fromVersion": "55.0.34", "toVersion": "55.0.35", "gitHead": "6236ef6fb42dffb16abde692583f30b317a94c47", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        IF p_constructor != '' THEN", "            constructor := p_constructor;", "        ELSE", "            constructor := COALESCE(NEW._constructor, OLD._constructor);", "        END IF;", "", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentication feature", "released", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Tags feature", "released", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Auditing feature", "released", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Workflow feature", "released", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Open item page display", "released", true, "@sage/xtrem-structure", false, "openItemPageOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Bill of material revision", "workInProgress", false, "@sage/xtrem-master-data", false, "billOfMaterialRevisionServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "released", false, "@sage/xtrem-master-data", true, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "landedCostStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "released", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Payment tracking option", "released", false, "@sage/xtrem-finance-data", false, "paymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation option", "released", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Network", "workInProgress", false, "@sage/xtrem-sage-network", true, "sageNetworkOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00L8HZ-74886\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona, operator_id", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories WorkflowStepTemplate"}, {"action": "reload_setup_data", "args": {"factory": "WorkflowStepTemplate"}}], "data": {"WorkflowStepTemplate": {"metadata": {"rootFactoryName": "WorkflowStepTemplate", "name": "WorkflowStepTemplate", "naturalKeyColumns": ["_tenant_id", "step_constructor", "variant"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "step_constructor", "type": "string"}, {"name": "variant", "type": "string"}, {"name": "factory", "type": "reference", "isNullable": true, "targetFactoryName": "MetaNodeFactory"}, {"name": "is_active", "type": "boolean"}, {"name": "title", "type": "string", "isLocalized": true}, {"name": "description", "type": "string", "isLocalized": true}, {"name": "icon", "type": "enum", "enumMembers": ["addons", "bright", "clock", "print", "mail", "megaphone", "accounting", "binocular", "database", "pencil", "connected", "hourglass", "undo"]}, {"name": "color", "type": "string"}, {"name": "service_options", "type": "referenceArray", "targetFactoryName": "MetaServiceOption"}, {"name": "config_data", "type": "json"}]}, "rows": [["sage", "entity-created", "_generic", null, "Y", "{\"base\":\"<PERSON><PERSON><PERSON> created\"}", "{\"base\":\"This event is triggered when a new instance of the selected entity type is created in the system.\"}", "addons", "335b70ff", "[]", null], ["sage", "entity-deleted", "_generic", null, "Y", "{\"base\":\"<PERSON><PERSON><PERSON> deleted\"}", "{\"base\":\"This event is triggered when an instance of the selected entity type is deleted from the system.\"}", "undo", "000000ff", "[]", null], ["sage", "entity-updated", "_generic", null, "Y", "{\"base\":\"En<PERSON><PERSON> updated\"}", "{\"base\":\"This event is triggered when an instance of the selected entity type is updated in the system.\"}", "bright", "0060a7ff", "[]", null], ["sage", "test-started", "_generic", null, "Y", "{\"base\":\"Test scenario started\"}", "{\"base\":\"This event is triggered when a workflow test scenario starts.\"}", "binocular", "000000ff", "[]", null], ["sage", "condition", "_generic", null, "Y", "{\"base\":\"Condition\"}", "{\"base\":\"Allows the workflow logic to be branched based on a condition.\"}", "connected", "ef6700ff", "[]", null], ["sage", "calculate", "_generic", null, "Y", "{\"base\":\"Calculate\"}", "{\"base\":\"Calculates a value using simple mathematical operations using variable from the previous steps.\"}", "accounting", "335b70ff", "[]", null], ["sage", "delete-entity", "_generic", null, "Y", "{\"base\":\"Delete entity\"}", "{\"base\":\"Deletes an entity from the database.\"}", "undo", "335b70ff", "[]", null], ["sage", "read-entity", "_generic", null, "Y", "{\"base\":\"Read record\"}", "{\"base\":\"Reads a record by its ID field and use its value in subsequent steps.\"}", "binocular", "335b70ff", "[]", null], ["sage", "schedule", "_generic", null, "Y", "{\"base\":\"Schedule\"}", "{\"base\":\"This event is triggered on based on a predefined schedule.\"}", "clock", "0060a7ff", "[]", null], ["sage", "send-user-notification", "_generic", null, "Y", "{\"base\":\"Send notification\"}", "{\"base\":\"This action sends a notification to a set of users.\"}", "megaphone", "335b70ff", "[]", null], ["sage", "test-stub", "_generic", null, "Y", "{\"base\":\"Test stub\"}", "{\"base\":\"Ends the test scenario.\"}", "megaphone", "335b70ff", "[]", null], ["sage", "update-entity", "_generic", null, "Y", "{\"base\":\"Update entity\"}", "{\"base\":\"Updates a record in the database.\"}", "pencil", "335b70ff", "[]", null], ["sage", "wait", "_generic", null, "Y", "{\"base\":\"Wait\"}", "{\"base\":\"Waits for a predefined period before taking any further actions.\"}", "hourglass", "335b70ff", "[\"workflowAdvanced\"]", null], ["sage", "mutation", "_generic", null, "Y", "{\"base\":\"Mutation\"}", "{\"base\":\"Allows the workflow logic to execute a mutation.\"}", "connected", "ef6700ff", "[\"workflowAdvanced\"]", null], ["sage", "transfer-attachment", "_generic", null, "Y", "{\"base\":\"Share attachment\"}", "{\"base\":\"This action shares an attachement from one entity to another.\"}", "connected", "335b70ff", null, null], ["sage", "print-document", "_generic", null, "Y", "{\"base\":\"Print document\"}", "{\"base\":\"Generates a document using a predefined template.\"}", "print", "335b70ff", null, null], ["sage", "send-email", "_generic", null, "Y", "{\"base\":\"Send email\"}", "{\"base\":\"This action sends an email using a predefined email template.\"}", "mail", "335b70ff", null, null], ["sage", "mutation", "_sales_order_confirm", "SalesOrder", "Y", "{\"base\":\"Sales order: Confirm\"}", "{\"base\":\"Confirms a sales order.\"}", "connected", "335b70ff", null, "{\n    \"selector\": \"{_id status}\",\n    \"localizedTitle\": {\n        \"base\": \"Sales Order - confirm\",\n        \"en-US\": \"Sales Order - confirm\"\n    },\n    \"outputVariables\": [\n        {\n            \"node\": \"SalesOrder\",\n            \"path\": \"salesOrder._id\",\n            \"type\": \"reference\",\n            \"title\": \"salesOrder\"\n        }\n    ],\n    \"actionParameters\": [],\n    \"mutationArguments\": [\n        {\n            \"name\": \"salesOrder\",\n            \"node\": \"SalesOrder\",\n            \"type\": \"reference\",\n            \"value\": \"salesOrder._id\",\n            \"origin\": \"fromVariable\"\n        },\n        {\n            \"name\": \"isSafeToRetry\",\n            \"type\": \"boolean\",\n            \"value\": true,\n            \"origin\": \"manual\",\n            \"isMandatory\": false\n        }\n    ],\n    \"mutationNaturalKey\": \"SalesOrder|confirmSalesOrder\",\n    \"outputVariableName\": \"salesOrder\"\n}\n"], ["sage", "mutation", "_sales_order_auto_allocate", "SalesOrder", "Y", "{\"base\":\"Sales order: Allocate stock\"}", "{\"base\":\"Allocate sales order stock.\"}", "connected", "335b70ff", null, "{\n    \"selector\": \"{_id status allocationStatus}\",\n    \"localizedTitle\": {\n        \"base\": \"Sales Order - allocate\",\n        \"en-US\": \"Sales Order - allocate\"\n    },\n    \"outputVariables\": [\n        {\n            \"node\": \"SalesOrder\",\n            \"path\": \"salesOrder._id\",\n            \"type\": \"reference\",\n            \"title\": \"salesOrder\"\n        }\n    ],\n    \"actionParameters\": [],\n    \"mutationArguments\": [\n        {\n            \"name\": \"salesOrder\",\n            \"node\": \"SalesOrder\",\n            \"type\": \"reference\",\n            \"value\": \"salesOrder._id\",\n            \"origin\": \"fromVariable\"\n        },\n        {\n            \"name\": \"requestType\",\n            \"type\": \"enum\",\n            \"value\": \"allocation\",\n            \"origin\": \"manual\",\n            \"enumType\": \"@sage/xtrem-stock-data/AllocationRequestType\",\n            \"isMandatory\": true\n        },\n        {\n            \"name\": \"isSafeToRetry\",\n            \"type\": \"boolean\",\n            \"value\": true,\n            \"origin\": \"manual\",\n            \"isMandatory\": false\n        }\n    ],\n    \"mutationNaturalKey\": \"SalesOrder|autoAllocate|start\",\n    \"outputVariableName\": \"salesOrder\"\n}\n"], ["sage", "mutation", "_sales_shipment_confirm", "SalesShipment", "Y", "{\"base\":\"Sales shipment: Confirm\"}", "{\"en\":\"Confirms a sales shipment.\",\"base\":\"Confirms a sales shipment.\",\"en-US\":\"Confirms a sales shipment.\"}", "connected", "ef6700ff", null, "{\n    \"selector\": \"{ _id status}\",\n    \"stepVariables\": [\n        {\n            \"node\": \"SalesShipment\",\n            \"path\": \"salesShipment._id\",\n            \"type\": \"IntReference\",\n            \"title\": \"Sales shipment / 🆔\",\n            \"isCustom\": false\n        }\n    ],\n    \"localizedTitle\": {\n        \"base\": \"Sales shipment - confirm\",\n        \"en-US\": \"Sales shipment - confirm\"\n    },\n    \"outputVariables\": [\n        {\n            \"node\": \"SalesShipment\",\n            \"path\": \"salesShipment._id\",\n            \"type\": \"reference\",\n            \"title\": \"salesShipment\"\n        }\n    ],\n    \"actionParameters\": [],\n    \"mutationArguments\": [\n        {\n            \"name\": \"salesShipment\",\n            \"node\": \"SalesShipment\",\n            \"type\": \"reference\",\n            \"value\": \"salesShipment._id\",\n            \"origin\": \"fromVariable\"\n        },\n        {\n            \"name\": \"isSafeToRetry\",\n            \"type\": \"boolean\",\n            \"value\": true,\n            \"origin\": \"manual\",\n            \"isMandatory\": false\n        }\n    ],\n    \"mutationNaturalKey\": \"SalesShipment|confirm\",\n    \"outputVariableName\": \"salesShipment\"\n}\n"], ["sage", "mutation", "_sales_shipment_create_sales_invoice", "SalesShipment", "Y", "{\"base\":\"Sales shipment: Create sales invoice\"}", "{\"en\":\"Creates a sales invoice from a sales shipment.\",\"base\":\"Creates a sales invoice from a sales shipment.\",\"en-US\":\"Creates a sales invoice from a sales shipment.\"}", "connected", "ef6700ff", null, "{\n    \"selector\": \"{ _id number}\",\n    \"stepVariables\": [\n        {\n            \"node\": \"SalesShipment\",\n            \"path\": \"salesShipment._id\",\n            \"type\": \"IntReference\",\n            \"title\": \"Sales shipment / 🆔\",\n            \"isCustom\": false\n        }\n    ],\n    \"localizedTitle\": {\n        \"base\": \"Sales shipment - create sales invoice\",\n        \"en-US\": \"Sales shipment - create sales invoice\"\n    },\n    \"outputVariables\": [\n        {\n            \"path\": \"salesInvoices._id\",\n            \"type\": \"array\",\n            \"title\": \"salesInvoices\"\n        }\n    ],\n    \"actionParameters\": [],\n    \"mutationArguments\": [\n        {\n            \"name\": \"salesShipment\",\n            \"node\": \"SalesShipment\",\n            \"type\": \"reference\",\n            \"value\": \"salesShipment._id\",\n            \"origin\": \"fromVariable\",\n            \"isMandatory\": true\n        },\n        {\n            \"name\": \"isSafeToRetry\",\n            \"type\": \"boolean\",\n            \"value\": true,\n            \"origin\": \"manual\"\n        },\n        {\n            \"name\": \"invoiceDate\",\n            \"type\": \"date\",\n            \"origin\": \"fromParameter\"\n        }\n    ],\n    \"mutationNaturalKey\": \"SalesShipment|createSalesInvoicesFromShipment\",\n    \"outputVariableName\": \"salesInvoices\"\n}\n"], ["sage", "mutation", "_sales_order_create_shipments_from_order", "SalesOrder", "Y", "{\"base\":\"Sales order: Create shipments\"}", "{\"en\":\"Creates one or more sales shipments from a sales order.\",\"base\":\"Creates one or more sales shipments from a sales order.\",\"en-US\":\"Creates one or more sales shipments from a sales order.\"}", "connected", "ef6700ff", null, "{\n    \"selector\": \"{ _id status}\",\n    \"stepVariables\": [\n        {\n            \"node\": \"SalesOrder\",\n            \"path\": \"salesOrder._id\",\n            \"type\": \"IntReference\",\n            \"title\": \"Sales order / 🆔\",\n            \"isCustom\": false\n        }\n    ],\n    \"localizedTitle\": {\n        \"base\": \"Sales order - create shipments from order\",\n        \"en-US\": \"Sales order - create shipments from order\"\n    },\n    \"outputVariables\": [\n        {\n            \"path\": \"salesShipments._id\",\n            \"type\": \"array\",\n            \"title\": \"salesShipments\"\n        }\n    ],\n    \"actionParameters\": [],\n    \"mutationArguments\": [\n        {\n            \"name\": \"salesOrder\",\n            \"node\": \"SalesOrder\",\n            \"type\": \"reference\",\n            \"value\": \"salesOrder._id\",\n            \"origin\": \"fromVariable\",\n            \"isMandatory\": true\n        },\n        {\n            \"name\": \"isSafeToRetry\",\n            \"type\": \"boolean\",\n            \"value\": true,\n            \"origin\": \"manual\",\n            \"isMandatory\": false\n        }\n    ],\n    \"mutationNaturalKey\": \"SalesOrder|createShipmentsFromOrder\",\n    \"outputVariableName\": \"salesShipments\"\n}\n"]]}}}