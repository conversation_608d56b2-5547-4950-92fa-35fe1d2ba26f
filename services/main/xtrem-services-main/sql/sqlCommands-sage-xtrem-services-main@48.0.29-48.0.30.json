{"fromVersion": "48.0.29", "toVersion": "48.0.30", "gitHead": "35ed75c3f1075aebc79fab472513c27bcb1e08c1", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        is_audit_enabled BOOLEAN;", "        p_root_table_name VARCHAR;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        p_root_table_name := TG_ARGV[0];", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = COALESCE(NEW._id, OLD._id)", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_audit_log_ind0;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_process ADD COLUMN IF NOT EXISTS execution_user INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_process.execution_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.tax ADD COLUMN IF NOT EXISTS type %%SCHEMA_NAME%%.tax_type_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.tax.type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"tax_type_enum\"", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.tax AS t0 SET \"type\"=$1 WHERE ((t0.type IS NULL))", "args": ["purchasingAndSales"], "actionDescription": "Auto data action for property Tax.type"}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$NODE.Tax\\\"}\",\"containerId\":\"x3-devops00EOK7-58502\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": "DELETE FROM %%SCHEMA_NAME%%.workflow_process;", "actionDescription": "Resets the workflow_process table"}, {"isSysPool": false, "sql": ["UPDATE %%SCHEMA_NAME%%.tax", "\tSET type = CASE", "\t\tWHEN intacct_tax_type = 'Purchase' THEN 'purchasing' ::%%SCHEMA_NAME%%.tax_type_enum", "\t\tWHEN intacct_tax_type = 'Sale' THEN 'sales' ::%%SCHEMA_NAME%%.tax_type_enum", "\t\tWHEN intacct_tax_type = '' THEN 'purchasingAndSales' ::%%SCHEMA_NAME%%.tax_type_enum", "\t\tEND;"], "actionDescription": "Set the new property type for all existing taxes"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.tax DROP COLUMN IF EXISTS tax_type;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.tax ALTER COLUMN type SET NOT NULL;"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.workflow_process ADD CONSTRAINT \"workflow_process_execution_user_fk\" FOREIGN KEY(_tenant_id,execution_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT workflow_process_execution_user_fk ON %%SCHEMA_NAME%%.workflow_process IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"execution_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.workflow_process ALTER COLUMN execution_user SET NOT NULL;"}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_audit_log_ind0 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,record_id ASC,COALESCE(new_update_tick, (- ((2)::bigint ^ (62)::bigint))::bigint) ASC);"}, {"isSysPool": true, "sql": "UPDATE %%SCHEMA_NAME%%.sequence_number SET rtz_level = CASE rtz_level::TEXT WHEN 'fiscalYear' THEN 'noReset' WHEN 'period' THEN 'noReset' ELSE  rtz_level END WHERE rtz_level::TEXT IN ('fiscalYear','period');"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.sequence_number_reset_frequency_enum RENAME TO sequence_number_reset_frequency_enum__old"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='sequence_number_reset_frequency_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.sequence_number_reset_frequency_enum AS ENUM('noReset','yearly','monthly');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sequence_number ALTER COLUMN rtz_level TYPE %%SCHEMA_NAME%%.sequence_number_reset_frequency_enum USING rtz_level::TEXT::%%SCHEMA_NAME%%.sequence_number_reset_frequency_enum;"}, {"isSysPool": true, "sql": "DROP TYPE IF EXISTS %%SCHEMA_NAME%%.sequence_number_reset_frequency_enum__old  ;"}, {"action": "reload_setup_data", "args": {"factory": "Tax"}}, {"action": "reload_setup_data", "args": {"factory": "SysEnumTransformation"}}, {"action": "reload_setup_data", "args": {"factory": "SysEnumMapping"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT workflow_process_execution_user_fk ON %%SCHEMA_NAME%%.workflow_process IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"execution_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"Tax": {"metadata": {"rootFactoryName": "Tax", "name": "Tax", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "primary_external_reference", "type": "string"}, {"name": "secondary_external_reference", "type": "string"}, {"name": "tax_category", "type": "reference", "targetFactoryName": "TaxCategory"}, {"name": "country", "type": "reference", "targetFactoryName": "Country"}, {"name": "is_reverse_charge", "type": "boolean"}, {"name": "jurisdiction_name", "type": "string"}, {"name": "legal_mention", "type": "string", "isLocalized": true}, {"name": "type", "type": "enum", "enumMembers": ["purchasing", "sales", "purchasingAndSales"]}, {"name": "posting_class", "type": "reference", "isNullable": true, "targetFactoryName": "PostingClass"}, {"name": "posting_key", "type": "integer", "isNullable": true}, {"name": "is_intacct", "type": "boolean"}, {"name": "u_intacct_id", "type": "string"}, {"name": "record_no", "type": "integer", "isNullable": true}, {"name": "intacct_tax_type", "type": "string"}, {"name": "intacct_account", "type": "string"}, {"name": "is_intacct_reverse_charge", "type": "boolean"}, {"name": "rate", "type": "decimal", "isNullable": true}, {"name": "intacct_secondary_external_reference", "type": "reference", "isNullable": true, "targetFactoryName": "Tax"}, {"name": "intacct_solution_id", "type": "string"}, {"name": "service_fabric_id", "type": "string"}, {"name": "legislation", "type": "reference", "isNullable": true, "targetFactoryName": "Legislation"}]}, "rows": [["", "FR_TVA_NORMAL_DEDUCTIBLE_FA_INTRASTAT", "Y", "{\"en\":\"EU tax deductible on fixed assets, standard rate\",\"en-US\":\"EU tax deductible on fixed assets, standard rate\",\"fr-FR\":\"TVA à déduire sur immobilisations, taux normal\"}", "TVA déductible intracomm. taux normal - Acquisition d'immobilisations", "TVA due intracomm. taux normal - Acquisition d'immobilisations", "VAT", "FR", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_INTRASTAT", "Y", "{\"en\":\"EU tax deductible on purchases, standard rate\",\"en-US\":\"EU tax deductible on purchases, standard rate\",\"fr-FR\":\"TVA à déduire sur achats, taux normal\"}", "TVA déductible intracomm. taux normal", "TVA due intracomm. taux normal", "VAT", "FR", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, intermediate rate\",\"de-DE\":\"Vors<PERSON>uer, teilreduzier<PERSON> (Ist)\",\"en-US\":\"Tax deductible on payments, intermediate rate\",\"fr-FR\":\"Taux intermédiaire déductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, reduced rate\",\"de-DE\":\"<PERSON><PERSON><PERSON><PERSON>, erm<PERSON><PERSON><PERSON><PERSON> (Ist)\",\"en-US\":\"Tax deductible on payments, reduced rate\",\"fr-FR\":\"Taux réduit déductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, standard rate\",\"de-DE\":\"Vorsteuer, Regelsatz (Ist)\",\"en-US\":\"Tax deductible on payments, standard rate\",\"fr-FR\":\"Taux normal déductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, intermediate rate\",\"de-DE\":\"Vorsteuer, teilreduzierter Satz\",\"en-US\":\"Tax deductible on debits, intermediate rate\",\"fr-FR\":\"Taux intermédiaire déductible sur les débits\"}", "FR -BS TVA Déductible 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, reduced rate\",\"de-DE\":\" <PERSON><PERSON><PERSON>uer, ermäß<PERSON><PERSON>tz\",\"en-US\":\"Tax deductible on debits, reduced rate\",\"fr-FR\":\"Taux réduit déductible sur les débits\"}", "FR -BS TVA Déductible 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, standard rate\",\"de-DE\":\" Vorsteuer, Regelsatz\",\"en-US\":\"Tax deductible on debits, standard rate\",\"fr-FR\":\"Taux normal déductible sur les débits\"}", "FR -BS TVA Déductible 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_DEBITS", "Y", "{\"en\":\"Tax deductible on debits, exempt rate\",\"de-DE\":\"Vorsteuer, mehrwertsteuerfrei\",\"en-US\":\"Tax deductible on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération déductible sur les débits\"}", "FR -BS Exempté de TVA Déductible 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_DEBITS_INTRASTAT", "Y", "{\"en\":\"EU tax deductible on debits, exempt rate\",\"en-US\":\"EU tax deductible on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération déductible sur les débits DEB\"}", "Déductible débits exonéré de TVA intracommunautaire", "", "VAT", "FR", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, standard rate\",\"de-DE\":\"Vorsteuer Anlagen, Regelsatz\",\"en-US\":\"Tax deductible on fixed assets, standard rate\",\"fr-FR\":\"Taux normal déductible sur les immobilisations\"}", "FR -Immos TVA Déductible 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, intermediate rate\",\"de-DE\":\"Umsatzsteuer, teilreduzierter <PERSON> (Ist)\",\"en-US\":\"Tax collected on payments, intermediate rate\",\"fr-FR\":\"Taux intermédiaire collecté sur les encaissements\"}", "FR -TVA Enc -BS Collectée 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, reduced rate\",\"de-DE\":\"Umsatzsteuer, ermäßig<PERSON> (Ist)\",\"en-US\":\"Tax collected on payments, reduced rate\",\"fr-FR\":\"Taux réduit collecté sur les encaissements\"}", "FR -TVA Enc -BS Collectée 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, standard rate\",\"de-DE\":\"Umsatzsteuer, Regelsatz (Ist)\",\"en-US\":\"Tax collected on payments, standard rate\",\"fr-FR\":\"Taux normal collecté sur les encaissements\"}", "FR -TVA Enc -BS Collectée 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, intermediate rate\",\"de-DE\":\"UUmsatzsteuer, teilreduzierter Satz\",\"en-US\":\"Tax collected on debits, intermediate rate\",\"fr-FR\":\"Taux intermédiaire collecté sur les débits\"}", "FR -BS TVA Collectée 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, reduced rate\",\"de-DE\":\"Umsatzsteuer, ermäßigter Satz\",\"en-US\":\"Tax collected on debits, reduced rate\",\"fr-FR\":\"Taux réduit collecté sur les débits\"}", "FR -BS TVA Collectée 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_NORMAL_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, standard rate\",\"en-US\":\"Tax collected on debits, standard rate\",\"fr-FR\":\"Taux normal collecté sur les débits\"}", "FR -BS TVA Collectée 20%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_COLLECTED_ON_DEBITS", "Y", "{\"en\":\"Tax collected on debits, exempt rate\",\"en-US\":\"Tax collected on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération collecté sur les débits\"}", "FR -BS Exempté de TVA Collectée 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_COLLECTED_ON_DEBITS_INTRASTAT", "Y", "{\"en\":\"EU tax collected on debits, exempt rate\",\"en-US\":\"EU tax collected on debits, exempt rate\",\"fr-FR\":\"Taux d’exonération collecté sur les débits DEB\"}", "Collectée débits exonéré de TVA intracommunautaire", "", "VAT", "FR", "Y", null, "{\"en\":\"Tax exemption, article 262 ter-1 of the general tax code\",\"en-US\":\"Tax exemption, article 262 ter-1 of the general tax code\",\"fr-FR\":\"exonération de TVA, article 262 ter-1 du code général des impôts\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_INTERMEDIATE_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, intermediate rate\",\"base\":\"Tax deductible on fixed assets, intermediate rate\",\"en-US\":\"Tax deductible on fixed assets, intermediate rate\",\"fr-FR\":\"Taux intermédiaire déductible sur les immobilisations\"}", "FR -Immos TVA Déductible 10%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_REDUCED_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, reduced rate\",\"base\":\"Tax deductible on fixed assets, reduced rate\",\"en-US\":\"Tax deductible on fixed assets, reduced rate\",\"fr-FR\":\"Taux réduit déductible sur les immobilisations\"}", "FR -Immos TVA Déductible TVA 5,5%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_FA", "Y", "{\"en\":\"Tax deductible on fixed assets, exempt rate\",\"base\":\"Tax deductible on fixed assets, exempt rate\",\"en-US\":\"Tax deductible on fixed assets, exempt rate\",\"fr-FR\":\"Taux d'exonération déductible sur les immobilisations\"}", "FR -Immos TVA Déductible Exonéré 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_DEDUCTIBLE_ON_PAYMENT", "Y", "{\"en\":\"Tax deductible on payments, exempt rate\",\"base\":\"Tax deductible on payments, exempt rate\",\"en-US\":\"Tax deductible on payments, exempt rate\",\"fr-FR\":\"Taux d'exonérationdéductible sur les encaissements\"}", "FR -TVA Enc -BS TVA Déductible Exonéré 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "FR_TVA_EXEMPT_COLLECTED_ON_PAYMENT", "Y", "{\"en\":\"Tax collected on payments, exempt rate\",\"base\":\"Tax collected on payments, exempt rate\",\"en-US\":\"Tax collected on payments, exempt rate\",\"fr-FR\":\"Taux normal collectée sur les encaissements\"}", "FR -TVA Enc -BS Collectée Exonéré 0%", "", "VAT", "FR", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK Sale Goods Standard Rate\",\"en-US\":\"UK Sale Goods Standard Rate\",\"fr-FR\":\"UK ventes de biens, taux standard\"}", "UK Sale Goods Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK Sale Goods Reduced Rate\",\"en-US\":\"UK Sale Goods Reduced Rate\",\"fr-FR\":\"UK Ventes de biens, taux réduit\"}", "UK Sale Goods Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK Sale Goods Zero Rate\",\"en-US\":\"UK Sale Goods Zero Rate\",\"fr-FR\":\"UK Ventes de biens, taux zéro\"}", "UK Sale Goods Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK Sale Goods Exempt Rate\",\"en-US\":\"UK Sale Goods Exempt Rate\",\"fr-FR\":\"UK ventes de biens, taux d’exonération\"}", "UK Sale Goods Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK Sale Services Standard Rate\",\"en-US\":\"UK Sale Services Standard Rate\",\"fr-FR\":\"UK ventes de service, taux standard\"}", "UK Sale Services Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK Sale Services Reduced Rate\",\"en-US\":\"UK Sale Services Reduced Rate\",\"fr-FR\":\"UK Ventes de services, taux réduit\"}", "UK Sale Services Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK Sale Services Zero Rate\",\"en-US\":\"UK Sale Services Zero Rate\",\"fr-FR\":\"UK Ventes de services, taux zéro\"}", "UK Sale Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_HOME_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK Sale Services Exempt Rate\",\"en-US\":\"UK Sale Services Exempt Rate\",\"fr-FR\":\"UK ventes de services, taux d’exonération\"}", "UK Sale Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK General Export Standard Rate\",\"en-US\":\"UK General Export Standard Rate\",\"fr-FR\":\"UK Export de biens, taux normal\"}", "UK General Export Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK General Export Reduced Rate\",\"en-US\":\"UK General Export Reduced Rate\",\"fr-FR\":\"UK Export de biens, taux réduit\"}", "UK General Export Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK General Export Zero Rate\",\"en-US\":\"UK General Export Zero Rate\",\"fr-FR\":\"UK Export de biens, taux zéro\"}", "UK General Export Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK General Export Exempt Rate\",\"en-US\":\"UK General Export Exempt Rate\",\"fr-FR\":\"UK export, taux d’exonération\"}", "UK General Export Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK General Export Services Standard Rate\",\"en-US\":\"UK General Export Services Standard Rate\",\"fr-FR\":\"UK export de services, taux standard\"}", "UK General Export Services Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK General Export Services Reduced Rate\",\"en-US\":\"UK General Export Services Reduced Rate\",\"fr-FR\":\"UK Export de services, taux réduit\"}", "UK General Export Services Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK General Export Services Zero Rate\",\"en-US\":\"UK General Export Services Zero Rate\",\"fr-FR\":\"UK Export de services, taux zéro\"}", "UK General Export Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_ROW_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK General Export Services, Exempt Rate\",\"en-US\":\"UK General Export Services, Exempt Rate\",\"fr-FR\":\"UK export de services, taux d’exonération\"}", "UK General Export Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_SALES_NO_VAT_INPUT", "Y", "{\"en\":\"No VAT Output\",\"en-US\":\"No VAT Output\",\"fr-FR\":\"Ventes sans taux\"}", "No VAT Output", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK Purchase Goods Standard Rate\",\"en-US\":\"UK Purchase Goods Standard Rate\",\"fr-FR\":\"UK Achats de biens, taux standard\"}", "UK Purchase Goods Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK Purchase Goods Reduced Rate\",\"en-US\":\"UK Purchase Goods Reduced Rate\",\"fr-FR\":\"UK Achats de biens, taux réduit\"}", "UK Purchase Goods Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK Purchase Goods Zero Rate\",\"en-US\":\"UK Purchase Goods Zero Rate\",\"fr-FR\":\"UK Achats de biens, taux zéro\"}", "UK Purchase Goods Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK Purchase Goods Exempt Rate\",\"en-US\":\"UK Purchase Goods Exempt Rate\",\"fr-FR\":\"UK achats de biens, taux d’exonération\"}", "UK Purchase Goods Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK Purchase Services Standard Rate\",\"en-US\":\"UK Purchase Services Standard Rate\",\"fr-FR\":\"UK Achats de services, taux standard\"}", "UK Purchase Services Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK Purchase Services Reduced Rate\",\"en-US\":\"UK Purchase Services Reduced Rate\",\"fr-FR\":\"UK Achats de services, taux réduit\"}", "UK Purchase Services Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK Purchase Services Zero Rate\",\"en-US\":\"UK Purchase Services Zero Rate\",\"fr-FR\":\"UK Achats de services, taux zéro\"}", "UK Purchase Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_HOME_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK Purchase Services Exempt Rate\",\"en-US\":\"UK Purchase Services Exempt Rate\",\"fr-FR\":\"UK achats de services, taux d’exonération\"}", "UK Purchase Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_STANDARD_RATE", "Y", "{\"en\":\"UK Import Goods Standard Rate\",\"en-US\":\"UK Import Goods Standard Rate\",\"fr-FR\":\"UK Import de biens, taux standard\"}", "UK Import Goods Standard Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_REDUCED_RATE", "Y", "{\"en\":\"UK Import Goods Reduced Rate\",\"en-US\":\"UK Import Goods Reduced Rate\",\"fr-FR\":\"UK Import de biens, taux réduit\"}", "UK Import Goods Reduced Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_ZERO_RATE", "Y", "{\"en\":\"UK Import Goods Zero Rate\",\"en-US\":\"UK Import Goods Zero Rate\",\"fr-FR\":\"UK Import de biens, taux zéro\"}", "UK Import Goods Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_EXEMPT_RATE", "Y", "{\"en\":\"UK Import Goods Exempt Rate\",\"en-US\":\"UK Import Goods Exempt Rate\",\"fr-FR\":\"UK import de biens, taux d’exonération\"}", "UK Import Goods Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"UK Import Services Standard Rate\",\"en-US\":\"UK Import Services Standard Rate\",\"fr-FR\":\"UK Import de services, taux standard\"}", "UK Import Services Standard Rate", "UK Import Services Standard Rate Reverse Charge", "VAT", "GB", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_REDUCED_RATE", "Y", "{\"en\":\"UK Import Services Reduced Rate\",\"en-US\":\"UK Import Services Reduced Rate\",\"fr-FR\":\"UK Import de services, taux réduit\"}", "UK Import Services Reduced Rate", "UK Import Services Reduced Rate Reverse Charge", "VAT", "GB", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_ZERO_RATE", "Y", "{\"en\":\"UK Import Services Zero Rate\",\"en-US\":\"UK Import Services Zero Rate\",\"fr-FR\":\"UK Import de services, taux zéro\"}", "UK Import Services Zero Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_SERVICES_EXEMPT_RATE", "Y", "{\"en\":\"UK Import Services Exempt Rate\",\"en-US\":\"UK Import Services Exempt Rate\",\"fr-FR\":\"UK import de services, taux d’exonération\"}", "UK Import Services Exempt Rate", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_NO_VAT_INPUT", "Y", "{\"en\":\"No VAT Input\",\"en-US\":\"No VAT Input\",\"fr-FR\":\"Achats sans taux\"}", "No VAT Input", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_PVA_STANDARD_RATE", "Y", "{\"en\":\"UK PVA Import Goods Standard Rate Input\",\"en-US\":\"UK PVA Import Goods Standard Rate Input\",\"fr-FR\":\"UK Import de biens, taux standard reporté\"}", "UK PVA Import Goods Standard Rate Input", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "GB_VAT_PURCHASES_ROW_GOODS_PVA_REDUCED_RATE", "Y", "{\"en\":\"UK PVA Import Goods Reduced Rate Input\",\"en-US\":\"UK PVA Import Goods Reduced Rate Input\",\"fr-FR\":\"UK Import de biens, taux réduit reporté\"}", "UK PVA Import Goods Reduced Rate Input", "", "VAT", "GB", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_HOME_GOODS", "Y", "{\"en\":\"Standard Rate Output\",\"en-US\":\"Standard Rate Output\",\"fr-FR\":\"Ventes de biens\"}", "Standard Rate Output", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_HOME_CAPITAL_GOODS", "Y", "{\"en\":\"Standard Rate (Capital Goods) Output\",\"en-US\":\"Standard Rate (Capital Goods) Output\",\"fr-FR\":\"Ventes de biens d'équipement\"}", "Standard Rate (Capital Goods) Output", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_HOME_ZERO_RATE", "Y", "{\"en\":\"Zero Rate (Excluding Goods Exported)\",\"en-US\":\"Zero Rate (Excluding Goods Exported)\",\"fr-FR\":\"Ventes taux zéro\"}", "Zero Rate (Excluding Goods Exported)", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_EXEMPT_RATE", "Y", "{\"en\":\"Exempt and Non-Supplies\",\"en-US\":\"Exempt and Non-Supplies\",\"fr-FR\":\"Ventes taux exonéré\"}", "Exempt and Non-Supplies", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_SALES_EXPORT_ZERO_RATE", "Y", "{\"en\":\"Zero Rate (Only Goods Exported)\",\"en-US\":\"Zero Rate (Only Goods Exported)\",\"fr-FR\":\"Export de biens\"}", "Zero Rate (Only Goods Exported)", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_HOME_GOODS", "Y", "{\"en\":\"Standard Rate Input\",\"en-US\":\"Standard Rate Input\",\"fr-FR\":\"Achats de biens\"}", "Standard Rate Input", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_HOME_CAPITAL_GOODS", "Y", "{\"en\":\"Standard Rate (Capital Goods) Input\",\"en-US\":\"Standard Rate (Capital Goods) Input\",\"fr-FR\":\"Achats de biens d'équipement\"}", "Standard Rate (Capital Goods) Input", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_HOME_NO_INPUT", "Y", "{\"en\":\"No Input VAT\",\"en-US\":\"No Input VAT\",\"fr-FR\":\"Achats taux zéro\"}", "No Input VAT", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_IMPORT_GOODS", "Y", "{\"en\":\"Other Goods Imported (Not Capital Goods)\",\"en-US\":\"Other Goods Imported (Not Capital Goods)\",\"fr-FR\":\"Import de biens\"}", "Other Goods Imported (Not Capital Goods)", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_IMPORT_SERVICES", "Y", "{\"en\":\"Other Output Tax Adjustments\",\"en-US\":\"Other Output Tax Adjustments\",\"fr-FR\":\"Import de services\"}", "Other Output Tax Adjustments", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "ZA_VAT_PURCHASES_IMPORT_CAPITAL_GOODS", "Y", "{\"en\":\"Capital Goods Imported\",\"en-US\":\"Capital Goods Imported\",\"fr-FR\":\"Import de biens d'équipement\"}", "Capital Goods Imported", "", "VAT", "ZA", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_STANDARD_RATE", "Y", "{\"en\":\"Sale Standard Rate\",\"de-DE\":\"Umsatzsteuer Regelsatz\",\"en-US\":\"Sale Standard Rate\",\"fr-FR\":\"Ventes taux normal\"}", "Sale Standard Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_REDUCED_RATE", "Y", "{\"en\":\"Sale Reduced Rate\",\"de-DE\":\"Umsatzsteuer ermäßigter Satz\",\"en-US\":\"Sale Reduced Rate\",\"fr-FR\":\"Ventes taux réduit\"}", "Sale Reduced Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_ZERO_RATE", "Y", "{\"en\":\"Sale Zero Rate\",\"de-DE\":\"Umsatzsteuerfrei\",\"en-US\":\"Sale Zero Rate\",\"fr-FR\":\"Ventes taux zéro\"}", "Sale Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_HOME_MISC_RATE", "Y", "{\"en\":\"Sale Miscellaneous Rate\",\"de-DE\":\"Umsatzsteuer sonstiger Satz\",\"en-US\":\"Sale Miscellaneous Rate\",\"fr-FR\":\"Ventes taux divers\"}", "Sale Miscellaneous Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_STANDARD_RATE", "Y", "{\"en\":\"Purchase Standard Rate\",\"de-DE\":\"Vorsteuer Regelsatz\",\"en-US\":\"Purchase Standard Rate\",\"fr-FR\":\"Achats taux normal\"}", "Purchase Standard Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_REDUCED_RATE", "Y", "{\"en\":\"Purchase Reduced Rate\",\"de-DE\":\"Vorsteuer ermäßigter Satz\",\"en-US\":\"Purchase Reduced Rate\",\"fr-FR\":\"Achats taux réduit\"}", "Purchase Reduced Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_ZERO_RATE", "Y", "{\"en\":\"Purchase Zero Rate\",\"de-DE\":\"Vorsteuerfrei\",\"en-US\":\"Purchase Zero Rate\",\"fr-FR\":\"Achats taux zéro\"}", "Purchase Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_MISC_RATE", "Y", "{\"en\":\"Purchase Miscellaneous Rate\",\"de-DE\":\"Vorsteuer sonstiger Satz\",\"en-US\":\"Purchase Miscellaneous Rate\",\"fr-FR\":\"Achats taux divers\"}", "Purchase Miscellaneous Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_INTRASTAT_ZERO_RATE", "Y", "{\"en\":\"EU Sale Zero Rate\",\"de-DE\":\"Umsatzsteuer innergem. Lieferungen\",\"en-US\":\"EU Sale Zero Rate\",\"fr-FR\":\"Ventes CEE taux zéro\"}", "EU Sale Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_STANDARD_RATE", "Y", "{\"en\":\"EU Purchase Standard Rate\",\"de-DE\":\"Vorsteuer innergem. Erwerb Regelsatz\",\"en-US\":\"EU Purchase Standard Rate\",\"fr-FR\":\"Achats CEE taux normal\"}", "EU Purchase Standard Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_REDUCED_RATE", "Y", "{\"en\":\"EU Purchase Reduced Rate\",\"de-DE\":\"Vorsteuer innergem. Erwerb ermäßigter Satz\",\"en-US\":\"EU Purchase Reduced Rate\",\"fr-FR\":\"Achats CEE taux réduit\"}", "EU Purchase Reduced Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_ZERO_RATE", "Y", "{\"en\":\"EU Purchase Zero Rate\",\"de-DE\":\"Vorsteuer frei EU\",\"en-US\":\"EU Purchase Zero Rate\",\"fr-FR\":\"Achats CEE taux zéro\"}", "EU Purchase Zero Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_INTRASTAT_MISC_RATE", "Y", "{\"en\":\"EU Purchase Miscellaneous Rate\",\"de-DE\":\"Vorsteuer inergem. Erwerb sonstige Sätze\",\"en-US\":\"EU Purchase Miscellaneous Rate\",\"fr-FR\":\"Achats CEE taux divers\"}", "EU Purchase Miscellaneous Rate", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_SALES_ROW_ZERO_RATE", "Y", "{\"en\":\"Export Zero Rate\",\"de-DE\":\"Umsatzsteuer frei Drittland\",\"en-US\":\"Export Zero Rate\",\"fr-FR\":\"Export taux zéro\"}", "Export Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_ROW_ZERO_RATE", "Y", "{\"en\":\"Import Zero Rate\",\"de-DE\":\"Vorsteuer frei Drittland\",\"en-US\":\"Import Zero Rate\",\"fr-FR\":\"Import taux zéro\"}", "Import Zero Rate", "", "VAT", "DE", "N", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "DE_VAT_PURCHASES_HOME_13B_RATE", "Y", "{\"en\":\"Purchase Standard Rate section 13b\",\"de-DE\":\"Abz. Vorsteuer §13b UStG Regelsatz\",\"en-US\":\"Purchase reverse charge Standard Rate section 13b\",\"fr-FR\":\"Achats taux normal section 13b\"}", "Purchase reverse charge Standard Rate section 13b", "", "VAT", "DE", "Y", null, "{\"en\":\"\",\"en-US\":\"\",\"fr-FR\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_CAPITAL_STANDARD_RATE", "Y", "{\"en\":\"G15 Capital Purchases for Private Use\",\"base\":\"G15 Capital Purchases for Private Use\",\"en-US\":\"G15 Capital Purchases for Private Use\"}", "G15 Capital Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_CAPITAL_ZERO_RATE", "Y", "{\"en\":\"G15 GST Free Capital Purchases for Private Use\",\"base\":\"G15 GST Free Capital Purchases for Private Use\",\"en-US\":\"G15 GST Free Capital Purchases for Private Use\"}", "G15 GST Free Capital Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_GOODS_ZERO_RATE", "Y", "{\"en\":\"G15 GST Free Purchases for Private Use\",\"base\":\"G15 GST Free Purchases for Private Use\",\"en-US\":\"G15 GST Free Purchases for Private Use\"}", "G15 GST Free Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G15_STANDARD_RATE", "Y", "{\"en\":\"G15 Purchases for Private Use\",\"base\":\"G15 Purchases for Private Use\",\"en-US\":\"G15 Purchases for Private Use\"}", "G15 Purchases for Private Use", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_1F_LUXURY_CAR_ZERO_RATE", "Y", "{\"en\":\"1F Luxury Car Tax Refundable\",\"base\":\"1F Luxury Car Tax Refundable\",\"en-US\":\"1F Luxury Car Tax Refundable\"}", "1F Luxury Car Tax Refundable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G11_OTHER_STANDARD_RATE", "Y", "{\"en\":\"G11 Other Acquisition\",\"base\":\"G11 Other Acquisition\",\"en-US\":\"G11 Other Acquisition\"}", "G11 Other Acquisition", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G10_CAPITAL_STANDARD_RATE", "Y", "{\"en\":\"G10 Capital Acquisition\",\"base\":\"G10 Capital Acquisition\",\"en-US\":\"G10 Capital Acquisition\"}", "G10 Capital Acquisition", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_1F_WINE_ZERO_RATE", "Y", "{\"en\":\"1D Wine Equalisation Tax Refundable\",\"base\":\"1D Wine Equalisation Tax Refundable\",\"en-US\":\"1D Wine Equalisation Tax Refundable\"}", "1D Wine Equalisation Tax Refundable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G10_MOTOR_VEHICLE_STANDARD_RATE", "Y", "{\"en\":\"G10 Motor Vehicle Acquisition\",\"base\":\"G10 Motor Vehicle Acquisition\",\"en-US\":\"G10 Motor Vehicle Acquisition\"}", "G10 Motor Vehicle Acquisition", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_CAPITAL_STANDARD_RATE", "Y", "{\"en\":\"G13 Capital Purchases for Input Tax Sales\",\"base\":\"G13 Capital Purchases for Input Tax Sales\",\"en-US\":\"G13 Capital Purchases for Input Tax Sales\"}", "G13 Capital Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_CAPITAL_ZERO_RATE", "Y", "{\"en\":\"G13 GST Free Capital Purchases for Input Tax Sales\",\"base\":\"G13 GST Free Capital Purchases for Input Tax Sales\",\"en-US\":\"G13 GST Free Capital Purchases for Input Tax Sales\"}", "G13 GST Free Capital Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_ZERO_RATE", "Y", "{\"en\":\"G13 GST Free Purchases for Input Tax Sales\",\"base\":\"G13 GST Free Purchases for Input Tax Sales\",\"en-US\":\"G13 GST Free Purchases for Input Tax Sales\"}", "G13 GST Free Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G13_STANDARD_RATE", "Y", "{\"en\":\"G13 Purchases for Input Tax Sales\",\"base\":\"G13 Purchases for Input Tax Sales\",\"en-US\":\"G13 Purchases for Input Tax Sales\"}", "G13 Purchases for Input Tax Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G14_CAPITAL_ZERO_RATE", "Y", "{\"en\":\"G14 GST Free Capital Purchases\",\"base\":\"G14 GST Free Capital Purchases\",\"en-US\":\"G14 GST Free Capital Purchases\"}", "G14 GST Free Capital Purchases", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G14_ZERO_RATE", "Y", "{\"en\":\"G14 GST Free Non-Capital Purchases\",\"base\":\"G14 GST Free Non-Capital Purchases\",\"en-US\":\"G14 GST Free Non-Capital Purchases\"}", "G14 GST Free Non-Capital Purchases", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_G18_INPUT_TAX_ZERO_RATE", "Y", "{\"en\":\"G18 Input Tax Credit Adjustment\",\"base\":\"G18 Input Tax Credit Adjustment\",\"en-US\":\"G18 Input Tax Credit Adjustment\"}", "G18 Input Tax Credit Adjustment", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_PURCHASES_W4_WITHOLDING_TAX_ZERO_RATE", "Y", "{\"en\":\"W4 Withholding Tax\",\"base\":\"W4 Withholding Tax\",\"en-US\":\"W4 Withholding Tax\"}", "W4 Withholding Tax", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "purchasing", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_1C_WINE_ZERO_RATE", "Y", "{\"en\":\"1C Wine Equalisation Tax Payable\",\"base\":\"1C Wine Equalisation Tax Payable\",\"en-US\":\"1C Wine Equalisation Tax Payable\"}", "1C Wine Equalisation Tax Payable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_1E_LUXURY_CAR_ZERO_RATE", "Y", "{\"en\":\"1E Luxury Car Tax Payable\",\"base\":\"1E Luxury Car Tax Payable\",\"en-US\":\"1E Luxury Car Tax Payable\"}", "1E Luxury Car Tax Payable", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G1_GOODS_SERVICES_STANDARD_RATE", "Y", "{\"en\":\"G1 Goods and Services Tax\",\"base\":\"G1 Goods and Services Tax\",\"en-US\":\"G1 Goods and Services Tax\"}", "G1 Goods and Services Tax", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G1_GOODS_SERVICES_ZERO_RATE", "Y", "{\"en\":\"G1 Goods and Services Tax (GST Free)\",\"base\":\"G1 Goods and Services Tax (GST Free)\",\"en-US\":\"G1 Goods and Services Tax (GST Free)\"}", "G1 Goods and Services Tax (GST Free)", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G2_EXPORT_ZERO_RATE", "Y", "{\"en\":\"G2 Exports (GST Free)\",\"base\":\"G2 Exports (GST Free)\",\"en-US\":\"G2 Exports (GST Free)\"}", "G2 Exports (GST Free)", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G3_OTHER_ZERO_RATE", "Y", "{\"en\":\"G3 Other GST Free Sales\",\"base\":\"G3 Other GST Free Sales\",\"en-US\":\"G3 Other GST Free Sales\"}", "G3 Other GST Free Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G4_INPUT_TAX_ZERO_RATE", "Y", "{\"en\":\"G4 Input Taxed Sales\",\"base\":\"G4 Input Taxed Sales\",\"en-US\":\"G4 Input Taxed Sales\"}", "G4 Input Taxed Sales", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null], ["", "AU_GST_SALES_G7_GST_PAYABLE_TAX_ZERO_RATE", "Y", "{\"en\":\"G7 GST Payable Adjustment\",\"base\":\"G7 GST Payable Adjustment\",\"en-US\":\"G7 GST Payable Adjustment\"}", "G7 GST Payable Adjustment", "", "GST", "AU", "N", null, "{\"en\":\"\",\"base\":\"\",\"en-US\":\"\"}", "sales", null, null, null, null, null, null, null, null, null, null, null, null, null]]}, "SysEnumTransformation": {"metadata": {"rootFactoryName": "SysEnumTransformation", "name": "SysEnumTransformation", "naturalKeyColumns": ["_tenant_id", "local_enum", "remote_app", "remote_app_version", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "local_enum", "type": "reference", "targetFactoryName": "MetaDataType"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "remote_app", "type": "reference", "targetFactoryName": "SysApp"}, {"name": "remote_app_version", "type": "string"}, {"name": "remote_enum", "type": "string"}]}, "rows": []}, "SysEnumMapping": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SysEnumMapping", "name": "SysEnumMapping", "naturalKeyColumns": ["_tenant_id", "transform", "local_enum_value", "remote_enum_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "transform", "type": "reference", "targetFactoryName": "SysEnumTransformation"}, {"name": "local_enum_value", "type": "string"}, {"name": "remote_enum_value", "type": "string"}], "vitalParentColumn": {"name": "transform", "type": "reference", "targetFactoryName": "SysEnumTransformation"}}, "rows": []}}}