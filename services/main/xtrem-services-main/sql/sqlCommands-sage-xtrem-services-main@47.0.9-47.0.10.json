{"fromVersion": "47.0.9", "toVersion": "47.0.10", "gitHead": "1db48096209200fdda028d6afbbbc41514fa03fd", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN ", "    \tSELECT trigger_name, event_object_table ", "    \tFROM information_schema.triggers ", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN ", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN ", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", ""]}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_notify_created()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'BasePurchaseDocument/created',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"created\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_created", "            AFTER INSERT ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_notify_created();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_notify_updated()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'BasePurchaseDocument/updated',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"updated\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_updated", "            AFTER UPDATE ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_notify_updated();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_notify_deleted()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||OLD._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (OLD._tenant_id, origin_id, notification_id, '', '', email, login, '', 'BasePurchaseDocument/deleted',", "                        '{' || '\"_id\":' || to_json(OLD._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"deleted\\\"}\"}';", "                END IF;", "", "", "                RETURN OLD;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_deleted", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_notify_deleted();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_credit_memo_notify_created()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseCreditMemo/created',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"created\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_created", "            AFTER INSERT ON %%SCHEMA_NAME%%.purchase_credit_memo", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_credit_memo_notify_created();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_credit_memo_notify_updated()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseCreditMemo/updated',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"updated\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_updated", "            AFTER UPDATE ON %%SCHEMA_NAME%%.purchase_credit_memo", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_credit_memo_notify_updated();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_credit_memo_notify_deleted()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||OLD._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (OLD._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseCreditMemo/deleted',", "                        '{' || '\"_id\":' || to_json(OLD._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"deleted\\\"}\"}';", "                END IF;", "", "", "                RETURN OLD;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_deleted", "            AFTER DELETE ON %%SCHEMA_NAME%%.purchase_credit_memo", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_credit_memo_notify_deleted();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_invoice_notify_created()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseInvoice/created',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"created\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_created", "            AFTER INSERT ON %%SCHEMA_NAME%%.purchase_invoice", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_invoice_notify_created();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_invoice_notify_updated()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseInvoice/updated',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"updated\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_updated", "            AFTER UPDATE ON %%SCHEMA_NAME%%.purchase_invoice", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_invoice_notify_updated();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_invoice_notify_deleted()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||OLD._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (OLD._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseInvoice/deleted',", "                        '{' || '\"_id\":' || to_json(OLD._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"deleted\\\"}\"}';", "                END IF;", "", "", "                RETURN OLD;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_deleted", "            AFTER DELETE ON %%SCHEMA_NAME%%.purchase_invoice", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_invoice_notify_deleted();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_order_notify_created()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseOrder/created',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"created\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_created", "            AFTER INSERT ON %%SCHEMA_NAME%%.purchase_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_order_notify_created();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_order_notify_updated()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseOrder/updated',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"updated\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_updated", "            AFTER UPDATE ON %%SCHEMA_NAME%%.purchase_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_order_notify_updated();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_order_notify_deleted()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||OLD._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (OLD._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseOrder/deleted',", "                        '{' || '\"_id\":' || to_json(OLD._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"deleted\\\"}\"}';", "                END IF;", "", "", "                RETURN OLD;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_deleted", "            AFTER DELETE ON %%SCHEMA_NAME%%.purchase_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_order_notify_deleted();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_receipt_notify_created()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseReceipt/created',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"created\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_created", "            AFTER INSERT ON %%SCHEMA_NAME%%.purchase_receipt", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_receipt_notify_created();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_receipt_notify_updated()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseReceipt/updated',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"updated\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_updated", "            AFTER UPDATE ON %%SCHEMA_NAME%%.purchase_receipt", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_receipt_notify_updated();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_receipt_notify_deleted()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||OLD._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (OLD._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseReceipt/deleted',", "                        '{' || '\"_id\":' || to_json(OLD._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"deleted\\\"}\"}';", "                END IF;", "", "", "                RETURN OLD;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_deleted", "            AFTER DELETE ON %%SCHEMA_NAME%%.purchase_receipt", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_receipt_notify_deleted();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_return_notify_created()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseReturn/created',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"created\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_created", "            AFTER INSERT ON %%SCHEMA_NAME%%.purchase_return", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_return_notify_created();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_return_notify_updated()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||NEW._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (NEW._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseReturn/updated',", "                        '{' || '\"_id\":' || to_json(NEW._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"updated\\\"}\"}';", "                END IF;", "", "", "                RETURN NEW;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_updated", "            AFTER UPDATE ON %%SCHEMA_NAME%%.purchase_return", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_return_notify_updated();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.purchase_return_notify_deleted()", "        RETURNS TRIGGER", "        AS", "        $$", "            DECLARE", "                origin_id varchar;", "                notification_id varchar;", "                email varchar;", "                login varchar;", "                all_disabled varchar;", "                tenant_disabled varchar;", "            BEGIN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO all_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_'||OLD._tenant_id)", "                INTO tenant_disabled;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO email;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login;", "", "                IF (all_disabled <> 'true' and tenant_disabled <> 'true') THEN", "                    SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "                    INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                        (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale, topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                    VALUES (OLD._tenant_id, origin_id, notification_id, '', '', email, login, '', 'PurchaseReturn/deleted',", "                        '{' || '\"_id\":' || to_json(OLD._id) || '}', 'pending',", "                        '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "                    NOTIFY notification_queued, '{\"data\":\"{\\\"topic\\\":\\\"deleted\\\"}\"}';", "                END IF;", "", "", "                RETURN OLD;", "            END;", "        $$", "        LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER notify_deleted", "            AFTER DELETE ON %%SCHEMA_NAME%%.purchase_return", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.purchase_return_notify_deleted();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00D8AU-64052\",\"excludeSelf\":true}';", "args": []}], "data": {}}