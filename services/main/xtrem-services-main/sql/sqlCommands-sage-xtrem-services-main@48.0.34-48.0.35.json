{"fromVersion": "48.0.34", "toVersion": "48.0.35", "gitHead": "eaf53a8d6b3efa3dedf1e57e56fe8f56616a4b35", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled BOOLEAN;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO notify_all_disabled;", "            SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id) INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, '',", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_node_transformation_ind0;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_node_transformation_ind1;"}, {"isSysPool": true, "sql": ["", "                DO $$", "                    BEGIN", "                        IF NOT EXISTS (", "                        SELECT 1 FROM pg_type t", "                        LEFT JOIN pg_namespace p ON t.typnamespace=p.oid", "                        WHERE t.typname='stock_movement_type_enum' AND p.nspname='%%SCHEMA_NAME%%'", "                        ) THEN", "                            CREATE TYPE %%SCHEMA_NAME%%.stock_movement_type_enum AS ENUM('receipt','issue','change','adjustment','correction','valueChange','transfer');", "                        END IF;", "                    END", "                $$;", "                "]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_journal ADD COLUMN IF NOT EXISTS movement_type %%SCHEMA_NAME%%.stock_movement_type_enum;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_journal.movement_type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"stock_movement_type_enum\"", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.stock_journal AS t0 SET movement_type=$1 WHERE ((t0.movement_type IS NULL))", "args": ["receipt"], "actionDescription": "Auto data action for property StockJournal.movementType"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.stock_update_result_action_enum ADD VALUE IF NOT EXISTS 'transferred'   ;"}, {"isSysPool": false, "sql": ["", "            UPDATE %%SCHEMA_NAME%%.stock_journal sj", "                SET", "                movement_type=(", "                    CASE", "                    WHEN bsd._constructor = 'StockChangeDetail' AND bdl._constructor = 'StockTransferShipment' THEN 'transfer'::%%SCHEMA_NAME%%.stock_movement_type_enum", "                    WHEN bsd._constructor = 'StockReceiptDetail' THEN 'receipt'::%%SCHEMA_NAME%%.stock_movement_type_enum", "                    WHEN bsd._constructor = 'StockIssueDetail' THEN 'issue'::%%SCHEMA_NAME%%.stock_movement_type_enum", "                    WHEN bsd._constructor = 'StockChangeDetail' THEN 'change'::%%SCHEMA_NAME%%.stock_movement_type_enum", "                    WHEN bsd._constructor = 'StockAdjustmentDetail' THEN 'adjustment'::%%SCHEMA_NAME%%.stock_movement_type_enum", "                    WHEN bsd._constructor = 'StockValueDetail' THEN 'valueChange'::%%SCHEMA_NAME%%.stock_movement_type_enum", "                    ELSE 'correction'::%%SCHEMA_NAME%%.stock_movement_type_enum", "                    END", "                )", "                FROM %%SCHEMA_NAME%%.base_stock_detail bsd, %%SCHEMA_NAME%%.base_document_line bdl", "                WHERE bsd._tenant_id = sj._tenant_id", "                AND bsd._id = sj.stock_detail", "                AND bdl._tenant_id = sj._tenant_id", "                AND bdl._id = sj.document_line", "            "], "actionDescription": "Adds the movement type value to the stockJournal."}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.workflow_definition", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('workflow_definition', 'WorkflowDefinition');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_purchase_document', 'BasePurchaseDocument');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_credit_memo", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_purchase_document', 'PurchaseCreditMemo');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_invoice", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_purchase_document', 'PurchaseInvoice');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_purchase_document', 'PurchaseOrder');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_receipt", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_purchase_document', 'PurchaseReceipt');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.purchase_return", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_purchase_document', 'PurchaseReturn');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.sales_credit_memo", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('sales_credit_memo', 'SalesCreditMemo');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.sales_invoice", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('sales_invoice', 'SalesInvoice');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.sales_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('sales_order', 'SalesOrder');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.sales_return_receipt", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('sales_return_receipt', 'SalesReturnReceipt');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.sales_return_request", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('sales_return_request', 'SalesReturnRequest');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.sales_shipment", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('sales_shipment', 'SalesShipment');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.work_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('work_order', 'WorkOrder');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.stock_journal ALTER COLUMN movement_type SET NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sys_node_transformation DROP COLUMN IF EXISTS version;"}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_node_transformation_ind0 ON %%SCHEMA_NAME%%.sys_node_transformation(_tenant_id ASC,local_node ASC,remote_app ASC,remote_app_version ASC,id ASC);"}, {"action": "reload_setup_data", "args": {"factory": "SysNodeTransformation"}}, {"action": "reload_setup_data", "args": {"factory": "SysNodeMapping"}}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.sys_node_transformation IS '{", "  \"isSharedByAllTenants\": false,", "  \"isSetupNode\": true,", "  \"naturalKey\": [", "    \"localNode\",", "    \"remoteApp\",", "    \"remoteAppVersion\",", "    \"id\"", "  ]", "}';"]}], "data": {"SysNodeTransformation": {"metadata": {"rootFactoryName": "SysNodeTransformation", "name": "SysNodeTransformation", "naturalKeyColumns": ["_tenant_id", "local_node", "remote_app", "remote_app_version", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "remote_app", "type": "reference", "targetFactoryName": "SysApp"}, {"name": "remote_app_version", "type": "string"}, {"name": "remote_node_name", "type": "string"}, {"name": "local_node", "type": "reference", "targetFactoryName": "MetaNodeFactory"}, {"name": "local_node_full_name", "type": "string"}, {"name": "filter", "type": "json", "isNullable": true}, {"name": "last_error", "type": "date", "isNullable": true}, {"name": "last_sync", "type": "date", "isNullable": true}]}, "rows": []}, "SysNodeMapping": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SysNodeMapping", "name": "SysNodeMapping", "naturalKeyColumns": ["_tenant_id", "transform", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}, {"name": "local_property", "type": "string"}, {"name": "remote_property", "type": "string"}, {"name": "kind", "type": "enum", "enumMembers": ["path_to_path", "path_to_constant", "path_to_function", "constant_to_path", "function_to_path"]}], "vitalParentColumn": {"name": "transform", "type": "reference", "targetFactoryName": "SysNodeTransformation"}}, "rows": []}}}