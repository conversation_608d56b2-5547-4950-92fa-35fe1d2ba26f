{"fromVersion": "49.0.7", "toVersion": "49.0.8", "gitHead": "be85a1370ca00b10469be0cf19711928863b8061", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled BOOLEAN;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL') INTO notify_all_disabled;", "            SELECT %%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id) INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.origin_id') INTO origin_id;", "                SELECT %%SCHEMA_NAME%%.get_config('xtrem.user_email') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, '',", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.finance_document_type_enum ADD VALUE IF NOT EXISTS 'stockTransferReceipt'   ;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.accounts_receivable_open_item_ind0;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_open_item DROP CONSTRAINT IF EXISTS accounts_receivable_open_item_accounts_receivable_invoice_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_open_item DROP CONSTRAINT IF EXISTS accounts_receivable_open_item_accounts_receivable_invoice_fk;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_open_item ADD CONSTRAINT \"accounts_receivable_open_item_accounts_receivable_invoice_fk\" FOREIGN KEY(_tenant_id,accounts_receivable_invoice) REFERENCES %%SCHEMA_NAME%%.accounts_receivable_invoice(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT accounts_receivable_open_item_accounts_receivable_invoice_fk ON %%SCHEMA_NAME%%.accounts_receivable_open_item IS '{", "  \"targetTableName\": \"accounts_receivable_invoice\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"accounts_receivable_invoice\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_open_item DROP COLUMN IF EXISTS _sort_value;"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "workInProgress", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "workInProgress", false, "@sage/xtrem-master-data", false, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation Service Option", "workInProgress", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "workInProgress", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "workInProgress", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "experimental", false, "@sage/xtrem-supply-chain", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00F5RG-58832\",\"excludeSelf\":true}';", "args": []}, {"action": "reload_setup_data", "args": {"factory": "PostingClassDefinition"}}, {"action": "reload_setup_data", "args": {"factory": "JournalEntryType"}}, {"action": "reload_setup_data", "args": {"factory": "JournalEntryTypeLine"}}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.accounts_receivable_open_item IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_open_item\",", "  \"rootTable\": \"base_open_item\"", "}';;COMMENT ON CONSTRAINT accounts_receivable_open_item_accounts_receivable_invoice_fk ON %%SCHEMA_NAME%%.accounts_receivable_open_item IS '{", "  \"targetTableName\": \"accounts_receivable_invoice\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"accounts_receivable_invoice\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"PostingClassDefinition": {"metadata": {"rootFactoryName": "PostingClassDefinition", "name": "PostingClassDefinition", "naturalKeyColumns": ["_tenant_id", "legislation", "posting_class_type", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "legislation", "type": "reference", "targetFactoryName": "Legislation"}, {"name": "id", "type": "string"}, {"name": "posting_class_type", "type": "enum", "enumMembers": ["item", "supplier", "customer", "tax", "company", "header", "line", "resource"]}, {"name": "account_type_name", "type": "string", "isLocalized": true}, {"name": "is_detailed", "type": "boolean", "isOwnedByCustomer": true}, {"name": "is_stock_item_allowed", "type": "boolean"}, {"name": "is_non_stock_item_allowed", "type": "boolean"}, {"name": "is_service_item_allowed", "type": "boolean"}, {"name": "is_landed_cost_item_allowed", "type": "boolean"}, {"name": "can_have_additional_criteria", "type": "boolean"}, {"name": "additional_criteria", "type": "enum", "isNullable": true, "isOwnedByCustomer": true, "enumMembers": ["item", "supplier", "customer", "tax", "company", "header", "line", "resource"]}]}, "rows": [["sage", "FR", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "FR", "ArGsni", "customer", "{\"en\":\"Accounts receivable - GSNI\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable - GSNI\",\"fr-FR\":\"Collectif client FAE\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "Y", null], ["sage", "FR", "PurchaseExpense", "item", "{\"en\":\"Expense\",\"de-DE\":\"Aufwand\",\"en-US\":\"Expense\",\"fr-FR\":\"Charges - Achats\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "FR", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "FR", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "StockVariation", "item", "{\"en\":\"Purchased item stock variation\",\"de-DE\":\"Bestandsabweichung Einkaufsartikel\",\"en-US\":\"Purchased item stock variation\",\"fr-FR\":\"Variation de stock article acheté\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "StockVariationRevenue", "item", "{\"en\":\"Sold item stock variation\",\"de-DE\":\"Bestandsabweichung verkaufte Artikel\",\"en-US\":\"Sold item stock variation\",\"fr-FR\":\"Variation de stock article vendu\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "NonAbsorbedStock", "item", "{\"en\":\"Non absorbed stock\",\"de-DE\":\"Nicht absorbierter Bestand\",\"en-US\":\"Non absorbed stock\",\"fr-FR\":\"Stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "FR", "NonAbsorbedStockVariance", "item", "{\"en\":\"Non absorbed stock variance\",\"de-DE\":\"Nicht absorbierte Bestandsabweichung\",\"en-US\":\"Non absorbed stock variance\",\"fr-FR\":\"Variation de stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "FR", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "FR", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "FR", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "FR", "ApGrni", "supplier", "{\"en\":\"Accounts payable - GRNI\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable - GRNI\",\"fr-FR\":\"Collectif fournisseur FAR\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "ReverseChargeVat", "tax", "{\"en\":\"Reverse charge VAT\",\"de-DE\":\"Reverse Charge (Steuer EU-Erwerb/§13b)\",\"en-US\":\"Reverse charge VAT\",\"fr-FR\":\"Autoliquidation TVA sur achats\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "FR", "Vat", "tax", "{\"en\":\"VAT\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"VAT\",\"fr-FR\":\"TVA\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "FR", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "FR", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "US", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance US\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance US\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance US\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance US\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "US", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "US", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit des ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "US", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "US", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "US", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress\",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "US", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "US", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "US", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "US", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de la lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "US", "SalesTaxPayable", "tax", "{\"en\":\"Tax payable\",\"de-DE\":\"Vorsteuer\",\"en-US\":\"Tax payable\",\"fr-FR\":\"Taxe sur ventes\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "TaxReceivable", "tax", "{\"en\":\"Tax receivable\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"Tax receivable\",\"fr-FR\":\"Taxe sur achats\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "US", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "GB", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "GB", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "GB", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "GB", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "GB", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "GB", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "GB", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "GB", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "GB", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "GB", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "GB", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "GB", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "GB", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "GB", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "ZA", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "ZA", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "ZA", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "ZA", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "ZA", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "ZA", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "ZA", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "ZA", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "ZA", "SalesTaxPayable", "tax", "{\"en\":\"Tax payable\",\"de-DE\":\"Vorsteuer\",\"en-US\":\"Tax payable\",\"fr-FR\":\"Taxe sur ventes\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "TaxReceivable", "tax", "{\"en\":\"Tax receivable\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"Tax receivable\",\"fr-FR\":\"Taxe sur achats\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "ZA", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de la ligne de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Client\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "Y", null], ["sage", "DE", "PurchaseExpense", "item", "{\"en\":\"Expense\",\"de-DE\":\"Aufwand\",\"en-US\":\"Expense\",\"fr-FR\":\"Charge - Achats\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "DE", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "Y", null], ["sage", "DE", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "StockVariation", "item", "{\"en\":\"Purchased item stock variation\",\"de-DE\":\"Bestandsabweichung Einkaufsartikel\",\"en-US\":\"Purchased item stock variation\",\"fr-FR\":\"Variation de stock article acheté\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "StockVariationSales", "item", "{\"en\":\"Sold item stock variation\",\"de-DE\":\"Bestandsabweichung verkaufte Artikel\",\"en-US\":\"Sold item stock variation\",\"fr-FR\":\"Variation de stock article vendu\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Fertigungsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "DE", "NonAbsorbedStock", "item", "{\"en\":\"Non absorbed stock\",\"de-DE\":\"Nicht absorbierter Bestand\",\"en-US\":\"Non absorbed stock\",\"fr-FR\":\"Stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "NonAbsorbedStockVariance", "item", "{\"en\":\"Non absorbed stock variance\",\"de-DE\":\"Nicht absorbierte Bestandsabweichung\",\"en-US\":\"Non absorbed stock variance\",\"fr-FR\":\"Variation de stock non absorbé\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "DE", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Fournisseur\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "Vat", "tax", "{\"en\":\"VAT\",\"de-DE\":\"Umsatzsteuer\",\"en-US\":\"VAT\",\"fr-FR\":\"TVA\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "DE", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "DE", "ReverseChargeVat", "tax", "{\"en\":\"Reverse charge VAT\",\"de-DE\":\"Reverse Charge (Steuer EU-Erwerb/§13b)\",\"en-US\":\"Reverse charge VAT\",\"fr-FR\":\"Autoliquidation TVA sur achats\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "AU", "CreditorRoundingVariance", "company", "{\"en\":\"Creditor rounding variance\",\"de-DE\":\"Rundungsdifferenz Kreditoren\",\"en-US\":\"Creditor rounding variance\",\"fr-FR\":\"Gains de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "DebtorRoundingVariance", "company", "{\"en\":\"Debtor rounding variance\",\"de-DE\":\"Rundungsdifferenz Debitoren\",\"en-US\":\"Debtor rounding variance\",\"fr-FR\":\"Pertes de change en devise\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "Ar", "customer", "{\"en\":\"Accounts receivable\",\"de-DE\":\"Debitorenbuchhaltung\",\"en-US\":\"Accounts receivable\",\"fr-FR\":\"Collectif client\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "Ap", "supplier", "{\"en\":\"Accounts payable\",\"de-DE\":\"Kreditorenbuchhaltung\",\"en-US\":\"Accounts payable\",\"fr-FR\":\"Collectif fournisseur\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "Account", "header", "{\"en\":\"Document header account\",\"de-DE\":\"Konto Belegkopf\",\"en-US\":\"Document header account\",\"fr-FR\":\"Compte d'en-tête de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "AU", "Account", "line", "{\"en\":\"Document line account\",\"de-DE\":\"Konto Belegzeile\",\"en-US\":\"Document line account\",\"fr-FR\":\"Compte de lignes de document\"}", "Y", "N", "N", "N", "N", "N", null], ["sage", "AU", "CostOfGoods", "item", "{\"en\":\"Cost of goods\",\"de-DE\":\"Kosten der Warenabgabe\",\"en-US\":\"Cost of goods\",\"fr-FR\":\"Coût des marchandises\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "GoodsReceivedNotInvoiced", "item", "{\"en\":\"Goods received not invoiced\",\"de-DE\":\"Nicht fakturierter Wareneingang\",\"en-US\":\"Goods received not invoiced\",\"fr-FR\":\"Marchandises stockées à recevoir\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "LandedCostExpense", "item", "{\"en\":\"Landed cost expense\",\"de-DE\":\"Einstandskosten - Soll\",\"en-US\":\"Landed cost expense\",\"fr-FR\":\"Frais d'approche - Débit\"}", "Y", "N", "N", "N", "Y", "N", null], ["sage", "AU", "PayableNotInvoiced", "item", "{\"en\":\"Unbilled accounts payable: non-stock items\",\"de-DE\":\"Nicht fakturierte Verbindlichkeiten: nicht bestandsgeführte Artikel\",\"en-US\":\"Unbilled accounts payable: non-stock items\",\"fr-FR\":\"Articles non stockés à recevoir\"}", "N", "N", "Y", "Y", "N", "N", null], ["sage", "AU", "Overhead", "item", "{\"en\":\"Overhead\",\"de-DE\":\"Gemeinkosten\",\"en-US\":\"Overhead\",\"fr-FR\":\"Frais généraux\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "AU", "Pur<PERSON><PERSON><PERSON><PERSON>", "item", "{\"en\":\"Purchase variance\",\"de-DE\":\"Einkaufspreisdifferenz\",\"en-US\":\"Purchase variance\",\"fr-FR\":\"Ecart sur achat\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "AU", "SalesRevenue", "item", "{\"en\":\"Sales revenue\",\"de-DE\":\"Umsatzerlöse\",\"en-US\":\"Sales revenue\",\"fr-FR\":\"Produit - Ventes\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "AU", "SalesRevenueAccrual", "item", "{\"en\":\"Sales revenue accrual\",\"de-DE\":\"Abgrenzung der Umsatzerlöse\",\"en-US\":\"Sales revenue accrual\",\"fr-FR\":\"Produit à émettre\"}", "Y", "Y", "Y", "Y", "N", "N", null], ["sage", "AU", "ShippedNotInvoiced", "item", "{\"en\":\"Shipped not invoiced clearing\",\"de-DE\":\"Verrechnungskonto Warenausgang\",\"en-US\":\"Shipped not invoiced clearing\",\"fr-FR\":\"FAE\"}", "N", "Y", "Y", "Y", "N", "N", null], ["sage", "AU", "Stock", "item", "{\"en\":\"Stock\",\"de-DE\":\"Bestand\",\"en-US\":\"Stock\",\"fr-FR\":\"Stock\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "AU", "StockAdjustment", "item", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"en-US\":\"Stock adjustment\",\"fr-FR\":\"Régularisation de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "StockIssue", "item", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"en-US\":\"Stock issue\",\"fr-FR\":\"Sortie de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "StockReceipt", "item", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"en-US\":\"Stock receipt\",\"fr-FR\":\"Entrée de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "WorkInProgress", "item", "{\"en\":\"Work in progress\",\"de-DE\":\"Work-In-Progress\",\"en-US\":\"Work in progress \",\"fr-FR\":\"En cours\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "GoodsInTransit", "item", "{\"en\":\"Goods in transit\",\"de-DE\":\"Waren in Zustellung\",\"en-US\":\"Goods in transit\",\"fr-FR\":\"Marchandises en transit\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "Expense", "item", "{\"en\":\"Expense or asset purchase\",\"de-DE\":\"Aufwand oder Anlagenkauf\",\"en-US\":\"Expense or asset purchase\",\"fr-FR\":\"Charge ou achat d'immobilisation\"}", "Y", "N", "Y", "Y", "N", "N", null], ["sage", "AU", "ProductionVariance", "item", "{\"en\":\"Production variance\",\"de-DE\":\"Produktionsabweichung\",\"en-US\":\"Production variance\",\"fr-FR\":\"Ecart de production\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "LandedCostAccrual", "item", "{\"en\":\"Landed cost accrual\",\"de-DE\":\"Einstandskosten - Haben\",\"en-US\":\"Landed cost accrual\",\"fr-FR\":\"Frais d'approche - Crédit\"}", "Y", "Y", "N", "N", "N", "N", null], ["sage", "AU", "TransferStockInTransit", "item", "{\"en\":\"Stock transfer in transit\",\"de-DE\":\"Bestandstransfer in Transit\",\"en-US\":\"Stock transfer in transit\",\"fr-FR\":\"Transfert de stock\"}", "N", "Y", "N", "N", "N", "N", null], ["sage", "AU", "SetupProductionLabor", "resource", "{\"en\":\"Labor setup time\",\"de-DE\":\"Produktionslaufzeit Einrichtung\",\"en-US\":\"Labor setup time\",\"fr-FR\":\"Temps de réglage main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "RunTimeProductionLabor", "resource", "{\"en\":\"Labor run time\",\"de-DE\":\"Produktionslaufzeit Arbeitskosten\",\"en-US\":\"Labor run time\",\"fr-FR\":\"Temps de production main d'oeuvre\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "SetupProductionMachine", "resource", "{\"en\":\"Machine setup time\",\"de-DE\":\"Maschinenrüstzeit\",\"en-US\":\"Machine setup time\",\"fr-FR\":\"Temps de réglage machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "RunTimeProductionMachine", "resource", "{\"en\":\"Machine run time\",\"de-DE\":\"Maschinenlaufzeit\",\"en-US\":\"Machine run time\",\"fr-FR\":\"Temps de production machine\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "SetupProductionTool", "resource", "{\"en\":\"Tool setup time\",\"de-DE\":\"Werkzeugrüstzeit\",\"en-US\":\"Tool setup time\",\"fr-FR\":\"Temps de réglage outillage\"}", "N", "N", "N", "N", "N", "N", null], ["sage", "AU", "RunTimeProductionTool", "resource", "{\"en\":\"Tool run time\",\"de-DE\":\"Werkzeuglaufzeit\",\"en-US\":\"Tool run time\",\"fr-FR\":\"Temps de production outillage\"}", "N", "N", "N", "N", "N", "N", null]]}, "JournalEntryType": {"metadata": {"rootFactoryName": "JournalEntryType", "name": "JournalEntryType", "naturalKeyColumns": ["_tenant_id", "legislation", "document_type", "target_document_type"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "is_active", "type": "boolean", "isOwnedByCustomer": true}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "legislation", "type": "reference", "targetFactoryName": "Legislation"}, {"name": "document_type", "type": "enum", "enumMembers": ["miscellaneousStockReceipt", "miscellaneousStockIssue", "purchaseReceipt", "purchaseInvoice", "purchaseCreditMemo", "salesInvoice", "salesCreditMemo", "stockAdjustment", "salesShipment", "workInProgress", "apInvoice", "arInvoice", "purchaseReturn", "salesReturnReceipt", "bankReconciliationWithdrawal", "bankReconciliationDeposit", "stockCount", "stockValueChange", "stockTransferShipment", "stockTransferReceipt"]}, {"name": "target_document_type", "type": "enum", "enumMembers": ["journalEntry", "accountsReceivableInvoice", "accountsPayableInvoice", "accountsReceivableAdvance", "accountsReceivablePayment"]}, {"name": "immediate_posting", "type": "boolean", "isOwnedByCustomer": true}, {"name": "header_journal", "type": "reference", "isNullable": true, "isOwnedByCustomer": true, "targetFactoryName": "Journal"}, {"name": "header_posting_date", "type": "enum", "isNullable": true, "isOwnedByCustomer": true, "enumMembers": ["documentDate", "endOfMonth"]}, {"name": "header_description", "type": "enum", "isOwnedByCustomer": true, "enumMembers": ["documentType", "documentNumber", "transactionDescription"]}, {"name": "header_account_type", "type": "reference", "isNullable": true, "targetFactoryName": "PostingClassDefinition"}, {"name": "header_amount_type", "type": "enum", "isNullable": true, "enumMembers": ["amount", "varianceAmount", "amountIncludingTax", "deductibleTaxAmount", "nonDeductibleTaxAmount", "reverseChargeDeductibleTaxAmount", "reverseChargeNonDeductibleTaxAmount", "amountExcludingTax", "taxAmount", "adjustmentAmount", "adjustmentNonabsorbedAmount", "landedCostAdjustmentAmount", "landedCostAdjustmentNonabsorbedAmount", "landedCostStockInTransitAmount", "landedCostStockInTransitAdjustmentAmount", "landedCostStockInTransitAdjustmentNonabsorbedAmount", "inTransitAmount", "inTransitVarianceAmount"]}]}, "rows": [["sage", "Y", "{\"en\":\"AP invoice\",\"de-DE\":\"Eingangsrechnung\",\"fr-FR\":\"Facture comptable fournisseur\"}", "FR", "apInvoice", "journalEntry", "Y", "FR|ACH", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"AR invoice\",\"de-DE\":\"Ausgangsrechnung\",\"fr-FR\":\"Facture comptable client\"}", "FR", "arInvoice", "journalEntry", "Y", "FR|VEN", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "FR", "miscellaneousStockIssue", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "FR", "miscellaneousStockReceipt", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "FR", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "FR|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "FR", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "FR|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "FR", "purchaseInvoice", "journalEntry", "Y", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "FR", "purchaseReceipt", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "FR", "purchaseCreditMemo", "journalEntry", "Y", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "FR", "purchaseReturn", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir vente\"}", "FR", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "FR|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "FR", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "FR|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "FR", "salesReturnReceipt", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "FR", "salesShipment", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "FR", "stockAdjustment", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "FR", "stockCount", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "FR", "stockValueChange", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "FR", "stockTransferShipment", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer receipt\",\"de-DE\":\"TO BE TRANSLATED BY BERNADETTE\",\"fr-FR\":\"Réception de transfert de stock\"}", "FR", "stockTransferReceipt", "journalEntry", "N", "FR|OD", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "GB", "salesCreditMemo", "journalEntry", "Y", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "GB", "miscellaneousStockIssue", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "GB", "miscellaneousStockReceipt", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "GB", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "GB|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "GB", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "GB|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "GB", "purchaseInvoice", "journalEntry", "Y", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "GB", "purchaseReceipt", "journalEntry", "N", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "GB", "purchaseCreditMemo", "journalEntry", "Y", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "GB", "purchaseReturn", "journalEntry", "N", "GB|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "GB", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "GB|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "GB", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "GB|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "GB", "salesInvoice", "journalEntry", "Y", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "GB", "salesReturnReceipt", "journalEntry", "N", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "GB", "salesShipment", "journalEntry", "N", "GB|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "GB", "stockAdjustment", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "GB", "workInProgress", "journalEntry", "N", "GB|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "GB", "stockCount", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "GB", "stockValueChange", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "GB", "stockTransferShipment", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer receipt\",\"de-DE\":\"TO BE TRANSLATED BY BERNADETTE\",\"fr-FR\":\"Réception de transfert de stock\"}", "GB", "stockTransferReceipt", "journalEntry", "N", "GB|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "US", "salesCreditMemo", "journalEntry", "Y", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "US", "miscellaneousStockIssue", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "US", "miscellaneousStockReceipt", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "US", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "US|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "US", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "US|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "US", "purchaseInvoice", "journalEntry", "Y", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "US", "purchaseReceipt", "journalEntry", "N", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "US", "purchaseCreditMemo", "journalEntry", "Y", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "US", "purchaseReturn", "journalEntry", "N", "US|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "US", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "US|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "US", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "US|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "US", "salesInvoice", "journalEntry", "Y", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "US", "salesReturnReceipt", "journalEntry", "N", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "US", "salesShipment", "journalEntry", "N", "US|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "US", "stockAdjustment", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "US", "workInProgress", "journalEntry", "N", "US|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "US", "stockCount", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "US", "stockValueChange", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "US", "stockTransferShipment", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer receipt\",\"de-DE\":\"TO BE TRANSLATED BY BERNADETTE\",\"fr-FR\":\"Réception de transfert de stock\"}", "US", "stockTransferReceipt", "journalEntry", "N", "US|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Bank reconciliation deposit\",\"de-DE\":\"Bankabstimmung Einzahlungen\",\"fr-FR\":\"Sortie de compte de rapprochement bancaire\"}", "ZA", "bankReconciliationDeposit", "journalEntry", "Y", "ZA|CRJ", "documentDate", "transactionDescription", "", ""], ["sage", "Y", "{\"en\":\"Bank reconciliation withdrawal\",\"de-DE\":\"Bankabstimmung Auszahlungen\",\"fr-FR\":\"Dépôt sur compte de rapprochement bancaire \"}", "ZA", "bankReconciliationWithdrawal", "journalEntry", "Y", "ZA|CDJ", "documentDate", "transactionDescription", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "ZA", "salesCreditMemo", "journalEntry", "Y", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "ZA", "miscellaneousStockIssue", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "ZA", "miscellaneousStockReceipt", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "ZA", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "ZA|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "ZA", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "ZA|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "ZA", "purchaseInvoice", "journalEntry", "Y", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "ZA", "purchaseReceipt", "journalEntry", "N", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "ZA", "purchaseCreditMemo", "journalEntry", "Y", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "ZA", "purchaseReturn", "journalEntry", "N", "ZA|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "ZA", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "ZA|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "ZA", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "ZA|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "ZA", "salesInvoice", "journalEntry", "Y", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "ZA", "salesReturnReceipt", "journalEntry", "N", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "ZA", "salesShipment", "journalEntry", "N", "ZA|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "ZA", "stockAdjustment", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "ZA", "workInProgress", "journalEntry", "N", "ZA|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "ZA", "stockCount", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "ZA", "stockValueChange", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "ZA", "stockTransferShipment", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer receipt\",\"de-DE\":\"TO BE TRANSLATED BY BERNADETTE\",\"fr-FR\":\"Réceptionde transfert de stock\"}", "ZA", "stockTransferReceipt", "journalEntry", "N", "ZA|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "DE", "miscellaneousStockReceipt", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "DE", "miscellaneousStockIssue", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "DE", "stockAdjustment", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "DE", "stockCount", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "DE", "purchaseReceipt", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour achat\"}", "DE", "purchaseReturn", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "DE", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "DE|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "DE", "purchaseInvoice", "journalEntry", "Y", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir achat\"}", "DE", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "DE|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "DE", "salesShipment", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "DE", "salesReturnReceipt", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir vente\"}", "DE", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "DE|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "DE", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "DE|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"AP invoice\",\"de-DE\":\"Eingangsrechnung\",\"fr-FR\":\"Facture fournisseur\"}", "DE", "apInvoice", "journalEntry", "Y", "DE|EREC", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"AR invoice\",\"de-DE\":\"Ausgangsrechnung\",\"fr-FR\":\"Facture client\"}", "DE", "arInvoice", "journalEntry", "Y", "DE|VREC", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"En cours de production\"}", "DE", "workInProgress", "journalEntry", "N", "DE|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "DE", "purchaseCreditMemo", "journalEntry", "Y", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "DE", "stockValueChange", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "DE", "stockTransferShipment", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer receipt\",\"de-DE\":\"TO BE TRANSLATED BY BERNADETTE\",\"fr-FR\":\"Réception de transfert de stock\"}", "DE", "stockTransferReceipt", "journalEntry", "N", "DE|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales revenue accrual on credit memo\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Gutschrift\",\"fr-FR\":\"Produit à émettre sur avoir de vente\"}", "AU", "salesCreditMemo", "journalEntry", "Y", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock issue\",\"de-DE\":\"Bestandsabgang\",\"fr-FR\":\"Sortie de stock\"}", "AU", "miscellaneousStockIssue", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock receipt\",\"de-DE\":\"Bestandseingang\",\"fr-FR\":\"Entrée de stock\"}", "AU", "miscellaneousStockReceipt", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo\",\"de-DE\":\"Einkaufsgutschrift\",\"fr-FR\":\"Avoir d'achat\"}", "AU", "purchaseCreditMemo", "accountsPayableInvoice", "Y", "", "", "documentType", "AU|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice\",\"de-DE\":\"Einkaufsrechnung\",\"fr-FR\":\"Facture d'achat\"}", "AU", "purchaseInvoice", "accountsPayableInvoice", "Y", "", "", "documentType", "AU|supplier|Ap", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Purchase invoice stock variance\",\"de-DE\":\"Einkaufsrechnung Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur facture d'achat\"}", "AU", "purchaseInvoice", "journalEntry", "Y", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase receipt\",\"de-DE\":\"Wareneingang\",\"fr-FR\":\"Ré<PERSON> d'achat\"}", "AU", "purchaseReceipt", "journalEntry", "N", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase credit memo stock variance\",\"de-DE\":\"Einkaufsgutschrift Bestandsdifferenz\",\"fr-FR\":\"Régularisation de stock sur avoir d'achat\"}", "AU", "purchaseCreditMemo", "journalEntry", "Y", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Purchase return\",\"de-DE\":\"Einkaufsretoure\",\"fr-FR\":\"Retour d'achat\"}", "AU", "purchaseReturn", "journalEntry", "N", "AU|UNB", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales credit memo\",\"de-DE\":\"Verkaufsgutschrift\",\"fr-FR\":\"Avoir de vente\"}", "AU", "salesCreditMemo", "accountsReceivableInvoice", "Y", "", "", "documentType", "AU|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales invoice\",\"de-DE\":\"Verkaufsrechnung\",\"fr-FR\":\"Facture de vente\"}", "AU", "salesInvoice", "accountsReceivableInvoice", "Y", "", "", "documentType", "AU|customer|Ar", "amountIncludingTax"], ["sage", "Y", "{\"en\":\"Sales revenue accrual on sales invoice\",\"de-DE\":\"Auflösung Umsatzrealisierung bei Rechnung\",\"fr-FR\":\"Extourne de produit à émettre sur facture de vente\"}", "AU", "salesInvoice", "journalEntry", "Y", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales return receipt\",\"de-DE\":\"Verkaufsretoureneingang\",\"fr-FR\":\"Réception de retour de vente\"}", "AU", "salesReturnReceipt", "journalEntry", "N", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Sales shipment\",\"de-DE\":\"Warenausgang\",\"fr-FR\":\"Expédition de vente\"}", "AU", "salesShipment", "journalEntry", "N", "AU|SJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock adjustment\",\"de-DE\":\"Bestandskorrektur\",\"fr-FR\":\"Régularisation de stock\"}", "AU", "stockAdjustment", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Work in progress cost\",\"de-DE\":\"Work-In-Progress-Kosten\",\"fr-FR\":\"Coûts en-cours\"}", "AU", "workInProgress", "journalEntry", "N", "AU|WIP", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock count adjustment\",\"de-DE\":\"Korrektur Inventurdifferenz\",\"fr-FR\":\"Régularisation d'inventaire\"}", "AU", "stockCount", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock value change\",\"de-DE\":\"Änderung Bestandswert\",\"fr-FR\":\"Changement de la valeur du stock\"}", "AU", "stockValueChange", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer shipment\",\"de-DE\":\"Bestandstransferversand\",\"fr-FR\":\"Expédition de transfert de stock\"}", "AU", "stockTransferShipment", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""], ["sage", "Y", "{\"en\":\"Stock transfer receipt\",\"de-DE\":\"TO BE TRANSLATED BY BERNADETTE\",\"fr-FR\":\"Réception de transfert de stock\"}", "AU", "stockTransferReceipt", "journalEntry", "N", "AU|IJ", "documentDate", "documentType", "", ""]]}, "JournalEntryTypeLine": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "JournalEntryTypeLine", "name": "JournalEntryTypeLine", "naturalKeyColumns": ["_tenant_id", "journal_entry_type", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "journal_entry_type", "type": "reference", "targetFactoryName": "JournalEntryType"}, {"name": "movement_type", "type": "enum", "enumMembers": ["stockJournal", "document", "productionTracking", "materialTracking", "laborSetupTimeTracking", "laborRunTimeTracking", "machineSetupTimeTracking", "machineRunTimeTracking", "toolSetupTimeTracking", "toolRunTimeTracking", "workOrder<PERSON><PERSON>ce", "workOrderNegativeV<PERSON>ce", "workOrderActualCostAdjustment", "workOrderNegativeActualCostAdjustment", "workOrderActualCostAdjustmentNonAbsorbed", "workOrderNegativeActualCostAdjustmentNonAbsorbed"]}, {"name": "account_type", "type": "reference", "targetFactoryName": "PostingClassDefinition"}, {"name": "amount_type", "type": "enum", "enumMembers": ["amount", "varianceAmount", "amountIncludingTax", "deductibleTaxAmount", "nonDeductibleTaxAmount", "reverseChargeDeductibleTaxAmount", "reverseChargeNonDeductibleTaxAmount", "amountExcludingTax", "taxAmount", "adjustmentAmount", "adjustmentNonabsorbedAmount", "landedCostAdjustmentAmount", "landedCostAdjustmentNonabsorbedAmount", "landedCostStockInTransitAmount", "landedCostStockInTransitAdjustmentAmount", "landedCostStockInTransitAdjustmentNonabsorbedAmount", "inTransitAmount", "inTransitVarianceAmount"]}, {"name": "sign", "type": "enum", "isNullable": true, "enumMembers": ["D", "C"]}, {"name": "common_reference", "type": "enum", "isOwnedByCustomer": true, "enumMembers": ["documentNumber", "sourceDocumentNumber"]}, {"name": "is_stock_item_allowed", "type": "boolean"}, {"name": "is_non_stock_item_allowed", "type": "boolean"}, {"name": "is_service_item_allowed", "type": "boolean"}, {"name": "is_landed_cost_item_allowed", "type": "boolean"}, {"name": "contra_journal_entry_type_line", "type": "reference", "isNullable": true, "targetFactoryName": "JournalEntryTypeLine"}], "vitalParentColumn": {"name": "journal_entry_type", "type": "reference", "targetFactoryName": "JournalEntryType"}}, "rows": [["sage", "100", "FR|apInvoice|journalEntry", "document", "FR|header|Account", "amountIncludingTax", "C", "sourceDocumentNumber", "N", "N", "N", "N", "FR|apInvoice|journalEntry|200"], ["sage", "200", "FR|apInvoice|journalEntry", "document", "FR|line|Account", "amountExcludingTax", "D", "sourceDocumentNumber", "N", "N", "N", "N", "FR|apInvoice|journalEntry|100"], ["sage", "300", "FR|apInvoice|journalEntry", "document", "FR|tax|Vat", "nonDeductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "FR|apInvoice|journalEntry|100"], ["sage", "400", "FR|apInvoice|journalEntry", "document", "FR|tax|Vat", "deductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "FR|apInvoice|journalEntry|100"], ["sage", "500", "FR|apInvoice|journalEntry", "document", "FR|tax|ReverseChargeVat", "reverseChargeNonDeductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "FR|apInvoice|journalEntry|100"], ["sage", "600", "FR|apInvoice|journalEntry", "document", "FR|tax|ReverseChargeVat", "reverseChargeDeductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "FR|apInvoice|journalEntry|100"], ["sage", "100", "FR|arInvoice|journalEntry", "document", "FR|header|Account", "amountIncludingTax", "D", "sourceDocumentNumber", "N", "N", "N", "N", "FR|arInvoice|journalEntry|200"], ["sage", "200", "FR|arInvoice|journalEntry", "document", "FR|line|Account", "amountExcludingTax", "C", "sourceDocumentNumber", "N", "N", "N", "N", "FR|arInvoice|journalEntry|100"], ["sage", "300", "FR|arInvoice|journalEntry", "document", "FR|tax|Vat", "nonDeductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "FR|arInvoice|journalEntry|100"], ["sage", "400", "FR|arInvoice|journalEntry", "document", "FR|tax|Vat", "deductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "FR|arInvoice|journalEntry|100"], ["sage", "500", "FR|arInvoice|journalEntry", "document", "FR|tax|ReverseChargeVat", "reverseChargeNonDeductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "FR|arInvoice|journalEntry|100"], ["sage", "600", "FR|arInvoice|journalEntry", "document", "FR|tax|ReverseChargeVat", "reverseChargeDeductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "FR|arInvoice|journalEntry|100"], ["sage", "100", "FR|miscellaneousStockReceipt|journalEntry", "stockJournal", "FR|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|miscellaneousStockReceipt|journalEntry|200"], ["sage", "200", "FR|miscellaneousStockReceipt|journalEntry", "stockJournal", "FR|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|miscellaneousStockReceipt|journalEntry|100"], ["sage", "100", "FR|miscellaneousStockIssue|journalEntry", "stockJournal", "FR|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|miscellaneousStockIssue|journalEntry|200"], ["sage", "200", "FR|miscellaneousStockIssue|journalEntry", "stockJournal", "FR|item|StockVariation", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|miscellaneousStockIssue|journalEntry|100"], ["sage", "100", "FR|purchaseCreditMemo|accountsPayableInvoice", "document", "FR|item|PurchaseExpense", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "FR|purchaseInvoice|accountsPayableInvoice", "document", "FR|item|PurchaseExpense", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "200", "FR|purchaseInvoice|accountsPayableInvoice", "document", "FR|item|LandedCostExpense", "amountExcludingTax", "", "documentNumber", "N", "N", "N", "Y", ""], ["sage", "300", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|Stock", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|400"], ["sage", "400", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|StockVariation", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|300"], ["sage", "700", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|NonAbsorbedStock", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|800"], ["sage", "800", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|NonAbsorbedStockVariance", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|700"], ["sage", "900", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|Stock", "landedCostAdjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|1000"], ["sage", "1000", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|900"], ["sage", "1100", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|NonAbsorbedStock", "landedCostAdjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|1200"], ["sage", "1200", "FR|purchaseInvoice|journalEntry", "stockJournal", "FR|item|NonAbsorbedStockVariance", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseInvoice|journalEntry|1100"], ["sage", "100", "FR|purchaseCreditMemo|journalEntry", "stockJournal", "FR|item|StockVariation", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseCreditMemo|journalEntry|200"], ["sage", "200", "FR|purchaseCreditMemo|journalEntry", "stockJournal", "FR|item|Stock", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseCreditMemo|journalEntry|100"], ["sage", "300", "FR|purchaseCreditMemo|journalEntry", "stockJournal", "FR|item|NonAbsorbedStockVariance", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseCreditMemo|journalEntry|400"], ["sage", "400", "FR|purchaseCreditMemo|journalEntry", "stockJournal", "FR|item|NonAbsorbedStock", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseCreditMemo|journalEntry|300"], ["sage", "100", "FR|purchaseReceipt|journalEntry", "stockJournal", "FR|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|purchaseReceipt|journalEntry|200"], ["sage", "200", "FR|purchaseReceipt|journalEntry", "stockJournal", "FR|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|purchaseReceipt|journalEntry|100"], ["sage", "500", "FR|purchaseReceipt|journalEntry", "stockJournal", "FR|item|Stock", "landedCostAdjustmentAmount", "D", "documentNumber", "Y", "N", "N", "N", "FR|purchaseReceipt|journalEntry|600"], ["sage", "600", "FR|purchaseReceipt|journalEntry", "stockJournal", "FR|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseReceipt|journalEntry|500"], ["sage", "700", "FR|purchaseReceipt|journalEntry", "stockJournal", "FR|item|NonAbsorbedStock", "landedCostAdjustmentNonabsorbedAmount", "D", "documentNumber", "Y", "N", "N", "N", "FR|purchaseReceipt|journalEntry|800"], ["sage", "800", "FR|purchaseReceipt|journalEntry", "stockJournal", "FR|item|NonAbsorbedStockVariance", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|purchaseReceipt|journalEntry|700"], ["sage", "100", "FR|purchaseReturn|journalEntry", "stockJournal", "FR|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|purchaseReturn|journalEntry|200"], ["sage", "200", "FR|purchaseReturn|journalEntry", "stockJournal", "FR|item|StockVariation", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|purchaseReturn|journalEntry|100"], ["sage", "100", "FR|salesCreditMemo|accountsReceivableInvoice", "document", "FR|item|SalesRevenue", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "FR|salesInvoice|accountsReceivableInvoice", "document", "FR|item|SalesRevenue", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "FR|salesReturnReceipt|journalEntry", "stockJournal", "FR|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|salesReturnReceipt|journalEntry|200"], ["sage", "200", "FR|salesReturnReceipt|journalEntry", "stockJournal", "FR|item|StockVariationRevenue", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|salesReturnReceipt|journalEntry|100"], ["sage", "100", "FR|salesShipment|journalEntry", "stockJournal", "FR|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|salesShipment|journalEntry|200"], ["sage", "200", "FR|salesShipment|journalEntry", "stockJournal", "FR|item|StockVariationRevenue", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|salesShipment|journalEntry|100"], ["sage", "100", "FR|stockAdjustment|journalEntry", "stockJournal", "FR|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|stockAdjustment|journalEntry|200"], ["sage", "200", "FR|stockAdjustment|journalEntry", "stockJournal", "FR|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|stockAdjustment|journalEntry|100"], ["sage", "100", "FR|stockCount|journalEntry", "stockJournal", "FR|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|stockCount|journalEntry|200"], ["sage", "200", "FR|stockCount|journalEntry", "stockJournal", "FR|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|stockCount|journalEntry|100"], ["sage", "100", "FR|stockValueChange|journalEntry", "stockJournal", "FR|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "FR|stockValueChange|journalEntry|200"], ["sage", "200", "FR|stockValueChange|journalEntry", "stockJournal", "FR|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "FR|stockValueChange|journalEntry|100"], ["sage", "100", "FR|stockTransferShipment|journalEntry", "stockJournal", "FR|item|TransferStockInTransit", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|stockTransferShipment|journalEntry|200"], ["sage", "200", "FR|stockTransferShipment|journalEntry", "stockJournal", "FR|item|Stock", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|stockTransferShipment|journalEntry|100"], ["sage", "100", "FR|stockTransferReceipt|journalEntry", "stockJournal", "FR|item|Stock", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|stockTransferReceipt|journalEntry|200"], ["sage", "200", "FR|stockTransferReceipt|journalEntry", "stockJournal", "FR|item|TransferStockInTransit", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|stockTransferReceipt|journalEntry|100"], ["sage", "300", "FR|stockTransferReceipt|journalEntry", "stockJournal", "FR|item|Stock", "inTransitVarianceAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|stockTransferReceipt|journalEntry|400"], ["sage", "400", "FR|stockTransferReceipt|journalEntry", "stockJournal", "FR|item|PurchaseVariance", "inTransitVarianceAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "FR|stockTransferReceipt|journalEntry|300"], ["sage", "100", "GB|salesCreditMemo|journalEntry", "document", "GB|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "GB|salesCreditMemo|journalEntry|200"], ["sage", "200", "GB|salesCreditMemo|journalEntry", "document", "GB|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "GB|salesCreditMemo|journalEntry|100"], ["sage", "100", "GB|miscellaneousStockIssue|journalEntry", "stockJournal", "GB|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|miscellaneousStockIssue|journalEntry|200"], ["sage", "200", "GB|miscellaneousStockIssue|journalEntry", "stockJournal", "GB|item|StockIssue", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|miscellaneousStockIssue|journalEntry|100"], ["sage", "100", "GB|miscellaneousStockReceipt|journalEntry", "stockJournal", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|miscellaneousStockReceipt|journalEntry|200"], ["sage", "200", "GB|miscellaneousStockReceipt|journalEntry", "stockJournal", "GB|item|StockReceipt", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|miscellaneousStockReceipt|journalEntry|100"], ["sage", "100", "GB|purchaseCreditMemo|accountsPayableInvoice", "document", "GB|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "GB|purchaseCreditMemo|accountsPayableInvoice", "document", "GB|item|PayableNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "N", "Y", "Y", "N", ""], ["sage", "100", "GB|purchaseInvoice|accountsPayableInvoice", "document", "GB|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "documentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "GB|purchaseInvoice|accountsPayableInvoice", "document", "GB|item|PayableNotInvoiced", "amountExcludingTax", "", "documentNumber", "N", "Y", "Y", "N", ""], ["sage", "300", "GB|purchaseInvoice|accountsPayableInvoice", "document", "GB|item|LandedCostExpense", "amountExcludingTax", "", "documentNumber", "N", "N", "N", "Y", ""], ["sage", "100", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|200"], ["sage", "200", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|100"], ["sage", "300", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|PurchaseVariance", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|400"], ["sage", "400", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|300"], ["sage", "500", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|Stock", "landedCostAdjustmentAmount", "D", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|600"], ["sage", "600", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|GoodsInTransit", "landedCostStockInTransitAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|500"], ["sage", "700", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|800"], ["sage", "800", "GB|purchaseReceipt|journalEntry", "stockJournal", "GB|item|GoodsInTransit", "landedCostStockInTransitAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseReceipt|journalEntry|700"], ["sage", "900", "GB|purchaseReceipt|journalEntry", "document", "GB|item|Expense", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "GB|purchaseReceipt|journalEntry|1000"], ["sage", "1000", "GB|purchaseReceipt|journalEntry", "document", "GB|item|PayableNotInvoiced", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "GB|purchaseReceipt|journalEntry|900"], ["sage", "500", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|Stock", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|600"], ["sage", "600", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|500"], ["sage", "700", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|800"], ["sage", "800", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|700"], ["sage", "900", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|Stock", "landedCostAdjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|1000"], ["sage", "1000", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|900"], ["sage", "1100", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|1200"], ["sage", "1200", "GB|purchaseInvoice|journalEntry", "stockJournal", "GB|item|LandedCostAccrual", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|1100"], ["sage", "1300", "GB|purchaseInvoice|journalEntry", "document", "GB|item|GoodsInTransit", "landedCostStockInTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|1400"], ["sage", "1400", "GB|purchaseInvoice|journalEntry", "document", "GB|item|LandedCostAccrual", "landedCostStockInTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseInvoice|journalEntry|1300"], ["sage", "100", "GB|purchaseCreditMemo|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseCreditMemo|journalEntry|200"], ["sage", "200", "GB|purchaseCreditMemo|journalEntry", "stockJournal", "GB|item|Stock", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseCreditMemo|journalEntry|100"], ["sage", "300", "GB|purchaseCreditMemo|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseCreditMemo|journalEntry|400"], ["sage", "400", "GB|purchaseCreditMemo|journalEntry", "stockJournal", "GB|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|purchaseCreditMemo|journalEntry|300"], ["sage", "100", "GB|purchaseReturn|journalEntry", "stockJournal", "GB|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReturn|journalEntry|200"], ["sage", "200", "GB|purchaseReturn|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReturn|journalEntry|100"], ["sage", "300", "GB|purchaseReturn|journalEntry", "stockJournal", "GB|item|PurchaseVariance", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReturn|journalEntry|400"], ["sage", "400", "GB|purchaseReturn|journalEntry", "stockJournal", "GB|item|GoodsReceivedNotInvoiced", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "GB|purchaseReturn|journalEntry|300"], ["sage", "500", "GB|purchaseReturn|journalEntry", "document", "GB|item|Expense", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "GB|purchaseReturn|journalEntry|600"], ["sage", "600", "GB|purchaseReturn|journalEntry", "document", "GB|item|PayableNotInvoiced", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "GB|purchaseReturn|journalEntry|500"], ["sage", "100", "GB|salesCreditMemo|accountsReceivableInvoice", "document", "GB|item|SalesRevenue", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "GB|salesInvoice|accountsReceivableInvoice", "document", "GB|item|SalesRevenue", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "GB|salesInvoice|journalEntry", "document", "GB|item|SalesRevenueAccrual", "amountExcludingTax", "D", "sourceDocumentNumber", "Y", "Y", "Y", "N", "GB|salesInvoice|journalEntry|200"], ["sage", "200", "GB|salesInvoice|journalEntry", "document", "GB|item|ShippedNotInvoiced", "amountExcludingTax", "C", "sourceDocumentNumber", "Y", "Y", "Y", "N", "GB|salesInvoice|journalEntry|100"], ["sage", "100", "GB|salesReturnReceipt|journalEntry", "stockJournal", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|salesReturnReceipt|journalEntry|200"], ["sage", "200", "GB|salesReturnReceipt|journalEntry", "stockJournal", "GB|item|CostOfGoods", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|salesReturnReceipt|journalEntry|100"], ["sage", "300", "GB|salesReturnReceipt|journalEntry", "document", "GB|item|SalesRevenueAccrual", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "GB|salesReturnReceipt|journalEntry|400"], ["sage", "400", "GB|salesReturnReceipt|journalEntry", "document", "GB|item|ShippedNotInvoiced", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "GB|salesReturnReceipt|journalEntry|300"], ["sage", "100", "GB|salesShipment|journalEntry", "stockJournal", "GB|item|CostOfGoods", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|salesShipment|journalEntry|200"], ["sage", "200", "GB|salesShipment|journalEntry", "stockJournal", "GB|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|salesShipment|journalEntry|100"], ["sage", "300", "GB|salesShipment|journalEntry", "document", "GB|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "GB|salesShipment|journalEntry|400"], ["sage", "400", "GB|salesShipment|journalEntry", "document", "GB|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "GB|salesShipment|journalEntry|300"], ["sage", "100", "GB|stockAdjustment|journalEntry", "stockJournal", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|stockAdjustment|journalEntry|200"], ["sage", "200", "GB|stockAdjustment|journalEntry", "stockJournal", "GB|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|stockAdjustment|journalEntry|100"], ["sage", "1000", "GB|workInProgress|journalEntry", "productionTracking", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|1010"], ["sage", "1010", "GB|workInProgress|journalEntry", "productionTracking", "GB|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|1000"], ["sage", "2000", "GB|workInProgress|journalEntry", "materialTracking", "GB|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|2010"], ["sage", "2010", "GB|workInProgress|journalEntry", "materialTracking", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|2000"], ["sage", "3000", "GB|workInProgress|journalEntry", "laborSetupTimeTracking", "GB|resource|SetupProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "GB|workInProgress|journalEntry|3010"], ["sage", "3010", "GB|workInProgress|journalEntry", "laborSetupTimeTracking", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|3000"], ["sage", "3030", "GB|workInProgress|journalEntry", "laborRunTimeTracking", "GB|resource|RunTimeProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "GB|workInProgress|journalEntry|3040"], ["sage", "3040", "GB|workInProgress|journalEntry", "laborRunTimeTracking", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|3030"], ["sage", "3100", "GB|workInProgress|journalEntry", "machineSetupTimeTracking", "GB|resource|SetupProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "GB|workInProgress|journalEntry|3110"], ["sage", "3110", "GB|workInProgress|journalEntry", "machineSetupTimeTracking", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|3100"], ["sage", "3130", "GB|workInProgress|journalEntry", "machineRunTimeTracking", "GB|resource|RunTimeProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "GB|workInProgress|journalEntry|3140"], ["sage", "3140", "GB|workInProgress|journalEntry", "machineRunTimeTracking", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|3130"], ["sage", "3200", "GB|workInProgress|journalEntry", "toolSetupTimeTracking", "GB|resource|SetupProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "GB|workInProgress|journalEntry|3210"], ["sage", "3210", "GB|workInProgress|journalEntry", "toolSetupTimeTracking", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|3200"], ["sage", "3230", "GB|workInProgress|journalEntry", "toolRunTimeTracking", "GB|resource|RunTimeProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "GB|workInProgress|journalEntry|3240"], ["sage", "3240", "GB|workInProgress|journalEntry", "toolRunTimeTracking", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|3230"], ["sage", "4000", "GB|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "GB|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4010"], ["sage", "4010", "GB|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "GB|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4000"], ["sage", "4100", "GB|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4110"], ["sage", "4110", "GB|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "GB|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4100"], ["sage", "4200", "GB|workInProgress|journalEntry", "workOrderActualCostAdjustment", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4210"], ["sage", "4210", "GB|workInProgress|journalEntry", "workOrderActualCostAdjustment", "GB|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4200"], ["sage", "4220", "GB|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "GB|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4230"], ["sage", "4230", "GB|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "GB|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4220"], ["sage", "4300", "GB|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4310"], ["sage", "4310", "GB|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "GB|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4300"], ["sage", "4320", "GB|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "GB|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4330"], ["sage", "4330", "GB|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "GB|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|workInProgress|journalEntry|4320"], ["sage", "100", "GB|stockCount|journalEntry", "stockJournal", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|stockCount|journalEntry|200"], ["sage", "200", "GB|stockCount|journalEntry", "stockJournal", "GB|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|stockCount|journalEntry|100"], ["sage", "100", "GB|stockValueChange|journalEntry", "stockJournal", "GB|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "GB|stockValueChange|journalEntry|200"], ["sage", "200", "GB|stockValueChange|journalEntry", "stockJournal", "GB|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "GB|stockValueChange|journalEntry|100"], ["sage", "100", "GB|stockTransferShipment|journalEntry", "stockJournal", "GB|item|TransferStockInTransit", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|stockTransferShipment|journalEntry|200"], ["sage", "200", "GB|stockTransferShipment|journalEntry", "stockJournal", "GB|item|Stock", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|stockTransferShipment|journalEntry|100"], ["sage", "100", "GB|stockTransferReceipt|journalEntry", "stockJournal", "GB|item|Stock", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|stockTransferReceipt|journalEntry|200"], ["sage", "200", "GB|stockTransferReceipt|journalEntry", "stockJournal", "GB|item|TransferStockInTransit", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|stockTransferReceipt|journalEntry|100"], ["sage", "300", "GB|stockTransferReceipt|journalEntry", "stockJournal", "GB|item|Stock", "inTransitVarianceAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|stockTransferReceipt|journalEntry|400"], ["sage", "400", "GB|stockTransferReceipt|journalEntry", "stockJournal", "GB|item|PurchaseVariance", "inTransitVarianceAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "GB|stockTransferReceipt|journalEntry|300"], ["sage", "100", "US|salesCreditMemo|journalEntry", "document", "US|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "US|salesCreditMemo|journalEntry|200"], ["sage", "200", "US|salesCreditMemo|journalEntry", "document", "US|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "US|salesCreditMemo|journalEntry|100"], ["sage", "100", "US|miscellaneousStockIssue|journalEntry", "stockJournal", "US|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|miscellaneousStockIssue|journalEntry|200"], ["sage", "200", "US|miscellaneousStockIssue|journalEntry", "stockJournal", "US|item|StockIssue", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|miscellaneousStockIssue|journalEntry|100"], ["sage", "100", "US|miscellaneousStockReceipt|journalEntry", "stockJournal", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|miscellaneousStockReceipt|journalEntry|200"], ["sage", "200", "US|miscellaneousStockReceipt|journalEntry", "stockJournal", "US|item|StockReceipt", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|miscellaneousStockReceipt|journalEntry|100"], ["sage", "100", "US|purchaseCreditMemo|accountsPayableInvoice", "document", "US|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "US|purchaseCreditMemo|accountsPayableInvoice", "document", "US|item|PayableNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "N", "Y", "Y", "N", ""], ["sage", "300", "US|purchaseCreditMemo|accountsPayableInvoice", "document", "US|tax|TaxReceivable", "taxAmount", "", "sourceDocumentNumber", "N", "N", "N", "N", ""], ["sage", "100", "US|purchaseInvoice|accountsPayableInvoice", "document", "US|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "documentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "US|purchaseInvoice|accountsPayableInvoice", "document", "US|item|PayableNotInvoiced", "amountExcludingTax", "", "documentNumber", "N", "Y", "Y", "N", ""], ["sage", "300", "US|purchaseInvoice|accountsPayableInvoice", "document", "US|item|LandedCostExpense", "amountExcludingTax", "", "documentNumber", "N", "N", "N", "Y", ""], ["sage", "400", "US|purchaseInvoice|accountsPayableInvoice", "document", "US|tax|TaxReceivable", "taxAmount", "", "documentNumber", "N", "N", "N", "N", ""], ["sage", "100", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|200"], ["sage", "200", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|100"], ["sage", "300", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|PurchaseVariance", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|400"], ["sage", "400", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|300"], ["sage", "500", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|Stock", "landedCostAdjustmentAmount", "D", "documentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|600"], ["sage", "600", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|GoodsInTransit", "landedCostStockInTransitAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|500"], ["sage", "700", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "documentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|800"], ["sage", "800", "US|purchaseReceipt|journalEntry", "stockJournal", "US|item|GoodsInTransit", "landedCostStockInTransitAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseReceipt|journalEntry|700"], ["sage", "500", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|Stock", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|600"], ["sage", "600", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|500"], ["sage", "700", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|800"], ["sage", "800", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|700"], ["sage", "900", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|Stock", "landedCostAdjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|1000"], ["sage", "1000", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|900"], ["sage", "1100", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|1200"], ["sage", "1200", "US|purchaseInvoice|journalEntry", "stockJournal", "US|item|LandedCostAccrual", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|1100"], ["sage", "1300", "US|purchaseInvoice|journalEntry", "document", "US|item|GoodsInTransit", "landedCostStockInTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|1400"], ["sage", "1400", "US|purchaseInvoice|journalEntry", "document", "US|item|LandedCostAccrual", "landedCostStockInTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseInvoice|journalEntry|1300"], ["sage", "100", "US|purchaseCreditMemo|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseCreditMemo|journalEntry|200"], ["sage", "200", "US|purchaseCreditMemo|journalEntry", "stockJournal", "US|item|Stock", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseCreditMemo|journalEntry|100"], ["sage", "300", "US|purchaseCreditMemo|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseCreditMemo|journalEntry|400"], ["sage", "400", "US|purchaseCreditMemo|journalEntry", "stockJournal", "US|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|purchaseCreditMemo|journalEntry|300"], ["sage", "900", "US|purchaseReceipt|journalEntry", "document", "US|item|Expense", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "US|purchaseReceipt|journalEntry|1000"], ["sage", "1000", "US|purchaseReceipt|journalEntry", "document", "US|item|PayableNotInvoiced", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "US|purchaseReceipt|journalEntry|900"], ["sage", "100", "US|purchaseReturn|journalEntry", "stockJournal", "US|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|purchaseReturn|journalEntry|200"], ["sage", "200", "US|purchaseReturn|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|purchaseReturn|journalEntry|100"], ["sage", "300", "US|purchaseReturn|journalEntry", "stockJournal", "US|item|PurchaseVariance", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "US|purchaseReturn|journalEntry|400"], ["sage", "400", "US|purchaseReturn|journalEntry", "stockJournal", "US|item|GoodsReceivedNotInvoiced", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "US|purchaseReturn|journalEntry|300"], ["sage", "500", "US|purchaseReturn|journalEntry", "document", "US|item|Expense", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "US|purchaseReturn|journalEntry|600"], ["sage", "600", "US|purchaseReturn|journalEntry", "document", "US|item|PayableNotInvoiced", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "US|purchaseReturn|journalEntry|500"], ["sage", "100", "US|salesCreditMemo|accountsReceivableInvoice", "document", "US|item|SalesRevenue", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "200", "US|salesCreditMemo|accountsReceivableInvoice", "document", "US|tax|SalesTaxPayable", "taxAmount", "", "sourceDocumentNumber", "N", "N", "N", "N", ""], ["sage", "100", "US|salesInvoice|accountsReceivableInvoice", "document", "US|item|SalesRevenue", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "200", "US|salesInvoice|accountsReceivableInvoice", "document", "US|tax|SalesTaxPayable", "taxAmount", "", "documentNumber", "N", "N", "N", "N", ""], ["sage", "100", "US|salesInvoice|journalEntry", "document", "US|item|SalesRevenueAccrual", "amountExcludingTax", "D", "sourceDocumentNumber", "Y", "Y", "Y", "N", "US|salesInvoice|journalEntry|200"], ["sage", "200", "US|salesInvoice|journalEntry", "document", "US|item|ShippedNotInvoiced", "amountExcludingTax", "C", "sourceDocumentNumber", "Y", "Y", "Y", "N", "US|salesInvoice|journalEntry|100"], ["sage", "100", "US|salesReturnReceipt|journalEntry", "stockJournal", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|salesReturnReceipt|journalEntry|200"], ["sage", "200", "US|salesReturnReceipt|journalEntry", "stockJournal", "US|item|CostOfGoods", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|salesReturnReceipt|journalEntry|100"], ["sage", "300", "US|salesReturnReceipt|journalEntry", "document", "US|item|SalesRevenueAccrual", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "US|salesReturnReceipt|journalEntry|400"], ["sage", "400", "US|salesReturnReceipt|journalEntry", "document", "US|item|ShippedNotInvoiced", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "US|salesReturnReceipt|journalEntry|300"], ["sage", "100", "US|salesShipment|journalEntry", "stockJournal", "US|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|salesShipment|journalEntry|200"], ["sage", "200", "US|salesShipment|journalEntry", "stockJournal", "US|item|CostOfGoods", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|salesShipment|journalEntry|100"], ["sage", "300", "US|salesShipment|journalEntry", "document", "US|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "US|salesShipment|journalEntry|400"], ["sage", "400", "US|salesShipment|journalEntry", "document", "US|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "US|salesShipment|journalEntry|300"], ["sage", "100", "US|stockAdjustment|journalEntry", "stockJournal", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|stockAdjustment|journalEntry|200"], ["sage", "200", "US|stockAdjustment|journalEntry", "stockJournal", "US|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|stockAdjustment|journalEntry|100"], ["sage", "1000", "US|workInProgress|journalEntry", "productionTracking", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|1010"], ["sage", "1010", "US|workInProgress|journalEntry", "productionTracking", "US|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|1000"], ["sage", "2000", "US|workInProgress|journalEntry", "materialTracking", "US|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|2010"], ["sage", "2010", "US|workInProgress|journalEntry", "materialTracking", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|2000"], ["sage", "3000", "US|workInProgress|journalEntry", "laborSetupTimeTracking", "US|resource|SetupProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "US|workInProgress|journalEntry|3010"], ["sage", "3010", "US|workInProgress|journalEntry", "laborSetupTimeTracking", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|3000"], ["sage", "3030", "US|workInProgress|journalEntry", "laborRunTimeTracking", "US|resource|RunTimeProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "US|workInProgress|journalEntry|3040"], ["sage", "3040", "US|workInProgress|journalEntry", "laborRunTimeTracking", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|3030"], ["sage", "3100", "US|workInProgress|journalEntry", "machineSetupTimeTracking", "US|resource|SetupProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "US|workInProgress|journalEntry|3110"], ["sage", "3110", "US|workInProgress|journalEntry", "machineSetupTimeTracking", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|3100"], ["sage", "3130", "US|workInProgress|journalEntry", "machineRunTimeTracking", "US|resource|RunTimeProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "US|workInProgress|journalEntry|3140"], ["sage", "3140", "US|workInProgress|journalEntry", "machineRunTimeTracking", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|3130"], ["sage", "3200", "US|workInProgress|journalEntry", "toolSetupTimeTracking", "US|resource|SetupProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "US|workInProgress|journalEntry|3210"], ["sage", "3210", "US|workInProgress|journalEntry", "toolSetupTimeTracking", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|3200"], ["sage", "3230", "US|workInProgress|journalEntry", "toolRunTimeTracking", "US|resource|RunTimeProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "US|workInProgress|journalEntry|3240"], ["sage", "3240", "US|workInProgress|journalEntry", "toolRunTimeTracking", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|3230"], ["sage", "4000", "US|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "US|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4010"], ["sage", "4010", "US|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "US|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4000"], ["sage", "4100", "US|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4110"], ["sage", "4110", "US|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "US|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4100"], ["sage", "4200", "US|workInProgress|journalEntry", "workOrderActualCostAdjustment", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4210"], ["sage", "4210", "US|workInProgress|journalEntry", "workOrderActualCostAdjustment", "US|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4200"], ["sage", "4220", "US|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "US|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4230"], ["sage", "4230", "US|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "US|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4220"], ["sage", "4300", "US|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4310"], ["sage", "4310", "US|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "US|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4300"], ["sage", "4320", "US|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "US|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4330"], ["sage", "4330", "US|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "US|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|workInProgress|journalEntry|4320"], ["sage", "100", "US|stockCount|journalEntry", "stockJournal", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|stockCount|journalEntry|200"], ["sage", "200", "US|stockCount|journalEntry", "stockJournal", "US|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|stockCount|journalEntry|100"], ["sage", "100", "US|stockValueChange|journalEntry", "stockJournal", "US|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "US|stockValueChange|journalEntry|200"], ["sage", "200", "US|stockValueChange|journalEntry", "stockJournal", "US|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "US|stockValueChange|journalEntry|100"], ["sage", "100", "US|stockTransferShipment|journalEntry", "stockJournal", "US|item|TransferStockInTransit", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|stockTransferShipment|journalEntry|200"], ["sage", "200", "US|stockTransferShipment|journalEntry", "stockJournal", "US|item|Stock", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|stockTransferShipment|journalEntry|100"], ["sage", "100", "US|stockTransferReceipt|journalEntry", "stockJournal", "US|item|Stock", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|stockTransferReceipt|journalEntry|200"], ["sage", "200", "US|stockTransferReceipt|journalEntry", "stockJournal", "US|item|TransferStockInTransit", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|stockTransferReceipt|journalEntry|100"], ["sage", "300", "US|stockTransferReceipt|journalEntry", "stockJournal", "US|item|Stock", "inTransitVarianceAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "US|stockTransferReceipt|journalEntry|400"], ["sage", "400", "US|stockTransferReceipt|journalEntry", "stockJournal", "US|item|PurchaseVariance", "inTransitVarianceAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "US|stockTransferReceipt|journalEntry|300"], ["sage", "100", "ZA|bankReconciliationDeposit|journalEntry", "document", "ZA|line|Account", "amountIncludingTax", "D", "documentNumber", "N", "N", "N", "N", "ZA|bankReconciliationDeposit|journalEntry|200"], ["sage", "200", "ZA|bankReconciliationDeposit|journalEntry", "document", "ZA|line|Account", "amountExcludingTax", "C", "documentNumber", "N", "N", "N", "N", "ZA|bankReconciliationDeposit|journalEntry|100"], ["sage", "300", "ZA|bankReconciliationDeposit|journalEntry", "document", "ZA|tax|SalesTaxPayable", "taxAmount", "C", "documentNumber", "N", "N", "N", "N", "ZA|bankReconciliationDeposit|journalEntry|100"], ["sage", "100", "ZA|bankReconciliationWithdrawal|journalEntry", "document", "ZA|line|Account", "amountIncludingTax", "C", "documentNumber", "N", "N", "N", "N", "ZA|bankReconciliationWithdrawal|journalEntry|200"], ["sage", "200", "ZA|bankReconciliationWithdrawal|journalEntry", "document", "ZA|line|Account", "amountExcludingTax", "D", "documentNumber", "N", "N", "N", "N", "ZA|bankReconciliationWithdrawal|journalEntry|100"], ["sage", "300", "ZA|bankReconciliationWithdrawal|journalEntry", "document", "ZA|tax|TaxReceivable", "taxAmount", "D", "documentNumber", "N", "N", "N", "N", "ZA|bankReconciliationWithdrawal|journalEntry|100"], ["sage", "100", "ZA|salesCreditMemo|journalEntry", "document", "ZA|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "ZA|salesCreditMemo|journalEntry|200"], ["sage", "200", "ZA|salesCreditMemo|journalEntry", "document", "ZA|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "ZA|salesCreditMemo|journalEntry|100"], ["sage", "100", "ZA|miscellaneousStockIssue|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|miscellaneousStockIssue|journalEntry|200"], ["sage", "200", "ZA|miscellaneousStockIssue|journalEntry", "stockJournal", "ZA|item|StockIssue", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|miscellaneousStockIssue|journalEntry|100"], ["sage", "100", "ZA|miscellaneousStockReceipt|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|miscellaneousStockReceipt|journalEntry|200"], ["sage", "200", "ZA|miscellaneousStockReceipt|journalEntry", "stockJournal", "ZA|item|StockReceipt", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|miscellaneousStockReceipt|journalEntry|100"], ["sage", "100", "ZA|purchaseCreditMemo|accountsPayableInvoice", "document", "ZA|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "ZA|purchaseCreditMemo|accountsPayableInvoice", "document", "ZA|item|PayableNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "N", "Y", "Y", "N", ""], ["sage", "100", "ZA|purchaseInvoice|accountsPayableInvoice", "document", "ZA|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "documentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "ZA|purchaseInvoice|accountsPayableInvoice", "document", "ZA|item|PayableNotInvoiced", "amountExcludingTax", "", "documentNumber", "N", "Y", "Y", "N", ""], ["sage", "300", "ZA|purchaseInvoice|accountsPayableInvoice", "document", "ZA|item|LandedCostExpense", "amountExcludingTax", "", "documentNumber", "N", "N", "N", "Y", ""], ["sage", "100", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|200"], ["sage", "200", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|100"], ["sage", "300", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|PurchaseVariance", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|400"], ["sage", "400", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|300"], ["sage", "500", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|Stock", "landedCostAdjustmentAmount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|600"], ["sage", "600", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|GoodsInTransit", "landedCostStockInTransitAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|500"], ["sage", "700", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|800"], ["sage", "800", "ZA|purchaseReceipt|journalEntry", "stockJournal", "ZA|item|GoodsInTransit", "landedCostStockInTransitAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseReceipt|journalEntry|700"], ["sage", "500", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|Stock", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|600"], ["sage", "600", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|500"], ["sage", "700", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|800"], ["sage", "800", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|700"], ["sage", "900", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|Stock", "landedCostAdjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|1000"], ["sage", "1000", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|900"], ["sage", "1100", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|1200"], ["sage", "1200", "ZA|purchaseInvoice|journalEntry", "stockJournal", "ZA|item|LandedCostAccrual", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|1100"], ["sage", "1300", "ZA|purchaseInvoice|journalEntry", "document", "ZA|item|GoodsInTransit", "landedCostStockInTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|1400"], ["sage", "1400", "ZA|purchaseInvoice|journalEntry", "document", "ZA|item|LandedCostAccrual", "landedCostStockInTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseInvoice|journalEntry|1300"], ["sage", "100", "ZA|purchaseCreditMemo|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseCreditMemo|journalEntry|200"], ["sage", "200", "ZA|purchaseCreditMemo|journalEntry", "stockJournal", "ZA|item|Stock", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseCreditMemo|journalEntry|100"], ["sage", "300", "ZA|purchaseCreditMemo|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseCreditMemo|journalEntry|400"], ["sage", "400", "ZA|purchaseCreditMemo|journalEntry", "stockJournal", "ZA|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|purchaseCreditMemo|journalEntry|300"], ["sage", "900", "ZA|purchaseReceipt|journalEntry", "document", "ZA|item|Expense", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "ZA|purchaseReceipt|journalEntry|1000"], ["sage", "1000", "ZA|purchaseReceipt|journalEntry", "document", "ZA|item|PayableNotInvoiced", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "ZA|purchaseReceipt|journalEntry|900"], ["sage", "100", "ZA|purchaseReturn|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReturn|journalEntry|200"], ["sage", "200", "ZA|purchaseReturn|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReturn|journalEntry|100"], ["sage", "300", "ZA|purchaseReturn|journalEntry", "stockJournal", "ZA|item|PurchaseVariance", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReturn|journalEntry|400"], ["sage", "400", "ZA|purchaseReturn|journalEntry", "stockJournal", "ZA|item|GoodsReceivedNotInvoiced", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|purchaseReturn|journalEntry|300"], ["sage", "500", "ZA|purchaseReturn|journalEntry", "document", "ZA|item|Expense", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "ZA|purchaseReturn|journalEntry|600"], ["sage", "600", "ZA|purchaseReturn|journalEntry", "document", "ZA|item|PayableNotInvoiced", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "ZA|purchaseReturn|journalEntry|500"], ["sage", "100", "ZA|salesCreditMemo|accountsReceivableInvoice", "document", "ZA|item|SalesRevenue", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "ZA|salesInvoice|accountsReceivableInvoice", "document", "ZA|item|SalesRevenue", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "ZA|salesInvoice|journalEntry", "document", "ZA|item|SalesRevenueAccrual", "amountExcludingTax", "D", "sourceDocumentNumber", "Y", "Y", "Y", "N", "ZA|salesInvoice|journalEntry|200"], ["sage", "200", "ZA|salesInvoice|journalEntry", "document", "ZA|item|ShippedNotInvoiced", "amountExcludingTax", "C", "sourceDocumentNumber", "Y", "Y", "Y", "N", "ZA|salesInvoice|journalEntry|100"], ["sage", "100", "ZA|salesReturnReceipt|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|salesReturnReceipt|journalEntry|200"], ["sage", "200", "ZA|salesReturnReceipt|journalEntry", "stockJournal", "ZA|item|CostOfGoods", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|salesReturnReceipt|journalEntry|100"], ["sage", "300", "ZA|salesReturnReceipt|journalEntry", "document", "ZA|item|SalesRevenueAccrual", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "ZA|salesReturnReceipt|journalEntry|400"], ["sage", "400", "ZA|salesReturnReceipt|journalEntry", "document", "ZA|item|ShippedNotInvoiced", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "ZA|salesReturnReceipt|journalEntry|300"], ["sage", "100", "ZA|salesShipment|journalEntry", "stockJournal", "ZA|item|CostOfGoods", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|salesShipment|journalEntry|200"], ["sage", "200", "ZA|salesShipment|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|salesShipment|journalEntry|100"], ["sage", "300", "ZA|salesShipment|journalEntry", "document", "ZA|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "ZA|salesShipment|journalEntry|400"], ["sage", "400", "ZA|salesShipment|journalEntry", "document", "ZA|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "ZA|salesShipment|journalEntry|300"], ["sage", "100", "ZA|stockAdjustment|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|stockAdjustment|journalEntry|200"], ["sage", "200", "ZA|stockAdjustment|journalEntry", "stockJournal", "ZA|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|stockAdjustment|journalEntry|100"], ["sage", "1000", "ZA|workInProgress|journalEntry", "productionTracking", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|1010"], ["sage", "1010", "ZA|workInProgress|journalEntry", "productionTracking", "ZA|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|1000"], ["sage", "2000", "ZA|workInProgress|journalEntry", "materialTracking", "ZA|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|2010"], ["sage", "2010", "ZA|workInProgress|journalEntry", "materialTracking", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|2000"], ["sage", "3000", "ZA|workInProgress|journalEntry", "laborSetupTimeTracking", "ZA|resource|SetupProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "ZA|workInProgress|journalEntry|3010"], ["sage", "3010", "ZA|workInProgress|journalEntry", "laborSetupTimeTracking", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|3000"], ["sage", "3030", "ZA|workInProgress|journalEntry", "laborRunTimeTracking", "ZA|resource|RunTimeProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "ZA|workInProgress|journalEntry|3040"], ["sage", "3040", "ZA|workInProgress|journalEntry", "laborRunTimeTracking", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|3030"], ["sage", "3100", "ZA|workInProgress|journalEntry", "machineSetupTimeTracking", "ZA|resource|SetupProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "ZA|workInProgress|journalEntry|3110"], ["sage", "3110", "ZA|workInProgress|journalEntry", "machineSetupTimeTracking", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|3100"], ["sage", "3130", "ZA|workInProgress|journalEntry", "machineRunTimeTracking", "ZA|resource|RunTimeProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "ZA|workInProgress|journalEntry|3140"], ["sage", "3140", "ZA|workInProgress|journalEntry", "machineRunTimeTracking", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|3130"], ["sage", "3200", "ZA|workInProgress|journalEntry", "toolSetupTimeTracking", "ZA|resource|SetupProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "ZA|workInProgress|journalEntry|3210"], ["sage", "3210", "ZA|workInProgress|journalEntry", "toolSetupTimeTracking", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|3200"], ["sage", "3230", "ZA|workInProgress|journalEntry", "toolRunTimeTracking", "ZA|resource|RunTimeProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "ZA|workInProgress|journalEntry|3240"], ["sage", "3240", "ZA|workInProgress|journalEntry", "toolRunTimeTracking", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|3230"], ["sage", "4000", "ZA|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "ZA|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4010"], ["sage", "4010", "ZA|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "ZA|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4000"], ["sage", "4100", "ZA|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4110"], ["sage", "4110", "ZA|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "ZA|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4100"], ["sage", "4200", "ZA|workInProgress|journalEntry", "workOrderActualCostAdjustment", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4210"], ["sage", "4210", "ZA|workInProgress|journalEntry", "workOrderActualCostAdjustment", "ZA|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4200"], ["sage", "4220", "ZA|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "ZA|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4230"], ["sage", "4230", "ZA|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "ZA|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4220"], ["sage", "4300", "ZA|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4310"], ["sage", "4310", "ZA|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "ZA|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4300"], ["sage", "4320", "ZA|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "ZA|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4330"], ["sage", "4330", "ZA|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "ZA|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|workInProgress|journalEntry|4320"], ["sage", "100", "ZA|stockCount|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|stockCount|journalEntry|200"], ["sage", "200", "ZA|stockCount|journalEntry", "stockJournal", "ZA|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|stockCount|journalEntry|100"], ["sage", "100", "ZA|stockValueChange|journalEntry", "stockJournal", "ZA|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "ZA|stockValueChange|journalEntry|200"], ["sage", "200", "ZA|stockValueChange|journalEntry", "stockJournal", "ZA|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "ZA|stockValueChange|journalEntry|100"], ["sage", "100", "ZA|stockTransferShipment|journalEntry", "stockJournal", "ZA|item|TransferStockInTransit", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|stockTransferShipment|journalEntry|200"], ["sage", "200", "ZA|stockTransferShipment|journalEntry", "stockJournal", "ZA|item|Stock", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|stockTransferShipment|journalEntry|100"], ["sage", "100", "ZA|stockTransferReceipt|journalEntry", "stockJournal", "ZA|item|Stock", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|stockTransferReceipt|journalEntry|200"], ["sage", "200", "ZA|stockTransferReceipt|journalEntry", "stockJournal", "ZA|item|TransferStockInTransit", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|stockTransferReceipt|journalEntry|100"], ["sage", "300", "ZA|stockTransferReceipt|journalEntry", "stockJournal", "ZA|item|Stock", "inTransitVarianceAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|stockTransferReceipt|journalEntry|400"], ["sage", "400", "ZA|stockTransferReceipt|journalEntry", "stockJournal", "ZA|item|PurchaseVariance", "inTransitVarianceAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "ZA|stockTransferReceipt|journalEntry|300"], ["sage", "100", "DE|miscellaneousStockReceipt|journalEntry", "stockJournal", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|miscellaneousStockReceipt|journalEntry|200"], ["sage", "200", "DE|miscellaneousStockReceipt|journalEntry", "stockJournal", "DE|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|miscellaneousStockReceipt|journalEntry|100"], ["sage", "100", "DE|miscellaneousStockIssue|journalEntry", "stockJournal", "DE|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|miscellaneousStockIssue|journalEntry|200"], ["sage", "200", "DE|miscellaneousStockIssue|journalEntry", "stockJournal", "DE|item|StockVariation", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|miscellaneousStockIssue|journalEntry|100"], ["sage", "100", "DE|stockAdjustment|journalEntry", "stockJournal", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|stockAdjustment|journalEntry|200"], ["sage", "200", "DE|stockAdjustment|journalEntry", "stockJournal", "DE|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|stockAdjustment|journalEntry|100"], ["sage", "100", "DE|stockCount|journalEntry", "stockJournal", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|stockCount|journalEntry|200"], ["sage", "200", "DE|stockCount|journalEntry", "stockJournal", "DE|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|stockCount|journalEntry|100"], ["sage", "100", "DE|purchaseReceipt|journalEntry", "stockJournal", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|purchaseReceipt|journalEntry|200"], ["sage", "200", "DE|purchaseReceipt|journalEntry", "stockJournal", "DE|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|purchaseReceipt|journalEntry|100"], ["sage", "500", "DE|purchaseReceipt|journalEntry", "stockJournal", "DE|item|Stock", "landedCostAdjustmentAmount", "D", "documentNumber", "Y", "N", "N", "N", "DE|purchaseReceipt|journalEntry|600"], ["sage", "600", "DE|purchaseReceipt|journalEntry", "stockJournal", "DE|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseReceipt|journalEntry|500"], ["sage", "700", "DE|purchaseReceipt|journalEntry", "stockJournal", "DE|item|NonAbsorbedStock", "landedCostAdjustmentNonabsorbedAmount", "D", "documentNumber", "Y", "N", "N", "N", "DE|purchaseReceipt|journalEntry|800"], ["sage", "800", "DE|purchaseReceipt|journalEntry", "stockJournal", "DE|item|NonAbsorbedStockVariance", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseReceipt|journalEntry|700"], ["sage", "100", "DE|purchaseReturn|journalEntry", "stockJournal", "DE|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|purchaseReturn|journalEntry|200"], ["sage", "200", "DE|purchaseReturn|journalEntry", "stockJournal", "DE|item|StockVariation", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|purchaseReturn|journalEntry|100"], ["sage", "100", "DE|purchaseInvoice|accountsPayableInvoice", "document", "DE|item|PurchaseExpense", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "200", "DE|purchaseInvoice|accountsPayableInvoice", "document", "DE|item|LandedCostExpense", "amountExcludingTax", "", "documentNumber", "N", "N", "N", "Y", ""], ["sage", "300", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|Stock", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|400"], ["sage", "400", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|StockVariation", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|300"], ["sage", "700", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|NonAbsorbedStock", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|800"], ["sage", "800", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|NonAbsorbedStockVariance", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|700"], ["sage", "900", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|Stock", "landedCostAdjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|1000"], ["sage", "1000", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|900"], ["sage", "1100", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|NonAbsorbedStock", "landedCostAdjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|1200"], ["sage", "1200", "DE|purchaseInvoice|journalEntry", "stockJournal", "DE|item|NonAbsorbedStockVariance", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseInvoice|journalEntry|1100"], ["sage", "100", "DE|purchaseCreditMemo|accountsPayableInvoice", "document", "DE|item|PurchaseExpense", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "DE|salesShipment|journalEntry", "stockJournal", "DE|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|salesShipment|journalEntry|200"], ["sage", "200", "DE|salesShipment|journalEntry", "stockJournal", "DE|item|StockVariationSales", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|salesShipment|journalEntry|100"], ["sage", "100", "DE|salesReturnReceipt|journalEntry", "stockJournal", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|salesReturnReceipt|journalEntry|200"], ["sage", "200", "DE|salesReturnReceipt|journalEntry", "stockJournal", "DE|item|StockVariationSales", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|salesReturnReceipt|journalEntry|100"], ["sage", "100", "DE|salesCreditMemo|accountsReceivableInvoice", "document", "DE|item|SalesRevenue", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "DE|salesInvoice|accountsReceivableInvoice", "document", "DE|item|SalesRevenue", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "DE|apInvoice|journalEntry", "document", "DE|header|Account", "amountIncludingTax", "C", "sourceDocumentNumber", "N", "N", "N", "N", "DE|apInvoice|journalEntry|200"], ["sage", "200", "DE|apInvoice|journalEntry", "document", "DE|line|Account", "amountExcludingTax", "D", "sourceDocumentNumber", "N", "N", "N", "N", "DE|apInvoice|journalEntry|100"], ["sage", "300", "DE|apInvoice|journalEntry", "document", "DE|tax|Vat", "nonDeductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "DE|apInvoice|journalEntry|100"], ["sage", "400", "DE|apInvoice|journalEntry", "document", "DE|tax|Vat", "deductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "DE|apInvoice|journalEntry|100"], ["sage", "500", "DE|apInvoice|journalEntry", "document", "DE|tax|ReverseChargeVat", "reverseChargeNonDeductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "DE|apInvoice|journalEntry|100"], ["sage", "600", "DE|apInvoice|journalEntry", "document", "DE|tax|ReverseChargeVat", "reverseChargeDeductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "DE|apInvoice|journalEntry|100"], ["sage", "100", "DE|arInvoice|journalEntry", "document", "DE|header|Account", "amountIncludingTax", "D", "sourceDocumentNumber", "N", "N", "N", "N", "DE|arInvoice|journalEntry|200"], ["sage", "200", "DE|arInvoice|journalEntry", "document", "DE|line|Account", "amountExcludingTax", "C", "sourceDocumentNumber", "N", "N", "N", "N", "DE|arInvoice|journalEntry|100"], ["sage", "300", "DE|arInvoice|journalEntry", "document", "DE|tax|Vat", "nonDeductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "DE|arInvoice|journalEntry|100"], ["sage", "400", "DE|arInvoice|journalEntry", "document", "DE|tax|Vat", "deductibleTaxAmount", "C", "sourceDocumentNumber", "N", "N", "N", "N", "DE|arInvoice|journalEntry|100"], ["sage", "500", "DE|arInvoice|journalEntry", "document", "DE|tax|ReverseChargeVat", "reverseChargeNonDeductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "DE|arInvoice|journalEntry|100"], ["sage", "600", "DE|arInvoice|journalEntry", "document", "DE|tax|ReverseChargeVat", "reverseChargeDeductibleTaxAmount", "D", "sourceDocumentNumber", "N", "N", "N", "N", "DE|arInvoice|journalEntry|100"], ["sage", "1000", "DE|workInProgress|journalEntry", "productionTracking", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|1010"], ["sage", "1010", "DE|workInProgress|journalEntry", "productionTracking", "DE|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|1000"], ["sage", "2000", "DE|workInProgress|journalEntry", "materialTracking", "DE|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|2010"], ["sage", "2010", "DE|workInProgress|journalEntry", "materialTracking", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|2000"], ["sage", "3000", "DE|workInProgress|journalEntry", "laborSetupTimeTracking", "DE|resource|SetupProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "DE|workInProgress|journalEntry|3010"], ["sage", "3010", "DE|workInProgress|journalEntry", "laborSetupTimeTracking", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|3000"], ["sage", "3030", "DE|workInProgress|journalEntry", "laborRunTimeTracking", "DE|resource|RunTimeProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "DE|workInProgress|journalEntry|3040"], ["sage", "3040", "DE|workInProgress|journalEntry", "laborRunTimeTracking", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|3030"], ["sage", "3100", "DE|workInProgress|journalEntry", "machineSetupTimeTracking", "DE|resource|SetupProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "DE|workInProgress|journalEntry|3110"], ["sage", "3110", "DE|workInProgress|journalEntry", "machineSetupTimeTracking", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|3100"], ["sage", "3130", "DE|workInProgress|journalEntry", "machineRunTimeTracking", "DE|resource|RunTimeProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "DE|workInProgress|journalEntry|3140"], ["sage", "3140", "DE|workInProgress|journalEntry", "machineRunTimeTracking", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|3130"], ["sage", "3200", "DE|workInProgress|journalEntry", "toolSetupTimeTracking", "DE|resource|SetupProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "DE|workInProgress|journalEntry|3210"], ["sage", "3210", "DE|workInProgress|journalEntry", "toolSetupTimeTracking", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|3200"], ["sage", "3230", "DE|workInProgress|journalEntry", "toolRunTimeTracking", "DE|resource|RunTimeProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "DE|workInProgress|journalEntry|3240"], ["sage", "3240", "DE|workInProgress|journalEntry", "toolRunTimeTracking", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|3230"], ["sage", "4000", "DE|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "DE|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4010"], ["sage", "4010", "DE|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "DE|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4000"], ["sage", "4100", "DE|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4110"], ["sage", "4110", "DE|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "DE|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4100"], ["sage", "4200", "DE|workInProgress|journalEntry", "workOrderActualCostAdjustment", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4210"], ["sage", "4210", "DE|workInProgress|journalEntry", "workOrderActualCostAdjustment", "DE|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4200"], ["sage", "4220", "DE|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "DE|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4230"], ["sage", "4230", "DE|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "DE|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4220"], ["sage", "4300", "DE|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4310"], ["sage", "4310", "DE|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "DE|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4300"], ["sage", "4320", "DE|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "DE|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4330"], ["sage", "4330", "DE|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "DE|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|workInProgress|journalEntry|4320"], ["sage", "100", "DE|purchaseCreditMemo|journalEntry", "stockJournal", "DE|item|StockVariation", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseCreditMemo|journalEntry|200"], ["sage", "200", "DE|purchaseCreditMemo|journalEntry", "stockJournal", "DE|item|Stock", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseCreditMemo|journalEntry|100"], ["sage", "300", "DE|purchaseCreditMemo|journalEntry", "stockJournal", "DE|item|NonAbsorbedStockVariance", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseCreditMemo|journalEntry|400"], ["sage", "400", "DE|purchaseCreditMemo|journalEntry", "stockJournal", "DE|item|NonAbsorbedStock", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|purchaseCreditMemo|journalEntry|300"], ["sage", "100", "DE|stockValueChange|journalEntry", "stockJournal", "DE|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "DE|stockValueChange|journalEntry|200"], ["sage", "200", "DE|stockValueChange|journalEntry", "stockJournal", "DE|item|StockVariation", "amount", "C", "documentNumber", "Y", "N", "N", "N", "DE|stockValueChange|journalEntry|100"], ["sage", "100", "DE|stockTransferShipment|journalEntry", "stockJournal", "DE|item|TransferStockInTransit", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|stockTransferShipment|journalEntry|200"], ["sage", "200", "DE|stockTransferShipment|journalEntry", "stockJournal", "DE|item|Stock", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|stockTransferShipment|journalEntry|100"], ["sage", "100", "DE|stockTransferReceipt|journalEntry", "stockJournal", "DE|item|Stock", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|stockTransferReceipt|journalEntry|200"], ["sage", "200", "DE|stockTransferReceipt|journalEntry", "stockJournal", "DE|item|TransferStockInTransit", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|stockTransferReceipt|journalEntry|100"], ["sage", "300", "DE|stockTransferReceipt|journalEntry", "stockJournal", "DE|item|Stock", "inTransitVarianceAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|stockTransferReceipt|journalEntry|400"], ["sage", "400", "DE|stockTransferReceipt|journalEntry", "stockJournal", "DE|item|PurchaseVariance", "inTransitVarianceAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "DE|stockTransferReceipt|journalEntry|300"], ["sage", "100", "AU|salesCreditMemo|journalEntry", "document", "AU|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "AU|salesCreditMemo|journalEntry|200"], ["sage", "200", "AU|salesCreditMemo|journalEntry", "document", "AU|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "AU|salesCreditMemo|journalEntry|100"], ["sage", "100", "AU|miscellaneousStockIssue|journalEntry", "stockJournal", "AU|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|miscellaneousStockIssue|journalEntry|200"], ["sage", "200", "AU|miscellaneousStockIssue|journalEntry", "stockJournal", "AU|item|StockIssue", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|miscellaneousStockIssue|journalEntry|100"], ["sage", "100", "AU|miscellaneousStockReceipt|journalEntry", "stockJournal", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|miscellaneousStockReceipt|journalEntry|200"], ["sage", "200", "AU|miscellaneousStockReceipt|journalEntry", "stockJournal", "AU|item|StockReceipt", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|miscellaneousStockReceipt|journalEntry|100"], ["sage", "100", "AU|purchaseCreditMemo|accountsPayableInvoice", "document", "AU|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "AU|purchaseCreditMemo|accountsPayableInvoice", "document", "AU|item|PayableNotInvoiced", "amountExcludingTax", "", "sourceDocumentNumber", "N", "Y", "Y", "N", ""], ["sage", "100", "AU|purchaseInvoice|accountsPayableInvoice", "document", "AU|item|GoodsReceivedNotInvoiced", "amountExcludingTax", "", "documentNumber", "Y", "N", "N", "N", ""], ["sage", "200", "AU|purchaseInvoice|accountsPayableInvoice", "document", "AU|item|PayableNotInvoiced", "amountExcludingTax", "", "documentNumber", "N", "Y", "Y", "N", ""], ["sage", "300", "AU|purchaseInvoice|accountsPayableInvoice", "document", "AU|item|LandedCostExpense", "amountExcludingTax", "", "documentNumber", "N", "N", "N", "Y", ""], ["sage", "100", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|200"], ["sage", "200", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|100"], ["sage", "300", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|PurchaseVariance", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|400"], ["sage", "400", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|300"], ["sage", "500", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|Stock", "landedCostAdjustmentAmount", "D", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|600"], ["sage", "600", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|GoodsInTransit", "landedCostStockInTransitAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|500"], ["sage", "700", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|800"], ["sage", "800", "AU|purchaseReceipt|journalEntry", "stockJournal", "AU|item|GoodsInTransit", "landedCostStockInTransitAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseReceipt|journalEntry|700"], ["sage", "900", "AU|purchaseReceipt|journalEntry", "document", "AU|item|Expense", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "AU|purchaseReceipt|journalEntry|1000"], ["sage", "1000", "AU|purchaseReceipt|journalEntry", "document", "AU|item|PayableNotInvoiced", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "AU|purchaseReceipt|journalEntry|900"], ["sage", "500", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|Stock", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|600"], ["sage", "600", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|500"], ["sage", "700", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|800"], ["sage", "800", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|700"], ["sage", "900", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|Stock", "landedCostAdjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|1000"], ["sage", "1000", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|LandedCostAccrual", "landedCostAdjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|900"], ["sage", "1100", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|PurchaseVariance", "landedCostAdjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|1200"], ["sage", "1200", "AU|purchaseInvoice|journalEntry", "stockJournal", "AU|item|LandedCostAccrual", "landedCostAdjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|1100"], ["sage", "1300", "AU|purchaseInvoice|journalEntry", "document", "AU|item|GoodsInTransit", "landedCostStockInTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|1400"], ["sage", "1400", "AU|purchaseInvoice|journalEntry", "document", "AU|item|LandedCostAccrual", "landedCostStockInTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseInvoice|journalEntry|1300"], ["sage", "100", "AU|purchaseCreditMemo|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "adjustmentAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseCreditMemo|journalEntry|200"], ["sage", "200", "AU|purchaseCreditMemo|journalEntry", "stockJournal", "AU|item|Stock", "adjustmentAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseCreditMemo|journalEntry|100"], ["sage", "300", "AU|purchaseCreditMemo|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "adjustmentNonabsorbedAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseCreditMemo|journalEntry|400"], ["sage", "400", "AU|purchaseCreditMemo|journalEntry", "stockJournal", "AU|item|PurchaseVariance", "adjustmentNonabsorbedAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|purchaseCreditMemo|journalEntry|300"], ["sage", "100", "AU|purchaseReturn|journalEntry", "stockJournal", "AU|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReturn|journalEntry|200"], ["sage", "200", "AU|purchaseReturn|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReturn|journalEntry|100"], ["sage", "300", "AU|purchaseReturn|journalEntry", "stockJournal", "AU|item|PurchaseVariance", "varianceAmount", "C", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReturn|journalEntry|400"], ["sage", "400", "AU|purchaseReturn|journalEntry", "stockJournal", "AU|item|GoodsReceivedNotInvoiced", "varianceAmount", "D", "documentNumber", "Y", "N", "N", "N", "AU|purchaseReturn|journalEntry|300"], ["sage", "500", "AU|purchaseReturn|journalEntry", "document", "AU|item|Expense", "amountExcludingTax", "C", "documentNumber", "N", "Y", "Y", "N", "AU|purchaseReturn|journalEntry|600"], ["sage", "600", "AU|purchaseReturn|journalEntry", "document", "AU|item|PayableNotInvoiced", "amountExcludingTax", "D", "documentNumber", "N", "Y", "Y", "N", "AU|purchaseReturn|journalEntry|500"], ["sage", "100", "AU|salesCreditMemo|accountsReceivableInvoice", "document", "AU|item|SalesRevenue", "amountExcludingTax", "", "sourceDocumentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "AU|salesInvoice|accountsReceivableInvoice", "document", "AU|item|SalesRevenue", "amountExcludingTax", "", "documentNumber", "Y", "Y", "Y", "N", ""], ["sage", "100", "AU|salesInvoice|journalEntry", "document", "AU|item|SalesRevenueAccrual", "amountExcludingTax", "D", "sourceDocumentNumber", "Y", "Y", "Y", "N", "AU|salesInvoice|journalEntry|200"], ["sage", "200", "AU|salesInvoice|journalEntry", "document", "AU|item|ShippedNotInvoiced", "amountExcludingTax", "C", "sourceDocumentNumber", "Y", "Y", "Y", "N", "AU|salesInvoice|journalEntry|100"], ["sage", "100", "AU|salesReturnReceipt|journalEntry", "stockJournal", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|salesReturnReceipt|journalEntry|200"], ["sage", "200", "AU|salesReturnReceipt|journalEntry", "stockJournal", "AU|item|CostOfGoods", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|salesReturnReceipt|journalEntry|100"], ["sage", "300", "AU|salesReturnReceipt|journalEntry", "document", "AU|item|SalesRevenueAccrual", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "AU|salesReturnReceipt|journalEntry|400"], ["sage", "400", "AU|salesReturnReceipt|journalEntry", "document", "AU|item|ShippedNotInvoiced", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "AU|salesReturnReceipt|journalEntry|300"], ["sage", "100", "AU|salesShipment|journalEntry", "stockJournal", "AU|item|CostOfGoods", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|salesShipment|journalEntry|200"], ["sage", "200", "AU|salesShipment|journalEntry", "stockJournal", "AU|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|salesShipment|journalEntry|100"], ["sage", "300", "AU|salesShipment|journalEntry", "document", "AU|item|ShippedNotInvoiced", "amountExcludingTax", "D", "documentNumber", "Y", "Y", "Y", "N", "AU|salesShipment|journalEntry|400"], ["sage", "400", "AU|salesShipment|journalEntry", "document", "AU|item|SalesRevenueAccrual", "amountExcludingTax", "C", "documentNumber", "Y", "Y", "Y", "N", "AU|salesShipment|journalEntry|300"], ["sage", "100", "AU|stockAdjustment|journalEntry", "stockJournal", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|stockAdjustment|journalEntry|200"], ["sage", "200", "AU|stockAdjustment|journalEntry", "stockJournal", "AU|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|stockAdjustment|journalEntry|100"], ["sage", "1000", "AU|workInProgress|journalEntry", "productionTracking", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|1010"], ["sage", "1010", "AU|workInProgress|journalEntry", "productionTracking", "AU|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|1000"], ["sage", "2000", "AU|workInProgress|journalEntry", "materialTracking", "AU|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|2010"], ["sage", "2010", "AU|workInProgress|journalEntry", "materialTracking", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|2000"], ["sage", "3000", "AU|workInProgress|journalEntry", "laborSetupTimeTracking", "AU|resource|SetupProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "AU|workInProgress|journalEntry|3010"], ["sage", "3010", "AU|workInProgress|journalEntry", "laborSetupTimeTracking", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|3000"], ["sage", "3030", "AU|workInProgress|journalEntry", "laborRunTimeTracking", "AU|resource|RunTimeProductionLabor", "amount", "C", "documentNumber", "N", "N", "N", "N", "AU|workInProgress|journalEntry|3040"], ["sage", "3040", "AU|workInProgress|journalEntry", "laborRunTimeTracking", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|3030"], ["sage", "3100", "AU|workInProgress|journalEntry", "machineSetupTimeTracking", "AU|resource|SetupProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "AU|workInProgress|journalEntry|3110"], ["sage", "3110", "AU|workInProgress|journalEntry", "machineSetupTimeTracking", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|3100"], ["sage", "3130", "AU|workInProgress|journalEntry", "machineRunTimeTracking", "AU|resource|RunTimeProductionMachine", "amount", "C", "documentNumber", "N", "N", "N", "N", "AU|workInProgress|journalEntry|3140"], ["sage", "3140", "AU|workInProgress|journalEntry", "machineRunTimeTracking", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|3130"], ["sage", "3200", "AU|workInProgress|journalEntry", "toolSetupTimeTracking", "AU|resource|SetupProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "AU|workInProgress|journalEntry|3210"], ["sage", "3210", "AU|workInProgress|journalEntry", "toolSetupTimeTracking", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|3200"], ["sage", "3230", "AU|workInProgress|journalEntry", "toolRunTimeTracking", "AU|resource|RunTimeProductionTool", "amount", "C", "documentNumber", "N", "N", "N", "N", "AU|workInProgress|journalEntry|3240"], ["sage", "3240", "AU|workInProgress|journalEntry", "toolRunTimeTracking", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|3230"], ["sage", "4000", "AU|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "AU|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4010"], ["sage", "4010", "AU|workInProgress|journalEntry", "workOrder<PERSON><PERSON>ce", "AU|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4000"], ["sage", "4100", "AU|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4110"], ["sage", "4110", "AU|workInProgress|journalEntry", "workOrderNegativeV<PERSON>ce", "AU|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4100"], ["sage", "4200", "AU|workInProgress|journalEntry", "workOrderActualCostAdjustment", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4210"], ["sage", "4210", "AU|workInProgress|journalEntry", "workOrderActualCostAdjustment", "AU|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4200"], ["sage", "4220", "AU|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "AU|item|ProductionVariance", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4230"], ["sage", "4230", "AU|workInProgress|journalEntry", "workOrderActualCostAdjustmentNonAbsorbed", "AU|item|WorkInProgress", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4220"], ["sage", "4300", "AU|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4310"], ["sage", "4310", "AU|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustment", "AU|item|Stock", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4300"], ["sage", "4320", "AU|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "AU|item|WorkInProgress", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4330"], ["sage", "4330", "AU|workInProgress|journalEntry", "workOrderNegativeActualCostAdjustmentNonAbsorbed", "AU|item|ProductionVariance", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|workInProgress|journalEntry|4320"], ["sage", "100", "AU|stockCount|journalEntry", "stockJournal", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|stockCount|journalEntry|200"], ["sage", "200", "AU|stockCount|journalEntry", "stockJournal", "AU|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|stockCount|journalEntry|100"], ["sage", "100", "AU|stockValueChange|journalEntry", "stockJournal", "AU|item|Stock", "amount", "D", "documentNumber", "Y", "N", "N", "N", "AU|stockValueChange|journalEntry|200"], ["sage", "200", "AU|stockValueChange|journalEntry", "stockJournal", "AU|item|StockAdjustment", "amount", "C", "documentNumber", "Y", "N", "N", "N", "AU|stockValueChange|journalEntry|100"], ["sage", "100", "AU|stockTransferShipment|journalEntry", "stockJournal", "AU|item|TransferStockInTransit", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|stockTransferShipment|journalEntry|200"], ["sage", "200", "AU|stockTransferShipment|journalEntry", "stockJournal", "AU|item|Stock", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|stockTransferShipment|journalEntry|100"], ["sage", "100", "AU|stockTransferReceipt|journalEntry", "stockJournal", "AU|item|Stock", "inTransitAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|stockTransferReceipt|journalEntry|200"], ["sage", "200", "AU|stockTransferReceipt|journalEntry", "stockJournal", "AU|item|TransferStockInTransit", "inTransitAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|stockTransferReceipt|journalEntry|100"], ["sage", "300", "AU|stockTransferReceipt|journalEntry", "stockJournal", "AU|item|Stock", "inTransitVarianceAmount", "D", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|stockTransferReceipt|journalEntry|400"], ["sage", "400", "AU|stockTransferReceipt|journalEntry", "stockJournal", "AU|item|PurchaseVariance", "inTransitVarianceAmount", "C", "sourceDocumentNumber", "Y", "N", "N", "N", "AU|stockTransferReceipt|journalEntry|300"]]}}}