{"fromVersion": "55.0.11", "toVersion": "55.0.12", "gitHead": "8ed130f5428cbe7aaa75e8a52e024039606072f1", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_document ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.base_document.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounting_staging_document_tax ALTER COLUMN document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging_document_tax.document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.finance_transaction_line ALTER COLUMN source_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.finance_transaction_line.source_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.finance_transaction ALTER COLUMN finance_integration_app_record_id TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.finance_transaction.finance_integration_app_record_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.finance_transaction ALTER COLUMN source_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.finance_transaction.source_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.finance_transaction ALTER COLUMN target_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.finance_transaction.target_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.finance_transaction ALTER COLUMN document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.finance_transaction.document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_payment_document ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.base_payment_document.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounting_staging ALTER COLUMN supplier_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging.supplier_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounting_staging ALTER COLUMN source_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging.source_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounting_staging ALTER COLUMN document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging.document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_open_item ALTER COLUMN document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.base_open_item.document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_finance_document ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.base_finance_document.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.datev_export_journal_entry_line ALTER COLUMN supplier_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.datev_export_journal_entry_line.supplier_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.datev_export_journal_entry_line ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.datev_export_journal_entry_line.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_invoice_line ALTER COLUMN source_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_invoice_line.source_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_payable_invoice_line ALTER COLUMN source_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_payable_invoice_line.source_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.journal_entry ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_payment ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_payment.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_invoice ALTER COLUMN sales_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_invoice.sales_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_invoice ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_invoice.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_receivable_advance ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_advance.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_payable_invoice ALTER COLUMN purchase_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_payable_invoice.purchase_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_payable_invoice ALTER COLUMN supplier_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_payable_invoice.supplier_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.accounts_payable_invoice ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_payable_invoice.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.purchase_return ALTER COLUMN supplier_return_reference TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_return.supplier_return_reference IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.purchase_return ALTER COLUMN supplier_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_return.supplier_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.purchase_receipt ALTER COLUMN supplier_document_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_receipt.supplier_document_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.production_tracking ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.production_tracking.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.operation_tracking ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.operation_tracking.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.material_tracking ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.material_tracking.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.work_order ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.intacct_bank_account_transaction_feed ALTER COLUMN journal_entry_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_bank_account_transaction_feed.journal_entry_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_value_correction ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_value_correction.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_value_change ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_value_change.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_receipt ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_receipt.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_issue ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_issue.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_count ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_count.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_change ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_change.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.stock_adjustment ALTER COLUMN number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_adjustment.number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.supply_planning ALTER COLUMN work_order_number TYPE VARCHAR(30) COLLATE \"und-x-icu\";COMMENT ON COLUMN %%SCHEMA_NAME%%.supply_planning.work_order_number IS '{", "  \"type\": \"string\",", "  \"isSystem\": false,", "  \"maxLength\": 30", "}';;"]}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.base_status_enum ADD VALUE IF NOT EXISTS 'quote'   ;"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.base_display_status_enum ADD VALUE IF NOT EXISTS 'quote'   ;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN number DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN display_status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN date DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN is_printed DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN is_sent DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN internal_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN is_external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN is_transfer_header_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN is_transfer_line_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN currency DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN stock_site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _create_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _update_user DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _create_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _update_stamp DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _update_tick DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _source_id DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS sales_order_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order DROP CONSTRAINT IF EXISTS sales_order_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS sales_order_currency_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order DROP CONSTRAINT IF EXISTS sales_order_currency_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS sales_order_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order DROP CONSTRAINT IF EXISTS sales_order_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS sales_order__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order DROP CONSTRAINT IF EXISTS sales_order__create_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS sales_order__update_user_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order DROP CONSTRAINT IF EXISTS sales_order__update_user_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sales_order_ind0;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.bill_of_material_revision ADD COLUMN IF NOT EXISTS status_order INT8;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.bill_of_material_revision.status_order IS '{", "  \"type\": \"integer\",", "  \"isSystem\": false", "}';"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.payment_tracking ADD COLUMN IF NOT EXISTS payment_term INT8, ADD COLUMN IF NOT EXISTS discount_payment_amount NUMERIC(28,10), ADD COLUMN IF NOT EXISTS penalty_payment_amount NUMERIC(28,10), ADD COLUMN IF NOT EXISTS penalty_payment_type %%SCHEMA_NAME%%.payment_term_discount_or_penalty_type_enum, ADD COLUMN IF NOT EXISTS discount_payment_type %%SCHEMA_NAME%%.payment_term_discount_or_penalty_type_enum, ADD COLUMN IF NOT EXISTS discount_payment_before_date DATE;", "COMMENT ON COLUMN %%SCHEMA_NAME%%.payment_tracking.payment_term IS '{", "  \"type\": \"reference\",", "  \"isSystem\": false,", "  \"targetTableName\": \"payment_term\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.payment_tracking.discount_payment_amount IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": false,", "  \"precision\": 10,", "  \"scale\": 3", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.payment_tracking.penalty_payment_amount IS '{", "  \"type\": \"decimal\",", "  \"isSystem\": false,", "  \"precision\": 10,", "  \"scale\": 3", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.payment_tracking.penalty_payment_type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"payment_term_discount_or_penalty_type_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.payment_tracking.discount_payment_type IS '{", "  \"type\": \"enum\",", "  \"isSystem\": false,", "  \"enumTypeName\": \"payment_term_discount_or_penalty_type_enum\"", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.payment_tracking.discount_payment_before_date IS '{", "  \"type\": \"date\",", "  \"isSystem\": false", "}';"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.payment_tracking AS t00 SET discount_payment_amount=(CASE WHEN t1.discount_amount IS NULL THEN NULL ELSE t1.discount_amount END) FROM %%SCHEMA_NAME%%.payment_tracking AS t0 LEFT JOIN %%SCHEMA_NAME%%.payment_term AS t1 ON t0.payment_term=t1._id AND t0._tenant_id=t1._tenant_id WHERE (t0._id = t00._id) AND (t00._tenant_id = t0._tenant_id) AND ((t0.discount_payment_amount IS NULL))", "args": [], "actionDescription": "Auto data action for property PaymentTracking.discountPaymentAmount"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.payment_tracking AS t00 SET penalty_payment_amount=(CASE WHEN t1.penalty_amount IS NULL THEN NULL ELSE t1.penalty_amount END) FROM %%SCHEMA_NAME%%.payment_tracking AS t0 LEFT JOIN %%SCHEMA_NAME%%.payment_term AS t1 ON t0.payment_term=t1._id AND t0._tenant_id=t1._tenant_id WHERE (t0._id = t00._id) AND (t00._tenant_id = t0._tenant_id) AND ((t0.penalty_payment_amount IS NULL))", "args": [], "actionDescription": "Auto data action for property PaymentTracking.penaltyPaymentAmount"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.payment_tracking AS t00 SET penalty_payment_type=((CASE WHEN t1.penalty_type IS NULL THEN NULL ELSE t1.penalty_type END))::%%SCHEMA_NAME%%.payment_term_discount_or_penalty_type_enum FROM %%SCHEMA_NAME%%.payment_tracking AS t0 LEFT JOIN %%SCHEMA_NAME%%.payment_term AS t1 ON t0.payment_term=t1._id AND t0._tenant_id=t1._tenant_id WHERE (t0._id = t00._id) AND (t00._tenant_id = t0._tenant_id) AND ((t0.penalty_payment_type IS NULL))", "args": [], "actionDescription": "Auto data action for property PaymentTracking.penaltyPaymentType"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.payment_tracking AS t00 SET discount_payment_type=((CASE WHEN t1.discount_type IS NULL THEN NULL ELSE t1.discount_type END))::%%SCHEMA_NAME%%.payment_term_discount_or_penalty_type_enum FROM %%SCHEMA_NAME%%.payment_tracking AS t0 LEFT JOIN %%SCHEMA_NAME%%.payment_term AS t1 ON t0.payment_term=t1._id AND t0._tenant_id=t1._tenant_id WHERE (t0._id = t00._id) AND (t00._tenant_id = t0._tenant_id) AND ((t0.discount_payment_type IS NULL))", "args": [], "actionDescription": "Auto data action for property PaymentTracking.discountPaymentType"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN _sort_value DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN document DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN item DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN item_description DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN unit DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN unit_to_stock_unit_conversion_factor DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN stock_unit DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN internal_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN is_external_note DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN quantity DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN quantity_in_stock_unit DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN analytical_data DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN stock_site DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ALTER COLUMN stock_site_linked_address DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_document_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_document_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_document_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_item_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_item_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_item_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_unit_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_analytical_data_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_site_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_site_linked_address_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line_stock_site_linked_address_fk;"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sales_order_line_ind0;"}, {"isSysPool": true, "comment": "Manually grant sys pool to execute ALTER SEQUENCE + fix stock_site, isPrinted, isSent, externalNode, isExternalNote, upgrade + fix currencies", "sql": ["", "      DO $$", "      DECLARE", "        soRecord RECORD;", "        base_id %%SCHEMA_NAME%%.base_document._id%TYPE := 1;", "        start_id %%SCHEMA_NAME%%.base_document._id%TYPE := 1;", "        financial_site_id %%SCHEMA_NAME%%.site._id%TYPE;", "      BEGIN", "        -- Defer constraints to avoid violations during updates", "        SET CONSTRAINTS ALL DEFERRED;", "        -- Get the max ID from sales_order table", "        start_id := (", "          SELECT COALESCE(MAX(_id), 0)", "          FROM %%SCHEMA_NAME%%.sales_order", "        );", "        -- Update the base_document sequence if needed", "        IF start_id > COALESCE(", "          (SELECT last_value FROM %%SCHEMA_NAME%%.base_document__id_seq),", "          0", "        ) THEN", "          EXECUTE 'ALTER SEQUENCE %%SCHEMA_NAME%%.base_document__id_seq RESTART WITH ' || (start_id + 1);", "        END IF;", "        -- Iterate over sales orders to insert into base_document", "        FOR soRecord IN (", "          SELECT", "            so._id,", "            so._tenant_id,", "            so.number,", "            so.status::text::%%SCHEMA_NAME%%.base_status_enum,", "            so.display_status::text::%%SCHEMA_NAME%%.base_display_status_enum,", "            so.site,", "            so.stock_site,", "            so.is_printed,", "            so.is_sent,", "            so.external_note,", "            so.is_external_note,", "            so.date AS order_date,", "            so.internal_note,", "            so.is_transfer_header_note,", "            so.is_transfer_line_note,", "            so._create_user,", "            so._create_stamp,", "            so._update_user,", "            so._update_stamp,", "            site.is_finance,", "            site.financial_site,", "            so.currency", "          FROM %%SCHEMA_NAME%%.sales_order so", "          INNER JOIN %%SCHEMA_NAME%%.site AS site", "            ON site._id = so.site AND so._tenant_id = site._tenant_id", "        )", "        LOOP", "          financial_site_id := CASE", "            WHEN soRecord.is_finance = TRUE THEN soRecord.site", "            ELSE soRecord.financial_site", "          END;", "          -- Insert into base_document", "          INSERT INTO %%SCHEMA_NAME%%.base_document (", "            _tenant_id,", "            _constructor,", "            number,", "            status,", "            approval_status,", "            display_status,", "            date,", "            is_printed,", "            is_sent,", "            site,", "            stock_site,", "            currency,", "            financial_site,", "            internal_note,", "            external_note,", "            is_external_note,", "            is_transfer_header_note,", "            is_transfer_line_note,", "            text,", "            _create_user,", "            _create_stamp,", "            _update_user,", "            _update_stamp", "          )", "          VALUES (", "            soRecord._tenant_id,", "            'SalesOrder',", "            soRecord.number,", "            soRecord.status,", "            'draft',", "            soRecord.display_status,", "            soRecord.order_date,", "            soRecord.is_printed,", "            soRecord.is_sent,", "            soRecord.site,", "            soRecord.stock_site,", "            soRecord.currency,", "            financial_site_id,", "            soRecord.internal_note,", "            soRecord.external_note,", "            soRecord.is_external_note,", "            soRecord.is_transfer_header_note,", "            soRecord.is_transfer_line_note,", "            '',", "            soRecord._create_user,", "            soRecord._create_stamp,", "            soRecord._update_user,", "            soRecord._update_stamp", "          )", "          RETURNING _id INTO base_id;", "          -- Update sales_order with new base ID", "          UPDATE %%SCHEMA_NAME%%.sales_order", "          SET _id = base_id", "          WHERE _id = soRecord._id", "            AND _tenant_id = soRecord._tenant_id;", "          -- Update sales_order_line with new document ID", "          UPDATE %%SCHEMA_NAME%%.sales_order_line", "          SET document = base_id", "          WHERE document = soRecord._id", "            AND _tenant_id = soRecord._tenant_id;", "          -- Update sales_order_tax with new document ID", "          UPDATE %%SCHEMA_NAME%%.sales_order_tax", "          SET document = base_id", "          WHERE document = soRecord._id", "            AND _tenant_id = soRecord._tenant_id;", "        -- Update sales_order_tax with new document ID", "          UPDATE %%SCHEMA_NAME%%.proforma_invoice", "          SET sales_order = base_id", "          WHERE sales_order = soRecord._id", "            AND _tenant_id = soRecord._tenant_id;", "          -- Update attachment association with new source node ID", "          UPDATE %%SCHEMA_NAME%%.attachment_association", "          SET source_node_id = base_id", "          WHERE source_node_id = soRecord._id", "            AND source_node_name = 'SalesOrder';", "        END LOOP;", "      END $$;", "    "], "actionDescription": "Move the sales order to the base_document table"}, {"isSysPool": false, "sql": ["", "            INSERT INTO %%SCHEMA_NAME%%.base_document_item_line (", "                _tenant_id,", "                _id,", "                _constructor,", "                _sort_value,", "                document,", "                status,", "                site,", "                site_linked_address,", "                item,", "                item_description,", "                stock_site,", "                stock_site_linked_address,", "                stock_unit,", "                unit,", "                quantity,", "                internal_note,", "                external_note,", "                is_external_note,", "                stored_dimensions,", "                stored_attributes,", "                analytical_data,", "                quantity_in_stock_unit,", "                unit_to_stock_unit_conversion_factor,", "                _custom_data", "            )", "            SELECT", "                line._tenant_id,", "                line._id,", "                'SalesOrderLine',", "                line._sort_value,", "                line.document,", "                line.status::text::%%SCHEMA_NAME%%.base_status_enum,", "                line.site,", "                addressBase._id,", "                line.item,", "                line.item_description,", "                line.stock_site,", "                line.stock_site_linked_address,", "                line.stock_unit,", "                line.unit,", "                line.quantity,", "                line.internal_note,", "                line.external_note,", "                line.is_external_note,", "                line.stored_dimensions,", "                line.stored_attributes,", "                line.analytical_data,", "                line.quantity_in_stock_unit,", "                line.unit_to_stock_unit_conversion_factor,", "                line._custom_data", "            FROM %%SCHEMA_NAME%%.sales_order_line AS line", "            INNER JOIN %%SCHEMA_NAME%%.site AS site", "                ON line.site = site._id", "                AND line._tenant_id = site._tenant_id", "            INNER JOIN %%SCHEMA_NAME%%.business_entity_address AS businessEntityAddress", "                ON site.primary_address = businessEntityAddress._id", "                AND site._tenant_id = businessEntityAddress._tenant_id", "            INNER JOIN %%SCHEMA_NAME%%.address_base AS addressBase", "                ON businessEntityAddress._id = addressBase._id", "                AND businessEntityAddress._tenant_id = addressBase._tenant_id", "                AND addressBase._constructor = 'BusinessEntityAddress'", "            INNER JOIN %%SCHEMA_NAME%%.address AS address", "                ON addressBase.address = address._id", "                AND addressBase._tenant_id = address._tenant_id;", ""], "actionDescription": "Move sales order line to base document item line"}, {"isSysPool": false, "sql": ["UPDATE %%SCHEMA_NAME%%.bill_of_material_revision AS t0", "            SET status_order=(CASE WHEN (t0.status::TEXT = 'availableToUse')", "            THEN 10 ELSE (CASE WHEN (t0.status::TEXT = 'inDevelopment')", "            THEN 20 ELSE 30 END) END) WHERE ((t0.status_order IS NULL))"], "actionDescription": "billOfMaterialRevision statusOrder init property value"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _id DROP DEFAULT", "actionDescription": "SalesOrder: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": "DROP SEQUENCE IF EXISTS %%SCHEMA_NAME%%.sales_order__id_seq", "actionDescription": "SalesOrder: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sales_order ALTER COLUMN _id TYPE INT8;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_order._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;"], "actionDescription": "SalesOrder: retype _id from integer to integer' (autoIncrement->integer)"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS audit_table ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS insert_table ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS update_table ON %%SCHEMA_NAME%%.sales_order;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER audit_table", "            AFTER DELETE OR INSERT OR UPDATE ON %%SCHEMA_NAME%%.sales_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.audit_table('base_document', 'SalesOrder');", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.sales_order_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_document WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.sales_order", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.sales_order_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_delete ON %%SCHEMA_NAME%%.sales_order_line;", "DROP FUNCTION IF EXISTS %%SCHEMA_NAME%%.sales_order_line_base_delete();"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.sales_order_line_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_document_item_line WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.sales_order_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.sales_order_line_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS sales_order_line__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS sales_order_line__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP CONSTRAINT IF EXISTS sales_order_line__id_fk;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sales_order_line ADD CONSTRAINT \"sales_order_line__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_document_item_line(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sales_order_line__id_fk ON %%SCHEMA_NAME%%.sales_order_line IS '{", "  \"targetTableName\": \"base_document_item_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order_line DROP COLUMN IF EXISTS _sort_value, DROP COLUMN IF EXISTS document, DROP COLUMN IF EXISTS status, DROP COLUMN IF EXISTS item, DROP COLUMN IF EXISTS item_description, DROP COLUMN IF EXISTS site, DROP COLUMN IF EXISTS unit, DROP COLUMN IF EXISTS unit_to_stock_unit_conversion_factor, DROP COLUMN IF EXISTS stock_unit, DROP COLUMN IF EXISTS stored_dimensions, DROP COLUMN IF EXISTS stored_attributes, DROP COLUMN IF EXISTS internal_note, DROP COLUMN IF EXISTS external_note, DROP COLUMN IF EXISTS is_external_note, DROP COLUMN IF EXISTS quantity, DROP COLUMN IF EXISTS quantity_in_stock_unit, DROP COLUMN IF EXISTS analytical_data, DROP COLUMN IF EXISTS stock_site, DROP COLUMN IF EXISTS stock_site_linked_address;"}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.sales_order_line_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_document_item_line WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.sales_order_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.sales_order_line_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.payment_tracking ADD CONSTRAINT \"payment_tracking_payment_term_fk\" FOREIGN KEY(_tenant_id,payment_term) REFERENCES %%SCHEMA_NAME%%.payment_term(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT payment_tracking_payment_term_fk ON %%SCHEMA_NAME%%.payment_tracking IS '{", "  \"targetTableName\": \"payment_term\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"payment_term\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.bill_of_material_revision ALTER COLUMN status_order SET NOT NULL;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sales_order ADD CONSTRAINT \"sales_order__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_document(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sales_order__id_fk ON %%SCHEMA_NAME%%.sales_order IS '{", "  \"targetTableName\": \"base_document\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sales_order DROP COLUMN IF EXISTS number, DROP COLUMN IF EXISTS status, DROP COLUMN IF EXISTS display_status, DROP COLUMN IF EXISTS date, DROP COLUMN IF EXISTS is_printed, DROP COLUMN IF EXISTS is_sent, DROP COLUMN IF EXISTS site, DROP COLUMN IF EXISTS internal_note, DROP COLUMN IF EXISTS external_note, DROP COLUMN IF EXISTS is_external_note, DROP COLUMN IF EXISTS is_transfer_header_note, DROP COLUMN IF EXISTS is_transfer_line_note, DROP COLUMN IF EXISTS currency, DROP COLUMN IF EXISTS stock_site, DROP COLUMN IF EXISTS _create_user, DROP COLUMN IF EXISTS _update_user, DROP COLUMN IF EXISTS _create_stamp, DROP COLUMN IF EXISTS _update_stamp, DROP COLUMN IF EXISTS _update_tick, DROP COLUMN IF EXISTS _source_id;"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentification feature", "experimental", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tags (not yet released)", "experimental", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "experimental", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "experimental", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "workInProgress", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Open item page display", "released", true, "@sage/xtrem-structure", false, "openItemPageOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Bill of material revision", "workInProgress", false, "@sage/xtrem-master-data", false, "billOfMaterialRevisionServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "released", false, "@sage/xtrem-master-data", true, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "landedCostStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "released", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Discount payment tracking option", "workInProgress", false, "@sage/xtrem-finance-data", false, "discountPaymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Payment tracking option", "released", false, "@sage/xtrem-finance-data", false, "paymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation option", "released", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "workInProgress", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "workInProgress", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Network", "workInProgress", false, "@sage/xtrem-sage-network", true, "sageNetworkOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00KKA1-75415\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.sales_order IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_document\",", "  \"rootTable\": \"base_document\",", "  \"hasAttachments\": true,", "  \"naturalKey\": [", "    \"number\"", "  ]", "}';;COMMENT ON CONSTRAINT sales_order__id_fk ON %%SCHEMA_NAME%%.sales_order IS '{", "  \"targetTableName\": \"base_document\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT payment_tracking_payment_term_fk ON %%SCHEMA_NAME%%.payment_tracking IS '{", "  \"targetTableName\": \"payment_term\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"payment_term\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.sales_order_line IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_document_item_line\",", "  \"rootTable\": \"base_document_line\",", "  \"naturalKey\": [", "    \"document\",", "    \"_sortValue\"", "  ]", "}';;COMMENT ON CONSTRAINT sales_order_line__id_fk ON %%SCHEMA_NAME%%.sales_order_line IS '{", "  \"targetTableName\": \"base_document_item_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';"]}], "data": {}}