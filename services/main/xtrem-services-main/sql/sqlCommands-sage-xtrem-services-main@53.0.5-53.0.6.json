{"fromVersion": "53.0.5", "toVersion": "53.0.6", "gitHead": "5ae7ae1ee3ecc8f8333697fb7925e409336fa26f", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.base_display_status_enum ADD VALUE IF NOT EXISTS 'paid'   ;"}, {"isSysPool": true, "sql": "ALTER TYPE %%SCHEMA_NAME%%.base_display_status_enum ADD VALUE IF NOT EXISTS 'partiallyPaid'   ;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN supplier DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN fx_rate_date DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN payment_term DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN tax_calculation_status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN company_fx_rate DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN company_fx_rate_divisor DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN total_amount_excluding_tax DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN total_amount_excluding_tax_in_company_currency DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN total_tax_amount DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN total_taxable_amount DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN total_exempt_amount DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ALTER COLUMN total_tax_amount_adjusted DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document_supplier_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_distribution_document DROP CONSTRAINT IF EXISTS base_purchase_document_supplier_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document_supplier_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document_payment_term_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_distribution_document DROP CONSTRAINT IF EXISTS base_purchase_document_payment_term_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document_payment_term_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order ALTER COLUMN invoice_status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_receipt ALTER COLUMN invoice_status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_return ALTER COLUMN invoice_status DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN gross_price DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN net_price DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN line_amount_excluding_tax DROP NOT NULL;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ALTER COLUMN line_amount_excluding_tax_in_company_currency DROP NOT NULL;"}, {"isSysPool": true, "sql": ["", "            DO $$ DECLARE", "                pRecord RECORD;", "                BEGIN", "                 FOR pRecord IN (", "                                        SELECT", "                        doc._tenant_id,", "                        doc._id,", "                        doc._constructor,", "                        doc.supplier,", "                        doc.supplier as business_relation ,", "                        doc.bill_by_supplier,", "                        doc.fx_rate_date,", "                        doc.payment_term,", "                        doc.total_amount_excluding_tax,", "                        doc.total_amount_excluding_tax_in_company_currency,", "                        doc.total_tax_amount,", "                        doc.total_taxable_amount,", "                        doc.total_exempt_amount,", "                        doc.tax_calculation_status,", "                        doc.total_tax_amount_adjusted,", "                        doc.company_fx_rate,", "                        doc.company_fx_rate_divisor,", "                        doc._custom_data,", "    \t\t\t\t\tCOALESCE(porder.invoice_status::TEXT, return.invoice_status::TEXT, receipt.invoice_status::TEXT, 'notInvoiced')::%%SCHEMA_NAME%%.invoice_status_enum AS invoice_status", "                    FROM %%SCHEMA_NAME%%.base_purchase_document doc", "                        left join %%SCHEMA_NAME%%.purchase_order as porder on porder._id = doc._id", "                            and porder._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseOrder'", "                        left join %%SCHEMA_NAME%%.purchase_return as return on return._id = doc._id", "                            and return._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReturn'", "                        left join %%SCHEMA_NAME%%.purchase_receipt as receipt on receipt._id = doc._id", "                            and receipt._tenant_id = doc._tenant_id and doc._constructor = 'PurchaseReceipt'", "                    )", "                LOOP", "                    INSERT INTO %%SCHEMA_NAME%%.base_distribution_document(", "                            _tenant_id,", "                            _id,", "                            _constructor,", "                            business_relation,", "                            fx_rate_date,", "                            company_fx_rate,", "                            _custom_data,", "                            payment_term,", "                            tax_calculation_status,", "                            company_fx_rate_divisor,", "                            total_amount_excluding_tax,", "                            total_tax_amount,", "                            total_taxable_amount,", "                            total_exempt_amount,", "                            total_tax_amount_adjusted,", "                            invoice_status,", "                            total_amount_excluding_tax_in_company_currency", "                        )", "                        VALUES (", "                            pRecord._tenant_id,", "                            pRecord._id,", "                            pRecord._constructor,", "                            pRecord.business_relation,", "                            pRecord.fx_rate_date,", "                            pRecord.company_fx_rate,", "                            pRecord._custom_data,", "                            pRecord.payment_term,", "                            pRecord.tax_calculation_status,", "                            pRecord.company_fx_rate_divisor,", "                            pRecord.total_amount_excluding_tax,", "                            pRecord.total_tax_amount,", "                            pRecord.total_taxable_amount,", "                            pRecord.total_exempt_amount,", "                            pRecord.total_tax_amount_adjusted,", "                            pRecord.invoice_status,", "                            pRecord.total_amount_excluding_tax_in_company_currency", "                        );", "                 END LOOP;", "            END $$;", "        "], "actionDescription": "Upgrade BasePurchaseDocuments to BaseDocument"}, {"isSysPool": false, "sql": ["", "                    INSERT INTO %%SCHEMA_NAME%%.base_distribution_document_line", "                                (", "                        _tenant_id,", "                        _id,", "                        _constructor,", "                        _sort_value,", "                        _custom_data,", "                        gross_price,", "                        price_origin,", "                        net_price,", "                        line_amount_excluding_tax,", "                        line_amount_excluding_tax_in_company_currency", "                                )", "                    SELECT", "                        line._tenant_id,", "                        line._id,", "                        line._constructor,", "                        line._sort_value,", "                        line._custom_data,", "                        line.gross_price,", "                        line.price_origin::text::%%SCHEMA_NAME%%.base_price_origin_enum,", "                        line.net_price,", "                        line.line_amount_excluding_tax,", "                        line.line_amount_excluding_tax_in_company_currency", "                    FROM      %%SCHEMA_NAME%%.base_purchase_document_line line", "                    LEFT JOIN %%SCHEMA_NAME%%.purchase_order_line AS porderline", "                    ON        porderline._id = line._id", "                    AND       porderline._tenant_id = line._tenant_id", "                    AND       line._constructor = 'PurchaseOrderLine'", "                    LEFT JOIN %%SCHEMA_NAME%%.purchase_return_line AS returnline", "                    ON        returnline._id = line._id", "                    AND       returnline._tenant_id = line._tenant_id", "                    AND       line._constructor = 'PurchaseReturnLine'", "                    LEFT JOIN %%SCHEMA_NAME%%.purchase_credit_memo_line AS pcmline", "                    ON        pcmline._id = line._id", "                    AND       pcmline._tenant_id = line._tenant_id", "                    AND       line._constructor = 'PurchaseCreditMemoLine'", "                    LEFT JOIN %%SCHEMA_NAME%%.purchase_invoice_line AS invoiceline", "                    ON        invoiceline._id = line._id", "                    AND       invoiceline._tenant_id = line._tenant_id", "                    AND       line._constructor = 'PurchaseInvoiceLine'", "                    LEFT JOIN %%SCHEMA_NAME%%.purchase_receipt_line AS receiptline", "                    ON        receiptline._id = line._id", "                    AND       receiptline._tenant_id = line._tenant_id", "                    AND       line._constructor = 'PurchaseReceiptLine'", "            "], "actionDescription": "Move some line properties from purchase to base document item line"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_delete ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP FUNCTION IF EXISTS %%SCHEMA_NAME%%.base_purchase_document_base_delete();"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_distribution_document WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_delete ON %%SCHEMA_NAME%%.base_purchase_document_line;", "DROP FUNCTION IF EXISTS %%SCHEMA_NAME%%.base_purchase_document_line_base_delete();"], "args": []}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_line_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_distribution_document_line WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_line_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document_item_line DROP CONSTRAINT IF EXISTS base_purchase_document_line__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_distribution_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP CONSTRAINT IF EXISTS base_purchase_document_line__id_fk;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line ADD CONSTRAINT \"base_purchase_document_line__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_distribution_document_line(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_purchase_document_line__id_fk ON %%SCHEMA_NAME%%.base_purchase_document_line IS '{", "  \"targetTableName\": \"base_distribution_document_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document_line DROP COLUMN IF EXISTS gross_price, DROP COLUMN IF EXISTS price_origin, DROP COLUMN IF EXISTS net_price, DROP COLUMN IF EXISTS line_amount_excluding_tax, DROP COLUMN IF EXISTS line_amount_excluding_tax_in_company_currency;"}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_line_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_distribution_document_line WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document_line", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_line_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_return DROP COLUMN IF EXISTS invoice_status;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_receipt DROP COLUMN IF EXISTS invoice_status;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.purchase_order DROP COLUMN IF EXISTS invoice_status;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_document DROP CONSTRAINT IF EXISTS base_purchase_document__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_distribution_document DROP CONSTRAINT IF EXISTS base_purchase_document__id_fk;"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP CONSTRAINT IF EXISTS base_purchase_document__id_fk;"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document ADD CONSTRAINT \"base_purchase_document__id_fk\" FOREIGN KEY(_tenant_id,_id) REFERENCES %%SCHEMA_NAME%%.base_distribution_document(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT base_purchase_document__id_fk ON %%SCHEMA_NAME%%.base_purchase_document IS '{", "  \"targetTableName\": \"base_distribution_document\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.base_purchase_document DROP COLUMN IF EXISTS supplier, DROP COLUMN IF EXISTS fx_rate_date, DROP COLUMN IF EXISTS payment_term, DROP COLUMN IF EXISTS tax_calculation_status, DROP COLUMN IF EXISTS company_fx_rate, DROP COLUMN IF EXISTS company_fx_rate_divisor, DROP COLUMN IF EXISTS total_amount_excluding_tax, DROP COLUMN IF EXISTS total_amount_excluding_tax_in_company_currency, DROP COLUMN IF EXISTS total_tax_amount, DROP COLUMN IF EXISTS total_taxable_amount, DROP COLUMN IF EXISTS total_exempt_amount, DROP COLUMN IF EXISTS total_tax_amount_adjusted;"}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.base_purchase_document_base_delete()", "            RETURNS TRIGGER", "            AS", "            $$", "                BEGIN", "                    EXECUTE 'DELETE FROM %%SCHEMA_NAME%%.base_distribution_document WHERE _id = '", "                        || OLD._id || ' AND _tenant_id = ''' || OLD._tenant_id || '''';", "                    RETURN OLD;", "                END;", "            $$", "            LANGUAGE plpgsql;", "DO $$ BEGIN", "            CREATE TRIGGER base_delete", "            AFTER DELETE ON %%SCHEMA_NAME%%.base_purchase_document", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.base_purchase_document_base_delete();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tags (not yet released)", "experimental", false, "@sage/xtrem-system", false, "sysTag"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "experimental", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "experimental", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "workInProgress", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Open item page display", "released", true, "@sage/xtrem-structure", false, "openItemPageOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "workInProgress", false, "@sage/xtrem-master-data", false, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "released", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Payment tracking option", "workInProgress", false, "@sage/xtrem-finance-data", false, "apPaymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Payment tracking option", "released", false, "@sage/xtrem-finance-data", false, "paymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation option", "released", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "workInProgress", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "workInProgress", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Network", "workInProgress", false, "@sage/xtrem-sage-network", true, "sageNetworkOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00IS5O-75570\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["COMMENT ON TABLE %%SCHEMA_NAME%%.base_purchase_document IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_distribution_document\",", "  \"rootTable\": \"base_document\",", "  \"naturalKey\": [", "    \"_constructor\",", "    \"number\"", "  ]", "}';;COMMENT ON CONSTRAINT base_purchase_document__id_fk ON %%SCHEMA_NAME%%.base_purchase_document IS '{", "  \"targetTableName\": \"base_distribution_document\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON TABLE %%SCHEMA_NAME%%.base_purchase_document_line IS '{", "  \"isSharedByAllTenants\": false,", "  \"baseTable\": \"base_distribution_document_line\",", "  \"rootTable\": \"base_document_line\",", "  \"naturalKey\": [", "    \"document\",", "    \"_sortValue\"", "  ]", "}';;COMMENT ON CONSTRAINT base_purchase_document_line__id_fk ON %%SCHEMA_NAME%%.base_purchase_document_line IS '{", "  \"targetTableName\": \"base_distribution_document_line\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_id\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';"]}], "data": {}}