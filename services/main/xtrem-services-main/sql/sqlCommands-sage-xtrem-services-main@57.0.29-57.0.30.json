{"fromVersion": "57.0.29", "toVersion": "57.0.30", "gitHead": "ede2f94305aef92e1c6cb115b57fd26e7d3acbbe", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        IF p_constructor != '' THEN", "            constructor := p_constructor;", "        ELSE", "            constructor := COALESCE(NEW._constructor, OLD._constructor);", "        END IF;", "", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                    VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "\t        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%, update_tick=%->%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id(), OLD._update_tick, NEW._update_tick;", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (node_name, root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                    VALUES (constructor, p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "\t        RAISE NOTICE 'Inserted new audit log record root_table=%, table=%, _id=%, transaction_id=%', p_root_table_name, TG_TABLE_NAME, NEW._id, pg_current_xact_id();", "            END IF;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                UPDATE %%SCHEMA_NAME%%.sys_audit_log", "                SET record_data = log_record.record_data || to_jsonb(NEW), new_update_tick = NEW._update_tick", "                WHERE root_table_name = p_root_table_name", "                    AND record_id = NEW._id", "                    AND transaction_id = pg_current_xact_id()::TEXT;", "\t        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%, updateTick=%->%', p_root_table_name, NEW._id, pg_current_xact_id(), log_record.old_update_tick, NEW._update_tick;", "            ELSE", "                UPDATE %%SCHEMA_NAME%%.sys_audit_log", "                SET record_data = log_record.record_data || to_jsonb(NEW)", "                WHERE root_table_name = p_root_table_name", "                    AND record_id = NEW._id", "                    AND transaction_id = pg_current_xact_id()::TEXT;", "\t        RAISE NOTICE 'Updated audit log record %:%, transaction_id=%', p_root_table_name, NEW._id, pg_current_xact_id();", "            END IF;", "        END IF;", "", "        -- do not send a notification for the update if the record was created in the same transaction", "        -- this will happen with nodes with deferred saves (sales order for instance)", "        IF (p_root_table_name = TG_TABLE_NAME) AND (log_record IS NULL) THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": ["CREATE TABLE %%SCHEMA_NAME%%.sage_100_de (_tenant_id VARCHAR(21) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _id SERIAL8 NOT NULL, is_active BOOL NOT NULL, _create_user INT8 NOT NULL, _update_user INT8 NOT NULL, _create_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_stamp TIMESTAMPTZ(3) DEFAULT now() NOT NULL, _update_tick INT8 NOT NULL, _source_id VARCHAR(128) COLLATE \"und-x-icu\" DEFAULT '' NOT NULL, _custom_data JSONB,CONSTRAINT \"sage_100_de_PK\" PRIMARY KEY(_tenant_id,_id));", "COMMENT ON TABLE %%SCHEMA_NAME%%.sage_100_de IS '{", "  \"isSharedByAllTenants\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._tenant_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 21", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._id IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true,", "  \"isAutoIncrement\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de.is_active IS '{", "  \"type\": \"boolean\",", "  \"isSystem\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._create_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._update_user IS '{", "  \"type\": \"reference\",", "  \"isSystem\": true,", "  \"targetTableName\": \"user\",", "  \"isSelfReference\": false", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._create_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._update_stamp IS '{", "  \"type\": \"datetime\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._source_id IS '{", "  \"type\": \"string\",", "  \"isSystem\": true,", "  \"maxLength\": 128", "}';", "COMMENT ON COLUMN %%SCHEMA_NAME%%.sage_100_de._custom_data IS '{", "  \"type\": \"json\",", "  \"isSystem\": true", "}';"]}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER insert_table", "            BEFORE INSERT ON %%SCHEMA_NAME%%.sage_100_de", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.insert_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;", "DO $$ BEGIN", "            CREATE TRIGGER update_table", "            BEFORE UPDATE ON %%SCHEMA_NAME%%.sage_100_de", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.update_table();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "GRANT DELETE, INSERT, UPDATE, SELECT on %%SCHEMA_NAME%%.sage_100_de to xtrem"}, {"isSysPool": true, "sql": "GRANT USAGE, SELECT ON SEQUENCE %%SCHEMA_NAME%%.sage_100_de__id_seq TO xtrem"}, {"isSysPool": true, "sql": ["CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%._insert_sage_100_de(", "    p__custom_data JSONB,", "    p__id INT8,", "    p__source_id TEXT,", "    p_is_active BOOL,", "    p__tenant_id TEXT)", "RETURNS JSONB AS $$", "", "DECLARE l_id INT8;", "DECLARE l_user_id TEXT;", "DECLARE p__create_user INT8;", "DECLARE p__update_user INT8;", "DECLARE ret_1 RECORD;", "DECLARE RET JSONB;", "", "BEGIN", "    IF p__id IS NULL OR p__id <= 0 THEN", "        SELECT nextval((pg_get_serial_sequence('%%SCHEMA_NAME%%.sage_100_de'::text, '_id'::text))::regclass) into l_id;", "    ELSE", "        l_id := p__id;", "    END IF;", "", "    l_user_id := COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '');", "    IF l_user_id = '' THEN l_user_id := NULL; END IF;", "    p__create_user := l_user_id::INT8;", "    p__update_user := l_user_id::INT8;", "    ", "", "    RET := jsonb_build_object('_id', l_id);", "", "    INSERT INTO %%SCHEMA_NAME%%.sage_100_de", "        (_update_user,_create_user,_custom_data,_source_id,_id,is_active,_tenant_id)", "        VALUES (p__update_user,p__create_user,p__custom_data,p__source_id,l_id,p_is_active,p__tenant_id)", "        RETURNING _create_user,_update_user,_create_stamp,_update_stamp,_update_tick,_id INTO ret_1;", "RET := RET || to_jsonb(ret_1);", "", "    RETURN RET;", "END;", "$$ LANGUAGE plpgsql;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sage_100_de ADD CONSTRAINT \"sage_100_de__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sage_100_de__update_user_fk ON %%SCHEMA_NAME%%.sage_100_de IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sage_100_de ADD CONSTRAINT \"sage_100_de__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sage_100_de__create_user_fk ON %%SCHEMA_NAME%%.sage_100_de IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sage_100_de ADD CONSTRAINT \"sage_100_de__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sage_100_de__tenant_id_fk ON %%SCHEMA_NAME%%.sage_100_de IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable PIN code authentication feature", "released", false, "@sage/xtrem-system", false, "sysDeviceToken"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Tags feature", "released", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Auditing feature", "released", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Enable Workflow feature", "released", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable asyncPerPostProcessing print engine feature", "experimental", false, "@sage/xtrem-reporting", false, "asyncPrePostProcessing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Open item page display", "released", true, "@sage/xtrem-structure", false, "openItemPageOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Bill of material revision", "workInProgress", false, "@sage/xtrem-master-data", false, "billOfMaterialRevisionServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "released", false, "@sage/xtrem-master-data", true, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "landedCostStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "released", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Payment tracking option", "released", false, "@sage/xtrem-finance-data", false, "paymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation option", "released", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Network", "workInProgress", false, "@sage/xtrem-sage-network", true, "sageNetworkOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops001LTA-81157\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona, operator_id", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories Role,RoleActivity"}, {"action": "reload_setup_data", "args": {"factory": "Role"}}, {"action": "reload_setup_data", "args": {"factory": "RoleActivity"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT sage_100_de__update_user_fk ON %%SCHEMA_NAME%%.sage_100_de IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sage_100_de__create_user_fk ON %%SCHEMA_NAME%%.sage_100_de IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sage_100_de__tenant_id_fk ON %%SCHEMA_NAME%%.sage_100_de IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"Role": {"metadata": {"rootFactoryName": "Role", "name": "Role", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "is_active", "type": "boolean"}, {"name": "name", "type": "string", "isLocalized": true}, {"name": "description", "type": "string"}, {"name": "id", "type": "string"}, {"name": "is_billing_role", "type": "boolean"}]}, "rows": [["sage", "Y", "{\"en\":\"Administrator - Technical\"}", null, "Admin - Technical", "N"], ["sage", "Y", "{\"en\":\"Administrator\"}", null, "Admin", "N"], ["sage", "Y", "{\"en\":\"Support Access Read-only\"}", null, "Support User Read-only", "N"], ["sage", "Y", "{\"en\":\"Support Access\"}", null, "Support User", "N"], ["sage", "Y", "{\"en\":\"Operational User\"}", null, "Operational User", "Y"], ["sage", "Y", "{\"en\":\"Business User\"}", null, "Business User", "Y"], ["sage", "Y", "{\"en\":\"IT manager\"}", null, "100", null], ["sage", "Y", "{\"en\":\"Sales manager\"}", null, "200", null], ["sage", "Y", "{\"en\":\"Sales administrator\"}", null, "300", null], ["sage", "Y", "{\"en\":\"Purchasing manager\"}", null, "400", null], ["sage", "Y", "{\"en\":\"Buyer\"}", null, "500", null], ["sage", "Y", "{\"en\":\"Inventory manager\"}", null, "600", null], ["sage", "Y", "{\"en\":\"Inventory operator\"}", null, "700", null], ["sage", "Y", "{\"en\":\"Design/Production engineer\"}", null, "800", null], ["sage", "Y", "{\"en\":\"Production manager\"}", null, "900", null], ["sage", "Y", "{\"en\":\"Production operator\"}", null, "1000", null], ["sage", "Y", "{\"en\":\"Controller/Finance manager\",\"base\":\"Controller/Finance manager\",\"en-US\":\"Controller/Finance manager\"}", null, "1100", null]]}, "RoleActivity": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "RoleActivity", "name": "RoleActivity", "naturalKeyColumns": ["_tenant_id", "role", "activity"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "role", "type": "reference", "targetFactoryName": "Role"}, {"name": "activity", "type": "reference", "targetFactoryName": "Activity"}, {"name": "has_all_permissions", "type": "boolean"}, {"name": "permissions", "type": "stringArray"}, {"name": "is_active", "type": "boolean"}], "vitalParentColumn": {"name": "role", "type": "reference", "targetFactoryName": "Role"}}, "rows": [["sage", "100", "Support User", "role", "Y", "[]", "Y"], ["sage", "200", "Support User", "user", "Y", "[]", "Y"], ["sage", "300", "Support User", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Support User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Support User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Support User", "site", "Y", "[]", "Y"], ["sage", "21400", "Support User", "company", "Y", "[]", "Y"], ["sage", "21500", "Support User", "tenant", "Y", "[]", "Y"], ["sage", "21400", "Support User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "21500", "Support User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "22200", "Support User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "22300", "Support User", "sysTag", "Y", "[]", "Y"], ["sage", "100", "Support User Read-only", "role", "N", "[\"read\"]", "Y"], ["sage", "200", "Support User Read-only", "user", "N", "[\"read\"]", "Y"], ["sage", "300", "Support User Read-only", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "400", "Support User Read-only", "groupRoleSite", "N", "[\"read\"]", "Y"], ["sage", "500", "Support User Read-only", "supportAccessHistory", "N", "[\"read\"]", "Y"], ["sage", "600", "Support User Read-only", "site", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "company", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "tenant", "N", "[\"read\"]", "Y"], ["sage", "21400", "Support User Read-only", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "21500", "Support User Read-only", "sysNotificationState", "N", "[\"read\"]", "Y"], ["sage", "22200", "Support User Read-only", "serviceOptionState", "N", "[\"read\"]", "Y"], ["sage", "22300", "Support User Read-only", "sysTag", "N", "[\"read\"]", "Y"], ["sage", "100", "Admin", "role", "Y", "[]", "Y"], ["sage", "200", "Admin", "user", "Y", "[]", "Y"], ["sage", "300", "Admin", "siteGroup", "Y", "[]", "Y"], ["sage", "400", "Admin", "groupRoleSite", "Y", "[]", "Y"], ["sage", "500", "Admin", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "600", "Admin", "site", "Y", "[]", "Y"], ["sage", "700", "Admin", "tenant", "Y", "[]", "Y"], ["sage", "12400", "Admin", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "12200", "Admin", "company", "Y", "[]", "Y"], ["sage", "12500", "Admin", "sysNotificationState", "Y", "[]", "Y"], ["sage", "13800", "Admin", "serviceOptionState", "Y", "[]", "Y"], ["sage", "13900", "Admin", "sysTag", "Y", "[]", "Y"], ["sage", "18600", "Admin", "sysDeviceToken", "Y", "[]", "Y"], ["sage", "13500", "Business User", "role", "Y", "[]", "Y"], ["sage", "13600", "Business User", "user", "Y", "[]", "Y"], ["sage", "13700", "Business User", "siteGroup", "Y", "[]", "Y"], ["sage", "13800", "Business User", "groupRoleSite", "Y", "[]", "Y"], ["sage", "13900", "Business User", "supportAccessHistory", "Y", "[]", "Y"], ["sage", "14200", "Business User", "tenant", "Y", "[]", "Y"], ["sage", "14100", "Business User", "company", "Y", "[]", "Y"], ["sage", "14150", "Business User", "sysNotificationHistory", "Y", "[]", "Y"], ["sage", "14170", "Business User", "sysNotificationState", "Y", "[]", "Y"], ["sage", "14180", "Business User", "site", "Y", "[]", "Y"], ["sage", "17200", "Business User", "serviceOptionState", "Y", "[]", "Y"], ["sage", "21600", "Support User", "customField", "Y", "[]", "Y"], ["sage", "21600", "Support User Read-only", "customField", "N", "[\"read\"]", "Y"], ["sage", "13000", "Admin", "customField", "Y", "[]", "Y"], ["sage", "900", "Business User", "customField", "Y", "[]", "Y"], ["sage", "80", "Operational User", "dashboardActivity", "Y", "[\"read\"]", "Y"], ["sage", "700", "Admin", "dashboardActivity", "Y", "[]", "Y"], ["sage", "21100", "Support User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "1000", "Business User", "dashboardActivity", "Y", "[]", "Y"], ["sage", "100", "Admin", "workflowDefinition", "Y", null, "Y"], ["sage", "18700", "Admin", "workflowProcess", "Y", null, "Y"], ["sage", "700", "Admin", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "1500", "Admin - Technical", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "21100", "Support User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "20900", "Support User Read-only", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "1700", "Business User", "sysJobSchedule", "Y", "[]", "Y"], ["sage", "18500", "Admin", "attachment", "Y", "[]", "Y"], ["sage", "20000", "Support User", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "20100", "Support User", "reportTemplate", "Y", "[]", "Y"], ["sage", "20200", "Support User", "report", "Y", "[]", "Y"], ["sage", "21300", "Support User", "reportResource", "Y", "[]", "Y"], ["sage", "20000", "Support User Read-only", "reportStyleVariable", "N", "[\"read\"]", "Y"], ["sage", "20100", "Support User Read-only", "reportTemplate", "N", "[\"read\"]", "Y"], ["sage", "20200", "Support User Read-only", "report", "N", "[\"read\"]", "Y"], ["sage", "21100", "Support User Read-only", "reportResource", "N", "[\"read\"]", "Y"], ["sage", "12600", "Admin", "reportResource", "Y", "[]", "Y"], ["sage", "12700", "Admin", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "12800", "Admin", "reportTemplate", "Y", "[]", "Y"], ["sage", "12900", "Admin", "report", "Y", "[]", "Y"], ["sage", "5200", "Admin", "sysEmailConfig", "Y", null, "Y"], ["sage", "11800", "Support User", "importData", "Y", "[]", "Y"], ["sage", "12900", "Support User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "11800", "Support User Read-only", "importData", "N", "[\"read\"]", "Y"], ["sage", "12900", "Support User Read-only", "importExportTemplate", "N", "[\"read\"]", "Y"], ["sage", "11900", "Admin", "importData", "Y", "[]", "Y"], ["sage", "12000", "Admin", "importExportTemplate", "Y", "[]", "Y"], ["sage", "1100", "Business User", "importData", "Y", "[]", "Y"], ["sage", "1200", "Business User", "importExportTemplate", "Y", "[]", "Y"], ["sage", "21200", "Support User", "synchronizationState", "Y", "[]", "Y"], ["sage", "21000", "Support User Read-only", "synchronizationState", "N", "[\"read\"]", "Y"], ["sage", "12100", "Admin", "synchronizationState", "Y", "[]", "Y"], ["sage", "1800", "Business User", "synchronizationState", "Y", "[]", "Y"], ["sage", "17200", "100", "groupRoleSite", "Y", "[]", "Y"], ["sage", "200", "100", "user", "Y", "[]", "Y"], ["sage", "15600", "100", "siteGroup", "Y", "[]", "Y"], ["sage", "15700", "100", "site", "Y", "[]", "Y"], ["sage", "15600", "100", "company", "Y", "[]", "Y"], ["sage", "22200", "100", "serviceOptionState", "Y", "[]", "Y"], ["sage", "3700", "200", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "3800", "200", "user", "Y", "[]", "Y"], ["sage", "3900", "200", "site", "N", "[\"read\"]", "Y"], ["sage", "3500", "200", "company", "N", "[\"read\"]", "Y"], ["sage", "3600", "200", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3400", "300", "company", "N", "[\"read\"]", "Y"], ["sage", "3900", "400", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "4000", "400", "site", "N", "[\"read\"]", "Y"], ["sage", "3700", "400", "company", "N", "[\"read\"]", "Y"], ["sage", "3800", "400", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "2500", "500", "company", "N", "[\"read\"]", "Y"], ["sage", "6200", "600", "company", "N", "[\"read\"]", "Y"], ["sage", "6300", "700", "company", "N", "[\"read\"]", "Y"], ["sage", "6400", "600", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6500", "600", "user", "Y", "[]", "Y"], ["sage", "6600", "600", "site", "N", "[\"read\"]", "Y"], ["sage", "6300", "600", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "6500", "800", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6600", "800", "user", "Y", "[]", "Y"], ["sage", "6700", "800", "site", "N", "[\"read\"]", "Y"], ["sage", "6400", "800", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "3500", "900", "siteGroup", "N", "[\"read\"]", "Y"], ["sage", "6700", "900", "user", "Y", "[]", "Y"], ["sage", "6800", "900", "site", "N", "[\"read\"]", "Y"], ["sage", "3400", "900", "sysNotificationHistory", "N", "[\"read\"]", "Y"], ["sage", "530", "1100", "company", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "15700", "100", "customField", "Y", "[]", "Y"], ["sage", "4300", "200", "customField", "Y", "[]", "Y"], ["sage", "4400", "400", "customField", "Y", "[]", "Y"], ["sage", "7000", "600", "customField", "Y", "[]", "Y"], ["sage", "7100", "800", "customField", "Y", "[]", "Y"], ["sage", "7200", "900", "customField", "Y", "[]", "Y"], ["sage", "15500", "100", "dashboardActivity", "N", "[\"read\"]", "Y"], ["sage", "3100", "200", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2100", "300", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3300", "400", "dashboardActivity", "Y", "[]", "Y"], ["sage", "2200", "500", "dashboardActivity", "Y", "[]", "Y"], ["sage", "5900", "600", "dashboardActivity", "Y", "[]", "Y"], ["sage", "6000", "700", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3500", "800", "dashboardActivity", "Y", "[]", "Y"], ["sage", "3000", "900", "dashboardActivity", "N", "[]", "Y"], ["sage", "1900", "1000", "dashboardActivity", "N", "[]", "Y"], ["sage", "10", "1100", "dashboardActivity", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "3000", "200", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3200", "400", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "5400", "600", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3400", "800", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "2900", "900", "sysJobSchedule", "N", "[\"read\"]", "Y"], ["sage", "3900", "200", "reportResource", "Y", "[]", "Y"], ["sage", "4000", "200", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4100", "200", "reportTemplate", "Y", "[]", "Y"], ["sage", "4200", "200", "report", "Y", "[]", "Y"], ["sage", "3500", "300", "reportResource", "Y", "[]", "Y"], ["sage", "3600", "300", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "3700", "300", "reportTemplate", "Y", "[]", "Y"], ["sage", "3800", "300", "report", "Y", "[]", "Y"], ["sage", "4000", "400", "reportResource", "Y", "[]", "Y"], ["sage", "4100", "400", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "4200", "400", "reportTemplate", "Y", "[]", "Y"], ["sage", "4300", "400", "report", "Y", "[]", "Y"], ["sage", "2600", "500", "reportResource", "Y", "[]", "Y"], ["sage", "2700", "500", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2800", "500", "reportTemplate", "Y", "[]", "Y"], ["sage", "2900", "500", "report", "Y", "[]", "Y"], ["sage", "6600", "600", "reportResource", "Y", "[]", "Y"], ["sage", "6700", "600", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6800", "600", "reportTemplate", "Y", "[]", "Y"], ["sage", "6900", "600", "report", "Y", "[]", "Y"], ["sage", "6400", "700", "reportResource", "Y", "[]", "Y"], ["sage", "6500", "700", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6600", "700", "reportTemplate", "Y", "[]", "Y"], ["sage", "6700", "700", "report", "Y", "[]", "Y"], ["sage", "6700", "800", "reportResource", "Y", "[]", "Y"], ["sage", "6800", "800", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "6900", "800", "reportTemplate", "Y", "[]", "Y"], ["sage", "7000", "800", "report", "Y", "[]", "Y"], ["sage", "6800", "900", "reportResource", "Y", "[]", "Y"], ["sage", "6900", "900", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "7000", "900", "reportTemplate", "Y", "[]", "Y"], ["sage", "7100", "900", "report", "Y", "[]", "Y"], ["sage", "2200", "1000", "reportResource", "Y", "[]", "Y"], ["sage", "2300", "1000", "reportStyleVariable", "Y", "[]", "Y"], ["sage", "2400", "1000", "reportTemplate", "Y", "[]", "Y"], ["sage", "2500", "1000", "report", "Y", "[]", "Y"], ["sage", "8800", "100", "legislation", "Y", "[]", "Y"], ["sage", "8850", "100", "country", "Y", "[]", "Y"], ["sage", "10400", "Support User", "baseOptionManagement", "Y", "[]", "Y"], ["sage", "10600", "Support User", "country", "Y", "[]", "Y"], ["sage", "10700", "Support User", "legislation", "Y", "[]", "Y"], ["sage", "10400", "Support User Read-only", "baseOptionManagement", "N", "[\"read\"]", "Y"], ["sage", "10600", "Support User Read-only", "country", "N", "[\"read\"]", "Y"], ["sage", "10700", "Support User Read-only", "legislation", "N", "[\"read\"]", "Y"], ["sage", "5500", "Admin", "baseOptionManagement", "Y", "[]", "Y"], ["sage", "5600", "Admin", "country", "Y", "[]", "Y"], ["sage", "5700", "Admin", "legislation", "Y", "[]", "Y"], ["sage", "12300", "Business User", "baseOptionManagement", "Y", "[]", "Y"], ["sage", "12400", "Business User", "country", "Y", "[]", "Y"], ["sage", "12500", "Business User", "legislation", "Y", "[]", "Y"], ["sage", "520", "1100", "legislation", "N", "[\"read\",\"update\"]", "Y"], ["sage", "3400", "200", "synchronizationState", "N", "[\"read\"]", "Y"], ["sage", "3600", "400", "synchronizationState", "N", "[\"read\"]", "Y"], ["sage", "3200", "200", "importData", "Y", "[]", "Y"], ["sage", "2200", "300", "importData", "Y", "[]", "Y"], ["sage", "3400", "400", "importData", "Y", "[]", "Y"], ["sage", "2300", "500", "importData", "Y", "[]", "Y"], ["sage", "6000", "600", "importData", "Y", "[]", "Y"], ["sage", "6100", "700", "importData", "Y", "[]", "Y"], ["sage", "3600", "800", "importData", "Y", "[]", "Y"], ["sage", "3100", "900", "importData", "N", "[]", "Y"], ["sage", "2000", "1000", "importData", "N", "[]", "Y"], ["sage", "15500", "100", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3300", "200", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2300", "300", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3500", "400", "importExportTemplate", "Y", "[]", "Y"], ["sage", "2400", "500", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6100", "600", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6200", "700", "importExportTemplate", "Y", "[]", "Y"], ["sage", "6300", "800", "importExportTemplate", "Y", "[]", "Y"], ["sage", "3200", "900", "importExportTemplate", "N", "[]", "Y"], ["sage", "2100", "1000", "importExportTemplate", "N", "[]", "Y"], ["sage", "5400", "100", "allergen", "Y", "[]", "Y"], ["sage", "5500", "100", "businessEntity", "Y", "[]", "Y"], ["sage", "5900", "100", "currency", "Y", "[]", "Y"], ["sage", "6000", "100", "customerPriceReason", "Y", "[]", "Y"], ["sage", "6100", "100", "dailyShift", "Y", "[]", "Y"], ["sage", "6200", "100", "deliveryMode", "Y", "[]", "Y"], ["sage", "6500", "100", "incoterm", "Y", "[]", "Y"], ["sage", "6600", "100", "indirectCostOrigin", "N", "[\"read\"]", "Y"], ["sage", "6700", "100", "indirectCostSection", "N", "[\"read\"]", "Y"], ["sage", "6800", "100", "item", "Y", "[]", "Y"], ["sage", "6900", "100", "itemSite", "Y", "[]", "Y"], ["sage", "7200", "100", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "7300", "100", "location", "Y", "[]", "Y"], ["sage", "7400", "100", "locationSequence", "Y", "[]", "Y"], ["sage", "7500", "100", "locationType", "Y", "[]", "Y"], ["sage", "7600", "100", "locationZone", "N", "[\"read\"]", "Y"], ["sage", "7700", "100", "machineResource", "Y", "[]", "Y"], ["sage", "7800", "100", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "7900", "100", "reasonCode", "Y", "[]", "Y"], ["sage", "8000", "100", "sequenceNumber", "Y", "[]", "Y"], ["sage", "8200", "100", "standard", "N", "[\"read\"]", "Y"], ["sage", "8400", "100", "toolResource", "Y", "[]", "Y"], ["sage", "8500", "100", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "8600", "100", "weeklyShift", "N", "[\"read\"]", "Y"], ["sage", "8700", "100", "itemCategory", "Y", "[]", "Y"], ["sage", "8900", "100", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "16800", "100", "customer", "Y", "[]", "Y"], ["sage", "16900", "100", "supplier", "Y", "[]", "Y"], ["sage", "17110", "100", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "1400", "200", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "1500", "200", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1550", "200", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "1560", "200", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "2700", "200", "businessEntity", "Y", "[]", "Y"], ["sage", "2800", "200", "customerPriceReason", "Y", "[]", "Y"], ["sage", "2900", "200", "incoterm", "Y", "[]", "Y"], ["sage", "4400", "200", "customer", "Y", "[]", "Y"], ["sage", "4500", "200", "supplier", "Y", "[]", "Y"], ["sage", "1000", "300", "businessEntity", "Y", "[]", "Y"], ["sage", "1200", "300", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1300", "300", "item", "N", "[\"read\"]", "Y"], ["sage", "1400", "300", "itemSite", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1500", "300", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "3900", "300", "customer", "Y", "[]", "Y"], ["sage", "4000", "300", "supplier", "Y", "[]", "Y"], ["sage", "710", "400", "businessEntity", "Y", "[]", "Y"], ["sage", "900", "400", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "950", "400", "incoterm", "Y", "[]", "Y"], ["sage", "1000", "400", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1100", "400", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "1400", "400", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "1450", "400", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "4500", "400", "customer", "N", "[\"read\"]", "Y"], ["sage", "4600", "400", "supplier", "N", "[\"read\"]", "Y"], ["sage", "5100", "400", "supplierCertificate", "Y", "[]", "Y"], ["sage", "700", "500", "businessEntity", "Y", "[]", "Y"], ["sage", "800", "500", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "900", "500", "item", "N", "[\"read\"]", "Y"], ["sage", "1000", "500", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "3000", "500", "customer", "N", "[\"read\"]", "Y"], ["sage", "3100", "500", "supplier", "N", "[\"read\"]", "Y"], ["sage", "5100", "500", "supplierCertificate", "Y", "[]", "Y"], ["sage", "1700", "700", "itemSite", "Y", "[]", "Y"], ["sage", "2000", "700", "locationType", "N", "[\"read\"]", "Y"], ["sage", "5800", "700", "allergen", "N", "[\"read\"]", "Y"], ["sage", "5900", "700", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1050", "800", "allergen", "Y", "[]", "Y"], ["sage", "1060", "800", "capabilityLevel", "Y", "[]", "Y"], ["sage", "1070", "800", "container", "Y", "[]", "Y"], ["sage", "1080", "800", "costCategory", "Y", "[]", "Y"], ["sage", "1100", "800", "dailyShift", "N", "[\"read\"]", "Y"], ["sage", "1200", "800", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1300", "800", "groupResource", "Y", "[]", "Y"], ["sage", "1350", "800", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "1360", "800", "indirectCostSection", "Y", "[]", "Y"], ["sage", "1400", "800", "item", "Y", "[]", "Y"], ["sage", "1500", "800", "itemSite", "Y", "[]", "Y"], ["sage", "1700", "800", "labourResource", "Y", "[]", "Y"], ["sage", "1750", "800", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "1800", "800", "machineResource", "Y", "[]", "Y"], ["sage", "1900", "800", "shiftDetail", "Y", "[]", "Y"], ["sage", "2000", "800", "standard", "Y", "[]", "Y"], ["sage", "2200", "800", "toolResource", "Y", "[]", "Y"], ["sage", "2300", "800", "weeklyShift", "N", "[\"read\"]", "Y"], ["sage", "2400", "800", "itemCategory", "Y", "[]", "Y"], ["sage", "7200", "800", "customer", "N", "[\"read\"]", "Y"], ["sage", "7300", "800", "supplier", "N", "[\"read\"]", "Y"], ["sage", "17110", "800", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "950", "900", "capabilityLevel", "Y", "[]", "Y"], ["sage", "960", "900", "container", "Y", "[]", "Y"], ["sage", "1000", "900", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "1100", "900", "groupResource", "Y", "[]", "Y"], ["sage", "1200", "900", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1300", "900", "itemSite", "N", "[\"read\",\"update\"]", "Y"], ["sage", "1500", "900", "labourResource", "Y", "[]", "Y"], ["sage", "1600", "900", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "1700", "900", "machineResource", "Y", "[]", "Y"], ["sage", "1800", "900", "standard", "Y", "[]", "Y"], ["sage", "2000", "900", "toolResource", "Y", "[]", "Y"], ["sage", "2050", "900", "location", "Y", "[]", "Y"], ["sage", "2060", "900", "weeklyShift", "Y", "[]", "Y"], ["sage", "7300", "900", "customer", "N", "[\"read\"]", "Y"], ["sage", "7400", "900", "supplier", "N", "[\"read\"]", "Y"], ["sage", "17110", "900", "bomRevisionSequence", "N", "[\"read\"]", "Y"], ["sage", "1200", "1000", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "1400", "1000", "licensePlateNumber", "N", "[\"read\"]", "Y"], ["sage", "1500", "1000", "machineResource", "N", "[\"read\"]", "Y"], ["sage", "17110", "1000", "bomRevisionSequence", "N", "[\"read\"]", "Y"], ["sage", "6300", "Support User", "allergen", "Y", "[]", "Y"], ["sage", "6400", "Support User", "businessEntity", "Y", "[]", "Y"], ["sage", "6500", "Support User", "capabilityLevel", "Y", "[]", "Y"], ["sage", "6700", "Support User", "container", "Y", "[]", "Y"], ["sage", "6800", "Support User", "costCategory", "Y", "[]", "Y"], ["sage", "6900", "Support User", "currency", "Y", "[]", "Y"], ["sage", "7000", "Support User", "customerPriceReason", "Y", "[]", "Y"], ["sage", "7200", "Support User", "dailyShift", "Y", "[]", "Y"], ["sage", "7300", "Support User", "deliveryMode", "Y", "[]", "Y"], ["sage", "7400", "Support User", "ghsClassification", "Y", "[]", "Y"], ["sage", "7500", "Support User", "groupResource", "Y", "[]", "Y"], ["sage", "7600", "Support User", "incoterm", "Y", "[]", "Y"], ["sage", "7700", "Support User", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "7800", "Support User", "indirectCostSection", "Y", "[]", "Y"], ["sage", "8100", "Support User", "itemSite", "Y", "[]", "Y"], ["sage", "8200", "Support User", "item", "Y", "[]", "Y"], ["sage", "8300", "Support User", "labourResource", "Y", "[]", "Y"], ["sage", "8400", "Support User", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "8600", "Support User", "locationType", "Y", "[]", "Y"], ["sage", "8700", "Support User", "locationZone", "Y", "[]", "Y"], ["sage", "8800", "Support User", "location", "Y", "[]", "Y"], ["sage", "8900", "Support User", "machineResource", "Y", "[]", "Y"], ["sage", "9000", "Support User", "paymentTerm", "Y", "[]", "Y"], ["sage", "9100", "Support User", "reasonCode", "Y", "[]", "Y"], ["sage", "9200", "Support User", "sequenceNumberAssignment", "Y", "[]", "Y"], ["sage", "9300", "Support User", "sequenceNumber", "Y", "[]", "Y"], ["sage", "9400", "Support User", "shiftDetail", "Y", "[]", "Y"], ["sage", "9500", "Support User", "standard", "Y", "[]", "Y"], ["sage", "9700", "Support User", "toolResource", "Y", "[]", "Y"], ["sage", "9800", "Support User", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "9900", "Support User", "weeklyShift", "Y", "[]", "Y"], ["sage", "9950", "Support User", "locationSequence", "Y", "[]", "Y"], ["sage", "9960", "Support User", "itemCategory", "Y", "[]", "Y"], ["sage", "9970", "Support User", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "21700", "Support User", "customer", "Y", "[]", "Y"], ["sage", "21800", "Support User", "supplier", "Y", "[]", "Y"], ["sage", "17110", "Support User", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "22300", "Support User", "supplierCertificate", "Y", "[]", "Y"], ["sage", "6300", "Support User Read-only", "allergen", "N", "[\"read\"]", "Y"], ["sage", "6400", "Support User Read-only", "businessEntity", "N", "[\"read\"]", "Y"], ["sage", "6500", "Support User Read-only", "capabilityLevel", "N", "[\"read\"]", "Y"], ["sage", "6700", "Support User Read-only", "container", "N", "[\"read\"]", "Y"], ["sage", "6800", "Support User Read-only", "costCategory", "N", "[\"read\"]", "Y"], ["sage", "6900", "Support User Read-only", "currency", "N", "[\"read\"]", "Y"], ["sage", "7000", "Support User Read-only", "customerPriceReason", "N", "[\"read\"]", "Y"], ["sage", "7200", "Support User Read-only", "dailyShift", "N", "[\"read\"]", "Y"], ["sage", "7300", "Support User Read-only", "deliveryMode", "N", "[\"read\"]", "Y"], ["sage", "7400", "Support User Read-only", "ghsClassification", "N", "[\"read\"]", "Y"], ["sage", "7500", "Support User Read-only", "groupResource", "N", "[\"read\"]", "Y"], ["sage", "7600", "Support User Read-only", "incoterm", "N", "[\"read\"]", "Y"], ["sage", "7700", "Support User Read-only", "indirectCostOrigin", "N", "[\"read\"]", "Y"], ["sage", "7800", "Support User Read-only", "indirectCostSection", "N", "[\"read\"]", "Y"], ["sage", "8100", "Support User Read-only", "itemSite", "N", "[\"read\"]", "Y"], ["sage", "8200", "Support User Read-only", "item", "N", "[\"read\"]", "Y"], ["sage", "8300", "Support User Read-only", "labourResource", "N", "[\"read\"]", "Y"], ["sage", "8400", "Support User Read-only", "licensePlateNumber", "N", "[\"read\"]", "Y"], ["sage", "8600", "Support User Read-only", "locationType", "N", "[\"read\"]", "Y"], ["sage", "8700", "Support User Read-only", "locationZone", "N", "[\"read\"]", "Y"], ["sage", "8800", "Support User Read-only", "location", "N", "[\"read\"]", "Y"], ["sage", "8900", "Support User Read-only", "machineResource", "N", "[\"read\"]", "Y"], ["sage", "9000", "Support User Read-only", "paymentTerm", "N", "[\"read\"]", "Y"], ["sage", "9100", "Support User Read-only", "reasonCode", "N", "[\"read\"]", "Y"], ["sage", "9200", "Support User Read-only", "sequenceNumberAssignment", "N", "[\"read\"]", "Y"], ["sage", "9300", "Support User Read-only", "sequenceNumber", "N", "[\"read\"]", "Y"], ["sage", "9400", "Support User Read-only", "shiftDetail", "N", "[\"read\"]", "Y"], ["sage", "9500", "Support User Read-only", "standard", "N", "[\"read\"]", "Y"], ["sage", "9700", "Support User Read-only", "toolResource", "N", "[\"read\"]", "Y"], ["sage", "9800", "Support User Read-only", "unitOfMeasure", "N", "[\"read\"]", "Y"], ["sage", "9900", "Support User Read-only", "weeklyShift", "N", "[\"read\"]", "Y"], ["sage", "9950", "Support User Read-only", "locationSequence", "Y", "[]", "Y"], ["sage", "9960", "Support User Read-only", "itemCategory", "Y", "[\"read\"]", "Y"], ["sage", "21700", "Support User Read-only", "customer", "N", "[\"read\"]", "Y"], ["sage", "21800", "Support User Read-only", "supplier", "N", "[\"read\"]", "Y"], ["sage", "17110", "Support User Read-only", "bomRevisionSequence", "N", "[\"read\"]", "Y"], ["sage", "22300", "Support User Read-only", "supplierCertificate", "N", "[\"read\"]", "Y"], ["sage", "7200", "Admin", "allergen", "Y", "[]", "Y"], ["sage", "7300", "Admin", "businessEntity", "Y", "[]", "Y"], ["sage", "7400", "Admin", "capabilityLevel", "Y", "[]", "Y"], ["sage", "7500", "Admin", "container", "Y", "[]", "Y"], ["sage", "7600", "Admin", "costCategory", "Y", "[]", "Y"], ["sage", "7700", "Admin", "currency", "Y", "[]", "Y"], ["sage", "7800", "Admin", "customerPriceReason", "Y", "[]", "Y"], ["sage", "7900", "Admin", "dailyShift", "Y", "[]", "Y"], ["sage", "8000", "Admin", "deliveryMode", "Y", "[]", "Y"], ["sage", "8100", "Admin", "ghsClassification", "Y", "[]", "Y"], ["sage", "8200", "Admin", "groupResource", "Y", "[]", "Y"], ["sage", "8300", "Admin", "incoterm", "Y", "[]", "Y"], ["sage", "8400", "Admin", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "8500", "Admin", "indirectCostSection", "Y", "[]", "Y"], ["sage", "8600", "Admin", "itemSite", "Y", "[]", "Y"], ["sage", "8700", "Admin", "item", "Y", "[]", "Y"], ["sage", "8800", "Admin", "labourResource", "Y", "[]", "Y"], ["sage", "8900", "Admin", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "9000", "Admin", "locationType", "Y", "[]", "Y"], ["sage", "9100", "Admin", "locationZone", "Y", "[]", "Y"], ["sage", "9200", "Admin", "location", "Y", "[]", "Y"], ["sage", "9300", "Admin", "machineResource", "Y", "[]", "Y"], ["sage", "9400", "Admin", "paymentTerm", "Y", "[]", "Y"], ["sage", "9500", "Admin", "reasonCode", "Y", "[]", "Y"], ["sage", "9600", "Admin", "sequenceNumberAssignment", "Y", "[]", "Y"], ["sage", "9700", "Admin", "sequenceNumber", "Y", "[]", "Y"], ["sage", "9800", "Admin", "shiftDetail", "Y", "[]", "Y"], ["sage", "9900", "Admin", "standard", "Y", "[]", "Y"], ["sage", "10000", "Admin", "toolResource", "Y", "[]", "Y"], ["sage", "10100", "Admin", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "10200", "Admin", "weeklyShift", "Y", "[]", "Y"], ["sage", "10300", "Admin", "locationSequence", "Y", "[]", "Y"], ["sage", "10400", "Admin", "itemCategory", "Y", "[]", "Y"], ["sage", "10500", "Admin", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "13100", "Admin", "customer", "Y", "[]", "Y"], ["sage", "13200", "Admin", "supplier", "Y", "[]", "Y"], ["sage", "17110", "Admin", "bomRevisionSequence", "Y", "[]", "Y"], ["sage", "13900", "Admin", "supplierCertificate", "Y", "[]", "Y"], ["sage", "18400", "Admin", "employee", "Y", "[]", "Y"], ["sage", "8100", "Business User", "allergen", "Y", "[]", "Y"], ["sage", "8200", "Business User", "businessEntity", "Y", "[]", "Y"], ["sage", "8300", "Business User", "capabilityLevel", "Y", "[]", "Y"], ["sage", "8400", "Business User", "container", "Y", "[]", "Y"], ["sage", "8500", "Business User", "costCategory", "Y", "[]", "Y"], ["sage", "8600", "Business User", "currency", "Y", "[]", "Y"], ["sage", "8700", "Business User", "customerPriceReason", "Y", "[]", "Y"], ["sage", "9000", "Business User", "dailyShift", "Y", "[]", "Y"], ["sage", "9100", "Business User", "deliveryMode", "Y", "[]", "Y"], ["sage", "9200", "Business User", "ghsClassification", "Y", "[]", "Y"], ["sage", "9300", "Business User", "groupResource", "Y", "[]", "Y"], ["sage", "9400", "Business User", "incoterm", "Y", "[]", "Y"], ["sage", "9500", "Business User", "indirectCostOrigin", "Y", "[]", "Y"], ["sage", "9600", "Business User", "indirectCostSection", "Y", "[]", "Y"], ["sage", "9700", "Business User", "itemSite", "Y", "[]", "Y"], ["sage", "9800", "Business User", "item", "Y", "[]", "Y"], ["sage", "9900", "Business User", "labourResource", "Y", "[]", "Y"], ["sage", "10000", "Business User", "licensePlateNumber", "Y", "[]", "Y"], ["sage", "10100", "Business User", "locationType", "Y", "[]", "Y"], ["sage", "10200", "Business User", "locationZone", "Y", "[]", "Y"], ["sage", "10300", "Business User", "location", "Y", "[]", "Y"], ["sage", "10400", "Business User", "machineResource", "Y", "[]", "Y"], ["sage", "10500", "Business User", "paymentTerm", "Y", "[]", "Y"], ["sage", "10600", "Business User", "reasonCode", "Y", "[]", "Y"], ["sage", "10700", "Business User", "sequenceNumberAssignment", "Y", "[]", "Y"], ["sage", "10800", "Business User", "sequenceNumber", "Y", "[]", "Y"], ["sage", "10900", "Business User", "shiftDetail", "Y", "[]", "Y"], ["sage", "11000", "Business User", "standard", "Y", "[]", "Y"], ["sage", "11100", "Business User", "toolResource", "Y", "[]", "Y"], ["sage", "11200", "Business User", "unitOfMeasure", "Y", "[]", "Y"], ["sage", "11300", "Business User", "weeklyShift", "Y", "[]", "Y"], ["sage", "11400", "Business User", "locationSequence", "Y", "[]", "Y"], ["sage", "11500", "Business User", "itemCategory", "Y", "[]", "Y"], ["sage", "11600", "Business User", "customerSupplierCategory", "Y", "[]", "Y"], ["sage", "11650", "Business User", "customer", "Y", "[]", "Y"], ["sage", "11700", "Business User", "supplier", "Y", "[]", "Y"], ["sage", "17110", "Business User", "bomRevisionSequence", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "17200", "Business User", "supplierCertificate", "Y", "[]", "Y"], ["sage", "290", "1100", "businessEntity", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "300", "1100", "costCategory", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "310", "1100", "currency", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "320", "1100", "customerPriceReason", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "330", "1100", "customerSupplierCategory", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "340", "1100", "indirectCostOrigin", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "350", "1100", "indirectCostSection", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "360", "1100", "item", "N", "[\"read\",\"update\"]", "Y"], ["sage", "370", "1100", "itemSite", "N", "[\"read\",\"update\",\"delete\"]", "Y"], ["sage", "380", "1100", "paymentTerm", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "390", "1100", "sequenceNumber", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "400", "1100", "sequenceNumberAssignment", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "8100", "1100", "supplierCertificate", "Y", "[]", "Y"], ["sage", "2200", "200", "stock", "N", "[\"read\"]", "Y"], ["sage", "2000", "300", "stock", "N", "[\"read\"]", "Y"], ["sage", "2700", "400", "stock", "N", "[\"read\"]", "Y"], ["sage", "2100", "500", "stock", "N", "[\"read\"]", "Y"], ["sage", "2800", "600", "stockStatus", "Y", "[]", "Y"], ["sage", "1000", "600", "stock", "N", "[\"read\"]", "Y"], ["sage", "1050", "600", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "2400", "700", "stockStatus", "N", "[\"read\"]", "Y"], ["sage", "2600", "700", "stock", "N", "[\"read\"]", "Y"], ["sage", "2700", "700", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "800", "900", "stock", "N", "[\"read\"]", "Y"], ["sage", "800", "1000", "stock", "N", "[\"read\"]", "Y"], ["sage", "10100", "Support User", "stockJournal", "Y", "[]", "Y"], ["sage", "10200", "Support User", "stockStatus", "Y", "[]", "Y"], ["sage", "10300", "Support User", "stock", "Y", "[]", "Y"], ["sage", "10350", "Support User", "allocationResult", "Y", "[]", "Y"], ["sage", "10100", "Support User Read-only", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "10200", "Support User Read-only", "stockStatus", "N", "[\"read\"]", "Y"], ["sage", "10300", "Support User Read-only", "stock", "N", "[\"read\"]", "Y"], ["sage", "10350", "Support User Read-only", "allocationResult", "N", "[\"read\"]", "Y"], ["sage", "250", "Operational User", "stockJournal", "N", "[\"read\"]", "Y"], ["sage", "255", "Operational User", "stock", "N", "[\"read\"]", "Y"], ["sage", "5800", "Admin", "stockStatus", "Y", "[]", "Y"], ["sage", "5900", "Admin", "stockJournal", "Y", "[]", "Y"], ["sage", "6000", "Admin", "stock", "Y", "[]", "Y"], ["sage", "6100", "Admin", "allocationResult", "Y", "[]", "Y"], ["sage", "11900", "Business User", "stockStatus", "Y", "[]", "Y"], ["sage", "12000", "Business User", "stockJournal", "Y", "[]", "Y"], ["sage", "12100", "Business User", "stock", "Y", "[]", "Y"], ["sage", "12200", "Business User", "allocationResult", "Y", "[]", "Y"], ["sage", "8900", "100", "countryGroup", "Y", "[]", "Y"], ["sage", "9000", "100", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "9100", "100", "tax", "N", "[\"read\",\"create\",\"update\",\"delete\",\"getTaxValues\"]", "Y"], ["sage", "9200", "100", "taxCategory", "N", "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "9300", "100", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "10000", "100", "taxSolution", "Y", "[]", "Y"], ["sage", "10100", "100", "taxZone", "Y", "[]", "Y"], ["sage", "1700", "200", "tax", "N", "[\"read\"]", "Y"], ["sage", "1800", "200", "taxCategory", "N", "[\"read\"]", "Y"], ["sage", "1900", "200", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "2000", "200", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "2100", "200", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "2200", "400", "tax", "N", "[\"read\"]", "Y"], ["sage", "2300", "400", "taxCategory", "N", "[\"read\"]", "Y"], ["sage", "2400", "400", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "2500", "400", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "2600", "400", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "5600", "700", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "5700", "700", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "10800", "Support User", "countryGroup", "Y", "[]", "Y"], ["sage", "10900", "Support User", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "11000", "Support User", "taxCategory", "Y", "[]", "Y"], ["sage", "11100", "Support User", "taxDetermination", "Y", "[]", "Y"], ["sage", "11200", "Support User", "taxSolution", "Y", "[]", "Y"], ["sage", "11300", "Support User", "tax", "Y", "[]", "Y"], ["sage", "11350", "Support User", "taxZone", "Y", "[]", "Y"], ["sage", "10800", "Support User Read-only", "countryGroup", "N", "[\"read\"]", "Y"], ["sage", "10900", "Support User Read-only", "itemTaxGroup", "N", "[\"read\"]", "Y"], ["sage", "11000", "Support User Read-only", "taxCategory", "N", "[\"read\"]", "Y"], ["sage", "11100", "Support User Read-only", "taxDetermination", "N", "[\"read\"]", "Y"], ["sage", "11200", "Support User Read-only", "taxSolution", "N", "[\"read\"]", "Y"], ["sage", "11300", "Support User Read-only", "tax", "N", "[\"read\"]", "Y"], ["sage", "11350", "Support User Read-only", "taxZone", "N", "[\"read\"]", "Y"], ["sage", "4700", "Admin", "countryGroup", "Y", "[]", "Y"], ["sage", "4800", "Admin", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "4900", "Admin", "tax", "Y", "[]", "Y"], ["sage", "5000", "Admin", "taxCategory", "Y", "[]", "Y"], ["sage", "5100", "Admin", "taxDetermination", "Y", "[]", "Y"], ["sage", "5200", "Admin", "taxSolution", "Y", "[]", "Y"], ["sage", "5200", "Admin", "taxZone", "Y", "[]", "Y"], ["sage", "12600", "Business User", "countryGroup", "Y", "[]", "Y"], ["sage", "12700", "Business User", "itemTaxGroup", "Y", "[]", "Y"], ["sage", "12800", "Business User", "tax", "Y", "[]", "Y"], ["sage", "12900", "Business User", "taxCategory", "Y", "[]", "Y"], ["sage", "13000", "Business User", "taxDetermination", "Y", "[]", "Y"], ["sage", "13100", "Business User", "taxSolution", "Y", "[]", "Y"], ["sage", "13200", "Business User", "taxZone", "Y", "[]", "Y"], ["sage", "540", "1100", "countryGroup", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "550", "1100", "itemTaxGroup", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "560", "1100", "tax", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "570", "1100", "taxCategory", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "580", "1100", "taxDetermination", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "590", "1100", "taxSolution", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "600", "1100", "taxZone", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "3900", "100", "account", "Y", "[]", "Y"], ["sage", "4100", "100", "attribute", "Y", "[]", "Y"], ["sage", "4200", "100", "attributeType", "Y", "[]", "Y"], ["sage", "4250", "100", "datevConfiguration", "Y", "[]", "Y"], ["sage", "4300", "100", "dimension", "Y", "[]", "Y"], ["sage", "4400", "100", "dimensionType", "Y", "[]", "Y"], ["sage", "4000", "100", "journalEntryType", "Y", "[]", "Y"], ["sage", "4500", "100", "journal", "Y", "[]", "Y"], ["sage", "4600", "100", "postingClass", "Y", "[]", "Y"], ["sage", "4700", "100", "postingClassDefinition", "N", "[\"read\",\"update\"]", "Y"], ["sage", "4750", "100", "bankAccount", "Y", "[]", "Y"], ["sage", "2300", "200", "attribute", "N", "[\"read\"]", "Y"], ["sage", "2400", "200", "dimension", "N", "[\"read\"]", "Y"], ["sage", "2500", "200", "journal", "N", "[\"read\"]", "Y"], ["sage", "2600", "200", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "2800", "400", "attribute", "N", "[\"read\"]", "Y"], ["sage", "2900", "400", "dimension", "N", "[\"read\"]", "Y"], ["sage", "3000", "400", "journal", "N", "[\"read\"]", "Y"], ["sage", "3100", "400", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "5500", "600", "attribute", "N", "[\"read\"]", "Y"], ["sage", "5600", "600", "dimension", "N", "[\"read\"]", "Y"], ["sage", "5700", "600", "journal", "N", "[\"read\"]", "Y"], ["sage", "5800", "600", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "2500", "900", "attribute", "N", "[\"read\"]", "Y"], ["sage", "2600", "900", "dimension", "N", "[\"read\"]", "Y"], ["sage", "2700", "900", "journal", "N", "[\"read\"]", "Y"], ["sage", "2800", "900", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "5400", "Support User", "account", "Y", "[]", "Y"], ["sage", "5500", "Support User", "attributeType", "Y", "[]", "Y"], ["sage", "5600", "Support User", "attribute", "Y", "[]", "Y"], ["sage", "5650", "Support User", "datevConfiguration", "Y", "[]", "Y"], ["sage", "5700", "Support User", "dimensionType", "Y", "[]", "Y"], ["sage", "5800", "Support User", "dimension", "Y", "[]", "Y"], ["sage", "5900", "Support User", "journalEntryType", "Y", "[]", "Y"], ["sage", "6000", "Support User", "journal", "Y", "[]", "Y"], ["sage", "6100", "Support User", "postingClassDefinition", "Y", "[]", "Y"], ["sage", "6200", "Support User", "postingClass", "Y", "[]", "Y"], ["sage", "6250", "Support User", "bankAccount", "Y", "[]", "Y"], ["sage", "5400", "Support User Read-only", "account", "N", "[\"read\"]", "Y"], ["sage", "5500", "Support User Read-only", "attributeType", "N", "[\"read\"]", "Y"], ["sage", "5600", "Support User Read-only", "attribute", "N", "[\"read\"]", "Y"], ["sage", "5650", "Support User Read-only", "datevConfiguration", "N", "[\"read\"]", "Y"], ["sage", "5700", "Support User Read-only", "dimensionType", "N", "[\"read\"]", "Y"], ["sage", "5800", "Support User Read-only", "dimension", "N", "[\"read\"]", "Y"], ["sage", "5900", "Support User Read-only", "journalEntryType", "N", "[\"read\"]", "Y"], ["sage", "6000", "Support User Read-only", "journal", "N", "[\"read\"]", "Y"], ["sage", "6100", "Support User Read-only", "postingClassDefinition", "N", "[\"read\"]", "Y"], ["sage", "6200", "Support User Read-only", "postingClass", "N", "[\"read\"]", "Y"], ["sage", "6250", "Support User Read-only", "bankAccount", "N", "[\"read\"]", "Y"], ["sage", "1800", "Operational User", "account", "N", "[\"read\"]", "Y"], ["sage", "6200", "Admin", "account", "Y", "[]", "Y"], ["sage", "6300", "Admin", "attribute", "Y", "[]", "Y"], ["sage", "6400", "Admin", "attributeType", "Y", "[]", "Y"], ["sage", "6450", "Admin", "datevConfiguration", "Y", "[]", "Y"], ["sage", "6500", "Admin", "dimension", "Y", "[]", "Y"], ["sage", "6600", "Admin", "dimensionType", "Y", "[]", "Y"], ["sage", "6700", "Admin", "journalEntryType", "Y", "[]", "Y"], ["sage", "6800", "Admin", "journal", "Y", "[]", "Y"], ["sage", "6900", "Admin", "postingClass", "Y", "[]", "Y"], ["sage", "7000", "Admin", "postingClassDefinition", "Y", "[]", "Y"], ["sage", "7100", "Admin", "bankAccount", "Y", "[]", "Y"], ["sage", "18100", "Admin", "dimensionDefinitionLevelAndDefault", "Y", "[]", "Y"], ["sage", "18200", "Admin", "companyDefaultAttribute", "Y", "[]", "Y"], ["sage", "18300", "Admin", "companyDefaultDimension", "Y", "[]", "Y"], ["sage", "7100", "Business User", "account", "Y", "[]", "Y"], ["sage", "7200", "Business User", "attribute", "Y", "[]", "Y"], ["sage", "7300", "Business User", "attributeType", "Y", "[]", "Y"], ["sage", "7400", "Business User", "dimension", "Y", "[]", "Y"], ["sage", "7500", "Business User", "dimensionType", "Y", "[]", "Y"], ["sage", "7600", "Business User", "journalEntryType", "Y", "[]", "Y"], ["sage", "7700", "Business User", "journal", "Y", "[]", "Y"], ["sage", "7800", "Business User", "postingClass", "Y", "[]", "Y"], ["sage", "7900", "Business User", "postingClassDefinition", "Y", "[]", "Y"], ["sage", "8000", "Business User", "bankAccount", "Y", "[]", "Y"], ["sage", "120", "1100", "account", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "130", "1100", "attribute", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "140", "1100", "attributeType", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "150", "1100", "companyDefaultAttribute", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "160", "1100", "companyDefaultDimension", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "170", "1100", "datevConfiguration", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "180", "1100", "dimension", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "190", "1100", "dimensionDefinitionLevelAndDefault", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "200", "1100", "dimensionType", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "210", "1100", "journal", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "220", "1100", "journalEntryType", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "230", "1100", "postingClass", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "240", "1100", "postingClassDefinition", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "8000", "1100", "bankAccount", "Y", "[]", "Y"], ["sage", "8000", "Admin - Technical", "bankAccount", "Y", "[]", "Y"], ["sage", "15800", "100", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "15900", "100", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "16000", "100", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "16100", "100", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "16150", "100", "datevExport", "Y", "[]", "Y"], ["sage", "16200", "100", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "16300", "100", "journalEntry", "Y", "[]", "Y"], ["sage", "16400", "100", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "16450", "100", "payment", "Y", "[]", "Y"], ["sage", "16500", "100", "receipt", "Y", "[]", "Y"], ["sage", "3100", "100", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "2700", "100", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "1400", "200", "accountsReceivableInvoice", "N", "[\"read\"]", "Y"], ["sage", "1500", "200", "accountsReceivablePayment", "N", "[\"read\"]", "Y"], ["sage", "1600", "200", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "1700", "200", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "1800", "400", "accountsPayableInvoice", "N", "[\"read\"]", "Y"], ["sage", "1900", "400", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "1700", "400", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "1900", "500", "accountsPayableInvoice", "N", "[\"read\"]", "Y"], ["sage", "5400", "700", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "5500", "700", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "2300", "900", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "2400", "900", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "2400", "Support User", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "2500", "Support User", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "2600", "Support User", "journalEntry", "Y", "[]", "Y"], ["sage", "2700", "Support User", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "2800", "Support User", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "2850", "Support User", "datevExport", "Y", "[]", "Y"], ["sage", "2900", "Support User", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "3000", "Support User", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "3050", "Support User", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "3100", "Support User", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "3150", "Support User", "payment", "Y", "[]", "Y"], ["sage", "3200", "Support User", "receipt", "Y", "[]", "Y"], ["sage", "3300", "Support User", "accountingInterfaceListener", "Y", "[]", "Y"], ["sage", "2400", "Support User Read-only", "accountsPayableInvoice", "N", "[\"read\"]", "Y"], ["sage", "2500", "Support User Read-only", "accountsReceivableInvoice", "N", "[\"read\"]", "Y"], ["sage", "2550", "Support User Read-only", "datevExport", "N", "[\"read\"]", "Y"], ["sage", "2600", "Support User Read-only", "journalEntry", "N", "[\"read\"]", "Y"], ["sage", "2650", "Support User Read-only", "accountsPayableOpenItem", "N", "[\"read\"]", "Y"], ["sage", "2700", "Support User Read-only", "accountsReceivableOpenItem", "N", "[\"read\"]", "Y"], ["sage", "2800", "Support User Read-only", "payment", "N", "[\"read\"]", "Y"], ["sage", "2900", "Support User Read-only", "receipt", "N", "[\"read\"]", "Y"], ["sage", "1100", "Operational User", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "1200", "Operational User", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "1300", "Operational User", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "1400", "Operational User", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "1500", "Operational User", "journalEntry", "Y", "[]", "Y"], ["sage", "1600", "Operational User", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "4000", "Admin", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "4100", "Admin", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "4200", "Admin", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "4300", "Admin", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "4350", "Admin", "datevExport", "Y", "[]", "Y"], ["sage", "4400", "Admin", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "4500", "Admin", "journalEntry", "Y", "[]", "Y"], ["sage", "4600", "Admin", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "4650", "Admin", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "4700", "Admin", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "4750", "Admin", "payment", "Y", "[]", "Y"], ["sage", "4800", "Admin", "receipt", "Y", "[]", "Y"], ["sage", "4900", "Admin", "accountingInterfaceListener", "Y", "[]", "Y"], ["sage", "3500", "Business User", "accountsPayableInvoice", "Y", "[]", "Y"], ["sage", "3600", "Business User", "accountsReceivableAdvance", "Y", "[]", "Y"], ["sage", "3700", "Business User", "accountsReceivableInvoice", "Y", "[]", "Y"], ["sage", "3800", "Business User", "accountsReceivablePayment", "Y", "[]", "Y"], ["sage", "3900", "Business User", "generateJournalEntries", "Y", "[]", "Y"], ["sage", "4000", "Business User", "journalEntry", "Y", "[]", "Y"], ["sage", "4100", "Business User", "journalEntryInquiry", "Y", "[]", "Y"], ["sage", "60", "1100", "accountsPayableInvoice", "N", "[\"read\",\"post\"]", "Y"], ["sage", "70", "1100", "accountsReceivableInvoice", "N", "[\"read\",\"post\",\"updateOpenItemFromIntacct\"]", "Y"], ["sage", "80", "1100", "datevExport", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "90", "1100", "generateJournalEntries", "N", "[\"accountingIntegration\"]", "Y"], ["sage", "100", "1100", "journalEntry", "N", "[\"read\",\"manage\",\"post\"]", "Y"], ["sage", "110", "1100", "journalEntryInquiry", "N", "[\"read\"]", "Y"], ["sage", "111", "1100", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "112", "1100", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "120", "1100", "payment", "Y", "[]", "Y"], ["sage", "125", "1100", "receipt", "Y", "[]", "Y"], ["sage", "130", "1100", "accountingInterfaceListener", "Y", "[]", "Y"], ["sage", "112", "Admin - Technical", "accountsPayableOpenItem", "Y", "[]", "Y"], ["sage", "115", "Admin - Technical", "accountsReceivableOpenItem", "Y", "[]", "Y"], ["sage", "120", "Admin - Technical", "payment", "Y", "[]", "Y"], ["sage", "130", "Admin - Technical", "receipt", "Y", "[]", "Y"], ["sage", "16600", "100", "purchaseInvoice", "Y", "[\"read\",\"post\"]", "Y"], ["sage", "16700", "100", "purchaseOrder", "Y", "[]", "Y"], ["sage", "1100", "200", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "1200", "200", "purchaseOrder", "N", "[\"read\"]", "Y"], ["sage", "1700", "300", "purchaseRequisition", "N", "[\"read\"]", "Y"], ["sage", "1800", "300", "purchaseOrder", "N", "[\"read\"]", "Y"], ["sage", "100", "400", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "200", "400", "purchaseReturn", "Y", "[]", "Y"], ["sage", "300", "400", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "400", "400", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "500", "400", "purchaseOrder", "Y", "[]", "Y"], ["sage", "600", "400", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "100", "500", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "200", "500", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "300", "500", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "400", "500", "purchaseOrder", "Y", "[]", "Y"], ["sage", "500", "500", "purchaseReturn", "Y", "[]", "Y"], ["sage", "600", "500", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "600", "600", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "700", "600", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "750", "600", "purchaseReturn", "Y", "[]", "Y"], ["sage", "600", "700", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "700", "700", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "750", "700", "purchaseReturn", "Y", "[]", "Y"], ["sage", "600", "800", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "600", "900", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "3400", "Support User", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "3500", "Support User", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "3600", "Support User", "purchaseOrder", "Y", "[]", "Y"], ["sage", "3700", "Support User", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "3800", "Support User", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "3900", "Support User", "purchaseReturn", "Y", "[]", "Y"], ["sage", "3400", "Support User Read-only", "purchaseCreditMemo", "N", "[\"read\"]", "Y"], ["sage", "3500", "Support User Read-only", "purchaseInvoice", "N", "[\"read\"]", "Y"], ["sage", "3600", "Support User Read-only", "purchaseOrder", "N", "[\"read\"]", "Y"], ["sage", "3700", "Support User Read-only", "purchaseReceipt", "N", "[\"read\"]", "Y"], ["sage", "3800", "Support User Read-only", "purchaseRequisition", "N", "[\"read\"]", "Y"], ["sage", "3900", "Support User Read-only", "purchaseReturn", "N", "[\"read\"]", "Y"], ["sage", "85", "Operational User", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "300", "Operational User", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "400", "Operational User", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "500", "Operational User", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "1100", "Admin", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "1200", "Admin", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "1300", "Admin", "purchaseOrder", "Y", "[]", "Y"], ["sage", "1400", "Admin", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "1500", "Admin", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "1600", "Admin", "purchaseReturn", "Y", "[]", "Y"], ["sage", "4800", "Business User", "purchaseCreditMemo", "Y", "[]", "Y"], ["sage", "4900", "Business User", "purchaseInvoice", "Y", "[]", "Y"], ["sage", "5000", "Business User", "purchaseOrder", "Y", "[]", "Y"], ["sage", "5100", "Business User", "purchaseReceipt", "Y", "[]", "Y"], ["sage", "5200", "Business User", "purchaseRequisition", "Y", "[]", "Y"], ["sage", "5300", "Business User", "purchaseReturn", "Y", "[]", "Y"], ["sage", "410", "1100", "purchaseCreditMemo", "N", "[\"read\",\"post\",\"manage\"]", "Y"], ["sage", "420", "1100", "purchaseInvoice", "N", "[\"read\",\"manage\",\"acceptAllVariances\",\"post\"]", "Y"], ["sage", "3100", "100", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "3200", "100", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "3300", "100", "salesInvoice", "Y", "[]", "Y"], ["sage", "3400", "100", "salesOrder", "Y", "[]", "Y"], ["sage", "3500", "100", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "3600", "100", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "3700", "100", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "3800", "100", "salesShipment", "Y", "[]", "Y"], ["sage", "3850", "100", "proformaInvoice", "Y", "[]", "Y"], ["sage", "200", "200", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "300", "200", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "400", "200", "salesInvoice", "Y", "[]", "Y"], ["sage", "500", "200", "salesOrder", "Y", "[]", "Y"], ["sage", "600", "200", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "700", "200", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "800", "200", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "900", "200", "salesShipment", "Y", "[]", "Y"], ["sage", "950", "200", "proformaInvoice", "Y", "[]", "Y"], ["sage", "200", "300", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "300", "300", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "400", "300", "salesInvoice", "Y", "[]", "Y"], ["sage", "500", "300", "salesOrder", "Y", "[]", "Y"], ["sage", "600", "300", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "700", "300", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "800", "300", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "900", "300", "salesShipment", "Y", "[]", "Y"], ["sage", "950", "300", "proformaInvoice", "Y", "[]", "Y"], ["sage", "1600", "400", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "1700", "500", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "780", "600", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "800", "600", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "900", "600", "salesShipment", "Y", "[]", "Y"], ["sage", "950", "600", "salesReturnRequest", "N", "[\"read\"]", "Y"], ["sage", "800", "700", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "900", "700", "salesShipment", "Y", "[]", "Y"], ["sage", "1000", "800", "salesShipment", "N", "[\"read\"]", "Y"], ["sage", "1050", "800", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "700", "900", "salesShipment", "N", "[\"read\"]", "Y"], ["sage", "750", "900", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "4000", "Support User", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "4100", "Support User", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "4200", "Support User", "salesInvoice", "Y", "[]", "Y"], ["sage", "4300", "Support User", "salesOrder", "Y", "[]", "Y"], ["sage", "4400", "Support User", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "4500", "Support User", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "4600", "Support User", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "20300", "Support User", "salesShipment", "Y", "[]", "Y"], ["sage", "20350", "Support User", "proformaInvoice", "Y", "[]", "Y"], ["sage", "4000", "Support User Read-only", "salesCreditMemoReason", "N", "[\"read\"]", "Y"], ["sage", "4100", "Support User Read-only", "salesCreditMemo", "N", "[\"read\"]", "Y"], ["sage", "4200", "Support User Read-only", "salesInvoice", "N", "[\"read\"]", "Y"], ["sage", "4300", "Support User Read-only", "salesOrder", "N", "[\"read\"]", "Y"], ["sage", "4400", "Support User Read-only", "salesReturnReceipt", "N", "[\"read\"]", "Y"], ["sage", "4500", "Support User Read-only", "salesReturnRequestReason", "N", "[\"read\"]", "Y"], ["sage", "4600", "Support User Read-only", "salesReturnRequest", "N", "[\"read\"]", "Y"], ["sage", "20300", "Support User Read-only", "salesShipment", "N", "[\"read\"]", "Y"], ["sage", "20350", "Support User Read-only", "proformaInvoice", "N", "[\"read\"]", "Y"], ["sage", "600", "Operational User", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "700", "Operational User", "salesShipment", "Y", "[]", "Y"], ["sage", "800", "Operational User", "salesOrder", "N", "[\"read\",\"print\"]", "Y"], ["sage", "900", "Operational User", "salesInvoice", "Y", "[]", "Y"], ["sage", "1000", "Operational User", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "1700", "Admin", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "1800", "Admin", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "1900", "Admin", "salesInvoice", "Y", "[]", "Y"], ["sage", "2000", "Admin", "salesOrder", "Y", "[]", "Y"], ["sage", "2100", "Admin", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "2200", "Admin", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "2300", "Admin", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "2400", "Admin", "salesShipment", "Y", "[]", "Y"], ["sage", "2500", "Admin", "proformaInvoice", "Y", "[]", "Y"], ["sage", "5400", "Business User", "salesCreditMemo", "Y", "[]", "Y"], ["sage", "5500", "Business User", "salesCreditMemoReason", "Y", "[]", "Y"], ["sage", "5600", "Business User", "salesInvoice", "Y", "[]", "Y"], ["sage", "5700", "Business User", "salesOrder", "Y", "[]", "Y"], ["sage", "5800", "Business User", "salesReturnReceipt", "Y", "[]", "Y"], ["sage", "5900", "Business User", "salesReturnRequest", "Y", "[]", "Y"], ["sage", "6000", "Business User", "salesReturnRequestReason", "Y", "[]", "Y"], ["sage", "6100", "Business User", "salesShipment", "Y", "[]", "Y"], ["sage", "6200", "Business User", "proformaInvoice", "Y", "[]", "Y"], ["sage", "430", "1100", "proformaInvoice", "N", "[\"print\"]", "Y"], ["sage", "440", "1100", "salesCreditMemo", "N", "[\"read\",\"manage\",\"print\",\"post\"]", "Y"], ["sage", "450", "1100", "salesCreditMemoReason", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "460", "1100", "salesInvoice", "N", "[\"read\",\"manage\",\"post\",\"print\"]", "Y"], ["sage", "17000", "100", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "17100", "100", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "13300", "Admin", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "13400", "Admin", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "18000", "Admin", "apAutomationCompany", "Y", null, "Y"], ["sage", "1600", "Admin - Technical", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "1700", "Admin - Technical", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "21900", "Support User", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "22000", "Support User", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "22500", "Support User", "apAutomationCompany", "Y", null, "Y"], ["sage", "21900", "Support User Read-only", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "22000", "Support User Read-only", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "22500", "Support User Read-only", "apAutomationCompany", "Y", null, "Y"], ["sage", "700", "1100", "apAutomationConfiguration", "Y", null, "Y"], ["sage", "710", "1100", "uploadedPurchasingDocument", "Y", null, "Y"], ["sage", "8200", "1100", "apAutomationCompany", "Y", null, "Y"], ["sage", "800", "100", "avalaraCompany", "Y", "[]", "Y"], ["sage", "900", "100", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "1000", "100", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "1100", "100", "entityUse", "Y", "[]", "Y"], ["sage", "200", "Admin - Technical", "avalaraCompany", "Y", "[]", "Y"], ["sage", "300", "Admin - Technical", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "400", "Admin - Technical", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "500", "Admin - Technical", "entityUse", "Y", "[]", "Y"], ["sage", "600", "Support User", "avalaraCompany", "Y", "[]", "Y"], ["sage", "700", "Support User", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "800", "Support User", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "900", "Support User", "entityUse", "Y", "[]", "Y"], ["sage", "600", "Support User Read-only", "avalaraCompany", "N", "[\"read\"]", "Y"], ["sage", "700", "Support User Read-only", "avalaraConfiguration", "N", "[\"read\"]", "Y"], ["sage", "800", "Support User Read-only", "avalaraItemTax", "N", "[\"read\"]", "Y"], ["sage", "900", "Support User Read-only", "entityUse", "N", "[\"read\"]", "Y"], ["sage", "10600", "Admin", "avalaraCompany", "Y", "[]", "Y"], ["sage", "10700", "Admin", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "10800", "Admin", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "10900", "Admin", "entityUse", "Y", "[]", "Y"], ["sage", "1900", "Business User", "avalaraCompany", "Y", "[]", "Y"], ["sage", "2000", "Business User", "avalaraConfiguration", "Y", "[]", "Y"], ["sage", "2100", "Business User", "avalaraItemTax", "Y", "[]", "Y"], ["sage", "2200", "Business User", "entityUse", "Y", "[]", "Y"], ["sage", "9600", "100", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "9700", "100", "movementRule", "Y", "[]", "Y"], ["sage", "9800", "100", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "9900", "100", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "1900", "Support User", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "2000", "Support User", "movementRule", "Y", "[]", "Y"], ["sage", "2100", "Support User", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "2200", "Support User", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "1900", "Support User Read-only", "intrastatDeclaration", "N", "[\"read\"]", "Y"], ["sage", "2000", "Support User Read-only", "movementRule", "N", "[\"read\"]", "Y"], ["sage", "2100", "Support User Read-only", "natureOfTransaction", "N", "[\"read\"]", "Y"], ["sage", "2200", "Support User Read-only", "statisticalProcedure", "N", "[\"read\"]", "Y"], ["sage", "4700", "Admin", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "4800", "Admin", "movementRule", "Y", "[]", "Y"], ["sage", "4900", "Admin", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "5000", "Admin", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "3100", "Business User", "intrastatDeclaration", "Y", "[]", "Y"], ["sage", "3200", "Business User", "movementRule", "Y", "[]", "Y"], ["sage", "3300", "Business User", "natureOfTransaction", "Y", "[]", "Y"], ["sage", "3400", "Business User", "statisticalProcedure", "Y", "[]", "Y"], ["sage", "20", "1100", "intrastatDeclaration", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "30", "1100", "movementRule", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "40", "1100", "natureOfTransaction", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "50", "1100", "statisticalProcedure", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "1400", "100", "intacct", "Y", "[]", "Y"], ["sage", "900", "Admin - Technical", "intacct", "Y", "[]", "Y"], ["sage", "1300", "Support User", "intacct", "Y", "[]", "Y"], ["sage", "1300", "Support User Read-only", "intacct", "N", "[\"read\"]", "Y"], ["sage", "11200", "Admin", "intacct", "Y", "[]", "Y"], ["sage", "2500", "Business User", "intacct", "Y", "[]", "Y"], ["sage", "250", "1100", "intacct", "N", "[\"read\",\"manage\"]", "Y"], ["sage", "1500", "100", "intacctMap", "Y", "[]", "Y"], ["sage", "15400", "100", "financeListener", "Y", "[]", "Y"], ["sage", "1000", "Admin - Technical", "intacctMap", "Y", "[]", "Y"], ["sage", "1400", "Support User", "intacctBankAccountTransactionFeed", "Y", "[]", "Y"], ["sage", "1500", "Support User", "intacctMap", "Y", "[]", "Y"], ["sage", "1400", "Support User Read-only", "intacctBankAccountTransactionFeed", "N", "[\"read\"]", "Y"], ["sage", "1500", "Support User Read-only", "intacctMap", "N", "[\"read\"]", "Y"], ["sage", "11300", "Admin", "intacctMap", "Y", "[]", "Y"], ["sage", "11400", "Admin", "intacctBankAccountTransactionFeed", "Y", "[]", "Y"], ["sage", "11800", "Admin", "financeListener", "Y", "[]", "Y"], ["sage", "2700", "Business User", "intacctBankAccountTransactionFeed", "Y", "[]", "Y"], ["sage", "2800", "Business User", "financeListener", "Y", "[]", "Y"], ["sage", "260", "1100", "financeListener", "N", "[\"retryFinanceDocument\"]", "Y"], ["sage", "270", "1100", "intacctMap", "N", "[\"read\",\"manage\",\"synchronizationWithSageIntacct\"]", "Y"], ["sage", "9400", "100", "billOfMaterial", "Y", "[]", "Y"], ["sage", "2900", "600", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "5400", "600", "routing", "N", "[\"read\"]", "Y"], ["sage", "2500", "700", "billOfMaterial", "N", "[\"read\"]", "Y"], ["sage", "2400", "800", "billOfMaterial", "Y", "[]", "Y"], ["sage", "2500", "800", "routing", "Y", "[]", "Y"], ["sage", "2100", "900", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "1800", "1000", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "11400", "Support User", "billOfMaterial", "Y", "[]", "Y"], ["sage", "11600", "Support User", "routing", "Y", "[]", "Y"], ["sage", "11400", "Support User Read-only", "billOfMaterial", "N", "[\"read\"]", "Y"], ["sage", "11600", "Support User Read-only", "routing", "N", "[\"read\"]", "Y"], ["sage", "1700", "Operational User", "billOfMaterial", "N", "[\"read\",\"print\"]", "Y"], ["sage", "5300", "Admin", "billOfMaterial", "Y", "[]", "Y"], ["sage", "5400", "Admin", "routing", "Y", "[]", "Y"], ["sage", "13300", "Business User", "billOfMaterial", "Y", "[]", "Y"], ["sage", "13400", "Business User", "routing", "Y", "[]", "Y"], ["sage", "16400", "100", "productionTracking", "Y", "[]", "Y"], ["sage", "16500", "100", "workInProgressCost", "Y", "[]", "Y"], ["sage", "1300", "200", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "1900", "300", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "1700", "400", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "1800", "500", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "100", "600", "workOrder", "N", "[\"read\",\"tracking\"]", "Y"], ["sage", "200", "600", "materialTracking", "Y", "[]", "Y"], ["sage", "300", "600", "operationTracking", "N", "[\"read\"]", "Y"], ["sage", "400", "600", "productionTracking", "N", "[\"read\"]", "Y"], ["sage", "550", "600", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "100", "700", "workOrder", "N", "[\"read\",\"tracking\"]", "Y"], ["sage", "200", "700", "materialTracking", "Y", "[]", "Y"], ["sage", "100", "800", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "200", "800", "materialTracking", "N", "[\"read\"]", "Y"], ["sage", "300", "800", "operationTracking", "N", "[\"read\"]", "Y"], ["sage", "400", "800", "productionTracking", "N", "[\"read\"]", "Y"], ["sage", "450", "800", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "100", "900", "workOrder", "Y", "[]", "Y"], ["sage", "300", "900", "materialTracking", "Y", "[]", "Y"], ["sage", "400", "900", "operationTracking", "Y", "[]", "Y"], ["sage", "500", "900", "productionTracking", "Y", "[]", "Y"], ["sage", "550", "900", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "650", "900", "workOrderCategory", "Y", "[]", "Y"], ["sage", "200", "1000", "materialTracking", "Y", "[]", "Y"], ["sage", "300", "1000", "operationTracking", "Y", "[]", "Y"], ["sage", "400", "1000", "productionTracking", "Y", "[]", "Y"], ["sage", "500", "1000", "workOrder", "N", "[\"read\",\"manage\",\"close\",\"repost\",\"tracking\"]", "Y"], ["sage", "650", "1000", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "2700", "Support User", "materialTracking", "Y", "[]", "Y"], ["sage", "2800", "Support User", "operationTracking", "Y", "[]", "Y"], ["sage", "2900", "Support User", "productionTracking", "Y", "[]", "Y"], ["sage", "3000", "Support User", "workInProgressCost", "Y", "[]", "Y"], ["sage", "3100", "Support User", "workOrderCategory", "Y", "[]", "Y"], ["sage", "3300", "Support User", "workOrder", "Y", "[]", "Y"], ["sage", "2700", "Support User Read-only", "materialTracking", "N", "[\"read\"]", "Y"], ["sage", "2800", "Support User Read-only", "operationTracking", "N", "[\"read\"]", "Y"], ["sage", "2900", "Support User Read-only", "productionTracking", "N", "[\"read\"]", "Y"], ["sage", "3000", "Support User Read-only", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "3100", "Support User Read-only", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "3300", "Support User Read-only", "workOrder", "N", "[\"read\"]", "Y"], ["sage", "20", "Operational User", "materialTracking", "Y", "[]", "Y"], ["sage", "30", "Operational User", "operationTracking", "Y", "[]", "Y"], ["sage", "40", "Operational User", "productionTracking", "Y", "[]", "Y"], ["sage", "260", "Operational User", "workOrder", "N", "[\"read\",\"manage\",\"close\",\"repost\",\"tracking\"]", "Y"], ["sage", "270", "Operational User", "workOrderCategory", "N", "[\"read\"]", "Y"], ["sage", "2600", "Admin", "materialTracking", "Y", "[]", "Y"], ["sage", "2700", "Admin", "operationTracking", "Y", "[]", "Y"], ["sage", "2800", "Admin", "productionTracking", "Y", "[]", "Y"], ["sage", "2900", "Admin", "workInProgressCost", "Y", "[]", "Y"], ["sage", "3000", "Admin", "workOrderCategory", "Y", "[]", "Y"], ["sage", "3100", "Admin", "workOrder", "Y", "[]", "Y"], ["sage", "4200", "Business User", "materialTracking", "Y", "[]", "Y"], ["sage", "4300", "Business User", "operationTracking", "Y", "[]", "Y"], ["sage", "4400", "Business User", "productionTracking", "Y", "[]", "Y"], ["sage", "4500", "Business User", "workInProgressCost", "Y", "[]", "Y"], ["sage", "4600", "Business User", "workOrderCategory", "Y", "[]", "Y"], ["sage", "4700", "Business User", "workOrder", "Y", "[]", "Y"], ["sage", "280", "1100", "workInProgressCost", "N", "[\"read\"]", "Y"], ["sage", "100", "Admin", "sage100DeIntegrationConfiguration", null, "[\"manage\"]", "Y"], ["sage", "100", "Admin", "sysApp", null, "[\"read\",\"manage\"]", "Y"], ["sage", "100", "Admin", "sysSynchronization", null, "[\"read\",\"create\",\"update\",\"delete\",\"defaultInstance\"]", "Y"], ["sage", "100", "Admin", "sysEnumSynchronization", null, "[\"read\",\"create\",\"update\",\"delete\"]", "Y"], ["sage", "18600", "Admin", "organization", "Y", null, "Y"], ["sage", "1100", "Admin - Technical", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "1200", "Admin - Technical", "taxRateRepository", "Y", "[]", "Y"], ["sage", "1600", "Support User", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "1700", "Support User", "taxRateRepository", "Y", "[]", "Y"], ["sage", "1600", "Support User Read-only", "organisationServiceFabric", "N", "[\"manage\"]", "Y"], ["sage", "1700", "Support User Read-only", "taxRateRepository", "N", "[\"manage\"]", "Y"], ["sage", "11500", "Admin", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "11600", "Admin", "taxRateRepository", "Y", "[]", "Y"], ["sage", "2900", "Business User", "organisationServiceFabric", "Y", "[]", "Y"], ["sage", "3000", "Business User", "taxRateRepository", "Y", "[]", "Y"], ["sage", "470", "1100", "organisationServiceFabric", "N", "[\"manage\"]", "Y"], ["sage", "480", "1100", "taxRateRepository", "N", "[\"manage\"]", "Y"], ["sage", "2000", "400", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "400", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "2000", "500", "mrpCalculation", "N", "[\"read\"]", "Y"], ["sage", "2000", "600", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "600", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "2000", "800", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "800", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "2000", "900", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "900", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "10000", "Support User", "mrpCalculation", "Y", "[]", "Y"], ["sage", "10100", "Support User", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "10000", "Support User Read-only", "mrpCalculation", "N", "[\"read\"]", "Y"], ["sage", "10100", "Support User Read-only", "mrpSynchronization", "N", "[\"read\"]", "Y"], ["sage", "2000", "Admin", "mrpCalculation", "Y", "[]", "Y"], ["sage", "2100", "Admin", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "11700", "Business User", "mrpCalculation", "Y", "[]", "Y"], ["sage", "11800", "Business User", "mrpSynchronization", "Y", "[]", "Y"], ["sage", "5100", "400", "stockReorder", "Y", "[]", "Y"], ["sage", "5100", "500", "stockReorder", "Y", "[]", "Y"], ["sage", "7400", "600", "stockReorder", "Y", "[]", "Y"], ["sage", "3000", "600", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "3100", "600", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "3400", "600", "stockAdjustment", "Y", "[]", "Y"], ["sage", "4900", "600", "stockChange", "Y", "[]", "Y"], ["sage", "5000", "600", "stockCount", "Y", "[]", "Y"], ["sage", "5100", "600", "stockIssue", "Y", "[]", "Y"], ["sage", "5200", "600", "stockReceipt", "Y", "[]", "Y"], ["sage", "5300", "600", "stockValueChange", "Y", "[]", "Y"], ["sage", "1000", "700", "stockIssue", "Y", "[]", "Y"], ["sage", "3500", "700", "stockAdjustment", "Y", "[]", "Y"], ["sage", "5100", "700", "stockCount", "Y", "[]", "Y"], ["sage", "5200", "700", "stockReceipt", "Y", "[]", "Y"], ["sage", "5300", "700", "stockValueChange", "Y", "[]", "Y"], ["sage", "3200", "800", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "3300", "800", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "4700", "Support User", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "4800", "Support User", "stockAdjustment", "Y", "[]", "Y"], ["sage", "4900", "Support User", "stockChange", "Y", "[]", "Y"], ["sage", "5000", "Support User", "stockCount", "Y", "[]", "Y"], ["sage", "5100", "Support User", "stockIssue", "Y", "[]", "Y"], ["sage", "5200", "Support User", "stockReceipt", "Y", "[]", "Y"], ["sage", "5250", "Support User", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "5300", "Support User", "stockValueChange", "Y", "[]", "Y"], ["sage", "22400", "Support User", "stockReorder", "Y", "[]", "Y"], ["sage", "4700", "Support User Read-only", "stockValuationInputSet", "N", "[\"read\"]", "Y"], ["sage", "4800", "Support User Read-only", "stockAdjustment", "N", "[\"read\"]", "Y"], ["sage", "4900", "Support User Read-only", "stockChange", "N", "[\"read\"]", "Y"], ["sage", "5000", "Support User Read-only", "stockCount", "N", "[\"read\"]", "Y"], ["sage", "5100", "Support User Read-only", "stockIssue", "N", "[\"read\"]", "Y"], ["sage", "5200", "Support User Read-only", "stockReceipt", "N", "[\"read\"]", "Y"], ["sage", "5200", "Operational User", "stockReorder", "Y", "[]", "Y"], ["sage", "190", "Operational User", "stockAdjustment", "Y", "[]", "Y"], ["sage", "210", "Operational User", "stockCount", "Y", "[]", "Y"], ["sage", "220", "Operational User", "stockIssue", "Y", "[]", "Y"], ["sage", "230", "Operational User", "stockReceipt", "Y", "[]", "Y"], ["sage", "235", "Operational User", "stockValueChange", "Y", "[]", "Y"], ["sage", "3200", "Admin", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "3250", "Admin", "stockCount", "Y", "[]", "Y"], ["sage", "3300", "Admin", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "3400", "Admin", "stockAdjustment", "Y", "[]", "Y"], ["sage", "3500", "Admin", "stockChange", "Y", "[]", "Y"], ["sage", "3700", "Admin", "stockReceipt", "Y", "[]", "Y"], ["sage", "3800", "Admin", "stockValueChange", "Y", "[]", "Y"], ["sage", "3900", "Admin", "stockIssue", "Y", "[]", "Y"], ["sage", "14000", "Admin", "stockReorder", "Y", "[]", "Y"], ["sage", "6300", "Business User", "stockValuationInputSet", "Y", "[]", "Y"], ["sage", "6400", "Business User", "stockCount", "Y", "[]", "Y"], ["sage", "6500", "Business User", "costRollUpInputSet", "Y", "[]", "Y"], ["sage", "6600", "Business User", "stockAdjustment", "Y", "[]", "Y"], ["sage", "6700", "Business User", "stockChange", "Y", "[]", "Y"], ["sage", "6800", "Business User", "stockReceipt", "Y", "[]", "Y"], ["sage", "6900", "Business User", "stockValueChange", "Y", "[]", "Y"], ["sage", "7000", "Business User", "stockIssue", "Y", "[]", "Y"], ["sage", "14300", "Business User", "stockReorder", "Y", "[]", "Y"], ["sage", "490", "1100", "costRollUpInputSet", "N", "[\"manage\"]", "Y"], ["sage", "500", "1100", "stockValuationInputSet", "N", "[\"read\",\"manageCost\"]", "Y"], ["sage", "510", "1100", "stockValueChange", "N", "[\"read\",\"manage\",\"post\"]", "Y"], ["sage", "4600", "200", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "4700", "200", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "48001", "200", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "200", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "4700", "400", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "4800", "400", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "4900", "400", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "400", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "3200", "500", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "3300", "500", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "3400", "500", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "500", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "7100", "600", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "7200", "600", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "7300", "600", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "600", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "6800", "700", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "6900", "700", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "7000", "700", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "700", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "7500", "900", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "7600", "900", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "7700", "900", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "900", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "2600", "1000", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "2700", "1000", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "2800", "1000", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "1000", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "13400", "Admin", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "13500", "Admin", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "13600", "Admin", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "Admin", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "13700", "Admin", "supplyPlanning", "Y", "[]", "Y"], ["sage", "21900", "Support User Read-only", "stockTransferOrder", "N", "[\"read\"]", "Y"], ["sage", "22000", "Support User Read-only", "stockTransferShipment", "N", "[\"read\"]", "Y"], ["sage", "22100", "Support User Read-only", "stockTransferReceipt", "N", "[\"read\"]", "Y"], ["sage", "5000", "Support User Read-only", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "22200", "Support User Read-only", "supplyPlanning", "N", "[\"read\"]", "Y"], ["sage", "21900", "Support User", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "22000", "Support User", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "22100", "Support User", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "Support User", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "22200", "Support User", "supplyPlanning", "Y", "[]", "Y"], ["sage", "1900", "Operational User", "stockTransferOrder", "Y", "[]", "Y"], ["sage", "2000", "Operational User", "stockTransferShipment", "Y", "[]", "Y"], ["sage", "2100", "Operational User", "stockTransferReceipt", "Y", "[]", "Y"], ["sage", "5000", "Operational User", "stockInTransitInquiry", "Y", "[]", "Y"], ["sage", "5100", "Operational User", "supplyPlanning", "Y", "[]", "Y"]]}}}