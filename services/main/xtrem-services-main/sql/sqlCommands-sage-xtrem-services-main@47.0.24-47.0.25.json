{"fromVersion": "47.0.24", "toVersion": "47.0.25", "gitHead": "e604b623747459d2576fd5342159500287d52f0f", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        is_audit_enabled BOOLEAN;", "        p_root_table_name VARCHAR;", "        login_email VARCHAR;", "        user_id INT8;", "        log_record RECORD;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled') INTO is_audit_enabled;", "        IF NOT is_audit_enabled THEN", "            RETURN NEW;", "        END IF;", "        p_root_table_name := TG_ARGV[0];", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.login_email') INTO login_email;", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id')::INT8 INTO user_id;", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = COALESCE(NEW._id, OLD._id)", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id, user_id);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, COALESCE(NEW._tenant_id, OLD._tenant_id), COALESCE(NEW._id, OLD._id), TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.sys_audit_log_ind0;"}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;", "DROP TRIGGER IF EXISTS workflow_definition_audit ON %%SCHEMA_NAME%%.workflow_definition;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.base_purchase_document;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;", "DROP TRIGGER IF EXISTS base_purchase_document_audit ON %%SCHEMA_NAME%%.purchase_return;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;", "DROP TRIGGER IF EXISTS sales_credit_memo_audit ON %%SCHEMA_NAME%%.sales_credit_memo;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;", "DROP TRIGGER IF EXISTS sales_invoice_audit ON %%SCHEMA_NAME%%.sales_invoice;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;", "DROP TRIGGER IF EXISTS sales_order_audit ON %%SCHEMA_NAME%%.sales_order;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;", "DROP TRIGGER IF EXISTS sales_return_receipt_audit ON %%SCHEMA_NAME%%.sales_return_receipt;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;", "DROP TRIGGER IF EXISTS sales_return_request_audit ON %%SCHEMA_NAME%%.sales_return_request;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;", "DROP TRIGGER IF EXISTS sales_shipment_audit ON %%SCHEMA_NAME%%.sales_shipment;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;", "DROP TRIGGER IF EXISTS work_order_audit ON %%SCHEMA_NAME%%.work_order;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sys_audit_log_ind0 ON %%SCHEMA_NAME%%.sys_audit_log(_tenant_id ASC,root_table_name ASC,record_id ASC,COALESCE(new_update_tick, (- ((2)::bigint ^ (62)::bigint))::bigint) ASC);"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", false, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00DR7H-64358\",\"excludeSelf\":true}';", "args": []}], "data": {}}