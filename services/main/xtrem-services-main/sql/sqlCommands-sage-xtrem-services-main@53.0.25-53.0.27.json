{"fromVersion": "53.0.25", "toVersion": "53.0.27", "gitHead": "9af2efa302411be5ec8605bf6121ec054e7d8725", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN NULL;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN", "    \tSELECT trigger_name, event_object_table", "    \tFROM information_schema.triggers", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO USER_ID;", "        IF (USER_ID <> '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.audit_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        -- parameters", "        p_root_table_name VARCHAR;", "        p_constructor VARCHAR;", "", "        -- audit variables", "        is_audit_enabled VARCHAR;", "        tenant_id VARCHAR;", "        rid INT8;", "        login_email VARCHAR;", "        user_id VARCHAR;", "        locale VARCHAR;", "        log_record RECORD;", "", "        -- notify variables", "        origin_id VARCHAR;", "        notify_all_disabled VARCHAR;", "        notify_tenant_disabled VARCHAR;", "        notification_id VARCHAR;", "        user_email VARCHAR;", "        constructor VARCHAR;", "        event VARCHAR;", "        topic VARCHAR;", "        envelope VARCHAR;", "        payload VARCHAR;", "    BEGIN", "        p_root_table_name := TG_ARGV[0];", "        p_constructor := TG_ARGV[1];", "", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.is_audit_enabled'), 'false') INTO is_audit_enabled;", "        IF (is_audit_enabled <> 'true') THEN", "            RETURN NEW;", "        END IF;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.login_email'), '') INTO login_email;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id'), '') INTO user_id;", "        SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.locale'), 'base') INTO locale;", "", "        tenant_id := COALESCE(NEW._tenant_id, OLD._tenant_id);", "        rid := COALESCE(NEW._id, OLD._id);", "", "        SELECT * FROM %%SCHEMA_NAME%%.sys_audit_log", "        WHERE root_table_name = p_root_table_name", "            AND record_id = rid", "            AND transaction_id::TEXT = pg_current_xact_id()::TEXT", "        INTO log_record;", "", "        IF log_record IS NULL THEN", "            RAISE NOTICE 'Inserting new audit log record %:%', p_root_table_name, NEW._id;", "            IF p_root_table_name = TG_TABLE_NAME THEN", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick, _create_user, _update_user)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), OLD._update_tick, NEW._update_tick, user_id::INT8, user_id::INT8);", "            ELSE", "                INSERT INTO %%SCHEMA_NAME%%.sys_audit_log (root_table_name, _tenant_id, record_id, operation, login_email, timestamp, transaction_id, record_data, old_update_tick, new_update_tick)", "                VALUES (p_root_table_name, tenant_id, rid, TG_OP::%%SCHEMA_NAME%%.audit_operation_enum, login_email, NOW(), pg_current_xact_id(), to_json(NEW), NULL, NULL);", "            END IF;", "            RAISE NOTICE 'Inserted  new audit log record root_table=%, table=%, _id=%', p_root_table_name, TG_TABLE_NAME, NEW._id;", "        ELSE", "            RAISE NOTICE 'Updating audit log record %:%', p_root_table_name, NEW._id;", "            UPDATE %%SCHEMA_NAME%%.sys_audit_log", "            SET record_data = log_record.record_data || to_jsonb(NEW)", "            WHERE root_table_name = p_root_table_name", "                AND record_id = NEW._id", "                AND transaction_id = pg_current_xact_id()::TEXT;", "            RAISE NOTICE 'Updated  audit log record %:%', p_root_table_name, NEW._id;", "        END IF;", "", "        IF p_root_table_name = TG_TABLE_NAME THEN", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.ALL'), 'false') INTO notify_all_disabled;", "            SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.notification.disable.t_' || tenant_id), 'false') INTO notify_tenant_disabled;", "", "            IF (notify_all_disabled <> 'true' and notify_tenant_disabled <> 'true') THEN", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.origin_id'), '') INTO origin_id;", "                SELECT COALESCE(%%SCHEMA_NAME%%.get_config('xtrem.user_email'), '') INTO user_email;", "                SELECT %%SCHEMA_NAME%%.nanoid() INTO notification_id;", "", "                IF p_constructor != '' THEN", "                    constructor := p_constructor;", "                ELSE", "                    constructor := COALESCE(NEW._constructor, OLD._constructor);", "                END IF;", "", "                CASE TG_OP", "                    WHEN 'INSERT' THEN event := 'created';", "                    WHEN 'UPDATE' THEN event := 'updated';", "                    WHEN 'DELETE' THEN event := 'deleted';", "                END CASE;", "", "                topic := constructor || '/' || event;", "                payload := '{ \"_id\":' || rid || ', \"_updateTick\":' || COALESCE(NEW._update_tick, OLD._update_tick) || '}';", "", "                RAISE NOTICE 'Inserted new notification %:%', topic, notification_id;", "                INSERT INTO %%SCHEMA_NAME%%.sys_notification", "                    (tenant_id, origin_id, notification_id, reply_id, reply_topic, user_email, login, locale,", "                    topic, payload, status, _source_id, _update_tick, _create_stamp, _update_stamp)", "                VALUES (tenant_id, origin_id, notification_id, '', '', user_email, login_email, locale,", "                    topic, payload, 'pending', '', 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);", "", "                RAISE NOTICE 'Notifying %:%', TG_OP, event;", "                PERFORM pg_notify('notification_queued', '{\"data\":\"{\\\"topic\\\":\\\"' || event || '\\\"}\"}');", "            END IF;", "        END IF;", "", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", ""]}, {"isSysPool": true, "sql": "ALTER TABLE IF EXISTS %%SCHEMA_NAME%%.email_reply_to RENAME TO sender_email_setting;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to__create_user_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to__tenant_id_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to__update_user_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to__vendor_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to_company_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to_email_config_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to_meta_package_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to_node_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting DROP CONSTRAINT IF EXISTS email_reply_to_site_fk;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": ["DO $$", "                    BEGIN", "                        ALTER INDEX %%SCHEMA_NAME%%.\"email_reply_to_PK\" RENAME TO \"sender_email_setting_PK\";", "                    EXCEPTION", "                        WHEN OTHERS THEN", "                            ALTER INDEX %%SCHEMA_NAME%%.\"email_reply_to_pk\" RENAME TO \"sender_email_setting_PK\";", "                    END $$;", "                    "], "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "DROP INDEX IF EXISTS %%SCHEMA_NAME%%.\"email_reply_to_ind0\";", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER SEQUENCE IF EXISTS %%SCHEMA_NAME%%.email_reply_to__id_seq RENAME TO sender_email_setting__id_seq;", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ALTER COLUMN \"_sort_value\" SET DEFAULT (currval((pg_get_serial_sequence('%%SCHEMA_NAME%%.sender_email_setting'::text, '_id'::text))::regclass) * 100)", "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": "UPDATE %%SCHEMA_NAME%%.attachment_association SET source_node_name=$1 WHERE source_node_name=$2", "args": ["SenderEmailSetting", "EmailReplyTo"], "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": ["UPDATE %%SCHEMA_NAME%%.custom_field table_to_fix SET node = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.node = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.custom_field table_to_fix SET target_node = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.target_node = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.report_wizard table_to_fix SET data_source = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.data_source = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.sender_email_setting table_to_fix SET node = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.node = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.sequence_number_assignment_document_type table_to_fix SET node_factory = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.node_factory = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.base_mapping table_to_fix SET node_factory = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.node_factory = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.synchronization_state table_to_fix SET node = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.node = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.payment_document_line table_to_fix SET original_node_factory = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.original_node_factory = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.sys_node_transformation table_to_fix SET local_node = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.local_node = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.sys_operation_transformation table_to_fix SET local_node = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.local_node = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);", "UPDATE %%SCHEMA_NAME%%.sys_synchronization_state table_to_fix SET node = new_factory._id FROM (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='SenderEmailSetting') new_factory WHERE table_to_fix.node = (SELECT _id FROM %%SCHEMA_NAME%%.meta_node_factory WHERE name='EmailReplyTo' AND is_active=false);"], "actionDescription": "SenderEmailSetting: rename node 'EmailReplyTo' to 'SenderEmailSetting'"}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting__vendor_fk\" FOREIGN KEY(_vendor) REFERENCES %%SCHEMA_NAME%%.sys_vendor(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting__vendor_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting__update_user_fk\" FOREIGN KEY(_tenant_id,_update_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting__update_user_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting__create_user_fk\" FOREIGN KEY(_tenant_id,_create_user) REFERENCES %%SCHEMA_NAME%%.user(_tenant_id,_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting__create_user_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting_email_config_fk\" FOREIGN KEY(_tenant_id,email_config) REFERENCES %%SCHEMA_NAME%%.sys_email_config(_tenant_id,_id) ON DELETE CASCADE DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting_email_config_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"sys_email_config\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"email_config\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting_node_fk\" FOREIGN KEY(node) REFERENCES %%SCHEMA_NAME%%.meta_node_factory(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting_node_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"meta_node_factory\",", "  \"columns\": {", "    \"node\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting_meta_package_fk\" FOREIGN KEY(meta_package) REFERENCES %%SCHEMA_NAME%%.meta_package(_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting_meta_package_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"meta_package\",", "  \"columns\": {", "    \"meta_package\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting_company_fk\" FOREIGN KEY(_tenant_id,company) REFERENCES %%SCHEMA_NAME%%.company(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting_company_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting_site_fk\" FOREIGN KEY(_tenant_id,site) REFERENCES %%SCHEMA_NAME%%.site(_tenant_id,_id) ON DELETE NO ACTION DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting_site_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": ["ALTER TABLE %%SCHEMA_NAME%%.sender_email_setting ADD CONSTRAINT \"sender_email_setting__tenant_id_fk\" FOREIGN KEY(_tenant_id) REFERENCES %%SCHEMA_NAME%%.sys_tenant(tenant_id) ON DELETE RESTRICT DEFERRABLE DEFERRABLE;", "COMMENT ON CONSTRAINT sender_email_setting__tenant_id_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;"]}, {"isSysPool": true, "sql": "CREATE UNIQUE INDEX sender_email_setting_ind0 ON %%SCHEMA_NAME%%.sender_email_setting(_tenant_id ASC,email_config ASC,_sort_value ASC);"}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tags (not yet released)", "experimental", false, "@sage/xtrem-system", false, "tags"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "experimental", false, "@sage/xtrem-auditing", false, "auditing"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Auditing option", "workInProgress", true, "@sage/xtrem-auditing", false, "auditingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "experimental", false, "@sage/xtrem-workflow", false, "workflow"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow advanced features (not yet released)", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowAdvanced"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option (obsolete)", "workInProgress", true, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable ReportAssignment feature", "workInProgress", false, "@sage/xtrem-reporting", false, "reportAssignment"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "workInProgress", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Open item page display", "released", true, "@sage/xtrem-structure", false, "openItemPageOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Bill of material revision", "workInProgress", false, "@sage/xtrem-master-data", false, "billOfMaterialRevisionServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Customer 360 view option", "workInProgress", false, "@sage/xtrem-master-data", true, "customer360ViewOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "released", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "released", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": ["INSERT INTO %%SCHEMA_NAME%%.sys_service_option", "(_update_tick,_source_id,package,option_name,description,status,is_hidden,is_active_by_default)", "VALUES ($1,$2,$3,$4,$5,$6,$7,$8)", "RETURNING _create_stamp,_update_stamp,_id"], "args": [1, "", "@sage/xtrem-finance-data", "discountPaymentTrackingOption", "Discount payment tracking option", "workInProgress", false, false]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Payment tracking option", "released", false, "@sage/xtrem-finance-data", false, "paymentTrackingOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["AP Automation option", "released", false, "@sage/xtrem-ap-automation", false, "apAutomationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "workInProgress", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "workInProgress", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "workInProgress", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "released", false, "@sage/xtrem-interop", true, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Network", "workInProgress", false, "@sage/xtrem-sage-network", true, "sageNetworkOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00JANN-74802\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": false, "sql": ["SELECT", "                _id, email, is_active, first_name, last_name,", "                 is_administrator, is_api_user, is_demo_persona", "            FROM %%SCHEMA_NAME%%.user WHERE _tenant_id=$1 AND email = $2"], "args": ["777777777777777777777", "<EMAIL>"], "actionDescription": "Reload setup layer for factories SysEmailConfig,SysNodeTransformation,SysServiceOptionState,SenderEmailSetting,SysNodeMapping"}, {"action": "reload_setup_data", "args": {"factory": "SysEmailConfig"}}, {"action": "reload_setup_data", "args": {"factory": "SysServiceOptionState"}}, {"action": "reload_setup_data", "args": {"factory": "SenderEmailSetting"}}, {"isSysPool": true, "sql": ["COMMENT ON CONSTRAINT sender_email_setting__vendor_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"sys_vendor\",", "  \"columns\": {", "    \"_vendor\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting__update_user_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_update_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting__create_user_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"user\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"_create_user\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting_email_config_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"sys_email_config\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"email_config\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"cascade\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting_node_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"meta_node_factory\",", "  \"columns\": {", "    \"node\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting_meta_package_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"meta_package\",", "  \"columns\": {", "    \"meta_package\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting_company_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"company\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"company\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting_site_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"site\",", "  \"columns\": {", "    \"_tenant_id\": \"_tenant_id\",", "    \"site\": \"_id\"", "  },", "  \"onDeleteBehaviour\": \"noAction\",", "  \"isDeferrable\": true", "}';;COMMENT ON CONSTRAINT sender_email_setting__tenant_id_fk ON %%SCHEMA_NAME%%.sender_email_setting IS '{", "  \"targetTableName\": \"sys_tenant\",", "  \"columns\": {", "    \"_tenant_id\": \"tenant_id\"", "  },", "  \"onDeleteBehaviour\": \"restrict\",", "  \"isDeferrable\": true", "}';"]}], "data": {"SysEmailConfig": {"metadata": {"rootFactoryName": "SysEmailConfig", "name": "SysEmailConfig", "naturalKeyColumns": ["_tenant_id", "id"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "id", "type": "string"}, {"name": "email_system", "type": "enum", "enumMembers": ["sage", "custom"]}, {"name": "configuration", "type": "json", "isNullable": true}]}, "rows": [["", "1", "sage", null]]}, "SysServiceOptionState": {"metadata": {"rootFactoryName": "SysServiceOptionState", "name": "SysServiceOptionState", "naturalKeyColumns": ["_tenant_id", "service_option"], "columns": [{"name": "is_activable", "type": "boolean"}, {"name": "is_active", "type": "boolean"}, {"name": "service_option", "type": "reference", "targetFactoryName": "SysServiceOption"}]}, "rows": [["Y", null, "devTools"], ["Y", null, "isDemoTenant"], ["Y", null, "changelog"], ["Y", null, "tags"], ["Y", null, "notificationCenter"], ["Y", null, "authorizationServiceOption"], ["Y", null, "auditingOption"], ["Y", null, "auditing"], ["Y", null, "workflow"], ["Y", null, "workflowAdvanced"], ["N", null, "workflowOption"], ["Y", null, "reportAssignment"], ["Y", null, "frp1000ActivationOption"], ["Y", null, "intacctActivationOption"], ["Y", null, "openItemPageOption"], ["Y", "N", "serialNumberOption"], ["Y", "N", "orderToOrderOption"], ["Y", "N", "landedCostOption"], ["Y", "N", "fifoValuationMethodOption"], ["Y", "N", "allocationTransferOption"], ["Y", "N", "landedCostOrderOption"], ["Y", "N", "datevOption"], ["Y", "N", "phantomItemOption"], ["Y", "Y", "customer360ViewOption"], ["Y", "N", "billOfMaterialRevisionServiceOption"], ["Y", null, "paymentTrackingOption"], ["Y", null, "discountPaymentTrackingOption"], ["Y", null, "apAutomationOption"], ["Y", null, "avalaraOption"], ["Y", null, "hrOption"], ["Y", null, "frp1000Option"], ["Y", "Y", "intacctOption"], ["Y", null, "intacctCashbookManagement"], ["Y", "Y", "synchronizationServiceOption"], ["Y", null, "sageNetworkOption"], ["Y", "Y", "serviceFabricTaxRepository"], ["Y", "Y", "serviceFabricTaxId"], ["Y", "Y", "serviceFabricOption"]]}, "SenderEmailSetting": {"metadata": {"isVitalChild": true, "isVitalCollectionChild": true, "rootFactoryName": "SenderEmailSetting", "name": "SenderEmailSetting", "naturalKeyColumns": ["_tenant_id", "email_config", "_sort_value"], "columns": [{"name": "_vendor", "type": "reference", "isNullable": true, "targetFactoryName": "SysVendor"}, {"name": "_sort_value", "type": "integer"}, {"name": "email_config", "type": "reference", "targetFactoryName": "SysEmailConfig"}, {"name": "from_prefix", "type": "string"}, {"name": "node", "type": "reference", "isNullable": true, "targetFactoryName": "MetaNodeFactory"}, {"name": "meta_package", "type": "reference", "isNullable": true, "targetFactoryName": "MetaPackage"}, {"name": "company", "type": "reference", "isNullable": true, "targetFactoryName": "Company"}, {"name": "site", "type": "reference", "isNullable": true, "targetFactoryName": "Site"}, {"name": "reply_to_prefixes", "type": "stringArray"}], "vitalParentColumn": {"name": "email_config", "type": "reference", "targetFactoryName": "SysEmailConfig"}}, "rows": [["", "100", "1", "no-reply", null, null, null, null, "[\"no-reply\"]"]]}}}