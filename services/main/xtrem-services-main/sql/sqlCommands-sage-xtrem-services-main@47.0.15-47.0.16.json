{"fromVersion": "47.0.15", "toVersion": "47.0.16", "gitHead": "1407da814beda9b810563b7965f7810c1dcca2b1", "commands": [{"isSysPool": true, "sql": ["", "", "CREATE EXTENSION IF NOT EXISTS pgcrypto;", ""]}, {"isSysPool": true, "sql": ["", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.get_config(setting_name varchar)", "RETURNS varchar AS", "$$", "declare", "    setting_value varchar;", "BEGIN", "    SELECT current_setting(setting_name) into setting_value;", "    RETURN setting_value;", "EXCEPTION", "    WHEN OTHERS THEN", "    RETURN '';", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_triggers(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "begin", "\trecord_count = 0;", "    FOR triggerRecord IN ", "    \tSELECT trigger_name, event_object_table ", "    \tFROM information_schema.triggers ", "    \tWHERE trigger_schema = schema_name AND (name_filter = '' OR event_object_table = name_filter)", "\tLOOP", "\t\trecord_count = record_count + 1;", "        EXECUTE 'DROP TRIGGER ' || triggerRecord.trigger_name || ' ON ' || schema_name || '.\"' || triggerRecord.event_object_table || '\";';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.drop_notify_functions(schema_name varchar, name_filter varchar DEFAULT '') RETURNS integer AS ", "$$", "DECLARE", "    triggerRecord RECORD;", "    record_count integer;", "BEGIN", "    record_count = 0;", "    FOR triggerRecord IN ", "    SELECT routine_name", "    FROM information_schema.routines", "    WHERE specific_schema = schema_name and routine_name IN (name_filter || '_notify_deleted', name_filter || '_notify_created', name_filter || '_notify_updated')", "    LOOP", "        record_count = record_count + 1;", "        EXECUTE 'DROP FUNCTION ' || schema_name || '.' || triggerRecord.routine_name || ';';", "    END LOOP;", "", "    RETURN record_count;", "END;", "$$ LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN ", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._create_user :=  CAST(USER_ID AS INT8);", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.insert_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        IF (NEW._create_stamp IS NULL) THEN", "            NEW._create_stamp := NOW();", "        END IF;", "        IF (NEW._update_stamp IS NULL) THEN", "            NEW._update_stamp := NOW();", "        END IF;", "        IF (NEW._update_tick IS NULL) THEN", "            NEW._update_tick := 1;", "        END IF;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_table()", "RETURNS TRIGGER", "AS", "$$", "    DECLARE", "        USER_ID VARCHAR;", "    BEGIN", "        SELECT %%SCHEMA_NAME%%.get_config('xtrem.transaction_user_id') INTO USER_ID;", "        IF (USER_ID != '') THEN", "            NEW._update_user :=  CAST(USER_ID AS INT8);", "        END IF;", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.update_shared_table()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN", "        NEW._update_stamp := NOW();", "        NEW._update_tick := OLD._update_tick + 1;", "", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.set_sync_tick()", "RETURNS TRIGGER", "AS", "$$", "    BEGIN ", "        NEW._sync_tick :=  pg_current_xact_id();", "        RETURN NEW;", "    END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.signedInt32(a bigint)", "RETURNS bigint AS", "$$", "DECLARE", "BEGIN", "\t-- Convert to 32 bit signed (if leftmost bit is 1, it's a negative number)", "  \tIF (a > 2^31) THEN", "    \tRETURN a - (2^32)::bigint;", "  \tEND IF;", "  \tRETURN a;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.imul(a bigint, b bigint)", "RETURNS bigint AS", "$$", "DECLARE", "    aHi bigint;", "    aLo bigint;", "    bHi bigint;", "    bLo bigint;", "    res bigint;", "BEGIN", "    aHi = %%SCHEMA_NAME%%.zeroFillShift(a, 16) & 65535;", "    aLo = a & 65535;", "    bHi = %%SCHEMA_NAME%%.zeroFillShift(b, 16) & 65535;", "    bLo = b & 65535;", "    res = ((aLo * bLo) + %%SCHEMA_NAME%%.zeroFillShift(((aHi * bLo + aLo * bHi) << 16) % (2^32)::bigint, 0)) | 0;", "    RETURN %%SCHEMA_NAME%%.signedInt32(res);", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.zeroFillShift(a bigint, b int)", "RETURNS bigint AS", "$$", "DECLARE", "  \tres bigint;", "BEGIN", "\tIF (a < 0) THEN", "\t\tres = a + 2^32;", "\tELSE", "\t\tres = a;", "\tEND IF;", "\tres = res >> b;", "\tRETURN res;", "END;", "$$", "LANGUAGE plpgsql;", "", "", "CREATE OR REPLACE FUNCTION %%SCHEMA_NAME%%.nanoid(\"size\" int4 DEFAULT 21)", "    RETURNS text", "    LANGUAGE plpgsql", "    STABLE", "    AS", "    $$", "    DECLARE", "        id text := '';", "        i int := 0;", "        urlAl<PERSON><PERSON> char(64) := 'ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW';", "        bytes bytea;", "        byte int;", "        pos int;", "    BEGIN", "        SELECT gen_random_bytes(size) INTO bytes;", "    WHILE i < size LOOP", "        byte := get_byte(bytes, i);", "        pos := (byte & 63) + 1; -- + 1 because substr starts at 1", "        id := id || substr(urlAlphabet, pos, 1);", "        i = i + 1;", "    END LOOP;", "    RETURN id;", "    END", "    $$", "    ;", " ", ""]}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.unit_of_measure;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.item;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.company;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.site;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.base_resource;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;", "DROP TRIGGER IF EXISTS set_sync_tick ON %%SCHEMA_NAME%%.employee;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.employee", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.base_resource", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.site", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.company", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.item", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": true, "sql": ["DO $$ BEGIN", "            CREATE TRIGGER set_sync_tick", "            BEFORE INSERT OR UPDATE ON %%SCHEMA_NAME%%.unit_of_measure", "            FOR EACH ROW", "            EXECUTE PROCEDURE %%SCHEMA_NAME%%.set_sync_tick();", "            EXCEPTION", "                WHEN others THEN null;", "            END $$;"], "args": []}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Is a demo tenant", "released", false, "@sage/xtrem-system", false, "isDemoTenant"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["enable dev-only features", "experimental", false, "@sage/xtrem-system", false, "devTools"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["allow to display changelog in the app", "experimental", false, "@sage/xtrem-system", false, "changelog"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Notification center", "experimental", false, "@sage/xtrem-communication", false, "notificationCenter"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Authorization access control", "released", false, "@sage/xtrem-authorization", false, "authorizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Workflow option", "workInProgress", false, "@sage/xtrem-workflow", false, "workflowOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage DMO integration activation option", "released", true, "@sage/xtrem-structure", false, "intacctActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage FRP1000 activation option", "released", false, "@sage/xtrem-structure", false, "frp1000ActivationOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Allocation transfer option", "released", false, "@sage/xtrem-master-data", false, "allocationTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["DATEV option", "workInProgress", false, "@sage/xtrem-master-data", false, "datevOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FIFO valuation method option", "released", false, "@sage/xtrem-master-data", false, "fifoValuationMethodOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Intersite stock transfer option", "workInProgress", false, "@sage/xtrem-master-data", false, "intersiteStockTransferOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost option", "released", false, "@sage/xtrem-master-data", false, "landedCostOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Landed cost for orders option", "experimental", false, "@sage/xtrem-master-data", false, "landedCostOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Order to order option", "released", false, "@sage/xtrem-master-data", false, "orderToOrderOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Phantom item option", "experimental", false, "@sage/xtrem-master-data", false, "phantomItemOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Serial number option", "released", false, "@sage/xtrem-master-data", false, "serialNumberOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Avalara integration option", "released", true, "@sage/xtrem-avalara-gateway", false, "avalaraOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage HR integration option", "released", false, "@sage/xtrem-cake-hr", true, "hrOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["FRP 1000 integration option", "released", false, "@sage/xtrem-frp-1000", true, "frp1000Option"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct integration option", "released", true, "@sage/xtrem-intacct", true, "intacctOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Sage Intacct Bank Manager option", "released", true, "@sage/xtrem-intacct-finance", false, "intacctCashbookManagement"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Synchronization", "experimental", false, "@sage/xtrem-interop", false, "synchronizationServiceOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Service Fabric", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricOption"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Id validation", "released", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxId"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Tax Repository", "experimental", false, "@sage/xtrem-service-fabric", true, "serviceFabricTaxRepository"]}, {"isSysPool": false, "sql": "UPDATE %%SCHEMA_NAME%%.sys_service_option AS t0 SET description=$1,status=$2,is_hidden=$3,package=$4,is_active_by_default=$5 WHERE (t0.option_name = $6)", "args": ["Shopify integration option", "released", false, "@sage/xtrem-shopify", true, "shopifyOption"]}, {"isSysPool": false, "sql": "NOTIFY invalidate_category_cache, '{\"data\":\"{\\\"tenantId\\\":null,\\\"category\\\":\\\"$SHARED_NODE.SysServiceOption\\\"}\",\"containerId\":\"x3-devops00DGRF-64559\",\"excludeSelf\":true}';", "args": []}, {"isSysPool": true, "sql": ["COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_customer._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_tenant._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_changelog._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_csv_checksum._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_pack_version._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_service_option._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_service_option_to_service_option._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_upgrade._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_vendor._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_navigation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_preferences._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_message._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_message_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.activity._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.group_role_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.restricted_node._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.restricted_node_user_grant._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.role_activity._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.role_to_role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site_group_to_site_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.support_access_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_billing_role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.user_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_activity._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_activity_permission._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_node_factory._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_node_operation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_node_property._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_package._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_service_option._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.custom_field._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dashboard._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dashboard_item._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.widget_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_diagram._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_job_schedule._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.attachment._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.attachment_association._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.uploaded_file._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_resource._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_style_variable._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_template._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_translatable_text._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_wizard._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_option_management._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.legislation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.allergen._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_document_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_discount_charge._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.capability_level._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.contact._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.contact_base._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.cost_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.currency._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.customer_price_reason._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.daily_shift._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.delivery_mode._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dev_tools._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.exchange_rate._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.ghs_classification._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.incoterm._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.indirect_cost_origin._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.indirect_cost_section._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.indirect_cost_section_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.location_sequence._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.location_sequence_component._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.location_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.payment_term._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.reason_code._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number_assignment_module._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number_assignment_setup._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number_component._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.shift_detail._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.standard._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.standard_industrial_classification._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.unit_of_measure._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.version_information._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.weekly_shift._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.landed_cost_base_document._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.allocation_result._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_status._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.third_party_application._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.country_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_tax_group._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_solution._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_solution_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.analytical_data._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.attribute_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_finance_document._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.datev_configuration._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dimension_definition_level_and_default._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dimension_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.posting_class._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.posting_class_definition._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry_inquiry._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_invoice_matching._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_receipt_matching._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_return_matching._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.unbilled_account_payable_input_set._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_sales_document_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_credit_memo_reason._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_return_request_reason._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.unbilled_account_receivable_input_set._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.avalara_company._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.avalara_configuration._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.avalara_item_tax._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.entity_use._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.map_company._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.cake_hr_configuration._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.nature_of_transaction._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.statistical_procedure._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.frp_1000_api_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.frp_1000_configuration._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order_view._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.frp_1000_map._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.frp_1000_map_property._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.import_export_template._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.import_result._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_app._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_node_transformation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_operation_transformation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_synchronization_state._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.organisation_service_fabric._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_rate_repository._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.shopify_configuration._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_bom._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_bom_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_calculation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_input_set._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_result_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_synchronization._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_work_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_valuation_input_set._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_global_lock._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.locale._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_client_notification._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_client_notification_action._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_client_user_settings._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_custom_record._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_custom_sql_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_pack_allocation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_patch_history._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_service_option_state._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification_state._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.group_role._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.group_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.meta_data_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_definition._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.workflow_process._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.report_variable._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.chart_of_account._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.country._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.address._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.address_base._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_capability._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_certificate._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.business_entity._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.container._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.customer_supplier_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.daily_shift_detail._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_allergen._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_classifications._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.license_plate_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number_assignment_document_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.landed_cost_item._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.lot._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_mapping._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.synchronization_state._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.country_group_to_country._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_determination._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_determination_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_value._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_zone._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.tax_zone_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.account._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.account_attribute_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.account_dimension_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.attribute._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_open_item._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.dimension._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry_type_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.posting_class_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.posting_class_line_detail._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.movement_rule._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_enum_transformation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_node_mapping._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_input._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.mrp_input_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.company._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_notification_log_entry._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.site_group_to_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_business_relation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_document._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_resource._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.delivery_detail._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.employee._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_site_cost._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.location_zone._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number_assignment._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sequence_number_value._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.team._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_in_progress._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.serial_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_tax._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.bank_account._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.company_attribute_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.company_default_attribute._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.company_default_dimension._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.company_dimension_type._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.finance_transaction._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.finance_transaction_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging_amount._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_payable_invoice._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_advance._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_invoice._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_payment._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.datev_export._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.datev_export_account._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.datev_export_business_relation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry_line_dimension._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.journal_entry_line_staging._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_purchase_document._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_requisition._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_requisition_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.unbilled_account_payable_result_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_credit_memo._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_invoice._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_order._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_return_receipt._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_return_request._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sales_shipment._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.unbilled_account_receivable_result_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intrastat_declaration._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.bill_of_material._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.bill_of_material_printout._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.bill_of_material_tracking._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.component._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.formula._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.formula_step._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.routing._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order_operation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order_operation_resource._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order_operation_resource_detail._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_order_serial_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_accounts_payable_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_accounts_receivable_advance_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_accounts_receivable_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_accounts_receivable_payment_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_bank_account_matching._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_import_session._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intacct_journal_entry_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.map_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.map_property._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.sys_enum_mapping._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.cost_roll_up_input_set._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.cost_roll_up_result_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.cost_roll_up_sub_assembly._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_adjustment._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_change._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_count._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_issue._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_receipt._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_valuation_input_set_to_site._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_valuation_result_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_value_change._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_value_correction._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.supply_planning._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_customer._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_customer_price._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_supplier._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_supplier_price._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.location._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.resource_cost_category._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.resource_group_replacement._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.unit_conversion_factor._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_payable_invoice_line_dimension._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_payable_invoice_line_staging._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_invoice_line_dimension._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounts_receivable_invoice_line_staging._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.proforma_invoice._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.component_printout._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.formula_component._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.operation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.operation_resource._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.operation_resource_detail._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.material_tracking._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.operation_tracking._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.production_tracking._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_count_line_serial_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_line_to_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.item_site_supplier._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.landed_cost_document_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.allocation_queue._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.allocation_result_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_stock_detail._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.fifo_valuation_issue._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.fifo_valuation_tier._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.order_assignment._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_allocation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_journal._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_journal_serial_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_transaction._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.base_finance_line_dimension._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_invoice_line_to_purchase_credit_memo_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_order_line_to_purchase_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_order_line_to_purchase_receipt_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_receipt_line_to_purchase_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_receipt_line_to_purchase_return_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_requisition_line_to_purchase_order_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_return_line_to_purchase_credit_memo_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.purchase_return_line_to_purchase_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intrastat_declaration_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intrastat_declaration_line_to_purchase_credit_memo_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intrastat_declaration_line_to_purchase_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intrastat_declaration_line_to_sales_credit_memo_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.intrastat_declaration_line_to_sales_invoice_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.work_in_progress_cost._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.landed_cost_allocation._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.landed_cost_line._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging_document_tax._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.accounting_staging_line_tax._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_detail_lot._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';;COMMENT ON COLUMN %%SCHEMA_NAME%%.stock_detail_serial_number._update_tick IS '{", "  \"type\": \"integer\",", "  \"isSystem\": true", "}';"]}], "data": {}}