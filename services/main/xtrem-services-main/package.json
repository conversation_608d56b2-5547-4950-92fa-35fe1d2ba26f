{"name": "@sage/xtrem-services-main", "version": "58.0.2", "description": "", "xtrem": {"isMain": true}, "keywords": [], "author": "Sage", "files": ["build", "data", "sql", "routing.json"], "license": "UNLICENSED", "dependencies": {"@sage/xtrem-ap-automation": "workspace:*", "@sage/xtrem-avalara-gateway": "workspace:*", "@sage/xtrem-cli": "workspace:*", "@sage/xtrem-cli-bundle-cloud": "workspace:*", "@sage/xtrem-date-time": "workspace:*", "@sage/xtrem-declarations": "workspace:*", "@sage/xtrem-intacct-gateway": "workspace:*", "@sage/xtrem-integration": "workspace:*", "@sage/xtrem-interop": "workspace:*", "@sage/xtrem-metadata": "workspace:*", "@sage/xtrem-routing": "workspace:*", "@sage/xtrem-sage-network": "workspace:*", "@sage/xtrem-scheduler": "workspace:*", "@sage/xtrem-service-fabric": "workspace:*", "@sage/xtrem-standalone": "workspace:*", "@sage/xtrem-stock": "workspace:*", "@sage/xtrem-stock-blend-po": "workspace:*", "@sage/xtrem-stock-blend-po-so": "workspace:*", "@sage/xtrem-supply-chain": "workspace:*", "@sage/xtrem-workflow": "workspace:*"}, "devDependencies": {"@sage/xtrem-cli-bundle-dev": "workspace:*"}, "scripts": {"build": "xtrem compile --skip-client --skip-cop --skip-dts --skip-api-client --skip-clean", "build:cache": "turbo run build --concurrency=${XTREM_CONCURRENCY:=10}", "extract:demo:data": "xtrem layers --extract demo", "extract:layer:data": "xtrem layers --extract $XTREM_LAYER", "extract:qa:data": "xtrem layers --extract qa", "extract:setup:data": "xtrem layers --extract setup", "extract:test:data": "xtrem layers --extract test", "load:demo:data": "xtrem layers --load setup,demo", "load:layer:data": "xtrem layers --load $XTREM_LAYER", "load:perf:data": "xtrem layers --load setup,master-data,perf", "load:qa:data": "xtrem layers --load setup,qa", "load:setup:data": "xtrem layers --load setup", "load:test:data": "xtrem layers --load setup,test", "manage": "xtrem manage", "pg-anonymizer": "xtrem pg-anonymizer", "schema": "xtrem schema", "schema:create": "xtrem schema --create", "schema:reset": "xtrem schema --reset", "schema:restore": "xtrem schema --restore-from-s3", "schema:upgrade": "xtrem upgrade --run --dev", "schema:upgrade:test": "pnpm xtrem -- upgrade --test", "sqs:clean": "pnpm -sw sqs:clean", "sqs:reset": "pnpm -sw sqs:reset", "sqs:setup": "pnpm -sw sqs:setup", "sqs:stop": "pnpm -sw sqs:stop", "sqs:sync": "pnpm -sw sqs:sync", "start": "xtrem start", "start:flamegraph": "echo 'Cannot be started with NPM. Run the following command directly in bash: \nUNSECURE_DEV_LOGIN=TRUE 0x -- node ./node_modules/.bin/xtrem start'", "start:unsecuredevlogin": "UNSECURE_DEV_LOGIN=1 XTREM_ALL_METRICS_ENABLED=1 pnpm run start", "tenant": "xtrem tenant", "test:smoke": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test", "test:smoke:ci": "xtrem test 'test/cucumber/smoke-test*.feature' --integration --layers=test --ci", "test:smoke:static": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test", "test:smoke:static:ci": "xtrem test test/cucumber/smoke-test-static.feature --integration --layers=test --ci", "xtrem": "xtrem"}}