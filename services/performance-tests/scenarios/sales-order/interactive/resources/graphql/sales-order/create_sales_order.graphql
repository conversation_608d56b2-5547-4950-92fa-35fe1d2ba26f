mutation CreateMutation(
    $paymentTerm: IntReference
    $soldToCustomer: IntReference
    $salesSite: IntReference
    $item: IntReference
    $shipToCustomerAddress: IntReference
    $unit: IntReference
) {
    xtremSales {
        salesOrder {
            create(
                data: {
                    paymentTerm: $paymentTerm
                    soldToCustomer: $soldToCustomer
                    salesSite: $salesSite
                    requestedDeliveryDate: "2022-04-29"
                    shippingDate: "2022-04-29"
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            item: $item
                            quantity: 10
                            quantityInStockUnit: 10
                            requestedDeliveryDate: "2022-04-29"
                            salesSite: $salesSite
                            unit: $salesUnit
                            unitToStockUnitConversionFactor: 1
                            shipToCustomerAddress: $shipToCustomerAddress
                            stockSite: $salesSite
                        }
                    ]
                }
            ) {
                _id
                number
                salesSite {
                    name
                    legalCompany__name: legalCompany {
                        currency__name: currency {
                            _id
                            name
                            id
                            symbol
                            decimalDigits
                        }
                        legislation__name: legislation {
                            _id
                            name
                            id
                        }
                        _id
                        name
                        id
                        taxEngine
                    }
                    _id
                    id
                    description
                    isInventory
                }
                customerNumber
                orderDate
                soldToCustomer {
                    businessEntity {
                        name
                    }
                    primaryAddress__name: primaryAddress {
                        country__name: country {
                            name
                            _id
                        }
                        _id
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                    businessEntity__id: businessEntity {
                        id
                        name
                        _id
                    }
                    country__name: country {
                        name
                        _id
                    }
                    _id
                    taxIdNumber
                }
                soldToLinkedAddress {
                    name
                    businessEntity__id: businessEntity {
                        id
                        _id
                    }
                    country__name: country {
                        name
                        id
                        _id
                        regionLabel
                        zipLabel
                    }
                    _id
                    isActive
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                soldToAddress {
                    country__name: country {
                        name
                        id
                        _id
                        regionLabel
                        zipLabel
                    }
                    _id
                    isActive
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                currency {
                    name
                    id
                    symbol
                    decimalDigits
                    _id
                }
                taxEngine
                companyCurrency {
                    name
                    id
                    decimalDigits
                    rounding
                    symbol
                    _id
                }
                fxRateDate
                companyFxRate
                companyFxRateDivisor
                rateDescription
                status
                taxCalculationStatus
                isOnHold
                lines {
                    query(first: 10, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                shipToAddress {
                                    country__name: country {
                                        name
                                        id
                                        _id
                                        regionLabel
                                        zipLabel
                                    }
                                    _id
                                    name
                                    locationPhoneNumber
                                    addressLine1
                                    addressLine2
                                    city
                                    region
                                    postcode
                                    concatenatedAddress
                                }
                                priceReason__name: priceReason {
                                    _id
                                    isActive
                                    name
                                    description
                                }
                                priceReasonDeterminated__name: priceReasonDeterminated {
                                    _id
                                    isActive
                                    name
                                    description
                                }
                                deliveryMode__name: deliveryMode {
                                    _id
                                    name
                                    description
                                }
                                entityUse__name: entityUse {
                                    name
                                    id
                                    description
                                    _id
                                }
                                shipToAddress__shipToAddress__name: shipToCustomerAddress {
                                    shipToCustomerAddress {
                                        name
                                    }
                                    shipmentSite__name: shipmentSite {
                                        legalCompany__id: legalCompany {
                                            _id
                                            id
                                        }
                                        name
                                        _id
                                    }
                                    deliveryMode__name: deliveryMode {
                                        _id
                                        name
                                    }
                                    incoterm__name: incoterm {
                                        _id
                                        name
                                    }
                                    shipToAddress__name: shipToCustomerAddress {
                                        businessEntity__id: businessEntity {
                                            id
                                            _id
                                        }
                                        country__name: country {
                                            name
                                            _id
                                        }
                                        _id
                                        isActive
                                        name
                                        addressLine1
                                        addressLine2
                                        city
                                        region
                                        postcode
                                        locationPhoneNumber
                                    }
                                    customer__name: customer {
                                        _id
                                        name
                                    }
                                    _id
                                    isPrimary
                                    deliveryLeadTime
                                }
                                uSalesUnit__name: uSalesUnit {
                                    _id
                                    name
                                    decimalDigits
                                    id
                                }
                                salesUnit__name: salesUnit {
                                    _id
                                    name
                                    decimalDigits
                                    id
                                }
                                stockSite__name: stockSite {
                                    id
                                    name
                                    description
                                    _id
                                }
                                uStockUnit__name: uStockUnit {
                                    _id
                                    name
                                    decimalDigits
                                    id
                                }
                                stockUnit__name: stockUnit {
                                    _id
                                    name
                                    decimalDigits
                                    id
                                }
                                salesSite__name: salesSite {
                                    legalCompany__name: legalCompany {
                                        legislation__name: legislation {
                                            _id
                                            name
                                            id
                                        }
                                        _id
                                        name
                                        id
                                    }
                                    _id
                                    name
                                    id
                                    description
                                }
                                itemImage {
                                    value
                                }
                                item__name: item {
                                    salesUnit__name: salesUnit {
                                        name
                                        id
                                        _id
                                        decimalDigits
                                    }
                                    stockUnit__name: stockUnit {
                                        _id
                                        name
                                        id
                                        decimalDigits
                                    }
                                    name
                                    id
                                    minimumSalesQuantity
                                    maximumSalesQuantity
                                    _id
                                }
                                _id
                                originDocumentType
                                shippingStatus
                                invoiceStatus
                                uStatus
                                taxCalculationStatus
                                taxDate
                                itemId
                                itemDescription
                                status
                                uQuantityInStockUnit
                                availableQuantityInStockUnit
                                stockShortageInStockUnit
                                quantity
                                uQuantityInSalesUnit
                                availableQuantityInSalesUnit
                                unitToStockUnitConversionFactor
                                stockShortageInSalesUnit
                                quantityInStockUnit
                                stockShortageStatus
                                quantityToShipInProgressInSalesUnit
                                shippedQuantityInSalesUnit
                                remainingQuantityToShipInSalesUnit
                                quantityToInvoiceInProgressInSalesUnit
                                invoicedQuantityInSalesUnit
                                remainingQuantityToInvoiceInSalesUnit
                                requestedDeliveryDate
                                deliveryLeadTime
                                shippingDate
                                expectedDeliveryDate
                                doNotShipBeforeDate
                                doNotShipAfterDate
                                grossPrice
                                discount
                                charge
                                grossPriceDeterminated
                                discountDeterminated
                                chargeDeterminated
                                priceOriginDeterminated
                                isPriceDeterminated
                                netPrice
                                priceOrigin
                                amountExcludingTax
                                amountExcludingTaxInCompanyCurrency
                                taxAmount
                                taxAmountAdjusted
                                amountIncludingTax
                                amountIncludingTaxInCompanyCurrency
                                computedAttributes
                                storedAttributes
                                storedDimensions
                                uiTaxes
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                shipToCustomer {
                    businessEntity {
                        name
                    }
                    businessEntity__id: businessEntity {
                        id
                        _id
                    }
                    country__name: country {
                        name
                        _id
                    }
                    _id
                    name
                    id
                    taxIdNumber
                }
                shipToCustomerAddress {
                    shipToCustomerAddress {
                        name
                    }
                    shipmentSite__name: shipmentSite {
                        legalCompany__id: legalCompany {
                            _id
                            id
                        }
                        name
                        _id
                    }
                    deliveryMode__name: deliveryMode {
                        _id
                        name
                    }
                    incoterm__name: incoterm {
                        _id
                        name
                    }
                    shipToAddress__name: shipToCustomerAddress {
                        businessEntity__id: businessEntity {
                            id
                            _id
                        }
                        country__name: country {
                            name
                            id
                            _id
                            regionLabel
                            zipLabel
                        }
                        _id
                        isActive
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        locationPhoneNumber
                    }
                    customer__name: customer {
                        _id
                        name
                    }
                    _id
                    isPrimary
                    deliveryLeadTime
                }
                shipToAddress {
                    country__name: country {
                        name
                        id
                        _id
                        regionLabel
                        zipLabel
                    }
                    _id
                    isActive
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                stockSite {
                    name
                    legalCompany__name: legalCompany {
                        _id
                        id
                        name
                    }
                    _id
                    id
                    description
                }
                incoterm {
                    name
                    _id
                    id
                    description
                }
                deliveryMode {
                    name
                    _id
                    description
                }
                requestedDeliveryDate
                deliveryLeadTime
                workDays
                shippingDate
                expectedDeliveryDate
                doNotShipBeforeDate
                doNotShipAfterDate
                shippingStatus
                billToCustomer {
                    businessEntity {
                        name
                    }
                    paymentTerm__name: paymentTerm {
                        name
                        _id
                    }
                    currency__name: currency {
                        id
                        name
                        symbol
                        _id
                    }
                    businessEntity__id: businessEntity {
                        id
                        _id
                    }
                    country__name: country {
                        name
                        _id
                    }
                    _id
                    name
                    id
                    taxIdNumber
                    isOnHold
                }
                billToLinkedAddress {
                    name
                    businessEntity__id: businessEntity {
                        id
                        _id
                    }
                    country__name: country {
                        name
                        id
                        _id
                        regionLabel
                        zipLabel
                    }
                    _id
                    isActive
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                paymentTerm {
                    name
                    description
                    _id
                }
                invoiceStatus
                billToAddress {
                    country__name: country {
                        name
                        id
                        _id
                        regionLabel
                        zipLabel
                    }
                    _id
                    isActive
                    name
                    concatenatedAddress
                    locationPhoneNumber
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                }
                totalAmountExcludingTax
                totalTaxAmount
                totalTaxAmountAdjusted
                totalAmountIncludingTax
                taxDetails: taxes {
                    query(first: 10, orderBy: "{\"taxCategory\":1,\"tax\":1}") {
                        edges {
                            node {
                                _id
                                taxCategory
                                tax
                                taxableAmount
                                taxRate
                                taxAmount
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                totalAmountExcludingTaxInCompanyCurrency
                totalAmountIncludingTaxInCompanyCurrency
                entityUse {
                    name
                    id
                    description
                    _id
                }
            }
        }
    }
}
