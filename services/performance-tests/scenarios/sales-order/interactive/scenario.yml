scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/sales-order/*'
    flow:
      - log: '***********************************************************************************************************************************'
      - log: "create sales order  payment term:'{{paymentTermNameVal}}', customer:'{{customerIdVal}}', sales site:'{{salesSiteIdVal}}', line item:'{{lineItemIdVal}}'"
      - post:
          url: /api
          json:
            query: get_payment_term.graphql
            variables:
              filter: '{"name":{"_eq":"{{ paymentTermNameVal }}"}}'
          capture:
            - json: data.xtremMasterData.paymentTerm.query.edges[0].node._id
              as: paymentTermId
          expect:
            - notHasProperty: errors
            - hasProperty: data.xtremMasterData.paymentTerm.query.edges[0].node._id
            - statusCode: 200
          name: get_payment_term
      - log: "=> payment term's id of '{{paymentTermNameVal}}':{{paymentTermId}}"

      - post:
          url: /api
          json:
            query: get_sold_to_customer.graphql
            variables:
              filter: '{"id":{"_eq":"{{ customerIdVal }}"}}'
          capture:
            - json: data.xtremMasterData.customer.query.edges[0].node._id
              as: soldToCustomerId
          expect:
            - notHasProperty: errors
            - hasProperty: data.xtremMasterData.customer.query.edges[0].node._id
            - statusCode: 200
          name: get_sold_to_customer
      - log: "=> customer's id of '{{customerIdVal}}':{{soldToCustomerId}}"

      - post:
          url: /api
          json:
            query: get_sales_site.graphql
            variables:
              filter: '{"id":{"_eq":"{{ salesSiteIdVal}}"}}'
          capture:
            - json: data.xtremSystem.site.query.edges[0].node._id
              as: salesSiteId
          expect:
            - notHasProperty: errors
            - hasProperty: data.xtremSystem.site.query.edges[0].node._id
            - statusCode: 200
          name: get_sales_site
      - log: "=> sales site's id of '{{salesSiteIdVal}}':{{salesSiteId}}"

      - post:
          url: /api
          json:
            query: get_lines_item.graphql
            variables:
              filter: '{"item":{"id":{"_eq":"{{ lineItemIdVal }}"}}}'
          capture:
            - json: data.xtremSales.salesOrderLine.query.edges[0].node.item._id
              as: lineItemId
          expect:
            - notHasProperty: errors
            - hasProperty: data.xtremSales.salesOrderLine.query.edges[0].node.item._id
            - statusCode: 200
          name: get_lines_item

      - log: "=> line's id of '{{lineItemIdVal}}':{{lineItemId}}"

      - post:
          url: /api
          json:
            query: get_ship_to_address.graphql
            variables:
              filter: '{"customer":{"id":{"_eq":"{{ customerIdVal }}"}}}'
          capture:
            - json: data.xtremMasterData.customerDeliveryAddress.query.edges[0].node._id
              as: shipToAddressId
          expect:
            - notHasProperty: errors
            - hasProperty: data.xtremMasterData.customerDeliveryAddress.query.edges[0].node._id
            - statusCode: 200
          name: get_ship_to_address
      - log: "=> ship to addresss id of '{{customerIdVal}}':{{shipToAddressId}}"

      - post:
          url: /api
          json:
            query: get_unit_of_measure.graphql
            variables:
              filter: '{"id":{"_eq":"{{ salesUnitIdVal }}"}}'
          capture:
            - json: data.xtremMasterData.unitOfMeasure.query.edges[0].node._id
              as: salesUnitId
          expect:
            - notHasProperty: errors
            - hasProperty: data.xtremMasterData.unitOfMeasure.query.edges[0].node._id
            - statusCode: 200
          name: get_unit_of_measure

      - log: "=> sales unit's id of '{{salesUnitIdVal}}':{{salesUnitId}}"

      - post:
          url: /api
          json:
            query: create_sales_order.graphql
            variables:
              paymentTerm: '_id:{{ paymentTermId }}'
              soldToCustomer: '_id:{{ soldToCustomerId }}'
              salesSite: '_id:{{ salesSiteId }}'
              item: '_id:{{ lineItemId }}'
              shipToCustomerAddress: '_id:{{ shipToAddressId }}'
              unit: '_id:{{ salesUnitId }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: create_sales_order
      - log: 'Stress data:'
      - log: '"{{paymentTermId}}","{{soldToCustomerId}}","{{salesSiteId}}","{{lineItemId}}","{{shipToAddressId}}","{{salesUnitId}}"'
