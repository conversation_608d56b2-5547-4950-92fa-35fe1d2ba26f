config:
  plugins:
    xtrem: {}
    ensure: {}
    expect: {}

    metrics-by-endpoint:
      # Group metrics by request name rather than URL:
      useOnlyRequestNames: false

  phases:
    - duration: '{{ $processEnvironment.RAMP_UP }}'
      arrivalCount: '{{ $processEnvironment.TOTAL_USERS }}'
      name: 'Ramp up {{ $processEnvironment.TOTAL_USERS }} users in {{ $processEnvironment.RAMP_UP }} seconds'
    - pause: '{{ $processEnvironment.DURATION }}'
      name: 'Stress {{ $processEnvironment.TOTAL_USERS }} users for {{ $processEnvironment.DURATION }} seconds'
  processor: '../../../../../platform/back-end/artillery-plugin-xtrem/lib/resources/processor.js'
  environments:
    local:
      target: http://localhost:8240
      payload:
        path: './resources/data/create-sales-order-interactive.csv'
        fields:
          - 'paymentTermNameVal'
          - 'customerIdVal'
          - 'salesSiteIdVal'
          - 'lineItemIdVal'
          - 'shipToAddressIdVal'
          - 'salesUnitIdVal'
        order: sequence
        skipHeader: true
    devops:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: '{{ $processEnvironment.CLUSTER }}'
