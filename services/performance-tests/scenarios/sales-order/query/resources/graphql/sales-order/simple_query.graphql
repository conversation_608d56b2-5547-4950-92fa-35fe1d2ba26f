{
    xtremSales {
        salesOrder {
            query(filter: "{ _id: { _eq: '7' } }") {
                edges {
                    node {
                        billToAddress {
                            _id
                            isActive
                            name
                        }
                        paymentTerm {
                            name
                            description
                            _id
                        }
                        billToLinkedAddress {
                            name
                            _id
                            isActive
                        }
                        billToCustomer {
                            _id
                            name
                            isOnHold
                        }
                        deliveryMode {
                            name
                            _id
                            description
                        }
                        incoterm {
                            name
                            _id
                            id
                            description
                        }
                        stockSite {
                            name
                            _id
                            id
                            description
                        }
                        shipToAddress {
                            _id
                            isActive
                            name
                            locationPhoneNumber
                            addressLine1
                            addressLine2
                            city
                            region
                            postcode
                        }
                        shipToCustomerAddress {
                            _id
                            isPrimary
                            deliveryLeadTime
                        }
                        shipToCustomer {
                            _id
                            name
                            id
                            taxIdNumber
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
