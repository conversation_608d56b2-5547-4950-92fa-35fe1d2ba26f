scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/sales-order/*'
    flow:
      - function: 'initializeDuration'
      - log: 'elapsed time: {{ elapsedTime }} / {{ duration }}'
      - log: 'sales-order-query in tenant {{tenant}} with user {{email}}'
      - post:
          beforeRequest: 'checkDuration'
          ifTrue: 'continueLooping'
          url: /api
          json:
            query: simple_query.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: sales-order-query
