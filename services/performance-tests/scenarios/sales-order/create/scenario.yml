scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/sales-order/*'
    flow:
      - function: 'getSalesOrderValues'
      - log: 'email:{{ email }} payment term:{{ paymentTermIdVal }} sold to customer:{{ soldToCustomerIdVal }} site:{{ salesSiteIdVal }} line item:{{ lineItemIdVal }} ship to address:{{ shipToAddressIdVal }} unit:{{salesUnitIdVal}}'
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'
      - post:
          url: /api
          json:
            query: create-sales-order.graphql
            variables:
              paymentTerm: '#{{ paymentTermIdVal }}'
              soldToCustomer: '#{{ soldToCustomerIdVal }}'
              salesSite: '#{{ salesSiteIdVal }}'
              item: '#{{ lineItemIdVal }}'
              shipToCustomerAddress: '#{{ shipToAddressIdVal }}'
              unit: '#{{salesUnitIdVal}}'
              email: '{{ email }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: sales-order-create
