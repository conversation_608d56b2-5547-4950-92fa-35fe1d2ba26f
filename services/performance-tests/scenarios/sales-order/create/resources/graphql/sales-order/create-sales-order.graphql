mutation CreateMutation(
    $paymentTerm: IntReference
    $soldToCustomer: IntReference
    $salesSite: IntReference
    $item: IntReference
    $shipToCustomerAddress: IntReference
    $unit: IntReference
) {
    xtremSales {
        salesOrder {
            create(
                data: {
                    paymentTerm: $paymentTerm
                    soldToCustomer: $soldToCustomer
                    salesSite: $salesSite
                    requestedDeliveryDate: "2022-04-29"
                    shippingDate: "2022-04-29"
                    lines: [
                        {
                            _action: "create"
                            _id: "-1"
                            item: $item
                            quantity: 10
                            quantityInStockUnit: 10
                            requestedDeliveryDate: "2022-04-29"
                            salesSite: $salesSite
                            unit: $salesUnit
                            unitToStockUnitConversionFactor: 1
                            shipToCustomerAddress: $shipToCustomerAddress
                            stockSite: $salesSite
                        }
                    ]
                }
            ) {
                _id
            }
        }
    }
}
