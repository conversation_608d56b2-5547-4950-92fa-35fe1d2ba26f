- log: 'create sales order'
- post:
    url: /api
    json:
      query: create_sales_order.graphql
      variables:
        paymentTerm: '_id:{{ paymentTermIdVal }}'
        soldToCustomer: '_id:{{ soldToCustomerIdVal }}'
        salesSite: '_id:{{ salesSiteIdVal }}'
        item: '_id:{{ lineItemIdVal }}'
        shipToCustomerAddress: '_id:{{ shipToAddressIdVal }}'
        unit: '_id:{{ salesUnitIdVal }}'
    expect:
      - notHasProperty: errors
      - statusCode: 200
    name: create_sales_order
