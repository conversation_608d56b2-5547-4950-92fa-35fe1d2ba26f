//
// This script implements functions specific to the sales-order/create scenario
//
module.exports = {
    // Sales-order-create functions:
    getSalesOrderValues: getSalesOrderValues,
};

let salesOrderValueId = 0;

const values = [
    {
        paymentTermIdVal: 'DUE_UPON_RECEIPT_ALL',
        soldToCustomerIdVal: 'CN007',
        salesSiteIdVal: 'S01-UK',
        lineItemIdVal: 'IMPS0001',
        shipToAddressIdVal: 'CN007|30',
        salesUnitIdVal: 'METER',
    },
    {
        paymentTermIdVal: 'DUE_UPON_RECEIPT_ALL',
        soldToCustomerIdVal: 'CN013',
        salesSiteIdVal: 'S01-FR',
        lineItemIdVal: 'IMPS0010',
        shipToAddressIdVal: 'CN013|40',
        salesUnitIdVal: 'QUART',
    },
    {
        paymentTermIdVal: 'DUE_UPON_RECEIPT_ALL',
        soldToCustomerIdVal: 'CN019',
        salesSiteIdVal: 'S01-ZA',
        lineItemIdVal: 'IMPS0080',
        shipToAddressIdVal: 'CN019|50',
        salesUnitIdVal: 'CENTIMETER',
    },
    {
        paymentTermIdVal: 'DUE_UPON_RECEIPT_ALL',
        soldToCustomerIdVal: 'CN025',
        salesSiteIdVal: 'S02-US',
        lineItemIdVal: 'IMPS0090',
        shipToAddressIdVal: 'CN025|50',
        salesUnitIdVal: 'INCH',
    },
    {
        paymentTermIdVal: 'DUE_UPON_RECEIPT_ALL',
        soldToCustomerIdVal: 'CN031',
        salesSiteIdVal: 'S02-UK',
        lineItemIdVal: 'IMPS0359',
        shipToAddressIdVal: 'CN031|50',
        salesUnitIdVal: 'INCH',
    },
    {
        paymentTermIdVal: 'DUE_UPON_RECEIPT_ALL',
        soldToCustomerIdVal: 'CN037',
        salesSiteIdVal: 'S02-FR',
        lineItemIdVal: 'IMPS0051',
        shipToAddressIdVal: 'CN037|10',
        salesUnitIdVal: 'SQUARE_CENTIMETER',
    },
];

/*
Loads values at position context.vars.$loopCount in array values
*/
function getSalesOrderValues(context, events, done) {
    if (salesOrderValueId >= values.length) {
        salesOrderValueId = 0;
    }

    context.vars = {
        ...context.vars,
        ...values[salesOrderValueId],
    };

    salesOrderValueId++;

    return done(); // MUST be called for the scenario to continue
}
