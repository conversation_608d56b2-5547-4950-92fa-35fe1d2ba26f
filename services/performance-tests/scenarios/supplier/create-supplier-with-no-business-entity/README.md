# Load demo data (locally) and run xtrem server and

## For testing purpose locally, load test data:

```bash
pnpm run load:test:data
```

## Make sure xtrem project is built and xtrem is started with unsecure dev login:

```bash
pnpm run start:unsecuredevlogin
```

# run test script

## run scenario test

```bash
  ../../scripts/artillery/scenarios-test-run.sh  --scenario supplier/create-supplier-with-no-business-entity --total-users 5 --ramp-up 15 --duration 30
```
