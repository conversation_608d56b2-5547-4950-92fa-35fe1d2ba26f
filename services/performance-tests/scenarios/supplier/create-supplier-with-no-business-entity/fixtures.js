module.exports = {
    generatePayloadVariables: generatePayloadVariables,
};
const tools = require('../../../shared/tools');
/**
 * Hook called from the scenario loop in order to set up variables that have uniqueness constrains
 * See https://www.artillery.io/docs/guides/guides/http-reference#function-steps-in-a-flow
 * @param {*} context
 * @param {*} events
 * @param {*} done
 * @returns
 */
function generatePayloadVariables(context, events, done) {
    // Set the "query" variables for the virtual user.
    context.vars['supplier_uk_tax_id'] = tools.generateId();
    context.vars['supplier_new_id'] = tools.generateId('SUPPLIER_', 4);
    return done();
}
