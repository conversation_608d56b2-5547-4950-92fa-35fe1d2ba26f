query {
    xtremMasterData {
        supplier {
            getDefaults(data: {}) {
                purchaseOrders {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                totalAmountExcludingTax
                                paymentTerm {
                                    _id
                                    name
                                }
                                orderDate
                                stockSite {
                                    _id
                                    name
                                }
                                site {
                                    _id
                                    name
                                }
                                approvalStatus
                                status
                                number
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                defaultBuyer {
                    _id
                    displayName
                    email
                    firstName
                    lastName
                }
                postingClass {
                    _id
                    name
                }
                supplierPrices: prices {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                currency {
                                    _id
                                    decimalDigits
                                    name
                                    id
                                }
                                price
                                unit {
                                    _id
                                    decimalDigits
                                    name
                                    id
                                }
                                toQuantity
                                fromQuantity
                                dateValidTo
                                dateValidFrom
                                dateValid
                                site {
                                    _id
                                    name
                                    id
                                }
                                item {
                                    _id
                                    name
                                    id
                                }
                                priority
                                type
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                items {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                supplier {
                                    _id
                                    name
                                    id
                                }
                                purchaseLeadTime
                                minimumPurchaseQuantity
                                purchaseUnitOfMeasure {
                                    _id
                                    name
                                    id
                                }
                                isDefaultItemSupplier
                                supplierItemName
                                supplierItemCode
                                item {
                                    _id
                                    name
                                    id
                                }
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                certificates {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                certificationBody
                                validUntil
                                dateOfCertification
                                dateOfOriginalCertification
                                standard {
                                    _id
                                    id
                                }
                                id
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                returnToAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                payToAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                billByAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                paymentMethod
                paymentTerm {
                    _id
                    name
                    description
                }
                returnToSupplier {
                    _id
                    name
                    id
                    primaryAddress {
                        _id
                        name
                        locationPhoneNumber
                        country {
                            _id
                            id
                            name
                        }
                        postcode
                        region
                        city
                        addressLine2
                        addressLine1
                    }
                    paymentTerm {
                        _id
                        name
                        description
                    }
                    businessEntity {
                        _id
                        id
                        name
                    }
                }
                payToSupplier {
                    _id
                    name
                    id
                    primaryAddress {
                        _id
                        name
                        locationPhoneNumber
                        country {
                            _id
                            id
                            name
                        }
                        postcode
                        region
                        city
                        addressLine2
                        addressLine1
                    }
                    paymentTerm {
                        _id
                        name
                        description
                    }
                    businessEntity {
                        _id
                        id
                        name
                    }
                }
                billBySupplier {
                    _id
                    name
                    id
                    primaryAddress {
                        _id
                        name
                        locationPhoneNumber
                        country {
                            _id
                            id
                            name
                        }
                        postcode
                        region
                        city
                        addressLine2
                        addressLine1
                    }
                    paymentTerm {
                        _id
                        name
                        description
                    }
                    businessEntity {
                        _id
                        id
                        name
                    }
                }
                incoterm {
                    _id
                    name
                    id
                }
                deliveryMode {
                    _id
                    name
                    description
                }
                parent {
                    _id
                    businessEntity {
                        name
                    }
                    id
                    name
                }
                minimumOrderAmount
                primaryAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        id
                        name
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                displayAddresses: addresses {
                    query(first: 20, orderBy: "{\"name\":1}") {
                        edges {
                            node {
                                isActive
                                intacctIntegrationState
                                isPrimary
                                locationPhoneNumber
                                concatenatedAddressWithoutName
                                country {
                                    _id
                                    name
                                    zipLabel
                                    regionLabel
                                    id
                                }
                                postcode
                                region
                                city
                                addressLine2
                                addressLine1
                                name
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                addresses {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                intacctIntegrationState
                                concatenatedAddressWithoutName
                                _id
                                locationPhoneNumber
                                country {
                                    _id
                                    name
                                    zipLabel
                                    regionLabel
                                    id
                                }
                                postcode
                                region
                                city
                                addressLine2
                                addressLine1
                                name
                                isPrimary
                                isActive
                                businessEntity {
                                    _id
                                }
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                standardIndustrialClassification {
                    _id
                    sicCode
                    sicDescription
                }
                supplierType
                siret
                legalEntity
                businessEntity {
                    _id
                    name
                    id
                    image {
                        value
                    }
                    isSite
                    isSupplier
                    isCustomer
                    legalEntity
                    currency {
                        _id
                        name
                        decimalDigits
                        symbol
                        id
                    }
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    siret
                    taxIdNumber
                    isActive
                }
                isActive
            }
        }
    }
}
