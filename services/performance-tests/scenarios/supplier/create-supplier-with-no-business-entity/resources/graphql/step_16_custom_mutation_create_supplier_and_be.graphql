mutation CreateMutation(
    $supplier_uk_tax_id: String
    $business_entity_id: String
    $business_entity_id_name: String
    $business_entity_primary_address_name: String
    $payment_term: IntReference
) {
    xtremMasterData {
        supplier {
            createSupplierAndCreateOrUpdateBusinessEntity(
                inputBusinessEntity: {
                    _id: null
                    id: $business_entity_id
                    name: $business_entity_id_name
                    currency: "3"
                    country: "4"
                    taxIdNumber: $supplier_uk_tax_id
                    siret: ""
                    isSupplier: true
                    isCustomer: false
                    isSite: false
                    legalEntity: "corporation"
                    image: null
                    addresses: [
                        {
                            _id: "-1"
                            addressLine1: "Some street"
                            addressLine2: "Some street 2"
                            city: "Some city"
                            country: "_id:4"
                            isActive: true
                            isPrimary: true
                            locationPhoneNumber: ""
                            name: $business_entity_primary_address_name
                            postcode: "22222"
                            region: "Some county"
                            contacts: []
                        }
                    ]
                }
                inputSupplier: {
                    isActive: true
                    businessEntity: null
                    supplierType: null
                    standardIndustrialClassification: null
                    primaryAddress: "_id:-1"
                    minimumOrderAmount: 0
                    parent: null
                    deliveryMode: null
                    incoterm: null
                    billBySupplier: "_id:-1"
                    payToSupplier: "_id:-1"
                    returnToSupplier: "_id:-1"
                    paymentTerm: $payment_term
                    paymentMethod: null
                    billByAddress: "_id:-1"
                    payToAddress: "_id:-1"
                    returnToAddress: "_id:-1"
                    postingClass: null
                    defaultBuyer: null
                }
                inputSupplierItems: []
                inputSupplierCertificates: []
            ) {
                _id
            }
        }
    }
}
