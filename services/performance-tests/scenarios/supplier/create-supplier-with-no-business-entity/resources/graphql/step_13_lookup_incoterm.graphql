query {
    xtremMasterData {
        supplier {
            lookups(
                data: {
                    isActive: true
                    businessEntity: null
                    supplierType: null
                    standardIndustrialClassification: null
                    primaryAddress: "_id:-1"
                    minimumOrderAmount: 0
                    parent: null
                    deliveryMode: null
                    incoterm: null
                    billBySupplier: "_id:-1"
                    payToSupplier: "_id:-1"
                    returnToSupplier: "_id:-1"
                    paymentTerm: null
                    paymentMethod: null
                    billByAddress: "_id:-1"
                    payToAddress: "_id:-1"
                    returnToAddress: "_id:-1"
                    postingClass: null
                    defaultBuyer: null
                }
            ) {
                incoterm(orderBy: "{\"name\":1}") {
                    edges {
                        node {
                            _id
                            name
                            id
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
