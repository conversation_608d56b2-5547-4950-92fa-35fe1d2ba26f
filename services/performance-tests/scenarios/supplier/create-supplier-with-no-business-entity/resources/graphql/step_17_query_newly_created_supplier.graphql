query ReadQuery($filter: String) {
    xtremMasterData {
        supplier {
            query(filter: $filter, first: 30) {
                edges {
                    node {
                        _id
                        defaultBuyer {
                            _id
                            displayName
                        }
                        paymentMethod
                        paymentTerm {
                            _id
                            name
                        }
                        siret
                        taxIdNumber
                        legalEntity
                        currency {
                            _id
                            name
                        }
                        country {
                            _id
                            name
                        }
                        businessEntity {
                            _id
                            id
                            name
                        }
                        image {
                            value
                        }
                        primaryAddress {
                            _id
                            name
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
