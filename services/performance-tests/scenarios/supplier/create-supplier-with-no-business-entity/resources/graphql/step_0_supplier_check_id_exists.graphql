query ReadQuery($filter: String) {
    xtremMasterData {
        supplier {
            query(filter: $filter) {
                edges {
                    node {
                        _id
                        id
                        name
                        taxIdNumber
                        country {
                            _id
                            id
                            name
                        }
                        currency {
                            _id
                            id
                            name
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
