query ReadQuery($filterSupplierContact: String) {
    xtremMasterData {
        businessEntityAddressContact {
            query(filter: $filterSupplierContact) {
                edges {
                    node {
                        _id
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
