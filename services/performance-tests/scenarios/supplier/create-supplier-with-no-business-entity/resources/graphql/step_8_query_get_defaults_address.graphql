query {
    nestedDefaults: xtremMasterData {
        businessEntityAddress {
            getDefaults {
                isActive
                intacctIntegrationState
                isPrimary
                locationPhoneNumber
                concatenatedAddressWithoutName
                country {
                    _id
                    name
                    zipLabel
                    regionLabel
                    id
                }
                postcode
                region
                city
                addressLine2
                addressLine1
                name
            }
        }
    }
}
