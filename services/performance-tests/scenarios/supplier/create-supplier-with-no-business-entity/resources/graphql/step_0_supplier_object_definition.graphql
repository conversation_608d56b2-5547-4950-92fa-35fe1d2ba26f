query {
    RegionLabel: __type(name: "<PERSON>Label") {
        enumValues {
            name
        }
    }
    ZipLabel: __type(name: "<PERSON><PERSON><PERSON>abe<PERSON>") {
        enumValues {
            name
        }
    }
    LegalEntity: __type(name: "LegalEntity") {
        enumValues {
            name
        }
    }
    SupplierType: __type(name: "SupplierType") {
        enumValues {
            name
        }
    }
    IntegrationState: __type(name: "IntegrationState") {
        enumValues {
            name
        }
    }
    Title: __type(name: "Title") {
        enumValues {
            name
        }
    }
    ContactRole: __type(name: "ContactRole") {
        enumValues {
            name
        }
    }
    PurchaseDocumentStatus: __type(name: "PurchaseDocumentStatus") {
        enumValues {
            name
        }
    }
    PurchaseDocumentApprovalStatus: __type(name: "PurchaseDocumentApprovalStatus") {
        enumValues {
            name
        }
    }
    BusinessEntity: __type(name: "BusinessEntity") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    BusinessEntity_Input: __type(name: "BusinessEntity_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Country: __type(name: "Country") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Country_Input: __type(name: "Country_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Currency: __type(name: "Currency") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Currency_Input: __type(name: "Currency_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    StandardIndustrialClassification: __type(name: "StandardIndustrialClassification") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    StandardIndustrialClassification_Input: __type(name: "StandardIndustrialClassification_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    BusinessEntityAddress: __type(name: "BusinessEntityAddress") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    BusinessEntityAddress_Input: __type(name: "BusinessEntityAddress_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    BusinessEntityAddressContact: __type(name: "BusinessEntityAddressContact") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    BusinessEntityAddressContact_Input: __type(name: "BusinessEntityAddressContact_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Supplier: __type(name: "Supplier") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Supplier_Input: __type(name: "Supplier_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    DeliveryMode: __type(name: "DeliveryMode") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    DeliveryMode_Input: __type(name: "DeliveryMode_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Incoterm: __type(name: "Incoterm") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Incoterm_Input: __type(name: "Incoterm_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    PaymentTerm: __type(name: "PaymentTerm") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    PaymentTerm_Input: __type(name: "PaymentTerm_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    SupplierCertificate: __type(name: "SupplierCertificate") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    SupplierCertificate_Input: __type(name: "SupplierCertificate_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Standard: __type(name: "Standard") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Standard_Input: __type(name: "Standard_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    ItemSupplier: __type(name: "ItemSupplier") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    ItemSupplier_Input: __type(name: "ItemSupplier_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Item: __type(name: "Item") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Item_Input: __type(name: "Item_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    UnitOfMeasure: __type(name: "UnitOfMeasure") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    UnitOfMeasure_Input: __type(name: "UnitOfMeasure_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    ItemSupplierPrice: __type(name: "ItemSupplierPrice") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    ItemPrice_Input: __type(name: "ItemPrice_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Site: __type(name: "Site") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Site_Input: __type(name: "Site_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    PostingClass: __type(name: "PostingClass") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    PostingClass_Input: __type(name: "PostingClass_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    User: __type(name: "User") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    User_Input: __type(name: "User_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    PurchaseOrder: __type(name: "PurchaseOrder") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    PurchaseOrder_Input: __type(name: "PurchaseOrder_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    rootNode: xtremMasterData {
        supplier {
            getDefaults(data: {}) {
                purchaseOrders {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                totalAmountExcludingTax
                                paymentTerm {
                                    _id
                                    name
                                }
                                orderDate
                                stockSite {
                                    _id
                                    name
                                }
                                site {
                                    _id
                                    name
                                }
                                approvalStatus
                                status
                                number
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                defaultBuyer {
                    _id
                    displayName
                    email
                    firstName
                    lastName
                }
                postingClass {
                    _id
                    name
                }
                supplierPrices: prices {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                currency {
                                    _id
                                    decimalDigits
                                    name
                                    id
                                }
                                price
                                unit {
                                    _id
                                    decimalDigits
                                    name
                                    id
                                }
                                toQuantity
                                fromQuantity
                                dateValidTo
                                dateValidFrom
                                dateValid
                                site {
                                    _id
                                    name
                                    id
                                }
                                item {
                                    _id
                                    name
                                    id
                                }
                                priority
                                type
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                items {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                supplier {
                                    _id
                                    name
                                    id
                                }
                                purchaseLeadTime
                                minimumPurchaseQuantity
                                purchaseUnitOfMeasure {
                                    _id
                                    name
                                    id
                                }
                                isDefaultItemSupplier
                                supplierItemName
                                supplierItemCode
                                item {
                                    _id
                                    name
                                    id
                                }
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                certificates {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                certificationBody
                                validUntil
                                dateOfCertification
                                dateOfOriginalCertification
                                standard {
                                    _id
                                    id
                                }
                                id
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                returnToAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                payToAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                billByAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                paymentMethod
                paymentTerm {
                    _id
                    name
                    description
                }
                returnToSupplier {
                    _id
                    name
                    id
                    primaryAddress {
                        _id
                        name
                        locationPhoneNumber
                        country {
                            _id
                            id
                            name
                        }
                        postcode
                        region
                        city
                        addressLine2
                        addressLine1
                    }
                    paymentTerm {
                        _id
                        name
                        description
                    }
                    businessEntity {
                        _id
                        id
                        name
                    }
                }
                payToSupplier {
                    _id
                    name
                    id
                    primaryAddress {
                        _id
                        name
                        locationPhoneNumber
                        country {
                            _id
                            id
                            name
                        }
                        postcode
                        region
                        city
                        addressLine2
                        addressLine1
                    }
                    paymentTerm {
                        _id
                        name
                        description
                    }
                    businessEntity {
                        _id
                        id
                        name
                    }
                }
                billBySupplier {
                    _id
                    name
                    id
                    primaryAddress {
                        _id
                        name
                        locationPhoneNumber
                        country {
                            _id
                            id
                            name
                        }
                        postcode
                        region
                        city
                        addressLine2
                        addressLine1
                    }
                    paymentTerm {
                        _id
                        name
                        description
                    }
                    businessEntity {
                        _id
                        id
                        name
                    }
                }
                incoterm {
                    _id
                    name
                    id
                }
                deliveryMode {
                    _id
                    name
                    description
                }
                parent {
                    _id
                    businessEntity {
                        name
                    }
                    id
                    name
                }
                minimumOrderAmount
                primaryAddress {
                    _id
                    name
                    isActive
                    locationPhoneNumber
                    country {
                        _id
                        id
                        name
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    businessEntity {
                        _id
                        name
                        id
                    }
                }
                displayAddresses: addresses {
                    query(first: 20, orderBy: "{\"name\":1}") {
                        edges {
                            node {
                                isActive
                                intacctIntegrationState
                                isPrimary
                                locationPhoneNumber
                                concatenatedAddressWithoutName
                                country {
                                    _id
                                    name
                                    zipLabel
                                    regionLabel
                                    id
                                }
                                postcode
                                region
                                city
                                addressLine2
                                addressLine1
                                name
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                addresses {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                intacctIntegrationState
                                concatenatedAddressWithoutName
                                _id
                                locationPhoneNumber
                                country {
                                    _id
                                    name
                                    zipLabel
                                    regionLabel
                                    id
                                }
                                postcode
                                region
                                city
                                addressLine2
                                addressLine1
                                name
                                isPrimary
                                isActive
                                businessEntity {
                                    _id
                                }
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                standardIndustrialClassification {
                    _id
                    sicCode
                    sicDescription
                }
                supplierType
                siret
                legalEntity
                businessEntity {
                    _id
                    name
                    id
                    image {
                        value
                    }
                    isSite
                    isSupplier
                    isCustomer
                    legalEntity
                    currency {
                        _id
                        name
                        decimalDigits
                        symbol
                        id
                    }
                    country {
                        _id
                        name
                        zipLabel
                        regionLabel
                        id
                    }
                    siret
                    taxIdNumber
                    isActive
                }
                isActive
            }
        }
    }
    navigationPanelItems: xtremMasterData {
        supplier {
            query(orderBy: "{\"businessEntity\":{\"name\":1,\"id\":1}}") {
                edges {
                    node {
                        _id
                        defaultBuyer {
                            _id
                            displayName
                        }
                        paymentMethod
                        paymentTerm {
                            _id
                            name
                        }
                        siret
                        taxIdNumber
                        legalEntity
                        currency {
                            _id
                            name
                        }
                        country {
                            _id
                            name
                        }
                        businessEntity {
                            _id
                            id
                            name
                        }
                        image {
                            value
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
