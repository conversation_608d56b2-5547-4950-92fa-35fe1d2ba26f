query ReadQuery($filter: String) {
    xtremMasterData {
        businessEntity {
            query(filter: $filter) {
                edges {
                    node {
                        _id
                        id
                        name
                        isActive
                        taxIdNumber
                        siret
                        country {
                            _id
                            id
                            name
                            regionLabel
                            zipLabel
                        }
                        currency {
                            _id
                            id
                            name
                            decimalDigits
                            symbol
                        }
                        isCustomer
                        isSupplier
                        isSite
                        legalEntity
                        image {
                            value
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
