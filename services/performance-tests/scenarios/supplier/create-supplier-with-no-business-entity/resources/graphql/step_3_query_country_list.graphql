query {
    xtremStructure {
        country {
            query(orderBy: "{\"name\":1}", first: 10) {
                edges {
                    node {
                        _id
                        name
                        id
                        currency {
                            _id
                            symbol
                            decimalDigits
                            id
                            name
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
