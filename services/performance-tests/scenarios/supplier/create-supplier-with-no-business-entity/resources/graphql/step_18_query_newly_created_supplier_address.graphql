query ReadQuery($filterSupplierAddress: String) {
    xtremMasterData {
        businessEntityAddress {
            query(filter: $filterSupplierAddress) {
                edges {
                    node {
                        _id
                        contacts {
                            query(first: 100) {
                                edges {
                                    node {
                                        _id
                                        email
                                        locationPhoneNumber
                                        position
                                        role
                                        preferredName
                                        lastName
                                        firstName
                                        title
                                        isPrimary
                                        isActive
                                    }
                                    cursor
                                }
                                pageInfo {
                                    startCursor
                                    endCursor
                                    hasPreviousPage
                                    hasNextPage
                                }
                            }
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
