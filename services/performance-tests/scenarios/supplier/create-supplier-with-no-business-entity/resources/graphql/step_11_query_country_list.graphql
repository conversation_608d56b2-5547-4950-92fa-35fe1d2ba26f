query {
    xtremStructure {
        country {
            query(orderBy: "{\"name\":1}", first: 10) {
                edges {
                    node {
                        _id
                        name
                        id
                        zipLabel
                        regionLabel
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
