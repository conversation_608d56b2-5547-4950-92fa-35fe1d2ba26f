query {
    xtremMasterData {
        currency {
            query(filter: "{\"isActive\":true}", orderBy: "{\"name\":1}", first: 10) {
                edges {
                    node {
                        _id
                        isActive
                        name
                        id
                        decimalDigits
                        symbol
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
