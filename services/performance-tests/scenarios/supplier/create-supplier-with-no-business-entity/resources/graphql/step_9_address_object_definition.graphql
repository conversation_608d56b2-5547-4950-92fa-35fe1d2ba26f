query {
    RegionLabel: __type(name: "RegionLabel") {
        enumValues {
            name
        }
    }
    ZipLabel: __type(name: "<PERSON><PERSON><PERSON>abe<PERSON>") {
        enumValues {
            name
        }
    }
    WeekDays: __type(name: "WeekDays") {
        enumValues {
            name
        }
    }
    Title: __type(name: "Title") {
        enumValues {
            name
        }
    }
    ContactRole: __type(name: "ContactRole") {
        enumValues {
            name
        }
    }
    Country: __type(name: "Country") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Country_Input: __type(name: "Country_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Site: __type(name: "Site") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Site_Input: __type(name: "Site_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Company: __type(name: "Company") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Company_Input: __type(name: "Company_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    DeliveryMode: __type(name: "DeliveryMode") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    DeliveryMode_Input: __type(name: "DeliveryMode_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Incoterm: __type(name: "Incoterm") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    Incoterm_Input: __type(name: "Incoterm_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    BusinessEntityAddressContact: __type(name: "BusinessEntityAddressContact") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    BusinessEntityAddressContact_Input: __type(name: "BusinessEntityAddressContact_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    EntityUse: __type(name: "EntityUse") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                fields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        fields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
    EntityUse_Input: __type(name: "EntityUse_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
                kind
                inputFields {
                    name
                    type {
                        ofType {
                            name
                        }
                        name
                        enumValues {
                            name
                        }
                        kind
                        inputFields {
                            name
                            type {
                                ofType {
                                    name
                                }
                                name
                                enumValues {
                                    name
                                }
                                kind
                            }
                        }
                    }
                }
            }
        }
    }
}
