scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/*'
    flow:
      - function: 'generatePayloadVariables' #  fixtures : supplier_new_id, supplier_uk_tax_id (constraint : unique value per be)
      - log: Tax id "{{ supplier_uk_tax_id }}" is generated for this loop
      - log: 'Current environment is set to: {{ $environment }}'
      - post:
          url: /api
          json:
            query: step_0_supplier_object_definition.graphql
          expect:
            - hasProperty: data.Supplier.fields[6].name
            - notHasProperty: errors
            - statusCode: 200
          name: step_0_supplier_object_definition
      - log: Check if supplier id {{ supplier_new_id }} exists
      - post:
          url: /api
          json:
            query: step_0_supplier_check_id_exists.graphql
            variables:
              filter: "{id:{_regex:'^{{ supplier_new_id }}$',_options:'i'}}"
          expect:
            - notHasProperty: data.xtremMasterData.supplier.query.edges[0].node
            - notHasProperty: errors
            - statusCode: 200
          name: step_0_supplier_check_id_exists
      - log: Creating a supplier having the id {{ supplier_new_id }}
      - log: Supplier creation - step 1 get defaults
      - post:
          url: /api
          json:
            query: step_1_supplier_get_defaults.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_1_supplier_get_defaults
      - log: Supplier creation - step 2 checking existence of a business entity having the new id {{supplier_new_id}}
      - log: "{id:{_regex:'^{{ supplier_new_id }}$',_options:'i'}}"
      - post:
          url: /api
          json:
            query: step_2_query_exists_business_entity.graphql
            variables:
              filter: "{id:{_regex:'^{{ supplier_new_id }}$',_options:'i'}}"
          expect:
            - notHasProperty: data.xtremMasterData.businessEntity.query.edges[0].node
            - notHasProperty: errors
            - statusCode: 200
          name: step_2_query_exists_business_entity
      - log: Supplier creation - step 3 country list
      - post:
          url: /api
          json:
            query: step_3_query_country_list.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_3_query_country_list
      - log: Supplier creation - step 4 read country uk
      - post:
          url: /api
          json:
            query: step_4_read_country_uk.graphql
          expect:
            - hasProperty: data.xtremStructure.country.query.edges[0].node
            - notHasProperty: errors
            - statusCode: 200
          name: step_4_read_country_uk
      - log: Supplier creation - step 5 query currency list
      - post:
          url: /api
          json:
            query: step_5_query_currency_list.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_3_query_currency_list
      - log: Supplier creation - step 6 read currency pound
      - post:
          url: /api
          json:
            query: step_6_read_currency_pound.graphql
          expect:
            - hasProperty: data.xtremMasterData.currency.query.edges[0].node
            - notHasProperty: errors
            - statusCode: 200
          name: step_6_read_currency_pound
      - log: Supplier creation - step 7 query exists same tax id {{ supplier_uk_tax_id }}
      - log: '{"taxIdNumber":{"_regex":"^{{ supplier_uk_tax_id }}$","_options":"i"}}'
      - post:
          url: /api
          json:
            query: step_7_query_exists_same_tax_id.graphql
            variables:
              filter: '{"taxIdNumber":{"_regex":"^{{ supplier_uk_tax_id }}$","_options":"i"}}'
          expect:
            - notHasProperty: data.xtremMasterData.businessEntity.query.edges[0].node
            - notHasProperty: errors
            - statusCode: 200
          name: step_7_query_exists_same_tax_id
      - log: Supplier creation - step 8 query get defaults address
      - post:
          url: /api
          json:
            query: step_8_query_get_defaults_address.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_8_query_get_defaults_address
      - log: Supplier creation - step 9 address object definition
      - post:
          url: /api
          json:
            query: step_9_address_object_definition.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_9_address_object_definition
      - log: Supplier creation - step 10 read country uk
      - post:
          url: /api
          json:
            query: step_10_read_country_uk.graphql
          expect:
            - hasProperty: data.xtremStructure.country.query.edges[0].node
            - notHasProperty: errors
            - statusCode: 200
          name: step_10_read_country_uk
      - log: Supplier creation - step 11 country list
      - post:
          url: /api
          json:
            query: step_11_query_country_list.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_11_query_country_list
      - log: Supplier creation - step 12 lookup delivery mode
      - post:
          url: /api
          json:
            query: step_12_lookup_delivery_mode.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_12_lookup_delivery_mode
      - log: Supplier creation - step 13 lookup incoterm
      - post:
          url: /api
          json:
            query: step_13_lookup_incoterm.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_12_lookup_incoterm
      - log: Supplier creation - step 14 lookup payment term
      - post:
          url: /api
          json:
            query: step_14_lookup_payment_term.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_14_lookup_payment_term
      - log: Supplier creation - step 15 lookup payment term net30
      - post:
          url: /api
          json:
            query: step_15_lookup_payment_term_net_30.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          capture:
            - json: 'data.xtremMasterData.supplier.lookups.paymentTerm.edges[0].node._id'
              as: payment_term
          name: step_15_lookup_payment_term_net_30
      - log: Supplier creation - step 16 custom mutation create supplier and be
      # - log: "DEBUG - Variables"
      # - log: "DEBUG - supplier_new_id: '{{ supplier_new_id }}'"
      # - log: "DEBUG - supplier_new_id_name: '{{ supplier_new_id }}'"
      # - log: "DEBUG - supplier_uk_tax_id: '{{ supplier_uk_tax_id }}'"
      # - log: "DEBUG - business_entity_id: '{{ supplier_new_id }}'"
      # - log: "DEBUG - business_entity_primary_address_name: '{{ supplier_new_id }}'"
      # - log: "DEBUG - payment_term: '_id:{{ payment_term }}'"
      - post:
          url: /api
          json:
            query: step_16_custom_mutation_create_supplier_and_be.graphql
            variables:
              supplier_uk_tax_id: '{{ supplier_uk_tax_id }}'
              business_entity_id: '{{ supplier_new_id }}'
              business_entity_id_name: '{{ supplier_new_id }}'
              business_entity_primary_address_name: '{{ supplier_new_id }}'
              payment_term: '_id:{{ payment_term }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_16_custom_mutation_create_supplier_and_be
      - log: Supplier creation - step 17 query newly created supplier
      - post:
          url: /api
          json:
            query: step_17_query_newly_created_supplier.graphql
            variables:
              filter: "{'id':'{{ supplier_new_id }}'}"
          capture:
            - json: 'data.xtremMasterData.supplier.query.edges[0].node.primaryAddress._id'
              as: business_entity_primary_address__id
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_17_query_newly_created_supplier
      - log: Supplier creation - step 18 query newly created supplier address
      - post:
          url: /api
          json:
            query: step_18_query_newly_created_supplier_address.graphql
            variables:
              filterSupplierAddress: '{ _id: {{business_entity_primary_address__id}} }'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_18_query_newly_created_supplier_address
      - log: Supplier creation - step 19 query newly created supplier address contact
      - post:
          url: /api
          json:
            query: step_19_query_newly_created_supplier_address_contact.graphql
            variables:
              filterSupplierContact: "{ address: '{_id: {{business_entity_primary_address__id}} }'}"
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: step_19_query_newly_created_supplier_address_contact
