config:
  http:
    # AWS timeout is 28 seconds, we expect to complete the request before that timeout
    timeout: 27

  plugins:
    xtrem: {}
    ensure: {}
    expect: {}

    metrics-by-endpoint:
      # Group metrics by request name rather than URL:
      useOnlyRequestNames: true

  phases:
    - name: 'Ramp to {{ $processEnvironment.TOTAL_USERS }} users in {{ $processEnvironment.RAMP_UP }} seconds'
      duration: '{{ $processEnvironment.RAMP_UP }}'
      # Ramp up arrival rate to reach maxVusers at the end of this phase.
      arrivalRate: 1
      rampTo: '{{ $processEnvironment.TOTAL_USERS }}'
      maxVusers: '{{ $processEnvironment.TOTAL_USERS }}'
    - name: 'Stress {{ $processEnvironment.TOTAL_USERS }} users for {{ $processEnvironment.DURATION }} seconds'
      duration: '{{ $processEnvironment.DURATION }}'
      # Keep the arrival rate maxed out to force replacement of vusers when they leave and keep their number constant.
      arrivalRate: '{{ $processEnvironment.TOTAL_USERS }}'
      maxVusers: '{{ $processEnvironment.TOTAL_USERS }}'

  processor: './fixtures.js'

  environments:
    local:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: http://localhost:8240
      payload:
        delimiter: ';'
        path: '../../../../../platform/system/xtrem-authorization/data/layers/test/user.csv'
        fields:
          - 'email'
        order: sequence
        skipHeader: true
    devops:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: '{{ $processEnvironment.CLUSTER_URL }}'
      variables:
        cluster: '{{ $processEnvironment.CLUSTER }}'
        tenant: '{{ $processEnvironment.TENANT_ID }}'
      payload:
        path: '../../../../../platform/system/xtrem-authorization/data/layers/test/user.csv'
        fields:
          - 'email'
          - 'tenant'
        order: sequence
        skipHeader: true
        delimiter: ';'
