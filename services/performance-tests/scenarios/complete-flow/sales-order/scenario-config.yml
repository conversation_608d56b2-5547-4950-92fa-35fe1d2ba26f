config:
  http:
    # AWS timeout is 28 seconds, we expect to complete the request before that timeout
    timeout: 27

  plugins:
    xtrem: {}
    ensure: {}
    expect: {}

    metrics-by-endpoint:
      # Group metrics by request name rather than URL:
      useOnlyRequestNames: false

  phases:
    - name: 'Ramp to {{ $processEnvironment.TOTAL_USERS }} users in {{ $processEnvironment.RAMP_UP }} seconds'
      duration: '{{ $processEnvironment.RAMP_UP }}'
      arrivalRate: 1
      rampTo: '{{ $processEnvironment.TOTAL_USERS }}'
      maxVusers: '{{ $processEnvironment.TOTAL_USERS }}'
    - name: 'Stress {{ $processEnvironment.TOTAL_USERS }} users for {{ $processEnvironment.DURATION }} seconds'
      duration: '{{ $processEnvironment.DURATION }}'
      # Keep the arrival rate maxed out to force replacement of vusers when they leave and keep their number constant.
      arrivalRate: '{{ $processEnvironment.TOTAL_USERS }}'
      maxVusers: '{{ $processEnvironment.TOTAL_USERS }}'

  environments:
    local:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: http://localhost:8240
      payload:
        delimiter: ';'
        path: '../../../seed/input/01-user.csv'
        fields:
          - 'email'
        order: sequence
        skipHeader: true
    devops:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: '{{ $processEnvironment.CLUSTER_URL }}'
      variables:
        tenant: '{{ $processEnvironment.TENANT_ID }}'
        cluster: '{{ $processEnvironment.CLUSTER }}'
      payload:
        path: '../../../seed/input/01-user.csv'
        fields:
          - 'email'
        order: iterate
        skipHeader: true
        delimiter: ';'

  processor: './fixtures.js'

  variables:
    max_number_of_sites: 8
    max_number_of_customers: 5 #10 # add customer-items if the following are uncommented
    max_number_of_items: 300
    max_number_of_lines: 300
    available_customers:
      # Removing US Customers due to non existent tax determination (avalara not active on perf test data tenant)
      #-['#CN091', '#CN092', '#CN093', '#CN094', '#CN095'] # , '#CN096', '#CN097', '#CN098', '#CN099', '#CN100'] # add customer-items if the following are uncommented
      #-['209',    '210',    '212',    '215',    '216']
      - ['#CN092', '#CN093', '#CN095', '#CN098', '#CN099']
    available_sites:
      #-['198',     '199',     '192',     '193',     '195',     '196',     '201',     '202']
      - ['#S01-FR', '#S02-FR', '#S01-UK', '#S02-UK', '#S01-US', '#S02-US', '#S01-ZA', '#S02-ZA']
    available_items:
      - [
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
          # 10
          '#IMPS0445', #  9853
          '#IMPS0036', #  9444
          '#IMPS0848', # 10256
          '#IMPS0548', #  9956
          '#IMPS0746', # 10154
          '#IMPS0089', #  9497
          '#IMPS0229', #  9637
          '#IMPS0541', #  9949
          '#IMPS0190', #  9598
          '#IMPS0171', #  9579
        ]
    current_salesSite:
      - '#S01-FR'
      - '#S02-FR'
      - '#S01-UK'
      - '#S02-UK'
      - '#S01-US'
      - '#S02-US'
      - '#S01-ZA'
      - '#S02-ZA'
    current_soldToCustomer:
      - '#CN092'
      - '#CN093'
      - '#CN095'
      - '#CN098'
      - '#CN099'
    # Default values:
    # number_of_items: 1
    # number_of_customers: 1
    # number_of_sites: 1
    # number_of_lines: 1
    # Override --variables '{"number_of_lines": 2,"number_of_sites": 3,"number_of_customers":4,"number_of_items": 5}'
