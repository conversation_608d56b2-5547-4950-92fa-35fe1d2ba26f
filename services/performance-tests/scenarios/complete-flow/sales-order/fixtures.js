module.exports = {
    // Setting the scenario variables (number of sites, customers, items and/or lines)
    setFlowVariables,
    // Sales orders header hooks
    afterResponse_getDefaultsStockSite,
    afterResponse_getDefaults,
    afterResponse_getNestedDefaults,
    beforeRequest_4_1_3_getNestedDefaultsPayload,
    afterResponse_queryItemCustomer,
    afterResponse_queryItemSite,
    setSalesOrderHeaderData,
    afterResponse_captured_sales_order_id,

    // Sales order lines hooks
    resetLineVariables,
    afterResponse_getSalesPrice,
    userEntersQuantityInSalesUnit,
    beforeRequest_4_2_1_getNestedDefaultsPayload,
    userEntersGrossPrice,
    beforeRequest_4_3_1_getNestedDefaultsPayload,
    beforeRequest_calculateLineTaxes,
    afterResponse_calculateLineTaxes,
    beforeRequest_4_3_4_getNestedDefaultsPayload,
    addLineToSalesOrderLinesData,

    // Create sales order hooks
    setSalesOrderCreateData,
    afterResponse_createSalesOrder,

    // Create shipment hooks
    afterResponse_createShipment,
    afterResponse_captureSalesShipment,
    setSalesShipmentLine,
    getNextTick_shipmentStockTransactionStatusCompleted,

    // Create invoice hooks
    afterResponse_createInvoice,
    afterResponse_captureSalesInvoice,
    setBillToAddressPayload,
    getNextTick_invoicePostingStatusPosted,
};

function resetLineVariables(context, events, done) {
    context.vars.loopElementId = context.vars.$loopElement.substring(1);
    __resetCurrentSalesOrderLineData(context);

    return done();
}

function setSalesOrderCreateData(context, events, done) {
    try {
        context.vars.sales_order_create_data = {
            ...context.vars.sales_order_header_data,
            lines: context.vars.sales_order_lines_data,
        };
    } catch (e) {
        console.log(e);
    }
    return done();
}

const __getNewValueOrReturnPrevious = (currentValue, previousValue) => {
    switch (typeof currentValue) {
        case 'string':
            return currentValue === '' || currentValue === '0' ? previousValue : currentValue;
        case 'number':
            return currentValue === 0 ? previousValue : currentValue;
        default:
            return currentValue ?? previousValue;
    }
};

function __getOrderLineData(context, captured_sales_order_line_response) {
    try {
        return {
            charge: captured_sales_order_line_response.charge,
            chargeDeterminated: captured_sales_order_line_response.chargeDeterminated,
            deliveryLeadTime: captured_sales_order_line_response.deliveryLeadTime,
            deliveryMode: captured_sales_order_line_response.deliveryMode?._id || null,
            discount: captured_sales_order_line_response.discount,
            discountDeterminated: captured_sales_order_line_response.discountDeterminated,
            doNotShipAfterDate: captured_sales_order_line_response.doNotShipAfterDate,
            doNotShipBeforeDate: captured_sales_order_line_response.doNotShipBeforeDate,
            entityUse: captured_sales_order_line_response.entityUse,
            expectedDeliveryDate: captured_sales_order_line_response.expectedDeliveryDate,
            externalNote: null,
            grossPrice: captured_sales_order_line_response.grossPrice,
            grossPriceDeterminated: captured_sales_order_line_response.grossPriceDeterminated,
            internalNote: captured_sales_order_line_response.internalNote,
            isExternalNote: captured_sales_order_line_response.isExternalNote,
            isPriceDeterminated: captured_sales_order_line_response.isPriceDeterminated,
            item: captured_sales_order_line_response.item?._id ?? null,
            itemDescription: captured_sales_order_line_response.itemDescription,
            netPrice: captured_sales_order_line_response.netPrice,
            originDocumentType: captured_sales_order_line_response.originDocumentType,
            priceOrigin: captured_sales_order_line_response.priceOrigin,
            priceOriginDeterminated: captured_sales_order_line_response.priceOriginDeterminated,
            priceReason: captured_sales_order_line_response.priceReason,
            priceReasonDeterminated: captured_sales_order_line_response.priceReasonDeterminated,
            quantity: captured_sales_order_line_response.quantity,
            quantityInStockUnit: captured_sales_order_line_response.quantityInStockUnit,
            requestedDeliveryDate: captured_sales_order_line_response.requestedDeliveryDate,
            salesSite: captured_sales_order_line_response.salesSite?._id ?? null,
            unit: captured_sales_order_line_response.salesUnit?._id ?? null,
            shipToCustomerAddress: captured_sales_order_line_response.shipToCustomerAddress?._id ?? null,
            shipToAddress: captured_sales_order_line_response.shipToAddress
                ? {
                      addressLine1: captured_sales_order_line_response.shipToAddress.addressLine1,
                      addressLine2: captured_sales_order_line_response.shipToAddress.addressLine2,
                      city: captured_sales_order_line_response.shipToAddress.city,
                      country: captured_sales_order_line_response.shipToAddress.country?._id ?? null,
                      locationPhoneNumber: captured_sales_order_line_response.shipToAddress.locationPhoneNumber,
                      name: captured_sales_order_line_response.shipToAddress.name,
                      postcode: captured_sales_order_line_response.shipToAddress.postcode,
                      region: captured_sales_order_line_response.shipToAddress.region,
                  }
                : null,
            shippingDate: captured_sales_order_line_response.shippingDate,
            shippingStatus: captured_sales_order_line_response.shippingStatus,
            status: captured_sales_order_line_response.status,
            stockSite: captured_sales_order_line_response.stockSite?._id ?? null,
            stockUnit: captured_sales_order_line_response.stockUnit?._id ?? null,
            allocationRequestStatus: captured_sales_order_line_response.allocationRequestStatus,
            chargeDeterminated: captured_sales_order_line_response.chargeDeterminated,
            deliveryLeadTime: captured_sales_order_line_response.deliveryLeadTime,
            discount: captured_sales_order_line_response.discount,
            discountDeterminated: captured_sales_order_line_response.discountDeterminated,
            grossPrice: captured_sales_order_line_response.grossPrice,
            grossPriceDeterminated: captured_sales_order_line_response.grossPriceDeterminated,
            invoiceStatus: captured_sales_order_line_response.invoiceStatus,
            amountExcludingTaxInCompanyCurrency:
                captured_sales_order_line_response.amountExcludingTaxInCompanyCurrency,
            amountIncludingTax: captured_sales_order_line_response.amountIncludingTax,
            amountIncludingTaxInCompanyCurrency:
                captured_sales_order_line_response.amountIncludingTaxInCompanyCurrency,
            netPrice: captured_sales_order_line_response.netPrice,
            quantity: captured_sales_order_line_response.quantity,
            quantityInStockUnit: captured_sales_order_line_response.quantityInStockUnit,
            unitToStockUnitConversionFactor: captured_sales_order_line_response.salesUnitToStockUnitConversion,
            storedAttributes: captured_sales_order_line_response.storedAttributes,
            storedDimensions: captured_sales_order_line_response.storedDimensions,
            taxAmount: captured_sales_order_line_response.taxAmount,
            taxAmountAdjusted: captured_sales_order_line_response.taxAmountAdjusted,
            uiTaxes: captured_sales_order_line_response.uiTaxes,
            workInProgress: captured_sales_order_line_response.workInProgress,
        };
    } catch (e) {
        console.log(e);
    }
}
function addLineToSalesOrderLinesData(context, events, done) {
    if (!context.vars.sales_order_lines_data) {
        context.vars.sales_order_lines_data = [];
    }
    try {
        context.vars.sales_order_lines_data.push({
            _action: 'create',
            _id: `-${context.vars.$loopCount + 1}`,
            ...context.vars.sales_order_line_data,
        });
    } catch (e) {
        console.log(e);
    }

    return done();
}

function setSalesOrderHeaderData(context, events, done) {
    try {
        context.vars.sales_order_header_data = {
            salesSite: context.vars.captured_sales_order_response.salesSite?._id ?? null,
            soldToCustomer: context.vars.captured_sales_order_response.soldToCustomer._id,
            entityUse: context.vars.captured_sales_order_response.entityUse?._id ?? null,
            isTransferLineNote: context.vars.captured_sales_order_response.isTransferLineNote,
            isTransferHeaderNote: context.vars.captured_sales_order_response.isTransferHeaderNote,
            externalNote: context.vars.captured_sales_order_response.externalNote?._id ?? null,
            isExternalNote: context.vars.captured_sales_order_response.isExternalNote,
            internalNote: context.vars.captured_sales_order_response.internalNote?._id ?? null,
            billToAddress: context.vars.captured_sales_order_response.billToAddress ?? null,
            paymentTerm: context.vars.captured_sales_order_response.paymentTerm?._id ?? null,
            billToLinkedAddress: context.vars.captured_sales_order_response.billToLinkedAddress?._id ?? null,
            billToCustomer: context.vars.captured_sales_order_response.billToCustomer?._id ?? null,
            invoiceStatus: context.vars.captured_sales_order_response.invoiceStatus,
            shippingStatus: context.vars.captured_sales_order_response.shippingStatus,
            requestedDeliveryDate: context.vars.captured_sales_order_response.requestedDeliveryDate,
            deliveryMode: context.vars.captured_sales_order_response.deliveryMode?._id ?? null,
            incoterm: context.vars.captured_sales_order_response.incoterm?._id ?? null,
            shipToAddress: context.vars.captured_sales_order_response.shipToAddress ?? null,
            shipToCustomer: context.vars.captured_sales_order_response.shipToCustomer._id,
            stockSite: context.vars.captured_sales_order_response.stockSite?._id ?? null,
            shipToCustomerAddress: context.vars.captured_sales_order_response.shipToCustomerAddress._id,
            soldToAddress: context.vars.captured_sales_order_response.soldToAddress ?? null,
            soldToLinkedAddress: context.vars.captured_sales_order_response.soldToLinkedAddress?._id ?? null,
            currency: context.vars.captured_sales_order_response.currency?._id ?? null,
            isSent: context.vars.captured_sales_order_response.isSent,
            isPrinted: context.vars.captured_sales_order_response.isPrinted,
            status: context.vars.captured_sales_order_response.status,
            displayStatus: context.vars.captured_sales_order_response.displayStatus,
            isQuote: true,
            companyFxRateDivisor: 1,
            companyFxRate: 1,
            fxRateDate: context.vars.captured_sales_order_response.requestedDeliveryDate,
            taxCalculationStatus: 'done',
            orderDate: context.vars.captured_sales_order_response.requestedDeliveryDate,
            soldToContact: context.vars.captured_sales_order_response.soldToContact ?? null,
            number: null,
            expectedDeliveryDate: context.vars.captured_sales_order_response.requestedDeliveryDate,
            customerNumber: null,
            deliveryLeadTime: 0,
            shippingDate: context.vars.captured_sales_order_response.requestedDeliveryDate,
            doNotShipBeforeDate: null,
            doNotShipAfterDate: null,
            totalTaxAmount: context.vars.captured_sales_order_response.totalTaxAmount,
            totalTaxAmountAdjusted: context.vars.captured_sales_order_response.totalTaxAmountAdjusted,
            taxes: context.vars.captured_sales_order_response.taxes,
        };
    } catch (e) {
        console.log(e);
    }

    return done();
}

function afterResponse_createSalesOrder(requestParams, response, context, ee, next) {
    try {
        context.vars.sales_order = JSON.parse(response.body).data.xtremSales.salesOrder.create;
        // We need to delete the concatenatedAddress values from the response addresses, so that we can use the rest as payload data
        delete context.vars.sales_order.soldToAddress.concatenatedAddress;
        delete context.vars.sales_order.shipToAddress.concatenatedAddress;
        delete context.vars.sales_order.billToAddress.concatenatedAddress;
    } catch (e) {
        console.log(e);
    }
    return next();
}

const __resetCurrentSalesOrderLineData = context => {
    context.vars.captured_sales_order_line_response = {
        amountIncludingTax: '0',
        itemDescription: 'Some random item description',
        status: 'quote',
        item: null,
        itemImage: null,
        uAssignmentOrder: null,
        suppliedQuantity: null,
        workInProgress: null,
        uPurchaseOrderLine: null,
        uWorkOrderLine: null,
        uiTaxes: '{"taxEngine":"null","taxes":[]}',
        shipToAddress: null,
        storedDimensions: null,
        storedAttributes: null,
        computedAttributes: null,
        shipToCustomerAddress: null,
        remainingQuantityToAllocate: '0',
        quantityAllocated: '0',
        remainingQuantityToShipInStockUnit: '0',
        taxDate: context.vars.captured_sales_order_response.orderDate,
        taxCalculationStatus: 'notDone',
        invoiceStatus: 'notInvoiced',
        shippingStatus: 'notShipped',
        stockShortageStatus: false,
        allocationRequestStatus: 'noRequest',
        allocationStatus: 'notManaged',
        deliveryMode: null,
        entityUse: null,
        expectedDeliveryDate: context.vars.captured_sales_order_response.orderDate,
        deliveryLeadTime: 0,
        requestedDeliveryDate: context.vars.captured_sales_order_response.orderDate,
        doNotShipAfterDate: null,
        doNotShipBeforeDate: null,
        shippingDate: context.vars.captured_sales_order_response.orderDate,
        stockSite: null,
        stockShortageInSalesUnit: '0',
        availableQuantityInSalesUnit: '0',
        grossProfitAmount: null,
        stockCostAmount: null,
        amountIncludingTaxInCompanyCurrency: '0',
        taxAmountAdjusted: '0',
        taxAmount: '0',
        amountExcludingTaxInCompanyCurrency: '0',
        amountExcludingTax: '0',
        priceReason: null,
        priceOrigin: null,
        isPriceDeterminated: false,
        priceReasonDeterminated: null,
        priceOriginDeterminated: null,
        chargeDeterminated: '0',
        discountDeterminated: '0',
        grossPriceDeterminated: '0',
        netPrice: '0',
        charge: '0',
        discount: '0',
        grossPrice: context.vars.$loopCount + 1,
        remainingQuantityToInvoiceInSalesUnit: '0',
        invoicedQuantityInSalesUnit: '0',
        quantityToInvoiceInProgressInSalesUnit: '0',
        remainingQuantityToShipInSalesUnit: '0',
        shippedQuantityInSalesUnit: '0',
        quantityToShipInProgressInSalesUnit: '0',
        quantityInStockUnit: '0',
        unitToStockUnitConversionFactor: '0',
        stockShortageInStockUnit: '0',
        availableQuantityInStockUnit: '0',
        stockOnHand: '0',
        stockUnit: null,
        quantity: context.vars.$loopCount + 1,
        unit: null,
        salesSite: null,
        itemSite: null,
        originDocumentType: 'direct',
        isExternalNote: false,
        internalNote: {
            value: '',
        },
        externalNote: {
            value: '',
        },
    };
    context.vars.sales_order_line_data = __getOrderLineData(context, context.vars.captured_sales_order_line_response);
};

const __resetCurrentSalesOrderData = context => {
    context.vars.captured_sales_order_response = {
        soldToContact: null,
        number: '',
        salesSite: null,
        soldToCustomer: null,
        orderDate: '2023-05-16',
        customerNumber: '',
        soldToLinkedAddress: null,
        soldToAddress: null,
        currency: null,
        fxRateDate: '2023-05-16',
        companyFxRate: '0',
        companyFxRateDivisor: '0',
        isPrinted: false,
        isSent: false,
        displayStatus: 'quote',
        status: 'quote',
        taxCalculationStatus: 'notDone',
        shipToCustomer: null,
        shipToCustomerAddress: null,
        shipToAddress: null,
        stockSite: null,
        incoterm: null,
        deliveryMode: null,
        requestedDeliveryDate: '2023-05-16',
        deliveryLeadTime: 0,
        shippingDate: '2023-05-16',
        expectedDeliveryDate: '2023-05-16',
        doNotShipBeforeDate: null,
        doNotShipAfterDate: null,
        shippingStatus: 'notShipped',
        billToCustomer: null,
        billToLinkedAddress: null,
        paymentTerm: null,
        invoiceStatus: 'notInvoiced',
        billToAddress: null,
        internalNote: {
            value: '',
        },
        isExternalNote: false,
        externalNote: {
            value: '',
        },
        entityUse: null,
    };
};

function afterResponse_queryItemCustomer(requestParams, response, context, ee, next) {
    try {
        const itemCustomer = JSON.parse(response.body).data.xtremMasterData.itemCustomer.query.edges[0].node;
        if (itemCustomer) {
            context.vars.captured_sales_order_line_response.salesUnit = itemCustomer.salesUnit;
            context.vars.captured_sales_order_line_response.minimumSalesQuantity = itemCustomer.minimumSalesQuantity;
            context.vars.captured_sales_order_line_response.maximumSalesQuantity = itemCustomer.maximumSalesQuantity;
            context.vars.captured_sales_order_line_response.salesUnitToStockUnitConversion =
                itemCustomer.salesUnitToStockUnitConversion;

            // Updating the sales order lines data payload
            context.vars.sales_order_line_data = {
                ...__getOrderLineData(context, context.vars.captured_sales_order_line_response),
            };
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function afterResponse_queryItemSite(requestParams, response, context, ee, next) {
    try {
        const itemSite = JSON.parse(response.body).data.xtremMasterData.itemSite.query.edges[0].node;
        if (itemSite) {
            context.vars.captured_sales_order_line_response.minimumSalesQuantity = itemSite.allocatedQuantity;
            context.vars.captured_sales_order_line_response.maximumSalesQuantity = itemSite.acceptedStockQuantity;
            context.vars.captured_sales_order_line_response.salesUnitToStockUnitConversion = itemSite.inStockQuantity;

            // Updating the sales order lines data payload
            context.vars.sales_order_line_data = {
                ...__getOrderLineData(context, context.vars.captured_sales_order_line_response),
            };
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function afterResponse_getSalesPrice(requestParams, response, context, ee, next) {
    try {
        const salesPrice = JSON.parse(response.body).data.xtremMasterData.itemCustomerPrice.getSalesPrice;
        if (salesPrice) {
            context.vars.captured_sales_order_line_response.grossPrice = salesPrice.grossPrice;
            context.vars.captured_sales_order_line_response.discount = salesPrice.discount;
            context.vars.captured_sales_order_line_response.charge = salesPrice.charge;
            context.vars.captured_sales_order_line_response.priceReason = salesPrice.priceReason;

            // Updating the sales order lines data payload
            context.vars.sales_order_line_data = {
                ...__getOrderLineData(context, context.vars.captured_sales_order_line_response),
            };
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function afterResponse_calculateLineTaxes(requestParams, response, context, ee, next) {
    try {
        const computedTaxes = JSON.parse(response.body).data.xtremSales.salesOrderLine.calculateLineTaxes;
        if (computedTaxes) {
            context.vars.captured_sales_order_line_response.taxAmount = computedTaxes.taxAmount;
            context.vars.captured_sales_order_line_response.taxAmountAdjusted = computedTaxes.taxAmountAdjusted;
            context.vars.captured_sales_order_line_response.amountIncludingTax =
                computedTaxes.amountIncludingTax;
            context.vars.captured_sales_order_line_response.taxes = [...computedTaxes.taxes];

            // Updating the sales order lines data payload
            context.vars.sales_order_line_data = {
                ...__getOrderLineData(context, context.vars.captured_sales_order_line_response),
            };
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}
function afterResponse_getDefaultsStockSite(requestParams, response, context, ee, next) {
    try {
        const defaultStockSite = JSON.parse(response.body).data.xtremSales.salesOrder.getDefaults.stockSite;
        if (defaultStockSite) {
            context.vars.captured_sales_order_response.stockSite = defaultStockSite;
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function afterResponse_getDefaults(requestParams, response, context, ee, next) {
    try {
        const salesOrder = JSON.parse(response.body).data.xtremSales.salesOrder.getDefaults;
        if (salesOrder) {
            if (!context.vars.captured_sales_order_response) {
                __resetCurrentSalesOrderData(context);
            }

            context.vars.captured_sales_order_response = {
                ...context.vars.captured_sales_order_response,

                salesSite: salesOrder.salesSite ?? context.vars.captured_sales_order_response.salesSite,
                soldToCustomer: salesOrder.soldToCustomer ?? context.vars.captured_sales_order_response.soldToCustomer,
                // _attachmentsList: ,
                entityUse: __getNewValueOrReturnPrevious(
                    salesOrder.entityUse,
                    context.vars.captured_sales_order_response.entityUse,
                ),
                // proformaInvoices: ,
                isTransferLineNote: __getNewValueOrReturnPrevious(
                    salesOrder.isTransferLineNote,
                    context.vars.captured_sales_order_response.isTransferLineNote,
                ),
                isTransferHeaderNote: __getNewValueOrReturnPrevious(
                    salesOrder.isTransferHeaderNote,
                    context.vars.captured_sales_order_response.isTransferHeaderNote,
                ),
                externalNote: __getNewValueOrReturnPrevious(
                    salesOrder.externalNote,
                    context.vars.captured_sales_order_response.externalNote,
                ),
                isExternalNote: __getNewValueOrReturnPrevious(
                    salesOrder.isExternalNote,
                    context.vars.captured_sales_order_response.isExternalNote,
                ),
                internalNote: __getNewValueOrReturnPrevious(
                    salesOrder.internalNote,
                    context.vars.captured_sales_order_response.internalNote,
                ),
                totalAmountIncludingTaxInCompanyCurrency: __getNewValueOrReturnPrevious(
                    salesOrder.totalAmountIncludingTaxInCompanyCurrency,
                    context.vars.captured_sales_order_response.totalAmountIncludingTaxInCompanyCurrency,
                ),
                totalAmountExcludingTaxInCompanyCurrency: __getNewValueOrReturnPrevious(
                    salesOrder.totalAmountExcludingTaxInCompanyCurrency,
                    context.vars.captured_sales_order_response.totalAmountExcludingTaxInCompanyCurrency,
                ),
                // taxDetails: ,
                totalGrossProfit: __getNewValueOrReturnPrevious(
                    salesOrder.totalGrossProfit,
                    context.vars.captured_sales_order_response.totalGrossProfit,
                ),
                totalAmountIncludingTax: __getNewValueOrReturnPrevious(
                    salesOrder.totalAmountIncludingTax,
                    context.vars.captured_sales_order_response.totalAmountIncludingTax,
                ),
                totalTaxAmountAdjusted: __getNewValueOrReturnPrevious(
                    salesOrder.totalTaxAmountAdjusted,
                    context.vars.captured_sales_order_response.totalTaxAmountAdjusted,
                ),
                totalTaxAmount: __getNewValueOrReturnPrevious(
                    salesOrder.totalTaxAmount,
                    context.vars.captured_sales_order_response.totalTaxAmount,
                ),
                totalAmountExcludingTax: __getNewValueOrReturnPrevious(
                    salesOrder.totalAmountExcludingTax,
                    context.vars.captured_sales_order_response.totalAmountExcludingTax,
                ),
                billToAddress: salesOrder.billToAddress
                    ? {
                          _id: salesOrder.billToAddress?._id,
                          country: salesOrder.billToAddress?.country?._id,
                          postcode: salesOrder.billToAddress?.postcode,
                          region: salesOrder.billToAddress?.region,
                          city: salesOrder.billToAddress?.city,
                          addressLine2: salesOrder.billToAddress?.addressLine2,
                          addressLine1: salesOrder.billToAddress?.addressLine1,
                          locationPhoneNumber: salesOrder.billToAddress?.locationPhoneNumber,
                          name: salesOrder.billToAddress?.name,
                          isActive: salesOrder.billToAddress?.isActive,
                      }
                    : context.vars.captured_sales_order_response.billToAddress,
                paymentTerm: salesOrder.paymentTerm ?? context.vars.captured_sales_order_response.paymentTerm,
                billToLinkedAddress:
                    salesOrder.billToLinkedAddress?._id ||
                    context.vars.captured_sales_order_response.billToLinkedAddress,
                billToCustomer: salesOrder.billToCustomer ?? context.vars.captured_sales_order_response.billToCustomer,
                invoiceStatus: __getNewValueOrReturnPrevious(
                    salesOrder.invoiceStatus,
                    context.vars.captured_sales_order_response.invoiceStatus,
                ),
                shippingStatus: __getNewValueOrReturnPrevious(
                    salesOrder.shippingStatus,
                    context.vars.captured_sales_order_response.shippingStatus,
                ),
                doNotShipAfterDate: __getNewValueOrReturnPrevious(
                    salesOrder.doNotShipAfterDate,
                    context.vars.captured_sales_order_response.doNotShipAfterDate,
                ),
                doNotShipBeforeDate: __getNewValueOrReturnPrevious(
                    salesOrder.doNotShipBeforeDate,
                    context.vars.captured_sales_order_response.doNotShipBeforeDate,
                ),
                expectedDeliveryDate: __getNewValueOrReturnPrevious(
                    salesOrder.expectedDeliveryDate,
                    context.vars.captured_sales_order_response.expectedDeliveryDate,
                ),
                shippingDate: __getNewValueOrReturnPrevious(
                    salesOrder.shippingDate,
                    context.vars.captured_sales_order_response.shippingDate,
                ),
                workDays: __getNewValueOrReturnPrevious(
                    salesOrder.workDays,
                    context.vars.captured_sales_order_response.workDays,
                ),
                deliveryLeadTime: __getNewValueOrReturnPrevious(
                    salesOrder.deliveryLeadTime,
                    context.vars.captured_sales_order_response.deliveryLeadTime,
                ),
                requestedDeliveryDate: __getNewValueOrReturnPrevious(
                    salesOrder.requestedDeliveryDate,
                    context.vars.captured_sales_order_response.requestedDeliveryDate,
                ),
                deliveryMode: salesOrder.deliveryMode ?? context.vars.captured_sales_order_response.deliveryMode,
                incoterm: salesOrder.incoterm ?? context.vars.captured_sales_order_response.incoterm,
                shipToAddress: salesOrder.shipToAddress
                    ? {
                          _id: salesOrder.shipToAddress?._id,
                          country: salesOrder.shipToAddress?.country?._id,
                          postcode: salesOrder.shipToAddress?.postcode,
                          region: salesOrder.shipToAddress?.region,
                          city: salesOrder.shipToAddress?.city,
                          addressLine2: salesOrder.shipToAddress?.addressLine2,
                          addressLine1: salesOrder.shipToAddress?.addressLine1,
                          locationPhoneNumber: salesOrder.shipToAddress?.locationPhoneNumber,
                          name: salesOrder.shipToAddress?.name,
                          isActive: salesOrder.shipToAddress?.isActive,
                      }
                    : context.vars.captured_sales_order_response.shipToAddress,
                shipToCustomer: salesOrder.shipToCustomer ?? context.vars.captured_sales_order_response.shipToCustomer,
                stockSite: salesOrder.stockSite ?? context.vars.captured_sales_order_response.stockSite,
                shipToCustomerAddress:
                    salesOrder.shipToCustomerAddress ??
                    context.vars.captured_sales_order_response.shipToCustomerAddress,
                soldToAddress: salesOrder.soldToAddress
                    ? {
                          _id: salesOrder.soldToAddress?._id,
                          country: salesOrder.soldToAddress?.country?._id,
                          postcode: salesOrder.soldToAddress?.postcode,
                          region: salesOrder.soldToAddress?.region,
                          city: salesOrder.soldToAddress?.city,
                          addressLine2: salesOrder.soldToAddress?.addressLine2,
                          addressLine1: salesOrder.soldToAddress?.addressLine1,
                          locationPhoneNumber: salesOrder.soldToAddress?.locationPhoneNumber,
                          name: salesOrder.soldToAddress?.name,
                          isActive: salesOrder.soldToAddress?.isActive,
                      }
                    : context.vars.captured_sales_order_response.soldToAddress,
                soldToLinkedAddress:
                    salesOrder.soldToLinkedAddress?._id ||
                    context.vars.captured_sales_order_response.soldToLinkedAddress,
                customerNumber: __getNewValueOrReturnPrevious(
                    salesOrder.customerNumber,
                    context.vars.captured_sales_order_response.customerNumber,
                ),
                rateDescription: __getNewValueOrReturnPrevious(
                    salesOrder.rateDescription,
                    context.vars.captured_sales_order_response.rateDescription,
                ),
                currency: salesOrder.currency ?? context.vars.captured_sales_order_response.currency,
                allocationRequestStatus: __getNewValueOrReturnPrevious(
                    salesOrder.allocationRequestStatus,
                    context.vars.captured_sales_order_response.allocationRequestStatus,
                ),
                allocationStatus: __getNewValueOrReturnPrevious(
                    salesOrder.allocationStatus,
                    context.vars.captured_sales_order_response.allocationStatus,
                ),
                isOrderAssignmentLinked: __getNewValueOrReturnPrevious(
                    salesOrder.isOrderAssignmentLinked,
                    context.vars.captured_sales_order_response.isOrderAssignmentLinked,
                ),
                isSent: __getNewValueOrReturnPrevious(
                    salesOrder.isSent,
                    context.vars.captured_sales_order_response.isSent,
                ),
                isPrinted: __getNewValueOrReturnPrevious(
                    salesOrder.isPrinted,
                    context.vars.captured_sales_order_response.isPrinted,
                ),
                salesOrderIncludingTax: __getNewValueOrReturnPrevious(
                    salesOrder.salesOrderIncludingTax,
                    context.vars.captured_sales_order_response.salesOrderIncludingTax,
                ),
                salesOrderExcludingTax: __getNewValueOrReturnPrevious(
                    salesOrder.salesOrderExcludingTax,
                    context.vars.captured_sales_order_response.salesOrderExcludingTax,
                ),
                expectedDeliveryDateHeader: __getNewValueOrReturnPrevious(
                    salesOrder.expectedDeliveryDateHeader,
                    context.vars.captured_sales_order_response.expectedDeliveryDateHeader,
                ),
                salesOrderLineCount: __getNewValueOrReturnPrevious(
                    salesOrder.salesOrderLineCount,
                    context.vars.captured_sales_order_response.salesOrderLineCount,
                ),
                isOnHold: __getNewValueOrReturnPrevious(
                    salesOrder.isOnHold,
                    context.vars.captured_sales_order_response.isOnHold,
                ),
                status: __getNewValueOrReturnPrevious(
                    salesOrder.status,
                    context.vars.captured_sales_order_response.status,
                ),
                displayStatus: __getNewValueOrReturnPrevious(
                    salesOrder.displayStatus,
                    context.vars.captured_sales_order_response.displayStatus,
                ),
                isQuote: __getNewValueOrReturnPrevious(
                    salesOrder.isQuote,
                    context.vars.captured_sales_order_response.isQuote,
                ),
                companyFxRateDivisor: __getNewValueOrReturnPrevious(
                    salesOrder.companyFxRateDivisor,
                    context.vars.captured_sales_order_response.companyFxRateDivisor,
                ),
                companyFxRate: __getNewValueOrReturnPrevious(
                    salesOrder.companyFxRate,
                    context.vars.captured_sales_order_response.companyFxRate,
                ),
                fxRateDate: __getNewValueOrReturnPrevious(
                    salesOrder.fxRateDate,
                    context.vars.captured_sales_order_response.fxRateDate,
                ),
                companyCurrency:
                    salesOrder.companyCurrency ?? context.vars.captured_sales_order_response.companyCurrency,
                taxEngine: __getNewValueOrReturnPrevious(
                    salesOrder.taxEngine,
                    context.vars.captured_sales_order_response.taxEngine,
                ),
                taxCalculationStatus: __getNewValueOrReturnPrevious(
                    salesOrder.taxCalculationStatus,
                    context.vars.captured_sales_order_response.taxCalculationStatus,
                ),
                orderDate: __getNewValueOrReturnPrevious(
                    salesOrder.orderDate,
                    context.vars.captured_sales_order_response.orderDate,
                ),
                number: __getNewValueOrReturnPrevious(
                    salesOrder.number,
                    context.vars.captured_sales_order_response.number,
                ),
                isCloseHidden: __getNewValueOrReturnPrevious(
                    salesOrder.isCloseHidden,
                    context.vars.captured_sales_order_response.isCloseHidden,
                ),
                soldToContact: salesOrder.soldToContact
                    ? {
                          _id: salesOrder.soldToContact?._id,
                          email: salesOrder.soldToContact?.email,
                          title: salesOrder.soldToContact?.title,
                          locationPhoneNumber: salesOrder.soldToContact?.locationPhoneNumber,
                          position: salesOrder.soldToContact?.position,
                          role: salesOrder.soldToContact?.role,
                          preferredName: salesOrder.soldToContact?.preferredName,
                          firstName: salesOrder.soldToContact?.firstName,
                          lastName: salesOrder.soldToContact?.lastName,
                      }
                    : context.vars.captured_sales_order_response.soldToContact,
            };
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function userEntersQuantityInSalesUnit(context, events, done) {
    try {
        context.vars.sales_order_line_data.quantity = context.vars.$loopCount + 1;
    } catch (e) {
        console.log(e);
    }
    return done();
}

function userEntersGrossPrice(context, events, done) {
    try {
        context.vars.sales_order_line_data.grossPrice = context.vars.$loopCount + 10;
    } catch (e) {
        console.log(e);
    }
    return done();
}

function beforeRequest_calculateLineTaxes(requestParams, context, ee, next) {
    try {
        context.vars.current_line_taxes_payload = [];
        for (const tax of context.vars.sales_order_line_data.uiTaxes) {
            if (tax != null) {
                context.vars.current_line_taxes_payload.push({
                    taxCategoryReference: tax.taxCategoryReference?._id ?? null,
                    isTaxMandatory: !!tax.isTaxMandatory,
                    isSubjectToGlTaxExcludedAmount: !!tax.isSubjectToGlTaxExcludedAmount,
                    taxReference: tax.taxReference?._id ?? null,
                    _sortValue: tax._sortValue,
                });
            }
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

const __commonGetDefaultsPayload = context => {
    context.vars.get_defaults_header_payload = {
        ...context.vars.sales_order_header_data,
    };
    context.vars.get_defaults_lines_payload = [];
    if (context.vars.sales_order_lines_data) {
        context.vars.get_defaults_lines_payload.push(...context.vars.sales_order_lines_data);
    }
};

function beforeRequest_4_1_3_getNestedDefaultsPayload(requestParams, context, ee, next) {
    __commonGetDefaultsPayload(context);
    if (context.vars.sales_order_line_data) {
        context.vars.get_defaults_lines_payload.push({
            _action: 'create', // in case of more than 1 line, we need to specify the action (if not, an error is thrown)
            item: context.vars.$loopElement,
        });
    }
    context.vars.get_defaults_payload = {
        ...context.vars.get_defaults_header_payload,
        lines: context.vars.get_defaults_lines_payload,
    };
    return next();
}

function beforeRequest_4_2_1_getNestedDefaultsPayload(requestParams, context, ee, next) {
    __commonGetDefaultsPayload(context);
    if (context.vars.sales_order_line_data) {
        context.vars.get_defaults_lines_payload.push({
            _action: 'create', // in case of more than 1 line, we need to specify the action (if not, an error is thrown)
            item: context.vars.sales_order_line_data.item,
            grossPrice: context.vars.sales_order_line_data.grossPrice,
            uiTaxes: context.vars.sales_order_line_data.uiTaxes,
            quantity: context.vars.sales_order_line_data.quantity,
        });
    }
    context.vars.get_defaults_payload = {
        ...context.vars.get_defaults_header_payload,
        lines: context.vars.get_defaults_lines_payload,
    };
    return next();
}

function beforeRequest_4_3_1_getNestedDefaultsPayload(requestParams, context, ee, next) {
    return beforeRequest_4_2_1_getNestedDefaultsPayload(requestParams, context, ee, next);
}

function beforeRequest_4_3_4_getNestedDefaultsPayload(requestParams, context, ee, next) {
    return beforeRequest_4_1_3_getNestedDefaultsPayload(requestParams, context, ee, next);
}

function afterResponse_getNestedDefaults(requestParams, response, context, ee, next) {
    try {
        // TODO: Add current_sortValue so that we can filter the current line
        const salesOrderLine = JSON.parse(response.body).data.nestedDefaults.salesOrder.getDefaults.lines.query.edges[
            context.vars.$loopCount
        ].node;
        if (salesOrderLine) {
            context.vars.captured_sales_order_line_response.shipToAddress =
                salesOrderLine.shipToAddress ?? context.vars.captured_sales_order_response.shipToAddress;
            context.vars.captured_sales_order_line_response = { ...salesOrderLine };

            // Updating the sales order lines data payload
            context.vars.sales_order_line_data = {
                ...__getOrderLineData(context, context.vars.captured_sales_order_line_response),
            };
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function afterResponse_createShipment(requestParams, response, context, ee, next) {
    try {
        const result = JSON.parse(response.body).data.xtremSales.salesOrder.createSalesShipmentsFromOrders;
        if (result) {
            context.vars.number_of_shipments = result.numberOfShipments;
            context.vars.created_shipments_ids = result.documentsCreated.map(doc => doc._id);
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function afterResponse_captureSalesShipment(requestParams, response, context, ee, next) {
    try {
        context.vars.captured_sales_shipment = JSON.parse(response.body).data.rootNode.salesShipment.read;

        context.vars.captured_sales_shipment_line_ids = context.vars.captured_sales_shipment.lines.query.edges.map(
            edge => edge.node._id,
        );
    } catch (e) {
        console.log(e);
    }

    return next();
}

function setSalesShipmentLine(context, events, done) {
    try {
        context.vars.captured_sales_shipment_line = context.vars.captured_sales_shipment.lines.query.edges.find(
            edge => {
                return edge.node._id === context.vars.$loopElement;
            },
        ).node;
    } catch (e) {
        console.log(e);
    }
    return done();
}

function getNextTick_shipmentStockTransactionStatusCompleted(context, next) {
    const continueLooping =
        context.vars.$loopCount < (context.vars.max_posting_check ?? 200) &&
        context.vars.result_shipment_stockTransactionStatus !== 'completed';
    // While `continueLooping` is true, the `next` function will
    // continue the loop in the test scenario.
    return next(continueLooping);
}

function afterResponse_createInvoice(requestParams, response, context, ee, next) {
    try {
        const result = JSON.parse(response.body).data.xtremSales.salesShipment.createSalesInvoicesFromShipments;
        if (result) {
            context.vars.number_of_invoices = result.numberOfInvoices;
            context.vars.created_invoices_ids = result.documentsCreated.map(doc => doc._id);
            if (context.vars.created_invoices_ids.length < 1) throw new Error('No invoice created');
        }
    } catch (e) {
        console.log(e);
    }
    return next();
}

function afterResponse_captureSalesInvoice(requestParams, response, context, ee, next) {
    try {
        context.vars.captured_sales_invoice = JSON.parse(response.body).data.rootNode.salesInvoice.read;

        context.vars.captured_sales_invoice_line_ids = context.vars.captured_sales_invoice.lines.query.edges.map(
            edge => edge.node._id,
        );
    } catch (e) {
        console.log(e);
    }

    return next();
}

function getNextTick_invoicePostingStatusPosted(context, next) {
    const continueLooping =
        context.vars.$loopCount < (context.vars.max_posting_check ?? 200) &&
        context.vars.result_invoice_status !== 'posted';
    // While `continueLooping` is true, the `next` function will
    // continue the loop in the test scenario.
    return next(continueLooping);
}

function setBillToAddressPayload(requestParams, context, ee, next) {
    try {
        context.vars.billToAddress_payload = {
            _id: context.vars.captured_sales_invoice.billToAddress._id,
            country: context.vars.captured_sales_invoice.billToAddress.country._id,
            postcode: context.vars.captured_sales_invoice.billToAddress.postcode,
            region: context.vars.captured_sales_invoice.billToAddress.region,
            city: context.vars.captured_sales_invoice.billToAddress.city,
            addressLine2: context.vars.captured_sales_invoice.billToAddress.addressLine2,
            addressLine1: context.vars.captured_sales_invoice.billToAddress.addressLine1,
            locationPhoneNumber: context.vars.captured_sales_invoice.billToAddress.locationPhoneNumber,
            name: context.vars.captured_sales_invoice.billToAddress.name,
            isActive: context.vars.captured_sales_invoice.billToAddress.isActive,
        };
    } catch (e) {
        console.log(e);
    }
    return next();
}
function setFlowVariables(context, ee, done) {
    try {
        __setVariables(context);
        // Hack in order to take into account the number_sites constraint (therefor, only working on first <<number_of_site>> elements of available_sites)
        // Hard-coded available_sites in scenario_config.yml :
        // ['#S01-FR', '#S02-FR', '#S01-UK', '#S02-UK', '#S01-US', '#S02-US', '#S01-ZA', '#S02-ZA']
        const current_salesSitesIndex = context.vars.available_sites.findIndex(
            site => site === context.vars.current_salesSite,
        );
        if (current_salesSitesIndex >= 0 && current_salesSitesIndex >= context.vars.number_of_sites) {
            context.vars.current_salesSite =
                context.vars.available_sites[
                    (current_salesSitesIndex - context.vars.number_of_sites) % context.vars.number_of_sites
                ];
        }
        context.vars.current_salesSite_id = context.vars.current_salesSite.substring(1);
        // Hack in order to take into account the number_customers constraint (therefor, only working on first <<number_of_customers>> elements of available_number_of_customers)
        // Hard-coded available_number_of_customers in scenario_config.yml :
        // ['#CN092', '#CN093', '#CN095', '#CN098', '#CN099']
        const current_soldToCustomerIndex = context.vars.available_customers.findIndex(
            customer => customer === context.vars.current_soldToCustomer,
        );
        if (current_soldToCustomerIndex >= 0 && current_soldToCustomerIndex >= context.vars.number_of_customers) {
            context.vars.current_soldToCustomer =
                context.vars.available_customers[
                    (current_soldToCustomerIndex - context.vars.number_of_customers) % context.vars.number_of_customers
                ];
        }
        context.vars.current_soldToCustomer_id = context.vars.current_soldToCustomer.substring(1);

        // Items will be handled in a loop handling the order lines creation
    } catch (e) {
        console.log(e);
    }
    return done();
}

function __parseAndAddToContextScenarioVariables(context, scenario_variables) {
    const variables = JSON.parse(scenario_variables);
    Object.keys(variables).forEach(key => (context.vars[key] = variables[key]));
    // Checking some needed parameters, and defaulting them if not provided
    context.vars.max_posting_check = context.vars.max_posting_check ?? 200;
    context.vars.pause_before_new_user = context.vars.pause_before_new_user ?? 0;
}
function __setNumberOfSites(context) {
    context.vars.number_of_sites = Math.min(context.vars.number_of_sites || 1, context.vars.max_number_of_sites);
}

function __setNumberOfCustomers(context) {
    context.vars.number_of_customers = Math.min(
        context.vars.number_of_customers || 1,
        context.vars.max_number_of_customers,
    );
}

function __setNumberOfLines(context) {
    context.vars.number_of_lines = Math.min(context.vars.number_of_lines || 1, context.vars.max_number_of_lines);
}

function __setNumberOfItems(context) {
    // First, we set the number_of_lines according to the variables passed
    __setNumberOfLines(context);
    // We compute the number_of_items with the constraint that it should not exceed the number_of_lines
    context.vars.number_of_items = Math.max(
        Math.min(context.vars.number_of_items || 1, context.vars.max_number_of_items),
        context.vars.number_of_lines,
    );
}

/**
 * Hook called from the scenario flow in order to set up variables that have uniqueness constrains
 * See https://www.artillery.io/docs/guides/guides/http-reference#function-steps-in-a-flow
 * @param {*} context
 * @param {*} events
 * @param {*} done
 * @returns
 */
function __generateSitesCustomersAndItemsVariables(context) {
    // Set the "query" variables for the virtual user.
    if (!context.vars.sites?.length || context.vars.sites?.length !== context.vars.number_of_sites) {
        context.vars.sites = context.vars.available_sites.slice(0, context.vars.number_of_sites || 1);
        context.vars.number_of_sites = context.vars.sites.length;
    }
    if (!context.vars.customers?.length || context.vars.customers?.length !== context.vars.number_of_customers) {
        context.vars.customers = context.vars.available_customers.slice(0, context.vars.number_of_customers || 1);
        context.vars.number_of_customers = context.vars.customers.length;
    }
    if (!context.vars.items?.length || context.vars.items.length !== context.vars.number_of_items) {
        context.vars.items = context.vars.available_items.slice(0, context.vars.number_of_items || 1);
        context.vars.number_of_items = context.vars.items.length;
    }
}

function __setVariables(context) {
    __parseAndAddToContextScenarioVariables(context, context.vars.$processEnvironment?.SCENARIO_VARIABLES || '{}');
    __setNumberOfSites(context);
    __setNumberOfCustomers(context);
    __setNumberOfItems(context);
    __generateSitesCustomersAndItemsVariables(context);
}

function afterResponse_captured_sales_order_id(requestParams, response, context, ee, next) {
    try {
        context.vars.captured_sales_order_id = JSON.parse(
            response.body,
        )?.data?.navigationPanelItems?.salesOrder?.query?.edges[0]?.node._id;
    } catch (e) {
        console.log(e);
    }

    return next();
}
