# Load demo data (locally) and run xtrem server and

## For testing purposes locally, load test data:

The needed test data is located in the 'Perf test small' tenant, therefore, it needs to be imported by following the steps defined in /platform/performance-test/README.md file

## Make sure that xtrem project is built and xtrem is started with unsecure dev login:

```bash
pnpm run start:unsecuredevlogin
```

# run test script

## run scenario test from xtrem/services/performance-tests

```bash
  ../../scripts/artillery/scenarios-test-run.sh  --scenario complete-flow/sales-order --total-users 5 --ramp-up 15 --duration 10
```

By default, the scenario uses the following number of sites/customers/items and lines which is 1.
In case there is a need for more, run the scenario by giving the wanted values for these variables:

```bash
  ../../scripts/artillery/scenarios-test-run.sh  --scenario complete-flow/sales-order --total-users 20 --ramp-up 5 --duration 120 --variables '{"number_of_lines": 300, "number_of_sites": 8, "number_of_customers":5, "number_of_items": 10}'
```

You may have to increase the graphql timeout in xtrem-config.yml

```yml
graphql:
    timeLimitInSeconds: 120
```
