scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/**/*'
    flow:
      - log: Environment variables {{ $processEnvironment.SCENARIO_VARIABLES }} / {{ $processEnvironment.SCENARIO_VARIABLES.number_of_lines }}
      # - log: Available variables - {{ available_sites }} / {{ available_customers }} / {{ available_items }}
      - function: 'setFlowVariables'
      - log: Flow variables - number_of_lines {{ number_of_lines}} / sites {{ sites }} / number_of_sites {{ number_of_sites }}, customers {{ customers }} / number_of_customers {{ number_of_customers }}, items {{ items }} / number_of_items {{ number_of_items }}
      - log: current_salesSite {{ current_salesSite }}/ current_soldToCustomer {{ current_soldToCustomer }}

      - log: User {{ email }} - (Step 01-1) Sales order page - metadata query
      - post:
          url: /metadata
          json:
            query: 1-1-metadata-query-sales-order-page.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 01-1) metadata-query-sales-order-page

      - log: User {{ email }} - (Step 01-2) Sales order page - get defaults
      - post:
          url: /api
          json:
            query: 1-2-get-defaults-sales-order.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          afterResponse: 'afterResponse_captured_sales_order_id'
          name: (Step 01-2) get-defaults-sales-order

      - log: User {{ email }} - (Step 01-3) Sales order page - user preferences service options
      - post:
          url: /api
          json:
            query: 1-3-user-preferences-service-option.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 01-3) user-preferences-service-option

      - log: User {{ email }} - (Step 01-4) Sales order page - query sales order first 20 records
      - post:
          url: /api
          json:
            query: 1-4-query-sales-order-first-20-records.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 01-4) query-sales-order-first-20-records

      - log: User {{ email }} - (Step 01-5) Sales order page - query sales order line list first 10 records
      - post:
          url: /api
          json:
            query: 1-5-query-sales-order-line-list-first-10-records.graphql
            variables:
              filter: "{'_id':{'_eq':'{{ captured_sales_order_id }}'}}"
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 01-5) query-sales-order-line-list-first-10-records

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 02-1)  Sales order page - get defaults
      - post:
          url: /api
          json:
            query: 2-1-get-defaults-sales-order.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 02-1) get-sales-order-default-values

      - log: User {{ email }} - (Step 02-2)  Sales order page - get nested defaults
      - post:
          url: /api
          json:
            query: 2-2-get-nested-defaults-sales-order-with-payload.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 02-2) get-sales-order-nested-default-values

      - log: User {{ email }} - (Step 02-3)  Sales order page - sales site selection
      - post:
          url: /api
          json:
            query: 2-3-lookups-sales-site.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 02-3) user-lookup-sales-order-sales-site

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 02-4)  Sales order page - sold to customer selection
      - post:
          url: /api
          json:
            query: 2-4-lookups-sold-to-customer.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 02-4) user-lookup-sales-order-sold-to-customer

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 02-5) Sales order page - user preferences service options
      - post:
          url: /api
          json:
            query: 2-5-user-preferences-sales-order.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 02-5) user-preferences-sales-order-service-option

      - log: User {{ email }} - (Step 02-6)  Sales order page - nested defaults
      - post:
          url: /api
          json:
            query: 2-6-get-nested-defaults-for-lines.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 02-6) get-sales-order-nested-default-values

      - log: User {{ email }} - (Step 03-1-1)  Sales order page - lookups sales site
      - post:
          url: /api
          json:
            query: 3-1-1-lookups-sales-site.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-1-1) lookups-sales-site

      - log: User {{ email }} - (Step 03-1-2)  Sales order page - lookups sales site first 10 records
      - post:
          url: /api
          json:
            query: 3-1-2-lookups-sales-site-first-10-records.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-1-2) lookups-sales-site

      - log: User {{ email }} - (Step 03-1-3)  Sales order page - nested defaults for selected sales site {{ current_salesSite }}
      - post:
          url: /api
          json:
            query: 3-1-3-nested-defaults-lines.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.nestedDefaults.salesOrder.getDefaults.lines.query.edges[0].node.salesSite.legalCompany.id
            - hasProperty: data.nestedDefaults.salesOrder.getDefaults.lines.query.edges[0].node.salesSite.legalCompany.name
            - equals:
                - '#{{ result_salesSite_id }}'
                - '{{ current_salesSite }}'
          # The captured result_<something> are only used for the expect equals
          capture:
            - json: 'data.nestedDefaults.salesOrder.getDefaults.lines.query.edges[0].node.salesSite.id'
              as: result_salesSite_id
            - json: 'data.nestedDefaults.salesOrder.getDefaults.lines.query.edges[0].node.salesSite._id'
              as: current_salesSite__id
            - json: 'data.nestedDefaults.salesOrder.getDefaults.lines.query.edges[0].node.salesSite.legalCompany._id'
              as: current_company__id
          name: (Step 03-1-3) get-sales-order-nested-default-values

      - log: User {{ email }} - (Step 03-1-4)  Sales order page - get defaults for selected sales site {{ current_salesSite }}
      - post:
          url: /api
          json:
            query: 3-1-4-get-defaults-for-selected-sales-site.graphql
            variables:
              salesSite_id: '{{ current_salesSite }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-1-4) get-defaults-sales-site

      - log: User {{ email }} - (Step 03-1-5)  Sales order page - lookups for selected sales site {{ current_salesSite }}
      - post:
          url: /api
          json:
            query: 3-1-5-lookups-for-selected-sales-site.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
              salesSite_filter: '{"_and":[{"isSales":true},{"_or":[{"id":{"_regex":"{{ current_salesSite_id }}","_options":"i"}},{"name":{"_regex":"{{ current_salesSite_id }}","_options":"i"}}]}]}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremSales.salesOrder.lookups.salesSite.edges[0].node.id
            - equals:
                - '#{{ result_salesSite_id }}'
                - '{{ current_salesSite }}'
          # Some captured results are only used for the expect equals
          capture:
            - json: 'data.xtremSales.salesOrder.lookups.salesSite.edges[0].node.id'
              as: result_salesSite_id
            - json: 'data.xtremSales.salesOrder.lookups.salesSite.edges[0].node'
              as: captured_sales_order_response.salesSite
          name: (Step 03-1-5) lookups-sales-site

      - log: User {{ email }} - (Step 03-1-6)  Sales order page - get defaults stock site
      - post:
          url: /api
          json:
            query: 3-1-6-get-defaults-stock-site.graphql
            variables:
              salesSite_id: '{{ current_salesSite }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          afterResponse: 'afterResponse_getDefaultsStockSite'
          name: (Step 03-1-6) get-sales-order-default-stock-site

      - log: User {{ email }} - (Step 03-1-7)  Sales order page - nested defaults for selected sales site {{ current_salesSite }}
      - post:
          url: /api
          json:
            query: 3-1-7-nested-defaults-lines.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-1-7) get-sales-order-nested-default-values

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 03-2-1)  Sales order page - lookups sold to customer first 10 records
      - post:
          url: /api
          json:
            query: 3-2-1-lookups-sold-to-customer-first-10-records.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-1-2) lookups-sold-to-customer

      - log: User {{ email }} - (Step 03-2-2)  Sales order page - lookups sold to customer after
      - post:
          url: /api
          json:
            query: 3-2-2-lookups-sold-to-customer-after.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-2-2) lookups-sold-to-customer

      - log: User {{ email }} - (Step 03-2-3)  Sales order page - lookups sold to customer after
      - post:
          url: /api
          json:
            query: 3-2-3-lookups-sold-to-customer-after.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-2-3) lookups-sold-to-customer

      - log: User {{ email }} - (Step 03-2-4)  Sales order page - nested defaults for selected sold to customer {{ current_soldToCustomer }}
      - post:
          url: /api
          json:
            query: 3-2-4-nested-defaults-lines.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
              soldToCustomer_id: '{{ current_soldToCustomer }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-2-4) get-sales-order-nested-default-values

      - log: User {{ email }} - (Step 03-2-5)  Sales order page - get defaults for selected sales site {{ current_salesSite }} and sold to customer {{ current_soldToCustomer }}
      - post:
          url: /api
          json:
            query: 3-2-5-get-defaults-for-selected-sales-site-sold-to-customer.graphql
            variables:
              salesSite_id: '{{ current_salesSite }}'
              soldToCustomer_id: '{{ current_soldToCustomer }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          afterResponse: 'afterResponse_getDefaults'
          name: (Step 03-2-5) get-sales-order-default-values

      - log: User {{ email }} - (Step 03-2-6)  Sales order page - lookups for selected sales site {{ current_salesSite }} and sold to customer {{ current_soldToCustomer }}
      - post:
          url: /api
          json:
            query: 3-2-6-lookups-for-selected-sold-to-customer.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
              salesSite_filter: '{"_and":[{"isSales":true},{"_or":[{"id":{"_regex":"{{ current_salesSite_id }}","_options":"i"}},{"name":{"_regex":"{{ current_salesSite_id }}","_options":"i"}}]}]}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremSales.salesOrder.lookups.salesSite.edges[0].node.id
            - equals:
                - '#{{ result_salesSite_id }}'
                - '{{ current_salesSite }}'
          capture:
            - json: 'data.xtremSales.salesOrder.lookups.salesSite.edges[0].node.id'
              as: result_salesSite_id
          name: (Step 03-2-6) lookups-sales-site

      - log: User {{ email }} - (Step 03-2-7)  Sales order page - get defaults for selected sales site {{ current_salesSite }} and sold to customer {{ current_soldToCustomer }}
      - post:
          url: /api
          json:
            query: 3-2-7-get-defaults-for-selected-sales-site-sold-to-customer.graphql
            variables:
              salesSite_id: '{{ current_salesSite }}'
              soldToCustomer_id: '{{ current_soldToCustomer }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremSales.salesOrder.getDefaults.soldToCustomer.id
            - hasProperty: data.xtremSales.salesOrder.getDefaults.soldToCustomer._id
            - equals:
                - '{{ result_soldToCustomer_id }}'
                - '{{ current_soldToCustomer_id }}'
          capture:
            - json: 'data.xtremSales.salesOrder.getDefaults.soldToCustomer.id'
              as: result_soldToCustomer_id
            - json: 'data.xtremSales.salesOrder.getDefaults.soldToCustomer._id'
              as: current_soldToCustomer__id
          afterResponse: 'afterResponse_getDefaults'
          name: (Step 03-2-7) get-sales-order-default-values

      - log: User {{ email }} - (Step 03-2-8)  Sales order page - nested defaults for selected sales site {{ current_salesSite }} and sold to customer {{ current_soldToCustomer }}
      - post:
          url: /api
          json:
            query: 3-2-8-nested-defaults-lines.graphql
            variables:
              orderDate: '{{ captured_sales_order_response.orderDate }}'
              salesSite_id: '{{ current_salesSite }}'
              soldToCustomer_id: '{{ current_soldToCustomer }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-2-8) get-sales-order-nested-default-values

      - log: User {{ email }} - (Step 03-2-9)  Sales order page - get default attributes and dimensions
      - post:
          url: /api
          json:
            query: 3-2-9-get-default-attributes-and-dimensions.graphql
            variables:
              company_id: '{{ current_company__id }}'
              salesSite_id: '{{ current_salesSite__id }}'
              soldToCustomer_id: '{{ current_soldToCustomer__id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 03-2-9) get-sales-order-default-attributes-and-dimensions

      - function: 'setSalesOrderHeaderData'

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - adding one line for each item of {{ items }}. Number of lines {{ number_of_lines }} / Number of items {{ number_of_items }}
      - loop:
          - function: 'resetLineVariables'

          - log: User {{ email }} - (Step 04-1-1) User entering item selection
          - post:
              url: /api
              json:
                query: 4-1-1-lookups-item.graphql
                variables:
                  salesSite_id: '{{ captured_sales_order_response.salesSite }}'
                  orderDate: '{{ captured_sales_order_response.orderDate }}'
                  shipToAddress: '{{ captured_sales_order_response.shipToAddress }}'
                  shipToCustomerAddress: '{{ captured_sales_order_response.shipToCustomerAddress }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 04-1-1) user-selecting-an-item

          - log: User {{ email }} - (Step 04-1-2) User entering item selection
          - post:
              url: /api
              json:
                query: 4-1-2-lookups-item-after.graphql
                variables:
                  salesSite_id: '{{ captured_sales_order_response.salesSite }}'
                  orderDate: '{{ captured_sales_order_response.orderDate }}'
                  shipToAddress: '{{ captured_sales_order_response.shipToAddress }}'
                  shipToCustomerAddress: '{{ captured_sales_order_response.shipToCustomerAddress }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 04-1-2) user-selecting-an-item

          - log: User {{ email }} - (Step 04-1-3)  Sales order page - nested defaults for selected item {{ $loopElement }}
          - post:
              url: /api
              # Setting the get defaults payload
              beforeRequest: 'beforeRequest_4_1_3_getNestedDefaultsPayload'
              json:
                query: 4-1-3-nested-defaults-lines.graphql
                variables:
                  get_defaults_payload: '{{ get_defaults_payload }}'
                  number_of_lines: '{{ number_of_lines }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_getNestedDefaults'
              name: (Step 04-1-3) get-sales-order-nested-default-values

          - log: User {{ email }} - (Step 04-1-4)  Sales order page - query item customer for selected item {{ $loopElement }}
          - post:
              url: /api
              json:
                query: 4-1-4-query-item-customer.graphql
                variables:
                  itemCustomer_filter: '{"item":"{{ $loopElement }}","customer": "{{ current_soldToCustomer }}"}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_queryItemCustomer'
              name: (Step 04-1-4) query-item-customer

          - log: User {{ email }} - (Step 04-1-5)  Sales order page - convert quantity from sales unit to stock unit
          - post:
              url: /api
              json:
                query: 4-1-5-convert-unit-of-measure.graphql
                variables:
                  from_unit: '{{ captured_sales_order_line_response.unit._id }}'
                  to_unit: '{{ captured_sales_order_line_response.stockUnit._id }}'
                  item: '{{ $loopElement }}'
                  quantity: '{{ captured_sales_order_line_response.quantity }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 04-1-5) query-item-customer

          - log: User {{ email }} - (Step 04-1-6)  Sales order page - query item site for selected item {{ $loopElement }}
          - post:
              url: /api
              json:
                query: 4-1-6-query-item-site.graphql
                variables:
                  itemSite_filter: '{"item":"{{ $loopElement }}","site": "{{ current_salesSite }}"}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_queryItemSite'
              name: (Step 04-1-6) query-item-site

          - log: User {{ email }} - (Step 04-1-7)  Sales order page - get sales price for selected item {{ $loopElement }}
          - post:
              url: /api
              json:
                query: 4-1-7-get-sales-price.graphql
                variables:
                  salesSite: '{{ current_salesSite }}'
                  stockSite: '{{ captured_sales_order_line_response.stockSite }}'
                  soldToCustomer: '{{ current_soldToCustomer }}'
                  currency: '{{ captured_sales_order_response.currency._id }}'
                  item: '{{ $loopElement }}'
                  quantity: '{{ captured_sales_order_line_response.quantity }}'
                  unit: '{{ captured_sales_order_line_response.unit._id }}'
                  orderDate: '{{ captured_sales_order_response.orderDate }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_getSalesPrice'
              name: (Step 04-1-7) get-sales-price

          - log: User {{ email }} - (Step 04-1-8)  Sales order page - calculate line taxes for selected item {{ $loopElement }}
          - post:
              url: /api
              # Setting the taxes collection payload
              beforeRequest: 'beforeRequest_calculateLineTaxes'
              json:
                query: 4-1-8-calculate-line-taxes.graphql
                variables:
                  salesSite: '{{ current_salesSite }}'
                  businessPartner: '{{ current_soldToCustomer }}'
                  item: '{{ $loopElement }}'
                  currency: '{{ captured_sales_order_response.currency._id }}'
                  amountExcludingTax: '{{ captured_sales_order_line_response.amountExcludingTax }}'
                  quantity: '{{ captured_sales_order_line_response.quantity }}'
                  orderDate: '{{ captured_sales_order_response.orderDate }}'
                  consumer_country: '{{ captured_sales_order_response.shipToAddress.country }}'
                  consumer_postcode: '{{ captured_sales_order_response.shipToAddress.postcode }}'
                  shipToCustomerAddress: '{{ captured_sales_order_response.shipToCustomerAddress }}'
                  taxes: '{{ current_line_taxes_payload }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_calculateLineTaxes'
              name: (Step 04-1-8) calculate-line-taxes

          - log: User {{ email }} - (Step 04-1-9)  Sales order page - query item site for selected item {{ $loopElement }}
          - post:
              url: /api
              json:
                query: 4-1-9-query-item-site.graphql
                variables:
                  itemSite_filter: '{"item":"{{ $loopElement }}"}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 04-1-9) query-item-site

          - log: User {{ email }} - (Step 04-1-10)  Sales order page - get valuation cost for selected item {{ $loopElement }}
          - post:
              url: /api
              json:
                query: 4-1-10-get-valuation-cost.graphql
                variables:
                  stockSite: '{{ captured_sales_order_line_response.stockSite }}'
                  item: '{{ captured_sales_order_line_response.item._id }}'
                  quantity: '{{ captured_sales_order_line_response.quantity }}'
                  orderDate: '{{ captured_sales_order_response.orderDate }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 04-1-10) get-valuations-cost

          - log: User {{ email }} - (Step 04-1-11)  Sales order page - get default attributes and dimensions
          - post:
              url: /api
              json:
                query: 4-1-11-get-default-attributes-and-dimensions.graphql
                variables:
                  company_id: '{{ current_company__id }}'
                  salesSite_id: '{{ current_salesSite__id }}'
                  item_id: '{{ captured_sales_order_line_response.item._id }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 04-1-11) get-sales-order-default-attributes-and-dimensions

          - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
          - think: '{{ $processEnvironment.USER_THINK_TIME }}'

          # Here, we must set the wanted quantity
          - function: 'userEntersQuantityInSalesUnit'

          - log: User {{ email }} - (Step 04-2-1)  Sales order page - nested defaults for entered quantity {{ sales_order_line_data.quantity }}
          - post:
              url: /api
              # Setting the get defaults payload
              beforeRequest: 'beforeRequest_4_2_1_getNestedDefaultsPayload'
              json:
                query: 4-2-1-nested-defaults-lines.graphql
                variables:
                  get_defaults_payload: '{{ get_defaults_payload }}'
                  number_of_lines: '{{ number_of_lines }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_getNestedDefaults'
              name: (Step 04-2-1) get-sales-order-nested-default-values

          - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
          - think: '{{ $processEnvironment.USER_THINK_TIME }}'

          # Here, we must set the wanted grossPrice
          - function: 'userEntersGrossPrice'

          - log: User {{ email }} - (Step 04-3-1)  Sales order page - nested defaults for entered gross price {{ $current_gross_price }}
          - post:
              url: /api
              # Setting the get defaults payload
              beforeRequest: 'beforeRequest_4_3_1_getNestedDefaultsPayload'
              json:
                log: get_defaults_payload = '{{ get_defaults_payload }}'
                query: 4-3-1-nested-defaults-lines.graphql
                variables:
                  get_defaults_payload: '{{ get_defaults_payload }}'
                  number_of_lines: '{{ number_of_lines }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_getNestedDefaults'
              name: (Step 04-3-1) get-sales-order-nested-default-values

          - log: User {{ email }} - (Step 04-3-2)  Sales order page - calculate line taxes for selected item {{ $loopElement }}
          - post:
              url: /api
              # Setting the taxes collection payload
              beforeRequest: 'beforeRequest_calculateLineTaxes'
              json:
                query: 4-3-2-calculate-line-taxes.graphql
                variables:
                  orderDate: '{{ captured_sales_order_response.orderDate }}'
                  salesSite: '{{ current_salesSite }}'
                  businessPartner: '{{ current_soldToCustomer }}'
                  item: '{{ $loopElement }}'
                  currency: '{{ captured_sales_order_response.currency._id }}'
                  amountExcludingTax: '{{ captured_sales_order_line_response.amountExcludingTax }}'
                  quantity: '{{ captured_sales_order_line_response.quantity }}'
                  consumer_country: '{{ captured_sales_order_line_response.shipToAddress.country._id }}'
                  consumer_postcode: '{{ captured_sales_order_line_response.shipToAddress.postcode }}'
                  shipToCustomerAddress: '{{ captured_sales_order_line_response.shipToCustomerAddress._id }}'
                  taxes: '{{ current_line_taxes_payload }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              afterResponse: 'afterResponse_calculateLineTaxes'
              name: (Step 04-3-2) calculate-line-taxes

          - log: User {{ email }} - (Step 04-3-3)  Sales order page - get valuation cost for selected item {{ $loopElement }}
          - post:
              url: /api
              json:
                query: 4-3-3-get-valuation-cost.graphql
                variables:
                  stockSite: '{{ captured_sales_order_line_response.stockSite }}'
                  item: '{{ $loopElement }}'
                  quantity: '{{ captured_sales_order_line_response.quantity }}'
                  orderDate: '{{ captured_sales_order_response.orderDate }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 04-3-3) get-valuations-cost

          - log: User {{ email }} - (Step 04-3-4)  Sales order page - nested defaults after calculation of valuation cost
          - post:
              url: /api
              # Setting the get defaults payload
              beforeRequest: 'beforeRequest_4_3_4_getNestedDefaultsPayload'
              json:
                query: 4-3-4-nested-defaults-lines.graphql
                variables:
                  get_defaults_payload: '{{ get_defaults_payload }}'
                  number_of_lines: '{{ number_of_lines }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              # DO NOT UNCCOMENT THIS : weird behavior even while using pages !
              # (lines: [{}] - i.e. send as empty, therefor the response resets all entries on lines)
              # afterResponse: 'afterResponse_getNestedDefaults'
              name: (Step 04-3-4) get-sales-order-nested-default-values

          - function: 'addLineToSalesOrderLinesData'
        over: items

      # TODO: add a step to get the header taxes in case you want to handle the taxes
      - function: 'setSalesOrderCreateData'

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 05-1) Saving sales order
      - post:
          url: /api
          json:
            query: 5-1-mutation-create-sales-order.graphql
            variables:
              sales_order_create_data: '{{ sales_order_create_data }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremSales.salesOrder.create.lines.query.edges[0].node._id
            - hasProperty: data.xtremSales.salesOrder.create.number
            - hasProperty: data.xtremSales.salesOrder.create._id
          capture:
            - json: 'data.xtremSales.salesOrder.create'
              as: 'sales_order'
          afterResponse: 'afterResponse_createSalesOrder'
          name: (Step 05-1) user-saves-sales-order

      - log: User {{ email }} - (Step 05-2) Sales order page - query sales orders
      - post:
          url: /api
          json:
            query: 5-2-query-sales-orders.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 05-2) query-sales-orders

      - log: User {{ email }} - (Step 05-3)  Sales order page - nested defaults after calculation of valuation cost
      - post:
          url: /api
          json:
            query: 5-3-nested-defaults-lines.graphql
            variables:
              salesOrder_id: '{{ sales_order._id }}'
              salesOrder_number: '{{ sales_order.number }}'
              orderDate: '{{ sales_order.orderDate }}'
              salesSite_id: '{{ sales_order.salesSite._id }}'
              stockSite_id: '{{ sales_order.stockSite._id }}'
              soldToCustomer_id: '{{ sales_order.soldToCustomer._id }}'
              soldToContact: '{{ sales_order.soldToContact }}'
              soldToAddress: '{{ sales_order.soldToAddress }}'
              shipToCustomerAddress: '{{ sales_order.shipToCustomerAddress._id }}'
              shipToCustomer: '{{ sales_order.shipToCustomer ._id}}'
              shipToAddress: '{{ sales_order.shipToAddress }}'
              deliveryMode: '{{ sales_order.deliveryMode._id }}'
              billToCustomer: '{{ sales_order.billToCustomer._id }}'
              billToLinkedAddress: '{{ sales_order.billToLinkedAddress._id }}'
              paymentTerm: '{{ sales_order.paymentTerm._id }}'
              billToAddress: '{{ sales_order.billToAddress }}'
              currency: '{{ sales_order.currency._id }}'
              soldToLinkedAddress: '{{ sales_order.soldToLinkedAddress._id }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 05-3) get-sales-order-nested-default-values

      - log: User {{ email }} - (Step 05-4)  Sales order page - get default attributes and dimensions
      - post:
          url: /api
          json:
            query: 5-4-get-default-attributes-and-dimensions.graphql
            variables:
              company_id: '{{ sales_order.salesSite.legalCompany._id }}'
              salesSite_id: '{{ sales_order.salesSite._id }}'
              soldToCustomer_id: '{{ sales_order.soldToCustomer._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 05-4) get-sales-order-default-attributes-and-dimensions

      - log: User {{ email }} - (Step 05-5) Sales order page - user preferences service options
      - post:
          url: /api
          json:
            query: 5-5-user-preferences-sales-order.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 05-5) user-preferences-sales-order-service-option

      - log: User {{ email }} - (Step 05-6) Sales order page - finance integration check
      - post:
          url: /api
          json:
            query: 5-6-mutation-finance-integration-check.graphql
            variables:
              salesOrder_id: '{{ sales_order._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 05-6) finance-integration-check

      - log: User {{ email }} - (Step 05-7) Sales order page - query sales order
      - post:
          url: /api
          json:
            query: 5-7-query-sales-order.graphql
          variables:
            filter: "{'_id':{'_eq':'{{ sales_order._id }}'}}"
            number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 05-7) query-sales-order

      - log: User {{ email }} - (Step 05-8)  Sales order page - nested defaults after calculation of valuation cost
      - post:
          url: /api
          json:
            query: 5-8-nested-defaults-lines.graphql
            variables:
              salesOrder_id: '{{ sales_order._id }}'
              salesOrder_number: '{{ sales_order.number }}'
              orderDate: '{{ sales_order.orderDate }}'
              salesSite_id: '{{ sales_order.salesSite._id }}'
              stockSite_id: '{{ sales_order.stockSite._id }}'
              soldToCustomer_id: '{{ sales_order.soldToCustomer._id }}'
              soldToContact: '{{ sales_order.soldToContact }}'
              soldToAddress: '{{ sales_order.soldToAddress }}'
              shipToCustomerAddress: '{{ sales_order.shipToCustomerAddress._id }}'
              shipToCustomer: '{{ sales_order.shipToCustomer ._id}}'
              shipToAddress: '{{ sales_order.shipToAddress }}'
              deliveryMode: '{{ sales_order.deliveryMode._id }}'
              billToCustomer: '{{ sales_order.billToCustomer._id }}'
              billToLinkedAddress: '{{ sales_order.billToLinkedAddress._id }}'
              paymentTerm: '{{ sales_order.paymentTerm._id }}'
              billToAddress: '{{ sales_order.billToAddress }}'
              currency: '{{ sales_order.currency._id }}'
              soldToLinkedAddress: '{{ sales_order.soldToLinkedAddress._id }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 05-8) get-sales-order-nested-default-values

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 06-1) Sales order page - confirm sales orders
      - post:
          url: /api
          json:
            query: 6-1-mutation-confirm-sales-order.graphql
            variables:
              salesOrder_number: '{{ sales_order.number }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 06-1) confirm-sales-orders

      - log: User {{ email }} - (Step 06-2) Sales order page - set is printed sales orders
      - post:
          url: /api
          json:
            query: 6-2-mutation-set-is-printed.graphql
            variables:
              salesOrder_id: '{{ sales_order._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 06-2) set-is-printed-sales-orders

      - log: User {{ email }} - (Step 06-3) Sales order page - query sales orders
      - post:
          url: /api
          json:
            query: 6-3-query-sales-orders.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 06-3) query-sales-orders

      - log: User {{ email }} - (Step 06-4) Sales order page - read sales orders
      - post:
          url: /api
          json:
            query: 6-4-read-sales-order.graphql
            variables:
              salesOrder_id: '{{ sales_order._id }}'
              number_of_lines: '{{ number_of_lines }}'
          capture:
            - json: 'data.xtremSales.salesOrder.read.displayStatus'
              as: captured_displayStatus
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - equals:
                - 'confirmed'
                - '{{ captured_displayStatus }}'
          name: (Step 06-4) read-sales-orders

      - log: User {{ email }} - (Step 06-5)  Sales order page - get default attributes and dimensions
      - post:
          url: /api
          json:
            query: 6-5-get-default-attributes-and-dimensions.graphql
            variables:
              company_id: '{{ sales_order.salesSite.legalCompany._id }}'
              salesSite_id: '{{ sales_order.salesSite._id }}'
              soldToCustomer_id: '{{ sales_order.soldToCustomer._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 06-5) get-sales-order-default-attributes-and-dimensions

      - log: User {{ email }} - (Step 06-6)  Sales order page - nested defaults after calculation of valuation cost
      - post:
          url: /api
          json:
            query: 6-6-nested-defaults-lines.graphql
            variables:
              salesOrder_id: '{{ sales_order._id }}'
              salesOrder_number: '{{ sales_order.number }}'
              orderDate: '{{ sales_order.orderDate }}'
              salesSite_id: '{{ sales_order.salesSite._id }}'
              stockSite_id: '{{ sales_order.stockSite._id }}'
              soldToCustomer_id: '{{ sales_order.soldToCustomer._id }}'
              soldToContact: '{{ sales_order.soldToContact }}'
              soldToAddress: '{{ sales_order.soldToAddress }}'
              shipToCustomerAddress: '{{ sales_order.shipToCustomerAddress._id }}'
              shipToCustomer: '{{ sales_order.shipToCustomer ._id}}'
              shipToAddress: '{{ sales_order.shipToAddress }}'
              deliveryMode: '{{ sales_order.deliveryMode._id }}'
              billToCustomer: '{{ sales_order.billToCustomer._id }}'
              billToLinkedAddress: '{{ sales_order.billToLinkedAddress._id }}'
              paymentTerm: '{{ sales_order.paymentTerm._id }}'
              billToAddress: '{{ sales_order.billToAddress }}'
              currency: '{{ sales_order.currency._id }}'
              soldToLinkedAddress: '{{ sales_order.soldToLinkedAddress._id }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 06-6) get-sales-order-nested-default-values

      - log: User {{ email }} - (Step 06-7) Sales order page - user preferences service options
      - post:
          url: /api
          json:
            query: 6-7-user-preferences-sales-order.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 06-7) user-preferences-sales-order-service-option

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 07-1) Sales order page - create shipment from sales orders
      - post:
          url: /api
          json:
            query: 7-1-mutation-create-shipment-from-order.graphql
            variables:
              salesOrder_id: '{{ sales_order._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremSales.salesOrder.createSalesShipmentsFromOrders.numberOfShipments
            - hasProperty: data.xtremSales.salesOrder.createSalesShipmentsFromOrders.documentsCreated
          afterResponse: 'afterResponse_createShipment'
          name: (Step 07-1) create-shipments-from-sales-orders

      - log: User {{ email }} - (Step 07-2) Sales shipment page - metadata query
      - post:
          url: /metadata
          json:
            query: 7-2-metadata-query-sales-shipment-page.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 07-2) metadata-query-sales-shipment-page

      - log: User {{ email }} - (Step 07-3) Sales shipment page - read sales shipment
      - post:
          url: /api
          json:
            query: 7-3-read-sales-shipment.graphql
            variables:
              salesShipment_id: '{{ created_shipments_ids[0] }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.rootNode.salesShipment.read.lines.query.edges[0]
          afterResponse: 'afterResponse_captureSalesShipment'
          name: (Step 07-3) read-sales-shipment

      - log: User {{ email }} - (Step 07-4) Sales shipment page - user preferences service options
      - post:
          url: /api
          json:
            query: 7-4-user-preferences-sales-shipment.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 07-4) user-preferences-sales-shipment-service-option

      - log: User {{ email }} - looping on each shipment line in order to allocate it
      - loop:
          - log: Looping on the following line _id {{ $loopElement }}
          # From the _id (i.e. $loopElement) we get the current sales shipment line in 'captured_sales_shipment_line' variable
          - function: 'setSalesShipmentLine'

          - log: User {{ email }} - (Step 08-1) Sales shipment page - query order lines to shipment lines
          - post:
              url: /api
              json:
                query: 8-1-query-sales-order-line-to-sale-shipment-line.graphql
                variables:
                  filter: "{'document':{'_id':'{{ $loopElement }}'}}"
              expect:
                - notHasProperty: errors
                - statusCode: 200
                - hasProperty: data.xtremSales.salesOrderLineToSalesShipmentLine.query.edges[0].node.linkedDocument._id
                - hasProperty: data.xtremSales.salesOrderLineToSalesShipmentLine.query.edges[0].node.linkedDocument.document._id
                - hasProperty: data.xtremSales.salesOrderLineToSalesShipmentLine.query.edges[0].node.document._id
              capture:
                - json: 'data.xtremSales.salesOrderLineToSalesShipmentLine.query.edges[0].node.linkedDocument._id'
                  as: 'captured_original_sales_order_line_id'
              name: (Step 08-1) query-order-lines-to-shipment-lines

          - log: User {{ email }} - (Step 08-2) Get stock allocation
          - post:
              url: /api
              json:
                query: 8-2-query-stock-allocation.graphql
                variables:
                  filter: "{'documentLine':{'_id':{'_in':['{{ captured_original_sales_order_line_id }}','{{ $loopElement }}']}}}"
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-2) get-stock-allocation

          - log: User {{ email }} - (Step 08-3) Stock search
          - post:
              url: /api
              json:
                query: 8-3-query-search-stock.graphql
                variables:
                  active_quantity_in_stock_unit: 0 # '{{ captured_sales_shipment_line.quantityInStockUnit }}'
                  item: '{{ captured_sales_shipment_line.item._id }}'
                  site: '{{ captured_sales_shipment.stockSite._id }}'
                  stock_unit: '{{ captured_sales_shipment_line.stockUnit._id }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
                - hasProperty: data.xtremStockData.stock.searchStock[0].stockRecord._id
              capture:
                - json: 'data.xtremStockData.stock.searchStock[0].stockRecord._id'
                  as: 'stock_record_id'
                - json: 'data.xtremStockData.stock.searchStock[0].quantityInStockUnit'
                  as: captured_available_quantityInStockUnit
                - json: 'data.xtremStockData.stock.searchStock[0].stockRecord._id'
                  as: captured_stock_record_id
              name: (Step 08-3) get-stock-search-for-stock

          - log: User {{ email }} - (Step 08-4) Get stock allocation panel metadata - previously captured stock_record_id {{ stock_record_id }}
          - post:
              url: /metadata
              json:
                query: 8-4-metadata-query-stock-allocation-details-panel-page.graphql
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-4) user-allocates-stock-panel-metadata

          - log: User {{ email }} - (Step 08-5) Sales shipment page - read item
          - post:
              url: /api
              json:
                query: 8-5-read-item.graphql
                variables:
                  item: '{{ captured_sales_shipment_line.item._id }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-5) read-item

          - log: User {{ email }} - (Step 08-6) Sales shipment page - read site
          - post:
              url: /api
              json:
                query: 8-6-read-site.graphql
                variables:
                  site: '{{ captured_sales_shipment.stockSite._id }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-6) read-site

          - log: User {{ email }} - (Step 08-7) Get stock allocation
          - post:
              url: /api
              json:
                query: 8-7-query-stock-allocation.graphql
                variables:
                  filter: "{'documentLine':{'_id':{'_in':['{{ captured_original_sales_order_line_id }}','{{ $loopElement }}']}}}"
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-7) get-stock-allocation

          - log: User {{ email }} - (Step 08-8) Sales shipment page - Get stock allocation
          - post:
              url: /api
              json:
                query: 8-8-query-stock-movement-detail.graphql
                variables:
                  documentLine: '{{ $loopElement }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-8) get-stock-allocation

          - log: User {{ email }} - (Step 08-9) Sales shipment page - Update stock allocation
          - post:
              url: /api
              json:
                query: 8-9-mutation-update-allocations.graphql
                variables:
                  documentLine: '{{ $loopElement }}'
                  stockRecord_id: '{{ captured_stock_record_id }}'
                  quantityInStockUnit: '{{ captured_sales_shipment_line.quantityInStockUnit }}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-9) user-updates-stock-allocation

          - log: User {{ email }} - (Step 08-10) Sales shipment page - Get current sales shipment
          - post:
              url: /api
              json:
                query: 8-10-query-sales-shipment.graphql
                variables:
                  filter: "{'_id':{'_eq':'{{ captured_sales_shipment._id }}'}}"
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-10) get-current-sales-shipment

          - log: User {{ email }} - (Step 08-11) Sales shipment page - Get current sales shipment
          - post:
              url: /api
              json:
                query: 8-11-query-sales-shipment-line.graphql
                variables:
                  filter: '{"document":{"number":"{{ captured_sales_shipment.number }}"}}'
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 08-11) get-current-sales-shipment-line

          - log: Simulating user thinking for 5 seconds (needed by the stock posting) # {{ $processEnvironment.USER_THINK_TIME }} seconds
          - think: '{{ $processEnvironment.USER_THINK_TIME }}'

        over: captured_sales_shipment_line_ids

      - log: User {{ email }} - (Step 09-1) Sales shipment page - Confirm sales shipment
      - post:
          url: /api
          json:
            query: 9-1-mutation-confirm-sales-shipment.graphql
            variables:
              salesShipment_id: '{{ captured_sales_shipment._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 09-1) user-confirms-sales-shipment

      - log: User {{ email }} - (Step 09-2) Sales shipment page - Get stock allocation panel metadata - previously captured stock_record_id {{ stock_record_id }}
      - post:
          url: /metadata
          json:
            query: 9-2-metadata-query-sales-shipment-page.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 09-2) user-allocates-stock-panel-metadata

      - log: User {{ email }} - (Step 09-3) Sales shipment page - read sales shipment
      - post:
          url: /api
          json:
            query: 9-3-read-sales-shipment.graphql
            variables:
              salesShipment_id: '{{ captured_sales_shipment._id }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.rootNode.salesShipment.read.lines.query.edges[0]
          name: (Step 09-3) read-sales-shipment

      - log: User {{ email }} - (Step 09-4) Sales shipment page - user preferences service options
      - post:
          url: /api
          json:
            query: 9-4-user-preferences-sales-shipment.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 09-4) user-preferences-sales-shipment-service-option

      - log: User {{ email }} - (Step 10-1) Sales shipment page - Query sales shipment lines
      - post:
          url: /api
          json:
            query: 10-1-query-sales-shipment-line.graphql
            variables:
              filter: '{"document":{"number":"{{ captured_sales_shipment.number }}"}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 10-1) get-current-sales-shipment

      - log: Simulating user thinking for 5 seconds (needed by the stock posting) # {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 10-2) Sales shipment page - Post the sales shipment
      - post:
          url: /api
          json:
            query: 10-2-mutation-post-to-stock.graphql
            variables:
              document_ids: ['{{ captured_sales_shipment._id }}']
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 10-2) user-posts-sales-shipment

      # we trigger a query on the shipment status until it changes from 'Posting in progress'
      - loop:
          - log: User {{ email }} - (Step 10-2-1) Sales shipment page - Loop querying the shipment status
          - post:
              url: /api
              json:
                query: 10-2-1-query-current-sales-shipment-posting-status.graphql
                variables:
                  salesShipment_id: '{{ captured_sales_shipment._id }}'
              capture:
                - json: 'data.xtremSales.salesShipment.read.displayStatus'
                  as: result_shipment_displayStatus
                - json: 'data.xtremSales.salesShipment.read.stockTransactionStatus'
                  as: result_shipment_stockTransactionStatus
                - json: 'data.xtremSales.salesShipment.read.status'
                  as: result_shipment_status
              expect:
                - notHasProperty: errors
                - statusCode: 200
                - hasProperty: data.xtremSales.salesShipment.read.stockTransactionStatus
              name: (Step 10-2-1) query-current-sales-shipment-posting-status
          - think: '1' # Avoiding running zillions of reads (in fact, we send a max of 100)
        whileTrue: getNextTick_shipmentStockTransactionStatusCompleted

      - log: User {{ email }} - (Step 10-3) Sales shipment page - Get current sales shipment
      - post:
          url: /api
          json:
            query: 10-3-query-sales-shipment.graphql
            variables:
              filter: "{'_id':{'_eq':'{{ captured_sales_shipment._id }}'}}"
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 10-3) get-current-sales-shipment

      - log: User {{ email }} - (Step 11-1) Sales shipment page - Create invoice from the sales shipment
      - post:
          url: /api
          json:
            query: 11-1-mutation-create-sales-invoice-from-shipment.graphql
            variables:
              salesShipment_ids: ['{{ captured_sales_shipment._id }}']
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremSales.salesShipment.createSalesInvoicesFromShipments.numberOfInvoices
            - hasProperty: data.xtremSales.salesShipment.createSalesInvoicesFromShipments.documentsCreated
          afterResponse: 'afterResponse_createInvoice'
          name: (Step 11-1) user-creates-sales-invoice

      - log: User {{ email }} - (Step 11-2) Sales invoice page - Get sales shipment line to sales invoice line - previously captured sales invoice number {{ captured_sales_invoice.number }}
      - post:
          url: /api
          json:
            query: 11-2-query-sales-shipment-line-to-sales-invoice-line.graphql
            variables:
              filter: "{'linkedDocument':{'document':{'_in':['{{ captured_sales_shipment._id }}']}}}"
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 11-2) get-sales-shipment-line-to-sales-invoice-line

      - log: User {{ email }} - (Step 11-3) Sales invoice page - Get stock allocation panel metadata
      - post:
          url: /metadata
          json:
            query: 11-3-metadata-query-sales-invoice-page.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 11-3) user-allocates-stock-panel-metadata

      - log: User {{ email }} - (Step 11-4) Sales invoice page - get enums values for posting status
      - post:
          url: /api
          json:
            query: 11-4-target-doc-type-posting-status-enum-values.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 11-4) user-allocates-stock-panel-metadata

      - log: User {{ email }} - (Step 11-5) Sales invoice page - read sales invoice
      - post:
          url: /api
          json:
            query: 11-5-read-sales-invoice.graphql
            variables:
              salesInvoice_id: '{{ created_invoices_ids[0] }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.rootNode.salesInvoice.read.lines.query.edges[0]
          afterResponse: 'afterResponse_captureSalesInvoice'
          name: (Step 11-5) read-sales-invoice

      - log: User {{ email }} - (Step 11-6)  Sales invoice page - get default attributes and dimensions
      - post:
          url: /api
          json:
            query: 11-6-get-default-attributes-and-dimensions.graphql
            variables:
              company_id: '{{ captured_sales_invoice.salesSite.legalCompany._id }}'
              salesSite_id: '{{ captured_sales_invoice.salesSite._id }}'
              billToCustomer_id: '{{ captured_sales_invoice.billToCustomer._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 11-6) get-sales-invoice-default-attributes-and-dimensions

      - log: User {{ email }} - (Step 11-7) Sales invoice page - Getting the posting status
      - post:
          url: /api
          json:
            query: 11-7-get-posting-status-data.graphql
            variables:
              salesInvoice_number: '{{ captured_sales_invoice.number }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 11-7) get-sales-invoice-posting-status

      - log: User {{ email }} - (Step 11-8) Sales invoice page - user preferences service options
      - post:
          url: /api
          json:
            query: 11-8-user-preferences-sales-invoice.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 11-8) user-preferences-sales-invoice-service-option

      - log: User {{ email }} - (Step 11-9)  Sales invoice page - Get nested defaults lines
      - post:
          url: /api
          # Setting the billToAddress payload (removing the concatenatedAddress field)
          beforeRequest: 'setBillToAddressPayload'
          json:
            query: 11-9-nested-defaults-lines.graphql
            variables:
              invoice_id: '{{ captured_sales_invoice._id }}'
              salesSite_id: '{{ captured_sales_invoice.salesSite._id }}'
              billToCustomer_id: '{{ captured_sales_invoice.billToCustomer._id }}'
              invoice_number: '{{ captured_sales_invoice.number }}'
              invoiceDate: '{{ captured_sales_invoice.invoiceDate }}'
              fxRateDate: '{{ captured_sales_invoice.fxRateDate }}'
              companyFxRate: '{{ captured_sales_invoice.companyFxRate }}'
              companyFxRateDivisor: '{{ captured_sales_invoice.companyFxRateDivisor }}'
              billToLinkedAddress_id: '{{ captured_sales_invoice.billToLinkedAddress._id }}'
              totalTaxAmount: '{{ captured_sales_invoice.totalTaxAmount }}'
              totalTaxAmountAdjusted: '{{ captured_sales_invoice.totalTaxAmountAdjusted }}'
              creationNumber: '{{ captured_sales_invoice.creationNumber }}'
              currency_id: '{{ captured_sales_invoice.currency._id }}'
              paymentTerm_id: '{{ captured_sales_invoice.paymentTerm._id }}'
              dueDate: '{{ captured_sales_invoice.dueDate }}'
              billToAddress: '{{ billToAddress_payload }}'
              billToContact: '{{ captured_sales_invoice.billToContact }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.nestedDefaults.salesInvoice.getDefaults.lines.query.edges[0].node
          name: (Step 11-9) get-sales-invoice-nested-default-values

      - log: Simulating user thinking for {{ $processEnvironment.USER_THINK_TIME }} seconds
      - think: '{{ $processEnvironment.USER_THINK_TIME }}'

      - log: User {{ email }} - (Step 12-1) Sales invoice page - Post the sales invoice
      - post:
          url: /api
          json:
            query: 12-1-mutation-sales-invoice-post.graphql
            variables:
              salesInvoice_id: '{{ captured_sales_invoice._id }}'
          capture:
            - json: 'data.xtremSales.salesInvoice.post.wasSuccessful'
              as: 'invoice_posting_successful'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremSales.salesInvoice.post.wasSuccessful
            - hasProperty: data.xtremSales.salesInvoice.post.message
            - equals:
                - 'true'
                - '{{ invoice_posting_successful }}'
          name: (Step 12-1) sales-invoice-posting

      - log: User {{ email }} - (Step 12-2) Sales invoice page - Get current sales invoice after posting
      - post:
          url: /api
          json:
            query: 12-2-query-sales-invoice-display-status.graphql
            variables:
              filter: "{'_id':'{{ captured_sales_invoice._id }}'}"
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 12-2) get-current-sales-invoice-after-posting

      # we trigger a query on the invoice status until it changes from 'Posting in progress'
      - loop:
          - log: User {{ email }} - (Step 12-2-1) Sales invoice page - Loop querying the invoice posting status
          - post:
              url: /api
              json:
                query: 12-2-1_query_current_sales_invoice_posting_status.graphql
                variables:
                  salesInvoice_id: '{{ captured_sales_invoice._id }}'
              capture:
                - json: 'data.xtremSales.salesInvoice.read.displayStatus'
                  as: result_invoice_displayStatus
                - json: 'data.xtremSales.salesInvoice.read.status'
                  as: result_invoice_status
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: (Step 12-2-1) query-current-sales-invoice-posting-status
          - think: '2' # Avoiding running zillions of reads (in fact, we send a max of 100)
        whileTrue: getNextTick_invoicePostingStatusPosted

      - log: User {{ email }} - (Step 12-3) Sales invoice page - read sales invoice after 'Posted' posting status
      - post:
          url: /api
          json:
            query: 12-3-read-sales-invoice.graphql
            variables:
              salesInvoice_id: '{{ captured_sales_invoice._id }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.rootNode.salesInvoice.read.lines.query.edges[0]
          afterResponse: 'afterResponse_captureSalesInvoice'
          name: (Step 12-3) read-sales-invoice-after-posted-posting-status

      - log: User {{ email }} - (Step 12-4)  Sales invoice page - get default attributes and dimensions
      - post:
          url: /api
          json:
            query: 12-4-get-default-attributes-and-dimensions.graphql
            variables:
              company_id: '{{ captured_sales_invoice.salesSite.legalCompany._id }}'
              salesSite_id: '{{ captured_sales_invoice.salesSite._id }}'
              billToCustomer_id: '{{ captured_sales_invoice.billToCustomer._id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 12-4) get-sales-invoice-default-attributes-and-dimensions

      - log: User {{ email }} - (Step 12-5)  Sales invoice page - Get nested defaults lines after 'Posted' posting status
      - post:
          url: /api
          # Setting the billToAddress payload (removing the concatenatedAddress field)
          beforeRequest: 'setBillToAddressPayload'
          json:
            query: 12-5-nested-defaults-lines.graphql
            variables:
              invoice_id: '{{ captured_sales_invoice._id }}'
              salesSite_id: '{{ captured_sales_invoice.salesSite._id }}'
              billToCustomer_id: '{{ captured_sales_invoice.billToCustomer._id }}'
              invoice_number: '{{ captured_sales_invoice.number }}'
              invoiceDate: '{{ captured_sales_invoice.invoiceDate }}'
              fxRateDate: '{{ captured_sales_invoice.fxRateDate }}'
              companyFxRate: '{{ captured_sales_invoice.companyFxRate }}'
              companyFxRateDivisor: '{{ captured_sales_invoice.companyFxRateDivisor }}'
              billToLinkedAddress_id: '{{ captured_sales_invoice.billToLinkedAddress._id }}'
              totalTaxAmount: '{{ captured_sales_invoice.totalTaxAmount }}'
              totalTaxAmountAdjusted: '{{ captured_sales_invoice.totalTaxAmountAdjusted }}'
              creationNumber: '{{ captured_sales_invoice.creationNumber }}'
              currency_id: '{{ captured_sales_invoice.currency._id }}'
              paymentTerm_id: '{{ captured_sales_invoice.paymentTerm._id }}'
              dueDate: '{{ captured_sales_invoice.dueDate }}'
              billToAddress: '{{ billToAddress_payload }}'
              billToContact: '{{ captured_sales_invoice.billToContact }}'
              number_of_lines: '{{ number_of_lines }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.nestedDefaults.salesInvoice.getDefaults.lines.query.edges[0].node
          name: (Step 12-5) get-sales-invoice-nested-default-values-after-posted-posting-status

      - log: User {{ email }} - (Step 12-6) Sales invoice page - read sales invoice after 'Posted' posting status
      - post:
          url: /api
          json:
            query: 12-6-query-get-posting-status-data.graphql
            variables:
              - document_number: '{{ captured_sales_invoice.number }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremFinanceData.financeTransaction.getPostingStatusData[0]._id
          name: (Step 12-6) read-sales-invoice-after-posted-posting-status

      - log: User {{ email }} - (Step 12-7) Sales invoice page - user preferences service options
      - post:
          url: /api
          json:
            query: 12-7-user-preferences-sales-invoice.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 12-7) user-preferences-sales-invoice-service-option

      - log: User {{ email }} - (Step 12-8) Sales invoice page - query invoice list
      - post:
          url: /api
          json:
            query: 12-8-query-sales-invoice-list.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 12-8) query-invoice-list
