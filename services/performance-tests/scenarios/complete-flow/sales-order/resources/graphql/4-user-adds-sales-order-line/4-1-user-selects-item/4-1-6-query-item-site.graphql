query Query($itemSite_filter: String) {
    xtremMasterData {
        itemSite {
            query(filter: $itemSite_filter) {
                edges {
                    node {
                        allocatedQuantity
                        acceptedStockQuantity
                        inStockQuantity
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
