query Query($itemSite_filter: String) {
    xtremMasterData {
        itemSite {
            query(filter: $itemSite_filter) {
                edges {
                    node {
                        site {
                            id
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
