mutation Mutation(
    $salesSite: IntReference
    $businessPartner: IntReference
    $item: IntReference
    $currency: IntReference
    $amountExcludingTax: Decimal
    $quantity: Decimal
    $orderDate: Date
    $consumer_country: IntReference
    $consumer_postcode: String
    $shipToCustomerAddress: IntReference
    $taxes: [SalesOrderLineCalculateLineTaxesDataTaxes_Input] = []
) {
    xtremSales {
        salesOrderLine {
            calculateLineTaxes(
                data: {
                    site: $salesSite
                    businessPartner: $businessPartner
                    item: $item
                    currency: $currency
                    amountExcludingTax: $amountExcludingTax
                    quantity: $quantity
                    taxDate: $orderDate
                    addresses: {
                        consumerCountry: $consumer_country
                        consumerPostcode: $consumer_postcode
                        shipToCustomerAddress: $shipToCustomerAddress
                    }
                    taxes: $taxes
                }
            ) {
                taxAmount
                taxAmountAdjusted
                amountIncludingTax
                taxes {
                    taxCategoryReference {
                        _id
                        name
                    }
                    taxCategory
                    taxRate
                    taxAmount
                    nonTaxableAmount
                    exemptAmount
                    taxableAmount
                    currency {
                        _id
                        name
                    }
                    deductibleTaxRate
                    deductibleTaxAmount
                    isReverseCharge
                    taxAmountAdjusted
                    isTaxMandatory
                    isSubjectToGlTaxExcludedAmount
                    taxReference {
                        _id
                        name
                    }
                    tax
                    _sortValue
                }
            }
        }
    }
}
