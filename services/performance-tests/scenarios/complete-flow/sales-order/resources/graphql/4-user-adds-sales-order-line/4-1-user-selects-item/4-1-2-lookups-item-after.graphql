query Lookups(
    $orderDate: Date
    $salesSite_id: IntReference
    $shipToAddress: Address_Input
    $shipToCustomerAddress: IntReference
) {
    xtremSales {
        salesOrderLine {
            lookups(
                data: {
                    allocationRequestStatus: "noRequest"
                    charge: 0
                    chargeDeterminated: 0
                    deliveryLeadTime: 0
                    deliveryMode: "_id:186"
                    discount: 0
                    discountDeterminated: 0
                    doNotShipAfterDate: null
                    doNotShipBeforeDate: null
                    entityUse: null
                    expectedDeliveryDate: $orderDate
                    externalNote: null
                    grossPrice: null
                    grossPriceDeterminated: 0
                    internalNote: null
                    invoiceStatus: "notInvoiced"
                    isExternalNote: false
                    isPriceDeterminated: false
                    itemDescription: null
                    amountExcludingTaxInCompanyCurrency: 0
                    amountIncludingTax: 0
                    amountIncludingTaxInCompanyCurrency: 0
                    netPrice: 0
                    originDocumentType: "direct"
                    priceOrigin: null
                    priceOriginDeterminated: null
                    priceReason: null
                    priceReasonDeterminated: null
                    quantity: 0
                    quantityInStockUnit: 0
                    requestedDeliveryDate: $orderDate
                    salesSite: $salesSite_id
                    unit: null
                    unitToStockUnitConversionFactor: 0
                    shipToAddress: $shipToAddress
                    shipToCustomerAddress: $shipToCustomerAddress
                    shippingDate: $orderDate
                    shippingStatus: "notShipped"
                    status: "quote"
                    stockSite: $salesSite_id
                    stockUnit: null
                    storedAttributes: null
                    storedDimensions: null
                    taxAmount: 0
                    taxAmountAdjusted: 0
                    uiTaxes: "{\"taxEngine\":\"genericTaxCalculation\",\"taxes\":[]}"
                    workInProgress: null
                }
            ) {
                item(orderBy: "{\"name\":1,\"_id\":1}", after: "[\"Awesome Fresh Tuna\",9579]#74", first: 10) {
                    edges {
                        node {
                            _id
                            name
                            id
                            maximumSalesQuantity
                            minimumSalesQuantity
                            stockUnit {
                                decimalDigits
                                symbol
                                id
                                name
                                _id
                            }
                            purchaseUnit {
                                decimalDigits
                                symbol
                                id
                                name
                                _id
                            }
                            salesUnit {
                                decimalDigits
                                symbol
                                id
                                name
                                _id
                            }
                            isBought
                            isManufactured
                            isStockManaged
                            type
                            image {
                                value
                            }
                            description
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
