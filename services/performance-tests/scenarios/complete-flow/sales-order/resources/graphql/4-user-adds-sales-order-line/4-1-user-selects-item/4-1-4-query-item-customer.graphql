query Query($itemCustomer_filter: String) {
    xtremMasterData {
        itemCustomer {
            query(filter: $itemCustomer_filter) {
                edges {
                    node {
                        _id
                        salesUnit {
                            _id
                            name
                            id
                            symbol
                            decimalDigits
                        }
                        minimumSalesQuantity
                        maximumSalesQuantity
                        salesUnitToStockUnitConversion
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
