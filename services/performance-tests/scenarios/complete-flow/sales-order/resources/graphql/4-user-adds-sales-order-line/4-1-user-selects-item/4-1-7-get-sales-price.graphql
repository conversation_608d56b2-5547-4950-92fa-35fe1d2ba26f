query Query(
    $salesSite: IntReference
    $stockSite: IntReference
    $soldToCustomer: IntReference
    $currency: IntReference
    $item: IntReference
    $quantity: Decimal
    $unit: IntReference
    $orderDate: Date
) {
    xtremMasterData {
        itemCustomerPrice {
            getSalesPrice(
                priceParameters: {
                    salesSite: $salesSite
                    stockSite: $stockSite
                    customer: $soldToCustomer
                    currency: $currency
                    item: $item
                    quantity: $quantity
                    unit: $unit
                    date: $orderDate
                }
            ) {
                grossPrice
                discount
                charge
                priceReason {
                    _id
                    name
                }
                error
            }
        }
    }
}
