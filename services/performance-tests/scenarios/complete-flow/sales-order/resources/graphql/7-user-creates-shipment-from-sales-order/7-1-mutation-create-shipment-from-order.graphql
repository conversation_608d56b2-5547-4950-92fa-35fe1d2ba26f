mutation CreateSalesShipmentsFromOrders($salesOrder_id: IntReference) {
    xtremSales {
        salesOrder {
            createSalesShipmentsFromOrders(
                salesDocuments: [$salesOrder_id]
                processOptions: { processAllShippableLines: true }
            ) {
                status
                numberOfShipments
                documentsCreated {
                  _updateTick
                  _customData
                  _sourceId
                  _id
                  number
                  reference
                  status
                  invoiceStatus
                  returnRequestStatus
                  returnReceiptStatus
                  isPrinted
                  effectiveDate
                  trackingNumber
                  deliveryLeadTime
                  workDays
                  financeIntegrationStatus
                  isOnHold
                  isExternalNote
                  isTransferHeaderNote
                  isTransferLineNote
                  intacctId
                  _etag
                  shippingDate
                  deliveryDate
                  fxRateDate
                  companyFxRate
                  companyFxRateDivisor
                  allocationStatus
                  totalAmountExcludingTax
                  totalAmountExcludingTaxInCompanyCurrency
                  totalGrossProfit
                  stockTransactionStatus
                  rateDescription
                  displayStatus
                  _createStamp
                  _updateStamp
                }
                lineErrors {
                    lineNumber
                    linePosition
                    message
                }
            }
        }
    }
}
