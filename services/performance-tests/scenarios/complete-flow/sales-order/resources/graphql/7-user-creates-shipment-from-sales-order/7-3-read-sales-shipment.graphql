query Read($salesShipment_id: Id, $number_of_lines: Int = 10) {
    rootNode: xtremSales {
        salesShipment {
            read(_id: $salesShipment_id) {
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                entityUse {
                    _id
                    name
                    id
                    description
                }
                isTransferLineNote
                isTransferHeaderNote
                externalNote {
                    value
                }
                isExternalNote
                internalNote {
                    value
                }
                invoiceStatus
                paymentTerm {
                    _id
                    name
                    id
                    description
                }
                currency {
                    _id
                    name
                    id
                    symbol
                }
                billToAddress {
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    locationPhoneNumber
                    concatenatedAddress
                    name
                    _id
                }
                billToLinkedAddress {
                    _id
                    name
                    isActive
                    businessEntity {
                        id
                        name
                        _id
                    }
                    locationPhoneNumber
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                }
                billToCustomer {
                    _id
                    businessEntity {
                        name
                        id
                        currency {
                            symbol
                            id
                            name
                            _id
                        }
                        _id
                        country {
                            _id
                            name
                            id
                            flag {
                                value
                            }
                        }
                        taxIdNumber
                    }
                    creditLimit
                    paymentTerm {
                        name
                        _id
                    }
                    isOnHold
                }
                deliveryDate
                deliveryLeadTime
                workDays
                deliveryMode {
                    _id
                    name
                    id
                    description
                }
                shipToAddress {
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    locationPhoneNumber
                    concatenatedAddress
                    name
                    _id
                }
                incoterm {
                    _id
                    name
                    id
                    description
                }
                trackingNumber
                reference
                salesSite {
                    _id
                    name
                    id
                    isSales
                    isInventory
                    description
                    legalCompany {
                        _id
                        name
                        id
                    }
                }
                stockTransactionStatus
                returnReceiptStatus
                returnRequestStatus
                isPrinted
                lines {
                    query(first: $number_of_lines, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                itemDescription
                                status
                                item {
                                    name
                                    image {
                                        value
                                    }
                                    id
                                    _id
                                    lotManagement
                                    stockUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        name
                                        _id
                                    }
                                    isStockManaged
                                    type
                                    isExpiryManaged
                                    description
                                }
                                _sortValue
                                quantityReceiptInSalesUnit
                                quantityRequestedInSalesUnit
                                quantityInvoicedPostedInSalesUnit
                                quantityInvoicedInProgressInSalesUnit
                                returnReceiptStatus
                                returnRequestStatus
                                invoiceStatus
                                priceReason {
                                    description
                                    isActive
                                    priority
                                    name
                                    _id
                                }
                                priceOrigin
                                charge
                                discount
                                storedDimensions
                                storedAttributes
                                allocationStatus
                                remainingQuantity
                                stockUnit {
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    id
                                }
                                remainingQuantityToAllocate
                                quantityAllocated
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                quantity
                                salesUnit {
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    id
                                }
                                itemImage {
                                    value
                                }
                                stockTransactionStatus
                                isExternalNote
                                internalNote {
                                    value
                                }
                                externalNote {
                                    value
                                }
                                document {
                                    _id
                                }
                                originDocumentType
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                isOnHold
                status
                displayStatus
                shipToCustomerAddress {
                    _id
                    name
                    isActive
                    deliveryDetail {
                        _id
                        isActive
                        shipmentSite {
                            _id
                            name
                            id
                            legalCompany {
                                _id
                                name
                                id
                            }
                        }
                        leadTime
                        mode {
                            _id
                            name
                            id
                        }
                        incoterm {
                            _id
                            name
                            id
                        }
                        isPrimary
                    }
                    businessEntity {
                        id
                        name
                        _id
                    }
                    locationPhoneNumber
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                }
                deliveryDateHeader: deliveryDate
                salesOrderLineCount: lines {
                    query {
                        totalCount
                    }
                }
                shippingDate
                number
                shipToCustomer {
                    _id
                    businessEntity {
                        name
                        id
                        country {
                            id
                            name
                            _id
                        }
                        _id
                        taxIdNumber
                    }
                    creditLimit
                }
                stockSite {
                    _id
                    businessEntity {
                        name
                        id
                    }
                    isInventory
                    isLocationManaged
                    name
                    id
                    description
                    legalCompany {
                        _id
                        name
                        id
                        customerOnHoldCheck
                    }
                }
                _id
                _etag
            }
        }
    }
    navigationPanelItems: xtremSales {
        salesShipment {
            query(
                filter: "{\"displayStatus\":{\"_nin\":[\"shipped\",\"partiallyInvoiced\",\"invoiced\",\"postingInProgress\",\"error\"]}}"
                orderBy: "{\"shippingDate\":-1,\"number\":-1,\"_id\":1}"
            ) {
                edges {
                    node {
                        _id
                        displayStatus
                        isOnHold
                        shippingDate
                        invoiceStatus
                        stockTransactionStatus
                        status
                        paymentTerm {
                            _id
                            name
                            id
                            dueDateType
                            description
                        }
                        billToCustomer {
                            _id
                            businessEntity {
                                name
                                id
                                taxIdNumber
                            }
                        }
                        deliveryDate
                        deliveryLeadTime
                        deliveryMode {
                            _id
                            name
                            id
                        }
                        entityUse {
                            _id
                            name
                            isActive
                            description
                            id
                        }
                        incoterm {
                            _id
                            name
                            id
                            description
                        }
                        trackingNumber
                        stockSite {
                            _id
                            name
                            id
                            legalCompany {
                                _id
                                name
                                id
                                customerOnHoldCheck
                            }
                        }
                        returnReceiptStatus
                        returnRequestStatus
                        isPrinted
                        reference
                        salesSite {
                            _id
                            name
                            id
                            description
                        }
                        shipToCustomer {
                            _id
                            businessEntity {
                                name
                                id
                                taxIdNumber
                            }
                        }
                        number
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
