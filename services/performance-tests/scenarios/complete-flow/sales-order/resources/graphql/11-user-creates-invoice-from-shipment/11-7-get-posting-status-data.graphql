query GetPostingStatusData($salesInvoice_number: String) {
    xtremFinanceData {
        financeTransaction {
            getPostingStatusData(documentNumber: $salesInvoice_number) {
                _id
                documentType
                documentNumber
                documentSysId
                status
                message
                hasFinanceIntegrationApp
                financeIntegrationApp
                financeIntegrationAppRecordId
                financeIntegrationAppUrl
                externalLink
                hasSourceForDimensionLines
            }
        }
    }
}
