query Query($filter: String) {
    xtremSales {
        salesShipmentLineToSalesInvoiceLine {
            query(filter: $filter) {
                edges {
                    node {
                        document {
                            document {
                                _id
                                number
                            }
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
