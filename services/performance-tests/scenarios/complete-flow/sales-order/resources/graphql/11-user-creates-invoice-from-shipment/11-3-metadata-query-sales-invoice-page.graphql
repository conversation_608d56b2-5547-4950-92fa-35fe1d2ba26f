query {
    pages(filter: { packageOrPage: "@sage/xtrem-sales/SalesInvoice", exactMatch: true }) {
        duplicateBindings
        content
        fragments {
            name
            content
        }
        extensions
        plugins
        customFields {
            name
            properties {
                name
                dataType
                componentType
                anchorPropertyName
                anchorPosition
                destinationTypes
                componentAttributes
            }
        }
        customizableNodes {
            fullName
        }
        customizableNodesWizard {
            fullName
        }
        exportTemplatesByNode {
            name
            exportTemplates {
                id
                name
            }
        }
        nodeDetails {
            name
            title
            defaultDataType
            defaultDataTypeDetails {
                name
                type
                title
                node
                precision
                scale
                maxLength
                value {
                    bind
                    title
                    type
                }
                helperText {
                    bind
                    title
                    type
                }
                imageField {
                    bind
                    title
                    type
                }
                columns {
                    bind
                    title
                    type
                }
                values {
                    value
                    title
                }
                tunnelPage
                tunnelPageId {
                    bind
                    title
                    type
                }
            }
            hasAttachments
            properties {
                name
                title
                canSort
                canFilter
                type
                isCustom
                isMutable
                isOnInputType
                isOnOutputType
                enumType
                dataType
                dataTypeDetails {
                    name
                    type
                    title
                    node
                    precision
                    scale
                    maxLength
                    value {
                        bind
                        title
                        type
                    }
                    helperText {
                        bind
                        title
                        type
                    }
                    imageField {
                        bind
                        title
                        type
                    }
                    columns {
                        bind
                        title
                        type
                    }
                    values {
                        value
                        title
                    }
                    tunnelPage
                    tunnelPageId {
                        bind
                        title
                        type
                    }
                }
                targetNode
            }
            mutations {
                name
                title
                parameters {
                    name
                    title
                }
            }
        }
        access {
            node
            bindings {
                name
                status
            }
        }
        strings {
            key
            content
        }
    }
}
