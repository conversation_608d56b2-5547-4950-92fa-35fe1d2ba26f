query GetDefaults(
    $invoice_id: Id
    $salesSite_id: IntReference
    $billToCustomer_id: IntReference
    $invoice_number: String
    $invoiceDate: Date
    $fxRateDate: Date
    $companyFxRate: Decimal
    $companyFxRateDivisor: Decimal
    $billToLinkedAddress_id: IntReference
    $totalTaxAmount: Decimal
    $totalTaxAmountAdjusted: Decimal
    $creationNumber: String
    $currency_id: IntReference
    $paymentTerm_id: IntReference
    $dueDate: Date
    $billToAddress: Address_Input
    $billToContact: Contact_Input
    $number_of_lines: Int = 10
) {
    nestedDefaults: xtremSales {
        salesInvoice {
            getDefaults(
                data: {
                    _id: $invoice_id
                    salesSite: $salesSite_id
                    billToCustomer: $billToCustomer_id
                    number: $invoice_number
                    invoiceDate: $invoiceDate
                    fxRateDate: $fxRateDate
                    companyFxRate: $companyFxRate
                    companyFxRateDivisor: $companyFxRateDivisor
                    status: "draft"
                    displayStatus: "draft"
                    creditStatus: "notCredited"
                    billToLinkedAddress: $billToLinkedAddress_id
                    taxCalculationStatus: "done"
                    isPrinted: false
                    isSent: false
                    creationNumber: $creationNumber
                    currency: $currency_id
                    paymentTerm: $paymentTerm_id
                    dueDate: $dueDate
                    incoterm: null
                    billToAddress: $billToAddress
                    totalTaxAmount: $totalTaxAmount
                    totalTaxAmountAdjusted: $totalTaxAmountAdjusted
                    internalNote: null
                    isExternalNote: false
                    externalNote: null
                    isTransferHeaderNote: false
                    isTransferLineNote: false
                    billToContact: $billToContact
                    lines: [{}]
                }
            ) {
                lines {
                    query(first: $number_of_lines, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                amountIncludingTax
                                itemDescription
                                item {
                                    name
                                    id
                                    _id
                                    maximumSalesQuantity
                                    minimumSalesQuantity
                                    salesUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        _id
                                    }
                                    stockUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        _id
                                    }
                                    isStockManaged
                                    type
                                    image {
                                        value
                                    }
                                    description
                                }
                                itemImage {
                                    value
                                }
                                entityUse {
                                    _id
                                    name
                                    id
                                    description
                                }
                                uiTaxes
                                quantityCreditedPostedInSalesUnit
                                quantityCreditedInProgressInSalesUnit
                                consumptionAddress {
                                    concatenatedAddress
                                    country {
                                        zipLabel
                                        regionLabel
                                        id
                                        name
                                        _id
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                    locationPhoneNumber
                                    name
                                    _id
                                }
                                creditStatus
                                storedDimensions
                                storedAttributes
                                consumptionLinkedAddress {
                                    _id
                                    name
                                    isActive
                                    businessEntity {
                                        id
                                        name
                                        _id
                                    }
                                    locationPhoneNumber
                                    country {
                                        _id
                                        name
                                        id
                                        flag {
                                            value
                                        }
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                }
                                isPriceDeterminated
                                priceReasonDeterminated {
                                    isActive
                                    description
                                    priority
                                    name
                                    _id
                                }
                                priceOriginDeterminated
                                chargeDeterminated
                                discountDeterminated
                                grossPriceDeterminated
                                stockUnit {
                                    _id
                                    symbol
                                    name
                                    decimalDigits
                                    id
                                }
                                salesSite {
                                    _id
                                    name
                                    id
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                        legislation {
                                            id
                                            name
                                            _id
                                        }
                                    }
                                }
                                providerSite {
                                    _id
                                    name
                                    id
                                    businessEntity {
                                        _id
                                    }
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                grossProfitAmount
                                stockCostAmount
                                taxAmountAdjusted
                                taxAmount
                                amountIncludingTaxInCompanyCurrency
                                amountExcludingTaxInCompanyCurrency
                                amountExcludingTax
                                priceReason {
                                    _id
                                    name
                                    id
                                    description
                                    isActive
                                    priority
                                }
                                priceOrigin
                                netPrice
                                charge
                                discount
                                grossPrice
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                quantity
                                salesUnit {
                                    _id
                                    symbol
                                    name
                                    decimalDigits
                                    id
                                }
                                taxDate
                                taxCalculationStatus
                                originDocumentType
                                isExternalNote
                                internalNote {
                                    value
                                }
                                externalNote {
                                    value
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
    }
}
