query Read($salesOrder_id: Id, $number_of_lines: Int = 10) {
    xtremSales {
        salesOrder {
            read(_id: $salesOrder_id) {
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                entityUse {
                    _id
                    name
                    id
                    description
                }
                proformaInvoices {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                version
                                salesOrder {
                                    number
                                }
                                link
                                isLinkExpired
                                fileName
                                isActive
                                customerComment {
                                    value
                                }
                                _id
                                linkExpirationDateTime
                                isSent
                                createdBy
                                expirationDate
                                issueDate
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                isTransferLineNote
                isTransferHeaderNote
                externalNote {
                    value
                }
                isExternalNote
                internalNote {
                    value
                }
                totalAmountIncludingTaxInCompanyCurrency
                totalAmountExcludingTaxInCompanyCurrency
                taxDetails: taxes {
                    query(first: 10, orderBy: "{\"_sortValue\":1,\"tax\":1}") {
                        edges {
                            node {
                                taxableAmount
                                exemptAmount
                                taxAmount
                                nonTaxableAmount
                                taxAmountAdjusted
                                deductibleTaxAmount
                                currency {
                                    id
                                }
                                tax
                                taxCategory
                                jurisdictionName
                                isSubjectToGlTaxExcludedAmount
                                isTaxMandatory
                                isReverseCharge
                                deductibleTaxRate
                                taxReference {
                                    id
                                }
                                taxCategoryReference {
                                    id
                                }
                                taxRate
                                _sortValue
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                totalGrossProfit
                totalAmountIncludingTax
                totalTaxAmountAdjusted
                totalTaxAmount
                totalAmountExcludingTax
                billToAddress {
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    locationPhoneNumber
                    concatenatedAddress
                    name
                    _id
                }
                paymentTerm {
                    _id
                    name
                    id
                    dueDateType
                    description
                }
                billToLinkedAddress {
                    _id
                    name
                    isActive
                    businessEntity {
                        id
                        name
                        _id
                    }
                    locationPhoneNumber
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                }
                billToCustomer {
                    _id
                    businessEntity {
                        name
                        id
                        _id
                        country {
                            id
                            name
                            _id
                        }
                        currency {
                            symbol
                            id
                            name
                            _id
                        }
                        taxIdNumber
                    }
                    paymentTerm {
                        name
                        _id
                    }
                    isOnHold
                    creditLimit
                }
                invoiceStatus
                shippingStatus
                doNotShipAfterDate
                doNotShipBeforeDate
                expectedDeliveryDate
                shippingDate
                workDays
                deliveryLeadTime
                requestedDeliveryDate
                deliveryMode {
                    _id
                    name
                    id
                    description
                }
                incoterm {
                    _id
                    name
                    id
                    description
                }
                shipToAddress {
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    locationPhoneNumber
                    concatenatedAddress
                    name
                    _id
                }
                shipToCustomer {
                    _id
                    businessEntity {
                        name
                        id
                        _id
                        country {
                            id
                            name
                            _id
                        }
                        taxIdNumber
                    }
                }
                stockSite {
                    _id
                    businessEntity {
                        name
                        id
                        _id
                    }
                    primaryAddress {
                        country {
                            id
                            name
                            _id
                        }
                        _id
                    }
                    isInventory
                    description
                    name
                    id
                    legalCompany {
                        _id
                        name
                        id
                    }
                }
                shipToCustomerAddress {
                    _id
                    name
                    isActive
                    deliveryDetail {
                        _id
                        isActive
                        shipmentSite {
                            _id
                            name
                            id
                            legalCompany {
                                _id
                                name
                                id
                            }
                        }
                        mode {
                            _id
                            name
                            id
                        }
                        incoterm {
                            _id
                            name
                            id
                        }
                        leadTime
                        isPrimary
                    }
                    businessEntity {
                        id
                        name
                        _id
                    }
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    locationPhoneNumber
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                }
                soldToAddress {
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    locationPhoneNumber
                    concatenatedAddress
                    name
                    _id
                }
                soldToLinkedAddress {
                    _id
                    name
                    isActive
                    businessEntity {
                        id
                        name
                        _id
                    }
                    locationPhoneNumber
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                }
                customerNumber
                rateDescription
                currency {
                    _id
                    name
                    id
                    decimalDigits
                    symbol
                }
                allocationRequestStatus
                allocationStatus
                isOrderAssignmentLinked
                isSent
                isPrinted
                salesOrderIncludingTax: lines {
                    readAggregate {
                        amountIncludingTax {
                            sum
                        }
                    }
                }
                salesOrderExcludingTax: lines {
                    readAggregate {
                        amountExcludingTax {
                            sum
                        }
                    }
                }
                expectedDeliveryDateHeader: expectedDeliveryDate
                salesOrderLineCount: lines {
                    query {
                        totalCount
                    }
                }
                lines {
                    query(first: $number_of_lines, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                amountIncludingTax
                                itemDescription
                                status
                                item {
                                    name
                                    id
                                    _id
                                    maximumSalesQuantity
                                    minimumSalesQuantity
                                    stockUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        name
                                        _id
                                    }
                                    purchaseUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        name
                                        _id
                                    }
                                    salesUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        name
                                        _id
                                    }
                                    isBought
                                    isManufactured
                                    isStockManaged
                                    type
                                    image {
                                        value
                                    }
                                    description
                                }
                                itemImage {
                                    value
                                }
                                uAssignmentOrder
                                suppliedQuantity
                                workInProgress {
                                    _id
                                }
                                uPurchaseOrderLine {
                                    documentId
                                    documentNumber
                                    _id
                                }
                                uWorkOrderLine {
                                    documentId
                                    documentNumber
                                    _id
                                }
                                uiTaxes
                                shipToAddress {
                                    concatenatedAddress
                                    country {
                                        zipLabel
                                        regionLabel
                                        id
                                        name
                                        _id
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                    locationPhoneNumber
                                    name
                                    _id
                                }
                                storedDimensions
                                storedAttributes
                                computedAttributes
                                shipToCustomerAddress {
                                    _id
                                    name
                                    isActive
                                    deliveryDetail {
                                        _id
                                        isActive
                                        shipmentSite {
                                            _id
                                            name
                                            id
                                            businessEntity {
                                                _id
                                            }
                                            legalCompany {
                                                _id
                                                name
                                                id
                                            }
                                        }
                                        leadTime
                                        mode {
                                            _id
                                            name
                                            id
                                            description
                                        }
                                        incoterm {
                                            _id
                                            name
                                            id
                                            description
                                        }
                                        isPrimary
                                    }
                                    businessEntity {
                                        id
                                        name
                                        _id
                                    }
                                    locationPhoneNumber
                                    country {
                                        _id
                                        name
                                        id
                                        flag {
                                            value
                                        }
                                        zipLabel
                                        regionLabel
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                }
                                remainingQuantityToAllocate
                                quantityAllocated
                                remainingQuantityToShipInStockUnit
                                taxDate
                                taxCalculationStatus
                                invoiceStatus
                                shippingStatus
                                stockShortageStatus
                                allocationRequestStatus
                                allocationStatus
                                deliveryMode {
                                    _id
                                    name
                                    id
                                    description
                                }
                                entityUse {
                                    _id
                                    name
                                    id
                                    description
                                }
                                expectedDeliveryDate
                                deliveryLeadTime
                                requestedDeliveryDate
                                doNotShipAfterDate
                                doNotShipBeforeDate
                                shippingDate
                                stockSite {
                                    _id
                                    name
                                    id
                                    businessEntity {
                                        _id
                                    }
                                    isManufacturing
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                stockShortageInSalesUnit
                                availableQuantityInSalesUnit
                                grossProfitAmount
                                stockCostAmount
                                amountIncludingTaxInCompanyCurrency
                                taxAmountAdjusted
                                taxAmount
                                amountExcludingTaxInCompanyCurrency
                                amountExcludingTax
                                priceReason {
                                    _id
                                    name
                                    id
                                    description
                                    isActive
                                    priority
                                }
                                priceOrigin
                                isPriceDeterminated
                                priceReasonDeterminated {
                                    description
                                    isActive
                                    priority
                                    name
                                    _id
                                }
                                priceOriginDeterminated
                                chargeDeterminated
                                discountDeterminated
                                grossPriceDeterminated
                                netPrice
                                charge
                                discount
                                grossPrice
                                remainingQuantityToInvoiceInSalesUnit
                                invoicedQuantityInSalesUnit
                                quantityToInvoiceInProgressInSalesUnit
                                remainingQuantityToShipInSalesUnit
                                shippedQuantityInSalesUnit
                                quantityToShipInProgressInSalesUnit
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                stockShortageInStockUnit
                                availableQuantityInStockUnit
                                stockOnHand
                                stockUnit {
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    id
                                }
                                quantity
                                salesUnit {
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    id
                                }
                                salesSite {
                                    description
                                    legalCompany {
                                        legislation {
                                            id
                                            name
                                            _id
                                        }
                                        id
                                        name
                                        _id
                                    }
                                    id
                                    name
                                    _id
                                }
                                itemSite {
                                    inStockQuantity
                                    preferredProcess
                                    isOrderToOrder
                                    prodLeadTime
                                    _id
                                }
                                originDocumentType
                                isExternalNote
                                internalNote {
                                    value
                                }
                                externalNote {
                                    value
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                isOnHold
                status
                displayStatus
                isQuote
                companyFxRateDivisor
                companyFxRate
                fxRateDate
                companyCurrency {
                    _id
                    name
                    id
                    rounding
                    decimalDigits
                    symbol
                }
                taxEngine
                taxCalculationStatus
                orderDate
                number
                soldToCustomer {
                    _id
                    businessEntity {
                        name
                        id
                        _id
                        country {
                            id
                            name
                            _id
                        }
                        taxIdNumber
                    }
                    primaryAddress {
                        locationPhoneNumber
                        country {
                            id
                            name
                            _id
                        }
                        postcode
                        region
                        city
                        addressLine2
                        addressLine1
                        name
                        isActive
                        _id
                    }
                    creditLimit
                }
                salesSite {
                    _id
                    name
                    id
                    businessEntity {
                        _id
                        currency {
                            decimalDigits
                            symbol
                            id
                            name
                            _id
                        }
                    }
                    isPurchase
                    isSales
                    isInventory
                    description
                    legalCompany {
                        _id
                        name
                        id
                        customerOnHoldCheck
                        taxEngine
                        currency {
                            decimalDigits
                            symbol
                            id
                            name
                            _id
                        }
                        legislation {
                            id
                            name
                            _id
                        }
                    }
                }
                isCloseHidden
                _id
                soldToContact {
                    _id
                    email
                    title
                    locationPhoneNumber
                    position
                    role
                    preferredName
                    firstName
                    lastName
                }
                _etag
            }
        }
    }
}
