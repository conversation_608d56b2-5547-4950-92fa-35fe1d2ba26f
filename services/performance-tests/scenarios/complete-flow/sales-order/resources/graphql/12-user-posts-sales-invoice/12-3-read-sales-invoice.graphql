query Read($salesInvoice_id: Id, $number_of_lines: Int = 10) {
    rootNode: xtremSales {
        salesInvoice {
            read(_id: $salesInvoice_id) {
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                billToContact {
                    _id
                    email
                    title
                    locationPhoneNumber
                    position
                    role
                    preferredName
                    firstName
                    lastName
                }
                isTransferLineNote
                isTransferHeaderNote
                externalNote {
                    value
                }
                isExternalNote
                internalNote {
                    value
                }
                taxDetails: taxes {
                    query(first: 10, orderBy: "{\"_sortValue\":1,\"tax\":1}") {
                        edges {
                            node {
                                taxableAmount
                                exemptAmount
                                taxAmount
                                nonTaxableAmount
                                taxAmountAdjusted
                                deductibleTaxAmount
                                currency {
                                    id
                                }
                                tax
                                taxCategory
                                jurisdictionName
                                isSubjectToGlTaxExcludedAmount
                                isTaxMandatory
                                isReverseCharge
                                deductibleTaxRate
                                taxReference {
                                    id
                                }
                                taxCategoryReference {
                                    id
                                }
                                taxRate
                                _sortValue
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                totalGrossProfit
                totalAmountIncludingTaxInCompanyCurrency
                totalAmountIncludingTax
                totalTaxAmountAdjusted
                totalTaxAmount
                totalAmountExcludingTaxInCompanyCurrency
                totalAmountExcludingTax
                billToAddress {
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                    locationPhoneNumber
                    concatenatedAddress
                    name
                    _id
                }
                incoterm {
                    _id
                    name
                    id
                    description
                }
                dueDate
                paymentTerm {
                    _id
                    name
                    id
                    days
                    dueDateType
                    description
                }
                rateDescription
                currency {
                    _id
                    name
                    id
                    decimalDigits
                    symbol
                }
                creationNumber
                isSent
                isPrinted
                taxCalculationStatus
                lines {
                    query(first: $number_of_lines, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                amountIncludingTax
                                itemDescription
                                item {
                                    name
                                    id
                                    _id
                                    maximumSalesQuantity
                                    minimumSalesQuantity
                                    salesUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        _id
                                    }
                                    stockUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        _id
                                    }
                                    isStockManaged
                                    type
                                    image {
                                        value
                                    }
                                    description
                                }
                                itemImage {
                                    value
                                }
                                entityUse {
                                    _id
                                    name
                                    id
                                    description
                                }
                                uiTaxes
                                quantityCreditedPostedInSalesUnit
                                quantityCreditedInProgressInSalesUnit
                                consumptionAddress {
                                    concatenatedAddress
                                    country {
                                        zipLabel
                                        regionLabel
                                        id
                                        name
                                        _id
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                    locationPhoneNumber
                                    name
                                    _id
                                }
                                creditStatus
                                storedDimensions
                                storedAttributes
                                consumptionLinkedAddress {
                                    _id
                                    name
                                    isActive
                                    businessEntity {
                                        id
                                        name
                                        _id
                                    }
                                    locationPhoneNumber
                                    country {
                                        _id
                                        name
                                        id
                                        flag {
                                            value
                                        }
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                }
                                isPriceDeterminated
                                priceReasonDeterminated {
                                    isActive
                                    description
                                    priority
                                    name
                                    _id
                                }
                                priceOriginDeterminated
                                chargeDeterminated
                                discountDeterminated
                                grossPriceDeterminated
                                stockUnit {
                                    _id
                                    symbol
                                    name
                                    decimalDigits
                                    id
                                }
                                salesSite {
                                    _id
                                    name
                                    id
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                        legislation {
                                            id
                                            name
                                            _id
                                        }
                                    }
                                }
                                providerSite {
                                    _id
                                    name
                                    id
                                    businessEntity {
                                        _id
                                    }
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                grossProfitAmount
                                stockCostAmount
                                taxAmountAdjusted
                                taxAmount
                                amountIncludingTaxInCompanyCurrency
                                amountExcludingTaxInCompanyCurrency
                                amountExcludingTax
                                priceReason {
                                    _id
                                    name
                                    id
                                    description
                                    isActive
                                    priority
                                }
                                priceOrigin
                                netPrice
                                charge
                                discount
                                grossPrice
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                quantity
                                salesUnit {
                                    _id
                                    symbol
                                    name
                                    decimalDigits
                                    id
                                }
                                taxDate
                                taxCalculationStatus
                                originDocumentType
                                isExternalNote
                                internalNote {
                                    value
                                }
                                externalNote {
                                    value
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                billToLinkedAddress {
                    _id
                    name
                    isActive
                    businessEntity {
                        id
                        name
                        _id
                    }
                    locationPhoneNumber
                    country {
                        _id
                        name
                        id
                        flag {
                            value
                        }
                        zipLabel
                        regionLabel
                    }
                    postcode
                    region
                    city
                    addressLine2
                    addressLine1
                }
                creditStatus
                #canPrint
                displayStatus
                status
                taxEngine
                companyFxRateDivisor
                companyFxRate
                fxRateDate
                companyCurrency {
                    _id
                    name
                    id
                    rounding
                    decimalDigits
                    symbol
                }
                salesInvoiceIncludingTax: lines {
                    readAggregate {
                        amountIncludingTax {
                            sum
                        }
                    }
                }
                salesInvoiceExcludingTax: lines {
                    readAggregate {
                        amountExcludingTax {
                            sum
                        }
                    }
                }
                salesInvoiceLineCount: lines {
                    query {
                        totalCount
                    }
                }
                invoiceDate
                number
                billToCustomer {
                    _id
                    businessEntity {
                        name
                        id
                        _id
                        currency {
                            decimalDigits
                            symbol
                            id
                            name
                            _id
                        }
                        country {
                            id
                            name
                            _id
                        }
                        taxIdNumber
                    }
                    paymentTerm {
                        description
                        name
                        _id
                    }
                    isOnHold
                }
                salesSite {
                    _id
                    name
                    id
                    businessEntity {
                        _id
                        currency {
                            decimalDigits
                            symbol
                            id
                            name
                            _id
                        }
                        id
                        name
                    }
                    isSales
                    isInventory
                    description
                    primaryAddress {
                        country {
                            id
                            name
                            _id
                        }
                        _id
                    }
                    legalCompany {
                        _id
                        name
                        id
                        taxEngine
                        currency {
                            decimalDigits
                            symbol
                            id
                            name
                            _id
                        }
                        legislation {
                            id
                            name
                            _id
                        }
                    }
                }
                _id
                _etag
            }
        }
    }
}
