query GetDefaults($sales_site_id: String, $number_of_lines: Int) {
    nestedDefaults: xtremSales {
        salesInvoice {
            getDefaults(
                data: {
                    _id: "98"
                    salesSite: $sales_site_id
                    billToCustomer: "_id:209"
                    number: "SIPF1240001"
                    invoiceDate: "2024-07-08"
                    fxRateDate: "2024-07-08"
                    companyFxRate: 1
                    companyFxRateDivisor: 1
                    status: "posted"
                    displayStatus: "posted"
                    creditStatus: "notCredited"
                    billToLinkedAddress: "_id:1204059"
                    taxCalculationStatus: "done"
                    isPrinted: false
                    isSent: false
                    creationNumber: "SIF1240001"
                    currency: "_id:214"
                    paymentTerm: "_id:48"
                    dueDate: "2024-07-08"
                    incoterm: null
                    billToAddress: {
                        country: "_id:217"
                        postcode: "46455"
                        region: "Basse-Normandie"
                        city: "Vitry-sur-Seine"
                        addressLine2: "0 étage"
                        addressLine1: "85 Boulevard Monsieur-le-Prince"
                        locationPhoneNumber: "************"
                        name: "Vitry-sur-Seine-Boulevard Monsieur-le-Prince"
                        _id: "9894"
                    }
                    totalTaxAmount: 2.4
                    totalTaxAmountAdjusted: 2.4
                    internalNote: null
                    isExternalNote: false
                    externalNote: null
                    isTransferHeaderNote: false
                    isTransferLineNote: false
                    billToContact: {
                        _id: "19389"
                        email: "<EMAIL>"
                        title: "mr"
                        locationPhoneNumber: "************"
                        position: null
                        role: "mainContact"
                        preferredName: null
                        firstName: "Toby"
                        lastName: "Gulgowski"
                    }
                    lines: [{}]
                }
            ) {
                lines {
                    query(first: $number_of_lines, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                amountIncludingTax
                                itemDescription
                                item {
                                    name
                                    id
                                    _id
                                    maximumSalesQuantity
                                    minimumSalesQuantity
                                    salesUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        _id
                                    }
                                    stockUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        _id
                                    }
                                    isStockManaged
                                    type
                                    image {
                                        value
                                    }
                                    description
                                }
                                itemImage {
                                    value
                                }
                                entityUse {
                                    _id
                                    name
                                    id
                                    description
                                }
                                uiTaxes
                                quantityCreditedPostedInSalesUnit
                                quantityCreditedInProgressInSalesUnit
                                consumptionAddress {
                                    concatenatedAddress
                                    country {
                                        zipLabel
                                        regionLabel
                                        id
                                        name
                                        _id
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                    locationPhoneNumber
                                    name
                                    _id
                                }
                                creditStatus
                                storedDimensions
                                storedAttributes
                                consumptionLinkedAddress {
                                    _id
                                    name
                                    isActive
                                    businessEntity {
                                        id
                                        name
                                        _id
                                    }
                                    locationPhoneNumber
                                    country {
                                        _id
                                        name
                                        id
                                        flag {
                                            value
                                        }
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                }
                                isPriceDeterminated
                                priceReasonDeterminated {
                                    isActive
                                    description
                                    priority
                                    name
                                    _id
                                }
                                priceOriginDeterminated
                                chargeDeterminated
                                discountDeterminated
                                grossPriceDeterminated
                                stockUnit {
                                    _id
                                    symbol
                                    name
                                    decimalDigits
                                    id
                                }
                                salesSite {
                                    _id
                                    name
                                    id
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                        legislation {
                                            id
                                            name
                                            _id
                                        }
                                    }
                                }
                                providerSite {
                                    _id
                                    name
                                    id
                                    businessEntity {
                                        _id
                                    }
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                grossProfitAmount
                                stockCostAmount
                                taxAmountAdjusted
                                taxAmount
                                amountIncludingTaxInCompanyCurrency
                                amountExcludingTaxInCompanyCurrency
                                amountExcludingTax
                                priceReason {
                                    _id
                                    name
                                    id
                                    description
                                    isActive
                                    priority
                                }
                                priceOrigin
                                netPrice
                                charge
                                discount
                                grossPrice
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                quantity
                                salesUnit {
                                    _id
                                    symbol
                                    name
                                    decimalDigits
                                    id
                                }
                                taxDate
                                taxCalculationStatus
                                originDocumentType
                                isExternalNote
                                internalNote {
                                    value
                                }
                                externalNote {
                                    value
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
    }
}
