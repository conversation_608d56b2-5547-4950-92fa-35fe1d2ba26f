query GetPostingStatusData($document_number: String) {
    xtremFinanceData {
        financeTransaction {
            getPostingStatusData(documentNumber: $document_number) {
                _id
                documentType
                documentNumber
                documentSysId
                status
                message
                hasFinanceIntegrationApp
                financeIntegrationApp
                financeIntegrationAppRecordId
                financeIntegrationAppUrl
                externalLink
                hasSourceForDimensionLines
            }
        }
    }
}
