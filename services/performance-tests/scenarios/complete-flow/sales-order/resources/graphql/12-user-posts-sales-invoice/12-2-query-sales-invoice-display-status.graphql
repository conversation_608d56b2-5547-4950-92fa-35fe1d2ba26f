query Query($filter: String) {
    xtremSales {
        salesInvoice {
            query(filter: $filter) {
                edges {
                    node {
                        displayStatus
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
