query {
    xtremSales {
        salesInvoice {
            query(
                filter: "{\"displayStatus\":{\"_nin\":[\"posted\",\"partiallyCredited\",\"credited\"]}}"
                orderBy: "{\"invoiceDate\":-1,\"number\":-1,\"_id\":1}"
                first: 30
            ) {
                edges {
                    node {
                        _id
                        displayStatus
                        billToContact {
                            locationPhoneNumber
                            position
                            role
                            preferredName
                            email
                            title
                            firstName
                            lastName
                            _id
                        }
                        creditStatus
                        taxCalculationStatus
                        status
                        #canPrint
                        billToLinkedAddress {
                            _id
                        }
                        totalTaxAmount
                        totalGrossProfit
                        totalAmountExcludingTax
                        isSent
                        isPrinted
                        creationNumber
                        currency {
                            _id
                            name
                            id
                            symbol
                            rounding
                            decimalDigits
                        }
                        incoterm {
                            _id
                            name
                            id
                            description
                        }
                        paymentTerm {
                            _id
                            name
                            id
                            dueDateType
                            description
                        }
                        dueDate
                        totalAmountIncludingTax
                        salesSite {
                            _id
                            name
                            id
                            businessEntity {
                                _id
                            }
                            description
                        }
                        invoiceDate
                        billToCustomer {
                            _id
                            businessEntity {
                                name
                                id
                                _id
                                taxIdNumber
                            }
                        }
                        number
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
