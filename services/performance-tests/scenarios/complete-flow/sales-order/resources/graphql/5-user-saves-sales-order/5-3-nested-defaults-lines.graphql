query NestedDefaults(
    $salesOrder_id: Id
    $salesOrder_number: String
    $orderDate: Date
    $salesSite_id: IntReference
    $stockSite_id: IntReference
    $soldToCustomer_id: IntReference
    $soldToContact: Contact_Input
    $soldToAddress: Address_Input
    $shipToCustomerAddress: IntReference
    $shipToCustomer: IntReference
    $shipToAddress: Address_Input
    $deliveryMode: IntReference
    $billToCustomer: IntReference
    $billToLinkedAddress: IntReference
    $paymentTerm: IntReference
    $billToAddress: Address_Input
    $currency: IntReference
    $soldToLinkedAddress: IntReference
    $number_of_lines: Int = 10
) {
    nestedDefaults: xtremSales {
        salesOrder {
            getDefaults(
                data: {
                    _id: $salesOrder_id
                    soldToContact: $soldToContact
                    salesSite: $salesSite_id
                    soldToCustomer: $soldToCustomer_id
                    number: $salesOrder_number
                    orderDate: $orderDate
                    taxCalculationStatus: "done"
                    fxRateDate: $orderDate
                    companyFxRate: 1
                    companyFxRateDivisor: 1
                    isQuote: true
                    displayStatus: "quote"
                    status: "quote"
                    expectedDeliveryDate: $orderDate
                    isPrinted: false
                    isSent: false
                    currency: $currency
                    customerNumber: null
                    soldToLinkedAddress: $soldToLinkedAddress
                    soldToAddress: $soldToAddress
                    shipToCustomerAddress: $shipToCustomerAddress
                    stockSite: $stockSite_id
                    shipToCustomer: $shipToCustomer
                    shipToAddress: $shipToAddress
                    incoterm: null
                    deliveryMode: $deliveryMode
                    requestedDeliveryDate: $orderDate
                    deliveryLeadTime: 0
                    shippingDate: $orderDate
                    doNotShipBeforeDate: null
                    doNotShipAfterDate: null
                    shippingStatus: "notShipped"
                    invoiceStatus: "notInvoiced"
                    billToCustomer: $billToCustomer
                    billToLinkedAddress: $billToLinkedAddress
                    paymentTerm: $paymentTerm
                    billToAddress: $billToAddress
                    totalTaxAmount: 2.4
                    totalTaxAmountAdjusted: 2.4
                    internalNote: null
                    isExternalNote: false
                    externalNote: null
                    isTransferHeaderNote: false
                    isTransferLineNote: false
                    entityUse: null
                    lines: [{}]
                }
            ) {
                lines {
                    query(first: $number_of_lines, orderBy: "{\"_sortValue\":1}") {
                        edges {
                            node {
                                amountIncludingTax
                                itemDescription
                                status
                                item {
                                    name
                                    id
                                    _id
                                    maximumSalesQuantity
                                    minimumSalesQuantity
                                    stockUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        name
                                        _id
                                    }
                                    purchaseUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        name
                                        _id
                                    }
                                    salesUnit {
                                        decimalDigits
                                        symbol
                                        id
                                        name
                                        _id
                                    }
                                    isBought
                                    isManufactured
                                    isStockManaged
                                    type
                                    image {
                                        value
                                    }
                                    description
                                }
                                itemImage {
                                    value
                                }
                                uAssignmentOrder
                                suppliedQuantity
                                workInProgress {
                                    _id
                                }
                                uPurchaseOrderLine {
                                    documentId
                                    documentNumber
                                    _id
                                }
                                uWorkOrderLine {
                                    documentId
                                    documentNumber
                                    _id
                                }
                                uiTaxes
                                shipToAddress {
                                    concatenatedAddress
                                    country {
                                        zipLabel
                                        regionLabel
                                        id
                                        name
                                        _id
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                    locationPhoneNumber
                                    name
                                    _id
                                }
                                storedDimensions
                                storedAttributes
                                computedAttributes
                                shipToCustomerAddress {
                                    _id
                                    name
                                    isActive
                                    deliveryDetail {
                                        _id
                                        isActive
                                        shipmentSite {
                                            _id
                                            name
                                            id
                                            businessEntity {
                                                _id
                                            }
                                            legalCompany {
                                                _id
                                                name
                                                id
                                            }
                                        }
                                        leadTime
                                        mode {
                                            _id
                                            name
                                            id
                                            description
                                        }
                                        incoterm {
                                            _id
                                            name
                                            id
                                            description
                                        }
                                        isPrimary
                                    }
                                    businessEntity {
                                        id
                                        name
                                        _id
                                    }
                                    locationPhoneNumber
                                    country {
                                        _id
                                        name
                                        id
                                        flag {
                                            value
                                        }
                                        zipLabel
                                        regionLabel
                                    }
                                    postcode
                                    region
                                    city
                                    addressLine2
                                    addressLine1
                                }
                                remainingQuantityToAllocate
                                quantityAllocated
                                remainingQuantityToShipInStockUnit
                                taxDate
                                taxCalculationStatus
                                invoiceStatus
                                shippingStatus
                                stockShortageStatus
                                allocationRequestStatus
                                allocationStatus
                                deliveryMode {
                                    _id
                                    name
                                    id
                                    description
                                }
                                entityUse {
                                    _id
                                    name
                                    id
                                    description
                                }
                                expectedDeliveryDate
                                deliveryLeadTime
                                requestedDeliveryDate
                                doNotShipAfterDate
                                doNotShipBeforeDate
                                shippingDate
                                stockSite {
                                    _id
                                    name
                                    id
                                    businessEntity {
                                        _id
                                    }
                                    isManufacturing
                                    description
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                stockShortageInSalesUnit
                                availableQuantityInSalesUnit
                                grossProfitAmount
                                stockCostAmount
                                amountIncludingTaxInCompanyCurrency
                                taxAmountAdjusted
                                taxAmount
                                amountExcludingTaxInCompanyCurrency
                                amountExcludingTax
                                priceReason {
                                    _id
                                    name
                                    id
                                    description
                                    isActive
                                    priority
                                }
                                priceOrigin
                                isPriceDeterminated
                                priceReasonDeterminated {
                                    description
                                    isActive
                                    priority
                                    name
                                    _id
                                }
                                priceOriginDeterminated
                                chargeDeterminated
                                discountDeterminated
                                grossPriceDeterminated
                                netPrice
                                charge
                                discount
                                grossPrice
                                remainingQuantityToInvoiceInSalesUnit
                                invoicedQuantityInSalesUnit
                                quantityToInvoiceInProgressInSalesUnit
                                remainingQuantityToShipInSalesUnit
                                shippedQuantityInSalesUnit
                                quantityToShipInProgressInSalesUnit
                                quantityInStockUnit
                                unitToStockUnitConversionFactor
                                stockShortageInStockUnit
                                availableQuantityInStockUnit
                                stockOnHand
                                stockUnit {
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    id
                                }
                                quantity
                                salesUnit {
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    id
                                }
                                salesSite {
                                    description
                                    legalCompany {
                                        legislation {
                                            id
                                            name
                                            _id
                                        }
                                        id
                                        name
                                        _id
                                    }
                                    id
                                    name
                                    _id
                                }
                                itemSite {
                                    inStockQuantity
                                    preferredProcess
                                    isOrderToOrder
                                    prodLeadTime
                                    _id
                                }
                                originDocumentType
                                isExternalNote
                                internalNote {
                                    value
                                }
                                externalNote {
                                    value
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
    }
}
