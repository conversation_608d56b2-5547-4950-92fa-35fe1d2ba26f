query Lookups($orderDate: Date, $salesSite_id: IntReference, $salesSite_filter: String) {
    xtremSales {
        salesOrder {
            lookups(
                data: {
                    salesSite: $salesSite_id
                    soldToCustomer: null
                    number: null
                    orderDate: $orderDate
                    taxCalculationStatus: "notDone"
                    fxRateDate: $orderDate
                    companyFxRate: 0
                    companyFxRateDivisor: 0
                    isQuote: true
                    displayStatus: "quote"
                    status: "quote"
                    expectedDeliveryDate: $orderDate
                    isPrinted: false
                    isSent: false
                    currency: null
                    customerNumber: null
                    soldToLinkedAddress: null
                    soldToAddress: null
                    shipToCustomerAddress: null
                    stockSite: null
                    shipToCustomer: null
                    shipToAddress: null
                    incoterm: null
                    deliveryMode: null
                    requestedDeliveryDate: $orderDate
                    deliveryLeadTime: 0
                    shippingDate: $orderDate
                    doNotShipBeforeDate: null
                    doNotShipAfterDate: null
                    shippingStatus: "notShipped"
                    invoiceStatus: "notInvoiced"
                    billToCustomer: null
                    billToLinkedAddress: null
                    paymentTerm: null
                    billToAddress: null
                    totalTaxAmount: 0
                    totalTaxAmountAdjusted: 0
                    internalNote: null
                    isExternalNote: false
                    externalNote: null
                    isTransferHeaderNote: false
                    isTransferLineNote: false
                    entityUse: null
                }
            ) {
                salesSite(
                    filter: $salesSite_filter
                    orderBy: "{\"name\":1,\"_id\":1}"
                ) {
                    edges {
                        node {
                            _id
                            isSales
                            id
                            name
                            businessEntity {
                                _id
                                currency {
                                    decimalDigits
                                    symbol
                                    id
                                    name
                                    _id
                                }
                            }
                            isPurchase
                            isInventory
                            description
                            legalCompany {
                                _id
                                name
                                id
                                customerOnHoldCheck
                                taxEngine
                                currency {
                                    decimalDigits
                                    symbol
                                    id
                                    name
                                    _id
                                }
                                legislation {
                                    id
                                    name
                                    _id
                                }
                            }
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
