query Lookup($orderDate: Date, $salesSite_id: IntReference) {
    xtremSales {
        salesOrder {
            lookups(
                data: {
                    salesSite: $salesSite_id
                    soldToCustomer: null
                    number: null
                    orderDate: $orderDate
                    taxCalculationStatus: "notDone"
                    fxRateDate: $orderDate
                    companyFxRate: 0
                    companyFxRateDivisor: 0
                    isQuote: true
                    displayStatus: "quote"
                    status: "quote"
                    expectedDeliveryDate: $orderDate
                    isPrinted: false
                    isSent: false
                    currency: null
                    customerNumber: null
                    soldToLinkedAddress: null
                    soldToAddress: null
                    shipToCustomerAddress: null
                    stockSite: null
                    shipToCustomer: null
                    shipToAddress: null
                    incoterm: null
                    deliveryMode: null
                    requestedDeliveryDate: $orderDate
                    deliveryLeadTime: 0
                    shippingDate: $orderDate
                    doNotShipBeforeDate: null
                    doNotShipAfterDate: null
                    shippingStatus: "notShipped"
                    invoiceStatus: "notInvoiced"
                    billToCustomer: null
                    billToLinkedAddress: null
                    paymentTerm: null
                    billToAddress: null
                    totalTaxAmount: 0
                    totalTaxAmountAdjusted: 0
                    internalNote: null
                    isExternalNote: false
                    externalNote: null
                    isTransferHeaderNote: false
                    isTransferLineNote: false
                    entityUse: null
                }
            ) {
                soldToCustomer(orderBy: "{\"businessEntity\":{\"name\":1},\"_id\":1}", first: 10) {
                    edges {
                        node {
                            _id
                            businessEntity {
                                name
                                id
                                _id
                                country {
                                    id
                                    name
                                    _id
                                }
                                taxIdNumber
                            }
                            primaryAddress {
                                locationPhoneNumber
                                country {
                                    id
                                    name
                                    _id
                                }
                                postcode
                                region
                                city
                                addressLine2
                                addressLine1
                                name
                                isActive
                                _id
                            }
                            creditLimit
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
