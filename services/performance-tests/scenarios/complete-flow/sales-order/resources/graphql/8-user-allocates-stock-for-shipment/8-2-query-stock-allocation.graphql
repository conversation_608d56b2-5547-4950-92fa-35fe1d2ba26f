query Query($filter: String) {
    xtremStockData {
        stockAllocation {
            query(first: 500, filter: $filter) {
                edges {
                    node {
                        _id
                        stockRecord {
                            _id
                        }
                        quantityInStockUnit
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
