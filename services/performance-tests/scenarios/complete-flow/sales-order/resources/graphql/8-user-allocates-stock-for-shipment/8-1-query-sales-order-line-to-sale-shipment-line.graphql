query Query($filter: String) {
    xtremSales {
        salesOrderLineToSalesShipmentLine {
            query(filter: $filter) {
                edges {
                    node {
                        _id
                        linkedDocument {
                            # this is the order line
                            _id
                            document {
                                _id
                                number
                            }
                            status
                        }
                        document {
                            # this is the shipment line
                            _id
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
