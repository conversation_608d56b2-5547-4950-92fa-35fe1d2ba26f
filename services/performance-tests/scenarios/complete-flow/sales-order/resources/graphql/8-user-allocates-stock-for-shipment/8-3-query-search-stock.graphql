query SearchStock(
    $active_quantity_in_stock_unit: Decimal = 0
    $item: IntReference
    $site: IntReference
    $stock_unit: IntReference
) {
    xtremStockData {
        stock {
            searchStock(
                searchCriteria: {
                    activeQuantityInStockUnit: $active_quantity_in_stock_unit
                    item: $item
                    site: $site
                    stockUnit: $stock_unit
                    statusList: [{ statusType: "accepted" }]
                    knownAllocations: []
                }
            ) {
                quantityInStockUnit
                stockRecord {
                    _id
                    stockUnit {
                        _id
                        id
                        name
                        decimalDigits
                        symbol
                    }
                    status {
                        _id
                        id
                        name
                    }
                    lot {
                        id
                        expirationDate
                    }
                    location {
                        _id
                        id
                    }
                    owner
                }
            }
        }
    }
}
