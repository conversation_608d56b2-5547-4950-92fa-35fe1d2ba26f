query Query($filter: String) {
    xtremStockData {
        stockAllocation {
            query(filter: $filter) {
                edges {
                    node {
                        _id
                        quantityInStockUnit
                        stockRecord {
                            _id
                            stockUnit {
                                id
                                symbol
                                name
                                decimalDigits
                            }
                            location {
                                id
                            }
                            lot {
                                id
                                expirationDate
                            }
                            status {
                                name
                            }
                        }
                        documentLine {
                            _id
                        }
                        serialNumbers {
                            query(first: 500) {
                                edges {
                                    node {
                                        _id
                                        id
                                        allocation {
                                            _id
                                        }
                                    }
                                }
                            }
                        }
                        orderDocumentLine {
                            documentNumber
                        }
                        quantityTransferred
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
