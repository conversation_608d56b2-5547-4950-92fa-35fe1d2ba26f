mutation UpdateAllocation($documentLine: IntReference, $quantityInStockUnit: Decimal, $stockRecord_id: IntReference) {
    xtremStockData {
        stock {
            updateAllocations(
                allocationData: {
                    documentLine: $documentLine
                    allocationUpdates: [
                        {
                            action: "create"
                            stockRecord: $stockRecord_id
                            quantity: $quantityInStockUnit
                            serialNumbers: []
                        }
                    ]
                }
                stockAllocationParameters: { cannotOverAllocate: true, shouldControlAllocationInProgress: false }
            ) {
                resultAction
                allocationRecord {
                    _id
                }
            }
        }
    }
}
