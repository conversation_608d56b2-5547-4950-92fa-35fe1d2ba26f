query Query($filter: String, $number_of_lines: Int = 10) {
    xtremSales {
        salesShipment {
            query(filter: $filter) {
                edges {
                    node {
                        lines {
                            query(first: $number_of_lines, orderBy: "{\"_sortValue\":1}") {
                                edges {
                                    node {
                                        itemDescription
                                        status
                                        item {
                                            name
                                            image {
                                                value
                                            }
                                            id
                                            _id
                                            lotManagement
                                            stockUnit {
                                                decimalDigits
                                                symbol
                                                id
                                                name
                                                _id
                                            }
                                            isStockManaged
                                            type
                                            isExpiryManaged
                                            description
                                        }
                                        _sortValue
                                        quantityReceiptInSalesUnit
                                        quantityRequestedInSalesUnit
                                        quantityInvoicedPostedInSalesUnit
                                        quantityInvoicedInProgressInSalesUnit
                                        returnReceiptStatus
                                        returnRequestStatus
                                        invoiceStatus
                                        priceReason {
                                            description
                                            isActive
                                            priority
                                            name
                                            _id
                                        }
                                        priceOrigin
                                        charge
                                        discount
                                        storedDimensions
                                        storedAttributes
                                        allocationStatus
                                        remainingQuantity
                                        stockUnit {
                                            _id
                                            name
                                            symbol
                                            decimalDigits
                                            id
                                        }
                                        remainingQuantityToAllocate
                                        quantityAllocated
                                        quantityInStockUnit
                                        unitToStockUnitConversionFactor
                                        quantity
                                        salesUnit {
                                            _id
                                            name
                                            symbol
                                            decimalDigits
                                            id
                                        }
                                        itemImage {
                                            value
                                        }
                                        stockTransactionStatus
                                        isExternalNote
                                        internalNote {
                                            value
                                        }
                                        externalNote {
                                            value
                                        }
                                        document {
                                            _id
                                        }
                                        originDocumentType
                                        _id
                                    }
                                    cursor
                                }
                                pageInfo {
                                    startCursor
                                    endCursor
                                    hasPreviousPage
                                    hasNextPage
                                }
                            }
                        }
                        _id
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
