query Query($filter: String) {
    xtremSales {
        salesShipmentLine {
            query(filter: $filter) {
                edges {
                    node {
                        quantityInStockUnit
                        quantityAllocated
                        stockTransactionStatus
                        allocationStatus
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
