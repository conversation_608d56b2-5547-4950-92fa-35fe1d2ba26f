query Read($item: Id) {
    xtremMasterData {
        item {
            read(_id: $item) {
                _id
                id
                name
                lotManagement
                isExpiryManaged
                lotSequenceNumber {
                    _id
                    id
                }
                stockUnit {
                    _id
                    id
                    name
                    decimalDigits
                    symbol
                }
                useSupplierSerialNumbers
                serialNumberUsage
                serialNumberManagement
                serialNumberSequenceNumber {
                    _id
                    id
                    components {
                        query {
                            edges {
                                node {
                                    sequenceNumber {
                                        id
                                    }
                                    type
                                    length
                                    constant
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
