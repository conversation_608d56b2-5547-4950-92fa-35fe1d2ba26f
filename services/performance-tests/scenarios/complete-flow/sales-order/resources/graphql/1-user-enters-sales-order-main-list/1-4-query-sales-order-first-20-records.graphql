query {
    xtremSales {
        salesOrder {
            query(
                orderBy: "{\"orderDate\":-1,\"number\":-1,\"_id\":1}"
                after: "[\"2024-06-24\",\"SO240537G10000537\",35722]#62"
                first: 20
            ) {
                edges {
                    node {
                        _id
                        soldToContact {
                            locationPhoneNumber
                            position
                            role
                            preferredName
                            email
                            title
                            firstName
                            lastName
                            _id
                        }
                        allocationStatus
                        allocationRequestStatus
                        isCloseHidden
                        isOrderAssignmentLinked
                        taxEngine
                        taxCalculationStatus
                        status
                        soldToLinkedAddress {
                            _id
                        }
                        isOnHold
                        shippingStatus
                        paymentTerm {
                            _id
                            name
                            id
                            dueDateType
                            description
                        }
                        billToCustomer {
                            _id
                            businessEntity {
                                name
                                id
                                _id
                                taxIdNumber
                            }
                        }
                        doNotShipAfterDate
                        doNotShipBeforeDate
                        shippingDate
                        deliveryLeadTime
                        requestedDeliveryDate
                        deliveryMode {
                            _id
                            name
                            id
                        }
                        entityUse {
                            _id
                            name
                            isActive
                            description
                            id
                        }
                        incoterm {
                            _id
                            name
                            id
                            description
                        }
                        stockSite {
                            _id
                            name
                            id
                            businessEntity {
                                _id
                            }
                            description
                        }
                        shipToCustomer {
                            _id
                            businessEntity {
                                name
                                id
                                _id
                                taxIdNumber
                            }
                        }
                        isSent
                        isPrinted
                        totalTaxAmount
                        totalGrossProfit
                        totalAmountExcludingTax
                        transactionCurrency {
                            _id
                            name
                            id
                            isActive
                            symbol
                            rounding
                            decimalDigits
                        }
                        customerNumber
                        displayStatus
                        expectedDeliveryDate
                        totalAmountIncludingTax
                        salesSite {
                            _id
                            name
                            id
                            businessEntity {
                                _id
                            }
                            legalCompany {
                                _id
                                name
                                id
                                customerOnHoldCheck
                            }
                        }
                        orderDate
                        soldToCustomer {
                            _id
                            businessEntity {
                                name
                                id
                                _id
                                taxIdNumber
                            }
                        }
                        number
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
