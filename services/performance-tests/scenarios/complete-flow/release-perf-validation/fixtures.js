module.exports = {
    afterResponseBOMFilter_2_7: afterResponseBOMFilter_2_7,
    afterResponseItemSites_1_31: afterResponseItemSites_1_31,
    setBOMVariables: setBOMVariables,
    setFlowVariables: setFlowVariables,
};

function afterResponseBOMFilter_2_7(requestParams, response, context, ee, next) {
    try {
        const responseBody = JSON.parse(response.body);
        const nodesList = responseBody?.data?.xtremTechnicalData?.billOfMaterial?.query?.edges || [];
        for (const node of nodesList) {
            if (
                node.node.name === context.vars.defaultBOMSearchName &&
                node.node.item.id === context.vars.defaultBOMSearchItemId
            ) {
                context.vars.selectedBOMId = node.node._id;
            }
        }
    } catch (error) {
        console.log(`Error while processing afterResponseBOMFilter_2_7 ${error}`);
    }
    return next();
}

function afterResponseItemSites_1_31(requestParams, response, context, ee, next) {
    context.vars.selected_item_site = `${context.vars.selectedItemSite_id}`;
    return next();
}

function generateId(prefix) {
    const min = Math.ceil(100000);
    const max = Math.floor(999999);
    return `${prefix}_${Math.floor(Math.random() * (max - min + 1)) + min}`;
}

function setConstantVariable(context) {
    context.vars.defaultItemSiteId = 'H5E1PWCU7YQJU663';
    context.vars.defaultItemSiteName = 'UN Ixkhujuc Qwes Dcxn';
}

function setBOMVariables(context, ee, next) {
    context.vars.defaultBOMSearchName = 'CartCaddy Vehicle Mover';
    context.vars.defaultBOMSearchItemId = '600100-02HPVM';
    context.vars.defaultBOMId = '27216';
    context.vars.defaultBOMStatus = 'availableToUse';
    next();
}

function setFlowVariables(context, ee, next) {
    setConstantVariable(context);
    const randomProductId = context.funcs.$randProductName();
    const itemName = generateId(`${randomProductId}`);
    context.vars.item_name = itemName;
    context.vars.item_id = itemName.toLocaleLowerCase().replace(/ /g, '');
    console.log('Generated Item Name -: ' + context.vars.item_name + ' --');
    console.log('Generated Item Id ---: ' + context.vars.item_id + ' --');
    return next();
}
