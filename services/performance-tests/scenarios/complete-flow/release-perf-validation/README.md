# Load demo data (locally) and run xtrem server and

## For testing purposes locally, load test data:

The needed test data is located in the 'Perf test small' tenant, therefore, it needs to be imported by following the steps defined in /platform/performance-test/README.md file

## Make sure that xtrem project is built and xtrem is started with unsecure dev login:

```bash
pnpm run start:unsecuredevlogin
```

# run test script

## run scenario test from xtrem/services/performance-tests

```bash
  ../../scripts/artillery/scenarios-test-run.sh  --scenario complete-flow/release-perf-validation --total-users 5 --ramp-up 15 --duration 10
```
