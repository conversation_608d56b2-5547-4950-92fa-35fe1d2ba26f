scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/*'
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/create-item/*'
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/duplicate-bill-of-material/*'
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/work-order/*'
    # beforeScenario: 'beforeScenario'
    flow:
      - log: ---- Creating Item ----
      - log: For User ---> {{ email }}
      - function: 'setFlowVariables'
      - log: 1_1 - (Step 1 - Create-Item - 1) Get Metadata Pages
      - post:
          url: /metadata
          json:
            query: step_1_1_meta_get_pages.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_1 - (Step 1 - Create-Item - 1) Get Metadata Pages
      - log: 1_2 - (Step 1 - Create-Item - 2) Get Installed Packages
      - post:
          url: /metadata
          json:
            query: step_1_2_meta_get_installed_packages.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_2 - (Step 1 - Create-Item - 2) Get Installed Packages
      - log: 1_3 - (Step 1 - Create-Item - 3) Get Stickers
      - post:
          url: /metadata
          json:
            query: step_1_3_meta_get_stickers.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_3 - (Step 1 - Create-Item - 2) Get Get Stickers
      - log: 1_4 - (Step 1 - Create-Item - 4) Get Item Pages Fields
      - post:
          url: /metadata
          json:
            query: step_1_4_meta_get_edges.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_4 - (Step 1 - Create-Item - 4) Get Item Pages Fields
      - log: 1_5 - (Step 1 - Create-Item - 5) Get Enum Values
      - post:
          url: /api
          json:
            query: step_1_5_get_enum_values.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_5 - (Step 1 - Create-Item - 5) Get Enum Values
      - log: 1_6 - (Step 1 - Create-Item - 6) Get Defaults for Item Page
      - post:
          url: /api
          json:
            query: step_1_6_get_defaults_item.graphql
          capture:
            - json: 'data.rootNode.item.getDefaults.status'
              as: default_item_status
          expect:
            - statusCode: 200
            - hasProperty: data.rootNode.item.getDefaults.status
            - log:
            - equals:
                - '{{ default_item_status }}'
                - 'active'
          name: 1_6 - (Step 1 - Create-Item - 6) Get Defaults for Item Page
      - log: 1_8 - (Step 1 - Create-Item - 8) Get finance data attribute
      - post:
          url: /api
          json:
            query: step_1_8_get_finance_attributes.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_8 - (Step 1 - Create-Item - 8) Get finance data attribute
      - log: 1_9 - (Step 1 - Create-Item - 9) Get finance data attribute type
      - post:
          url: /api
          json:
            query: step_1_9_get_finance_attribute_type.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_9 - (Step 1 - Create-Item - 9) Get finance data attribute type
      - log: 1_10 - (Step 1 - Create-Item - 10) Get finance data diemention type
      - post:
          url: /api
          json:
            query: step_1_10_get_finance_dimension_type.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_10 - (Step 1 - Create-Item - 10) Get finance data dimention type
      - log: 1_11 - (Step 1 - Create-Item - 11) Get Intacct Instance
      - post:
          url: /api
          json:
            query: step_1_11_get_intacct_instance.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_11 - (Step 1 - Create-Item - 11) Get Intacct Instance
      - log: 1_12 - (Step 1 - Create-Item - 12) Get Item Field List
      - post:
          url: /api
          json:
            query: step_1_12_get_item_list.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_12 - (Step 1 - Create-Item - 12) Get Item Field List
      - log: 1_13 - (Step 1 - Create-Item - 13) Get Default values xtrem master data item
      - post:
          url: /api
          json:
            query: step_1_13_get_defaults.graphql
          capture:
            - json: 'data.xtremMasterData.item.getDefaults.status'
              as: default_item_status
          expect:
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.getDefaults.status
            - log:
            - equals:
                - '{{ default_item_status }}'
                - 'active'
          name: 1_13 - (Step 1 - Create-Item - 13) Get Default values xtrem master data item
      - log: 1_15 - (Step 1 - Create-Item - 15) Get Finance Data attribute list
      - post:
          url: /api
          json:
            query: step_1_15_get_finance_data_attribute.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_15 - (Step 1 - Create-Item - 15) Get Finance Data attribute list
      - log: 1_16 - (Step 1 - Create-Item - 16) Get Finance Data attribute type list
      - post:
          url: /api
          json:
            query: step_1_16_get_finance_data_attribute_type.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_16 - (Step 1 - Create-Item - 16) Get Finance Data attribute type list
      - log: 1_17 - (Step 1 - Create-Item - 17) Get Finance Data dimension type list
      - post:
          url: /api
          json:
            query: step_1_17_get_finance_data_dimension_type.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_17 - (Step 1 - Create-Item - 17) Get Finance Data dimension type list
      - log: 1_18 - (Step 1 - Create-Item - 18) Get Intacct Instance
      - post:
          url: /api
          json:
            query: step_1_18_get_intacct_instance.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_18 - (Step 1 - Create-Item - 18) Get Intacct Instance
      - log: 1_19 - (Step 1 - Create-Item - 19) Get stock unit lookup items
      - post:
          url: /api
          json:
            query: step_1_19_lookup_stock_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node.name
          name: 1_19 - (Step 1 - Create-Item - 19) Get stock unit lookup items
      - log: 1_20 - (Step 1 - Create-Item - 20) Get purchase unit lookup items
      - post:
          url: /api
          json:
            query: step_1_20_lookup_purchase_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node.name
          name: 1_20 - (Step 1 - Create-Item - 20) Get purchase unit lookup items
      - log: 1_21 - (Step 1 - Create-Item - 21) Get weight unit lookup items
      - post:
          url: /api
          json:
            query: step_1_21_lookup_weight_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.weightUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.weightUnit.edges[0].node.name
          name: 1_21 - (Step 1 - Create-Item - 21) Get weight unit lookup items
      - log: 1_22 - (Step 1 - Create-Item - 22) Get volume unit lookup items
      - post:
          url: /api
          json:
            query: step_1_22_lookup_volume_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.volumeUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.volumeUnit.edges[0].node.name
          name: 1_22 - (Step 1 - Create-Item - 22) Get volume unit lookup items
      - log: 1_23_1 - (Step 1 - Create-Item - 23_1) Get stock unit lookup items
      - post:
          url: /api
          json:
            query: step_1_23_1_lookup_stock_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node.name
          name: 1_23_1 - (Step 1 - Create-Item - 23_1) Get stock unit lookup items
      - log: 1_23_2 - (Step 1 - Create-Item - 23_2) Get stock unit lookup items
      - post:
          url: /api
          json:
            query: step_1_23_2_lookup_stock_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node.name
          name: 1_23_2 - (Step 1 - Create-Item - 23_2) Get stock unit lookup items
      - log: 1_23_3 - (Step 1 - Create-Item - 23_3) Get stock unit lookup items
      - post:
          url: /api
          json:
            query: step_1_23_3_lookup_stock_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.stockUnit.edges[0].node.name
          name: 1_23_3 - (Step 1 - Create-Item - 23_3) Get stock unit lookup items
      - log: 1_24 - (Step 1 - Create-Item - 24) Get unit conversion factor
      - post:
          url: /api
          json:
            query: step_1_24_get_unit_conversion_factor.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_24 - (Step 1 - Create-Item - 24) Get unit conversion factor
      - log: 1_25_1 - (Step 1 - Create-Item - 25_1) Get purchase unit lookup items
      - post:
          url: /api
          json:
            query: step_1_25_1_lookup_purchase_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node.name
          name: 1_25_1 - (Step 1 - Create-Item - 25_1) Get purchase unit lookup items
      - log: 1_25_2 - (Step 1 - Create-Item - 25_2) Get purchase unit lookup items
      - post:
          url: /api
          json:
            query: step_1_25_2_lookup_purchase_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node.name
          name: 1_25_2 - (Step 1 - Create-Item - 25_2) Get purchase unit lookup items
      - log: 1_25_3 - (Step 1 - Create-Item - 25_3) Get purchase unit lookup items
      - post:
          url: /api
          json:
            query: step_1_25_3_lookup_purchase_unit_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.purchaseUnit.edges[0].node.name
          name: 1_25_3 - (Step 1 - Create-Item - 25_3) Get purchase unit lookup items
      - log: 1_26 - (Step 1 - Create-Item - 26) Get unit conversion factor
      - post:
          url: /api
          json:
            query: step_1_26_get_unit_conversion_factor.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_26 - (Step 1 - Create-Item - 26) Get unit conversion factor
      - log: 1_27 - (Step 1 - Create-Item - 27) Get metadata item site details
      - post:
          url: /metadata
          json:
            query: step_1_27_meta_itemsite_details.graphql
          capture:
            - json: 'data.pages[0].customFields[0].name'
              as: item_site_first_custom_field
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.pages[0].customFields[0].name
            - equals:
                - '{{item_site_first_custom_field}}'
                - '@sage/xtrem-master-data/ItemSite'

          name: 1_27 - (Step 1 - Create-Item - 27) Get metadata item site details
      - log: 1_28 - (Step 1 - Create-Item - 28) Get item site navigation items list
      - post:
          url: /api
          json:
            query: step_1_28_get_itemsite_nav_items.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.navigationPanelItems.itemSite.query.edges[0].node._id
          name: 1_28 - (Step 1 - Create-Item - 28) Get item site navigation items list
      - log: 1_30 - (Step 1 - Create-Item - 30) Item sites lookup list
      - post:
          url: /api
          json:
            query: step_1_30_lookup_item_site_list.graphql
          capture:
            - json: 'data.xtremMasterData.itemSite.lookups.site.edges[0].node._id'
              as: selectedItemSite_id
            - json: 'data.xtremMasterData.itemSite.lookups.site.edges[0].node.id'
              as: selectedItemSiteId
            - json: 'data.xtremMasterData.itemSite.lookups.site.edges[0].node.name'
              as: selectedItemSiteName
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.itemSite.lookups.site.edges[0].node._id
            - hasProperty: data.xtremMasterData.itemSite.lookups.site.edges[0].node.name
          name: 1_30 - (Step 1 - Create-Item - 30) Item sites lookup list
      - log: 1_31 - (Step 1 - Create-Item - 31) Item sites lookup list
      - post:
          url: /api
          json:
            query: step_1_31_lookup_item_site_selected.graphql
            variables:
              siteId: '_id:{{ selectedItemSite_id }}'
              siteFilter: '{"_and":[{"isInventory":true},{"_or":[{"id":{"_regex":"{{ selectedItemSiteName }}","_options":"i"}},{"name":{"_regex":"{{ selectedItemSiteName }}","_options":"i"}}]}]}'
          capture:
            - json: 'data.xtremMasterData.itemSite.lookups.site.edges[0].node._id'
              as: currentItemSite_id
            - json: 'data.xtremMasterData.itemSite.lookups.site.edges[0].node.id'
              as: currentItemSiteId
            - json: 'data.xtremMasterData.itemSite.lookups.site.edges[0].node.name'
              as: currentItemSiteName
          afterResponse: 'afterResponseItemSites_1_31'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.itemSite.lookups.site.edges[0].node._id
            - hasProperty: data.xtremMasterData.itemSite.lookups.site.edges[0].node.id
            - hasProperty: data.xtremMasterData.itemSite.lookups.site.edges[0].node.name
            - equals:
                - '{{currentItemSiteId}}'
                - '{{defaultItemSiteId}}'
          name: 1_31 - (Step 1 - Create-Item - 31) Item sites lookup list
      - log: 1_32 - (Step 1 - Create-Item - 32) Suppliers Default Value
      - post:
          url: /api
          json:
            query: step_1_32_get_defaults_suppliers.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_32 - (Step 1 - Create-Item - 32) Suppliers Default Value
      - log: 1_33 - (Step 1 - Create-Item - 33) Suppliers Prices Default Value
      - post:
          url: /api
          json:
            query: step_1_33_get_defaults_supplier_prices.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.nestedDefaults.item.getDefaults.supplierPrices.query.edges[0].node._id
          name: 1_33 - (Step 1 - Create-Item - 33) Suppliers Prices Default Value
      - log: 1_34 - (Step 1 - Create-Item - 34) Sales Prices Default Value
      - post:
          url: /api
          json:
            query: step_1_34_get_defaults_sales_prices.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.nestedDefaults.item.getDefaults.salesPrices.query.edges[0].node._id
            - hasProperty: data.nestedDefaults.item.getDefaults.salesPrices.query.edges[0].node.item.id
          name: 1_34 - (Step 1 - Create-Item - 34) Sales Prices Default Value
      - log: 1_35 - (Step 1 - Create-Item - 35) get Cutomers Default Value
      - post:
          url: /api
          json:
            query: step_1_35_get_defaults_customers.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.nestedDefaults.item.getDefaults.customers.query.edges[0].node._id
            - hasProperty: data.nestedDefaults.item.getDefaults.customers.query.edges[0].node.isActive
          name: 1_35 - (Step 1 - Create-Item - 35) get Cutomers Default Value
      - log: 1_36 - (Step 1 - Create-Item - 36) Lookup item tax group list - first 10
      - post:
          url: /api
          json:
            query: step_1_36_lookup_item_tax_group_lits.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
              itemSite: '{{ selected_item_site }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.itemTaxGroup.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.itemTaxGroup.edges[0].node.name
          name: 1_36 - (Step 1 - Create-Item - 36) Lookup item tax group list - first 10
      - log: 1_37 - (Step 1 - Create-Item - 37) get dimenstion list
      - post:
          url: /api
          json:
            query: step_1_37_get_dimension_list.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremFinanceData.dimension.query.edges[0].node._id
            - hasProperty: data.xtremFinanceData.dimension.query.edges[0].node.name
          name: 1_37 - (Step 1 - Create-Item - 37) get dimenstion list
      - log: 1_38 - (Step 1 - Create-Item - 38) lookup posting class list
      - post:
          url: /api
          json:
            query: step_1_38_lookup_posting_class_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
              itemSite: '{{ selected_item_site }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.postingClass.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.postingClass.edges[0].node.name
          name: 1_38 - (Step 1 - Create-Item - 38) lookup posting class list
      - log: 1_39 - (Step 1 - Create-Item - 39) lookup item tax group
      - post:
          url: /api
          json:
            query: step_1_39_lookup_item_tax_group_list.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
              itemSite: '{{ selected_item_site }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.lookups.itemTaxGroup.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.lookups.itemTaxGroup.edges[0].node.name
          name: 1_39 - (Step 1 - Create-Item - 39) lookup item tax group
      - log: 1_40 - (Step 1 - Create-Item - 40) Mutatuion Query - Item Creation
      - post:
          url: /api
          json:
            query: step_1_40_mutation_create_item.graphql
            variables:
              itemName: '{{ item_name }}'
              itemId: '{{ item_id}}'
              itemSite: '{{ selected_item_site }}'
          capture:
            - json: 'data.xtremMasterData.item.create._id'
              as: createdItem_id
            - json: 'data.xtremMasterData.item.create.id'
              as: createdItemId
            - json: 'data.xtremMasterData.item.create.name'
              as: createdItemName
            - json: 'data.xtremMasterData.item.create.itemSites.query.edges[0].node.site.id'
              as: createdItemSiteId
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.create._id
            - hasProperty: data.xtremMasterData.item.create.id
            - hasProperty: data.xtremMasterData.item.create.itemSites.query.edges[0].node.site.id
            - equals:
                - '{{defaultItemSiteId}}'
                - '{{createdItemSiteId}}'
          name: 1_40 - (Step 1 - Create-Item - 40) Mutatuion Query - Item Creation
      - log: 1_41 - (Step 1 - Create-Item - 41)  get item pages details
      - post:
          url: /api
          json:
            query: step_1_41_get_item_page_elements.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremMasterData.item.query.edges[0].node._id
            - hasProperty: data.xtremMasterData.item.query.edges[0].node.name
          name: 1_41 - (Step 1 - Create-Item - 41) get item pages details
      - log: 1_43 - (Step 1 - Create-Item - 43) Get unit conversion
      - post:
          url: /api
          json:
            query: step_1_43_get_unit_conversion_factor_purchase.graphql
            variables:
              itemId: '{{ createdItem_id }}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_43 - (Step 1 - Create-Item - 43) Get unit conversion
      - log: 1_44 - (Step 1 - Create-Item - 44) Get Stock elements list
      - post:
          url: /api
          json:
            query: step_1_44_get_stock_elements.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_44 - (Step 1 - Create-Item - 44) Get Stock elements list
      - log: 1_45 - (Step 1 - Create-Item - 45) Get finance attributes list
      - post:
          url: /api
          json:
            query: step_1_45_get_finance_attribute.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_45 - (Step 1 - Create-Item - 45) Get finance attributes list
      - log: 1_46 - (Step 1 - Create-Item - 46)  Get finance attribute type list
      - post:
          url: /api
          json:
            query: step_1_46_get_finance_attribute_type.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremFinanceData.attributeType.query.edges[0].node.id
            - hasProperty: data.xtremFinanceData.attributeType.query.edges[0].node.name
          name: 1_46 - (Step 1 - Create-Item - 46) Get finance attribute type list
      - log: 1_47 - (Step 1 - Create-Item - 47)  get dimension type list
      - post:
          url: /api
          json:
            query: step_1_47_get_dimension_type_elments.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremFinanceData.dimensionType.query.edges[0].node.name
            - hasProperty: data.xtremFinanceData.dimensionType.query.edges[0].node.isActive
          name: 1_47 - (Step 1 - Create-Item - 47) get dimension type list
      - log: 1_48 - (Step 1 - Create-Item - 48) Get intacct instance
      - post:
          url: /api
          json:
            query: step_1_48_get_intacct_instace.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 1_48 - (Step 1 - Create-Item - 48) Get intacct instance

      # -
      # - Test Duplicate Bill of Material
      # -

      - function: 'setBOMVariables'
      - log: 2_1 - (Step 2 - Dup. Bill of Material - 1) Get Metadata Pages
      - post:
          url: /metadata
          json:
            query: step_2_1_metadata_get_pages.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 2_1 - (Step 2 - Dup. Bill of Material - 1) Get Metadata Pages
      - log: 2_2 - (Step 2 - Dup. Bill of Material - 2) Get Installed Packages
      - post:
          url: /metadata
          json:
            query: step_2_2_metadata_get_installed_packages.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 2_2 - (Step 2 - Dup. Bill of Material - 2) Get Installed Packages
      - log: 2_3 - (Step 2 - Dup. Bill of Material - 3) Get Stickers
      - post:
          url: /metadata
          json:
            query: step_2_3_metadata_get_stickers.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 2_3 - (Step 2 - Dup. Bill of Material - 2) Get Get Stickers
      - log: 2_4 - (Step 2 - Dup. Bill of Material - 4) Get Item Pages Fields
      - post:
          url: /metadata
          json:
            query: step_2_4_metadata_get_page_details.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: 2_4 - (Step 2 - Dup. Bill of Material - 4) Get Item Pages Fields
      - log: 2_5 - (Step 2 - Dup. Bill of Material - 5) Get default bill of materail data
      - post:
          url: /api
          json:
            query: step_2_5_get_default_values.graphql
          expect:
            - statusCode: 200
            - hasProperty: data.rootNode.billOfMaterial.getDefaults.status
          name: 2_5 - (Step 2 - Dup. Bill of Material - 5) Get default bill of materail data
      - log: 2_6 - (Step 2 - Dup. Bill of Material - 6) Get bill of material list - first 10
      - post:
          url: /api
          json:
            query: step_2_6_get_bom_list.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremTechnicalData.billOfMaterial.query.edges[0].node._id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.query.edges[0].node.name
          name: 2_6 - (Step 2 - Dup. Bill of Material - 6) Get bill of material list - first 10
      - log: 2_7 - (Step 2 - Dup. Bill of Material - 7) Get bill of material list - filter by name
      - post:
          url: /api
          json:
            query: step_2_7_get_bom_list_filter_by_name.graphql
            variables:
              billOfMaterialFilter: '{"name":{"_regex":"{{ defaultBOMSearchName }}","_options":"i"}}'
          afterResponse: 'afterResponseBOMFilter_2_7'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremTechnicalData.billOfMaterial.query.edges[0].node._id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.query.edges[0].node.name
          name: 2_7 - (Step 2 - Dup. Bill of Material - 7) Get bill of material list - filter by name
      - log: 2_7 - Selected BOM is -- {{selectedBOMId}}
      - log: 2_8 - (Step 2 - Dup. Bill of Material - 8) read selected bill of material
      - post:
          url: /api
          json:
            query: step_2_8_read_bom_data.graphql
            variables:
              billOfMaterialId: '{{ selectedBOMId }}'
          capture:
            - json: 'data.xtremTechnicalData.billOfMaterial.read.name'
              as: selectedBomName
            - json: 'data.xtremTechnicalData.billOfMaterial.read.status'
              as: selectedBomStatus
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read._id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read.name
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read.item.id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read.status
            - equals:
                - '{{ selectedBOMId }}'
                - '{{ defaultBOMId }}'
            - equals:
                - '{{ selectedBomName }}'
                - '{{ defaultBOMSearchName }}'
            - equals:
                - '{{ selectedBomStatus }}'
                - '{{ defaultBOMStatus }}'
          name: 2_8 - (Step 2 - Dup. Bill of Material - 8) read selected bill of material
      - log: 2_9 - (Step 2 - Dup. Bill of Material - 9) Get metadata page details
      - post:
          url: /metadata
          json:
            query: step_2_9_metadata_get_pages_details.graphql
          capture:
            - json: 'data.pages[0].nodeDetails[0].name'
              as: bom_page_first_node
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.pages[0].nodeDetails[0].name
            - equals:
                - '{{ bom_page_first_node }}'
                - 'BillOfMaterial'
          name: 2_9 - (Step 2 - Dup. Bill of Material - 9) Get metadata page details
      - log: 2_10 - (Step 2 - Dup. Bill of Material - 10) Duplicate bill of material
      - post:
          url: /api
          json:
            query: step_2_10_duplicate_bom.graphql
            variables:
              billOfMaterialId: '{{selectedBOMId}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.rootNode.billOfMaterial.getDuplicate.name
            - hasProperty: data.rootNode.billOfMaterial.getDuplicate._id
            - equals:
                - '{{ bom_page_first_node }}'
                - 'BillOfMaterial'
          name: 2_10 - (Step 2 - Dup. Bill of Material - 10) Duplicate bill of material
      - log: 2_11_1 - (Step 2 - Dup. Bill of Material - 11_1) lookup item list - first 10
      - post:
          url: /api
          json:
            query: step_2_11_1_looup_item_list_first_10.graphql
            variables:
              billOfMaterialName: '{{selectedBomName}}'
              siteId: '_id:{{selectedItemSite_id}}'
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremTechnicalData.billOfMaterial.lookups.item.edges[0].node._id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.lookups.item.edges[0].node.name
          name: 2_11_1 - (Step 2 - Dup. Bill of Material - 11_1) lookup item list - first 10
      - log: 2_11_2 - (Step 2 - Dup. Bill of Material - 11_2) lookup item list - filterby item name
      - post:
          url: /api
          json:
            query: step_2_11_2_looup_item_list_filter_by_name.graphql
            variables:
              billOfMaterialName: '{{selectedBomName}}'
              filteredItemId: '_id:{{createdItem_id}}'
              siteId: '_id:{{selectedItemSite_id}}'
              itemNameFilter: '{"_or":[{"id":{"_regex":"{{ createdItemName }}","_options":"i"}},{"name":{"_regex":"{{ createdItemName }}","_options":"i"}},{"name":{"_regex":"{{ createdItemName }}","_options":"i"}},{"id":{"_regex":"{{ createdItemName }}","_options":"i"}},{"description":{"_regex":"{{ createdItemName }}","_options":"i"}},{"stockUnit":{"id":{"_regex":"{{ createdItemName }}","_options":"i"}}},{"stockUnit":{"symbol":{"_regex":"{{ createdItemName }}","_options":"i"}}},{"stockUnit":{"symbol":{"_regex":"{{ createdItemName }}","_options":"i"}}},{"stockUnit":{"symbol":{"_regex":"{{ createdItemName }}","_options":"i"}}},{"stockUnit":{"description":{"_regex":"{{ createdItemName }}","_options":"i"}}},{"stockUnit":{"symbol":{"_regex":"{{ createdItemName }}","_options":"i"}}},{"stockUnit":{"symbol":{"_regex":"{{ createdItemName }}","_options":"i"}}}]}'
          capture:
            - json: data.xtremTechnicalData.billOfMaterial.lookups.item.edges[0].node._id
              as: filteredBOMItemId
            - json: data.xtremTechnicalData.billOfMaterial.lookups.item.edges[0].node.name
              as: filteredBOMItemName
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremTechnicalData.billOfMaterial.lookups.item.edges[0].node._id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.lookups.item.edges[0].node.name
            - equals:
                - '{{ filteredBOMItemId }}'
                - '{{ createdItemId }}'
            - equals:
                - '{{ filteredBOMItemName }}'
                - '{{ createadItemName}}'
          name: 2_11_2 - (Step 2 - Dup. Bill of Material - 11_2) lookup item list - filterby item name
      - log: 2_12 - (Step 2 - Dup. Bill of Material - 12) duplicate mutation of bill of material
      - post:
          url: /api
          json:
            query: step_2_12_duplicate_bom.graphql
            variables:
              billOfMaterialId: '{{selectedBOMId}}'
              billOfMaterialName: '{{selectedBomName}}'
              filteredItemId: '_id:{{filteredBOMItemId}}'
          capture:
            - json: data.xtremTechnicalData.billOfMaterial.duplicate._id
              as: duplicatedBOMId
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremTechnicalData.billOfMaterial.duplicate._id
          name: 2_12 - (Step 2 - Dup. Bill of Material - 12) duplicate mutation of bill of material
      - log: 2_13 - (Step 2 - Dup. Bill of Material - 13) read duplicated bill of material
      - post:
          url: /api
          json:
            query: step_2_13_read_bom_data.graphql
            variables:
              billOfMaterialId: '{{duplicatedBOMId}}'
          capture:
            - json: 'data.xtremTechnicalData.billOfMaterial.read._id'
              as: currentBomId
            - json: 'data.xtremTechnicalData.billOfMaterial.read.name'
              as: currentBomName
            - json: 'data.xtremTechnicalData.billOfMaterial.read.status'
              as: currentBomStatus
          expect:
            - notHasProperty: errors
            - statusCode: 200
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read._id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read.name
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read.item.id
            - hasProperty: data.xtremTechnicalData.billOfMaterial.read.status
            - equals:
                - '{{ currentBomId }}'
                - '{{ duplicatedBOMId }}'
            - equals:
                - '{{ currentBomName }}'
                - '{{ selectedBomName}}'
            - equals:
                - '{{ currentBomStatus }}'
                - '{{ defaultBOMStatus }}'
          name: 2_13 - (Step 2 - Dup. Bill of Material - 13) read duplicated bill of material

      # -
      # -
      # - Test create Work order
      # -

      - log: User {{ email }} - (Step 54) Get pages_name
      - post:
          url: /metadata
          json:
            query: step_54_get_pages_name.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 54) get-pages_name

      - log: User {{ email }} - (Step 55) Get installedPackages
      - post:
          url: /metadata
          json:
            query: step_55_get_installedPackages.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 55) get-installedPackages

      - log: User {{ email }} - (Step 56) Get stickers
      - post:
          url: /metadata
          json:
            query: step_56_get_stickers.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 56) get-stickers

      - log: User {{ email }} - (Step 57) Get filter_package_or_page
      - post:
          url: /metadata
          json:
            query: step_57_get_filter_package_or_page.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 57) get-filter_package_or_page

      - log: User {{ email }} - (Step 58) Get navigationPanelItems
      - post:
          url: /api
          json:
            query: step_58_get_navigationPanelItems.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 58) get-navigationPanelItems

      - log: User {{ email }} - (Step 59) Get work_order_panel
      - post:
          url: /metadata
          json:
            query: step_59_get_work_order_panel.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 59) get-work_order_panel

      - log: User {{ email }} - (Step 60) Get xtrem_manu_facturing_work_order
      - post:
          url: /api
          json:
            query: step_60_get_xtrem_manu_facturing_work_order.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 60) get-xtrem_manu_facturing_work_order

      - log: User {{ email }} - (Step 61) Get xtremSystem_site_query
      - post:
          url: /api
          json:
            query: step_61_get_xtremSystem_site_query.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 61) get-xtremSystem_site_query

      - log: User {{ email }} - (Step 62) Get xtremSystem_site_query
      - post:
          url: /api
          json:
            query: step_62_get_xtremSystem_site_query.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 62) get-xtremSystem_site_query

      - log: User {{ email }} - (Step 63) Get xtremtechnical_data_query
      - post:
          url: /api
          json:
            query: step_63_get_xtremtechnical_data_query.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 63) get-xtremtechnical_data_query

      - log: User {{ email }} - (Step 64) Get xtremtechnical_routing_data_query
      - post:
          url: /api
          json:
            query: step_64_get_xtremtechnical_routing_data_query.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 64) get-xtremtechnical_routing_data_query

      - log: User {{ email }} - (Step 65) Get xtremtechnical_mutation_workOrder
      - post:
          url: /api
          json:
            query: step_65_get_xtremtechnical_mutation_workOrder.graphql
            variables:
              siteId: 'Q11GVWPWQPQRFDQP'
              name: 'TestAssy2002'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 65) xtremtechnical_mutation_workOrder

      - log: User {{ email }} - (Step 66) Get xtremManufacturing_workOrder_data
      - post:
          url: /api
          json:
            query: step_66_get_xtremManufacturing_workOrder_data.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 66) get-xtremManufacturing_workOrder_data

      - log: User {{ email }} - (Step 67) Get xtremManufacturing_workOrder_data
      - post:
          url: /api
          json:
            query: step_67_get_xtremManufacturing_workOrder_data.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 67) get-xtremManufacturing_workOrder_data

      - log: User {{ email }} - (Step 68) Get xtrem_finance_data
      - post:
          url: /api
          json:
            query: step_68_get_xtrem_finance_data.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 68) get-xtrem_finance_data

      - log: User {{ email }} - (Step 69) Get xtrem_stock_data
      - post:
          url: /api
          json:
            query: step_69_get_xtrem_stock_data.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 69) get-xtrem_stock_data

      - log: User {{ email }} - (Step 70) Get xtrem_manufacturing_work_order
      - post:
          url: /api
          json:
            query: step_70_get_xtrem_manufacturing_work_order.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 70) get-xtrem_manufacturing_work_order

      - log: User {{ email }} - (Step 70-1) Get xtrem_manufacturing_workOrder
      - post:
          url: /api
          json:
            query: step_70_1_get_xtrem_manufacturing_workOrder.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 70-1) xtrem_manufacturing_workOrder

      - log: User {{ email }} - (Step 70-2) Get xtrem_manufacturing_workOrder
      - post:
          url: /api
          json:
            query: step_70_2_get_xtrem_manufacturing_workOrder.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 70-2) xtrem_manufacturing_workOrder

      - log: User {{ email }} - (Step 71) Get xtremFinanceData
      - post:
          url: /api
          json:
            query: step_71_get_xtremFinanceData.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 71) xtremFinanceData

      - log: User {{ email }} - (Step 71) Get xtremStockData
      - post:
          url: /api
          json:
            query: step_72_get_xtremStockData.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: (Step 71) xtremStockData
