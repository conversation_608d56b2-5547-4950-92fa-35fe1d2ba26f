query ($billOfMaterialFilter: String) {
    xtremTechnicalData {
        billOfMaterial {
            query(filter: $billOfMaterialFilter, orderBy: "{\"name\":1,\"item\":{\"name\":1},\"_id\":1}", first: 20) {
                edges {
                    node {
                        _id
                        name
                        standardCost
                        baseQuantity
                        status
                        site {
                            _id
                            id
                            businessEntity {
                                _id
                            }
                            isManufacturing
                            legalCompany {
                                _id
                                description
                                id
                                currency {
                                    _id
                                    name
                                    id
                                    decimalDigits
                                    symbol
                                }
                            }
                            name
                        }
                        item {
                            id
                            _id
                            name
                            description
                        }
                        image {
                            value
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
