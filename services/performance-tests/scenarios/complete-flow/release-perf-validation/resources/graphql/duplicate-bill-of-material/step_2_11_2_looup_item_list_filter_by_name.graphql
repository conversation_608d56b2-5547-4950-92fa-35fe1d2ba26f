query ($billOfMaterialName: String, $filteredItemId: IntReference, $itemNameFilter: String, $siteId: IntReference) {
    xtremTechnicalData {
        billOfMaterial {
            lookups(
                data: {
                    status: "availableToUse"
                    name: $billOfMaterialName
                    item: $filteredItemId
                    site: $siteId
                    baseQuantity: 1
                }
            ) {
                item(filter: $itemNameFilter, orderBy: "{\"name\":1,\"_id\":1}") {
                    edges {
                        node {
                            _id
                            id
                            name
                            description
                            stockUnit {
                                id
                                _id
                                symbol
                                description
                                decimalDigits
                                name
                            }
                            serialNumberManagement
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
