query ($billOfMaterialId: Id) {
    xtremTechnicalData {
        billOfMaterial {
            read(_id: $billOfMaterialId) {
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                trackings {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                _id
                                message
                                timeTrackingId
                                productionTrackingId
                                materialTrackingId
                                workOrderId
                                status
                                billOfMaterial {
                                    item {
                                        stockUnit {
                                            _id
                                            name
                                            symbol
                                        }
                                        id
                                        _id
                                        name
                                    }
                                    _id
                                    name
                                }
                                quantity
                                date
                                id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                components {
                    query(first: 10, orderBy: "{\"componentNumber\":1}") {
                        edges {
                            node {
                                standardCost
                                currency {
                                    id
                                }
                                operation {
                                    operationNumber
                                    _id
                                    name
                                }
                                scrapFactor
                                linkQuantity
                                isFixedLinkQuantity
                                unit {
                                    id
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    description
                                }
                                name
                                _id
                                bomItem {
                                    _id
                                }
                                itemSite {
                                    site {
                                        id
                                    }
                                }
                                item {
                                    id
                                    description
                                    _id
                                    name
                                    serialNumberManagement
                                    image {
                                        value
                                    }
                                }
                                lineType
                                componentNumber
                                instruction {
                                    value
                                }
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                treeComponents: components {
                    query(first: 20, orderBy: "{\"componentNumber\":1}") {
                        edges {
                            node {
                                bom {
                                    components {
                                        query {
                                            totalCount
                                        }
                                    }
                                }
                                item {
                                    name
                                    description
                                    _id
                                    id
                                    serialNumberManagement
                                    image {
                                        value
                                    }
                                }
                                instruction {
                                    value
                                }
                                standardCost
                                scrapFactor
                                unit {
                                    _id
                                    name
                                    symbol
                                    decimalDigits
                                    description
                                    id
                                }
                                linkQuantity
                                isFixedLinkQuantity
                                operation {
                                    _id
                                    name
                                    operationNumber
                                }
                                lineType
                                name
                                componentNumber
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _id
                routingCode {
                    _id
                    item {
                        id
                    }
                    status
                    name
                }
                standardCost
                baseQuantity
                site {
                    _id
                    name
                    id
                    businessEntity {
                        _id
                    }
                    isManufacturing
                    legalCompany {
                        _id
                        description
                        id
                        currency {
                            _id
                            name
                            id
                            decimalDigits
                            symbol
                        }
                    }
                }
                item {
                    _id
                    name
                    id
                    serialNumberManagement
                    stockUnit {
                        _id
                        decimalDigits
                        symbol
                        id
                        name
                        description
                    }
                    description
                }
                name
                status
                image {
                    value
                }
                _etag
            }
        }
    }
}
