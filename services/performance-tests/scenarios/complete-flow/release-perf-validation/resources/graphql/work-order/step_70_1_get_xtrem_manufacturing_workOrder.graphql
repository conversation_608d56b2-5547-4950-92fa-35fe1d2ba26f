query {
    xtremManufacturing {
        workOrder {
            read(_id: "6166") {
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                productionItems {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                releasedItem {
                                    id
                                    _id
                                    serialNumberManagement
                                    name
                                }
                                itemSite {
                                    preferredProcess
                                    isOrderToOrder
                                    prodLeadTime
                                    _id
                                }
                                lineStatus
                                storedDimensions
                                storedAttributes
                                computedAttributes
                                totalActualQuantity
                                releasedQuantity
                                stockUnit {
                                    _id
                                    symbol
                                    decimalDigits
                                    id
                                    name
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                note {
                    value
                }
                productionComponents {
                    query(first: 20, orderBy: "{\"componentNumber\":1}") {
                        edges {
                            node {
                                componentNumber
                                allocationRequestStatus
                                allocationStatus
                                storedDimensions
                                instruction {
                                    value
                                }
                                storedAttributes
                                computedAttributes
                                isFixedLinkQuantity
                                unit {
                                    decimalDigits
                                    description
                                    symbol
                                    id
                                    name
                                    _id
                                }
                                linkQuantity
                                actualCost
                                plannedCost
                                completedQuantityPercentage
                                scrapFactor
                                operation {
                                    _id
                                    operationNumber
                                    workOrder {
                                        number
                                        _id
                                    }
                                    name
                                }
                                quantityAllocated
                                consumedQuantity
                                requiredQuantity
                                lineType
                                isAdded
                                lineStatus
                                item {
                                    description
                                    id
                                    _id
                                    stockUnit {
                                        _id
                                        description
                                        symbol
                                        decimalDigits
                                        id
                                        name
                                    }
                                    isStockManaged
                                    name
                                }
                                name
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                productionOperations {
                    query(first: 20, orderBy: "{\"operationNumber\":1}") {
                        edges {
                            node {
                                runTimeUnit {
                                    _id
                                    symbol
                                    decimalDigits
                                    description
                                    id
                                    name
                                }
                                setupTimeUnit {
                                    _id
                                    symbol
                                    decimalDigits
                                    description
                                    id
                                    name
                                }
                                instruction {
                                    value
                                }
                                _id
                                minCapabilityLevel {
                                    level
                                    name
                                    description
                                    id
                                    _id
                                }
                                resourceGroupNumber
                                isProductionStep
                                actualBatchCost
                                expectedBatchCost
                                completedTimePercentage
                                endDatetime
                                startDatetime
                                actualRunTime
                                actualSetupTime
                                expectedRunTime
                                expectedSetupTime
                                completedQuantity
                                plannedQuantity
                                isAdded
                                status
                                name
                                operationNumber
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                baseQuantity
                routingTimeUnit {
                    _id
                    name
                    symbol
                    decimalDigits
                    id
                }
                bomCode {
                    _id
                    name
                    item {
                        name
                        _id
                        id
                        description
                    }
                }
                isServiceOptionsSerialNumberActive
                category {
                    _id
                    name
                    id
                    billOfMaterial
                    routing
                }
                routingCode {
                    _id
                    name
                    item {
                        id
                    }
                    doSerialNumberPreGeneration
                }
                stockTransactionStatus
                workOrderCurrency: site {
                    _id
                    legalCompany {
                        currency {
                            name
                            id
                        }
                    }
                    description
                    id
                    name
                }
                closingDate
                endDate
                startDate
                requestedDate
                creationDate
                timeZone
                schedulingStatus
                isForwardScheduling
                materialCompletionPercentage
                processCompletionPercentage
                productionCompletionPercentage
                actualOverheadCost
                actualMaterialCost
                actualProcessCost
                plannedOverheadCost
                plannedMaterialCost
                plannedProcessCost
                allocationRequestStatus
                allocationStatus
                status
                type
                site {
                    _id
                    name
                    id
                    businessEntity {
                        _id
                    }
                    legalCompany {
                        currency {
                            symbol
                            _id
                        }
                        name
                        _id
                    }
                }
                canBeClosed
                name
                number
                _id
                _etag
            }
        }
    }
}
