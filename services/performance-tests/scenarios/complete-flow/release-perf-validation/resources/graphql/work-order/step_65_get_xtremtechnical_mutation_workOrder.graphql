mutation ($name: String, $siteId: String) {
    xtremManufacturing {
        workOrder {
            createWorkOrder(
                data: {
                    workOrderNumber: ""
                    startDate: "2024-07-11"
                    releasedQuantity: 10
                    siteId: $siteId
                    releasedItem: "testassy2002"
                    workOrderCategory: "87"
                    type: "planned"
                    name: $name
                    bom: "testassy2002"
                    route: "testassy2002"
                }
            ) {
                _id
                number
                name
                productionItems {
                    query {
                        edges {
                            node {
                                _id
                                releasedQuantity
                                workInProgress {
                                    _id
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
