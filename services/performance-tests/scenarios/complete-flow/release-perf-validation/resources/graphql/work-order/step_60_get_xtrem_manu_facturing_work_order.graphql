query {
    rootNode: xtremManufacturing {
        workOrder {
            getDefaults(data: { _id: null }) {
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                category {
                    _id
                    name
                    id
                    billOfMaterial
                    routing
                }
            }
        }
    }
    navigationPanelItems: xtremManufacturing {
        workOrder {
            query(orderBy: "{\"site\":{\"id\":1},\"_id\":1}") {
                edges {
                    node {
                        _id
                        number
                        site {
                            id
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
