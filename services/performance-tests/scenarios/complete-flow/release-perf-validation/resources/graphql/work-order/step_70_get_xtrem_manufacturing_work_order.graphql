query {
    xtremManufacturing {
        workOrder {
            query(orderBy: "{\"startDate\":-1,\"number\":-1,\"_id\":1}", first: 30) {
                edges {
                    node {
                        _id
                        status
                        site {
                            _id
                            id
                            description
                            name
                        }
                        endDate
                        startDate
                        category {
                            _id
                            name
                            id
                        }
                        productionItem {
                            _id
                            totalActualQuantity
                            releasedItem {
                                id
                                image {
                                    value
                                }
                            }
                            releasedQuantity
                        }
                        type
                        bomCode {
                            item {
                                id
                            }
                        }
                        name
                        number
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
