query {
    xtremManufacturing {
        workOrder {
            query(filter: "{\"_id\":\"6166\"}") {
                edges {
                    node {
                        materialTrackings {
                            query(filter: "{\"workOrder\":{\"_id\":\"6166\"}}") {
                                edges {
                                    node {
                                        _id
                                        number
                                        entryDate
                                        site {
                                            _id
                                            id
                                            name
                                            isLocationManaged
                                        }
                                        stockTransactionStatus
                                    }
                                    cursor
                                }
                                pageInfo {
                                    hasNextPage
                                    endCursor
                                    hasPreviousPage
                                    startCursor
                                }
                            }
                        }
                        productionTrackings {
                            query(filter: "{\"workOrder\":{\"_id\":\"6166\"}}") {
                                edges {
                                    node {
                                        _id
                                        number
                                        entryDate
                                        site {
                                            _id
                                            id
                                            name
                                            isLocationManaged
                                        }
                                        stockTransactionStatus
                                    }
                                    cursor
                                }
                                pageInfo {
                                    hasNextPage
                                    endCursor
                                    hasPreviousPage
                                    startCursor
                                }
                            }
                        }
                        timeTrackings {
                            query(filter: "{\"workOrder\":{\"_id\":\"6166\"}}") {
                                edges {
                                    node {
                                        _id
                                        number
                                        entryDate
                                        site {
                                            _id
                                            id
                                            name
                                            isLocationManaged
                                        }
                                    }
                                    cursor
                                }
                                pageInfo {
                                    hasNextPage
                                    endCursor
                                    hasPreviousPage
                                    startCursor
                                }
                            }
                        }
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
