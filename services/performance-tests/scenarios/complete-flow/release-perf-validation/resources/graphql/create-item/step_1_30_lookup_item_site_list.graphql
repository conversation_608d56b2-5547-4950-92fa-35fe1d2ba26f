query {
    xtremMasterData {
        itemSite {
            lookups(
                data: {
                    preferredProcess: "purchasing"
                    prodLeadTime: null
                    purchaseLeadTime: null
                    isOrderToOrder: null
                    safetyStock: null
                    reorderPoint: null
                    batchQuantity: null
                    economicOrderQuantity: null
                    indirectCostSection: null
                    valuationMethod: null
                    isSpecificItemTaxGroup: null
                    itemTaxGroup: null
                    lastCountDate: null
                    _customData: "{\"revision\":null}"
                }
            ) {
                site(filter: "{\"isInventory\":true}", orderBy: "{\"name\":1,\"_id\":1}", first: 10) {
                    edges {
                        node {
                            _id
                            isInventory
                            name
                            id
                            businessEntity {
                                _id
                            }
                            isPurchase
                            isSales
                            financialCurrency {
                                _id
                                name
                                id
                                decimalDigits
                                symbol
                            }
                            legalCompany {
                                _id
                                name
                                id
                            }
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
