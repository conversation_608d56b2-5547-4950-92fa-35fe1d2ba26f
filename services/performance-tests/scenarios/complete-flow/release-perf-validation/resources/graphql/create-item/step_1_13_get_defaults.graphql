query {
    xtremMasterData {
        item {
            getDefaults(data: {}) {
                _customField_item_isPartMagical: _customData
                _customField_item_location: _customData
                _customField_item_isThisPartHeavy: _customData
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                allocationRuleUnit: landedCostItem {
                    allocationRuleUnit {
                        _id
                        name
                        symbol
                        decimalDigits
                        id
                    }
                }
                allocationRule: landedCostItem {
                    allocationRule
                }
                landedCostType: landedCostItem {
                    landedCostType
                }
                lastMessage: intacctItem {
                    lastMessage
                }
                intacctItemSysId: intacctItem {
                    _id
                }
                intacctUrl: intacctItem {
                    url
                }
                intacctId: intacctItem {
                    intacctId
                }
                intacctIntegrationState: intacctItem {
                    state
                }
                billOfMaterials {
                    query(first: 10, orderBy: "{\"baseQuantity\":-1}") {
                        edges {
                            node {
                                site {
                                    id
                                    name
                                }
                                item {
                                    id
                                    _id
                                    name
                                    stockUnit {
                                        symbol
                                        id
                                    }
                                }
                                status
                                prodLeadTime
                                baseQuantity
                                routingCode {
                                    name
                                    _id
                                }
                                _id
                                name
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                intrastatAdditionalUnitToStockUnitConversion
                intrastatAdditionalUnit {
                    _id
                    name
                    symbol
                    decimalDigits
                    type
                    id
                }
                itemDocuments: purchaseDocuments {
                    query(first: 10, orderBy: "{\"document\":{\"orderDate\":-1},\"stockSite\":1}") {
                        edges {
                            node {
                                lineInvoiceStatus
                                lineReceiptStatus
                                expectedReceiptDate
                                quantityToReceive
                                quantity
                                _constructor
                                document {
                                    supplier {
                                        _id
                                        id
                                        businessEntity {
                                            id
                                            taxIdNumber
                                            name
                                        }
                                        name
                                    }
                                    site {
                                        _id
                                        id
                                        description
                                        name
                                    }
                                    displayStatus
                                    number
                                    orderDate
                                    _id
                                }
                                stockSite {
                                    _id
                                    id
                                    description
                                    name
                                }
                                lineStatus
                                item {
                                    stockUnit {
                                        symbol
                                        id
                                    }
                                    _id
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                attributes: storedAttributes
                dimensions: storedDimensions
                postingClass {
                    _id
                    name
                    id
                }
                itemTaxGroup {
                    _id
                    name
                    id
                    description
                }
                ghsClassifications: classifications {
                    query(first: 10, orderBy: "{\"classification\":{\"name\":1,\"id\":1}}") {
                        edges {
                            node {
                                classification {
                                    id
                                    _id
                                    name
                                    pictogram {
                                        value
                                    }
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                allergens {
                    query(first: 10, orderBy: "{\"allergen\":{\"name\":1,\"id\":1}}") {
                        edges {
                            node {
                                allergen {
                                    id
                                    _id
                                    name
                                    pictogram {
                                        value
                                    }
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                customers {
                    query(first: 10, orderBy: "{\"customer\":{\"businessEntity\":{\"name\":1}}}") {
                        edges {
                            node {
                                customer {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        taxIdNumber
                                    }
                                    _id
                                }
                                maximumSalesQuantity
                                minimumSalesQuantity
                                salesUnitToStockUnitConversion
                                salesUnit {
                                    _id
                                    name
                                    symbol
                                    isActive
                                    id
                                }
                                id
                                name
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                salesPrices: customerPrices {
                    query(
                        first: 10
                        orderBy: "{\"priceReason\":{\"priority\":1},\"customer\":{\"businessEntity\":{\"name\":1}},\"salesSite\":{\"businessEntity\":{\"name\":1}},\"stockSite\":{\"businessEntity\":{\"name\":1}},\"unit\":{\"id\":1},\"fromQuantity\":1,\"startDate\":1}"
                    ) {
                        edges {
                            node {
                                priceReason {
                                    name
                                    _id
                                    id
                                    priority
                                }
                                customer {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        currency {
                                            decimalDigits
                                            symbol
                                            name
                                            id
                                            _id
                                        }
                                        _id
                                    }
                                    _id
                                }
                                stockSite {
                                    _id
                                    businessEntity {
                                        name
                                        id
                                        _id
                                    }
                                    isInventory
                                    isActive
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                salesSite {
                                    _id
                                    businessEntity {
                                        name
                                        id
                                        _id
                                    }
                                    isSales
                                    isActive
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                endDate
                                startDate
                                toQuantity
                                fromQuantity
                                unit {
                                    _id
                                    name
                                    symbol
                                    isActive
                                    decimalDigits
                                    id
                                }
                                item {
                                    _id
                                    id
                                }
                                charge
                                discount
                                price
                                currency {
                                    _id
                                    name
                                    id
                                    decimalDigits
                                    symbol
                                }
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                supplierPrices {
                    query(
                        first: 10
                        orderBy: "{\"type\":1,\"priority\":1,\"supplier\":{\"businessEntity\":{\"name\":1}},\"currency\":{\"id\":1},\"price\":1,\"unit\":{\"id\":1},\"fromQuantity\":1,\"toQuantity\":1,\"dateValidFrom\":1,\"dateValidTo\":1,\"site\":{\"id\":1}}"
                    ) {
                        edges {
                            node {
                                type
                                supplier {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        name
                                        id
                                        currency {
                                            decimalDigits
                                            symbol
                                            id
                                            name
                                            _id
                                        }
                                        country {
                                            id
                                            name
                                            _id
                                        }
                                        _id
                                        taxIdNumber
                                    }
                                    _id
                                }
                                site {
                                    businessEntity {
                                        id
                                        name
                                    }
                                    _id
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                dateValidTo
                                dateValidFrom
                                toQuantity
                                fromQuantity
                                unit {
                                    _id
                                    name
                                    symbol
                                    type
                                    decimalDigits
                                    id
                                }
                                price
                                currency {
                                    _id
                                    name
                                    id
                                    decimalDigits
                                    symbol
                                }
                                priority
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                suppliers {
                    query(first: 10, orderBy: "{\"supplier\":{\"businessEntity\":{\"name\":1}}}") {
                        edges {
                            node {
                                supplier {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        currency {
                                            decimalDigits
                                            symbol
                                            id
                                            name
                                            _id
                                        }
                                        country {
                                            id
                                            name
                                            _id
                                        }
                                        _id
                                        taxIdNumber
                                    }
                                    _id
                                }
                                purchaseLeadTime
                                minimumPurchaseQuantity
                                purchaseUnitOfMeasure {
                                    _id
                                    name
                                    symbol
                                    type
                                    decimalDigits
                                    id
                                }
                                isDefaultItemSupplier
                                supplierItemCode
                                supplierItemName
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                itemSites {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                site {
                                    id
                                    _id
                                    name
                                    businessEntity {
                                        _id
                                    }
                                    financialCurrency {
                                        decimalDigits
                                        symbol
                                        _id
                                    }
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                routing {
                                    _id
                                    name
                                    item {
                                        id
                                    }
                                    site {
                                        id
                                    }
                                }
                                billOfMaterial {
                                    _id
                                    name
                                    item {
                                        id
                                    }
                                    site {
                                        id
                                    }
                                }
                                _customData
                                itemTaxGroup {
                                    _id
                                    name
                                    id
                                    description
                                }
                                isSpecificItemTaxGroup
                                stockValuationAtAverageCost
                                indirectCostSection {
                                    _id
                                    name
                                    id
                                    calculationMethod
                                }
                                economicOrderQuantity
                                batchQuantity
                                safetyStock
                                purchaseLeadTime
                                prodLeadTime
                                preferredProcess
                                replenishmentMethod
                                requiredQuantity
                                expectedQuantity
                                valuationMethod
                                currentCost
                                stdCostValue
                                item {
                                    id
                                    name
                                    _id
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                capacity
                density
                volume
                volumeUnit {
                    _id
                    name
                    symbol
                    decimalDigits
                    type
                    id
                }
                weight
                weightUnit {
                    _id
                    name
                    symbol
                    decimalDigits
                    id
                    type
                }
                salesUnitToStockUnitConversionDedicated
                salesUnit {
                    _id
                    name
                    symbol
                    isActive
                    decimalDigits
                    type
                    id
                }
                purchaseUnitToStockUnitConversionDedicated
                purchaseUnit {
                    _id
                    name
                    symbol
                    isActive
                    decimalDigits
                    type
                    id
                }
                stockUnit {
                    _id
                    name
                    symbol
                    isActive
                    decimalDigits
                    type
                    id
                }
                maximumSalesQuantity
                minimumSalesQuantity
                minimumPrice
                basePrice
                currency {
                    _id
                    name
                    id
                    isActive
                    decimalDigits
                    symbol
                }
                serialNumberUsage
                serialNumberSequenceNumber {
                    _id
                    name
                    id
                }
                serialNumberManagement
                lotSequenceNumber {
                    _id
                    name
                    id
                }
                isExpiryManaged
                lotManagement
                statusLabel: status
                statusTest: stockUnit {
                    name
                }
                status
                commodityCode
                eanNumber
                description
                image {
                    value
                }
                isSold
                isManufactured
                isBought
                isStockManaged
                type
                category {
                    _id
                    name
                    id
                    type
                }
                id
                name
            }
        }
    }
}
