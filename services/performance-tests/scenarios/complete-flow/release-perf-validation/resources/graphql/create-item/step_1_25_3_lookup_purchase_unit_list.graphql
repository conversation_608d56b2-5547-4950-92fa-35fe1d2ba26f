query ($itemName: String, $itemId: String) {
    xtremMasterData {
        item {
            lookups(
                data: {
                    name: $itemName
                    id: $itemId
                    category: null
                    type: "good"
                    isStockManaged: true
                    isBought: true
                    isManufactured: false
                    isSold: false
                    image: null
                    description: "Keg Pump Test 10"
                    eanNumber: null
                    commodityCode: null
                    status: "active"
                    stockUnit: "_id:1404"
                    lotManagement: "notManaged"
                    isExpiryManaged: false
                    lotSequenceNumber: null
                    serialNumberManagement: "notManaged"
                    serialNumberSequenceNumber: null
                    serialNumberUsage: "issueAndReceipt"
                    currency: null
                    basePrice: 0
                    minimumPrice: 0
                    minimumSalesQuantity: 0
                    maximumSalesQuantity: 0
                    purchaseUnit: null
                    salesUnit: "_id:1404"
                    weightUnit: null
                    weight: 0
                    volumeUnit: null
                    volume: 0
                    density: 0
                    capacity: null
                    itemTaxGroup: null
                    postingClass: null
                    storedDimensions: null
                    storedAttributes: null
                    intrastatAdditionalUnit: null
                    intrastatAdditionalUnitToStockUnitConversion: 0
                    intacctItem: null
                    landedCostItem: null
                    _customData: "{\"isThisPartHeavy\":null,\"location\":null,\"isPartMagical\":null}"
                }
            ) {
                purchaseUnit(
                    filter: "{\"isActive\":true}"
                    orderBy: "{\"name\":1,\"_id\":1}"
                    after: "[\"Meter\",1408]#41"
                    first: 10
                ) {
                    edges {
                        node {
                            _id
                            isActive
                            name
                            symbol
                            decimalDigits
                            type
                            id
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
