query {
    xtremFinanceData {
        dimension {
            query(
                filter: "{\"dimensionType\":{\"docProperty\":{\"_eq\":\"dimensionType01\"},\"isActive\":true},\"isActive\":true}"
                orderBy: "{\"name\":1,\"_id\":1}"
            ) {
                edges {
                    node {
                        _id
                        dimensionType {
                            docProperty
                            _id
                            isActive
                        }
                        isActive
                        name
                        id
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
