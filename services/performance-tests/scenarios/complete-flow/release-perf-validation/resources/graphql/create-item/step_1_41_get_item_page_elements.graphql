query {
    xtremMasterData {
        item {
            query(orderBy: "{\"name\":1,\"id\":1,\"_id\":1}", first: 41) {
                edges {
                    node {
                        _id
                        landedCostItem {
                            allocationRuleUnit {
                                _id
                                name
                                symbol
                                id
                            }
                            allocationRule
                            landedCostType
                        }
                        maximumSalesQuantity
                        minimumSalesQuantity
                        currency {
                            _id
                            name
                            id
                            symbol
                            rounding
                            decimalDigits
                        }
                        minimumPrice
                        basePrice
                        capacity
                        density
                        volume
                        volumeUnit {
                            _id
                            name
                            symbol
                            id
                            decimalDigits
                        }
                        weight
                        weightUnit {
                            _id
                            name
                            symbol
                            id
                            decimalDigits
                        }
                        intrastatAdditionalUnit {
                            _id
                            name
                            symbol
                            id
                        }
                        salesUnit {
                            _id
                            name
                            symbol
                            id
                        }
                        purchaseUnit {
                            _id
                            name
                            symbol
                            id
                        }
                        stockUnit {
                            _id
                            name
                            symbol
                            id
                        }
                        serialNumberSequenceNumber {
                            _id
                            name
                            id
                            legislation {
                                id
                            }
                        }
                        serialNumberManagement
                        lotSequenceNumber {
                            _id
                            name
                            id
                            legislation {
                                id
                            }
                        }
                        isExpiryManaged
                        lotManagement
                        itemTaxGroup {
                            _id
                            name
                            id
                        }
                        postingClass {
                            _id
                            name
                            id
                        }
                        commodityCode
                        eanNumber
                        intacctItem {
                            state
                            intacctId
                        }
                        status
                        isSold
                        isManufactured
                        isBought
                        isStockManaged
                        type
                        category {
                            _id
                            name
                            id
                        }
                        description
                        id
                        name
                        image {
                            value
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
