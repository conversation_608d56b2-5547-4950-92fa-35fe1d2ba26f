query ($siteId: IntReference, $siteFilter: String) {
    xtremMasterData {
        itemSite {
            lookups(
                data: {
                    site: $siteId
                    replenishmentMethod: null
                    preferredProcess: "purchasing"
                    prodLeadTime: null
                    purchaseLeadTime: null
                    isOrderToOrder: null
                    safetyStock: null
                    reorderPoint: null
                    batchQuantity: null
                    economicOrderQuantity: null
                    indirectCostSection: null
                    valuationMethod: null
                    isSpecificItemTaxGroup: null
                    itemTaxGroup: null
                    lastCountDate: null
                    _customData: "{\"revision\":null}"
                }
            ) {
                site(filter: $siteFilter, orderBy: "{\"name\":1,\"_id\":1}") {
                    edges {
                        node {
                            _id
                            isInventory
                            id
                            name
                            businessEntity {
                                _id
                            }
                            isPurchase
                            isSales
                            financialCurrency {
                                _id
                                name
                                id
                                decimalDigits
                                symbol
                            }
                            legalCompany {
                                _id
                                name
                                id
                            }
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
