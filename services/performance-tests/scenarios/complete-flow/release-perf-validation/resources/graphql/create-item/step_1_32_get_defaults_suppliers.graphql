query ($itemName: String, $itemId: String) {
    nestedDefaults: xtremMasterData {
        item {
            getDefaults(
                data: {
                    name: $itemName
                    id: $itemId
                    category: null
                    type: "good"
                    isStockManaged: true
                    isBought: true
                    isManufactured: false
                    isSold: false
                    image: null
                    description: "Keg Pump Test 10"
                    eanNumber: null
                    commodityCode: null
                    status: "active"
                    stockUnit: "_id:1404"
                    lotManagement: "notManaged"
                    isExpiryManaged: false
                    lotSequenceNumber: null
                    serialNumberManagement: "notManaged"
                    serialNumberSequenceNumber: null
                    serialNumberUsage: "issueAndReceipt"
                    currency: null
                    basePrice: 0
                    minimumPrice: 0
                    minimumSalesQuantity: 0
                    maximumSalesQuantity: 0
                    purchaseUnit: "_id:1404"
                    salesUnit: "_id:1404"
                    weightUnit: null
                    weight: 0
                    volumeUnit: null
                    volume: 0
                    density: 0
                    capacity: null
                    itemSites: [{ _id: "-1", _action: "create" }]
                    itemTaxGroup: null
                    postingClass: null
                    storedDimensions: null
                    storedAttributes: null
                    intrastatAdditionalUnit: null
                    intrastatAdditionalUnitToStockUnitConversion: 0
                    intacctItem: null
                    landedCostItem: null
                    _customData: "{\"isThisPartHeavy\":null,\"location\":null,\"isPartMagical\":null}"
                    _id: null
                    suppliers: [{}]
                }
            ) {
                suppliers {
                    query(first: 10, orderBy: "{\"supplier\":{\"businessEntity\":{\"name\":1}}}") {
                        edges {
                            node {
                                supplier {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        currency {
                                            decimalDigits
                                            symbol
                                            id
                                            name
                                            _id
                                        }
                                        country {
                                            id
                                            name
                                            _id
                                        }
                                        _id
                                        taxIdNumber
                                    }
                                    _id
                                }
                                purchaseLeadTime
                                minimumPurchaseQuantity
                                purchaseUnitOfMeasure {
                                    _id
                                    name
                                    symbol
                                    type
                                    decimalDigits
                                    id
                                }
                                isDefaultItemSupplier
                                supplierItemCode
                                supplierItemName
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
    }
}
