mutation ($itemName: String, $itemId: String, $itemSite: IntReference) {
    xtremMasterData {
        item {
            create(
                data: {
                    name: $itemName
                    id: $itemId
                    category: null
                    type: "good"
                    isStockManaged: true
                    isBought: true
                    isManufactured: true
                    isSold: false
                    image: null
                    description: "Keg Pump Test 10"
                    eanNumber: null
                    commodityCode: null
                    status: "active"
                    stockUnit: "_id:1404"
                    lotManagement: "notManaged"
                    isExpiryManaged: false
                    lotSequenceNumber: null
                    serialNumberManagement: "notManaged"
                    serialNumberSequenceNumber: null
                    serialNumberUsage: "issueAndReceipt"
                    currency: null
                    basePrice: 0
                    minimumPrice: 0
                    minimumSalesQuantity: 0
                    maximumSalesQuantity: 0
                    purchaseUnit: "_id:1404"
                    salesUnit: "_id:1404"
                    weightUnit: null
                    weight: 0
                    volumeUnit: null
                    volume: 0
                    density: 0
                    capacity: null
                    itemSites: [
                        {
                            _action: "create"
                            _id: "-1"
                            batchQuantity: 0
                            economicOrderQuantity: 0
                            indirectCostSection: null
                            isOrderToOrder: null
                            isSpecificItemTaxGroup: null
                            item: null
                            itemTaxGroup: null
                            lastCountDate: null
                            preferredProcess: "purchasing"
                            prodLeadTime: null
                            purchaseLeadTime: null
                            reorderPoint: null
                            replenishmentMethod: "notManaged"
                            safetyStock: null
                            site: $itemSite
                            valuationMethod: "standardCost"
                        }
                    ]
                    itemTaxGroup: "_id:3817"
                    postingClass: "_id:960"
                    storedDimensions: null
                    storedAttributes: null
                    intrastatAdditionalUnit: null
                    intrastatAdditionalUnitToStockUnitConversion: 0
                    intacctItem: null
                    landedCostItem: null
                    _customData: "{\"isThisPartHeavy\":null,\"location\":null,\"isPartMagical\":null}"
                }
            ) {
                _id
                name
                id
                category {
                    _id
                    name
                    id
                    type
                }
                type
                isStockManaged
                isBought
                isManufactured
                isSold
                image {
                    value
                }
                description
                eanNumber
                commodityCode
                status
                statusTest: stockUnit {
                    name
                }
                statusLabel: status
                lotManagement
                isExpiryManaged
                lotSequenceNumber {
                    _id
                    name
                    id
                }
                serialNumberManagement
                serialNumberSequenceNumber {
                    _id
                    name
                    id
                }
                serialNumberUsage
                currency {
                    _id
                    name
                    id
                    isActive
                    decimalDigits
                    symbol
                }
                basePrice
                minimumPrice
                minimumSalesQuantity
                maximumSalesQuantity
                stockUnit {
                    _id
                    name
                    symbol
                    isActive
                    decimalDigits
                    type
                    id
                }
                purchaseUnit {
                    _id
                    name
                    symbol
                    isActive
                    decimalDigits
                    type
                    id
                }
                purchaseUnitToStockUnitConversionDedicated
                salesUnit {
                    _id
                    name
                    symbol
                    isActive
                    decimalDigits
                    type
                    id
                }
                salesUnitToStockUnitConversionDedicated
                weightUnit {
                    _id
                    name
                    symbol
                    decimalDigits
                    id
                    type
                }
                weight
                volumeUnit {
                    _id
                    name
                    symbol
                    decimalDigits
                    type
                    id
                }
                volume
                density
                capacity
                itemSites {
                    query(first: 10, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                site {
                                    id
                                    _id
                                    name
                                    businessEntity {
                                        _id
                                    }
                                    financialCurrency {
                                        decimalDigits
                                        symbol
                                        _id
                                    }
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                routing {
                                    _id
                                    name
                                    item {
                                        id
                                    }
                                    site {
                                        id
                                    }
                                }
                                billOfMaterial {
                                    _id
                                    name
                                    item {
                                        id
                                    }
                                    site {
                                        id
                                    }
                                }
                                _customData
                                itemTaxGroup {
                                    _id
                                    name
                                    id
                                    description
                                }
                                isSpecificItemTaxGroup
                                stockValuationAtAverageCost
                                indirectCostSection {
                                    _id
                                    name
                                    id
                                    calculationMethod
                                }
                                economicOrderQuantity
                                batchQuantity
                                safetyStock
                                purchaseLeadTime
                                prodLeadTime
                                preferredProcess
                                replenishmentMethod
                                requiredQuantity
                                expectedQuantity
                                valuationMethod
                                currentCost
                                stdCostValue
                                item {
                                    id
                                    name
                                    _id
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                suppliers {
                    query(first: 10, orderBy: "{\"supplier\":{\"businessEntity\":{\"name\":1}}}") {
                        edges {
                            node {
                                supplier {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        currency {
                                            decimalDigits
                                            symbol
                                            id
                                            name
                                            _id
                                        }
                                        country {
                                            id
                                            name
                                            _id
                                        }
                                        _id
                                        taxIdNumber
                                    }
                                    _id
                                }
                                purchaseLeadTime
                                minimumPurchaseQuantity
                                purchaseUnitOfMeasure {
                                    _id
                                    name
                                    symbol
                                    type
                                    decimalDigits
                                    id
                                }
                                isDefaultItemSupplier
                                supplierItemCode
                                supplierItemName
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                supplierPrices {
                    query(
                        first: 10
                        orderBy: "{\"type\":1,\"priority\":1,\"supplier\":{\"businessEntity\":{\"name\":1}},\"currency\":{\"id\":1},\"price\":1,\"unit\":{\"id\":1},\"fromQuantity\":1,\"toQuantity\":1,\"dateValidFrom\":1,\"dateValidTo\":1,\"site\":{\"id\":1}}"
                    ) {
                        edges {
                            node {
                                type
                                supplier {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        name
                                        id
                                        currency {
                                            decimalDigits
                                            symbol
                                            id
                                            name
                                            _id
                                        }
                                        country {
                                            id
                                            name
                                            _id
                                        }
                                        _id
                                        taxIdNumber
                                    }
                                    _id
                                }
                                site {
                                    businessEntity {
                                        id
                                        name
                                    }
                                    _id
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                dateValidTo
                                dateValidFrom
                                toQuantity
                                fromQuantity
                                unit {
                                    _id
                                    name
                                    symbol
                                    type
                                    decimalDigits
                                    id
                                }
                                price
                                currency {
                                    _id
                                    name
                                    id
                                    decimalDigits
                                    symbol
                                }
                                priority
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                salesPrices: customerPrices {
                    query(
                        first: 10
                        orderBy: "{\"priceReason\":{\"priority\":1},\"customer\":{\"businessEntity\":{\"name\":1}},\"salesSite\":{\"businessEntity\":{\"name\":1}},\"stockSite\":{\"businessEntity\":{\"name\":1}},\"unit\":{\"id\":1},\"fromQuantity\":1,\"startDate\":1}"
                    ) {
                        edges {
                            node {
                                priceReason {
                                    name
                                    _id
                                    id
                                    priority
                                }
                                customer {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        currency {
                                            decimalDigits
                                            symbol
                                            name
                                            id
                                            _id
                                        }
                                        _id
                                    }
                                    _id
                                }
                                stockSite {
                                    _id
                                    businessEntity {
                                        name
                                        id
                                        _id
                                    }
                                    isInventory
                                    isActive
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                salesSite {
                                    _id
                                    businessEntity {
                                        name
                                        id
                                        _id
                                    }
                                    isSales
                                    isActive
                                    legalCompany {
                                        _id
                                        name
                                        id
                                    }
                                }
                                endDate
                                startDate
                                toQuantity
                                fromQuantity
                                unit {
                                    _id
                                    name
                                    symbol
                                    isActive
                                    decimalDigits
                                    id
                                }
                                item {
                                    _id
                                    id
                                }
                                charge
                                discount
                                price
                                currency {
                                    _id
                                    name
                                    id
                                    decimalDigits
                                    symbol
                                }
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                customers {
                    query(first: 10, orderBy: "{\"customer\":{\"businessEntity\":{\"name\":1}}}") {
                        edges {
                            node {
                                customer {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        taxIdNumber
                                    }
                                    _id
                                }
                                maximumSalesQuantity
                                minimumSalesQuantity
                                salesUnitToStockUnitConversion
                                salesUnit {
                                    _id
                                    name
                                    symbol
                                    isActive
                                    id
                                }
                                id
                                name
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                allergens {
                    query(first: 10, orderBy: "{\"allergen\":{\"name\":1,\"id\":1}}") {
                        edges {
                            node {
                                allergen {
                                    id
                                    _id
                                    name
                                    pictogram {
                                        value
                                    }
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                ghsClassifications: classifications {
                    query(first: 10, orderBy: "{\"classification\":{\"name\":1,\"id\":1}}") {
                        edges {
                            node {
                                classification {
                                    id
                                    _id
                                    name
                                    pictogram {
                                        value
                                    }
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                itemTaxGroup {
                    _id
                    name
                    id
                    description
                }
                postingClass {
                    _id
                    name
                    id
                }
                dimensions: storedDimensions
                attributes: storedAttributes
                itemDocuments: purchaseDocuments {
                    query(first: 10, orderBy: "{\"document\":{\"orderDate\":-1},\"stockSite\":1}") {
                        edges {
                            node {
                                lineInvoiceStatus
                                lineReceiptStatus
                                expectedReceiptDate
                                quantityToReceive
                                quantity
                                _constructor
                                document {
                                    supplier {
                                        _id
                                        id
                                        businessEntity {
                                            id
                                            taxIdNumber
                                            name
                                        }
                                        name
                                    }
                                    site {
                                        _id
                                        id
                                        description
                                        name
                                    }
                                    displayStatus
                                    number
                                    orderDate
                                    _id
                                }
                                stockSite {
                                    _id
                                    id
                                    description
                                    name
                                }
                                lineStatus
                                item {
                                    stockUnit {
                                        symbol
                                        id
                                    }
                                    _id
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                intrastatAdditionalUnit {
                    _id
                    name
                    symbol
                    decimalDigits
                    type
                    id
                }
                intrastatAdditionalUnitToStockUnitConversion
                billOfMaterials {
                    query(first: 10, orderBy: "{\"baseQuantity\":-1}") {
                        edges {
                            node {
                                site {
                                    id
                                    name
                                }
                                item {
                                    id
                                    _id
                                    name
                                    stockUnit {
                                        symbol
                                        id
                                    }
                                }
                                status
                                prodLeadTime
                                baseQuantity
                                routingCode {
                                    name
                                    _id
                                }
                                _id
                                name
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                intacctIntegrationState: intacctItem {
                    state
                }
                intacctId: intacctItem {
                    intacctId
                }
                intacctUrl: intacctItem {
                    url
                }
                intacctItemSysId: intacctItem {
                    _id
                }
                lastMessage: intacctItem {
                    lastMessage
                }
                landedCostType: landedCostItem {
                    landedCostType
                }
                allocationRule: landedCostItem {
                    allocationRule
                }
                allocationRuleUnit: landedCostItem {
                    allocationRuleUnit {
                        _id
                        name
                        symbol
                        decimalDigits
                        id
                    }
                }
                _attachmentsList: _attachments {
                    query(first: 20, orderBy: "{\"_id\":-1}") {
                        edges {
                            node {
                                attachment {
                                    mimeType
                                    contentLength
                                    downloadUrl
                                    status
                                    filename
                                    _createUser {
                                        displayName
                                    }
                                    _createStamp
                                }
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                _customField_item_isThisPartHeavy: _customData
                _customField_item_location: _customData
                _customField_item_isPartMagical: _customData
            }
        }
    }
}
