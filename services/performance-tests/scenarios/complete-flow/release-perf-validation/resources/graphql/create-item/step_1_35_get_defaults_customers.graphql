query ($itemName: String, $itemId: String) {
    nestedDefaults: xtremMasterData {
        item {
            getDefaults(
                data: {
                    name: $itemName
                    id: $itemId
                    category: "_id:171"
                    type: "good"
                    isStockManaged: true
                    isBought: true
                    isManufactured: true
                    isSold: true
                    image: null
                    description: null
                    eanNumber: null
                    commodityCode: null
                    status: "active"
                    stockUnit: "_id:1404"
                    lotManagement: "notManaged"
                    isExpiryManaged: false
                    lotSequenceNumber: null
                    serialNumberManagement: "notManaged"
                    serialNumberSequenceNumber: null
                    serialNumberUsage: "issueAndReceipt"
                    currency: null
                    basePrice: 0
                    minimumPrice: 0
                    minimumSalesQuantity: 200
                    maximumSalesQuantity: 1000
                    purchaseUnit: "_id:1404"
                    salesUnit: "_id:1404"
                    weightUnit: null
                    weight: 0
                    volumeUnit: null
                    volume: 0
                    density: 0
                    capacity: null
                    itemSites: [{ _id: "-1", _action: "create" }]
                    itemTaxGroup: null
                    postingClass: null
                    storedDimensions: null
                    storedAttributes: null
                    intrastatAdditionalUnit: "_id:1404"
                    intrastatAdditionalUnitToStockUnitConversion: 1
                    intacctItem: null
                    landedCostItem: null
                    _customData: "{\"isThisPartHeavy\":null,\"location\":null,\"isPartMagical\":null}"
                    _id: null
                    customers: [{}]
                }
            ) {
                customers {
                    query(first: 10, orderBy: "{\"customer\":{\"businessEntity\":{\"name\":1}}}") {
                        edges {
                            node {
                                customer {
                                    name
                                    businessEntity {
                                        image {
                                            value
                                        }
                                        id
                                        name
                                        taxIdNumber
                                    }
                                    _id
                                }
                                maximumSalesQuantity
                                minimumSalesQuantity
                                salesUnitToStockUnitConversion
                                salesUnit {
                                    _id
                                    name
                                    symbol
                                    isActive
                                    id
                                }
                                id
                                name
                                isActive
                                _id
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
    }
}
