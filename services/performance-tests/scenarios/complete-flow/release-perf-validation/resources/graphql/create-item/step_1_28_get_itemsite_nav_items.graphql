query {
    navigationPanelItems: xtremMasterData {
        itemSite {
            query(orderBy: "{\"item\":{\"name\":1},\"site\":{\"name\":1},\"_id\":1}") {
                edges {
                    node {
                        _id
                        economicOrderQuantity
                        batchQuantity
                        reorderPoint
                        safetyStock
                        purchaseLeadTime
                        stockValue
                        stdCostValue
                        valuationMethod
                        rejectedStockQuantity
                        onQualityControlStockQuantity
                        acceptedStockQuantity
                        requiredQuantity
                        expectedQuantity
                        itemTaxGroup {
                            _id
                            name
                            id
                        }
                        preferredProcess
                        replenishmentMethod
                        allocatedQuantity
                        inStockQuantity
                        site {
                            _id
                            name
                            id
                            businessEntity {
                                _id
                            }
                            description
                        }
                        item {
                            id
                            _id
                            name
                            description
                        }
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
