config:
  http:
    # AWS timeout is 28 seconds, we expect to complete the request before that timeout
    timeout: 27

  plugins:
    xtrem: {}
    ensure: {}
    expect: {}
    fake-data:
      randProductName:
        maxCharCount: 20
    metrics-by-endpoint:
      # Group metrics by request name rather than URL:
      useOnlyRequestNames: false

  phases:
    - name: 'Ramp to {{ $processEnvironment.TOTAL_USERS }} users in {{ $processEnvironment.RAMP_UP }} seconds'
      duration: '{{ $processEnvironment.RAMP_UP }}'
      arrivalRate: 1
      rampTo: '{{ $processEnvironment.TOTAL_USERS }}'
      maxVusers: '{{ $processEnvironment.TOTAL_USERS }}'
    - name: 'Stress {{ $processEnvironment.TOTAL_USERS }} users for {{ $processEnvironment.DURATION }} seconds'
      duration: '{{ $processEnvironment.DURATION }}'
      # Keep the arrival rate maxed out to force replacement of vusers when they leave and keep their number constant.
      arrivalRate: '{{ $processEnvironment.TOTAL_USERS }}'
      maxVusers: '{{ $processEnvironment.TOTAL_USERS }}'

  environments:
    local:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: http://localhost:8240
      payload:
        delimiter: ';'
        path: '../../../seed/input/01-user.csv'
        fields:
          - 'email'
        order: sequence
        skipHeader: true
    devops:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: '{{ $processEnvironment.CLUSTER_URL }}'
      variables:
        tenant: '{{ $processEnvironment.TENANT_ID }}'
        cluster: '{{ $processEnvironment.CLUSTER }}'
      payload:
        path: '../../../seed/input/01-user.csv'
        fields:
          - 'email'
        order: iterate
        skipHeader: true
        delimiter: ';'

  processor: './fixtures.js'
  variables:
    availableItemSites:
      - ['122']
