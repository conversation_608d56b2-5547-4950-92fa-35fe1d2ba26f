mutation CreateMutation(
    $business_entity_id: IntReference
    $business_entity_primary_address_id: IntReference
    $payment_term: IntReference
) {
    xtremMasterData {
        customer {
            create(
                data: {
                    postingClass: null
                    paymentTerm: $payment_term
                    billToAddress: null
                    billToCustomer: null
                    deliveryAddresses: {{ customer_delivery_addresses }}
                    primaryAddress: $business_entity_primary_address_id
                    businessEntity: $business_entity_id
                    isActive: false
                    minimumOrderAmount: 400
                    isOnHold: false
                    _id: null
                }
            ) {
                _id
                isActive
                businessEntity {
                    image {
                        value
                    }
                    currency__name: currency {
                        name
                        id
                        decimalDigits
                        symbol
                        _id
                    }
                    country__name: country {
                        name
                        id
                        regionLabel
                        zipLabel
                        _id
                    }
                    _id
                    name
                    id
                    isActive
                    taxIdNumber
                    isCustomer
                    isSupplier
                    isSite
                }
                primaryAddress {
                    country__id: country {
                        name
                        id
                        _id
                    }
                    _id
                    name
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                minimumOrderAmount
                billToCustomer {
                    businessEntity {
                        name
                    }
                    primaryAddress__name: primaryAddress {
                        _id
                        name
                    }
                    paymentTerm__name: paymentTerm {
                        _id
                        name
                    }
                    businessEntity__id: businessEntity {
                        _id
                        id
                    }
                    _id
                    name
                    id
                }
                billToAddress {
                    country__id: country {
                        name
                        id
                        _id
                    }
                    _id
                    name
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                paymentTerm {
                    name
                    description
                    _id
                }
                postingClass {
                    type
                    name
                    _id
                }
                isOnHold
                items {
                    query(first: 20) {
                        edges {
                            node {
                                salesUnit__name: salesUnit {
                                    name
                                    id
                                    _id
                                }
                                item__name: item {
                                    stockUnit__name: stockUnit {
                                        name
                                        _id
                                    }
                                    name
                                    id
                                    _id
                                }
                                _id
                                isActive
                                id
                                name
                                salesUnitToStockUnitConversion
                                minimumSalesQuantity
                                maximumSalesQuantity
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                deliveryAddresses {
                    query(first: 20) {
                        edges {
                            node {
                                incoterm__name: incoterm {
                                    name
                                    _id
                                }
                                deliveryMode__name: deliveryMode {
                                    name
                                    _id
                                }
                                shipmentSite__name: shipmentSite {
                                    name
                                    _id
                                }
                                shipToAddress__name: shipToAddress {
                                    name
                                    _id
                                }
                                _id
                                isActive
                                isPrimary
                                deliveryLeadTime
                                isMondayWorkDay
                                isTuesdayWorkDay
                                isWednesdayWorkDay
                                isThursdayWorkDay
                                isFridayWorkDay
                                isSaturdayWorkDay
                                isSundayWorkDay
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
            }
        }
    }
}
