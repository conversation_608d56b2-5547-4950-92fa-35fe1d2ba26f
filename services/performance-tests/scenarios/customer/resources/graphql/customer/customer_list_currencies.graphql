query {
    xtremMasterData {
        currency {
            query(first: 16, filter: "{isActive:true}", orderBy: "{name:1}") {
                edges {
                    node {
                        _id
                        isActive
                        name
                        id
                        symbol
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
