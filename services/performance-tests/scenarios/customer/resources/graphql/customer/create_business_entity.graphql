mutation CreateMutation($customer_new_id: String, $customer_new_id_name: String, $customer_tax_number: String) {
    xtremMasterData {
        businessEntity {
            create(
                data: {
                    id: $customer_new_id
                    name: $customer_new_id_name
                    currency: "#EUR"
                    country: "#FR"
                    taxIdNumber: $customer_tax_number
                    image: null
                    siret: "82214601500019"
                    addresses: [
                        {
                            isActive: true
                            name: "Main addr"
                            addressLine1: "1 place truc"
                            city: "Tours"
                            region: "Centre"
                            postcode: "37100"
                            country: "#FR"
                            isPrimary: true
                            addressLine2: ""
                            locationPhoneNumber: ""
                            _action: "create"
                        }
                    ]
                }
            ) {
                id
                _id
                primaryAddress {
                    _id
                }
            }
        }
    }
}
