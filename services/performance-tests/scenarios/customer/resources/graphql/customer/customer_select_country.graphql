query {
    xtremStructure {
        country {
            query(filter: "{_or:[{name:{_regex:'France',_options:'i'}}]}", orderBy: "{name:1}") {
                edges {
                    node {
                        _id
                        name
                        id
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
}
