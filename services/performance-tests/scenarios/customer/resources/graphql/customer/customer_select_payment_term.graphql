query ReadQuery($payment_term: IntReference) {
    xtremMasterData {
        customer {
            lookups(
                data: {
                    postingClass: null
                    paymentTerm: $payment_term
                    billToAddress: null
                    billToCustomer: null
                    primaryAddress: null
                    businessEntity: null
                    isActive: false
                    minimumOrderAmount: 400
                    isOnHold: false
                }
            ) {
                paymentTerm(filter: "{_or:[{name:{_regex:'30 days EoM',_options:'i'}}]}", orderBy: "{name:1}") {
                    edges {
                        node {
                            _id
                            name
                            description
                        }
                        cursor
                    }
                    pageInfo {
                        startCursor
                        endCursor
                        hasPreviousPage
                        hasNextPage
                    }
                }
            }
        }
    }
}
