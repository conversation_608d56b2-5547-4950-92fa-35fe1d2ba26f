mutation CreateMutation($customer_new_id: String, $customer_tax_number: String) {
    xtremMasterData {
        businessEntity {
            create(
                data: {
                    id: $customer_new_id
                    name: "sample customer"
                    currency: "#EUR"
                    country: "#FR"
                    taxIdNumber: undefined
                    image: null
                    addresses: [
                        {
                            isActive: true
                            name: "Main addr"
                            addressLine1: "1 place truc"
                            city: "Tours"
                            region: "Centre"
                            postcode: "37100"
                            country: "#FR"
                            isPrimary: true
                            addressLine2: ""
                            locationPhoneNumber: ""
                            _action: "create"
                        }
                    ]
                }
            ) {
                _id
            }
        }
    }
}
