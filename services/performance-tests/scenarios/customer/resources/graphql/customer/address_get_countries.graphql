query {
    Country: __type(name: "Country") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Country_Input: __type(name: "Country_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
}
