query {
    RegionLabel: __type(name: "<PERSON><PERSON>abel") {
        enumValues {
            name
        }
    }
    ZipLabel: __type(name: "<PERSON><PERSON><PERSON><PERSON><PERSON>") {
        enumValues {
            name
        }
    }
    Title: __type(name: "Title") {
        enumValues {
            name
        }
    }
    ContactRole: __type(name: "ContactRole") {
        enumValues {
            name
        }
    }
    PostingClassType: __type(name: "PostingClassType") {
        enumValues {
            name
        }
    }
    rootNode: xtremMasterData {
        customer {
            getDefaults(data: {}) {
                deliveryAddresses {
                    query(first: 20) {
                        edges {
                            node {
                                incoterm__name: incoterm {
                                    name
                                    _id
                                }
                                deliveryMode__name: deliveryMode {
                                    name
                                    _id
                                }
                                shipmentSite__name: shipmentSite {
                                    name
                                    _id
                                }
                                shipToAddress__name: shipToAddress {
                                    name
                                    _id
                                }
                                _id
                                isActive
                                isPrimary
                                deliveryLeadTime
                                isMondayWorkDay
                                isTuesdayWorkDay
                                isWednesdayWorkDay
                                isThursdayWorkDay
                                isFridayWorkDay
                                isSaturdayWorkDay
                                isSundayWorkDay
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                items {
                    query(first: 20) {
                        edges {
                            node {
                                salesUnit__name: salesUnit {
                                    name
                                    id
                                    _id
                                }
                                item__name: item {
                                    stockUnit__name: stockUnit {
                                        name
                                        _id
                                    }
                                    name
                                    id
                                    _id
                                }
                                _id
                                isActive
                                id
                                name
                                salesUnitToStockUnitConversion
                                minimumSalesQuantity
                                maximumSalesQuantity
                            }
                            cursor
                        }
                        pageInfo {
                            startCursor
                            endCursor
                            hasPreviousPage
                            hasNextPage
                        }
                    }
                }
                postingClass {
                    type
                    name
                    _id
                }
                paymentTerm {
                    name
                    description
                    _id
                }
                billToAddress {
                    country__id: country {
                        name
                        id
                        _id
                    }
                    _id
                    name
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                billToCustomer {
                    businessEntity {
                        name
                    }
                    primaryAddress__name: primaryAddress {
                        _id
                        name
                    }
                    paymentTerm__name: paymentTerm {
                        _id
                        name
                    }
                    businessEntity__id: businessEntity {
                        _id
                        id
                    }
                    _id
                    name
                    id
                }
                primaryAddress {
                    country__id: country {
                        name
                        id
                        _id
                    }
                    _id
                    name
                    addressLine1
                    addressLine2
                    city
                    region
                    postcode
                    locationPhoneNumber
                }
                businessEntity {
                    image {
                        value
                    }
                    currency__name: currency {
                        name
                        id
                        decimalDigits
                        symbol
                        _id
                    }
                    country__name: country {
                        name
                        id
                        regionLabel
                        zipLabel
                        _id
                    }
                    _id
                    name
                    id
                    isActive
                    taxIdNumber
                    isCustomer
                    isSupplier
                    isSite
                }
                isActive
                minimumOrderAmount
                isOnHold
            }
        }
    }
    navigationPanelItems: xtremMasterData {
        customer {
            query(orderBy: "{ businessEntity: { name: 1 }}") {
                edges {
                    node {
                        businessEntity__id: businessEntity {
                            id
                            _id
                        }
                        businessEntity__name: businessEntity {
                            name
                            id
                            _id
                        }
                        _id
                    }
                    cursor
                }
                pageInfo {
                    startCursor
                    endCursor
                    hasPreviousPage
                    hasNextPage
                }
            }
        }
    }
    BusinessEntity: __type(name: "BusinessEntity") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    BusinessEntity_Input: __type(name: "BusinessEntity_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Country: __type(name: "Country") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Country_Input: __type(name: "Country_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Currency: __type(name: "Currency") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Currency_Input: __type(name: "Currency_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    BusinessEntityAddress: __type(name: "BusinessEntityAddress") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    BusinessEntityAddress_Input: __type(name: "BusinessEntityAddress_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    BusinessEntityAddressContact: __type(name: "BusinessEntityAddressContact") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    BusinessEntityAddressContact_Input: __type(name: "BusinessEntityAddressContact_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Customer: __type(name: "Customer") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Customer_Input: __type(name: "Customer_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    PaymentTerm: __type(name: "PaymentTerm") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    PaymentTerm_Input: __type(name: "PaymentTerm_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    PostingClass: __type(name: "PostingClass") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    PostingClass_Input: __type(name: "PostingClass_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    ItemCustomer: __type(name: "ItemCustomer") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    ItemCustomer_Input: __type(name: "ItemCustomer_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Item: __type(name: "Item") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Item_Input: __type(name: "Item_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    UnitOfMeasure: __type(name: "UnitOfMeasure") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    UnitOfMeasure_Input: __type(name: "UnitOfMeasure_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    CustomerDeliveryAddress: __type(name: "CustomerDeliveryAddress") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    CustomerDeliveryAddress_Input: __type(name: "CustomerDeliveryAddress_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Site: __type(name: "Site") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Site_Input: __type(name: "Site_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    DeliveryMode: __type(name: "DeliveryMode") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    DeliveryMode_Input: __type(name: "DeliveryMode_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Incoterm: __type(name: "Incoterm") {
        fields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
    Incoterm_Input: __type(name: "Incoterm_Input") {
        inputFields {
            name
            type {
                ofType {
                    name
                }
                name
                enumValues {
                    name
                }
            }
        }
    }
}
