query ReadQuery($filter: String) {
    xtremMasterData {
        businessEntityAddress {
            query(filter: $filter) {
                edges {
                    node {
                        _id
                        isActive
                        isPrimary
                        name
                        addressLine1
                        addressLine2
                        city
                        region
                        postcode
                        country {
                            _id
                            id
                            name
                            regionLabel
                            zipLabel
                        }
                        locationPhoneNumber
                    }
                    cursor
                }
                pageInfo {
                    hasNextPage
                    endCursor
                    hasPreviousPage
                    startCursor
                }
            }
        }
    }
}
