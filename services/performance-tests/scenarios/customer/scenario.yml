scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/customer/*'
    flow:
      - function: 'generatePayloadVariables' #  fixtures : customer_new_id, customer_uk_tax_id (constraint : unique value per be)
      - log: 'Current environment is set to: {{ $environment }}'
      - post:
          url: /api
          json:
            query: customer_object_definition.graphql
          expect:
            - hasProperty: data.Customer.fields[1].name
            - notHasProperty: errors
            - statusCode: 200
          name: customer_object_definition
      - log: Check if business-entity-id exists
      - post:
          url: /api
          json:
            query: business_entity_check_id_exists.graphql
            variables:
              filter: "{id:{_regex:'^{{ customer_new_id }}$',_options:'i'}}"
          expect:
            - notHasProperty: data.xtremMasterData.businessEntity.query.edges[0].node
            - notHasProperty: errors
            - statusCode: 200
          name: business-entity_check_id_exists
      - log: Creating a customer having the id {{ customer_new_id }}
      - log: Create list countries
      - post:
          url: /api
          json:
            query: customer_create_list_countries.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: customer_create_list_countries
      - log: List currencies
      - post:
          url: /api
          json:
            query: customer_list_currencies.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: customer_list_currencies
      - log: Select currency
      - post:
          url: /api
          json:
            query: customer_select_currency.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: customer_select_currency
      - log: Address tab metadata
      - post:
          url: /metadata
          json:
            query: metadata_address_tab.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: metadata_address_tab
      - log: Create list payment term
      - post:
          url: /api
          json:
            query: customer_create_list_payment_term.graphql
          headers:
            accept: 'application/json'
          expect:
            - notHasProperty: errors
            - statusCode: 200
          capture:
            json: 'data.xtremMasterData.customer.lookups.paymentTerm.edges[0].node._id'
            as: payment_term
          name: customer_create_list_payment_term
      - log: Select payment term
      - post:
          url: /api
          json:
            query: customer_select_payment_term.graphql
            variables:
              payment_term: _id:{{ payment_term }}
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: customer_select_payment_term
      - log: Creation fail
      - post:
          url: /api
          json:
            query: customer_creation_fail.graphql
            variables:
              customer_new_id: '{{ customer_new_id }}'
              customer_tax_number: '{{ customer_fr_tax_id }}'
          expect:
            - statusCode: 400
            - hasProperty: errors
          name: customer_creation_fail
      - log: Create business entity
      # - log: "DEBUG - customer_tax_number: '{{ customer_fr_tax_id }}'"
      - post:
          url: /api
          json:
            query: create_business_entity.graphql
            variables:
              customer_new_id: '{{ customer_new_id }}'
              customer_new_id_name: 'name:{{ customer_new_id }}'
              customer_tax_number: '{{ customer_fr_tax_id }}'
          expect:
            - hasProperty: data.xtremMasterData.businessEntity.create._id
            - notHasProperty: errors
            - statusCode: 200
            - equals:
                - '{{ id }}'
                - '{{ customer_new_id }}'
          capture:
            - json: data.xtremMasterData.businessEntity.create._id
              as: created_business_entity_id
            - json: data.xtremMasterData.businessEntity.create.primaryAddress._id
              as: created_business_entity_primary_address_id
            - json: 'data.xtremMasterData.businessEntity.create.id'
              as: 'id'
          name: create_business_entity
      - log: Get delivery address
      - get:
          url: /api
          json:
            query: customer_delivery_address.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          capture:
            - json: data.xtremMasterData.customerDeliveryAddress.query.edges[0]
              as: customer_delivery_addresses
      - log: Create customer
      - post:
          url: /api
          json:
            query: create_customer.graphql
            variables:
              business_entity_id: _id:{{ created_business_entity_id }}
              business_entity_primary_address_id: _id:{{ created_business_entity_primary_address_id }}
              payment_term: _id:{{ payment_term }}
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: create_customer
      - log: Read created business entity
      - post:
          url: /api
          json:
            query: read_created_business_entity.graphql
            variables:
              filter: "{businessEntity:'_id:{{ created_business_entity_id }}'}"
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: read_created_business_entity
