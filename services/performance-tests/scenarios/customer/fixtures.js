module.exports = {
    generatePayloadVariables: generatePayloadVariables,
};
const tools = require('../../shared/tools');
/**
 * <PERSON> called from the scenario loop in order to set up variables that have uniqueness constrains
 * See https://www.artillery.io/docs/guides/guides/http-reference#function-steps-in-a-flow
 * @param {*} context
 * @param {*} events
 * @param {*} done
 * @returns
 */
function generatePayloadVariables(context, events, done) {
    // Set the "query" variables for the virtual user.
    context.vars['customer_fr_tax_id'] = tools.generateId('FR', 11); // FR12345678901
    context.vars['customer_new_id'] = tools.generateId('CUSTOMER_', 4);
    return done();
}
