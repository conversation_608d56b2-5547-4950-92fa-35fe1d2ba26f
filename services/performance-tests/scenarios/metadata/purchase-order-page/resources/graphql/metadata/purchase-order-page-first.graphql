query {
    pages(filter: { packageOrPage: "@sage/xtrem-purchasing/PurchaseOrder", exactMatch: true }) {
        content
        extensions
        plugins
        access {
            node
            bindings {
                name
                status
            }
        }
        strings {
            key
            content
        }
    }
    strings(filter: { packageOrPage: "@sage/xtrem-ui,@sage/xtrem-date-time" }) {
        key
        content
    }
}
