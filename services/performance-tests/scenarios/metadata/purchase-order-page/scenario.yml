scenarios:
  - xtrem:
      resources:
        - '{{ $processEnvironment.SCENARIO_PATH }}/resources/graphql/metadata/*'
    flow:
      - post:
          beforeRequest: 'checkDuration'
          url: /metadata
          json:
            query: pages.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: metadata-pages
      - post:
          beforeRequest: 'checkDuration'
          url: /metadata
          json:
            query: installed-packages.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: metadata-installed-packages
      - post:
          beforeRequest: 'checkDuration'
          url: /metadata
          json:
            query: stickers.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: metadata-stickers
      - post:
          beforeRequest: 'checkDuration'
          url: /metadata
          json:
            query: demo-persona-stickers.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: metadata-demo-persona-stickers
      - post:
          beforeRequest: 'checkDuration'
          url: /metadata
          json:
            query: purchase-order-page-first.graphql
          expect:
            - notHasProperty: errors
            - statusCode: 200
          name: metadata-purchase-order-page-first
      - loop:
          - think: 0.5
          - post:
              beforeRequest: 'checkDuration'
              url: /metadata
              json:
                query: purchase-order-line-inquiry-page.graphql
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: purchase-order-line-inquiry-page
          - think: 0.5
          - post:
              beforeRequest: 'checkDuration'
              url: /metadata
              json:
                query: purchase-order-page-next.graphql
              expect:
                - notHasProperty: errors
                - statusCode: 200
              name: metadata-purchase-order-page-next
        whileTrue: 'notExceedsDuration'
