config:
  plugins:
    xtrem: {}
    ensure: {}
    expect: {}
    metrics-by-endpoint:
      # Group metrics by request name rather than URL:
      useOnlyRequestNames: true
  phases:
    - duration: '{{ $processEnvironment.RAMP_UP }}'
      arrivalCount: '{{ $processEnvironment.TOTAL_USERS }}'
      name: 'Ramp up {{ $processEnvironment.TOTAL_USERS }} users in {{ $processEnvironment.RAMP_UP }} seconds'
    - pause: '{{ $processEnvironment.DURATION }}'
      name: 'Stress {{ $processEnvironment.TOTAL_USERS }} users for {{ $processEnvironment.DURATION }} seconds'
  environments:
    local:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: http://localhost:8240
      payload:
        delimiter: ';'
        path: '../../../seed/input/01-user.csv'
        fields:
          - 'email'
        order: sequence
        skipHeader: true
    devops:
      xtrem:
        loginManager: 'UnsecureDevLogin'
      target: '{{ $processEnvironment.CLUSTER_URL }}'
      variables:
        tenant: '{{ $processEnvironment.TENANT_ID }}'
        cluster: '{{ $processEnvironment.CLUSTER }}'
      payload:
        path: '../../../seed/input/01-user.csv'
        fields:
          - 'email'
        order: iterate
        skipHeader: true
        delimiter: ';'
