module.exports = {
    generateId: generateId,
};
const crypto = require('crypto'); // Required in order to fix sonarcloud code smell

function generateId(prefix = '', length = 9) {
    const min = Math.pow(10, Math.max(length - 1, 0));
    const max = Math.floor(Number.parseInt('9'.padEnd(length, '9'), 10));
    return prefix && prefix !== ''
        ? `${prefix}${Math.floor(crypto.randomInt(min, max))}`
        : `${Math.floor(crypto.randomInt(min, max))}`;
}
