# Artillery Readme

## Install artillery and all the used plugins in the script

```bash
pnpm i -g artillery artillery-plugin-ensure@1.15.0 artillery-plugin-expect artillery-plugin-metrics-by-endpoint
```

Or use the install.sh in ./scripts/install.sh

## Load demo data (locally) and run xtrem server and

## For testing purpose locally, load test data:

Explained in : /xtrem-dev/xtrem-third/platform/performance-tests/README.md

## Make sure xtrem project is built and xtrem is started with unsecure dev login:

```bash
pnpm run start:unsecuredevlogin
```

## run test script

### From the services/performance-test folder - run scenario test example : sales-order/create

```bash
  ../../scripts/artillery/scenarios-test-run.sh  --scenario sales-order/create --total-users 1 --ramp-up 15 --duration 30
```

## available scenarios

-   sales-order/create
-   supplier/create-supplier-with-no-business-entity
-   customer
