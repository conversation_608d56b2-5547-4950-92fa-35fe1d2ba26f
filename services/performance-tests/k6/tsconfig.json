{"compilerOptions": {"target": "es5", "moduleResolution": "node", "module": "commonjs", "noEmit": true, "outDir": "build", "allowJs": true, "removeComments": false, "strict": true, "noImplicitAny": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "skipLibCheck": true, "strictNullChecks": true}, "references": [{"path": "../../../platform/shared/xtrem-async-helper"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/system/xtrem-communication/api"}, {"path": "../../applications/xtrem-distribution/api"}, {"path": "../../shared/xtrem-finance-data/api"}, {"path": "../../applications/xtrem-manufacturing/api"}, {"path": "../../shared/xtrem-master-data/api"}, {"path": "../../shared/xtrem-mrp-data/api"}, {"path": "../../applications/xtrem-purchasing/api"}, {"path": "../../applications/xtrem-sales/api"}, {"path": "../../applications/xtrem-stock/api"}, {"path": "../../shared/xtrem-stock-data/api"}, {"path": "../../../platform/system/xtrem-system/api"}, {"path": "../../shared/xtrem-technical-data/api"}, {"path": "../../../platform/front-end/xtrem-ui"}]}