import { group } from 'k6';
import { Gauge } from 'k6/metrics';
import { Options } from 'k6/options';
import {
    createItem,
    createItemCategory,
    deleteItem,
    queryItems,
    readItem,
    readItemCategory,
} from '../../queries/master-data';
import { TenantConnector } from '../../tooling/tenant-connector';
import { randomString } from '../../tooling/utils';

export const options: Options = {
    vus: 1,
    duration: '10s',
    thresholds: {
        create_item_duration: ['value<60000'],
        read_item_duration: ['value<60000'],
        delete_item_duration: ['value<60000'],
        query_item_duration: ['value<60000'],
    },
    throw: false,
};
const createItemDuration = new Gauge('create_item_duration', true);
const readItemDuration = new Gauge('read_item_duration', true);
const deleteItemDuration = new Gauge('delete_item_duration', true);
const queryItemDuration = new Gauge('query_item_duration', true);

const numberOfCategories = 10;

export async function setup() {
    const tenant = new TenantConnector();
    const categories: Array<{ _id: string; name: string }> = [];

    // create some item category
    for (let i = 0; i < numberOfCategories; i++) {
        const id = `Ca${i}`;
        const read = await readItemCategory(tenant, id).execute();
        if (read) {
            // category already exists
            categories.push(read);
            continue;
        }
        const create = createItemCategory(tenant, { id, name: `category ${i}` });
        const categoryCreated = await create.execute();
        console.log('Category created :', categoryCreated);
        categories.push(categoryCreated);
    }

    return { categories };
}

export default function (data: { categories: Array<{ _id: string; name: string }> }) {
    const tenant = new TenantConnector();
    const { categories } = data;

    group('create items', async function () {
        const itemCreation = createItem(tenant, {
            id: randomString(10),
            name: `item ${randomString(10)}`,
            category: categories[0]._id,
            stockUnit: '#EACH',
            isManufactured: true,
        });
        const itemCreated = await itemCreation.execute();
        createItemDuration.add(itemCreation.timings.duration);
        const read = readItem(tenant, itemCreated._id);
        const itemRead = await read.execute();
        readItemDuration.add(read.timings.duration);
        const itemDeleted = deleteItem(tenant, itemRead._id);
        await itemDeleted.execute();
        deleteItemDuration.add(itemDeleted.timings.duration);
    });

    group('query items', async function () {
        const queryItem = queryItems(tenant, {});
        await queryItem.execute();
        queryItemDuration.add(queryItem.timings.duration);
    });
}
