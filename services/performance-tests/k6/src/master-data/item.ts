import { Site } from '@sage/xtrem-system-api';
import { group, sleep } from 'k6';
import { Options } from 'k6/options';
import { addItemSite, addItemSiteCost, executeCreateItem, ItemSelection } from '../../queries/master-data';
import { getSite } from '../../queries/system';
import { generateJsonSummary, generateTextSummary, generateWebSummary } from '../../tooling/report';
import { TenantConnector } from '../../tooling/tenant-connector';
import { randomString } from '../../tooling/utils';

export const options: Options = {
    vus: 1,
    iterations: 1,
    thresholds: {
        'group_duration{group:::01 - Get a site}': ['max>=0'],
        'group_duration{group:::02 - Create an item}': ['max>=0'],
        'group_duration{group:::03 - Add a site to the item}': ['max>=0'],
    },
    throw: false,
};

export default async function () {
    const tenant = new TenantConnector();
    const waitBeetween = 1;
    let site: Site;
    let createdItem: ItemSelection;

    await group('01 - Get a site', async function () {
        console.info('*** Get a site ***');
        site = await getSite(tenant, '#US002', {});
    });
    sleep(waitBeetween);

    await group('02 - Create an item', async function () {
        console.info('*** Create an item ***');
        createdItem = await executeCreateItem(tenant, {
            id: randomString(10),
            name: `item ${randomString(10)}`,
            description: `Description ${randomString(50)}`,
            isSold: true,
            isBought: true,
            isManufactured: true,
            isStockManaged: true,
            salesUnit: '#EACH',
            purchaseUnit: '#EACH',
            stockUnit: '#EACH',
        });
    });
    sleep(waitBeetween);

    await group('03 - Add a site to the item', async function () {
        console.info(`*** Add a site to the item ${createdItem.id} ***`);
        await addItemSite(tenant, createdItem, site);
    });

    await group('04 - add Item cost ', async function () {
        console.info(`*** Add Item cost for item ${createdItem.id} ***`);
        await addItemSiteCost(tenant, { _id: `#${createdItem.id}|${site.id}`, item: createdItem });
    });

    // add a supplier to the item

    // add a customer to the item
}

export async function handleSummary(result: any) {
    const { fileName, data } = generateJsonSummary(result, 'item');
    const { webFileName, webData } = await generateWebSummary(result, 'item');
    return {
        stdout: generateTextSummary(result, { enableColors: true }),
        [fileName]: JSON.stringify(data, null, 2),
        [webFileName]: webData,
    };
}
