import { Item } from '@sage/xtrem-master-data-api';
import { Site } from '@sage/xtrem-system-api';
import { group, sleep } from 'k6';
import { Options } from 'k6/options';
import { addItemSite, createMrpItem, getRandomItems } from '../../queries/master-data';
import { getSite } from '../../queries/system';
import { addItemToBom, createBomRequest } from '../../queries/technical-data';
import { TenantConnector } from '../../tooling/tenant-connector';

export const options: Options = {
    vus: 5,
    duration: '10m',
    thresholds: {
        'group_duration{group:::01 - Get a site}': ['max>=0'],
        'group_duration{group:::02 - Create an item}': ['max>=0'],
        'group_duration{group:::03 - Add a site to the item}': ['max>=0'],
    },
    throw: false,
};

export default async function () {
    const tenant = new TenantConnector();
    const waitBeetween = 5;
    const bomCreationNb = 10;
    const componentCreationNb = 5;

    let site: Site;
    let createdItem: Item;
    let bom: { _id: string };

    await group('01 - Get a site', async function () {
        console.info('*** Get a site ***');
        site = await getSite(tenant, '#US002', {});
    });
    sleep(waitBeetween);

    await group('02 - Create an item', async function () {
        createdItem = await createMrpItem(tenant, { site });
    });
    sleep(waitBeetween);

    await group('03 - Create a BOM', async function () {
        console.info('*** Create a BOM ***');

        const randomItems = Array.from(
            await getRandomItems(tenant, bomCreationNb, {
                isStockManaged: true,
                _id: { _ne: createdItem?._id ?? '' },
            }),
        );

        const components = [];
        for (const item of randomItems) {
            await addItemSite(tenant, item, site);
            components.push({
                item: item?._id ?? '',
                linkQuantity: 1,
                unit: '#EACH',
            });
        }
        bom = await createBomRequest(tenant, { item: createdItem, site, components });
    });
    sleep(waitBeetween);

    await group('04 - Add components to the BOM', async function () {
        console.info('*** Add components to the BOM ***');
        await addItemToBom(tenant, {
            count: componentCreationNb,
            isCompomentCreated: true,
            site,
            _id: bom._id,
        });
    });
}
