import { Graph } from '@sage/xtrem-client';
import { GraphApi as GraphApiManufacturing, WorkOrder } from '@sage/xtrem-manufacturing-api';
import { Item } from '@sage/xtrem-master-data-api';
import { PurchaseOrder } from '@sage/xtrem-purchasing-api';
import { GraphApi as GraphApiSales } from '@sage/xtrem-sales-api';
import { StockReceiptInput } from '@sage/xtrem-stock-api';
import { Site } from '@sage/xtrem-system-api';
import { group, sleep } from 'k6';
import { Options } from 'k6/options';
import { allocateWorkOrder, createWorkOrder, waitWorkOrderAllocation } from '../../queries/manufacturing';
import {
    addItemSite,
    createMrpItem,
    getRandomItems,
    getRandomSupplier,
    ItemSelection,
    SupplierType,
} from '../../queries/master-data';
import { runMrpCalculation } from '../../queries/mrp-data';
import { confirmPurchaseOrder, createPurchaseOrder, purchaseOrderLineInquiry } from '../../queries/purchase';
import {
    confirmSalesOrder,
    createSalesOrder,
    CreateSalesOrderType,
    queryCustomer,
    salesOrderLineInquiry,
} from '../../queries/sales';
import { checkPostStockReceipt, createStockReceipt, postStockReceipt, stockJournalInquiry } from '../../queries/stock';
import { getSite, trackNotification } from '../../queries/system';
import { addItemToBom, createBomRequest } from '../../queries/technical-data';
import { CustomRequest, fetcher } from '../../tooling/functions';
import { generateJsonSummary, generateTextSummary, generateWebSummary } from '../../tooling/report';
import { TenantConnector } from '../../tooling/tenant-connector';
import { randomInt } from '../../tooling/utils';

const graphPurchasing = new Graph<GraphApiManufacturing>({ fetcher });
const graphSales = new Graph<GraphApiSales>({ fetcher });

export const options: Options = {
    vus: 5,
    iterations: 50,
    duration: '120m',
    thresholds: {
        'group_duration{group:::01 - Get a site}': ['max>=0'],
        'group_duration{group:::02 - Create an item}': ['max>=0'],
        'group_duration{group:::03 - Create a BOM}': ['max>=0'],
        'group_duration{group:::04 - Add components to the BOM}': ['max>=0'],
        'group_duration{group:::05 - Add stock for all components}': ['max>=0'],
        'group_duration{group:::05a Post the receipt}': ['max>=0'],
        'group_duration{group:::06 - Wait for the post}': ['max>=0'],
        'group_duration{group:::07 - Create a work order}': ['max>=0'],
        'group_duration{group:::08 - Allocate the work order}': ['max>=0'],
        'group_duration{group:::09 - Wait for work order allocation}': ['max>=0'],
        'group_duration{group:::10 - Create a purchase order}': ['max>=0'],
        'group_duration{group:::11 - Confirm the purchase order}': ['max>=0'],
        'group_duration{group:::12 - Create a receipt from the order}': ['max>=0'],
        'group_duration{group:::13 - Perform a purchase order line inquiry}': ['max>=0'],
        'group_duration{group:::14 - Create the sales order}': ['max>=0'],
        'group_duration{group:::15 - confirm the sales order}': ['max>=0'],
        'group_duration{group:::16 - Allocate stock}': ['max>=0'],
        'group_duration{group:::17 - Perform a sales order line inquiry}': ['max>=0'],
        'group_duration{group:::18 - Perform a stock journal inquiry}': ['max>=0'],
        'group_duration{group:::19 - Perform a journal entry inquiry}': ['max>=0'],
        'group_duration{group:::20 - Perform an MRP calculation}': ['max>=0'],
        'group_duration{group:::21 - Wait for mrp calculation}': ['max>=0'],
    },
    throw: false,
};

export default async function () {
    const tenant = new TenantConnector();
    console.log(`Version : ${await tenant.getVersion()}`);

    let createdItem: Item;
    let bom: { _id: string };
    let workOrder: WorkOrder;
    let site: Site;
    let supplier: SupplierType;
    let purchaseOrder: Partial<PurchaseOrder>;
    let salesOrder: CreateSalesOrderType;
    const itemsToAddStock: ItemSelection[] = [];
    let stockReceipt: StockReceiptInput;
    let trackingId: string;

    const bomCreationNb = 10;
    const componentCreationNb = 5; // 100
    const purchaseOrderLinesNb = 20; // 80 lines
    const salesOrderLinesNb = 20; // 80 lines
    const waitBeetween = 5;

    // Randomize the start for each iteration
    const sleepTime = randomInt({ min: 0, max: 30 });
    sleep(sleepTime);

    await group('01 - Get a site', async function () {
        console.info('*** Get a site ***');
        site = await getSite(tenant, '#US002', {});
    });
    sleep(waitBeetween);

    await group('02 - Create an item', async function () {
        console.info('*** Create an item ***');
        createdItem = await createMrpItem(tenant, { site });
    });
    sleep(waitBeetween);

    await group('02 - Create some items', async function () {
        console.info('*** Create some items ***');
        for (let i = 0; i < componentCreationNb; i++) {
            await createMrpItem(tenant, { site });
        }
    });
    sleep(waitBeetween);

    await group('03 - Create a BOM', async function () {
        console.info('*** Create a BOM ***');

        const randomItems = Array.from(
            await getRandomItems(tenant, bomCreationNb, {
                isStockManaged: true,
                isPhantom: false,
                _id: { _ne: createdItem?._id ?? '' },
            }),
        );

        const components = [];
        for (const item of randomItems) {
            if (!itemsToAddStock.some(i => i._id === item._id)) {
                await addItemSite(tenant, item, site);
            }
            components.push({
                item: item?._id ?? '',
                linkQuantity: 1,
                unit: '#EACH',
            });
            itemsToAddStock.push(item);
        }
        bom = await createBomRequest(tenant, { item: createdItem, site, components });
    });
    sleep(waitBeetween);

    await group('04 - Add components to the BOM', async function () {
        console.info('*** Add components to the BOM ***');
        const componentsAdded = await addItemToBom(tenant, {
            count: componentCreationNb,
            isCompomentCreated: true,
            site,
            _id: bom._id,
        });
        itemsToAddStock.push(...componentsAdded);
    });
    sleep(waitBeetween);

    await group('05 - Add stock for all components', async function () {
        console.info('*** Add stock for all components ***');
        stockReceipt = await createStockReceipt(tenant, itemsToAddStock, site);
    });
    sleep(waitBeetween);

    await group('05a Post the receipt', async function () {
        console.info('*** Post the receipt ***');
        await postStockReceipt(tenant, { _id: stockReceipt._id ?? '' });
    });

    await group('06 - Wait for the post', async function () {
        console.info('*** Wait for the post ***');
        const checkStockReceipt = await checkPostStockReceipt(tenant, { _id: stockReceipt._id ?? '' });
        console.info('Check the post result:', JSON.stringify(checkStockReceipt));
    });
    sleep(waitBeetween);

    await group('07 - Create a work order', async function () {
        console.info('*** Create a work order ***');
        workOrder = await createWorkOrder(tenant, { item: createdItem, site });
    });
    sleep(waitBeetween);

    await group('08 - Allocate the work order', async function () {
        console.info('*** Allocate the work order ***');
        await allocateWorkOrder(tenant, workOrder);
    });

    await group('09 - Wait for work order allocation', async function () {
        console.info('*** Wait for work order allocation ***');
        const order = await waitWorkOrderAllocation(tenant, workOrder, 300);
        console.info('Work order allocation status:', order.allocationStatus);
    });
    sleep(waitBeetween);

    await group('10 - Create a purchase order', async function () {
        console.info('*** Create a purchase order ***');
        supplier = await getRandomSupplier(tenant, { isActive: true });
        purchaseOrder = await createPurchaseOrder(tenant, { site, supplier, numberOfLines: purchaseOrderLinesNb });
    });
    sleep(waitBeetween);

    await group('11 - Confirm the purchase order', async function () {
        console.info('*** Confirm the purchase order ***');
        await confirmPurchaseOrder(tenant, { _id: purchaseOrder._id ?? '', site });
    });
    sleep(waitBeetween);

    await group('12 - Create a receipt from the order', async function () {
        console.info('*** Create a receipt from the order ***');
        const createdReceipts = new CustomRequest(
            graphPurchasing
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.createPurchaseReceipt({ _id: true }, { document: purchaseOrder._id ?? '' }),
            tenant,
        ).execute();
        console.info(`${(await createdReceipts).length} receipts created`);
    });
    sleep(waitBeetween);

    await group('13 - Perform a purchase order line inquiry', async function () {
        console.info('*** Perform a purchase order line inquiry ***');
        const { pageInfo } = await purchaseOrderLineInquiry(tenant, { filter: {} });

        if (pageInfo?.endCursor) {
            await purchaseOrderLineInquiry(tenant, { first: 20, after: pageInfo.endCursor ?? '' });
        }
    });
    sleep(waitBeetween);

    await group('14 - Create the sales order', async function () {
        console.info('*** Create the sales order ***');
        const customer = await queryCustomer(tenant, { isActive: true, currency: { id: { _in: ['USD', 'EUR'] } } });
        salesOrder = await createSalesOrder(tenant, { customer, site, salesOrderLinesNb });
    });
    sleep(waitBeetween);

    await group('15 - confirm the sales order', async function () {
        await confirmSalesOrder(tenant, salesOrder);
    });
    sleep(waitBeetween);

    let processIdAllocateSalesOrder: string;

    await group('16 - Allocate stock', async function () {
        processIdAllocateSalesOrder = await new CustomRequest(
            graphSales.node('@sage/xtrem-sales/SalesOrder').mutations.requestAutoAllocation(true, {
                salesOrder: salesOrder._id,
                requestType: 'allocation',
            }),
            tenant,
        ).execute();
    });

    await group('Wait for allocation', async function () {
        console.info('*** Wait for allocation ***');

        const { status } = await trackNotification(tenant, processIdAllocateSalesOrder);
        console.log(` status: ${status}`);

        const salesOrderStatus = await new CustomRequest(
            graphSales.node('@sage/xtrem-sales/SalesOrder').read({ allocationStatus: true }, salesOrder._id ?? ''),
            tenant,
        ).execute();

        if (salesOrderStatus?.allocationStatus !== 'allocated') {
            console.error(`Sales order not allocated ${salesOrderStatus?.allocationStatus}`);
        }
    });
    sleep(waitBeetween);

    await group('17 - Perform a sales order line inquiry', async function () {
        console.info('*** Perform a sales order line inquiry ***');
        const { pageInfo } = await salesOrderLineInquiry(tenant, { filter: {} });

        if (pageInfo?.endCursor) {
            await salesOrderLineInquiry(tenant, { first: 20, after: pageInfo.endCursor ?? '' });
        }
    });
    sleep(waitBeetween);

    await group('18 - Perform a stock journal inquiry', async function () {
        console.info('*** Perform a stock journal inquiry ***');
        const { pageInfo } = await stockJournalInquiry(tenant, { filter: { site: site?._id } });

        if (pageInfo?.endCursor) {
            await stockJournalInquiry(tenant, { first: 20, after: pageInfo.endCursor ?? '' });
        }
    });
    sleep(waitBeetween);

    await group('19 - Perform a journal entry inquiry', async function () {});
    sleep(waitBeetween);

    await group('20 - Perform an MRP calculation', async function () {
        trackingId = await runMrpCalculation(tenant, [site]);
    });

    await group('21 - Wait for mrp calculation', async function () {
        await trackNotification(tenant, trackingId, 300);
    });
}

export async function handleSummary(result: any) {
    const tenant = new TenantConnector();
    const version = await tenant.getVersion();

    const { fileName, data } = generateJsonSummary(result, 'flow', version);
    const { webFileName, webData } = await generateWebSummary(result, 'flow', version);
    const summaries = {
        stdout: generateTextSummary(result, { enableColors: true }, version),
        [fileName]: JSON.stringify(data, null, 2),
        [webFileName]: webData,
    };
    console.log('Summary generated:\n', Object.keys(summaries).join('\n'));
    return summaries;
}
