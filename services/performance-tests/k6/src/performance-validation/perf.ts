import { Graph } from '@sage/xtrem-client';
import { GraphApi as GraphApiManufacturing, WorkOrder } from '@sage/xtrem-manufacturing-api';
import { Item } from '@sage/xtrem-master-data-api';
import { GraphApi as GraphApiSales } from '@sage/xtrem-sales-api';
import { Site } from '@sage/xtrem-system-api';
import { group, sleep } from 'k6';
import { Options } from 'k6/options';
import { allocateWorkOrder, createWorkOrder, waitWorkOrderAllocation } from '../../queries/manufacturing';
import { createMrpItem } from '../../queries/master-data';
import { runMrpCalculation } from '../../queries/mrp-data';
import {
    confirmPurchaseOrder,
    duplicatePurchaseOrder,
    getPurchaseOrder,
    purchaseOrderLineInquiry,
    PurchaseOrderType,
} from '../../queries/purchase';
import {
    confirmSalesOrder,
    createSalesShipment,
    duplicateSalesOrder,
    getSalesOrder,
    requestAutoAllocation,
    salesOrderLineInquiry,
    SalesOrderType,
} from '../../queries/sales';
import { stockJournalInquiry } from '../../queries/stock';
import { getSite, trackNotification } from '../../queries/system';
import { bomType, duplicateBom, getBom } from '../../queries/technical-data';
import { CustomRequest, fetcher, waitForResult } from '../../tooling/functions';
import { generateJsonSummary, generateTextSummary, generateWebSummary } from '../../tooling/report';
import { TenantConnector } from '../../tooling/tenant-connector';
import { randomInt } from '../../tooling/utils';
const graphPurchasing = new Graph<GraphApiManufacturing>({ fetcher });
const graphSales = new Graph<GraphApiSales>({ fetcher });

export const options: Options = {
    vus: 10,
    iterations: 100,
    duration: '120m',
    thresholds: {
        'group_duration{group:::01 - Get a site}': ['max>=0'],
        'group_duration{group:::02 - Create an item}': ['max>=0'],
        'group_duration{group:::03 - Read a BOM}': ['max>=0'],
        'group_duration{group:::04 - Duplicate a BOM}': ['max>=0'],
        'group_duration{group:::05 - Create a work order}': ['max>=0'],
        'group_duration{group:::06 - Allocate the work order}': ['max>=0'],
        'group_duration{group:::07 - Wait for work order allocation}': ['max>=0'],
        'group_duration{group:::08 - Get a purchase order}': ['max>=0'],
        'group_duration{group:::09 - Duplicate a purchase order}': ['max>=0'],
        'group_duration{group:::10 - Confirm the purchase order}': ['max>=0'],
        'group_duration{group:::11 - Create a receipt from the order}': ['max>=0'],
        'group_duration{group:::12 - Perform a purchase order line inquiry}': ['max>=0'],
        'group_duration{group:::13 - Read the sales order}': ['max>=0'],
        'group_duration{group:::14 - Duplicate the sales order}': ['max>=0'],
        'group_duration{group:::15 - confirm the sales order}': ['max>=0'],
        'group_duration{group:::16 - Allocate stock}': ['max>=0'],
        'group_duration{group:::17 - Create a Sales shipment}': ['max>=0'],
        'group_duration{group:::18 - Perform a sales order line inquiry}': ['max>=0'],
        'group_duration{group:::19 - Perform a stock journal inquiry}': ['max>=0'],
        'group_duration{group:::20 - Perform a journal entry inquiry}': ['max>=0'],
        'group_duration{group:::21 - Perform an MRP calculation}': ['max>=0'],
        'group_duration{group:::22 - Wait for mrp calculation}': ['max>=0'],
    },
    throw: false,
};

// https://confluence.sage.com/display/XTREEM/Performance+validation
export default async function () {
    const tenant = new TenantConnector();
    console.log(`Version : ${await tenant.getVersion()}`);

    let createdItem: Item;
    let workOrder: WorkOrder;
    let bomRead: bomType;
    let bom: bomType;
    let site: Site;
    let purchaseOrderRead: PurchaseOrderType;
    let purchaseOrder: PurchaseOrderType;
    let salesOrderRead: SalesOrderType;
    let salesOrder: SalesOrderType;
    let trackingId: string;

    const waitBeetween = 10;

    // Randomize the start for each iteration
    const sleepTime = randomInt({ min: 0, max: 30 });
    sleep(sleepTime);

    // Site name is VN Zihbrvbb Zylp Fjmo id : Q11GVWPWQPQRFDQP
    await group('01 - Get a site', async function () {
        console.info('*** Get a site ***');
        site = await getSite(tenant, '#Q11GVWPWQPQRFDQP', { isManufacturing: true });
    });
    sleep(waitBeetween);

    await group('02 - Create an item', async function () {
        console.info('*** Create an item ***');
        createdItem = await createMrpItem(tenant, { site });
    });
    sleep(waitBeetween);

    await group('03 - Read a BOM', async function () {
        console.info('*** Read a BOM ***');
        bomRead = await getBom(tenant, {
            default: { site: site._id, item: { id: '600100-02HPVM' } },
            filter: { site: site._id, isActive: true },
        });
    });
    sleep(waitBeetween);

    await group('04 - Duplicate a BOM', async function () {
        console.info('*** Duplicate a BOM ***');
        try {
            bom = (await duplicateBom(tenant, bomRead, { item: createdItem, site: site })) as bomType;
        } catch (error) {
            // if not timeout retrow the error
            if (error instanceof Error && error.message.includes('timeout')) {
                console.warn('BOM duplication timed out, retrying...');
            } else {
                throw error;
            }
            console.error('BOM duplication failed:', error);
            bom = await waitForResult(getBom, tenant, { default: { site: site._id, item: createdItem._id } });
        }
        console.info('BOM duplicated:', bom.name);
    });
    sleep(waitBeetween);

    await group('05 - Create a work order', async function () {
        console.info('*** Create a work order ***');
        workOrder = await createWorkOrder(tenant, { item: createdItem, site });
    });
    sleep(waitBeetween);

    await group('06 - Allocate the work order', async function () {
        console.info('*** Allocate the work order ***');
        await allocateWorkOrder(tenant, workOrder);
    });

    await group('07 - Wait for work order allocation', async function () {
        console.info('*** Wait for work order allocation ***');
        const order = await waitWorkOrderAllocation(tenant, workOrder, 300);
        console.info('Work order allocation status:', order.allocationStatus);
    });
    sleep(waitBeetween);

    await group('08 - Get a purchase order', async function () {
        console.info('*** Get a purchase order ***');
        purchaseOrderRead = await getPurchaseOrder(tenant, {
            default: { number: 'PO2400015' },
            filter: { site: site._id },
        });
    });
    sleep(waitBeetween);

    await group('09 - Duplicate a purchase order', async function () {
        console.info('*** Duplicate a purchase order ***');
        purchaseOrder = (await duplicatePurchaseOrder(tenant, purchaseOrderRead)) as PurchaseOrderType;
    });
    sleep(waitBeetween);

    await group('10 - Confirm the purchase order', async function () {
        console.info('*** Confirm the purchase order ***');
        await confirmPurchaseOrder(tenant, { _id: purchaseOrder._id ?? '', site });
    });
    sleep(waitBeetween);

    await group('11 - Create a receipt from the order', async function () {
        console.info('*** Create a receipt from the order ***');
        const createdReceipts = new CustomRequest(
            graphPurchasing
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.createPurchaseReceipt({ _id: true }, { document: purchaseOrder._id ?? '' }),
            tenant,
        ).execute();
        console.info(`${(await createdReceipts).length} receipts created`);
    });
    sleep(waitBeetween);

    await group('12 - Perform a purchase order line inquiry', async function () {
        console.info('*** Perform a purchase order line inquiry ***');
        const { pageInfo } = await purchaseOrderLineInquiry(tenant, { filter: {} });

        if (pageInfo?.endCursor) {
            await purchaseOrderLineInquiry(tenant, { first: 20, after: pageInfo.endCursor ?? '' });
        }
    });
    sleep(waitBeetween);

    await group('13 - Read the sales order', async function () {
        console.info('*** Read the sales order ***');
        salesOrderRead = await getSalesOrder(tenant, { default: { number: 'SO24000117' }, filter: { site: site._id } });
    });

    await group('14 - Duplicate the sales order', async function () {
        console.info('*** Duplicate the sales order ***');
        salesOrder = (await duplicateSalesOrder(tenant, salesOrderRead)) as SalesOrderType;
    });
    sleep(waitBeetween);

    await group('15 - confirm the sales order', async function () {
        console.info('*** Confirm the sales order ***');
        if (!salesOrder) {
            console.error('Sales order not created');
        }
        await confirmSalesOrder(tenant, salesOrder);
    });
    sleep(waitBeetween);

    await group('16 - Allocate stock', async function () {
        console.info('*** Allocate stock ***');
        if (!salesOrder) {
            console.error('Sales order not created');
            return;
        }

        const processId = await requestAutoAllocation(tenant, salesOrder);

        const { status } = await trackNotification(tenant, processId);
        console.log(` status: ${status}`);

        const salesOrderStatus = await new CustomRequest(
            graphSales.node('@sage/xtrem-sales/SalesOrder').read({ allocationStatus: true }, salesOrder._id ?? ''),
            tenant,
        ).execute();

        if (salesOrderStatus?.allocationStatus !== 'allocated') {
            console.error(`Sales order not allocated ${salesOrderStatus?.allocationStatus}`);
        }
    });
    sleep(waitBeetween);

    await group('17 - Create a Sales shipment', async function () {
        console.info('*** Create a Sales shipment ***');
        await createSalesShipment(tenant, salesOrder);
    });

    await group('18 - Perform a sales order line inquiry', async function () {
        console.info('*** Perform a sales order line inquiry ***');
        const { pageInfo } = await salesOrderLineInquiry(tenant, { filter: {} });

        if (pageInfo?.endCursor) {
            await salesOrderLineInquiry(tenant, { first: 20, after: pageInfo.endCursor ?? '' });
        }
    });
    sleep(waitBeetween);

    await group('19 - Perform a stock journal inquiry', async function () {
        console.info('*** Perform a stock journal inquiry ***');
        const { pageInfo } = await stockJournalInquiry(tenant, { filter: { site: site?._id } });

        if (pageInfo?.endCursor) {
            await stockJournalInquiry(tenant, { first: 20, after: pageInfo.endCursor ?? '' });
        }
    });
    sleep(waitBeetween);

    await group('20 - Perform a journal entry inquiry', async function () {});
    sleep(waitBeetween);

    await group('21 - Perform an MRP calculation', async function () {
        trackingId = await runMrpCalculation(tenant, [site]);
    });

    await group('22 - Wait for mrp calculation', async function () {
        await trackNotification(tenant, trackingId, 300);
    });
}

export async function handleSummary(result: any) {
    const { fileName, data } = generateJsonSummary(result, 'perf');
    const { webFileName, webData } = await generateWebSummary(result, 'perf');
    return {
        stdout: generateTextSummary(result, { enableColors: true }),
        [fileName]: JSON.stringify(data, null, 2),
        [webFileName]: webData,
    };
}
