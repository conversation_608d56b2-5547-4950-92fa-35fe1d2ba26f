import { Graph, withoutEdges } from '@sage/xtrem-client';
import { GraphApi as GraphApiSales } from '@sage/xtrem-sales-api';
import { Site } from '@sage/xtrem-system-api';
import { sleep } from 'k6';
import { Options } from 'k6/options';
import { querySites } from '../../queries/purchase';
import { getSalesOrderLines, queryCustomer } from '../../queries/sales';
import { CustomRequest, fetcher } from '../../tooling/functions';
import { TenantConnector } from '../../tooling/tenant-connector';
import { randomInt } from '../../tooling/utils';

const graph = new Graph<GraphApiSales>({ fetcher });

export const options: Options = {
    vus: 1,
    iterations: 1,
    thresholds: {},
    tags: { name: 'value' },
    throw: false,
};

export default async function () {
    const tenant = new TenantConnector();
    console.log('Start creating sales order');
    const numberOfLines = 10;

    const query3Sites = querySites(tenant, {
        isPurchase: true,
        isInventory: true,
        id: { _in: ['501'] },
    });

    const sites = withoutEdges(await query3Sites.execute());
    const site = sites.at(randomInt({ min: 0, max: sites.length - 1 })) as Site;
    if (!site) {
        throw new Error('No site found');
    }

    const customer = await queryCustomer(tenant, { isActive: true, currency: { id: { _in: ['USD', 'EUR'] } } });
    if (!customer) {
        throw new Error('No customer found');
    }

    const salesOrder = await new CustomRequest(
        graph.node('@sage/xtrem-sales/SalesOrder').create(
            { number: true },
            {
                data: {
                    soldToCustomer: customer._id,
                    site: site._id,
                    lines: await getSalesOrderLines(tenant, numberOfLines, { site }),
                    requestedDeliveryDate: new Date().toISOString().split('T')[0],
                },
            },
        ),
        tenant,
    ).execute();
    console.info(`Sales order created: ${salesOrder.number}`);
    sleep(2);
}
