import { WorkOrder } from '@sage/xtrem-manufacturing-api';
import { Site } from '@sage/xtrem-system-api';
import { group, sleep } from 'k6';
import { Options } from 'k6/options';
import { allocateWorkOrder, createWorkOrder, waitWorkOrderAllocation } from '../../queries/manufacturing';
import { getSite } from '../../queries/system';
import { bomType, getBom } from '../../queries/technical-data';
import { TenantConnector } from '../../tooling/tenant-connector';
import { randomString } from '../../tooling/utils';

export const options: Options = {
    vus: 1,
    iterations: 1,
    thresholds: {
        'group_duration{group:::01 - Get a site}': ['max>=0'],
        'group_duration{group:::02 - Get a BOM}': ['max>=0'],
        'group_duration{group:::03 - Create a work order}': ['max>=0'],
        'group_duration{group:::04 - Allocate the work order}': ['max>=0'],
        'group_duration{group:::05 - Wait for work order allocation}': ['max>=0'],
    },
    throw: false,
};

export default async function () {
    const tenant = new TenantConnector();
    let bom: bomType;
    let workOrder: WorkOrder;
    let site: Site;

    const waitBeetween = 1;

    await group('01 - Get a site', async function () {
        console.info('*** Get a site ***');
        site = await getSite(tenant, '#US002', {});
    });
    sleep(waitBeetween);

    await group('02 - Get a BOM', async function () {
        console.info('*** Get a BOM ***');
        bom = await getBom(tenant, { filter: { site: site._id } });
    });
    sleep(waitBeetween);

    await group('03 - Create a work order', async function () {
        console.info('*** Create a work order ***');
        workOrder = await createWorkOrder(tenant, {
            item: bom.item,
            site: bom.site,
            workOrderNumber: randomString(24),
        });
    });
    sleep(waitBeetween);

    await group('04 - Allocate the work order', async function () {
        console.info('*** Allocate the work order ***');
        await allocateWorkOrder(tenant, workOrder);
    });

    await group('05 - Wait for work order allocation', async function () {
        console.info('*** Wait for work order allocation ***');
        const order = await waitWorkOrderAllocation(tenant, workOrder, 300);
        console.info('Work order allocation status:', order.allocationStatus);
    });
}
