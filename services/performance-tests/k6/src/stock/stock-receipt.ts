import { StockReceiptInput } from '@sage/xtrem-stock-api';
import { Site } from '@sage/xtrem-system-api';
import { group } from 'k6';
import { Options } from 'k6/options';
import { getRandomItems, ItemSelection } from '../../queries/master-data';
import { checkPostStockReceipt, createStockReceipt, postStockReceipt } from '../../queries/stock';
import { getSite } from '../../queries/system';
import { TenantConnector } from '../../tooling/tenant-connector';

export const options: Options = {
    vus: 10,
    duration: '1m',
    thresholds: {
        'group_duration{group:::Get a site}': ['max>=0'],
        'group_duration{group:::Get a random items}': ['max>=0'],
        'group_duration{group:::Create Stock receipt}': ['max>=0'],
        'group_duration{group:::Post the receipt}': ['max>=0'],
        'group_duration{group:::Wait for the post}': ['max>=0'],
    },
    throw: false,
};

export default async function () {
    const tenant = new TenantConnector();
    const numberOfItems = 100;

    let site: Site;
    let items: Array<ItemSelection>;
    let stockReceipt: StockReceiptInput;

    await group('Get a site', async function () {
        console.info('*** Get a site ***');
        site = await getSite(tenant, '#US002', {});
    });

    await group('Get a random items', async function () {
        console.info('*** Get a random items ***');
        items = await getRandomItems(tenant, numberOfItems, { isStockManaged: true, lotManagement: 'notManaged' });
    });

    await group('Create Stock receipt', async function () {
        console.info('*** Create Stock receipt ***');
        stockReceipt = await createStockReceipt(tenant, items, site);
    });

    await group('Post the receipt', async function () {
        console.info('*** Post the receipt ***');
        await postStockReceipt(tenant, { _id: stockReceipt._id ?? '' });
    });

    await group('Wait for the post', async function () {
        console.info('*** Check the post ***');
        const checkStockReceipt = await checkPostStockReceipt(tenant, { _id: stockReceipt._id ?? '' });

        console.info('Check the post result:', JSON.stringify(checkStockReceipt));
    });
}
