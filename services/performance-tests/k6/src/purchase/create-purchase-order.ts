import { edgesSelector, Graph } from '@sage/xtrem-client';
import { GraphApi as GraphApiPurchasing } from '@sage/xtrem-purchasing-api';
import { Site } from '@sage/xtrem-system-api';
import { group, sleep } from 'k6';
import { Options } from 'k6/options';
import { createMrpItem, getRandomSupplier, SupplierType } from '../../queries/master-data';
import { confirmPurchaseOrder, createPurchaseOrder, querySites } from '../../queries/purchase';
import { getSite } from '../../queries/system';
import { CustomRequest, fetcher } from '../../tooling/functions';
import { TenantConnector } from '../../tooling/tenant-connector';

const graphPurchasing = new Graph<GraphApiPurchasing>({ fetcher });

export const options: Options = {
    vus: 1,
    iterations: 1,
    duration: '120m',
    thresholds: {
        'group_duration{group:::Get a site}': ['max>=0'],
        'group_duration{group:::Get a supplier}': ['max>=0'],
        'group_duration{group:::Create a purchase order}': ['max>=0'],
        'group_duration{group:::Confirm the purchase order}': ['max>=0'],
        'group_duration{group:::Select order lines}': ['max>=0'],
    },
    tags: { name: 'value' },
    throw: false,
};

export default async function () {
    let site: Site;
    let supplier: SupplierType;
    let purchaseOrder: { _id: string; number: string };
    const tenant = new TenantConnector();
    const numberOfLines = 4;
    const waitBeetween = 5;

    await group('Get a site', async function () {
        console.info('*** Get a site ***');

        const querySite = querySites(tenant, {
            isPurchase: true,
            isInventory: true,
        });
        await querySite.execute();

        site = await getSite(tenant, '501', {
            isPurchase: true,
            isInventory: true,
        });
    });
    sleep(waitBeetween);

    await group('Get a supplier', async function () {
        console.info('*** Get a supplier ***');
        supplier = await getRandomSupplier(tenant, { isActive: true, id: { _in: ['20075', 'BARRES'] } });
    });
    sleep(waitBeetween);

    await group(' Create some item sites for the supplier', async function () {
        console.info('*** Create some item sites for the supplier ***');
        for (let i = 0; i < numberOfLines; i++) {
            await createMrpItem(tenant, { site, supplier });
        }
    });

    sleep(waitBeetween);

    await group('Create a purchase order', async function () {
        console.info('*** Create a purchase order ***');
        purchaseOrder = await createPurchaseOrder(tenant, {
            site,
            supplier,
            numberOfLines,
            withSupplierItems: true,
        });
        console.log(`Purchase order created with number ${purchaseOrder.number}`);
    });

    sleep(waitBeetween);

    await group('Confirm the purchase order', async function () {
        console.info('*** Confirm the purchase order ***');
        await confirmPurchaseOrder(tenant, {
            _id: purchaseOrder._id,
            site,
        });
    });

    await group('Select order lines', async function () {
        console.info('*** Select order lines from receipt ***');

        if (!site.primaryAddress) {
            console.error(`No primary address found for site ${site.id}`);
        }

        const orderLines = await new CustomRequest(
            graphPurchasing.node('@sage/xtrem-purchasing/PurchaseOrderLine').query(
                edgesSelector(
                    { _id: true, quantity: true, item: { id: true, name: true } },
                    {
                        filter: {
                            document: {
                                supplier: { _id: supplier._id },
                                site: { _id: site._id ?? '' },
                                currency: { _id: supplier.currency?._id ?? '' },
                                status: { _in: ['pending', 'inProgress'] },
                                receiptStatus: { _in: ['notReceived', 'partiallyReceived'] },
                                supplierAddress: { _id: supplier.primaryAddress?._id ?? '' },
                            },
                            status: { _in: ['pending', 'inProgress'] },
                            lineReceiptStatus: { _in: ['notReceived', 'partiallyReceived'] },
                            quantityToReceiveInStockUnit: { _gt: '0' },
                            ...(site.primaryAddress?._id ? { stockSiteAddress: { _id: site.primaryAddress._id } } : {}),

                            // Not realy sure about this one
                            purchaseReceiptLines: {
                                _atLeast: 1,
                                purchaseReceiptLine: { _id: { _nin: [] }, completed: { _eq: false } },
                            },
                        },
                        first: 10,
                    },
                ),
            ),
            tenant,
        ).execute();

        console.info(`Length : ${orderLines.edges.length} `);
    });
}
