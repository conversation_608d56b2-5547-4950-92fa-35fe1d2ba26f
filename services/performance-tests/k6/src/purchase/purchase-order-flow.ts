import { Graph } from '@sage/xtrem-client';
import { GraphApi as GraphApiManufacturing } from '@sage/xtrem-manufacturing-api';
import { PurchaseOrder } from '@sage/xtrem-purchasing-api';
import { Site } from '@sage/xtrem-system-api';
import { group, sleep } from 'k6';
import { Options } from 'k6/options';
import { createMrpItem, getRandomSupplier, getUnit, SupplierType } from '../../queries/master-data';
import {
    confirmPurchaseOrder,
    createPurchaseOrder,
    purchaseOrderLineInquiry,
    purchaseOrderMainList,
} from '../../queries/purchase';
import { getSite } from '../../queries/system';
import { CustomRequest, fetcher } from '../../tooling/functions';
import { generateJsonSummary, generateTextSummary, generateWebSummary } from '../../tooling/report';
import { TenantConnector } from '../../tooling/tenant-connector';
const graphPurchasing = new Graph<GraphApiManufacturing>({ fetcher });

export const options: Options = {
    vus: 1,
    iterations: 1,
    duration: '20m',
    thresholds: {
        'group_duration{group:::0 - Get a supplier}': ['max>=0'],
        'group_duration{group:::0 - Get a site}': ['max>=0'],
        'group_duration{group:::0 - Create purchase items}': ['max>=0'],
        'group_duration{group:::1 - Create a purchase order}': ['max>=0'],
        'group_duration{group:::2 - Confirm the purchase order}': ['max>=0'],
        'group_duration{group:::3 - Create a receipt from the order}': ['max>=0'],
        'group_duration{group:::4 -Perform a purchase order line inquiry}': ['max>=0'],
        'group_duration{group:::5 - Look for purchaseOrder MainList}': ['max>=0'],
    },
    throw: false,
};

export default async function () {
    // add infos to root_group

    const tenant = new TenantConnector();
    console.log(`Version : ${await tenant.getVersion()}`);
    let site: Site;
    let supplier: SupplierType;
    let purchaseOrder: Partial<PurchaseOrder>;
    const purchaseOrderLinesNb = 30; // 80 lines max 160 lines
    const waitBeetween = 2;

    const itemIds: string[] = [];

    await group('0 - Get a supplier', async function () {
        console.info('*** Get a supplier ***');
        supplier = await getRandomSupplier(tenant, { isActive: true, businessEntity: { id: 'BARRES' } });
    });

    await group('0 - Get a site', async function () {
        console.info('*** Get a site ***');
        site = await getSite(tenant, '#US002', {});
    });
    sleep(waitBeetween);

    await group('0 - Create purchase items', async function () {
        console.info('*** Create items ***');
        for (let i = 0; i < purchaseOrderLinesNb; i++) {
            const purchaseUnit = (
                await getUnit(tenant, {
                    id: { _ne: 'EACH' },
                    type: { _nin: ['time', 'temperature'] },
                })
            )._id;
            const item = await createMrpItem(tenant, { site, supplier, stockUnit: '#EACH', purchaseUnit });
            itemIds.push(item.id ?? '');
        }
    });

    await group('1 - Create a purchase order', async function () {
        console.info('*** Create a purchase order ***');
        purchaseOrder = await createPurchaseOrder(tenant, {
            site,
            supplier,
            numberOfLines: purchaseOrderLinesNb,
            itemIds,
        });
    });
    sleep(waitBeetween);

    await group('2 - Confirm the purchase order', async function () {
        console.info('*** Confirm the purchase order ***');
        await confirmPurchaseOrder(tenant, { _id: purchaseOrder._id ?? '', site });
    });
    sleep(waitBeetween);

    await group('3 - Create a receipt from the order', async function () {
        console.info('*** Create a receipt from the order ***');
        const createdReceipts = new CustomRequest(
            graphPurchasing
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.createPurchaseReceipt({ _id: true }, { document: purchaseOrder._id ?? '' }),
            tenant,
        ).execute();
        console.info(`${(await createdReceipts).length} receipts created`);
    });
    sleep(waitBeetween);
    await group('4 -Perform a purchase order line inquiry', async function () {
        console.info('*** Perform a purchase order line inquiry ***');
        const { pageInfo } = await purchaseOrderLineInquiry(tenant, { filter: {} });

        if (pageInfo?.endCursor) {
            await purchaseOrderLineInquiry(tenant, { first: 20, after: pageInfo.endCursor ?? '' });
        }
    });
    sleep(waitBeetween);

    await group('5 - Look for purchaseOrder MainList', async function () {
        console.info('*** Look for purchaseOrder MainList ***');
        await purchaseOrderMainList(tenant, {});
    });
    sleep(waitBeetween);
}

export async function handleSummary(result: any) {
    const { fileName, data } = generateJsonSummary(result, 'purchase-order-flow');
    const { webFileName, webData } = await generateWebSummary(result, 'purchase-order-flow');
    return {
        stdout: generateTextSummary(result, { enableColors: true }),
        [fileName]: JSON.stringify(data, null, 2),
        [webFileName]: webData,
    };
}
