export interface Tenant {
    server: string;
    cluster: string;
    tenantId: string;
    user: string;
    app: string;
}

export const ekAnonTenantLatest: Tenant = {
    server: 'eu.dev',
    cluster: 'cls-perf-test',
    tenantId: 'jOCeeEJipmUbWEul0PR1F',
    user: 'guillau<PERSON>.<EMAIL>',
    app: 'sdmo',
};

export const k6DemoTenant: Tenant = {
    server: 'eu.dev',
    cluster: 'ci-v2',
    tenantId: 'En4HfjS5XmlHRrDNmpgo1',
    user: '<EMAIL>',
    app: 'sdmo',
};

export const ekDemoTenant: Tenant = {
    server: 'na.qa',
    cluster: 'cls-prd-anon',
    tenantId: 'L39xZxqU6WgejU_MHRd5a',
    user: '<EMAIL>',
    app: 'sdmo',
};
