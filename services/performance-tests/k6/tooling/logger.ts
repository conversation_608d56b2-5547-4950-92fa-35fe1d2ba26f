// write a simple logger class that can log messages with different levels (info, warn, error)

type LogLevel = 'info' | 'warn' | 'error' | 'debug';

export class Logger {
    private level: LogLevel;

    constructor(level: LogLevel = 'info') {
        this.level = level;
    }

    setLevel(level: LogLevel) {
        this.level = level;
    }

    debug(message: string) {
        if (this.level === 'debug') {
            console.log(`DEBUG: ${message}`);
        }
    }

    info(message: string) {
        if (this.level === 'info' || this.level === 'debug') {
            console.log(`INFO: ${message}`);
        }
    }

    warn(message: string) {
        if (this.level === 'info' || this.level === 'warn') {
            console.log(`WARN: ${message}`);
        }
    }

    error(message: string) {
        console.log(`ERROR: ${message}`);
    }
}
