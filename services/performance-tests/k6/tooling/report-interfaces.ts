import { Options } from 'k6/options';

export interface ReportOptions extends Options {
    indent?: string;
    enableColors?: boolean;
    summaryTimeUnit?: string;
    summaryTrendStats?: string[];
}

export interface GroupTime {
    avg: number;
    min: number;
    med: number;
    max: number;
    p90: number;
    p95: number;
}

export interface ScenarioReport {
    duration: {
        total: GroupTime;
        [key: string]: GroupTime;
        iteration: GroupTime;
    };
    nbOfIteration: number;
    nbOfVusers: number;
}

export interface K6Group {
    path: string;
    id: string;
    name: string;
    groups: K6Group[];
    checks: any[]; // You can define a more specific type if needed
}

export interface K6Metric {
    type: string;
    contains: string;
    values: { [key: string]: number };
    thresholds?: { [threshold: string]: { ok: boolean } };
}

export interface K6SummaryJson {
    state: {
        isStdOutTTY: boolean;
        isStdErrTTY: boolean;
        testRunDurationMs: number;
    };
    metrics: { [metricName: string]: K6Metric };
    root_group: K6Group;
    options: ReportOptions;
}

export interface Unit {
    unit: string;
    coef: number;
}
