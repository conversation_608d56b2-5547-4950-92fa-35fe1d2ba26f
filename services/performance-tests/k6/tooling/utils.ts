import * as _ from 'lodash';

export function randomString(length: number, charset = 'abcdefghijklmnopqrstuvwxyz') {
    let res = '';
    while (length) {
        length--;
        res += charset[(unsafeRandom() * charset.length) | 0];
    }
    return res;
}

export function randomInt(options: { min?: number; max: number; not?: number[] } = { max: 100, not: [] }) {
    const min = options.min ?? 0;
    const not = _.uniq(options.not) ?? [];
    if (min > options.max) {
        throw new Error('Min must be lower than max');
    }
    if (min === options.max) {
        return min;
    }
    if (not.length === options.max - min + 1) {
        throw new Error('All numbers are excluded');
    }
    if (not.length === 0) {
        return Math.floor(unsafeRandom() * (options.max - min + 1) + min);
    }

    while (true) {
        const random = Math.floor(unsafeRandom() * (options.max - min + 1) + min);
        if (!not.includes(random)) {
            return random;
        }
    }
}

function unsafeRandom() {
    return Math.random();
}

export function today() {
    return new Date().toISOString().split('T')[0];
}

export function getMajorMinorPatch(version: string): { major: number; minor: number; patch: number } {
    const [major, minor, patch] = version.split('.').map(Number);
    return {
        major: major || 0,
        minor: minor || 0,
        patch: patch || 0,
    };
}

export function isVersionGreaterThan(currentVersion: string, versionToCompare: string): boolean {
    const { major: cMajor, minor: cMinor, patch: cPatch } = getMajorMinorPatch(currentVersion);
    const { major: vMajor, minor: vMinor, patch: vPatch } = getMajorMinorPatch(versionToCompare);

    return cMajor > vMajor || (cMajor === vMajor && (cMinor > vMinor || (cMinor === vMinor && cPatch > vPatch)));
}
