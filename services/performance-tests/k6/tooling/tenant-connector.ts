import { edgesSelector, Graph, withoutEdges } from '@sage/xtrem-client';
import { GraphApi } from '@sage/xtrem-system-api';
import { fail } from 'k6';
import http from 'k6/http';
import { CustomRequest } from '../tooling/functions';
import { Tenant } from './tenant-list';

export const fetcher = () => Promise.resolve({ data: {} });
const graph = new Graph<GraphApi>({ fetcher });

export class TenantConnector {
    server = 'eu.dev';
    cluster = 'ci-v2';
    tenantId = '';
    user = '<EMAIL>';
    app = 'sdmo';
    domain = 'sagextrem.com';
    isLocal = false;
    version = '0.0.0';

    private cookieJar: { jar: http.CookieJar; expireDateTime: Date } = {
        jar: http.cookieJar(),
        expireDateTime: new Date(),
    };

    constructor(tenant?: Tenant) {
        if (__ENV.SERVER && __ENV.CLUSTER && __ENV.TENANT_ID && __ENV.USER && __ENV.APP) {
            this.server = __ENV.SERVER;
            this.cluster = __ENV.CLUSTER;
            this.tenantId = __ENV.TENANT_ID;
            this.user = __ENV.USER;
            this.app = __ENV.APP;
        } else if (tenant) {
            this.server = tenant.server;
            this.cluster = tenant.cluster;
            this.tenantId = tenant.tenantId;
            this.user = tenant.user;
            this.app = tenant.app;
        } else {
            this.isLocal = true;
        }
        if (this.isLocal) {
            console.log(`Connecting to local app with user ${this.user}`);
        } else {
            console.log(
                `Connecting to ${this.app} ${this.server} ${this.cluster} Tenant: ${this.tenantId} with user ${this.user}`,
            );
        }
        console.log(`URL: ${this.getUrl()}`);
    }

    get unsercureLoginUrl() {
        return `https://login.${this.server}-${this.domain}/unsecuredevlogin?cluster=${this.cluster}&tenant=${this.tenantId}&user=${this.user}&app=${this.app}`;
    }

    getUrl() {
        if (this.isLocal) {
            return `http://localhost:8240`;
        }
        return `https://${this.app}-${this.cluster}.${this.server}-${this.domain}`;
    }

    getApiUrl() {
        return `${this.getUrl()}/api`;
    }

    getCookie() {
        if (this.isLocal) {
            return http.cookieJar();
        }

        if (this.cookieJar.expireDateTime > new Date()) {
            return this.cookieJar.jar;
        }

        const loginResponse = http.get(this.unsercureLoginUrl, { redirects: 0 });

        const cookies = loginResponse.cookies;

        const cookieJar = http.cookieJar();

        const accessToken = cookies.access_token[0]?.value;
        const accessTokenSign = cookies.access_token_sign[0]?.value;

        if (!accessToken || !accessTokenSign) {
            fail('No access token or access token sign');
        }

        cookieJar.set(this.getUrl(), 'access_token', accessToken);
        cookieJar.set(this.getUrl(), 'access_token_sign', accessTokenSign);

        this.cookieJar = { jar: cookieJar, expireDateTime: new Date(new Date().getTime() + 60 * 5) };

        return cookieJar;
    }

    async getVersion() {
        const pack = withoutEdges(
            await new CustomRequest(
                graph.node('@sage/xtrem-system/SysPackVersion').query(edgesSelector({ version: true }, { first: 1 })),
                this,
            ).execute(),
        ).at(0);

        if (!pack?.version) {
            throw new Error('No version found');
        }
        this.version = pack.version;
        return pack.version;
    }
}
