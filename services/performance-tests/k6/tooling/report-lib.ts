import { Dict } from '@sage/xtrem-client';
import { Unit } from './report-interfaces';

export const palette = {
    bold: 1,
    faint: 2,
    red: 31,
    green: 32,
    cyan: 36,
    yellow: 33,
    blue: 34,
    magenta: 35,
};

export const defaultOptions = {
    indent: ' ',
    enableColors: true,
};

export const icons = {
    info: '📢',
    verbose: '💬',
    success: '✅',
    warn: '⚠️',
    error: '❌',
    debug: '🐛',
    polling: '☕',
    waiting: '⏳',
    loading: '🔄',
    done: '✅',
};

export const unitMap: Dict<Unit> = {
    s: { unit: 's', coef: 0.001 },
    ms: { unit: 'ms', coef: 1 },
    us: { unit: 'µs', coef: 1000 },
};

export const succMark = icons.success;
export const failMark = icons.error;
