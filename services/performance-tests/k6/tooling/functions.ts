import { ExtractEdges, Request } from '@sage/xtrem-client';
import { jsonToGraphQLQuery } from 'json-to-graphql-query';
import { check, fail, sleep } from 'k6';
import http, { RefinedResponse, Response, ResponseType } from 'k6/http';
import * as _ from 'lodash';
import { TenantConnector } from './tenant-connector';

export const fetcher = () => Promise.resolve({ data: {} });

function unwrapPath(path: string[], data: any) {
    return path.reduce((r, k) => r[k], data);
}

function unwrapResult(obj: any): any {
    if (!obj || typeof obj !== 'object') return obj;
    if (Array.isArray(obj)) return obj.map(unwrapResult);
    return Object.keys(obj).reduce((r, k) => {
        r[k] = unwrapResult(obj[k]);
        return r;
    }, {} as any);
}

function unwrap(path: string[], data: any) {
    return unwrapPath(path, data.data);
}

export class CustomRequest<ResultT> extends Request<ResultT> {
    constructor(
        request: Request<ResultT>,
        public tenant?: TenantConnector,
    ) {
        super(request.config, request.path, request.name, request.selector, false);
        this.timings = {
            duration: 0,
            blocked: 0,
            connecting: 0,
            sending: 0,
            receiving: 0,
            waiting: 0,
            tls_handshaking: 0,
        };
        this.tenant = this.tenant ?? new TenantConnector();
    }

    timings: Response['timings'];

    // define a getter for the url
    get url() {
        return this.tenant?.getApiUrl() ?? '';
    }

    getHeaders() {
        return {
            headers: {
                'Proxy-Connection': `keep-alive`,
                'Accept-Language': `en-US`,
                Accept: `application/json, text/plain, */*`,
                'Content-Type': `application/json`,
                'custom-header-for-graphql': `This could well be a token`,
                'Accept-Encoding': `gzip, deflate`,
            },
            jar: this.tenant?.getCookie(),
        };
    }

    fetcher(query: string): Promise<RefinedResponse<any>> {
        query = query.replace(/\n/g, '');
        query = query.replace(/\s+/g, ' ');
        // in case of query we can use the get
        const result = http.post(this.url, JSON.stringify({ query }), this.getHeaders());
        this.timings = result.timings;
        const response = manageError(result);
        return Promise.resolve(response);
    }

    override async execute(): Promise<ResultT> {
        const query = jsonToGraphQLQuery(this.selector, { pretty: true });
        const result = await this.fetcher(query);
        const data = unwrapResult(result);
        if (!data.data) {
            console.error(` failing query : ${query}`);
            throw new Error(`request failed: ${JSON.stringify(data.errors ?? data)}`);
        }
        const unwrapped = unwrap(this.path, data);
        return this.name ? unwrapped[this.name] : unwrapped;
    }
}

export function manageError(resp: RefinedResponse<ResponseType | undefined>) {
    if (typeof resp.body !== 'string') {
        fail('Response body is not a string');
    }
    const response = JSON.parse(resp.body);
    if (response.errors) {
        const [error] = response.errors;
        console.error(`***Error*** : `);
        console.error(`${JSON.stringify(response.data)} `);
        if (error.extensions?.diagnoses) {
            error.extensions?.diagnoses
                .filter((diag: any) => diag.severity > 2)
                .forEach((diag: any) => {
                    console.error(`Severity: ${diag.severity} - ${diag.message} - ${diag.path}`);
                });
        }
        return { data: null, errors: response.errors };
    }
    return response;
}

const apiUrl = http.url`http://localhost:8240/api`;
const params = {
    headers: {
        'Proxy-Connection': `keep-alive`,
        'Accept-Language': `en-US`,
        Accept: `application/json, text/plain, */*`,
        'Content-Type': `application/json`,
        'custom-header-for-graphql': `This could well be a token`,
        Origin: `http://localhost:8240`,
        'Accept-Encoding': `gzip, deflate`,
    },
    cookies: {},
};

function sanitizeQuery(query: string) {
    query = query.replace(/\n/g, '');
    query = query.replace(/\s+/g, ' ');
    return JSON.stringify({ query });
}

export function post(param: { query: Request<any>; selector?: any; tag?: string[] }): {
    result: any;
    duration: number;
} {
    if (!param.query) {
        fail('Query is missing');
    }
    // delete the \n on query and the spaces ( keep only one space )
    const query = sanitizeQuery(jsonToGraphQLQuery(param.query.selector));
    const postResp = http.post(apiUrl, query, params);

    if (!check(postResp, { 'status matches 200': r => r.status === 200 })) {
        console.error(` failing query : ${query}`);
        fail(`Response status is not 200 but ${postResp.status} \n ${query}`);
    }

    const result = manageError(postResp);

    if (!result) {
        return { result: null, duration: postResp.timings.duration };
    }
    if (param.selector) {
        const get = _.get(result, param.selector);
        if (!get) {
            fail(`Selector ${param.selector} not found in response`);
        }
        return { result: get, duration: postResp.timings.duration };
    }

    return { result, duration: postResp.timings.duration };
}

export async function track(query: Request<{ status: string; result: string }>) {
    const isPending = true;
    while (isPending) {
        const { status, result } = await query.execute();
        if (status !== 'running') {
            if (status === 'error') {
                console.error('Items not created');
            }
            return result;
        }
        sleep(2);
    }
    return '';
}

export function withoutEdgesCustom<T>(node: T): ExtractEdges<T> {
    const transform = (val: any): any => {
        if (!val || typeof val !== 'object') return val;
        if (val.query) return transform(val.query);
        if (Array.isArray(val)) return val.map(transform);
        if (Array.isArray(val.edges)) return val.edges.map((v: any) => transform(v.node));
        return Object.keys(val).reduce((r, k) => {
            r[k] = transform(val[k]);
            return r;
        }, {} as any);
    };

    return transform(node);
}

export async function waitForResult<T>(queryFunction: (...args: any) => Promise<T>, ...args: any[]): Promise<T> {
    let polling = true;
    // Set a timeout for polling
    const timeout = setTimeout(() => {
        polling = false;
        console.error('Polling timed out');
    }, 180000); // 3min timeout
    // Clear the timeout when we find a result
    const clearPollingTimeout = () => {
        clearTimeout(timeout);
    };
    while (polling) {
        sleep(2);
        console.log(`Polling for result...`);
        try {
            const result = await queryFunction(...args);
            if (result) {
                console.log(`Result found:`, result);
                clearPollingTimeout();
                return result;
            }
        } catch (error) {
            console.error(`Error while polling: ${error}`);
        }
    }
    throw new Error('Polling ended without finding a result');
}
