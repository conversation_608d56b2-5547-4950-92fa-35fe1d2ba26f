/** typescript portage of https://jslib.k6.io/k6-summary/0.0.1/index.js  */
import { Dict } from '@sage/xtrem-client';
import { compile } from 'handlebars';
import { Logger } from './logger';
import { K6Metric, K6Summary<PERSON>son, ReportOptions } from './report-interfaces';
import { defaultOptions, failMark, palette, succMark, unitMap } from './report-lib';

const reportLogger = new Logger();

export function generateTextSummary(
    data: K6SummaryJson,
    options: ReportOptions = {},
    version: string = '0.0.0',
): string {
    const summaryTrendStats = options.summaryTrendStats ?? ['count', 'avg', 'min', 'med', 'max', 'p(90)', 'p(95)'];

    const mergedOpts: ReportOptions = {
        summaryTimeUnit: 'ms',
        ...defaultOptions,
        ...data.options,
        ...options,
        ...summaryTrendStats,
    };
    const decorate = mergedOpts.enableColors
        ? function (text: string, color: number /*, ...rest*/) {
              let result = '\x1b[' + color;
              for (let i = 2; i < arguments.length; i++) {
                  result += ';' + arguments[i];
              }
              return result + 'm' + text + '\x1b[0m';
          }
        : function (text: string) {
              return text;
          };

    reportLogger.debug(JSON.stringify(data.root_group, null, 2));

    return ['', ...summarizeMetrics(mergedOpts, data, decorate, version)].join('\nW');
}

function groupDurationFirstSort(a: string, b: string): number {
    if (a === 'group_duration') return -1;
    if (b === 'group_duration') return 1;
    return a.localeCompare(b);
}

export function generateJsonSummary(
    result: K6SummaryJson,
    name: string = 'dummy',
    version: string = '0.0.0',
): {
    fileName: string;
    data: { name: string; date: string; metrics: { group_duration: K6Metric; [key: string]: K6Metric } };
} {
    const directory = __ENV.K6_REPORT_DIR || './';
    const fileName = `${directory}report-${name}-${Date.now()}.json`;
    const data = {
        // name, version and date
        name,
        version,
        date: new Date().toISOString(),
        metrics: {
            group_duration: result.metrics['group_duration'],
            ...Object.entries(result.metrics)
                .sort(([a], [b]) => a.localeCompare(b))
                .filter(([name]) => name.startsWith('group_duration{'))
                .reduce((acc, [name, metric]) => {
                    const groupName = name.substring(name.indexOf('{') + 1, name.length - 1);
                    acc[groupName] = metric;
                    return acc;
                }, {} as Dict<K6Metric>),
        },
    };

    return { fileName, data };
}

export async function generateWebSummary(
    result: K6SummaryJson,
    name: string = 'dummy',
    version: string = '0.0.0',
): Promise<{ webFileName: string; webData: string }> {
    const directory = __ENV.K6_REPORT_DIR_WEB || './';
    const webFileName = `${directory}report-${name}-${Date.now()}.html`;
    const metrics = generateJsonSummary(result, name);
    // generate html table
    const headers: string[] = [`<th></th>`];
    const { group_duration, ...group_metrics } = metrics.data.metrics;

    const firstLine = `<tr><td></td>${Object.entries(group_duration.values)
        .map(([metric, value]) => {
            headers.push(`<th>${metric}</th>`);
            return `<td>${humanizeValue(value, metrics.data.metrics.group_duration, result.options.summaryTimeUnit ?? 'ms')}</td>`;
        })
        .join('')}</tr>`;

    const tableRows = [
        firstLine,
        ...Object.entries(group_metrics).map(([groupName, metric]) => {
            return `<th>${groupName}</th> ${Object.entries(metric.values)
                .map(([, value]) => {
                    return `<td>${humanizeValue(value, metric, result.options.summaryTimeUnit ?? 'ms')}</td>`;
                })
                .join('')}</tr>`;
        }),
    ].join('\n');

    const tableHeader = `<tr>${headers.join('')}</tr>`;

    const template = require('./template.hbs');
    const templateContent = await template();

    const compiledTemplate = compile(templateContent, { noEscape: true });
    const webData = compiledTemplate({
        tableRows,
        tableHeader,
        reportDate: new Date().toISOString(),
        reportName: name,
        reportVersion: version,
    });

    return { webFileName, webData };
}

function indentForMetric(name: string): string {
    if (name.indexOf('{') >= 0) {
        return '  ';
    }
    return '';
}

function displayNameForMetric(name: string): string {
    const subMetricPos = name.indexOf('{');
    if (subMetricPos >= 0) {
        return '{ ' + name.substring(subMetricPos + 1, name.length - 1) + ' }';
    }
    return name;
}

function toFixedNoTrailingZerosTrunc(val: number, precision: number): string {
    const mult = Math.pow(10, precision);
    return toFixedNoTrailingZeros(Math.trunc(mult * val) / mult, precision);
}

function toFixedNoTrailingZeros(val: number, precision: number): string {
    // TODO: figure out something better?
    return parseFloat(val.toFixed(precision)).toString();
}

function humanizeGenericDuration(dur: number): string {
    if (dur === 0) {
        return '0s';
    }

    if (dur < 0.001) {
        // smaller than a microsecond, print nanoseconds
        return Math.trunc(dur * 1000000) + 'ns';
    }
    if (dur < 1) {
        // smaller than a millisecond, print microseconds
        return toFixedNoTrailingZerosTrunc(dur * 1000, 2) + 'µs';
    }
    if (dur < 1000) {
        // duration is smaller than a second
        return toFixedNoTrailingZerosTrunc(dur, 2) + 'ms';
    }

    let result = toFixedNoTrailingZerosTrunc((dur % 60000) / 1000, dur > 60000 ? 0 : 2) + 's';
    let rem = Math.trunc(dur / 60000);
    if (rem < 1) {
        // less than a minute
        return result;
    }
    result = (rem % 60) + 'm' + result;
    rem = Math.trunc(rem / 60);
    if (rem < 1) {
        // less than an hour
        return result;
    }
    return rem + 'h' + result;
}

function humanizeBytes(bytes: number) {
    const units = ['B', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const base = 1000;
    if (bytes < 10) {
        return bytes + ' B';
    }

    const e = Math.floor(Math.log(bytes) / Math.log(base));
    const suffix = units[e | 0];
    const val = Math.floor((bytes / Math.pow(base, e)) * 10 + 0.5) / 10;
    return val.toFixed(val < 10 ? 1 : 0) + ' ' + suffix;
}

function humanizeDuration(dur: number, timeUnit: string) {
    if (timeUnit !== '' && unitMap.hasOwnProperty(timeUnit)) {
        return (dur * unitMap[timeUnit].coef).toFixed(2) + unitMap[timeUnit].unit;
    }

    return humanizeGenericDuration(dur);
}

function humanizeValue(val: number, metric: K6Metric, timeUnit: string) {
    if (metric.type == 'rate') {
        // Truncate instead of round when decreasing precision to 2 decimal places
        return (Math.trunc(+val * 100 * 100) / 100).toFixed(2) + '%';
    }

    switch (metric.contains) {
        case 'data':
            return humanizeBytes(+val);
        case 'time':
            return humanizeDuration(+val, timeUnit);
        default:
            return toFixedNoTrailingZeros(+val, 6);
    }
}

// strWidth tries to return the actual width the string will take up on the
// screen, without any terminal formatting, unicode ligatures, etc.
function strWidth(s: string) {
    // TODO: determine if NFC or NFKD are not more appropriate? or just give up? https://hsivonen.fi/string-length/
    const data = s.normalize('NFKC'); // This used to be NFKD in Go, but this should be better
    let inEscSeq = false;
    let inLongEscSeq = false;
    let width = 0;
    for (const char of data) {
        // Skip over ANSI escape codes.
        if (char == '\x1b') {
            inEscSeq = true;
            continue;
        }
        if (inEscSeq && char == '[') {
            inLongEscSeq = true;
            continue;
        }
        if (inEscSeq && inLongEscSeq && char.charCodeAt(0) >= 0x40 && char.charCodeAt(0) <= 0x7e) {
            inEscSeq = false;
            inLongEscSeq = false;
            continue;
        }
        if (inEscSeq && !inLongEscSeq && char.charCodeAt(0) >= 0x40 && char.charCodeAt(0) <= 0x5f) {
            inEscSeq = false;
            continue;
        }

        if (!inEscSeq && !inLongEscSeq) {
            width++;
        }
    }
    return width;
}

function nonTrendMetricValueForSum(metric: K6Metric, timeUnit: string) {
    switch (metric.type) {
        case 'counter':
            return [
                humanizeValue(+metric.values.count, metric, timeUnit),
                humanizeValue(+metric.values.rate, metric, timeUnit) + '/s',
            ];
        case 'gauge':
            return [
                humanizeValue(metric.values.value, metric, timeUnit),
                'min=' + humanizeValue(metric.values.min, metric, timeUnit),
                'max=' + humanizeValue(metric.values.max, metric, timeUnit),
            ];
        case 'rate':
            return [
                humanizeValue(metric.values.rate, metric, timeUnit),
                succMark + ' ' + metric.values.passes,
                failMark + ' ' + metric.values.fails,
            ];
        default:
            return ['[no data]'];
    }
}

function summarizeMetrics(
    options: ReportOptions,
    data: K6SummaryJson,
    decorate: Function,
    version: string = '0.0.0',
): any[] {
    const indent = options.indent + '  ';

    let nameLenMax = 0;

    const nonTrendValues: Dict<string> = {};
    let nonTrendValueMaxLen = 0;
    const nonTrendExtras: Dict<string[]> = {};
    const nonTrendExtraMaxLens = [0, 0];

    const summaryTrendStats = options.summaryTrendStats ?? ['count', 'avg', 'min', 'med', 'max', 'p(90)', 'p(95)'];
    const numTrendColumns = summaryTrendStats.length;

    const trendCols: Dict<string[]> = {};

    const trendColMaxLens = new Array(numTrendColumns).fill(0);
    const versionLine = [`    Version: ${version}`];
    const names = Object.entries(data.metrics).map(([name, metric]) => {
        const displayName = indentForMetric(name) + displayNameForMetric(name);
        const displayNameWidth = strWidth(displayName);
        if (displayNameWidth > nameLenMax) {
            nameLenMax = displayNameWidth;
        }
        reportLogger.debug(
            ` **** Processing metric: ${name}, displayName: ${displayName} **** width=${displayNameWidth}`,
        );

        if (metric.type == 'trend') {
            const cols = [];
            for (let i = 0; i < numTrendColumns; i++) {
                const tc = summaryTrendStats.at(i) ?? '';
                let value: string | number = metric.values[tc];
                if (tc !== 'count') {
                    value = humanizeValue(value, metric, options.summaryTimeUnit ?? 'ms');
                }
                const valLen = strWidth(value.toString());
                if (valLen > trendColMaxLens[i]) {
                    trendColMaxLens[i] = valLen;
                }
                cols[i] = value.toString();
            }
            trendCols[name] = cols;
            return name;
        }
        const values = nonTrendMetricValueForSum(metric, options.summaryTimeUnit ?? 'ms');
        nonTrendValues[name] = values[0];
        const valueLen = strWidth(values[0]);
        if (valueLen > nonTrendValueMaxLen) {
            nonTrendValueMaxLen = valueLen;
        }
        nonTrendExtras[name] = values.slice(1);
        for (let i = 1; i < values.length; i++) {
            const extraLen = strWidth(values[i]);
            if (extraLen > nonTrendExtraMaxLens[i - 1]) {
                nonTrendExtraMaxLens[i - 1] = extraLen;
            }
        }
        return name;
    });
    // Sort by alphabetical,

    const getData = function (name: string) {
        if (trendCols.hasOwnProperty(name)) {
            const cols = trendCols[name];
            const tmpCols = new Array(numTrendColumns);
            for (let i = 0; i < cols.length; i++) {
                tmpCols[i] =
                    `${options.summaryTrendStats?.at(i) ?? ''}=${decorate(cols[i], palette.cyan)} ${' '.repeat(trendColMaxLens[i] - strWidth(cols[i]))}`;
            }
            return tmpCols.join(' ');
        }

        const value = nonTrendValues[name];
        let fmtData = decorate(value, palette.cyan) + ' '.repeat(nonTrendValueMaxLen - strWidth(value));

        const extras = nonTrendExtras[name];
        if (extras.length == 1) {
            fmtData = fmtData + ' ' + decorate(extras[0], palette.cyan, palette.faint);
        } else if (extras.length > 1) {
            const parts = new Array(extras.length);
            for (let i = 0; i < extras.length; i++) {
                parts[i] =
                    decorate(extras[i], palette.cyan, palette.faint) +
                    ' '.repeat(nonTrendExtraMaxLens[i] - strWidth(extras[i]));
            }
            fmtData = fmtData + ' ' + parts.join(' ');
        }

        return fmtData;
    };
    function generateLine(name: string) {
        if (!name) return ''; // For typing
        const metric = data.metrics[name];
        let mark = ' ';

        let markColor = function (text: string) {
            return text;
        }; // noop

        if (metric.thresholds) {
            mark = succMark;
            markColor = function (text) {
                return decorate(text, palette.green);
            };
            if (Object.entries(metric.thresholds).some(([, threshold]) => !threshold.ok)) {
                mark = failMark;
                markColor = function (text: string) {
                    return decorate(text, palette.red);
                };
            }
        }
        const fmtIndent = indentForMetric(name);
        let fmtName = displayNameForMetric(name);
        fmtName =
            fmtName +
            decorate('.'.repeat(nameLenMax - strWidth(fmtName) - strWidth(fmtIndent) + 3) + ':', palette.faint);

        return indent + fmtIndent + markColor(mark) + ' ' + fmtName + ' ' + getData(name);
    }

    reportLogger.debug('**** Summary Report ****');
    reportLogger.debug(`${JSON.stringify(names)}`);
    return [
        versionLine,
        ...names
            .filter(name => !!name)
            .filter(name => name.startsWith('group_duration'))
            // i want group_duration to be first then all others then i want to sort them alphabetically
            .sort(groupDurationFirstSort)
            .map(generateLine),

        '\n' + decorate(`Test duration: ${humanizeGenericDuration(data.state.testRunDurationMs)}`, palette.yellow),
        '',
    ];
}
