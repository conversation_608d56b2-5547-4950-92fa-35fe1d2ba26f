import { edgesSelector, Filter, Graph } from '@sage/xtrem-client';
import { GraphApi } from '@sage/xtrem-communication-api';
import { GraphApi as GraphApiSales } from '@sage/xtrem-sales-api';
import { Site } from '@sage/xtrem-system-api';
import { sleep } from 'k6';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';

const graph = new Graph<GraphApi>({ fetcher });
const graphSales = new Graph<GraphApiSales>({ fetcher });

export async function trackNotification(tenant: TenantConnector, processId: string, maxLoop = 20) {
    let loop = 0;
    while (loop < maxLoop) {
        const track = await new CustomRequest(
            graph.node('@sage/xtrem-communication/SysNotificationState').read({ status: true }, processId),
            tenant,
        ).execute();

        if (track?.status && track.status !== 'running') {
            console.info(`Status: ${track?.status}`);
            return track;
        }
        sleep(2);
        loop++;
    }
    return { status: 'timeout' };
}

export async function getSite(tenant: TenantConnector, defaultSite: string, filter: Filter<Site>) {
    const selector = {
        _id: true,
        id: true,
        name: true,
        isPurchaseOrderApprovalManaged: true,
        stockSite: { _id: true },
        legalCompany: { currency: { _id: true } },
    };

    const siteRead = new CustomRequest(graphSales.node('@sage/xtrem-system/Site').read(selector, defaultSite), tenant);
    const site = (await siteRead.execute()) as Site;
    if (site) {
        return site;
    }
    console.log(`Site ${defaultSite} not found, searching for a site with filter ${JSON.stringify(filter)}`);

    const siteQuery = new CustomRequest(
        graphSales
            .node('@sage/xtrem-system/Site')
            .query(edgesSelector(selector, { first: 1, filter: { ...filter, isActive: true } })),
        tenant,
    );
    return (await siteQuery.execute()).edges.at(0)?.node as Site;
}
