import { edgesSelector, Graph, withoutEdges } from '@sage/xtrem-client';
import { GraphApi } from '@sage/xtrem-distribution-api';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { randomInt } from '../tooling/utils';

const graph = new Graph<GraphApi>({ fetcher });

export async function getLines(
    tenant: TenantConnector,
    document: {
        supplier: { _id: string };
        lines: { count: number };
    },
): Promise<{ item: string; quantity: number; grossPrice: number; discount: number; charge: number }[]> {
    const getItemsOfSupplier = new CustomRequest(
        graph
            .node('@sage/xtrem-master-data/ItemSupplier')
            .query(
                edgesSelector(
                    { _id: true, item: { id: true, name: true } },
                    { filter: { supplier: document.supplier._id } },
                ),
            ),
        tenant,
    );
    const itemSuppliers = await getItemsOfSupplier.execute();
    const itemIds = withoutEdges(itemSuppliers).map(itemSupplier => itemSupplier.item.id);

    const getItems = new CustomRequest(
        graph
            .node('@sage/xtrem-master-data/Item')
            .query(
                edgesSelector(
                    { _id: true, id: true, name: true },
                    { filter: { id: { _in: itemIds }, isBought: true, isActive: true, type: 'good' } },
                ),
            ),
        tenant,
    );

    const lines = [];
    const itemSelection = withoutEdges(await getItems.execute());

    for (let i = 0; i <= document.lines.count; i++) {
        const randomItemInt = randomInt({ min: 0, max: itemSelection.length - 1 });
        const item = itemSelection.at(randomItemInt);
        const quantity = randomInt({ min: 10, max: 100 });

        if (!item) {
            console.warn(`${JSON.stringify(itemSelection)}`);
            throw new Error(`No item found at index ${randomItemInt}`);
        }

        lines.push({ item: item._id, quantity, grossPrice: 10, discount: 2, charge: 2 });
    }
    return lines;
}
