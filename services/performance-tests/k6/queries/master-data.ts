import { edgesSelector, extractEdges, Filter, Graph, withoutEdges } from '@sage/xtrem-client';
import {
    GraphApi as GraphApiMasterData,
    Item,
    ItemCategoryInput,
    ItemCustomerInput,
    ItemInput,
    ItemSiteInput,
    ItemSupplierInput,
    Supplier,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';

import { Site } from '@sage/xtrem-system-api';
import { CustomRequest, fetcher, withoutEdgesCustom } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { randomInt, randomString } from '../tooling/utils';

const graphMasterData = new Graph<GraphApiMasterData>({ fetcher });

export const itemSelector = {
    _id: true,
    id: true,
    name: true,
    description: true,
    isStockManaged: true,
    isManufactured: true,
    isBought: true,
    isSold: true,
    stockUnit: { _id: true, id: true },
    purchaseUnit: { _id: true, id: true },
    salesUnit: { _id: true, id: true },
    maximumSalesQuantity: true,
    minimumSalesQuantity: true,
    itemSites: { query: edgesSelector({ _id: true, site: { _id: true } }) },
    lotManagement: true,
    isExpiryManaged: true,
    lotSequenceNumber: { _id: true },
};

export function createItemCategory(tenant: TenantConnector, category: ItemCategoryInput) {
    return new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/ItemCategory')
            .create({ _id: true, name: true }, { data: category }),
        tenant,
    );
}

export function readItemCategory(tenant: TenantConnector, id: string) {
    return new CustomRequest(
        graphMasterData.node('@sage/xtrem-master-data/ItemCategory').read({ _id: true, name: true }, `#${id}`),
        tenant,
    );
}

// return type of createItem
export type ItemType = Awaited<ReturnType<typeof executeCreateItem>>;

export async function executeCreateItem(tenant: TenantConnector, item: ItemInput) {
    const createdItem = withoutEdgesCustom(await createItem(tenant, item).execute());
    if (!createdItem) {
        throw new Error('Item not created');
    }
    return createdItem;
}

export function createItem(tenant: TenantConnector, item: ItemInput) {
    return new CustomRequest(
        graphMasterData.node('@sage/xtrem-master-data/Item').create(itemSelector, { data: item }),
        tenant,
    );
}

export function readItem(tenant: TenantConnector, _id: string) {
    return new CustomRequest(
        graphMasterData.node('@sage/xtrem-master-data/Item').read({ _id: true, name: true }, _id),
        tenant,
    );
}

export function deleteItem(tenant: TenantConnector, _id: string) {
    return new CustomRequest(graphMasterData.node('@sage/xtrem-master-data/Item').delete({ _id }), tenant);
}

export function queryItems(tenant: TenantConnector, filter: Filter<ItemInput>) {
    return new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/Item')
            .query(edgesSelector({ id: true, name: true }, { filter, first: 200 })),
        tenant,
    );
}

export type SupplierType = Awaited<ReturnType<typeof getRandomSupplier>>;

export async function getRandomSupplier(tenant: TenantConnector, filter: Filter<Supplier>) {
    const supplierSelection = await new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/Supplier')
            .query(
                edgesSelector(
                    { _id: true, id: true, name: true, currency: { _id: true }, primaryAddress: { _id: true } },
                    { filter: { isActive: true, ...filter } },
                ),
            ),
        tenant,
    ).execute();
    if (!supplierSelection.edges.length) {
        throw new Error('No suppliers found for filter: ' + JSON.stringify(filter));
    }

    const supplier = withoutEdges(supplierSelection).at(randomInt({ min: 0, max: supplierSelection.edges.length - 1 }));

    if (!supplier) {
        throw new Error('No supplier found');
    }

    return supplier;
}

// return type of the function itemSelection without the array
export type ItemSelectionType = Awaited<ReturnType<typeof itemSelection>>[0];

async function itemSelection(tenant: TenantConnector, filter: Filter<Item>) {
    const selection = await new CustomRequest(
        graphMasterData.node('@sage/xtrem-master-data/Item').query(edgesSelector(itemSelector, { filter, first: 200 })),
        tenant,
    ).execute();

    return extractEdges(selection);
}

export async function getRandomItems(tenant: TenantConnector, number: number, filter: Filter<Item>) {
    const myItemSelection = await itemSelection(tenant, filter);

    const items = new Set<ItemSelectionType>();
    const indexes = [];

    if (myItemSelection.length < number) {
        throw new Error(`Not enough items found: ${myItemSelection.length}`);
    }

    while (items.size < number) {
        const randomIndex = randomInt({ min: 0, max: myItemSelection.length - 1, not: indexes });
        indexes.push(randomIndex);
        const item = myItemSelection.at(randomIndex);
        if (!item) {
            throw new Error('No item found');
        }
        items.add(item);
    }
    return Array.from(items);
}
// get the return type of the function getRandomItems without the Set
export type ItemSelection = Awaited<ReturnType<typeof getRandomItems>> extends Array<infer T> ? T : never;

export async function addItemSite(
    tenant: TenantConnector,
    item: ItemSelection,
    site: { _id: string; id: string },
): Promise<void> {
    if (!site) {
        throw new Error(`addItemSite - No site for item ${item.id}`);
    }

    if (item.itemSites) {
        const itemSite = item.itemSites.find(line => line.site._id === site?._id);
        if (itemSite) {
            return;
        }
    }

    const dataItemStockManaged: Partial<ItemSiteInput> = item.isStockManaged
        ? { safetyStock: '4', batchQuantity: '9', replenishmentMethod: 'byReorderPoint' }
        : {};

    const itemSiteUpdate = await new CustomRequest(
        graphMasterData.node('@sage/xtrem-master-data/Item').update(
            { _id: true, id: true },
            {
                data: {
                    _id: item._id,
                    itemSites: [
                        {
                            site: site._id,
                            preferredProcess: item.isManufactured ? 'production' : 'purchasing',
                            prodLeadTime: 2,
                            purchaseLeadTime: 10,
                            ...dataItemStockManaged,
                            _action: 'create',
                        },
                    ],
                },
            },
        ),
        tenant,
    ).execute();

    if (!itemSiteUpdate) {
        throw new Error('Item site not updated');
    }
}

export async function addItemSiteCost(
    tenant: TenantConnector,
    itemSite: { _id: string; item: ItemSelection },
): Promise<void> {
    if (!itemSite) {
        throw new Error(`addItemSiteCost -itemSite empty`);
    }
    const lastItemSiteCost = (
        await new CustomRequest(
            graphMasterData.node('@sage/xtrem-master-data/ItemSiteCost').query(
                edgesSelector(
                    { version: true, _id: true },
                    {
                        filter: { itemSite: itemSite._id, costCategory: '#Standard' },
                        orderBy: { _updateStamp: 1 },
                        first: 1,
                    },
                ),
            ),
            tenant,
        ).execute()
    ).edges.at(0)?.node;

    const version = lastItemSiteCost ? lastItemSiteCost.version + 1 : 1;
    console.log(`Adding item site cost for item ${itemSite._id} with version ${version}`);
    const itemSiteCostUpdate = await new CustomRequest(
        graphMasterData.node('@sage/xtrem-master-data/ItemSiteCost').create(
            { _id: true },
            {
                data: {
                    itemSite: itemSite._id,
                    costCategory: '#Standard',
                    version,
                    forQuantity: 10,
                    indirectCost: randomInt({ min: 0, max: 10 }),
                    ...(itemSite.item.isManufactured
                        ? { laborCost: randomInt({ min: 0, max: 10 }), toolCost: randomInt({ min: 0, max: 10 }) }
                        : {}),
                    materialCost: randomInt({ min: 0, max: 10 }),
                },
            },
        ),
        tenant,
    ).execute();

    if (!itemSiteCostUpdate) {
        throw new Error('Item site cost not updated');
    }
}

function getItemSitePayload(site: { _id: string }): ItemSiteInput[] {
    return [
        {
            _action: 'create',
            site: site._id,
            preferredProcess: 'purchasing',
            prodLeadTime: 2,
            safetyStock: '4',
            batchQuantity: '9',
            purchaseLeadTime: 10,
            replenishmentMethod: 'byMRP',
        },
    ];
}

async function addItemSupplier(
    tenant: TenantConnector,
    item: { _id: string; supplier: { _id: string } },
): Promise<void> {
    await new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/Supplier')
            .update({ _id: true }, { data: { _id: item.supplier._id, items: getItemSupplierPayload(item) } }),
        tenant,
    ).execute();
}

function getItemSupplierPayload(item: { _id: string }): ItemSupplierInput[] {
    return [
        {
            _action: 'create',
            item: item._id,
            purchaseUnitOfMeasure: '#EACH',
            isActive: true,
        },
    ];
}

async function addItemCustomer(
    tenant: TenantConnector,
    item: { _id: string; customer: { _id: string } },
): Promise<void> {
    await new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/Customer')
            .update({ _id: true }, { data: { _id: item.customer._id, items: getItemCustomerPayload(item) } }),
        tenant,
    ).execute();
}

function getItemCustomerPayload(item: { _id: string }): ItemCustomerInput[] {
    return [
        {
            _action: 'create',
            item: item._id,
            isActive: true,
        },
    ];
}

/** Item is sold purchase and manufactured - ite,Site automaticly added  */
export async function createMrpItem(
    tenant: TenantConnector,
    item: {
        site?: Site;
        supplier?: { _id: string };
        customer?: { _id: string };
        purchaseUnit?: string;
        salesUnit?: string;
        stockUnit?: string;
    },
) {
    const itemSites = item.site ? getItemSitePayload(item.site) : undefined;

    const salesUnit = item.salesUnit ?? '#EACH';
    const purchaseUnit = item.purchaseUnit ?? '#EACH';
    const stockUnit = item.stockUnit ?? '#EACH';

    const itemCreated = await createItem(tenant, {
        id: randomString(10),
        name: `item ${randomString(10)}`,
        description: `Description ${randomString(50)}`,
        isSold: true,
        isBought: true,
        isManufactured: true,
        isStockManaged: true,
        salesUnit,
        purchaseUnit,
        stockUnit,
        purchaseUnitToStockUnitConversion: stockUnit === purchaseUnit ? 1 : randomInt({ min: 1, max: 50000 }) / 10000,
        salesUnitToStockUnitConversion: stockUnit === salesUnit ? 1 : randomInt({ min: 1, max: 50000 }) / 10000,
        itemSites,
    }).execute();

    if (item.supplier) {
        await addItemSupplier(tenant, { _id: itemCreated._id, supplier: item.supplier });
    }
    if (item.customer) {
        await addItemCustomer(tenant, { _id: itemCreated._id, customer: item.customer });
    }

    return itemCreated as Item;
}

export async function getUnit(tenant: TenantConnector, filter: Filter<UnitOfMeasure>) {
    const unitSelection = await new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/UnitOfMeasure')
            .query(edgesSelector({ _id: true, id: true }, { filter })),
        tenant,
    ).execute();

    const unitFound = withoutEdges(unitSelection).at(randomInt({ min: 0, max: unitSelection.edges.length - 1 }));

    if (!unitFound) {
        throw new Error(`No unit found for ${JSON.stringify(filter)}`);
    }

    return unitFound;
}
