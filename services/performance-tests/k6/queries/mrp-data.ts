import { Graph } from '@sage/xtrem-client';
import { GraphApi } from '@sage/xtrem-mrp-data-api';
import { Site } from '@sage/xtrem-system-api';
import { sleep } from 'k6';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { randomString, today } from '../tooling/utils';

const graph = new Graph<GraphApi>({ fetcher });

export async function runMrpCalculation(tenant: TenantConnector, sites: Array<Site>) {
    const { trackingId } = await new CustomRequest(
        graph.node('@sage/xtrem-mrp-data/MrpCalculation').asyncOperations.mrpCalculationRequest.start(
            { trackingId: true },
            {
                data: {
                    sites: { array: sites.map(site => ({ id: site.id, name: site.name })) },
                    startDate: today(),
                    numberOfWeeks: 5,
                    explodeBillOfMaterial: true,
                    isSalesQuoteIncluded: true,
                    description: `Calculation ${today()} - ${randomString(10)}`,
                },
            },
        ),
        tenant,
    ).execute();

    return trackingId;
}

export async function trackMrpCalculation(tenant: TenantConnector, trackingId: string, maxWaitTime = 60): Promise<any> {
    while (maxWaitTime > 0) {
        maxWaitTime -= 1;
        const track = await trackMrpCalculationQuery(tenant, trackingId);

        if (track?.status !== 'pending') {
            console.info(`Status: ${track?.status}`);
            return track;
        }
        sleep(1);
    }
    return { status: 'timeout', result: {} };
}

async function trackMrpCalculationQuery(tenant: TenantConnector, trackingId: string) {
    return new CustomRequest(
        graph
            .node('@sage/xtrem-mrp-data/MrpCalculation')
            .asyncOperations.mrpCalculationRequest.track({ status: true, result: true }, { trackingId }),
        tenant,
    ).execute();
}
