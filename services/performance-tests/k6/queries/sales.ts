import { edgesSelector, Filter, Graph, PagingOptions, withoutEdges } from '@sage/xtrem-client';
import { Customer } from '@sage/xtrem-master-data-api';
import { GraphApi as GraphApiSales, SalesOrder, SalesOrderLine, SalesOrderLineInput } from '@sage/xtrem-sales-api';
import { Site } from '@sage/xtrem-system-api';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { isVersionGreaterThan, randomInt, randomString } from '../tooling/utils';
import { addItemSite, getRandomItems } from './master-data';

const graph = new Graph<GraphApiSales>({ fetcher });

export type CustomerType = Awaited<ReturnType<typeof queryCustomer>>;

export async function queryCustomer(tenant: TenantConnector, filter: Filter<Customer>) {
    const customers = withoutEdges(
        await new CustomRequest(
            graph
                .node('@sage/xtrem-master-data/Customer')
                .query(edgesSelector({ _id: true, id: true, name: true }, { filter: { ...filter, isActive: true } })),
            tenant,
        ).execute(),
    );
    const customer = customers.at(randomInt({ min: 0, max: customers.length - 1 }));
    if (!customer) {
        throw new Error('No customer found');
    }
    return customer;
}

export async function getSalesOrderLines(
    tenant: TenantConnector,
    numberOfLines: number,
    data: { site: Site },
): Promise<Array<SalesOrderLineInput>> {
    const lines: Array<SalesOrderLineInput> = [];
    const stockSite = data.site.stockSite;
    const items = await getRandomItems(tenant, numberOfLines, { isSold: true, isStockManaged: true, isActive: true });

    for (const item of items) {
        const itemDescription = item.description ?? item.id ?? randomString(10);
        await addItemSite(tenant, item, stockSite);

        const minQuantity = +(item.minimumSalesQuantity ?? 1);
        const maxQuantity = +(item.maximumSalesQuantity ?? 10);

        lines.push({
            item: item._id,
            itemDescription: itemDescription === '' ? randomString(10) : itemDescription,
            quantity: randomInt({
                min: minQuantity !== 0 ? minQuantity : 1,
                max: maxQuantity !== 0 && minQuantity < maxQuantity ? maxQuantity : minQuantity + 10,
            }),
            grossPrice: randomInt({ min: 1, max: 100 }),
            stockSite: stockSite._id,
        });
    }
    return lines;
}

export type SalesOrderType = Awaited<ReturnType<typeof getSalesOrder>>;

export async function getSalesOrder(
    tenant: TenantConnector,
    order: { default?: Filter<SalesOrder>; filter?: Filter<SalesOrder> },
) {
    const filter = order.default ? order.default : (order.filter ?? { status: 'pending' });
    const ordersList = await new CustomRequest(
        graph.node('@sage/xtrem-sales/SalesOrder').query(
            edgesSelector(
                {
                    _id: true,
                    number: true,
                    status: true,
                    customerNumber: true,
                    soldToCustomer: { id: true, _id: true, name: true },
                    site: { id: true, _id: true, name: true },
                },
                { filter, first: 1 },
            ),
        ),
        tenant,
    ).execute();

    const queryOrders = withoutEdges(ordersList);
    const orderSelected = queryOrders.at(0);
    if (orderSelected) {
        return orderSelected;
    }
    if (order.default && order.filter) {
        console.log(
            `No sales order default found with  ${JSON.stringify(order.default)} - using filter ${JSON.stringify(
                order.filter,
            )}`,
        );
        return getSalesOrder(tenant, { filter: order.filter });
    }

    throw new Error('No sales order found');
}

export async function duplicateSalesOrder(tenant: TenantConnector, order: SalesOrderType) {
    const duplicatedOrder = await new CustomRequest(
        graph.node('@sage/xtrem-sales/SalesOrder').duplicate({ _id: true, number: true }, { _id: order._id, data: {} }),
        tenant,
    ).execute();
    if (!duplicatedOrder) {
        throw new Error('Sales order duplication failed');
    }
    return duplicatedOrder;
}

export async function salesOrderLineInquiry(tenant: TenantConnector, options: PagingOptions<SalesOrderLine>) {
    return new CustomRequest(
        graph.node('@sage/xtrem-sales/SalesOrderLine').query(
            edgesSelector(
                {
                    _id: true,
                    grossProfitAmountInCompanyCurrency: true,
                    remainingAmountToShipExcludingTaxInCompanyCurrency: true,
                    remainingAmountToShipExcludingTax: true,
                    ...(isVersionGreaterThan(tenant.version, '56.0.15')
                        ? { amountExcludingTaxInCompanyCurrency: true, amountExcludingTax: true }
                        : {
                              lineAmountExcludingTaxInCompanyCurrency: true,
                              lineAmountExcludingTax: true,
                          }),

                    netPrice: true,
                    document: {
                        companyCurrency: { id: true, _id: true, name: true, decimalDigits: true, symbol: true },
                        currency: { id: true, _id: true, name: true, decimalDigits: true, symbol: true },
                        date: true,
                        customerNumber: true,
                        _id: true,
                        number: true,
                        soldToCustomer: {
                            id: true,
                            _id: true,
                            name: true,
                            businessEntity: { id: true, taxIdNumber: true, name: true },
                        },
                    },
                    invoiceStatus: true,
                    shippingStatus: true,
                    salesUnit: { _id: true, name: true, symbol: true, decimalDigits: true },
                    remainingQuantityToShipInSalesUnit: true,
                    quantityInSalesUnit: true,
                    item: {
                        description: true,
                        id: true,
                        _id: true,
                        name: true,
                    },
                    shippingDate: true,
                    status: true,
                    site: {
                        id: true,
                        _id: true,
                        name: true,
                        description: true,
                        legalCompany: { id: true, _id: true, name: true },
                    },
                },
                options,
            ),
        ),
        tenant,
    ).execute();
}

export async function createSalesShipment(tenant: TenantConnector, order: SalesOrderType) {
    const salesShipmentRequest = new CustomRequest(
        graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.createSalesShipmentsFromOrders({ numberOfShipments: true }, { salesDocuments: [order._id] }),
        tenant,
    );
    return salesShipmentRequest.execute();
}

export async function confirmSalesOrder52(tenant: TenantConnector, order: { number: string }) {
    // Any to match the 53 version
    const confirm = await new CustomRequest(
        (graph.node('@sage/xtrem-sales/SalesOrder').mutations as any).confirmSalesOrder(true, {
            salesOrderNumber: order.number ?? '',
        }),
        tenant,
    ).execute();

    if (!confirm) {
        throw new Error('Sales order confirmation failed');
    }

    return confirm;
}

export async function confirmSalesOrder(tenant: TenantConnector, order: { _id: string; number: string }) {
    if ((await tenant.getVersion()).startsWith('53')) {
        return confirmSalesOrder52(tenant, order);
    }
    const confirm = await new CustomRequest(
        graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.confirmSalesOrder({ _id: true, status: true }, { salesOrder: order._id ?? '' }),
        tenant,
    ).execute();

    if (!confirm) {
        throw new Error('Sales order confirmation failed');
    }

    return confirm;
}

export type CreateSalesOrderType = Awaited<ReturnType<typeof createSalesOrder>>;

export async function createSalesOrder(
    tenant: TenantConnector,
    order: { customer: CustomerType; site: Site; salesOrderLinesNb: number },
) {
    const orderCreated = await new CustomRequest(
        graph.node('@sage/xtrem-sales/SalesOrder').create(
            { number: true, _id: true },
            {
                data: {
                    soldToCustomer: order.customer._id,
                    site: order.site._id,
                    lines: await getSalesOrderLines(tenant, order.salesOrderLinesNb, { site: order.site }),
                    requestedDeliveryDate: new Date().toISOString().split('T')[0],
                },
            },
        ),
        tenant,
    ).execute();

    if (!orderCreated) {
        throw new Error('Sales order creation failed');
    }

    return orderCreated;
}

export async function requestAutoAllocationV53(tenant: TenantConnector, order: SalesOrderType) {
    const processId = await new CustomRequest(
        graph.node('@sage/xtrem-sales/SalesOrder').mutations.requestAutoAllocation(true, {
            data: { document: order._id, requestType: 'allocation' },
        } as any),
        tenant,
    ).execute();
    if (!processId) {
        throw new Error('Sales order auto allocation request failed');
    }
    return processId;
}

export async function requestAutoAllocation(tenant: TenantConnector, order: SalesOrderType) {
    if ((await tenant.getVersion()).startsWith('53')) {
        return requestAutoAllocationV53(tenant, order);
    }

    const processId = await new CustomRequest(
        graph
            .node('@sage/xtrem-sales/SalesOrder')
            .mutations.requestAutoAllocation(true, { salesOrder: order._id, requestType: 'allocation' }),
        tenant,
    ).execute();

    if (!processId) {
        throw new Error('Sales order auto allocation request failed');
    }

    return processId;
}
