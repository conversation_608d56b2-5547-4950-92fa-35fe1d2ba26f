import { Graph } from '@sage/xtrem-client';
import { GraphApi, WorkOrder } from '@sage/xtrem-manufacturing-api';
import { sleep } from 'k6';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { randomInt, randomString } from '../tooling/utils';

const graph = new Graph<GraphApi>({ fetcher });

/** Create an assembly workOrder */
export async function createWorkOrder(
    tenant: TenantConnector,
    order: {
        item: { id: string };
        site: { id: string };
        category?: string;
        workOrderNumber?: string;
    },
): Promise<WorkOrder> {
    const category =
        order.category ??
        (
            await new CustomRequest(
                graph.node('@sage/xtrem-manufacturing/WorkOrderCategory').read({ _id: true }, '#Assembly'),
                tenant,
            ).execute()
        )?._id ??
        '';

    const workOrderCreationRequest = new CustomRequest(
        graph.node('@sage/xtrem-manufacturing/WorkOrder').mutations.createWorkOrder(
            { _id: true, number: true },
            {
                data: {
                    ...(order.workOrderNumber ? { workOrderNumber: order.workOrderNumber } : {}),
                    name: `WO ${randomString(10)}`,
                    releasedItem: order.item.id,
                    releasedQuantity: randomInt({ min: 1, max: 100 }),
                    siteId: order.site.id ?? '',
                    type: 'firm',
                    workOrderCategory: category,
                    bom: order.item.id,
                },
            },
        ),
        tenant,
    );
    return workOrderCreationRequest.execute() as Promise<WorkOrder>;
}

export async function readWorkOrder(tenant: TenantConnector, order: { _id: string }): Promise<WorkOrder> {
    return new CustomRequest(
        graph
            .node('@sage/xtrem-manufacturing/WorkOrder')
            .read(
                { _id: true, number: true, status: true, allocationStatus: true, allocationRequestStatus: true },
                order._id,
            ),
        tenant,
    ).execute() as Promise<WorkOrder>;
}

export async function allocateWorkOrder(tenant: TenantConnector, order: WorkOrder) {
    const workOrderAllocationRequest = new CustomRequest(
        graph.node('@sage/xtrem-manufacturing/WorkOrder').mutations.requestAutoAllocation(true, {
            data: { document: order._id, requestType: 'allocation' },
        }),
        tenant,
    );
    await workOrderAllocationRequest.execute();
}

export async function waitWorkOrderAllocation(
    tenant: TenantConnector,
    order: WorkOrder,
    maxWaitTime = 60,
): Promise<WorkOrder> {
    while (maxWaitTime > 0) {
        maxWaitTime = maxWaitTime - 1;
        const result = await readWorkOrder(tenant, order);
        switch (result.allocationStatus) {
            case 'partiallyAllocated':
            case 'allocated':
                return result;
            default:
                break;
        }
        switch (result.allocationRequestStatus) {
            case 'inProgress':
                break;
            case 'error':
            case 'completed':
                return result;
            default:
                break;
        }
        sleep(2);
    }
    console.error(`Allocation request timed out for work order ${order.number}`);
    return readWorkOrder(tenant, order);
}
