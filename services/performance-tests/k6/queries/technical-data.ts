import { edgesSelector, Filter, Graph } from '@sage/xtrem-client';
import { GraphApi } from '@sage/xtrem-manufacturing-api';
import { Item } from '@sage/xtrem-master-data-api';
import { Site } from '@sage/xtrem-system-api';
import { BillOfMaterial, ComponentInput } from '@sage/xtrem-technical-data-api';
import { CustomRequest, fetcher, withoutEdgesCustom } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { randomString } from '../tooling/utils';
import { getRandomItems, itemSelector } from './master-data';

const graph = new Graph<GraphApi>({ fetcher });

export type bomType = Awaited<ReturnType<typeof getBom>>;

export async function getBom(
    tenant: TenantConnector,
    bom: { default?: Filter<BillOfMaterial>; filter?: Filter<BillOfMaterial>; isCreate?: boolean },
) {
    const filter = bom.default ?? bom.filter ?? { isActive: true };

    const bomRequest = new CustomRequest(
        graph.node('@sage/xtrem-technical-data/BillOfMaterial').query(
            edgesSelector(
                {
                    _id: true,
                    name: true,
                    item: { _id: true, id: true },
                    site: { _id: true, id: true },
                    status: true,
                    components: {
                        query: edgesSelector({
                            _id: true,
                            item: itemSelector,
                            linkQuantity: true,
                            unit: { _id: true, id: true },
                        }),
                    },
                },
                { filter, first: 1 },
            ),
        ),
        tenant,
    );
    const bomReturn = (await bomRequest.execute()).edges.at(0)?.node;
    if (bomReturn) {
        return bomReturn;
    }
    if (bom.default && bom.filter) {
        console.log(
            `No BOM default found with  ${JSON.stringify(bom.default)} - using filter ${JSON.stringify(bom.filter)}`,
        );
        return getBom(tenant, { filter: bom.filter });
    }
    if (!bomReturn) {
        throw new Error(`No BOM found with filter ${JSON.stringify(filter)}`);
    }
    return bomReturn;
}

export async function createBomRequest(
    tenant: TenantConnector,
    bom: { item: Item; site: Site; components: Partial<ComponentInput>[] },
) {
    const bomCreationRequest = new CustomRequest(
        graph.node('@sage/xtrem-technical-data/BillOfMaterial').create(
            { _id: true },
            {
                data: {
                    name: `BOM ${randomString(10)}`,
                    item: bom.item?._id,
                    site: bom.site._id,
                    status: 'availableToUse',
                    components: bom.components,
                },
            },
        ),
        tenant,
    );
    return bomCreationRequest.execute();
}

export async function createComponent(tenant: TenantConnector, site: Site, i: number) {
    const componentCreationRequest = new CustomRequest(
        graph.node('@sage/xtrem-master-data/Item').create(itemSelector, {
            data: {
                id: randomString(10),
                name: `component ${randomString(10)} `,
                description: `Description - ${i} ${randomString(50)}`,
                isSold: true,
                isBought: true,
                isManufactured: true,
                isStockManaged: true,
                salesUnit: '#EACH',
                purchaseUnit: '#EACH',
                stockUnit: '#EACH',
                itemSites: [
                    {
                        site: site?._id,
                        preferredProcess: 'purchasing',
                        prodLeadTime: 2,
                        safetyStock: '4',
                        batchQuantity: '9',
                        purchaseLeadTime: 10,
                        replenishmentMethod: 'byReorderPoint',
                    },
                ],
            },
        }),
        tenant,
    );
    return withoutEdgesCustom(await componentCreationRequest.execute());
}

export async function addItemToBom(
    tenant: TenantConnector,
    bom: { count: number; isCompomentCreated: boolean; site: Site; _id: string },
) {
    const items = bom.isCompomentCreated
        ? []
        : await getRandomItems(tenant, bom.count, {
              itemSites: { _atLeast: 1, site: bom.site._id },
              isActive: true,
              isStockManaged: true,
          });

    const components = [];

    for (let i = 0; i < bom.count; i++) {
        const component = bom.isCompomentCreated ? await createComponent(tenant, bom.site, i) : items[i];

        const bomUpdateRequest = new CustomRequest(
            graph.node('@sage/xtrem-technical-data/BillOfMaterial').update(
                { _id: true },
                {
                    data: {
                        _id: bom?._id,
                        components: [{ _action: 'create', item: component._id, linkQuantity: 1, unit: '#EACH' }],
                    },
                },
            ),
            tenant,
        );
        await bomUpdateRequest.execute();
        components.push(component);
    }
    return components;
}

export async function duplicateBom(tenant: TenantConnector, bom: bomType, duplicate: { item: Item; site: Site }) {
    const bomDuplicationRequest = new CustomRequest(
        graph.node('@sage/xtrem-technical-data/BillOfMaterial').duplicate(
            { _id: true, name: true },
            {
                _id: bom._id,
                data: {
                    name: `BOM ${randomString(10)}`,
                    site: duplicate.site._id,
                    item: duplicate.item._id,
                },
            },
        ),
        tenant,
    );
    return bomDuplicationRequest.execute();
}
