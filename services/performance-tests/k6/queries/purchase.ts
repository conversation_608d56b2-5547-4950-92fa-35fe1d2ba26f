import { edgesSelector, extractEdges, Filter, Graph, PagingOptions, withoutEdges } from '@sage/xtrem-client';
import { GraphApi as GraphApiMasterData, Item, Supplier } from '@sage/xtrem-master-data-api';
import {
    GraphApi as GraphApiPurchasing,
    PurchaseOrder,
    PurchaseOrderInput,
    PurchaseOrderLine,
    PurchaseOrderLineInput,
} from '@sage/xtrem-purchasing-api';
import { GraphApi as GraphApiStock } from '@sage/xtrem-stock-api';
import { GraphApi as GraphApiSystem, Site } from '@sage/xtrem-system-api';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { isVersionGreaterThan, randomInt } from '../tooling/utils';
import { addItemSite, getRandomItems, SupplierType } from './master-data';

const graphSystem = new Graph<GraphApiSystem>({ fetcher });
const graphMasterData = new Graph<GraphApiMasterData>({ fetcher });
const graphPurchasing = new Graph<GraphApiPurchasing>({ fetcher });
const graphStock = new Graph<GraphApiStock>({ fetcher });

export function getServiceOptionState(tenant: TenantConnector, name: string) {
    return new CustomRequest(
        graphSystem
            .node('@sage/xtrem-system/SysServiceOptionState')
            .query(edgesSelector({ _id: true }, { filter: { serviceOption: { optionName: name } } })),
        tenant,
    );
}

export function activateServiceOption(tenant: TenantConnector, serviceOption: string) {
    return new CustomRequest(
        graphSystem
            .node('@sage/xtrem-system/SysServiceOptionState')
            .update({ _id: true }, { data: { _id: serviceOption, isActive: true } }),
        tenant,
    );
}

export function createTestItemQuery(
    tenant: TenantConnector,
    itemConfig: {
        itemId: string;
        quantity: number;
        maxStockQuantity: number;
        isStockQuantityFixed: boolean;
    },
) {
    const { itemId, quantity, maxStockQuantity, isStockQuantityFixed } = itemConfig;

    return new CustomRequest(
        graphStock
            .node('@sage/xtrem-master-data/Item')
            .asyncOperations.createTestItems.start(
                { trackingId: true },
                { itemId, quantity, stockCreation: { maxStockQuantity, isStockQuantityFixed } },
            ),
        tenant,
    );
}

export function createTestItemTrack(tenant: TenantConnector, trackingId: string) {
    return new CustomRequest(
        graphStock
            .node('@sage/xtrem-master-data/Item')
            .asyncOperations.createTestItems.track({ status: true, result: true }, { trackingId }),
        tenant,
    );
}

export function duplicatePurchaseOrder(tenant: TenantConnector, order: PurchaseOrderInput) {
    const duplicateQuery = new CustomRequest(
        graphPurchasing
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .duplicate({ _id: true, number: true }, { _id: order._id, data: {} }),
        tenant,
    );
    return duplicateQuery.execute();
}

export function readPurchaseOrder(tenant: TenantConnector, purchaseOrder: string) {
    return new CustomRequest(
        graphPurchasing
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .query(edgesSelector({ _id: true, orderNumber: true, status: true }, { filter: { _id: purchaseOrder } })),
        tenant,
    );
}

export function querySupplier(tenant: TenantConnector, filter: Filter<Supplier>) {
    return new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/Supplier')
            .query(edgesSelector({ _id: true, id: true, name: true }, { filter })),
        tenant,
    );
}

//
export function querySites(tenant: TenantConnector, siteFilter: Filter<Site>) {
    const filter: Filter<Site> = {
        isActive: true,
        ...siteFilter,
    };

    return new CustomRequest(
        graphSystem.node('@sage/xtrem-system/Site').query(
            edgesSelector(
                {
                    _id: true,
                    id: true,
                    name: true,
                    stockSite: { _id: true },
                    legalCompany: { _id: true, currency: { _id: true, id: true } },
                    primaryAddress: { _id: true },
                },
                { filter },
            ),
        ),
        tenant,
    );
}

export function queryCurrency(tenant: TenantConnector) {
    return new CustomRequest(
        graphMasterData
            .node('@sage/xtrem-master-data/Currency')
            .query(edgesSelector({ _id: true, name: true }, { filter: { isActive: true } })),
        tenant,
    );
}

export async function createPurchaseOrder(
    tenant: TenantConnector,
    order: {
        supplier: SupplierType;
        site: Site;
        numberOfLines: number;
        itemIds?: string[];
        withSupplierItems?: boolean;
    },
): Promise<PurchaseOrder> {
    const lines = await getPurchaseOrderLine(tenant, order.numberOfLines, {
        site: order.site,
        itemFilter: { id: { _in: order.itemIds } },
        ...(order.withSupplierItems ? { supplier: order.supplier } : {}),
    });

    const data: PurchaseOrderInput = {
        businessRelation: order.supplier._id,
        site: order.site._id,
        lines,
    };
    return new CustomRequest(
        graphPurchasing.node('@sage/xtrem-purchasing/PurchaseOrder').create({ _id: true, number: true }, { data }),
        tenant,
    ).execute() as Promise<PurchaseOrder>;
}

export function createItemSite(tenant: TenantConnector, param: { item: string; site: string }) {
    return new CustomRequest(
        graphPurchasing.node('@sage/xtrem-master-data/Item').update(
            { _id: true },
            {
                data: {
                    _id: param.item,
                    itemSites: [
                        {
                            _action: 'create',
                            site: param.site,
                            preferredProcess: 'purchasing',
                            prodLeadTime: 2,
                            safetyStock: '4',
                            batchQuantity: '9',
                            purchaseLeadTime: 10,
                            replenishmentMethod: 'byReorderPoint',
                        },
                    ],
                },
            },
        ),
        tenant,
    );
}

export function createItemSupplier(
    tenant: TenantConnector,
    param: {
        item: string;
        supplier: string;
        unitOfMeasure: string;
        supplierItemName: string;
    },
) {
    return new CustomRequest(
        graphPurchasing.node('@sage/xtrem-master-data/Supplier').update(
            { _id: true },
            {
                data: {
                    _id: param.supplier,
                    items: [
                        {
                            _action: 'create',
                            item: param.item,
                            supplierItemCode: '00008980',
                            supplierItemName: param.supplierItemName,
                            supplierPriority: 1,
                            isDefaultItemSupplier: true,
                            purchaseUnitOfMeasure: param.unitOfMeasure,
                            minimumPurchaseQuantity: '100',
                            purchaseLeadTime: 30,
                        },
                    ],
                },
            },
        ),
        tenant,
    );
}

export function readItem(tenant: TenantConnector, item: string) {
    return new CustomRequest(
        graphMasterData.node('@sage/xtrem-master-data/Item').read(
            {
                _id: true,
                id: true,
                name: true,
                purchaseUnit: { id: true },
                salesUnit: { id: true },
                itemSites: { query: { edges: { node: { site: { _id: true, id: true } } } } },
                suppliers: { query: { edges: { node: { supplier: { _id: true, id: true } } } } },
            },
            item,
        ),
        tenant,
    );
}

export async function getItemsOfSupplier(tenant: TenantConnector, supplier: SupplierType): Promise<Array<string>> {
    const getItemsOfSupplier = new CustomRequest(
        graphPurchasing
            .node('@sage/xtrem-master-data/ItemSupplier')
            .query(
                edgesSelector({ _id: true, item: { id: true, name: true } }, { filter: { supplier: supplier._id } }),
            ),
        tenant,
    );
    const itemSuppliers = await getItemsOfSupplier.execute();

    return withoutEdges(itemSuppliers).map(itemSupplier => itemSupplier.item.id);
}

export async function getPurchaseOrderLine(
    tenant: TenantConnector,
    numberOfLines: number,
    data: { site: Site; itemFilter: Filter<Item>; supplier?: SupplierType },
): Promise<Array<PurchaseOrderLineInput>> {
    const itemIdsFromSupplier = data.supplier ? await getItemsOfSupplier(tenant, data.supplier) : null;

    const lines: Array<PurchaseOrderLineInput> = [];
    const items = await getRandomItems(tenant, numberOfLines, {
        isBought: true,
        type: { _ne: 'landedCost' },
        ...data.itemFilter,
        ...(itemIdsFromSupplier ? { id: { _in: itemIdsFromSupplier } } : {}),
    });

    const uniqueItems = new Set<string>();

    for (const item of items) {
        if (!uniqueItems.has(item._id)) {
            await addItemSite(tenant, item, data.site);
            uniqueItems.add(item._id);
        }
        lines.push({
            item: item._id,
            itemDescription: item.name,
            unit: item.purchaseUnit._id,
            quantity: randomInt({ min: 1, max: 10 }),
            grossPrice: randomInt({ min: 1, max: 100 }),
            discount: randomInt({ min: 1, max: 10 }),
            charge: randomInt({ min: 1, max: 10 }),
        });
    }
    return lines;
}

export async function purchaseOrderLineInquiry(tenant: TenantConnector, options: PagingOptions<PurchaseOrderLine>) {
    return new CustomRequest(
        graphPurchasing.node('@sage/xtrem-purchasing/PurchaseOrderLine').query(
            edgesSelector(
                {
                    _id: true,
                    numberOfPurchaseReceiptLines: true,
                    remainingAmountToReceiveExcludingTaxInCompanyCurrency: true,
                    remainingAmountToReceiveExcludingTax: true,
                    ...(isVersionGreaterThan(tenant.version, '56.0.15')
                        ? {
                              amountExcludingTaxInCompanyCurrency: true,
                              amountExcludingTax: true,
                          }
                        : ({
                              lineAmountExcludingTaxInCompanyCurrency: true,
                              lineAmountExcludingTax: true,
                          } as any)), // for version < 56.0.16
                    netPrice: true,
                    document: {
                        companyCurrency: { id: true, _id: true, name: true, decimalDigits: true, symbol: true },
                        currency: { id: true, _id: true, name: true, decimalDigits: true, symbol: true },
                        orderDate: true,
                        _id: true,
                        number: true,
                        supplier: {
                            id: true,
                            _id: true,
                            name: true,
                            businessEntity: { id: true, taxIdNumber: true, name: true },
                        },
                    },
                    lineInvoiceStatus: true,
                    lineReceiptStatus: true,
                    unit: { _id: true, name: true, symbol: true, decimalDigits: true },
                    quantityToReceive: true,
                    quantity: true,
                    item: { description: true, id: true, _id: true, name: true },
                    expectedReceiptDate: true,
                    status: true,
                    itemSupplier: {
                        supplierItemName: true,
                        supplierItemCode: true,
                        _id: true,
                        id: true,
                        item: { id: true },
                    },
                    site: {
                        id: true,
                        _id: true,
                        name: true,
                        description: true,
                        legalCompany: { id: true, _id: true, name: true },
                    },
                },
                options,
            ),
        ),
        tenant,
    ).execute();
}

export async function confirmPurchaseOrder(tenant: TenantConnector, document: { _id: string; site: Site }) {
    if (!document.site.isPurchaseOrderApprovalManaged) {
        // i don't get this !!
        return new CustomRequest(
            graphPurchasing
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .mutations.confirm(true, { isConfirmed: true, document: document._id }),
            tenant,
        ).execute();
    }

    return new CustomRequest(
        graphPurchasing
            .node('@sage/xtrem-purchasing/PurchaseOrder')
            .update({ _id: true }, { data: { _id: document._id, status: 'confirmed', approvalStatus: 'approved' } }),
        tenant,
    ).execute();
}

export type PurchaseOrderType = Awaited<ReturnType<typeof getPurchaseOrder>>;

export async function getPurchaseOrder(
    tenant: TenantConnector,
    order: { default?: Filter<PurchaseOrder>; filter?: Filter<PurchaseOrder> },
) {
    const filter = order.default ? order.default : (order.filter ?? { isActive: true });
    const orderSelected = extractEdges(
        await new CustomRequest(
            graphPurchasing
                .node('@sage/xtrem-purchasing/PurchaseOrder')
                .query(edgesSelector({ _id: true, number: true }, { filter })),
            tenant,
        ).execute(),
    ).at(0);
    if (orderSelected) {
        return orderSelected;
    }
    if (order.default && order.filter) {
        console.log(
            `No purchase order default found with  ${JSON.stringify(order.default)} - using filter ${JSON.stringify(
                order.filter,
            )}`,
        );
        return getPurchaseOrder(tenant, { filter: order.filter });
    }
    throw new Error('No purchase order found');
}

export async function purchaseOrderMainList(tenant: TenantConnector, options: PagingOptions<PurchaseOrder>) {
    const purchaseOrders = await new CustomRequest(
        graphPurchasing.node('@sage/xtrem-purchasing/PurchaseOrder').query(
            edgesSelector(
                {
                    _id: true,
                    number: true,
                    isGrossPriceMissing: true,
                    isApprovalManaged: true,
                    totalQuantityToReceiveInStockUnit: true,
                    supplierLinkedAddress: { _id: true },
                    isOrderAssignmentLinked: true,
                    taxEngine: true,
                    taxCalculationStatus: true,
                    approvalStatus: true,
                    status: true,
                    transactionCurrency: {
                        _id: true,
                        name: true,
                        id: true,
                        symbol: true,
                        rounding: true,
                        decimalDigits: true,
                    },
                    totalTaxAmount: true,
                    totalAmountExcludingTax: true,
                    paymentTerm: { _id: true, name: true, id: true, dueDateType: true, description: true },
                    isSent: true,
                    isPrinted: true,
                    invoiceStatus: true,
                    displayStatus: true,
                    earliestExpectedDate: true,
                    totalAmountIncludingTax: true,
                    site: { _id: true, name: true, id: true },
                    orderDate: true,
                    supplier: { id: true, _id: true, businessEntity: { name: true, id: true, taxIdNumber: true } },
                },
                options,
            ),
        ),
        tenant,
    ).execute();
    return extractEdges(purchaseOrders);
}
