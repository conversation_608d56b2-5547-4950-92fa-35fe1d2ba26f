import { Graph, PagingOptions } from '@sage/xtrem-client';

import { GraphApi, Journal } from '@sage/xtrem-finance-data-api';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';

const graph = new Graph<GraphApi>({ fetcher });

export async function journalInquiry(tenant: TenantConnector, options: PagingOptions<Journal>) {
    return new CustomRequest(
        graph.node('@sage/xtrem-finance-data/Journal').query(
            {
                edges: {
                    node: { _id: true, name: true, id: true, legislation: { _id: true, name: true, id: true } },
                },
            },
            options,
        ),
        tenant,
    ).execute();
}
