import { Graph, PagingOptions } from '@sage/xtrem-client';
import { GraphApi, GraphApi as GraphApiStock } from '@sage/xtrem-stock-api';
import { StockJournal, StockReceiptDetailInput } from '@sage/xtrem-stock-data-api';
import { Site } from '@sage/xtrem-system-api';
import { sleep } from 'k6';
import { CustomRequest, fetcher } from '../tooling/functions';
import { TenantConnector } from '../tooling/tenant-connector';
import { randomInt, today } from '../tooling/utils';
import { ItemSelection } from './master-data';

const graph = new Graph<GraphApi>({ fetcher });
const graphStock = new Graph<GraphApiStock>({ fetcher });

export async function stockJournalInquiry(tenant: TenantConnector, options: PagingOptions<StockJournal>) {
    return new CustomRequest(
        graph.node('@sage/xtrem-stock-data/StockJournal').query(
            {
                edges: {
                    node: {
                        _id: true,
                        movementType: true,
                        reasonCode: {
                            _id: true,
                            name: true,
                            id: true,
                        },
                        currency: {
                            id: true,
                            _id: true,
                            name: true,
                            decimalDigits: true,
                            symbol: true,
                        },
                        stockUnit: {
                            _id: true,
                            name: true,
                            symbol: true,
                            decimalDigits: true,
                        },
                        nonAbsorbedAmount: true,
                        amountVariance: true,
                        costVariance: true,
                        movementAmount: true,
                        valuedCost: true,
                        orderAmount: true,
                        orderCost: true,
                        lot: {
                            expirationDate: true,
                            supplierLot: true,
                            sublot: true,
                            id: true,
                        },
                        status: {
                            _id: true,
                            name: true,
                            id: true,
                            type: true,
                            description: true,
                        },
                        location: {
                            locationZone: {
                                name: true,
                            },
                            id: true,
                            _id: true,
                            name: true,
                        },
                        quantityInStockUnit: true,
                        item: {
                            serialNumberManagement: true,
                            id: true,
                            _id: true,
                            name: true,
                            description: true,
                        },
                        documentLine: {
                            documentNumber: true,
                            _factory: {
                                title: true,
                                name: true,
                            },
                            documentId: true,
                        },
                        sourceDocumentLine: {
                            documentId: true,
                        },
                        effectiveDate: true,
                        site: {
                            id: true,
                            _id: true,
                            name: true,
                            description: true,
                        },
                    },
                },
            },
            options,
        ),
        tenant,
    ).execute();
}

export async function createStockReceipt(tenant: TenantConnector, items: Array<ItemSelection>, site: Site) {
    return new CustomRequest(
        graphStock.node('@sage/xtrem-stock/StockReceipt').create(
            { _id: true },
            {
                data: {
                    stockSite: site._id,
                    effectiveDate: today(),
                    lines: items.map((item, index) => {
                        const sublot = item.lotManagement === 'lotSublotManagement' ? `${index + 1}` : undefined;
                        const expirationDate = item.isExpiryManaged
                            ? new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0]
                            : undefined;
                        const lotNumber =
                            item.lotManagement !== 'notManaged' && item.lotSequenceNumber == null
                                ? `LOT-${index + 1}`
                                : '';
                        const orderCost = randomInt({ min: 1, max: 100 });
                        const valuedCost = randomInt({ min: 1, max: 100 });
                        const quantityInStockUnit = randomInt({ min: 100, max: 200 });
                        const jsonStockDetails: StockReceiptDetailInput = {
                            site: site._id,
                            item: item._id,
                            stockUnit: item.stockUnit._id,
                            effectiveDate: today(),
                            status: '#A',
                            orderCost,
                            valuedCost,
                            quantityInStockUnit,
                            movementType: 'receipt',
                            _action: 'create',
                            ...(item.lotManagement !== 'notManaged'
                                ? {
                                      jsonStockDetailLot: JSON.stringify({
                                          ...(sublot ? { sublot } : {}),
                                          ...(expirationDate ? { expirationDate } : {}),
                                          lotNumber,
                                      }),
                                  }
                                : {}),
                        };
                        return {
                            _action: 'create',
                            item: item._id,
                            orderCost,
                            valuedCost,
                            quantityInStockUnit,
                            jsonStockDetails: JSON.stringify([jsonStockDetails]),
                        };
                    }),
                },
            },
        ),
        tenant,
    ).execute();
}

export async function postStockReceipt(tenant: TenantConnector, receipt: { _id: string }) {
    return new CustomRequest(
        graphStock.node('@sage/xtrem-stock/StockReceipt').mutations.postToStock(true, { documentIds: [receipt._id] }),
        tenant,
    ).execute();
}

// type of readReceipt function return
export type receiptType = Awaited<ReturnType<typeof readReceipt>>;

export async function checkPostStockReceipt(tenant: TenantConnector, receipt: { _id: string }, maxWaitTime = 3_000) {
    let waitTime = maxWaitTime;
    let result = await readReceipt(tenant, receipt);
    while (waitTime > 0) {
        waitTime -= 5;
        result = await readReceipt(tenant, receipt);
        switch (result.displayStatus) {
            case 'received':
            case 'stockPostingError':
                return result;
            default:
                break;
        }
        sleep(2);
    }
    console.warn('Timeout waiting for the post - ');
    return { ...result, displayStatus: 'timeout' };
}

export async function readReceipt(tenant: TenantConnector, receipt: { _id: string }) {
    return new CustomRequest(
        graphStock.node('@sage/xtrem-stock/StockReceipt').read(
            {
                _id: true,
                number: true,
                displayStatus: true,
                stockTransactionStatus: true,
            },
            receipt._id,
        ),
        tenant,
    ).execute();
}
