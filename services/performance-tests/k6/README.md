# Build the scenarios

## pnpm build

    Will do the type check to be sure dev don't break scenarios

## Run the tests

### Manufacturing

    - Create a work order :                     run:work-order

### MasterData

    - Create/Delete items :                         run:item-crud
    - Create multiple items :                       run:item

### Validation flows

    - Full validation flow :                        run:flow
    - Performance validation flow with duplicate    run:perf

### Purchase

    - Create purchase order :                       run:create-order

### Sales

    - Create sales order                            run:sales-order

### Technical Data

    - Create BOM                                    run:bom

## Test on local environements

On the flow file tenant = new TenantConnector() with no parameters will connect to localhost
