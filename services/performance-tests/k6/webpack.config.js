const path = require('path');
const { CleanWebpackPlugin } = require('clean-webpack-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const GlobEntries = require('webpack-glob-entries');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin'); // Import the plugin
const HtmlWebpackPlugin = require('html-webpack-plugin');


module.exports = {
    mode: 'production',
    entry: GlobEntries('./src/**/*.ts'),
    output: {
        path: path.join(__dirname, 'dist'),
        libraryTarget: 'commonjs',
        filename: '[name].js',
    },
    resolve: { extensions: ['.ts', '.js'], },
    module: {
        rules: [
            { test: /\.ts$/, use: 'babel-loader', exclude: /node_modules/, },
            { test: /\.hbs$/, use: 'handlebars-loader', exclude: /node_modules/, }
        ],
    },
    target: 'web',
    externals: /^(k6|https?\:\/\/)(\/.*)?/,
    // Generate map files for compiled scripts
    devtool: "source-map",
    stats: { colors: true, },
    plugins: [
        new CleanWebpackPlugin(),
        // Copy assets to the destination folder
        // see `src/post-file-test.ts` for an test example using an asset
        new CopyPlugin({
            patterns: [{
                from: path.resolve(__dirname, 'assets'),
                noErrorOnMissing: true
            }],
        }),
        new ForkTsCheckerWebpackPlugin(),
        new HtmlWebpackPlugin()
    ],
    optimization: {
        // Don't minimize, as it's not used in the browser
        minimize: false,
    },
};
