{"extends": "../../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "composite": true}, "include": ["api.d.ts"], "references": [{"path": "../../../../platform/system/xtrem-authorization/api"}, {"path": "../../../../platform/front-end/xtrem-client"}, {"path": "../../../../platform/system/xtrem-communication/api"}, {"path": "../../../shared/xtrem-master-data/api"}, {"path": "../../../shared/xtrem-mrp-data/api"}, {"path": "../../../applications/xtrem-purchasing/api"}, {"path": "../../../applications/xtrem-sales/api"}, {"path": "../../../applications/xtrem-stock/api"}, {"path": "../../../shared/xtrem-stock-data/api"}, {"path": "../../../../platform/system/xtrem-system/api"}]}