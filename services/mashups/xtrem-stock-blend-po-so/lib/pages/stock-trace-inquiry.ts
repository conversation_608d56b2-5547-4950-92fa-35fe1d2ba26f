import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { Item } from '@sage/xtrem-master-data-api';
import type { GraphApi, Lot, SerialNumber, Stock, StockJournal } from '@sage/xtrem-stock-data-api';
import * as DocumentLink from '@sage/xtrem-stock-data/build/lib/client-functions/document-link';
import { StockInquiries } from '@sage/xtrem-stock-data/build/lib/menu-items/stock-inquiries';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<StockTraceInquiry>({
    menuItem: StockInquiries,
    priority: 200,
    title: 'Item traceability',
    module: 'stock',
    mode: 'default',
    isTransient: true,
    access: { node: '@sage/xtrem-stock-data/StockJournal' },
    onLoad() {
        this.showHideSearchButton();
    },
})
export class StockTraceInquiry extends ui.Page<GraphApi> {
    @ui.decorators.section<StockTraceInquiry>({
        isOpen: true,
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<StockTraceInquiry>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
        title: 'Criteria',
    })
    criteriaBlock: ui.containers.Block;

    @ui.decorators.tile<StockTraceInquiry>({
        parent() {
            return this.mainSection;
        },
    })
    tileContainer: ui.containers.Tile;

    @ui.decorators.numericField<StockTraceInquiry>({
        parent() {
            return this.tileContainer;
        },
        title: 'On hand',
        scale() {
            return this.item.value?.stockUnit?.decimalDigits || 0;
        },
        postfix() {
            return this.item.value?.stockUnit?.symbol || '';
        },
    })
    totalOnHandQuantity: ui.fields.Numeric;

    @ui.decorators.referenceField<StockTraceInquiry, Lot>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Lot',
        node: '@sage/xtrem-stock-data/Lot',
        valueField: 'id',
        columns: [
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.text({ title: 'Sublot', bind: 'sublot' }),
            ui.nestedFields.reference<StockTraceInquiry, Lot, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.reference({
                        title: 'Unit',
                        bind: 'stockUnit',
                        valueField: 'symbol',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
        ],
        placeholder: 'Select lot',
        lookupDialogTitle: 'Select lot',
        minLookupCharacters: 0,
        width: 'small',
        onChange() {
            if (this.lot.value) {
                this.serialNumber.value = null;
            }
            this.item.value = this.lot.value?.item || null;
            this.site.value = null;
            this.showHideSearchButton();
        },
    })
    lot: ui.fields.Reference<Lot>;

    @ui.decorators.referenceField<StockTraceInquiry, SerialNumber>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Serial number',
        node: '@sage/xtrem-stock-data/SerialNumber',
        valueField: 'id',
        columns: [
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isInStock' }),
            ui.nestedFields.reference<StockTraceInquiry, SerialNumber, Item>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                tunnelPage: '@sage/xtrem-master-data/Item',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'lotManagement' }),
                    ui.nestedFields.reference({
                        title: 'Unit',
                        bind: 'stockUnit',
                        valueField: 'symbol',
                        node: '@sage/xtrem-master-data/UnitOfMeasure',
                        tunnelPage: '@sage/xtrem-master-data/UnitOfMeasure',
                        columns: [
                            ui.nestedFields.technical({ bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'decimalDigits' }),
                            ui.nestedFields.technical({ bind: 'symbol' }),
                        ],
                    }),
                ],
            }),
            ui.nestedFields.reference<StockTraceInquiry, SerialNumber, Site>({
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                isHidden: true,
                columns: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        placeholder: 'Select serial number',
        lookupDialogTitle: 'Select serial number',
        minLookupCharacters: 0,
        width: 'small',
        isHidden() {
            return !this.$.isServiceOptionEnabled('serialNumberOption');
        },
        onChange() {
            if (this.serialNumber.value) {
                this.lot.value = null;
            }
            this.item.value = this.serialNumber.value?.item || null;
            this.site.value = null;
            this.showHideSearchButton();
        },
    })
    serialNumber: ui.fields.Reference<SerialNumber>;

    @ui.decorators.referenceField<StockTraceInquiry, Site>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Site',
        node: '@sage/xtrem-system/Site',
        valueField: 'name',
        helperTextField: 'id',
        width: 'small',
        isReadOnly: true,
    })
    site: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<StockTraceInquiry, Item>({
        parent() {
            return this.criteriaBlock;
        },
        title: 'Item',
        node: '@sage/xtrem-master-data/Item',
        tunnelPage: '@sage/xtrem-master-data/Item',
        valueField: 'name',
        helperTextField: 'id',
        isReadOnly: true,
    })
    item: ui.fields.Reference<Item>;

    @ui.decorators.tableField<StockTraceInquiry>({
        isHelperTextHidden: true,
        isTransient: true,
        canUserHideColumns: false,
        canSelect: false,
        canExport: true,
        title: 'Results',
        isReadOnly: true,
        parent() {
            return this.mainSection;
        },
        columns: [
            ui.nestedFields.date({
                bind: 'effectiveDate',
                title: 'Date',
            }),
            ui.nestedFields.text({
                bind: 'documentType',
                title: 'Document type',
            }),
            ui.nestedFields.link({
                isTransient: true,
                isFullWidth: true,
                bind: '_id', // Dummy link to a string; value is set in the map, as we cannot use valueField like syntax in bind of link field type
                title: 'Document',
                map(_value, rowValue) {
                    return `${rowValue?.documentLine?.documentNumber}`;
                },
                onClick(_id, data: ui.PartialCollectionValue<StockJournal>) {
                    const page = DocumentLink.getDocumentPageName(data?.documentLine?._constructor || '');
                    return this.$.dialog.page(
                        page,
                        { _id: data?.documentLine?.documentId || '' },
                        {
                            fullScreen: true,
                            resolveOnCancel: true,
                        },
                    );
                },
            }),
            ui.nestedFields.numeric({
                bind: 'quantityInStockUnit',
                title: 'Quantity',
                scale(_rowId, rowData) {
                    return rowData?.stockUnit?.decimalDigits || 0;
                },
                postfix(_rowId, rowData) {
                    return rowData?.stockUnit?.symbol || '';
                },
            }),
            ui.nestedFields.text({
                bind: 'customer',
                title: 'Customer',
            }),
            ui.nestedFields.text({
                bind: 'supplier',
                title: 'Supplier',
            }),
            ui.nestedFields.reference({
                bind: 'lot',
                node: '@sage/xtrem-stock-data/Lot',
                valueField: 'lot',
                title: 'Lot',
                isHidden() {
                    return (
                        this.lot.value !== null ||
                        (this.serialNumber.value !== null && this.item.value?.lotManagement === 'notManaged')
                    );
                },
            }),
        ],
    })
    stockJournals: ui.fields.Table;

    @ui.decorators.buttonField<StockTraceInquiry>({
        parent() {
            return this.criteriaBlock;
        },
        map() {
            return ui.localize('@sage/xtrem-stock-blend-po-so/search', 'Search');
        },
        width: 'small',
        async onClick() {
            this.$.loader.isHidden = false;
            await this.search();
            this.$.loader.isHidden = true;
        },
    })
    searchButton: ui.fields.Button;

    static setLines(stockJournal: any): object {
        switch (stockJournal.documentLine._constructor) {
            case 'SalesReturnReceiptLine':
            case 'SalesShipmentLine':
                return { ...stockJournal, customer: stockJournal.documentLine.document.shipToCustomer.name };
            case 'PurchaseReceiptLine':
            case 'PurchaseReturnLine':
                return { ...stockJournal, supplier: stockJournal.documentLine.document.supplier.name };
            default:
                return stockJournal;
        }
    }

    async searchLot(stockJournalProperties: string) {
        if (this.lot.value) {
            const filter1 = `lot: { id: '${this.lot.value.id}'}, item: ${this.lot.value.item?._id}, quantityInStockUnit: {_ne: 0}`;
            const results1 = await this.$.graph.raw(
                `{ xtremStockData { stockJournal { query (filter:"{${filter1}}",first:500) { edges { node {
                ${stockJournalProperties}
                }}}}}}`,
            );

            // cannot remove any when using inline fragments in a graphQL query
            this.stockJournals.value = results1?.xtremStockData?.stockJournal?.query?.edges.map((result: any) =>
                StockTraceInquiry.setLines(result.node),
            );

            const filter2: Filter<Stock> = {
                _and: [{ item: this.lot.value.item?._id }, { lot: { id: this.lot.value.id } }],
            };
            const results2 = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-stock-data/Stock')
                    .aggregate.query(
                        ui.queryUtils.edgesSelector(
                            {
                                group: {
                                    site: {
                                        _id: { _by: 'value' },
                                        id: { _by: 'value' },
                                        name: { _by: 'value' },
                                    },
                                },
                                values: {
                                    quantityInStockUnit: {
                                        sum: true,
                                    },
                                },
                            },
                            { filter: filter2, first: 500 },
                        ),
                    )
                    .execute(),
            );

            this.site.value = results2.length === 1 ? results2[0].group.site : null;
            this.totalOnHandQuantity.value = results2.reduce(
                (acc, result) => acc + Number(result.values.quantityInStockUnit?.sum),
                0,
            );
        }
    }

    async searchSerialNumber(stockJournalProperties: string) {
        if (this.serialNumber.value) {
            const results = await this.$.graph.raw(
                `{ xtremStockData { stockJournalSerialNumber { query (filter:"{serialNumber: ${this.serialNumber.value._id}, stockMovement: {quantityInStockUnit: {_ne: 0}}}", first:500) { edges { node {
                    stockMovement {
                        ${stockJournalProperties}
                    }
                }}}}}}`,
            );

            // cannot remove any when using inline fragments in a graphQL query
            this.stockJournals.value = results?.xtremStockData?.stockJournalSerialNumber?.query?.edges.map(
                (result: any) => ({
                    ...StockTraceInquiry.setLines(result.node.stockMovement),
                    quantityInStockUnit: String(Math.sign(+result.node.stockMovement.quantityInStockUnit)),
                }),
            );

            this.site.value = this.serialNumber.value.site || null;
            this.totalOnHandQuantity.value = this.serialNumber.value.isInStock ? 1 : 0;
        }
    }

    async search() {
        if (!this.lot.value && !this.serialNumber.value) {
            this.stockJournals.value = [];
        } else {
            const stockJournalProperties = `_id
        effectiveDate
        documentType
        documentLine {
            _constructor
            documentNumber
            documentId
            _constructor ... on SalesShipmentLine { document { shipToCustomer { name }}}
            _constructor ... on SalesReturnReceiptLine { document { shipToCustomer { name }}}
            _constructor ... on PurchaseReceiptLine { document { supplier{ name }}}
            _constructor ... on PurchaseReturnLine { document { supplier { name }}}
        }
        quantityInStockUnit
        stockUnit {
            id
            name
            symbol
            decimalDigits
        }
        lot {
            id
            sublot
        }`;

            if (this.lot.value) {
                await this.searchLot(stockJournalProperties);
            } else {
                await this.searchSerialNumber(stockJournalProperties);
            }
        }
        this.$.setPageClean();
    }

    showHideSearchButton() {
        this.searchButton.isDisabled = this.lot.value === null && this.serialNumber.value === null;
    }
}
