{"extends": "../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": "."}, "include": ["index.ts", "application.ts", "lib/**/*", "test/**/*.ts", "test/**/*.json", "api/api.d.ts"], "exclude": ["lib/pages/**/*", "lib/widgets/**/*", "lib/page-extensions/**/*", "lib/page-fragments/**/*", "lib/stickers/**/*", "lib/i18n/**/*", "**/*.feature", "**/*.png", "lib/client-functions/**/*"], "references": [{"path": "../../../platform/system/xtrem-authorization"}, {"path": "../../../platform/front-end/xtrem-client"}, {"path": "../../../platform/system/xtrem-communication"}, {"path": "../../../platform/back-end/xtrem-core"}, {"path": "../../../platform/shared/xtrem-date-time"}, {"path": "../../../platform/shared/xtrem-decimal"}, {"path": "../../shared/xtrem-master-data"}, {"path": "../../shared/xtrem-mrp-data"}, {"path": "../../applications/xtrem-purchasing"}, {"path": "../../applications/xtrem-sales"}, {"path": "../../../platform/shared/xtrem-shared"}, {"path": "../../applications/xtrem-stock"}, {"path": "../../shared/xtrem-stock-data"}, {"path": "../../../platform/system/xtrem-system"}, {"path": "../../../platform/front-end/xtrem-ui"}, {"path": "../../../platform/back-end/eslint-plugin-xtrem"}, {"path": "../../../platform/cli/xtrem-cli"}, {"path": "../../../platform/back-end/xtrem-config"}, {"path": "../../shared/xtrem-master-data/api"}, {"path": "../../shared/xtrem-mrp-data/api"}, {"path": "../../applications/xtrem-purchasing/api"}, {"path": "../../../platform/system/xtrem-routing"}, {"path": "../../applications/xtrem-sales/api"}, {"path": "../../applications/xtrem-stock/api"}, {"path": "../../shared/xtrem-stock-data/api"}, {"path": "../../../platform/system/xtrem-system/api"}]}