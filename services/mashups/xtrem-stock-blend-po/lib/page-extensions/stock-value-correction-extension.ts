import { extractEdges } from '@sage/xtrem-client';
import type { PurchaseReceiptLine } from '@sage/xtrem-purchasing-api';
import type { StockValueCorrectionLineBinding } from '@sage/xtrem-stock-api';
import type { GraphApi } from '@sage/xtrem-stock-blend-po-api';
import type { StockValueCorrection } from '@sage/xtrem-stock/build/lib/pages/stock-value-correction';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';

@ui.decorators.pageExtension<StockValueCorrectionExtension>({
    extends: '@sage/xtrem-stock/StockValueCorrection',
    async onLoad() {
        if (this.$.recordId) {
            if (this.lines.value.length) {
                if (this.lines.value[0].correctedDocumentType === 'purchaseReceipt') {
                    this.correctedDocumentType = 'purchaseReceipt';
                    this._correctedDocumentType.value = this._correctedDocumentType.options?.at(0) ?? null;
                    const queryRequest = this.$.graph.node('@sage/xtrem-purchasing/PurchaseReceiptLine').query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                _sortValue: true,
                                document: {
                                    number: true,
                                    stockSite: {
                                        _id: true,
                                    },
                                },
                                orderCost: true,
                            },
                            {
                                filter: {
                                    _id: {
                                        _in: this.lines.value.reduce(
                                            (
                                                lines: PartialCollectionValueWithIds<StockValueCorrectionLineBinding>[],
                                                line: PartialCollectionValueWithIds<StockValueCorrectionLineBinding>,
                                            ) =>
                                                lines.concat(
                                                    line.correctedDocumentLine
                                                        ?._id as unknown as PartialCollectionValueWithIds<StockValueCorrectionLineBinding>,
                                                ),
                                            [],
                                        ),
                                    },
                                },
                            },
                        ),
                    );
                    const lineMissingInfo = await queryRequest.execute();
                    if (lineMissingInfo.edges.length) {
                        this.lines.value = this.lines.value.map(line => {
                            const currentCorrectedLine = line.correctedDocumentLine;
                            const queryCorrectedLine: any = extractEdges(lineMissingInfo).find(
                                (line2: any) => line2._id === currentCorrectedLine?._id,
                            );
                            return {
                                ...line,
                                _correctedPurchaseDocumentLine: {
                                    ...currentCorrectedLine,
                                    _sortValue: queryCorrectedLine._sortValue,
                                    orderCost: queryCorrectedLine.orderCost,
                                    document: queryCorrectedLine.document,
                                },
                            };
                        });
                    }
                    await this.lines.redraw();
                }
            }
        }
    },
})
export class StockValueCorrectionExtension extends ui.PageExtension<StockValueCorrection, GraphApi> {
    @ui.decorators.dropdownListFieldOverride<StockValueCorrectionExtension>({
        // impossible to use optionType for this field maybe because it's transient
        // optionType: '@sage/xtrem-stock/AdjustableDocumentType',
        // options: ['stockReceipt', 'purchaseReceipt'],
        options() {
            return [
                ui.localizeEnumMember('@sage/xtrem-stock/AdjustableDocumentType', 'stockReceipt'),
                ui.localizeEnumMember('@sage/xtrem-stock/AdjustableDocumentType', 'purchaseReceipt'),
            ];
        },
        async onChange() {
            if (this._correctedDocumentType.value === this._correctedDocumentType.options?.at(1)) {
                this.correctedDocumentType = 'purchaseReceipt';
            } else if (this._correctedDocumentType.value === this._correctedDocumentType.options?.at(0)) {
                this.correctedDocumentType = 'stockReceipt';
            }
            await this.lines.redraw();
        },
    })
    _correctedDocumentType: ui.fields.DropdownList;

    @ui.decorators.tableFieldOverride<StockValueCorrectionExtension, StockValueCorrectionLineBinding>({
        columns: [
            ui.nestedFieldExtensions.reference<
                StockValueCorrectionExtension,
                StockValueCorrectionLineBinding,
                PurchaseReceiptLine
            >({
                title: 'Document line',
                bind: '_correctedPurchaseDocumentLine' as any,
                node: '@sage/xtrem-purchasing/PurchaseReceiptLine' as any,
                valueField: { document: { number: true } },
                minLookupCharacters: 0,
                isTransient: true,
                insertBefore: 'unitCost',
                columns: [
                    ui.nestedFields.reference({
                        bind: 'document',
                        valueField: 'number',
                        node: '@sage/xtrem-purchasing/PurchaseReceipt' as any,
                        title: 'document number',
                        columns: [
                            ui.nestedFields.reference({
                                bind: 'stockSite',
                                valueField: '_id',
                                node: '@sage/xtrem-system/Site',
                                title: 'Site',
                                isHidden: true,
                            }),
                        ],
                    }),
                    ui.nestedFields.numeric({ bind: 'orderCost', title: 'Order cost' }),
                    ui.nestedFields.text({ bind: '_sortValue', title: 'line number' }),
                ],
                filter(this, rowValue) {
                    return {
                        item: { _id: rowValue.item._id },
                        document: { stockSite: { _id: this.stockSite.value?._id } },
                        stockTransactionStatus: 'completed',
                    };
                },
                isReadOnly(this: any, _rowId, rowData) {
                    return this.lineStatusReadonly(rowData?.stockTransactionStatus);
                },
                isHidden() {
                    return this._correctedDocumentType.value !== this._correctedDocumentType.options?.at(1);
                },
                onChange(_id, value) {
                    const line = this.lines.getRecordValue(value._id);
                    if (line) {
                        line.orderCost = value._correctedPurchaseDocumentLine.orderCost;
                        line.correctedDocumentLine = value._correctedPurchaseDocumentLine._id;
                        this.lines.addOrUpdateRecordValue(line);
                    }
                },
            }),
        ],
    })
    lines: ui.fields.Table<StockValueCorrectionLineBinding>;
}

declare module '@sage/xtrem-stock/build/lib/pages/stock-value-correction' {
    export interface StockValueCorrection extends StockValueCorrectionExtension {}
}
