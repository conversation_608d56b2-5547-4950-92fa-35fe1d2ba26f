{"extends": "../../../tsconfig-base.json", "compilerOptions": {"outDir": "build", "rootDir": ".", "baseUrl": ".", "composite": true}, "include": ["api.d.ts"], "references": [{"path": "../../../../platform/system/xtrem-authorization/api"}, {"path": "../../../../platform/front-end/xtrem-client"}, {"path": "../../../../platform/system/xtrem-communication/api"}, {"path": "../../../../platform/system/xtrem-dashboard/api"}, {"path": "../../../../platform/system/xtrem-mailer/api"}, {"path": "../../../../platform/system/xtrem-reporting/api"}, {"path": "../../xtrem-structure/api"}, {"path": "../../../../platform/system/xtrem-system/api"}]}