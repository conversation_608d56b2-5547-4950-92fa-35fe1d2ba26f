import type { Node, NodeControlFilters } from '@sage/xtrem-core';
import type * as xtremSystem from '@sage/xtrem-system';
/** not used for now waiting for - https://jira.sage.com/browse/XT-99026 */

type NodeWithSite = Node & {
    site: Promise<xtremSystem.nodes.Site>;
};

export function siteBelongSameCompany(
    instance: NodeWithSite,
): NodeControlFilters<xtremSystem.nodes.Site & Node, NodeWithSite> {
    return [
        {
            filter: {
                async legalCompany() {
                    return (await (await instance.site)?.legalCompany)?._id;
                },
            },
            getErrorMessage() {
                return instance.$.context.localize(
                    '@sage/xtrem-master-data/nodes__base_document__stock_site_legal_company_mismatch',
                    'The site needs to belong to the same legal company.',
                );
            },
        },
    ];
}

export function siteIsInventory(
    instance: NodeWithSite,
): NodeControlFilters<xtremSystem.nodes.Site & Node, NodeWithSite> {
    return [
        {
            filter: { isInventory: true },
            getErrorMessage() {
                return instance.$.context.localize(
                    '@sage/xtrem-master-data/nodes__base_document__stock_site_is_not_inventory',
                    'The site needs to be a stock site.',
                );
            },
        },
    ];
}

export function siteIsFinancial(
    instance: NodeWithSite,
): NodeControlFilters<xtremSystem.nodes.Site & Node, NodeWithSite> {
    return [
        {
            filter: { isFinance: true },
            getErrorMessage() {
                return instance.$.context.localize(
                    '@sage/xtrem-master-data/nodes__base_document__financial_site_is_not_financial',
                    'The site needs to be a financial site.',
                );
            },
        },
    ];
}
