import type {
    AnyValue,
    AsyncResponse,
    BulkUpdateOptions,
    Context,
    NodeCreateData,
    NodeFactory,
    NodeKey,
    StaticThis,
    ValidationContext,
    integer,
} from '@sage/xtrem-core';
import { BusinessRuleError, NodeStatus, date } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import type * as xtremSystem from '@sage/xtrem-system';
import { searchSequenceNumber } from '../functions/sequence-number-assignement';
import { sequenceNumberGeneratorLibrary as lib } from '../functions/sequence-number-lib';
import {
    chronologicalControlMustBeActive,
    documentDateCannotBeLaterThanToday,
    documentDateEarlierThanPreviousDocumentDate,
    documentDateLaterThanNextDocumentDate,
    monthlySequenceNumbersNotAllowed,
    nodeInstanceIsRequired,
} from '../functions/sequence-number-localized';
import * as xtremMasterData from '../index';
import { NumberGenerator } from './number-generator';

/**
 * This class generate a document sequence number
 */
export class DocumentNumberGenerator extends NumberGenerator {
    constructor(
        override readonly context: Context,
        override readonly parameters: xtremMasterData.interfaces.DocumentNumberGenerator,
    ) {
        super(context, { sequenceNumber: parameters.sequenceNumber, site: parameters.site });
        this.logger.debug(() => `DocumentNumberGenerator constructor`);
    }

    static async create(
        context: Context,
        parameters: xtremMasterData.interfaces.DocumentNumberGenerator,
    ): Promise<DocumentNumberGenerator> {
        const generator = new DocumentNumberGenerator(context, parameters);

        if (!parameters.skipControls) await generator.controls();
        return generator;
    }

    async getControlParameters(): Promise<xtremMasterData.interfaces.ChronologicalControl> {
        if (this.parameters.nodeInstance) {
            return {
                documentNumber: await this.documentNumber,
                sequenceNumber: await this.sequenceNumberInstance,
                site: (await this.site) ?? undefined,
                currentDate: await this.currentDate,
                numberField: this.numberField,
                dateField: this.documentDateField,
                node: this.parameters.nodeInstance.$.factory.nodeConstructor,
            };
        }
        return {
            sequenceNumber: await this.sequenceNumberInstance,
            site: await this.site,
            currentDate: this.parameters.currentDate ?? date.today(),
        };
    }

    /** Method to control the parameters are ok  */
    override async controls(cx?: ValidationContext): Promise<void> {
        await super.controls(cx);
        if (this.isPosting || this.action === NodeStatus.added) {
            await this.creationControl();
        }
        if (this.parameters.nodeInstance && (await this.documentNumber)) {
            await this.chronologicalControl();
        }
    }

    #sequenceNumberNodeValueIndexes: NodeKey<xtremMasterData.nodes.SequenceNumberValue>;

    /**
     * @internal
     */
    private get sequenceNumberNodeValueIndexes(): Promise<NodeKey<xtremMasterData.nodes.SequenceNumberValue>> {
        return (async () => {
            if (!this.#sequenceNumberNodeValueIndexes) {
                this.#sequenceNumberNodeValueIndexes = await this.resolveSequenceNumberValueNodeIndexes(
                    (await this.site) ?? null,
                    (await this.company) ?? null,
                    await this.currentDate,
                    this.parameters.complementary ?? '',
                );
            }
            return this.#sequenceNumberNodeValueIndexes;
        })();
    }

    /** SequenceNumberComponent containing the type SequenceNumber  */
    #componentWithSequenceNumberType: xtremMasterData.nodes.SequenceNumberComponent;

    /**
     * @internal
     *  Component with type = sequenceNumber
     */
    get componentWithSequenceNumberType(): Promise<xtremMasterData.nodes.SequenceNumberComponent | null> {
        return (async () => {
            if (!this.#componentWithSequenceNumberType) {
                this.#componentWithSequenceNumberType = await lib.getComponentsWithSequenceNumberType(
                    await this.sequenceNumberInstance,
                );
            }
            return this.#componentWithSequenceNumberType;
        })();
    }

    /** The number to allocate  */
    private uGeneratedSequence: integer;

    /**
     * Last generated Sequence number - undefined if no allocation done
     */
    get generatedSequence(): integer {
        return this.uGeneratedSequence;
    }

    get legislation(): Promise<xtremStructure.nodes.Legislation | undefined> {
        return (async () => {
            if (this.parameters.legislation) {
                return typeof this.parameters.legislation === 'string'
                    ? this.context.read(xtremStructure.nodes.Legislation, { id: this.parameters.legislation })
                    : this.parameters.legislation;
            }
            return (await this.company)?.legislation;
        })();
    }

    get legislationSysId(): Promise<number | undefined> {
        return (async () => {
            return (await this.legislation)?._id;
        })();
    }

    get legislationId(): Promise<string | undefined> {
        return (async () => {
            return (await this.legislation)?.id;
        })();
    }

    async getSequenceNumberFromParameters(): Promise<xtremMasterData.nodes.SequenceNumber | null> {
        if (this.parameters.sequenceNumber) {
            if (typeof this.parameters.sequenceNumber === 'string') {
                const sequenceNumber =
                    (await this.context.tryRead(xtremMasterData.nodes.SequenceNumber, {
                        id: this.parameters.sequenceNumber,
                        legislation: (await this.legislationSysId) ?? null,
                    })) ||
                    (await this.context.read(xtremMasterData.nodes.SequenceNumber, {
                        id: this.parameters.sequenceNumber,
                        legislation: null,
                    }));
                if (sequenceNumber) return sequenceNumber;
            }

            if (this.parameters.sequenceNumber instanceof xtremMasterData.nodes.SequenceNumber)
                return this.parameters.sequenceNumber;
        }

        return null;
    }

    override get sequenceNumberInstance(): Promise<xtremMasterData.nodes.SequenceNumber> {
        return (async () => {
            if (this.storedSequenceNumberInstance) return this.storedSequenceNumberInstance;

            const sequenceNumber = await this.getSequenceNumberFromParameters();

            if (this.parameters.nodeInstance) {
                const storedSequenceNumberInstance = await searchSequenceNumber(
                    this.context,
                    {
                        nodeFactoryName: this.parameters.nodeInstance.$.factory.name,
                        ...(sequenceNumber ? { sequenceNumber } : {}),
                        ...((await this.site) ? { site: (await this.site)?._id } : {}),
                        ...((await this.company) ? { company: (await this.company)?._id } : {}),
                        ...((await this.legislationSysId) ? { legislation: await this.legislationSysId } : {}),
                        isAssignOnPosting: this.isPosting,
                        throwError: true,
                    },
                    this.logger,
                );
                if (storedSequenceNumberInstance) {
                    this.storedSequenceNumberInstance = storedSequenceNumberInstance;
                    return this.storedSequenceNumberInstance;
                }
            }

            if (sequenceNumber) {
                this.storedSequenceNumberInstance = sequenceNumber;
                return sequenceNumber;
            }

            throw new BusinessRuleError('No sequence number found');
        })();
    }

    async getDocumentDate(number: string): Promise<date | undefined> {
        if (!this.parameters.nodeInstance) {
            return undefined;
        }
        const node = this.parameters.nodeInstance.$.factory.nodeConstructor;

        const document = await this.context.tryRead(node, { [this.numberField ?? 'number']: number });
        if (!document) return undefined;
        const documentDate = await document.$.getValue(this.documentDateField ?? 'date');

        return documentDate as date;
    }

    /**
     * chronologicalControlOnCreation
     *  Chronological control on document creation
     * @param context Current context
     * @param previousDocumentNumber Previous document number to maintain the chronological order
     * @param parameters Parameters for node reading
     */
    private async chronologicalControlPrevious(previousDocumentNumber?: string): Promise<void> {
        const { context } = this;
        this.logger.debug(() => ` ** previousDocumentNumber : ${previousDocumentNumber}`);

        if (previousDocumentNumber) {
            const previousDocumentDate = await this.getDocumentDate(previousDocumentNumber);
            const currentDate = await this.currentDate;

            if (previousDocumentDate && previousDocumentDate > currentDate) {
                const message = documentDateEarlierThanPreviousDocumentDate(context, {
                    current: currentDate,
                    previousDocument: previousDocumentDate,
                });
                this.error(message);
            }
        }
    }

    /**
     * chronologicalControlOnCreation
     *  Chronological control on document creation
     * @param context Current context
     * @param previousDocumentNumber Previous document number to maintain the chronological order
     * @param parameters Parameters for node reading
     */
    private async chronologicalControlNext(previousDocumentNumber: string): Promise<void> {
        const nextDocumentDate = await this.getDocumentDate(previousDocumentNumber);
        const currentDate = await this.currentDate;
        if (nextDocumentDate && nextDocumentDate < currentDate) {
            const message = documentDateLaterThanNextDocumentDate(this.context, {
                current: currentDate,
                nextDocument: nextDocumentDate,
            });
            this.error(message);
        }
    }

    async chronologicalControl(): Promise<void> {
        this.logger.debug(() => ` ** chronologicalControl ** `);
        const sequenceNumberInstance = await this.sequenceNumberInstance;
        if (!(await sequenceNumberInstance.isChronological)) {
            return;
        }

        const documentNumber = await this.documentNumber;
        if (!documentNumber) {
            return;
        }
        const previousSequenceNumber = await xtremMasterData.functions.getPreviousSequenceNumber(
            documentNumber,
            sequenceNumberInstance,
        );
        this.logger.debug(() => ` ** previousSequenceNumber : ${previousSequenceNumber}`);
        if (previousSequenceNumber && previousSequenceNumber !== documentNumber) {
            await this.chronologicalControlPrevious(previousSequenceNumber);
        }

        const nextSequenceNumber = await xtremMasterData.functions.getNextSequenceNumber(
            documentNumber,
            sequenceNumberInstance,
        );
        this.logger.debug(() => ` ** nextSequenceNumber : ${nextSequenceNumber}`);
        if (nextSequenceNumber && nextSequenceNumber !== documentNumber) {
            await this.chronologicalControlNext(nextSequenceNumber);
        }
    }

    /**
     * Resolve the indexes values of the table DocumentSequenceValue
     * @param site
     * @param company
     * @param companyPeriods
     * @param currentDate
     * @param complementary
     * @returns object of sequenceNumber , site , company , period , additionalInfo
     */
    private async resolveSequenceNumberValueNodeIndexes(
        site: xtremSystem.nodes.Site | null,
        company: xtremSystem.nodes.Company | null,
        currentDate: date,
        complementary: string,
    ): Promise<{
        sequenceNumber: xtremMasterData.nodes.SequenceNumber;
        site: xtremSystem.nodes.Site | null;
        company: xtremSystem.nodes.Company | null;
        period: number;
        additionalInfo: string;
    }> {
        return {
            sequenceNumber: await this.sequenceNumber,
            site: await lib.resolveSequenceNumberComponents.site(await this.sequenceNumber, site),
            company: await lib.resolveSequenceNumberComponents.company(await this.sequenceNumber, site, company),
            period: await lib.resolveSequenceNumberComponents.period(await this.sequenceNumber, currentDate),
            additionalInfo: complementary || '',
        };
    }

    /**
     * Generate a normal sequence value according to the number of sequence. The sequence will be saved on database.
     * return the sequenceValue to Allocate
     * @param sequenceNumberNodeValueIndexes
     * @param numberOfSequences number of additional value we want to generate set to 0 if normal
     */
    private async generateNormalSequenceValue(numberOfSequences: integer): Promise<integer> {
        let sequenceNumberValue = await this.context.tryRead(
            xtremMasterData.nodes.SequenceNumberValue,
            await this.sequenceNumberNodeValueIndexes,
            { forUpdate: true },
        );
        if (!sequenceNumberValue) {
            this.logger.debug(() => `SequenceNumberValue not found`);

            // here we need to create the sequenceNumberValue record in a separated committed context because we
            // can face a situation that multiple create document mutation are called in parallel and they all want
            // to create the same record in the database.
            const seqValueSysId = await this.createSequenceValueRecord();
            const seqValueSysIdKey =
                seqValueSysId === 0 ? await this.sequenceNumberNodeValueIndexes : { _id: seqValueSysId };

            // try to read the sequenceNumberValue again
            sequenceNumberValue = await this.context.read(xtremMasterData.nodes.SequenceNumberValue, seqValueSysIdKey, {
                forUpdate: true,
            });
        }

        await sequenceNumberValue.$.set({
            sequenceValue: (await sequenceNumberValue.sequenceValue) + 1 + numberOfSequences,
        });
        await sequenceNumberValue.$.save();

        return (await sequenceNumberValue.sequenceValue) - (1 + numberOfSequences);
    }

    /**
     * Creates a new sequence value record in the database.
     * If the record already exists, it logs a warning message and continues.
     * @param sequenceNumberNodeValueIndexes - The indexes for the sequence value record.
     * @returns A Promise that resolves when the operation is complete.
     */
    private createSequenceValueRecord(): Promise<number> {
        try {
            return this.context.runInIsolatedContext(async childContext => {
                const data = (await this
                    .sequenceNumberNodeValueIndexes) as NodeCreateData<xtremMasterData.nodes.SequenceNumberValue>;
                const seqValue = await childContext.create(xtremMasterData.nodes.SequenceNumberValue, data);
                await seqValue.$.save();
                return seqValue._id;
            });
        } catch (e) {
            // if the error is a duplicate key value violates unique constraint, we log a warn message and continue
            // or throw the error if it's not the case
            if (e.innerError?.code !== '23505') throw e;
            this.logger.warn(
                () =>
                    `SequenceNumberValue creation failed - sequenceNumberValue already exists (created by other transactions)`,
            );
        }
        return Promise.resolve(0);
    }

    /**
     * get the SequenceNumberValue
     * @param sequenceNumberNodeValueIndexes  sequenceNumber / company / site / additionalInfo / period
     * @returns sequenceValue , null if no SequenceNumberValue finded
     */
    private async getPreviousSequenceValue(): Promise<integer | null> {
        const sequenceNumberValue = await this.context.tryRead(
            xtremMasterData.nodes.SequenceNumberValue,
            await this.sequenceNumberNodeValueIndexes,
        );
        if (sequenceNumberValue) {
            return (await sequenceNumberValue.sequenceValue) - 1;
        }
        return null;
    }

    /**
     * Build the sequence number par of each component.
     * @param component
     */
    private async buildSequenceNumber(component: xtremMasterData.nodes.SequenceNumberComponent): Promise<AnyValue> {
        const length = await component.length;
        const constant = await component.constant;
        const type = await component.type;
        this.logger.debug(() => `Build sequence number : Type :${type} ${constant} length:${length}`);
        switch (type) {
            case 'constant':
                return constant;
            case 'year':
                return lib.parsePartsOfSequenceNumber.year(await this.currentDate, length);
            case 'month':
                return lib.parsePartsOfSequenceNumber.monthByComponentLength(await this.currentDate, length);
            case 'week':
                return (await this.currentDate)
                    .weekNumber(1)
                    .toString()
                    .padStart(length || 0, '0');
            case 'day':
                return lib.parsePartsOfSequenceNumber.day(await this.currentDate, length);
            case 'company':
                return lib.parsePartsOfSequenceNumber.company(
                    (await this.company) || ((await this.site) ? await (await this.site)!.legalCompany : null),
                    await this.sequenceNumber,
                    length,
                );
            case 'site':
                return lib.parsePartsOfSequenceNumber.site(
                    (await this.site) ?? null,
                    await this.sequenceNumber,
                    length,
                );
            case 'sequenceNumber':
                return lib.parsePartsOfSequenceNumber.sequenceNumber(
                    this.generatedSequence,
                    await this.sequenceNumber,
                    length,
                );
            default:
                throw new BusinessRuleError(
                    this.context.localize(
                        '@sage/xtrem-master-data/classes__sequence-number-generator__invalid-component-type-value',
                        'Invalid component type : {{type}}',
                        { type },
                    ),
                );
        }
    }

    /**
     * Loop on components of the sequenceNumber node
     * @returns Sequence to allocate
     */
    private async uPrepareFinalCounterSequence(): Promise<string> {
        let finalCounterSequence = await (
            await this.sequenceNumber
        ).components.reduce(async (counterSequence, component: xtremMasterData.nodes.SequenceNumberComponent) => {
            const currentSequence = await this.buildSequenceNumber(component);
            this.logger.debug(() => `sequence : ${JSON.stringify(currentSequence)}`);
            return counterSequence + currentSequence;
        }, '');

        if ((await (await this.sequenceNumber).type) === 'numeric') {
            finalCounterSequence = Number.parseInt(finalCounterSequence, 10).toString();
        }
        return finalCounterSequence;
    }

    /**
     * Build the final sequence number.
     * get the sequenceValue to allocate then prepare final counter sequence
     */
    async allocate(): Promise<string> {
        this.uGeneratedSequence = await this.generateNormalSequenceValue(0);
        return this.uPrepareFinalCounterSequence();
    }

    /**
     * Check previous sequence number.
     */
    async getPreviousSequenceNumber(): Promise<string | undefined> {
        const generatedSequence = await this.getPreviousSequenceValue();
        if (generatedSequence) {
            this.uGeneratedSequence = generatedSequence;
            return this.uPrepareFinalCounterSequence();
        }
        return undefined;
    }

    private async postingFrLegislation(sequence: {
        isPosting: boolean;
        legislationId?: string | null;
        nodeName: string;
        date: date;
    }): Promise<void> {
        if (
            sequence.legislationId === 'FR' &&
            sequence.isPosting &&
            ['SalesInvoice', 'SalesCreditMemo'].includes(sequence.nodeName)
        ) {
            if ((await (await this.sequenceNumberInstance).rtzLevel) === 'monthly') {
                const error = monthlySequenceNumbersNotAllowed(this.context, {
                    sequenceNumber: await (await this.sequenceNumberInstance).id,
                });
                this.error(error);
            }

            if ((await (await this.sequenceNumberInstance).isChronological) === false) {
                const error = chronologicalControlMustBeActive(this.context, {
                    sequenceNumber: await (await this.sequenceNumberInstance).id,
                });
                this.error(error);
            }

            if (sequence.date > date.today()) {
                const error = documentDateCannotBeLaterThanToday(this.context);
                this.error(error);
            }
        }
    }

    private async creationControl(): Promise<void> {
        if (await (await this.sequenceNumber).isChronological) {
            const previousSequenceNumber = await this.getPreviousSequenceNumber();
            this.logger.debug(() => ` ** creationControl ** ${previousSequenceNumber}`);
            if (previousSequenceNumber) {
                await this.chronologicalControlPrevious(previousSequenceNumber);
            }
        }
        // FR legislation controls
        await this.postingFrLegislation({
            isPosting: this.isPosting,
            legislationId: await this.legislationId,
            nodeName: this.parameters.nodeInstance?.$.factory.nodeConstructor.name ?? '',
            date: await this.currentDate,
        });
    }

    /**
     *  Read nodeFactory in case of sending the instance to get property name/values
     */
    #nodeFactory: NodeFactory | undefined;

    private get nodeFactory(): NodeFactory {
        if (this.#nodeFactory) {
            return this.#nodeFactory;
        }
        this.#nodeFactory = this.parameters.nodeInstance?.$.factory;
        if (!this.#nodeFactory) {
            throw new BusinessRuleError(nodeInstanceIsRequired(this.context));
        }
        return this.#nodeFactory;
    }

    /**
     *  check if sequenceNumber components of the current sequenceNumberInstance have site or company
     */
    get isLocationComponentPresent(): Promise<boolean> {
        return (async () => {
            return (await this.sequenceNumberInstance).components.some(
                async sequenceNumberComponent =>
                    (await sequenceNumberComponent.type) === 'site' ||
                    (await sequenceNumberComponent.type) === 'company',
            );
        })();
    }

    private async getInstanceValue<T extends AnyValue>(propertyName: string | undefined, defValue: T): Promise<T> {
        const instance = this.parameters.nodeInstance;
        if (!instance) return defValue;
        if (!propertyName) return defValue;
        if (instance.$.isValueDeferred(propertyName)) return defValue;
        return ((await instance.$.getValue(propertyName)) as T) || defValue;
    }

    get company(): AsyncResponse<xtremSystem.nodes.Company | null> {
        return (async () => {
            if (this.parameters.nodeInstance) {
                const companyProperty = this.nodeFactory.getTaggedProperty('company')?.name;
                return (
                    (await this.getInstanceValue(companyProperty, null)) ??
                    (await (
                        await this.site
                    )?.legalCompany) ??
                    null
                );
            }
            return this.parameters.company || (await this.parameters.site?.legalCompany) || null;
        })();
    }

    get complementary(): string {
        return this.parameters.complementary ?? '';
    }

    override get site(): AsyncResponse<xtremSystem.nodes.Site | undefined> {
        if (this.parameters.nodeInstance) {
            const siteProperty = this.nodeFactory.getTaggedProperty('site')?.name;
            return this.getInstanceValue(siteProperty, undefined);
        }
        return this.parameters.site;
    }

    get currentDate(): Promise<date> {
        if (!this.parameters.nodeInstance) {
            return Promise.resolve(this.parameters.currentDate ?? date.today());
        }
        return this.getInstanceValue(this.documentDateField, this.parameters.currentDate!);
    }

    get documentDateField(): string | undefined {
        return this.nodeFactory.getTaggedProperty('documentDate')?.name;
    }

    get numberField(): string | undefined {
        return this.nodeFactory.getTaggedProperty('sequenceNumber')?.name;
    }

    get documentNumber(): Promise<string | undefined> {
        return this.getInstanceValue(this.numberField, this.parameters.documentNumber);
    }

    get action(): NodeStatus {
        return this.parameters.nodeInstance?.$.status ?? NodeStatus.invalid;
    }

    get isPosting(): boolean {
        return this.parameters.posting ?? false;
    }

    /*
    Updating a row in the middle of a transaction can cause contention issues between concurrent transactions. This issue
    can be mitigated by setting number with a temporary nanoId when creating an instance and updating it with
    documentNumberUpdate in saveEnd: the related UPDATE will be the last SQL operation executed in the transaction.

    NB: This behavior will be improved in next versions by updating this number using a specfic transaction
    */
    async updateDocumentNumber<This extends xtremMasterData.interfaces.Document>(
        nodeConstructor: StaticThis<This>,
        nodeInstance: xtremMasterData.interfaces.Document,
    ): Promise<void> {
        // Let's compute a sequence number:
        await nodeInstance.$.set({ number: await this.allocate() });

        // Update number in the schema
        await nodeInstance.$.context.bulkUpdate(nodeConstructor, {
            set: { number: await nodeInstance.number },
            where: {
                _id: nodeInstance._id,
            },
        } as BulkUpdateOptions<This>);
    }

    async isTemporaryNumber(): Promise<boolean> {
        const sequenceNumberForPosting = await searchSequenceNumber(
            this.context,
            {
                nodeFactoryName: this.parameters.nodeInstance?.$?.factory?.name ?? '',
                ...((await this.site) ? { site: (await this.site)?._id } : {}),
                ...((await this.company) ? { company: (await this.company)?._id } : {}),
                ...((await this.legislationSysId) ? { legislation: await this.legislationSysId } : {}),
                isAssignOnPosting: true,
                throwError: false,
            },
            this.logger,
        );
        return !!sequenceNumberForPosting;
    }
}
