import type { Context, decimal } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import * as xtremMasterData from '../index';

/**
 * Class dedicated to From Context when converting one unit of measure into another unit of measure
 */
export class FromContext {
    constructor(
        private readonly _value: decimal,
        private readonly _context?: Context,
    ) {}

    private async getNodeFromUnit(
        _fromUnit: xtremMasterData.nodes.UnitOfMeasure | string,
    ): Promise<xtremMasterData.nodes.UnitOfMeasure> {
        let fromUnit = _fromUnit;
        if (typeof fromUnit === 'string') {
            if (!this._context) {
                throw new BusinessRuleError('no Context');
            }
            fromUnit =
                (
                    await this._context
                        .query(xtremMasterData.nodes.UnitOfMeasure, { filter: { id: fromUnit }, first: 1 })
                        .toArray()
                )[0] || null;
        }

        return fromUnit;
    }

    /**
     * Lets the converter know the source unit abbreviation
     */
    async from(
        fromUnit: xtremMasterData.nodes.UnitOfMeasure | string,
    ): Promise<xtremMasterData.classes.ToContext | null> {
        const _fromUnit = await this.getNodeFromUnit(fromUnit);
        return _fromUnit ? new xtremMasterData.classes.ToContext(this._value, _fromUnit) : null;
    }

    /**
     * You can also get the possible conversions for a measure:
     * @param fromUnit measure '(can be a string or a node unitOfMeasure)
     */
    async possibilities(
        fromUnit: xtremMasterData.nodes.UnitOfMeasure | string,
    ): Promise<xtremMasterData.interfaces.ListOfMeasureInterface[]> {
        return xtremMasterData.functions.listOfPossibilities(await this.getNodeFromUnit(fromUnit));
    }
}
