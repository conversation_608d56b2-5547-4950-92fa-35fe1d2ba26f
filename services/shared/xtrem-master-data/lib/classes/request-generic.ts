import type { AsyncResponse, Context } from '@sage/xtrem-core';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidationSeverity } from '@sage/xtrem-core';
import type { AxiosRequestConfig, RawAxiosRequestHeaders, Method } from 'axios';
import axios from 'axios';
import * as fs from 'fs';
import * as fsp from 'path';

export interface RequestGenericConstructor {
    method?: Method;
    url?: string;
    data?: any;
    urlParameters?: { [key: string]: string };
    logger?: Logger;
}

export type RequestGenericResponse<T extends {}> = T & {
    errors: Array<{ code: string; description: string }>;
};
/** This class can be move in a lower package Generic request for axios calls  */

export class RequestGeneric {
    public diagnoses: Diagnose[];

    public isLocalEnv = !process.env.XTREM_ENV || process.env.XTREM_ENV === 'local';

    /** To be able to delete some headers on mocks  */
    protected authHeader: string[] = [];

    protected logger: Logger;

    protected headers: RawAxiosRequestHeaders = { 'Content-Type': 'application/json' };

    protected defaultUrl: string;

    private readonly mockFolder = 'axios';

    /**
     * First level of generic call to axios using mocks
     * @param context
     * @param parameters method default : GET
     */
    constructor(
        public readonly context: Context,
        public parameters?: RequestGenericConstructor,
    ) {
        this.logger = this.parameters?.logger || Logger.getLogger(__filename, 'req');
        this.diagnoses = [];
    }

    /** Call before axios mocks */
    beforeMocks() {
        this.logger.debug(() => `Mocks : ${this.context.testConfig?.mocks}`);
        /** When mocks we don't want to keep all the authentication stuff ( Authorization is automaticly deleted ) */
        if (this.context.testConfig?.mocks) {
            this.authHeader.forEach(header => {
                delete this.headers[header];
            });
        }
    }

    get urlParametersObject() {
        return this.parameters?.urlParameters;
    }

    get urlParameters() {
        if (this.parameters?.urlParameters) {
            const paramToAdd = Object.keys(this.parameters.urlParameters).reduce(
                (accumulator: string, currentKey: string) =>
                    `${accumulator}${accumulator ? '&' : ''}${currentKey}=${
                        this.parameters?.urlParameters ? this.parameters?.urlParameters[currentKey] : ''
                    }`,
                '',
            );
            this.logger.debug(() => `Param of the ${paramToAdd}`);
            return `?${paramToAdd}`;
        }
        return '';
    }

    get url(): AsyncResponse<string> {
        return `${this.parameters?.url || this.defaultUrl}${this.urlParameters}`;
    }

    get method() {
        return this.parameters?.method || 'GET';
    }

    get data() {
        return this.parameters?.data || undefined;
    }

    /** Generate the AxiosRequestConfig & launch the request
     *  we are returning a promise so we need await     */

    async execute<T extends {}>(): Promise<RequestGenericResponse<T>> {
        if (!(await this.url)) {
            throw new SystemError('No URL to request');
        }

        const config: AxiosRequestConfig = {
            headers: this.headers,
            url: await this.url,
            method: this.method,
            data: this.data,
        };
        /** SonarCloud ask for  Immediately return this expression instead of
         * assigning it to the temporary variable "requestResult". */

        return this.request(config);
    }

    /** Axios request with mocks  */
    async request<T extends {}>(config: AxiosRequestConfig): Promise<RequestGenericResponse<T>> {
        const axiosMock = Mocker.get('axios', require);

        this.beforeMocks();

        this.logger.debug(
            () => `${config.method} :${config.url} \n Data : ${JSON.stringify(config.data, null, 4)} 
         ${JSON.stringify(config.headers, null, 4)}`,
        );

        let responseData: RequestGenericResponse<T>;

        try {
            const axiosResult = await axiosMock(config);
            this.logger.debug(() => `${axiosResult.status} - ${axiosResult.statusText}`);
            responseData = axiosResult.data;
        } catch (error) {
            if (!axios.isAxiosError(error)) {
                this.logger.error(() => JSON.stringify(error, null, 4));
                throw new SystemError(error);
            }
            this.logger.error(() => `Axios error status : ${error.response?.status} ${error.response?.statusText} `);
            this.logger.debug(() => `Axios error errors : ${JSON.stringify(error.response?.data.errors)} `);
            this.diagnoses.push(
                new Diagnose(
                    ValidationSeverity.error,
                    ['Request', 'execute'],
                    `${error.response?.status} : ${error.response?.statusText}`,
                ),
            );
            if ((error.response?.status || 0) > 500) {
                throw new SystemError(`Internal Server Error ${error.response?.status}`);
            }

            return error.response?.data;
        }
        if (this.context.testConfig?.testAttributes?.createMockData) {
            this.writeMockData(config, responseData);
        }

        if (responseData?.errors && Array.isArray(responseData.errors)) {
            this.diagnoses.push(
                ...responseData.errors.map(error => {
                    return new Diagnose(
                        ValidationSeverity.error,
                        ['Request', 'execute'],
                        `${error.code} : ${error.description}`,
                    );
                }),
            );
        }
        return responseData;
    }

    /**
     * Automatic writing of mock data  ==> testAttributes?.createMockData
     * @param request
     * @param response
     */
    writeMockData(request: any, response: any) {
        if (this.context.testConfig?.directory && this.context.testConfig.scenario) {
            const { directory, scenario } = this.context.testConfig;

            let mockData;
            if (!fs.existsSync(fsp.join(directory, this.mockFolder)))
                fs.mkdirSync(fsp.join(directory, this.mockFolder));
            if (!fs.existsSync(fsp.join(directory, this.mockFolder, `${scenario}.json`))) {
                mockData = [];
            } else {
                this.context.logger.debug(() => `Try to open file : ${directory}/${this.mockFolder}/${scenario}.json`);
                mockData = JSON.parse(
                    fs.readFileSync(fsp.join(directory, this.mockFolder, `${scenario}.json`), 'utf8').toString(),
                );
            }

            if (request.headers.Authorization) delete request.headers.Authorization;
            this.authHeader.forEach(header => {
                if (request.headers[header]) delete request.headers[header];
            });

            this.logger.debug(() => `Request : ${JSON.stringify(request)}`);

            mockData.push({
                request,
                response: { data: { isMock: true, ...response } },
            });
            fs.writeFileSync(
                fsp.join(directory, this.mockFolder, `${scenario}.json`),
                JSON.stringify(mockData, null, 4),
            );
        }
    }
}
