import type { decimal } from '@sage/xtrem-core';
import * as xtremMasterData from '../index';

/**
 * Class dedicated to To Context when converting one unit of measure into another unit of measure
 */
export class ToContext {
    constructor(
        private readonly _value: decimal,
        private readonly _fromUnit: xtremMasterData.nodes.UnitOfMeasure,
    ) {}

    /**
     * function dedicated to convert the unit ands returns the value
     */
    async to(_toUnit: xtremMasterData.nodes.UnitOfMeasure | string): Promise<number> {
        let toUnit = _toUnit;
        if (typeof toUnit === 'string') {
            toUnit =
                (
                    await this._fromUnit.$.context
                        .query(xtremMasterData.nodes.UnitOfMeasure, {
                            filter: { id: toUnit },
                            first: 1,
                        })
                        .toArray()
                )[0] || null;
        }
        return xtremMasterData.functions.convertFromTo(this._fromUnit, toUnit, this._value);
    }

    /**
     * If you ever want to know the possible conversions for a unit, just use .possibilities
     */
    possibilities() {
        return xtremMasterData.functions.listOfPossibilities(this._fromUnit);
    }
}
