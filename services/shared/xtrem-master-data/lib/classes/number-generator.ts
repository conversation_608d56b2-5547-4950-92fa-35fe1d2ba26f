import type { AsyncResponse, Context, ValidationContext } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type * as xtremSystem from '@sage/xtrem-system';

import { loggers } from '../functions/loggers';
import {
    sequenceNumberInstanceNotFound,
    sequenceNumberNotApplicationLevelAndNoSite,
    sequenceNumberNotDefinedAtApplicationLevel,
} from '../functions/sequence-number-localized';
import * as xtremMasterData from '../index';

export class NumberGenerator {
    readonly logger = loggers.sequenceNumber;

    storedSequenceNumberInstance: xtremMasterData.nodes.SequenceNumber;

    #cx: ValidationContext;

    constructor(
        readonly context: Context,
        readonly parameters: {
            sequenceNumber?: string | xtremMasterData.nodes.SequenceNumber;
            site?: xtremSystem.nodes.Site;
        },
    ) {
        this.logger.debug(() => `** DocumentNumberGenerator constructor ** `);
    }

    get sequenceNumberInstance(): Promise<xtremMasterData.nodes.SequenceNumber> {
        if (this.parameters.sequenceNumber) {
            if (typeof this.parameters.sequenceNumber === 'string') {
                return this.context.read(xtremMasterData.nodes.SequenceNumber, {
                    id: this.parameters.sequenceNumber,
                    legislation: null,
                });
            }

            if (this.parameters.sequenceNumber instanceof xtremMasterData.nodes.SequenceNumber)
                return Promise.resolve(this.parameters.sequenceNumber);
        }
        throw new BusinessRuleError(sequenceNumberInstanceNotFound(this.context));
    }

    // alias to keep compatibility
    get sequenceNumber(): AsyncResponse<xtremMasterData.nodes.SequenceNumber> {
        return this.sequenceNumberInstance;
    }

    async sequenceNumberHasCompanySiteComponent(): Promise<boolean> {
        return (await this.sequenceNumberInstance).components.some(
            async component => (await component.type) !== null && ['site', 'company'].includes(await component.type),
        );
    }

    get site(): AsyncResponse<xtremSystem.nodes.Site | undefined> {
        return Promise.resolve(this.parameters.site);
    }

    async controls(cx?: ValidationContext): Promise<void> {
        if (cx) {
            this.#cx = cx;
        }
        await this.sequenceNumberControls();
    }

    error(message: string) {
        if (this.#cx) {
            this.#cx.addError(message);
            return;
        }
        throw new BusinessRuleError(message);
    }

    /** Verify that the sequenceNumber and paranmeters are OK  */
    async sequenceNumberControls() {
        await this.logger.debugAsync(async () => `${await (await this.sequenceNumberInstance).name} `);
        if ((await (await this.sequenceNumberInstance).definitionLevel) === 'application') {
            return;
        }

        if (!(await this.site)) {
            const error = sequenceNumberNotApplicationLevelAndNoSite(this.context, {
                id: await (await this.sequenceNumberInstance).id,
                definitionLevel: await (await this.sequenceNumberInstance).definitionLevel,
            });
            this.error(error);
        }

        if (!(await this.sequenceNumberHasCompanySiteComponent())) {
            const error = sequenceNumberNotDefinedAtApplicationLevel(this.context, {
                id: await (await this.sequenceNumberInstance).id,
            });
            this.error(error);
        }
    }
}
