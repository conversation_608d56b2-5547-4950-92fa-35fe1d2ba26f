import type { Customer, Item, ItemCustomer, ItemCustomerPrice } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { isEmpty } from 'lodash';
import { Item as ItemPage } from '../pages/item';
import { CustomerPage } from './business-entity';
import { getCustomer } from './business-entity-customer';
import { confirmDialogWithAcceptButtonText } from './page-functions';

export async function addOrEditCustomerItem(customerPage: CustomerPage, itemCustomer?: ui.PartialNode<ItemCustomer>) {
    const dialogQueryParameters =
        itemCustomer?._id && Number(itemCustomer._id) > 0 ? { _id: JSON.stringify(itemCustomer._id) } : { _id: '$new' };

    const values = { isActive: true, ...itemCustomer, customer: getCustomer(customerPage) };

    const changedCustomerItem: ui.PartialNodeWithId<ItemCustomer> = await customerPage.$.dialog.page(
        '@sage/xtrem-master-data/ItemCustomerPanel',
        dialogQueryParameters,
        { rightAligned: true, size: 'large', resolveOnCancel: true, values },
    );

    if (changedCustomerItem && !isEmpty(changedCustomerItem)) {
        customerPage.items.addOrUpdateRecordValue(changedCustomerItem);
    }
}

async function refreshPageFields(page: CustomerPage | ItemPage) {
    if (page instanceof CustomerPage) {
        await page.itemPrices.refresh();
    } else {
        await page.salesPrices.refresh();
    }
}

export async function manageItemCustomerPrice(
    page: CustomerPage | ItemPage,
    itemPrice: { data?: ui.PartialNodeWithId<ItemCustomerPrice>; action: 'create' | 'update' },
) {
    const customer: ui.PartialNodeWithId<Customer> | undefined =
        page instanceof CustomerPage ? getCustomer(page) : undefined;
    const item: ui.PartialNodeWithId<Item> | undefined =
        page instanceof ItemPage
            ? {
                  _id: page._id.value || '',
                  id: page.id.value || '',
                  name: page.name.value || '',
                  stockUnit: page.stockUnit.value || {},
              }
            : undefined;

    const _id = itemPrice.data?._id && Number(itemPrice.data._id) > 0 ? itemPrice.data._id : '$new';

    await page.$.dialog.page(
        '@sage/xtrem-master-data/ItemCustomerPricePanel',
        {
            _id,
            price: itemPrice.data ? JSON.stringify(itemPrice.data) : 0,
            action: itemPrice.action,
            ...(customer ? { customer: JSON.stringify(customer) } : {}),
            ...(item ? { item: JSON.stringify(item) } : {}),
        },
        { rightAligned: true, size: 'extra-large', resolveOnCancel: true },
    );

    await refreshPageFields(page);
}

export async function deleteCustomerPrice(page: CustomerPage | ItemPage, _id: string) {
    if (
        await confirmDialogWithAcceptButtonText(
            page,
            ui.localize('@sage/xtrem-master-data/pages__delete_page_dialog_title', 'Confirm deletion'),
            ui.localize(
                '@sage/xtrem-master-data/pages__delete_page_dialog_content',
                'You are about to delete this record.',
            ),
            ui.localize('@sage/xtrem-master-data/pages-confirm-delete', 'Delete'),
        )
    ) {
        await page.$.graph.delete({ _id, nodeName: '@sage/xtrem-master-data/ItemCustomerPrice' });

        await refreshPageFields(page);
    }
}
