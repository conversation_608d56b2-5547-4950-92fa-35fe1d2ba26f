import { asyncArray } from '@sage/xtrem-async-helper';
import type * as ui from '@sage/xtrem-ui';

export async function setReferenceIfSingleValue(referenceFields: ui.fields.Reference[]) {
    await asyncArray(referenceFields).forEach(async referenceField => {
        const suggestions = await referenceField.fetchSuggestions();
        if (suggestions.length === 1) {
            [referenceField.value] = suggestions;
        }
    });
}

export async function setMultiReferenceIfSingleValue(referenceFields: ui.fields.MultiReference[]) {
    await asyncArray(referenceFields).forEach(async referenceField => {
        const suggestions = await referenceField.fetchSuggestions('');
        if (suggestions.length === 1) {
            referenceField.value = suggestions;
        }
    });
}
