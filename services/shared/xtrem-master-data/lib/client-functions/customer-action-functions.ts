import * as ui from '@sage/xtrem-ui';
import type { SetOnHoldActionParameter } from './interfaces/customer-action-functions';

export async function setOnHold(parameters: SetOnHoldActionParameter) {
    await parameters.customerPage.$.graph
        .node('@sage/xtrem-master-data/Customer')
        .update(
            {
                _id: true,
            },
            {
                data: {
                    _id: parameters.recordId,
                    isOnHold: parameters.isOnHold,
                },
            },
        )
        .execute();
    const onHoldMessage = parameters.isOnHold
        ? ui.localize('@sage/xtrem-master-data/pages__customer__put_on_hold', 'Customer put on hold')
        : ui.localize('@sage/xtrem-master-data/pages__customer__remove_on_hold', 'Customer hold removed');
    parameters.customerPage.$.showToast(onHoldMessage, { type: 'success' });
}
