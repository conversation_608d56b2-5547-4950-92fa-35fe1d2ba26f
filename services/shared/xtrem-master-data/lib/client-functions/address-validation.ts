import { isValidEmail, validTelephoneNumber } from '../shared-functions';
import { invalidEmailAddress, invalidPhoneNumber } from './business-entity-localize';

export function validPhoneNumber(phoneNumber: string) {
    if (phoneNumber && !validTelephoneNumber(phoneNumber)) {
        return invalidPhoneNumber;
    }
    return undefined;
}

/**  */
export function validEmail(email: string) {
    if (email && !isValidEmail(email)) {
        return invalidEmailAddress;
    }
    return undefined;
}
