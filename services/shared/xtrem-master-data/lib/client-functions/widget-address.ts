import { edgesSelector } from '@sage/xtrem-client';
import type { BusinessEntityAddress } from '@sage/xtrem-master-data-api';
import type * as ui from '@sage/xtrem-ui';

export type AddressWidget = Pick<
    ui.widgets.ContactCardWidgetContent,
    'address' | 'addressFunction' | 'addressRole' | 'numberOfContacts'
>;

export type AddressCache = AddressWidget & {
    _id: string;
};

// Used to type the query of the widget
export type AddressNode = Pick<
    BusinessEntityAddress,
    '_id' | 'isPrimary' | 'concatenatedAddressWithoutName' | 'name'
> & {
    primaryContact?: Pick<BusinessEntityAddress['primaryContact'], '_id'>;
};

export const addressSelector = edgesSelector<AddressNode>(
    {
        isPrimary: true,
        _id: true,
        concatenatedAddressWithoutName: true,
        name: true,
        primaryContact: { _id: true },
    },
    {},
);

export const emptyAddress: AddressCache = {
    addressFunction: '',
    address: '',
    _id: '',
    addressRole: '',
    numberOfContacts: 0,
};
