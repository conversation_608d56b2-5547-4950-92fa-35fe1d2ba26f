import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { withoutEdges } from '@sage/xtrem-client';
import type {
    BomRevisionSequence,
    GraphApi,
    Item,
    SerialNumberManagement,
    UnitOfMeasure,
} from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { commodityCodeFormat } from '../shared-functions';
import { confirmDialogWithAcceptButtonText } from './page-functions';

export type PageWithItem = ui.Page<GraphApi> & { item: ui.fields.Reference<Item> };

export function itemInactiveMessage(page: PageWithItem) {
    if (!page.item.value) {
        return;
    }
    if (!page.item.value.isActive) {
        page.$.showToast(
            ui.localize(
                '@sage/xtrem-master-data/pages__item_customer_panel__item_is_inactive',
                'The item is inactive. The status is {{status}}.',
                { status: ui.localizeEnumMember('@sage/xtrem-master-data/ItemStatus', page.item.value.status || '') },
            ),
            { type: 'warning' },
        );
    }
}

export function validateEanNumber(number: string): string | undefined {
    return !!number && !/^[\d]{13}$/.test(number)
        ? ui.localize(
              '@sage/xtrem-master-data/events/control__item__code-must-be-a-number',
              'The {{gtinCode}} code needs to have 13 numbers.',
              {
                  gtinCode: 'EAN 13',
              },
          )
        : undefined;
}

export function validateCommodityCode(code: string): string | undefined {
    return !!code && !validateCommodityCode(code)
        ? ui.localize(
              '@sage/xtrem-master-data/nodes__item__commodity_code_format',
              'Use the commodity code format: {{format}}',
              { format: commodityCodeFormat },
          )
        : undefined;
}

export function stockUnitValidation(
    serialNumberManagement: SerialNumberManagement | null,
    stockUnit: UnitOfMeasure,
): string | undefined {
    if (serialNumberManagement === 'managed' && stockUnit.decimalDigits > 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item__stock_unit_not_0_decimal_places',
            'The stock unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.',
            {
                unitOfMeasure: stockUnit.id,
            },
        );
    }
    return '';
}

export function purchaseUnitValidation(
    serialNumberManagement: SerialNumberManagement | null,
    purchaseUnit: UnitOfMeasure,
): string | undefined {
    if (serialNumberManagement === 'managed' && purchaseUnit.decimalDigits > 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item__purchase_unit_not_0_decimal_places',
            'The purchase unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.',
            { unitOfMeasure: purchaseUnit.id },
        );
    }
    return '';
}

export function salesUnitValidation(
    serialNumberManagement: SerialNumberManagement | null,
    salesUnit: UnitOfMeasure,
): string | undefined {
    if (serialNumberManagement === 'managed' && salesUnit.decimalDigits > 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item__sales_unit_not_0_decimal_places',
            'The sales unit ({{unitOfMeasure}}) cannot have any decimal places for serial numbered items.',
            { unitOfMeasure: salesUnit.id },
        );
    }
    return '';
}

export function deleteLineConfirmation(page: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        page,
        ui.localize('@sage/xtrem-master-data/pages__delete_page_dialog_title', 'Confirm deletion'),
        ui.localize(
            '@sage/xtrem-master-data/pages__delete_page_Item_delete_supplier_price_dialog_content',
            'You are about to delete this line. This action cannot be undone after you save the document.',
        ),
        ui.localize('@sage/xtrem-master-data/pages-confirm-delete', 'Delete'),
    );
}

export function confirmSequenceNumberManagementChange(page: ui.Page<GraphApi>) {
    return confirmDialogWithAcceptButtonText(
        page,
        ui.localize('@sage/xtrem-master-data/pages__item__selectId', 'Select ID number'),
        ui.localize(
            '@sage/xtrem-master-data/pages__item__generateNewId',
            'The category you selected is already linked to a sequence number. Do you want to keep the ID or generate a new one?',
        ),
        ui.localize('@sage/xtrem-master-data/pages__item__generateId', 'Generate ID'),
        ui.localize('@sage/xtrem-master-data/pages__item__keepCurrentId', 'Use current Id'),
    );
}

export async function getDefaultBomRevisionSequence(
    page: ui.Page<GraphApi>,
): Promise<ExtractEdgesPartial<BomRevisionSequence> | null> {
    return (
        withoutEdges(
            await page.$.graph
                .node('@sage/xtrem-master-data/BomRevisionSequence')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            id: true,
                            name: true,
                        },
                        {
                            filter: {
                                isDefault: true,
                            },
                            first: 1,
                        },
                    ),
                )
                .execute(),
        ).at(0) ?? null
    );
}
