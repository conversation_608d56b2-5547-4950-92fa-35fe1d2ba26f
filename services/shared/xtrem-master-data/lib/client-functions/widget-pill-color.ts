// Display status pill features (color, filled, etc)
export function getWidgetDisplayStatusPillFeatures(value: string) {
    switch (value) {
        case ['draft', 'quote', 'noVariance'].find(enumValue => enumValue === value):
            return 'filledNeutral';
        case [
            'pendingApproval',
            'variance',
            'partiallyCredited',
            'partiallyOrdered',
            'partiallyInvoiced',
            'partiallyReturned',
            'partiallyReceived',
            'partiallyShipped',
        ].find(enumValue => enumValue === value):
            return 'outlinedCaution';
        case [
            'approved',
            'postingInProgress',
            'invoiced',
            'varianceApproved',
            'credited',
            'confirmed',
            'inactive',
        ].find(enumValue => enumValue === value):
            return 'filledInformation';
        case 'returned':
            return 'filledPositive';
        case ['rejected', 'taxCalculationFailed', 'error', 'postingError', 'stockError'].find(
            enumValue => enumValue === value,
        ):
            return 'filledNegative';
        case ['ordered', 'received', 'posted', 'shipped', 'active'].find(enumValue => enumValue === value):
            return 'filledPositive';
        case ['readyToShip', 'readyToProcess', 'onHold'].find(enumValue => enumValue === value):
            return 'filledCaution';
        default:
            return 'neutral';
    }
}
