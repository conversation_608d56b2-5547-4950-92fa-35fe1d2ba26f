import { DateRange } from '@sage/xtrem-date-time';
import type {
    Item,
    ItemSupplier,
    ItemSupplierPrice,
    ItemSupplierPriceBinding,
    Supplier,
} from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { isEmpty } from 'lodash';
import { Item as ItemPage } from '../pages/item';
import { SupplierPage } from './business-entity';
import { getSupplier } from './business-entity-supplier';
import { confirmDialogWithAcceptButtonText } from './page-functions';

export async function addOrEditSupplierItem(
    supplierPage: SupplierPage,
    supplier: { name: string; itemSupplier?: ui.PartialNodeWithId<ItemSupplier> },
) {
    const values = { isActive: true, ...supplier.itemSupplier, supplier: getSupplier(supplierPage) };

    const _id =
        supplier.itemSupplier?._id && Number(supplier.itemSupplier._id) > 0 ? supplier.itemSupplier._id : '$new';

    const changedSupplierItem: ui.PartialNodeWithId<ItemSupplier> = await supplierPage.$.dialog.page(
        '@sage/xtrem-master-data/SupplierItemPanel',
        { _id },
        { rightAligned: true, size: 'large', resolveOnCancel: true, values },
    );
    if (changedSupplierItem && !isEmpty(changedSupplierItem)) {
        supplierPage.items.addOrUpdateRecordValue(changedSupplierItem);
    }
}

export function mapSupplierPriceDates(
    price: ui.PartialNode<ItemSupplierPriceBinding>,
): ui.PartialNode<ItemSupplierPriceBinding> {
    if (price.dateValid) {
        const parsedDateRange = DateRange.parse(price.dateValid);
        price.dateValidFrom = parsedDateRange.includedStart?.toString();
        price.dateValidTo = parsedDateRange.includedEnd?.toString();
    }
    return price;
}

function setSupplierPricesDates(page: SupplierPage | ItemPage) {
    page.supplierPrices.value.forEach(price => {
        if (price.dateValid) {
            page.supplierPrices.addOrUpdateRecordValue(mapSupplierPriceDates(price));
        }
    });
}

export async function manageItemSupplierPrice(
    page: SupplierPage | ItemPage,
    itemPrice: { data?: ui.PartialNodeWithId<ItemSupplierPrice>; action: 'create' | 'update' | 'delete' },
) {
    const dirty = page.$.isDirty;
    const supplier: ui.PartialNodeWithId<Supplier> | undefined =
        page instanceof SupplierPage ? getSupplier(page) : undefined;
    const item: ui.PartialNodeWithId<Item> | undefined =
        page instanceof ItemPage
            ? {
                  _id: page._id.value || '',
                  id: page.id.value || '',
                  name: page.name.value || '',
                  stockUnit: page.stockUnit.value || {},
              }
            : undefined;

    const _id = itemPrice.data?._id && Number(itemPrice.data._id) > 0 ? itemPrice.data._id : '$new';

    await page.$.dialog.page(
        '@sage/xtrem-master-data/ItemSupplierPricePanel',
        {
            _id,
            price: itemPrice.data ? JSON.stringify(itemPrice.data) : 0,
            action: itemPrice.action,
            ...(supplier ? { supplier: JSON.stringify(supplier) } : {}),
            ...(item ? { item: JSON.stringify(item) } : {}),
        },
        { rightAligned: true, size: 'extra-large', resolveOnCancel: true },
    );
    await page.supplierPrices.refresh();
    setSupplierPricesDates(page);
    if (page.$.isDirty && !dirty) page.$.setPageClean();
}

export async function deleteItemSupplierPrice(page: SupplierPage | ItemPage, _id: string) {
    if (
        await confirmDialogWithAcceptButtonText(
            page,
            ui.localize('@sage/xtrem-master-data/pages__delete_page_dialog_title', 'Confirm deletion'),
            ui.localize(
                '@sage/xtrem-master-data/pages__delete_page_dialog_content',
                'You are about to delete this record.',
            ),
            ui.localize('@sage/xtrem-master-data/pages-confirm-delete', 'Delete'),
        )
    ) {
        await page.$.graph.delete({ _id, nodeName: '@sage/xtrem-master-data/ItemSupplierPrice' });

        await page.supplierPrices.refresh();
    }
}
