import * as ui from '@sage/xtrem-ui';
import type { BeConstructorPagesName } from './business-entity';

export function primaryActiveAddressMandatory(entityType: BeConstructorPagesName): string {
    switch (entityType) {
        case 'Customer':
            return ui.localize(
                '@sage/xtrem-master-data/pages__address_functions__customer_primary_active_address_mandatory',
                'Assign a primary, active address to the customer.',
            );

        case 'Supplier':
            return ui.localize(
                '@sage/xtrem-master-data/pages__address_functions__supplier_primary_active_address_mandatory',
                'Assign a primary, active address to the supplier.',
            );

        case 'Site':
            return ui.localize(
                '@sage/xtrem-master-data/pages__address_functions__site_primary_active_address_mandatory',
                'Assign a primary, active address to the site.',
            );
        case 'BusinessEntity':
            return ui.localize(
                '@sage/xtrem-master-data/pages__address_functions__businessentity_primary_active_address_mandatory',
                'Assign a primary, active address to the business entity.',
            );
        case 'Company':
            return ui.localize(
                '@sage/xtrem-master-data/pages__address_functions__company_primary_active_address_mandatory',
                'Assign a primary, active address to the company.',
            );
        default:
            throw new Error(`${entityType} - not managed`);
    }
}
