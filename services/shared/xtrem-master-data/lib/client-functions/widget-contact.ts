import type { Dict } from '@sage/xtrem-client';
import { edgesSelector } from '@sage/xtrem-client';
import type { BusinessEntityContact } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { WidgetValueOrCallback } from '@sage/xtrem-ui/build/lib/dashboard/widgets/widget-utils';
import type { CustomerContactList } from '../widgets/customer-contact-list';
import { noteAssociationSelector } from './notes';

export const contactSelector = edgesSelector<ContactNode>(
    {
        _id: true,
        _sortValue: true,
        firstName: true,
        lastName: true,
        email: true,
        locationPhoneNumber: true,
        position: true,
        role: true,
        isPrimary: true,
        address: { _id: true, isPrimary: true },
        image: { value: true },
        _notes: { query: noteAssociationSelector },
    },
    { orderBy: { address: { isPrimary: -1 }, isPrimary: -1, firstName: 1 } },
);

export type ContactWidget = Pick<
    ui.widgets.ContactCardWidgetContent,
    | 'contactName'
    | 'contactEmailAddress'
    | 'contactPhoneNumber'
    | 'contactPosition'
    | 'contactRole'
    | 'iconSrc'
    | 'contactImage'
    | 'notes'
>;

export type ContactCache = ContactWidget & {
    _id: string;
    selectedAddressId: string;
    _sortValue?: number;
};

// Used to type the query of the widget
export type ContactNode = Pick<
    BusinessEntityContact,
    | '_id'
    | '_sortValue'
    | 'firstName'
    | 'lastName'
    | 'email'
    | 'locationPhoneNumber'
    | 'position'
    | 'role'
    | 'isPrimary'
    | 'image'
    | '_notes'
> & {
    address?: Pick<BusinessEntityContact['address'], '_id' | 'isPrimary'>;
};

export const primaryContactLocalized = ui.localize(
    '@sage/xtrem-master-data/pages__customer_contact_list__primary_contact',
    'Primary contact',
);

type WidgetContact = WidgetValueOrCallback<CustomerContactList, ui.widgets.ContactCardWidgetContent>;
type WidgetContactReturnType = WidgetContact extends WidgetValueOrCallback<any, infer R> ? R : never;

export const emptyWidgetWithContent: WidgetContactReturnType = {
    contacts: {},
    addresses: {},
    numberOfAddresses: 0,
    numberOfContacts: 0,
    address: '',
    contactName: '',
    addressFunction: '',
    contactEmailAddress: '',
    contactPhoneNumber: '',
    contactPosition: '',
    contactRole: '',
    iconSrc: '',
    addressRole: '',
    contactImage: '',
    canAddNotes: false,
    notes: [],
    onAddressAdd: () => Promise.resolve(),
    onContactAdd: () => Promise.resolve(),
    onContactTypeSwitchChanged: () => Promise.resolve(),
    onNoteAdded: () => Promise.resolve(),
    onNoteDeleted: () => Promise.resolve(),
    onNoteEdited: () => Promise.resolve(),
    onSelectedAddressChanged: () => Promise.resolve(),
    onSelectedContactChanged: () => Promise.resolve(),
    selectedAddressId: '',
    selectedContactId: '',
};

export const emptyContact: ContactCache = {
    contactName: '',
    contactEmailAddress: '',
    contactPhoneNumber: '',
    contactPosition: '',
    contactRole: '',
    iconSrc: '',
    selectedAddressId: '',
    _id: '',
    _sortValue: 0,
    contactImage: '',
    notes: [],
};

export function filterContactsNameByAddressId(addressId: string, contactCache: Dict<ContactCache>): Dict<string> {
    return Object.entries(contactCache)
        .filter(([, value]: [string, ContactCache]) => value.selectedAddressId === addressId)
        .reduce((contactNames, [key, contact]) => {
            contactNames[key] = contact.contactName ?? '';
            return contactNames;
        }, {} as Dict<string>);
}

/** return a string array of the keys of contact ordered  */
export function sortContactsByPrimaryAndName(
    filteredContacts: Dict<string>,
    contactCache: Dict<ContactCache>,
): string[] {
    return Object.keys(filteredContacts).sort((a, b) => {
        const isPrimaryA = contactCache[a].contactRole === primaryContactLocalized ? 1 : 0;
        const isPrimaryB = contactCache[b].contactRole === primaryContactLocalized ? 1 : 0;
        if (isPrimaryB - isPrimaryA !== 0) {
            return isPrimaryB - isPrimaryA;
        }
        return contactCache[a].contactName?.localeCompare(contactCache[b].contactName ?? '') ?? 0;
    });
}
