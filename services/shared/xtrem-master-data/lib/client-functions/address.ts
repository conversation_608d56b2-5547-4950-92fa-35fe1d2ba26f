import type { AddressEntityType } from '@sage/xtrem-master-data-api';
import type { AllBePageWithCompany } from './business-entity';

export const beCompany: AddressEntityType[] = ['businessEntity', 'company'];
export const customerSiteSupplier: AddressEntityType[] = ['customer', 'site', 'supplier'];

/** enable the address in function of name & currency  */
export function newEnableDisableAddAddress(page: AllBePageWithCompany) {
    const isAddressEnable = page.name.value && page.currency.value;

    page.addressSection.isDisabled = !isAddressEnable;
}
