import type { Graph<PERSON><PERSON> } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { confirmDialogWithAcceptButtonText } from './page-functions';

export function isUsedDialog(page: ui.Page<GraphApi>, isUsed: boolean | null) {
    if (!isUsed) return true;

    return confirmDialogWithAcceptButtonText(
        page,
        ui.localize('@sage/xtrem-master-data/pages__already_used_title', 'Sequence number used'),
        ui.localize(
            '@sage/xtrem-master-data/pages__already_used_message',
            'The previous sequence number has already been used to generate a document number.',
        ),
        ui.localize('@sage/xtrem-master-data/pages-confirm-continue', 'Continue'),
    );
}

export async function updateCreateDialog(page: ui.Page<GraphApi>, updateCreate: 'update' | 'create') {
    await page.$.dialog.page(
        '@sage/xtrem-master-data/SequenceNumberValue',
        {
            _id: String(page.$.recordId),
            pageSelection: updateCreate,
        },
        {
            size: 'extra-large',
            resolveOnCancel: true,
        },
    );
}
