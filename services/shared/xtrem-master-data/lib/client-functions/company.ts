import type { CompanyPage } from './business-entity';

/**
 * Legal Rules for FR Legislation ( siren  / naf / rcs / legalForm  )
 *  TODO : later to be adapted for other Legislation
 */
export function legalRulesLegislation(page: CompanyPage) {
    const isHidden = page.legislation.value?.id !== 'FR';

    page.siren.isHidden = isHidden;
    page.naf.isHidden = isHidden;
    page.rcs.isHidden = isHidden;
    page.legalForm.isHidden = isHidden;
}
