import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges, withoutEdges } from '@sage/xtrem-client';
import type { date } from '@sage/xtrem-date-time';
import { DateValue } from '@sage/xtrem-date-time';
import type { GraphApi, Item, ItemCustomerPrice, PaymentTerm, UnitConversionType } from '@sage/xtrem-master-data-api';
import type { Company, Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import * as _ from 'lodash';
import { getDueDate } from '../shared-functions/due-date-function';
import type { ItemSiteEdge, ItemSiteFilter, QueryItemSiteResult } from './interfaces';
import type { OpenItemSupplierPriceListParameters } from './interfaces/common';

export type CreateUpdateDelete = 'create' | 'update' | 'delete';

export function isNumeric(value: any) {
    const parsed = parseFloat(value);
    return !Number.isNaN(parsed) && Number.isFinite(parsed);
}

export function valueMustBePositive(value: number, notZero = false) {
    if (value < 0 || (value === 0 && notZero)) {
        return ui.localize('@sage/xtrem-master-data/value-must-be-positive', 'Value {{value}} must be positive.', {
            value: `(${value})`,
        });
    }
    return undefined;
}

/** Used on salesOrder page :
 * Todo : ItemCustomerPriceViewPanel must be a binded page to item/Customer so we can delete this & just open the panel naturally
 * itemCustomerPrice
 */
export async function openItemCustomerPriceList(
    page: ui.Page, // this is failing when adding <GraphApi>,
    itemId: string,
    soldToCustomerId: string,
    salesSiteId: string,
    stockSiteId: string,
    itemName: string,
    orderDate: string,
) {
    const results = extractEdges<ItemCustomerPrice>(
        await page.$.graph
            .node('@sage/xtrem-master-data/ItemCustomerPrice')
            .query(
                ui.queryUtils.edgesSelector<ItemCustomerPrice>(
                    {
                        _id: true,
                        isActive: true,
                        item: { id: true, name: true },
                        customer: { _id: true, businessEntity: { id: true, name: true } },
                        priceReason: { name: true, priority: true },
                        fromQuantity: true,
                        toQuantity: true,
                        unit: { _id: true, id: true, symbol: true, name: true, decimalDigits: true },
                        startDate: true,
                        endDate: true,
                        price: true,
                        discount: true,
                        charge: true,
                        currency: { _id: true, id: true, name: true, decimalDigits: true },
                        salesSite: { _id: true, id: true, name: true },
                        stockSite: { _id: true, id: true, name: true },
                    },
                    { filter: { isActive: true, item: { _id: { _eq: itemId } } } },
                ),
            )
            .execute(),
    );

    if (results.length) {
        const orderDateValue = DateValue.parse(orderDate).toString();
        const customerItemPrices = results.filter(
            k =>
                (k.customer === null || k.customer._id === soldToCustomerId) &&
                (k.salesSite === null || k.salesSite._id === salesSiteId) &&
                (k.stockSite === null || k.stockSite._id === stockSiteId) &&
                (k.startDate === null || k.startDate <= orderDateValue) &&
                (k.endDate === null || k.endDate >= orderDateValue),
        );

        if (customerItemPrices.length) {
            await page.$.dialog.page(
                '@sage/xtrem-master-data/ItemCustomerPriceViewPanel',
                {
                    items: JSON.stringify(customerItemPrices),
                    itemName,
                },
                {
                    rightAligned: false,
                    size: 'extra-large',
                },
            );
        } else {
            page.$.showToast(
                ui.localize(
                    '@sage/xtrem-master-data/page__item_customer_price_panel__no_price_list_available',
                    'No price list available.',
                ),
                { type: 'warning' },
            );
        }
    } else {
        page.$.showToast(
            ui.localize(
                '@sage/xtrem-master-data/page__item_customer_price_panel__no_price_list_available',
                'No price list available.',
            ),
            { type: 'warning' },
        );
    }
}

/** Used on purchaseOrder page
 * TODO : ItemSupplierPriceViewPanel have to be a binded page bind to ItemSupplier
 * ItemSupplierPrice
 *  // this is failing when adding <GraphApi>,
 */
export async function openItemSupplierPriceList(page: ui.Page<GraphApi>, options: OpenItemSupplierPriceListParameters) {
    const supplierItemPriceQuery = extractEdges(
        await page.$.graph
            .node('@sage/xtrem-master-data/ItemSupplierPrice')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        item: { id: true, name: true },
                        supplier: { _id: true, businessEntity: { id: true, name: true } },
                        fromQuantity: true,
                        toQuantity: true,
                        unit: { _id: true, id: true, symbol: true, name: true, decimalDigits: true },
                        dateValid: true,
                        dateValidFrom: true,
                        dateValidTo: true,
                        price: true,
                        currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                        site: { _id: true, id: true, name: true },
                        type: true,
                        priority: true,
                    },
                    { filter: { item: { _id: { _eq: options.itemId } } } },
                ),
            )
            .execute(),
    );

    if (supplierItemPriceQuery.length) {
        const orderDateValue = DateValue.parse(options.orderDate).toString();
        const supplierItemPrices = supplierItemPriceQuery.filter(
            k =>
                (k.supplier === null || k.supplier._id === options.supplierId) &&
                (k.site === null || k.site._id === options.siteId) &&
                (k.dateValidFrom === null || k.dateValidFrom <= orderDateValue) &&
                (k.dateValidTo === null || k.dateValidTo >= orderDateValue),
        );

        if (supplierItemPrices.length) {
            await page.$.dialog.page(
                '@sage/xtrem-master-data/ItemSupplierPriceViewPanel',
                {
                    items: JSON.stringify(supplierItemPrices),
                    itemName: options.itemName,
                },
                {
                    rightAligned: false,
                    size: 'extra-large',
                    resolveOnCancel: true,
                },
            );
        } else {
            page.$.showToast(
                ui.localize(
                    '@sage/xtrem-master-data/page__item_supplier_price_panel__no_price_list_available',
                    'No price list available.',
                ),
                { type: 'warning' },
            );
        }
    } else {
        page.$.showToast(
            ui.localize(
                '@sage/xtrem-master-data/page__item_supplier_price_panel__no_price_list_available',
                'No price list available.',
            ),
            { type: 'warning' },
        );
    }
}

export function getPurchaseUnit(graph: ui.GraphQLApi<GraphApi>, item: string, supplier: string) {
    return graph
        .node('@sage/xtrem-master-data/UnitOfMeasure')
        .queries.getPurchaseUnit(
            {
                _id: true,
                id: true,
                symbol: true,
                name: true,
                description: true,
                decimalDigits: true,
                isActive: true,
                type: true,
            },
            {
                item,
                supplier,
            },
        )
        .execute();
}

export async function convertFromTo(
    graph: ui.GraphQLApi<GraphApi>,
    fromUnit: string,
    toUnit: string,
    quantity: number,
    item?: string,
    supplier?: string,
    customer?: string,
    type?: string,
    formatToUnitDecimalDigits?: boolean,
) {
    const convertFromToArg = {
        fromUnit,
        quantity: 1,
        toUnit,
        ...(item ? { item } : {}),
        ...(supplier ? { supplier } : {}),
        ...(customer ? { customer } : {}),
        ...(type ? { type: type as UnitConversionType } : {}),
        ...(formatToUnitDecimalDigits ? {} : { formatToUnitDecimalDigits }),
    };

    const conversionFactor = Number(
        await graph
            .node('@sage/xtrem-master-data/UnitOfMeasure')
            .queries.convertFromTo(false, convertFromToArg)
            .execute(),
    );

    const convertedQuantity = Number(
        await graph
            .node('@sage/xtrem-master-data/UnitOfMeasure')
            .queries.convertFromTo(false, { ...convertFromToArg, quantity })
            .execute(),
    );
    return { stockUnit: toUnit, conversionFactor, convertedQuantity };
}

export function getCompanyPriceScale(company?: ExtractEdgesPartial<Company> | Company) {
    return company?.priceScale ?? 2;
}

export function getItemPriceScale() {
    return 5;
}

/**
 * Gets unit information used to manage the page, mandatory fields, etc.
 */
export async function getUnitInfo(pageInstance: ui.Page /** Must add <GraphApi> */, unitId: string) {
    return (await pageInstance.$.graph
        .node('@sage/xtrem-master-data/UnitOfMeasure')
        .read(
            {
                _id: true,
                id: true,
                name: true,
                symbol: true,
                decimalDigits: true,
            },
            unitId,
        )
        .execute()) as { _id: string; id: string; name: string; symbol: string; decimalDigits: string };
}

/**
 * Gets currency information used to manage the page, mandatory fields, etc.
 */
export async function getCurrencyInfo(pageInstance: ui.Page /** Must add <GraphApi> */, currencyId: string) {
    return (await pageInstance.$.graph
        .node('@sage/xtrem-master-data/Currency')
        .read(
            {
                _id: true,
                id: true,
                name: true,
                symbol: true,
                decimalDigits: true,
            },
            currencyId,
        )
        .execute()) as { _id: string; id: string; name: string; symbol: string; decimalDigits: string };
}

export async function queryItemSite(
    pageInstance: ui.Page,
    item: ExtractEdgesPartial<Item>,
    stockSite: ExtractEdgesPartial<Site>,
): Promise<QueryItemSiteResult[]> {
    const edges: ItemSiteEdge = {
        allocatedQuantity: true,
        acceptedStockQuantity: true,
        inStockQuantity: true,
    };

    const filter: ItemSiteFilter = {
        item: { _id: item._id as string },
        site: { _id: stockSite._id as string },
    };

    return withoutEdges(
        await pageInstance.$.graph
            .node('@sage/xtrem-master-data/ItemSite')
            .query(ui.queryUtils.edgesSelector(edges, { filter }))
            .execute(),
    );
}

function objectToString(obj: any, path = ''): string {
    if (!obj) {
        return '';
    }
    if (typeof obj === 'boolean' && obj) {
        return path;
    }
    if (typeof obj === 'string') {
        const firstPart = path ? `${path}.` : '';
        return `${firstPart}${obj}`;
    }

    return Object.keys(obj).reduce((accumulatedPath, key) => {
        const updatedPath = path ? `${path}.${key}` : key;
        const subPath = objectToString(obj[key], updatedPath);
        return subPath || accumulatedPath; // Return first found non-null path
    }, '');
}

/** Generic function to get the full payload of a page ( used on the finish({values:getBindedValues(this)})) */
export function getBindedValues(page: ui.Page<GraphApi>) {
    const { values: pageValues } = page.$;

    Object.entries(page._pageMetadata.uiComponentProperties)
        .filter(([, onlyReference]) => onlyReference._controlObjectType === 'Reference')
        .forEach(([key, field]) => {
            const { bind, isTransient } = field as ui.ReadonlyFieldProperties;
            if (!isTransient) {
                const payloadKey = bind ? objectToString(bind) : key;
                _.set(pageValues, payloadKey, _.get(page, `${key}.value`));
            }
        });
    return pageValues;
}

export function fetchDefaultsForDueDate(parameters: {
    paymentTerm: ExtractEdgesPartial<PaymentTerm> | null;
    baseDate: date.DateValue | null;
    dueDateValue: string | null;
}): string | null {
    if (
        parameters.paymentTerm &&
        parameters.paymentTerm.dueDateType &&
        parameters.paymentTerm.days &&
        parameters.baseDate
    ) {
        return getDueDate({
            dueDateType: parameters.paymentTerm.dueDateType,
            days: parameters.paymentTerm.days,
            baseDate: parameters.baseDate,
        }).toString();
    }
    return parameters.dueDateValue;
}

export function selectUnselectCheckboxes(lines: ui.fields.Table, selectAllCheckbox: boolean | null) {
    if (lines.value.length > 0) {
        lines.value.forEach(line => {
            if (selectAllCheckbox) {
                lines.selectRecord(line._id);
            } else {
                lines.unselectAllRecords();
            }
        });
    }
}

/**
 * returns the value of a property in a nested object
 * @param obj an object extending ClientNode
 * @param path the path of the property value to retrieve
 * @returns the value of the property
 * @example
 * const obj = {
 *   x: 1,
 *   a: {
 *     b: {
 *       c: 2
 *     }
 *   }
 * }
 * const path = 'x'
 * const value = getPropertyValue(obj, path) // -> 1
 *
 * const path2 = '{a:{b:{c:true}}}'
 * const value2 = getPropertyValue(obj, path) // -> 2
 */
export function getPropertyValue<T extends ui.ClientNode>(obj: ExtractEdgesPartial<T>, path: ui.PropertyValueType<T>) {
    if (typeof path === 'string') return obj?.[path as keyof ExtractEdgesPartial<T>];

    const key = Object.keys(path)[0];
    if (obj == null) return undefined;

    const newPath = path[key as keyof typeof path] as ui.PropertyValueType<ui.ClientNode> | true;
    const valueObj = obj[key as keyof ui.ClientNode] as any;
    if (newPath === true) return valueObj;

    return getPropertyValue(valueObj, newPath);
}

export function stockJournalTitle(status: string) {
    if (['error', 'stockPostingError', 'stockError', 'postingError'].includes(status)) {
        return ui.localize('@sage/xtrem-master-data/pages__stock_posting_error', 'Stock posting error');
    }
    return ui.localize('@sage/xtrem-master-data/pages__stock_journal_inquiry', 'Stock journal inquiry');
}
