import * as ui from '@sage/xtrem-ui';

export function salesToStockConversion(val: number) {
    if (val <= 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item-customer_panel__conversion_negative_value',
            'The sales to stock conversion must not be less than or equal to 0.',
        );
    }
    return undefined;
}

export function salesQuantityMustNotBeLessThanZero(quantity: number) {
    if (quantity < 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item-customer_panel__minimum_quantity_negative_value',
            'The minimum sales quantity must not be less than 0.',
        );
    }
    return undefined;
}

export function salesQuantityMaxControl(quantity: number, minimumQuantity: number | null) {
    if (quantity < 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_negative_value',
            'The maximum sales quantity must not be less than 0.',
        );
    }
    if (minimumQuantity && quantity < minimumQuantity) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item-customer_panel__maximum_quantity_less_than_minimum_value',
            'The maximum sales quantity must not be less than the minimum sales quantity.',
        );
    }

    return undefined;
}

export function priceCannotBeNegativeFront(value: number) {
    if (value < 0) {
        return ui.localize('@sage/xtrem-master-data/item__price-cannot-be-negative', 'The price cannot be negative.');
    }
    return undefined;
}
