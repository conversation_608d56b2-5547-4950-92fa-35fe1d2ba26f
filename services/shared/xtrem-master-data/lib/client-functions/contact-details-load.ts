import type { Edges, ExtractEdges, ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { BusinessEntityContact, ContactBase, GraphApi } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { SendEmailPanel } from '../pages/send-email-panel';

/**
 * Load the default address contact detail
 * @param originContact
 * @param page
 */
export function assignEmailContactFrom(originContact: ExtractEdgesPartial<ContactBase>, page: SendEmailPanel) {
    if (originContact) {
        if (originContact.title) page.emailTitles.value = originContact?.title as string;
        if (originContact.lastName) page.emailLastName.value = originContact?.lastName as string;
        if (originContact.firstName) page.emailFirstName.value = originContact?.firstName as string;
        if (originContact.email) page.emailAddress.value = originContact?.email as string;
    }
}

export async function contactsQuery(
    page: ui.Page<GraphApi>,
    addressId: string,
): Promise<ExtractEdges<BusinessEntityContact>[]> {
    const result = await page.$.graph
        .node('@sage/xtrem-master-data/BusinessEntityContact')
        .query(
            ui.queryUtils.edgesSelector<BusinessEntityContact>(
                {
                    _id: true,
                    title: true,
                    firstName: true,
                    lastName: true,
                    email: true,
                    preferredName: true,
                    role: true,
                    position: true,
                    locationPhoneNumber: true,
                },
                {
                    filter: {
                        isActive: true,
                        address: addressId,
                    },
                },
            ),
        )
        .execute();

    return extractEdges<BusinessEntityContact>(result as Edges<BusinessEntityContact>);
}

/**
 * Load all contacts linked to this page address contact detail into the selection grid
 * @param contactDetail
 * @param page
 * @param addressId
 */
export function loadContacts(page: ui.Page<GraphApi>, addressId: string) {
    return contactsQuery(page, addressId);
}
