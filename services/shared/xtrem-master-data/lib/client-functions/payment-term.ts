import type { PaymentTermDiscountOrPenaltyType } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { PaymentTerm as PaymentTermPage } from '../pages/payment-term';

function getTitle(type: PaymentTermDiscountOrPenaltyType): string {
    return type === 'percentage'
        ? ui.localize('@sage/xtrem-master-data/pages__payment_term__percentage', 'Percentage')
        : ui.localize('@sage/xtrem-master-data/pages__payment_term__amount', 'Amount');
}

export function discountAmountTitleDisplay(paymentTermPage: PaymentTermPage) {
    paymentTermPage.discountAmount.isTitleHidden = !paymentTermPage.discountType.value;
    paymentTermPage.discountAmount.title = getTitle(
        paymentTermPage.discountType.value as PaymentTermDiscountOrPenaltyType,
    );
}

export function penaltyAmountTitleDisplay(paymentTermPage: PaymentTermPage) {
    paymentTermPage.penaltyAmount.isTitleHidden = !paymentTermPage.discountType.value;
    paymentTermPage.penaltyAmount.title = getTitle(
        paymentTermPage.penaltyType.value as PaymentTermDiscountOrPenaltyType,
    );
}
