import type { TaxCalculationStatus } from '@sage/xtrem-master-data-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';
import type { LabelFieldStyle } from '@sage/xtrem-ui';

function taxCalculationStatusColor(status: TaxCalculationStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'notDone':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'done':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'failed':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

export function getLabelColorByStatus(
    enumEntry: string,
    status?: TaxCalculationStatus | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'TaxCalculationStatus':
                return taxCalculationStatusColor(status as TaxCalculationStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}

function isPrintedColor(status: boolean, coloredElement: ColoredElement) {
    switch (status) {
        case false:
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case true:
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.transparent[coloredElement];
    }
}

function isSentColor(status: boolean, coloredElement: ColoredElement) {
    switch (status) {
        case false:
            return colorfulPillPattern.outlinedInformation[coloredElement];
        case true:
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function isOnHoldColor(status: boolean, coloredElement: ColoredElement) {
    switch (status) {
        case false:
            return colorfulPillPattern.transparent[coloredElement];
        case true:
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.transparent[coloredElement];
    }
}

function isDefaultUser(status: boolean, coloredElement: ColoredElement) {
    switch (status) {
        case false:
            return colorfulPillPattern.transparent[coloredElement];
        case true:
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.transparent[coloredElement];
    }
}

// function to be called to set a color to a pill's value for a boolean
export function setBooleanStatusColors(booleanEntry: string, status: boolean, coloredElement: ColoredElement): string {
    switch (booleanEntry) {
        case 'isPrinted':
            return isPrintedColor(status, coloredElement);
        case 'isSent':
            return isSentColor(status, coloredElement);
        case 'isOnHold':
            return isOnHoldColor(status, coloredElement);
        case 'isDefaultUser':
            return isDefaultUser(status, coloredElement);
        default:
            return colorfulPillPatternDefaulted(coloredElement);
    }
}

// Display status pill features (color, filled, etc)
export function getDisplayStatusPillFeatures(
    value?: string | null,
    isPurchaseReturn = false,
): Required<LabelFieldStyle> {
    switch (value) {
        case ['draft', 'quote', 'noVariance'].find(enumValue => enumValue === value):
            return colorfulPillPattern.filledNeutral;
        case [
            'pendingApproval',
            'variance',
            'partiallyCredited',
            'partiallyOrdered',
            'partiallyInvoiced',
            'partiallyReturned',
            'partiallyReceived',
            'partiallyShipped',
            'partiallyPaid',
        ].find(enumValue => enumValue === value):
            return colorfulPillPattern.outlinedCaution;
        case [
            'approved',
            'postingInProgress',
            'invoiced',
            'varianceApproved',
            'credited',
            'confirmed',
            'inactive',
        ].find(enumValue => enumValue === value):
            return colorfulPillPattern.filledInformation;
        case 'returned':
            if (isPurchaseReturn) {
                return colorfulPillPattern.filledPositive;
            }
            return colorfulPillPattern.filledInformation;
        case ['rejected', 'taxCalculationFailed', 'error', 'postingError', 'stockError'].find(
            enumValue => enumValue === value,
        ):
            return colorfulPillPattern.filledNegative;
        case ['ordered', 'received', 'posted', 'shipped', 'active', 'paid'].find(enumValue => enumValue === value):
            return colorfulPillPattern.filledPositive;
        case 'closed':
            return colorfulPillPattern.filledClosing;
        case ['readyToShip', 'readyToProcess', 'onHold'].find(enumValue => enumValue === value):
            return colorfulPillPattern.filledCaution;
        default:
            return colorfulPillPattern.filledClosing;
    }
}

export function getStatusPillFeatures(value?: string | null): Required<LabelFieldStyle> {
    switch (value) {
        case 'active':
            return colorfulPillPattern.filledPositive;
        case 'inDevelopment':
            return colorfulPillPattern.filledInformation;
        case 'notRenewed':
            return colorfulPillPattern.filledCaution;
        case 'obsolete':
            return colorfulPillPattern.filledClosing;
        case 'notUsable':
            return colorfulPillPattern.filledClosing;
        default:
            return colorfulPillPattern.filledClosing;
    }
}
