import type { ItemSiteCostPanel as ItemSiteCostPagePanel } from '../pages/item-site-cost-panel';

// Atm only ItemSiteCostPanel uses the displayChart function, but this can be extended
// if needed to multiple types similar to business-entity.ts in client-functions
export function displayChart(page: ItemSiteCostPagePanel): void {
    if (!page.materialCost.value) {
        page.materialCost.value = 0;
    }
    if (!page.machineCost.value) {
        page.machineCost.value = 0;
    }
    if (!page.laborCost.value) {
        page.laborCost.value = 0;
    }
    if (!page.toolCost.value) {
        page.toolCost.value = 0;
    }

    // TODO: Indirect cost removal until refactor
    // if (!page.indirectCost.value) {
    //     page.indirectCost.value = 0;
    // }
    const costs = [
        { _id: '1', name: page.toolCost.title, cost: page.toolCost.value },
        { _id: '2', name: page.machineCost.title, cost: page.machineCost.value },
        { _id: '3', name: page.laborCost.title, cost: page.laborCost.value },
        { _id: '4', name: page.materialCost.title, cost: page.materialCost.value },
        // { _id: '5', name: page.indirectCost.title, cost: page.indirectCost.value },TODO: Indirect cost removal until refactor
    ];
    page.costChart.value = costs;
}
