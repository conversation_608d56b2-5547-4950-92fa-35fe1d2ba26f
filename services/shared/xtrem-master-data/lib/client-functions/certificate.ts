import type { Dict } from '@sage/xtrem-client';
import type { SupplierCertificate } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { SupplierPage } from './business-entity';

export async function addOrEditSupplierCertificate(
    supplierPage: SupplierPage,
    certificate?: ui.PartialNodeWithId<SupplierCertificate>,
) {
    const dialogQueryParameters: Dict<string> = certificate
        ? { id: JSON.stringify(certificate._id), certificate: JSON.stringify(certificate) }
        : {};

    const changedCertificate = await supplierPage.$.dialog.page(
        '@sage/xtrem-master-data/SupplierCertificatePanel',
        dialogQueryParameters,
        { rightAligned: true, size: 'large', resolveOnCancel: false },
    );
    if (changedCertificate) {
        changedCertificate._id = certificate?._id;
        supplierPage.certificates.addOrUpdateRecordValue(changedCertificate);
    }
}

export const newSupplierCertificate = `${ui.localize(
    '@sage/xtrem-master-data/pages__supplier_certificate_panel__new____title',
    'New supplier certificate',
)}`;

export const editSupplierCertificate = `${ui.localize(
    '@sage/xtrem-master-data/pages__supplier_certificate_panel__edit____title',
    'Edit supplier certificate',
)}`;
