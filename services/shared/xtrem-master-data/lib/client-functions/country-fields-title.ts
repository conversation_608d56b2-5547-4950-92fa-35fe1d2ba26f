import * as ui from '@sage/xtrem-ui';

export function getCountryRegionTitle(addressValue: any) {
    return addressValue?.country?.regionLabel
        ? ui.localizeEnumMember('@sage/xtrem-structure/RegionLabel', addressValue.country.regionLabel)
        : ui.localizeEnumMember('@sage/xtrem-structure/RegionLabel', 'stateCountyRegion');
}

export function getCountryPostCodeTitle(addressValue: any) {
    return addressValue?.country?.zipLabel
        ? ui.localizeEnumMember('@sage/xtrem-structure/ZipLabel', addressValue.country.zipLabel)
        : ui.localizeEnumMember('@sage/xtrem-structure/ZipLabel', 'postalCode');
}
