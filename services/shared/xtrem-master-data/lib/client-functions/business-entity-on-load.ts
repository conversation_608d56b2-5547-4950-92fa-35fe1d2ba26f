import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { CustomerSupplierSitePage } from './business-entity';
import { CustomerPage, SitePage, SupplierPage, getDeepBindProperties } from './business-entity';
import { setBaseBusinessRelationPrimaryAddress } from './business-entity-address';
import { initTaxIdNumber, legalSiretFrenchLeg } from './business-entity-validation';

export function childCreation(page: CustomerSupplierSitePage): { isChildCreation: boolean; childName: string } {
    if (page instanceof SupplierPage) {
        return { isChildCreation: Number(page.$.recordId || -1) < 0, childName: 'supplier' };
    }
    if (page instanceof CustomerPage) {
        return { isChildCreation: Number(page.$.recordId || -1) < 0, childName: 'customer' };
    }
    if (page instanceof SitePage) {
        return { isChildCreation: Number(page.$.recordId || -1) < 0, childName: 'site' };
    }
    return { isChildCreation: false, childName: '' };
}

export async function onLoad(page: CustomerSupplierSitePage) {
    setApplicativePageCrudActions({
        page,
        isDirty: false,
        save: page.save,
        cancel: page.$standardCancelAction,
        duplicate: page.$standardDuplicateAction,
        remove: page.$standardDeleteAction,
        actions: [page.$standardOpenCustomizationPageWizardAction],
    });
    page.$.setPageClean();

    const { isChildCreation, childName } = childCreation(page);

    page.separatorFirstLine.isHidden = !isChildCreation;

    if (page.$.recordId) {
        if (isChildCreation) {
            await page.$.fetchDefaults(getDeepBindProperties(page, childName));
            page.isActive.value = true;
            page.save.isHidden = false;
        } else {
            setBaseBusinessRelationPrimaryAddress(page);
            page.$.setPageClean();
        }
        page.id.isDisabled = true;

        await legalSiretFrenchLeg(page);
        await initTaxIdNumber(page);
    }
}
