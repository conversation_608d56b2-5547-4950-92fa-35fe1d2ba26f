import type { ExtractEdgesPartial, integer } from '@sage/xtrem-client';
import { ClientDiagnoseSeverity } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import { noop } from 'lodash';

/**
 * Apply the new value of a line on the grid data and return it as well.
 * @param gridControl the TableField or NestedGridControlObject on which the lines are
 * @param line modified to apply and return.
 * @param level (optional) if the gridControl passed is a NestedGridField you need to pass this parameter.
 * @returns the line received.
 */
export function applyToLineAndReturnData<Node extends ui.PartialNodeWithId<any>>(
    gridControl: ui.fields.TableControlObject | ui.fields.NestedGridControlObject,
    line: Node,
    level?: number,
): Node {
    if (gridControl instanceof ui.fields.NestedGridControlObject) {
        // here we don't want true when level=0
        if (level === undefined) {
            // dev only error
            throw Error("You can't set a record value on a nested grid without its level.");
        }
        gridControl.setRecordValue(line, level);
    } else {
        gridControl.addOrUpdateRecordValue(line);
    }
    return line;
}

/**
 * Checks if the page record id is existing or correspond to a new/empty value.
 * @param pageRecordId
 * @returns a boolean having true for new/empty record id.
 */
export function isEmptyOrNewRecordPage(pageRecordId: string): boolean {
    return pageRecordId === null;
}

/**
 * Type guard function for data is a Node.
 * @param data to be checked.
 * @returns a boolean having true if data is a Node.
 */
export function isPartialNodeWithId(data: any): data is ui.PartialNodeWithId<any> {
    return data && data._id;
}

/**
 * Wraps dialog calls to catch the close and exit action as a noop function
 * @param panelPromise to be wrapped
 * @returns the modified data or void
 */
export async function catchPanelCrossQuitButtonAsNoop<Data extends any>(
    panelPromise: Promise<Data>,
): Promise<void | Data> {
    return (
        await Promise.all([
            panelPromise.catch(err => {
                if (err) {
                    throw err;
                }
                return noop();
            }),
        ])
    )[0];
}

/**
 * Wraps dialog calls dedicated to grid line, it applies onto the gride row the modified data when the promise resolve,
 * it also return the modified data to be used/stored for other purposes. And it manages the close/exit action trough
 * the catchPanelCrossQuitButtonAsNoop function.
 * @param gridControl the TableField or NestedGridControlObject on which the lines are
 * @param panelPromise to be wrapped
 * @param level (optional) if the gridControl passed is a NestedGridField you need to pass this parameter.
 * @returns the modified data or void
 */
export function applyPanelToLineIfChanged<Node extends ui.PartialNodeWithId<any>>(
    gridControl: ui.fields.TableControlObject | ui.fields.NestedGridControlObject,
    panelPromise: Promise<Node>,
    level?: number,
): Promise<Node> {
    // casting promise resolve type to remove the undefined possibility
    return catchPanelCrossQuitButtonAsNoop(panelPromise).then(resolveData => {
        if (isPartialNodeWithId(resolveData)) {
            return applyToLineAndReturnData(gridControl, resolveData, level);
        }
        return undefined;
    }) as Promise<Node>;
}

/** MUST BE MOVE to xtrem-system
 * Wraps confirm dialogue calls into a boolean promise
 * @param dialogPromise to be wrapped
 * @returns a boolean having false for exit or cancel dialog finish action.
 */
export function confirmDialogToBoolean(dialogPromise: Promise<any>): Promise<boolean> {
    return dialogPromise
        .then(() => true)
        .catch(err => {
            if (err) {
                throw err;
            }
            return false;
        });
}

/**
 * Wraps confirm dialogue calls into an object promise
 * @param dialogPromise to be wrapped
 * @returns an object or false for when cancelled.
 */
export async function confirmDialogToObject(dialogPromise: Promise<any>): Promise<any> {
    try {
        const result = await dialogPromise;
        return result;
    } catch (err) {
        if (err) {
            throw err;
        }
        return false;
    }
}

export function formatError(
    page: ui.Page | ui.widgets.TableWidget,
    error: string | (Error & { errors: Array<any> }),
): string {
    page.$.loader.isHidden = true;
    if (typeof error === 'string') {
        return error;
    }

    if (error.errors?.length) {
        const errorMessageLines: string[] = [];
        error.errors.forEach(e => {
            errorMessageLines.push(...e.message.split('\n').map((message: string) => `**${message}**`));
            if (e.extensions?.diagnoses) {
                e.extensions.diagnoses.forEach((d: any) => {
                    if (
                        d.severity === ClientDiagnoseSeverity.error ||
                        d.severity === ClientDiagnoseSeverity.exception
                    ) {
                        if (e.message !== d.message) {
                            errorMessageLines.push(` - ${d.message}`);
                        }
                    }
                });
            }
        });
        if (errorMessageLines.length === 1) {
            return `${errorMessageLines[0].replace(/\*/g, '')}`;
        }
        return errorMessageLines.join('\n\n');
    }

    return error.message;
}

export function tryParseJSON<Type>(json: string): Type | null {
    try {
        return JSON.parse(json) as Type;
    } catch (err) {
        // dev only error
        ui.console.error('Bad json received');
    }
    return null;
}

type PartialCollectionValueWithIds<T> = ui.PartialNodeWithId<T>;

/**
 * Enforces type casting when needed from Partial to a non-partial objects.
 * @param line to be casted.
 * @returns the line casted
 */
export function removeExtractEdgesPartial<T>(partialObject: ExtractEdgesPartial<T>) {
    return partialObject as unknown as T;
}

export function removePartialCollectionValueWithIds<T>(partialObject: Array<PartialCollectionValueWithIds<T>>) {
    return partialObject as unknown as Array<T>;
}

/**
 * Boilerplate logic for managing custom validation on pages
 */
export async function runCustomValidation(
    pageInstance: ui.Page,
    controlCallback: (
        check: (checkOperation: boolean, message: string) => void,
        isValidated: () => boolean,
    ) => Promise<void>,
    notify = false,
    skipValidationLengthCheck = false,
) {
    const validation: string[] = !skipValidationLengthCheck ? await pageInstance.$.page.validate() : [];

    let isValidated = validation.length === 0;

    const check = (checkOperation: boolean, message: string) => {
        if (!checkOperation) validation.push(message);
        isValidated = isValidated && checkOperation;
    };

    await controlCallback(check, () => isValidated);

    if (notify && validation.length > 0) {
        pageInstance.$.showToast(
            ui.localize(
                '@sage/xtrem-master-data/pages__utils__notification__custom_validation_error',
                'Validation errors occurred:{{#each errors}}\n\n- {{this}}{{/each}}',
                {
                    errors: validation,
                },
            ),
            {
                type: 'error',
            },
        );
    }

    return validation;
}

/**
 * First quick version for 2 levels nested grid
 */
export function returnSelectedChildrenFromNestedGrid(gridControl: ui.fields.NestedGridControlObject): string[] {
    const selectedParent = gridControl.selectedRecords[0];
    const selectedChildrenFromParent: Array<string> = [];
    selectedParent.forEach(rowId => {
        const parentLevel = gridControl.getRecordValue(rowId, 0) as any;
        selectedChildrenFromParent.push(...parentLevel.lines.map((record: any) => record._id));
    });
    const selectedChildren = gridControl.selectedRecords[1];
    return [...new Set([...selectedChildren, ...selectedChildrenFromParent])];
}

export function getScaleValue(defaultScale: integer, decimalDigits?: integer): integer {
    return decimalDigits ?? defaultScale;
}
