import type { BusinessEntityAddress } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { isEmpty } from 'lodash';
import { BusinessEntitySelectionForCustomer } from '../pages/business-entity-selection-for-customer';
import { getConcatenatedAddress } from '../shared-functions/address-functions';
import type { AllBePageWithCompanyAndSelection, CustomerSupplierSitePage } from './business-entity';
import { CompanyPage, CustomerPage, SitePage, SupplierPage, isCustomerSupplierSitePage } from './business-entity';
import { manageDeliveryAddress } from './business-entity-customer';

/**
 * @param address page and new primary address sysId
 */
export function switchIsPrimaryAddress(address: { page: AllBePageWithCompanyAndSelection; primarySysId: string }) {
    const primaryAddress = address.page.addresses.value.find(
        addressFilter => addressFilter.isPrimary && addressFilter._id !== address.primarySysId,
    );
    if (primaryAddress) {
        address.page.addresses.addOrUpdateRecordValue({ ...primaryAddress, isPrimary: false });
    }
    address.page.addresses.addOrUpdateRecordValue({ _id: address.primarySysId, isPrimary: true });

    if (isCustomerSupplierSitePage(address.page)) {
        address.page.primaryAddress.value = { _id: address.primarySysId };
    }
}

export function titleBusinessEntityAddressPanel(page: AllBePageWithCompanyAndSelection, address: { _id: string }) {
    const { _id } = address;
    if (Number(_id) < 0) {
        if (page instanceof CustomerPage) {
            return ui.localize(
                '@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_customer_address_title',
                'New customer address',
            );
        }
        if (page instanceof SupplierPage) {
            return ui.localize(
                '@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_supplier_address_title',
                'New supplier address',
            );
        }
        if (page instanceof SitePage) {
            return ui.localize(
                '@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_site_address_title',
                'New site address',
            );
        }
        if (page instanceof CompanyPage) {
            return ui.localize(
                '@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_company_address_title',
                'New company address',
            );
        }
        return ui.localize(
            '@sage/xtrem-master-data/pages__client_functions__business_entity_address__new_business_entity_address_title',
            'New business entity address',
        );
    }

    if (page instanceof CustomerPage) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_customer_address_title',
            'Edit customer address',
        );
    }
    if (page instanceof SupplierPage) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_supplier_address_title',
            'Edit supplier address',
        );
    }
    if (page instanceof SitePage) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_site_address_title',
            'Edit site address',
        );
    }
    if (page instanceof CompanyPage) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_company_address_title',
            'Edit company address',
        );
    }
    return ui.localize(
        '@sage/xtrem-master-data/pages__client_functions__business_entity_address__edit_business_entity_address_title',
        'Edit business entity address',
    );
}

export async function openBusinessEntityAddressPanel(page: AllBePageWithCompanyAndSelection, address: { _id: string }) {
    page.$.loader.isHidden = false;
    const isCustomer = page instanceof CustomerPage || page instanceof BusinessEntitySelectionForCustomer;
    const { _id } = address;
    const {
        isPrimary: currentIsPrimary,
        country: currentCountry,
        isActive: currentIsActive,
        ...currentAddress
    } = page.addresses.getRecordValue(_id) || {
        name: '',
        country: page.country.value,
    };
    const country = currentCountry ?? (page.country.value || undefined);

    const addressValue: ui.PartialNode<BusinessEntityAddress> = {
        isPrimary: !!currentIsPrimary,
        isActive: currentIsActive !== false, // Ensure isActive is true if not explicitly set to false
        country,
        ...currentAddress,
    };
    page.$.loader.isHidden = true;
    // addresses are managed in one field contact in an other ( technical )
    // ...(Number(_id) > 0 ? { _id } : {})
    const result = (await page.$.dialog.page(
        '@sage/xtrem-master-data/BusinessEntityAddressPanel',
        { isCustomer, title: titleBusinessEntityAddressPanel(page, address) },
        { rightAligned: true, size: 'large', resolveOnCancel: true, values: addressValue },
    )) as { address: ui.PartialNodeWithId<BusinessEntityAddress> };

    if (!result?.address && Number(_id) < 0 && !currentAddress?.name) {
        page.addresses.removeRecord(_id);
    }

    if (!result?.address) {
        return;
    }

    const updatedAddress = result.address;

    if (updatedAddress && !isEmpty(updatedAddress)) {
        if (!isCustomer) {
            delete updatedAddress.deliveryDetail;
        }
        page.addresses.addOrUpdateRecordValue({
            ...updatedAddress,
            ...(+(_id || 0) < 0 ? { _id } : {}),
            concatenatedAddressWithoutName: getConcatenatedAddress(updatedAddress, { isWithoutName: true }),
            concatenatedAddress: getConcatenatedAddress(updatedAddress, { isWithoutName: false }),
            businessEntity: {
                _id: page instanceof CustomerPage ? (page.businessEntitySysId.value ?? '-1') : (page._id.value ?? '-1'),
                name: page.name.value || '',
            },
        });

        /** if we change from not primary address to a primary address from the panel */
        if (_id && updatedAddress?.isPrimary && !addressValue.isPrimary) {
            switchIsPrimaryAddress({ page, primarySysId: updatedAddress._id });
        }

        if (isCustomer) {
            if (addressValue.deliveryDetail && !updatedAddress.deliveryDetail) {
                // Ship-to address was switched off
                delete updatedAddress.deliveryDetail;
            }
            manageDeliveryAddress(page, { new: updatedAddress, old: { _id, ...addressValue } });
        }
    }
}

export function newDeleteAddressEntityAddress(address: {
    page: AllBePageWithCompanyAndSelection;
    rowId: string;
    rowItem: ui.PartialNodeWithId<BusinessEntityAddress>;
}) {
    /** Controls delete businessEntityAddress  */
    address.page.addresses.removeRecord(address.rowId);
}

/** On Customer Supplier Site Page we want the primary address to be the one of the customer  site / supplier */
export function setBaseBusinessRelationPrimaryAddress(page: CustomerSupplierSitePage) {
    const businessEntityPrimaryAddress = page.addresses.getRecordByFieldValue('isPrimary', true);
    const businessRelationPrimaryAddress = page.primaryAddress.value;
    if (
        businessEntityPrimaryAddress?._id &&
        businessRelationPrimaryAddress?._id &&
        businessEntityPrimaryAddress._id !== businessRelationPrimaryAddress._id
    ) {
        page.addresses.addOrUpdateRecordValue({ ...businessEntityPrimaryAddress, isPrimary: false });
        page.addresses.addOrUpdateRecordValue({ _id: businessRelationPrimaryAddress._id, isPrimary: true });
    }
}

/** On Customer Supplier Site Page we don't want to set the primary address on the businessEntity  */
export function deleteIsPrimaryProperty(addresses: ui.PartialNodeWithId<BusinessEntityAddress>[]) {
    return addresses.map(address => {
        const { isPrimary, ...newAddress } = address;
        return newAddress;
    });
}
