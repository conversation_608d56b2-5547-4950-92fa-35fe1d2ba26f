import type { LegalEntity } from '@sage/xtrem-master-data-api';
import { exampleNafFR, validateNaf } from '@sage/xtrem-structure/build/lib/shared-functions/naf-controls';
import { exampleRcsFR, validateRcs } from '@sage/xtrem-structure/build/lib/shared-functions/rcs-controls';
import { sirenFormat, validateSiren } from '@sage/xtrem-structure/build/lib/shared-functions/siren-controls';
import * as ui from '@sage/xtrem-ui';
import { exampleTaxIdentification, siretFormat, validTaxIdentification, validateSiretRegex } from '../shared-functions';
import type { AllBePage } from './business-entity';
import { CompanyPage, isCustomerSupplierSitePage } from './business-entity';
import { orText } from './localize';

/**
 *  page.businessEntity is optionnal. /
 *  Country & taxIdNumber field are mandatory, /
 *  to use it on validation control taxIdNumber field
 * @param page
 * @param val tax id number
 * @returns undefined if ok , localized test if ko
 */
export function taxIdNumberValidation(page: AllBePage, val: string) {
    // check before if we have businessEntity in the page then if we don't have a value in page.businessEntity?.value
    if (isCustomerSupplierSitePage(page) && page.legalEntity && page.legalEntity.value === 'corporation' && !val) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__site_page_taxIdNumber_required_if_no_business_entity',
            'Tax ID is required if no business entity is selected.',
        );
    }

    if (
        !(page instanceof CompanyPage) &&
        page.country.value?.id &&
        page.taxIdNumber.value &&
        !validTaxIdentification(page.country.value.id, page.taxIdNumber.value)
    ) {
        return ui.localize(
            '@sage/xtrem-master-data/nodes__business_entity__not-a-valid-tax-id',
            'The format is incorrect. Use the tax ID format: {{format}}.',
            {
                format: exampleTaxIdentification(page.country.value.id).join(` ${orText()} `),
            },
        );
    }

    return undefined;
}
/**
 * siret validation
 * @param page
 * @param val  siret
 * @returns localized error or undefined
 */
export function siretValidation(val: string) {
    if (val && !validateSiretRegex(val)) {
        return ui.localize(
            '@sage/xtrem-master-data/nodes__business_entity__incorrect_format_siret',
            'The format is incorrect. Use the SIRET number format: {{format}}.',
            {
                format: siretFormat,
            },
        );
    }
    return undefined;
}

export function sirenValidation(val: string) {
    if (!validateSiren(val)) {
        return ui.localize(
            '@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_siren',
            'Use the SIREN number format: {{format}}',
            {
                format: sirenFormat,
            },
        );
    }
    return undefined;
}

export function rcsValidation(val: string) {
    // if RCS starts with RCS Anon A => we are in anonymized DB
    if (!val.startsWith('RCS Anon A ') && !validateRcs(val)) {
        return ui.localize(
            '@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_rcs',
            'Use the RCS number format: {{format}}',
            {
                format: exampleRcsFR,
            },
        );
    }
    return undefined;
}

export function nafValidation(val: string) {
    if (!validateNaf(val)) {
        return ui.localize(
            '@sage/xtrem-master-data/nodes__fr_legal_data__incorrect_format_naf',
            'Use the NAF number format: {{format}}',
            {
                format: exampleNafFR,
            },
        );
    }
    return undefined;
}

export async function legalSiretFrenchLeg(page: AllBePage) {
    page.siret.isMandatory = page.country.value?.id === 'FR' && page.legalEntity.value === 'corporation';
    page.siret.isHidden = page.country.value?.id !== 'FR';

    if (page.country.value) {
        await page.$.commitValueAndPropertyChanges();
        if (page.siret.value) {
            await page.siret.validate();
        }
    }
}

function isTaxIdNumberMandatory(legalEntity: LegalEntity) {
    return legalEntity === 'corporation';
}

export function isCountryUs(country: string) {
    return country === 'US';
}

export async function initTaxIdNumber(page: AllBePage) {
    if (page.taxIdNumber) {
        page.taxIdNumber.isMandatory =
            (page.legalEntity.value ? isTaxIdNumberMandatory(page.legalEntity.value as LegalEntity) : true) &&
            !isCountryUs(page.country.value?.id || '');
        if (page.taxIdNumber.value) {
            await page.taxIdNumber.validate();
        }
    }
}
