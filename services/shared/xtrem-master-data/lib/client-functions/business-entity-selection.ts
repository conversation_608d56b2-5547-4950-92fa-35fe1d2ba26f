import type { AllBusinessEntitySelectionPage } from './business-entity';

export function onLoadFromSelection(page: AllBusinessEntitySelectionPage) {
    if (page.$.recordId) {
        page.businessEntity.value = {
            _id: page.$.recordId,
            id: page.id.value ?? '',
            name: page.name.value ?? '',
        };
    }
}

export async function selectRecordFromSelection(page: AllBusinessEntitySelectionPage) {
    if (page.businessEntity.value?._id && page.$.recordId !== page.businessEntity.value._id) {
        await page.$.router.selectRecord(page.businessEntity.value._id, true);
    }
}
