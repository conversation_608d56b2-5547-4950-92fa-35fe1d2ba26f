import * as ui from '@sage/xtrem-ui';

export function valueIsPercentage(value: number) {
    if (value < 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_is_negative',
            'The percentage cannot be negative.',
        );
    }
    if (value > 100) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__item_customer_price_panel__percentage_greater_than_100',
            'The percentage cannot exceed 100.',
        );
    }
    return undefined;
}

export function controlRange(valueFrom: number, valueTo: number) {
    if (valueTo < valueFrom) {
        return ui.localize(
            '@sage/xtrem-master-data/invalid-quantity-range',
            'The quantity range {{qtyRange}} is invalid',
            { qtyRange: `(${valueFrom}, ${valueTo})` },
        );
    }
    return undefined;
}

export function controlPeriod(dateFrom: string, dateTo: string) {
    if (dateTo < dateFrom) {
        return ui.localize('@sage/xtrem-master-data/invalid-period', 'The period {{dates}} is invalid.', {
            dates: `(${dateTo},${dateFrom})`,
        });
    }
    return undefined;
}
