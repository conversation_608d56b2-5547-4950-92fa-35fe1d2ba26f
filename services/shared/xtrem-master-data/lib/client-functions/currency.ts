import * as ui from '@sage/xtrem-ui';
import type { Currency } from '../pages/currency';
import { confirmDialogWithAcceptButtonText } from './page-functions';

/**
 * Checks if there's already an exchange rate on the database for the base currency, destination currency and rate date entered
 */
export async function rateExists(params: {
    pageInstance: Currency;
    base: string;
    destination: string;
    dateRate: string;
}) {
    const rate = await params.pageInstance.$.graph
        .node('@sage/xtrem-master-data/ExchangeRate')
        .aggregate.query(
            ui.queryUtils.edgesSelector(
                {
                    group: {
                        _id: { _by: 'value' },
                    },
                    values: {
                        _id: {
                            distinctCount: true,
                        },
                    },
                },
                {
                    filter: {
                        base: params.base,
                        destination: params.destination,
                        dateRate: params.dateRate,
                    },
                },
            ),
        )
        .execute();

    return rate.edges.length;
}

export async function addExchangeRate(pageInstance: Currency) {
    pageInstance.addRateSection.isHidden = false;
    pageInstance.wasDirty = pageInstance.$.isDirty;
    await pageInstance.$.dialog
        .custom('info', [pageInstance.addRateSection], {
            rightAligned: true,
            dialogTitle: ui.localize(
                '@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_title',
                'Add exchange rate',
            ),
        })
        .then(async () => {
            if (
                pageInstance.destinationCurrency.value &&
                pageInstance.rateDate.value &&
                pageInstance.currencyRate.value
            ) {
                let saveRate = true;
                if (
                    (await rateExists({
                        pageInstance,
                        base: pageInstance._id.value ?? '',
                        destination: pageInstance.destinationCurrency?.value?._id ?? '',
                        dateRate: pageInstance.rateDate.value,
                    })) > 0
                ) {
                    saveRate = await confirmDialogWithAcceptButtonText(
                        pageInstance,
                        ui.localize(
                            '@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_title',
                            'Rate already exists',
                        ),
                        ui.localize(
                            '@sage/xtrem-master-data/pages__currency__side_panel_add_currency_rate_confirmation_message',
                            'The currencies and date are already linked to an exchange rate. Do you want to keep the existing rate or apply your new one?',
                        ),
                        ui.localize('@sage/xtrem-master-data/pages-confirm-apply', 'Apply new'),
                        ui.localize('@sage/xtrem-master-data/pages-cancel-keep', 'Keep'),
                    );
                }
                if (saveRate) {
                    await pageInstance.$.graph
                        .node('@sage/xtrem-master-data/Currency')
                        .mutations.saveExchangeRate(false, {
                            base: pageInstance.$.recordId ?? '',
                            destination: pageInstance.destinationCurrency.value._id ?? '',
                            dateRate: pageInstance.rateDate.value,
                            rate: pageInstance.currencyRate.value,
                        })
                        .execute();
                }
                if (
                    (await rateExists({
                        pageInstance,
                        base: pageInstance.destinationCurrency?.value?._id ?? '',
                        destination: pageInstance._id.value ?? '',
                        dateRate: pageInstance.rateDate.value,
                    })) > 0
                ) {
                    saveRate = await confirmDialogWithAcceptButtonText(
                        pageInstance,
                        ui.localize(
                            '@sage/xtrem-master-data/pages__currency__side_panel_add_inverse_currency_rate_confirmation_title',
                            'Inverse rate already exists',
                        ),
                        ui.localize(
                            '@sage/xtrem-master-data/pages__currency__side_panel_add_currency_reverse_rate_confirmation_message',
                            'The inverse rate already exists for the specified date. Do you want to keep the existing rate or apply a new one?',
                        ),
                        ui.localize('@sage/xtrem-master-data/pages-confirm-apply-new', 'Apply new'),
                        ui.localize('@sage/xtrem-master-data/pages-cancel-keep', 'Keep'),
                    );
                }
                if (saveRate) {
                    const baseRate = Number(pageInstance.currencyRate.value);
                    const inverseRate = (1.0 / baseRate).toFixed(10);

                    await pageInstance.$.graph
                        .node('@sage/xtrem-master-data/Currency')
                        .mutations.saveExchangeRate(false, {
                            base: pageInstance.destinationCurrency.value._id ?? '',
                            destination: pageInstance.$.recordId ?? '',
                            dateRate: pageInstance.rateDate.value,
                            rate: inverseRate,
                        })
                        .execute();
                }
                await pageInstance.currentExchangeRates.refresh();
                await pageInstance.exchangeRates.refresh();
            }
        })
        .finally(() => {
            pageInstance.addRateSection.isHidden = true;
            pageInstance.addRateBlockClean();
            if (!pageInstance.wasDirty && pageInstance.$.isDirty) {
                pageInstance.$.setPageClean();
            }
        });
}

export async function deleteExchangeRate(pageInstance: Currency, recordId: string, inverseRateId: string | null) {
    if (
        await confirmDialogWithAcceptButtonText(
            pageInstance,
            ui.localize('@sage/xtrem-master-data/pages_currency_delete_page_dialog_title', 'Confirm deletion'),
            ui.localize(
                '@sage/xtrem-master-data/pages_currency_delete_page_dialog_content',
                'You are about to delete this record.',
            ),
            ui.localize('@sage/xtrem-master-data/pages-confirm-delete', 'Delete'),
        )
    ) {
        await pageInstance.$.graph.node('@sage/xtrem-master-data/ExchangeRate').deleteById(recordId).execute();
        if (inverseRateId) {
            if (
                await confirmDialogWithAcceptButtonText(
                    pageInstance,
                    ui.localize('@sage/xtrem-master-data/pages_currency_delete_page_dialog_title', 'Confirm deletion'),
                    ui.localize(
                        '@sage/xtrem-master-data/pages_currency_delete_inverse_rate_page_dialog_content',
                        'The inverse rate already exists for the specified date. Do you want to keep the inverse rate or delete it?',
                    ),
                    ui.localize('@sage/xtrem-master-data/pages-confirm-delete', 'Delete'),
                )
            ) {
                await pageInstance.$.graph
                    .node('@sage/xtrem-master-data/ExchangeRate')
                    .deleteById(inverseRateId)
                    .execute();
            }
        }
    }
    await pageInstance.currentExchangeRates.refresh();
    await pageInstance.exchangeRates.refresh();
    await pageInstance.exchangeRatesGraph.refresh();
}

export async function getRateId(params: {
    pageInstance: Currency;
    base: string;
    destination: string;
    dateRate: string;
}) {
    const rate = await params.pageInstance.$.graph
        .node('@sage/xtrem-master-data/ExchangeRate')
        .query(
            ui.queryUtils.edgesSelector(
                {
                    _id: true,
                },
                {
                    filter: {
                        base: params.base,
                        destination: params.destination,
                        dateRate: params.dateRate,
                    },
                },
            ),
        )
        .execute();
    return rate.edges.length > 0 ? rate.edges[0].node._id : null;
}
