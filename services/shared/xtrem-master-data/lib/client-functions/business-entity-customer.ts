import type { BusinessEntityAddress, Customer, WeekDays } from '@sage/xtrem-master-data-api';
import type * as ui from '@sage/xtrem-ui';
import type { BusinessEntityAddressPanel } from '../pages/business-entity-address-panel';
import type { BusinessEntitySelectionForCustomer as BusinessEntitySelectionForCustomerPage } from '../pages/business-entity-selection-for-customer';
import type { CustomerPage } from './business-entity';

export function getCustomer(page: CustomerPage): ui.PartialNodeWithId<Customer> {
    return {
        _id: page.$.recordId ?? '-1',
        businessEntity: {
            _id: page.businessEntitySysId.value ?? '',
            id: page.id.value ?? '',
            name: page.name.value ?? '',
        },
    };
}

/**
 * Switches the primary delivery address of a customer.
 *  getRecordByFieldValue('deliveryDetail.isPrimary', true), isn't working for now but it will be better to use it
 */
export function switchIsPrimaryDeliveryAddress(address: {
    page: CustomerPage | BusinessEntitySelectionForCustomerPage;
    primarySysId: string;
}) {
    const currentPrimaryAddressId = address.page.addresses.value
        .filter(newPrimaryAddress => newPrimaryAddress._id !== address.primarySysId) // exclude the new primary address
        .find(currentPrimaryAddress => currentPrimaryAddress.deliveryDetail?.isPrimary)?._id;

    if (currentPrimaryAddressId) {
        address.page.addresses.addOrUpdateRecordValue({
            _id: currentPrimaryAddressId,
            deliveryDetail: { isPrimary: false },
        });
    }
    address.page.addresses.addOrUpdateRecordValue({ _id: address.primarySysId, deliveryDetail: { isPrimary: true } });
}

export function manageDeliveryAddress(
    page: CustomerPage | BusinessEntitySelectionForCustomerPage,
    address: { new: ui.PartialNodeWithId<BusinessEntityAddress>; old: ui.PartialNodeWithId<BusinessEntityAddress> },
) {
    const { new: newAddress, old: oldAddress } = address;

    if (oldAddress.deliveryDetail && !newAddress.deliveryDetail) {
        // Ship-to address was removed
        page.addresses.addOrUpdateRecordValue({
            _id: oldAddress._id,
            deliveryDetail: undefined,
        });

        if (oldAddress.deliveryDetail.isPrimary) {
            // If the removed ship-to address was primary, find a new primary
            const newPrimaryShipTo = page.addresses.value.find(
                addr => addr._id !== oldAddress._id && addr.deliveryDetail?.isActive,
            );
            if (newPrimaryShipTo) {
                switchIsPrimaryDeliveryAddress({ page, primarySysId: newPrimaryShipTo._id });
            }
        }
    } else if (newAddress.deliveryDetail) {
        // Update or add ship-to address
        page.addresses.addOrUpdateRecordValue({
            _id: newAddress._id,
            deliveryDetail: {
                ...newAddress.deliveryDetail,
                isActive: newAddress.deliveryDetail.isActive !== false,
            },
        });

        if (newAddress.deliveryDetail.isPrimary && !oldAddress.deliveryDetail?.isPrimary) {
            switchIsPrimaryDeliveryAddress({ page, primarySysId: newAddress._id });
        }
    }
}

export const defaultWorkingDays: WeekDays[] = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

/** Default working days to  weekDays */
export function setDefaultWorkingDays(page: BusinessEntityAddressPanel) {
    if (!page.deliveryDetail.value && !page.workDaysSelection.value) {
        page.workDaysSelection.value = defaultWorkingDays;
    }
}

export function deliveryAddressValues(
    deliveryDetail: ui.PartialNodeWithId<BusinessEntityAddress['deliveryDetail']>,
): ui.PartialNodeWithId<BusinessEntityAddress['deliveryDetail']> {
    return {
        ...deliveryDetail,
        workDaysSelection: deliveryDetail?.workDaysSelection || defaultWorkingDays,
        isMondayWorkDay: deliveryDetail?.workDaysSelection?.includes('monday') || false,
        isTuesdayWorkDay: deliveryDetail?.workDaysSelection?.includes('tuesday') || false,
        isWednesdayWorkDay: deliveryDetail?.workDaysSelection?.includes('wednesday') || false,
        isThursdayWorkDay: deliveryDetail?.workDaysSelection?.includes('thursday') || false,
        isFridayWorkDay: deliveryDetail?.workDaysSelection?.includes('friday') || false,
        isSaturdayWorkDay: deliveryDetail?.workDaysSelection?.includes('saturday') || false,
        isSundayWorkDay: deliveryDetail?.workDaysSelection?.includes('sunday') || false,
    };
}
