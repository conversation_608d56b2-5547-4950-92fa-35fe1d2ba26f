import * as ui from '@sage/xtrem-ui';

/** Contacts for business entity address {{addressName}} */
export function getTitleContacts(addressName: string) {
    return ui.localize(
        '@sage/xtrem-master-data/pages__address_functions_businessentity_address_contacts____title',
        'Contacts for business entity address {{addressName}}',
        { addressName },
    );
}

/** Active */
export const contactActive = ui.localize('@sage/xtrem-master-data/pages__business_entity__contact_active', 'Active');

/** Inactive */
export const contactInActive = ui.localize(
    '@sage/xtrem-master-data/pages__business_entity__contact_inactive',
    'Inactive',
);

/** */
export const addressActive = ui.localize('@sage/xtrem-master-data/pages__business_entity__address_active', 'Active');

/** Inactive */
export const addressInActive = ui.localize(
    '@sage/xtrem-master-data/pages__business_entity__address_inactive',
    'Inactive',
);

/** New business entity address */
export const newBeAddres = ui.localize(
    '@sage/xtrem-master-data/pages__address_functions_businessentity_add_new____title',
    'New business entity address',
);

/** Edit business entity address */
export const editBeAddres = ui.localize(
    '@sage/xtrem-master-data/pages__address_functions_businessentity_edit____title',
    'Edit business entity address',
);

/** New contact */
export const newContact = ui.localize(
    '@sage/xtrem-master-data/pages__address-contacts_panel_add_new____title',
    'New contact',
);

/** Edit contact */
export const editContact = ui.localize(
    '@sage/xtrem-master-data/pages__address-contacts_panel_edit____title',
    'Edit contact',
);

/** The address must have a primary and active contact */
export const addressMustHavePrimaryAndActiveContact = ui.localize(
    '@sage/xtrem-master-data/pages__customer__primary_active_address_contact_mandatory',
    'The address must have a primary and active contact',
);

/** Invalid phone number */
export const invalidPhoneNumber = ui.localize(
    '@sage/xtrem-master-data/telephone-validation-error',
    'Invalid phone number',
);

/** Invalid email address */
export const invalidEmailAddress = ui.localize(
    '@sage/xtrem-master-data/email-validation-error',
    'Invalid email address',
);

/** A primary address must be active. */
export const aPrimaryAddressMustBeActive = ui.localize(
    '@sage/xtrem-master-data/pages__address_panel__isPrimary_must_be_active',
    'A primary address must be active.',
);

/** The contact is primary and cannot be deleted. */
export const cantDeletePrimaryContact = ui.localize(
    '@sage/xtrem-master-data/pages__customer__contact_assigned_primary',
    'The contact is primary and cannot be deleted.',
);

/** New address */
export const newAddress = ui.localize('@sage/xtrem-master-data/pages__address_panel__new____title', 'New address');

/** Edit address */
export const editAddress = ui.localize('@sage/xtrem-master-data/pages__address_panel__edit____title', 'Edit address');

/** Invalid ZIP code */
export const invalidZipCode = ui.localize(
    '@sage/xtrem-master-data/events/control__address-control__zipcode-validation-error',
    'Invalid ZIP code',
);

/** Invalid postal code */
export const invalidPostalCode = ui.localize(
    '@sage/xtrem-master-data/events/control__address-control__postalcode-validation-error',
    'Invalid postal code',
);

export const displayAddressActive = ui.localize(
    '@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_active',
    'Active',
);
export const displayAddressInActive = ui.localize(
    '@sage/xtrem-master-data/pages__address-contacts_panel_panel__display_address_inactive',
    'Inactive',
);
