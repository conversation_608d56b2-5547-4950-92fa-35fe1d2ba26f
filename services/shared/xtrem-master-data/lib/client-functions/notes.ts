import {
    edgesSelector,
    type ClientCollection,
    type UpdateOperation,
    type VitalClientNode,
    type VitalClientNodeInput,
} from '@sage/xtrem-client';
import type { GraphApi as MasterDataApi } from '@sage/xtrem-master-data-api';
import type { GraphApi, SysNoteAssociation, SysNoteAssociationInput } from '@sage/xtrem-system-api';
import type { GraphQLMutationApi } from '@sage/xtrem-ui';
import * as ui from '@sage/xtrem-ui';

export interface NodeWithNote extends VitalClientNode {
    _notes: ClientCollection<SysNoteAssociation>;
}

export interface NodeWithNoteInput extends VitalClientNodeInput {
    _notes?: Partial<SysNoteAssociationInput & { _action: 'create' | 'update' | 'delete' }>[];
}

export interface NodeWithNoteMutation {
    update: UpdateOperation<NodeWithNoteInput, NodeWithNote>;
}

export const noteAssociationSelector = edgesSelector<SysNoteAssociation>(
    {
        _id: true,
        note: { _id: true, title: true, content: { value: true } },
    },
    { orderBy: { note: { _updateStamp: -1 } } },
);

export function createNote(graph: GraphQLMutationApi<GraphApi>, text: string) {
    return graph
        .node('@sage/xtrem-system/SysNote')
        .create({ _id: true }, { data: { title: text, content: { value: text } } })
        .execute();
}

export async function deleteAssociationNote(
    operation: NodeWithNoteMutation,
    node: { _id: string; associationSysId: string },
) {
    await operation
        .update(
            { _id: true },
            { data: { _id: `${node._id}`, _notes: [{ _action: 'delete', _id: `${node.associationSysId}` }] } },
        )
        .execute();
}

async function associateNote(
    operation: NodeWithNoteMutation,
    node: { _id: string; noteSysId: string; associationSysId?: string },
) {
    const association: Partial<
        SysNoteAssociationInput & {
            _action: 'create' | 'update';
        }
    > = {
        ...(node.associationSysId ? { _id: node.associationSysId } : {}),
        _action: node.associationSysId ? 'update' : 'create',
        note: node.noteSysId,
    };

    const createdNote = await operation
        .update(
            {
                _id: true,
                _notes: {
                    queryAggregate: {
                        edges: {
                            node: {
                                group: { sourceNodeName: { _by: 'value' }, sourceNodeId: { _by: 'value' } },
                                values: { _id: { max: true } },
                            },
                        },
                    },
                },
            },
            { data: { _id: `${node._id}`, _notes: [association] } },
        )
        .execute();

    return node.associationSysId
        ? node.associationSysId
        : (createdNote._notes.queryAggregate.edges.at(0)?.node.values._id?.max ?? '');
}

export function deleteAssociateContactNote(
    graph: GraphQLMutationApi<MasterDataApi>,
    association: { _id: string; associationSysId: string },
) {
    return deleteAssociationNote(graph.node('@sage/xtrem-master-data/BusinessEntityContact'), association);
}

export function associateContactNote(
    graph: GraphQLMutationApi<MasterDataApi>,
    association: { _id: string; noteSysId: string },
) {
    return associateNote(graph.node('@sage/xtrem-master-data/BusinessEntityContact'), association);
}

export function updateAssociationContactNote(
    graph: GraphQLMutationApi<MasterDataApi>,
    association: { _id: string; noteSysId: string; associationSysId: string },
) {
    return associateNote(graph.node('@sage/xtrem-master-data/BusinessEntityContact'), association);
}

export function confirmDialogWithAcceptButtonText(
    widget: ui.widgets.ContactCardWidget,
    title: string,
    message: string,
    acceptButtonText: string,
) {
    const options = {
        acceptButton: { text: acceptButtonText, isDestructive: true },
        cancelButton: { text: ui.localize('@sage/xtrem-master-data/widgets-confirm-cancel', 'Cancel') },
    };
    return widget.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}
