import type { BusinessEntityContact } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { BusinessEntity } from '../pages/business-entity';
import type { AllBePageWithCompany } from './business-entity';

export function switchIsPrimaryContact(contact: {
    page: AllBePageWithCompany;
    primarySysId: string;
    addressSysId?: string;
}) {
    if (!contact.addressSysId) {
        throw new Error('Address is mandatory');
    }

    const primaryCurrentContact = contact.page.contacts.value
        .filter(
            contactAddress =>
                contactAddress.address?._id === contact.addressSysId && contactAddress._id !== contact.primarySysId,
        )
        .find(primaryContact => primaryContact.isPrimary);

    if (primaryCurrentContact) {
        contact.page.contacts.addOrUpdateRecordValue({ ...primaryCurrentContact, isPrimary: false });
    }
    contact.page.contacts.addOrUpdateRecordValue({ _id: contact.primarySysId, isPrimary: true });
}

export function titleBusinessEntityContactPanel(contact: {
    _id?: string;
    data?: ui.PartialNode<BusinessEntityContact>;
}) {
    const { _id } = contact;
    if (Number(_id) < 0) {
        return ui.localize(
            '@sage/xtrem-master-data/pages__client_functions__business_entity_contact__new_contact_title',
            'New contact',
        );
    }
    return ui.localize(
        '@sage/xtrem-master-data/pages__client_functions__business_entity_contact__edit_contact_title',
        'Edit contact',
    );
}

/** Used on BusinessEntityContact pod collection field */
export async function openBusinessEntityAddressContactPanel(
    page: AllBePageWithCompany,
    contact: { _id?: string; data?: ui.PartialNode<BusinessEntityContact> },
) {
    page.$.loader.isHidden = false;
    const addresses = JSON.stringify(page.addresses.value);
    const { _id } = contact;
    const currentContactValue: ui.PartialNode<BusinessEntityContact> = {
        isActive: true,
        ...contact.data,
    };
    const contactSysId = Number(_id || 0);

    const result = (await page.$.dialog.page(
        '@sage/xtrem-master-data/BusinessEntityContactPanel',
        {
            ...(+contactSysId > 0 ? { _id: contactSysId } : {}),
            addresses,
            title: titleBusinessEntityContactPanel(contact),
        },
        { rightAligned: true, size: 'large', resolveOnCancel: true, values: currentContactValue },
    )) as { contact: ui.PartialNode<BusinessEntityContact> };

    if (contact._id && !result?.contact && !contact.data?.lastName) {
        page.contacts.removeRecord(contact._id);
    }

    if (!result?.contact) {
        return;
    }

    const changeContact = result.contact;

    delete changeContact.businessEntity;
    page.contacts.addOrUpdateRecordValue({ ...(+(_id || 0) < 0 ? { _id } : {}), ...changeContact });

    if (_id && changeContact?.isPrimary && (!contact.data?.isPrimary || !contact.data)) {
        switchIsPrimaryContact({ page, primarySysId: _id, addressSysId: changeContact.address?._id });
    }
}

export function contactTitleForPod(contact: ui.PartialNodeWithId<BusinessEntityContact>) {
    if (contact?.title) {
        return [
            contact?.title ? ui.localizeEnumMember('@sage/xtrem-master-data/Title', contact?.title) : '',
            contact?.firstName,
            contact?.lastName,
        ]
            .filter(s => !!s)
            .join(' ');
    }
    return '';
}

export function contactValidation(page: BusinessEntity, contact: ui.PartialNodeWithId<BusinessEntityContact>) {
    if (page.contacts.value.length === 1) {
        // deactivation for the first contact is not possible
        if (!contact.isActive) {
            page.$.showToast(
                ui.localize(
                    '@sage/xtrem-master-data/pages__customer__primary_active_address_contact_mandatory',
                    'The address must have a primary and active contact',
                ),
                { type: 'warning' },
            );
        }
    }
}
