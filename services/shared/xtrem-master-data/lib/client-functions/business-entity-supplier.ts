import type { Supplier } from '@sage/xtrem-master-data-api';
import type * as ui from '@sage/xtrem-ui';
import type { SupplierPage } from './business-entity';

export function getSupplier(page: SupplierPage): ui.PartialNodeWithId<Supplier> {
    return {
        _id: page.$.recordId || '-1',
        businessEntity: {
            _id: page.businessEntitySysId.value ?? '',
            id: page.id.value ?? '',
            name: page.name.value ?? '',
        },
    };
}
