import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi, SequenceNumber } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';

export async function setLocationRequired(
    page: ui.Page<GraphApi>,
    sequence: ExtractEdgesPartial<SequenceNumber>,
): Promise<boolean> {
    if (!sequence) {
        return false;
    }
    if (sequence.definitionLevel === 'site' || sequence.definitionLevel === 'company') {
        return true;
    }
    if (sequence._id) {
        const components = extractEdges(
            await page.$.graph
                .node('@sage/xtrem-master-data/SequenceNumberComponent')
                .query(
                    ui.queryUtils.edgesSelector(
                        { type: true },
                        {
                            filter: {
                                sequenceNumber: `${sequence._id}`,
                                type: { _in: ['site', 'company'] },
                            },
                        },
                    ),
                )
                .execute(),
        );
        return components.length > 0;
    }

    return false;
}
