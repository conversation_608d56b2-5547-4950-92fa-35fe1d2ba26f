import type { BusinessEntity, GraphApi } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { isEmpty } from 'lodash';
import { BusinessEntity as BusinessEntityPage } from '../pages/business-entity';
import { BusinessEntityAddressPanel } from '../pages/business-entity-address-panel';
import type { BusinessEntitySelectionForCustomer as BusinessEntitySelectionForCustomerPage } from '../pages/business-entity-selection-for-customer';
import type { BusinessEntitySelectionForSite as BusinessEntitySelectionForSitePage } from '../pages/business-entity-selection-for-site';
import type { BusinessEntitySelectionForSupplier as BusinessEntitySelectionForSupplierPage } from '../pages/business-entity-selection-for-supplier';
import { Company as CompanyPage } from '../pages/company';
import { Customer as CustomerPage } from '../pages/customer';
import { Site as SitePage } from '../pages/site';
import { Supplier as SupplierPage } from '../pages/supplier';
import { setBaseBusinessRelationPrimaryAddress } from './business-entity-address';
import { confirmDialogWithAcceptButtonText } from './page-functions';

interface DeletionArguments<K> {
    nodeName?: K;
    _id?: string | number;
}

export type BeConstructorPagesName = 'BusinessEntity' | 'Company' | 'Customer' | 'Site' | 'Supplier';

export type AllBePageWithCompany = BusinessEntityPage | CustomerPage | SitePage | SupplierPage | CompanyPage;
export type AllBePage = BusinessEntityPage | CustomerPage | SitePage | SupplierPage;
export type CustomerSupplierSitePage = CustomerPage | SitePage | SupplierPage;
export type AllbePageWithoutBe = CustomerPage | SitePage | SupplierPage | CompanyPage;
export type SiteSupplierPage = SitePage | SupplierPage;
export type BusinessEntityCompanyPage = BusinessEntityPage; // | CompanyPage;

export type AllBePageWithCompanyAndSelection = AllBePageWithCompany | BusinessEntitySelectionForCustomerPage;

export type AllBusinessEntitySelectionPage =
    | BusinessEntitySelectionForCustomerPage
    | BusinessEntitySelectionForSupplierPage
    | BusinessEntitySelectionForSitePage;

export type BusinessEntityAddressPage = BusinessEntityAddressPanel;

export type CreateOrUpdate = 'create' | 'update';

export { BusinessEntityPage, CompanyPage, CustomerPage, SitePage, SupplierPage };

export function isCustomerSupplierSitePage(page: AllBePageWithCompanyAndSelection): page is CustomerSupplierSitePage {
    return !!(page instanceof CustomerPage || page instanceof SupplierPage || page instanceof SitePage);
}

export function isBusinessEntityPage(page: AllBePageWithCompany): page is AllBePage {
    return !!(
        page instanceof CustomerPage ||
        page instanceof SupplierPage ||
        page instanceof SitePage ||
        page instanceof BusinessEntityPage
    );
}

/** Page binded to a BusinessEntityAddress (Panel or listContacts) */
export function isBusinessEntityAddressPage(page: any): page is BusinessEntityAddressPage {
    return !!(page instanceof BusinessEntityAddressPanel);
}

export function showNotificationAfterSave(page: ui.Page, action: 'create' | 'update') {
    if (action === 'create') {
        page.$.showToast(ui.localize('@sage/xtrem-master-data/create-confirmation', 'Record created'), {
            type: 'success',
        });
    }
    if (action === 'update') {
        page.$.showToast(ui.localize('@sage/xtrem-master-data/update-confirmation', 'The record has been updated'), {
            type: 'success',
        });
    }
}

function getDeleteParamChildType(page: CustomerSupplierSitePage): DeletionArguments<keyof GraphApi> {
    if (page instanceof CustomerPage) {
        return { nodeName: '@sage/xtrem-master-data/Customer', _id: page.$.recordId || '' };
    }
    if (page instanceof SitePage) {
        return { nodeName: '@sage/xtrem-system/Site', _id: page.$.recordId || '' };
    }
    if (page instanceof SupplierPage) {
        return { nodeName: '@sage/xtrem-master-data/Supplier', _id: page.$.recordId || '' };
    }
    throw new Error('Invalid page type');
}

export async function deleteChild(page: CustomerSupplierSitePage) {
    if (
        await confirmDialogWithAcceptButtonText(
            page,
            ui.localize('@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_title', 'Confirm deletion'),
            ui.localize(
                '@sage/xtrem-master-data/pages_business-entity_delete_page_dialog_content',
                'You are about to delete this record.',
            ),
            ui.localize('@sage/xtrem-master-data/pages-confirm-delete', 'Delete'),
        )
    ) {
        page.$.loader.isHidden = false;

        await page.$.graph.delete(getDeleteParamChildType(page));

        page.$.showToast(ui.localize('@sage/xtrem-master-data/delete-confirmation', 'Record deleted'), {
            type: 'success',
        });
        await page.$.refreshNavigationPanel();
        await page.$.router.closeRecord();

        page.$.loader.isHidden = true;
    }
}

/** Custom save because we don't pass into onLoad after $standarSaveAction */
export async function saveBusinessEntity(page: CustomerSupplierSitePage) {
    page.$.loader.isHidden = false;
    if (page.$.isDirty === false) {
        setBaseBusinessRelationPrimaryAddress(page);
        page.$.setPageClean();
    }
    await page.$standardSaveAction.execute(true);
    page.$.loader.isHidden = true;
}

export function getBusinessEntity(page: AllBePage): ui.PartialNodeWithId<BusinessEntity> {
    if (page instanceof CustomerPage) {
        return {
            _id: page.businessEntitySysId.value ?? '',
            id: page.id.value ?? '',
            name: page.name.value ?? '',
        };
    }
    return { _id: page._id.value || '', id: page.id.value || '', name: page.name.value || '' };
}

function deepBindChildField(field: ui.plugin.UiComponentProperties, childNode: string): boolean {
    const { bind } = field as any;
    return !!bind && typeof bind !== 'string' && !isEmpty(bind) && Object.keys(bind).at(0) === childNode;
}

export function getDeepBindProperties(page: AllBePage, childNode: string) {
    const fields = page._pageMetadata.uiComponentProperties;

    return Object.entries(fields)
        .filter(([, field]) => deepBindChildField(field, childNode))
        .map(([key]) => key);
}
