import { extractEdges } from '@sage/xtrem-client';
import type { CostCategory, GraphApi, ResourceCostCategory, ShiftDetail } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import type { GroupResource as GroupResourcePage } from '../pages/group-resource';
import { LaborResource as LaborResourcePage } from '../pages/labor-resource';
import type { MachineResource as MachineResourcePage } from '../pages/machine-resource';
import type { ToolResource as ToolResourcePage } from '../pages/tool-resource';

export type AllResourcePages = GroupResourcePage | LaborResourcePage | MachineResourcePage | ToolResourcePage;

export { GroupResourcePage, LaborResourcePage, MachineResourcePage, ToolResourcePage };

function durationInHoursAndMinutes(minutes: number): string {
    return ui.localize(
        '@sage/xtrem-master-data/pages__resource_functions__duration_in_hours_and_minutes',
        '{{hours}} hours {{minutes}} mins',
        { hours: minutes / 60, minutes: minutes % 60 },
    );
}

function getShiftPattern(index: number, shiftDetails: any): string {
    let shiftPattern = '';
    if (shiftDetails.length >= index) {
        shiftPattern = `${shiftDetails[index - 1].shiftDetail.shiftStart}-${
            shiftDetails[index - 1].shiftDetail.shiftEnd
        }`;
    }
    return shiftPattern;
}

function addWeeklyDetail(page: AllResourcePages, day: string, isFullWeek: boolean, dayShift: any, _id: number) {
    if (dayShift) {
        const shiftDetails = extractEdges<ShiftDetail>(dayShift.shiftDetails);
        return page.weeklyDetails.value.concat({
            _id: _id.toString(),
            day: ui.localizeEnumMember('@sage/xtrem-master-data/WeekDays', day),
            dailyShift: dayShift.id,
            capacity: durationInHoursAndMinutes(dayShift.capacity),
            shift1: getShiftPattern(1, shiftDetails),
            shift2: getShiftPattern(2, shiftDetails),
            shift3: getShiftPattern(3, shiftDetails),
            shift4: getShiftPattern(4, shiftDetails),
            shift5: getShiftPattern(5, shiftDetails),
        });
    }
    return page.weeklyDetails.value.concat({
        _id: _id.toString(),
        day,
        capacity: isFullWeek ? durationInHoursAndMinutes(24 * 60) : '',
    });
}

function updateShiftColumns(page: AllResourcePages, ws: any) {
    let maxShifts = 0;
    maxShifts = Math.max(
        ws.mondayShift && ws.mondayShift.shiftDetails ? ws.mondayShift?.shiftDetails?.length : 0,
        maxShifts,
    );
    maxShifts = Math.max(
        ws.tuesdayShift && ws.tuesdayShift.shiftDetails ? ws.tuesdayShift?.shiftDetails?.length : 0,
        maxShifts,
    );
    maxShifts = Math.max(
        ws.wednesdayShift && ws.wednesdayShift.shiftDetails ? ws.wednesdayShift?.shiftDetails?.length : 0,
        maxShifts,
    );
    maxShifts = Math.max(
        ws.thursdayShift && ws.thursdayShift.shiftDetails ? ws.thursdayShift?.shiftDetails?.length : 0,
        maxShifts,
    );
    maxShifts = Math.max(
        ws.fridayShift && ws.fridayShift.shiftDetails ? ws.fridayShift?.shiftDetails?.length : 0,
        maxShifts,
    );
    maxShifts = Math.max(
        ws.saturdayShift && ws.saturdayShift.shiftDetails ? ws.saturdayShift?.shiftDetails?.length : 0,
        maxShifts,
    );
    maxShifts = Math.max(
        ws.sundayShift && ws.sundayShift.shiftDetails ? ws.sundayShift.shiftDetails.length : 0,
        maxShifts,
    );

    for (let col = 1; col <= maxShifts; col += 1) {
        page.weeklyDetails.showColumn(`shift${col}`);
    }
    for (let col = maxShifts + 1; col <= 10; col += 1) {
        page.weeklyDetails.hideColumn(`shift${col}`);
    }
}
export function updateWeeklyDetails(page: AllResourcePages, ws: any) {
    const { capacity } = ws;
    page.resourceCapacity.value = durationInHoursAndMinutes(capacity);
    page.fullWeek.value = ws.isFullWeek;
    page.weeklyDetails.value = page.weeklyDetails.value.slice(page.weeklyDetails.value.length);
    page.weeklyDetails.value = addWeeklyDetail(page, 'monday', ws.isFullWeek, ws.mondayShift, 1);
    page.weeklyDetails.value = addWeeklyDetail(page, 'tuesday', ws.isFullWeek, ws.tuesdayShift, 2);
    page.weeklyDetails.value = addWeeklyDetail(page, 'wednesday', ws.isFullWeek, ws.wednesdayShift, 3);
    page.weeklyDetails.value = addWeeklyDetail(page, 'thursday', ws.isFullWeek, ws.thursdayShift, 4);
    page.weeklyDetails.value = addWeeklyDetail(page, 'friday', ws.isFullWeek, ws.fridayShift, 5);
    page.weeklyDetails.value = addWeeklyDetail(page, 'saturday', ws.isFullWeek, ws.saturdayShift, 6);
    page.weeklyDetails.value = addWeeklyDetail(page, 'sunday', ws.isFullWeek, ws.sundayShift, 7);

    updateShiftColumns(page, ws);
}

export async function getWeeklyShiftNode(page: ui.Page<GraphApi>, id: number) {
    const result = await page.$.graph
        .node('@sage/xtrem-master-data/WeeklyShift')
        .read(
            {
                _id: true,
                id: true,
                name: true,
                isFullWeek: true,
                capacity: true,
                mondayShift: {
                    _id: true,
                    id: true,
                    name: true,
                    isFullDay: true,
                    capacity: true,
                    shiftDetails: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    shiftDetail: {
                                        shiftStart: true,
                                        shiftEnd: true,
                                    },
                                },
                            },
                        },
                    },
                },
                tuesdayShift: {
                    _id: true,
                    id: true,
                    name: true,
                    isFullDay: true,
                    capacity: true,
                    shiftDetails: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    shiftDetail: {
                                        shiftStart: true,
                                        shiftEnd: true,
                                    },
                                },
                            },
                        },
                    },
                },
                wednesdayShift: {
                    _id: true,
                    id: true,
                    name: true,
                    isFullDay: true,
                    capacity: true,
                    shiftDetails: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    shiftDetail: {
                                        shiftStart: true,
                                        shiftEnd: true,
                                    },
                                },
                            },
                        },
                    },
                },
                thursdayShift: {
                    _id: true,
                    id: true,
                    name: true,
                    isFullDay: true,
                    capacity: true,
                    shiftDetails: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    shiftDetail: {
                                        shiftStart: true,
                                        shiftEnd: true,
                                    },
                                },
                            },
                        },
                    },
                },
                fridayShift: {
                    _id: true,
                    id: true,
                    name: true,
                    isFullDay: true,
                    capacity: true,
                    shiftDetails: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    shiftDetail: {
                                        shiftStart: true,
                                        shiftEnd: true,
                                    },
                                },
                            },
                        },
                    },
                },
                saturdayShift: {
                    _id: true,
                    id: true,
                    name: true,
                    isFullDay: true,
                    capacity: true,
                    shiftDetails: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    shiftDetail: {
                                        shiftStart: true,
                                        shiftEnd: true,
                                    },
                                },
                            },
                        },
                    },
                },
                sundayShift: {
                    _id: true,
                    id: true,
                    name: true,
                    isFullDay: true,
                    capacity: true,
                    shiftDetails: {
                        query: {
                            edges: {
                                node: {
                                    _id: true,
                                    shiftDetail: {
                                        shiftStart: true,
                                        shiftEnd: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
            `${id}`,
        )
        .execute();

    return result ?? null;
}

export async function loadMandatoryCostCategories(page: AllResourcePages) {
    const mandatoryCostCategories = extractEdges<PartialCollectionValueWithIds<CostCategory>>(
        await page.$.graph
            .node('@sage/xtrem-master-data/CostCategory')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        costCategoryType: true,
                        name: true,
                        id: true,
                    },
                    { filter: { isMandatory: true } },
                ),
            )
            .execute(),
    );

    mandatoryCostCategories.forEach(costCategory => {
        page.resourceCostCategories.addRecord({ costCategory, setupCost: '0', runCost: '0' });
    });
}

export async function loadResourceCostCategories(page: AllResourcePages, resourceID: string) {
    const resourceCostCategories = extractEdges<PartialCollectionValueWithIds<ResourceCostCategory>>(
        await page.$.graph
            .node('@sage/xtrem-master-data/ResourceCostCategory')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        costCategory: {
                            _id: true,
                            costCategoryType: true,
                            name: true,
                            id: true,
                        },
                        setupCost: true,
                        runCost: true,
                        costUnit: {
                            _id: true,
                            id: true,
                            symbol: true,
                            name: true,
                        },
                        indirectCostSection: {
                            _id: true,
                            name: true,
                            id: true,
                        },
                    },
                    { filter: { resource: { _id: resourceID } } },
                ),
            )
            .execute(),
    );

    resourceCostCategories.forEach(resourceCostCategory => {
        page.resourceCostCategories.addRecord({
            costCategory: resourceCostCategory.costCategory,
            setupCost: resourceCostCategory.setupCost,
            runCost: resourceCostCategory.runCost,
            costUnit: resourceCostCategory.costUnit,
            indirectCostSection: resourceCostCategory.indirectCostSection,
        });
    });
}

export function getSerializedValuesForResource(page: AllResourcePages) {
    const pageValuesObj = page.$.values;
    pageValuesObj.activeRange = `[${page.activeFrom.value ?? ''},${page.activeTo.value ?? ''}]`;
    return pageValuesObj;
}
