import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { GraphApi, Item } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';

export function confirmDialogWithAcceptButtonText(
    page: ui.Page,
    title: string,
    message: string,
    acceptButtonText: string,
    cancelButtonText?: string,
) {
    const options = {
        acceptButton: { text: acceptButtonText },
        cancelButton: {
            text: cancelButtonText || ui.localize('@sage/xtrem-master-data/pages-confirm-cancel', 'Cancel'),
        },
    };
    return page.$.dialog
        .confirmation('warn', title, message, options)
        .then(() => true)
        .catch(() => false);
}

export function progressColorBar(value: any) {
    if (value < 40) {
        return ui.tokens.colorsSemanticNegative500;
    }
    if (value > 40 && value <= 70) {
        return ui.tokens.colorsSemanticCaution500;
    }
    return ui.tokens.colorsSemanticPositive500;
}

export async function checkStockSite(
    page: ui.Page<GraphApi>,
    item?: ExtractEdgesPartial<Item> | null,
    stockSite?: ExtractEdgesPartial<Site> | null,
): Promise<ExtractEdgesPartial<Site> | undefined> {
    if (item && stockSite) {
        const itemSites = extractEdges(
            await page.$.graph
                .node('@sage/xtrem-master-data/ItemSite')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            site: { id: true },
                        },
                        {
                            filter: { item: { _id: item._id } },
                        },
                    ),
                )
                .execute(),
        );
        if (itemSites.length > 0) {
            if (itemSites.some(itemSite => itemSite.site.id === stockSite.id)) {
                return stockSite;
            }
        }
    }
    return undefined;
}
