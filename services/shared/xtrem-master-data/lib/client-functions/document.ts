import { extractEdges } from '@sage/xtrem-client';
import type { Graph<PERSON><PERSON> } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import { confirmDialogWithAcceptButtonText } from './page-functions';

export async function resynchronizeDocument(page: ui.Page<GraphApi>, documentId: string) {
    const resyncAccess = await page.$.graph
        .node('@sage/xtrem-system/Company')
        .queries.isAccessibleForCurrentUser(true, {
            nodeName: 'BaseDocument',
            propertyOrOperation: 'bulkResync',
        })
        .execute();

    if (resyncAccess === false) {
        return;
    }

    if (
        !(await confirmDialogWithAcceptButtonText(
            page,
            ui.localize(
                '@sage/xtrem-master-data/client_functions__master_data__resync_status_title',
                'Check and update status.',
            ),
            ui.localize(
                '@sage/xtrem-master-data/client_functions__master_data__resync_status_message',
                'You are about to update the status.',
            ),
            ui.localize('@sage/xtrem-master-data/client_functions__master_data__resync_status_continue', 'Continue'),
        ))
    ) {
        return;
    }
    page.$.loader.isHidden = false;
    const track = await page.$.graph
        .node('@sage/xtrem-master-data/BaseDocument')
        .asyncOperations.bulkResync.start(
            {
                trackingId: true,
            },
            {
                filter: `{_id: ${documentId}}`,
            },
        )
        .execute();
    const batchTask = extractEdges<Partial<ui.ClientNode>>(
        await page.$.graph
            .node('@sage/xtrem-communication/SysNotificationState')
            .query(ui.queryUtils.edgesSelector({ _id: true }, { filter: { notificationId: track.trackingId } }))
            .execute(),
    );

    page.$.loader.isHidden = true;

    page.$.showToast(
        ui.localize(
            '@sage/xtrem-master-data/client_functions__master_data_resync_submitted',
            'Resync document request submitted:({{batchTaskId}}).',
            { batchTaskId: batchTask[0]._id },
        ),
        { type: 'info' },
    );
    await page.$.router.refresh();
}
