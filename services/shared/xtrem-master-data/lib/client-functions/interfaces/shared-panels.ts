import type { Item, ItemSite } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';

export interface WorkOrderPanelParameters {
    item: {
        name: string;
        item: Item;
        site: ItemSite | Site;
    };
    site: Site;
    quantity: string;
    startDate: string;
    endDate: string;
    isFromSalesOrder?: boolean;
    storedDimensions?: string;
    storedAttributes?: string;
}
