import type { Filter } from '@sage/xtrem-client';
import type { AddressEntityType, BusinessEntity, SequenceNumberValue } from '@sage/xtrem-master-data-api';
import type * as ui from '@sage/xtrem-ui';

export interface OpenItemSupplierPriceListParameters {
    itemId: string;
    supplierId: string;
    siteId: string;
    itemName: string;
    orderDate: string;
}

export interface ItemSiteEdge {
    allocatedQuantity: boolean;
    acceptedStockQuantity: boolean;
    inStockQuantity: boolean;
}

export interface ItemSiteFilter {
    item: { _id: string };
    site: { _id: string };
}

export interface QueryItemSiteResult {
    acceptedStockQuantity: number;
    allocatedQuantity: number;
    inStockQuantity: number;
}

export interface ValidateBePropertiesParameters {
    page: ui.Page;
    field: 'name' | 'id' | 'taxIdNumber';
    id: string | null;
    idMessage: string;
    nameMessage: string;
    taxIdMessage: string;
    dialogMessage: string;
}

export interface CheckExistingBeParameters {
    page: ui.Page;
    filter: Filter<BusinessEntity>;
    field: 'name' | 'id' | 'taxIdNumber';
    id: string | null;
    name: string | null;
    addressEntityType: AddressEntityType;
}

export interface SequenceNumberValues {
    sysId: number;
    sequenceValue: number;
}

export interface SequenceNumberValuesInput extends SequenceNumberValue {
    newNextValue: number;
}
