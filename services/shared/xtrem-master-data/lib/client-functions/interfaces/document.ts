import type { ClientNode } from '@sage/xtrem-client';
import type { User } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

export interface Document extends ClientNode {
    number: string;
}

export interface UserApprover extends ui.PartialNodeWithId<User> {
    type: string;
    sortOrder: number;
}

export interface FilteredUsers {
    _id: string;
    email: string;
    firstName: string;
    lastName: string;
}
