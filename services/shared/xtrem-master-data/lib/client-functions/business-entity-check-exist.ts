import type { Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type { BusinessEntity as BusinessEntityNode } from '@sage/xtrem-master-data-api';
import * as ui from '@sage/xtrem-ui';
import type { AllBePage, CustomerSupplierSitePage } from './business-entity';
import { BusinessEntityPage, CustomerPage, SitePage, SupplierPage } from './business-entity';
import { confirmDialogWithAcceptButtonText } from './page-functions';

type BeFields = 'name' | 'id' | 'taxIdNumber';

const dialogTitle = ui.localize(
    '@sage/xtrem-master-data/found-matching-business-entities',
    'Matching business entities found',
);

/**
 * Function to open the wizard dialog to create a new site/customer/supplier
 * from an existing BE
 * @param page
 * @param wizardPage
 * @param businessEntitySysId
 * @returns
 */
export function openBEWizard(page: CustomerSupplierSitePage, wizardPage: string, businessEntitySysId?: string) {
    return page.$.dialog.page(wizardPage, ...[businessEntitySysId ? { _id: businessEntitySysId } : {}], {
        resolveOnCancel: true,
        height: 500,
        size: 'extra-large',
    });
}

/** *
 * Function to prompt user about using the existing BE found
 * If user selects to use the found BE, update the values on the page.
 * The calling page has to have a populateBusinessEntityValues function to load the BE values
 * @param page BusinessEntityPages
 * @param businessEntity    BusinessEntity to use
 */
export async function useExistingBE(
    page: AllBePage,
    businessEntity: Awaited<ReturnType<typeof getBusinessEntity>>[number],
) {
    const doWeUseExistingBE = await page.$.dialog
        .confirmation(
            'warn',
            dialogTitle,
            ui.localize(
                '@sage/xtrem-master-data/use-existing-business-entity',
                'An existing business entity found with this details:  Name: {{beName}}, ID: {{beId}}, Tax ID: {{beTaxId}}. Would you like to use this business entity?',
                { beName: businessEntity.name, beId: businessEntity.id, beTaxId: businessEntity.taxIdNumber },
            ),
            { acceptButton: { text: 'Yes' }, cancelButton: { text: 'No' } },
        )
        .then(() => true)
        .catch(() => false);
    if (!doWeUseExistingBE) {
        return null;
    }
    if (page instanceof BusinessEntityPage) {
        return businessEntity;
    }
    if (page instanceof CustomerPage) {
        return openBEWizard(page, '@sage/xtrem-master-data/BusinessEntitySelectionForCustomer', businessEntity._id);
    }
    if (page instanceof SupplierPage) {
        return openBEWizard(page, '@sage/xtrem-master-data/BusinessEntitySelectionForSupplier', businessEntity._id);
    }
    if (page instanceof SitePage) {
        return openBEWizard(page, '@sage/xtrem-master-data/BusinessEntitySelectionForSite', businessEntity._id);
    }
    throw new Error('Invalid page');
}

function controlCustomer(field: BeFields) {
    switch (field) {
        case 'id':
            return ui.localize(
                '@sage/xtrem-master-data/pages__customer__already_exists_with_same_id',
                'A customer already exists with the same ID.',
            );

        case 'name':
            return ui.localize(
                '@sage/xtrem-master-data/pages__customer__already_exists_with_same_name',
                'A customer already exists with the same name.',
            );

        case 'taxIdNumber':
            return ui.localize(
                '@sage/xtrem-master-data/pages__customer__already_exists_with_same_taxIdNumber',
                'A customer already exists with the same tax identification number.',
            );
        default:
            return '';
    }
}

function controlSupplier(field: BeFields) {
    switch (field) {
        case 'id':
            return ui.localize(
                '@sage/xtrem-master-data/pages__supplier__already_exists_with_same_id',
                'A supplier already exists with the same ID.',
            );
        case 'name':
            return ui.localize(
                '@sage/xtrem-master-data/pages__supplier__already_exists_with_same_name',
                'A supplier already exists with the same name.',
            );
        case 'taxIdNumber':
            return ui.localize(
                '@sage/xtrem-master-data/pages__supplier__already_exists_with_same_taxIdNumber',
                'A supplier already exists with the same tax identification number.',
            );
        default:
            return '';
    }
}

function controlSite(field: BeFields) {
    switch (field) {
        case 'id':
            return ui.localize(
                '@sage/xtrem-master-data/pages__site__already_exists_with_same_id',
                'A site already exists with the same ID.',
            );
        case 'name':
            return ui.localize(
                '@sage/xtrem-master-data/pages__site__already_exists_with_same_name',
                'A site already exists with the same name.',
            );
        case 'taxIdNumber':
            return ui.localize(
                '@sage/xtrem-master-data/pages__site__already_exists_with_same_taxIdNumber',
                'A site already exists with the same tax identification number.',
            );
        default:
            return '';
    }
}

function controlExistingBE(
    page: AllBePage,
    change: { businessEntity: Awaited<ReturnType<typeof getBusinessEntity>>[number]; field: BeFields },
) {
    if (page instanceof CustomerPage && change.businessEntity.isCustomer) {
        return controlCustomer(change.field);
    }
    if (page instanceof SupplierPage && change.businessEntity.isSupplier) {
        return controlSupplier(change.field);
    }
    if (page instanceof SitePage && change.businessEntity.isSite) {
        return controlSite(change.field);
    }
    return '';
}

export async function getBusinessEntity(page: AllBePage, filter: Filter<BusinessEntityNode>) {
    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-master-data/BusinessEntity')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        _id: true,
                        id: true,
                        name: true,
                        isActive: true,
                        taxIdNumber: true,
                        siret: true,
                        country: { _id: true, id: true, name: true, regionLabel: true, zipLabel: true },
                        currency: { _id: true, id: true, name: true, decimalDigits: true, symbol: true },
                        isCustomer: true,
                        isSupplier: true,
                        isSite: true,
                        legalEntity: true,
                        image: { value: true },
                    },
                    { filter },
                ),
            )
            .execute(),
    );
}

function emptyBeField(page: AllBePage, field: BeFields) {
    switch (field) {
        case 'name':
            page.name.value = '';
            break;
        case 'id':
            page.id.value = '';
            break;
        case 'taxIdNumber':
            page.taxIdNumber.value = '';
            break;
        default:
            break;
    }
}

async function doesUserWantToUseExistingBE(
    page: AllBePage,
    change: { businessEntity: Awaited<ReturnType<typeof getBusinessEntity>>[number]; field: BeFields },
) {
    const be = await useExistingBE(page, change.businessEntity);
    if (be && Object.keys(be).length > 0) {
        if (page instanceof BusinessEntityPage) {
            await page.$.router.selectRecord(be._id, true);
        }
        return be;
    }
    emptyBeField(page, change.field);

    return null;
}

/** *
 * Function to check for existing BusinessEntity based on the filter passed in
 * If 1 matching BusinessEntity found, prompt user via useExistingBE function
 * If more than 1 match found, warn user to select BE via reference field on page
 * @param page BusinessEntityPages
 * @param filter    Filter for BE lookup (should be case insensitive). eg. { name: { _regex: `^${this.name.value}$`, _options: 'i' } }
 */
export async function checkExistingBE(check: { page: AllBePage; filter: Filter<BusinessEntityNode>; field: BeFields }) {
    const businessEntityList = await getBusinessEntity(check.page, check.filter);

    if (businessEntityList.length === 1) {
        const businessEntity = businessEntityList[0];
        const message = controlExistingBE(check.page, { businessEntity, field: check.field });

        if (message) {
            await check.page.$.dialog.message('warn', dialogTitle, message);
            emptyBeField(check.page, check.field);
            return null;
        }
        return doesUserWantToUseExistingBE(check.page, { businessEntity, field: check.field });
    }

    if (businessEntityList.length > 1) {
        await check.page.$.dialog.message(
            'warn',
            dialogTitle,
            ui.localize(
                '@sage/xtrem-master-data/multiple-existing-business-entities',
                'Multiple matching business entities found. Please use the Business entity field to select the appropriate existing business entity.',
            ),
        );
    }

    return null;
}

function getBeFilter(page: AllBePage, field: BeFields) {
    switch (field) {
        case 'name':
            return { name: { _regex: `^${page.name.value}$`, _options: 'i' } };
        case 'id':
            return { id: { _regex: `^${page.id.value}$`, _options: 'i' } };
        case 'taxIdNumber':
            return { taxIdNumber: { _regex: `^${page.taxIdNumber.value}$`, _options: 'i' } };
        default:
            throw new Error('Invalid field');
    }
}

export function checkExistingBEForName(page: AllBePage) {
    const field: BeFields = 'name';
    return checkExistingBE({ page, filter: getBeFilter(page, field), field });
}

export function checkExistingBEForId(page: AllBePage) {
    const field: BeFields = 'id';
    return checkExistingBE({ page, filter: getBeFilter(page, field), field });
}

export function checkExistingBEForTaxIdNumber(page: AllBePage) {
    const field: BeFields = 'taxIdNumber';
    return checkExistingBE({ page, filter: getBeFilter(page, field), field });
}

export async function onChangeCategory(page: CustomerPage | SupplierPage) {
    if (page.id.value && !page.$.recordId && page.category.value?.sequenceNumber) {
        if (
            await confirmDialogWithAcceptButtonText(
                page,
                ui.localize(
                    '@sage/xtrem-master-data/pages__customer-supplier__select_id_number_title',
                    'Select ID number',
                ),
                ui.localize(
                    '@sage/xtrem-master-data/pages__customer-supplier__category_dialog_content',
                    'The category you selected is linked to a sequence number. Do you want to generate a new ID, or keep the current one?',
                ),
                ui.localize('@sage/xtrem-master-data/pages__customer-supplier__generate_ID', 'Generate ID'),
                ui.localize('@sage/xtrem-master-data/pages__customer-supplier__keep_current_id-', 'Keep current ID'),
            )
        ) {
            page.id.value = '';
            page.id.isDisabled = true;
        } else page.id.isMandatory = true;
    } else if (!page.businessEntitySysId.value && !page.category.value?.sequenceNumber) {
        page.id.isDisabled = false;
        page.id.isMandatory = true;
    } else {
        page.id.isDisabled = true;
        page.id.isMandatory = false;
    }
}
