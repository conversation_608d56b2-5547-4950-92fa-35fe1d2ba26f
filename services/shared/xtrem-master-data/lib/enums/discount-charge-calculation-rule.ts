import { EnumDataType } from '@sage/xtrem-core';

export enum DiscountChargeCalculationRuleEnum {
    byUnit = 1,
    byLine = 2,
}

export type DiscountChargeCalculationRule = keyof typeof DiscountChargeCalculationRuleEnum;

export const discountChargeCalculationRuleDataType = new EnumDataType<DiscountChargeCalculationRule>({
    enum: DiscountChargeCalculationRuleEnum,
    filename: __filename,
});
