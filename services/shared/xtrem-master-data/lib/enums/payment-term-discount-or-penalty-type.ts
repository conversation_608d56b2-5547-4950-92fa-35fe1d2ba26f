import { EnumDataType } from '@sage/xtrem-core';

export enum PaymentTermDiscountOrPenaltyTypeEnum {
    percentage = 1,
    amount = 2,
}

export type PaymentTermDiscountOrPenaltyType = keyof typeof PaymentTermDiscountOrPenaltyTypeEnum;

export const paymentTermDiscountOrPenaltyTypeDataType = new EnumDataType<PaymentTermDiscountOrPenaltyType>({
    enum: PaymentTermDiscountOrPenaltyTypeEnum,
    filename: __filename,
});
