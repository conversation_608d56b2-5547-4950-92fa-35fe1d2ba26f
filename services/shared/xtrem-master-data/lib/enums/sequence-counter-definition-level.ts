import { EnumDataType } from '@sage/xtrem-core';

export enum SequenceCounterDefinitionLevelEnum {
    application = 1,
    company = 2,
    site = 3,
}

export type SequenceCounterDefinitionLevel = keyof typeof SequenceCounterDefinitionLevelEnum;

export const sequenceCounterDefinitionLevelDataType = new EnumDataType<SequenceCounterDefinitionLevel>({
    enum: SequenceCounterDefinitionLevelEnum,
    filename: __filename,
});
