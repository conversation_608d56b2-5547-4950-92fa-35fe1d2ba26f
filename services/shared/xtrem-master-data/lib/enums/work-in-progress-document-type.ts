import { EnumDataType } from '@sage/xtrem-core';

export enum WorkInProgressDocumentTypeEnum {
    workOrder,
    materialNeed,
    purchaseOrder,
    purchaseReceipt,
    purchaseReturn,
    salesOrder,
    stockTransferOrder,
    stockTransferReceipt,
}

export type WorkInProgressDocumentType = keyof typeof WorkInProgressDocumentTypeEnum;

export const workInProgressDocumentTypeDataType = new EnumDataType<WorkInProgressDocumentType>({
    enum: WorkInProgressDocumentTypeEnum,
    filename: __filename,
});
