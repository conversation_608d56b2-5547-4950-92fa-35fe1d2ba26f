import { EnumDataType } from '@sage/xtrem-core';

export enum DiscountChargeCalculationBasisEnum {
    grossPrice = 1,
    grossPriceAndCompound = 2,
}

export type DiscountChargeCalculationBasis = keyof typeof DiscountChargeCalculationBasisEnum;

export const discountChargeCalculationBasisDataType = new EnumDataType<DiscountChargeCalculationBasis>({
    enum: DiscountChargeCalculationBasisEnum,
    filename: __filename,
});
