import { EnumDataType } from '@sage/xtrem-core';

export enum SequenceNumberResetFrequencyEnum {
    noReset = 1,
    yearly = 2,
    monthly = 3,
}

export type SequenceNumberResetFrequency = keyof typeof SequenceNumberResetFrequencyEnum;

export const sequenceNumberResetFrequencyDataType = new EnumDataType<SequenceNumberResetFrequency>({
    enum: SequenceNumberResetFrequencyEnum,
    filename: __filename,
});
