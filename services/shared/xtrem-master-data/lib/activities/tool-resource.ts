import { Activity } from '@sage/xtrem-core';
import { commonResourceActivities } from '../functions/common';
import { ToolResource } from '../nodes/tool-resource';
import * as xtremMasterData from '../index';

export const toolResource = new Activity({
    description: 'Tool resource',
    node: () => ToolResource,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => xtremMasterData.nodes.WeeklyShift,
                    () => xtremMasterData.nodes.ShiftDetail,
                    () => xtremMasterData.nodes.DailyShift,
                ],
            },
            ...commonResourceActivities,
        ],
        manage: [
            {
                operations: ['lookup'],
                on: [
                    () => xtremMasterData.nodes.WeeklyShift,
                    () => xtremMasterData.nodes.ShiftDetail,
                    () => xtremMasterData.nodes.DailyShift,
                ],
            },
            { operations: ['create', 'update', 'delete'], on: [() => ToolResource] },
            ...commonResourceActivities,
        ],
    },
});
