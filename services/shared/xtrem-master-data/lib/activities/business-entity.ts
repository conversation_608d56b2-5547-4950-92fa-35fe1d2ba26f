import * as xtremAuthorization from '@sage/xtrem-authorization';
import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import {
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    Currency,
    Customer,
    CustomerPriceReason,
    DeliveryMode,
    Incoterm,
    Item,
    ItemCustomerPrice,
    ItemSupplierPrice,
    Supplier,
    UnitOfMeasure,
} from '../nodes/_index';

const { Country } = xtremStructure.nodes;
const { Company, Site } = xtremSystem.nodes;
const { GroupRoleSite, SiteGroup } = xtremAuthorization.nodes;
const { AttachmentAssociation } = xtremUpload.nodes;
const commonOperations: OperationGrant[] = [
    {
        operations: ['lookup'],
        on: [
            () => Company,
            () => ItemCustomerPrice,
            () => Currency,
            () => Country,
            () => Site,
            () => GroupRoleSite,
            () => SiteGroup,
            () => DeliveryMode,
            () => Incoterm,
            () => Item,
            () => Supplier,
            () => Customer,
            () => ItemCustomerPrice,
            () => ItemSupplierPrice,
            () => BusinessEntityAddress,
            () => CustomerPriceReason,
            () => BusinessEntityContact,
            () => AttachmentAssociation,
        ],
    },
    {
        operations: ['lookup', 'getUnitConversionFactor', 'getPurchaseUnit', 'convertFromTo'],
        on: [() => UnitOfMeasure],
    },
    { operations: ['getDefaultSupplier'], on: [() => Supplier] },
    { operations: ['timezones'], on: [() => Site] },
];

const createOperations: OperationGrant[] = [
    ...commonOperations,
    { operations: ['update'], on: [() => BusinessEntity] },
];

export const businessEntity = new Activity({
    description: 'Business entity',
    node: () => BusinessEntity,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: commonOperations,
        create: createOperations,
        update: commonOperations,
        delete: [...commonOperations],
    },
});
