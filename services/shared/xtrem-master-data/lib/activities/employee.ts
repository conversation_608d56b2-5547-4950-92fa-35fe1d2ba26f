import { Activity } from '@sage/xtrem-core';
import { commonResourceActivities } from '../functions/common';
import { Employee } from '../nodes/employee';

export const employee = new Activity({
    description: 'Employee',
    node: () => Employee,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [...commonResourceActivities],
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => Employee] }, ...commonResourceActivities],
    },
});
