import { Activity } from '@sage/xtrem-core';
import { LocationZone } from '../nodes/location-zone';
import { UnitOfMeasure } from '../nodes/unit-of-measure';
import { Location } from '../nodes/location';

export const locationZone = new Activity({
    description: 'Location zone',
    node: () => LocationZone,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [() => Location, () => UnitOfMeasure],
            },
        ],
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => LocationZone] },
            {
                operations: ['lookup'],
                on: [() => UnitOfMeasure, () => Location],
            },
        ],
    },
});
