import { Activity } from '@sage/xtrem-core';
import { nodes as xtremStructureNodes } from '@sage/xtrem-structure';
import { nodes as xtremSystemNodes } from '@sage/xtrem-system';
import { BusinessEntity } from '../nodes/business-entity';
import { BusinessEntityAddress } from '../nodes/business-entity-address';
import { CostCategory } from '../nodes/cost-category';
import { IndirectCostSectionLine } from '../nodes/indirect-cost-section-line';
import { ItemSite } from '../nodes/item-site';
import { ItemSiteCost } from '../nodes/item-site-cost';
import { ItemSiteSupplier } from '../nodes/item-site-supplier';
import { ItemSupplier } from '../nodes/item-supplier';
import { Supplier } from '../nodes/supplier';
import { UnitOfMeasure } from '../nodes/unit-of-measure';

const { Company, Site } = xtremSystemNodes;

const { Country } = xtremStructureNodes;

export const itemSite = new Activity({
    description: 'Item site',
    node: () => ItemSite,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => ItemSiteSupplier,
                    () => ItemSupplier,
                    () => Company,
                    () => Supplier,
                    () => Site,
                    () => ItemSiteCost,
                    () => BusinessEntity,
                    () => BusinessEntityAddress,
                    () => IndirectCostSectionLine,
                    () => Country,
                ],
            },
            { operations: ['getPurchaseUnit'], on: [() => UnitOfMeasure] },
            { operations: ['getValuedItemSite'], on: [() => ItemSite] },
            { operations: ['getItemSiteCost'], on: [() => ItemSiteCost] },
        ],
        create: [
            { operations: ['create', 'getItemSiteCost'], on: [() => ItemSiteCost] },
            { operations: ['create'], on: [() => ItemSiteSupplier] },
            { operations: ['getPurchaseUnit'], on: [() => UnitOfMeasure] },
            { operations: ['create'], on: [() => CostCategory] },
        ],
        update: [
            { operations: ['update', 'getItemSiteCost'], on: [() => ItemSiteCost] },
            { operations: ['update'], on: [() => ItemSiteSupplier] },
            { operations: ['getPurchaseUnit'], on: [() => UnitOfMeasure] },
            { operations: ['update'], on: [() => CostCategory] },
        ],
        delete: [
            { operations: ['delete', 'getItemSiteCost'], on: [() => ItemSiteCost] },
            { operations: ['delete'], on: [() => ItemSiteSupplier] },
        ],
    },
});
