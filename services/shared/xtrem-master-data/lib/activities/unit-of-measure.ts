import { Activity } from '@sage/xtrem-core';
import { UnitOfMeasure } from '../nodes/unit-of-measure';

export const unitOfMeasure = new Activity({
    description: 'Unit of measure',
    node: () => UnitOfMeasure,
    __filename,
    permissions: ['read', 'update'],
    operationGrants: {
        read: [
            {
                operations: ['getPurchaseUnit', 'convertFromTo', 'getUnitConversionFactor'],
                on: [() => UnitOfMeasure],
            },
        ],
        update: [
            {
                operations: ['create', 'update', 'delete'],
                on: [() => UnitOfMeasure],
            },
        ],
    },
});
