import { Activity } from '@sage/xtrem-core';
import { commonResourceActivities } from '../functions/common';
import { MachineResource } from '../nodes/machine-resource';

export const machineResource = new Activity({
    description: 'Machine resource',
    node: () => MachineResource,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [...commonResourceActivities],
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => MachineResource] },
            ...commonResourceActivities,
        ],
    },
});
