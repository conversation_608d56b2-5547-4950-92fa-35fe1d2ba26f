import { Activity } from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import { Location } from '../nodes/location';
import { RangeSequenceComponent } from '../nodes/range-sequence-component';
import { UnitOfMeasure } from '../nodes/unit-of-measure';

export const location = new Activity({
    description: 'Location',
    node: () => Location,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            { operations: ['lookup'], on: [() => UnitOfMeasure] },
            { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
        ],
        manage: [
            {
                operations: ['create', 'update', 'delete', 'getLocations', 'createBulkLocations'],
                on: [() => Location],
            },
            {
                operations: ['lookup'],
                on: [() => UnitOfMeasure, () => RangeSequenceComponent],
            },
            { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
        ],
    },
});
