import { Activity } from '@sage/xtrem-core';
import { CustomerSupplierCategory } from '../nodes/customer-supplier-category';

export const customerSupplierCategory = new Activity({
    description: 'Customer supplier category',
    node: () => CustomerSupplierCategory,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => CustomerSupplierCategory] }],
    },
});
