import { Activity } from '@sage/xtrem-core';
import { commonResourceActivities } from '../functions/common';
import { LaborResource } from '../nodes/labor-resource';

export const labourResource = new Activity({
    description: 'Labour resource',
    node: () => LaborResource,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [...commonResourceActivities],
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => LaborResource] },
            ...commonResourceActivities,
        ],
    },
});
