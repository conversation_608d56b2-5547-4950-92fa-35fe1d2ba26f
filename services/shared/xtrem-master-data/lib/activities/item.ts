import { Activity } from '@sage/xtrem-core';
import { nodes as xtremStructureNodes } from '@sage/xtrem-structure';
import { nodes as xtremSystemNodes } from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import { BusinessEntity } from '../nodes/business-entity';
import { BusinessEntityAddress } from '../nodes/business-entity-address';
import { CostCategory } from '../nodes/cost-category';
import { IndirectCostSectionLine } from '../nodes/indirect-cost-section-line';
import { Item } from '../nodes/item';
import { ItemSite } from '../nodes/item-site';
import { ItemSiteCost } from '../nodes/item-site-cost';
import { ItemSiteSupplier } from '../nodes/item-site-supplier';
import { ItemSupplier } from '../nodes/item-supplier';
import { Supplier } from '../nodes/supplier';
import { UnitConversionFactor } from '../nodes/unit-conversion-factor';
import { UnitOfMeasure } from '../nodes/unit-of-measure';

const { Company, Site } = xtremSystemNodes;
const { Country } = xtremStructureNodes;

export const item = new Activity({
    description: 'Item',
    node: () => Item,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => ItemSiteSupplier,
                    () => ItemSupplier,
                    () => Company,
                    () => Supplier,
                    () => Site,
                    () => ItemSiteCost,
                    () => BusinessEntity,
                    () => BusinessEntityAddress,
                    () => IndirectCostSectionLine,
                    () => Country,
                    () => UnitConversionFactor,
                    () => xtremUpload.nodes.AttachmentAssociation,
                ],
            },
            { operations: ['getValuedItemSite'], on: [() => ItemSite] },
            { operations: ['getItemSiteCost'], on: [() => ItemSiteCost] },
            { operations: ['getUnitConversionFactor', 'getPurchaseUnit', 'convertFromTo'], on: [() => UnitOfMeasure] },
            { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
        ],
        create: [
            {
                operations: ['lookup'],
                on: [
                    () => ItemSiteSupplier,
                    () => ItemSupplier,
                    () => Company,
                    () => Supplier,
                    () => Site,
                    () => ItemSiteCost,
                    () => BusinessEntity,
                    () => BusinessEntityAddress,
                    () => IndirectCostSectionLine,
                    () => Country,
                    () => UnitConversionFactor,
                ],
            },
            { operations: ['getUnitConversionFactor', 'getPurchaseUnit', 'convertFromTo'], on: [() => UnitOfMeasure] },
            { operations: ['getItemSiteCost'], on: [() => ItemSiteCost] },
            { operations: ['update'], on: [() => ItemSiteSupplier, () => ItemSiteCost, () => CostCategory] },
            { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
        ],
        update: [
            {
                operations: ['lookup'],
                on: [
                    () => ItemSiteSupplier,
                    () => ItemSupplier,
                    () => Company,
                    () => Supplier,
                    () => Site,
                    () => ItemSiteCost,
                    () => BusinessEntity,
                    () => BusinessEntityAddress,
                    () => IndirectCostSectionLine,
                    () => Country,
                    () => UnitConversionFactor,
                ],
            },
            { operations: ['getUnitConversionFactor', 'getPurchaseUnit', 'convertFromTo'], on: [() => UnitOfMeasure] },
            { operations: ['getItemSiteCost'], on: [() => ItemSiteCost] },
            { operations: ['update'], on: [() => ItemSiteSupplier, () => ItemSiteCost, () => CostCategory] },
            { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
        ],
        delete: [
            {
                operations: ['lookup'],
                on: [
                    () => ItemSiteSupplier,
                    () => ItemSupplier,
                    () => Company,
                    () => Supplier,
                    () => Site,
                    () => ItemSiteCost,
                    () => BusinessEntity,
                    () => BusinessEntityAddress,
                    () => IndirectCostSectionLine,
                    () => Country,
                    () => UnitConversionFactor,
                ],
            },
            { operations: ['getItemSiteCost'], on: [() => ItemSiteCost] },
            { operations: ['delete'], on: [() => ItemSiteSupplier, () => ItemSiteCost, () => CostCategory] },
            { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
        ],
    },
});
