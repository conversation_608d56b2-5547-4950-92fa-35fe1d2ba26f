import { Activity } from '@sage/xtrem-core';
import { BomRevisionSequence } from '../nodes/bom-revision-sequence';

export const bomRevisionSequence = new Activity({
    description: 'BOM revision sequence number',
    node: () => BomRevisionSequence,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => BomRevisionSequence] }],
    },
});
