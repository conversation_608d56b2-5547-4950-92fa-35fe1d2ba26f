import { Activity } from '@sage/xtrem-core';
import { CapabilityLevel } from '../nodes/capability-level';

export const capabilityLevel = new Activity({
    description: 'Capability level',
    node: () => CapabilityLevel,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => CapabilityLevel] }],
    },
});
