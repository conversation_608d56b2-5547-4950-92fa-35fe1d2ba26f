import { Activity } from '@sage/xtrem-core';
import { CustomerPriceReason } from '../nodes/customer-price-reason';

export const customerPriceReason = new Activity({
    description: 'Customer price reason',
    node: () => CustomerPriceReason,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => CustomerPriceReason] }],
    },
});
