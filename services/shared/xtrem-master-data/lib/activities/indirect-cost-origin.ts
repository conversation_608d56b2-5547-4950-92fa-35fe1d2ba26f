import { Activity } from '@sage/xtrem-core';
import { IndirectCostOrigin } from '../nodes/indirect-cost-origin';

export const indirectCostOrigin = new Activity({
    description: 'Indirect cost origin',
    node: () => IndirectCostOrigin,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => IndirectCostOrigin] }],
    },
});
