import type { OperationGrant } from '@sage/xtrem-core';
import { Activity } from '@sage/xtrem-core';
import * as xtremUpload from '@sage/xtrem-upload';
import {
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    Customer,
    ItemCustomerPrice,
} from '../nodes/_index';

const coreEntities = [
    () => Customer,
    () => BusinessEntityAddress,
    () => BusinessEntityContact,
    () => ItemCustomerPrice,
];

const lookupGrants: OperationGrant[] = [
    {
        operations: ['lookup'],
        on: [...coreEntities, () => BusinessEntity, () => xtremUpload.nodes.AttachmentAssociation],
    },
];

const createEntityGrant = (operations: string[]) => ({
    operations,
    on: coreEntities,
});

export const customer = new Activity({
    description: 'Customer',
    node: () => Customer,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [...lookupGrants],
        create: [...lookupGrants, createEntityGrant(['create', 'update'])],
        update: [...lookupGrants, createEntityGrant(['update'])],
        delete: [...lookupGrants, createEntityGrant(['delete'])],
    },
});
