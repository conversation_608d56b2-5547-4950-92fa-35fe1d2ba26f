import { Activity } from '@sage/xtrem-core';
import { Customer } from '../nodes/customer';
import { PaymentTerm } from '../nodes/payment-term';
import { Supplier } from '../nodes/supplier';

export const paymentTerm = new Activity({
    description: 'Payment term',
    node: () => PaymentTerm,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [{ operations: ['lookup'], on: [() => Supplier, () => Customer] }],
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => PaymentTerm] },
            { operations: ['lookup'], on: [() => Supplier, () => Customer] },
        ],
    },
});
