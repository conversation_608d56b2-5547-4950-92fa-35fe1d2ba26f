import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import {
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    CustomerSupplierCategory,
    DeliveryMode,
    Incoterm,
    Item,
    ItemSiteSupplier,
    ItemSupplier,
    ItemSupplierPrice,
    PaymentTerm,
    StandardIndustrialClassification,
    Supplier,
    SupplierCertificate,
    UnitOfMeasure,
} from '../nodes/_index';

const { Site, UserPreferences } = xtremSystem.nodes;

const businessEntities = [() => BusinessEntity, () => BusinessEntityAddress, () => BusinessEntityContact];

const priceEntities = [() => ItemSupplierPrice];

const commonOperations = [
    {
        operations: ['lookup'],
        on: [
            () => Item,
            () => Site,
            () => Supplier,
            () => Incoterm,
            () => PaymentTerm,
            () => DeliveryMode,
            () => ItemSupplier,
            ...businessEntities,
            () => UserPreferences,
            () => ItemSiteSupplier,
            ...priceEntities,
            () => SupplierCertificate,
            () => CustomerSupplierCategory,
            () => StandardIndustrialClassification,
            () => xtremUpload.nodes.AttachmentAssociation,
        ],
    },
    { operations: ['read'], on: businessEntities },
    {
        operations: ['lookup', 'getUnitConversionFactor', 'getPurchaseUnit', 'convertFromTo'],
        on: [() => UnitOfMeasure],
    },
    { operations: ['lookup'], on: [() => xtremUpload.nodes.AttachmentAssociation] },
];

export const supplier = new Activity({
    description: 'Supplier',
    node: () => Supplier,
    __filename,
    permissions: ['read', 'create', 'update', 'delete'],
    operationGrants: {
        read: [...commonOperations],
        create: [
            ...commonOperations,
            { operations: ['update'], on: [() => Supplier] },
            { operations: ['create', 'update'], on: [...businessEntities, ...priceEntities] },
        ],
        update: [...commonOperations, { operations: ['update'], on: [...businessEntities, ...priceEntities] }],
        delete: [...commonOperations, { operations: ['delete'], on: [...businessEntities, ...priceEntities] }],
    },
});
