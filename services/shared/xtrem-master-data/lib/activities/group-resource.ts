import { Activity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremMasterData from '../index';

export const groupResource = new Activity({
    description: 'Group resource',
    node: () => xtremMasterData.nodes.GroupResource,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        read: [
            {
                operations: ['lookup'],
                on: [
                    () => xtremMasterData.nodes.WeeklyShift,
                    () => xtremMasterData.nodes.ShiftDetail,
                    () => xtremMasterData.nodes.DailyShift,
                    () => xtremMasterData.nodes.Currency,
                    () => xtremSystem.nodes.Site,
                    () => xtremMasterData.nodes.BusinessEntity,
                    () => xtremSystem.nodes.Company,
                ],
            },
        ],
        manage: [
            {
                operations: ['lookup'],
                on: [
                    () => xtremMasterData.nodes.WeeklyShift,
                    () => xtremMasterData.nodes.ShiftDetail,
                    () => xtremMasterData.nodes.DailyShift,
                    () => xtremMasterData.nodes.Currency,
                    () => xtremMasterData.nodes.BusinessEntity,
                    () => xtremSystem.nodes.Site,
                    () => xtremSystem.nodes.Company,
                ],
            },
            { operations: ['create', 'update', 'delete'], on: [() => xtremMasterData.nodes.GroupResource] },
            {
                operations: ['lookup', 'create', 'update'],
                on: [
                    () => xtremMasterData.nodes.MachineResource,
                    () => xtremMasterData.nodes.ToolResource,
                    () => xtremMasterData.nodes.LaborResource,
                ],
            },
        ],
    },
});
