import { Activity } from '@sage/xtrem-core';
import { SupplierCertificate } from '../nodes/supplier-certificate';

export const supplierCertificate = new Activity({
    description: 'Supplier certificate',
    node: () => SupplierCertificate,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete', 'renewCertificate'], on: [() => SupplierCertificate] }],
    },
});
