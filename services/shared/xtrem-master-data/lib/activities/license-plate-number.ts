import { Activity } from '@sage/xtrem-core';
import { LicensePlateNumber } from '../nodes/license-plate-number';

export const licensePlateNumber = new Activity({
    description: 'License plate number',
    node: () => LicensePlateNumber,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            {
                operations: ['create', 'update', 'delete', 'createBulkLicensePlateNumbers'],
                on: [() => LicensePlateNumber],
            },
        ],
    },
});
