import { Activity } from '@sage/xtrem-core';
import { BaseSequenceNumber } from '../nodes/base-sequence-number';
import { SequenceNumber } from '../nodes/sequence-number';
import { SequenceNumberValue } from '../nodes/sequence-number-value';

export const sequenceNumber = new Activity({
    description: 'Sequence number',
    node: () => SequenceNumber,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => SequenceNumber] },
            { operations: ['getDocumentNodeNames'], on: [() => BaseSequenceNumber] },
            { operations: ['lookup', 'update', 'create'], on: [() => SequenceNumberValue] },
        ],
    },
});
