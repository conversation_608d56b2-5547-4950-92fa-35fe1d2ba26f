import { Activity } from '@sage/xtrem-core';
import { CostCategory } from '../nodes/cost-category';

export const costCategory = new Activity({
    description: 'Cost category',
    node: () => CostCategory,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => CostCategory] }],
    },
});
// TODO - Contact base node- which node needs access and in which package if not in the same package
