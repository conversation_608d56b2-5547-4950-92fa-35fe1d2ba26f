import { Activity } from '@sage/xtrem-core';
import { Container } from '../nodes/container';

export const container = new Activity({
    description: 'Container',
    node: () => Container,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [{ operations: ['create', 'update', 'delete'], on: [() => Container] }],
    },
});
// TODO - Contact base node- which node needs access and in which package if not in the same package
