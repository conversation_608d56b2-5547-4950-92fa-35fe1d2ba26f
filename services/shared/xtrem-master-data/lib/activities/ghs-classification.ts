import { Activity } from '@sage/xtrem-core';
import { GhsClassification } from '../nodes/ghs-classification';

export const ghsClassification = new Activity({
    description: 'GHS classification',
    node: () => GhsClassification,
    __filename,
    permissions: ['read'],
    operationGrants: {
        read: [{ operations: ['read', 'create', 'update', 'delete'], on: [() => GhsClassification] }],
    },
});
