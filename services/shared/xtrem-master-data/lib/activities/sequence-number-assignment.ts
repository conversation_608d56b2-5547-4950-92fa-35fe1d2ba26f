import { Activity } from '@sage/xtrem-core';
import { SequenceNumberAssignment } from '../nodes/sequence-number-assignment';
import { SequenceNumberAssignmentDocumentType } from '../nodes/sequence-number-assignment-document-type';
import { SequenceNumberAssignmentModule } from '../nodes/sequence-number-assignment-module';

export const sequenceNumberAssignment = new Activity({
    description: 'Sequence number assignment',
    node: () => SequenceNumberAssignment,
    __filename,
    permissions: ['read', 'manage'],
    operationGrants: {
        manage: [
            { operations: ['create', 'update', 'delete'], on: [() => SequenceNumberAssignment] },
            { operations: ['lookup'], on: [() => SequenceNumberAssignmentDocumentType] },
            { operations: ['lookup'], on: [() => SequenceNumberAssignmentModule] },
        ],
    },
});
