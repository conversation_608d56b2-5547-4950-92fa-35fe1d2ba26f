import type { OperationGrant } from '@sage/xtrem-core';
import { ActivityExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremUpload from '@sage/xtrem-upload';
import {
    Address,
    BusinessEntity,
    BusinessEntityAddress,
    BusinessEntityContact,
    CompanyAddress,
    CompanyContact,
    Contact,
    Currency,
} from '../nodes/_index';

const { Legislation, Country } = xtremStructure.nodes;
const { UserPreferences, Company, Site } = xtremSystem.nodes;
const { AttachmentAssociation } = xtremUpload.nodes;
const commonOperations: OperationGrant[] = [
    {
        operations: ['lookup'],
        on: [
            () => BusinessEntity,
            () => Contact,
            () => Address,
            () => Legislation,
            () => Country,
            () => Currency,
            () => Site,
            () => AttachmentAssociation,
            () => UserPreferences,
            () => Company,
        ],
    },
    {
        operations: ['read'],
        on: [() => BusinessEntityAddress, () => BusinessEntityContact, () => CompanyContact, () => CompanyAddress],
    },
    { operations: ['isAccessibleForCurrentUser'], on: [() => Company] },
];

const createOperations: OperationGrant[] = [...commonOperations, { operations: ['update'], on: [() => Company] }];

export const companyExtension = new ActivityExtension({
    extends: xtremSystem.activities.company,
    __filename,
    permissions: [],
    operationGrants: {
        read: commonOperations,
        create: createOperations,
        update: commonOperations,
        delete: commonOperations,
    },
});
