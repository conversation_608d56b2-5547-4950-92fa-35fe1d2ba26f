import type { decimal, DecimalDataTypeOptions, ValidationContext } from '@sage/xtrem-core';
import { DecimalDataType, RoundingMode } from '@sage/xtrem-core';

export interface PercentageDataTypeOptions extends DecimalDataTypeOptions {
    maxValue?: decimal;
    minValue?: decimal;
    defaultPercentage?: decimal;
}

export class PercentageDataType<T = unknown, ValT extends decimal | null = decimal> extends DecimalDataType<T, ValT> {
    static override defaultRoundingMode = RoundingMode.roundHalfUp;

    static override defaultPrecision = 4;

    static override defaultScale = 2;

    private maxValue: decimal | null;

    private minValue: decimal | null;

    private defaultPercentage: decimal | null;

    constructor(protected override options: PercentageDataTypeOptions) {
        super(options);
        // Use ?? rather than || to avoid clobbering 0 with null.
        this.maxValue = options.maxValue ?? null;
        this.minValue = options.minValue ?? null;
        this.defaultPercentage = options.defaultPercentage ?? null;
    }

    override get roundingMode() {
        return this.options.roundingMode ?? PercentageDataType.defaultRoundingMode;
    }

    override get precision() {
        return this.options.precision ?? PercentageDataType.defaultPrecision;
    }

    override get scale() {
        // if we have a precision but no scale, assume that scale is 0
        return this.options.scale ?? (this.options.precision === undefined ? PercentageDataType.defaultScale : 0);
    }

    public override defaultValue() {
        return (this.defaultPercentage ?? null) as ValT;
    }

    public override async controlValue(node: T, cx: ValidationContext, val?: decimal): Promise<void> {
        await super.controlValue(node, cx, val);

        if (val == null) return;

        if (this.minValue != null && val < this.minValue && this.maxValue == null) {
            cx.error.addLocalized(
                '@sage/xtrem-master-data/data-types/percentage__value_less_than_a_minimum',
                'Percentage value ({{value}}) must not be less than {{minValue}}.',
                {
                    value: val,
                    minValue: this.minValue,
                },
            );
        } else if (this.maxValue != null && val > this.maxValue && this.minValue == null) {
            cx.error.addLocalized(
                '@sage/xtrem-master-data/data-types/percentage__value_greater_than_a_maximum',
                'Percentage value ({{value}}) must not be greater than {{maxValue}}.',
                {
                    value: val,
                    maxValue: this.maxValue,
                },
            );
        } else if ((this.maxValue != null && val > this.maxValue) || (this.minValue != null && val < this.minValue)) {
            cx.error.addLocalized(
                '@sage/xtrem-master-data/data-types/percentage__value_not_in_allowed_range',
                'Percentage value ({{value}}) must be between {{minValue}} and {{maxValue}}.',
                {
                    value: val,
                    minValue: this.minValue,
                    maxValue: this.maxValue,
                },
            );
        }
    }
}

export const scrapFactorPercentage = new PercentageDataType({
    roundingMode: RoundingMode.roundUp,
    minValue: 0,
    maxValue: 99.99,
    defaultPercentage: 0,
});

export const efficiencyPercentage = new PercentageDataType({
    precision: 5,
    scale: 2,
    minValue: 0.01,
    maxValue: 100,
    defaultPercentage: 100,
});

export const potencyPercentage = new PercentageDataType({
    precision: 5,
    scale: 2,
    minValue: 0.01,
    maxValue: 100,
    defaultPercentage: 100,
});

export const indirectCostPercentage = new PercentageDataType({
    precision: 7,
    scale: 4,
    minValue: 0.0001,
});

export const percentageWorkOrderDataType = new PercentageDataType({
    precision: 10,
    scale: 4,
});

export const capacityPercentage = new PercentageDataType({
    precision: 7,
    scale: 2,
});

export const volumePercentage = new PercentageDataType({
    precision: 7,
    scale: 2,
});

export const weightPercentage = new PercentageDataType({
    precision: 7,
    scale: 2,
});

export const stockQuantityVariancePercentage = new PercentageDataType({
    precision: 10,
    scale: 4,
});

export const pricePercentage = new PercentageDataType({
    precision: 6,
    scale: 3,
    minValue: 0.0,
    maxValue: 100.0,
});

export const percentage = new PercentageDataType({});
