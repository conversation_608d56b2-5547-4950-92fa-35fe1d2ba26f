import type { decimal, DecimalDataTypeOptions, Node, PropertyName } from '@sage/xtrem-core';
import { DecimalDataType, RoundingMode } from '@sage/xtrem-core';
import type { Currency } from '../nodes/_index';

export interface ValueDataTypeOptions extends DecimalDataTypeOptions {
    currencyPropertyName?: string;
}

export class ValueDataType<T extends Node = Node> extends DecimalDataType<T> {
    static override defaultRoundingMode = RoundingMode.roundHalfUp;

    static override defaultPrecision = 20;

    static override defaultScale = 5;

    public currencyPropertyName: PropertyName<T>;

    constructor(options: ValueDataTypeOptions) {
        super(options);
        if (options.currencyPropertyName) {
            this.currencyPropertyName = options.currencyPropertyName as PropertyName<T>;
        }
    }

    override get roundingMode() {
        return this.options.roundingMode ?? ValueDataType.defaultRoundingMode;
    }

    override get precision() {
        return this.options.precision ?? ValueDataType.defaultPrecision;
    }

    override get scale() {
        // if we have a precision but no scale, assume that scale is 0
        return this.options.scale ?? (this.options.precision === undefined ? ValueDataType.defaultScale : 0);
    }

    public override async adaptValue(node: T, val: decimal): Promise<decimal> {
        let formatted = DecimalDataType.toDecimalPlaces(val, this.scale, this.roundingMode);
        if (this.currencyPropertyName) {
            const currency = (await node.$.getValue(this.currencyPropertyName)) as Currency;
            if (currency) {
                formatted = DecimalDataType.toDecimalPlaces(val, await currency.decimalDigits, this.roundingMode);
            }
        }
        return formatted;
    }
}

export const costDataType = new ValueDataType({ precision: 14, scale: 4 });
export const orderCostDataType = new ValueDataType({ precision: 14, scale: 6 });
/** depends on currency */
export const priceDataType = new ValueDataType({ currencyPropertyName: 'currency' });
/** depends on currency */
export const amountDataType = new ValueDataType({ currencyPropertyName: 'currency' });
export const timeDataType = new DecimalDataType({ precision: 10, scale: 4 });
export const resourceCost = new ValueDataType({ precision: 10, scale: 2 });
/** depends on currency */
export const stockVariationValue = new ValueDataType({ currencyPropertyName: 'currency' });
export const costValueDataType = new ValueDataType({});

/**
 * The following data type is used by the accounting engine when a company amount in company currency is needed.
 * The node must have a property called 'companyCurrency'.
 * When setting the property value, the amount will be rounded, using the decimal digits of the company currency.
 */
export const amountInCompanyCurrency = new ValueDataType({
    precision: 18,
    scale: 3,
    currencyPropertyName: 'companyCurrency',
});

/**
 * The following data type is used by the accounting engine when a financial site amount in financial site currency
 * is needed.
 * The node must have a property called 'financialSiteCurrency'.
 * When setting the property value, the amount will be rounded, using the decimal digits of the financial site currency.
 */
export const amountInFinancialSiteCurrency = new ValueDataType({
    precision: 18,
    scale: 3,
    currencyPropertyName: 'financialSiteCurrency',
});

/**
 * The following data type is used by the accounting engine when a transaction amount in transaction currency is needed.
 * The node must have a property called 'transactionCurrency'.
 * When setting the property value, the amount will be rounded, using the decimal digits of the transaction currency.
 */
export const amountInTransactionCurrency = new ValueDataType({
    precision: 18,
    scale: 3,
    currencyPropertyName: 'transactionCurrency',
});
