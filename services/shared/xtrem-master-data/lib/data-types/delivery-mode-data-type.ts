import { ReferenceDataType } from '@sage/xtrem-core';
import { DeliveryMode } from '../nodes/delivery-mode';

export const deliveryMode = new ReferenceDataType({
    reference: () => DeliveryMode,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        tunnelPage: '@sage/xtrem-master-data/DeliveryMode',
        columnPaths: ['id', 'name', 'description'],
    },
});
