import type { Node, PropertyName } from '@sage/xtrem-core';
import { DecimalDataType } from '@sage/xtrem-core';
import { QuantityDataType } from './quantity-data-type';

export const durationDataType = new DecimalDataType({ precision: 10, scale: 4 });

export const setupTimeDataType = new QuantityDataType({
    unitPropertyName: 'setupTimeUnit' as PropertyName<Node>,
});

export const runTimeDataType = new QuantityDataType({
    unitPropertyName: 'runTimeUnit' as PropertyName<Node>,
});
