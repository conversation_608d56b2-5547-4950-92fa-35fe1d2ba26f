import { StringDataType } from '@sage/xtrem-core';

export const addressLineDataType = new StringDataType({ maxLength: 150 });
export const cityDataType = new StringDataType({ maxLength: 100 });
export const regionDataType = new StringDataType({ maxLength: 35 });
export const postcodeDataType = new StringDataType({ maxLength: 10 });
export const telephoneNumberDataType = new StringDataType({ maxLength: 20 });
