import { ReferenceDataType } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

export const masterDataSite = new ReferenceDataType({
    reference: () => xtremSystem.nodes.Site,

    lookup: {
        valuePath: 'businessEntity.name',
        helperTextPath: 'businessEntity.id',
        columnPaths: ['businessEntity.name', 'businessEntity.id', 'description', 'businessEntity.taxIdNumber'],
        tunnelPage: '@sage/xtrem-master-data/Site',
    },
});
