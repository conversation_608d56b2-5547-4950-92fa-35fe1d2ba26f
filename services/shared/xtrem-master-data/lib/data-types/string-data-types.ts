import { StringDataType } from '@sage/xtrem-core';

export const documentNumber = new StringDataType({ maxLength: 30 });
export const mediumString = new StringDataType({ maxLength: 100 });
export const largeString = new StringDataType({ maxLength: 250 });
export const extraLargeString = new StringDataType({ maxLength: 1000, isLocalized: true });
export const timeZone = new StringDataType({ maxLength: 128 });
