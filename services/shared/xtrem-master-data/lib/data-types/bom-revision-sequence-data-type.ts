import { ReferenceDataType } from '@sage/xtrem-core';
import { BomRevisionSequence } from '../nodes/bom-revision-sequence';

export const bomRevisionSequence = new ReferenceDataType({
    reference: () => BomRevisionSequence,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id'],
        tunnelPage: '@sage/xtrem-master-data/BomRevisionSequence',
    },
});
