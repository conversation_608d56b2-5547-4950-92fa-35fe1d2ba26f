import type { decimal, DecimalDataTypeOptions, Node, PropertyName } from '@sage/xtrem-core';
import { DecimalDataType, RoundingMode } from '@sage/xtrem-core';
import type { UnitOfMeasure } from '../nodes/_index';

export interface QuantityDataTypeOptions<T extends Node> extends DecimalDataTypeOptions {
    unitPropertyName?: PropertyName<T>;
}

/**
 * Generic quantity data type.
 * Uses a default precision of 20, default scale of 5 and default rounding mode set to roundHalfUp.
 * It supports a property name for a unit of measure. In that case, it will use as scale the number of digits of that unit of measure.
 *
 */
export class QuantityDataType<T extends Node = Node> extends DecimalDataType<T> {
    static override defaultRoundingMode = RoundingMode.roundHalfUp;

    static override defaultPrecision = 20;

    static override defaultScale = 5;

    public unitPropertyName: PropertyName<T>;

    constructor(options: QuantityDataTypeOptions<T>) {
        super(options);
        if (options.unitPropertyName) {
            this.unitPropertyName = options.unitPropertyName;
        }
    }

    override get roundingMode() {
        return this.options.roundingMode ?? QuantityDataType.defaultRoundingMode;
    }

    override get precision() {
        return this.options.precision ?? QuantityDataType.defaultPrecision;
    }

    override get scale() {
        // if we have a precision but no scale, assume that scale is 0
        return this.options.scale ?? (this.options.precision === undefined ? QuantityDataType.defaultScale : 0);
    }

    public override async adaptValue(node: T, val: decimal): Promise<decimal> {
        let formatted = DecimalDataType.toDecimalPlaces(val, this.scale, this.roundingMode);
        if (this.unitPropertyName) {
            const unit = (await node.$.getValue(this.unitPropertyName)) as UnitOfMeasure;
            if (unit) {
                formatted = DecimalDataType.toDecimalPlaces(formatted, await unit.decimalDigits, this.roundingMode);
            }
        }
        return formatted;
    }
}

/**
 * Generic quantity data type to be used when referring to a stock quantity, without scaling to a particular unit of measure.
 */
export const stockQuantity = new QuantityDataType({});

/**
 * Quantity data type to be used when referring to a quantity in purchase unit.
 * Node must have a property called 'purchaseUnit'.
 * When setting the property value the quantity will be rounded, using the decimal digits of the purchase unit.
 */
export const quantityInPurchaseUnit = new QuantityDataType({
    unitPropertyName: 'purchaseUnit' as PropertyName<Node>,
});

/**
 * Quantity data type to be used when referring to a quantity in sales unit.
 * Node must have a property called 'salesUnit'.
 * When setting the property value the quantity will be rounded, using the decimal digits of the sales unit.
 */
export const quantityInSalesUnit = new QuantityDataType({
    unitPropertyName: 'salesUnit' as PropertyName<Node>,
});

/**
 * Quantity data type to be used when referring to a quantity in stock unit.
 * Node must have a property called 'stockUnit'.
 * When setting the property value the quantity will be rounded, using the decimal digits of the stock unit.
 */
export const quantityInStockUnit = new QuantityDataType({
    unitPropertyName: 'stockUnit' as PropertyName<Node>,
});

/**
 * Quantity data type to be used when referring to a quantity in weight unit.
 * Node must have a property called 'weightUnit'.
 * When setting the property value the quantity will be rounded, using the decimal digits of the weight unit.
 */
export const quantityInWeightUnit = new QuantityDataType({
    unitPropertyName: 'weightUnit' as PropertyName<Node>,
});

/**
 * Quantity data type to be used when referring to a quantity in volume unit.
 * Node must have a property called 'volumeUnit'.
 * When setting the property value the quantity will be rounded, using the decimal digits of the volume unit.
 */
export const quantityInVolumeUnit = new QuantityDataType({
    unitPropertyName: 'volumeUnit' as PropertyName<Node>,
});

/**
 * Quantity data type to be used when referring to a quantity in a given unit.
 * Node must have a property called 'unit'.
 * When setting the property value the quantity will be rounded, using the decimal digits of the unit.
 */
export const quantityInUnit = new QuantityDataType({
    unitPropertyName: 'unit' as PropertyName<Node>,
});
