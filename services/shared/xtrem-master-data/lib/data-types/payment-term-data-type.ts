import { ReferenceDataType } from '@sage/xtrem-core';
import { PaymentTerm } from '../nodes/payment-term';

export const paymentTerm = new ReferenceDataType({
    reference: () => PaymentTerm,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        tunnelPage: '@sage/xtrem-master-data/PaymentTerm',
        columnPaths: ['id', 'name', 'description', 'dueDateType'],
    },
});
