import { ReferenceDataType } from '@sage/xtrem-core';
import { LocationSequence } from '../nodes/location-sequence';

export const locationSequence = new ReferenceDataType({
    reference: () => LocationSequence,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id'],
        tunnelPage: '@sage/xtrem-master-data/LocationSequence',
    },
});
