import { ReferenceDataType } from '@sage/xtrem-core';
import { Location } from '../nodes/location';

export const location = new ReferenceDataType({
    reference: () => Location,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id', 'locationZone.name'],
        tunnelPage: '@sage/xtrem-master-data/Location',
    },
    filters: { lookup: { isActive: true } },
});
