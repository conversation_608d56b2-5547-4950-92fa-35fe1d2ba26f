import { ReferenceDataType } from '@sage/xtrem-core';
import { SequenceNumber } from '../nodes/sequence-number';

export const sequenceNumber = new ReferenceDataType({
    reference: () => SequenceNumber,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        tunnelPage: '@sage/xtrem-master-data/SequenceNumber',
        columnPaths: ['name', 'id'],
    },
});
