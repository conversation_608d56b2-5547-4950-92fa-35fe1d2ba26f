import { ReferenceDataType } from '@sage/xtrem-core';
import { Customer } from '../nodes/customer';

export const customer = new ReferenceDataType({
    reference: () => Customer,
    lookup: {
        valuePath: 'businessEntity.name',
        helperTextPath: 'businessEntity.id',
        columnPaths: ['businessEntity.name', 'businessEntity.id', 'businessEntity.taxIdNumber'],
        tunnelPage: '@sage/xtrem-master-data/Customer',
    },
});
