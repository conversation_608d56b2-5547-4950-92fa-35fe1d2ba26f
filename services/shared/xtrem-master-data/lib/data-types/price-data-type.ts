import type { decimal } from '@sage/xtrem-core';
import { DecimalDataType, RoundingMode } from '@sage/xtrem-core';
import type * as xtremMasterData from '../index';

export interface HasCurrency {
    currency: Promise<xtremMasterData.nodes.Currency | null>;
}

/**
 * DataType for amounts taking the currency on the node into account
 * The node has to have a currency: Currency column
 */
export class PriceDataType<T extends HasCurrency> extends DecimalDataType<T> {
    public override async adaptValue(node: T, val: decimal): Promise<decimal> {
        let formatted = DecimalDataType.toDecimalPlaces(val, this.scale, this.roundingMode);
        const decimalDigits = await (await node.currency)?.decimalDigits;
        if (decimalDigits) {
            formatted = DecimalDataType.toDecimalPlaces(val, decimalDigits, this.roundingMode);
        }
        return formatted;
    }
}

/**
 * Price data type to be used when referring to a price in sales price.
 * Node must have a property called 'currency'.
 * When setting the property value the price will be rounded, using the decimal digits of the sales price.
 */
export const priceInSalesPrice = new PriceDataType({
    precision: 18,
    scale: 3,
    roundingMode: RoundingMode.roundHalfUp,
});

/**
 * precision 20 scale 3 can't be negative
 */
export const basePrice = new PriceDataType({
    precision: 20, //
    scale: 3,
    roundingMode: RoundingMode.roundHalfUp,
});
