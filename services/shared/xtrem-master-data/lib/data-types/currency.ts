import { ReferenceDataType } from '@sage/xtrem-core';
import { Currency } from '../nodes/currency';

export const currency = new ReferenceDataType({
    reference: () => Currency,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id', 'symbol', 'decimalDigits', 'rounding'],
        tunnelPage: '@sage/xtrem-master-data/Currency',
    },
    filters: { lookup: { isActive: true } },
});
