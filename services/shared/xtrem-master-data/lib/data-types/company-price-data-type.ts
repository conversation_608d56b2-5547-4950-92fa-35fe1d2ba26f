import type { decimal, DecimalDataTypeOptions, Node, PropertyName } from '@sage/xtrem-core';
import { DecimalDataType, RoundingMode } from '@sage/xtrem-core';
import type * as xtremSystem from '@sage/xtrem-system';

export interface CompanyPriceDataTypeOptions extends DecimalDataTypeOptions {
    companyPropertyName?: string;
}

export class CompanyPriceDataType<T extends Node = Node> extends DecimalDataType<T> {
    static override defaultRoundingMode = RoundingMode.roundHalfUp;

    static override defaultPrecision = 28;

    static override defaultScale = 10;

    public companyPropertyName: PropertyName<T>;

    constructor(options: CompanyPriceDataTypeOptions) {
        super(options);
        if (options.companyPropertyName) {
            this.companyPropertyName = options.companyPropertyName as PropertyName<T>;
        }
    }

    override get roundingMode() {
        return this.options.roundingMode ?? CompanyPriceDataType.defaultRoundingMode;
    }

    override get precision() {
        return this.options.precision ?? CompanyPriceDataType.defaultPrecision;
    }

    override get scale() {
        // if we have a precision but no scale, assume that scale is 0
        return this.options.scale ?? (this.options.precision === undefined ? CompanyPriceDataType.defaultScale : 0);
    }

    public override async adaptValue(node: T, val: decimal): Promise<decimal> {
        let formatted = DecimalDataType.toDecimalPlaces(val, this.scale, this.roundingMode);
        if (this.companyPropertyName) {
            const company = (await node.$.getValue(this.companyPropertyName)) as xtremSystem.nodes.Company;
            if (company) {
                formatted = DecimalDataType.toDecimalPlaces(val, await company.priceScale, this.roundingMode);
            }
        }
        return formatted;
    }
}

export const companyPriceDataType = new CompanyPriceDataType({
    companyPropertyName: 'company',
});
