import { ReferenceDataType } from '@sage/xtrem-core';
import { Supplier } from '../nodes/supplier';

export const supplier = new ReferenceDataType({
    reference: () => Supplier,
    lookup: {
        valuePath: 'businessEntity.name',
        helperTextPath: 'businessEntity.id',
        columnPaths: ['businessEntity.name', 'businessEntity.id', 'businessEntity.taxIdNumber'],
        tunnelPage: '@sage/xtrem-master-data/Supplier',
    },
});
