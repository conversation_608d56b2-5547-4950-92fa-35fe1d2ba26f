import { ReferenceDataType } from '@sage/xtrem-core';

import { CustomerSupplierCategory } from '../nodes/customer-supplier-category';

export const customerSupplierCategory = new ReferenceDataType({
    reference: () => CustomerSupplierCategory,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id'],
        tunnelPage: '@sage/xtrem-master-data/CustomerSupplierCategory',
    },
});
