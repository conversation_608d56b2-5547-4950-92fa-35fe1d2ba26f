import { ReferenceDataType } from '@sage/xtrem-core';
import { BusinessEntity } from '../nodes/business-entity';

export const businessEntity = new ReferenceDataType({
    reference: () => BusinessEntity,
    isDefault: true,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id', 'taxIdNumber', 'isCustomer', 'isSupplier', 'isSite'],
        tunnelPage: '@sage/xtrem-master-data/BusinessEntity',
        imageFieldPath: 'image',
    },
});
