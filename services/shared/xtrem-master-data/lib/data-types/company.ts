import { ReferenceDataType } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';

export const masterDataCompany = new ReferenceDataType({
    reference: () => xtremSystem.nodes.Company,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id', 'description'],
        tunnelPage: '@sage/xtrem-master-data/Company',
    },
    filters: { lookup: { isActive: true } },
});
