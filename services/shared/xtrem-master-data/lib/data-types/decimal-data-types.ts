import { DecimalDataType } from '@sage/xtrem-core';

export const baseDecimal = new DecimalDataType({ precision: 18, scale: 3 });
export const price = new DecimalDataType({ precision: 10, scale: 3 });
export const quantity = new DecimalDataType({ precision: 10, scale: 5 });
export const unitConversionCoefficient = new DecimalDataType({ precision: 20, scale: 10 });
export const exchangeRate = new DecimalDataType({ precision: 20, scale: 10 });
