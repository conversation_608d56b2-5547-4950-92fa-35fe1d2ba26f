import { Test, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import { assert } from 'chai';
import { FakeDocumentTo } from '../../fixtures/lib/nodes';

describe('Fake base document', () => {
    it('The site needs to be a stock site. ', () =>
        Test.withContext(async context => {
            const notAnInventorySite = await context.read(xtremSystem.nodes.Site, { _id: '#US012' });

            assert.isFalse(await notAnInventorySite.isInventory);

            const fakeDoc = await context.create(FakeDocumentTo, {
                number: 'Fake01',
                site: notAnInventorySite,
                stockSite: notAnInventorySite,
            });
            await assert.isRejected(fakeDoc.$.save(), 'The record was not created.');
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['stockSite'],
                    severity: ValidationSeverity.error,
                    message: 'The site needs to be a stock site.',
                },
            ]);
        }));
    it('The site needs to be a financial site. ', () =>
        Test.withContext(async context => {
            const notAFinancialSite = await context.read(xtremSystem.nodes.Site, { _id: '#US010' });

            assert.isFalse(await notAFinancialSite.isFinance);

            const fakeDoc = await context.create(FakeDocumentTo, {
                number: 'Fake01',
                site: notAFinancialSite,
                financialSite: notAFinancialSite,
            });
            await assert.isRejected(fakeDoc.$.save(), 'The record was not created.');
            assert.deepEqual(context.diagnoses, [
                {
                    path: ['financialSite'],
                    severity: ValidationSeverity.error,
                    message: 'The site needs to be a financial site.',
                },
            ]);
        }));
});
