import type { Context, decimal } from '@sage/xtrem-core';
import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremMasterData from '../../../lib/index';

describe('ItemSiteCost getStandardCostAt', () => {
    // ItemSiteCosts of 17890-B|US003
    // 2020-01-01 -> 2020-12-31 : 0.8
    // 2021-01-01 -> 2022-12-31 : 4.0
    async function testGetStandardCostAt(context: Context, costDate: date, expectedCost: decimal) {
        const itemSite = await context.read(xtremMasterData.nodes.ItemSite, { item: '#17890-B', site: '#US003' });
        const unitCost = await xtremMasterData.functions.itemSiteCost.getStandardCostAt(
            itemSite.itemSiteCost,
            costDate,
        );
        assert.deepEqual(unitCost, expectedCost);
    }
    it('Get cost before all existing ones -> 0', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(1999, 12, 31), 0);
        }));
    it('Get cost at the beginning of the 1st cost -> 0.8', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(2020, 1, 1), 0.8);
        }));
    it('Get cost at the end of the 1st cost -> 0.8', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(2020, 12, 31), 0.8);
        }));
    it('Get cost between the start and end of the 1st cost -> 0.8', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(2020, 6, 30), 0.8);
        }));
    it('Get cost at the beginning of the 2nd cost -> 4.0', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(2021, 1, 1), 4.0);
        }));
    it('Get cost at the end of the 2nd cost -> 4.0', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(2022, 12, 31), 4.0);
        }));
    it('Get cost between the start and end of the 2nd cost -> 4.0', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(2021, 6, 30), 4.0);
        }));
    it('Get cost after last cost -> 0.0', () =>
        Test.withContext(async context => {
            await testGetStandardCostAt(context, date.make(2999, 1, 1), 0);
        }));
});
