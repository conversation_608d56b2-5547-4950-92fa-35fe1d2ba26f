"business_entity";"is_active";"primary_address";"payment_term";"minimum_order_amount";"bill_to_customer";"bill_to_address";"pay_by_customer";"pay_by_address";"display_status"
"10012";"Y";"10012|10";"DEMO_NET_15_CUSTOMER";"250";"10012";"10012|10";"10012";"10012|10";"active"
"10078";"Y";"10078|10";"DEMO_NET_15_CUSTOMER";"0";"10078";"10078|10";"10078";"10078|10";"active"
"17001";"Y";"17001|10";"DEMO_NET_15_CUSTOMER";"0";"17001";"17001|10";"17001";"17001|10";"active"
"ATSE00074";"Y";"ATSE00074|10";"DEMO_NET_15_CUSTOMER";"0";"ATSE00074";"ATSE00074|10";"ATSE00074";"ATSE00074|10";"active"
"DE072";"Y";"DE072|10";"DEMO_NET_15_CUSTOMER";"0";"DE072";"DE072|10";"DE072";"DE072|10";"active"
"Onhold_Customer_01";"Y";"Onhold_Customer_01|10";"DEMO_NET_15_CUSTOMER";"0";"Onhold_Customer_01";"Onhold_Customer_01|10";"Onhold_Customer_01";"Onhold_Customer_01|10";"active"
"Onhold_Customer_02";"Y";"Onhold_Customer_02|10";"DEMO_NET_15_CUSTOMER";"0";"Onhold_Customer_02";"Onhold_Customer_02|10";"Onhold_Customer_02";"Onhold_Customer_02|10";"active"
"MRP001";"Y";"MRP001|10";"DEMO_NET_15_CUSTOMER";"0";"MRP001";"MRP001|10";"MRP001";"MRP001|10";"active"
"KLZA1";"Y";"KLZA1|10";"DEMO_NET_15_CUSTOMER";"0";"KLZA1";"KLZA1|10";"KLZA1";"KLZA1|10";"active"
"KLZA2";"Y";"KLZA2|10";"DEMO_NET_15_CUSTOMER";"0";"KLZA2";"KLZA2|10";"KLZA2";"KLZA2|10";"active"
"61";"Y";"61|10";"DUE_UPON_RECEIPT_ALL";"0";"61";"61|10";"61";"61|10";"active"
"450";"Y";"450|10";"DUE_UPON_RECEIPT_ALL";"0";"450";"450|10";"450";"450|10";"active"
"800";"Y";"800|10";"DUE_UPON_RECEIPT_ALL";"0";"800";"800|10";"800";"800|10";"active"
"10063";"Y";"10063|10";"DEMO_NET_45_ALL";"0";"10063";"10063|10";"10063";"10063|10";"active"
"Menu_Action_01";"Y";"Menu_Action_01|10";"DEMO_NET_45_ALL";"0";"Menu_Action_01";"Menu_Action_01|10";"Menu_Action_01";"Menu_Action_01|10";"active"
"ML_AUTOMATION_01";"Y";"ML_AUTOMATION_01|10";"DEMO_30_DAYS_END_OF_MONTH_ALL";"0";"ML_AUTOMATION_01";"ML_AUTOMATION_01|10";"ML_AUTOMATION_01";"ML_AUTOMATION_01|10";"active"
"AutomationCW";"Y";"AutomationCW|30";"DEMO_30_DAYS_END_OF_MONTH_ALL";"0";"AutomationCW";"AutomationCW|30";"AutomationCW";"AutomationCW|30";"active"
