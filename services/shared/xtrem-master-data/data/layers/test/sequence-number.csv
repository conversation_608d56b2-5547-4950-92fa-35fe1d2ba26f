"id";"legislation";"_vendor";"name";"definition_level";"rtz_level";"is_cleared_by_reset";"type";"is_chronological"
"PostedSalesCreditMemo";;;"{""en"":""Posted Sales credit memo"",""en-US"":""Posted Sales credit memo"",""fr-FR"":""Avoir client (validé)""}";"site";"yearly";"N";"alphanumeric";"Y"
"PostedSalesInvoice";;;"{""en"":""Posted Sales invoice"",""en-US"":""Posted Sales invoice"",""fr-FR"":""Facture client (validée)""}";"site";"yearly";"N";"alphanumeric";"Y"
"SalesInvoice";;;"{""en"":""Sales invoice"",""en-US"":""Sales invoice"",""fr-FR"":""Facture client""}";"site";"yearly";"Y";"alphanumeric";"Y"
"SalesInvoiceUSA";"US";;"{""en"":""Sales invoice USA"",""en-US"":""Sales invoice USA"",""fr-FR"":""Facture client USA""}";"site";"yearly";"Y";"alphanumeric";"Y"
"TEST_CONTAINER_BOX";;;"{""en"":""Box""}";"application";"noReset";"N";"alphanumeric";"N"
"TEST_CONTAINER_PALLET";;;"{""en"":""Pallet""}";"application";"noReset";"N";"alphanumeric";"N"
"TEST_CONTAINER_SITE";;;"{""en"":""Container by site""}";"application";"noReset";"N";"alphanumeric";"N"
"TEST_COMPANY_LVL";;;"{""en"":""Testing company level without company component""}";"company";"noReset";"N";"alphanumeric";"N"
"TEST_YYMMDD";;;"{""en"":""Testing Company2 Year2  Month2 day2""}";"company";"noReset";"N";"alphanumeric";"N"
"TEST_NUMERIC";;;"{""en"":""Testing Numeric type""}";"application";"noReset";"N";"numeric";"N"
"TEST_SIMPLE";;;"{""en"":""Testing Simple only component sequenceNumber""}";"application";"noReset";"N";"alphanumeric";"N"
"TEST_LEGISLATION_US_LEG";"US";;"{""en"":""Testing Legislation US sequenceNumber""}";"application";"yearly";"N";"alphanumeric";"N"
"TEST_LEGISLATION_FR_LEG";"FR";;"{""en"":""Testing Legislation FR sequenceNumber""}";"application";"monthly";"N";"alphanumeric";"N"
"TEST_LEGISLATION_NO_LEG";;;"{""en"":""Testing Legislation No Leg sequenceNumber""}";"application";"noReset";"N";"alphanumeric";"N"
"SALES_INVOICE_USA2";"US";;"{""en"":""Sales invoice USA 2"",""en-US"":""Sales invoice USA 2"",""fr-FR"":""Facture client USA 2""}";"site";"yearly";"N";"alphanumeric";"Y"
"SALES_INVOICE_USA3";"US";;"{""en"":""Sales invoice USA 3"",""en-US"":""Sales invoice USA 3"",""fr-FR"":""Facture client USA 3""}";"site";"yearly";"N";"alphanumeric";"Y"
"TEST_SEQUENCE_VALUE";;;"{""en"":""Test sequence value""}";"application";"noReset";"N";"alphanumeric";"N"
"TEST_SN_STKCOUNT";;;"{""en"":""SequenceNumber of SN for Stock count""}";"application";"noReset";"N";"alphanumeric";"N"
"SALES_INVOICE_COMPANY";"US";;"{""en"":""Test company level"",""en-US"":""Test company level"",""fr-FR"":""Test company level""}";"company";"noReset";"N";"alphanumeric";"Y"
"TEST_ANOTHER";;;"{""en"":""Testing Another Simple only component sequenceNumber""}";"application";"noReset";"N";"alphanumeric";"N"
"TEST_ANOTHER";"US";;"{""en"":""Testing Another Simple only component sequenceNumber""}";"application";"noReset";"N";"alphanumeric";"N"
