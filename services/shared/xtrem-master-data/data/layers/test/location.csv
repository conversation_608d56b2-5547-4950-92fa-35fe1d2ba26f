"id";"location_zone";"name";"is_active";"dangerous_good_allowed";"location_type"
"LOC1";"US001|Loading dock";"location 1";"Y";"Y";"Internal"
"LOC2";"US002|Storage dock";"location 2";"Y";"Y";"Dock"
"LOC3";"US001|Loading dock";"location 3";"Y";"Y";"Customer"
"LOC4";"US002|Storage dock";"location 4";"Y";"N";"SubContract"
"LOC5";"US003|Production line";"location 5";"Y";"Y";"Internal"
"LOC6";"US001|Loading dock";"location 6";"N";"N";"Internal"
"LOC7";"US002|Storage dock";"location 7";"Y";"Y";"Dock"
"LOC8";"US001|Loading dock";"location 8";"Y";"Y";"Customer"
"LOC9";"US002|Storage dock";"location 9";"Y";"Y";"SubContract"
"LOC10";"US003|Production line";"location 10";"N";"N";"Internal"
"LOC11";"US003|Production line";"location 11";"Y";"Y";"Internal"
"LOC1";"US003|Production line";"location 1";"Y";"N";"Internal"
"LOC1";"US004|Alternative warehouse";"location 1";"Y";"Y";"Internal"
"LOC1";"US005|South street warehouse";"location 1";"Y";"N";"Internal"
"Location";"US002|Storage dock";"Location";"Y";"Y";"Internal"
"PTLOC01";"ETS1-S01|Central";"PTLOC01";"Y";"Y";"Internal"
"PTLOC02";"ETS1-S01|Central";"PTLOC02";"Y";"Y";"Internal"
"PTLOC03";"ETS1-S01|Central";"PTLOC03";"Y";"Y";"Dock"
"PTLOC11";"ETS1-S01|Receiving";"PTLOC01";"Y";"Y";"Dock"
"PTLOC12";"ETS1-S01|Receiving";"PTLOC02";"Y";"Y";"Internal"
"PTLOC13";"ETS1-S01|Receiving";"PTLOC03";"Y";"Y";"Internal"
"PTLOC21";"ETS1-S01|Receiving 2";"PTLOC03";"Y";"Y";"Internal"
"PTLOC22";"ETS1-S01|Receiving 2";"PTLOC03";"Y";"Y";"Internal"
"PTLOC23";"ETS1-S01|Receiving 2";"PTLOC03";"Y";"Y";"Internal"
"CUST01";"RX006|INTERNAL";"CUST01";"Y";"Y";"Customer"
"DOC01";"RX006|INTERNAL";"DOC01";"Y";"Y";"Dock"
"CUST02";"RX006|INTERNAL";"CUST02";"N";"Y";"Customer"
