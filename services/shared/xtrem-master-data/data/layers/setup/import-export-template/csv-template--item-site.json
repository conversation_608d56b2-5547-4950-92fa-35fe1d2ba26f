{"data": [{"_id": "0", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "10", "path": "!site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "20", "path": "prodLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "prod lead time"}, {"_id": "30", "path": "safetyStock", "locale": "", "dataType": "decimal", "isCustom": false, "description": "safety stock"}, {"_id": "40", "path": "batchQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "batch quantity"}, {"_id": "50", "path": "purchaseLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "purchase lead time"}, {"_id": "60", "path": "replenishmentMethod", "locale": "", "dataType": "enum(notManaged,byReorderPoint,byMRP)", "isCustom": false, "description": "replenishment method"}, {"_id": "70", "path": "reorderPoint", "locale": "", "dataType": "decimal", "isCustom": false, "description": "reorder point"}, {"_id": "80", "path": "preferredProcess", "locale": "", "dataType": "enum(purchasing,production)", "isCustom": false, "description": "preferred process"}, {"_id": "90", "path": "valuationMethod", "locale": "", "dataType": "enum(standardCost,averageCost,fifoCost)", "isCustom": false, "description": "valuation method"}, {"_id": "100", "path": "stockValuationAtAverageCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "stock valuation at average cost"}, {"_id": "110", "path": "isSpecificItemTaxGroup", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is specific item tax group (false/true)"}, {"_id": "120", "path": "itemTaxGroup", "locale": "", "dataType": "reference", "isCustom": false, "description": "item tax group (#id)"}, {"_id": "140", "path": "lastCountDate", "locale": "", "dataType": "date", "isCustom": false, "description": "last count date (YYYY-MM-DD)"}, {"_id": "150", "path": "economicOrderQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "economic order quantity"}, {"_id": "160", "path": "#valuations", "locale": "", "dataType": "collection", "isCustom": false, "description": "valuations"}, {"_id": "170", "path": "valuationMethod", "locale": "", "dataType": "enum(standardCost,averageCost,fifoCost)", "isCustom": false, "description": "valuation method"}, {"_id": "180", "path": "*valuationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "valuation date (YYYY-MM-DD)"}, {"_id": "190", "path": "quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity"}, {"_id": "200", "path": "cost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "cost"}, {"_id": "210", "path": "value", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value"}]}