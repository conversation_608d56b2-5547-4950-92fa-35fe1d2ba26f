{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "20", "path": "legalEntity", "locale": "", "dataType": "enum(corporation,physicalPerson)", "isCustom": false, "description": "legal entity"}, {"_id": "30", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "40", "path": "*country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "50", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "60", "path": "taxIdNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "tax id number"}, {"_id": "70", "path": "siret", "locale": "", "dataType": "string", "isCustom": false, "description": "siret"}, {"_id": "80", "path": "image", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "image"}, {"_id": "90", "path": "website", "locale": "", "dataType": "string", "isCustom": false, "description": "website"}, {"_id": "100", "path": "parent", "locale": "", "dataType": "reference", "isCustom": false, "description": "parent (#id)"}, {"_id": "130", "path": "#addresses", "locale": "", "dataType": "collection", "isCustom": false, "description": "addresses"}, {"_id": "140", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "150", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "160", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "170", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "180", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "190", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "200", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "210", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "220", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "230", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "530", "path": "#contacts", "locale": "", "dataType": "collection", "isCustom": false, "description": "contacts"}, {"_id": "540", "path": "isActive#3", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "550", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "560", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "570", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "580", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "590", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "600", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "610", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "620", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "630", "path": "*address", "locale": "", "dataType": "reference", "isCustom": false, "description": "address (#businessEntity|_sortValue)"}, {"_id": "640", "path": "isPrimary#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "650", "path": "/site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "660", "path": "!id#1", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "670", "path": "*name#2", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "680", "path": "description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "690", "path": "isActive#4", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "700", "path": "*legalCompany", "locale": "", "dataType": "reference", "isCustom": false, "description": "legal company (#id)"}, {"_id": "710", "path": "isFinance", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is finance (true/false)"}, {"_id": "720", "path": "isPurchase", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase (true/false)"}, {"_id": "730", "path": "isInventory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is inventory (true/false)"}, {"_id": "740", "path": "isSales", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sales (true/false)"}, {"_id": "750", "path": "isManufacturing", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is manufacturing (true/false)"}, {"_id": "760", "path": "isProjectManagement", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is project management (true/false)"}, {"_id": "770", "path": "primaryAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "780", "path": "financialSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "financial site (#id)"}, {"_id": "790", "path": "isLocationManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is location managed (false/true)"}, {"_id": "800", "path": "defaultLocation", "locale": "", "dataType": "reference", "isCustom": false, "description": "default location (#id|locationZone)"}, {"_id": "810", "path": "sequenceNumberId", "locale": "", "dataType": "string", "isCustom": false, "description": "sequence number id"}, {"_id": "820", "path": "defaultStockStatus", "locale": "", "dataType": "reference", "isCustom": false, "description": "default stock status (#id)"}, {"_id": "830", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "840", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "850", "path": "isPurchaseRequisitionApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase requisition approval managed (true/false)"}, {"_id": "860", "path": "purchaseRequisitionDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase requisition default approver (#email)"}, {"_id": "870", "path": "purchaseRequisitionSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase requisition substitute approver (#email)"}, {"_id": "880", "path": "isPurchaseOrderApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase order approval managed (true/false)"}, {"_id": "890", "path": "purchaseOrderDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase order default approver (#email)"}, {"_id": "900", "path": "purchaseOrderSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase order substitute approver (#email)"}, {"_id": "910", "path": "isSalesReturnRequestApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sales return request approval managed (true/false)"}, {"_id": "920", "path": "salesReturnRequestDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales return request default approver (#email)"}, {"_id": "930", "path": "salesReturnRequestSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales return request substitute approver (#email)"}]}