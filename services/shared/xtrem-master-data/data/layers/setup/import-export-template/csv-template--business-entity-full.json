{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "20", "path": "legalEntity", "locale": "", "dataType": "enum(corporation,physicalPerson)", "isCustom": false, "description": "legal entity"}, {"_id": "30", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "40", "path": "*country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "50", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "60", "path": "taxIdNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "tax id number"}, {"_id": "70", "path": "siret", "locale": "", "dataType": "string", "isCustom": false, "description": "siret"}, {"_id": "80", "path": "image", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "image"}, {"_id": "90", "path": "website", "locale": "", "dataType": "string", "isCustom": false, "description": "website"}, {"_id": "100", "path": "parent", "locale": "", "dataType": "reference", "isCustom": false, "description": "parent (#id)"}, {"_id": "130", "path": "#addresses", "locale": "", "dataType": "collection", "isCustom": false, "description": "addresses"}, {"_id": "140", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "150", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "160", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "170", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "180", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "190", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "200", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "210", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "220", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "230", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "240", "path": "//deliveryDetail", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery detail (#address)"}, {"_id": "250", "path": "isActive#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "260", "path": "isPrimary#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "270", "path": "shipmentSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "shipment site (#id)"}, {"_id": "280", "path": "*mode", "locale": "", "dataType": "reference", "isCustom": false, "description": "mode (#id)"}, {"_id": "290", "path": "leadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "lead time"}, {"_id": "300", "path": "incoterm", "locale": "", "dataType": "reference", "isCustom": false, "description": "incoterm (#id)"}, {"_id": "310", "path": "isMondayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is monday work day (true/false)"}, {"_id": "320", "path": "isTuesdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tuesday work day (true/false)"}, {"_id": "330", "path": "isWednesdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is wednesday work day (true/false)"}, {"_id": "340", "path": "isThursdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is thursday work day (true/false)"}, {"_id": "350", "path": "isFridayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is friday work day (true/false)"}, {"_id": "360", "path": "isSaturdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is saturday work day (false/true)"}, {"_id": "370", "path": "isSundayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sunday work day (false/true)"}, {"_id": "380", "path": "taxZone", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax zone (#id)"}, {"_id": "390", "path": "//intacctBusinessEntityAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "intacct business entity address (#contact)"}, {"_id": "400", "path": "*integration", "locale": "", "dataType": "reference", "isCustom": false, "description": "integration (#id)"}, {"_id": "410", "path": "*node", "locale": "", "dataType": "reference", "isCustom": false, "description": "node (#name)"}, {"_id": "420", "path": "sysId", "locale": "", "dataType": "string", "isCustom": false, "description": "sys id"}, {"_id": "430", "path": "state", "locale": "", "dataType": "enum(not,pending,success,error,desynchronized)", "isCustom": false, "description": "state"}, {"_id": "440", "path": "url", "locale": "", "dataType": "string", "isCustom": false, "description": "url"}, {"_id": "450", "path": "secondaryPageLink", "locale": "", "dataType": "json", "isCustom": false, "description": "secondary page link"}, {"_id": "460", "path": "lastMessage", "locale": "", "dataType": "string", "isCustom": false, "description": "last message"}, {"_id": "470", "path": "creationStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "creation stamp (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "480", "path": "updateStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "update stamp (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "490", "path": "version", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "500", "path": "difference", "locale": "", "dataType": "json", "isCustom": false, "description": "difference"}, {"_id": "510", "path": "intacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "520", "path": "recordNo", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}, {"_id": "530", "path": "#contacts", "locale": "", "dataType": "collection", "isCustom": false, "description": "contacts"}, {"_id": "540", "path": "isActive#3", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "550", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "560", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "570", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "580", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "590", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "600", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "610", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "620", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "630", "path": "*address", "locale": "", "dataType": "reference", "isCustom": false, "description": "address (#businessEntity|_sortValue)"}, {"_id": "640", "path": "isPrimary#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "650", "path": "/site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "660", "path": "!id#1", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "670", "path": "*name#2", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "680", "path": "description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "690", "path": "isActive#4", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "700", "path": "*legalCompany", "locale": "", "dataType": "reference", "isCustom": false, "description": "legal company (#id)"}, {"_id": "710", "path": "isFinance", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is finance (true/false)"}, {"_id": "720", "path": "isPurchase", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase (true/false)"}, {"_id": "730", "path": "isInventory", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is inventory (true/false)"}, {"_id": "740", "path": "isSales", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sales (true/false)"}, {"_id": "750", "path": "isManufacturing", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is manufacturing (true/false)"}, {"_id": "760", "path": "isProjectManagement", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is project management (true/false)"}, {"_id": "770", "path": "primaryAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "780", "path": "financialSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "financial site (#id)"}, {"_id": "790", "path": "isLocationManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is location managed (false/true)"}, {"_id": "800", "path": "defaultLocation", "locale": "", "dataType": "reference", "isCustom": false, "description": "default location (#id|locationZone)"}, {"_id": "810", "path": "sequenceNumberId", "locale": "", "dataType": "string", "isCustom": false, "description": "sequence number id"}, {"_id": "820", "path": "timeZone", "locale": "", "dataType": "string", "isCustom": false, "description": "time zone"}, {"_id": "830", "path": "defaultStockStatus", "locale": "", "dataType": "reference", "isCustom": false, "description": "default stock status (#id)"}, {"_id": "840", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "850", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "860", "path": "isPurchaseRequisitionApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase requisition approval managed (true/false)"}, {"_id": "870", "path": "purchaseRequisitionDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase requisition default approver (#email)"}, {"_id": "880", "path": "purchaseRequisitionSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase requisition substitute approver (#email)"}, {"_id": "890", "path": "isPurchaseOrderApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is purchase order approval managed (true/false)"}, {"_id": "900", "path": "purchaseOrderDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase order default approver (#email)"}, {"_id": "910", "path": "purchaseOrderSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase order substitute approver (#email)"}, {"_id": "920", "path": "isSalesReturnRequestApprovalManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sales return request approval managed (true/false)"}, {"_id": "930", "path": "salesReturnRequestDefaultApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales return request default approver (#email)"}, {"_id": "940", "path": "salesReturnRequestSubstituteApprover", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales return request substitute approver (#email)"}, {"_id": "950", "path": "/customer", "locale": "", "dataType": "reference", "isCustom": false, "description": "customer (#businessEntity)"}, {"_id": "960", "path": "isActive#5", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "970", "path": "primaryAddress#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "980", "path": "internalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "990", "path": "*paymentTerm", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "1000", "path": "minimumOrderAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum order amount"}, {"_id": "1010", "path": "category", "locale": "", "dataType": "reference", "isCustom": false, "description": "category (#id)"}, {"_id": "1020", "path": "billToCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to customer (#businessEntity)"}, {"_id": "1030", "path": "bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to address (#businessEntity|_sortValue)"}, {"_id": "1040", "path": "payByCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay by customer (#businessEntity)"}, {"_id": "1050", "path": "payByAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay by address (#businessEntity|_sortValue)"}, {"_id": "1060", "path": "isOnHold", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is on hold (false/true)"}, {"_id": "1070", "path": "creditLimit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "credit limit"}, {"_id": "1080", "path": "postingClass", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "1090", "path": "storedDimensions#1", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "1100", "path": "storedAttributes#1", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "1110", "path": "shopifyId", "locale": "", "dataType": "string", "isCustom": false, "description": "shopify id"}, {"_id": "1120", "path": "##items", "locale": "", "dataType": "collection", "isCustom": false, "description": "items"}, {"_id": "1130", "path": "isActive#6", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "1140", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1150", "path": "id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "1160", "path": "name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "1170", "path": "salesUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales unit (#id)"}, {"_id": "1180", "path": "salesUnitToStockUnitConversion", "locale": "", "dataType": "decimal", "isCustom": false, "description": "sales unit to stock unit conversion"}, {"_id": "1190", "path": "minimumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum sales quantity"}, {"_id": "1200", "path": "maximumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "maximum sales quantity"}, {"_id": "1210", "path": "//intacctCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "intacct customer (#parent)"}, {"_id": "1220", "path": "*integration#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "integration (#id)"}, {"_id": "1230", "path": "*node#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "node (#name)"}, {"_id": "1240", "path": "sysId#1", "locale": "", "dataType": "string", "isCustom": false, "description": "sys id"}, {"_id": "1250", "path": "state#1", "locale": "", "dataType": "enum(not,pending,success,error,desynchronized)", "isCustom": false, "description": "state"}, {"_id": "1260", "path": "url#1", "locale": "", "dataType": "string", "isCustom": false, "description": "url"}, {"_id": "1270", "path": "secondaryPageLink#1", "locale": "", "dataType": "json", "isCustom": false, "description": "secondary page link"}, {"_id": "1280", "path": "lastMessage#1", "locale": "", "dataType": "string", "isCustom": false, "description": "last message"}, {"_id": "1290", "path": "creationStamp#1", "locale": "", "dataType": "datetime", "isCustom": false, "description": "creation stamp (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1300", "path": "updateStamp#1", "locale": "", "dataType": "datetime", "isCustom": false, "description": "update stamp (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1310", "path": "version#1", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "1320", "path": "difference#1", "locale": "", "dataType": "json", "isCustom": false, "description": "difference"}, {"_id": "1330", "path": "intacctId#1", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "1340", "path": "recordNo#1", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}, {"_id": "1350", "path": "/supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "1360", "path": "isActive#7", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "1370", "path": "primaryAddress#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "1380", "path": "internalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "1390", "path": "*paymentTerm#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "1400", "path": "minimumOrderAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum order amount"}, {"_id": "1410", "path": "category#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "category (#id)"}, {"_id": "1420", "path": "supplierType", "locale": "", "dataType": "enum(chemical,foodAndBeverage,other)", "isCustom": false, "description": "supplier type"}, {"_id": "1430", "path": "standardIndustrialClassification", "locale": "", "dataType": "reference", "isCustom": false, "description": "standard industrial classification (#sicCode)"}, {"_id": "1440", "path": "paymentMethod", "locale": "", "dataType": "string", "isCustom": false, "description": "payment method"}, {"_id": "1450", "path": "parent#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "parent (#businessEntity)"}, {"_id": "1460", "path": "incoterm#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "incoterm (#id)"}, {"_id": "1470", "path": "billBySupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by supplier (#businessEntity)"}, {"_id": "1480", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by address (#businessEntity|_sortValue)"}, {"_id": "1490", "path": "payToSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay to supplier (#businessEntity)"}, {"_id": "1500", "path": "payToAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay to address (#businessEntity|_sortValue)"}, {"_id": "1510", "path": "returnToSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "return to supplier (#businessEntity)"}, {"_id": "1520", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "return to address (#businessEntity|_sortValue)"}, {"_id": "1530", "path": "deliveryMode", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery mode (#id)"}, {"_id": "1540", "path": "postingClass#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "1550", "path": "storedDimensions#2", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "1560", "path": "storedAttributes#2", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}, {"_id": "1570", "path": "defaultBuyer", "locale": "", "dataType": "reference", "isCustom": false, "description": "default buyer (#email)"}, {"_id": "1580", "path": "##certificates", "locale": "", "dataType": "collection", "isCustom": false, "description": "certificates"}, {"_id": "1590", "path": "*standard", "locale": "", "dataType": "reference", "isCustom": false, "description": "standard (#id)"}, {"_id": "1600", "path": "*id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "1610", "path": "dateOfCertification", "locale": "", "dataType": "date", "isCustom": false, "description": "date of certification (yyyy-MM-dd)"}, {"_id": "1620", "path": "validUntil", "locale": "", "dataType": "date", "isCustom": false, "description": "valid until (yyyy-MM-dd)"}, {"_id": "1630", "path": "certificationBody", "locale": "", "dataType": "string", "isCustom": false, "description": "certification body"}, {"_id": "1640", "path": "dateOfOriginalCertification", "locale": "", "dataType": "date", "isCustom": false, "description": "date of original certification (yyyy-MM-dd)"}, {"_id": "1650", "path": "##items#1", "locale": "", "dataType": "collection", "isCustom": false, "description": "items"}, {"_id": "1660", "path": "isActive#8", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "1670", "path": "!item#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "1680", "path": "supplierItemCode", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier item code"}, {"_id": "1690", "path": "supplierItemName", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier item name"}, {"_id": "1700", "path": "supplierPriority", "locale": "", "dataType": "integer", "isCustom": false, "description": "supplier priority"}, {"_id": "1710", "path": "isDefaultItemSupplier", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is default item supplier (false/true)"}, {"_id": "1720", "path": "*purchaseUnitOfMeasure", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase unit of measure (#id)"}, {"_id": "1730", "path": "minimumPurchaseQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum purchase quantity"}, {"_id": "1740", "path": "purchaseLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "purchase lead time"}, {"_id": "1750", "path": "//intacctSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "intacct supplier (#parent)"}, {"_id": "1760", "path": "*integration#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "integration (#id)"}, {"_id": "1770", "path": "*node#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "node (#name)"}, {"_id": "1780", "path": "sysId#2", "locale": "", "dataType": "string", "isCustom": false, "description": "sys id"}, {"_id": "1790", "path": "state#2", "locale": "", "dataType": "enum(not,pending,success,error,desynchronized)", "isCustom": false, "description": "state"}, {"_id": "1800", "path": "url#2", "locale": "", "dataType": "string", "isCustom": false, "description": "url"}, {"_id": "1810", "path": "secondaryPageLink#2", "locale": "", "dataType": "json", "isCustom": false, "description": "secondary page link"}, {"_id": "1820", "path": "lastMessage#2", "locale": "", "dataType": "string", "isCustom": false, "description": "last message"}, {"_id": "1830", "path": "creationStamp#2", "locale": "", "dataType": "datetime", "isCustom": false, "description": "creation stamp (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1840", "path": "updateStamp#2", "locale": "", "dataType": "datetime", "isCustom": false, "description": "update stamp (yyyy-MM-ddThh:mm:ssZ)"}, {"_id": "1850", "path": "version#2", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "1860", "path": "difference#2", "locale": "", "dataType": "json", "isCustom": false, "description": "difference"}, {"_id": "1870", "path": "intacctId#2", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "1880", "path": "recordNo#2", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}]}