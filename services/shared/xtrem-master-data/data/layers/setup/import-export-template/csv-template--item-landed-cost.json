{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "*name", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "description", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"description (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "30", "path": "status", "locale": "", "dataType": "enum(active,inDevelopment,notRenewed,obsolete,notUsable)", "isCustom": false, "description": "status"}, {"_id": "40", "path": "type", "locale": "", "dataType": "enum(service,good,landedCost)", "isCustom": false, "description": "type"}, {"_id": "50", "path": "isBought", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is bought (false/true)"}, {"_id": "60", "path": "isManufactured", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is manufactured (false/true)"}, {"_id": "70", "path": "isSold", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sold (false/true)"}, {"_id": "80", "path": "ean<PERSON>umber", "locale": "", "dataType": "string", "isCustom": false, "description": "ean number"}, {"_id": "90", "path": "commodityCode", "locale": "", "dataType": "string", "isCustom": false, "description": "commodity code"}, {"_id": "100", "path": "*stockUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock unit (#id)"}, {"_id": "110", "path": "volumeUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "volume unit (#id)"}, {"_id": "120", "path": "volume", "locale": "", "dataType": "decimal", "isCustom": false, "description": "volume"}, {"_id": "130", "path": "weightUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "weight unit (#id)"}, {"_id": "140", "path": "weight", "locale": "", "dataType": "decimal", "isCustom": false, "description": "weight"}, {"_id": "150", "path": "density", "locale": "", "dataType": "decimal", "isCustom": false, "description": "density"}, {"_id": "160", "path": "image", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "image"}, {"_id": "170", "path": "category", "locale": "", "dataType": "reference", "isCustom": false, "description": "category (#id)"}, {"_id": "180", "path": "isStockManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is stock managed (false/true)"}, {"_id": "190", "path": "isPotencyManagement", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is potency management (false/true)"}, {"_id": "200", "path": "isTraceabilityManagement", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is traceability management (false/true)"}, {"_id": "210", "path": "purchaseUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase unit (#id)"}, {"_id": "220", "path": "purchaseUnitToStockUnitConversion", "locale": "", "dataType": "decimal", "isCustom": false, "description": "purchase unit to stock unit conversion"}, {"_id": "230", "path": "salesUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales unit (#id)"}, {"_id": "240", "path": "salesUnitToStockUnitConversion", "locale": "", "dataType": "decimal", "isCustom": false, "description": "sales unit to stock unit conversion"}, {"_id": "250", "path": "minimumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum sales quantity"}, {"_id": "260", "path": "maximumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "maximum sales quantity"}, {"_id": "270", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "280", "path": "basePrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "base price"}, {"_id": "290", "path": "minimumPrice", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum price"}, {"_id": "300", "path": "capacity", "locale": "", "dataType": "integer", "isCustom": false, "description": "capacity"}, {"_id": "310", "path": "itemTaxGroup", "locale": "", "dataType": "reference", "isCustom": false, "description": "item tax group (#id)"}, {"_id": "320", "path": "postingClass", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "330", "path": "intrastatAdditionalUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "intrastat additional unit (#id)"}, {"_id": "340", "path": "intrastatAdditionalUnitToStockUnitConversion", "locale": "", "dataType": "decimal", "isCustom": false, "description": "intrastat additional unit to stock unit conversion"}, {"_id": "350", "path": "shopifyId", "locale": "", "dataType": "string", "isCustom": false, "description": "shopify id"}, {"_id": "360", "path": "shopifySKU", "locale": "", "dataType": "string", "isCustom": false, "description": "shopify sku"}, {"_id": "370", "path": "shopifyInventoryItemId", "locale": "", "dataType": "string", "isCustom": false, "description": "shopify inventory item id"}, {"_id": "380", "path": "shopifyInventoryLevelId", "locale": "", "dataType": "string", "isCustom": false, "description": "shopify inventory level id"}, {"_id": "390", "path": "onlineStoreUrlShopify", "locale": "", "dataType": "string", "isCustom": false, "description": "online store url shopify"}, {"_id": "400", "path": "lotManagement", "locale": "", "dataType": "enum(notManaged,lotManagement,lotSublotManagement)", "isCustom": false, "description": "lot management"}, {"_id": "410", "path": "lotSequenceNumber", "locale": "", "dataType": "reference", "isCustom": false, "description": "lot sequence number (#id|legislation)"}, {"_id": "420", "path": "isExpiryManaged", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is expiry managed (false/true)"}, {"_id": "430", "path": "#allergens", "locale": "", "dataType": "collection", "isCustom": false, "description": "allergens"}, {"_id": "440", "path": "*allergen", "locale": "", "dataType": "reference", "isCustom": false, "description": "allergen (#id)"}, {"_id": "450", "path": "#classifications", "locale": "", "dataType": "collection", "isCustom": false, "description": "classifications"}, {"_id": "460", "path": "*classification", "locale": "", "dataType": "reference", "isCustom": false, "description": "classification (#id)"}, {"_id": "470", "path": "#itemSites", "locale": "", "dataType": "collection", "isCustom": false, "description": "item sites"}, {"_id": "480", "path": "!site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "490", "path": "prodLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "prod lead time"}, {"_id": "500", "path": "safetyStock", "locale": "", "dataType": "decimal", "isCustom": false, "description": "safety stock"}, {"_id": "510", "path": "batchQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "batch quantity"}, {"_id": "520", "path": "purchaseLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "purchase lead time"}, {"_id": "530", "path": "replenishmentMethod", "locale": "", "dataType": "enum(notManaged,byReorderPoint,byMRP)", "isCustom": false, "description": "replenishment method"}, {"_id": "540", "path": "reorderPoint", "locale": "", "dataType": "decimal", "isCustom": false, "description": "reorder point"}, {"_id": "550", "path": "preferredProcess", "locale": "", "dataType": "enum(purchasing,production)", "isCustom": false, "description": "preferred process"}, {"_id": "560", "path": "valuationMethod", "locale": "", "dataType": "enum(standardCost,averageCost,fifoCost)", "isCustom": false, "description": "valuation method"}, {"_id": "570", "path": "stockValuationAtAverageCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "stock valuation at average cost"}, {"_id": "580", "path": "isSpecificItemTaxGroup", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is specific item tax group (false/true)"}, {"_id": "590", "path": "itemTaxGroup#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "item tax group (#id)"}, {"_id": "610", "path": "lastCountDate", "locale": "", "dataType": "date", "isCustom": false, "description": "last count date (YYYY-MM-DD)"}, {"_id": "620", "path": "economicOrderQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "economic order quantity"}, {"_id": "630", "path": "##valuations", "locale": "", "dataType": "collection", "isCustom": false, "description": "valuations"}, {"_id": "640", "path": "*valuationMethod", "locale": "", "dataType": "enum(standardCost,averageCost,fifoCost)", "isCustom": false, "description": "valuation method"}, {"_id": "650", "path": "*valuationDate", "locale": "", "dataType": "date", "isCustom": false, "description": "valuation date (YYYY-MM-DD)"}, {"_id": "660", "path": "quantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "quantity"}, {"_id": "670", "path": "cost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "cost"}, {"_id": "680", "path": "value", "locale": "", "dataType": "decimal", "isCustom": false, "description": "value"}, {"_id": "690", "path": "/landedCostItem", "locale": "", "dataType": "reference", "isCustom": false, "description": "landed cost item (#item)"}, {"_id": "700", "path": "landedCostType", "locale": "", "dataType": "enum(freight,customs,duty,tariffs,taxes,insurance,administration,handling,processing,transportation,storage,currencyConversion,crating,demurrage,portCharges,other)", "isCustom": false, "description": "landed cost type"}, {"_id": "710", "path": "allocationRule", "locale": "", "dataType": "enum(byAmount,byQuantity,byWeight,byVolume)", "isCustom": false, "description": "allocation rule"}, {"_id": "720", "path": "allocationRuleUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "allocation rule unit (#id)"}, {"_id": "730", "path": "/intacctItem", "locale": "", "dataType": "reference", "isCustom": false, "description": "intacct item (#item)"}, {"_id": "740", "path": "*integration", "locale": "", "dataType": "reference", "isCustom": false, "description": "integration (#id)"}, {"_id": "750", "path": "*node", "locale": "", "dataType": "reference", "isCustom": false, "description": "node (#name)"}, {"_id": "760", "path": "sysId", "locale": "", "dataType": "string", "isCustom": false, "description": "sys id"}, {"_id": "770", "path": "state", "locale": "", "dataType": "enum(not,pending,success,error,desynchronized)", "isCustom": false, "description": "state"}, {"_id": "780", "path": "url", "locale": "", "dataType": "string", "isCustom": false, "description": "url"}, {"_id": "790", "path": "lastMessage", "locale": "", "dataType": "string", "isCustom": false, "description": "last message"}, {"_id": "800", "path": "creationStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "creation stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "810", "path": "updateStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "update stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "820", "path": "version", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "830", "path": "difference", "locale": "", "dataType": "json", "isCustom": false, "description": "difference"}, {"_id": "840", "path": "intacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "850", "path": "recordNo", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}]}