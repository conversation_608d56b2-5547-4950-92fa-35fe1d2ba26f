{"data": [{"_id": "0", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "10", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "20", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "30", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "40", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "50", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "60", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "70", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "80", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "90", "path": "!businessEntity", "locale": "", "dataType": "reference", "isCustom": false, "description": "business entity (#id)"}, {"_id": "100", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "110", "path": "#contacts", "locale": "", "dataType": "collection", "isCustom": false, "description": "contacts"}, {"_id": "120", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "130", "path": "title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "140", "path": "firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "150", "path": "lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "160", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "170", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "180", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "190", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "200", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "210", "path": "isPrimary#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "220", "path": "/intacctBusinessEntityAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "intacct business entity address (#contact)"}, {"_id": "230", "path": "*integration", "locale": "", "dataType": "reference", "isCustom": false, "description": "integration (#id)"}, {"_id": "240", "path": "*node", "locale": "", "dataType": "reference", "isCustom": false, "description": "node (#name)"}, {"_id": "250", "path": "sysId", "locale": "", "dataType": "string", "isCustom": false, "description": "sys id"}, {"_id": "260", "path": "state", "locale": "", "dataType": "enum(not,pending,success,error,desynchronized)", "isCustom": false, "description": "state"}, {"_id": "270", "path": "url", "locale": "", "dataType": "string", "isCustom": false, "description": "url"}, {"_id": "280", "path": "lastMessage", "locale": "", "dataType": "string", "isCustom": false, "description": "last message"}, {"_id": "290", "path": "creationStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "creation stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "300", "path": "updateStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "update stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "310", "path": "version", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "320", "path": "difference", "locale": "", "dataType": "json", "isCustom": false, "description": "difference"}, {"_id": "330", "path": "intacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "340", "path": "recordNo", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}]}