{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "description", "locale": "", "dataType": "string", "isCustom": false, "description": "description"}, {"_id": "20", "path": "name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "30", "path": "*locationCategory", "locale": "", "dataType": "enum(internal,dock,customer,subcontract)", "isCustom": false, "description": "location category"}, {"_id": "40", "path": "isAcceptedStockAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is accepted stock allowed (false/true)"}, {"_id": "50", "path": "isQualityControlStockAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is quality control stock allowed (false/true)"}, {"_id": "60", "path": "isRejectedStockAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is rejected stock allowed (false/true)"}, {"_id": "70", "path": "weightAllowed", "locale": "", "dataType": "decimal", "isCustom": false, "description": "weight allowed"}, {"_id": "80", "path": "volumeAllowed", "locale": "", "dataType": "decimal", "isCustom": false, "description": "volume allowed"}, {"_id": "90", "path": "storageCapacity", "locale": "", "dataType": "integer", "isCustom": false, "description": "storage capacity"}]}