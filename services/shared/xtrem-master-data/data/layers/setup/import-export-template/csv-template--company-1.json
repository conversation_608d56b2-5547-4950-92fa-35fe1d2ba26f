{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "240", "path": "#addresses", "locale": "", "dataType": "collection", "isCustom": false, "description": "addresses"}, {"_id": "350", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "260", "path": "name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "270", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "280", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "290", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "330", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "340", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}]}