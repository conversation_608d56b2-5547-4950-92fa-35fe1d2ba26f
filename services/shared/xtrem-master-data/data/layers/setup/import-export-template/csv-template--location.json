{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "!locationZone", "locale": "", "dataType": "reference", "isCustom": false, "description": "location zone (#site|id)"}, {"_id": "20", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "30", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "40", "path": "dangerousGoodAllowed", "locale": "", "dataType": "boolean", "isCustom": false, "description": "dangerous good allowed (false/true)"}, {"_id": "50", "path": "*locationType", "locale": "", "dataType": "reference", "isCustom": false, "description": "location type (#id)"}, {"_id": "60", "path": "volumeAllowed", "locale": "", "dataType": "decimal", "isCustom": false, "description": "volume allowed"}, {"_id": "70", "path": "weightAllowed", "locale": "", "dataType": "decimal", "isCustom": false, "description": "weight allowed"}, {"_id": "80", "path": "storageCapacity", "locale": "", "dataType": "integer", "isCustom": false, "description": "storage capacity"}]}