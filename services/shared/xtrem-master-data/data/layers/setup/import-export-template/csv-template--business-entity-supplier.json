{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "20", "path": "legalEntity", "locale": "", "dataType": "enum(corporation,physicalPerson)", "isCustom": false, "description": "legal entity"}, {"_id": "30", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "40", "path": "*country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "50", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "60", "path": "taxIdNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "tax id number"}, {"_id": "70", "path": "siret", "locale": "", "dataType": "string", "isCustom": false, "description": "siret"}, {"_id": "80", "path": "image", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "image"}, {"_id": "90", "path": "website", "locale": "", "dataType": "string", "isCustom": false, "description": "website"}, {"_id": "100", "path": "parent", "locale": "", "dataType": "reference", "isCustom": false, "description": "parent (#id)"}, {"_id": "130", "path": "#addresses", "locale": "", "dataType": "collection", "isCustom": false, "description": "addresses"}, {"_id": "140", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "150", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "160", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "170", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "180", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "190", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "200", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "210", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "220", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "230", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "530", "path": "#contacts", "locale": "", "dataType": "collection", "isCustom": false, "description": "contacts"}, {"_id": "540", "path": "isActive#3", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "550", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "560", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "570", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "580", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "590", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "600", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "610", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "620", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "630", "path": "*address", "locale": "", "dataType": "reference", "isCustom": false, "description": "address (#businessEntity|_sortValue)"}, {"_id": "640", "path": "isPrimary#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "1340", "path": "/supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "1350", "path": "isActive#7", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "1360", "path": "primaryAddress#2", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "1370", "path": "internalNote#1", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "1380", "path": "*paymentTerm#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "1390", "path": "minimumOrderAmount#1", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum order amount"}, {"_id": "1400", "path": "category#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "category (#id)"}, {"_id": "1410", "path": "supplierType", "locale": "", "dataType": "enum(chemical,foodAndBeverage,other)", "isCustom": false, "description": "supplier type"}, {"_id": "1420", "path": "standardIndustrialClassification", "locale": "", "dataType": "reference", "isCustom": false, "description": "standard industrial classification (#sicCode)"}, {"_id": "1430", "path": "paymentMethod", "locale": "", "dataType": "string", "isCustom": false, "description": "payment method"}, {"_id": "1440", "path": "parent#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "parent (#businessEntity)"}, {"_id": "1450", "path": "incoterm#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "incoterm (#id)"}, {"_id": "1460", "path": "billBySupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by supplier (#businessEntity)"}, {"_id": "1470", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by address (#businessEntity|_sortValue)"}, {"_id": "1480", "path": "payToSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay to supplier (#businessEntity)"}, {"_id": "1490", "path": "payToAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay to address (#businessEntity|_sortValue)"}, {"_id": "1500", "path": "returnToSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "return to supplier (#businessEntity)"}, {"_id": "1510", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "return to address (#businessEntity|_sortValue)"}, {"_id": "1520", "path": "deliveryMode", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery mode (#id)"}, {"_id": "1530", "path": "postingClass#1", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "1540", "path": "defaultBuyer", "locale": "", "dataType": "reference", "isCustom": false, "description": "default buyer (#email)"}]}