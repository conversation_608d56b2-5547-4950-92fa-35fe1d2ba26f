{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "name", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "*shiftStart", "locale": "", "dataType": "string", "isCustom": false, "description": "shift start"}, {"_id": "30", "path": "*shiftEnd", "locale": "", "dataType": "string", "isCustom": false, "description": "shift end"}]}