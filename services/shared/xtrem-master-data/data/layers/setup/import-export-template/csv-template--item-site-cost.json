{"data": [{"_id": "0", "path": "*itemSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "item site (#item|site)"}, {"_id": "10", "path": "*costCategory", "locale": "", "dataType": "reference", "isCustom": false, "description": "cost category (#id)"}, {"_id": "20", "path": "fromDate", "locale": "", "dataType": "date", "isCustom": false, "description": "from date (YYYY-MM-DD)"}, {"_id": "30", "path": "toDate", "locale": "", "dataType": "date", "isCustom": false, "description": "to date (YYYY-MM-DD)"}, {"_id": "40", "path": "version", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "50", "path": "forQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "for quantity"}, {"_id": "60", "path": "isCalculated", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is calculated (false/true)"}, {"_id": "70", "path": "materialCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "material cost"}, {"_id": "80", "path": "machineCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "machine cost"}, {"_id": "90", "path": "laborCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "labor cost"}, {"_id": "100", "path": "toolCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "tool cost"}, {"_id": "110", "path": "indirectCost", "locale": "", "dataType": "decimal", "isCustom": false, "description": "indirect cost"}, {"_id": "120", "path": "storedDimensions", "locale": "", "dataType": "json", "isCustom": false, "description": "stored dimensions"}, {"_id": "130", "path": "storedAttributes", "locale": "", "dataType": "json", "isCustom": false, "description": "stored attributes"}]}