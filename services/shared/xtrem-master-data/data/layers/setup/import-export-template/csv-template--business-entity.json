{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "20", "path": "legalEntity", "locale": "", "dataType": "enum(corporation,physicalPerson)", "isCustom": false, "description": "legal entity"}, {"_id": "30", "path": "*name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "40", "path": "*country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "50", "path": "currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "60", "path": "taxIdNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "tax id number"}, {"_id": "70", "path": "siret", "locale": "", "dataType": "string", "isCustom": false, "description": "siret"}, {"_id": "80", "path": "image", "locale": "", "dataType": "binaryStream", "isCustom": false, "description": "image"}, {"_id": "90", "path": "website", "locale": "", "dataType": "string", "isCustom": false, "description": "website"}, {"_id": "100", "path": "parent", "locale": "", "dataType": "reference", "isCustom": false, "description": "parent (#id)"}, {"_id": "130", "path": "#addresses", "locale": "", "dataType": "collection", "isCustom": false, "description": "addresses"}, {"_id": "140", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "150", "path": "*name#1", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "160", "path": "addressLine1", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 1"}, {"_id": "170", "path": "addressLine2", "locale": "", "dataType": "string", "isCustom": false, "description": "address line 2"}, {"_id": "180", "path": "city", "locale": "", "dataType": "string", "isCustom": false, "description": "city"}, {"_id": "190", "path": "region", "locale": "", "dataType": "string", "isCustom": false, "description": "region"}, {"_id": "200", "path": "postcode", "locale": "", "dataType": "string", "isCustom": false, "description": "postcode"}, {"_id": "210", "path": "country", "locale": "", "dataType": "reference", "isCustom": false, "description": "country (#id)"}, {"_id": "220", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "230", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "240", "path": "//deliveryDetail", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery detail (#address)"}, {"_id": "250", "path": "isActive#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "260", "path": "isPrimary#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "270", "path": "shipmentSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "shipment site (#id)"}, {"_id": "280", "path": "*mode", "locale": "", "dataType": "reference", "isCustom": false, "description": "mode (#id)"}, {"_id": "290", "path": "leadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "lead time"}, {"_id": "300", "path": "incoterm", "locale": "", "dataType": "reference", "isCustom": false, "description": "incoterm (#id)"}, {"_id": "310", "path": "isMondayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is monday work day (true/false)"}, {"_id": "320", "path": "isTuesdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tuesday work day (true/false)"}, {"_id": "330", "path": "isWednesdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is wednesday work day (true/false)"}, {"_id": "340", "path": "isThursdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is thursday work day (true/false)"}, {"_id": "350", "path": "isFridayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is friday work day (true/false)"}, {"_id": "360", "path": "isSaturdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is saturday work day (false/true)"}, {"_id": "370", "path": "isSundayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sunday work day (false/true)"}, {"_id": "380", "path": "taxZone", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax zone (#id)"}, {"_id": "530", "path": "#contacts", "locale": "", "dataType": "collection", "isCustom": false, "description": "contacts"}, {"_id": "540", "path": "isActive#3", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "550", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "560", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "570", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "580", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "590", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "600", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "610", "path": "locationPhoneNumber#1", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "620", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "630", "path": "*address", "locale": "", "dataType": "reference", "isCustom": false, "description": "address (#businessEntity|_sortValue)"}, {"_id": "640", "path": "isPrimary#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}]}