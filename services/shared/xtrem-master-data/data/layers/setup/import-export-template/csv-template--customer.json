{"data": [{"_id": "0", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "10", "path": "!businessEntity", "locale": "", "dataType": "reference", "isCustom": false, "description": "business entity (#id)"}, {"_id": "20", "path": "category", "locale": "", "dataType": "reference", "isCustom": false, "description": "category (#id)"}, {"_id": "30", "path": "internalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "40", "path": "primaryAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "50", "path": "minimumOrderAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum order amount"}, {"_id": "60", "path": "billToCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to customer (#businessEntity)"}, {"_id": "70", "path": "bill<PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill to address (#businessEntity|_sortValue)"}, {"_id": "80", "path": "payByCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay by customer (#businessEntity)"}, {"_id": "90", "path": "payByAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay by address (#businessEntity|_sortValue)"}, {"_id": "100", "path": "*paymentTerm", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "110", "path": "isOnHold", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is on hold (false/true)"}, {"_id": "120", "path": "creditLimit", "locale": "", "dataType": "decimal", "isCustom": false, "description": "credit limit"}, {"_id": "130", "path": "postingClass", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "140", "path": "shopifyId", "locale": "", "dataType": "string", "isCustom": false, "description": "shopify id"}, {"_id": "150", "path": "#deliveryAddresses", "locale": "", "dataType": "collection", "isCustom": false, "description": "delivery addresses"}, {"_id": "160", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "170", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}, {"_id": "180", "path": "*shipToAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "ship to address (#businessEntity|_sortValue)"}, {"_id": "190", "path": "shipmentSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "shipment site (#id)"}, {"_id": "200", "path": "*deliveryMode", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery mode (#id)"}, {"_id": "210", "path": "deliveryLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "delivery lead time"}, {"_id": "220", "path": "incoterm", "locale": "", "dataType": "reference", "isCustom": false, "description": "incoterm (#id)"}, {"_id": "230", "path": "isMondayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is monday work day (true/false)"}, {"_id": "240", "path": "isTuesdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is tuesday work day (true/false)"}, {"_id": "250", "path": "isWednesdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is wednesday work day (true/false)"}, {"_id": "260", "path": "isThursdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is thursday work day (true/false)"}, {"_id": "270", "path": "isFridayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is friday work day (true/false)"}, {"_id": "280", "path": "isSaturdayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is saturday work day (false/true)"}, {"_id": "290", "path": "isSundayWorkDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is sunday work day (false/true)"}, {"_id": "300", "path": "taxZone", "locale": "", "dataType": "reference", "isCustom": false, "description": "tax zone (#id)"}, {"_id": "310", "path": "#items", "locale": "", "dataType": "collection", "isCustom": false, "description": "items"}, {"_id": "320", "path": "isActive#2", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "330", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "340", "path": "id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "350", "path": "name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "360", "path": "*salesUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales unit (#id)"}, {"_id": "370", "path": "*salesUnitToStockUnitConversion", "locale": "", "dataType": "decimal", "isCustom": false, "description": "sales unit to stock unit conversion"}, {"_id": "380", "path": "minimumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum sales quantity"}, {"_id": "390", "path": "maximumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "maximum sales quantity"}, {"_id": "400", "path": "/intacctCustomer", "locale": "", "dataType": "reference", "isCustom": false, "description": "intacct customer (#parent)"}, {"_id": "410", "path": "*integration", "locale": "", "dataType": "reference", "isCustom": false, "description": "integration (#id)"}, {"_id": "420", "path": "*node", "locale": "", "dataType": "reference", "isCustom": false, "description": "node (#name)"}, {"_id": "430", "path": "sysId", "locale": "", "dataType": "string", "isCustom": false, "description": "sys id"}, {"_id": "440", "path": "state", "locale": "", "dataType": "enum(not,pending,success,error,desynchronized)", "isCustom": false, "description": "state"}, {"_id": "450", "path": "url", "locale": "", "dataType": "string", "isCustom": false, "description": "url"}, {"_id": "460", "path": "lastMessage", "locale": "", "dataType": "string", "isCustom": false, "description": "last message"}, {"_id": "470", "path": "creationStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "creation stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "480", "path": "updateStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "update stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "490", "path": "version", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "500", "path": "difference", "locale": "", "dataType": "json", "isCustom": false, "description": "difference"}, {"_id": "510", "path": "intacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "520", "path": "recordNo", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}]}