{"data": [{"_id": "0", "path": "!_sortValue", "locale": "", "dataType": "integer", "isCustom": false, "description": "sort value"}, {"_id": "10", "path": "site", "locale": "", "dataType": "reference", "isCustom": false, "description": "site (#id)"}, {"_id": "20", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "30", "path": "*supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "40", "path": "unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "unit (#id)"}, {"_id": "50", "path": "date<PERSON><PERSON>d", "locale": "", "dataType": "date<PERSON><PERSON><PERSON>", "isCustom": false, "description": "date valid"}, {"_id": "60", "path": "*currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "70", "path": "price", "locale": "", "dataType": "decimal", "isCustom": false, "description": "price"}, {"_id": "80", "path": "type", "locale": "", "dataType": "enum(normal,specialOffer,discount)", "isCustom": false, "description": "type"}, {"_id": "90", "path": "priority", "locale": "", "dataType": "integer", "isCustom": false, "description": "priority"}, {"_id": "100", "path": "fromQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "from quantity"}, {"_id": "110", "path": "toQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "to quantity"}]}