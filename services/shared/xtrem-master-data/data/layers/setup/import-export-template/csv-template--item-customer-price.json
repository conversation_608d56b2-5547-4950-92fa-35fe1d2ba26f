{"data": [{"_id": "0", "path": "!customer", "locale": "", "dataType": "reference", "isCustom": false, "description": "customer (#businessEntity)"}, {"_id": "10", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "20", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "30", "path": "!priceReason", "locale": "", "dataType": "reference", "isCustom": false, "description": "price reason (#id)"}, {"_id": "40", "path": "!fromQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "from quantity"}, {"_id": "50", "path": "!toQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "to quantity"}, {"_id": "60", "path": "!endDate", "locale": "", "dataType": "date", "isCustom": false, "description": "end date (YYYY-MM-DD)"}, {"_id": "70", "path": "!salesSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales site (#id)"}, {"_id": "80", "path": "!stockSite", "locale": "", "dataType": "reference", "isCustom": false, "description": "stock site (#id)"}, {"_id": "90", "path": "!unit", "locale": "", "dataType": "reference", "isCustom": false, "description": "unit (#id)"}, {"_id": "100", "path": "*price", "locale": "", "dataType": "decimal", "isCustom": false, "description": "price"}, {"_id": "110", "path": "!currency", "locale": "", "dataType": "reference", "isCustom": false, "description": "currency (#id)"}, {"_id": "120", "path": "discount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "discount"}, {"_id": "130", "path": "charge", "locale": "", "dataType": "decimal", "isCustom": false, "description": "charge"}, {"_id": "140", "path": "!startDate", "locale": "", "dataType": "date", "isCustom": false, "description": "start date (YYYY-MM-DD)"}]}