{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "*name", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "isFullWeek", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is full week (false/true)"}, {"_id": "30", "path": "mondayShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "monday shift (#id)"}, {"_id": "40", "path": "tuesdayShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "tuesday shift (#id)"}, {"_id": "50", "path": "wednesdayShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "wednesday shift (#id)"}, {"_id": "60", "path": "thursdayShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "thursday shift (#id)"}, {"_id": "70", "path": "fridayShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "friday shift (#id)"}, {"_id": "80", "path": "saturdayShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "saturday shift (#id)"}, {"_id": "90", "path": "sundayShift", "locale": "", "dataType": "reference", "isCustom": false, "description": "sunday shift (#id)"}]}