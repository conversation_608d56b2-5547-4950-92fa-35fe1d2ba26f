{"data": [{"_id": "0", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "10", "path": "!businessEntity", "locale": "", "dataType": "reference", "isCustom": false, "description": "business entity (#id)"}, {"_id": "20", "path": "primaryAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "primary address (#businessEntity|_sortValue)"}, {"_id": "30", "path": "supplierType", "locale": "", "dataType": "enum(chemical,foodAndBeverage,other)", "isCustom": false, "description": "supplier type"}, {"_id": "40", "path": "standardIndustrialClassification", "locale": "", "dataType": "reference", "isCustom": false, "description": "standard industrial classification (#sicCode)"}, {"_id": "50", "path": "*paymentTerm", "locale": "", "dataType": "reference", "isCustom": false, "description": "payment term (#id)"}, {"_id": "60", "path": "paymentMethod", "locale": "", "dataType": "string", "isCustom": false, "description": "payment method"}, {"_id": "70", "path": "parent", "locale": "", "dataType": "reference", "isCustom": false, "description": "parent (#businessEntity)"}, {"_id": "80", "path": "incoterm", "locale": "", "dataType": "reference", "isCustom": false, "description": "incoterm (#id)"}, {"_id": "90", "path": "minimumOrderAmount", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum order amount"}, {"_id": "100", "path": "billBySupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by supplier (#businessEntity)"}, {"_id": "110", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "bill by address (#businessEntity|_sortValue)"}, {"_id": "120", "path": "payToSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay to supplier (#businessEntity)"}, {"_id": "130", "path": "payToAddress", "locale": "", "dataType": "reference", "isCustom": false, "description": "pay to address (#businessEntity|_sortValue)"}, {"_id": "140", "path": "returnToSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "return to supplier (#businessEntity)"}, {"_id": "150", "path": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locale": "", "dataType": "reference", "isCustom": false, "description": "return to address (#businessEntity|_sortValue)"}, {"_id": "160", "path": "deliveryMode", "locale": "", "dataType": "reference", "isCustom": false, "description": "delivery mode (#id)"}, {"_id": "170", "path": "internalNote", "locale": "", "dataType": "textStream", "isCustom": false, "description": "internal note"}, {"_id": "180", "path": "category", "locale": "", "dataType": "reference", "isCustom": false, "description": "category (#id)"}, {"_id": "190", "path": "postingClass", "locale": "", "dataType": "reference", "isCustom": false, "description": "posting class (#id)"}, {"_id": "200", "path": "defaultBuyer", "locale": "", "dataType": "reference", "isCustom": false, "description": "default buyer (#email)"}, {"_id": "210", "path": "#certificates", "locale": "", "dataType": "collection", "isCustom": false, "description": "certificates"}, {"_id": "220", "path": "*standard", "locale": "", "dataType": "reference", "isCustom": false, "description": "standard (#id)"}, {"_id": "230", "path": "*id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "240", "path": "dateOfCertification", "locale": "", "dataType": "date", "isCustom": false, "description": "date of certification (YYYY-MM-DD)"}, {"_id": "250", "path": "validUntil", "locale": "", "dataType": "date", "isCustom": false, "description": "valid until (YYYY-MM-DD)"}, {"_id": "260", "path": "certificationBody", "locale": "", "dataType": "string", "isCustom": false, "description": "certification body"}, {"_id": "270", "path": "dateOfOriginalCertification", "locale": "", "dataType": "date", "isCustom": false, "description": "date of original certification (YYYY-MM-DD)"}, {"_id": "280", "path": "#items", "locale": "", "dataType": "collection", "isCustom": false, "description": "items"}, {"_id": "290", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "300", "path": "supplierItemCode", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier item code"}, {"_id": "310", "path": "supplierItemName", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier item name"}, {"_id": "320", "path": "supplierPriority", "locale": "", "dataType": "integer", "isCustom": false, "description": "supplier priority"}, {"_id": "330", "path": "isDefaultItemSupplier", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is default item supplier (false/true)"}, {"_id": "340", "path": "*purchaseUnitOfMeasure", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase unit of measure (#id)"}, {"_id": "350", "path": "minimumPurchaseQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum purchase quantity"}, {"_id": "360", "path": "purchaseLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "purchase lead time"}, {"_id": "370", "path": "isActive#1", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "380", "path": "/intacctSupplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "intacct supplier (#parent)"}, {"_id": "390", "path": "*integration", "locale": "", "dataType": "reference", "isCustom": false, "description": "integration (#id)"}, {"_id": "400", "path": "*node", "locale": "", "dataType": "reference", "isCustom": false, "description": "node (#name)"}, {"_id": "410", "path": "sysId", "locale": "", "dataType": "string", "isCustom": false, "description": "sys id"}, {"_id": "420", "path": "state", "locale": "", "dataType": "enum(not,pending,success,error,desynchronized)", "isCustom": false, "description": "state"}, {"_id": "430", "path": "url", "locale": "", "dataType": "string", "isCustom": false, "description": "url"}, {"_id": "440", "path": "lastMessage", "locale": "", "dataType": "string", "isCustom": false, "description": "last message"}, {"_id": "450", "path": "creationStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "creation stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "460", "path": "updateStamp", "locale": "", "dataType": "datetime", "isCustom": false, "description": "update stamp (YYYY-MM-DD[T]HH:mm:ssZ)"}, {"_id": "470", "path": "version", "locale": "", "dataType": "integer", "isCustom": false, "description": "version"}, {"_id": "480", "path": "difference", "locale": "", "dataType": "json", "isCustom": false, "description": "difference"}, {"_id": "490", "path": "intacctId", "locale": "", "dataType": "string", "isCustom": false, "description": "intacct id"}, {"_id": "500", "path": "recordNo", "locale": "", "dataType": "integer", "isCustom": false, "description": "record no"}]}