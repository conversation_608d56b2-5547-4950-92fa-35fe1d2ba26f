{"data": [{"_id": "0", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (false/true)"}, {"_id": "10", "path": "*title", "locale": "", "dataType": "enum(ms,mr,dr,mrs)", "isCustom": false, "description": "title"}, {"_id": "20", "path": "*firstName", "locale": "", "dataType": "string", "isCustom": false, "description": "first name"}, {"_id": "30", "path": "*lastName", "locale": "", "dataType": "string", "isCustom": false, "description": "last name"}, {"_id": "40", "path": "preferredName", "locale": "", "dataType": "string", "isCustom": false, "description": "preferred name"}, {"_id": "50", "path": "role", "locale": "", "dataType": "enum(mainContact,commercialContact,financialContact)", "isCustom": false, "description": "role"}, {"_id": "60", "path": "position", "locale": "", "dataType": "string", "isCustom": false, "description": "position"}, {"_id": "70", "path": "locationPhoneNumber", "locale": "", "dataType": "string", "isCustom": false, "description": "location phone number"}, {"_id": "80", "path": "email", "locale": "", "dataType": "string", "isCustom": false, "description": "email"}, {"_id": "90", "path": "!company", "locale": "", "dataType": "reference", "isCustom": false, "description": "company (#id)"}, {"_id": "100", "path": "*address", "locale": "", "dataType": "reference", "isCustom": false, "description": "address (#company|_sortValue)"}, {"_id": "110", "path": "isPrimary", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is primary (false/true)"}]}