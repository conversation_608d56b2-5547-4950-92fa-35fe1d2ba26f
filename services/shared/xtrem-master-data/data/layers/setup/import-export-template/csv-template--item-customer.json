{"data": [{"_id": "0", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}, {"_id": "10", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "20", "path": "!customer", "locale": "", "dataType": "reference", "isCustom": false, "description": "customer (#businessEntity)"}, {"_id": "30", "path": "id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "40", "path": "name", "locale": "", "dataType": "string", "isCustom": false, "description": "name"}, {"_id": "50", "path": "*salesUnit", "locale": "", "dataType": "reference", "isCustom": false, "description": "sales unit (#id)"}, {"_id": "60", "path": "*salesUnitToStockUnitConversion", "locale": "", "dataType": "decimal", "isCustom": false, "description": "sales unit to stock unit conversion"}, {"_id": "70", "path": "minimumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum sales quantity"}, {"_id": "80", "path": "maximumSalesQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "maximum sales quantity"}]}