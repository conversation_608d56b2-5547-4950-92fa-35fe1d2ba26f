{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "*name", "locale": "en-US", "dataType": "localized text", "isCustom": false, "description": "\"default locale: en-US, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-US, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "isCustomer", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is customer (false/true)"}, {"_id": "30", "path": "isSupplier", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is supplier (true/false)"}]}