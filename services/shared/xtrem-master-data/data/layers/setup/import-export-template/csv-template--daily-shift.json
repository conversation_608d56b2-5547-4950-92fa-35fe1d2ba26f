{"data": [{"_id": "0", "path": "!id", "locale": "", "dataType": "string", "isCustom": false, "description": "id"}, {"_id": "10", "path": "name", "locale": "base", "dataType": "localized text", "isCustom": false, "description": "\"default locale: base, other locales can be specified with (locale-name), for example \"\"name (de-DE)\"\". Available locales are (en-GB, fr-FR, ...). You can also duplicate the columns to import several translations\""}, {"_id": "20", "path": "isFullDay", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is full day (false/true)"}, {"_id": "30", "path": "#shiftDetails", "locale": "", "dataType": "collection", "isCustom": false, "description": "shift details"}, {"_id": "40", "path": "*shiftDetail", "locale": "", "dataType": "reference", "isCustom": false, "description": "shift detail (#id)"}]}