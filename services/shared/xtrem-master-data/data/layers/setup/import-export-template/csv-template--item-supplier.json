{"data": [{"_id": "0", "path": "!item", "locale": "", "dataType": "reference", "isCustom": false, "description": "item (#id)"}, {"_id": "10", "path": "!supplier", "locale": "", "dataType": "reference", "isCustom": false, "description": "supplier (#businessEntity)"}, {"_id": "20", "path": "supplierItemCode", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier item code"}, {"_id": "30", "path": "supplierItemName", "locale": "", "dataType": "string", "isCustom": false, "description": "supplier item name"}, {"_id": "40", "path": "supplierPriority", "locale": "", "dataType": "integer", "isCustom": false, "description": "supplier priority"}, {"_id": "50", "path": "isDefaultItemSupplier", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is default item supplier (false/true)"}, {"_id": "60", "path": "*purchaseUnitOfMeasure", "locale": "", "dataType": "reference", "isCustom": false, "description": "purchase unit of measure (#id)"}, {"_id": "70", "path": "minimumPurchaseQuantity", "locale": "", "dataType": "decimal", "isCustom": false, "description": "minimum purchase quantity"}, {"_id": "80", "path": "purchaseLeadTime", "locale": "", "dataType": "integer", "isCustom": false, "description": "purchase lead time"}, {"_id": "90", "path": "isActive", "locale": "", "dataType": "boolean", "isCustom": false, "description": "is active (true/false)"}]}