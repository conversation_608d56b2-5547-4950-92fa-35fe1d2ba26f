"id";"business_entity";"is_finance";"is_purchase";"is_inventory";"is_sales";"is_manufacturing";"is_project_management";"primary_address";"financial_site";"is_location_managed";"default_location";"sequence_number_id";"time_zone"
"US001";"US001";"Y";"N";"N";"N";"N";"N";"US001|10";;"N";;;"America/New_York"
"US002";"US002";"Y";"Y";"Y";"Y";"Y";"N";"US002|10";;"N";;;"America/Los_Angeles"
"US003";"US003";"Y";"Y";"Y";"Y";"Y";"Y";"US003|10";;"N";;;"America/Chicago"
"US004";"US004";"Y";"Y";"Y";"Y";"Y";"Y";"US004|10";;"N";;;"America/Chicago"
"US005";"US005";"Y";"Y";"Y";"Y";"Y";"Y";"US005|10";;"N";;;"America/Los_Angeles"
"US006";"US006";"Y";"Y";"Y";"Y";"Y";"Y";"US006|10";;"N";;;"America/New_York"
"ETS1-S01";"ETS1-S01";"Y";"Y";"Y";"Y";"N";"Y";"ETS1-S01|10";;"N";;;"Europe/Paris"
"DEP1-S01";"DEP1-S01";"N";"Y";"Y";"Y";"Y";"Y";"DEP1-S01|10";"ETS1-S01";"N";;"ED";"Europe/Paris"
"ETS2-S01";"ETS2-S01";"Y";"Y";"Y";"Y";"Y";"Y";"ETS2-S01|10";;"N";;;"Europe/Paris"
"ETS1-S02";"ETS1-S02";"Y";"Y";"Y";"Y";"Y";"Y";"ETS1-S02|10";;"N";;;"Europe/Paris"
"ETS2-S02";"ETS2-S02";"Y";"Y";"Y";"Y";"Y";"Y";"ETS2-S02|10";;"N";;;"Europe/Paris"
"DEP1-S02";"DEP1-S02";"Y";"Y";"Y";"Y";"Y";"Y";"DEP1-S02|10";;"N";;;"Europe/Paris"
"ETS1-S03";"ETS1-S03";"Y";"Y";"Y";"Y";"Y";"Y";"ETS1-S03|10";;"N";;;"Europe/Paris"
"ETS2-S03";"ETS2-S03";"Y";"Y";"Y";"Y";"Y";"Y";"ETS2-S03|10";;"N";;;"Europe/Paris"
"500";"500";"Y";"Y";"Y";"Y";"N";"Y";"500|10";;"N";;;"Europe/London"
"501";"501";"N";"Y";"Y";"Y";"Y";"Y";"501|10";"500";"N";;"SW";"Europe/London"
"100";"100";"Y";"Y";"Y";"Y";"N";"Y";"100|10";;"N";;"TE";
"110";"110";"N";"Y";"Y";"Y";"Y";"N";"110|10";"100";"N";;"TH";"America/New_York"
"700";"700";"Y";"Y";"Y";"Y";"Y";"N";"700|10";;"Y";"BULK01|700|ZAStorage";"ZA";"Africa/Johannesburg"
"D1S";"D1S";"Y";"Y";"Y";"Y";"Y";"N";"D1S|10";;"Y";"BULK01|D1S|DEStorage";"DE";"Europe/Berlin"
"CHA-S01";"CHA-S01";"N";"Y";"Y";"Y";"Y";"Y";"CHA-S01|10";"ETS1-S01";"Y";;;"Europe/Paris"
"FCY01";"FCY01";"Y";"Y";"Y";"Y";"Y";"Y";"FCY01|10";;"N";;"F0";"America/Chicago"
"RUM-S01";"RUM-S01";"N";"Y";"Y";"Y";"Y";"Y";"RUM-S01|10";"ETS1-S01";"Y";;;"Europe/Paris"
"ZS01";"ZS01";"Y";"Y";"Y";"Y";"Y";"Y";"ZS01|10";;"N";;"Z4";"Africa/Johannesburg"
"ZS02";"ZS02";"Y";"Y";"Y";"Y";"Y";"Y";"ZS02|10";;"N";;"Z5";"Africa/Johannesburg"
"ZS03";"ZS03";"Y";"Y";"Y";"Y";"Y";"Y";"ZS03|10";;"N";;"Z6";"Africa/Johannesburg"
"350";"350";"Y";"Y";"Y";"Y";"Y";"Y";"350|10";;"N";;;
"450";"450";"N";"Y";"Y";"Y";"Y";"Y";"450|10";"350";"N";;;
"800";"800";"N";"Y";"Y";"Y";"Y";"Y";"800|10";"510";"N";;;
"510";"510";"Y";"Y";"Y";"Y";"Y";"Y";"510|10";;"N";;;
"122";"122";"Y";"Y";"Y";"Y";"Y";"Y";"122|10";;"N";;;
"61";"61";"N";"Y";"Y";"Y";"Y";"Y";"61|10";"122";"N";;;
"600";"600";"Y";"Y";"Y";"Y";"Y";"Y";"600|20";;"N";;"AU";"Australia/Sydney"
"STO-S01";"STO-S01";"N";"Y";"Y";"Y";"Y";"Y";"STO-S01|10";"ETS1-S01";"Y";;;"Europe/Paris"
