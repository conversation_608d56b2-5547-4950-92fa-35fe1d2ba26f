"id";"business_entity";"is_finance";"is_purchase";"is_inventory";"is_sales";"is_manufacturing";"is_project_management";"primary_address";"financial_site";"is_location_managed";"default_location";"sequence_number_id";"time_zone"
"500";"500";"Y";"Y";"Y";"Y";"Y";"Y";"500|800";;"N";;"Z1";
"700";"700";"Y";"Y";"Y";"Y";"Y";"Y";"700|1100";;"N";;"Z2";
"US001";"US001";"Y";"Y";"Y";"Y";"Y";"Y";"US001|7100";;"Y";"LOC1|US001|Loading dock";"E1";"America/Chicago"
"US002";"US002";"Y";"Y";"Y";"Y";"Y";"Y";"US002|7200";;"Y";"LOC7|US002|Storage dock";"E2";"America/Chicago"
"US003";"US003";"Y";"Y";"Y";"Y";"Y";"Y";"US003|7300";;"Y";"LOC11|US003|Production line";"E3";"America/Chicago"
"US004";"US004";"Y";"Y";"Y";"Y";"Y";"Y";"US004|7400";;"N";;"E4";"America/Chicago"
"US005";"US005";"Y";"Y";"Y";"Y";"Y";"Y";"US005|7500";;"N";"LOC1|US005|South street warehouse";"E5";"America/Los_Angeles"
"US006";"US006";"Y";"Y";"Y";"Y";"Y";"Y";"US006|7600";;"N";;"E6";"America/New_York"
"US007";"US007";"Y";"Y";"Y";"Y";"Y";"Y";"US007|7700";;"N";;"E7";"America/Denver"
"US008";"US008";"Y";"Y";"Y";"Y";"Y";"Y";"US008|7800";;"N";;"E8";"America/Los_Angeles"
"US009";"US009";"Y";"Y";"Y";"Y";"Y";"Y";"US009|7900";;"N";;"E9";"America/New_York"
"US010";"US010";"N";"Y";"Y";"Y";"Y";"Y";"US010|8000";"US001";"N";;"EA";"America/Chicago"
"US011";"US011";"Y";"Y";"Y";"Y";"Y";"Y";"US011|19500";;"N";;"EB";"America/Chicago"
"US012";"US012";"Y";"Y";"N";"Y";"Y";"Y";"US012|19600";;"N";;"EC";"America/New_York"
"US013";"US013";"Y";"Y";"Y";"Y";"Y";"Y";"US013|19700";;"N";;"ED";"America/New_York"
"ETS1-S01";"ETS1-S01";"Y";"Y";"Y";"Y";"Y";"Y";"ETS1-S01|510100";;"Y";;"EE";"Europe/Paris"
"DEP1-S01";"DEP1-S01";"Y";"Y";"Y";"Y";"Y";"Y";"DEP1-S01|510200";;"N";;"EF";"Europe/Paris"
"ETS2-S01";"ETS2-S01";"Y";"Y";"Y";"Y";"Y";"Y";"ETS2-S01|120300";;"N";;"EG";"Europe/Paris"
"ETS1-S02";"ETS1-S02";"Y";"Y";"Y";"Y";"Y";"Y";"ETS1-S02|520100";;"N";;"EH";"Europe/Paris"
"ETS2-S02";"ETS2-S02";"Y";"Y";"Y";"Y";"Y";"Y";"DEP1-S01|510200";;"N";;"EI";"Europe/Paris"
"DEP1-S02";"DEP1-S02";"Y";"Y";"Y";"Y";"Y";"Y";"ETS2-S01|510300";;"N";;"EJ";"Europe/Paris"
"ETS1-S03";"ETS1-S03";"Y";"Y";"Y";"Y";"Y";"Y";"ETS1-S03|530100";;"N";;"EK";"Europe/Paris"
"ETS2-S03";"ETS2-S03";"Y";"Y";"Y";"Y";"Y";"Y";"ETS2-S03|530200";;"N";;"EL";"Europe/Paris"
"ETS01-S04";"ETS01-S04";"Y";"Y";"Y";"Y";"Y";"Y";"ETS01-S04|840100";;"N";;"EM";
"UnitTest";"UnitTest";"Y";"Y";"Y";"Y";"Y";"Y";"UnitTest|19800";;"N";;"EN";
"US015";"US015";"Y";"Y";"Y";"Y";"Y";"Y";"US015|19900";;"N";;"EO";"America/Los_Angeles"
"US016";"US016";"Y";"Y";"Y";"Y";"Y";"Y";"US016|100";;"N";;"EP";
"US017";"US017";"Y";"Y";"Y";"Y";"Y";"Y";"US017|1300";;"N";;"EQ";
"US018";"US018";"Y";"Y";"Y";"Y";"Y";"Y";"US018|1400";;"N";;"ER";
"US019";"US019";"Y";"Y";"Y";"Y";"Y";"Y";"US019|1500";;"N";;"ES";
"US020";"US020";"Y";"Y";"Y";"Y";"Y";"Y";"US020|1600";;"N";;"ET";
"US021";"US021";"Y";"Y";"Y";"Y";"Y";"Y";"US021|1700";;"N";;"EU";
"US022";"US022";"Y";"Y";"Y";"Y";"Y";"Y";"US022|1800";;"N";;"EV";
"US023";"US023";"Y";"Y";"N";"Y";"Y";"Y";"US023|1900";;"N";;"EW";
"US024";"US024";"Y";"Y";"Y";"Y";"Y";"Y";"US024|2000";;"N";;"EX";
"CAS01";"CAS01";"Y";"Y";"Y";"Y";"Y";"Y";"CAS01|10";;"N";;"CA";"America/Toronto"
"RX001";"RX001";"Y";"Y";"Y";"Y";"Y";"Y";"RX001|10";;"N";;;"Africa/Johannesburg"
"RX002";"RX002";"Y";"Y";"Y";"Y";"Y";"Y";"RX002|10";;"Y";;;"Africa/Johannesburg"
"RX003";"RX003";"Y";"Y";"Y";"Y";"Y";"Y";"US024|2000";;"Y";;;"Africa/Johannesburg"
"RX004";"RX004";"Y";"Y";"Y";"Y";"Y";"Y";"CAS01|10";;"Y";;;"Africa/Johannesburg"
"RX005";"RX005";"Y";"Y";"Y";"Y";"Y";"Y";"RX001|10";;"Y";;;
"RX006";"RX006";"Y";"Y";"Y";"Y";"Y";"Y";"RX002|10";;"Y";;;
"UnitTestActive";"UnitTestActive";"Y";"Y";"Y";"Y";"Y";"Y";"UnitTest|19800";;"N";;"EN";
"CAS02";"CAS02";"N";"Y";"Y";"Y";"Y";"Y";"CAS02|10";"CAS01";"N";;;
"DES01";"DES01";"Y";"Y";"Y";"Y";"Y";"Y";"DES01|10";;"N";;"G1";
"LECLERC";"LECLERC";"Y";"Y";"Y";"Y";"Y";"Y";"LECLERC|1200";;"N";;"EZ";
"RM_USFCY_01";"RM_USFCY_01";"Y";"Y";"Y";"Y";"Y";"Y";"RM_USFCY_01|10";;"N";;"1R";
