import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { BaseLineTax } from './base-line-tax';

@decorators.subNode<DocumentLineTax>({
    extends: () => BaseLineTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class DocumentLineTax extends BaseLineTax {
    @decorators.referenceProperty<DocumentLineTax, 'line'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.BaseDocumentItemLine,
    })
    readonly line: Reference<xtremMasterData.nodes.BaseDocumentItemLine>;

    @decorators.referencePropertyOverride<DocumentLineTax, 'currency'>({
        dependsOn: ['line'],
        async getValue() {
            return (await (await this.line).document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
