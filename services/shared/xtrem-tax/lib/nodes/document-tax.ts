import type { Reference } from '@sage/xtrem-core';
import { decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { BaseTax } from './base-tax';

@decorators.subNode<DocumentTax>({
    extends: () => BaseTax,
    isPublished: true,
    isVitalCollectionChild: true,
    canSearch: true,
    canRead: true,
})
export class DocumentTax extends BaseTax {
    @decorators.referenceProperty<DocumentTax, 'document'>({
        isStored: true,
        isPublished: true,
        isVitalParent: true,
        node: () => xtremMasterData.nodes.BaseDocument,
    })
    readonly document: Reference<xtremMasterData.nodes.BaseDocument>;

    @decorators.referencePropertyOverride<DocumentTax, 'currency'>({
        dependsOn: [{ document: ['currency'] }],
        async getValue() {
            return (await this.document).currency;
        },
    })
    override readonly currency: Reference<xtremMasterData.nodes.Currency>;
}
