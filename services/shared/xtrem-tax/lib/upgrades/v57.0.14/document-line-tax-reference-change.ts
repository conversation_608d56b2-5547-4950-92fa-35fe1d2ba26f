import { CustomSqlAction } from '@sage/xtrem-system';

export const documentLineTaxReferenceUpdate = new CustomSqlAction({
    description:
        'Dummy script to update the line reference on DocumentLineTax that still point to a BaseDocumentItemLine',
    fixes: {
        notNullableColumns: [{ table: 'document_line_tax', column: 'line' }],
    },
    body: async helper => {
        await helper.executeSql(`
        DO $$
          BEGIN
          -- Intentionally left blank
          END
        $$;
        `);
    },
});
