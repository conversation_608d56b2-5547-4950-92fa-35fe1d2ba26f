import { CustomSqlAction } from '@sage/xtrem-system';

export const documentTaxReferenceUpdate = new CustomSqlAction({
    description: 'Dummy script to update the document reference on DocumentTax that still point to a BaseDocument',
    fixes: {
        notNullableColumns: [{ table: 'document_tax', column: 'document' }],
    },
    body: async helper => {
        await helper.executeSql(`
        DO $$
          BEGIN
          -- Intentionally left blank
          END
        $$;
        `);
    },
});
