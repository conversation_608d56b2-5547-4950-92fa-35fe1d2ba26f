import * as masterDataUtils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { GraphApi } from '@sage/xtrem-technical-data-api';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.page<BillOfMaterialSaveAsRevisionDialog>({
    title: 'Save as revision',
    module: 'technical-data',
    mode: 'default',
    node: '@sage/xtrem-technical-data/BillOfMaterialRevision',

    businessActions() {
        return [this.cancel, this.addRevision];
    },

    async onLoad() {
        this.bomId = this.$.queryParameters.bomId as string;
        this.bomRevisionSequenceId = this.$.queryParameters.bomRevisionSequenceId as string;
        this.status.value = 'inDevelopment';
        const nextRevision = await this.$.graph
            .node('@sage/xtrem-technical-data/BillOfMaterialRevision')
            .queries.getNextRevision({ revision: true, startDate: true }, { billOfMaterial: this.bomId })
            .execute();

        this.revision.value = nextRevision.revision;
        this.startDate.value = nextRevision.startDate;
        this.minStartDate = new Date(nextRevision.startDate);
    },

    onError(error: string | (Error & { errors: Array<any> })) {
        return masterDataUtils.formatError(this, error);
    },
})
export class BillOfMaterialSaveAsRevisionDialog extends ui.Page<GraphApi> {
    bomId: string;

    bomRevisionSequenceId: string;

    minStartDate: Date;

    @ui.decorators.pageAction<BillOfMaterialSaveAsRevisionDialog>({
        title: 'Add revision',
        buttonType: 'primary',
        async onClick() {
            const results = await this.$.page.validate();
            if (results.length > 0) {
                this.$.showToast(results.join('\n\n'), { type: 'error' });
                return;
            }
            this.$.finish({
                name: this.name.value,
                description: this.description.value,
                startDate: this.startDate.value,
                status: this.status.value,
                // if a BOM revision sequence is used, the revision number is not required
                // it will be calculated at save time
                ...(this.bomRevisionSequenceId ? {} : { revision: this.revision.value }),
            });
        },
    })
    addRevision: ui.PageAction;

    @ui.decorators.pageAction<BillOfMaterialSaveAsRevisionDialog>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<BillOfMaterialSaveAsRevisionDialog>({
        isTitleHidden: true,
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<BillOfMaterialSaveAsRevisionDialog>({
        isTitleHidden: true,
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    informationBlock: ui.containers.Block;

    @ui.decorators.textField<BillOfMaterialSaveAsRevisionDialog>({
        parent() {
            return this.informationBlock;
        },
        title: 'Revision name',
    })
    name: ui.fields.Text;

    @ui.decorators.textField<BillOfMaterialSaveAsRevisionDialog>({
        parent() {
            return this.informationBlock;
        },
        isMandatory: true,
        title: 'Revision number',
        isDisabled() {
            return this.bomRevisionSequenceId !== '';
        },
    })
    revision: ui.fields.Text;

    @ui.decorators.textField<BillOfMaterialSaveAsRevisionDialog>({
        parent() {
            return this.informationBlock;
        },
        title: 'Revision description',
        width: 'large',
    })
    description: ui.fields.Text;

    @ui.decorators.dropdownListField<BillOfMaterialSaveAsRevisionDialog>({
        parent() {
            return this.informationBlock;
        },
        title: 'Status',
        optionType: '@sage/xtrem-technical-data/Availability',
        options: ['inDevelopment', 'released'],
        isReadOnly: true,
    })
    status: ui.fields.DropdownList;

    @ui.decorators.dateField<BillOfMaterialSaveAsRevisionDialog>({
        parent() {
            return this.informationBlock;
        },
        title: 'Start date',
        isMandatory: true,
        minDate() {
            return this.minStartDate;
        },
    })
    startDate: ui.fields.Date;
}
