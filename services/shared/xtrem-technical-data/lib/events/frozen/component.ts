import type * as xtremTechnicalData from '../..';

export async function isComponentPropertyFrozen(component: xtremTechnicalData.nodes.Component): Promise<boolean> {
    // if the BOM item is not revision managed, the component is not frozen
    const billOfMaterial = await component.billOfMaterial;
    if (!(await (await billOfMaterial.item).isBomRevisionManaged)) return false;

    // if the modification concerns a BOM which is/was inDevelopment the component is not frozen
    const bomRevision = (await component.revision) ?? (await component.billOfMaterial);
    if ((await bomRevision.status) === 'inDevelopment') return false;
    const oldBomRevision = await bomRevision.$.old;
    if (!oldBomRevision || (await oldBomRevision.status) === 'inDevelopment') return false;

    return true;
}
