import { date, Test } from '@sage/xtrem-core';
import { assert } from 'chai';
import * as xtremTechnicalData from '../../../index';

/*
------------------------------------------------------------------------------------------------------------------------------
                           2025-05-31 ǁ 2025-06-01                         2025-06-30 ǁ 2025-07-01
           initial BOM                ǁ           intermediate revision               ǁ                last revision
                                      ǁ                                               ǁ
------------------------------------------------------------------------------------------------------------------------------
5 <USER> <GROUP>:          ^            ^                      ^                        ^                    ^
                        [0]          [1]                    [2]                      [3]                  [4]
                    2025-05-01    2025-06-01            2025-06-11               2025-07-01           2025-07-31
*/

const initialBomValidTo = '2025-05-31';
const intermediateRevisionStartDate = '2025-06-01';
const lastRevisionStartDate = '2025-07-01';
const billOfMaterial = { validTo: date.parse(initialBomValidTo) };
const revisions: { startDate: date; validTo: date | null }[] = [
    { startDate: date.parse(intermediateRevisionStartDate), validTo: date.parse(lastRevisionStartDate).addDays(-1) },
    { startDate: date.parse(lastRevisionStartDate), validTo: null },
];
const todays: string[] = ['2025-05-01', '2025-06-01', '2025-06-11', '2025-07-01', '2025-07-31'];

const initialBomComponent = { revision: null, billOfMaterial };
const intermediateRevisionComponent = { revision: revisions[0], billOfMaterial };
const lastRevisionComponent = { revision: revisions[1], billOfMaterial };

// The different test cases consist in moving 'today' through the timeline and checking the cost date of a component owned by each revision
const testGroups: {
    today: string;
    cases: {
        title: string;
        component: {
            revision: {
                validTo?: date | null;
                startDate: date;
            } | null;
            billOfMaterial: {
                validTo?: date | null;
            };
        };
        expected: date;
    }[];
}[] = [
    {
        today: '2025-06-01',
        cases: [
            {
                title: 'Component of initial BOM without revision',
                component: { revision: null, billOfMaterial: { validTo: null } },
                expected: date.parse('2025-06-01'),
            },
        ],
    },
    {
        today: todays[0],
        cases: [
            {
                title: 'Component of initial BOM and revision exists starting after today',
                component: initialBomComponent,
                expected: date.parse(todays[0]),
            },
            {
                title: 'Component of intermediate revision starting after today',
                component: intermediateRevisionComponent,
                expected: revisions[0].startDate,
            },
            {
                title: 'Component of last revision starting after today',
                component: lastRevisionComponent,
                expected: revisions[1].startDate,
            },
        ],
    },
    {
        today: todays[1],
        cases: [
            {
                title: 'Component of initial BOM and revision exists starting today',
                component: initialBomComponent,
                expected: date.parse(initialBomValidTo),
            },
            {
                title: 'Component of intermediate revision starting today',
                component: intermediateRevisionComponent,
                expected: revisions[0].startDate,
            },
            {
                title: 'Component of last revision starting after today',
                component: lastRevisionComponent,
                expected: revisions[1].startDate,
            },
        ],
    },
    {
        today: todays[2],
        cases: [
            {
                title: 'Component of initial BOM and revision exists starting before today',
                component: initialBomComponent,
                expected: date.parse(initialBomValidTo),
            },
            {
                title: 'Component of intermediate revision starting before today',
                component: intermediateRevisionComponent,
                expected: date.parse(todays[2]),
            },
            {
                title: 'Component of last revision starting after today',
                component: lastRevisionComponent,
                expected: revisions[1].startDate,
            },
        ],
    },
    {
        today: todays[3],
        cases: [
            {
                title: 'Component of initial BOM and revision exists starting before today',
                component: initialBomComponent,
                expected: date.parse(initialBomValidTo),
            },
            {
                title: 'Component of intermediate revision starting before today',
                component: intermediateRevisionComponent,
                expected: revisions[0].startDate,
            },
            {
                title: 'Component of last revision starting today',
                component: lastRevisionComponent,
                expected: date.parse(todays[3]),
            },
        ],
    },
    {
        today: todays[4],
        cases: [
            {
                title: 'Component of initial BOM and revision exists starting before today',
                component: initialBomComponent,
                expected: date.parse(initialBomValidTo),
            },
            {
                title: 'Component of intermediate revision starting before today',
                component: intermediateRevisionComponent,
                expected: revisions[0].startDate,
            },
            {
                title: 'Component of last revision starting before today',
                component: lastRevisionComponent,
                expected: date.parse(todays[4]),
            },
        ],
    },
];

describe('Test of getComponentCostDate', () => {
    testGroups.forEach(testGroup => {
        testGroup.cases.forEach(testCase =>
            it(testCase.title, () =>
                Test.withContext(
                    async () => {
                        assert.deepEqual(
                            await xtremTechnicalData.functions.componentFunctions.getComponentCostDate(
                                testCase.component as any,
                            ),
                            testCase.expected,
                            JSON.stringify({ today: testGroup.today, testCase }, null, 2),
                        );
                    },
                    { today: testGroup.today },
                ),
            ),
        );
    });
});
