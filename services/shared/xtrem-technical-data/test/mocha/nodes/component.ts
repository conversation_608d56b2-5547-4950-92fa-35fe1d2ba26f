import type { Context } from '@sage/xtrem-core';
import { Test } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import { assert } from 'chai';
import * as sinon from 'sinon';
import * as xtremTechnicalData from '../../../lib/index';

describe('Component node - update frozen properties', () => {
    async function testPropertyChange(
        context: Context,
        args: { billOfMaterialId: string; testCase: { propertyName: string; value: any; isRejected: boolean } },
    ) {
        const component = await context.read(
            xtremTechnicalData.nodes.Component,
            {
                _id: args.billOfMaterialId,
            },
            { forUpdate: true },
        );
        if (args.testCase.isRejected) {
            await assert.isRejected(
                component.$.set({ [args.testCase.propertyName]: args.testCase.value }),
                `Component.${args.testCase.propertyName}: cannot set value on frozen property`,
            );
        } else {
            await component.$.set({ [args.testCase.propertyName]: args.testCase.value });
        }
    }

    describe('Update a normal component without error', () => {
        [
            { propertyName: 'name', value: 'new description', isRejected: false },
            { propertyName: 'componentNumber', value: 123, isRejected: false },
            { propertyName: 'lineType', value: 'text', isRejected: false },
            { propertyName: 'item', value: '#STOAVC', isRejected: false },
            { propertyName: 'bomItem', value: '#COST_D|US001', isRejected: false },
            { propertyName: 'unit', value: '#GRAM', isRejected: false },
            { propertyName: 'isFixedLinkQuantity', value: true, isRejected: false },
            { propertyName: 'linkQuantity', value: 159, isRejected: false },
            { propertyName: 'scrapFactor', value: 0.78, isRejected: false },
            { propertyName: 'operation', value: '#CARROT-BOX|US001|10', isRejected: false },
            { propertyName: 'instruction', value: 'test new instruction', isRejected: false },
            { propertyName: 'revision', value: '#COST_REV_C|US001|REV-C', isRejected: true },
        ].forEach(testCase => {
            it(`Try to update ${testCase.propertyName} property - success`, () =>
                Test.withContext(
                    async context => {
                        await testPropertyChange(context, {
                            billOfMaterialId: `#COST_A|US001|10`,
                            testCase,
                        });
                    },
                    { testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption] },
                ));
            it(`Try to update ${testCase.propertyName} property BOM with revision in development - success`, () =>
                Test.withContext(
                    async context => {
                        await testPropertyChange(context, {
                            billOfMaterialId: `#COST_REV_D|US001|10`,
                            testCase,
                        });
                    },
                    { testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption] },
                ));
        });
    });
    describe('Update a text component without error', () => {
        [
            { propertyName: 'name', value: 'new description', isRejected: false },
            { propertyName: 'componentNumber', value: 123, isRejected: false },
            { propertyName: 'lineType', value: 'normal', isRejected: false },
            { propertyName: 'instruction', value: 'test new instruction', isRejected: false },
            { propertyName: 'revision', value: '#COST_REV_C|US001|REV-C', isRejected: true },
        ].forEach(testCase => {
            it(`Try to update ${testCase.propertyName} property - success`, () =>
                Test.withContext(
                    async context => {
                        await testPropertyChange(context, {
                            billOfMaterialId: `#CARROT-BOX|US001|10`,
                            testCase,
                        });
                    },
                    { testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption] },
                ));
            it(`Try to update ${testCase.propertyName} property of a text on revision in development status - success`, () =>
                Test.withContext(
                    async context => {
                        await testPropertyChange(context, {
                            billOfMaterialId: `#COST_REV_D|US001|40`,
                            testCase,
                        });
                    },
                    { testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption] },
                ));
        });
    });
    describe('Update a normal component with revision', () => {
        [
            { propertyName: 'name', value: 'new description', isRejected: false },
            { propertyName: 'componentNumber', value: 123, isRejected: true },
            { propertyName: 'lineType', value: 'text', isRejected: true },
            { propertyName: 'item', value: '#STOAVC', isRejected: true },
            { propertyName: 'bomItem', value: '#COST_D|US001', isRejected: true },
            { propertyName: 'unit', value: '#GRAM', isRejected: true },
            { propertyName: 'isFixedLinkQuantity', value: true, isRejected: true },
            { propertyName: 'linkQuantity', value: 159, isRejected: true },
            { propertyName: 'scrapFactor', value: 0.78, isRejected: true },
            { propertyName: 'operation', value: '#CARROT-BOX|US001|10', isRejected: true },
            { propertyName: 'instruction', value: 'test new instruction', isRejected: false },
            { propertyName: 'revision', value: '#COST_REV_C|US001|REV-C', isRejected: true },
        ].forEach(testCase => {
            it(`Try to update ${testCase.propertyName} property - fail`, () =>
                Test.withContext(
                    async context => {
                        await testPropertyChange(context, {
                            billOfMaterialId: `#COST_REV_A|US001|10`,
                            testCase,
                        });
                    },
                    { testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption] },
                ));
        });
    });
    describe('Update a text component with revision', () => {
        [
            { propertyName: 'name', value: 'new description', isRejected: false },
            { propertyName: 'componentNumber', value: 123, isRejected: false },
            { propertyName: 'lineType', value: 'normal', isRejected: true },
            { propertyName: 'instruction', value: 'test new instruction', isRejected: false },
            { propertyName: 'revision', value: '#COST_REV_C|US001|REV-C', isRejected: true },
        ].forEach(testCase => {
            it(`Try to update ${testCase.propertyName} property - fail`, () =>
                Test.withContext(
                    async context => {
                        await testPropertyChange(context, {
                            billOfMaterialId: `#COST_REV_A|US001|40`,
                            testCase,
                        });
                    },
                    { testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption] },
                ));
        });
    });
});

describe('Component node - add/delete component of a revision', () => {
    ['normal', 'text'].forEach((lineType: xtremTechnicalData.enums.BomLineType) => {
        describe(`Component line type: ${lineType}`, () => {
            [
                { revisionNumber: 'REV-C', status: 'inDevelopment', isRejected: false },
                { revisionNumber: 'REV-B', status: 'suspended', isRejected: lineType === 'normal' },
                { revisionNumber: 'REV-A', status: 'availableToUse', isRejected: lineType === 'normal' },
            ].forEach(testCase => {
                it(`Add component to ${testCase.status} revision - ${testCase.isRejected ? 'fail' : 'success'}`, () =>
                    Test.withContext(
                        async context => {
                            const item = await context.read(xtremMasterData.nodes.Item, {
                                _id: '#STOAVC',
                            });
                            const componentToCreate = context.create(xtremTechnicalData.nodes.Component, {
                                billOfMaterial: `#MODIFY_REVISION|US001`,
                                componentNumber: 123,
                                revision: `#MODIFY_REVISION|US001|${testCase.revisionNumber}`,
                                lineType,
                                ...(lineType === 'normal'
                                    ? { item, unit: await item.stockUnit, linkQuantity: 12 }
                                    : {}),
                            });
                            if (testCase.isRejected) {
                                await assert.isRejected(
                                    (await componentToCreate).$.save(),
                                    'The record was not created.',
                                );
                                assert.deepEqual((await componentToCreate).$.context.diagnoses, [
                                    {
                                        message: 'The BOM revision needs to be in development to add a component.',
                                        path: [],
                                        severity: 3,
                                    },
                                ]);
                            } else {
                                const component = await componentToCreate;
                                await component.$.save();
                                assert.deepEqual(component.$.context.diagnoses, []);
                            }
                        },
                        {
                            testActiveServiceOptions: [
                                xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption,
                            ],
                        },
                    ));

                it(`Delete component to ${testCase.status} revision - ${testCase.isRejected ? 'fail' : 'success'}`, () =>
                    Test.withContext(
                        async context => {
                            const component = await context
                                .query(xtremTechnicalData.nodes.Component, {
                                    filter: {
                                        billOfMaterial: `#MODIFY_REVISION|US001`,
                                        revision: `#MODIFY_REVISION|US001|${testCase.revisionNumber}`,
                                        lineType,
                                    },
                                    first: 1,
                                    forUpdate: true,
                                })
                                .elementAt(0);

                            assert.deepEqual(await component.lineType, lineType);

                            if (testCase.isRejected) {
                                await assert.isRejected(component.$.delete(), 'The record was not deleted.');
                                assert.deepEqual(component.$.context.diagnoses, [
                                    {
                                        message: 'The BOM revision needs to be in development to delete a component.',
                                        path: [],
                                        severity: 3,
                                    },
                                ]);
                            } else {
                                await component.$.delete();
                                assert.deepEqual(component.$.context.diagnoses, []);
                            }
                        },
                        {
                            testActiveServiceOptions: [
                                xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption,
                            ],
                        },
                    ));
            });
        });
    });
});

describe('Component node - get standard cost', () => {
    afterEach(() => {
        sinon.restore();
    });
    it(`Check components standard cost`, () =>
        Test.withContext(
            async context => {
                // if getComponentCostDate is called and its unit tests are successful, we can consider that the current test is enough
                // and we don't need to multiply the number of unit tests on standard costs
                // => add a spy to check getComponentCostDate is called for each component
                const getComponentCostDateSpy = sinon.spy(
                    xtremTechnicalData.functions.componentFunctions,
                    'getComponentCostDate',
                );
                const getQuantityWithScrapSpy = sinon.spy(
                    xtremTechnicalData.sharedFunctions.componentFunctions,
                    'getQuantityWithScrap',
                );

                // Fist, check data is correct
                let itemSite = await context.read(xtremMasterData.nodes.ItemSite, { _id: '#COST_REV_F|US001' });
                let stdCost = await itemSite.stdCostValue;
                assert.deepEqual(stdCost, 1000.0);
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, { _id: '#COST_REV_H|US001' });
                stdCost = await itemSite.stdCostValue;
                assert.deepEqual(stdCost, 22.0);
                itemSite = await context.read(xtremMasterData.nodes.ItemSite, { _id: '#COST_REV_I|US001' });
                stdCost = await itemSite.stdCostValue;
                assert.deepEqual(stdCost, 0);

                const billOfMaterial = await context.read(xtremTechnicalData.nodes.BillOfMaterial, {
                    _id: '#COST_REV_C|US001',
                });

                assert.deepEqual(
                    await billOfMaterial.components
                        .filter(
                            async component =>
                                (await component.item) != null &&
                                (await (await component.revision)?.revision) === 'REV-B',
                        )
                        .map(async component => ({
                            item: await (await component.item)?.id,
                            linkQuantity: await component.linkQuantity,
                            scrapFactor: await component.scrapFactor,
                        }))
                        .toArray(),
                    [
                        { item: 'COST_REV_F', linkQuantity: 10.0, scrapFactor: 15.0 },
                        { item: 'COST_REV_H', linkQuantity: 20.0, scrapFactor: 0.0 },
                        { item: 'COST_REV_I', linkQuantity: 30.0, scrapFactor: 0.0 },
                    ],
                );

                assert.deepEqual(
                    await (
                        await billOfMaterial.revisions.find(async rev => (await rev.revision) === 'REV-B')
                    )?.standardCost,
                    11940.0, // 10*1.15 (scrap) * 1000 (COST_REV_F) + 20 * 22 (COST_REV_H) + 30 * 0 (COST_REV_I)
                );

                sinon.assert.callCount(getComponentCostDateSpy, 3);
                assert.deepEqual(await (await getComponentCostDateSpy.firstCall.args[0].item)?.id, 'COST_REV_F');
                assert.deepEqual(await (await getComponentCostDateSpy.secondCall.args[0].item)?.id, 'COST_REV_H');
                assert.deepEqual(await (await getComponentCostDateSpy.thirdCall.args[0].item)?.id, 'COST_REV_I');

                sinon.assert.callCount(getQuantityWithScrapSpy, 3);
                assert.deepEqual(getQuantityWithScrapSpy.firstCall.returnValue, 11.5);
                assert.deepEqual(getQuantityWithScrapSpy.secondCall.returnValue, 20.0);
                assert.deepEqual(getQuantityWithScrapSpy.thirdCall.returnValue, 30.0);
            },
            { testActiveServiceOptions: [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption] },
        ));
});
