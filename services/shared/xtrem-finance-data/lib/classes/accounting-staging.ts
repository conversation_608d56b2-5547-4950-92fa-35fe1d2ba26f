import type { AsyncResponse, Context, decimal, integer } from '@sage/xtrem-core';
import { asyncArray, date, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '..';

export class AccountingStaging implements xtremFinanceData.interfaces.FinanceNodes.AccountingStagingWithArrays {
    public baseDocumentLine: AsyncResponse<xtremMasterData.nodes.BaseDocumentLine | null>;

    public sourceBaseDocumentLine: AsyncResponse<xtremMasterData.nodes.BaseDocumentLine | null>;

    public recipientSite: AsyncResponse<xtremSystem.nodes.Site | null>;

    public providerSite: AsyncResponse<xtremSystem.nodes.Site | null>;

    public stockJournal: AsyncResponse<xtremStockData.nodes.StockJournal | null>;

    public financialSite: AsyncResponse<xtremSystem.nodes.Site>;

    public item: AsyncResponse<xtremMasterData.nodes.Item | null>;

    public account: AsyncResponse<xtremFinanceData.nodes.Account | null>;

    public customer: AsyncResponse<xtremMasterData.nodes.Customer | null>;

    public supplier: AsyncResponse<xtremMasterData.nodes.Supplier | null>;

    public payToSupplier: AsyncResponse<xtremMasterData.nodes.Supplier | null>;

    public payToSupplierLinkedAddress: AsyncResponse<xtremMasterData.nodes.BusinessEntityAddress | null>;

    public returnLinkedAddress: AsyncResponse<xtremMasterData.nodes.BusinessEntityAddress | null>;

    public paymentTerm: AsyncResponse<xtremMasterData.nodes.PaymentTerm | null>;

    public transactionCurrency: AsyncResponse<xtremMasterData.nodes.Currency>;

    public resource: AsyncResponse<xtremMasterData.nodes.DetailedResource | null>;

    public get batchId(): string {
        return this.financeIntegrationDocument.batchId;
    }

    public get batchSize(): integer {
        return this.financeIntegrationDocument.batchSize;
    }

    public get documentNumber(): string {
        return this.financeIntegrationDocument.documentNumber;
    }

    public get documentSysId(): number {
        return this.financeIntegrationDocument.documentSysId;
    }

    public get sourceDocumentNumber(): string {
        return this.documentType === 'workInProgress'
            ? (this.financeIntegrationDocument.sourceDocumentNumber ?? '')
            : this.financeIntegrationDocumentLine.sourceDocumentNumber;
    }

    public get documentDate(): date {
        return date.parse(this.financeIntegrationDocument.documentDate);
    }

    public get taxDate(): date | null {
        return this.financeIntegrationDocumentLine.taxDate
            ? date.parse(this.financeIntegrationDocumentLine.taxDate)
            : null;
    }

    public get documentType(): xtremFinanceData.enums.FinanceDocumentType {
        return this.financeIntegrationDocument.documentType;
    }

    public get movementType(): xtremFinanceData.enums.MovementType {
        return this.financeIntegrationDocumentLine.movementType;
    }

    public get sourceDocumentType(): xtremFinanceData.enums.SourceDocumentType | null {
        return this.documentType === 'workInProgress'
            ? (this.financeIntegrationDocument.sourceDocumentType ?? null)
            : (this.financeIntegrationDocumentLine.sourceDocumentType ?? null);
    }

    public get targetDocumentType(): xtremFinanceData.enums.TargetDocumentType {
        return this.financeIntegrationDocument.targetDocumentType;
    }

    public get itemPostingClass(): Promise<xtremFinanceData.nodes.PostingClass | null> {
        return (async () => {
            return (await (await this.item)?.postingClass) || null;
        })();
    }

    public get customerPostingClass(): Promise<xtremFinanceData.nodes.PostingClass | null> {
        return (async () => {
            return (await (await this.customer)?.postingClass) || null;
        })();
    }

    public get supplierPostingClass(): Promise<xtremFinanceData.nodes.PostingClass | null> {
        return (async () => {
            return (await (await this.supplier)?.postingClass) || null;
        })();
    }

    public get resourcePostingClass(): Promise<xtremFinanceData.nodes.PostingClass | null> {
        return (async () => {
            return (await (await this.resource)?.postingClass) || null;
        })();
    }

    public get companyFxRate(): decimal {
        return this.financeIntegrationDocumentLine.companyFxRate;
    }

    public get companyFxRateDivisor(): decimal {
        return this.financeIntegrationDocumentLine.companyFxRateDivisor;
    }

    public get fxRateDate(): date {
        return date.parse(this.financeIntegrationDocumentLine.fxRateDate);
    }

    public get dueDate(): date | null {
        return this.financeIntegrationDocument.dueDate ? date.parse(this.financeIntegrationDocument.dueDate) : null;
    }

    public get supplierDocumentDate(): date | null {
        return this.financeIntegrationDocument.supplierDocumentDate
            ? date.parse(this.financeIntegrationDocument.supplierDocumentDate)
            : null;
    }

    public get supplierDocumentNumber(): string {
        return this.financeIntegrationDocument.supplierDocumentNumber || '';
    }

    public get isPrinted(): boolean {
        return this.financeIntegrationDocument.isPrinted || false;
    }

    public get taxCalculationStatus(): xtremMasterData.enums.TaxCalculationStatus | null {
        return this.financeIntegrationDocument.taxCalculationStatus || null;
    }

    /* eslint-disable class-methods-use-this */
    public get storedDimensions(): object | null {
        return this.financeIntegrationDocumentLine.storedDimensions;
    }

    public get storedAttributes(): xtremMasterData.interfaces.StoredAttributes | null {
        return this.financeIntegrationDocumentLine.storedAttributes;
    }

    public get storedComputedAttributes(): object {
        return {};
    }

    // public readonly get storedComputedAttributes(): object{

    // }

    public get description(): string {
        return this.financeIntegrationDocument.description || '';
    }

    public readonly amounts: xtremFinanceData.interfaces.FinanceNodes.AccountingStagingAmount[] = [];

    public readonly taxes: xtremFinanceData.interfaces.FinanceNodes.AccountingStagingLineTax[] = [];

    public readonly financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData = {
        batchId: this.financeIntegrationDocument.batchId,
        documentNumber: this.financeIntegrationDocument.documentNumber,
        documentType: this.financeIntegrationDocument.documentType,
        targetDocumentType: this.financeIntegrationDocument.targetDocumentType,
        targetDocumentNumber: '',
        targetDocumentSysId: 0,
        validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
        status: 'pending',
    };

    private constructor(
        private readonly context: Context,
        private readonly financeIntegrationDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument,
        private readonly financeIntegrationDocumentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
        public readonly originNotificationId: string,
        public readonly replyTopic: string,
        public readonly isProcessed: boolean,
    ) {}

    private async setLineValues(): Promise<this> {
        await this.setBaseDocumentLine();
        await this.setSourceBaseDocumentLine();
        await this.setStockJournal();
        await this.setRecipientSite();
        await this.setProviderSite();
        await this.setItem();
        await this.setAccount();
        await this.setCustomer();
        await this.setSupplier();
        await this.setPayToSupplier();
        await this.setPayToSupplierAddress();
        await this.setReturnAddress();
        await this.setTransactionCurrency();
        await this.setResource();

        return this;
    }

    private async setBaseDocumentLine() {
        if (this.financeIntegrationDocumentLine.baseDocumentLineSysId) {
            this.baseDocumentLine = await this.context.tryRead(xtremMasterData.nodes.BaseDocumentLine, {
                _id: this.financeIntegrationDocumentLine.baseDocumentLineSysId,
            });
            if (!this.baseDocumentLine) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.baseDocumentLineNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.baseDocumentLineSysId,
                    ),
                });
            }
        }
    }

    private async setSourceBaseDocumentLine() {
        if (this.financeIntegrationDocumentLine.sourceBaseDocumentLineSysId) {
            this.sourceBaseDocumentLine = await this.context.tryRead(xtremMasterData.nodes.BaseDocumentLine, {
                _id: this.financeIntegrationDocumentLine.sourceBaseDocumentLineSysId,
            });
            if (!this.sourceBaseDocumentLine) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.baseDocumentLineNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.sourceBaseDocumentLineSysId,
                    ),
                });
            }
        }
    }

    private async setStockJournal() {
        if (this.financeIntegrationDocumentLine.stockJournalSysId) {
            this.stockJournal = await this.context.tryRead(xtremStockData.nodes.StockJournal, {
                _id: this.financeIntegrationDocumentLine.stockJournalSysId,
            });
            if (!this.stockJournal) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.stockJournalNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.stockJournalSysId,
                    ),
                });
            }
        }
    }

    private async setRecipientSite() {
        if (this.financeIntegrationDocumentLine.recipientSiteSysId) {
            this.recipientSite = await this.context.tryRead(xtremSystem.nodes.Site, {
                _id: this.financeIntegrationDocumentLine.recipientSiteSysId,
            });
            if (!this.recipientSite) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.siteNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.recipientSiteSysId,
                    ),
                });
            }
        }
    }

    private async setProviderSite() {
        if (this.financeIntegrationDocumentLine.providerSiteSysId) {
            this.providerSite = await this.context.tryRead(xtremSystem.nodes.Site, {
                _id: this.financeIntegrationDocumentLine.providerSiteSysId,
            });
            if (!this.providerSite) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.siteNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.providerSiteSysId,
                    ),
                });
            }
        }
    }

    private async setItem() {
        if (this.financeIntegrationDocumentLine.itemSysId) {
            this.item = await this.context.tryRead(xtremMasterData.nodes.Item, {
                _id: this.financeIntegrationDocumentLine.itemSysId,
            });
            if (!this.item) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.itemNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.itemSysId,
                    ),
                });
            }
        }
    }

    private async setAccount() {
        if (this.financeIntegrationDocumentLine.accountSysId) {
            this.account = await this.context.tryRead(xtremFinanceData.nodes.Account, {
                _id: this.financeIntegrationDocumentLine.accountSysId,
            });
            if (!this.account) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.accountNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.accountSysId,
                    ),
                });
            }
        }
    }

    private async setCustomer() {
        if (this.financeIntegrationDocumentLine.customerSysId) {
            this.customer = await this.context.tryRead(xtremMasterData.nodes.Customer, {
                _id: this.financeIntegrationDocumentLine.customerSysId,
            });
            if (!this.customer) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.customerNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.customerSysId,
                    ),
                });
            }
        }
    }

    private async setSupplier() {
        if (this.financeIntegrationDocumentLine.supplierSysId) {
            this.supplier = await this.context.tryRead(xtremMasterData.nodes.Supplier, {
                _id: this.financeIntegrationDocumentLine.supplierSysId,
            });
            if (!this.supplier) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.supplierNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.supplierSysId,
                    ),
                });
            }
        }
    }

    private async setPayToSupplier() {
        if (this.financeIntegrationDocumentLine.payToSupplierSysId) {
            this.payToSupplier = await this.context.tryRead(xtremMasterData.nodes.Supplier, {
                _id: this.financeIntegrationDocumentLine.payToSupplierSysId,
            });
            if (!this.payToSupplier) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.supplierNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.payToSupplierSysId,
                    ),
                });
            }
        }
    }

    private async setPayToSupplierAddress() {
        if (this.financeIntegrationDocumentLine.payToSupplierAddressSysId) {
            this.payToSupplierLinkedAddress = await this.context.tryRead(xtremMasterData.nodes.BusinessEntityAddress, {
                _id: this.financeIntegrationDocumentLine.payToSupplierAddressSysId,
            });
            if (!this.payToSupplierLinkedAddress) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.addressNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.payToSupplierAddressSysId,
                    ),
                });
            }
        }
    }

    private async setReturnAddress() {
        if (this.financeIntegrationDocumentLine.returnAddressSysId) {
            this.returnLinkedAddress = await this.context.tryRead(xtremMasterData.nodes.BusinessEntityAddress, {
                _id: this.financeIntegrationDocumentLine.returnAddressSysId,
            });
            if (!this.returnLinkedAddress) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.addressNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.returnAddressSysId,
                    ),
                });
            }
        }
    }

    private async setTransactionCurrency() {
        if (this.financeIntegrationDocumentLine.currencySysId) {
            try {
                this.transactionCurrency = await this.context.read(xtremMasterData.nodes.Currency, {
                    _id: this.financeIntegrationDocumentLine.currencySysId,
                });
            } catch {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.currencyNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.currencySysId,
                    ),
                });
            }
        }
    }

    private async setResource() {
        if (this.financeIntegrationDocumentLine.resourceSysId) {
            this.resource = await this.context.tryRead(xtremMasterData.nodes.DetailedResource, {
                _id: this.financeIntegrationDocumentLine.resourceSysId,
            });
            if (!this.resource) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.resourceNotFound(
                        this.context,
                        this.financeIntegrationDocumentLine.resourceSysId,
                    ),
                });
            }
        }
    }

    private async init(): Promise<this> {
        if (this.financeIntegrationDocument.financialSiteSysId) {
            try {
                this.financialSite = await this.context.read(xtremSystem.nodes.Site, {
                    _id: this.financeIntegrationDocument.financialSiteSysId,
                });
            } catch {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.siteNotFound(
                        this.context,
                        this.financeIntegrationDocument.financialSiteSysId,
                    ),
                });
            }
        }

        if (this.financeIntegrationDocument.paymentTermSysId) {
            this.paymentTerm = await this.context.tryRead(xtremMasterData.nodes.PaymentTerm, {
                _id: this.financeIntegrationDocument.paymentTermSysId,
            });
            if (!this.paymentTerm) {
                this.financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: xtremFinanceData.classes.LocalizedMessages.paymentTermNotFound(
                        this.context,
                        this.financeIntegrationDocument.paymentTermSysId,
                    ),
                });
            }
        }

        await this.setLineValues();

        await asyncArray(this.financeIntegrationDocumentLine.taxes || []).forEach(async tax => {
            const baseTax = await xtremFinanceData.functions.AccountingEngineCommon.getBaseTax(
                this.context,
                tax.baseTaxSysId,
                this.financeTransactionData,
            );
            if (baseTax) {
                this.taxes.push({ baseTax });
            }
        });

        await asyncArray(this.financeIntegrationDocumentLine.amounts).forEach(async amountLine => {
            let tax: xtremTax.nodes.Tax | null = null;
            let baseTax: xtremTax.nodes.BaseTax | null = null;

            if (amountLine.taxSysId) {
                tax = await this.context.tryRead(xtremTax.nodes.Tax, { _id: amountLine.taxSysId });
                if (!tax) {
                    this.financeTransactionData.validationMessages.push({
                        type: ValidationSeverity.error,
                        message: xtremFinanceData.classes.LocalizedMessages.taxNotFound(
                            this.context,
                            amountLine.taxSysId,
                        ),
                    });
                }
            }

            if (amountLine.baseTaxSysId) {
                baseTax = await xtremFinanceData.functions.AccountingEngineCommon.getBaseTax(
                    this.context,
                    amountLine.baseTaxSysId,
                    this.financeTransactionData,
                );
            }

            this.amounts.push({
                amountType: amountLine.amountType,
                amount: +amountLine.amount,
                tax,
                taxPostingClass: (await tax?.postingClass) || null,
                baseTax,
                documentLineType: amountLine.documentLineType,
                taxRate: amountLine.taxRate || 0,
                taxDate: amountLine.taxDate ? date.parse(amountLine.taxDate) : null,
            });
        });

        return this;
    }

    static create(
        context: Context,
        params: {
            financeIntegrationDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument;
            financeIntegrationDocumentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine;
            originNotificationId: string;
            replyTopic: string;
            isProcessed: boolean;
        },
    ): Promise<AccountingStaging> {
        return new AccountingStaging(
            context,
            params.financeIntegrationDocument,
            params.financeIntegrationDocumentLine,
            params.originNotificationId,
            params.replyTopic,
            params.isProcessed,
        ).init();
    }

    public static getAccountingStagingLines(
        context: Context,
        params: {
            financeIntegrationDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument;
            originNotificationId: string;
            replyTopic: string;
            isProcessed: boolean;
        },
    ): Promise<xtremFinanceData.classes.AccountingStaging[]> {
        return asyncArray(params.financeIntegrationDocument.documentLines)
            .map(documentLine =>
                xtremFinanceData.classes.AccountingStaging.create(context, {
                    ...params,
                    financeIntegrationDocumentLine: documentLine,
                }),
            )
            .toArray();
    }
}
