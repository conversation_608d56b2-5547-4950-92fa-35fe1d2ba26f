import type { Context } from '@sage/xtrem-core';
import * as xtremFinanceData from '../index';

export class LocalizedMessages {
    private static readonly financeDocumentTypeEnumName = '@sage/xtrem-finance-data/FinanceDocumentType';

    public static cantReadPostingClass(
        context: Context,
        documentNumber: string,
        documentType: xtremFinanceData.enums.FinanceDocumentType,
        postingClassType: xtremFinanceData.enums.PostingClassType,
    ) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_journal_entry',
            'The posting class for the {{documentType}} {{documentNumber}} document could not be found. The posting class type is: {{postingClassType}}.',
            {
                documentNumber,
                documentType: context.localizeEnumMember(
                    xtremFinanceData.classes.LocalizedMessages.financeDocumentTypeEnumName,
                    documentType,
                ),
                postingClassType: context.localizeEnumMember(
                    '@sage/xtrem-finance-data/PostingClassType',
                    postingClassType,
                ),
            },
        );
    }

    public static postingClassMissingOnItem(context: Context, item: string) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__item_posting_class_missing_on_item',
            'Enter a posting class for the {{item}} item.',
            { item },
        );
    }

    public static postingClassMissingOnTax(context: Context, tax: string) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__tax_posting_class_missing_on_tax',
            'Enter a posting class for the {{tax}} tax.',
            {
                tax,
            },
        );
    }

    public static postingClassMissingOnResource(context: Context, resource: string) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__resource_posting_class_missing_on_resource',
            'Enter a posting class for the {{resource}} resource.',
            {
                resource,
            },
        );
    }

    public static noFinanceDocumentLinesGeneratedForItem(
        context: Context,
        item: string,
        journalEntryType: string,
        movementType: string,
    ) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__no_finance_document_lines_generated_for_item',
            'The account cannot be determined for the {{item}} item, {{journalEntryType}} journal entry type and {{movementType}} movement type.',
            {
                item,
                journalEntryType,
                movementType,
            },
        );
    }

    public static cantReadPostingClassForItem(
        context: Context,
        documentNumber: string,
        documentType: xtremFinanceData.enums.FinanceDocumentType,
        postingClassType: xtremFinanceData.enums.PostingClassType,
        itemId: string,
    ) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_posting_class_ap_ar_invoice',
            'The posting class for the {{documentType}} {{documentNumber}} document and {{itemId}} item could not be found. The posting class type is: {{postingClassType}}.',
            {
                documentNumber,
                documentType: context.localizeEnumMember(
                    xtremFinanceData.classes.LocalizedMessages.financeDocumentTypeEnumName,
                    documentType,
                ),
                postingClassType: context.localizeEnumMember(
                    '@sage/xtrem-finance-data/PostingClassType',
                    postingClassType,
                ),
                itemId,
            },
        );
    }

    public static customerNotFound(context: Context, customer: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_customer',
            'The {{customer}} customer could not be found.',
            { customer },
        );
    }

    public static supplierNotFound(context: Context, supplier: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_supplier',
            'The {{supplier}} supplier could not be found.',
            { supplier },
        );
    }

    public static addressNotFound(context: Context, address: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_address',
            'The {{address}} address could not be found.',
            { address },
        );
    }

    public static itemNotFound(context: Context, item: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_item',
            'The {{item}} item could not be found.',
            { item },
        );
    }

    public static resourceNotFound(context: Context, resource: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_resource',
            'The {{resource}} resource could not be found.',
            { resource },
        );
    }

    public static siteNotFound(context: Context, site: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_financial_site',
            'The {{financialSite}} site could not be found.',
            { site },
        );
    }

    public static taxNotFound(context: Context, tax: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_tax',
            'The {{tax}} tax could not be found.',
            { tax },
        );
    }

    public static bpAccountNotFound(
        context: Context,
        businessPartner: string,
        documentType: string,
        documentNumber: string,
    ) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__no_bp_account',
            '{{documentType}} {{documentNumber}}: The account could not be found for the {{businessPartner}} business partner.',
            {
                businessPartner,
                documentType: context.localizeEnumMember(
                    xtremFinanceData.classes.LocalizedMessages.financeDocumentTypeEnumName,
                    documentType,
                ),
                documentNumber,
            },
        );
    }

    public static currencyNotFound(context: Context, currency: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_transaction_currency',
            'The {{currency}} currency could not be found.',
            { currency },
        );
    }

    public static baseDocumentLineNotFound(context: Context, baseDocumentLine: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_document_line',
            'The base document line {{baseDocumentLine}} could not be found.',
            { baseDocumentLine },
        );
    }

    public static stockJournalNotFound(context: Context, stockJournal: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_stock_journal',
            'The stock journal {{stockJournal}} could not be found.',
            { stockJournal },
        );
    }

    public static accountNotFound(context: Context, account: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_account',
            'The {{account}} account could not be found.',
            { account },
        );
    }

    public static taxLineNotFound(context: Context, baseTax: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_base_tax',
            'The tax line {{baseTax}} could not be found.',
            { baseTax },
        );
    }

    public static paymentTermNotFound(context: Context, paymentTerm: number) {
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages__cant_read_payment_term',
            'The {{paymentTerm}} payment term could not be found.',
            { paymentTerm },
        );
    }

    public static mandatoryDimensionTypeNotFound(
        context: Context,
        dimension: string,
        item: string | null,
        level: string,
        document: string,
        taxAmount: boolean,
    ) {
        if (item && taxAmount === true) {
            return context.localize(
                '@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_True',
                'You need to select the {{level}} dimension [{{dimension}}] for this item: {{item}} on document: {{sourceDocumentNumber}} (Tax details) .',
                {
                    dimension,
                    item,
                    level: context.localizeEnumMember('@sage/xtrem-finance-data/AttributeDimensionTypeLevel', level),
                    sourceDocumentNumber: document,
                },
            );
        }
        if (item && taxAmount === false) {
            return context.localize(
                '@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type_item_tax_amount_false',
                'You need to select the {{level}} dimension [{{dimension}}] for this item: {{item}} on document: {{sourceDocumentNumber}} .',
                {
                    dimension,
                    item,
                    level: context.localizeEnumMember('@sage/xtrem-finance-data/AttributeDimensionTypeLevel', level),
                    sourceDocumentNumber: document,
                },
            );
        }
        return context.localize(
            '@sage/xtrem-finance-data/classes__localized-messages_downstream_document__no_dimension_type',
            'You need to select the {{level}} dimension: {{dimension}}.',
            {
                dimension,
                level: context.localizeEnumMember('@sage/xtrem-finance-data/AttributeDimensionTypeLevel', level),
            },
        );
    }
}
