import { extractEdges } from '@sage/xtrem-client';
import type { FinanceDocumentType } from '@sage/xtrem-finance-data-api';
import type { Country } from '@sage/xtrem-structure-api';
import * as ui from '@sage/xtrem-ui';
import { uniq } from 'lodash';

export async function getIsSubjectToGlTaxExcludedAmount(
    pageInstance: ui.Page,
    legislationId: string,
): Promise<boolean> {
    // first step: read all countries with the given legislationId assigned
    const countries = extractEdges<Country>(
        await pageInstance.$.graph
            .node('@sage/xtrem-structure/Country')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        id: true,
                        taxSolution: { _id: true, id: true },
                    },
                    {
                        filter: {
                            legislation: { id: legislationId },
                        },
                    },
                ),
            )
            .execute(),
    );

    // second step: get unique list of taxSolutions assigned to these countries
    const taxSolutionIds: string[] = uniq(countries.map(country => country.taxSolution._id));

    // third step: check if there is a taxSolutionLine in one of the found tax solutions with
    // isSubjectToGlTaxExcludedAmount = true and taxCategory = VAT
    const taxSolutionLines = extractEdges(
        await pageInstance.$.graph
            .node('@sage/xtrem-tax/TaxSolutionLine')
            .aggregate.query(
                ui.queryUtils.edgesSelector(
                    {
                        group: {
                            _id: { _by: 'value' },
                        },
                    },
                    {
                        filter: {
                            taxSolution: { _id: { _in: taxSolutionIds } },
                            isSubjectToGlTaxExcludedAmount: true,
                            taxCategory: { id: 'VAT' },
                        },
                        first: 1,
                    },
                ),
            )
            .execute(),
    );

    return !!taxSolutionLines.length;
}

export function getPage(documentType: FinanceDocumentType): string {
    switch (documentType) {
        case 'miscellaneousStockReceipt':
            return '@sage/xtrem-stock/StockReceipt';
        case 'miscellaneousStockIssue':
            return '@sage/xtrem-stock/StockIssue';
        case 'stockCount':
            return '@sage/xtrem-stock/StockCount';
        case 'purchaseReceipt':
            return '@sage/xtrem-purchasing/PurchaseReceipt';
        case 'purchaseInvoice':
            return '@sage/xtrem-purchasing/PurchaseInvoice';
        case 'purchaseCreditMemo':
            return '@sage/xtrem-purchasing/PurchaseCreditMemo';
        case 'salesInvoice':
            return '@sage/xtrem-sales/SalesInvoice';
        case 'salesCreditMemo':
            return '@sage/xtrem-sales/SalesCreditMemo';
        case 'stockAdjustment':
            return '@sage/xtrem-stock/StockAdjustment';
        case 'salesShipment':
            return '@sage/xtrem-sales/SalesShipment';
        case 'workInProgress':
            return '@sage/xtrem-manufacturing/WorkOrder';
        case 'apInvoice':
            return '@sage/xtrem-finance/AccountsPayableInvoice';
        case 'arInvoice':
            return '@sage/xtrem-finance/AccountsReceivableInvoice';
        case 'purchaseReturn':
            return '@sage/xtrem-purchasing/PurchaseReturn';
        case 'salesReturnReceipt':
            return '@sage/xtrem-sales/SalesReturnReceipt';
        case 'bankReconciliationWithdrawal':
            return '@sage/xtrem-intacct-finance/IntacctTransactionFeedQuery';
        case 'bankReconciliationDeposit':
            return '@sage/xtrem-intacct-finance/IntacctTransactionFeedQuery';
        case 'stockTransferShipment':
            return '@sage/xtrem-supply-chain/StockTransferShipment';
        default:
            return '';
    }
}
