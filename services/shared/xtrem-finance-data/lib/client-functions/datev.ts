import * as ui from '@sage/xtrem-ui';
import { inRange } from 'lodash';
import type { CustomerExtension } from '../page-extensions/customer-extension';
import type { SupplierExtension } from '../page-extensions/supplier-extension';
import type { Account as AccountPage } from '../pages/account';
import type { DatevConfiguration as DatevConfigurationPage } from '../pages/datev-configuration';

export interface DatevIdRange {
    fromValue: number;
    toValue: number;
    length: number;
}

interface DatevIdConfiguration {
    accountLength: number;
    customerRangeStart: number;
    customerRangeEnd: number;
    supplierRangeStart: number;
    supplierRangeEnd: number;
}

export function checkEmptyDatevId(options: {
    pageInstance: CustomerExtension | SupplierExtension | AccountPage;
}): void {
    if (options.pageInstance.$.isServiceOptionEnabled('datevOption') && !options.pageInstance.datevId.value) {
        options.pageInstance.$.showToast(
            ui.localize(
                '@sage/xtrem-finance-data/pages__business_entity_customer_extension__datev_id_warning',
                'You need to enter the DATEV ID before extracting the data when the DATEV integration is active.',
            ),
            { type: 'warning', timeout: 5000 },
        );
    }
}

export function checkEmptyTax(options: { pageInstance: AccountPage }): void {
    if (
        options.pageInstance.$.isServiceOptionEnabled('datevOption') &&
        options.pageInstance.isAutomaticAccount.value &&
        !options.pageInstance.tax.value
    ) {
        options.pageInstance.$.showToast(
            ui.localize(
                '@sage/xtrem-finance-data/pages__account__empty_tax_warning',
                'You need to enter the tax code when the Automatic account is enabled.',
            ),
            { type: 'warning', timeout: 5000 },
        );
    }
}

export function validateDatevId(options: { datevId: string | undefined; first: number; last: number }): string {
    if (options.datevId && !inRange(Number(options.datevId), options.first, options.last + 1)) {
        return ui.localize(
            '@sage/xtrem-finance-data/pages__business_entity_customer__wrong_datev_id',
            'Enter a number between {{first}} and {{last}}.',
            { first: options.first, last: options.last },
        );
    }
    return '';
}

export function setRangeStrings(pageInstance: DatevConfigurationPage) {
    pageInstance.customerIdRange.value = pageInstance.accountLength.value
        ? ui.localize(
              '@sage/xtrem-finance-data/pages__datev_configuration__customer_id_range',
              'From {{start}} to {{end}}',
              {
                  start: pageInstance.customerRangeStart.value?.toString(),
                  end: pageInstance.customerRangeEnd.value?.toString(),
              },
          )
        : '';
    pageInstance.supplierIdRange.value = pageInstance.accountLength.value
        ? ui.localize(
              '@sage/xtrem-finance-data/pages__datev_configuration__supplier_id_range',
              'From {{start}} to {{end}}',
              {
                  start: pageInstance.supplierRangeStart.value?.toString(),
                  end: pageInstance.supplierRangeEnd.value?.toString(),
              },
          )
        : '';
}

export function recalculateAndSetRangeStrings(pageInstance: DatevConfigurationPage) {
    if (pageInstance.accountLength.value) {
        pageInstance.customerRangeStart.value = 10 ** pageInstance.accountLength.value;
        pageInstance.customerRangeEnd.value = 10 ** pageInstance.accountLength.value * 7 - 1;
        pageInstance.supplierRangeStart.value = 10 ** pageInstance.accountLength.value * 7;
        pageInstance.supplierRangeEnd.value = 10 ** (pageInstance.accountLength.value + 1) - 1;
    }
    setRangeStrings(pageInstance);
}

// format a number to a string of length <stringLength> with leading zeros
export function formatStringWithLeadingZeros(stringToFormat: string, stringLength: number): string {
    return stringToFormat.padStart(stringLength, '0');
}

// This function is called on save of the DatevConfiguration page. It does the following controls and
// shows the corresponding warnings:
// - Check if there are accounts in the DB with a datevId length other than the one set in the DatevConfiguration
// - Check if there are customers in the DB with a datevId length other than the one set in the DatevConfiguration
// - Check if there are suppliers in the DB with a datevId length other than the one set in the DatevConfiguration
// - Check if there are customers in the DB with a datevId outside the range set in the DatevConfiguration
// - Check if there are suppliers in the DB with a datevId outside the range set in the DatevConfiguration
// To do the checks the function uses the graph mutation datevConfigurationControlsOnSave of the DatevConfiguration node.
export async function datevConfigurationControlsOnSave(pageInstance: DatevConfigurationPage): Promise<void> {
    if (
        pageInstance.accountLength.value &&
        validateDatevId({
            datevId: pageInstance.accountLength.value?.toString(),
            first: 4,
            last: 8,
        }) === ''
    ) {
        const controlResult = (await pageInstance.$.graph
            .node('@sage/xtrem-finance-data/DatevConfiguration')
            .mutations.datevConfigurationControlsOnSave(true, {
                datevConfiguration: {
                    accountLength: pageInstance.accountLength.value || undefined,
                    customerRangeStart: pageInstance.customerRangeStart.value || undefined,
                    customerRangeEnd: pageInstance.customerRangeEnd.value || undefined,
                    supplierRangeStart: pageInstance.supplierRangeStart.value || undefined,
                    supplierRangeEnd: pageInstance.supplierRangeEnd.value || undefined,
                },
            })
            .execute()) as string[];
        if (controlResult.length) {
            pageInstance.$.showToast(
                `**${ui.localize(
                    '@sage/xtrem-finance-data/pages__datev_configuration_save_warnings',
                    'Warnings while saving:',
                )}**\n\n${controlResult.map(result => result).join('\n\n')}`,
                { type: 'warning', timeout: 10000 },
            );
        }
    }
}

function getDatevConfiguration(
    pageInstance: CustomerExtension | SupplierExtension | AccountPage,
): Promise<DatevIdConfiguration | undefined> {
    return pageInstance.$.graph
        .node('@sage/xtrem-finance-data/DatevConfiguration')
        .read(
            {
                accountLength: true,
                customerRangeStart: true,
                customerRangeEnd: true,
                supplierRangeStart: true,
                supplierRangeEnd: true,
            },
            '#DATEV',
        )
        .execute();
}

export async function getDatevIdRangeAccounts(pageInstance: AccountPage): Promise<DatevIdRange> {
    const configuration = await getDatevConfiguration(pageInstance);
    if (configuration) {
        return {
            fromValue: 1,
            toValue: 10 ** configuration.accountLength - 1,
            length: configuration.accountLength,
        };
    }
    return { fromValue: 1, toValue: 9999, length: 4 };
}

export async function getDatevIdRangeCustomers(pageInstance: CustomerExtension): Promise<DatevIdRange> {
    const configuration = await getDatevConfiguration(pageInstance);
    if (configuration) {
        return {
            fromValue: configuration.customerRangeStart,
            toValue: configuration.customerRangeEnd,
            length: configuration.customerRangeStart.toString().length,
        };
    }
    return { fromValue: 10000, toValue: 69999, length: 5 };
}

export async function getDatevIdRangeSuppliers(pageInstance: SupplierExtension): Promise<DatevIdRange> {
    const configuration = await getDatevConfiguration(pageInstance);
    if (configuration) {
        return {
            fromValue: configuration.supplierRangeStart,
            toValue: configuration.supplierRangeEnd,
            length: configuration.supplierRangeStart.toString().length,
        };
    }
    return { fromValue: 70000, toValue: 99999, length: 5 };
}
