import type { FinanceIntegrationStatus, JournalStatus, PostingStatus } from '@sage/xtrem-finance-data-api';
import type { ColoredElement } from '@sage/xtrem-system-api';
import {
    colorfulPillPattern,
    colorfulPillPatternDefaulted,
} from '@sage/xtrem-system/build/lib/client-functions/color-pattern';

// document statuses
function financeDocumentPostingStatusColor(status: PostingStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'posted':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'generationError':
        case 'postingError':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'notPosted':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'generated':
        case 'postingInProgress':
        case 'generationInProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function financeDocumentJournalStatusColor(status: JournalStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'draft':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'inProgress':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}

function financeIntegrationStatusColor(status: FinanceIntegrationStatus, coloredElement: ColoredElement) {
    switch (status) {
        case 'toBeRecorded':
            return colorfulPillPattern.filledNeutral[coloredElement];
        case 'recording':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'pending':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'error':
            return colorfulPillPattern.filledNegative[coloredElement];
        case 'recorded':
            return colorfulPillPattern.filledPositive[coloredElement];
        case 'notRecorded':
            return colorfulPillPattern.filledCaution[coloredElement];
        case 'submitted':
            return colorfulPillPattern.filledInformation[coloredElement];
        case 'posted':
            return colorfulPillPattern.filledClosing[coloredElement];
        case 'failed':
            return colorfulPillPattern.filledNegative[coloredElement];
        default:
            return colorfulPillPattern.default[coloredElement];
    }
}
export function getLabelColorByStatus(
    enumEntry: string,
    status?: string | null,
): {
    backgroundColor: string;
    borderColor: string;
    textColor: string;
} {
    const getColor = (coloredElement: ColoredElement) => {
        switch (enumEntry) {
            case 'FinanceDocumentJournalStatus':
                return financeDocumentJournalStatusColor(status as JournalStatus, coloredElement);
            case 'FinanceDocumentPostingStatus':
                return financeDocumentPostingStatusColor(status as PostingStatus, coloredElement);
            case 'FinanceIntegrationStatus':
                return financeIntegrationStatusColor(status as FinanceIntegrationStatus, coloredElement);
            default:
                return colorfulPillPatternDefaulted(coloredElement);
        }
    };

    return {
        backgroundColor: getColor('backgroundColor'),
        borderColor: getColor('borderColor'),
        textColor: getColor('textColor'),
    };
}
