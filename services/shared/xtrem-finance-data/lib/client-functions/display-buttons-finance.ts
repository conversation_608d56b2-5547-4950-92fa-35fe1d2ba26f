import type * as isHiddenButtonsCommon from '@sage/xtrem-system/build/lib/client-functions/interfaces/is-hidden-buttons';
import type * as isHiddenButtons from './interfaces/is-hidden-buttons-finance';

export function isHiddenButtonGoToSysNotificationPageAction(
    data: isHiddenButtonsCommon.HeaderQuickActionButtonParameters<isHiddenButtons.GoToSysNotificationPageParameters>,
) {
    return (
        !data.recordId ||
        data.parameters.financeIntegrationStatus.every(financeIntegrationStatus =>
            ['toBeGenerated', 'posted'].includes(financeIntegrationStatus),
        )
    );
}
