import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { DimensionDefinitionLevel, GraphApi } from '@sage/xtrem-finance-data-api';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import * as ui from '@sage/xtrem-ui';
import * as _ from 'lodash';
import type * as interfaces from './interfaces';

/**
 * Provides the default filter callback for document-line
 * @param line a document-line
 * @returns a boolean having true for lines passing the filter
 */
function documentLineFilterCallback(line: ui.PartialNodeWithId<any>): boolean {
    return !!line;
}

/**
 * The type for a documentLineFilterCallback
 */
export type DocumentLineFilterCallbackType = typeof documentLineFilterCallback;

/**
 * Gets you into the dimension panel,
 * it handle the dimension per document line which concerns: the opening of the panel and return the line modified.
 * @param pageInstance the calling page's this (used to interact with the framework)
 * @param data an object containing documentLine (line node) the line on which the dimensions will be edited
 * @param options an object containing editable (Boolean) flags to enable/disable edition on this panel
 * @returns the documentLine edited
 */
export function editDisplayDimensions<Node extends interfaces.BaseDocumentLineWithAnalytical>(
    pageInstance: ui.Page,
    data: Omit<interfaces.AnalyticalPanelParameters<Node>, 'editable' | 'disabledAction'>,
    options: Omit<interfaces.AnalyticalPanelParameters<Node>, 'documentLine'>,
    defaultDimensionsAttributes?: interfaces.DefaultDimensions,
    defaultedFromItem?: string | undefined,
): Promise<Node> {
    return new Promise((resolve, reject) => {
        pageInstance.$.dialog
            .page(
                '@sage/xtrem-finance-data/DimensionPanel',
                {
                    disabledAction: JSON.stringify(options.disabledAction || {}),
                    enteredDimensions: JSON.stringify({
                        storedAttributes: defaultDimensionsAttributes
                            ? defaultDimensionsAttributes.attributes
                            : data.documentLine.storedAttributes,
                        storedDimensions: defaultDimensionsAttributes
                            ? defaultDimensionsAttributes.dimensions
                            : data.documentLine.storedDimensions,
                    }),
                    editable: JSON.stringify(options.editable),
                    isDefaultDimensionPage: JSON.stringify(false),
                    isDimensionPageToInherit: JSON.stringify(false),
                    calledFromMainGrid: JSON.stringify(options.calledFromMainGrid || false),
                    defaultedFromItem: JSON.stringify(defaultedFromItem || {}),
                },
                { size: 'large' },
            )
            .then(resolveData => {
                resolve({
                    ...data.documentLine,
                    storedAttributes: resolveData.attributes,
                    storedDimensions: resolveData.dimensions,
                } as Node);
            })
            .catch(() => {
                reject();
            });
    });
}

/**
 * Gets you into the dimension panel in default mode,
 * which is the same as the normal mode except for the action provided (apply, applyToNewLine).
 * It handle the dimension for multiple document line which concerns: the opening of the panel and
 * return the dimensions entered + action chosen.
 * @param pageInstance the calling page's this (used to interact with the framework)
 * @param defaultDimensionsAttributes an object containing the (existing) default dimensions and the apply action
 * @returns an object containing the default dimensions and the apply action
 */
export function editDefaultDimensions(
    pageInstance: interfaces.PageWithDefaultDimensions | interfaces.PageWithMultipleDefaultDimensions,
    defaultDimensionsAttributes: interfaces.DefaultDimensions,
    defaultedFromItem: string | undefined,
    options: Omit<interfaces.AnalyticalPanelParameters<any>, 'editable' | 'documentLine' | 'calledFromMainGrid'>,
): Promise<interfaces.DefaultDimensions> {
    return new Promise((resolve, reject) => {
        pageInstance.$.dialog
            .page(
                '@sage/xtrem-finance-data/DimensionPanel',
                {
                    enteredDimensions: JSON.stringify({
                        storedAttributes: defaultDimensionsAttributes.attributes,
                        storedDimensions: defaultDimensionsAttributes.dimensions,
                    }),
                    disabledAction: JSON.stringify(options.disabledAction || {}),
                    editable: JSON.stringify(true),
                    isDefaultDimensionPage: JSON.stringify(true),
                    isDimensionPageToInherit: JSON.stringify(false),
                    calledFromMainGrid: JSON.stringify(false),
                    defaultedFromItem: JSON.stringify(defaultedFromItem || {}),
                },
                { size: 'large' },
            )
            .then((resolveData: interfaces.DefaultDimensions) => {
                resolve(resolveData);
            })
            .catch(() => {
                reject();
            });
    });
}
/**
 * Enforces type casting when needed from Partial to a non-partial document line.
 * @param line to be casted.
 * @returns the line casted
 */
export function fromPartialToBaseDocumentLineWithAnalytical<Node extends interfaces.BaseDocumentLineWithAnalytical>(
    line: ExtractEdgesPartial<Node>,
): Node {
    if (line._id) {
        return line as unknown as Node;
    }
    // Dev-only error so no translation
    throw new Error("DocumentLine passed doesn't contains the mandatory _id property...");
}

/**
 * Special handling for task attribute: if the user changed the project and deleted the task, the task must be
 * removed from the existingAttributes
 * @param existingAttributes
 * @param newAttributes
 * @returns existingAttributes, if project hasn't been changed, existingAttributes with task removed otherwise
 */
export function handleTaskFromSetDimensions(existingAttributes: string, newAttributes: string): string {
    const existingProject = JSON.parse(existingAttributes).project;
    const newProject = JSON.parse(newAttributes).project;
    const existingTask = JSON.parse(existingAttributes).task;
    const newTask = JSON.parse(newAttributes).task;
    // if the project has changed and the task was previously filled and the task is now empty, we remove the task
    if (newProject && !newTask && existingProject !== newProject && existingTask !== newTask) {
        const { task, ...newExistingAttributes } = JSON.parse(existingAttributes);
        return JSON.stringify(newExistingAttributes);
    }
    return existingAttributes;
}

/**
 * @internal
 * Applies the dimension or attributes to a line if needed
 * @param line on which the dimensions will be applied
 * @param stringData JSON string containing the dimensions to apply
 * @param type of the data can be 'attributes' or 'dimensions'
 * @returns the line modified.
 */
function applyDimensionsOrAttributes<Node extends interfaces.BaseDocumentLineWithAnalytical>(
    line: ui.PartialNodeWithId<Node>,
    stringData: string,
    type: 'attributes' | 'dimensions',
): ui.PartialNodeWithId<Node> {
    // using as string here as partial prevent typescript from resolving it
    let existingAttributes = (line.storedAttributes as string) || '{}';
    const existingDimensions = (line.storedDimensions as string) || '{}';
    if (type === 'attributes') {
        // special handling for task attribute: if the user changed the project and deleted the task, the task must be empty
        existingAttributes = handleTaskFromSetDimensions(existingAttributes, stringData);
    }
    return {
        ...line,
        storedAttributes:
            type === 'attributes'
                ? JSON.stringify({ ...JSON.parse(existingAttributes), ...JSON.parse(stringData) })
                : existingAttributes,
        storedDimensions:
            type === 'dimensions'
                ? JSON.stringify({ ...JSON.parse(existingDimensions), ...JSON.parse(stringData) })
                : existingDimensions,
    };
}

/**
 * Applies the default dimensions on all the available line if needed
 * @param gridControl the TableField on which the lines are
 * @param defaultDimensionsAttributes the default dimension object containing:
 *  - dimension (JSON string)
 *  - attributes (JSON string)
 *  - apply (string)
 * @param filter (optional) a document-line filter callback
 */
export function applyDefaultDimensionsToLines<Node extends interfaces.BaseDocumentLineWithAnalytical>(
    gridControl: ui.fields.Table<Node> | ui.fields.NestedGrid,
    defaultDimensionsAttributes: interfaces.DefaultDimensions,
    filter = documentLineFilterCallback,
) {
    if (gridControl instanceof ui.fields.Table && defaultDimensionsAttributes.action === 'apply') {
        gridControl.value.forEach(line => {
            if (filter(line)) {
                let newLine = { ...line };

                if (defaultDimensionsAttributes.attributes !== '{}') {
                    // using as string here as partial prevent typescript from resolving it
                    newLine = applyDimensionsOrAttributes(
                        newLine,
                        defaultDimensionsAttributes.attributes as string,
                        'attributes',
                    );
                }

                if (defaultDimensionsAttributes.dimensions !== '{}') {
                    // using as string here as partial prevent typescript from resolving it
                    newLine = applyDimensionsOrAttributes(
                        newLine,
                        defaultDimensionsAttributes.dimensions as string,
                        'dimensions',
                    );
                }

                if (!_.isEqual(line, newLine)) {
                    utils.applyToLineAndReturnData(gridControl, newLine);
                }
            }
        });
    }
}

/**
 * Wrapper for editDefaultDimensions, provide a more convenient way to use the panel
 * It applies default dimension if needed and handle the panel close/cancel buttons.
 * @param pageInstance the calling page's this (used to interact with the framework)
 * @param gridControl the TableField on which the lines are
 * @param defaultDimensionsAttributes an object containing the (existing) default dimensions and the apply action
 * @param filter (optional) a document-line filter callback
 * @returns the modified default dimension + action object.
 */
export function editAndApplyDefaultDimensionsIfNeeded(
    pageInstance: interfaces.PageWithDefaultDimensions | interfaces.PageWithMultipleDefaultDimensions,
    gridControl: ui.fields.TableControlObject | ui.fields.NestedGridControlObject,
    defaultDimensionsAttributes: interfaces.DefaultDimensions,
    filter = documentLineFilterCallback,
    defaultedFromItem?: string,
): Promise<interfaces.DefaultDimensions> {
    const oldIsDirty = pageInstance.$.isDirty;
    return utils
        .catchPanelCrossQuitButtonAsNoop(
            editDefaultDimensions(pageInstance, defaultDimensionsAttributes, defaultedFromItem || undefined, {
                disabledAction: { apply: gridControl instanceof ui.fields.NestedGridControlObject },
            }),
        )
        .then(resolveData => {
            if (resolveData) {
                applyDefaultDimensionsToLines(gridControl, resolveData, filter);
                return resolveData;
            }
            return defaultDimensionsAttributes;
        })
        .catch(() => {
            if (!oldIsDirty && pageInstance.$.isDirty) {
                pageInstance.$.setPageClean();
            }
            return defaultDimensionsAttributes;
        });
}

/**
 * Applies the default dimensions/attributes on the newly created line.
 * @param line the newly created document line.
 * @param defaultDimensionsAttributes an object containing the (existing) default dimensions and the apply action.
 * @returns the line modified.
 */
export function applyDefaultDimensionsOnNewLineIfNeeded<Node extends interfaces.BaseDocumentLineWithAnalytical>(
    line: ui.PartialNodeWithId<Node>,
    defaultDimensionsAttributes: interfaces.DefaultDimensions,
): ui.PartialNodeWithId<Node> {
    let newLine = { ...line };

    if (defaultDimensionsAttributes && defaultDimensionsAttributes.action) {
        if (defaultDimensionsAttributes.attributes !== '{}') {
            // using as string here as partial prevent typescript from resolving it
            newLine = applyDimensionsOrAttributes(
                newLine,
                defaultDimensionsAttributes.attributes as string,
                'attributes',
            );
        }

        if (defaultDimensionsAttributes.dimensions !== '{}') {
            // using as string here as partial prevent typescript from resolving it
            newLine = applyDimensionsOrAttributes(
                newLine,
                defaultDimensionsAttributes.dimensions as string,
                'dimensions',
            );
        }
    }

    return newLine;
}

/**
 * Gets you into the dimension panel in inheritance mode,
 * which is the same as the normal mode except for the action provided (apply to released item, apply to operations and components).
 * It handles the dimensions for work order: opening of the panel and
 * return the dimensions entered + action chosen.
 * @param pageInstance the calling page (used to interact with the framework)
 * @param defaultDimensionsAttributes an object containing the (existing) default dimensions and the apply action
 * @returns an object containing the default dimensions and the apply action
 */
export function editDimensionsToInherit<Node extends interfaces.BaseDocumentLineWithAnalytical>(
    pageInstance: interfaces.PageWithMultipleDefaultDimensions,
    data: Omit<interfaces.AnalyticalPanelParameters<Node>, 'editable' | 'disabledAction'>,
    options: Omit<interfaces.AnalyticalPanelParameters<Node>, 'documentLine'>,
): Promise<Node> {
    return new Promise((resolve, reject) => {
        pageInstance.$.dialog
            .page(
                '@sage/xtrem-finance-data/DimensionPanel',
                {
                    enteredDimensions: JSON.stringify({
                        storedAttributes: data.documentLine.storedAttributes,
                        storedDimensions: data.documentLine.storedDimensions,
                    }),
                    disabledAction: JSON.stringify(options.disabledAction || {}),
                    editable: JSON.stringify(options.editable),
                    isDefaultDimensionPage: JSON.stringify(false),
                    isDimensionPageToInherit: JSON.stringify(true),
                    calledFromMainGrid: JSON.stringify(false),
                    defaultedFromItem: '{}',
                },
                { size: 'large' },
            )
            .then((resolveData: interfaces.DefaultDimensionsToInherit) => {
                if (resolveData.action === 'applyAll') {
                    pageInstance.inheritDimensions = true;
                } else {
                    pageInstance.inheritDimensions = false;
                }
                resolve({
                    ...data.documentLine,
                    storedAttributes: resolveData.attributes,
                    storedDimensions: resolveData.dimensions,
                } as Node);
            })
            .catch(() => {
                reject();
            });
    });
}

/**
 * Manages the isDisabled flags of buttons/actions to access the dimension panel
 * @param pageRecordId
 * @param pageIsDirtyFlag
 * @returns a boolean for isDisable flag.
 */
export function isDefaultDimensionActionActive(pageRecordId: string, pageIsDirtyFlag: boolean): boolean {
    return utils.isEmptyOrNewRecordPage(pageRecordId) || !pageIsDirtyFlag;
}

/**
 * Initialize the default dimension object.
 * @returns default dimension object.
 */
export function initDefaultDimensions() {
    return { attributes: '{}', dimensions: '{}' };
}

/**
 * Initialize the default dimension object following the defaulting rules of the company.
 * @returns default dimension object.
 */
export async function initDefaultDimensionsFromSetup(
    page: ui.Page<GraphApi>,
    options: {
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        companyId: number;
        siteId: number;
        customerId?: number;
        supplierId?: number;
        itemId?: number;
        shippingSiteId?: number;
        receivingSiteId?: number;
    },
): Promise<interfaces.DefaultDimensions> {
    return {
        ...(await page.$.graph
            .node('@sage/xtrem-finance-data/DimensionDefinitionLevelAndDefault')
            .queries.getDefaultAttributesAndDimensions(
                { attributes: true, dimensions: true },
                { data: { ...options, onlyFromItem: false } },
            )
            .execute()),
        action: 'applyOnNew',
    };
}

/**
 * Initialize the default dimension object following the defaulting rules of the company for order to order management.
 * @returns default dimension object.
 */
export async function initDefaultDimensionsOrderToOrderFromSetup(
    page: ui.Page<GraphApi>,
    options: {
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        companyId: number;
        siteId: number;
        supplierId?: number;
        itemId?: number;
        storedAttributes: string;
        storedDimensions: string;
    },
): Promise<interfaces.DefaultDimensions> {
    return {
        ...(await page.$.graph
            .node('@sage/xtrem-finance-data/DimensionDefinitionLevelAndDefault')
            .queries.getDefaultAttributesAndDimensionsOrderToOrder(
                { attributes: true, dimensions: true },
                { data: options },
            )
            .execute()),
    };
}

/**
 * Initialize the default dimension object following the defaulting rules of the company only for those defaulting from item.
 * @returns default dimension object.
 */
export async function initDefaultDimensionsFromItem(
    page: ui.Page<GraphApi>,
    options: {
        dimensionDefinitionLevel: DimensionDefinitionLevel;
        companyId: number;
        siteId: number;
        itemId: number;
        receivingSiteId?: number;
    },
): Promise<interfaces.DefaultDimensions> {
    return {
        ...(await page.$.graph
            .node('@sage/xtrem-finance-data/DimensionDefinitionLevelAndDefault')
            .queries.getDefaultAttributesAndDimensions(
                { attributes: true, dimensions: true },
                { data: { ...options, onlyFromItem: true } },
            )
            .execute()),
        action: 'applyOnNew',
    };
}
