import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import type { Legislation } from '@sage/xtrem-structure-api';
import type { PostingClass as PostingClassPage } from '../pages/posting-class';

// TODO: Remove this workaround when the filter is fixed in the backend
// https://jira.sage.com/browse/XT-71936
export async function getTaxCategoryIds(parameters: {
    page: PostingClassPage;
    legislation?: ExtractEdgesPartial<Legislation> | Legislation;
}): Promise<string[]> {
    return parameters.legislation
        ? ((await parameters.page.$.graph
              .node('@sage/xtrem-finance-data/PostingClassLineDetail')
              .mutations.getTaxCategoryIds(true, {
                  legislation: parameters.legislation._id,
              })
              .execute()) as string[])
        : [];
}
