import { asyncArray } from '@sage/xtrem-async-helper';
import type { ExtractEdgesPartial } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type {
    Attribute,
    AttributeType,
    Dimension,
    DimensionDefinitionLevel,
    DimensionType,
    DocProperty,
    GraphApi,
    MasterDataDefault,
} from '@sage/xtrem-finance-data-api';
import type { Customer, Item, Supplier } from '@sage/xtrem-master-data-api';
import type { Company } from '@sage/xtrem-master-data/build/lib/pages/company';
import type { Site } from '@sage/xtrem-system-api';
import * as ui from '@sage/xtrem-ui';
import type { ExtensionMembers } from '@sage/xtrem-ui/build/lib/service/page-extension';
import * as _ from 'lodash';
import type { CompanyExtension } from '../page-extensions/company-extension';
import type { DimensionPanel } from '../pages/dimension-panel';
import * as dimensionPanelHelpers from './dimension-panel-helpers';
import type { Analytical, DefaultAttributesAndDimensions, DefaultDimensions, JsonDimensions } from './interfaces';

export const attributeIDs = () => ({
    financialSite: 'financialSite',
    businessSite: 'businessSite',
    stockSite: 'stockSite',
    manufacturingSite: 'manufacturingSite',
    supplier: 'supplier',
    customer: 'customer',
    project: 'project',
    task: 'task',
    employee: 'employee',
    item: 'item',
});

export const dimensionDocProperties = () => ({
    docProperty01: 'dimensionType01',
    docProperty02: 'dimensionType02',
    docProperty03: 'dimensionType03',
    docProperty04: 'dimensionType04',
    docProperty05: 'dimensionType05',
    docProperty06: 'dimensionType06',
    docProperty07: 'dimensionType07',
    docProperty08: 'dimensionType08',
    docProperty09: 'dimensionType09',
    docProperty10: 'dimensionType10',
    docProperty11: 'dimensionType11',
    docProperty12: 'dimensionType12',
    docProperty13: 'dimensionType13',
    docProperty14: 'dimensionType14',
    docProperty15: 'dimensionType15',
    docProperty16: 'dimensionType16',
    docProperty17: 'dimensionType17',
    docProperty18: 'dimensionType18',
    docProperty19: 'dimensionType19',
    docProperty20: 'dimensionType20',
});

export interface BaseActiveType {
    _id: string;
    isActive: boolean;
    name: string;
}

export interface ActiveAttributeType extends BaseActiveType {
    id: string;
}

export interface ActiveDimensionType extends BaseActiveType {
    docProperty: DocProperty;
}

const attributeFields = ['employee', 'project', 'task'];

export async function getActiveAttributes(
    page: ui.Page<GraphApi>,
    params: { onlyNodeLinkAttribute: boolean } = { onlyNodeLinkAttribute: false },
): Promise<ActiveAttributeType[]> {
    const filter = params.onlyNodeLinkAttribute ? { isActive: true, nodeLink: 'attribute' } : { isActive: true };

    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance-data/AttributeType')
            .query(ui.queryUtils.edgesSelector({ _id: true, id: true, isActive: true, name: true }, { filter }))
            .execute(),
    );
}

export async function getActiveDimensions(page: ui.Page<GraphApi>): Promise<ActiveDimensionType[]> {
    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance-data/DimensionType')
            .query(
                ui.queryUtils.edgesSelector(
                    { _id: true, isActive: true, name: true, docProperty: true },
                    { filter: { isActive: true } },
                ),
            )
            .execute(),
    );
}

export function getDimensionTypeName(dimensionTypes: ActiveDimensionType[], docProperty: string): string {
    return dimensionTypes.find(dimensionType => dimensionType.docProperty === docProperty)?.name || '';
}

export function getAttributeTypeName(attributeTypes: ActiveAttributeType[], attributeTypeId: string): string {
    return attributeTypes.find(attributeType => attributeType.id === attributeTypeId)?.name || '';
}

/**
 * If the site of the page was already entered, init the dimension panel for 'Set dimensions'
 * with the default values following the rules of the company, otherwise init the panel with empty values
 * @param options.page
 * @param options.dimensionDefinitionLevel either manufacturingDirect or purchasingDirect or salesDirect
 * @param options.site
 * @param options.customer
 * @param options.supplier
 * @param options.item
 */
export function initDefaultDimensions<Context extends ui.Page = ui.Page<GraphApi>>(options: {
    page: Context;
    dimensionDefinitionLevel: DimensionDefinitionLevel;
    site: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    item?: ExtractEdgesPartial<Item> | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
    shippingSite?: ExtractEdgesPartial<Site> | null;
}): Promise<DefaultDimensions> {
    if (options.site) {
        // if a document was selected from the main list => get default attributes/dimensions
        return dimensionPanelHelpers.initDefaultDimensionsFromSetup(options.page, {
            dimensionDefinitionLevel: options.dimensionDefinitionLevel,
            companyId: Number(options.site.legalCompany?._id),
            siteId: Number(options.site._id),
            ...(options.customer ? { customerId: Number(options.customer._id) } : {}),
            ...(options.supplier ? { supplierId: Number(options.supplier._id) } : {}),
            ...(options.item ? { itemId: Number(options.item._id) } : {}),
            ...(options.shippingSite ? { shippingSiteId: Number(options.shippingSite._id) } : {}),
            ...(options.receivingSite ? { receivingSiteId: Number(options.receivingSite._id) } : {}),
        });
    }
    // if no document was selected from the main list => init attributes/dimensions with empty values
    return Promise.resolve(dimensionPanelHelpers.initDefaultDimensions());
}

export async function getAttributeTypes(page: ui.Page<GraphApi>) {
    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance-data/AttributeType')
            .query(
                ui.queryUtils.edgesSelector<AttributeType>(
                    {
                        id: true,
                        name: true,
                        isActive: true,
                        attributeTypeRestrictedTo: { id: true },
                        isLinkedToItem: true,
                        isLinkedToSite: true,
                    },
                    { filter: { id: { _in: attributeFields } } },
                ),
            )
            .execute(),
    ) as ExtractEdgesPartial<AttributeType>[];
}

function isDimensionPanel(page: ui.Page<GraphApi>): page is DimensionPanel {
    function areDimensionsAndAttributesPresent(): boolean {
        const dimensionFields = Object.keys(page._pageMetadata.defaultUiComponentProperties).filter(
            key => key.search(/dimension\d{2}/) !== -1,
        );
        return dimensionFields.every(field => field in page) && attributeFields.every(field => field in page);
    }

    if (!page || !areDimensionsAndAttributesPresent()) {
        throw new Error(
            ui.localize(
                '@sage/xtrem-finance-data/pages__dimension_panel__page_without_dimensions',
                'The page needs to have attributes and dimensions.',
            ),
        );
    }
    return true;
}

/**
 * This function is called from the DimensionPanel or Site or Customer or Supplier page to manage the attributes types restricted to
 * @param options.page
 */
export function manageAttributeTypeRestrictedToOnPage(options: { page: ui.Page<GraphApi>; clearTask: boolean }) {
    if (isDimensionPanel(options.page)) {
        if (options.page.taskRestrictedTo === 'project' && options.page.project.value === null) {
            options.page.task.isDisabled = true;
        } else {
            options.page.task.isDisabled = false;
        }
        if (options.clearTask) {
            options.page.task.value = null;
        }
    }
}

/**
 * This function is called from the DimensionPanel or Site or Customer or Supplier page to manage the attributes and dimensions
 * on load of the page.
 * It will hide the attributes and dimensions that are not active and set the title of the attributes and dimensions
 * @param options.page
 * @param options.isEditable it controls whether the attribute or dimension is disabled or not
 */
export async function manageAttributesDimensions(options: { page: ui.Page<GraphApi>; isEditable: boolean }) {
    const { page } = options;
    if (isDimensionPanel(page)) {
        const attributeTypes = extractEdges(
            await page.$.graph
                .node('@sage/xtrem-finance-data/AttributeType')
                .query(
                    ui.queryUtils.edgesSelector<AttributeType>(
                        {
                            id: true,
                            name: true,
                            isActive: true,
                            attributeTypeRestrictedTo: { id: true },
                        },
                        { filter: { id: { _in: attributeFields } } },
                    ),
                )
                .execute(),
        ) as ExtractEdgesPartial<AttributeType>[];

        const dimensionTypes = extractEdges(
            await page.$.graph
                .node('@sage/xtrem-finance-data/DimensionType')
                .query(ui.queryUtils.edgesSelector<DimensionType>({ name: true, docProperty: true, isActive: true }))
                .execute(),
        ) as ExtractEdgesPartial<DimensionType>[];

        attributeTypes.forEach(attributeType => {
            if (attributeType.id === 'employee') {
                page.employee.isHidden = !attributeType.isActive;
                page.employee.title = attributeType.name;
                page.employee.isDisabled = !options.isEditable;
            }
            if (attributeType.id === 'project') {
                page.project.isHidden = !attributeType.isActive;
                page.project.title = attributeType.name;
                page.project.isDisabled = !options.isEditable;
            }
            if (attributeType.id === 'task') {
                page.task.isHidden = !attributeType.isActive;
                page.task.title = attributeType.name;
                page.task.isDisabled = !options.isEditable;
            }
        });
        dimensionTypes.forEach(dimensionType => {
            const docProperty = dimensionType.docProperty?.toString();
            if (docProperty) {
                (
                    page[docProperty.replace('Type', '') as keyof DimensionPanel] as ui.fields.Reference<Dimension>
                ).isHidden = !dimensionType.isActive;
                (
                    page[docProperty.replace('Type', '') as keyof DimensionPanel] as ui.fields.Reference<Dimension>
                ).title = dimensionType.name;
                (
                    page[docProperty.replace('Type', '') as keyof DimensionPanel] as ui.fields.Reference<Dimension>
                ).isDisabled = !options.isEditable;
            }
        });

        // Disable attribute type task if project is null and task is linked to project
        const restrictedTo = attributeTypes.find(attributeType => attributeType.id === 'task');
        page.taskRestrictedTo = restrictedTo?.attributeTypeRestrictedTo?.id || null;
        manageAttributeTypeRestrictedToOnPage({ page, clearTask: false });
    }
}

/**
 * This function reads a dimension from the database and returns it.
 * @param options.page
 * @param options.type the type of the dimension to read
 * @param options.id the id of the dimension to read
 */
async function getDimension(page: ui.Page<GraphApi>, type: string, id: string) {
    if (id && isDimensionPanel(page)) {
        const [dimension] = extractEdges(
            await page.$.graph
                .node('@sage/xtrem-finance-data/Dimension')
                .query(
                    ui.queryUtils.edgesSelector<Dimension>(
                        {
                            _id: true,
                            id: true,
                            name: true,
                            dimensionType: { name: true, docProperty: true },
                        },
                        { filter: { id, dimensionType: { docProperty: type as DocProperty } } },
                    ),
                )
                .execute(),
        ) as ExtractEdgesPartial<Dimension>[];
        if (dimension && dimension.dimensionType?.docProperty)
            (
                page[
                    dimension.dimensionType.docProperty.toString().replace('Type', '') as keyof DimensionPanel
                ] as ui.fields.Reference<Dimension>
            ).value = dimension;
    }
}

/**
 * This function reads several attributes from the database and returns them.
 * @param page
 * @param ids the array of ids of the attributes to read
 */
async function getAttributes(page: ui.Page<GraphApi>, ids: string[]) {
    if (ids) {
        return extractEdges(
            await page.$.graph
                .node('@sage/xtrem-finance-data/Attribute')
                .query(
                    ui.queryUtils.edgesSelector<Attribute>(
                        {
                            _id: true,
                            id: true,
                            name: true,
                            attributeRestrictedToId: true,
                            attributeType: { id: true },
                        },
                        { filter: { id: { _in: ids }, attributeType: { id: { _in: attributeFields } } } },
                    ),
                )
                .execute(),
        ) as ExtractEdgesPartial<Attribute>[];
    }
    return null;
}

/**
 * This function is called from the DimensionPanel or Site or Customer or Supplier page to set values to the attributes and dimensions
 * on load of the page.
 * @param options.page
 * @param options.inputData the data to set the values of the attributes and dimensions
 */
export async function setAttributesLineValues(options: { page: ui.Page<GraphApi>; inputData: Analytical }) {
    const { page } = options;

    if (isDimensionPanel(page)) {
        if (options.inputData.storedAttributes) {
            const { employee, project, task } = JSON.parse(options.inputData.storedAttributes);

            const attributes = await getAttributes(options.page, [employee, project, task]);

            page.employee.value = attributes?.find(attribute => attribute.attributeType?.id === 'employee') || null;

            page.project.value = attributes?.find(attribute => attribute.attributeType?.id === 'project') || null;

            page.task.value =
                attributes?.find(
                    attribute =>
                        attribute.attributeType?.id === 'task' &&
                        attribute.attributeRestrictedToId === page.project.value?.id,
                ) || null;
        }

        if (options.inputData.storedDimensions) {
            const dimensions = JSON.parse(options.inputData.storedDimensions);
            await asyncArray(Object.keys(dimensions)).forEach(key => getDimension(options.page, key, dimensions[key]));
        }
    }
}

/**
 * This function is called from the DimensionPanel or Site or Customer or Supplier page to prepare the dimensions on save of the page.
 * @param options.page
 * @returns the dimensions in JSON format
 */
export function prepareDimensions(page: ui.Page<GraphApi>) {
    const result: JsonDimensions = {};
    if (isDimensionPanel(page)) {
        const dimensionFields = Object.keys(page._pageMetadata.defaultUiComponentProperties).filter(
            key => key.search(/dimension\d{2}/) !== -1,
        );
        dimensionFields
            .filter(keyFilter => (page[keyFilter as keyof DimensionPanel] as ui.fields.Reference<Dimension>).value)
            .forEach(key => {
                const dimensionField = page[key as keyof DimensionPanel] as ui.fields.Reference<Dimension>;
                _.set(result, dimensionField.value?.dimensionType?.docProperty || '', dimensionField.value?.id || '');
            });
    }
    return JSON.stringify(result);
}

/**
 * This function is called from the DimensionPanel or Site or Customer or Supplier page to prepare the attributes on save of the page.
 * @param options.page
 * @returns the attributes in JSON format
 */
export function prepareAttributes(page: ui.Page<GraphApi>) {
    if (isDimensionPanel(page)) {
        return JSON.stringify({
            employee: page.employee.value?.id,
            project: page.project.value?.id,
            task: page.task.value?.id,
        });
    }
    return '{}';
}

async function getAttributeTypesForSort(page: ui.Page<GraphApi>) {
    return (
        extractEdges(
            await page.$.graph
                .node('@sage/xtrem-finance-data/AttributeType')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            name: true,
                            id: true,
                            analyticalMeasureType: true,
                            attributeTypeRestrictedTo: { name: true },
                        },
                        { filter: { isActive: true, nodeLink: 'attribute' } },
                    ),
                )
                .execute(),
        ).map(element => ({
            attributeDimensionTypeId: element._id,
            id: element.name,
            type: element.analyticalMeasureType,
            attributeTypeRestrictedTo: element.attributeTypeRestrictedTo?.name,
        })) ?? []
    );
}

async function getDimensionTypesForSort(page: ui.Page<GraphApi>) {
    return (
        extractEdges(
            await page.$.graph
                .node('@sage/xtrem-finance-data/DimensionType')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            _id: true,
                            docProperty: true,
                            name: true,
                            analyticalMeasureType: true,
                        },
                        { filter: { isActive: true } },
                    ),
                )
                .execute(),
        ).map(element => ({
            attributeDimensionTypeId: element._id,
            id: element.name,
            docProperty: element.docProperty,
            type: element.analyticalMeasureType,
            attributeTypeRestrictedTo: '',
        })) ?? []
    );
}

/**
 * This function is called from the CompanyExtension page when the user adds a new line to the mandatory dimensions.
 */
export async function initAttributeDimensionTypes(page: ExtensionMembers<CompanyExtension & Company>) {
    const sortedAttributeTypes = (await getAttributeTypesForSort(page)).sort((e1, e2) => e1.id.localeCompare(e2.id));
    const sortedDimensionTypes = (await getDimensionTypesForSort(page)).sort((e1, e2) =>
        e1.docProperty.localeCompare(e2.docProperty),
    );
    const attributeDimensionTypes = sortedAttributeTypes.concat(sortedDimensionTypes);

    const selectionAttributeDimensionTypes = attributeDimensionTypes.filter(
        line =>
            !page.attributeDimensionTypes.value.some(
                selectLine =>
                    selectLine.id === line.id &&
                    selectLine.type === line.type &&
                    selectLine.attributeDimensionTypeId === line.attributeDimensionTypeId,
            ),
    );
    selectionAttributeDimensionTypes
        .map(element => ({
            attributeDimensionTypeId: element.attributeDimensionTypeId,
            id: element.id,
            type: element.type,
            attributeTypeRestrictedTo: element.attributeTypeRestrictedTo,
        }))
        .forEach(gridLine => {
            page.selectDimensionAndAttributeTypes.addOrUpdateRecordValue(gridLine);
        });
    page.$.setPageClean();
}

function getCompanyDefaultAttributes(page: CompanyExtension) {
    const defaultAttributes = page.defaultAttributes.value.map(element => ({
        document: element.dimensionDefinitionLevel,
        masterDataDefault: element.masterDataDefault,
        name: element.attributeType?.id,
        attributeTypeRestrictedTo: element.attributeType?.attributeTypeRestrictedTo,
    }));
    if (defaultAttributes) {
        return defaultAttributes;
    }
    return [];
}

function getCompanyDefaultDimensions(page: CompanyExtension) {
    const defaultDimensions = page.defaultDimensions.value.map(element => ({
        document: element.dimensionDefinitionLevel,
        masterDataDefault: element.masterDataDefault,
        name: element.dimensionType?.docProperty,
        attributeTypeRestrictedTo: undefined as any,
    }));
    if (defaultDimensions) {
        return defaultDimensions;
    }
    return [];
}

/**
 * This function is called from the CompanyExtension page from the onLoad event.
 */
export function initDefaultAttributesAndDimensions(page: CompanyExtension) {
    page.defaultAttributesAndDimensions.value = [];
    // read all company default attributes and dimensions and order by dimensionDefinitionLevel (document)
    const defaultAttributesAndDimensions = getCompanyDefaultAttributes(page)
        .concat(getCompanyDefaultDimensions(page))
        .sort((e1, e2) => {
            if ((e1.document || '') > (e2.document || '')) {
                return 1;
            }
            if ((e1.document || '') < (e2.document || '')) {
                return -1;
            }
            return 0;
        });

    if (defaultAttributesAndDimensions.length > 0) {
        let actLine: Partial<DefaultAttributesAndDimensions> = {};
        defaultAttributesAndDimensions.forEach(element => {
            // change of document => add line to grid and reset data on actLine
            if (actLine.document !== undefined && element.document !== actLine.document) {
                page.defaultAttributesAndDimensions.addOrUpdateRecordValue(actLine);
                actLine = {}; // reset all properties to undefined
            }
            actLine.document = element.document;
            switch (element.name) {
                case 'employee':
                    actLine.employeeAttribute = element.masterDataDefault;
                    break;
                case 'project':
                    actLine.projectAttribute = element.masterDataDefault;
                    break;
                case 'task':
                    actLine.taskAttribute = element.masterDataDefault;
                    break;
                case 'dimensionType01':
                    actLine.dimension01 = element.masterDataDefault;
                    break;
                case 'dimensionType02':
                    actLine.dimension02 = element.masterDataDefault;
                    break;
                case 'dimensionType03':
                    actLine.dimension03 = element.masterDataDefault;
                    break;
                case 'dimensionType04':
                    actLine.dimension04 = element.masterDataDefault;
                    break;
                case 'dimensionType05':
                    actLine.dimension05 = element.masterDataDefault;
                    break;
                case 'dimensionType06':
                    actLine.dimension06 = element.masterDataDefault;
                    break;
                case 'dimensionType07':
                    actLine.dimension07 = element.masterDataDefault;
                    break;
                case 'dimensionType08':
                    actLine.dimension08 = element.masterDataDefault;
                    break;
                case 'dimensionType09':
                    actLine.dimension09 = element.masterDataDefault;
                    break;
                case 'dimensionType10':
                    actLine.dimension10 = element.masterDataDefault;
                    break;
                case 'dimensionType11':
                    actLine.dimension11 = element.masterDataDefault;
                    break;
                case 'dimensionType12':
                    actLine.dimension12 = element.masterDataDefault;
                    break;
                case 'dimensionType13':
                    actLine.dimension13 = element.masterDataDefault;
                    break;
                case 'dimensionType14':
                    actLine.dimension14 = element.masterDataDefault;
                    break;
                case 'dimensionType15':
                    actLine.dimension15 = element.masterDataDefault;
                    break;
                case 'dimensionType16':
                    actLine.dimension16 = element.masterDataDefault;
                    break;
                case 'dimensionType17':
                    actLine.dimension17 = element.masterDataDefault;
                    break;
                case 'dimensionType18':
                    actLine.dimension18 = element.masterDataDefault;
                    break;
                case 'dimensionType19':
                    actLine.dimension19 = element.masterDataDefault;
                    break;
                case 'dimensionType20':
                    actLine.dimension20 = element.masterDataDefault;
                    break;
                default:
            }
        });
        page.defaultAttributesAndDimensions.addOrUpdateRecordValue(actLine); // add last line to grid
        page.$.setPageClean();
    }
}

/**
 * This function is called from the CompanyExtension page whenever a default value for an attribute is changed (from onChange event).
 */
export function onDefaultAttributeChange(
    page: ExtensionMembers<CompanyExtension & Company>,
    rowData: DefaultAttributesAndDimensions,
    id: string,
    masterDataDefault: MasterDataDefault | undefined | string,
) {
    // check if the attribute already exists in the company default attributes table
    const sysId = page.defaultAttributes.value.find(
        attribute => attribute.attributeType?.id === id && attribute.dimensionDefinitionLevel === rowData.document,
    )?._id;
    if (sysId) {
        if (!masterDataDefault || masterDataDefault === ' ') {
            // delete the record if the attribute is empty now
            page.defaultAttributes.removeRecord(sysId);
        } else {
            // update the record if the attribute has changed
            const attribute = page.defaultAttributes.getRecordValue(sysId);
            if (attribute) {
                attribute.masterDataDefault = masterDataDefault as MasterDataDefault;
                page.defaultAttributes.addOrUpdateRecordValue(attribute);
            }
        }
    } else {
        // insert the record if the attribute did not exist before
        const attributeType = page.activeAttributeTypes.find(type => type.id === id);
        page.defaultAttributes.addOrUpdateRecordValue({
            attributeType,
            company: { _id: page.$.recordId },
            dimensionDefinitionLevel: rowData.document,
            masterDataDefault: masterDataDefault as MasterDataDefault,
        });
    }
}

/**
 * This function is called from the CompanyExtension page whenever a default value for a dimension is changed (from onChange event).
 */
export function onDefaultDimensionChange(
    page: ExtensionMembers<CompanyExtension & Company>,
    rowData: DefaultAttributesAndDimensions,
    docProperty: DocProperty,
    masterDataDefault: MasterDataDefault | undefined | string,
) {
    // check if the dimension already exists in the company default dimension table
    const sysId = page.defaultDimensions.value.find(
        dimension =>
            dimension.dimensionType?.docProperty === docProperty &&
            dimension.dimensionDefinitionLevel === rowData.document,
    )?._id;
    if (sysId) {
        if (!masterDataDefault || masterDataDefault === ' ') {
            // delete the record if the dimension is empty now
            page.defaultDimensions.removeRecord(sysId);
        } else {
            // update the record if the dimension has changed
            const dimension = page.defaultDimensions.getRecordValue(sysId);
            if (dimension) {
                dimension.masterDataDefault = masterDataDefault as MasterDataDefault;
                page.defaultDimensions.addOrUpdateRecordValue(dimension);
            }
        }
    } else {
        // insert the record if the dimension did not exist before
        const dimensionType = page.activeDimensionTypes.find(type => type.docProperty === docProperty);
        page.defaultDimensions.addOrUpdateRecordValue({
            dimensionType,
            company: { _id: page.$.recordId },
            dimensionDefinitionLevel: rowData.document,
            masterDataDefault: masterDataDefault as MasterDataDefault,
        });
    }
}

/**
 * This function is called from the CompanyExtension page.
 * @param options.page
 * @returns array of DimensionDefinitionLevelAndDefault
 */
export async function getMasterDataDefaultOptionsFromDatabase(page: CompanyExtension) {
    return extractEdges(
        await page.$.graph
            .node('@sage/xtrem-finance-data/DimensionDefinitionLevelAndDefault')
            .query(
                ui.queryUtils.edgesSelector(
                    {
                        dimensionDefinitionLevel: true,
                        masterDataDefault: true,
                    },
                    { first: 50 },
                ),
            )
            .execute(),
    ).map(element => ({
        document: element.dimensionDefinitionLevel,
        default: element.masterDataDefault,
    }));
}

/**
 * This function is called from the CompanyExtension page.
 * @param page
 * @param rowData
 * @returns array of string (MasterDataDefault)
 */
export function getMasterDataDefaultOptions(
    page: ExtensionMembers<CompanyExtension & Company>,
    rowData: DefaultAttributesAndDimensions,
) {
    return page.masterDataDefaultOptions
        .filter(element => element.document === rowData.document)
        .map(element => element.default.toString());
}

function getCompanyAttributeTypes(page: CompanyExtension) {
    const attributeTypes = page.attributeTypes.value.map(element => ({
        companyAttributeDimensionTypeId: element._id,
        attributeDimensionTypeId: element.attributeType?._id,
        id: element.attributeType?.name,
        type: element.analyticalMeasureType,
    }));
    if (attributeTypes) {
        return attributeTypes;
    }
    return [];
}

function getCompanyDimensionTypes(page: CompanyExtension) {
    const dimensionTypes = page.dimensionTypes.value.map(element => ({
        companyAttributeDimensionTypeId: element._id,
        attributeDimensionTypeId: element.dimensionType?._id,
        id: element.dimensionType?.name,
        docProperty: element.dimensionType?.docProperty,
        type: element.analyticalMeasureType,
    }));
    if (dimensionTypes) {
        return dimensionTypes;
    }
    return [];
}

/**
 * This function is called from the CompanyExtension page from the onLoad event.
 */
export function initCompanyAttributeDimensionTypes(page: CompanyExtension) {
    const sortedCompanyAttributeTypes = getCompanyAttributeTypes(page).sort((e1, e2) =>
        (e1.id || '').localeCompare(e2.id || ''),
    );
    const sortedCompanyDimensionTypes = getCompanyDimensionTypes(page).sort((e1, e2) =>
        (e1.docProperty || '').localeCompare(e2.docProperty || ''),
    );
    const attributeAndDimension = sortedCompanyAttributeTypes.concat(sortedCompanyDimensionTypes);

    attributeAndDimension.forEach(gridLine => {
        page.attributeDimensionTypes.addOrUpdateRecordValue(gridLine);
    });
    page.$.setPageClean();
}

/**
 * Initialize the default dimension object for order to order management following the defaulting rules of the company.
 * @returns default dimension object.
 */
export function initDefaultDimensionsOrderToOrder(options: {
    page: ui.Page<GraphApi>;
    dimensionDefinitionLevel: DimensionDefinitionLevel;
    site: ExtractEdgesPartial<Site> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    item?: ExtractEdgesPartial<Item> | null;
    storedAttributes: string;
    storedDimensions: string;
}): Promise<DefaultDimensions> {
    if (options.site) {
        return dimensionPanelHelpers.initDefaultDimensionsOrderToOrderFromSetup(options.page, {
            dimensionDefinitionLevel: options.dimensionDefinitionLevel,
            companyId: Number(options.site.legalCompany?._id),
            siteId: Number(options.site._id),
            ...(options.supplier ? { supplierId: Number(options.supplier._id) } : {}),
            ...(options.item ? { itemId: Number(options.item._id) } : {}),
            storedAttributes: options.storedAttributes,
            storedDimensions: options.storedDimensions,
        });
    }
    return Promise.resolve(dimensionPanelHelpers.initDefaultDimensions());
}

/**
 * Checks if on company there are default settings using the item as source for defaulting. If yes, it
 * returns a comma separated string with the names of the attributes/dimensions that are defaulted from item
 * @param options.page
 * @param options.dimensionDefinitionLevel
 * @param options.companyId
 * @returns empty string if no attribute/dimension is defaulted from item, list of attributes/dimensions else
 */
export async function getAttributesAndDimensionsFromItem(options: {
    page: ui.Page<GraphApi>;
    dimensionDefinitionLevel: DimensionDefinitionLevel;
    companyId: number;
}): Promise<string> {
    const itemList = (await options.page.$.graph
        .node('@sage/xtrem-finance-data/DimensionDefinitionLevelAndDefault')
        .queries.getAttributesAndDimensionsFromItem(true, {
            data: { dimensionDefinitionLevel: options.dimensionDefinitionLevel, companyId: options.companyId },
        })
        .execute()) as string[];
    if (itemList.length > 1) {
        return ui.localize(
            '@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_plural',
            '{{itemList}} values default from the item and do not display at this level. You need to enter these values on the line for each item.',
            { itemList },
        );
    }
    if (itemList.length > 0) {
        return ui.localize(
            '@sage/xtrem-finance-data/pages__dimension_panel__defaulted_from_item_singular',
            '{{itemList}} value defaults from the item and does not display at this level. You need to enter this value on the line for each item.',
            { itemList },
        );
    }
    return '';
}

export async function defaultAttributesAndDimensionsWithItem(options: {
    page: ui.Page<GraphApi>;
    _defaultDimensionsAttributes: DefaultDimensions;
    dimensionDefinitionLevel: DimensionDefinitionLevel;
    site: ExtractEdgesPartial<Site> | null;
    customer?: ExtractEdgesPartial<Customer> | null;
    supplier?: ExtractEdgesPartial<Supplier> | null;
    item?: ExtractEdgesPartial<Item> | null;
    receivingSite?: ExtractEdgesPartial<Site> | null;
}): Promise<{ storedAttributes: string | undefined; storedDimensions: string | undefined }> {
    let defaultDimensionsAttributes = {} as DefaultDimensions;
    if (options.item) {
        // get all values defaulted (only) from the item
        defaultDimensionsAttributes = await dimensionPanelHelpers.initDefaultDimensionsFromItem(options.page, {
            dimensionDefinitionLevel: options.dimensionDefinitionLevel,
            companyId: Number(options.site?.legalCompany?._id),
            siteId: Number(options.site?._id),
            itemId: Number(options.item._id),
            receivingSiteId: Number(options.receivingSite?._id),
        });
    }
    if (!_.isEmpty(defaultDimensionsAttributes)) {
        // special handling for task attribute: if the user changed the project and deleted the task, the task must be empty
        defaultDimensionsAttributes.attributes = dimensionPanelHelpers.handleTaskFromSetDimensions(
            defaultDimensionsAttributes.attributes,
            options._defaultDimensionsAttributes.attributes,
        );
    }
    // if the user has entered something in the 'set dimensions' before, overwrite these values except the ones
    // where the user hasn't entered something in the dialog
    if (options._defaultDimensionsAttributes && options._defaultDimensionsAttributes.action) {
        defaultDimensionsAttributes.attributes = JSON.stringify({
            ...JSON.parse(defaultDimensionsAttributes.attributes),
            ...JSON.parse(options._defaultDimensionsAttributes.attributes),
        });
        defaultDimensionsAttributes.dimensions = JSON.stringify({
            ...JSON.parse(defaultDimensionsAttributes.dimensions),
            ...JSON.parse(options._defaultDimensionsAttributes.dimensions),
        });
    }

    const line = dimensionPanelHelpers.applyDefaultDimensionsOnNewLineIfNeeded(
        {} as ui.PartialNodeWithId<Node>,
        defaultDimensionsAttributes,
    );

    return { storedAttributes: line.storedAttributes, storedDimensions: line.storedDimensions };
}
