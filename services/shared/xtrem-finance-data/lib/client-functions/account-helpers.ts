import { withoutEdges } from '@sage/xtrem-client';
import * as ui from '@sage/xtrem-ui';
import type { Account as AccountPage } from '../pages/account';

export async function getCountryIds(page: AccountPage): Promise<string[]> {
    return page.chartOfAccount.value
        ? withoutEdges(
              await page.$.graph
                  .node('@sage/xtrem-structure/Country')
                  .query(
                      ui.queryUtils.edgesSelector(
                          {
                              id: true,
                          },
                          {
                              filter: {
                                  legislation: {
                                      id: await (await page.chartOfAccount.value?.legislation)?.id,
                                  },
                              },
                          },
                      ),
                  )
                  .execute(),
          ).map(country => country.id)
        : [];
}
