import type { AnalyticalMeasureType, DimensionDefinitionLevel, MasterDataDefault } from '@sage/xtrem-finance-data-api';
import type { BaseDocumentLine } from '@sage/xtrem-master-data-api';
import type * as ui from '@sage/xtrem-ui';

export interface JsonDimensions {
    [type: string]: string;
}

export interface StoredAttributes {
    project: string;
    task: string;
    employee: string;
    [key: string]: string;
}

export type Analytical = {
    computedAttributes: string;
    storedAttributes: string;
    storedDimensions: string;
};

type OmitUnionType = '_constructor' | '_factory' | 'documentNumber' | 'documentId' | '_createUser' | '_updateUser';

export interface BaseDocumentLineWithAnalytical extends Omit<BaseDocumentLine, OmitUnionType>, Analytical {}

export type DimensionsPanelDisabledActions = {
    apply?: boolean;
    applyOnNew?: boolean;
    ok?: boolean;
};

export type AnalyticalPanelParameters<N extends BaseDocumentLineWithAnalytical> = {
    disabledAction?: DimensionsPanelDisabledActions;
    documentLine: N;
    editable: boolean;
    calledFromMainGrid?: boolean;
};

export interface PageWithDefaultDimensions extends ui.Page {
    _defaultDimensionsAttributes: DefaultDimensions;
    defaultDimension: ui.PageAction;
}

export interface PageWithMultipleDefaultDimensions extends ui.Page {
    _defaultDimensionsAttributesArray: DefaultDimensions[];
    defaultDimensionActionArray: ui.PageAction[];
    inheritDimensions?: boolean;
}

export type DefaultDimensions = {
    attributes: string;
    dimensions: string;
    action?: 'apply' | 'applyOnNew';
};

export type DefaultDimensionsToInherit = {
    attributes: string;
    dimensions: string;
    action?: 'applyHeader' | 'applyAll';
};

export interface AttributeDimensionTypes {
    _id: string;
    accountAttributeDimensionTypeId?: string;
    companyAttributeDimensionTypeId?: string;
    attributeDimensionTypeId: string;
    id: string;
    type: AnalyticalMeasureType;
    attributeTypeRestrictedTo?: string;
}

// interface for the transient default dimensions grid on company-extension
export interface DefaultAttributesAndDimensions {
    _id: string;
    document: DimensionDefinitionLevel | undefined;
    projectAttribute: MasterDataDefault | undefined;
    taskAttribute: MasterDataDefault | undefined;
    employeeAttribute: MasterDataDefault | undefined;
    dimension01: MasterDataDefault | undefined;
    dimension02: MasterDataDefault | undefined;
    dimension03: MasterDataDefault | undefined;
    dimension04: MasterDataDefault | undefined;
    dimension05: MasterDataDefault | undefined;
    dimension06: MasterDataDefault | undefined;
    dimension07: MasterDataDefault | undefined;
    dimension08: MasterDataDefault | undefined;
    dimension09: MasterDataDefault | undefined;
    dimension10: MasterDataDefault | undefined;
    dimension11: MasterDataDefault | undefined;
    dimension12: MasterDataDefault | undefined;
    dimension13: MasterDataDefault | undefined;
    dimension14: MasterDataDefault | undefined;
    dimension15: MasterDataDefault | undefined;
    dimension16: MasterDataDefault | undefined;
    dimension17: MasterDataDefault | undefined;
    dimension18: MasterDataDefault | undefined;
    dimension19: MasterDataDefault | undefined;
    dimension20: MasterDataDefault | undefined;
}
