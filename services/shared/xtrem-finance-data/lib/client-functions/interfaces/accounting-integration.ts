import type {
    Account,
    FinanceDocumentType,
    FinanceIntegrationApp,
    PostingStatus,
    SourceDocumentType,
    TargetDocumentType,
} from '@sage/xtrem-finance-data-api';
import type { CurrencyBinding, ItemBinding, SupplierBinding, UnitOfMeasureBinding } from '@sage/xtrem-master-data-api';
import type { decimal, integer } from '@sage/xtrem-shared';
import type { CompanyBinding, SiteBinding } from '@sage/xtrem-system-api';
import type * as ui from '@sage/xtrem-ui';

// Interface for "Unbilled account payable" inquiry to be used in purchaseReceiptLine node for unbilledAccountPayable query
// the interface for the result array
// used in nodes (xtrem-purchasing) and pages => should be moved in shared-functions (but impossible because of the use of Account)

export interface FinanceUnbilledAccountPayable {
    billBySupplier: ui.PartialNodeWithId<SupplierBinding>;
    supplier: ui.PartialNodeWithId<SupplierBinding>;
    currency: ui.PartialNodeWithId<CurrencyBinding>;
    financialSite: ui.PartialNodeWithId<SiteBinding>;
    purchaseUnit: ui.PartialNodeWithId<UnitOfMeasureBinding>;
    netPrice: decimal;
    item: ui.PartialNodeWithId<ItemBinding>;
    stockSite: ui.PartialNodeWithId<SiteBinding>;
    quantity: decimal;
    account?: ui.PartialNodeWithId<Account>;
    accountItem?: ui.PartialNodeWithId<Account>;
    invoicedQuantity: decimal;
    creditedQuantity: decimal;
    returnedQuantity: decimal;
    invoiceReceivableQuantity: decimal;
    invoiceReceivableAmount: decimal;
    company: ui.PartialNodeWithId<CompanyBinding>;
    receiptNumber: string;
    receiptInternalId: integer;
    documentDate: string;
}

// Interface for "Unbilled account payable" inquiry to be used in purchaseReceiptLine node for unbilledAccountPayable query
// the interface for the searchCriteria parameter
// used in nodes (xtrem-purchasing) and pages => should be moved in shared-functions
export interface FinanceUnbilledAccountPayableSearch {
    company?: string;
    stockSites?: integer[];
    fromSupplier?: string;
    toSupplier?: string;
    asOfDate: string;
}

export interface FinanceOriginPostingStatusData {
    _id: string;
    documentType: FinanceDocumentType;
    documentNumber: string;
    documentSysId: number;
    postingStatus: PostingStatus;
    message: string;
    financeIntegrationAppRecordId: string;
    financeIntegrationAppUrl: string;
}

// used in nodes (xtrem-finance-data only) and pages
// used in nodes (xtrem-finance-data only) and pages => should be moved in shared-functions (but impossible because of the use of the enums)
export interface FinancePostingStatusData {
    _id: string;
    documentType: TargetDocumentType;
    documentNumber: string;
    documentSysId: number;
    status: PostingStatus;
    message: string;
    hasFinanceIntegrationApp: boolean;
    financeIntegrationApp: FinanceIntegrationApp | null;
    financeIntegrationAppRecordId: string;
    financeIntegrationAppUrl: string;
    externalLink: boolean;
    sourceDocumentType?: SourceDocumentType;
}
