import type { GraphApi, PostingClass } from '@sage/xtrem-finance-data-api';
import type { MachineResource as MachineResourcePage } from '@sage/xtrem-master-data/build/lib/pages/machine-resource';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<MachineResourceExtension>({
    extends: '@sage/xtrem-master-data/MachineResource',
})
export class MachineResourceExtension extends ui.PageExtension<MachineResourcePage, GraphApi> {
    @ui.decorators.referenceField<MachineResourceExtension>({
        parent() {
            return this.costBlock;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        width: 'medium',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [ui.nestedFields.text({ bind: 'name' })],
        insertBefore() {
            return this.resourceCostCategories;
        },
    })
    postingClass: ui.fields.Reference<PostingClass>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/machine-resource' {
    interface MachineResource extends MachineResourceExtension {}
}
