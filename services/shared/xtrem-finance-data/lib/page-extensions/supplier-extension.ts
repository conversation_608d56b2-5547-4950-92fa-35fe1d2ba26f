import type { Attribute, Dimension, GraphApi, PostingClass } from '@sage/xtrem-finance-data-api';
import type { Supplier as SupplierNode } from '@sage/xtrem-master-data-api';
import type { Supplier as SupplierPage } from '@sage/xtrem-master-data/build/lib/pages/supplier';
import * as ui from '@sage/xtrem-ui';
import {
    manageAttributesDimensions,
    manageAttributeTypeRestrictedToOnPage,
    prepareAttributes,
    prepareDimensions,
    setAttributesLineValues,
} from '../client-functions/attributes-and-dimensions';
import type { DatevIdRange } from '../client-functions/datev';
import { checkEmptyDatevId, getDatevIdRangeSuppliers, validateDatevId } from '../client-functions/datev';

@ui.decorators.pageExtension<SupplierExtension, SupplierNode>({
    extends: '@sage/xtrem-master-data/Supplier',
    navigationPanel: {
        listItem: {
            line11: ui.nestedFieldExtensions.reference<SupplierExtension, SupplierNode, PostingClass>({
                title: 'Posting class',
                node: '@sage/xtrem-finance-data/PostingClass',
                bind: { postingClass: true },
                valueField: 'name',
                insertBefore: 'isOnHold',
                isHiddenOnMainField: true,
            }),
            datevId: ui.nestedFields.text({
                bind: { datevId: true },
                title: 'DATEV ID',
                isHiddenOnMainField: true,
            }),
        },
    },
    async onLoad() {
        await setAttributesLineValues({
            page: this,
            inputData: {
                computedAttributes: '{}',
                storedDimensions: this.dimensions.value || '{}',
                storedAttributes: this.attributes.value || '{}',
            },
        });
        await manageAttributesDimensions({
            page: this,
            isEditable: true,
        });
        if (this.$.isServiceOptionEnabled('datevOption')) {
            this.datevIdRange = await getDatevIdRangeSuppliers(this);
        }
    },
})
export class SupplierExtension extends ui.PageExtension<SupplierPage, GraphApi> {
    datevIdRange: DatevIdRange;

    @ui.decorators.referenceField<SupplierExtension, PostingClass>({
        parent() {
            return this.financialBlock;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [ui.nestedFields.text({ bind: 'name' })],
        // This must come from server side - delete it after implementation of XT-68676
        filter: { type: 'supplier', isDetailed: true },
    })
    postingClass: ui.fields.Reference<PostingClass>;

    @ui.decorators.textField<SupplierExtension>({
        parent() {
            return this.mainBlock;
        },
        title: 'DATEV ID',
        validation(value: string | undefined) {
            return validateDatevId({
                datevId: value,
                first: this.datevIdRange.fromValue,
                last: this.datevIdRange.toValue,
            });
        },
    })
    datevId: ui.fields.Text;

    @ui.decorators.block<SupplierExtension>({
        parent() {
            return this.financialSection;
        },
        title: 'Dimensions',
    })
    dimensionBlock: ui.containers.Block;

    @ui.decorators.textField<SupplierExtension>({ bind: { storedDimensions: true } })
    dimensions: ui.fields.Text;

    @ui.decorators.textField<SupplierExtension>({ bind: { storedAttributes: true } })
    attributes: ui.fields.Text;

    @ui.decorators.referenceField<SupplierExtension, Attribute>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select employee',
        isHidden: true,
        title: 'Employee',
        helperTextField: 'id',
        filter: { attributeType: { id: { _eq: 'employee' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<SupplierExtension, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
        ],
        onChange() {
            this.attributes.value = prepareAttributes(this);
        },
    })
    employee: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<SupplierExtension, Attribute>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select project',
        isHidden: true,
        title: 'Project',
        helperTextField: 'id',
        filter: { attributeType: { id: { _eq: 'project' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<SupplierExtension, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
        ],
        onChange() {
            this.attributes.value = prepareAttributes(this);
            manageAttributeTypeRestrictedToOnPage({ page: this, clearTask: true });
        },
    })
    project: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<SupplierExtension, Attribute>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select task',
        isHidden: true,
        title: 'Task',
        helperTextField: 'id',
        filter() {
            return {
                attributeType: { id: { _eq: 'task' }, isActive: true },
                isActive: true,
                attributeRestrictedToId: this.project?.value?.id || '',
            };
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<SupplierExtension, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
        ],
        onChange() {
            this.attributes.value = prepareAttributes(this);
        },
    })
    task: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 01',
        isHidden: true,
        title: 'Dimension 01',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType01' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension01: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 02',
        isHidden: true,
        title: 'Dimension 02',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType02' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension02: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 03',
        isHidden: true,
        title: 'Dimension 03',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType03' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension03: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 04',
        isHidden: true,
        title: 'Dimension 04',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType04' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension04: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 05',
        isHidden: true,
        title: 'Dimension 05',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType05' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension05: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 06',
        isHidden: true,
        title: 'Dimension 06',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType06' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension06: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 07',
        isHidden: true,
        title: 'Dimension 07',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType07' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension07: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 08',
        isHidden: true,
        title: 'Dimension 08',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType08' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension08: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 09',
        isHidden: true,
        title: 'Dimension 09',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType09' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension09: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 10',
        isHidden: true,
        title: 'Dimension 10',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType10' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension10: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 11',
        isHidden: true,
        title: 'Dimension 11',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType11' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension11: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 12',
        isHidden: true,
        title: 'Dimension 12',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType12' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension12: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 13',
        isHidden: true,
        title: 'Dimension 13',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType13' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension13: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 14',
        isHidden: true,
        title: 'Dimension 14',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType14' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension14: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 15',
        isHidden: true,
        title: 'Dimension 15',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType15' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension15: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 16',
        isHidden: true,
        title: 'Dimension 16',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType16' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension16: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 17',
        isHidden: true,
        title: 'Dimension 17',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType17' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension17: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 18',
        isHidden: true,
        title: 'Dimension 18',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType18' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension18: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 19',
        isHidden: true,
        title: 'Dimension 19',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType19' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension19: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<SupplierExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 20',
        isHidden: true,
        title: 'Dimension 20',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType20' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<SupplierExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension20: ui.fields.Reference<Dimension>;

    // Warnings on save. This function overwrites a extensionControl 'noop' function on the main class to allow warnings on save
    extensionControl() {
        checkEmptyDatevId({ pageInstance: this });
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/supplier' {
    interface Supplier extends SupplierExtension {}
}
