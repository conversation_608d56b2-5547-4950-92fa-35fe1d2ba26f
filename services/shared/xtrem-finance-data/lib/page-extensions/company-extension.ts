import type {
    BankAccount,
    CompanyAttributeType,
    CompanyDefaultAttribute,
    CompanyDefaultDimension,
    CompanyDimensionType,
    DimensionDefinitionLevel,
    GraphApi,
    MasterDataDefault,
    PostingClass,
} from '@sage/xtrem-finance-data-api';
import type { Company as CompanyPage } from '@sage/xtrem-master-data/build/lib/pages/company';
import { confirmDialogWithAcceptButtonText } from '@sage/xtrem-master-data/lib/client-functions/page-functions';
import type { Country, Legislation } from '@sage/xtrem-structure-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import type { PartialCollectionValueWithIds } from '@sage/xtrem-ui/build/lib/component/types';
import type { ActiveAttributeType, ActiveDimensionType } from '../client-functions/attributes-and-dimensions';
import {
    attributeIDs,
    dimensionDocProperties,
    getActiveAttributes,
    getActiveDimensions,
    getAttributeTypeName,
    getDimensionTypeName,
    getMasterDataDefaultOptions,
    getMasterDataDefaultOptionsFromDatabase,
    initAttributeDimensionTypes,
    initCompanyAttributeDimensionTypes,
    initDefaultAttributesAndDimensions,
    onDefaultAttributeChange,
    onDefaultDimensionChange,
} from '../client-functions/attributes-and-dimensions';
import { validateDatevId } from '../client-functions/datev';
import type { AttributeDimensionTypes, DefaultAttributesAndDimensions } from '../client-functions/interfaces/dimension';
import { legislationsThatDoWipPosting } from '../shared-functions/common';

@ui.decorators.pageExtension<CompanyExtension>({
    extends: '@sage/xtrem-master-data/Company',
    navigationPanel: {
        listItem: {
            datevConsultantNumber: ui.nestedFields.text({
                bind: 'datevConsultantNumber',
                title: 'DATEV consultant number',
                isHiddenOnMainField: true,
            }),
            datevCustomerNumber: ui.nestedFields.text({
                bind: 'datevCustomerNumber',
                title: 'DATEV customer number',
                isHiddenOnMainField: true,
            }),
        },
    },

    async onLoad() {
        this.activeDimensionTypes = await getActiveDimensions(this);
        this.activeAttributeTypes = await getActiveAttributes(this, { onlyNodeLinkAttribute: true });
        this.masterDataDefaultOptions = await getMasterDataDefaultOptionsFromDatabase(this);
        if (this.$.recordId) {
            initCompanyAttributeDimensionTypes(this);
            initDefaultAttributesAndDimensions(this);
        }
    },
})
export class CompanyExtension extends ui.PageExtension<CompanyPage, GraphApi> {
    activeDimensionTypes: ActiveDimensionType[];

    activeAttributeTypes: ActiveAttributeType[];

    masterDataDefaultOptions: { document: DimensionDefinitionLevel; default: MasterDataDefault }[];

    @ui.decorators.section<CompanyExtension>({ title: 'Required dimensions', isHidden: true })
    selectAttributeAndDimensionSection: ui.containers.Section;

    @ui.decorators.block<CompanyExtension>({
        parent() {
            return this.managementSection;
        },
        title: 'Tax management',
        insertBefore() {
            return this.creditLimitBlock;
        },
    })
    taxManagementBlock: ui.containers.Block;

    @ui.decorators.referenceField<CompanyExtension, BankAccount>({
        parent() {
            return this.paymentTrackingBlock;
        },
        title: 'Default bank account',
        node: '@sage/xtrem-finance-data/BankAccount',
        valueField: 'name',
        width: 'medium',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select bank account',
        columns: [ui.nestedFields.text({ bind: 'id' }), ui.nestedFields.text({ bind: 'name' })],
    })
    bankAccount: ui.fields.Reference<BankAccount>;

    @ui.decorators.block<CompanyExtension>({
        parent() {
            return this.selectAttributeAndDimensionSection;
        },
        title: 'Management',
        width: 'extra-large',
        isTitleHidden: true,
    })
    selectAttributeAndDimensionBlock: ui.containers.Block;

    @ui.decorators.section<CompanyExtension>({ title: 'Posting' })
    postingSection: ui.containers.Section;

    @ui.decorators.block<CompanyExtension>({
        parent() {
            return this.postingSection;
        },
        title: 'Finance',
    })
    financePostingBlock: ui.containers.Block;

    @ui.decorators.block<CompanyExtension>({
        parent() {
            return this.postingSection;
        },
        title: 'Stock',
    })
    stockPostingBlock: ui.containers.Block;

    @ui.decorators.block<CompanyExtension>({
        parent() {
            return this.postingSection;
        },
        title: 'Manufacturing',
    })
    manufacturingPostingBlock: ui.containers.Block;

    @ui.decorators.switchField<CompanyExtension>({
        parent() {
            return this.financePostingBlock;
        },
        title: 'A/P posting',
        isDisabled: true,
    })
    doApPosting: ui.fields.Switch;

    @ui.decorators.switchField<CompanyExtension>({
        parent() {
            return this.financePostingBlock;
        },
        title: 'A/R posting',
        isDisabled: true,
    })
    doArPosting: ui.fields.Switch;

    @ui.decorators.referenceField<CompanyExtension, PostingClass>({
        parent() {
            return this.financePostingBlock;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        width: 'medium',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [ui.nestedFields.text({ bind: 'name' })],
    })
    postingClass: ui.fields.Reference<PostingClass>;

    @ui.decorators.switchField<CompanyExtension>({
        parent() {
            return this.stockPostingBlock;
        },
        title: 'Stock posting',
        async onChange() {
            this.doStockPosting.value = await this.applyScreen(this.doStockPosting.value || false);
            this.setDoNonAbsorbedPosting();
        },
    })
    doStockPosting: ui.fields.Switch;

    @ui.decorators.switchField<CompanyExtension>({
        parent() {
            return this.stockPostingBlock;
        },
        title: 'Non absorbed amount posting',
        async onChange() {
            this.doNonAbsorbedPosting.value = await this.applyScreen(this.doNonAbsorbedPosting.value || false);
        },
        isDisabled() {
            return !this.doStockPosting.value || false;
        },
        isHidden() {
            return !this.legislation.value?.doNonAbsorbedPosting;
        },
    })
    doNonAbsorbedPosting: ui.fields.Switch;

    @ui.decorators.switchField<CompanyExtension>({
        parent() {
            return this.manufacturingPostingBlock;
        },
        title: 'WIP posting',
        isDisabled() {
            return !legislationsThatDoWipPosting.includes(this.legislation?.value?.id ?? '');
        },
        async onChange() {
            this.doWipPosting.value = await this.applyScreen(this.doWipPosting.value || false);
        },
    })
    doWipPosting: ui.fields.Switch;

    /**
     * Management Tab
     */

    @ui.decorators.dropdownListField<CompanyExtension>({
        parent() {
            return this.taxManagementBlock;
        },
        title: 'Tax calculation package',
        width: 'medium',
        bind: 'taxEngine',
        optionType: '@sage/xtrem-finance-data/TaxEngine',
        isDisabled() {
            return this.legislation.value?.id !== 'US' || !this.$.isServiceOptionEnabled('avalaraOption');
        },
        insertBefore() {
            return this.customerOnHoldCheck;
        },
    })
    taxEngine: ui.fields.DropdownList;

    /**
     * Dimensions Tab
     */

    @ui.decorators.section<CompanyExtension>({ title: 'Dimensions' })
    dimensionsSection: ui.containers.Section;

    @ui.decorators.block<CompanyExtension>({
        parent() {
            return this.dimensionsSection;
        },
        title: 'Dimensions',
        isTitleHidden: true,
        width: 'extra-large',
    })
    dimensionsBlock: ui.containers.Block;

    // hidden non transient table field for synchronization with database (used in onLoad and onChange of mandatory attributes)
    @ui.decorators.tableField<CompanyExtension, CompanyAttributeType>({
        parent() {
            return this.dimensionsBlock;
        },
        title: 'Attributes',
        canSelect: false,
        bind: 'attributeTypes',
        node: '@sage/xtrem-finance-data/CompanyAttributeType',
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: { attributeType: { _id: true } } }),
            ui.nestedFields.text({ bind: { attributeType: { name: true } } }),
            ui.nestedFields.technical({ bind: { attributeType: { attributeTypeRestrictedTo: { name: true } } } }),
            ui.nestedFields.dropdownList({
                title: 'Analytical type',
                bind: 'analyticalMeasureType',
                optionType: '@sage/xtrem-master-data/AnalyticalMeasureType',
            }),
        ],
    })
    attributeTypes: ui.fields.Table<CompanyAttributeType>;

    // hidden non transient table field for synchronization with database (used in onLoad and onChange of mandatory dimensions)
    @ui.decorators.tableField<CompanyExtension, CompanyDimensionType>({
        parent() {
            return this.dimensionsBlock;
        },
        title: 'Dimensions',
        canSelect: false,
        bind: 'dimensionTypes',
        node: '@sage/xtrem-finance-data/CompanyDimensionType',
        isHidden: true,
        columns: [
            ui.nestedFields.text({ bind: { dimensionType: { _id: true } } }),
            ui.nestedFields.technical({ bind: { dimensionType: { docProperty: true } } }),
            ui.nestedFields.text({ bind: { dimensionType: { name: true } } }),
            ui.nestedFields.dropdownList({
                title: 'Analytical type',
                bind: 'analyticalMeasureType',
                optionType: '@sage/xtrem-master-data/AnalyticalMeasureType',
            }),
        ],
    })
    dimensionTypes: ui.fields.Table<CompanyDimensionType>;

    @ui.decorators.tableField<CompanyExtension, AttributeDimensionTypes>({
        parent() {
            return this.dimensionsBlock;
        },
        isTransient: true,
        title: 'Required dimensions',
        canSelect: false,
        isFullWidth: false,
        columns: [
            ui.nestedFields.technical({ bind: 'companyAttributeDimensionTypeId' }),
            ui.nestedFields.technical({ bind: 'attributeDimensionTypeId' }),
            ui.nestedFields.text({ bind: 'id', title: 'Name', isReadOnly: true }),
            ui.nestedFields.technical({ bind: 'attributeTypeRestrictedTo' }),
            ui.nestedFields.dropdownList({
                title: 'Analytical type',
                bind: 'type',
                optionType: '@sage/xtrem-master-data/AnalyticalMeasureType',
                isHidden: true,
            }),
        ],
        fieldActions() {
            return [this.addDimensionAttributeLine];
        },
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                onClick(rowId: string, rowData) {
                    if (rowData.type === 'attribute') {
                        // remove corresponding line from hidden attributeTypes tableField for later database sync
                        this.attributeTypes.removeRecord(rowData.companyAttributeDimensionTypeId || '');
                    }
                    if (rowData.type === 'dimension') {
                        // remove corresponding line from hidden dimensionTypes tableField for later database sync
                        this.dimensionTypes.removeRecord(rowData.companyAttributeDimensionTypeId || '');
                    }
                    // remove original line in grid
                    this.attributeDimensionTypes.removeRecord(rowId);
                    setApplicativePageCrudActions({
                        page: this,
                        isDirty: true,
                        save: this.$standardSaveAction,
                    });
                },
            },
        ],
    })
    attributeDimensionTypes: ui.fields.Table<AttributeDimensionTypes>;

    @ui.decorators.tableField<CompanyExtension, AttributeDimensionTypes>({
        parent() {
            return this.selectAttributeAndDimensionBlock;
        },
        isTransient: true,
        title: 'Required dimensions',
        isTitleHidden: true,
        canSelect: true,
        isFullWidth: false,
        columns: [
            ui.nestedFields.technical({ bind: 'attributeDimensionTypeId' }),
            ui.nestedFields.text({ bind: 'id', title: 'Name', isReadOnly: true }),
            ui.nestedFields.technical({ bind: 'type' }),
            ui.nestedFields.technical({ bind: 'attributeTypeRestrictedTo' }),
        ],
    })
    selectDimensionAndAttributeTypes: ui.fields.Table<AttributeDimensionTypes>;

    @ui.decorators.pageAction<CompanyExtension>({
        isTransient: true,
        icon: 'add',
        title: 'Mandatory dimension and attributes',
        isDisabled() {
            return this.$.isDirty;
        },
        async onClick() {
            this.selectAttributeAndDimensionSection.isHidden = false;
            this.selectDimensionAndAttributeTypes.value = [];

            await initAttributeDimensionTypes(this);

            const response = await this.$.dialog
                .custom('info', this.selectAttributeAndDimensionSection, {
                    cancelButton: { isHidden: false },
                    acceptButton: { isHidden: false },
                })
                .then(() => true)
                .catch(() => {
                    this.addDimensionAttributeLine.isDisabled = false;
                    this.selectAttributeAndDimensionSection.isHidden = true;
                });
            if (response) {
                if (
                    await confirmDialogWithAcceptButtonText(
                        this,
                        ui.localize(
                            '@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation',
                            'Confirm setup',
                        ),
                        ui.localize(
                            '@sage/xtrem-finance-data/pages__company__dimensions_setup_update_confirmation_apply',
                            'You are about to apply these settings to all pending and future documents.',
                        ),
                        ui.localize('@sage/xtrem-finance-data/pages_company_confirmation', 'Apply'),
                    )
                ) {
                    this.selectDimensionAndAttributeTypes.selectedRecords
                        .map(line => {
                            const selectedLine = this.selectDimensionAndAttributeTypes.getRecordValue(line);
                            return {
                                attributeDimensionTypeId: selectedLine?.attributeDimensionTypeId,
                                companyAttributeDimensionTypeId: selectedLine?._id,
                                id: selectedLine?.id,
                                type: selectedLine?.type,
                                attributeTypeRestrictedTo: selectedLine?.attributeTypeRestrictedTo,
                            };
                        })
                        .forEach(line => {
                            this.attributeDimensionTypes.addOrUpdateRecordValue(line);
                            if (line.type === 'attribute') {
                                // add line also in hidden attributeTypes tableField for later database sync
                                this.attributeTypes.addOrUpdateRecordValue({
                                    _id: line.companyAttributeDimensionTypeId,
                                    attributeType: {
                                        _id: line.attributeDimensionTypeId,
                                        name: line.id,
                                        attributeTypeRestrictedTo: { name: line.attributeTypeRestrictedTo },
                                    },
                                    company: { _id: this.$.recordId },
                                    isRequired: true,
                                });
                            }
                            if (line.type === 'dimension') {
                                // add line also in hidden dimensionTypes tableField for later database sync
                                this.dimensionTypes.addOrUpdateRecordValue({
                                    _id: line.companyAttributeDimensionTypeId,
                                    dimensionType: { _id: line.attributeDimensionTypeId, name: line.id },
                                    company: { _id: this.$.recordId },
                                    isRequired: true,
                                });
                            }
                        });
                    this.selectAttributeAndDimensionSection.isHidden = true;
                } else {
                    setApplicativePageCrudActions({
                        page: this,
                        isDirty: false,
                        save: this.$standardSaveAction,
                    });
                    this.addDimensionAttributeLine.isDisabled = false;
                    this.selectAttributeAndDimensionSection.isHidden = true;
                }
            }
        },
    })
    addDimensionAttributeLine: ui.PageAction;

    // hidden non transient table field for synchronization with database (used in onLoad and onChange of default attributes)
    @ui.decorators.tableField<CompanyExtension, CompanyDefaultAttribute>({
        parent() {
            return this.dimensionsBlock;
        },
        title: 'Attributes',
        canSelect: false,
        bind: 'defaultAttributes',
        node: '@sage/xtrem-finance-data/CompanyDefaultAttribute',
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Company',
                title: 'Company',
                bind: 'company',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.technical({ bind: '_id' }),
                ],
            }),
            ui.nestedFields.text({ bind: { attributeType: { _id: true } } }),
            ui.nestedFields.text({ bind: { attributeType: { id: true } } }),
            ui.nestedFields.text({ bind: { attributeType: { name: true } } }),
            ui.nestedFields.dropdownList({ bind: 'dimensionDefinitionLevel' }),
            ui.nestedFields.dropdownList({ bind: 'masterDataDefault' }),
        ],
    })
    defaultAttributes: ui.fields.Table<CompanyDefaultAttribute>;

    // hidden non transient table field for synchronization with database (used in onLoad and onChange of default dimensions)
    @ui.decorators.tableField<CompanyExtension, CompanyDefaultDimension>({
        parent() {
            return this.dimensionsBlock;
        },
        title: 'Dimensions',
        canSelect: false,
        bind: 'defaultDimensions',
        node: '@sage/xtrem-finance-data/CompanyDefaultDimension',
        isHidden: true,
        columns: [
            ui.nestedFields.technical({ bind: '_id' }),
            ui.nestedFields.reference({
                node: '@sage/xtrem-system/Company',
                title: 'Company',
                bind: 'company',
                valueField: 'name',
                columns: [
                    ui.nestedFields.text({ bind: 'name', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id', title: 'ID' }),
                    ui.nestedFields.technical({ bind: '_id' }),
                ],
            }),
            ui.nestedFields.text({ bind: { dimensionType: { _id: true } } }),
            ui.nestedFields.dropdownList({ bind: { dimensionType: { docProperty: true } } }),
            ui.nestedFields.text({ bind: { dimensionType: { name: true } } }),
            ui.nestedFields.dropdownList({ bind: 'dimensionDefinitionLevel' }),
            ui.nestedFields.dropdownList({ bind: 'masterDataDefault' }),
        ],
    })
    defaultDimensions: ui.fields.Table<CompanyDefaultDimension>;

    @ui.decorators.tableField<CompanyExtension, DefaultAttributesAndDimensions>({
        parent() {
            return this.dimensionsBlock;
        },
        isTransient: true,
        title: 'Default dimension rules',
        canSelect: false,
        isFullWidth: false,
        columns: [
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'document',
                optionType: '@sage/xtrem-finance-data/DimensionDefinitionLevel',
                title: 'Document and origin',
                isMandatory: true,
                validation(_val, rowData: DefaultAttributesAndDimensions) {
                    if (
                        this.defaultAttributesAndDimensions.value.some(
                            line =>
                                rowData._id !== line._id && // don't check actual row itself
                                rowData.document === line.document,
                        )
                    ) {
                        return ui.localize(
                            '@sage/xtrem-finance-data/pages__company_extension__duplicate_error',
                            'There are duplicate documents. Make sure that each line has a unique document.',
                        );
                    }
                    return undefined;
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'employeeAttribute',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().employee);
                },
                isHidden() {
                    return !this.activeAttributeTypes.map(attributeType => attributeType.id).includes('employee');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultAttributeChange(this, rowData, 'employee', rowData.employeeAttribute);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'projectAttribute',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().project);
                },
                isHidden() {
                    return !this.activeAttributeTypes.map(attributeType => attributeType.id).includes('project');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultAttributeChange(this, rowData, 'project', rowData.projectAttribute);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'taskAttribute',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getAttributeTypeName(this.activeAttributeTypes, attributeIDs().task);
                },
                isHidden() {
                    return !this.activeAttributeTypes.some(attributeType => attributeType.id === 'task');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultAttributeChange(this, rowData, 'task', rowData.taskAttribute);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension01',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty01);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType01');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType01', rowData.dimension01);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension02',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty02);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType02');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType02', rowData.dimension02);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension03',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty03);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType03');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType03', rowData.dimension03);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension04',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty04);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType04');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType04', rowData.dimension04);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension05',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty05);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType05');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType05', rowData.dimension05);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension06',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty06);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType06');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType06', rowData.dimension06);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension07',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty07);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType07');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType07', rowData.dimension07);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension08',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty08);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType08');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType08', rowData.dimension08);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension09',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty09);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType09');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType09', rowData.dimension09);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension10',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty10);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType10');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType10', rowData.dimension10);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension11',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty11);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType11');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType11', rowData.dimension11);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension12',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty12);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType12');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType12', rowData.dimension12);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension13',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty13);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType13');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType13', rowData.dimension13);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension14',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty14);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType14');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType14', rowData.dimension14);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension15',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty15);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType15');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType15', rowData.dimension15);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension16',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty16);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType16');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType16', rowData.dimension16);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension17',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty17);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType17');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType17', rowData.dimension17);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension18',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty18);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType18');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType18', rowData.dimension18);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension19',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty19);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType19');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType19', rowData.dimension19);
                },
            }),
            ui.nestedFields.dropdownList<CompanyExtension>({
                bind: 'dimension20',
                optionType: '@sage/xtrem-finance-data/MasterDataDefault',
                options(_value, rowData: DefaultAttributesAndDimensions) {
                    return getMasterDataDefaultOptions(this, rowData);
                },
                hasEmptyValue: true,
                title() {
                    return getDimensionTypeName(this.activeDimensionTypes, dimensionDocProperties().docProperty20);
                },
                isHidden() {
                    return !this.activeDimensionTypes
                        .map(dimensionType => dimensionType.docProperty)
                        .includes('dimensionType20');
                },
                onChange(_id, rowData: DefaultAttributesAndDimensions) {
                    onDefaultDimensionChange(this, rowData, 'dimensionType20', rowData.dimension20);
                },
            }),
        ],
        fieldActions() {
            return [this.addDefaultDimension];
        },
        dropdownActions: [
            {
                icon: 'bin',
                title: 'Delete',
                isDestructive: true,
                onClick(rowId: string, rowData: PartialCollectionValueWithIds<DefaultAttributesAndDimensions>) {
                    // remove all lines for the deleted rowData.document from hidden defaultAttributes tableField for later database sync
                    this.defaultAttributes.value
                        .filter(line => line.dimensionDefinitionLevel === rowData.document)
                        .forEach(line => {
                            this.defaultAttributes.removeRecord(line._id);
                        });
                    // remove all lines for the deleted rowData.document from hidden defaultDimensions tableField for later database sync
                    this.defaultDimensions.value
                        .filter(line => line.dimensionDefinitionLevel === rowData.document)
                        .forEach(line => {
                            this.defaultDimensions.removeRecord(line._id);
                        });
                    // remove line in original composed grid
                    this.defaultAttributesAndDimensions.removeRecord(rowId);
                    setApplicativePageCrudActions({
                        page: this,
                        isDirty: true,
                        save: this.$standardSaveAction,
                    });
                },
            },
        ],
    })
    defaultAttributesAndDimensions: ui.fields.Table<DefaultAttributesAndDimensions>;

    @ui.decorators.pageAction<CompanyExtension>({
        icon: 'add',
        title: 'Add line',
        onClick() {
            this.defaultAttributesAndDimensions.addRecord({});
        },
    })
    addDefaultDimension: ui.PageAction;

    @ui.decorators.referenceFieldOverride<CompanyExtension>({
        onChangeAfter() {
            this.doPostingSettings(this.legislation.value?.id || '');
        },
        columns: [
            ui.nestedFields.technical({ bind: { doStockPosting: true } }),
            ui.nestedFields.technical({ bind: { doNonAbsorbedPosting: true } }),
        ],
    })
    legislation: ui.fields.Reference<Legislation>;

    @ui.decorators.referenceFieldOverride<CompanyExtension>({
        onChangeAfter() {
            this.doPostingSettings(this.country.value?.legislation?.id || '');
        },
        columns: [
            ui.nestedFields.reference({
                node: '@sage/xtrem-structure/Legislation',
                tunnelPage: '@sage/xtrem-structure/Legislation',
                bind: 'legislation',
                title: 'Legislation',
                valueField: 'name',
                isHidden: true,
                columns: [
                    ui.nestedFields.text({ bind: 'name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: { doStockPosting: true } }),
                    ui.nestedFields.technical({ bind: { doNonAbsorbedPosting: true } }),
                ],
            }),
        ],
    })
    country: ui.fields.Reference<Country>;

    @ui.decorators.textField<CompanyExtension>({
        parent() {
            return this.mainBlock;
        },
        title: 'DATEV consultant number',
        isHidden() {
            return this.legislation.value?.id !== 'DE' || !this.$.isServiceOptionEnabled('datevOption');
        },
        validation(value: string | undefined) {
            return validateDatevId({
                datevId: value,
                first: 1001,
                last: 9999999,
            });
        },
    })
    datevConsultantNumber: ui.fields.Text;

    @ui.decorators.textField<CompanyExtension>({
        parent() {
            return this.mainBlock;
        },
        title: 'DATEV customer number',
        isHidden() {
            return this.legislation.value?.id !== 'DE' || !this.$.isServiceOptionEnabled('datevOption');
        },
        validation(value: string | undefined) {
            return validateDatevId({
                datevId: value,
                first: 1,
                last: 99999,
            });
        },
    })
    datevCustomerNumber: ui.fields.Text;

    async applyScreen(val: boolean) {
        const options: ui.dialogs.DialogOptions = { acceptButton: { text: 'Apply' }, cancelButton: { text: 'Cancel' } };
        if (
            !(await this.$.dialog
                .confirmation(
                    'warn',
                    ui.localize('@sage/xtrem-finance-data/confirm-dialog-title', 'Confirm setup'),
                    ui.localize(
                        '@sage/xtrem-finance-data/confirm-dialog-content',
                        'You are about to apply this posting setup to all future transactions.',
                    ),
                    options,
                )
                .then(() => true)
                .catch(() => false))
        )
            return !val;
        return val;
    }

    doPostingSettings(val: string) {
        this.doStockPosting.value = this.legislation.value?.doStockPosting || null;
        this.setDoNonAbsorbedPosting();
        this.doWipPosting.value = legislationsThatDoWipPosting.includes(val);
        if (val !== 'US') this.taxEngine.value = 'genericTaxCalculation';
    }

    setDoNonAbsorbedPosting() {
        this.doNonAbsorbedPosting.value = this.doStockPosting.value
            ? !!this.legislation.value?.doNonAbsorbedPosting
            : false;
        this.doNonAbsorbedPosting.isDisabled = !this.doStockPosting.value || false;
        this.doNonAbsorbedPosting.isHidden = !this.legislation.value?.doNonAbsorbedPosting;
    }

    attributeRestrictedToControl() {
        this.attributeTypes.value.forEach(attributeType => {
            if (
                attributeType.attributeType?.attributeTypeRestrictedTo &&
                attributeType.attributeType?.attributeTypeRestrictedTo?.name !== null
            ) {
                if (
                    this.attributeTypes.value.some(
                        attributeTypeRestrictedTo =>
                            attributeTypeRestrictedTo.attributeType?.name ===
                            attributeType.attributeType?.attributeTypeRestrictedTo?.name,
                    ) === false
                ) {
                    this.$.showToast(
                        ui.localize(
                            '@sage/xtrem-finance-data/pages__company_extension__project_task_warning',
                            'The {{attributeName}} is required when you enter a {{restrictedToName}}.',
                            {
                                attributeName: attributeType.attributeType?.name,
                                restrictedToName: attributeType.attributeType?.attributeTypeRestrictedTo?.name,
                            },
                        ),
                        { type: 'warning', timeout: 5000 },
                    );
                }
            }
        });
    }
}

declare module '@sage/xtrem-master-data/build/lib/pages/company' {
    interface Company extends CompanyExtension {}
}
