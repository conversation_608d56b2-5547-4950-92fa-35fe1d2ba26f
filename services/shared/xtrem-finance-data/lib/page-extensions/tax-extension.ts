import type { Graph<PERSON><PERSON> } from '@sage/xtrem-finance-data-api';
import type { Tax as TaxPage } from '@sage/xtrem-tax/build/lib/pages/tax';
import * as ui from '@sage/xtrem-ui';
import { validateDatevId } from '../client-functions/datev';

@ui.decorators.pageExtension<TaxExtension>({
    extends: '@sage/xtrem-tax/Tax',
    navigationPanel: {
        listItem: {
            postingKey: ui.nestedFieldExtensions.text({
                bind: 'postingKey',
                title: 'Posting key',
                insertBefore: 'isReverseCharge',
                isHiddenOnMainField: true,
            }),
            line7: ui.nestedFields.reference({
                bind: 'postingClass',
                node: '@sage/xtrem-finance-data/PostingClass',
                valueField: 'name',
                title: 'Posting class',
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class TaxExtension extends ui.PageExtension<TaxPage, GraphApi> {
    @ui.decorators.referenceField<TaxExtension>({
        parent() {
            return this.generalBlock;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [
            ui.nestedFields.select({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-finance-data/PostingClassType',
            }),
            ui.nestedFields.text({ bind: 'name' }),
        ],
    })
    postingClass: ui.fields.Reference;

    @ui.decorators.textField<TaxExtension>({
        parent() {
            return this.generalBlock;
        },
        title: 'Posting key',
        isHidden() {
            return this.country.value?.legislation?.id !== 'DE';
        },
        insertBefore() {
            return this.isReverseCharge;
        },
        validation(value: string | undefined) {
            return validateDatevId({
                datevId: value,
                first: 1,
                last: 9999,
            });
        },
    })
    postingKey: ui.fields.Text;
}

declare module '@sage/xtrem-tax/build/lib/pages/tax' {
    interface Tax extends TaxExtension {}
}
