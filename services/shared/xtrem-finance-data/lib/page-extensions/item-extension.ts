import type { Attribute, Dimension, GraphApi } from '@sage/xtrem-finance-data-api';
import type { Item as ItemPage } from '@sage/xtrem-master-data/build/lib/pages/item';
import * as ui from '@sage/xtrem-ui';
import {
    manageAttributesDimensions,
    manageAttributeTypeRestrictedToOnPage,
    prepareAttributes,
    prepareDimensions,
    setAttributesLineValues,
} from '../client-functions/attributes-and-dimensions';

@ui.decorators.pageExtension<ItemExtension>({
    extends: '@sage/xtrem-master-data/Item',
    extensionAccessBinding: { node: '@sage/xtrem-master-data/Item', bind: '$read' },
    navigationPanel: {
        listItem: {
            postingClass: ui.nestedFieldExtensions.reference({
                bind: 'postingClass',
                valueField: 'name',
                node: '@sage/xtrem-finance-data/PostingClass',
                title: 'Posting class',
                insertAfter: 'commodityCode',
                isHiddenOnMainField: true,
            }),
        },
    },
})
export class ItemExtension extends ui.PageExtension<ItemPage, GraphApi> {
    @ui.decorators.sectionOverride<ItemExtension>({
        async onActiveAfter() {
            await setAttributesLineValues({
                page: this,
                inputData: {
                    computedAttributes: '{}',
                    storedDimensions: this.dimensions.value || '{}',
                    storedAttributes: this.attributes.value || '{}',
                },
            });
            await manageAttributesDimensions({ page: this, isEditable: true });
        },
    })
    financialSection: ui.containers.Section;

    @ui.decorators.referenceField<ItemExtension>({
        parent() {
            return this.financialBlock;
        },
        insertBefore() {
            return this.positionField1;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        width: 'medium',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [ui.nestedFields.text({ bind: 'name' })],
    })
    postingClass: ui.fields.Reference;

    @ui.decorators.block<ItemExtension>({
        parent() {
            return this.financialSection;
        },
        title: 'Dimensions',
    })
    dimensionBlock: ui.containers.Block;

    @ui.decorators.textField<ItemExtension>({ bind: { storedDimensions: true } }) dimensions: ui.fields.Text;

    @ui.decorators.textField<ItemExtension>({ bind: { storedAttributes: true } }) attributes: ui.fields.Text;

    @ui.decorators.referenceField<ItemExtension, Attribute>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select employee',
        isHidden: true,
        title: 'Employee',
        helperTextField: 'id',
        filter: { attributeType: { id: { _eq: 'employee' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<ItemExtension, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
        ],
        onChange() {
            this.attributes.value = prepareAttributes(this);
        },
    })
    employee: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<ItemExtension, Attribute>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select project',
        isHidden: true,
        title: 'Project',
        helperTextField: 'id',
        filter: { attributeType: { id: { _eq: 'project' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<ItemExtension, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
        ],
        onChange() {
            this.attributes.value = prepareAttributes(this);
            manageAttributeTypeRestrictedToOnPage({ page: this, clearTask: true });
        },
    })
    project: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<ItemExtension, Attribute>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select task',
        isHidden: true,
        title: 'Task',
        helperTextField: 'id',
        filter() {
            return {
                attributeType: { id: { _eq: 'task' }, isActive: true },
                isActive: true,
                attributeRestrictedToId: this.project?.value?.id || '',
            };
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<ItemExtension, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
        ],
        onChange() {
            this.attributes.value = prepareAttributes(this);
        },
    })
    task: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 01',
        isHidden: true,
        title: 'Dimension 01',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType01' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension01: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 02',
        isHidden: true,
        title: 'Dimension 02',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType02' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension02: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 03',
        isHidden: true,
        title: 'Dimension 03',
        helperTextField: 'id',
        filter: {
            dimensionType: { docProperty: { _eq: 'dimensionType03' }, isActive: true },
            isActive: true,
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension03: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 04',
        isHidden: true,
        title: 'Dimension 04',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType04' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension04: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 05',
        isHidden: true,
        title: 'Dimension 05',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType05' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension05: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 06',
        isHidden: true,
        title: 'Dimension 06',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType06' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension06: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 07',
        isHidden: true,
        title: 'Dimension 07',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType07' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension07: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 08',
        isHidden: true,
        title: 'Dimension 08',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType08' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension08: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 09',
        isHidden: true,
        title: 'Dimension 09',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType09' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension09: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 10',
        isHidden: true,
        title: 'Dimension 10',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType10' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension10: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 11',
        isHidden: true,
        title: 'Dimension 11',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType11' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension11: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 12',
        isHidden: true,
        title: 'Dimension 12',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType12' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension12: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 13',
        isHidden: true,
        title: 'Dimension 13',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType13' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension13: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 14',
        isHidden: true,
        title: 'Dimension 14',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType14' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension14: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 15',
        isHidden: true,
        title: 'Dimension 15',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType15' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension15: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 16',
        isHidden: true,
        title: 'Dimension 16',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType16' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension16: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 17',
        isHidden: true,
        title: 'Dimension 17',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType17' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension17: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 18',
        isHidden: true,
        title: 'Dimension 18',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType18' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension18: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 19',
        isHidden: true,
        title: 'Dimension 19',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType19' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension19: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<ItemExtension, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        isTransient: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 20',
        isHidden: true,
        title: 'Dimension 20',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType20' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<ItemExtension, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
        onChange() {
            this.dimensions.value = prepareDimensions(this);
        },
    })
    dimension20: ui.fields.Reference<Dimension>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/item' {
    interface Item extends ItemExtension {}
}
