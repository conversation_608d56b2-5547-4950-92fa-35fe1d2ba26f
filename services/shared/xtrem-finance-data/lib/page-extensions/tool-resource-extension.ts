import type { Graph<PERSON><PERSON> } from '@sage/xtrem-finance-data-api';
import type { ToolResource as ToolResourcePage } from '@sage/xtrem-master-data/build/lib/pages/tool-resource';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<ToolResourceExtension>({
    extends: '@sage/xtrem-master-data/ToolResource',
})
export class ToolResourceExtension extends ui.PageExtension<ToolResourcePage, GraphApi> {
    @ui.decorators.referenceField<ToolResourceExtension>({
        parent() {
            return this.costBlock;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        width: 'medium',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [ui.nestedFields.text({ bind: 'name' })],
        insertBefore() {
            return this.resourceCostCategories;
        },
    })
    postingClass: ui.fields.Reference;
}

declare module '@sage/xtrem-master-data/build/lib/pages/tool-resource' {
    interface ToolResource extends ToolResourceExtension {}
}
