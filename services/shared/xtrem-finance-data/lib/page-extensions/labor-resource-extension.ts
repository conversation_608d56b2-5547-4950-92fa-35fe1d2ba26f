import type { GraphApi, PostingClass } from '@sage/xtrem-finance-data-api';
import type { LaborResource as LaborResourcePage } from '@sage/xtrem-master-data/build/lib/pages/labor-resource';
import * as ui from '@sage/xtrem-ui';

@ui.decorators.pageExtension<LaborResourceExtension>({
    extends: '@sage/xtrem-master-data/LaborResource',
})
export class LaborResourceExtension extends ui.PageExtension<LaborResourcePage, GraphApi> {
    @ui.decorators.referenceField<LaborResourceExtension>({
        parent() {
            return this.costBlock;
        },
        insertBefore() {
            return this.resourceCostCategories;
        },
        title: 'Posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        width: 'medium',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [ui.nestedFields.text({ bind: 'name' })],
    })
    postingClass: ui.fields.Reference<PostingClass>;
}

declare module '@sage/xtrem-master-data/build/lib/pages/labor-resource' {
    interface LaborResource extends LaborResourceExtension {}
}
