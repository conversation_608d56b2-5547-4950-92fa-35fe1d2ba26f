import type { Collection, Reference, integer } from '@sage/xtrem-core';
import { NodeExtension, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '..';

@decorators.nodeExtension<CompanyExtension>({
    extends: () => xtremSystem.nodes.Company,
    async controlBegin(cx) {
        // Check if there is a discrepancy between the default master data of project and task
        await xtremFinanceData.events.controlBegin.CompanyExtension.isProjectTaskDiscrepancy(
            this.defaultAttributes,
            cx,
        );
    },
})
export class CompanyExtension extends NodeExtension<xtremSystem.nodes.Company> {
    @decorators.referenceProperty<CompanyExtension, 'postingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        filters: { control: { type: 'company', isDetailed: true } },
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.enumProperty<CompanyExtension, 'taxEngine'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.taxEngineDataType,
        defaultValue() {
            return 'genericTaxCalculation';
        },
        async isFrozen() {
            return (await (await this.legislation).id) !== 'US';
        },
    })
    readonly taxEngine: Promise<xtremFinanceData.enums.TaxEngine | null>;

    @decorators.booleanProperty<CompanyExtension, 'doStockPosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['legislation'],
        async defaultValue() {
            return (await this.legislation).doStockPosting;
        },
        updatedValue: useDefaultValue,
    })
    readonly doStockPosting: Promise<boolean>;

    @decorators.booleanProperty<CompanyExtension, 'doWipPosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['legislation'],
        async defaultValue() {
            return xtremFinanceData.sharedFunctions.common.legislationsThatDoWipPosting.includes(
                await (
                    await this.legislation
                ).id,
            );
        },
        async control(cx, val) {
            await xtremFinanceData.events.control.CompanyExtension.wipPostingControl({
                company: this,
                doWipPosting: val,
                cx,
            });
        },
    })
    readonly doWipPosting: Promise<boolean>;

    @decorators.booleanProperty<CompanyExtension, 'doArPosting'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['legislation'],
        async getValue() {
            return (await this.legislation).doApPosting;
        },
    })
    readonly doArPosting: Promise<boolean>;

    @decorators.booleanProperty<CompanyExtension, 'doApPosting'>({
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['legislation'],
        async getValue() {
            return (await this.legislation).doApPosting;
        },
    })
    readonly doApPosting: Promise<boolean>;

    @decorators.booleanProperty<CompanyExtension, 'doNonAbsorbedPosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['doStockPosting'],
        async defaultValue() {
            return (await this.doStockPosting) ? (await this.legislation).doNonAbsorbedPosting : false;
        },
        async control(cx, val) {
            if (!val) return;
            if (!(await this.doStockPosting) && val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed',
                    'Non absorbed posting not allowed if there is no stock posting.',
                );
            }
            if (!(await (await this.legislation).doNonAbsorbedPosting) && val) {
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/node-extensions__company-extension__do_non_absorbed_posting_not_allowed_on_legislation',
                    'You cannot post non-absorbed amounts for this legislation.',
                );
            }
        },
    })
    readonly doNonAbsorbedPosting: Promise<boolean>;

    @decorators.collectionProperty<CompanyExtension, 'attributeTypes'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'company',
        isVital: true,
        node: () => xtremFinanceData.nodes.CompanyAttributeType,
    })
    readonly attributeTypes: Collection<xtremFinanceData.nodes.CompanyAttributeType>;

    @decorators.collectionProperty<CompanyExtension, 'dimensionTypes'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'company',
        isVital: true,
        node: () => xtremFinanceData.nodes.CompanyDimensionType,
    })
    readonly dimensionTypes: Collection<xtremFinanceData.nodes.CompanyDimensionType>;

    @decorators.collectionProperty<CompanyExtension, 'defaultAttributes'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'company',
        isVital: true,
        node: () => xtremFinanceData.nodes.CompanyDefaultAttribute,
    })
    readonly defaultAttributes: Collection<xtremFinanceData.nodes.CompanyDefaultAttribute>;

    @decorators.collectionProperty<CompanyExtension, 'defaultDimensions'>({
        isPublished: true,
        lookupAccess: true,
        reverseReference: 'company',
        isVital: true,
        node: () => xtremFinanceData.nodes.CompanyDefaultDimension,
    })
    readonly defaultDimensions: Collection<xtremFinanceData.nodes.CompanyDefaultDimension>;

    @decorators.integerProperty<CompanyExtension, 'datevConsultantNumber'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isNullable: true,
        async control(cx) {
            await xtremFinanceData.events.control.CompanyExtension.datevConsultantNumberControls({
                company: this,
                cx,
            });
        },
    })
    readonly datevConsultantNumber: Promise<integer | null>;

    @decorators.integerProperty<CompanyExtension, 'datevCustomerNumber'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isNullable: true,
        async control(cx) {
            await xtremFinanceData.events.control.CompanyExtension.datevCustomerNumberControls({
                company: this,
                cx,
            });
        },
    })
    readonly datevCustomerNumber: Promise<integer | null>;

    @decorators.referenceProperty<CompanyExtension, 'bankAccount'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.BankAccount,
        serviceOptions: () => [xtremFinanceData.serviceOptions.paymentTrackingOption],
        filters: {
            control: {
                async financialSite() {
                    return {
                        legalCompany: {
                            id: await this.id,
                        },
                    };
                },
            },
        },
    })
    readonly bankAccount: Reference<xtremFinanceData.nodes.BankAccount | null>;
}
declare module '@sage/xtrem-system/lib/nodes/company' {
    export interface Company extends CompanyExtension {}
}
