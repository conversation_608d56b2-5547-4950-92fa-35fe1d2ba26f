import type { Reference } from '@sage/xtrem-core';
import { NodeExtension, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

@decorators.nodeExtension<ItemExtension>({
    extends: () => xtremMasterData.nodes.Item,
})
export class ItemExtension extends NodeExtension<xtremMasterData.nodes.Item> {
    @decorators.referenceProperty<ItemExtension, 'postingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        filters: {
            control: {
                type: 'item',
                isDetailed: true,
                async isStockItemAllowed() {
                    return (await this.isStockManaged) ? true : { _ne: null };
                },
                async isNonStockItemAllowed() {
                    return (await this.type) === 'good' && !(await this.isStockManaged) ? true : { _ne: null };
                },
                async isServiceItemAllowed() {
                    return (await this.type) === 'service' ? true : { _ne: null };
                },
                async isLandedCostItemAllowed() {
                    return (await this.type) === 'landedCost' ? true : { _ne: null };
                },
            },
        },
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.jsonProperty<ItemExtension, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        lookupAccess: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<ItemExtension, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<ItemExtension, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        lookupAccess: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;
}

declare module '@sage/xtrem-master-data/lib/nodes/item' {
    interface Item extends ItemExtension {}
}
