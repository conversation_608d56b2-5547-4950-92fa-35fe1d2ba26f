import type { NodeQueryFilter, Reference } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

@decorators.nodeExtension<BaseDocumentLineInquiryExtension>({
    extends: () => xtremMasterData.nodes.BaseDocumentLineInquiry,
})
export class BaseDocumentLineInquiryExtension extends NodeExtension<xtremMasterData.nodes.BaseDocumentLineInquiry> {
    @decorators.referenceProperty<BaseDocumentLineInquiryExtension, 'postingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        dataType: () => xtremFinanceData.dataTypes.postingClass,
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    async addExtendedFilter(): Promise<Partial<NodeQueryFilter<xtremMasterData.nodes.BaseDocumentItemLine>>> {
        const postingClass = await this.postingClass;
        return {
            ...(postingClass
                ? {
                      item: {
                          postingClass: { _eq: postingClass?._id },
                      },
                  }
                : {}),
        };
    }
}

declare module '@sage/xtrem-master-data/lib/nodes/base-document-line-inquiry' {
    interface BaseDocumentLineInquiry extends BaseDocumentLineInquiryExtension {}
}
