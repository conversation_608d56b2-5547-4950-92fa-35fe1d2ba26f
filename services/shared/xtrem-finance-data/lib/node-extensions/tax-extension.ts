import type { Reference, integer } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '..';

@decorators.nodeExtension<TaxExtension>({
    extends: () => xtremTax.nodes.Tax,
})
export class TaxExtension extends NodeExtension<xtremTax.nodes.Tax> {
    @decorators.referenceProperty<TaxExtension, 'postingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        filters: {
            control: {
                type: 'tax',
                isDetailed: true,
            },
        },
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.integerProperty<TaxExtension, 'postingKey'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isNullable: true,
        async control(cx) {
            await xtremFinanceData.events.control.TaxExtension.postingKeyControls({ tax: this, cx });
        },
    })
    readonly postingKey: Promise<integer | null>;
}

declare module '@sage/xtrem-tax/lib/nodes/tax' {
    interface Tax extends TaxExtension {}
}
