import type { Reference } from '@sage/xtrem-core';
import { decorators, SubNodeExtension1 } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

@decorators.subNodeExtension1<DetailedResourceExtension>({
    extends: () => xtremMasterData.nodes.DetailedResource,
})
export class DetailedResourceExtension extends SubNodeExtension1<xtremMasterData.nodes.DetailedResource> {
    @decorators.referenceProperty<DetailedResourceExtension, 'postingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        filters: {
            control: {
                type: 'resource',
                isDetailed: true,
            },
        },
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;
}

declare module '@sage/xtrem-master-data/lib/nodes/detailed-resource' {
    interface DetailedResource extends DetailedResourceExtension {}
}
