import type { integer } from '@sage/xtrem-core';
import { NodeExtension, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

@decorators.nodeExtension<BaseBusinessRelationExtension>({
    extends: () => xtremMasterData.nodes.BaseBusinessRelation,
})
export class BaseBusinessRelationExtension extends NodeExtension<xtremMasterData.nodes.BaseBusinessRelation> {
    @decorators.integerProperty<BaseBusinessRelationExtension, 'datevId'>({
        serviceOptions: () => [xtremMasterData.serviceOptions.datevOption],
        isStored: true,
        isPublished: true,
        isNullable: true,
        async control(cx) {
            await xtremFinanceData.events.control.BaseBusinessRelationExtension.datevIdControls(this, cx);
        },
    })
    readonly datevId: Promise<integer | null>;
}

declare module '@sage/xtrem-master-data/lib/nodes/base-business-relation' {
    interface BaseBusinessRelation extends BaseBusinessRelationExtension {}
}
