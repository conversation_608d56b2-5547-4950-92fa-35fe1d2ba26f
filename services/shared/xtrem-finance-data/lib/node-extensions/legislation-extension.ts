import { decorators, NodeExtension } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';

@decorators.nodeExtension<LegislationExtension>({
    extends: () => xtremStructure.nodes.Legislation,
})
export class LegislationExtension extends NodeExtension<xtremStructure.nodes.Legislation> {
    @decorators.booleanProperty<LegislationExtension, 'doStockPosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly doStockPosting: Promise<boolean>;

    @decorators.booleanProperty<LegislationExtension, 'doNonAbsorbedPosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly doNonAbsorbedPosting: Promise<boolean>;

    @decorators.booleanProperty<LegislationExtension, 'doLandedCostGoodsInTransitPosting'>({
        isStored: true,
        isPublished: true,
        async defaultValue() {
            return !!['AU', 'GB', 'US', 'ZA'].includes(await this.id);
        },
        lookupAccess: true,
    })
    readonly doLandedCostGoodsInTransitPosting: Promise<boolean>;

    @decorators.booleanProperty<LegislationExtension, 'doApPosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly doApPosting: Promise<boolean>;

    @decorators.booleanProperty<LegislationExtension, 'doArPosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
    })
    readonly doArPosting: Promise<boolean>;

    @decorators.booleanProperty<LegislationExtension, 'doNonStockVariancePosting'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        async defaultValue() {
            return ['AU', 'GB', 'US', 'ZA'].includes(await this.id);
        },
    })
    readonly doNonStockVariancePosting: Promise<boolean>;
}
declare module '@sage/xtrem-structure/lib/nodes/legislation' {
    export interface Legislation extends LegislationExtension {}
}
