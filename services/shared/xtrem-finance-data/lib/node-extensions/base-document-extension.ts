import type { Collection } from '@sage/xtrem-core';
import { NodeExtension, NodeStatus, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as _ from 'lodash';
import * as xtremFinanceData from '..';
import { financeIntegrationStatusFailed } from '../functions/common';
import { loggers } from '../functions/loggers';

@decorators.nodeExtension<BaseDocumentExtension>({
    extends: () => xtremMasterData.nodes.BaseDocument,
    async prepareEnd() {
        if (this.$.status !== NodeStatus.added && (await this.status) === 'closed') {
            const financeIntegrationStatus = await this.financeIntegrationStatus;
            loggers.baseDocument.debug(() => `Prepare end for closed document - ${financeIntegrationStatus}`);
            const canUpdateClosedDocument =
                (await this.canUpdateClosedDocument) ||
                (await this.forceUpdateForFinance) ||
                financeIntegrationStatusFailed.includes(await this.financeIntegrationStatus);

            await this.$.set({
                canUpdateClosedDocument,
                forceUpdateForFinance: (await this.forceUpdateForFinance) ?? canUpdateClosedDocument,
            });
        }
    },
})
export class BaseDocumentExtension extends NodeExtension<xtremMasterData.nodes.BaseDocument> {
    @decorators.collectionProperty<BaseDocumentExtension, 'financeIntegrationRecords'>({
        node: () => xtremFinanceData.nodes.FinanceTransaction,
        join: {
            documentNumber() {
                return this.number;
            },
            documentType() {
                const financeDocumentType = _.camelCase(this.$.factory.name);
                if (xtremFinanceData.functions.Common.isFinanceDocumentType(financeDocumentType)) {
                    return financeDocumentType;
                }
                return null;
            },
        },
        orderBy: { status: +1 },
    })
    financeIntegrationRecords: Collection<xtremFinanceData.nodes.FinanceTransaction>;

    @decorators.enumProperty<BaseDocumentExtension, 'financeIntegrationStatus'>({
        isPublished: true,
        dataType: () => xtremFinanceData.enums.financeIntegrationStatusDataType,
        async computeValue() {
            const financeDocumentType = _.camelCase(this.$.factory.name);
            if (xtremFinanceData.functions.Common.isFinanceDocumentType(financeDocumentType)) {
                return xtremFinanceData.functions.getDocumentIntegrationStatus(
                    this.$.context,
                    financeDocumentType,
                    await this.number,
                );
            }
            return 'notRecorded';
        },
    })
    readonly financeIntegrationStatus: Promise<xtremFinanceData.enums.FinanceIntegrationStatus>;

    @decorators.booleanProperty<BaseDocumentExtension, 'forceUpdateForFinance'>({
        defaultValue: false,
    })
    readonly forceUpdateForFinance: Promise<boolean>;

    @decorators.booleanProperty<BaseDocumentExtension, 'wasTaxDataChanged'>({
        defaultValue: false,
    })
    readonly wasTaxDataChanged: Promise<boolean>;
}

declare module '@sage/xtrem-master-data/lib/nodes/base-document' {
    interface BaseDocument extends BaseDocumentExtension {}
}
