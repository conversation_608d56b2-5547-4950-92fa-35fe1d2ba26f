import type { Reference } from '@sage/xtrem-core';
import { SubNodeExtension1, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

@decorators.subNodeExtension1<CustomerExtension>({
    extends: () => xtremMasterData.nodes.Customer,
    async controlEnd(cx) {
        await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, await this.storedAttributes);
    },
})
export class CustomerExtension extends SubNodeExtension1<xtremMasterData.nodes.Customer> {
    @decorators.referenceProperty<CustomerExtension, 'postingClass'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        isNullable: true,
        node: () => xtremFinanceData.nodes.PostingClass,
        filters: { control: { type: 'customer', isDetailed: true } },
    })
    readonly postingClass: Reference<xtremFinanceData.nodes.PostingClass | null>;

    @decorators.jsonProperty<CustomerExtension, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
        lookupAccess: true,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.referenceProperty<CustomerExtension, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    @decorators.jsonProperty<CustomerExtension, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        lookupAccess: true,
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;
}

declare module '@sage/xtrem-master-data/lib/nodes/customer' {
    interface Customer extends CustomerExtension {}
}
