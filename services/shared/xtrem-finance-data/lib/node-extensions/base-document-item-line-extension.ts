import type { Context, Reference } from '@sage/xtrem-core';
import { BusinessRuleError, SubNodeExtension1, decorators, useDefaultValue } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

/** this need to be move on BaseDocumentLine  */
@decorators.subNodeExtension1<BaseDocumentItemLineExtension>({
    extends: () => xtremMasterData.nodes.BaseDocumentItemLine,
})
export class BaseDocumentItemLineExtension extends SubNodeExtension1<xtremMasterData.nodes.BaseDocumentItemLine> {
    @decorators.enumProperty<BaseDocumentItemLineExtension, 'dimensionDefinitionLevel'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremFinanceData.enums.dimensionDefinitionLevelDataType,
        getValue: () => 'intersiteTransferOrder',
    })
    readonly dimensionDefinitionLevel: Promise<xtremFinanceData.enums.DimensionDefinitionLevel>;

    @decorators.jsonProperty<BaseDocumentItemLineExtension, 'storedDimensions'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedDimensionsDataType,
    })
    readonly storedDimensions: Promise<object | null>;

    @decorators.jsonProperty<BaseDocumentItemLineExtension, 'storedAttributes'>({
        isPublished: true,
        isStored: true,
        isNullable: true,
        dataType: () => xtremFinanceData.dataTypes.storedAttributesDataType,
        async control(cx, attributes) {
            await xtremFinanceData.functions.attributTypeRestrictedToCheck(cx, this.$.context, attributes);
        },
    })
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;

    @decorators.jsonProperty<BaseDocumentItemLineExtension, 'computedAttributes'>({
        isPublished: true,
        getValue: () => ({}),
    })
    readonly computedAttributes: Promise<object>;

    @decorators.referenceProperty<BaseDocumentItemLineExtension, 'analyticalData'>({
        isStored: true,
        isMutable: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        isFrozen() {
            return xtremFinanceData.functions.isAnalyticalDataFrozen(this);
        },
        node: () => xtremFinanceData.nodes.AnalyticalData,
        dependsOn: ['storedAttributes', 'storedDimensions'],
        async defaultValue() {
            return (await xtremFinanceData.functions.getAnalyticalDataFromStoredAttributesAndDimensions(
                this.$.context,
                await this.storedAttributes,
                await this.storedDimensions,
            )) as any;
        },
        updatedValue: useDefaultValue,
    })
    readonly analyticalData: Reference<xtremFinanceData.nodes.AnalyticalData>;

    /**
     * Method that applies dimensions and attributes to the lines of the stock transfer order line
     * @param context
     * @param baseDocumentItemLine
     * @param storedDimensions
     * @param storedAttributes
     * @returns true or throws error
     */
    @decorators.mutation<typeof BaseDocumentItemLineExtension, 'setDimension'>({
        isPublished: true,
        parameters: [
            {
                name: 'baseDocumentItemLine',
                type: 'reference',
                node: () => xtremMasterData.nodes.BaseDocumentItemLine,
                isMandatory: true,
                isWritable: true,
            },
            { name: 'storedDimensions', type: 'string' },
            { name: 'storedAttributes', type: 'string' },
        ],
        return: 'boolean',
    })
    static async setDimension(
        context: Context,
        baseDocumentItemLine: xtremMasterData.nodes.BaseDocumentItemLine,
        storedDimensions?: string,
        storedAttributes?: string,
    ): Promise<boolean> {
        if (!baseDocumentItemLine) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance-data/base-document-item-line/set-dimension-missing-line',
                    'Missing line to update dimension.',
                ),
            );
        }
        const document = await baseDocumentItemLine.document;
        if ((await document.financeIntegrationStatus) === 'posted') {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance-data/base-document-item-line/update-dimension-posted',
                    'You cannot update dimension on a closed document.',
                ),
            );
        }
        const canUpdateClosedDocument = (await document.financeIntegrationStatus) !== 'posted'; // and submitted

        const updateDocument = await context.read(
            xtremMasterData.nodes.BaseDocument,
            { _id: document._id },
            { forUpdate: true },
        );

        await updateDocument.$.set({
            canUpdateClosedDocument,
            lines: [
                {
                    _action: 'update',
                    _sortValue: await baseDocumentItemLine._sortValue,
                    storedDimensions: storedDimensions ? JSON.parse(storedDimensions) : null,
                    storedAttributes: storedAttributes ? JSON.parse(storedAttributes) : null,
                },
            ],
        });
        await updateDocument.$.save();
        return true;
    }
}

declare module '@sage/xtrem-master-data/lib/nodes/base-document-item-line' {
    interface BaseDocumentItemLine extends BaseDocumentItemLineExtension {}
}
