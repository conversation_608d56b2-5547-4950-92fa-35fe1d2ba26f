import { Logger } from '@sage/xtrem-core';
import type * as xtremFinanceData from '..';

export const loggers = {
    notif: Logger.getLogger(__filename, 'notif'),
    baseDocument: Logger.getLogger(__filename, 'baseDoc'),
    financeIntegration: Logger.getLogger(__filename, 'financeIntegration'),
};

export function getFinanceIntegrationDocumentLogMessage(
    document: xtremFinanceData.interfaces.FinanceIntegrationDocument,
) {
    return `documentType:${document.documentType} documentNumber:${document.documentNumber}`;
}

export function getFinanceIntegrationDocumentLineLogMessage(
    document: xtremFinanceData.interfaces.FinanceIntegrationDocument,
    documentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
) {
    return `${getFinanceIntegrationDocumentLogMessage(document)} movementType:${documentLine.movementType}`;
}

export function getAccountingStagingAmountLogMessage(
    document: xtremFinanceData.interfaces.FinanceIntegrationDocument,
    documentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
    accountingStagingAmount: xtremFinanceData.interfaces.AccountingStagingAmount,
) {
    return `${getFinanceIntegrationDocumentLineLogMessage(document, documentLine)} documentLineType:${accountingStagingAmount.documentLineType}`;
}
