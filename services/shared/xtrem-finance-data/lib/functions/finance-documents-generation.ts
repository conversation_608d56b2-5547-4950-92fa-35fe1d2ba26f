import type { Context } from '@sage/xtrem-core';
import { asyncArray, ValidationSeverity } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '..';
import { mandatoryAccountDimensionAttributes, mandatoryCompanyDimensionAttributes } from './dimensions';

function getStagingLineData(
    item: xtremMasterData.nodes.Item | null,
    customer: xtremMasterData.nodes.Customer | null,
    supplier: xtremMasterData.nodes.Supplier | null,
    tax: xtremTax.nodes.Tax | null,
    resource: xtremMasterData.nodes.DetailedResource | null,
    account: xtremFinanceData.nodes.Account | null,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    movementType: xtremFinanceData.enums.MovementType,
    amountType: xtremFinanceData.enums.AmountType,
    financialSite: xtremSystem.nodes.Site,
): xtremFinanceData.interfaces.StagingLineData {
    return {
        item,
        customer,
        supplier,
        tax,
        resource,
        account,
        documentNumber: '',
        documentType,
        movementType,
        amountType,
        financialSite,
    };
}

/**
 * For a given journal entry type, filter only the lines that apply to a given accounting staging line amount,
 * based on the movement type, amount type, item type and the item stock management flags.
 * This filter applies only for a journal entry creation
 * @param journalEntryType: The journal entry type
 * @param movementType: The staging line amount type
 * @param amountType: The staging line amount amount type
 * @param itemIsStockManaged: Item stock management flag, if it is an item
 * @param itemType: Item type (good, service, landedCost), if it is an item
 */
function getApplicableJournalEntryTypeLinesForJournalEntry(parameters: {
    journalEntryType: xtremFinanceData.nodes.JournalEntryType;
    movementType: xtremFinanceData.enums.MovementType;
    amountType: xtremFinanceData.enums.AmountType;
    itemIsStockManaged: boolean;
    itemType: xtremMasterData.enums.ItemType;
}): Promise<xtremFinanceData.nodes.JournalEntryTypeLine[]> {
    return parameters.journalEntryType.lines
        .filter(
            async entryTypeLine =>
                (await entryTypeLine.movementType) === parameters.movementType &&
                (await entryTypeLine.amountType) === parameters.amountType &&
                ((await (await entryTypeLine.accountType).postingClassType) !== 'item' ||
                    ((await (await entryTypeLine.accountType).postingClassType) === 'item' &&
                        (((await entryTypeLine.isStockItemAllowed) &&
                            parameters.itemType === 'good' &&
                            parameters.itemIsStockManaged) ||
                            ((await entryTypeLine.isNonStockItemAllowed) &&
                                parameters.itemType === 'good' &&
                                !parameters.itemIsStockManaged) ||
                            ((await entryTypeLine.isServiceItemAllowed) && parameters.itemType === 'service') ||
                            ((await entryTypeLine.isLandedCostItemAllowed) && parameters.itemType === 'landedCost')))),
        )
        .toArray();
}

/**
 * For a given journal entry type, filter only the lines that apply to a given accounting staging line amount,
 * based on the movement type, amount type, item type and the item stock management flags.
 * This filter applies only for an ap or ar invoice creation
 * @param journalEntryType: The journal entry type
 * @param movementType: The staging line amount type
 * @param amountType: The staging line amount amount type
 * @param itemIsStockManaged: Item stock management flag, if it is an item
 * @param itemType: Item type (good, service, landedCost), if it is an item
 */
function getApplicableJournalEntryTypeLinesForAPARInvoice(parameters: {
    journalEntryType: xtremFinanceData.nodes.JournalEntryType;
    movementType: xtremFinanceData.enums.MovementType;
    amountType: xtremFinanceData.enums.AmountType;
    itemIsStockManaged: boolean;
    itemType: xtremMasterData.enums.ItemType;
}): Promise<xtremFinanceData.nodes.JournalEntryTypeLine[]> {
    return parameters.journalEntryType.lines
        .filter(
            async entryTypeLine =>
                (await entryTypeLine.movementType) === parameters.movementType &&
                (await entryTypeLine.amountType) === parameters.amountType &&
                !['header', 'line'].includes(await (await entryTypeLine.accountType).postingClassType) &&
                ((await (await entryTypeLine.accountType).postingClassType) !== 'item' ||
                    ((await (await entryTypeLine.accountType).postingClassType) === 'item' &&
                        (((await entryTypeLine.isStockItemAllowed) &&
                            parameters.itemType === 'good' &&
                            parameters.itemIsStockManaged) ||
                            ((await entryTypeLine.isNonStockItemAllowed) &&
                                parameters.itemType === 'good' &&
                                !parameters.itemIsStockManaged) ||
                            ((await entryTypeLine.isServiceItemAllowed) && parameters.itemType === 'service') ||
                            ((await entryTypeLine.isLandedCostItemAllowed) && parameters.itemType === 'landedCost')))),
        )
        .toArray();
}

export function addAccountToArray(
    accounts: xtremFinanceData.nodes.Account[],
    account: xtremFinanceData.nodes.Account | null,
) {
    if (account && !accounts.find(accountsElement => accountsElement === account)) {
        accounts.push(account);
    }
}

/**
 * For a given journal entry type line, create or update (if possible) journal entry line(s)
 * @param context: A context
 * @param entryTypeLine: The journal entry type line
 * @param stagingLineData: The relevant data from an accounting staging line and accounting staging line amount respecting the xtremFinanceData.interfaces.StagingLineData interface
 * @param createJournalResult: A CreateFinanceDocumentsReturn object
 */
async function processApplicableJournalEntryTypeLinesForJournalEntry(
    context: Context,
    entryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createJournalResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    addOrUpdateJournalEntryLineCallback?: (
        context: Context,
        financeDocumentClassInstance: xtremFinanceData.interfaces.FinanceDocumentClass,
        journalEntryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
        postingClassLine: xtremFinanceData.nodes.PostingClassLine | null,
        stagingLineData: xtremFinanceData.interfaces.StagingLineData,
        createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    ) => Promise<void>,
    journalEntryClassInstance?: xtremFinanceData.interfaces.FinanceDocumentClass,
): Promise<xtremFinanceData.nodes.Account[]> {
    let account: xtremFinanceData.nodes.Account | null = null;
    const accounts: xtremFinanceData.nodes.Account[] = [];

    const postingClass = await xtremFinanceData.functions.AccountingEngineCommon.getEntryTypeLinePostingClass(
        context,
        entryTypeLine,
        stagingLineData,
        createJournalResult,
    );

    if (!postingClass && !['header', 'line'].includes(await (await entryTypeLine.accountType).postingClassType)) {
        createJournalResult.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinanceData.classes.LocalizedMessages.cantReadPostingClass(
                context,
                stagingLineData.documentNumber,
                stagingLineData.documentType,
                await (
                    await entryTypeLine.accountType
                ).postingClassType,
            ),
        });
    }

    if (!postingClass) {
        if (addOrUpdateJournalEntryLineCallback && journalEntryClassInstance) {
            await addOrUpdateJournalEntryLineCallback(
                context,
                journalEntryClassInstance,
                entryTypeLine,
                null,
                stagingLineData,
                createJournalResult,
            );
        }
        account = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryLineAccount({
            postingClassLine: null,
            accountingStagingLineAccount: stagingLineData.account,
            tax: stagingLineData.tax,
        });
        addAccountToArray(accounts, account);
    } else {
        await postingClass.lines
            .filter(
                async postingClassLine =>
                    (await postingClassLine.definition) === (await entryTypeLine.accountType) &&
                    (await postingClassLine.chartOfAccount) ===
                        (await (
                            await stagingLineData.financialSite.legalCompany
                        ).chartOfAccount),
            )
            .forEach(async postingClassLine => {
                if (addOrUpdateJournalEntryLineCallback && journalEntryClassInstance) {
                    await addOrUpdateJournalEntryLineCallback(
                        context,
                        journalEntryClassInstance,
                        entryTypeLine,
                        postingClassLine,
                        stagingLineData,
                        createJournalResult,
                    );
                }
                account = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryLineAccount({
                    postingClassLine,
                    accountingStagingLineAccount: null,
                    tax: stagingLineData.tax,
                });
                addAccountToArray(accounts, account);
            });
    }

    return accounts;
}

/**
 * For a given journal entry type line, create (if possible) ap or ar invoice line(s)
 * @param context: A context
 * @param entryTypeLine: The journal entry type line
 * @param stagingLineData: The relevant data from an accounting staging line and accounting staging line amount respecting the xtremFinanceData.interfaces.StagingLineData interface
 * @param createApArInvoiceResult: A CreateFinanceDocumentsReturn object
 */
async function processApplicableJournalEntryTypeLinesForAPARInvoice(
    context: Context,
    entryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createApArInvoiceResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    addAPARInvoiceLineCallback?: (
        context: Context,
        financeDocumentClassInstance: xtremFinanceData.interfaces.FinanceDocumentClass,
        journalEntryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
        postingClassLine: xtremFinanceData.nodes.PostingClassLine | null,
        stagingLineData: xtremFinanceData.interfaces.StagingLineData,
        createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    ) => Promise<void>,
    accountsPayableReceivableInvoiceClassClassInstance?: xtremFinanceData.interfaces.FinanceDocumentClass,
): Promise<xtremFinanceData.nodes.Account[]> {
    let account: xtremFinanceData.nodes.Account | null = null;
    const accounts: xtremFinanceData.nodes.Account[] = [];

    const postingClass = await xtremFinanceData.functions.AccountingEngineCommon.getEntryTypeLinePostingClass(
        context,
        entryTypeLine,
        stagingLineData,
        createApArInvoiceResult,
    );

    if (!postingClass) {
        createApArInvoiceResult.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinanceData.classes.LocalizedMessages.cantReadPostingClassForItem(
                context,
                stagingLineData.documentNumber,
                stagingLineData.documentType,
                await (
                    await entryTypeLine.accountType
                ).postingClassType,
                stagingLineData.item ? await stagingLineData.item.id : '',
            ),
        });
    } else {
        await postingClass.lines.forEach(async postingClassLine => {
            if (
                (await postingClassLine.definition) === (await entryTypeLine.accountType) &&
                (await postingClassLine.chartOfAccount) ===
                    (await (
                        await stagingLineData.financialSite.legalCompany
                    ).chartOfAccount)
            ) {
                if (addAPARInvoiceLineCallback && accountsPayableReceivableInvoiceClassClassInstance) {
                    await addAPARInvoiceLineCallback(
                        context,
                        accountsPayableReceivableInvoiceClassClassInstance,
                        entryTypeLine,
                        postingClassLine,
                        stagingLineData,
                        createApArInvoiceResult,
                    );
                }
                account = await xtremFinanceData.functions.AccountingEngineCommon.getAPARInvoiceLineAccount(
                    postingClassLine,
                    stagingLineData.tax,
                );
                addAccountToArray(accounts, account);
            }
        });
    }

    return accounts;
}

/**
 * Processes a staging line amount for a given journal entry type
 * @param context: A context
 * @param journalEntryType: The journal entry type
 * @param stagingLineData: The relevant data from an accounting staging line and accounting staging line amount respecting the xtremFinanceData.interfaces.StagingLineData interface
 * @param createFinanceDocumentsReturn: A CreateFinanceDocumentsReturn object
 */
export async function processFinanceIntegrationDocumentLineAmount(
    context: Context,
    journalEntryType: xtremFinanceData.nodes.JournalEntryType,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    addOrUpdateLineCallback?: (
        context: Context,
        financeDocumentClassInstance: xtremFinanceData.interfaces.FinanceDocumentClass,
        journalEntryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
        postingClassLine: xtremFinanceData.nodes.PostingClassLine | null,
        stagingLineData: xtremFinanceData.interfaces.StagingLineData,
        createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    ) => Promise<void>,
    financeDocumentClassInstance?: xtremFinanceData.interfaces.FinanceDocumentClass,
): Promise<xtremFinanceData.nodes.Account[]> {
    const accounts: xtremFinanceData.nodes.Account[] = [];

    if ((await journalEntryType.targetDocumentType) === 'journalEntry') {
        await asyncArray(
            await getApplicableJournalEntryTypeLinesForJournalEntry({
                journalEntryType,
                movementType: stagingLineData.movementType,
                amountType: stagingLineData.amountType,
                itemIsStockManaged: (await stagingLineData.item?.isStockManaged) || false,
                itemType: (await stagingLineData.item?.type) || 'good',
            }),
        ).forEach(async applicableJournalEntryTypeLine => {
            (
                await processApplicableJournalEntryTypeLinesForJournalEntry(
                    context,
                    applicableJournalEntryTypeLine,
                    stagingLineData,
                    createFinanceDocumentsReturn,
                    addOrUpdateLineCallback,
                    financeDocumentClassInstance,
                )
            ).forEach(account => {
                addAccountToArray(accounts, account);
            });
        });
    } else {
        await asyncArray(
            await getApplicableJournalEntryTypeLinesForAPARInvoice({
                journalEntryType,
                movementType: stagingLineData.movementType,
                amountType: stagingLineData.amountType,
                itemIsStockManaged: (await stagingLineData.item?.isStockManaged) || false,
                itemType: (await stagingLineData.item?.type) || 'good',
            }),
        ).forEach(async applicableJournalEntryTypeLine => {
            (
                await processApplicableJournalEntryTypeLinesForAPARInvoice(
                    context,
                    applicableJournalEntryTypeLine,
                    stagingLineData,
                    createFinanceDocumentsReturn,
                    addOrUpdateLineCallback,
                    financeDocumentClassInstance,
                )
            ).forEach(account => {
                addAccountToArray(accounts, account);
            });
        });
    }
    return accounts;
}

export async function getTax(
    context: Context,
    params: {
        tax: xtremTax.nodes.Tax | null;
        documentLineType: xtremFinanceData.enums.AccountsPayableReceivableInvoiceDocumentLineType;
        createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn;
        amount: number;
        baseTaxId?: number;
        taxId?: number;
    },
): Promise<xtremTax.nodes.Tax | null> {
    let newLineAmountTax: xtremTax.nodes.Tax | null = null;

    if (params.documentLineType === 'taxLine') {
        if (params.baseTaxId) {
            const baseTax = await context.tryRead(xtremTax.nodes.BaseTax, {
                _id: params.baseTaxId,
            });
            if (await baseTax?.taxReference) {
                newLineAmountTax = (await baseTax?.taxReference) as xtremTax.nodes.Tax;
                if (!newLineAmountTax && params.amount) {
                    params.createFinanceDocumentsReturn.validationMessages.push({
                        type: ValidationSeverity.error,
                        message: xtremFinanceData.classes.LocalizedMessages.taxNotFound(context, params.baseTaxId),
                    });
                }
            }
        }
    } else if (params.taxId) {
        if (params.taxId !== params.tax?._id) {
            newLineAmountTax = await context.tryRead(xtremTax.nodes.Tax, {
                _id: params.taxId,
            });
        } else {
            newLineAmountTax = params.tax;
        }

        if (!newLineAmountTax) {
            params.createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinanceData.classes.LocalizedMessages.taxNotFound(context, params.taxId),
            });
        }
    }
    return newLineAmountTax;
}

async function processFinanceIntegrationDocumentLine(
    context: Context,
    journalEntryType: xtremFinanceData.nodes.JournalEntryType,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    financialSite: xtremSystem.nodes.Site,
    financeIntegrationDocumentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
    financeIntegrationDocument: {
        item: xtremMasterData.nodes.Item | null;
        customer: xtremMasterData.nodes.Customer | null;
        supplier: xtremMasterData.nodes.Supplier | null;
        resource: xtremMasterData.nodes.DetailedResource | null;
    },
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
    documentNumber: string,
    apArAccount: xtremFinanceData.nodes.Account | null,
): Promise<xtremFinanceData.nodes.Account[]> {
    let financeIntegrationDocumentLineAmountAccounts: xtremFinanceData.nodes.Account[] = [];
    const accounts: xtremFinanceData.nodes.Account[] = [];
    let tax: xtremTax.nodes.Tax | null = null;

    await asyncArray(financeIntegrationDocumentLine.amounts).forEach(async financeIntegrationDocumentLineAmount => {
        tax = await getTax(context, {
            tax,
            documentLineType: financeIntegrationDocumentLineAmount.documentLineType,
            createFinanceDocumentsReturn,
            amount: financeIntegrationDocumentLineAmount.amount,
            baseTaxId: financeIntegrationDocumentLineAmount.baseTaxSysId,
            taxId: financeIntegrationDocumentLineAmount.taxSysId,
        });
        financeIntegrationDocumentLineAmountAccounts = await processFinanceIntegrationDocumentLineAmount(
            context,
            journalEntryType,
            getStagingLineData(
                financeIntegrationDocument.item,
                financeIntegrationDocument.customer,
                financeIntegrationDocument.supplier,
                tax,
                financeIntegrationDocument.resource,
                null,
                documentType,
                financeIntegrationDocumentLine.movementType,
                financeIntegrationDocumentLineAmount.amountType,
                financialSite,
            ),
            createFinanceDocumentsReturn,
        );

        const isTaxAmount: boolean = financeIntegrationDocumentLineAmount.amountType === 'taxAmount';

        await mandatoryCompanyDimensionAttributes(context, {
            companyId: (await financialSite.legalCompany)._id,
            storedAttributes: financeIntegrationDocumentLine.storedAttributes,
            storedDimensions: financeIntegrationDocumentLine.storedDimensions,
            createFinanceDocumentsReturn,
            item: financeIntegrationDocument.item,
            documentNumber,
            isTaxAmount,
            uiBaseDocumentLineSysId: financeIntegrationDocumentLine.uiBaseDocumentLineSysId,
            uiSourceDocumentNumber: financeIntegrationDocumentLine.uiSourceDocumentNumber,
            sourceDocumentNumber: financeIntegrationDocumentLine.sourceDocumentNumber,
        });

        financeIntegrationDocumentLineAmountAccounts.forEach(account => addAccountToArray(accounts, account));

        // Check headerAccount for mandatory dimensions
        if (apArAccount) {
            await mandatoryAccountDimensionAttributes(context, {
                accounts: [apArAccount],
                storedAttributes: financeIntegrationDocumentLine.storedAttributes,
                storedDimensions: financeIntegrationDocumentLine.storedDimensions,
                createFinanceDocumentsReturn,
                item: financeIntegrationDocument.item,
                documentNumber,
                isTaxAmount,
                uiBaseDocumentLineSysId: financeIntegrationDocumentLine.uiBaseDocumentLineSysId,
                uiSourceDocumentNumber: financeIntegrationDocumentLine.uiSourceDocumentNumber,
                sourceDocumentNumber: financeIntegrationDocumentLine.sourceDocumentNumber,
            });
        }

        // check doucmentLineAccount for mandatory dimensions
        await mandatoryAccountDimensionAttributes(context, {
            accounts,
            storedAttributes: financeIntegrationDocumentLine.storedAttributes,
            storedDimensions: financeIntegrationDocumentLine.storedDimensions,
            createFinanceDocumentsReturn,
            item: financeIntegrationDocument.item,
            documentNumber,
            isTaxAmount,
            uiBaseDocumentLineSysId: financeIntegrationDocumentLine.uiBaseDocumentLineSysId,
            uiSourceDocumentNumber: financeIntegrationDocumentLine.uiSourceDocumentNumber,
            sourceDocumentNumber: financeIntegrationDocumentLine.sourceDocumentNumber,
        });
    });

    if (financeIntegrationDocumentLine.amounts.length > 0 && accounts.length === 0) {
        createFinanceDocumentsReturn.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinanceData.classes.LocalizedMessages.noFinanceDocumentLinesGeneratedForItem(
                context,
                (await financeIntegrationDocument.item?.id) || '',
                await journalEntryType.name,
                xtremFinanceData.enums.movementTypeDataType.getLocalizedValue(
                    context,
                    financeIntegrationDocumentLine.movementType,
                ),
            ),
        });
    }
    return accounts;
}

async function getItem(
    context: Context,
    item: xtremMasterData.nodes.Item | null,
    financeIntegrationDocumentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremMasterData.nodes.Item | null> {
    let newLineItem: xtremMasterData.nodes.Item | null = null;
    if (financeIntegrationDocumentLine.itemSysId) {
        if (financeIntegrationDocumentLine.itemSysId !== item?._id) {
            newLineItem = await context.tryRead(xtremMasterData.nodes.Item, {
                _id: financeIntegrationDocumentLine.itemSysId,
            });
        } else {
            newLineItem = item;
        }

        if (!newLineItem) {
            createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinanceData.classes.LocalizedMessages.itemNotFound(
                    context,
                    financeIntegrationDocumentLine.itemSysId,
                ),
            });
        }
    }
    return newLineItem;
}

async function getCustomer(
    context: Context,
    customer: xtremMasterData.nodes.Customer | null,
    financeIntegrationDocumentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremMasterData.nodes.Customer | null> {
    let newLineCustomer: xtremMasterData.nodes.Customer | null = null;
    if (financeIntegrationDocumentLine?.customerSysId) {
        if (financeIntegrationDocumentLine.customerSysId !== customer?._id) {
            newLineCustomer = await context.tryRead(xtremMasterData.nodes.Customer, {
                _id: financeIntegrationDocumentLine.customerSysId,
            });
        } else {
            newLineCustomer = customer;
        }

        if (!newLineCustomer) {
            createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinanceData.classes.LocalizedMessages.customerNotFound(
                    context,
                    financeIntegrationDocumentLine.customerSysId,
                ),
            });
        }
    }

    return newLineCustomer;
}

async function getSupplier(
    context: Context,
    supplier: xtremMasterData.nodes.Supplier | null,
    financeIntegrationDocumentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremMasterData.nodes.Supplier | null> {
    let newLineSupplier: xtremMasterData.nodes.Supplier | null = null;
    if (financeIntegrationDocumentLine?.supplierSysId) {
        if (financeIntegrationDocumentLine.supplierSysId !== supplier?._id) {
            newLineSupplier = await context.tryRead(xtremMasterData.nodes.Supplier, {
                _id: financeIntegrationDocumentLine.supplierSysId,
            });
        } else {
            newLineSupplier = supplier;
        }

        if (!newLineSupplier) {
            createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinanceData.classes.LocalizedMessages.supplierNotFound(
                    context,
                    financeIntegrationDocumentLine.supplierSysId,
                ),
            });
        }
    }
    return newLineSupplier;
}

async function getResource(
    context: Context,
    resource: xtremMasterData.nodes.DetailedResource | null,
    financeIntegrationDocumentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremMasterData.nodes.DetailedResource | null> {
    let newLineResource: xtremMasterData.nodes.DetailedResource | null = null;
    if (financeIntegrationDocumentLine?.resourceSysId) {
        if (financeIntegrationDocumentLine.resourceSysId !== resource?._id) {
            newLineResource = await context.tryRead(xtremMasterData.nodes.DetailedResource, {
                _id: financeIntegrationDocumentLine.resourceSysId,
            });
        } else {
            newLineResource = resource;
        }

        if (!newLineResource) {
            createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinanceData.classes.LocalizedMessages.resourceNotFound(
                    context,
                    financeIntegrationDocumentLine.resourceSysId,
                ),
            });
        }
    }
    return newLineResource;
}

async function processFinanceIntegrationDocument(
    context: Context,
    financeIntegrationDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<void> {
    let item: xtremMasterData.nodes.Item | null = null;
    let customer: xtremMasterData.nodes.Customer | null = null;
    let supplier: xtremMasterData.nodes.Supplier | null = null;
    let resource: xtremMasterData.nodes.DetailedResource | null = null;
    let apArAccount: xtremFinanceData.nodes.Account | null = null;

    const financialSite = await context.tryRead(xtremSystem.nodes.Site, {
        _id: financeIntegrationDocument.financialSiteSysId,
    });
    if (!financialSite) {
        createFinanceDocumentsReturn.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinanceData.classes.LocalizedMessages.siteNotFound(
                context,
                financeIntegrationDocument.financialSiteSysId,
            ),
        });
    }

    if (financialSite) {
        const journalEntryType = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryType(
            context,
            await (
                await financialSite.legalCompany
            ).legislation,
            financeIntegrationDocument.documentType,
            financeIntegrationDocument.targetDocumentType,
        );

        if (journalEntryType) {
            if (
                ['accountsPayableInvoice', 'accountsReceivableInvoice'].includes(
                    await journalEntryType.targetDocumentType,
                )
            ) {
                customer = await getCustomer(
                    context,
                    customer,
                    financeIntegrationDocument.documentLines[0],
                    createFinanceDocumentsReturn,
                );
                supplier = await getSupplier(
                    context,
                    supplier,
                    financeIntegrationDocument.documentLines[0],
                    createFinanceDocumentsReturn,
                );
                apArAccount = await xtremFinanceData.functions.AccountingEngineCommon.getApArInvoiceAccount(
                    context,
                    journalEntryType,
                    await journalEntryType.targetDocumentType,
                    financeIntegrationDocument.documentType,
                    financeIntegrationDocument.documentNumber,
                    customer,
                    supplier,
                    await (
                        await financialSite.legalCompany
                    ).chartOfAccount,
                    createFinanceDocumentsReturn,
                );
            }

            await asyncArray(financeIntegrationDocument.documentLines).forEach(async financeIntegrationDocumentLine => {
                item = await getItem(context, item, financeIntegrationDocumentLine, createFinanceDocumentsReturn);
                customer = await getCustomer(
                    context,
                    customer,
                    financeIntegrationDocumentLine,
                    createFinanceDocumentsReturn,
                );
                supplier = await getSupplier(
                    context,
                    supplier,
                    financeIntegrationDocumentLine,
                    createFinanceDocumentsReturn,
                );
                resource = await getResource(
                    context,
                    resource,
                    financeIntegrationDocumentLine,
                    createFinanceDocumentsReturn,
                );

                await processFinanceIntegrationDocumentLine(
                    context,
                    journalEntryType,
                    financeIntegrationDocument.documentType,
                    financialSite,
                    financeIntegrationDocumentLine,
                    {
                        item,
                        customer,
                        supplier,
                        resource,
                    },
                    createFinanceDocumentsReturn,
                    financeIntegrationDocument.documentNumber,
                    apArAccount,
                );
            });
        }
    }
}

export async function financeDownstreamDocumentControlFromNotificationPayload(
    context: Context,
    notificationPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[],
): Promise<xtremFinanceData.interfaces.CreateFinanceDocumentsReturn> {
    const createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn = {
        documentsCreated: [],
        validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
    };

    await asyncArray(notificationPayload).forEach(financeIntegrationDocument =>
        processFinanceIntegrationDocument(context, financeIntegrationDocument, createFinanceDocumentsReturn),
    );
    return createFinanceDocumentsReturn;
}
