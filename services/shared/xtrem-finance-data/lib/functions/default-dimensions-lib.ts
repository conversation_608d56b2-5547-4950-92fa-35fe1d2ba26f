import type { Context } from '@sage/xtrem-core';
import { BusinessRuleError } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import { isEmpty } from 'lodash';
import * as xtremFinanceData from '../index';

export interface DefaultOptions {
    dimensionDefinitionLevel: xtremFinanceData.enums.DimensionDefinitionLevel;
    onlyFromItem?: boolean;
    companyId: number;
    site: xtremSystem.nodes.Site;
    customer?: xtremMasterData.nodes.Customer;
    supplier?: xtremMasterData.nodes.Supplier;
    item?: xtremMasterData.nodes.Item;
    shippingSite?: xtremSystem.nodes.Site;
    receivingSite?: xtremSystem.nodes.Site | null;
}

export interface DefaultOptionsOrderToOrder extends DefaultOptions {
    storedAttributes?: Object | null;
    storedDimensions?: Object | null;
}

/**
 * Gets the default dimensions for a given company and site/customer/supplier using the rules for defaulting from the company
 * @param context current context
 * @param options.dimensionDefinitionLevel either 'manufacturingDirect', 'salesDirect', ... depending on the document for which dimensions are needed
 * @param options.companyId _id of the company to retrieve the rules for defaulting
 * @param options.site a site from which the defaults are taken if the rule specifies it as source
 * @param options.customer? a customer from which the defaults are taken if the rule specifies it as source
 * @param options.supplier? a supplier from which the defaults are taken if the rule specifies it as source
 * @param options.item? an item from which the defaults are taken if the rule specifies it as source
 * @param options.onlyFromItem? if true, only the attributes from the item are taken into account
 * @returns Promise of object or null (json object with all the defaulted dimensions)
 */
export async function getDefaultDimensions(context: Context, options: DefaultOptions): Promise<Object | null> {
    // get the rules for the company
    const companyDefaultDimensions = context.query(xtremFinanceData.nodes.CompanyDefaultDimension, {
        filter: { company: options.companyId, dimensionDefinitionLevel: options.dimensionDefinitionLevel },
    });
    // get an array of all active dimensions
    const activeDimensionTypes = context.query(xtremFinanceData.nodes.DimensionType, {
        filter: { isActive: true },
    });
    const storedDimensions: Object = {}; // prepare return value, unfortunately it can't be typed here
    // loop over all rules and try to default the corresponding dimensions
    await companyDefaultDimensions.forEach(async (defaultDimension: xtremFinanceData.nodes.CompanyDefaultDimension) => {
        const dimensionType = await activeDimensionTypes.find(
            async type => type._id === (await defaultDimension.dimensionType)._id,
        );
        if (dimensionType) {
            const dictKey = await dimensionType.docProperty;
            // take the default from either site or customer or supplier or ... (future development)
            switch (await defaultDimension.masterDataDefault) {
                case 'site':
                case 'shippingSite': {
                    const storedDimensionSite = (await options.site
                        ?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions;
                    if (storedDimensionSite?.[dictKey] && !options.onlyFromItem) {
                        (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] =
                            storedDimensionSite[dictKey];
                    }
                    break;
                }
                case 'receivingSite': {
                    const storedDimensionReceivingSite = (await options.receivingSite
                        ?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions;
                    if (storedDimensionReceivingSite?.[dictKey] && !options.onlyFromItem) {
                        (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] =
                            storedDimensionReceivingSite[dictKey];
                    }
                    break;
                }
                case 'customer': {
                    const storedDimensionCustomer = (await options.customer
                        ?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions;
                    if (storedDimensionCustomer?.[dictKey] && !options.onlyFromItem) {
                        (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] =
                            storedDimensionCustomer[dictKey];
                    }
                    break;
                }
                case 'supplier': {
                    const storedDimensionSupplier = (await options.supplier
                        ?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions;
                    if (storedDimensionSupplier?.[dictKey] && !options.onlyFromItem) {
                        (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] =
                            storedDimensionSupplier[dictKey];
                    }
                    break;
                }
                case 'item': {
                    const storedDimensionItem = (await options.item
                        ?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions;
                    if (storedDimensionItem?.[dictKey]) {
                        (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] =
                            storedDimensionItem[dictKey];
                    }
                    break;
                }
                default:
                    throw new BusinessRuleError(
                        context.localize(
                            '@sage/xtrem-finance-data/functions__default_dimension_lib__wrong_source_for_dimensions',
                            'You cannot assign the default {{masterDataDefault}} to this document {{dimensionDefinitionLevel}}.',
                            {
                                masterDataDefault: context.localizeEnumMember(
                                    '@sage/xtrem-finance-data/MasterDataDefault',
                                    await defaultDimension.masterDataDefault,
                                ),
                                dimensionDefinitionLevel: context.localizeEnumMember(
                                    '@sage/xtrem-finance-data/DimensionDefinitionLevel',
                                    await defaultDimension.dimensionDefinitionLevel,
                                ),
                            },
                        ),
                    );
            }
        }
    });
    return !isEmpty(storedDimensions) ? storedDimensions : null;
}

/**
 * Gets the default attributes for a given company and site/customer/supplier using the rules for defaulting from the company
 * @param context current context
 * @param options.dimensionDefinitionLevel either 'manufacturingDirect', 'salesDirect', ... depending on the document for which dimensions are needed
 * @param options.companyId _id of the company to retrieve the rules for defaulting
 * @param options.site a site from which the defaults are taken if the rule specifies it as source
 * @param options.customer? a customer from which the defaults are taken if the rule specifies it as source
 * @param options.supplier? a supplier from which the defaults are taken if the rule specifies it as source
 * @param options.item? an item from which the defaults are taken if the rule specifies it as source
 * @param options.onlyFromItem if true, only the attributes from the item are taken into account
 * @returns Promise of xtremMasterData.interfaces.StoredAttributes or null (json object with all the defaulted attributes)
 */
export async function getDefaultAttributes(
    context: Context,
    options: DefaultOptions,
): Promise<xtremMasterData.interfaces.StoredAttributes | null> {
    // get the rules for the company
    const companyDefaultAttributes = context.query(xtremFinanceData.nodes.CompanyDefaultAttribute, {
        filter: { company: options.companyId, dimensionDefinitionLevel: options.dimensionDefinitionLevel },
    });
    // get an array of all active attributes
    const activeAttributeTypes = context.query(xtremFinanceData.nodes.AttributeType, {
        filter: { isActive: true, nodeLink: 'attribute' },
    });
    const storedAttributes: Object | null = {}; // prepare return value, unfortunately it can't be typed here
    // loop over all rules and try to default the corresponding attributes
    await companyDefaultAttributes.forEach(async (defaultAttribute: xtremFinanceData.nodes.CompanyDefaultAttribute) => {
        const attributeType = await activeAttributeTypes.find(
            async type => type._id === (await defaultAttribute.attributeType)._id,
        );
        if (attributeType) {
            const dictKey = await attributeType.id;
            // take the default from either site or customer or supplier or ... (future development)
            switch (await defaultAttribute.masterDataDefault) {
                case 'site':
                case 'shippingSite': {
                    const storedAttributesSite: xtremMasterData.interfaces.StoredAttributes = (await options.site
                        ?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes;
                    if (storedAttributesSite?.[dictKey] && !options.onlyFromItem) {
                        (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] =
                            storedAttributesSite[dictKey];
                    }
                    break;
                }
                case 'receivingSite': {
                    const storedAttributesReceivingSite: xtremMasterData.interfaces.StoredAttributes = (await options
                        .receivingSite?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes;
                    if (storedAttributesReceivingSite?.[dictKey] && !options.onlyFromItem) {
                        (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] =
                            storedAttributesReceivingSite[dictKey];
                    }
                    break;
                }
                case 'customer': {
                    const storedAttributesCustomer: xtremMasterData.interfaces.StoredAttributes = (await options
                        .customer?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes;
                    if (storedAttributesCustomer?.[dictKey] && !options.onlyFromItem) {
                        (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] =
                            storedAttributesCustomer[dictKey];
                    }
                    break;
                }
                case 'supplier': {
                    const storedAttributesSupplier: xtremMasterData.interfaces.StoredAttributes = (await options
                        .supplier?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes;
                    if (storedAttributesSupplier?.[dictKey] && !options.onlyFromItem) {
                        (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] =
                            storedAttributesSupplier[dictKey];
                    }
                    break;
                }
                case 'item': {
                    const storedAttributesItem: xtremMasterData.interfaces.StoredAttributes = (await options.item
                        ?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes;
                    if (storedAttributesItem?.[dictKey]) {
                        (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] =
                            storedAttributesItem[dictKey];
                    }
                    break;
                }
                default:
                    throw new BusinessRuleError(
                        context.localize(
                            '@sage/xtrem-finance-data/functions__default_dimension_lib__wrong_source_for_dimensions',
                            'You cannot assign the default {{masterDataDefault}} to this document {{dimensionDefinitionLevel}}.',
                            {
                                masterDataDefault: context.localizeEnumMember(
                                    '@sage/xtrem-finance-data/MasterDataDefault',
                                    await defaultAttribute.masterDataDefault,
                                ),
                                dimensionDefinitionLevel: context.localizeEnumMember(
                                    '@sage/xtrem-finance-data/DimensionDefinitionLevel',
                                    await defaultAttribute.dimensionDefinitionLevel,
                                ),
                            },
                        ),
                    );
            }
        }
    });
    return !isEmpty(storedAttributes) ? (storedAttributes as xtremMasterData.interfaces.StoredAttributes) : null;
}

/**
 * Gets the combined dimensions for order to order management. If there is a rule on the company for order to order management:
 * - take the dimension from the source document (options.storedDimensions) if the default is 'sourceDocument',
 * - take the dimension from the master data defaultDimensions (site, ...), if specified
 * - leave the dimension empty if there is no rule for it on the orderToOrder setting on the company
 * @param context current context
 * @param options.dimensionDefinitionLevel definition level for the orderToOrder rules (e.g. manufacturingOrderToOrder)
 * @param options.companyId _id of the company to retrieve the rules for defaulting
 * @param options.site a site from which the defaults are taken if the rule specifies it as source
 * @param options.supplier? a supplier from which the defaults are taken if the rule specifies it as source
 * @param options.item? an item from which the defaults are taken if the rule specifies it as source
 * @param options.storedDimensions dimensions from the source document
 * @returns Promise of object or null (json object with all the defaulted dimensions)
 */
export async function getDefaultOrderToOrderDimensions(
    context: Context,
    options: DefaultOptionsOrderToOrder,
): Promise<Object | null> {
    // get the order to order rules for the company
    const companyDefaultDimensionsOrderToOrder = context.query(xtremFinanceData.nodes.CompanyDefaultDimension, {
        filter: { company: options.companyId, dimensionDefinitionLevel: options.dimensionDefinitionLevel },
    });
    // if neither attribute nor dimension settings for order to order management are found => return incoming dimensions from source document
    if (
        (await companyDefaultDimensionsOrderToOrder.length) === 0 &&
        (await context.queryCount(xtremFinanceData.nodes.CompanyDefaultAttribute, {
            filter: {
                company: options.companyId,
                dimensionDefinitionLevel: options.dimensionDefinitionLevel,
            },
        })) === 0
    ) {
        return options.storedDimensions ? options.storedDimensions : null;
    }

    // get an array of all active dimensions
    const activeDimensionTypes = context.query(xtremFinanceData.nodes.DimensionType, {
        filter: { isActive: true },
    });
    const storedDimensions: Object = {}; // prepare return value, unfortunately it can't be typed here
    // loop over all rules and try to default the corresponding dimensions
    await companyDefaultDimensionsOrderToOrder.forEach(
        async (defaultDimension: xtremFinanceData.nodes.CompanyDefaultDimension) => {
            const dimensionType = await activeDimensionTypes.find(
                async type => type._id === (await defaultDimension.dimensionType)._id,
            );
            if (dimensionType) {
                const dictKey = await dimensionType.docProperty;
                // take the default from either site/supplier/item or source document
                switch (await defaultDimension.masterDataDefault) {
                    case 'site':
                        if (
                            (await options.site.storedDimensions) &&
                            ((await options.site.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions)[
                                dictKey
                            ]
                        ) {
                            (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] = (
                                (await options.site.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions
                            )[dictKey];
                        }
                        break;
                    case 'sourceDocument':
                        if (
                            options.storedDimensions &&
                            (options.storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey]
                        ) {
                            (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] = (
                                options.storedDimensions as xtremFinanceData.interfaces.StoredDimensions
                            )[dictKey];
                        }
                        break;
                    case 'supplier':
                        if (
                            (await options.supplier?.storedDimensions) &&
                            (
                                (await options.supplier
                                    ?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions
                            )[dictKey]
                        ) {
                            (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] = (
                                (await options.supplier
                                    ?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions
                            )[dictKey];
                        }
                        break;
                    case 'item':
                        if (
                            (await options.item?.storedDimensions) &&
                            ((await options.item?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions)[
                                dictKey
                            ]
                        ) {
                            (storedDimensions as xtremFinanceData.interfaces.StoredDimensions)[dictKey] = (
                                (await options.item?.storedDimensions) as xtremFinanceData.interfaces.StoredDimensions
                            )[dictKey];
                        }
                        break;
                    default:
                        break;
                }
            }
        },
    );
    return !isEmpty(storedDimensions) ? storedDimensions : null;
}

/**
 * Gets the combined attributes for order to order management. If there is a rule on the company for order to order management:
 * - take the attribute from the source document (options.storedAttributes) if the default is 'sourceDocument',
 * - take the attribute from the master data defaultAttributes (site, ...), if specified,
 * - leave the attribute empty if there is no rule for it on the orderToOrder setting on the company
 * @param context current context
 * @param options.dimensionDefinitionLevel definition level for the orderToOrder rules (e.g. manufacturingOrderToOrder)
 * @param options.companyId _id of the company to retrieve the rules for defaulting
 * @param options.site a site from which the defaults are taken if the rule specifies it as source
 * @param options.supplier? a supplier from which the defaults are taken if the rule specifies it as source
 * @param options.item? an item from which the defaults are taken if the rule specifies it as source
 * @param options.storedAttributes attributes from the source document
 * @returns Promise of object or null (json object with all the defaulted attributes)
 */
export async function getDefaultOrderToOrderAttributes(
    context: Context,
    options: DefaultOptionsOrderToOrder,
): Promise<Object | null> {
    // get the order to order rules for the company
    const companyDefaultAttributesOrderToOrder = context.query(xtremFinanceData.nodes.CompanyDefaultAttribute, {
        filter: { company: options.companyId, dimensionDefinitionLevel: options.dimensionDefinitionLevel },
    });
    // if neither attribute nor dimension settings for order to order management are found => return incoming attributes from source document
    if (
        (await companyDefaultAttributesOrderToOrder.length) === 0 &&
        (await context.queryCount(xtremFinanceData.nodes.CompanyDefaultDimension, {
            filter: {
                company: options.companyId,
                dimensionDefinitionLevel: options.dimensionDefinitionLevel,
            },
        })) === 0
    ) {
        return options.storedAttributes ? options.storedAttributes : null;
    }

    // get an array of all active attributes
    const activeAttributeTypes = context.query(xtremFinanceData.nodes.AttributeType, {
        filter: { isActive: true },
    });
    const storedAttributes: Object = {}; // prepare return value, unfortunately it can't be typed here
    // loop over all rules and try to default the corresponding attributes
    await companyDefaultAttributesOrderToOrder.forEach(
        async (defaultAttribute: xtremFinanceData.nodes.CompanyDefaultAttribute) => {
            const attributeType = await activeAttributeTypes.find(
                async type => type._id === (await defaultAttribute.attributeType)._id,
            );
            if (attributeType) {
                const dictKey = await attributeType.id;
                // take the default from either site/supplier/item or source document
                switch (await defaultAttribute.masterDataDefault) {
                    case 'site':
                        if (
                            (await options.site.storedAttributes) &&
                            ((await options.site.storedAttributes) as xtremMasterData.interfaces.StoredAttributes)[
                                dictKey
                            ]
                        ) {
                            (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] = (
                                (await options.site.storedAttributes) as xtremMasterData.interfaces.StoredAttributes
                            )[dictKey];
                        }
                        break;
                    case 'sourceDocument':
                        if (
                            options.storedAttributes &&
                            (options.storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey]
                        ) {
                            (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] = (
                                options.storedAttributes as xtremMasterData.interfaces.StoredAttributes
                            )[dictKey];
                        }
                        break;
                    case 'supplier':
                        if (
                            (await options.supplier?.storedAttributes) &&
                            ((await options.supplier?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes)[
                                dictKey
                            ]
                        ) {
                            (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] = (
                                (await options.supplier
                                    ?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes
                            )[dictKey];
                        }
                        break;
                    case 'item':
                        if (
                            (await options.item?.storedAttributes) &&
                            ((await options.item?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes)[
                                dictKey
                            ]
                        ) {
                            (storedAttributes as xtremMasterData.interfaces.StoredAttributes)[dictKey] = (
                                (await options.item?.storedAttributes) as xtremMasterData.interfaces.StoredAttributes
                            )[dictKey];
                        }
                        break;
                    default:
                        break;
                }
            }
        },
    );
    return !isEmpty(storedAttributes) ? storedAttributes : null;
}
