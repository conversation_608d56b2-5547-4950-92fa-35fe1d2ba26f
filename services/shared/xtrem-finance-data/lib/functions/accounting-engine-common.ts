import type { Context, NodeQueryFilter } from '@sage/xtrem-core';
import {
    asyncArray,
    BusinessRuleError,
    DataInputError,
    date,
    Logger,
    NodeCreateData,
    ValidationSeverity,
} from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStructure from '@sage/xtrem-structure';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '..';

const logger = Logger.getLogger(__filename, 'accounting-engine');

export async function doImmediatePosting(
    context: Context,
    params: {
        documentType: xtremFinanceData.enums.FinanceDocumentType;
        targetDocumentType: xtremFinanceData.enums.TargetDocumentType;
        site: xtremSystem.nodes.Site;
    },
): Promise<boolean> {
    const hardDoImmediatePosting = (
        [
            'salesInvoice',
            'salesCreditMemo',
            'purchaseInvoice',
            'purchaseCreditMemo',
            'arInvoice',
            'apInvoice',
        ] as xtremFinanceData.enums.FinanceDocumentType[]
    ).includes(params.documentType);

    const journalEntryType = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryType(
        context,
        await (
            await params.site.legalCompany
        ).legislation,
        params.documentType,
        params.targetDocumentType,
    );

    return journalEntryType?.immediatePosting ?? hardDoImmediatePosting;
}

/** -------------------------------------------------------------------------------------
 * Common auxiliary functions used while creating both a journal entry and an ap/ar invoice
 * and validating an upstream document
 ------------------------------------------------------------------------------------- */
/**
 * Gets the posting class to be used based on a posting class definition and a posting class
 * If the posting class definition is detailed, returns the current posting class. Otherwise
 * it returns the posting class with the same type but with isDetailed flag set o false.
 * @param postingClassDefinition: posting class definition to check if it is detailed or not
 * @param postingClass: current posting class
 */
export async function getPostingClass(
    context: Context,
    postingClassDefinition: xtremFinanceData.nodes.PostingClassDefinition,
    postingClass: xtremFinanceData.nodes.PostingClass | null,
): Promise<xtremFinanceData.nodes.PostingClass | null> {
    return (await postingClassDefinition.isDetailed)
        ? postingClass
        : ((await context
              .query(xtremFinanceData.nodes.PostingClass, {
                  filter: { type: await postingClassDefinition.postingClassType, isDetailed: false },
              })
              .at(0)) ?? null);
}

/**
 * Returns an item posting class depending on the entry type line
 * @param context: The context
 * @param entryTypeLine: The entry type line
 * @param stagingLineData: The accounting staging data
 * @param createJournalResult: The create journal result object to store validation messages
 */
async function getItemPostingCLass(
    context: Context,
    entryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createJournalResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremFinanceData.nodes.PostingClass | null> {
    if ((await (await entryTypeLine.accountType).isDetailed) && !(await stagingLineData.item?.postingClass)) {
        createJournalResult.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinanceData.classes.LocalizedMessages.postingClassMissingOnItem(
                context,
                (await stagingLineData.item?.id) || '',
            ),
        });
        return null;
    }
    return getPostingClass(
        context,
        await entryTypeLine.accountType,
        (await stagingLineData.item?.postingClass) || null,
    );
}

/**
 * Returns a tax posting class depending on the entry type line
 * @param context: The context
 * @param entryTypeLine: The entry type line
 * @param stagingLineData: The accounting staging data
 * @param createJournalResult: The create journal result object to store validation messages
 */
async function getTaxPostingCLass(
    context: Context,
    entryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createJournalResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremFinanceData.nodes.PostingClass | null> {
    if ((await (await entryTypeLine.accountType).isDetailed) && !(await stagingLineData.tax?.postingClass)) {
        createJournalResult.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinanceData.classes.LocalizedMessages.postingClassMissingOnTax(
                context,
                (await stagingLineData.tax?.name) || '',
            ),
        });
        return null;
    }
    return getPostingClass(context, await entryTypeLine.accountType, (await stagingLineData.tax?.postingClass) || null);
}

/**
 * Returns an resource posting class depending on the entry type line
 * @param context: The context
 * @param entryTypeLine: The entry type line
 * @param stagingLineData: The accounting staging data
 * @param createJournalResult: The create journal result object to store validation messages
 */
async function getResourcePostingCLass(
    context: Context,
    entryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createJournalResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremFinanceData.nodes.PostingClass | null> {
    if ((await (await entryTypeLine.accountType).isDetailed) && !(await stagingLineData.resource?.postingClass)) {
        createJournalResult.validationMessages.push({
            type: ValidationSeverity.error,
            message: xtremFinanceData.classes.LocalizedMessages.postingClassMissingOnResource(
                context,
                (await stagingLineData.resource?.id) || '',
            ),
        });
        return null;
    }
    return getPostingClass(
        context,
        await entryTypeLine.accountType,
        (await stagingLineData.resource?.postingClass) || null,
    );
}

/**
 * Returns a posting class depending on the entry type line
 * @param context: The context
 * @param entryTypeLine: The entry type line
 * @param stagingLineData: The accounting staging data
 * @param createJournalResult: The create journal result object to store validation messages
 */
export async function getEntryTypeLinePostingClass(
    context: Context,
    entryTypeLine: xtremFinanceData.nodes.JournalEntryTypeLine,
    stagingLineData: xtremFinanceData.interfaces.StagingLineData,
    createJournalResult: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremFinanceData.nodes.PostingClass | null> {
    switch (await (await entryTypeLine.accountType).postingClassType) {
        case 'item':
            return getItemPostingCLass(context, entryTypeLine, stagingLineData, createJournalResult);
        case 'tax':
            return getTaxPostingCLass(context, entryTypeLine, stagingLineData, createJournalResult);
        case 'resource':
            return getResourcePostingCLass(context, entryTypeLine, stagingLineData, createJournalResult);
        default:
            return null;
    }
}

/**
 * Gets the journal entry type to use for a given document type and legislation
 * @param context: context
 * @param legislation: legislation
 * @param documentType: the finance document type
 * @param targetDocumentType: the target document type
 */
export function getJournalEntryType(
    context: Context,
    legislation: xtremStructure.nodes.Legislation,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
): Promise<xtremFinanceData.nodes.JournalEntryType | null> {
    return context.tryRead(xtremFinanceData.nodes.JournalEntryType, {
        legislation,
        documentType,
        targetDocumentType,
    });
}

/**
 * Gets the account to be used as the account for the business partner on an ap\ar invoice header
 */
export async function getApArInvoiceAccount(
    context: Context,
    journalEntryType: xtremFinanceData.nodes.JournalEntryType,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentNumber: string,
    customer: xtremMasterData.nodes.Customer | null,
    supplier: xtremMasterData.nodes.Supplier | null,
    chartOfAccount: xtremStructure.nodes.ChartOfAccount,
    createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn,
): Promise<xtremFinanceData.nodes.Account | null> {
    let account: xtremFinanceData.nodes.Account | null = null;
    let postingClass: xtremFinanceData.nodes.PostingClass | null = null;
    const postingClassDefinition = await journalEntryType.headerAccountType;

    const bpPostingClass =
        targetDocumentType === 'accountsReceivableInvoice'
            ? (await customer?.postingClass) || null
            : (await supplier?.postingClass) || null;

    if (postingClassDefinition) {
        postingClass = await xtremFinanceData.functions.AccountingEngineCommon.getPostingClass(
            context,
            postingClassDefinition,
            bpPostingClass,
        );
        if (postingClass) {
            account =
                (await (
                    await postingClass.lines.find(
                        async postingClassLine =>
                            (await postingClassLine.definition)._id === postingClassDefinition?._id &&
                            (await postingClassLine.chartOfAccount) === chartOfAccount,
                    )
                )?.account) || null;
        }

        if (!account) {
            createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinanceData.classes.LocalizedMessages.bpAccountNotFound(
                    context,
                    targetDocumentType === 'accountsReceivableInvoice'
                        ? (await (await customer?.businessEntity)?.id) || ''
                        : (await (await supplier?.businessEntity)?.id) || '',
                    documentType,
                    documentNumber,
                ),
            });
        }
    }
    return account;
}

async function getPostingClassLineDetailAccountByTax(
    postingClassLine: xtremFinanceData.nodes.PostingClassLine | null,
    tax: xtremTax.nodes.Tax | null,
): Promise<xtremFinanceData.nodes.Account | null> {
    return (
        (await (
            await postingClassLine?.details?.takeOne(async detail => (await detail.tax)?._id === tax?._id)
        )?.account) ?? null
    );
}

async function getAccountByPostingClassDefinition(parameters: {
    account: xtremFinanceData.nodes.Account | null;
    postingClassLine: xtremFinanceData.nodes.PostingClassLine | null;
    tax: xtremTax.nodes.Tax | null;
}): Promise<xtremFinanceData.nodes.Account | null> {
    const postingClassDefinition = await parameters.postingClassLine?.definition;
    if (
        (await postingClassDefinition?.canHaveAdditionalCriteria) &&
        (await postingClassDefinition?.additionalCriteria) === 'tax' &&
        parameters.tax
    ) {
        return (
            (await getPostingClassLineDetailAccountByTax(parameters.postingClassLine, parameters.tax)) ??
            parameters.account
        );
    }
    return parameters.account;
}
/**
 * Gets the account to use on a new ap or ar invoice line
 * @param postingClassLine: A journal entry type line for the new finance document line generation
 */
export async function getAPARInvoiceLineAccount(
    postingClassLine: xtremFinanceData.nodes.PostingClassLine,
    tax: xtremTax.nodes.Tax | null,
): Promise<xtremFinanceData.nodes.Account | null> {
    return getAccountByPostingClassDefinition({ account: await postingClassLine.account, postingClassLine, tax });
}

/**
 * Gets the account to use on a new journal entry line
 * @param postingClassLine: A journal entry type line for the new finance document line generation
 * @param accountingStagingLineAccount: An already calculated account
 */
export async function getJournalEntryLineAccount(params: {
    postingClassLine: xtremFinanceData.nodes.PostingClassLine | null;
    accountingStagingLineAccount: xtremFinanceData.nodes.Account | null;
    tax: xtremTax.nodes.Tax | null;
}): Promise<xtremFinanceData.nodes.Account | null> {
    return getAccountByPostingClassDefinition({
        account: (await params.postingClassLine?.account) ?? params.accountingStagingLineAccount,
        postingClassLine: params.postingClassLine,
        tax: params.tax,
    });
}

export async function getBaseTax(
    context: Context,
    baseTaxSysId: number,
    financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData,
): Promise<xtremTax.nodes.BaseTax | null> {
    let baseTax: xtremTax.nodes.BaseTax | null = null;
    if (baseTaxSysId) {
        baseTax = await context.tryRead(xtremTax.nodes.BaseTax, { _id: baseTaxSysId });
        if (!baseTax) {
            financeTransactionData.validationMessages.push({
                type: ValidationSeverity.error,
                message: xtremFinanceData.classes.LocalizedMessages.taxLineNotFound(context, baseTaxSysId),
            });
        }
    }
    return baseTax;
}

function getFilter(params: {
    documentNumber: string;
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    sourceDocumentNumber?: string;
    sourceDocumentType?: xtremFinanceData.enums.SourceDocumentType;
}) {
    return params.documentType === 'workInProgress'
        ? {
              documentType: params.documentType,
              documentNumber: params.documentNumber,
              sourceDocumentType: params.sourceDocumentType,
              sourceDocumentNumber: params.sourceDocumentNumber,
          }
        : {
              documentType: params.documentType,
              documentNumber: params.documentNumber,
          };
}

export async function validateResendNotificationForFinance(
    context: Context,
    params: {
        documentNumber: string;
        documentType: xtremFinanceData.enums.FinanceDocumentType;
        sourceDocumentNumber?: string;
        sourceDocumentType?: xtremFinanceData.enums.SourceDocumentType;
    },
): Promise<boolean> {
    const accountingStagingFilter: NodeQueryFilter<xtremFinanceData.nodes.AccountingStaging> = getFilter(params);

    const financeTransactionFilter: NodeQueryFilter<xtremFinanceData.nodes.FinanceTransaction> = getFilter(params);

    const accountingStagingDocumentTaxFilter: NodeQueryFilter<xtremFinanceData.nodes.AccountingStagingDocumentTax> = {
        documentType: params.documentType,
        documentNumber: params.documentNumber,
    };

    // Check if we have records already processed for this document on the accounting staging table
    if (
        (await context.queryCount(xtremFinanceData.nodes.AccountingStaging, {
            filter: { ...accountingStagingFilter, isProcessed: true },
        })) > 0
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_accounting_staging',
                'There are journal entries already created for this document. A notification will not be resent.',
            ),
        );
    }

    const financeTransactionRecords = context.query(xtremFinanceData.nodes.FinanceTransaction, {
        filter: financeTransactionFilter,
        forUpdate: true,
    });

    // Check that there are records on the finance transaction table
    if ((await financeTransactionRecords.length) === 0) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_records',
                'There are no finance transaction records for this document. A notification will not be resent.',
            ),
        );
    }

    // Check that they are not processed
    const financeTransactionRecordsUnderProcess = await financeTransactionRecords.find(
        async financeTransactionRecord =>
            !['recording', 'pending', 'error', 'notRecorded'].includes(await financeTransactionRecord.status) ||
            (await financeTransactionRecord.targetDocumentNumber) !== '',
    );
    if (financeTransactionRecordsUnderProcess) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_finance_transaction_status',
                'Document is being processed. A notification will not be resent.',
            ),
        );
    }

    // If it is a purchase invoice, check that we don't have any record regarding landed cost allocated to PO
    if (
        await financeTransactionRecords.some(
            async financeTransaction =>
                (await financeTransaction.documentType) === 'purchaseInvoice' &&
                financeTransaction.lines.some(
                    async financeTransactionLine =>
                        (await financeTransactionLine.sourceDocumentType) === 'purchaseOrder',
                ),
        )
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_pi_allocated_to_po',
                'This purchase invoice has values allocated to a purchase order. A notification will not be resent.',
            ),
        );
    }

    // If it is a purchase receipt, check that we don't have any record regarding landed cost allocated from PO
    if (
        await financeTransactionRecords.some(
            async financeTransaction =>
                (await financeTransaction.documentType) === 'purchaseReceipt' &&
                financeTransaction.lines.some(
                    async financeTransactionLine =>
                        (await financeTransactionLine.sourceDocumentType) === 'purchaseOrder',
                ),
        )
    ) {
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-finance-data/nodes__document__resend_notification_for_finance_check_po_allocated_to_pr',
                'This purchase receipt has values allocated from a purchase order. A notification will not be resent.',
            ),
        );
    }

    // Delete records from accounting staging
    const accountingStagingRecords = context.query(xtremFinanceData.nodes.AccountingStaging, {
        filter: accountingStagingFilter,
        forUpdate: true,
    });

    await accountingStagingRecords.forEach(accountingStagingRecord => accountingStagingRecord.$.delete());

    // Delete records from accounting staging document tax
    const AccountingStagingDocumentTaxRecords = context.query(xtremFinanceData.nodes.AccountingStagingDocumentTax, {
        filter: accountingStagingDocumentTaxFilter,
        forUpdate: true,
    });

    await AccountingStagingDocumentTaxRecords.forEach(accountingStagingDocumentTaxRecord =>
        accountingStagingDocumentTaxRecord.$.delete(),
    );

    // Delete records from finance transaction
    await financeTransactionRecords.forEach(financeTransactionRecord => financeTransactionRecord.$.delete());

    return true;
}

export function getInformationFromCreateFinanceDocumentsReturn(
    context: Context,
    createdJournalsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn[],
    journalsCreatedData = false,
): string {
    let journalsCreated = 0;
    let journalsCreatedNumbers = '';
    let journalsCreatedValidations = '';

    createdJournalsReturn.forEach(createdJournalReturn => {
        journalsCreated += createdJournalReturn.documentsCreated.length || 0;
        if (journalsCreatedData) {
            journalsCreatedNumbers = createdJournalReturn.documentsCreated.reduce(
                (previous, current) => `${previous} \n ${current.documentNumber}`,
                '',
            );
        }
        journalsCreatedValidations = createdJournalReturn.validationMessages.reduce(
            (previous, current) => `${previous} \n ${current.type}:${current.message}`,
            '',
        );
    });

    return `${context.localize(
        '@sage/xtrem-finance-data/functions__accounting_engine__journals_created',
        'Journals created: {{journalsCreated}}',
        {
            journalsCreated,
        },
    )} \n ${journalsCreatedNumbers} ${journalsCreatedValidations}`.trimEnd();
}

export function getFinanceTransactionDataFromFinanceDocument(
    financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument,
): xtremFinanceData.interfaces.FinanceTransactionData {
    return {
        batchId: financeDocument.batchId,
        documentNumber: financeDocument.documentNumber,
        documentType: financeDocument.documentType,
        targetDocumentType: financeDocument.targetDocumentType,
        targetDocumentNumber: '',
        targetDocumentSysId: 0,
        validationMessages: [] as xtremFinanceData.interfaces.ValidationMessage[],
        status: 'pending',
    };
}

async function updateFinanceTransactionOnError(
    context: Context,
    financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData,
) {
    await context.runInIsolatedContext(async isolatedContext => {
        // independently of what happens, we want to create the finance transaction records in order to be able to recover from errors that may happen
        await xtremFinanceData.functions.reactToFinanceIntegrationReply(isolatedContext, financeTransactionData);
    });
}

/** -------------------------------------------------------------------------------------
 * Creating records on the accounting staging table
 ------------------------------------------------------------------------------------- */
/**
 * Creates a record on the accounting staging table based on a payload from a notification
 * @param context context
 * @param financeDocument a set of data to save
 * @param notificationId the notification id. it will be used to reply
 * @param replyTopic the topic that that it will be used to reply to
 */
export async function saveAccountingStaging(
    context: Context,
    params: {
        financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocument;
        notificationId: string;
        replyTopic: string;
        isProcessed: boolean; // true value only used on repost when an ap\ar invoice was already created but we needed to recreate all acc staging records due to tax\amount updates
    },
): Promise<xtremFinanceData.interfaces.FinanceTransactionData> {
    const { financeDocument, notificationId, replyTopic, isProcessed } = params;

    const financeTransactionData: xtremFinanceData.interfaces.FinanceTransactionData =
        getFinanceTransactionDataFromFinanceDocument(financeDocument);

    const accountStagingLines = await xtremFinanceData.classes.AccountingStaging.getAccountingStagingLines(context, {
        financeIntegrationDocument: financeDocument,
        originNotificationId: notificationId,
        replyTopic,
        isProcessed,
    });

    accountStagingLines.forEach(accountingStagingLine => {
        accountingStagingLine.financeTransactionData.validationMessages.forEach(validationMessage => {
            financeTransactionData.validationMessages.push(validationMessage);
        });
    });

    if (!financeTransactionData.validationMessages?.length) {
        await asyncArray(accountStagingLines).forEach(async accountingStaging => {
            const accountingStagingNode = await context.create(
                xtremFinanceData.nodes.AccountingStaging,
                accountingStaging as NodeCreateData<xtremFinanceData.nodes.AccountingStaging>,
            );
            const isSaved = await accountingStagingNode.$.trySave();
            if (isSaved) {
                financeTransactionData.status = 'pending';
            } else {
                financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: context.diagnoses.join('\n'),
                });
                financeTransactionData.status = 'error';
                await updateFinanceTransactionOnError(context, financeTransactionData);
                throw new DataInputError(JSON.stringify(financeTransactionData.validationMessages));
            }
        });
    } else {
        financeTransactionData.status = 'error';
        logger.error(
            () => `Save accounting staging records ${JSON.stringify(financeTransactionData.validationMessages)}`,
        );
        await updateFinanceTransactionOnError(context, financeTransactionData);
        throw new DataInputError(JSON.stringify(financeTransactionData.validationMessages));
    }

    // If the document has attached taxes, create a record in node accountingStagingDocumentTax for each baseTaxSysId
    await asyncArray(financeDocument.taxes || []).forEach(async tax => {
        const baseTax = await xtremFinanceData.functions.AccountingEngineCommon.getBaseTax(
            context,
            tax.baseTaxSysId,
            financeTransactionData,
        );
        if (baseTax) {
            const documentTax = await context.create(xtremFinanceData.nodes.AccountingStagingDocumentTax, {
                batchId: financeDocument.batchId,
                documentNumber: financeDocument.documentNumber,
                documentType: financeDocument.documentType,
                targetDocumentType: financeDocument.targetDocumentType,
                baseTax,
            });
            const isSaved = await documentTax.$.trySave();
            if (!isSaved) {
                financeTransactionData.validationMessages.push({
                    type: ValidationSeverity.error,
                    message: context.diagnoses.join('\n'),
                });
                financeTransactionData.status = 'error';
                await updateFinanceTransactionOnError(context, financeTransactionData);
                throw new DataInputError(JSON.stringify(financeTransactionData.validationMessages));
            }
        }
    });

    await xtremFinanceData.functions.reactToFinanceIntegrationReply(context, financeTransactionData);

    return financeTransactionData;
}

/** -------------------------------------------------------------------------------------
 * Updating records on the accounting staging table
 ------------------------------------------------------------------------------------- */
/**
 * Updates a record on the accounting staging table based on a payload from a notification
 * @param context context
 * @param financeDocument a set of data to update
 */
export async function updateAccountingStaging(
    context: Context,
    financeDocument: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate,
): Promise<number> {
    let numberOfUpdatedLines = 0;

    await asyncArray(financeDocument.documentLines).forEach(async financeDocumentLine => {
        const filter = {
            batchId: financeDocument.batchId,
            documentNumber: financeDocument.documentNumber,
            documentType: financeDocument.documentType,
            targetDocumentType: financeDocument.targetDocumentType,
            baseDocumentLine: financeDocumentLine.baseDocumentLineSysId,
            sourceDocumentNumber: financeDocument.sourceDocumentNumber,
            // TODO: check later if we need to add source document type
            ...(financeDocumentLine.sourceBaseDocumentLineSysId
                ? { sourceBaseDocumentLine: { _id: financeDocumentLine.sourceBaseDocumentLineSysId } }
                : {}),
        };

        const dataToUpdate: xtremFinanceData.interfaces.AcoutingStagingUpdate = {
            supplierDocumentNumber: financeDocument.supplierDocumentNumber,
            storedAttributes: financeDocumentLine.storedAttributes,
            storedDimensions: financeDocumentLine.storedDimensions,
            toBeReprocessed: true,
        };
        if (financeDocument.supplierDocumentNumber) {
            dataToUpdate.supplierDocumentNumber = financeDocument.supplierDocumentNumber;
        }
        if (financeDocument.supplierDocumentDate) {
            dataToUpdate.supplierDocumentDate = date.parse(financeDocument.supplierDocumentDate);
        }
        if (financeDocument.dueDate) {
            dataToUpdate.dueDate = date.parse(financeDocument.dueDate);
        }
        if (financeDocument.paymentTerm) {
            dataToUpdate.paymentTerm = financeDocument.paymentTerm;
        }

        await context
            .query(xtremFinanceData.nodes.AccountingStaging, {
                filter,
                forUpdate: true,
            })
            .forEach(async accountingStaging => {
                await accountingStaging.$.set(dataToUpdate);
                await accountingStaging.$.save();
                numberOfUpdatedLines += 1;
            });
    });
    return numberOfUpdatedLines;
}

export async function removeAccountingStagingForApArInvoice(
    context: Context,
    financeTransaction: xtremFinanceData.nodes.FinanceTransaction,
) {
    const targetDocumentType = await financeTransaction.targetDocumentType;

    const accStagingFilter = {
        documentNumber: await financeTransaction.documentNumber,
        documentType: await financeTransaction.documentType,
        targetDocumentType,
        batchId: await financeTransaction.batchId,
    };

    logger.info(`Deleting records from AccountingStagingDocumentTax with filter ${JSON.stringify(accStagingFilter)}`);
    await context.deleteMany(xtremFinanceData.nodes.AccountingStagingDocumentTax, {
        ...accStagingFilter,
    });

    await context
        .query(xtremFinanceData.nodes.AccountingStaging, { filter: accStagingFilter })
        .forEach(async accStagingRecord => {
            if (targetDocumentType === 'accountsReceivableInvoice') {
                logger.info(`Deleting records from AccountsReceivableInvoiceLineStaging`);
                await context.deleteMany(xtremFinanceData.nodes.AccountsReceivableInvoiceLineStaging, {
                    accountingStaging: { _id: accStagingRecord._id },
                });
            }
            if (targetDocumentType === 'accountsPayableInvoice') {
                logger.info(`Deleting records from AccountsPayableInvoiceLineStaging`);
                await context.deleteMany(xtremFinanceData.nodes.AccountsPayableInvoiceLineStaging, {
                    accountingStaging: { _id: accStagingRecord._id },
                });
            }

            logger.info(`Deleting record from AccountingStaging`);
            await context.delete(xtremFinanceData.nodes.AccountingStaging, { _id: accStagingRecord._id });
        });
}

export async function updateAccountingStagingForApArInvoice(
    context: Context,
    params: {
        financeTransaction: xtremFinanceData.nodes.FinanceTransaction;
        document:
            | xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
            | xtremFinanceData.interfaces.SalesFinanceDocumentWithTax;
        lines: xtremFinanceData.interfaces.InvoiceDocumentLine[];
        replyTopic: string;
    },
) {
    logger.info(`Updating accounting staging for an ap/ar invoice`);
    const documentType = await params.financeTransaction.documentType;
    const targetDocumentType = await params.financeTransaction.targetDocumentType;
    const { document, lines } = params;

    const aparInvoicePayload = (
        await xtremFinanceData.functions.getPurchaseSalesInvoiceCreditMemoNotificationPayload(context, {
            document,
            lines,
            documentType,
            targetDocumentType,
            isJustChecking: false,
            batchId: await params.financeTransaction.batchId,
        })
    ).notificationsPayload;

    const { filteredNotificationsPayload, batchSize } =
        xtremFinanceData.functions.getFilteredNotificationsPayloadAndBatchSize(aparInvoicePayload);
    const financeDocument = filteredNotificationsPayload.at(0);
    if (financeDocument && xtremFinanceData.functions.Common.isFinanceIntegrationDocument(financeDocument)) {
        logger.info(`Saving data into the accounting staging table`);
        financeDocument.batchSize = batchSize;
        await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(context, {
            financeDocument,
            notificationId: 'direct-save',
            replyTopic: params.replyTopic,
            isProcessed: !!(await params.financeTransaction.targetDocumentNumber),
        });
    }
}
