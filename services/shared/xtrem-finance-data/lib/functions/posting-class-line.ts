import type { Collection } from '@sage/xtrem-core';
import type * as xtremFinanceData from '..';
import type {
    JournalEntryTypePostingClassLine,
    JournalEntryTypePostingClassLineDetail,
} from '../interfaces/posting-class-line';

// Filter posting class lines based on the definition
function filterPostingClassLines(
    postingClass: xtremFinanceData.nodes.PostingClass,
    postingClassDefinition: xtremFinanceData.nodes.PostingClassDefinition,
) {
    return postingClass.lines.filter(
        async postingClassLine => (await postingClassLine.definition)._id === postingClassDefinition._id,
    );
}

// Get tax and account from details for posting class lines
function getPostingClassLineDetails(
    details: Collection<xtremFinanceData.nodes.PostingClassLineDetail>,
): Promise<JournalEntryTypePostingClassLineDetail[]> {
    return details
        .map(async detail => {
            return { tax: await detail.tax, account: await detail.account };
        })
        .toArray();
}

// Process a single posting class to get account and details
export async function processPostingClass(
    postingClass: xtremFinanceData.nodes.PostingClass,
    postingClassDefinition: xtremFinanceData.nodes.PostingClassDefinition,
): Promise<JournalEntryTypePostingClassLine> {
    const postingClassLines = filterPostingClassLines(postingClass, postingClassDefinition);

    if ((await postingClassLines.length) > 0) {
        const postingClassLine = await postingClassLines.at(0);
        const account = (await postingClassLine?.account) || undefined;
        const details =
            postingClassLine?.details && (await postingClassLine.details.length)
                ? await getPostingClassLineDetails(postingClassLine.details)
                : undefined;
        const id = postingClassLine?._id;

        return { _id: id, postingClass, account, details };
    }

    return { postingClass };
}
