import type { ValidationContext, date } from '@sage/xtrem-core';

export async function voidDateControl(parameters: {
    context: ValidationContext;
    voidDate: date | null;
    paymentDate: date;
    isVoided: boolean;
}) {
    const { context, voidDate, paymentDate, isVoided } = parameters;
    await context.error
        .withMessage(
            '@sage/xtrem-finance-data/nodes__base-payment-document__void_date_mandatory',
            'The void date is mandatory.',
        )
        .if(!voidDate && isVoided)
        .is.true();

    await context.error
        .withMessage(
            '@sage/xtrem-finance-data/nodes__base-payment-document__void_date_should_be_after_payment_date',
            'The void date needs to be after the payment date.',
        )
        .if(voidDate && voidDate < paymentDate && isVoided)
        .is.true();
}
