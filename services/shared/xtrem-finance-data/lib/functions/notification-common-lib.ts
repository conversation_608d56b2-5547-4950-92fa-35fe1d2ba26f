import type { Context, decimal } from '@sage/xtrem-core';
import { SystemError, Uuid, asyncArray } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremFinanceData from '..';
import { loggers } from './loggers';

export async function getStockMovementsAmounts(
    line: xtremFinanceData.interfaces.FinanceOriginDocumentStockLine,
): Promise<{
    movementAmount: decimal;
    orderAmount: decimal;
}> {
    const stockMovements: { movementAmount: number; orderAmount: number }[] = line.stockMovementArray
        ? line.stockMovementArray
        : await line.stockMovements
              .filter(async stockMovement => !(await (await stockMovement.reasonCode)?.landedCostAdjustment))
              .map(async stockMovement => {
                  return {
                      movementAmount: await stockMovement.movementAmount,
                      orderAmount: await stockMovement.orderAmount,
                  };
              })
              .toArray();

    return stockMovements.reduce(
        (accumulator, stockMovement) => {
            accumulator.movementAmount += stockMovement ? stockMovement.movementAmount : 0;
            accumulator.orderAmount += stockMovement ? stockMovement.orderAmount : 0;

            return accumulator;
        },
        { movementAmount: 0, orderAmount: 0 },
    );
}

export async function getStockMovementsAmountsStockTransferShipment(
    line: xtremFinanceData.interfaces.FinanceOriginDocumentStockLine,
    filterCallBack?: (stockMovement: xtremStockData.nodes.StockJournal) => boolean | Promise<boolean>,
): Promise<{
    movementAmount: decimal;
    orderAmount: decimal;
}> {
    const stockMovements: { movementAmount: number; orderAmount: number }[] = line.stockMovementArray
        ? line.stockMovementArray
        : await line.stockMovements
              .filter(async stockMovement => {
                  const isNotLandedCostAdjustment = !(await (await stockMovement.reasonCode)?.landedCostAdjustment);
                  if (filterCallBack) {
                      return (await filterCallBack(stockMovement)) && isNotLandedCostAdjustment;
                  }
                  return isNotLandedCostAdjustment;
              })

              .map(async stockMovement => {
                  return {
                      movementAmount: await stockMovement.movementAmount,
                      orderAmount: await stockMovement.orderAmount,
                  };
              })
              .toArray();

    return stockMovements.reduce(
        (accumulator, stockMovement) => {
            accumulator.movementAmount += stockMovement ? stockMovement.movementAmount : 0;
            accumulator.orderAmount += stockMovement ? stockMovement.orderAmount : 0;

            return accumulator;
        },
        { movementAmount: 0, orderAmount: 0 },
    );
}

/**
 * In case of StockIssueLine and PurchaseReturnLine: return the negated movementAmount to turn it positive
 * @param documentLineType base document line
 * @param movementAmount movementAmount from stock journal
 * @return correct stock line amount to use
 */
export function getStockLineAmount(documentLineType: string, movementAmount: number): number {
    switch (documentLineType) {
        case 'StockIssueLine':
        case 'PurchaseReturnLine':
            return -movementAmount;
        default:
            return movementAmount;
    }
}

/**
 * Reads and returns the ItemSite for the given site and item
 * @param context
 * @param site site from stock journal
 * @param item item from stock journal
 * @return ItemSite node found or null
 */
export function getItemSite(
    context: Context,
    site: xtremSystem.nodes.Site,
    item: xtremMasterData.nodes.Item,
): Promise<xtremMasterData.nodes.ItemSite | null> {
    return context.tryRead(xtremMasterData.nodes.ItemSite, {
        item,
        site,
    });
}

/**
 * Gets common properties from a document to create an Accounting Staging Payload
 * @param document document from which we are creatng the notifications
 * @param number used only for material/production and operation tracking: the work order number
 * @param sysId used only for material/production and operation tracking: the work order _id
 * @return AccountingStagingCommonPayload the common properties as a AccountingStagingCommonPayload
 */
export async function getAccountingStagingCommonPayload(
    params: xtremFinanceData.interfaces.GetAccountingStagingCommonPayloadParameters,
): Promise<xtremFinanceData.interfaces.AccountingStagingCommonPayload> {
    let documentNumber = params.number ?? '';

    if (!params.isJustChecking && !params.number) {
        if (params.document?.$?.isValueDeferred('number')) {
            loggers.notif.error('Document number is deferred');
            throw new SystemError('Document number is deferred');
        }
        documentNumber = await params.document.number;
    }

    return {
        documentSysId: params.sysId && params.sysId !== 0 ? params.sysId : params.document._id,
        batchId: params.batchId ?? Uuid.generate().toString(),
        financialSiteSysId: (await params.document.financialSite)._id,
        documentNumber,
        documentDate: (await params.document.documentDate).toString(),
        currencySysId: (await params.document.transactionCurrency)._id,
    };
}

/**
 * Filters the amounts to remove amounts with a value of 0
 */
function getFilteredAmounts(
    document: xtremFinanceData.interfaces.FinanceIntegrationDocument,
    documentLine: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine,
): xtremFinanceData.interfaces.AccountingStagingAmount[] {
    return documentLine.amounts.filter(accountingStagingAmount => {
        // if the amount is 0, we remove the line from the notification
        if (accountingStagingAmount.amount === 0) {
            loggers.notif.verbose(
                () =>
                    `${xtremFinanceData.functions.getAccountingStagingAmountLogMessage(document, documentLine, accountingStagingAmount)} Amount is 0, accountingStagingAmount is removed from the notification`,
            );
            return false;
        }
        return true;
    });
}

/**
 * Filters the document lines to remove lines with no amounts
 */
function getFilteredDocumentLines(
    document: xtremFinanceData.interfaces.FinanceIntegrationDocument,
): xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[] {
    return document.documentLines
        .map(documentLine => ({
            ...documentLine,
            amounts: getFilteredAmounts(document, documentLine),
        }))
        .filter(documentLine => {
            if (documentLine.amounts.length === 0) {
                loggers.notif.verbose(
                    () =>
                        `${xtremFinanceData.functions.getFinanceIntegrationDocumentLineLogMessage(document, documentLine)} no amount, line is removed from the notification`,
                );
                return false;
            }
            return true;
        });
}

/**
 * Removes lines with no amounts from the document and removes documents with no lines
 */
export function getFilteredNotificationsPayload(
    notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[],
): xtremFinanceData.interfaces.FinanceIntegrationDocument[] {
    return notificationsPayload
        .map(document => ({
            ...document,
            documentLines:
                document.sourceDocumentType === 'workOrderClose'
                    ? getFilteredDocumentLines(document)
                    : document.documentLines,
        }))
        .filter(document => {
            if (document.documentLines.length === 0) {
                loggers.notif.verbose(
                    () =>
                        `${xtremFinanceData.functions.getFinanceIntegrationDocumentLogMessage(document)} no line, document is removed from the notification`,
                );
                return false;
            }
            return true;
        });
}

export function getFilteredNotificationsPayloadAndBatchSize(
    notificationsPayload:
        | xtremFinanceData.interfaces.FinanceIntegrationDocument[]
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate[],
): {
    filteredNotificationsPayload:
        | xtremFinanceData.interfaces.FinanceIntegrationDocument[]
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate[];
    batchSize: number;
} {
    // We filter all lines with amount = 0 and all documents with no lines
    let filteredNotificationsPayload = notificationsPayload;
    let batchSize = 0;
    if (xtremFinanceData.functions.Common.isArrayOfFinanceIntegrationDocument(filteredNotificationsPayload)) {
        filteredNotificationsPayload = getFilteredNotificationsPayload(filteredNotificationsPayload);
    }

    filteredNotificationsPayload.forEach(document => {
        batchSize += document.documentLines.length;
    });

    return { filteredNotificationsPayload, batchSize };
}

/**
 * Sends the notifications related with the isPrinted status
 * @param context Context
 * @param documentType The document type
 * @param documentSysId The document number
 */
export async function notifyDocumentIsPrinted(
    context: Context,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentSysId: number,
): Promise<void> {
    await context.notify('printingStatus/accountingInterface', {
        documentType,
        documentSysId,
    } as xtremFinanceData.interfaces.PrintedDocument);
}

/**
 * Creates the records on the finance transaction table and on the accounting staging tables. We no longer send notifications
 * @param context Context
 * @param notificationsPayload an array of AccountingStagingDocument containing all the payloads. Each element of the array will generate a notification.
 * @param replyTopic the reply topic for the notifications
 */
export async function sendNotificationsToAccountingEngine(options: {
    context: Context;
    notificationsPayload:
        | xtremFinanceData.interfaces.FinanceIntegrationDocument[]
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate[];
    replyTopic: string;
    isUpdate: boolean;
    paymentTracking?: xtremFinanceData.nodes.PaymentTracking;
    batchTrackingId?: string;
}): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    let notificationsSent = 0;
    const topic = options.isUpdate ? 'accountingInterfaceUpdate' : 'accountingInterface';

    // We filter all lines with amount = 0 and all documents with no lines
    const { filteredNotificationsPayload, batchSize } = getFilteredNotificationsPayloadAndBatchSize(
        options.notificationsPayload,
    );

    if (filteredNotificationsPayload.length === 0)
        return {
            batchId: '',
            batchSize,
            notificationsSent,
        };

    // we just create the notifications if we have at least one line
    if (batchSize) {
        let doImmediatePosting = false;

        // First we create the finance transaction records for all the financeDocuments in the array
        // This will allow us to recover from any error even if the staging records for a given finance document are not saved
        if (!options.isUpdate) {
            await asyncArray(filteredNotificationsPayload).forEach(async document => {
                const financialSite = await options.context.read(xtremSystem.nodes.Site, {
                    _id: document.financialSiteSysId,
                });
                await options.context.runInIsolatedContext(async isolatedContext => {
                    // independently of what happens, we want to create the finance transaction records in order to be able to recover from errors that may happen
                    const financeIntegrationStatusRecord: xtremFinanceData.nodes.FinanceTransaction =
                        await isolatedContext.create(xtremFinanceData.nodes.FinanceTransaction, {
                            batchId: document.batchId,
                            documentSysId: document.documentSysId,
                            documentNumber: document.documentNumber,
                            documentType: document.documentType,
                            financialSite,
                            targetDocumentType: document.targetDocumentType,
                            targetDocumentNumber: '',
                            sourceDocumentSysId: document.sourceDocumentSysId ?? undefined,
                            sourceDocumentNumber: document.sourceDocumentNumber ?? undefined,
                            sourceDocumentType: document.sourceDocumentType ?? undefined,
                            status: 'recording',
                            message: '',
                            lines: document.sourceLines,
                            paymentTracking: options.paymentTracking ?? null,
                        });
                    await financeIntegrationStatusRecord.$.save();
                });

                loggers.financeIntegration.info(
                    `Finance Integration record created: ${document.batchId} ${document.documentType} ${document.documentNumber} ${document.targetDocumentType}`,
                );
            });
        }

        // Then we save the data on the accounting staging tables
        await asyncArray(filteredNotificationsPayload).forEach(async document => {
            document.batchSize = batchSize;
            // Update case
            if (options.isUpdate) {
                // update records on the staging table
                await xtremFinanceData.functions.AccountingEngineCommon.updateAccountingStaging(
                    options.context,
                    document,
                );
                // send a notification to process the imediatePosting documents
                await options.context.notify(
                    topic,
                    { ...document, isJustForPost: true },
                    { replyTopic: options.replyTopic },
                );
            }
            // Creation case
            else {
                if (xtremFinanceData.functions.Common.isFinanceIntegrationDocument(document)) {
                    await xtremFinanceData.functions.AccountingEngineCommon.saveAccountingStaging(options.context, {
                        financeDocument: document,
                        notificationId: 'direct-save',
                        replyTopic: options.replyTopic,
                        isProcessed: false,
                    });
                    loggers.financeIntegration.info(
                        `Accounting staging records created: ${document.batchId} ${document.documentType} ${document.documentNumber} ${document.targetDocumentType}`,
                    );
                }
                // We check if the document needs to be immediately posted
                doImmediatePosting =
                    doImmediatePosting ||
                    (await xtremFinanceData.functions.AccountingEngineCommon.doImmediatePosting(options.context, {
                        documentType: document.documentType,
                        targetDocumentType: document.targetDocumentType,
                        site: await options.context.read(xtremSystem.nodes.Site, {
                            _id: document.financialSiteSysId,
                        }),
                    }));
            }
            notificationsSent += 1;
        });

        // We just do immediate posting on accounting staging creation (when needed), not on accounting staging update
        // For the accounting staging update we do it after updateAccountingStaging
        if (doImmediatePosting) {
            // At the moment, all documents on the array have the same batchId, this can be changed in the future
            const batchId = filteredNotificationsPayload?.at(0)?.batchId ?? '';
            loggers.financeIntegration.info(`Immediate posting: ${batchId}`);

            await options.context.notify('AccountingInterfaceListener/createJournalsFromAccountingStagingJob/start', {
                journalsCreatedData: false,
                filter: JSON.stringify({
                    batchId,
                    isProcessed: false,
                }),
                batchTrackingId: options.batchTrackingId,
            });
        }
    }
    return {
        batchId: filteredNotificationsPayload[0].batchId,
        batchSize: filteredNotificationsPayload[0].batchSize,
        notificationsSent,
    };
}

// build a unique array of source information (SysId + Number + type), 1 element for each
// source document linked to the document to add to the finance transaction record later
export async function getSourceLines(params: {
    lines: xtremFinanceData.interfaces.InvoiceDocumentLine[];
    isConstantSourceDocumentType: boolean;
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType;
    type?: xtremFinanceData.enums.SourceDocumentType;
}): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[]> {
    if (params.targetDocumentType === 'accountsPayableInvoice') {
        return [];
    }
    const sourceLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[] = (
        await asyncArray(params.lines)
            .filter(async line => (await line.sourceDocumentNumber) !== '' || !!(await line.landedCost))
            .map(async line => {
                const lineHasPurchaseReceipt =
                    params.isConstantSourceDocumentType &&
                    params.type &&
                    params.type === 'purchaseReceipt' &&
                    (await line.receiptNumber);
                const source: xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[] =
                    !(await line.landedCost)
                        ? // not a landed cost invoice, regular data
                          [
                              {
                                  sourceDocumentNumber: lineHasPurchaseReceipt
                                      ? await line.receiptNumber
                                      : await line.sourceDocumentNumber,
                                  sourceDocumentSysId: lineHasPurchaseReceipt
                                      ? await line.receiptSysId
                                      : await line.sourceDocumentSysId,
                                  sourceDocumentType:
                                      lineHasPurchaseReceipt ||
                                      (params.isConstantSourceDocumentType &&
                                          params.type &&
                                          params.type !== 'purchaseReceipt')
                                          ? params.type
                                          : (await line.sourceDocumentType) || params.type,
                                  isSourceForDimension: false,
                              },
                          ]
                        : // landed cost invoice, get receipts allocated
                          (await (
                              await line.landedCost
                          )?.allocations
                              .map(async landedCostAllocation => {
                                  return {
                                      sourceDocumentNumber: await (
                                          await landedCostAllocation.allocatedDocumentLine
                                      ).documentNumber,
                                      sourceDocumentSysId: await (
                                          await landedCostAllocation.allocatedDocumentLine
                                      ).documentId,
                                      sourceDocumentType: params.type,
                                      isSourceForDimension: true,
                                  } as xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties;
                              })
                              .toArray()) || [];

                return source;
            })
            .toArray()
    ).flat(1);
    return _.uniqWith(sourceLines, _.isEqual);
}

export async function reactToFinanceIntegrationReply(
    context: Context,
    replyPayload: xtremFinanceData.interfaces.FinanceTransactionData,
): Promise<boolean> {
    const financeIntegration: xtremFinanceData.nodes.FinanceTransaction = await context.read(
        xtremFinanceData.nodes.FinanceTransaction,
        {
            batchId: replyPayload.batchId,
            documentNumber: replyPayload.documentNumber,
            documentType: replyPayload.documentType,
            targetDocumentType: replyPayload.targetDocumentType,
        },
        { forUpdate: true },
    );

    if (
        !xtremFinanceData.functions.canUpdateFinanceTransactionStatus(
            replyPayload.status,
            await financeIntegration.status,
        )
    ) {
        context.logger.info(
            `The finance transaction status cannot be updated from ${await financeIntegration.status} to ${
                replyPayload.status
            }. Payload will be ignored: ${JSON.stringify(replyPayload)}`,
        );
        return false;
    }

    const validationMessages =
        typeof replyPayload.validationMessages === 'string'
            ? (JSON.parse(replyPayload.validationMessages) as xtremFinanceData.interfaces.ValidationMessage[])
            : replyPayload.validationMessages;

    if ((await financeIntegration.status) !== 'error') {
        await financeIntegration.$.set({
            status: replyPayload.status,
            targetDocumentNumber: replyPayload.targetDocumentNumber,
            targetDocumentSysId: replyPayload.targetDocumentSysId,
            message: validationMessages.reduce((prev, message) => `${prev}${message.message}\n`, ''),
        });
        if (replyPayload.financeExternalIntegration) {
            await financeIntegration.$.set({
                financeIntegrationApp: replyPayload.financeExternalIntegration.app,
                financeIntegrationAppRecordId: replyPayload.financeExternalIntegration.recordId || '',
                financeIntegrationAppUrl: replyPayload.financeExternalIntegration.url || '',
            });

            // if it's a journal entry created from an ap-arInvoice, update the integration status on the original sales\purchase invoice
            if (
                (await financeIntegration.targetDocumentType) === 'journalEntry' &&
                ['arInvoice', 'apInvoice'].includes(await financeIntegration.documentType)
            ) {
                const originalFinanceDocumentTargetDocumentType: xtremFinanceData.enums.TargetDocumentType =
                    (await financeIntegration.documentType) === 'arInvoice'
                        ? 'accountsReceivableInvoice'
                        : 'accountsPayableInvoice';

                const originalFinanceDocument = await context
                    .query(xtremFinanceData.nodes.FinanceTransaction, {
                        filter: {
                            targetDocumentNumber: replyPayload.documentNumber,
                            targetDocumentType: originalFinanceDocumentTargetDocumentType,
                            status: 'recorded',
                        },
                        forUpdate: true,
                    })
                    .toArray();
                if (originalFinanceDocument?.length) {
                    await originalFinanceDocument[0].$.set({
                        financeIntegrationApp: replyPayload.financeExternalIntegration.app,
                    });
                    await originalFinanceDocument[0].$.set({
                        financeIntegrationAppRecordId: replyPayload.financeExternalIntegration.recordId || '',
                    });
                    await originalFinanceDocument[0].$.set({
                        financeIntegrationAppUrl: replyPayload.financeExternalIntegration.url || '',
                    });
                    await originalFinanceDocument[0].$.save();
                }
            }
        }
    }

    await financeIntegration.$.save();
    return true;
}
