import type { Context } from '@sage/xtrem-core';
import { Decimal, asyncArray } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

async function getOperationTrackingNotificationDocumentLine(
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    line: xtremFinanceData.interfaces.OperationTrackingFinanceDocumentLine,
    wipCost: xtremFinanceData.interfaces.WipCost,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine> {
    let movementType: xtremFinanceData.enums.MovementType;
    if ((await wipCost.type) === 'setupTimeTracking') {
        switch (await line.workInProgressActualResourceType) {
            case 'labor':
                movementType = 'laborSetupTimeTracking';
                break;
            case 'machine':
                movementType = 'machineSetupTimeTracking';
                break;
            default:
                movementType = 'toolSetupTimeTracking';
        }
    } else {
        switch (await line.workInProgressActualResourceType) {
            case 'labor':
                movementType = 'laborRunTimeTracking';
                break;
            case 'machine':
                movementType = 'machineRunTimeTracking';
                break;
            default:
                movementType = 'toolRunTimeTracking';
        }
    }
    return {
        baseDocumentLineSysId: line._id,
        movementType,
        sourceDocumentNumber: '',
        currencySysId: (await document.transactionCurrency)._id,
        companyFxRate: 1,
        companyFxRateDivisor: 1,
        fxRateDate: (await document.documentDate).toString(),
        itemSysId: (await line.item)?._id,
        resourceSysId: (await line.actualResource)?._id,
        amounts: [
            {
                amountType: 'amount',
                amount: Decimal.roundAt(await wipCost.amount, await (await document.transactionCurrency).decimalDigits),
                documentLineType: 'documentLine',
            },
        ],
        storedDimensions: (await line.storedDimensions) || {},
        storedAttributes: {
            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
            ...((await line.computedAttributes) as {}),
        },
    };
}

async function getOperationTrackingNotificationDocumentLines(
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.OperationTrackingFinanceDocumentLine[],
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    return (
        await asyncArray(lines)
            .filter(async line => line.workInProgressCosts && (await line.workInProgressCosts.length) > 0)
            .map(line => {
                return line.workInProgressCosts
                    .map(wipCost => {
                        return getOperationTrackingNotificationDocumentLine(document, line, wipCost);
                    })
                    .toArray();
            })
            .toArray()
    ).flat();
}

/**
 * Creates all the notifications for the finance integration of a wip production tracking
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements WorkInProgressFinanceDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function productionTrackingNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.WorkInProgressFinanceDocumentLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    let number = '';
    let sysId = 0;
    if (lines.length > 0) {
        number = await lines[0].workInProgressWorkOrderNumber;
        sysId = await lines[0].workInProgressWorkOrderSysId;
    }
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        number !== ''
            ? await xtremFinanceData.functions.getAccountingStagingCommonPayload({
                  document,
                  isJustChecking,
                  number,
                  sysId,
              })
            : await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'workInProgress',
            targetDocumentType: 'journalEntry',
            sourceDocumentNumber: await document.number,
            sourceDocumentSysId: document._id,
            sourceDocumentType: 'productionTracking',
            documentLines: (await asyncArray(lines)
                .map(async line => {
                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'productionTracking',
                        sourceDocumentNumber: '',
                        currencySysId: (await document.transactionCurrency)._id,
                        companyFxRate: 1,
                        companyFxRateDivisor: 1,
                        fxRateDate: (await document.documentDate).toString(),
                        itemSysId: (await line.item)._id,
                        amounts: [
                            {
                                amountType: 'amount',
                                amount: Decimal.roundAt(
                                    await line.workInProgressCostAmount,
                                    await (
                                        await document.transactionCurrency
                                    ).decimalDigits,
                                ),
                                documentLineType: 'documentLine',
                            },
                        ],
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    };
                })
                .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[],
        },
    ];

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: 'ProductionTracking/accountingInterface',
        isUpdate: false,
    });
}

/**
 * Creates all the notifications for the finance integration of a wip material tracking
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements MaterialTrackingFinanceDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function materialTrackingNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.MaterialTrackingFinanceDocumentLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    let number = '';
    let sysId = 0;
    if (lines.length > 0) {
        number = await lines[0].workInProgressWorkOrderNumber;
        sysId = await lines[0].workInProgressWorkOrderSysId;
    }
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        number !== ''
            ? await xtremFinanceData.functions.getAccountingStagingCommonPayload({
                  document,
                  isJustChecking,
                  number,
                  sysId,
              })
            : await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'workInProgress',
            targetDocumentType: 'journalEntry',
            sourceDocumentNumber: await document.number,
            sourceDocumentSysId: document._id,
            sourceDocumentType: 'materialTracking',
            documentLines: (await asyncArray(lines)
                .map(async line => {
                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'materialTracking',
                        sourceDocumentNumber: '',
                        currencySysId: (await document.transactionCurrency)._id,
                        companyFxRate: 1,
                        companyFxRateDivisor: 1,
                        fxRateDate: (await document.documentDate).toString(),
                        itemSysId: (await line.item)?._id || undefined,
                        amounts: [
                            {
                                amountType: 'amount',
                                amount: Decimal.roundAt(
                                    await line.workInProgressCostAmount,
                                    await (
                                        await document.transactionCurrency
                                    ).decimalDigits,
                                ),
                                documentLineType: 'documentLine',
                            },
                        ],
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    };
                })
                .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[],
        },
    ];

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: 'MaterialTracking/accountingInterface',
        isUpdate: false,
    });
}

/**
 * Creates all the notifications for the finance integration of a wip operation (time) tracking
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements OperationTrackingFinanceDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function operationTrackingNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.OperationTrackingFinanceDocumentLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    let number = '';
    let sysId = 0;
    if (lines.length > 0) {
        number = await lines[0].workInProgressWorkOrderNumber;
        sysId = await lines[0].workInProgressWorkOrderSysId;
    }
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        number !== ''
            ? await xtremFinanceData.functions.getAccountingStagingCommonPayload({
                  document,
                  isJustChecking,
                  number,
                  sysId,
              })
            : await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'workInProgress',
            targetDocumentType: 'journalEntry',
            sourceDocumentNumber: await document.number,
            sourceDocumentSysId: document._id,
            sourceDocumentType: 'operationTracking',
            documentLines: await getOperationTrackingNotificationDocumentLines(document, lines),
        },
    ];

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: 'OperationTracking/accountingInterface',
        isUpdate: false,
    });
}

function getMovementTypeFromWIPCostType(workInProgressCostType: string): xtremFinanceData.enums.MovementType {
    return workInProgressCostType as xtremFinanceData.enums.MovementType;
}

/**
 * Creates all the notifications for the finance integration of a wip work order closing
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements WorkOrderClosingFinanceDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function workOrderClosingNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.WorkOrderClosingFinanceDocumentLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    const accountingStagingCommonPayload = await xtremFinanceData.functions.getAccountingStagingCommonPayload({
        document,
        isJustChecking,
    });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'workInProgress',
            targetDocumentType: 'journalEntry',
            sourceDocumentNumber: await document.number,
            sourceDocumentSysId: document._id,
            sourceDocumentType: 'workOrderClose',
            documentLines: (await asyncArray(lines)
                .map(async line => {
                    return {
                        baseDocumentLineSysId: (await line.originatingLine)._id,
                        movementType: getMovementTypeFromWIPCostType(await line.workInProgressCostType),
                        sourceDocumentNumber: '',
                        currencySysId: (await document.transactionCurrency)._id,
                        companyFxRate: 1,
                        companyFxRateDivisor: 1,
                        fxRateDate: (await document.documentDate).toString(),
                        itemSysId: (await line.item)._id,
                        amounts: [
                            {
                                amountType: 'amount',
                                amount: Decimal.roundAt(
                                    await line.amount,
                                    await (
                                        await document.transactionCurrency
                                    ).decimalDigits,
                                ),
                                documentLineType: 'documentLine',
                            },
                        ],
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    };
                })
                .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[],
        },
    ];

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: 'WorkOrderClosing/accountingInterface',
        isUpdate: false,
    });
}
