import type {
    AnyNode,
    Context,
    Node,
    NodeCreateData,
    NodeQueryFilter,
    OperationGrant,
    Property,
    StaticThis,
    ValidationContext,
} from '@sage/xtrem-core';
import { ValidationSeverity, asyncArray, registerSqlFunction } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import type { Dict } from '@sage/xtrem-shared';
import { LogicError } from '@sage/xtrem-shared';
import * as xtremSystem from '@sage/xtrem-system';
import type { StoredAttributesDataType, StoredDimensionsDataType } from '../data-types/_index';
import * as xtremFinanceData from '../index';

export async function checkValueInactive(
    cx: Context,
    clas: StaticThis<xtremFinanceData.nodes.Attribute | xtremFinanceData.nodes.Dimension>,
    id: string,
    typeFilter: any,
): Promise<boolean> {
    const result = await cx.tryRead(clas, { id, ...typeFilter });
    return !(await result?.isActive);
}

export async function checkReferenceValueInactive(
    cx: Context,
    clas: StaticThis<xtremMasterData.nodes.Customer | xtremMasterData.nodes.Supplier | xtremSystem.nodes.Site>,
    _id: string,
    typeFilter: any,
): Promise<boolean> {
    const result = await cx.tryRead(clas, { _id, ...typeFilter });
    return !(await result?.isActive);
}

export async function checkAttributeValue(cx: Context, attributeTypeID: string, attributeID: string): Promise<boolean> {
    const attributeType = await cx.tryRead(xtremFinanceData.nodes.AttributeType, { id: attributeTypeID });
    if (attributeType && (await attributeType.isActive)) {
        const clas = cx.introspection.getNodeFromTableName(await attributeType.nodeLink);
        if (clas) {
            let filter: NodeQueryFilter<
                | xtremSystem.nodes.Site
                | xtremMasterData.nodes.Customer
                | xtremMasterData.nodes.Supplier
                | xtremMasterData.nodes.Item
            > = {};
            const attributeQueryFilter = await attributeType.queryFilter;
            if (attributeTypeID === 'customer' || attributeTypeID === 'supplier') {
                filter = { businessEntity: { id: attributeID }, isActive: true, ...attributeQueryFilter };
            } else {
                filter = { id: attributeID, isActive: true, ...attributeQueryFilter };
            }
            const result = await cx.query(clas, { filter }).toArray();
            return result.length === 1;
        }
    }
    return false;
}

export async function checkReferenceItemValueInactive(
    cx: Context,
    clas: StaticThis<xtremMasterData.nodes.Item>,
    _id: string,
    typeFilter: any,
): Promise<boolean> {
    const result = await cx.tryRead(clas, { _id, ...typeFilter });
    return (await result?.status) !== 'active';
}

export async function checkDimensionTypeInactive(
    cx: Context,
    clas: StaticThis<xtremFinanceData.nodes.DimensionType>,
    docProperty: string,
): Promise<boolean> {
    const result = await cx.tryRead(clas, { docProperty });
    return !(await result?.isActive);
}

export async function checkAttributeTypeInactive(
    cx: Context,
    clas: StaticThis<xtremFinanceData.nodes.AttributeType>,
    id: string,
): Promise<boolean> {
    const result = await cx.tryRead(clas, { id });
    return !(await result?.isActive);
}

export async function checkAttributeTypeActive(
    cx: Context,
    clas: StaticThis<xtremFinanceData.nodes.AttributeType>,
    id: string,
    valueIfActive: string,
): Promise<string | undefined> {
    if (await checkAttributeTypeInactive(cx, clas, id)) {
        return undefined;
    }
    return valueIfActive;
}

export async function checkDimensionTypeActive(
    cx: Context,
    clas: StaticThis<xtremFinanceData.nodes.DimensionType>,
    docProperty: string,
    valueIfActive: string,
): Promise<string | undefined> {
    if (await checkDimensionTypeInactive(cx, clas, docProperty)) {
        return undefined;
    }
    return valueIfActive;
}

/**
 * Return the financial site of the site
 * @param site if the site isFInance return directly the site
 * @returns site or site.financialSite
 */
export async function getFinancialSite(site: xtremSystem.nodes.Site): Promise<xtremSystem.nodes.Site> {
    return (await site.isFinance) ? site : ((await site.financialSite) ?? site);
}

registerSqlFunction('xtremFinanceData.functions.getFinancialSite', getFinancialSite);

async function computedAttributeFinancialSite(
    context: Context,
    financialSiteData: xtremSystem.nodes.Site | null | undefined,
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    if (
        financialSiteData &&
        !(await xtremFinanceData.functions.checkAttributeTypeInactive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'financialSite',
        ))
    ) {
        return (await financialSiteData.isFinance)
            ? { financialSite: await financialSiteData.id }
            : { financialSite: (await (await financialSiteData.financialSite)?.id) || undefined };
    }
    return {};
}

async function computedAttributeStockSite(
    context: Context,
    stockSiteData: xtremSystem.nodes.Site | null | undefined,
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    const stockSite =
        stockSiteData &&
        !(await xtremFinanceData.functions.checkAttributeTypeInactive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'stockSite',
        )) &&
        (await stockSiteData.isInventory)
            ? await stockSiteData.id
            : undefined;

    return stockSite ? { stockSite } : {};
}

async function computedAttributeBusinessSite(
    context: Context,
    financialSite: string | undefined,
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    const businessSite =
        financialSite &&
        !(await xtremFinanceData.functions.checkAttributeTypeInactive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'businessSite',
        ))
            ? financialSite
            : undefined;

    return businessSite ? { businessSite } : {};
}

async function computedAttributeItem(
    context: Context,
    itemData: xtremMasterData.nodes.Item | null | undefined,
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    const item = itemData
        ? await checkAttributeTypeActive(context, xtremFinanceData.nodes.AttributeType, 'item', await itemData.id)
        : undefined;
    return item ? { item } : {};
}

/**
 * Returns the computed attributes
 * @param context current context
 * @param data stockSite, financialSite or item
 * @returns Promise
 */
export async function computeGenericAttributes(
    context: Context,
    data: {
        stockSite?: xtremSystem.nodes.Site | null;
        financialSite?: xtremSystem.nodes.Site | null;
        item?: xtremMasterData.nodes.Item | null;
        returnBusinessSite?: boolean;
    },
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    data.financialSite =
        !data.financialSite && data.stockSite ? await getFinancialSite(data.stockSite) : data.financialSite;

    // Fill financialSite attribute to be returned
    const computedAttributes = await computedAttributeFinancialSite(context, data.financialSite);

    if (data.returnBusinessSite === false) {
        return {
            ...computedAttributes,
            // Fill stockSite attribute to be returned
            ...(await computedAttributeStockSite(context, data.stockSite)),
            // Fill item attribute to be returned
            ...(await computedAttributeItem(context, data.item)),
        };
    }
    return {
        ...computedAttributes,
        // Fill stockSite attribute to be returned
        ...(await computedAttributeStockSite(context, data.stockSite)),
        // Fill item attribute to be returned
        ...(await computedAttributeItem(context, data.item)),
        // Fill businessSite attribute to be returned
        ...(await computedAttributeBusinessSite(context, computedAttributes.financialSite)),
    };
}

export async function getTypeFilter(
    node: xtremFinanceData.nodes.AttributeType | xtremFinanceData.nodes.DimensionType,
    properties: Property[],
): Promise<NodeQueryFilter<xtremFinanceData.nodes.AttributeType | xtremFinanceData.nodes.DimensionType>> {
    return {
        _or: await asyncArray(properties)
            .map(async property => {
                if (node instanceof xtremFinanceData.nodes.DimensionType) {
                    return {
                        [property.name]: { [await node.docProperty]: { _ne: null } },
                    };
                    // eslint-disable-next-line no-else-return
                } else return { [property.name]: { [await node.id]: { _ne: null } } };
            })
            .toArray(),
    };
}

export async function getAttributeValueFilter(
    node: xtremFinanceData.nodes.Attribute,
    properties: Property[],
): Promise<NodeQueryFilter<xtremFinanceData.nodes.Attribute>> {
    return {
        _or: await asyncArray(properties)
            .map(async property => {
                return {
                    [property.name]: { [await (await node.attributeType).id]: { _eq: await node.id } },
                };
            })
            .toArray(),
    };
}

export async function getDimensionValueFilter(
    node: xtremFinanceData.nodes.Dimension,
    properties: Property[],
): Promise<NodeQueryFilter<xtremFinanceData.nodes.Dimension>> {
    return {
        _or: await asyncArray(properties)
            .map(async property => {
                return {
                    [property.name]: { [await (await node.dimensionType).docProperty]: { _eq: await node.id } },
                };
            })
            .toArray(),
    };
}

export function checkTypeOrValue(
    node:
        | xtremFinanceData.nodes.Attribute
        | xtremFinanceData.nodes.AttributeType
        | xtremFinanceData.nodes.Dimension
        | xtremFinanceData.nodes.DimensionType,
    dataType: StoredAttributesDataType | StoredDimensionsDataType,
): Promise<boolean> {
    return asyncArray(node.$.context.application.findFactoriesUsingDatatype(dataType)).some(async factory => {
        let filter = {} as any;
        const properties = factory.properties.filter(property => property.dataType === dataType && property.isStored);
        if (
            node instanceof xtremFinanceData.nodes.AttributeType ||
            node instanceof xtremFinanceData.nodes.DimensionType
        )
            filter = await getTypeFilter(node, properties);
        else if (node instanceof xtremFinanceData.nodes.Attribute)
            filter = await getAttributeValueFilter(node, properties);
        else if (node instanceof xtremFinanceData.nodes.Dimension)
            filter = await getDimensionValueFilter(node, properties);

        const result = await node.$.context.query(factory.nodeConstructor, { filter, first: 1 } as any).toArray();
        return result.length > 0;
    });
}

export function getDimensionValue(
    context: Context,
    docProperty: xtremFinanceData.enums.DocProperty,
    storedDimensions: object | null,
): Promise<xtremFinanceData.nodes.Dimension | null> {
    const dimValue = (storedDimensions as Dict<string>)?.[docProperty];
    if (dimValue == null) return Promise.resolve(null);

    return context.tryRead(xtremFinanceData.nodes.Dimension, {
        id: dimValue,
        dimensionType: `#${docProperty}`,
    });
}

export const dimensionsAndAttributesOperations: Array<OperationGrant> = [
    { operations: ['lookup'], on: [() => xtremFinanceData.nodes.AttributeType] },
    { operations: ['lookup'], on: [() => xtremFinanceData.nodes.DimensionType] },
    { operations: ['lookup'], on: [() => xtremFinanceData.nodes.Attribute] },
    { operations: ['lookup'], on: [() => xtremFinanceData.nodes.Dimension] },
    {
        operations: [
            'lookup',
            'getDefaultAttributesAndDimensions',
            'getDefaultAttributesAndDimensionsOrderToOrder',
            'getAttributesAndDimensionsFromItem',
        ],
        on: [() => xtremFinanceData.nodes.DimensionDefinitionLevelAndDefault],
    },
];

export function mandatoryAttributeControl(
    storedAttributes: Object | null,
    mandatoryAttributes:
        | Omit<xtremFinanceData.nodes.CompanyAttributeType, 'company' | '$'>[]
        | Omit<xtremFinanceData.nodes.AccountAttributeType, 'account' | '$'>[],
) {
    return asyncArray(mandatoryAttributes)
        .filter(async lineAttribute => {
            const lineAttributeTypeId = await (await lineAttribute.attributeType).id;
            const lineAttributeTypeRestrictedToId = await (
                await (
                    await lineAttribute.attributeType
                ).attributeTypeRestrictedTo
            )?.id;

            const isAttributePopulated = Boolean((storedAttributes as Dict<string>)[lineAttributeTypeId]);

            // If the lineAttribute is restricted by another attribute,
            // only include it if attribute type restricted to is populated in storedAttributes and attribute is not populated
            if (lineAttributeTypeRestrictedToId) {
                const isRestrictedAttributePopulated = Boolean(
                    (storedAttributes as Dict<string>)[lineAttributeTypeRestrictedToId],
                );
                if (isRestrictedAttributePopulated && !isAttributePopulated) {
                    return true;
                }
            }

            // If lineAttributeTypeRestrictedToId is not present, still check if lineAttributeTypeId is in storedAttributes
            if (!lineAttributeTypeRestrictedToId && !isAttributePopulated) {
                return true;
            }

            return false;
        })
        .map(async lineAttribute => (await lineAttribute.attributeType).name)
        .toArray();
}

export function mandatoryDimensionControl(
    storedDimensions: Object,
    mandatoryDimensions:
        | Omit<xtremFinanceData.nodes.CompanyDimensionType, 'company' | '$'>[]
        | Omit<xtremFinanceData.nodes.AccountDimensionType, 'account' | '$'>[],
) {
    return asyncArray(mandatoryDimensions)
        .filter(async lineDimension => {
            const docProperty = await (await lineDimension.dimensionType).docProperty;
            return !(storedDimensions as Dict<string>)[docProperty];
        })
        .map(async lineDimension => (await lineDimension.dimensionType).name)
        .toArray();
}

export async function mandatoryCompanyDimensionAttributes(
    context: Context,
    parameters: {
        companyId: number;
        storedAttributes: xtremMasterData.interfaces.StoredAttributes | null;
        storedDimensions: Object;
        createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn;
        item: xtremMasterData.nodes.Item | null;
        documentNumber: string;
        isTaxAmount: boolean;
        uiBaseDocumentLineSysId?: number;
        uiSourceDocumentNumber?: string;
        sourceDocumentNumber?: string;
    },
) {
    const sourceDocumentNumber = parameters.uiSourceDocumentNumber || parameters.sourceDocumentNumber;

    const mandatoryAttributes = context.query(xtremFinanceData.nodes.CompanyAttributeType, {
        filter: { company: parameters.companyId, isRequired: true },
    });

    const mandatoryDimensions = context.query(xtremFinanceData.nodes.CompanyDimensionType, {
        filter: { company: parameters.companyId, isRequired: true },
    });

    const undefinedAttributes = await mandatoryAttributeControl(
        parameters.storedAttributes,
        await mandatoryAttributes.toArray(),
    );
    const undefinedDimensions = await mandatoryDimensionControl(
        parameters.storedDimensions,
        await mandatoryDimensions.toArray(),
    );
    const undefinedAttributeDimension = [...undefinedAttributes, ...undefinedDimensions];

    if (undefinedAttributeDimension.length > 0) {
        const arrayAsString = undefinedAttributeDimension.map(String).join(', ');

        const message = xtremFinanceData.classes.LocalizedMessages.mandatoryDimensionTypeNotFound(
            context,
            arrayAsString,
            (await parameters.item?.name) || null,
            'company',
            sourceDocumentNumber || parameters.documentNumber,
            parameters.isTaxAmount,
        );
        if (
            !parameters.createFinanceDocumentsReturn.validationMessages.find(
                validationMessages => validationMessages.message === message,
            )
        )
            parameters.createFinanceDocumentsReturn.validationMessages.push({
                type: ValidationSeverity.error,
                message,
                lineNumber: parameters.uiBaseDocumentLineSysId,
                sourceDocumentNumber: parameters.uiSourceDocumentNumber,
            });
    }

    return undefinedAttributeDimension;
}

export async function mandatoryAccountDimensionAttributes(
    context: Context,
    parameters: {
        accounts: xtremFinanceData.nodes.Account[];
        storedAttributes: xtremMasterData.interfaces.StoredAttributes;
        storedDimensions: Object;
        createFinanceDocumentsReturn: xtremFinanceData.interfaces.CreateFinanceDocumentsReturn;
        item: xtremMasterData.nodes.Item | null;
        documentNumber: string;
        isTaxAmount: boolean;
        uiBaseDocumentLineSysId?: number;
        uiSourceDocumentNumber?: string;
        sourceDocumentNumber?: string;
    },
) {
    const uniqueAccounts = Array.from(new Set(parameters.accounts));
    const sourceDocumentNumber = parameters.uiSourceDocumentNumber || parameters.sourceDocumentNumber;

    const uniqueAccountSysIds = parameters.accounts.map(account => account._id);
    const filter: NodeQueryFilter<
        xtremFinanceData.nodes.AccountAttributeType & xtremFinanceData.nodes.AccountDimensionType
    > = { account: { _in: uniqueAccountSysIds }, isRequired: true };

    const mandatoryAttributes = await context.query(xtremFinanceData.nodes.AccountAttributeType, { filter }).toArray();
    const mandatoryDimensions = await context.query(xtremFinanceData.nodes.AccountDimensionType, { filter }).toArray();

    await asyncArray(uniqueAccounts).forEach(async () => {
        const undefinedAttributes = await mandatoryAttributeControl(parameters.storedAttributes, mandatoryAttributes);
        const undefinedDimensions = await mandatoryDimensionControl(parameters.storedDimensions, mandatoryDimensions);
        const undefinedAttributeDimension = [...undefinedAttributes, ...undefinedDimensions];

        if (undefinedAttributeDimension.length > 0) {
            const arrayAsString = undefinedAttributeDimension.map(String).join(', ');

            const message = xtremFinanceData.classes.LocalizedMessages.mandatoryDimensionTypeNotFound(
                context,
                arrayAsString,
                (await parameters.item?.name) || null,
                'account',
                sourceDocumentNumber || parameters.documentNumber,
                parameters.isTaxAmount,
            );
            if (
                !parameters.createFinanceDocumentsReturn.validationMessages.find(
                    validationMessages => validationMessages.message === message,
                )
            )
                parameters.createFinanceDocumentsReturn.validationMessages.push({
                    type: ValidationSeverity.error,
                    message,
                    lineNumber: parameters.uiBaseDocumentLineSysId,
                    sourceDocumentNumber: parameters.uiSourceDocumentNumber,
                });
        }
    });
}

/**
 * Checks if restricted to attribute is populated.
 */
export async function attributTypeRestrictedToCheck(
    cx: ValidationContext,
    context: Context,
    storedAttributes: xtremMasterData.interfaces.StoredAttributes | null,
) {
    if (storedAttributes === null) return;

    const storedAttributeKeys = Object.keys(storedAttributes).filter(key => storedAttributes[key] !== '');
    const activeAttributeTypes = context.query(xtremFinanceData.nodes.AttributeType, {
        filter: { isActive: true, id: { _in: storedAttributeKeys } },
    });

    await activeAttributeTypes.forEach(async attributeType => {
        const attributeTypeRestrictedToId = await (await attributeType.attributeTypeRestrictedTo)?.id;
        if (attributeTypeRestrictedToId) {
            const isAttributeTypeRestrictedToPopulated = storedAttributes[attributeTypeRestrictedToId];
            if (!isAttributeTypeRestrictedToPopulated) {
                const attributeTypeRestrictedToName = await (await attributeType.attributeTypeRestrictedTo)?.name;
                cx.error.addLocalized(
                    '@sage/xtrem-finance-data/functions__dimensions__attribute_type_restricted_to',
                    'The {{attributeTypeRestrictedToName}} attribute needs to be filled in.',
                    { attributeTypeRestrictedToName },
                );
            }
        }
    });
}

export async function setAnalyticalValueFromStoredAttribute(
    context: Context,
    key: string,
    values: Dict<string> | null,
    nodeConstructor: StaticThis<Node & { id: Promise<string> }>,
    result: NodeCreateData<AnyNode>,
): Promise<void> {
    if (!values) return;
    const value = values[key];
    if (!value) return;

    let filter = { id: value } as Dict<any>;
    if (key === 'supplier' || key === 'customer') filter = { businessEntity: { id: value } };
    if (nodeConstructor === (xtremFinanceData.nodes.Attribute as any)) {
        filter = { id: value, attributeType: { id: key } };
        if (key === 'task') {
            // TODO: should throw if project is not set but for now we are relying on the control of storedAttributes
            // Changing this now would require a lot of changes in the tests and would break UX because
            // the error message will be poor and won't have a path.
            if (!values.project) return;
            filter.attributeRestrictedToId = values.project;
        }
    }

    const newValue = await context.read(nodeConstructor, filter);
    result[key] = newValue._id;
}

export async function setAnalyticalValueFromStoredDimension(
    context: Context,
    key: string,
    value: string | undefined,
    result: NodeCreateData<AnyNode>,
): Promise<void> {
    if (!value) return;
    if (!key.startsWith('dimensionType')) {
        throw new LogicError(`${key} is not a valid key in stored dimensions`);
    }
    const newKey = key.replace('dimensionType', 'dimension');
    const newValue = await context.read(xtremFinanceData.nodes.Dimension, { dimensionType: `#${key}`, id: value });
    result[newKey] = newValue._id;
}

export async function getAnalyticalDataFromStoredAttributesAndDimensions(
    context: Context,
    attributeValues: Dict<string> | object | null,
    dimensionValues: Dict<string> | object | null,
): Promise<NodeCreateData<xtremFinanceData.nodes.AnalyticalData>> {
    const result = {} as NodeCreateData<AnyNode>;
    await asyncArray([
        { key: 'financialSite', nodeConstructor: xtremSystem.nodes.Site },
        { key: 'businessSite', nodeConstructor: xtremSystem.nodes.Site },
        { key: 'stockSite', nodeConstructor: xtremSystem.nodes.Site },
        { key: 'manufacturingSite', nodeConstructor: xtremSystem.nodes.Site },
        { key: 'supplier', nodeConstructor: xtremMasterData.nodes.Supplier },
        { key: 'customer', nodeConstructor: xtremMasterData.nodes.Customer },
        { key: 'project', nodeConstructor: xtremFinanceData.nodes.Attribute },
        { key: 'task', nodeConstructor: xtremFinanceData.nodes.Attribute },
        { key: 'employee', nodeConstructor: xtremFinanceData.nodes.Attribute },
        { key: 'item', nodeConstructor: xtremMasterData.nodes.Item },
    ]).forEach(async ({ key, nodeConstructor }) => {
        await setAnalyticalValueFromStoredAttribute(
            context,
            key,
            (attributeValues as Dict<string>) || null,
            nodeConstructor,
            result,
        );
    });

    await asyncArray(Object.entries(dimensionValues || {})).forEach(async ([key, value]) => {
        await setAnalyticalValueFromStoredDimension(context, key, value, result);
    });
    return result;
}

export async function isAnalyticalDataFrozen(
    node: Node & { storedAttributes: Promise<object | null>; storedDimensions: Promise<object | null> },
) {
    const old = await node.$.old;
    if (!old) return true;

    return (
        (await node.storedAttributes) === (await old.storedAttributes) &&
        (await node.storedDimensions) === (await old.storedDimensions)
    );
}

export async function computeAttributes(
    context: Context,
    options: {
        salesSite: xtremSystem.nodes.Site;
        secondSite: xtremSystem.nodes.Site;
        item: xtremMasterData.nodes.Item;
        billToCustomer?: xtremMasterData.nodes.Customer;
        supplier?: xtremMasterData.nodes.Supplier;
    },
): Promise<xtremFinanceData.interfaces.ComputedAttributes> {
    const result = await xtremFinanceData.functions.computeGenericAttributes(context, {
        item: options.item,
        stockSite: options.secondSite,
        financialSite: options.salesSite,
        returnBusinessSite: false,
    });

    if (options.billToCustomer) {
        result.customer = await xtremFinanceData.functions.checkAttributeTypeActive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'customer',
            await (
                await options.billToCustomer.businessEntity
            ).id,
        );
    }

    if (options.supplier) {
        result.supplier = await xtremFinanceData.functions.checkAttributeTypeActive(
            context,
            xtremFinanceData.nodes.AttributeType,
            'supplier',
            await (
                await options.supplier.billBySupplier
            ).id,
        );
    }

    return result;
}
