import type { Collection, Context } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremTax from '@sage/xtrem-tax';
import * as xtremFinanceData from '..';

/**
 * Returns the tax line that has a tax category that belongs to the tax solution line that
 * has the isTaxMandatory property set to true and the lowest display order
 * @param context Context
 * @param line An object that implements ApArInvoiceLine and has a collection of taxes
 * @return The tax line
 */
export async function getTaxLineIsTaxMandatory(
    context: Context,
    params: {
        financialSite: xtremSystem.nodes.Site;
        taxes?: Collection<xtremTax.nodes.BaseLineTax>;
    },
): Promise<xtremTax.nodes.BaseLineTax | null | undefined> {
    const taxSolutionLineIsTaxMandatory = await context
        .query(xtremTax.nodes.TaxSolutionLine, {
            filter: {
                taxSolution: {
                    _id: (await (await (await params.financialSite.legalCompany).country).taxSolution)?._id,
                },
                isTaxMandatory: true,
            },
            orderBy: { displayOrder: 1 },
            first: 1,
        })
        .toArray();

    return taxSolutionLineIsTaxMandatory.length > 0
        ? params.taxes?.find(async taxLine => {
              return (
                  (await taxLine.taxCategoryReference)?._id ===
                  (await taxSolutionLineIsTaxMandatory[0].taxCategory)?._id
              );
          })
        : null;
}

/**
 * Returns the document lines paylod for an Ap/Ar invoice notification
 * @param context Context
 * @param document An object that implements ArInvoice to get header related properties from a document
 * @param lines An array of objects that implements ApArInvoiceLine to get line related properties from a document
 * @return The document lines payload
 */
async function getApArDocumentLinesPayload(parameters: {
    context: Context;
    document: xtremFinanceData.interfaces.ArInvoice | xtremFinanceData.interfaces.ApInvoice;
    lines: xtremFinanceData.interfaces.ApArInvoiceLine[];
    isApNotification: boolean;
}): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]> {
    const documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[] = [];
    await asyncArray(parameters.lines).forEach(async line => {
        // Records related with the header. We get them from the sum of all lines and this way we can record the baseDocumentLine on accounting staging table
        const headerAmounts = [
            {
                amountType: 'amountIncludingTax',
                amount: await line.amountIncludingTax,
                documentLineType: 'documentLine',
            } as xtremFinanceData.interfaces.AccountingStagingAmount,
        ];

        const taxLineSubjectToGlTaxExcludedAmount = await getTaxLineIsTaxMandatory(parameters.context, {
            financialSite: await parameters.document.financialSite,
            taxes: line.taxes,
        });

        if (taxLineSubjectToGlTaxExcludedAmount) {
            headerAmounts[0] = {
                ...headerAmounts[0],
                taxSysId: (await taxLineSubjectToGlTaxExcludedAmount.taxReference)?._id,
                baseTaxSysId: taxLineSubjectToGlTaxExcludedAmount._id,
            };
        }
        const basePayload = {
            baseDocumentLineSysId: line._id,
            movementType: 'document',
            sourceDocumentNumber: await line.sourceDocumentNumber,
            sourceDocumentType: await line.sourceDocumentType,
            fxRateDate: ((await parameters.document.fxRateDate) || (await parameters.document.documentDate)).toString(),
            currencySysId: (await parameters.document.transactionCurrency)._id,
            companyFxRate: (await parameters.document.companyFxRate) ?? 1.0,
            companyFxRateDivisor: (await parameters.document.companyFxRateDivisor) ?? 1.0,
            accountSysId: (await parameters.document.account)?._id,
            taxDate: (await line.taxDate)?.toString() ?? '',
            storedDimensions: (await line.storedDimensions) || {},
            storedAttributes: ((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes) || {},
            ...(!parameters.isApNotification
                ? {
                      customerSysId: (
                          await (parameters.document as xtremFinanceData.interfaces.ArInvoice).billToCustomer
                      )._id,
                  }
                : {}),
            ...(parameters.isApNotification
                ? {
                      supplierSysId: (
                          await (parameters.document as xtremFinanceData.interfaces.ApInvoice).billBySupplier
                      )._id,
                  }
                : {}),
        };
        documentLines.push({
            ...basePayload,
            amounts: headerAmounts,
        } as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine);

        // Records related with the lines.
        const payLoadLine = {
            ...basePayload,
            accountSysId: (await line.account)?._id,
            amounts: [
                {
                    amountType: 'amountExcludingTax',
                    amount: await line.amountExcludingTax,
                    documentLineType: 'documentLine',
                } as xtremFinanceData.interfaces.AccountingStagingAmount,
            ],
        };
        if (taxLineSubjectToGlTaxExcludedAmount) {
            payLoadLine.amounts[0] = {
                ...payLoadLine.amounts[0],
                taxSysId: (await taxLineSubjectToGlTaxExcludedAmount.taxReference)?._id,
                baseTaxSysId: taxLineSubjectToGlTaxExcludedAmount._id,
            };
        }

        // Add other amounts based on the tax lines
        await line.taxes?.forEach(async taxLine => {
            if ((await taxLine.deductibleTaxAmount) !== 0) {
                payLoadLine.amounts.push({
                    amountType: 'deductibleTaxAmount',
                    amount: await taxLine.deductibleTaxAmount,
                    taxSysId: (await taxLine?.taxReference)?._id,
                    baseTaxSysId: taxLine?._id,
                    documentLineType: 'documentLine',
                });
            }
            const taxAmountToCheck = parameters.isApNotification
                ? await taxLine.taxAmountAdjusted
                : await taxLine.taxAmount;
            if (taxAmountToCheck - (await taxLine.deductibleTaxAmount) !== 0) {
                payLoadLine.amounts.push({
                    amountType: 'nonDeductibleTaxAmount',
                    amount: taxAmountToCheck - (await taxLine.deductibleTaxAmount),
                    taxSysId: (await taxLine?.taxReference)?._id,
                    baseTaxSysId: taxLine?._id,
                    documentLineType: 'documentLine',
                });
            }
            if ((await taxLine.deductibleTaxAmount) !== 0 && (await taxLine.isReverseCharge)) {
                payLoadLine.amounts.push({
                    amountType: 'reverseChargeDeductibleTaxAmount',
                    amount: await taxLine.deductibleTaxAmount,
                    taxSysId: (await taxLine?.taxReference)?._id,
                    baseTaxSysId: taxLine?._id,
                    documentLineType: 'documentLine',
                });
            }
            if (taxAmountToCheck - (await taxLine.deductibleTaxAmount) !== 0 && (await taxLine.isReverseCharge)) {
                payLoadLine.amounts.push({
                    amountType: 'reverseChargeNonDeductibleTaxAmount',
                    amount: await taxLine.deductibleTaxAmount,
                    taxSysId: (await taxLine?.taxReference)?._id,
                    baseTaxSysId: taxLine?._id,
                    documentLineType: 'documentLine',
                });
            }
        });
        documentLines.push(payLoadLine as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine);
    });
    return documentLines;
}

/**
 * Creates all the notifications for the finance integration (creation of a journal entry) of an Ar Invoice
 * @param context Context
 * @param document An object that implements ArInvoice to get header related properties from a document
 * @param lines An array of objects that implements ApArInvoiceLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function accountsReceivableInvoiceNotification(
    context: Context,
    document: xtremFinanceData.interfaces.ArInvoice,
    lines: xtremFinanceData.interfaces.ApArInvoiceLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    const sourceLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[] = [
        {
            sourceDocumentNumber: await document.salesDocumentNumber,
            sourceDocumentSysId: await document.salesDocumentSysId,
            sourceDocumentType: (await document.origin) === 'invoice' ? 'salesInvoice' : 'salesCreditMemo',
        },
    ];

    const documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[] =
        await getApArDocumentLinesPayload({
            context,
            document,
            lines,
            isApNotification: false,
        });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'arInvoice',
            targetDocumentType: 'journalEntry',
            documentLines,
            sourceLines,
        },
    ];

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: 'AccountsReceivableInvoice/accountingInterface',
        isUpdate: false,
    });
}

/**
 * Creates all the notifications for the finance integration (creation of a journal entry) of an Ap Invoice
 * @param context Context
 * @param document An object that implements ApInvoice to get header related properties from a document
 * @param lines An array of objects that implements ApArInvoiceLine to get line related properties from a document
 * @return The number of notifications created
 */
export async function accountsPayableInvoiceNotification(
    context: Context,
    document: xtremFinanceData.interfaces.ApInvoice,
    lines: xtremFinanceData.interfaces.ApArInvoiceLine[],
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    const sourceLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[] = [
        {
            sourceDocumentNumber: await document.purchaseDocumentNumber,
            sourceDocumentSysId: await document.purchaseDocumentSysId,
            sourceDocumentType: (await document.origin) === 'invoice' ? 'purchaseInvoice' : 'purchaseCreditMemo',
        },
    ];

    const documentLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[] =
        await getApArDocumentLinesPayload({
            context,
            document,
            lines,
            isApNotification: true,
        });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'apInvoice',
            targetDocumentType: 'journalEntry',
            documentLines,
            sourceLines,
        },
    ];

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: 'AccountsPayableInvoice/accountingInterface',
        isUpdate: false,
    });
}

// UPDATES

async function getUpdatePayload(options: {
    financeIntegrationRecord: xtremFinanceData.nodes.FinanceTransaction | undefined;
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType;
    document: xtremFinanceData.interfaces.FinanceOriginDocument;
    lines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineNodeUpdate[];
    doNotPostOnUpdate: boolean;
}): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate> {
    return {
        batchId: (await options.financeIntegrationRecord?.batchId) || '',
        documentSysId: (await options.financeIntegrationRecord?.documentSysId) || 0,
        documentNumber: (await options.financeIntegrationRecord?.documentNumber) || '',
        financialSiteSysId: (await options.financeIntegrationRecord?.financialSite)?._id || 0,
        batchSize: 0,
        documentType: options.documentType,
        targetDocumentType: options.targetDocumentType,
        doNotPostOnUpdate: options.doNotPostOnUpdate,
        documentLines: (await asyncArray(options.lines)
            .map(async line => {
                return {
                    baseDocumentLineSysId: line._id,
                    sourceBaseDocumentLineSysId: line.sourceBaseDocumentLineSysId,
                    storedDimensions: (await line.storedDimensions) || {},
                    storedAttributes: {
                        ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                        ...((await line.computedAttributes) as {}),
                    },
                };
            })
            .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
    };
}

/**
 * Create the notifications for the update of a journal entry and\or ar invoice created from an operations document
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @param documentType The documentType for the notification
 * @param replyTopic The reply topic for the notifications
 * @param financeTransactionRecord? The financeTransaction record for which the reposting has to be done. If not sent, all the finance transaction records for the document, in a failed or error status, will be considered
 * @param sourceDocumentNumber Source document number will be used to filter the results on the accounting staging table to update, if we need such a filter
 * @return The number of notifications created
 */
export async function financeDocumentUpdateNotification(
    context: Context,
    params: {
        document: xtremFinanceData.interfaces.FinanceOriginDocument;
        lines: xtremFinanceData.interfaces.FinanceIntegrationDocumentLineNodeUpdate[];
        documentType: xtremFinanceData.enums.FinanceDocumentType;
        replyTopic: string;
        doNotPostOnUpdate: boolean;
        financeTransactionRecord?: xtremFinanceData.nodes.FinanceTransaction;
        sourceDocumentNumber?: string;
    },
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    let notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate[];
    if (!params.financeTransactionRecord) {
        notificationsPayload = await context
            .query(xtremFinanceData.nodes.FinanceTransaction, {
                filter: {
                    documentSysId: params.document._id,
                    documentType: params.documentType,
                    status: { _in: ['pending', 'failed', 'notRecorded'] }, // just saved at the staging table (not processed yet), or processed with error
                },
            })
            .map(async financeTransaction => {
                return getUpdatePayload({
                    financeIntegrationRecord: financeTransaction,
                    documentType: params.documentType,
                    targetDocumentType: await financeTransaction.targetDocumentType,
                    document: params.document,
                    lines: params.lines,
                    doNotPostOnUpdate: params.doNotPostOnUpdate,
                });
            })
            .toArray();
    } else {
        notificationsPayload = [
            {
                ...(await getUpdatePayload({
                    financeIntegrationRecord: params.financeTransactionRecord,
                    documentType: params.documentType,
                    targetDocumentType: await params.financeTransactionRecord.targetDocumentType,
                    document: params.document,
                    lines: params.lines,
                    doNotPostOnUpdate: params.doNotPostOnUpdate,
                })),
                sourceDocumentType: await params.financeTransactionRecord.sourceDocumentType,
                sourceDocumentNumber: params.sourceDocumentNumber,
            },
        ];
    }

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: params.replyTopic,
        isUpdate: true,
    });
}
