import type { Context, decimal } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';
import * as xtremTax from '@sage/xtrem-tax';
import * as _ from 'lodash';
import { uniq } from 'lodash';
import * as xtremFinanceData from '..';

export const financeIntegrationStatusFailed: xtremFinanceData.enums.FinanceIntegrationStatus[] = [
    'notRecorded',
    'error',
    'failed',
];

export function isFinanceDocumentType(value: any): value is xtremFinanceData.enums.FinanceDocumentTypeEnum {
    return Object.values(xtremFinanceData.enums.FinanceDocumentTypeEnum).includes(value);
}

export async function getTaxSolutionIdsForLegislation(options: {
    context: Context;
    legislationId: string;
}): Promise<number[]> {
    // first step: read all countries with the given legislationId assigned
    const countries = options.context.query(xtremStructure.nodes.Country, {
        filter: { legislation: { id: options.legislationId } },
    });
    // second step: get unique list of taxSolutions assigned to these countries
    return uniq(
        await countries
            .map(async country => {
                return (await country.taxSolution)?._id || 0;
            })
            .filter(_id => _id !== 0)
            .toArray(),
    );
}

export async function isSubjectToGlTaxExcludedAmount(options: {
    context: Context;
    legislationId: string;
    isVatFilter?: boolean;
}): Promise<boolean> {
    // first step: get unique list of taxSolutions assigned to all countries with the given legislation
    const taxSolutionIds = await getTaxSolutionIdsForLegislation({
        context: options.context,
        legislationId: options.legislationId,
    });
    // second step: check if there is a taxSolutionLine in one of the found tax solutions with
    // isSubjectToGlTaxExcludedAmount = true and taxCategory = VAT
    const filter: {
        taxCategory?: { id: string };
        taxSolution: { _id: { _in: number[] } };
        isSubjectToGlTaxExcludedAmount: boolean;
    } = {
        taxSolution: { _id: { _in: taxSolutionIds } },
        isSubjectToGlTaxExcludedAmount: true,
        ...(options.isVatFilter ? { taxCategory: { id: 'VAT' } } : undefined),
    };
    return (await options.context.queryCount(xtremTax.nodes.TaxSolutionLine, { filter })) > 0;
}

async function getPostingClass(
    context: Context,
    parameters: xtremFinanceData.interfaces.UnbilledInquiriesGetAccountParameters,
): Promise<xtremFinanceData.nodes.PostingClass | null> {
    // read postingClassDefinition of type given in 'type' parameter for the 'legislation' and id
    // given in 'id' parameter' in order to read the isDetailed flag, which indicates what posting class record
    // to use (either the one linked to the billToCustomer/item given in parameter 'postingClassDetailed', if
    // isDetailed = true or the one with isDetailed = false if isDetailed = false)
    const postingClassDefinition = await context.tryRead(xtremFinanceData.nodes.PostingClassDefinition, {
        postingClassType: parameters.type,
        id: parameters.id,
        legislation: parameters.legislation,
    });
    if (postingClassDefinition && !(await postingClassDefinition.isDetailed)) {
        // if not detailed > read posting class with isDetailed = false
        return (
            (await context
                .query(xtremFinanceData.nodes.PostingClass, {
                    filter: { type: parameters.type, isDetailed: false },
                })
                .at(0)) ?? null
        );
    }
    return parameters.postingClassDetailed; // if detailed > take posting class from billToCustomer/item
}
// Returns an account or null. To find the account, the posting class definition is read and the isDetailed flag
// is checked to decide which posting class to use. The line of the posting class,
// where the account is taken from, needs to be assigned to the posting class definition with the id given in
// the "id" parameter and the chart of account given in the "chartOfAccount" parameter
export async function getAccount(
    context: Context,
    parameters: xtremFinanceData.interfaces.UnbilledInquiriesGetAccountParameters,
): Promise<xtremFinanceData.nodes.Account | null> {
    if (parameters.invoicedQuantity === 0) {
        return null;
    }
    const postingClass = await getPostingClass(context, parameters);
    const postingClassLine =
        (await postingClass?.lines.takeOne(
            async (line: xtremFinanceData.nodes.PostingClassLine) =>
                (await (await line.definition).id) === parameters.id &&
                (await (await line.chartOfAccount).name) === (await parameters.chartOfAccount.name),
        )) ?? null;
    // get account from the lines of the retrieved posting class
    return xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryLineAccount({
        postingClassLine,
        accountingStagingLineAccount: null,
        tax: parameters.tax ?? null,
    });
}

export function calculateInvoicedValues(parameters: {
    lineQuantity: decimal;
    lineNetPrice: decimal;
    quantities: xtremFinanceData.interfaces.UnbilledInquiriesQuantities;
}): xtremFinanceData.interfaces.UnbilledInquiriesInvoicedValues {
    // calulate the invoiceIssuableQuantity using the following formula:
    // invoiceIssuableQuantity = max(max(shippedQuantity-returnedQuantity,0) - max(invoicedQuantity-creditedQuantity,0),0)
    const invoicedQuantity = Math.max(
        Math.max(parameters.lineQuantity - parameters.quantities.returned, 0) -
            Math.max(parameters.quantities.invoiced - parameters.quantities.credited, 0),
        0,
    );
    return {
        invoicedQuantity,
        invoicedAmount: invoicedQuantity * parameters.lineNetPrice,
    };
}

export function isFinanceIntegrationDocumentLine(
    documentLine:
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentLine
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate,
): documentLine is xtremFinanceData.interfaces.FinanceIntegrationDocumentLine {
    return _.hasIn(documentLine, 'amounts');
}
export function isArrayOfFinanceIntegrationDocumentLine(
    documentLines:
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[]
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
): documentLines is xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[] {
    if (documentLines.length === 0) return false;
    return isFinanceIntegrationDocumentLine(documentLines[0]);
}

export function isFinanceIntegrationDocument(
    document:
        | xtremFinanceData.interfaces.FinanceIntegrationDocument
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate,
): document is xtremFinanceData.interfaces.FinanceIntegrationDocument {
    if (document.documentLines.length === 0) return false;
    return isArrayOfFinanceIntegrationDocumentLine(document.documentLines);
}

export function isArrayOfFinanceIntegrationDocument(
    documents:
        | xtremFinanceData.interfaces.FinanceIntegrationDocument[]
        | xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate[],
): documents is xtremFinanceData.interfaces.FinanceIntegrationDocument[] {
    if (documents.length === 0) return false;
    return isFinanceIntegrationDocument(documents[0]);
}
