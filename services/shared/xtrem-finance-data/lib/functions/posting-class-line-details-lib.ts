import type * as xtremStructure from '@sage/xtrem-structure';

export function getTaxCategoryIdsFromLegislation(legislation: xtremStructure.nodes.Legislation | null) {
    if (!legislation) {
        return [];
    }
    const taxSolutions = legislation?.countries.map(country => country.taxSolution);
    return taxSolutions
        .map(taxSolution => taxSolution?.getFirstTaxCategoryIdWithIsTaxMandatory())
        .filter(taxSolutionId => taxSolutionId !== null)
        .toArray() as Promise<string[]>;
}
