import type { AsyncArrayReader, Context, NodeQueryFilter } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremFinanceData from '..';
import { getTaxLineIsTaxMandatory } from './notification-lib';

export async function getPurchaseInvoiceLinesWithReceiptStockJournalLines(
    context: Context,
    lines: xtremFinanceData.interfaces.InvoiceDocumentLine[],
    company: xtremSystem.nodes.Company,
): Promise<AsyncArrayReader<xtremFinanceData.interfaces.InvoiceDocumentLine>> {
    // if we have a stockMovementArray (it is a checking case), we return those lines
    // but only for stock items or landed cost items on lines where landed cost was already allocated
    let documentLines = asyncArray(lines).filter(async purchaseInvoiceLine => {
        return (
            !!purchaseInvoiceLine.stockMovementArray &&
            ((await (await purchaseInvoiceLine.item).isStockManaged) ||
                !!(await (
                    await purchaseInvoiceLine.landedCost
                )?.allocations.length))
        );
    });

    // if it's not a checking case, we get data from the stock journal
    const amountFilter: NodeQueryFilter<xtremStockData.nodes.StockJournal> = {
        _or: [
            {
                movementAmount: {
                    _ne: 0,
                },
            },
        ],
    };

    if (amountFilter._or && (await company.doNonAbsorbedPosting)) {
        amountFilter._or.push({
            nonAbsorbedAmount: {
                _ne: 0,
            },
        });
    }

    // return all purchase invoice lines that have at least on record on the stock journal for the given conditions
    if (!(await documentLines.length)) {
        documentLines = asyncArray(lines).filter(async purchaseInvoiceLine => {
            const stockJournalCount = await context.queryCount(xtremStockData.nodes.StockJournal, {
                filter: {
                    sourceDocumentLine: purchaseInvoiceLine._id,
                    ...amountFilter,
                },
            });
            return stockJournalCount > 0;
        });
    }

    return documentLines;
}

/**
 * Gets a notification payload needed for the finance integration of a Sales Shipment
 * @param document An object that implements SalesFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLineWithStock to get line related properties from a document
 */
export async function getSalesShipmentNotificationPayload(
    document: xtremFinanceData.interfaces.SalesFinanceDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
    documentNumber?: string, // this allows us to provide a dummy number when we are controlling a sales shipment that is not created yet (like from an order)
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({
            document,
            isJustChecking,
            number: documentNumber,
        });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'salesShipment',
            targetDocumentType: 'journalEntry',
            documentLines: (await asyncArray(lines)
                .filter(async line => (await line.item).isStockManaged)
                .map(async line => {
                    const movementAmount: number = -(await xtremFinanceData.functions.getStockMovementsAmounts(line))
                        .movementAmount;
                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'stockJournal',
                        sourceDocumentNumber: '',
                        fxRateDate: ((await document.fxRateDate) || (await document.documentDate)).toString(),
                        currencySysId: (await (await (await document.financialSite).legalCompany).currency)._id, // for 'stockJournal' take currency of financial site
                        companyFxRate: 1.0, // no currency conversion for 'stockJournal'
                        companyFxRateDivisor: 1.0,
                        itemSysId: (await line.item)._id,
                        customerSysId: (await document.billToCustomer)._id,
                        amounts: [{ amountType: 'amount', amount: movementAmount, documentLineType: 'documentLine' }],
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    };
                })
                .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[],
        },
    ];

    // Revenue recognition + non stock items
    if (
        !xtremFinanceData.sharedFunctions.common.legislationsWithoutRevenueRecognition.includes(
            await (
                await (
                    await (
                        await document.financialSite
                    ).legalCompany
                ).legislation
            ).id,
        )
    ) {
        await asyncArray(lines).forEach(async line => {
            notificationsPayload[0].documentLines.push({
                baseDocumentLineSysId: line._id,
                movementType: 'document',
                sourceDocumentNumber: '',
                fxRateDate: ((await document.fxRateDate) || (await document.documentDate)).toString(),
                currencySysId: (await document.transactionCurrency)._id, // for 'document' take transaction currency
                companyFxRate: (await document.companyFxRate) || 1.0,
                companyFxRateDivisor: (await document.companyFxRateDivisor) || 1.0,
                itemSysId: (await line.item)._id,
                customerSysId: (await document.billToCustomer)._id,
                amounts: [
                    {
                        amountType: 'amountExcludingTax',
                        amount: await line.amountExcludingTax,
                        documentLineType: 'documentLine',
                    },
                ],
                storedDimensions: (await line.storedDimensions) || {},
                storedAttributes: {
                    ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                    ...((await line.computedAttributes) as {}),
                },
            });
        });
    }

    return notificationsPayload;
}

/**
 * Creates all the notifications for the finance integration of a Sales Shipment
 * @param context Context
 * @param document An object that implements SalesFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLineWithStock to get line related properties from a document
 * @return The number of notifications created
 */
export async function salesShipmentNotification(
    context: Context,
    document: xtremFinanceData.interfaces.SalesFinanceDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getSalesShipmentNotificationPayload(document, lines),
        replyTopic: 'SalesShipment/accountingInterface',
        isUpdate: false,
    });
}

/**
 * Common part to create all the notifications for the finance integration of a Purchase or Sales Invoice or credit memo
 * @param document An object that implements PurchaseFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceInvoiceDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */

export async function getPurchaseSalesInvoiceCreditMemoNotificationPayload(
    context: Context,
    params: {
        document:
            | xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
            | xtremFinanceData.interfaces.SalesFinanceDocumentWithTax;
        lines: xtremFinanceData.interfaces.InvoiceDocumentLine[];
        documentType: xtremFinanceData.enums.FinanceDocumentType;
        targetDocumentType: xtremFinanceData.enums.TargetDocumentType;
        isJustChecking: boolean;
        batchId?: string;
    },
): Promise<{
    accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload;
    notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[];
}> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({
            document: params.document,
            isJustChecking: params.isJustChecking,
            batchId: params.batchId,
        });

    const documentTaxes: xtremFinanceData.interfaces.AccountingStagingTax[] = [];
    await params.document.taxes?.forEach(tax => {
        documentTaxes.push({ baseTaxSysId: tax._id });
    });

    // in case of purchase and sales credit memo:
    // build a unique array of source information (SysId + Number + type), 1 element for each
    // source document linked to the document to add to the finance transaction record later
    let sourceLines: xtremFinanceData.interfaces.FinanceIntegrationDocumentSourceProperties[] = [];
    if (!params.isJustChecking && ['purchaseCreditMemo', 'salesCreditMemo'].includes(params.documentType)) {
        sourceLines =
            params.documentType === 'salesCreditMemo'
                ? await xtremFinanceData.functions.getSourceLines({
                      lines: params.lines,
                      isConstantSourceDocumentType: false,
                      targetDocumentType: params.targetDocumentType,
                  })
                : await xtremFinanceData.functions.getSourceLines({
                      lines: params.lines,
                      isConstantSourceDocumentType: true,
                      targetDocumentType: params.targetDocumentType,
                      type: 'purchaseReceipt',
                  });
    }

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: params.documentType,
            targetDocumentType: params.targetDocumentType,
            paymentTermSysId: (await params.document.paymentTerm)._id,
            dueDate: (await params.document.dueDate).toString(),
            supplierDocumentDate:
                params.targetDocumentType === 'accountsPayableInvoice'
                    ? (
                          await (params.document as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax)
                              .supplierDocumentDate
                      ).toString()
                    : undefined,
            supplierDocumentNumber:
                params.targetDocumentType === 'accountsPayableInvoice'
                    ? await (params.document as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax)
                          .supplierDocumentNumber
                    : undefined,
            isPrinted:
                params.targetDocumentType === 'accountsReceivableInvoice'
                    ? await (params.document as xtremFinanceData.interfaces.SalesFinanceDocumentWithTax).isPrinted
                    : undefined,
            taxCalculationStatus:
                params.targetDocumentType === 'accountsReceivableInvoice'
                    ? await (params.document as xtremFinanceData.interfaces.SalesFinanceDocumentWithTax)
                          .taxCalculationStatus
                    : undefined,
            documentLines: await asyncArray(params.lines)
                .map(async line => {
                    const lineTaxes: xtremFinanceData.interfaces.AccountingStagingTax[] = [];
                    const taxLineSubjectToGlTaxExcludedAmount = await getTaxLineIsTaxMandatory(context, {
                        financialSite: await params.document.financialSite,
                        taxes: line.taxes,
                    });
                    const amounts: xtremFinanceData.interfaces.AccountingStagingAmount[] = [
                        {
                            amountType: 'amountExcludingTax',
                            amount: await line.amountExcludingTax,
                            documentLineType: 'documentLine',
                            ...(taxLineSubjectToGlTaxExcludedAmount
                                ? {
                                      taxSysId: (await taxLineSubjectToGlTaxExcludedAmount.taxReference)?._id,
                                      baseTaxSysId: taxLineSubjectToGlTaxExcludedAmount._id,
                                  }
                                : {}),
                        },
                    ];
                    await line.taxes?.forEach(async tax => {
                        lineTaxes.push({
                            baseTaxSysId: tax._id,
                        });
                        // US tax: add staging amount line for each tax line if tax amount not zero
                        if (
                            (await (
                                await (
                                    await (
                                        await params.document.financialSite
                                    ).legalCompany
                                ).legislation
                            ).id) === 'US' &&
                            (await tax.taxAmount) !== 0
                        ) {
                            amounts.push({
                                amountType: 'taxAmount',
                                amount: await tax.taxAmount,
                                documentLineType: 'taxLine',
                                baseTaxSysId: tax._id,
                            });
                        }
                    });
                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'document',
                        sourceDocumentNumber: !params.isJustChecking ? await line.sourceDocumentNumber : '',
                        sourceDocumentType: !params.isJustChecking ? await line.sourceDocumentType : null,
                        fxRateDate: (
                            (await params.document.fxRateDate) || (await params.document.documentDate)
                        ).toString(),
                        currencySysId: (await params.document.transactionCurrency)._id,
                        companyFxRate: (await params.document.companyFxRate) || 1.0,
                        companyFxRateDivisor: (await params.document.companyFxRateDivisor) || 1.0,
                        itemSysId: (await line.item)._id,
                        supplierSysId:
                            params.targetDocumentType === 'accountsPayableInvoice'
                                ? (
                                      await (
                                          params.document as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
                                      ).billBySupplier
                                  )._id
                                : undefined,
                        payToSupplierSysId:
                            params.targetDocumentType === 'accountsPayableInvoice'
                                ? (
                                      await (
                                          params.document as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
                                      ).payToSupplier
                                  )?._id
                                : undefined,
                        payToSupplierAddressSysId:
                            params.targetDocumentType === 'accountsPayableInvoice'
                                ? (
                                      await (
                                          params.document as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
                                      ).payToLinkedAddress
                                  )?._id
                                : undefined,
                        returnAddressSysId:
                            params.targetDocumentType === 'accountsPayableInvoice'
                                ? (
                                      await (
                                          params.document as xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
                                      ).returnLinkedAddress
                                  )?._id
                                : undefined,
                        customerSysId:
                            params.targetDocumentType === 'accountsReceivableInvoice'
                                ? (
                                      await (params.document as xtremFinanceData.interfaces.SalesFinanceDocumentWithTax)
                                          .billToCustomer
                                  )._id
                                : undefined,
                        recipientSiteSysId:
                            params.targetDocumentType === 'accountsPayableInvoice'
                                ? (await line.recipientSite)?._id
                                : undefined,
                        providerSiteSysId:
                            params.targetDocumentType === 'accountsReceivableInvoice'
                                ? (await line.providerSite)?._id
                                : undefined,
                        amounts,
                        taxDate: (await line.taxDate)?.toString() || '',
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                        taxes: lineTaxes,
                    } as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine;
                })
                .toArray(),
            taxes: documentTaxes,
            sourceLines,
        },
    ];
    return { accountingStagingCommonPayload, notificationsPayload };
}

// UPDATES

/**
 * Create the notifications for the update of a journal entry and ar invoice created from a sales invoice
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @param documentType the documentType for the notification
 * @param replyTopic the reply topic for the notifications
 * @return The number of notifications created
 */
export async function salesInvoiceUpdateNotification(
    context: Context,
    targetDocumentTypes: xtremFinanceData.enums.TargetDocumentType[],
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[],
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    replyTopic: string,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate[] = await asyncArray(
        targetDocumentTypes,
    )
        .map(async targetDocumentType => {
            const financeIntegrationRecord = await context
                .query(xtremFinanceData.nodes.FinanceTransaction, {
                    filter: { documentSysId: document._id, documentType, targetDocumentType },
                })
                .at(0);

            return {
                batchId: (await financeIntegrationRecord?.batchId) || '',
                documentSysId: (await financeIntegrationRecord?.documentSysId) || 0,
                documentNumber: (await financeIntegrationRecord?.documentNumber) || '',
                financialSiteSysId: (await document.financialSite)._id,
                batchSize: 0,
                documentType,
                targetDocumentType,
                documentLines: (await asyncArray(lines)
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedDimensions: (await line.storedDimensions) || {},
                            storedAttributes: {
                                ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                                ...((await line.computedAttributes) as {}),
                            },
                        };
                    })
                    .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
            };
        })
        .toArray();

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic,
        isUpdate: true,
    });
}

function isPurchaseDocument(
    document:
        | xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
        | xtremFinanceData.interfaces.SalesFinanceDocumentWithTax,
): document is xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax {
    return _.hasIn(document, 'supplierDocumentDate') || _.hasIn(document, 'supplierDocumentNumber');
}

/**
 * Create the notifications for the update of a journal entry and ar invoice created from a purchase invoice
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @param documentType the documentType for the notification
 * @param replyTopic the reply topic for the notifications
 * @return The number of notifications created
 */
export async function purchaseSalesInvoiceCreditMemoUpdateNotification(
    context: Context,
    params: {
        targetDocumentTypes: xtremFinanceData.enums.TargetDocumentType[];
        document:
            | xtremFinanceData.interfaces.PurchaseFinanceDocumentWithTax
            | xtremFinanceData.interfaces.SalesFinanceDocumentWithTax;
        lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineBase[];
        documentType: xtremFinanceData.enums.FinanceDocumentType;
        replyTopic: string;
    },
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocumentUpdate[] = await context
        .query(xtremFinanceData.nodes.FinanceTransaction, {
            filter: {
                documentSysId: params.document._id,
                documentType: params.documentType,
                targetDocumentType: { _in: params.targetDocumentTypes },
            },
        })
        .map(async financeIntegrationRecord => {
            const purchaseData = isPurchaseDocument(params.document)
                ? {
                      supplierDocumentDate: (await params.document.supplierDocumentDate)?.toString(),
                      supplierDocumentNumber: await params.document.supplierDocumentNumber,
                  }
                : {};
            return {
                ...purchaseData,
                batchId: (await financeIntegrationRecord?.batchId) ?? '',
                documentSysId: (await financeIntegrationRecord?.documentSysId) ?? 0,
                documentNumber: (await financeIntegrationRecord?.documentNumber) ?? '',
                financialSiteSysId: (await financeIntegrationRecord?.financialSite)?._id ?? 0,
                batchSize: 0,
                documentType: params.documentType,
                targetDocumentType: await financeIntegrationRecord.targetDocumentType,
                dueDate: (await params.document.dueDate).toString(),
                paymentTerm: await params.document.paymentTerm,
                documentLines: (await asyncArray(params.lines)
                    .map(async line => {
                        return {
                            baseDocumentLineSysId: line._id,
                            storedDimensions: (await line.storedDimensions) || {},
                            storedAttributes: {
                                ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                                ...((await line.computedAttributes) as {}),
                            },
                        };
                    })
                    .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLineUpdate[],
            };
        })
        .toArray();

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload,
        replyTopic: params.replyTopic,
        isUpdate: true,
    });
}

/**
 * Gets a notification payload needed for the finance integration of a Stock Transfer Shipment
 * @param document An object that implements StockTransferFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLineWithStock to get line related properties from a document
 */
export async function getStockTransferShipmentNotificationPayload(
    document: xtremFinanceData.interfaces.FinanceOriginBaseDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
    sourceDocumentNumber: string,
    documentNumber?: string, // this allows us to provide a dummy number when we are controlling a stock transfer shipment that is not created yet (like from an order)
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({
            document,
            isJustChecking,
            number: documentNumber,
        });
    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'stockTransferShipment',
            targetDocumentType: 'journalEntry',
            documentLines: (await asyncArray(lines)
                .filter(async line => (await line.item).isStockManaged)
                .map(async line => {
                    const movementAmount: number = -(await xtremFinanceData.functions.getStockMovementsAmounts(line))
                        .movementAmount;
                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'stockJournal',
                        sourceDocumentNumber,
                        sourceDocumentType: 'stockTransferOrder',
                        fxRateDate: ((await document.fxRateDate) || (await document.documentDate)).toString(),
                        currencySysId: (await (await (await document.financialSite).legalCompany).currency)._id, // for 'stockJournal' take currency of financial site
                        companyFxRate: 1.0, // no currency conversion for 'stockJournal'
                        companyFxRateDivisor: 1.0,
                        itemSysId: (await line.item)._id,
                        customerSysId: document.billToCustomer ? (await document.billToCustomer)._id : undefined,
                        amounts: [
                            { amountType: 'inTransitAmount', amount: movementAmount, documentLineType: 'documentLine' },
                        ],
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    };
                })
                .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[],
        },
    ];

    return notificationsPayload;
}

/**
 * Creates all the notifications for the finance integration of a Stock Transfer Shipment
 * @param context Context
 * @param document An object that implements SalesFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLineWithStock to get line related properties from a document
 * @return The number of notifications created
 */
export async function stockTransferShipmentNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginBaseDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
    sourceDocumentNumber: string,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getStockTransferShipmentNotificationPayload(document, lines, sourceDocumentNumber),
        replyTopic: 'StockTransferShipment/accountingInterface',
        isUpdate: false,
    });
}

/**
 * Gets a notification payload needed for the finance integration of a Stock Transfer Receipt
 * @param document An object that implements StockTransferFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLineWithStock to get line related properties from a document
 */
export async function getStockTransferReceiptNotificationPayload(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginBaseDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
    sourceDocumentNumber: string,
    documentNumber?: string, // this allows us to provide a dummy number when we are controlling a stock transfer shipment that is not created yet (like from an order)
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({
            document,
            isJustChecking,
            number: documentNumber,
        });

    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] = [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType: 'stockTransferReceipt',
            targetDocumentType: 'journalEntry',
            documentLines: (await asyncArray(lines)
                .filter(async line => (await line.item).isStockManaged)
                .map(async line => {
                    const { movementAmount, orderAmount } =
                        await xtremFinanceData.functions.getStockMovementsAmounts(line);

                    const itemSite = await xtremFinanceData.functions.getItemSite(
                        context,
                        await document.site,
                        await line.item,
                    );

                    const lineAmount: number = (await xtremFinanceData.functions.getStockMovementsAmounts(line))
                        .orderAmount;

                    const amounts: xtremFinanceData.interfaces.AccountingStagingAmount[] = [
                        { amountType: 'inTransitAmount', amount: lineAmount, documentLineType: 'documentLine' },
                    ]; // always create an amount of type 'amount'

                    const valuationMethod = itemSite ? await itemSite.valuationMethod : 'standardCost';
                    // for stock transfer receipt we need an additional amount of type 'varianceAmount' if 'standardCost' and
                    // orderAmount different from movementAmount
                    if (valuationMethod === 'standardCost' && orderAmount !== movementAmount) {
                        const varAmount = orderAmount - movementAmount;
                        amounts.push({
                            amountType: 'inTransitVarianceAmount',
                            amount: varAmount,
                            documentLineType: 'documentLine',
                        });
                    }

                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'stockJournal',
                        sourceDocumentNumber,
                        sourceDocumentType: 'stockTransferOrder',
                        fxRateDate: ((await document.fxRateDate) || (await document.documentDate)).toString(),
                        currencySysId: (await (await (await document.financialSite).legalCompany).currency)._id, // for 'stockJournal' take currency of financial site
                        companyFxRate: 1.0, // no currency conversion for 'stockJournal'
                        companyFxRateDivisor: 1.0,
                        itemSysId: (await line.item)._id,
                        customerSysId: document.billToCustomer ? (await document.billToCustomer)._id : undefined,
                        amounts,
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    };
                })
                .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[],
        },
    ];

    return notificationsPayload;
}

/**
 * Creates all the notifications for the finance integration of a Stock Transfer Shipment
 * @param context Context
 * @param document An object that implements SalesFinanceDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLineWithStock to get line related properties from a document
 * @return The number of notifications created
 */
export async function stockTransferReceiptNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginBaseDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLineWithStock[],
    sourceDocumentNumber: string,
): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context,
        notificationsPayload: await getStockTransferReceiptNotificationPayload(
            context,
            document,
            lines,
            sourceDocumentNumber,
        ),
        replyTopic: 'StockTransferReceipt/accountingInterface',
        isUpdate: false,
    });
}
