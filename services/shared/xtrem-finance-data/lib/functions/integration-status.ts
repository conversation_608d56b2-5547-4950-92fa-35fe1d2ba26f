import type * as xtremCommunication from '@sage/xtrem-communication';
import type { Context } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import type * as xtremSystem from '@sage/xtrem-system';
import * as xtremFinanceData from '..';

export async function getDocumentIntegrationStatus(
    context: Context,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentNumber: string,
): Promise<xtremFinanceData.enums.FinanceIntegrationStatus> {
    let financeIntegrationStatus: xtremFinanceData.enums.FinanceIntegrationStatus = 'toBeRecorded';

    const financeIntegrationRecords = await context
        .query(xtremFinanceData.nodes.FinanceTransaction, {
            filter: { documentType, documentNumber },
            orderBy: { status: +1 },
        })
        .toArray();

    if (financeIntegrationRecords.length) {
        const errorStatus: xtremFinanceData.enums.FinanceIntegrationStatus[] = ['error', 'notRecorded', 'failed'];
        const financeIntegrationErrors = await asyncArray(financeIntegrationRecords)
            .filter(async financeIntegrationRecord => errorStatus.includes(await financeIntegrationRecord.status))
            .toArray();
        if (financeIntegrationErrors.length) {
            financeIntegrationStatus = await (
                await asyncArray(financeIntegrationErrors).reduce(async (previous, current) => {
                    if ((await previous.status) === 'error') {
                        return previous;
                    }
                    if ((await previous.status) === 'notRecorded' && (await current.status) !== 'error') {
                        return previous;
                    }
                    return current;
                })
            ).status;
        } else {
            financeIntegrationStatus = await financeIntegrationRecords[0].status;
        }
    }

    return financeIntegrationStatus;
}

export function getFinanceIntegrationStatusFromFinanceIntegrationAppStatus(
    financeIntegrationAppStatus: xtremCommunication.enums.IntegrationState,
): xtremFinanceData.enums.FinanceIntegrationStatus {
    switch (financeIntegrationAppStatus) {
        case 'success':
            return 'posted';
        case 'error':
            return 'failed';
        case 'pending':
            return 'submitted';
        case 'not':
        default:
            return 'recorded';
    }
}

export async function getFinanceTransactionFromOrigin(
    context: Context,
    documentSysId: number,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
): Promise<xtremFinanceData.nodes.FinanceTransaction | null> {
    const financeIntegrationRecords = await context
        .query(xtremFinanceData.nodes.FinanceTransaction, {
            filter: { documentSysId, documentType },
            orderBy: { status: -1 },
        })
        .toArray();

    return financeIntegrationRecords.at(0) || null;
}

export async function getFinanceTransaction(
    context: Context,
    targetDocumentNumber: string,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
): Promise<xtremFinanceData.nodes.FinanceTransaction | null> {
    if (!targetDocumentNumber.length) {
        return null;
    }

    const financeIntegrationRecords = await context
        .query(xtremFinanceData.nodes.FinanceTransaction, {
            filter: { targetDocumentNumber, targetDocumentType },
            orderBy: { status: -1 },
        })
        .toArray();

    return financeIntegrationRecords.length ? financeIntegrationRecords[0] : null;
}

export async function getFinanceIntegrationApp(
    context: Context,
    targetDocumentNumber: string,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
): Promise<xtremFinanceData.enums.FinanceIntegrationApp | null> {
    return (
        (await (
            await getFinanceTransaction(context, targetDocumentNumber, targetDocumentType)
        )?.financeIntegrationApp) || null
    );
}

export async function getFinanceIntegrationAppFromOrigin(
    context: Context,
    documentSysId: number,
    documentType: xtremFinanceData.enums.FinanceDocumentType,
): Promise<xtremFinanceData.enums.FinanceIntegrationApp | null> {
    return (
        (await (await getFinanceTransactionFromOrigin(context, documentSysId, documentType))?.financeIntegrationApp) ||
        null
    );
}

export async function getFinanceIntegrationAppRecordId(
    context: Context,
    targetDocumentNumber: string,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
): Promise<string> {
    return (
        (await (
            await getFinanceTransaction(context, targetDocumentNumber, targetDocumentType)
        )?.financeIntegrationAppRecordId) || ''
    );
}

export async function getFinanceIntegrationAppUrl(
    context: Context,
    targetDocumentNumber: string,
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType,
): Promise<string> {
    return (
        (await (
            await getFinanceTransaction(context, targetDocumentNumber, targetDocumentType)
        )?.financeIntegrationAppUrl) || ''
    );
}

export async function getPostingStatusFromFinanceIntegrationStatus(
    context: Context,
    params: {
        financeIntegrationStatus: xtremFinanceData.enums.FinanceIntegrationStatus;
        externalIntegration: boolean;
        financialSite: xtremSystem.nodes.Site;
        documentType: xtremFinanceData.enums.FinanceDocumentType;
        targetDocumentType: xtremFinanceData.enums.TargetDocumentType;
    },
): Promise<xtremFinanceData.enums.PostingStatus> {
    const journalEntryType = await xtremFinanceData.functions.AccountingEngineCommon.getJournalEntryType(
        context,
        await (
            await params.financialSite.legalCompany
        ).legislation,
        params.documentType,
        params.targetDocumentType,
    );

    switch (params.financeIntegrationStatus) {
        case 'toBeRecorded': // document was not even posted, there's no record on the finance transaction node
            return 'notPosted';
        case 'recording': // initial state, a notification was created to the accounting engine
            return (await journalEntryType?.immediatePosting) ? 'generationInProgress' : 'toBeGenerated';
        case 'pending': // notification received by the accounting engine, a record was created on the accounting staging node
            return (await journalEntryType?.immediatePosting) ? 'generationInProgress' : 'toBeGenerated';
        case 'error': // notification received by the accounting engine, but it was not possible to create a record on the accounting staging node
            return 'generationError';
        case 'recorded': // accounting engine processed the record from the accounting staging node and a finance document
            // (pre journal, accounts payable or accounts receivable invoice) was created
            // set status to 'generated' if exteral integration with either FRP1000 or Intacct, else to 'posted'
            return params.externalIntegration ? 'generated' : 'posted';
        case 'notRecorded': // accounting engine processed the record from the accounting staging node but is was not possible
            // to create a finance document (pre journal, accounts payable or accounts receivable invoice)
            return 'generationError';
        case 'submitted': // the finance document (pre journal, accounts payable or accounts receivable invoice) was sent to a
            // third party software (intacct? frp100?)
            return 'postingInProgress';
        case 'posted': // the finance document (pre journal, accounts payable or accounts receivable invoice) was sucessfully created
            // on the third party software (intacct? frp100?)
            return 'posted';
        case 'failed': // it was not possible to create the finance document (pre journal, accounts payable or accounts receivable invoice)
            // on the third party software (intacct? frp100?)
            return 'postingError';
        default:
            return 'notPosted';
    }
}

/**
 * Does the mapping between an internal finance posting status to the document status for the
 * finance integration of a Sales or Purchasing document or an AP/AR invoice
 * @param financeIntegrationStatus the internal finance posting status
 * @param financeExternalIntegration A boolean that indicates whether en external application is integrated (intacct/frp1000)
 * @return the mapped document posting status (inProgress, error, posted)
 */
export function statusMapping(
    financeIntegrationStatus: xtremFinanceData.enums.FinanceIntegrationStatus,
    financeExternalIntegration: boolean,
) {
    switch (financeIntegrationStatus) {
        case 'toBeRecorded': // document was not even posted, there's no record on the finance transaction node
            return 'inProgress';
        case 'recording': // initial state, a notification was created to the accounting engine
            return 'inProgress';
        case 'pending': // notification received by the accounting engine, a record was created on the accounting staging node
            return 'inProgress';
        case 'error': // notification received by the accounting engine, but it was not possible to create a record on the accounting staging node
            return 'error';
        case 'recorded': // accounting engine processed the record from the accounting staging node and a finance document
            // (pre journal, accounts payable or accounts receivable invoice) was created
            // set status to 'inProgress' if exteral integration with either FRP1000 or Intacct, else to 'posted'
            return financeExternalIntegration ? 'inProgress' : 'posted';
        case 'notRecorded': // accounting engine processed the record from the accounting staging node but is was not possible
            // to create a finance document (pre journal, accounts payable or accounts receivable invoice)
            return 'error';
        case 'submitted': // the finance document (pre journal, accounts payable or accounts receivable invoice) was sent to a
            // third party software (intacct? frp100?)
            return 'inProgress';
        case 'posted': // the finance document (pre journal, accounts payable or accounts receivable invoice) was sucessfully
            // created on the third party software (intacct? frp100?)
            return 'posted';
        case 'failed': // it was not possible to create the finance document (pre journal, accounts payable or accounts receivable invoice)
            // on the third party software (intacct? frp100?)
            return 'error';
        default:
            return 'inProgress';
    }
}

/**
 * Does the mapping between an internal finance posting status to the document status for the
 * finance integration of an AP/AR invoice
 * @param financeIntegrationStatus The internal finance posting status
 * @param financeExternalIntegration A boolean that indicates whether en external application is integrated (intacct/frp1000)
 * @param currentStatus The current status of the document
 * @return the mapped document posting status (inProgress, error, posted)
 */
export function financeStatusMapping(
    financeIntegrationStatus: xtremFinanceData.enums.FinanceIntegrationStatus,
    financeExternalIntegration: boolean,
    currentStatus: xtremFinanceData.enums.JournalStatus,
) {
    switch (financeIntegrationStatus) {
        case 'toBeRecorded': // document was not even posted, there's no record on the finance transaction node
            return currentStatus;
        case 'recording': // initial state, a notification was created to the accounting engine
            return currentStatus;
        case 'pending': // notification received by the accounting engine, a record was created on the accounting staging node
            return currentStatus;
        case 'error': // notification received by the accounting engine, but it was not possible to create a record on the accounting staging node
            return 'error';
        case 'recorded': // accounting engine processed the record from the accounting staging node and a finance document
            // (pre journal, accounts payable or accounts receivable invoice) was created
            // set status to 'inProgress' if exteral integration with either FRP1000 or Intacct, else to 'posted'
            return financeExternalIntegration ? 'inProgress' : 'posted';
        case 'notRecorded': // accounting engine processed the record from the accounting staging node but is was not possible
            // to create a finance document (pre journal, accounts payable or accounts receivable invoice)
            return 'error';
        case 'submitted': // the finance document (pre journal, accounts payable or accounts receivable invoice) was sent to a
            // third party software (intacct? frp100?)
            return currentStatus;
        case 'posted': // the finance document (pre journal, accounts payable or accounts receivable invoice) was sucessfully
            // created on the third party software (intacct? frp100?)
            return 'posted';
        case 'failed': // it was not possible to create the finance document (pre journal, accounts payable or accounts receivable invoice)
            // on the third party software (intacct? frp100?)
            return 'error';
        default:
            return currentStatus;
    }
}

export function canUpdateFinanceTransactionStatus(
    newStatus: xtremFinanceData.enums.FinanceIntegrationStatus,
    previousStatus: xtremFinanceData.enums.FinanceIntegrationStatus,
): boolean {
    switch (previousStatus) {
        case 'pending':
            return !['recording', 'error'].includes(newStatus);
        case 'error':
            return !['recording', 'pending'].includes(newStatus);
        case 'recorded':
            return !['recording', 'pending', 'error', 'notRecorded'].includes(newStatus);
        case 'notRecorded':
            return !['recording', 'pending', 'error'].includes(newStatus);
        case 'submitted':
            return !['recording', 'pending', 'error', 'recorded', 'notRecorded'].includes(newStatus);
        case 'posted':
            return !['recording', 'pending', 'error', 'recorded', 'notRecorded', 'failed'].includes(newStatus);
        case 'failed':
            return !['recording', 'pending', 'error', 'recorded', 'notRecorded'].includes(newStatus);
        default:
            return true;
    }
}

export function canRepost(financeIntegrationStatus: xtremFinanceData.enums.FinanceIntegrationStatus): boolean {
    return ['failed', 'notRecorded'].includes(financeIntegrationStatus);
}
