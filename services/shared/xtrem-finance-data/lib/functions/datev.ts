import type { Context, integer } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '../index';

export interface DatevConfiguration {
    accountLength: integer;
    customerRangeStart: integer;
    customerRangeEnd: integer;
    supplierRangeStart: integer;
    supplierRangeEnd: integer;
}

// check if all accounts have a DATEV ID with the correct length
export async function checkCorrectDatevIdLengthOnAccounts(
    context: Context,
    datevConfiguration: xtremFinanceData.functions.datev.DatevConfiguration,
): Promise<string> {
    if (
        datevConfiguration.accountLength &&
        (await context.queryCount(xtremFinanceData.nodes.Account, {
            filter: {
                _and: [
                    { datevId: { _ne: null } },
                    {
                        _or: [
                            { datevId: { _lt: 10 ** (datevConfiguration.accountLength - 1) } },
                            { datevId: { _gte: 10 ** datevConfiguration.accountLength } },
                        ],
                    },
                ],
            },
        })) > 0
    ) {
        return context.localize(
            '@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_account_datev_id_length',
            'The DATEV ID length for certain accounts does not match the account length. They need to be the same.',
        );
    }
    return '';
}

// check if all customers have a DATEV ID with the correct length
export async function checkCorrectDatevIdLengthOnCustomers(
    context: Context,
    datevConfiguration: xtremFinanceData.functions.datev.DatevConfiguration,
): Promise<string> {
    if (
        datevConfiguration.accountLength &&
        (await context.queryCount(xtremMasterData.nodes.Customer, {
            filter: {
                _and: [
                    { datevId: { _ne: null } },
                    {
                        _or: [
                            { datevId: { _lt: 10 ** datevConfiguration.accountLength } },
                            { datevId: { _gte: 10 ** (datevConfiguration.accountLength + 1) } },
                        ],
                    },
                ],
            },
        })) > 0
    ) {
        return context.localize(
            '@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_length',
            'The DATEV ID length for certain customers does not match the customer and supplier ID length. They need to be the same.',
        );
    }
    return '';
}

// check if all customers have a DATEV ID in the correct range
export async function checkCorrectDatevIdRangeOnCustomers(
    context: Context,
    datevConfiguration: xtremFinanceData.functions.datev.DatevConfiguration,
): Promise<string> {
    const customerCount = await context.queryCount(xtremMasterData.nodes.Customer, {
        filter: {
            _and: [
                { datevId: { _ne: null } },
                {
                    _or: [
                        { datevId: { _lt: datevConfiguration.customerRangeStart } },
                        { datevId: { _gt: datevConfiguration.customerRangeEnd } },
                    ],
                },
            ],
        },
    });
    if (customerCount > 0) {
        return context.localize(
            '@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_customer_datev_id_range',
            'There are DATEV IDs for certain customers that are outside the customer ID range. They need to be within the range.',
        );
    }
    return '';
}

// check if all suppliers have a DATEV ID with the correct length
export async function checkCorrectDatevIdLengthOnSuppliers(
    context: Context,
    datevConfiguration: xtremFinanceData.functions.datev.DatevConfiguration,
): Promise<string> {
    if (
        (await context.queryCount(xtremMasterData.nodes.Supplier, {
            filter: {
                _and: [
                    { datevId: { _ne: null } },
                    {
                        _or: [
                            { datevId: { _lt: 10 ** datevConfiguration.accountLength } },
                            { datevId: { _gte: 10 ** (datevConfiguration.accountLength + 1) } },
                        ],
                    },
                ],
            },
        })) > 0
    ) {
        return context.localize(
            '@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_length',
            'The DATEV ID length for certain suppliers does not match the customer and supplier ID length. They need to be the same.',
        );
    }
    return '';
}

// check if all suppliers have a DATEV ID in the correct range
export async function checkCorrectDatevIdRangeOnSuppliers(
    context: Context,
    datevConfiguration: xtremFinanceData.functions.datev.DatevConfiguration,
): Promise<string> {
    const supplierCount = await context.queryCount(xtremMasterData.nodes.Supplier, {
        filter: {
            _and: [
                { datevId: { _ne: null } },
                {
                    _or: [
                        { datevId: { _lt: datevConfiguration.supplierRangeStart } },
                        { datevId: { _gt: datevConfiguration.supplierRangeEnd } },
                    ],
                },
            ],
        },
    });
    if (supplierCount > 0) {
        return context.localize(
            '@sage/xtrem-finance-data/nodes__datev_configuration__accounts_with_wrong_supplier_datev_id_range',
            'There are DATEV IDs for certain suppliers that are outside the supplier ID range. They need to be within the range.',
        );
    }
    return '';
}

// return DATEV configuration
export function getDatevConfiguration(context: Context): Promise<xtremFinanceData.nodes.DatevConfiguration> {
    return context.read(xtremFinanceData.nodes.DatevConfiguration, { _id: '#DATEV' });
}
