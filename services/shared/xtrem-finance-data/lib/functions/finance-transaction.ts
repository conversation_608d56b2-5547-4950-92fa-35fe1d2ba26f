import { asyncArray } from '@sage/xtrem-core';
import type * as xtremSynchronization from '@sage/xtrem-synchronization';
import type * as xtremFinanceData from '..';
import { getDocumentPageNameFinance } from '../shared-functions/get-page';

async function getNumberAndType(
    transaction: xtremFinanceData.nodes.FinanceTransaction,
): Promise<{ number: string; type: string }> {
    if ((await transaction.documentType) === 'workInProgress') {
        return { number: await transaction.sourceDocumentNumber, type: await transaction.sourceDocumentType };
    }

    return { number: await transaction.documentNumber, type: await transaction.documentType };
}

export async function getDocumentNumberLink(
    transaction: xtremFinanceData.nodes.FinanceTransaction,
): Promise<xtremSynchronization.sharedFunctions.LinkToPage> {
    const { number } = await getNumberAndType(transaction);

    return {
        text: number,
        page: getDocumentPageNameFinance({
            type: await transaction.documentType,
            sourceType: await transaction.sourceDocumentType,
        }),
        parameters: { _id: await transaction.documentSysId },
    };
}

export function getPostingStatusDocumentData(
    financeTransactionRecords: xtremFinanceData.nodes.FinanceTransaction[],
): Promise<Array<xtremFinanceData.interfaces.FinancePostingStatusData>> {
    // loop over the array of found finance transaction records and init correct result values
    return asyncArray(financeTransactionRecords)
        .map(async (resultRecord: xtremFinanceData.nodes.FinanceTransaction) => {
            const data = {
                _id: resultRecord._id.toString(),
                documentType: await resultRecord.targetDocumentType,
                documentNumber: await resultRecord.targetDocumentNumber,
                documentSysId: await resultRecord.targetDocumentSysId,
                status: await resultRecord.postingStatus,
                message: await resultRecord.message,
                hasFinanceIntegrationApp: (await resultRecord.financeIntegrationApp) !== null,
                financeIntegrationApp: await resultRecord.financeIntegrationApp,
                financeIntegrationAppRecordId: await resultRecord.financeIntegrationAppRecordId,
                financeIntegrationAppUrl: await resultRecord.financeIntegrationAppUrl,
                externalLink: (await resultRecord.financeIntegrationApp) === 'intacct',
                hasSourceForDimensionLines: await resultRecord.hasSourceForDimensionLines,
            };
            return (await resultRecord.documentType) === 'workInProgress'
                ? { ...data, sourceDocumentType: await resultRecord.sourceDocumentType }
                : data;
        })
        .toArray();
}
