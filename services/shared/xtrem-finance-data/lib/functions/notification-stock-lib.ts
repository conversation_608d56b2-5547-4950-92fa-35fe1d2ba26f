import type { Context } from '@sage/xtrem-core';
import { asyncArray } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremFinanceData from '..';

/**
 * General part to create the notification payload for the finance integration of a stock document
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @param documentType the documentType for the notification
 * @param documentLineType base document line
 * @param withItemSite boolean to decide whether to get an ItemSite or to use null for amount determination
 * @return The number of notifications created
 */
export async function getStockNotificationPayload(
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
    documentType: xtremFinanceData.enums.FinanceDocumentType,
    documentLineType: string,
    isJustChecking = false,
): Promise<xtremFinanceData.interfaces.FinanceIntegrationDocument[]> {
    const accountingStagingCommonPayload: xtremFinanceData.interfaces.AccountingStagingCommonPayload =
        await xtremFinanceData.functions.getAccountingStagingCommonPayload({ document, isJustChecking });

    return [
        {
            ...accountingStagingCommonPayload,
            batchSize: 0,
            documentType,
            targetDocumentType: 'journalEntry',
            documentLines: (await asyncArray(lines)
                .filter(async line => (await line.item).isStockManaged)
                .map(async line => {
                    const { movementAmount } = await xtremFinanceData.functions.getStockMovementsAmounts(line);
                    const lineAmount = xtremFinanceData.functions.getStockLineAmount(documentLineType, movementAmount);

                    return {
                        baseDocumentLineSysId: line._id,
                        movementType: 'stockJournal',
                        sourceDocumentNumber: '',
                        currencySysId: (await document.transactionCurrency)._id,
                        companyFxRate: 1,
                        companyFxRateDivisor: 1,
                        fxRateDate: (await document.documentDate).toString(),
                        itemSysId: (await line.item)._id,
                        amounts: [{ amountType: 'amount', amount: lineAmount, documentLineType: 'documentLine' }],
                        storedDimensions: (await line.storedDimensions) || {},
                        storedAttributes: {
                            ...((await line.storedAttributes) as xtremMasterData.interfaces.StoredAttributes),
                            ...((await line.computedAttributes) as {}),
                        },
                    };
                })
                .toArray()) as xtremFinanceData.interfaces.FinanceIntegrationDocumentLine[],
        },
    ];
}

/**
 * General part to create all the notifications for the finance integration of a stock document
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @param documentType the documentType for the notification
 * @param documentLineType base document line
 * @param withItemSite boolean to decide whether to get an ItemSite or to use null for amount determination
 * @param replyTopic the reply topic for the notifications
 * @return The number of notifications created
 */
async function stockNotification(options: {
    context: Context;
    document: xtremFinanceData.interfaces.FinanceOriginDocument;
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[];
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    documentLineType: string;
    replyTopic: string;
}): Promise<xtremFinanceData.interfaces.FinanceOriginDocumentPostReturn> {
    const notificationsPayload: xtremFinanceData.interfaces.FinanceIntegrationDocument[] =
        await getStockNotificationPayload(
            options.document,
            options.lines,
            options.documentType,
            options.documentLineType,
        );

    return xtremFinanceData.functions.sendNotificationsToAccountingEngine({
        context: options.context,
        notificationsPayload,
        replyTopic: options.replyTopic,
        isUpdate: false,
    });
}

/**
 * Creates all the notifications for the finance integration of a Stock Issue
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export function stockIssueNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
) {
    return stockNotification({
        context,
        document,
        lines,
        documentType: 'miscellaneousStockIssue',
        documentLineType: 'StockIssueLine',
        replyTopic: 'StockIssue/accountingInterface',
    });
}

/**
 * Creates all the notifications for the finance integration of a Stock Receipt
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export function stockReceiptNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
) {
    return stockNotification({
        context,
        document,
        lines,
        documentType: 'miscellaneousStockReceipt',
        documentLineType: 'StockReceiptLine',
        replyTopic: 'StockReceipt/accountingInterface',
    });
}

/**
 * Creates all the notifications for the finance integration of a Stock Adjustment
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export function stockAdjustmentNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
) {
    return stockNotification({
        context,
        document,
        lines,
        documentType: 'stockAdjustment',
        documentLineType: 'StockAdjustmentLine',
        replyTopic: `${document.constructor.name}/accountingInterface`,
    });
}

/**
 * Creates all the notifications for the finance integration of a Stock Adjustment
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export function stockCountAdjustmentNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
) {
    return stockNotification({
        context,
        document,
        lines,
        documentType: 'stockCount',
        documentLineType: 'StockCountLine',
        replyTopic: `${document.constructor.name}/accountingInterface`,
    });
}

/**
 * Creates all the notifications for the finance integration of a Stock Value Change
 * @param context Context
 * @param document An object that implements FinanceOriginDocument to get header related properties from a document
 * @param lines An array of objects that implements FinanceOriginDocumentLine to get line related properties from a document
 * @return The number of notifications created
 */
export function stockValueChangeNotification(
    context: Context,
    document: xtremFinanceData.interfaces.FinanceOriginDocument,
    lines: xtremFinanceData.interfaces.FinanceOriginDocumentLine[],
) {
    return stockNotification({
        context,
        document,
        lines,
        documentType: 'stockValueChange',
        documentLineType: 'StockValueChangeLine',
        replyTopic: `${document.constructor.name}/accountingInterface`,
    });
}
