import { BusinessRuleError, ServiceOption } from '@sage/xtrem-core';
import * as xtremStructure from '@sage/xtrem-structure';

export const paymentTrackingOption = new ServiceOption({
    __filename,
    status: 'released',
    description: 'Payment tracking option',
    isSubscribable: false,
    isHidden: false,
    notifies: true,
    async onEnabled(context) {
        if (await context.isServiceOptionEnabled(xtremStructure.serviceOptions.intacctActivationOption)) {
            throw new BusinessRuleError(
                context.localize(
                    '@sage/xtrem-finance-data/service_options__payment_tracking_option__intacct_is_active',
                    'Payment tracking is not possible if intacct integration is active.',
                ),
            );
        }
    },
});
