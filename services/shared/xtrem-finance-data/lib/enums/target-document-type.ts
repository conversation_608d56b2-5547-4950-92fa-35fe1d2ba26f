import { EnumDataType } from '@sage/xtrem-core';

export enum TargetDocumentTypeEnum {
    journalEntry,
    accountsReceivableInvoice,
    accountsPayableInvoice,
    accountsReceivableAdvance,
    accountsReceivablePayment,
}

export type TargetDocumentType = keyof typeof TargetDocumentTypeEnum;

export const targetDocumentTypeDataType = new EnumDataType<TargetDocumentType>({
    enum: TargetDocumentTypeEnum,
    filename: __filename,
});
