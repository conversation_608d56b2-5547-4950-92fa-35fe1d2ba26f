import { EnumDataType } from '@sage/xtrem-core';

export enum AccountsPayableReceivableInvoiceLineTypeEnum {
    goods,
    services,
    fixedAssets,
}

export type AccountsPayableReceivableInvoiceLineType = keyof typeof AccountsPayableReceivableInvoiceLineTypeEnum;

export const accountsPayableReceivableInvoiceLineTypeDataType =
    new EnumDataType<AccountsPayableReceivableInvoiceLineType>({
        enum: AccountsPayableReceivableInvoiceLineTypeEnum,
        filename: __filename,
    });
