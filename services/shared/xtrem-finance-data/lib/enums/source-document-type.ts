import { EnumDataType } from '@sage/xtrem-core';

export enum SourceDocumentTypeEnum {
    materialTracking,
    operationTracking,
    productionTracking,
    workOrderClose,
    purchaseOrder,
    purchaseReceipt,
    purchaseReturn,
    purchaseInvoice,
    purchaseCreditMemo,
    salesCreditMemo,
    salesInvoice,
    salesReturnRequest,
    salesShipment,
    stockTransferShipment,
    stockTransferOrder,
    stockTransferReceipt,
}

export type SourceDocumentType = keyof typeof SourceDocumentTypeEnum;

export const sourceDocumentTypeDataType = new EnumDataType<SourceDocumentType>({
    enum: SourceDocumentTypeEnum,
    filename: __filename,
});
