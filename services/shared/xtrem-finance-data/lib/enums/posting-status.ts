import { EnumDataType } from '@sage/xtrem-core';

export enum PostingStatusEnum {
    notPosted,
    generationInProgress,
    generationError,
    generated,
    postingInProgress,
    postingError,
    posted,
    toBeGenerated,
}

export type PostingStatus = keyof typeof PostingStatusEnum;

export const postingStatusDataType = new EnumDataType<PostingStatus>({
    enum: PostingStatusEnum,
    filename: __filename,
});
