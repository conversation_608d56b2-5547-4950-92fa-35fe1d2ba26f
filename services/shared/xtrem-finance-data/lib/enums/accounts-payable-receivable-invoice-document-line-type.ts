import { EnumDataType } from '@sage/xtrem-core';

export enum AccountsPayableReceivableInvoiceDocumentLineTypeEnum {
    documentLine,
    taxLine,
}

export type AccountsPayableReceivableInvoiceDocumentLineType =
    keyof typeof AccountsPayableReceivableInvoiceDocumentLineTypeEnum;

export const accountsPayableReceivableInvoiceDocumentLineTypeDataType =
    new EnumDataType<AccountsPayableReceivableInvoiceDocumentLineType>({
        enum: AccountsPayableReceivableInvoiceDocumentLineTypeEnum,
        filename: __filename,
    });
