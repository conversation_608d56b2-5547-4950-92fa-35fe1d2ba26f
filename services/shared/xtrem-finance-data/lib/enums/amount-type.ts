import { EnumDataType } from '@sage/xtrem-core';

export enum AmountTypeEnum {
    amount,
    varianceAmount,
    amountIncludingTax,
    deductibleTaxAmount,
    nonDeductibleTaxAmount,
    reverseChargeDeductibleTaxAmount,
    reverseChargeNonDeductibleTaxAmount,
    amountExcludingTax,
    taxAmount,
    adjustmentAmount,
    adjustmentNonabsorbedAmount,
    landedCostAdjustmentAmount,
    landedCostAdjustmentNonabsorbedAmount,
    landedCostStockInTransitAmount,
    landedCostStockInTransitAdjustmentAmount,
    landedCostStockInTransitAdjustmentNonabsorbedAmount,
    inTransitAmount,
    inTransitVarianceAmount,
}

export type AmountType = keyof typeof AmountTypeEnum;

export const amountTypeDataType = new EnumDataType<AmountType>({ enum: AmountTypeEnum, filename: __filename });
