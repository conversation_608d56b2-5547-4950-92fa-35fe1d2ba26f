import { EnumDataType } from '@sage/xtrem-core';

export enum AccountsPayableReceivableInvoiceOriginEnum {
    direct,
    invoice,
    creditMemo,
}

export type AccountsPayableReceivableInvoiceOrigin = keyof typeof AccountsPayableReceivableInvoiceOriginEnum;

export const accountsPayableReceivableInvoiceOriginDataType = new EnumDataType<AccountsPayableReceivableInvoiceOrigin>({
    enum: AccountsPayableReceivableInvoiceOriginEnum,
    filename: __filename,
});
