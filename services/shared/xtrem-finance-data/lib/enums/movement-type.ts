import { EnumDataType } from '@sage/xtrem-core';

export enum MovementTypeEnum {
    stockJournal,
    document,
    productionTracking,
    materialTracking,
    laborSetupTimeTracking,
    laborRunTimeTracking,
    machineSetupTimeTracking,
    machineRunTimeTracking,
    toolSetupTimeTracking,
    toolRunTimeTracking,
    workOrderVariance,
    workOrderNegativeVariance,
    workOrderActualCostAdjustment,
    workOrderNegativeActualCostAdjustment,
    workOrderActualCostAdjustmentNonAbsorbed,
    workOrderNegativeActualCostAdjustmentNonAbsorbed,
}

export type MovementType = keyof typeof MovementTypeEnum;

export const movementTypeDataType = new EnumDataType<MovementType>({ enum: MovementTypeEnum, filename: __filename });
