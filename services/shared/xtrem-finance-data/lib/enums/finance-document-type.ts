import { EnumDataType } from '@sage/xtrem-core';

// If this enum changes, review the _getJournalEntryOrigin, _getARInvoiceOrigin functions on xtrem-finance
export enum FinanceDocumentTypeEnum {
    miscellaneousStockReceipt,
    miscellaneousStockIssue,
    purchaseReceipt,
    purchaseInvoice,
    purchaseCreditMemo,
    salesInvoice,
    salesCreditMemo,
    stockAdjustment,
    salesShipment,
    workInProgress,
    apInvoice,
    arInvoice,
    purchaseReturn,
    salesReturnReceipt,
    bankReconciliationWithdrawal,
    bankReconciliationDeposit,
    stockCount,
    stockValueChange,
    stockTransferShipment,
    stockTransferReceipt,
}

export type FinanceDocumentType = keyof typeof FinanceDocumentTypeEnum;

export const financeDocumentTypeDataType = new EnumDataType<FinanceDocumentType>({
    enum: FinanceDocumentTypeEnum,
    filename: __filename,
});
