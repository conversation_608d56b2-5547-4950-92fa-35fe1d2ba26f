import { EnumDataType } from '@sage/xtrem-core';

export enum FinanceIntegrationStatusEnum {
    toBeRecorded, // document was not even posted, there's no record on the finance transaction node
    recording, // initial state, a notification was created to the accounting engine
    pending, // notification received by the accounting engine, a record was created on the accounting staging node
    error, // notification received by the accounting engine, but it was not possible to create a record on the accounting staging node
    recorded, // accounting engine processed the record from the accounting staging node and a finance document (pre journal, accounts payable or accounts receivable invoice) was created
    notRecorded, // accounting engine processed the record from the accounting staging node but is was not possible to create a finance document (pre journal, accounts payable or accounts receivable invoice)
    submitted, // the finance document (pre journal, accounts payable or accounts receivable invoice) was sent to a third party software (intacct? frp100?)
    posted, // the finance document (pre journal, accounts payable or accounts receivable invoice) was sucessfully created on the third party software (intacct? frp100?)
    failed, // it was not possible to create the finance document (pre journal, accounts payable or accounts receivable invoice) on the third party software (intacct? frp100?)
}

export type FinanceIntegrationStatus = keyof typeof FinanceIntegrationStatusEnum;

export const financeIntegrationStatusDataType = new EnumDataType<FinanceIntegrationStatus>({
    enum: FinanceIntegrationStatusEnum,
    filename: __filename,
});
