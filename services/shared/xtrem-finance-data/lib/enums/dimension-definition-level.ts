import { EnumDataType } from '@sage/xtrem-core';

export enum DimensionDefinitionLevelEnum {
    manufacturingDirect,
    salesDirect,
    stockDirect,
    purchasingDirect,
    manufacturingOrderToOrder,
    purchasingOrderToOrder,
    intersiteTransferOrder,
}

export type DimensionDefinitionLevel = keyof typeof DimensionDefinitionLevelEnum;

export const dimensionDefinitionLevelDataType = new EnumDataType<DimensionDefinitionLevel>({
    enum: DimensionDefinitionLevelEnum,
    filename: __filename,
});
