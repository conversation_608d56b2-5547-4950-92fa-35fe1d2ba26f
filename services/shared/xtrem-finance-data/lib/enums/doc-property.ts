import { EnumDataType } from '@sage/xtrem-core';

export enum DocPropertyEnum {
    dimensionType01 = 1,
    dimensionType02 = 2,
    dimensionType03 = 3,
    dimensionType04 = 4,
    dimensionType05 = 5,
    dimensionType06 = 6,
    dimensionType07 = 7,
    dimensionType08 = 8,
    dimensionType09 = 9,
    dimensionType10 = 10,
    dimensionType11 = 11,
    dimensionType12 = 12,
    dimensionType13 = 13,
    dimensionType14 = 14,
    dimensionType15 = 15,
    dimensionType16 = 16,
    dimensionType17 = 17,
    dimensionType18 = 18,
    dimensionType19 = 19,
    dimensionType20 = 20,
}

export type DocProperty = keyof typeof DocPropertyEnum;

export const docPropertyDataType = new EnumDataType<DocProperty>({ enum: DocPropertyEnum, filename: __filename });
