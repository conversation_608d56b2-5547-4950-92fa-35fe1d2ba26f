import type * as xtremMasterData from '@sage/xtrem-master-data';

export interface MutationResult {
    wasSuccessful: boolean;
    message: string;
    validationMessages?: xtremMasterData.sharedFunctions.interfaces.FinanceIntegration.LineValidationMessage[];
}

export interface FinanceItemTypeOptions {
    isStockItemAllowed: boolean;
    isNonStockItemAllowed: boolean;
    isServiceItemAllowed: boolean;
    isLandedCostItemAllowed: boolean;
}

export interface LineWithFinanceItemType extends FinanceItemTypeOptions {
    definition: FinanceItemTypeOptions;
}
