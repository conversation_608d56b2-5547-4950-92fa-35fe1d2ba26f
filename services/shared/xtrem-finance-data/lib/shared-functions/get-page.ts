enum FinanceDocumentTypeEnum {
    miscellaneousStockReceipt,
    miscellaneousStockIssue,
    purchaseReceipt,
    purchaseInvoice,
    purchaseCreditMemo,
    salesInvoice,
    salesCreditMemo,
    stockAdjustment,
    salesShipment,
    workInProgress,
    apInvoice,
    arInvoice,
    purchaseReturn,
    salesReturnReceipt,
    bankReconciliationWithdrawal,
    bankReconciliationDeposit,
    stockCount,
    stockValueChange,
}
type FinanceDocumentType = keyof typeof FinanceDocumentTypeEnum;

enum SourceDocumentTypeEnum {
    materialTracking,
    operationTracking,
    productionTracking,
    workOrderClose,
    purchaseOrder,
    purchaseReceipt,
    purchaseReturn,
    purchaseInvoice,
    purchaseCreditMemo,
    salesCreditMemo,
    salesInvoice,
    salesReturnRequest,
    salesShipment,
    stockTransferShipment,
    stockTransferReceipt,
    stockTransferOrder,
}
type SourceDocumentType = keyof typeof SourceDocumentTypeEnum;

enum TargetDocumentTypeEnum {
    journalEntry,
    accountsReceivableInvoice,
    accountsPayableInvoice,
    accountsReceivableAdvance,
    accountsReceivablePayment,
}

type TargetDocumentType = keyof typeof TargetDocumentTypeEnum;

function getSourceDocumentPageNameFinance(sourceType: SourceDocumentType) {
    switch (sourceType) {
        case 'materialTracking':
            return '@sage/xtrem-manufacturing/MaterialTrackingInquiry';
        case 'operationTracking':
            return '@sage/xtrem-manufacturing/TimeTrackingInquiry';
        case 'productionTracking':
            return '@sage/xtrem-manufacturing/ProductionTrackingInquiry';
        case 'workOrderClose':
            return '@sage/xtrem-manufacturing/WorkOrder';

        case 'purchaseOrder':
            return '@sage/xtrem-purchasing/PurchaseOrder';
        case 'purchaseReceipt':
            return '@sage/xtrem-purchasing/PurchaseReceipt';
        default:
            throw Error(`Type ${sourceType} not handle `);
    }
}

export function getTargetDocumentPageNameFinance(targetDocumentType: TargetDocumentType): string {
    switch (targetDocumentType) {
        case 'journalEntry': {
            return '@sage/xtrem-finance/JournalEntry';
        }
        case 'accountsPayableInvoice': {
            return '@sage/xtrem-finance/AccountsPayableInvoice';
        }
        case 'accountsReceivableInvoice': {
            return '@sage/xtrem-finance/AccountsReceivableInvoice';
        }
        case 'accountsReceivableAdvance': {
            return '@sage/xtrem-finance/AccountsReceivableAdvance';
        }
        default:
            throw Error(`Type ${targetDocumentType} not handle `);
    }
}

export function getDocumentPageNameFinance(document: {
    type: FinanceDocumentType | SourceDocumentType;
    sourceType?: SourceDocumentType;
}): string {
    switch (document.type) {
        case 'workInProgress':
            if (document.sourceType) {
                return getSourceDocumentPageNameFinance(document.sourceType);
            }
            return '@sage/xtrem-manufacturing/WorkOrder';

        case 'miscellaneousStockIssue':
            return '@sage/xtrem-stock/StockIssue';
        case 'miscellaneousStockReceipt':
            return '@sage/xtrem-stock/StockReceipt';
        case 'stockAdjustment':
            return '@sage/xtrem-stock/StockAdjustment';
        case 'stockValueChange':
            return '@sage/xtrem-stock/StockValueChange';
        case 'stockCount':
            return '@sage/xtrem-stock/StockCount';

        case 'purchaseOrder':
            return '@sage/xtrem-purchasing/PurchaseOrder';
        case 'purchaseReceipt':
            if (document.sourceType) {
                return getSourceDocumentPageNameFinance(document.sourceType);
            }
            return '@sage/xtrem-purchasing/PurchaseReceipt';
        case 'purchaseReturn':
            return '@sage/xtrem-purchasing/PurchaseReturn';
        case 'purchaseInvoice':
            if (document.sourceType) {
                return getSourceDocumentPageNameFinance(document.sourceType);
            }
            return '@sage/xtrem-purchasing/PurchaseInvoice';
        case 'purchaseCreditMemo':
            return '@sage/xtrem-purchasing/PurchaseCreditMemo';

        case 'salesShipment':
            return '@sage/xtrem-sales/SalesShipment';
        case 'salesReturnReceipt':
            return '@sage/xtrem-sales/SalesReturnReceipt';
        case 'salesInvoice':
            return '@sage/xtrem-sales/SalesInvoice';
        case 'salesCreditMemo':
            return '@sage/xtrem-sales/SalesCreditMemo';

        case 'apInvoice':
            return '@sage/xtrem-finance/AccountsPayableInvoice';
        case 'arInvoice':
            return '@sage/xtrem-finance/AccountsReceivableInvoice';

        case 'bankReconciliationDeposit':
            return '@sage/xtrem-intacct-finance/IntacctTransactionFeedQuery';
        case 'bankReconciliationWithdrawal':
            return '@sage/xtrem-intacct-finance/IntacctTransactionFeedQuery';
        case 'stockTransferShipment':
            return '@sage/xtrem-supply-chain/StockTransferShipment';
        case 'stockTransferReceipt':
            return '@sage/xtrem-supply-chain/StockTransferReceipt';
        default:
            throw Error(`Type ${document.type} not handle `);
    }
}
