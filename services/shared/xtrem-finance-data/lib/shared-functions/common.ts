import type { FinanceItemTypeOptions, LineWithFinanceItemType } from './interfaces/common';

enum FinanceItemTypeEnum {
    stockItem,
    nonStockItem,
    serviceItem,
    landedCostItem,
}
type FinanceItemType = keyof typeof FinanceItemTypeEnum;
/** DE - FR  */
export const legislationsWithoutRevenueRecognition = ['DE', 'FR'];
/** AU DE GB US ZA */
export const legislationsThatDoWipPosting = ['AU', 'DE', 'GB', 'US', 'ZA'];

export function getFinanceItemTypeSelection(options: FinanceItemTypeOptions): FinanceItemType[] {
    const financeItemTypeSelection: FinanceItemType[] = [];
    if (options.isStockItemAllowed) {
        financeItemTypeSelection.push('stockItem');
    }
    if (options.isNonStockItemAllowed) {
        financeItemTypeSelection.push('nonStockItem');
    }
    if (options.isServiceItemAllowed) {
        financeItemTypeSelection.push('serviceItem');
    }
    if (options.isLandedCostItemAllowed) {
        financeItemTypeSelection.push('landedCostItem');
    }
    return financeItemTypeSelection;
}

export function getUsedFinanceItemTypesNotSelected(
    selectedFinanceItemTypes: FinanceItemType[],
    lines: LineWithFinanceItemType[],
    isLandedCostServiceOptionActive: boolean,
): FinanceItemType[] {
    const allFinanceItemTypes: FinanceItemType[] = isLandedCostServiceOptionActive
        ? ['stockItem', 'nonStockItem', 'serviceItem', 'landedCostItem']
        : ['stockItem', 'nonStockItem', 'serviceItem'];

    const unselectedFinanceItemTypes: FinanceItemType[] = allFinanceItemTypes.filter(
        financeItemType => !selectedFinanceItemTypes.includes(financeItemType),
    );

    return unselectedFinanceItemTypes.filter(unselectedFinanceItemType => {
        return (
            lines.filter(line => {
                const linePostingClassDefinitionFinanceItemTypes = getFinanceItemTypeSelection({
                    isStockItemAllowed: line.isStockItemAllowed,
                    isNonStockItemAllowed: line.isNonStockItemAllowed,
                    isServiceItemAllowed: line.isServiceItemAllowed,
                    isLandedCostItemAllowed: line.isLandedCostItemAllowed,
                });
                // check if, for the given unselectedFinanceItemType, we have at least one line using it without using any selected finance item type
                return (
                    linePostingClassDefinitionFinanceItemTypes.includes(unselectedFinanceItemType) &&
                    !linePostingClassDefinitionFinanceItemTypes.some(
                        (linePostingClassDefinitionFinanceItemType: FinanceItemType) =>
                            selectedFinanceItemTypes.includes(linePostingClassDefinitionFinanceItemType),
                    )
                );
            }).length > 0
        );
    });
}
