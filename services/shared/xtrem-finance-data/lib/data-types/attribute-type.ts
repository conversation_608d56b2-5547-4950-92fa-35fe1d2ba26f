import { ReferenceDataType } from '@sage/xtrem-core';
import { AttributeType } from '../nodes/attribute-type';

export const attributeType = new ReferenceDataType({
    reference: () => AttributeType,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id'],
        tunnelPage: '@sage/xtrem-finance-data/AttributeType',
    },
    filters: { lookup: { isActive: true } },
});
