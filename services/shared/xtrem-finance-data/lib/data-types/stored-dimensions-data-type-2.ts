import type { Dict, ValidationContext } from '@sage/xtrem-core';
import { JsonDataType, Node } from '@sage/xtrem-core';
import { storedDimensionsGenericControl } from '../events/control/document-with-dimensions';

/**
 * New data type for storedDimensions fields
 *
 * It provides better typing than storeDimensionDataType.
 * The JSON value is a dictionary of strings instead of an object.
 * We keep the old data type around to avoid a big refactoring now but the old data type will be removed
 * and this one will be renamed to storedDimensionsDataType.
 */
export class StoredDimensionsDataType2<T = unknown> extends JsonDataType<T, Dict<string> | null> {
    public override async controlValue(node: T, cx: ValidationContext, val: object): Promise<void> {
        await super.controlValue(node, cx, val);
        if (node instanceof Node) await storedDimensionsGenericControl(node.$.context, cx, val);
    }
}

export const storedDimensionsDataType2 = new StoredDimensionsDataType2();
