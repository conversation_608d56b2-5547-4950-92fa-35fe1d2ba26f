import type { ValidationContext } from '@sage/xtrem-core';
import { JsonDataType, Node } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import { storedAttributesGenericControl } from '../events/control/document-with-dimensions';

export class StoredAttributesDataType<T = unknown> extends JsonDataType<
    T,
    xtremMasterData.interfaces.StoredAttributes | null
> {
    public override async controlValue(
        node: T,
        cx: ValidationContext,
        val: xtremMasterData.interfaces.StoredAttributes,
    ): Promise<void> {
        await super.controlValue(node, cx, val);
        if (node instanceof Node) await storedAttributesGenericControl(node.$.context, cx, val);
    }
}

export const storedAttributesDataType = new StoredAttributesDataType();
