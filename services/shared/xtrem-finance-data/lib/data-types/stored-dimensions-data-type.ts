import type { Dict, ValidationContext } from '@sage/xtrem-core';
import { JsonDataType, Node } from '@sage/xtrem-core';
import { storedDimensionsGenericControl } from '../events/control/document-with-dimensions';

export class StoredDimensionsDataType<T = unknown> extends JsonDataType<T, Dict<string> | null> {
    public override async controlValue(node: T, cx: ValidationContext, val: object): Promise<void> {
        await super.controlValue(node, cx, val);
        if (node instanceof Node) await storedDimensionsGenericControl(node.$.context, cx, val);
    }
}

export const storedDimensionsDataType = new StoredDimensionsDataType();
