import { ReferenceDataType } from '@sage/xtrem-core';
import { BankAccount } from '../nodes/bank-account';

export const bankAccount = new ReferenceDataType({
    reference: () => BankAccount,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id'],
        tunnelPage: '@sage/xtrem-finance-data/BankAccount',
    },
    filters: { lookup: { isActive: true } },
});
