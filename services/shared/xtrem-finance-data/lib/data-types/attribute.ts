import { ReferenceDataType } from '@sage/xtrem-core';
import { Attribute } from '../nodes/attribute';

export const attribute = new ReferenceDataType({
    reference: () => Attribute,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id'],
        tunnelPage: '@sage/xtrem-finance-data/Attribute',
    },
    filters: { lookup: { isActive: true } },
});
