import { ReferenceDataType } from '@sage/xtrem-core';
import { DimensionType } from '../nodes/dimension-type';

export const dimensionType = new ReferenceDataType({
    reference: () => DimensionType,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'docProperty',
        columnPaths: ['name', 'docProperty'],
        tunnelPage: '@sage/xtrem-finance-data/DimensionType',
    },
    filters: { lookup: { isActive: true } },
});
