import { ReferenceDataType } from '@sage/xtrem-core';
import { Dimension } from '../nodes/dimension';

export const dimension = new ReferenceDataType({
    reference: () => Dimension,
    lookup: {
        valuePath: 'name',
        helperTextPath: 'id',
        columnPaths: ['name', 'id'],
        tunnelPage: '@sage/xtrem-finance-data/Dimension',
    },
    filters: { lookup: { isActive: true } },
});
