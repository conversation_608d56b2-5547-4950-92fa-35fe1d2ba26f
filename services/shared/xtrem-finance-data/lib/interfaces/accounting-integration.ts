import type { Collection, date, decimal, integer, Node, Reference, UpdateAction } from '@sage/xtrem-core';
import type * as xtremLandedCost from '@sage/xtrem-landed-cost';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremStructure from '@sage/xtrem-structure';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremFinanceData from '..';

export interface FinanceDocumentClass {
    addOrUpdateLine: Function;
    getNewFinanceDocumentLine: Function;
}

export interface StagingLineData {
    item: xtremMasterData.nodes.Item | null;
    customer: xtremMasterData.nodes.Customer | null;
    supplier: xtremMasterData.nodes.Supplier | null;
    tax: xtremTax.nodes.Tax | null;
    resource: xtremMasterData.nodes.DetailedResource | null;
    account: xtremFinanceData.nodes.Account | null;
    documentNumber: string;
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    movementType: xtremFinanceData.enums.MovementType;
    amountType: xtremFinanceData.enums.AmountType;
    financialSite: xtremSystem.nodes.Site;
}

export interface StoredAttributesAndDimensionsPromises {
    readonly storedAttributes: Promise<xtremMasterData.interfaces.StoredAttributes | null>;
    readonly storedDimensions: Promise<Object | null>;
}

export interface StoredAttributesAndDimensions {
    storedDimensions: Object;
    storedAttributes: xtremMasterData.interfaces.StoredAttributes;
}

// Common interface for a stock based and shipment document line
// Extended by FinanceOriginDocumentLine and FinanceOriginDocumentLineWithStock
export interface FinanceOriginDocumentStockLine {
    readonly stockMovements: Collection<xtremStockData.nodes.StockJournal>;
    stockMovementArray?: { movementAmount: number; orderAmount: number; nonAbsorbedAmount: number }[];
    readonly orderCost?: Promise<decimal>;
    readonly valuedCost?: Promise<decimal>;
}

// Common base interface for a document line, basis for the stock based and sales lines
// Extended by FinanceOriginDocumentLine, InvoiceDocumentLine,
// FinanceOriginDocumentLineWithStock and WorkInProgressFinanceDocumentLine
export interface FinanceOriginDocumentLineBase
    extends xtremMasterData.nodes.BaseDocumentLine,
        StoredAttributesAndDimensionsPromises {
    readonly item: Reference<xtremMasterData.nodes.Item>;
    readonly computedAttributes: Promise<Object | null>;
}

// Interface for a stock based document line
// Used on misc. stock receipt/issue and adjustment
// Used on purchase receipt and sales shipment
export interface FinanceOriginDocumentLine extends FinanceOriginDocumentLineBase, FinanceOriginDocumentStockLine {
    readonly site: Reference<xtremSystem.nodes.Site>; // the stock site for getting the evaluation method of the correct ItemSite (stock site not financial)
    readonly amountExcludingTax?: Promise<decimal>;
    readonly lineAdjustmentAmount?: Promise<decimal>;
}

// Interface for a purchase return line
// Used on purchase return
export interface ReturnDocumentLine extends FinanceOriginDocumentLine {
    readonly sourceDocumentNumber: Promise<string>;
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;
    readonly site: Reference<xtremSystem.nodes.Site>;
}

// Interface for an invoice line
// Used in Sales and Purchase invoice and credit memo line
export interface InvoiceDocumentLine extends FinanceOriginDocumentLineBase {
    readonly amountExcludingTax: Promise<decimal>;
    readonly taxDate: Promise<date | null>;
    readonly sourceDocumentNumber: Promise<string>;
    readonly taxes?: Collection<xtremTax.nodes.BaseLineTax>;
    readonly providerSite?: Reference<xtremSystem.nodes.Site>;
    readonly recipientSite?: Reference<xtremSystem.nodes.Site>;
    readonly sourceDocumentSysId?: Promise<integer>;
    readonly sourceDocumentType?: Promise<xtremFinanceData.enums.SourceDocumentType | null>;
    stockMovementArray?: {
        movementAmount: number;
        nonAbsorbedAmount: number;
    }[];
    readonly receiptNumber?: Promise<string>;
    readonly receiptSysId?: Promise<integer>;
    readonly landedCost?: Reference<xtremLandedCost.nodes.LandedCostDocumentLine | null>;
}

// interface for a sales document line with stock (used in Sales shipment line)
export interface FinanceOriginDocumentLineWithStock
    extends FinanceOriginDocumentLineBase,
        FinanceOriginDocumentStockLine {
    readonly amountExcludingTax: Promise<decimal>;
    readonly stockCostAmountInCompanyCurrency?: Promise<decimal>;
}

// interface for a wip document line (used in production tracking line and material tracking line)
export interface WorkInProgressFinanceDocumentLine extends FinanceOriginDocumentLineBase {
    readonly workInProgressCostAmount: Promise<decimal>;
    readonly workInProgressWorkOrderNumber: Promise<string>;
    readonly workInProgressWorkOrderSysId: Promise<number>;
}
export interface MaterialTrackingFinanceDocumentLine
    extends xtremMasterData.nodes.BaseDocumentLine,
        StoredAttributesAndDimensionsPromises {
    readonly item: Reference<xtremMasterData.nodes.Item | null>;
    readonly computedAttributes: Promise<Object | null>;
    readonly workInProgressCostAmount: Promise<decimal>;
    readonly workInProgressWorkOrderNumber: Promise<string>;
    readonly workInProgressWorkOrderSysId: Promise<number>;
}

export interface WipCost extends Node {
    amount: Promise<decimal>;
    type: Promise<string>;
}

// interface for a wip operation tracking document line (used in operation tracking line)
export interface OperationTrackingFinanceDocumentLine
    extends xtremMasterData.nodes.BaseDocumentLine,
        StoredAttributesAndDimensionsPromises {
    readonly item: Reference<xtremMasterData.nodes.Item>;
    readonly computedAttributes: Promise<Object | null>;
    readonly workInProgressCostAmount: Promise<decimal>;
    readonly workInProgressCostType: Promise<string>;
    readonly workInProgressActualResourceType: Promise<string>;
    readonly actualResource: Reference<xtremMasterData.nodes.DetailedResource>;
    readonly workInProgressWorkOrderNumber: Promise<string>;
    readonly workInProgressWorkOrderSysId: Promise<number>;
    readonly workInProgressCosts: Collection<WipCost>;
}

// interface for a wip work order closing workInProgressCost line (used in work in progress cost record)
export interface WorkOrderClosingFinanceDocumentLine extends StoredAttributesAndDimensionsPromises {
    readonly originatingLine: Reference<xtremMasterData.nodes.BaseDocumentLine>;
    readonly item: Reference<xtremMasterData.nodes.Item>;
    readonly computedAttributes: Promise<Object | null>;
    readonly amount: Promise<decimal>;
    readonly workInProgressCostType: Promise<string>;
}
export interface ApArInvoiceLine extends xtremMasterData.nodes.BaseDocumentLine, StoredAttributesAndDimensionsPromises {
    readonly amountExcludingTax: Promise<decimal>;
    readonly amountIncludingTax: Promise<decimal>;
    readonly taxDate: Promise<date | null>;
    readonly account: Reference<xtremFinanceData.nodes.Account>;
    readonly financialSite: Reference<xtremSystem.nodes.Site>;
    readonly currency: Reference<xtremMasterData.nodes.Currency>;
    readonly sourceDocumentNumber: Promise<string>;
    readonly sourceDocumentType: Promise<xtremFinanceData.enums.SourceDocumentType | null>;
    readonly taxes: Collection<xtremTax.nodes.BaseLineTax>;
}

export interface ApArInvoice extends FinanceOriginDocument {
    readonly account: Reference<xtremFinanceData.nodes.Account>;
    readonly origin: Promise<xtremFinanceData.enums.AccountsPayableReceivableInvoiceOrigin>;
}

export interface ApInvoice extends ApArInvoice {
    readonly billBySupplier: Reference<xtremMasterData.nodes.Supplier>;
    readonly payToSupplier?: Reference<xtremMasterData.nodes.Supplier | null>;
    readonly payToSupplierLinkedAddress?: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;
    readonly returnLinkedAddress?: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;
    readonly purchaseDocumentNumber: Promise<string>;
    readonly purchaseDocumentSysId: Promise<integer>;
}

export interface ArInvoice extends ApArInvoice {
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;
    readonly salesDocumentNumber: Promise<string>;
    readonly salesDocumentSysId: Promise<integer>;
}

export interface FinanceOriginDocumentHeader {
    readonly financialSite: Reference<xtremSystem.nodes.Site>;
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;
    readonly documentDate: Promise<date>;
}

// interface for a stock based document
export interface FinanceOriginDocument extends xtremMasterData.interfaces.Document, FinanceOriginDocumentHeader {
    readonly companyFxRate?: Promise<number>;
    readonly companyFxRateDivisor?: Promise<number>;
    readonly fxRateDate?: Promise<date>;
    readonly billToCustomer?: Reference<xtremMasterData.nodes.Customer>;
}

export interface FinanceOriginBaseDocument extends FinanceOriginDocument, xtremMasterData.nodes.BaseDocument {}

// Interface for a sales document
export interface SalesFinanceDocument extends xtremMasterData.interfaces.Document, FinanceOriginDocument {
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;
}

// Interface for a sales document with taxes
export interface SalesFinanceDocumentWithTax extends SalesFinanceDocument {
    taxes?: Collection<xtremTax.nodes.BaseLineTax>;
    forceUpdateForFinance?: Promise<boolean>;
    dueDate: PromiseLike<date>;
    paymentTerm: Promise<xtremMasterData.nodes.PaymentTerm>;
    isPrinted: Promise<boolean>;
    taxCalculationStatus: Promise<xtremMasterData.enums.TaxCalculationStatus>;
}

// Interface for purchase document
export interface PurchaseFinanceDocument extends FinanceOriginDocument {
    billBySupplier: Reference<xtremMasterData.nodes.Supplier>;
}

// Interface for purchase document with taxes
export interface PurchaseFinanceDocumentWithTax extends PurchaseFinanceDocument {
    taxes?: Collection<xtremTax.nodes.BaseLineTax>;
    forceUpdateForFinance?: Promise<boolean>;
    supplierDocumentDate: PromiseLike<date>;
    supplierDocumentNumber: Promise<string>;
    dueDate: PromiseLike<date>;
    paymentTerm: Promise<xtremMasterData.nodes.PaymentTerm>;
    payToSupplier: Promise<xtremMasterData.nodes.Supplier>;
    payToLinkedAddress?: Promise<xtremMasterData.nodes.BusinessEntityAddress | null>;
    returnLinkedAddress?: Promise<xtremMasterData.nodes.BusinessEntityAddress | null>;
    paymentTracking?: Reference<xtremFinanceData.nodes.PaymentTracking | null>;
}

export interface AccountingStagingCommonPayload {
    batchId: string;
    documentSysId: number;
    documentNumber: string;
    documentDate: string;
    currencySysId: number;
    financialSiteSysId: number;
}

export interface FinanceDocumentsCreated {
    type: xtremFinanceData.enums.TargetDocumentType;
    documentNumber: string;
    documentSysId: number;
}
export interface CreateFinanceDocumentsReturn {
    documentsCreated: FinanceDocumentsCreated[];
    validationMessages: xtremFinanceData.interfaces.ValidationMessage[];
}

export interface FinanceTransactionRecord {
    batchId: string;
    documentNumber: string;
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType;
    paymentTracking?: xtremFinanceData.nodes.PaymentTracking | null;
}

export interface GroupedAccountingRecord extends FinanceTransactionRecord {
    batchSize: integer;
    replyTopic: string;
}
export interface FinanceExternalIntegration {
    app: xtremFinanceData.enums.FinanceIntegrationApp;
    recordId: string;
    url: string;
}
export interface FinanceTransactionData extends FinanceTransactionRecord {
    validationMessages: xtremFinanceData.interfaces.ValidationMessage[];
    status: xtremFinanceData.enums.FinanceIntegrationStatus;
    targetDocumentNumber: string;
    targetDocumentSysId: number;
    financeExternalIntegration?: FinanceExternalIntegration;
    isJustForStatus?: boolean;
    batchTrackingId?: string;
}

export interface FinanceOriginDocumentPostReturn {
    batchId: string;
    batchSize: number;
    notificationsSent: number;
}

// Interface for "Unbilled account payable" inquiry to be used in purchaseReceiptLine node for unbilledAccountPayable query
// the interface for the result array
export interface FinanceUnbilledAccountPayable {
    billBySupplier: xtremMasterData.nodes.Supplier;
    supplier: xtremMasterData.nodes.Supplier;
    currency: xtremMasterData.nodes.Currency;
    financialSite: xtremSystem.nodes.Site;
    purchaseUnit: xtremMasterData.nodes.UnitOfMeasure;
    netPrice: decimal;
    item: xtremMasterData.nodes.Item;
    stockSite: xtremSystem.nodes.Site;
    quantity: decimal;
    account?: xtremFinanceData.nodes.Account | null;
    accountItem?: xtremFinanceData.nodes.Account | null;
    invoicedQuantity: decimal;
    creditedQuantity: decimal;
    returnedQuantity: decimal;
    invoiceReceivableQuantity: decimal;
    invoiceReceivableAmount: decimal;
    invoiceReceivableAmountInCompanyCurrency: decimal;
    invoiceReceivableAmountInCompanyCurrencyAtAsOfDate: decimal;
    companyCurrency: xtremMasterData.nodes.Currency;
    company: xtremSystem.nodes.Company;
    receiptNumber: string;
    receiptInternalId: integer;
    documentDate: date;
}

// Interface for "Unbilled accounts payable" and "Unbilled accounts receivable" inquiries to gather the various quantities
export interface UnbilledInquiriesQuantities {
    invoiced: decimal;
    credited: decimal;
    returned: decimal;
}
export interface UnbilledInquiriesInvoicedValues {
    invoicedQuantity: decimal;
    invoicedAmount: decimal;
}

export interface UnbilledInquiriesGetAccountParameters {
    chartOfAccount: xtremStructure.nodes.ChartOfAccount;
    legislation: xtremStructure.nodes.Legislation;
    type: string;
    id: string;
    postingClassDetailed: xtremFinanceData.nodes.PostingClass | null;
    invoicedQuantity: decimal;
    tax?: xtremTax.nodes.Tax | null;
}

// Interface for "Unbilled account payable" inquiry to be used in purchaseReceiptLine node for unbilledAccountPayable query
// the interface for the searchCriteria parameter
export interface FinanceUnbilledAccountPayableSearch {
    company?: string;
    stockSites?: integer[];
    fromSupplier?: string;
    toSupplier?: string;
    asOfDate: date;
}

// Interface for "Unbilled account receivable" inquiry to be used in UnbilledAccountReceivableInputSet node for unbilledAccountReceivableInquiry
// the interface for the result array
export interface FinanceUnbilledAccountReceivable {
    customer: xtremMasterData.nodes.Customer;
    currency: xtremMasterData.nodes.Currency;
    financialSite: xtremSystem.nodes.Site;
    salesUnit: xtremMasterData.nodes.UnitOfMeasure;
    netPrice: decimal;
    item: xtremMasterData.nodes.Item;
    site: xtremSystem.nodes.Site;
    quantity: decimal;
    account?: xtremFinanceData.nodes.Account | null;
    accountItem?: xtremFinanceData.nodes.Account | null;
    invoicedQuantity: decimal;
    creditedQuantity: decimal;
    returnedQuantity: decimal;
    invoiceIssuableQuantity: decimal;
    invoiceIssuableAmount: decimal;
    invoiceIssuableAmountInCompanyCurrency: decimal;
    invoiceIssuableAmountInCompanyCurrencyAtAsOfDate: decimal;
    companyCurrency: xtremMasterData.nodes.Currency;
    company: xtremSystem.nodes.Company;
    shipmentNumber: string;
    shipmentInternalId: integer;
    documentDate: date;
    shipToCustomer: xtremMasterData.nodes.Customer;
}

// Interface for "Unbilled account receivable" inquiry to be used in UnbilledAccountReceivableInputSet node for unbilledAccountReceivableInquiry
// the interface for the searchCriteria parameter
export interface FinanceUnbilledAccountReceivableSearch {
    company?: string;
    stockSites?: integer[];
    fromCustomer?: string;
    toCustomer?: string;
    asOfDate: date;
}

export interface PrintedDocument {
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    documentSysId: number;
}

export interface JournalEntryLineTaxData {
    tax?: xtremTax.nodes.Tax | null;
    taxDate?: date;
    taxRate?: decimal;
    deductibleTaxRate?: decimal;
}

export interface AccountingStagingAmount {
    amountType: xtremFinanceData.enums.AmountType;
    amount: decimal;
    taxSysId?: decimal;
    baseTaxSysId?: decimal;
    documentLineType: xtremFinanceData.enums.AccountsPayableReceivableInvoiceDocumentLineType;
    taxDate?: string;
    taxRate?: decimal;
}

export interface AccountingStagingTax {
    baseTaxSysId: number;
}

export interface FinanceIntegrationDocumentSourceProperties {
    sourceDocumentType?: xtremFinanceData.enums.SourceDocumentType;
    sourceDocumentSysId?: number;
    sourceDocumentNumber?: string;
    isSourceForDimension?: boolean;
}

export interface FinanceIntegrationDocument extends AccountingStagingCommonPayload, FinanceIntegrationDocumentUpdate {
    documentLines: FinanceIntegrationDocumentLine[];
    taxes?: AccountingStagingTax[];
    paymentTermSysId?: number;
    isPrinted?: boolean;
    taxCalculationStatus?: xtremMasterData.enums.TaxCalculationStatus;
    description?: string;
    batchTrackingId?: string;
}

export interface FinanceIntegrationDocumentLine extends FinanceIntegrationDocumentLineUpdate {
    stockJournalSysId?: number;
    recipientSiteSysId?: number;
    providerSiteSysId?: number;
    movementType: xtremFinanceData.enums.MovementType;
    sourceDocumentNumber: string;
    sourceDocumentType?: xtremFinanceData.enums.SourceDocumentType | null;
    currencySysId: number;
    companyFxRate: number;
    companyFxRateDivisor: number;
    fxRateDate: string;
    itemSysId?: number;
    accountSysId?: number;
    customerSysId?: number;
    supplierSysId?: number;
    payToSupplierSysId?: number;
    payToSupplierAddressSysId?: number;
    returnAddressSysId?: number;
    storedDimensions: Object;
    storedAttributes: xtremMasterData.interfaces.StoredAttributes;
    taxDate?: string;
    amounts: AccountingStagingAmount[];
    taxes?: AccountingStagingTax[];
    resourceSysId?: number;
    uiBaseDocumentLineSysId?: number;
    uiSourceDocumentNumber?: string;
}

export interface FinanceIntegrationDocumentUpdate extends FinanceIntegrationDocumentSourceProperties {
    batchId: string; // To filter the accounting staging records to update
    batchSize: number;
    documentType: xtremFinanceData.enums.FinanceDocumentType; // To filter the accounting staging records to update
    documentSysId: number;
    financialSiteSysId: number;
    documentNumber: string; // To filter the accounting staging records to update
    targetDocumentType: xtremFinanceData.enums.TargetDocumentType; // To filter the accounting staging records to update
    sourceDocumentType?: xtremFinanceData.enums.SourceDocumentType; // To filter the accounting staging records to update
    supplierDocumentDate?: string; // Data that will be updated on the accounting staging node
    supplierDocumentNumber?: string; // Data that will be updated on the accounting staging node
    dueDate?: string; // Data that will be updated on the accounting staging node
    paymentTerm?: xtremMasterData.nodes.PaymentTerm; // Data that will be updated on the accounting staging node
    documentLines: FinanceIntegrationDocumentLineUpdate[];
    sourceLines?: FinanceIntegrationDocumentSourceProperties[];
    doNotPostOnUpdate?: boolean;
    isJustForPost?: boolean;
}

export interface FinanceIntegrationDocumentLineUpdate extends StoredAttributesAndDimensions {
    _action?: UpdateAction;
    baseDocumentLineSysId: number;
    sourceBaseDocumentLineSysId?: number;
    uiTaxes?: xtremTax.interfaces.UiTaxes | null;
}

export interface FinanceIntegrationDocumentLineNodeUpdate {
    _id: number;
    sourceBaseDocumentLineSysId?: number;
    storedDimensions: Promise<Object> | Object;
    storedAttributes: Promise<Object> | Object;
    computedAttributes?: Promise<Object> | Object;
}

export interface AnyFinanceIntegrationDocument {
    documentLines: any;
    [key: string]: any;
}

// Interface for "Posting data" query to be used in FinanceTransaction node for getPostingStatusData query
// the interface for the result array (everything related to a documentNumber/Type/SysId will be taken from the
// corresponding targetDocumentNumber/Type/SysId of the FinanceTransaction record)

export interface FinancePostingStatusData {
    _id: string;
    documentType: xtremFinanceData.enums.TargetDocumentType;
    documentNumber: string;
    documentSysId: number;
    status: xtremFinanceData.enums.PostingStatus;
    message: string;
    hasFinanceIntegrationApp: boolean;
    financeIntegrationApp: xtremFinanceData.enums.FinanceIntegrationApp | null;
    financeIntegrationAppRecordId: string;
    financeIntegrationAppUrl: string;
    externalLink: boolean;
    hasSourceForDimensionLines: boolean;
    sourceDocumentType?: xtremFinanceData.enums.SourceDocumentType;
}

export interface FinanceOriginPostingStatusData {
    _id: string;
    documentType: xtremFinanceData.enums.FinanceDocumentType;
    documentNumber: string;
    documentSysId: number;
    postingStatus: xtremFinanceData.enums.PostingStatus;
    message: string;
    financeIntegrationAppRecordId: string;
    financeIntegrationAppUrl: string;
}

export interface RetryFinanceDocumentMutationReply {
    xtremIntacctFinance: { financeListener: { retryFinanceDocument: xtremFinanceData.interfaces.MutationResult } };
}

export interface GetAccountingStagingCommonPayloadParameters {
    document: xtremFinanceData.interfaces.FinanceOriginDocument;
    isJustChecking: boolean;
    number?: string;
    sysId?: integer;
    batchId?: string;
}

export interface FinanceDocumenLineStockJournalLine {
    item: xtremMasterData.nodes.Item;
    reasonCode: xtremMasterData.nodes.ReasonCode | null;
    documentLine: xtremMasterData.nodes.BaseDocumentLine;
    sourceDocumentLine: xtremMasterData.nodes.BaseDocumentLine | null;
    movementAmount: decimal;
    nonAbsorbedAmount: decimal;
    storedDimensions: Object;
    storedAttributes: Object;
    computedAttributes: Object;
}

export interface StockTransferShipmentDocument extends xtremMasterData.interfaces.Document, FinanceOriginDocument {
    readonly billToCustomer: Reference<xtremMasterData.nodes.Customer>;
}

export interface AcoutingStagingUpdate extends StoredAttributesAndDimensions {
    supplierDocumentDate?: date;
    dueDate?: date;
    supplierDocumentNumber?: string;
    paymentTerm?: xtremMasterData.nodes.PaymentTerm;
    toBeReprocessed: boolean;
}
