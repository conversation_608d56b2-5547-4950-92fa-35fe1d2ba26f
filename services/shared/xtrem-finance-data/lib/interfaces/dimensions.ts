import type * as xtremFinanceData from '../index';

export interface ComputedAttributes {
    financialSite?: string;
    businessSite?: string;
    stockSite?: string;
    manufacturingSite?: string;
    customer?: string;
    supplier?: string;
    item?: string;
}

export interface StoredAttributesJournalEntryLine {
    financialSite: string;
    businessSite: string;
    stockSite: string;
    manufacturingSite: string;
    customer: string;
    supplier: string;
    item: string;
    project: string;
    task: string;
    employee: string;
    [key: string]: string;
}

export interface AttributeDimensionObject {
    type?: string;
    value?: string;
}

// This interface is needed for creating the stored dimensions of a document from default dimensions (from site, ...)
export interface StoredDimensions {
    [Key: string]: {
        dimensionType01?: string;
        dimensionType02?: string;
        dimensionType03?: string;
        dimensionType04?: string;
        dimensionType05?: string;
        dimensionType06?: string;
        dimensionType07?: string;
        dimensionType08?: string;
        dimensionType09?: string;
        dimensionType10?: string;
        dimensionType11?: string;
        dimensionType12?: string;
        dimensionType13?: string;
        dimensionType14?: string;
        dimensionType15?: string;
        dimensionType16?: string;
        dimensionType17?: string;
        dimensionType18?: string;
        dimensionType19?: string;
        dimensionType20?: string;
    };
}

export interface DefaultQueryOptions {
    dimensionDefinitionLevel: xtremFinanceData.enums.DimensionDefinitionLevel;
    onlyFromItem: boolean;
    companyId: number;
    siteId: number;
    customerId?: number;
    supplierId?: number;
    itemId?: number;
    receivingSiteId?: number;
    shippingSiteId?: number;
}

export interface DefaultQueryOptionsOrderToOrder extends DefaultQueryOptions {
    storedAttributes: string;
    storedDimensions: string;
}
