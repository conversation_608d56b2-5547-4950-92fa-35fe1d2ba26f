import type * as xtremMasterData from '@sage/xtrem-master-data';
import type { ValidationSeverity } from '@sage/xtrem-shared';

export interface ValidationMessage
    extends xtremMasterData.sharedFunctions.interfaces.FinanceIntegration.LineValidationMessage {
    type: ValidationSeverity;
}

export interface MutationResult {
    wasSuccessful: boolean;
    message: string;
    validationMessages?: ValidationMessage[];
}
