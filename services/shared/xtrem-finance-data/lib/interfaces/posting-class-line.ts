import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremFinanceData from '..';

export interface JournalEntryTypePostingClassLine {
    _id?: number;
    postingClass: xtremFinanceData.nodes.PostingClass;
    account?: xtremFinanceData.nodes.Account;
    details?: JournalEntryTypePostingClassLineDetail[];
}

export interface JournalEntryTypePostingClassLineDetail {
    tax: xtremTax.nodes.Tax | null;
    account: xtremFinanceData.nodes.Account;
}
