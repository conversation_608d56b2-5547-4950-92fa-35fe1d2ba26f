import type { AsyncResponse, date, decimal, Node } from '@sage/xtrem-core';
import type * as xtremTax from '@sage/xtrem-tax';
import type * as xtremFinanceData from '../../index';

export interface AccountingStagingAmount {
    amountType: AsyncResponse<xtremFinanceData.enums.AmountType>;
    amount: AsyncResponse<decimal>;
    tax: AsyncResponse<xtremTax.nodes.Tax | null>;
    taxPostingClass: AsyncResponse<xtremFinanceData.nodes.PostingClass | null>;
    baseTax: AsyncResponse<xtremTax.nodes.BaseTax | null>;
    documentLineType: AsyncResponse<xtremFinanceData.enums.AccountsPayableReceivableInvoiceDocumentLineType>;
    taxDate: AsyncResponse<date | null>;
    taxRate: AsyncResponse<decimal>;
}

export interface AccountingStagingAmountNode extends AccountingStagingAmount, Node {}
