import type { AsyncResponse, date, decimal, integer } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import type * as xtremStockData from '@sage/xtrem-stock-data';
import type * as xtremSystem from '@sage/xtrem-system';
import type * as xtremFinanceData from '../../index';

export interface AccountingStaging {
    batchId: AsyncResponse<string>;
    batchSize: AsyncResponse<integer>;
    baseDocumentLine: AsyncResponse<xtremMasterData.nodes.BaseDocumentLine | null>;
    stockJournal: AsyncResponse<xtremStockData.nodes.StockJournal | null>;
    documentNumber: AsyncResponse<string>;
    documentSysId: AsyncResponse<number>;
    sourceDocumentNumber: AsyncResponse<string>;
    sourceDocumentType: AsyncResponse<AsyncResponse<xtremFinanceData.enums.SourceDocumentType | null>>;
    documentDate: AsyncResponse<date>;
    taxDate: AsyncResponse<date | null>;
    documentType: AsyncResponse<xtremFinanceData.enums.FinanceDocumentType>;
    movementType: AsyncResponse<xtremFinanceData.enums.MovementType>;
    targetDocumentType: AsyncResponse<xtremFinanceData.enums.TargetDocumentType>;
    recipientSite: AsyncResponse<xtremSystem.nodes.Site | null>;
    providerSite: AsyncResponse<xtremSystem.nodes.Site | null>;
    financialSite: AsyncResponse<xtremSystem.nodes.Site>;
    item: AsyncResponse<xtremMasterData.nodes.Item | null>;
    account: AsyncResponse<xtremFinanceData.nodes.Account | null>;
    customer: AsyncResponse<xtremMasterData.nodes.Customer | null>;
    supplier: AsyncResponse<xtremMasterData.nodes.Supplier | null>;
    dueDate: AsyncResponse<date | null>;
    payToSupplier: AsyncResponse<xtremMasterData.nodes.Supplier | null>;
    payToSupplierLinkedAddress: AsyncResponse<xtremMasterData.nodes.BusinessEntityAddress | null>;
    returnLinkedAddress: AsyncResponse<xtremMasterData.nodes.BusinessEntityAddress | null>;
    paymentTerm: AsyncResponse<xtremMasterData.nodes.PaymentTerm | null>;
    supplierDocumentDate: AsyncResponse<date | null>;
    supplierDocumentNumber: AsyncResponse<string>;
    isPrinted: AsyncResponse<boolean>;
    taxCalculationStatus: AsyncResponse<xtremMasterData.enums.TaxCalculationStatus | null>;
    itemPostingClass: AsyncResponse<xtremFinanceData.nodes.PostingClass | null>;
    customerPostingClass: AsyncResponse<xtremFinanceData.nodes.PostingClass | null>;
    supplierPostingClass: AsyncResponse<xtremFinanceData.nodes.PostingClass | null>;
    transactionCurrency: AsyncResponse<xtremMasterData.nodes.Currency>;
    companyFxRate: AsyncResponse<decimal>;
    companyFxRateDivisor: AsyncResponse<decimal>;
    fxRateDate: AsyncResponse<date>;
    storedDimensions: AsyncResponse<object | null>;
    storedAttributes: AsyncResponse<xtremMasterData.interfaces.StoredAttributes | null>;
    originNotificationId: AsyncResponse<string>;
    storedComputedAttributes: AsyncResponse<object>;
    isProcessed: AsyncResponse<boolean>;
    replyTopic: AsyncResponse<string>;
    resourcePostingClass: AsyncResponse<xtremFinanceData.nodes.PostingClass | null>;
    resource: AsyncResponse<xtremMasterData.nodes.DetailedResource | null>;
}

export interface AccountingStagingWithArrays extends AccountingStaging {
    amounts: xtremFinanceData.interfaces.FinanceNodes.AccountingStagingAmount[];
    taxes: xtremFinanceData.interfaces.FinanceNodes.AccountingStagingLineTax[];
}
