import type { GraphApi, PostingClassDefinition as PostingClassDefinitionNode } from '@sage/xtrem-finance-data-api';
import type { Legislation } from '@sage/xtrem-structure-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<PostingClassDefinition>({
    title: 'Posting class definition',
    objectTypeSingular: 'Posting class definition',
    objectTypePlural: 'Posting class definitions',
    idField() {
        return this.postingClassType;
    },
    menuItem: featuresFinancialIntegration,
    priority: 300,
    node: '@sage/xtrem-finance-data/PostingClassDefinition',
    module: 'finance',
    areNavigationTabsHidden: true,
    navigationPanel: {
        listItem: {
            line3: ui.nestedFields.text({ bind: 'id' }),
            title: ui.nestedFields.select({
                bind: 'postingClassType',
                optionType: '@sage/xtrem-finance-data/PostingClassType',
            }),
            line2: ui.nestedFields.text({ bind: 'accountTypeName' }),
            titleRight: ui.nestedFields.text({ bind: { legislation: { id: true } }, title: 'Legislation' }),
            line_4: ui.nestedFields.switch({ bind: 'isDetailed', title: 'Detailed', isHiddenOnMainField: true }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Customer', graphQLFilter: { postingClassType: { _eq: 'customer' } } },
            { title: 'Item', graphQLFilter: { postingClassType: { _eq: 'item' } } },
            { title: 'Resource', graphQLFilter: { postingClassType: { _eq: 'resource' } } },
            { title: 'Supplier', graphQLFilter: { postingClassType: { _eq: 'supplier' } } },
            { title: 'Tax', graphQLFilter: { postingClassType: { _eq: 'tax' } } },
        ],
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
        });
    },
})
export class PostingClassDefinition extends ui.Page<GraphApi, PostingClassDefinitionNode> {
    @ui.decorators.section<PostingClassDefinition>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.block<PostingClassDefinition>({
        parent() {
            return this.generalSection;
        },
        title: 'Posting class definition',
        width: 'large',
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.switchField<PostingClassDefinition>({})
    canHaveAdditionalCriteria: ui.fields.Switch;

    @ui.decorators.referenceField<PostingClassDefinition, Legislation>({
        parent() {
            return this.generalBlock;
        },
        minLookupCharacters: 0,
        placeholder: 'Select legislation',
        lookupDialogTitle: 'Select legislation',
        isMandatory: true,
    })
    legislation: ui.fields.Reference;

    @ui.decorators.switchField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        title: 'Detailed',
    })
    isDetailed: ui.fields.Switch;

    @ui.decorators.separatorField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    firstSeparator: ui.fields.Separator;

    @ui.decorators.selectField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        title: 'Posting class type',
        optionType: '@sage/xtrem-finance-data/PostingClassType',
        isReadOnly: true,
        onChange() {
            if (this.postingClassType.value !== 'item') {
                this.canHaveAdditionalCriteria.value = false;
            }
        },
    })
    postingClassType: ui.fields.Select;

    @ui.decorators.multiDropdownField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        optionType: '@sage/xtrem-finance-data/FinanceItemType',
        title: 'Item type',
        width: 'large',
        isReadOnly: true,
        isHidden() {
            return this.postingClassType.value !== 'item';
        },
    })
    financeItemType: ui.fields.MultiDropdown;

    @ui.decorators.separatorField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    secondSeparator: ui.fields.Separator;

    @ui.decorators.textField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        title: 'Account type name',
        width: 'large',
        isReadOnly: true,
    })
    accountTypeName: ui.fields.Text;

    @ui.decorators.textField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        width: 'small',
        isReadOnly: true,
    })
    id: ui.fields.Text;

    @ui.decorators.separatorField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    thirdSeparator: ui.fields.Separator;

    @ui.decorators.selectField<PostingClassDefinition>({
        parent() {
            return this.generalBlock;
        },
        title: 'Additional criteria',
        options() {
            return ['tax'];
        },
        map(type: string) {
            return type === 'tax' ? ui.localizeEnumMember('@sage/xtrem-finance-data/PostingClassType', 'tax') : type;
        },
        optionType: '@sage/xtrem-finance-data/PostingClassType',
        isHidden() {
            return !this.canHaveAdditionalCriteria.value;
        },
        isDisabled() {
            return this.postingClassType.value !== 'item';
        },
    })
    additionalCriteria: ui.fields.Select;
}
