import type { BankAccount as Bank<PERSON><PERSON>untN<PERSON>, GraphApi } from '@sage/xtrem-finance-data-api';
import type { Currency } from '@sage/xtrem-master-data-api';
import type { Site } from '@sage/xtrem-system-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { paymentTracking } from '../menu-items/payment-tracking';

@ui.decorators.page<BankAccount, BankAccountNode>({
    module: 'finance',
    title: 'Bank account',
    objectTypeSingular: 'Bank account',
    objectTypePlural: 'Bank accounts',
    idField() {
        return this.name;
    },
    mode: 'tabs',
    menuItem: paymentTracking,
    priority: 100,
    node: '@sage/xtrem-finance-data/BankAccount',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.reference({
                bind: 'financialSite',
                node: '@sage/xtrem-system/Site',
                valueField: 'name',
                title: 'Financial site',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line_4: ui.nestedFields.reference({
                bind: 'currency',
                node: '@sage/xtrem-master-data/Currency',
                valueField: 'name',
                title: 'Currency',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
        },

        optionsMenu: [
            {
                title: ui.localize('@sage/xtrem-finance-data/pages__bank-account__option_menu____title__all', 'All'),
                graphQLFilter: {},
            },
        ],
    },
    businessActions() {
        return [this.$standardSaveAction, this.$standardCancelAction];
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerSection() {
        return this.headerSection;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class BankAccount extends ui.Page<GraphApi> {
    @ui.decorators.section<BankAccount>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<BankAccount>({ title: 'Information', isTitleHidden: true })
    mainSection: ui.containers.Section;

    @ui.decorators.block<BankAccount>({
        parent() {
            return this.headerSection;
        },
        width: 'extra-large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.switchField<BankAccount>({
        parent() {
            return this.headerBlock;
        },
        title: 'Active',
        fetchesDefaults: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<BankAccount>({
        parent() {
            return this.headerBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<BankAccount>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<BankAccount>({
        parent() {
            return this.headerBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    // @ui.decorators.separatorField<BankAccount>({
    //     parent() {
    //         return this.headerBlock;
    //     },
    //     isFullWidth: true,
    //     isInvisible: true,
    // })
    // nameSeparator: ui.fields.Separator;

    @ui.decorators.block<BankAccount>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.referenceField<BankAccount, Site>({
        parent() {
            return this.mainBlock;
        },
        title: 'Financial site',
        isMandatory: true,
        fetchesDefaults: true,
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select financial site',
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID ', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isFinance' }),
            ui.nestedFields.reference<BankAccount, Site, Currency>({
                node: '@sage/xtrem-master-data/Currency',
                tunnelPage: '@sage/xtrem-master-data/Currency',
                title: 'Currency',
                bind: 'currency',
                valueField: 'name',
                columns: [
                    ui.nestedFields.technical({ bind: 'name' }),
                    ui.nestedFields.technical({ bind: 'id' }),
                    ui.nestedFields.technical({ bind: 'symbol' }),
                    ui.nestedFields.technical({ bind: 'decimalDigits' }),
                ],
            }),
        ],
        async onChange() {
            await this.$.fetchDefaults(['currency']);
        },
    })
    financialSite: ui.fields.Reference<Site>;

    @ui.decorators.referenceField<BankAccount, Currency>({
        parent() {
            return this.mainBlock;
        },
        title: 'Currency',
        lookupDialogTitle: 'Select currency',
        node: '@sage/xtrem-master-data/Currency',
        tunnelPage: '@sage/xtrem-master-data/Currency',
        valueField: 'name',
        helperTextField: 'id',
        isMandatory: true,
        fetchesDefaults: true,
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id', title: 'ISO 4217 code' }),
            ui.nestedFields.technical({ bind: 'decimalDigits' }),
            ui.nestedFields.technical({ bind: 'rounding' }),
            ui.nestedFields.text({ title: 'Symbol', bind: 'symbol', canFilter: false }),
            ui.nestedFields.technical({ bind: 'isActive' }),
        ],
        minLookupCharacters: 1,
        placeholder: 'Select currency',
        width: 'small',
    })
    currency: ui.fields.Reference<Currency>;
}
