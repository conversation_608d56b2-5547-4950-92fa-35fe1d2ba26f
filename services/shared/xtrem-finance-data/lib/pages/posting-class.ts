import type { ExtractEdgesPartial, Filter } from '@sage/xtrem-client';
import { extractEdges } from '@sage/xtrem-client';
import type {
    Account,
    FinanceItemType,
    GraphApi,
    PostingClassDefinition,
    PostingClassLine,
    PostingClassLineDetail as PostingClassLineDetailNode,
    PostingClassLine as PostingClassLineNode,
    PostingClass as PostingClassNode,
    PostingClassType,
} from '@sage/xtrem-finance-data-api';
import * as common from '@sage/xtrem-master-data/build/lib/client-functions/common';
import * as utils from '@sage/xtrem-master-data/build/lib/client-functions/utils';
import type { ChartOfAccount, Legislation } from '@sage/xtrem-structure-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { Tax } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';
import { merge } from 'lodash';
import { getIsSubjectToGlTaxExcludedAmount } from '../client-functions/common';
import { getTaxCategoryIds } from '../client-functions/posting-class';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<PostingClass, PostingClassNode>({
    title: 'Posting class',
    objectTypeSingular: 'Posting class',
    objectTypePlural: 'Posting classes',
    mode: 'tabs',
    idField() {
        if (!this.type.value || !this.name.value) {
            return null;
        }

        const typeValue = ui.localizeEnumMember('@sage/xtrem-finance-data/PostingClassType', this.type.value);

        return `${typeValue}-${this.name.value}`;
    },
    menuItem: featuresFinancialIntegration,
    priority: 400,
    node: '@sage/xtrem-finance-data/PostingClass',
    module: 'finance',
    areNavigationTabsHidden: true,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.select({ bind: 'type', optionType: '@sage/xtrem-finance-data/PostingClassType' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.switch({ bind: 'isDetailed', title: 'Detailed', isHiddenOnMainField: true }),
        },
        optionsMenu: [
            { title: 'All', graphQLFilter: {} },
            { title: 'Company', graphQLFilter: { type: { _eq: 'company' } } },
            { title: 'Customer', graphQLFilter: { type: { _eq: 'customer' } } },
            { title: 'Item', graphQLFilter: { type: { _eq: 'item' } } },
            { title: 'Resource', graphQLFilter: { type: { _eq: 'resource' } } },
            { title: 'Supplier', graphQLFilter: { type: { _eq: 'supplier' } } },
            { title: 'Tax', graphQLFilter: { type: { _eq: 'tax' } } },
        ],
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.selectLines, this.$standardSaveAction];
    },
    headerSection() {
        return this.generalSection;
    },
    async onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
        await this.init();
        this.selectedOptionsBeforeChange = this.financeItemType.value ?? [];
        this.pageLines = this.lines.value ?? [{ definition: { _id: '', financeItemType: [], id: '' } }]; //  for having a main copy of lines.
    },

    onDirtyStateUpdated(isDirty: boolean) {
        // The type is part of the title. After creating a new record with another type the onLoad is not
        // triggered (where the title is updated) -> this is the reason why we update the title here in onDirtyStateUpdated again
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class PostingClass extends ui.Page<GraphApi, PostingClassNode> {
    _chartOfAccount: ExtractEdgesPartial<ChartOfAccount> | undefined;

    isSubjectToGlTaxExcludedAmount: boolean;

    removedLines: ui.PartialNodeWithId<PostingClassLine>[];

    selectedOptionsBeforeChange: string[];

    pageLines: any;

    allOptions: FinanceItemType[] = ['stockItem', 'nonStockItem', 'serviceItem'];

    // TODO: Remove this workaround when the filter is fixed in the backend
    // Used to to filter the detail fields when we are creating a new posting class line
    // https://jira.sage.com/browse/XT-71936
    _detailFilter: { categoryIds: string[]; chartOfAccount?: ExtractEdgesPartial<ChartOfAccount> };

    @ui.decorators.section<PostingClass>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.block<PostingClass>({
        parent() {
            return this.generalSection;
        },
        title: 'Posting class',
        isTitleHidden: true,
    })
    generalBlock: ui.containers.Block;

    @ui.decorators.section<PostingClass>({ isTitleHidden: true }) linesSection: ui.containers.Section;

    @ui.decorators.textField<PostingClass>({ title: '_id' }) _id: ui.fields.Text;

    @ui.decorators.dropdownListField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        title: 'Type',
        optionType: '@sage/xtrem-finance-data/PostingClassType',
        isMandatory: true,
        onChange() {
            if (this.type.value !== 'item') {
                this.resetItemType();
            }
        },
    })
    type: ui.fields.DropdownList;

    @ui.decorators.switchField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        title: 'Detailed',
        onChange() {
            if (!this.isDetailed.value) {
                this.resetItemType();
            }
        },
        fetchesDefaults: true,
    })
    isDetailed: ui.fields.Switch;

    @ui.decorators.textField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.multiDropdownField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        options() {
            return this.$.isServiceOptionEnabled('landedCostOption')
                ? ['stockItem', 'nonStockItem', 'serviceItem', 'landedCostItem']
                : ['stockItem', 'nonStockItem', 'serviceItem'];
        },
        map(value: string) {
            switch (value) {
                case 'stockItem':
                    return ui.localizeEnumMember('@sage/xtrem-master-data/FinanceItemType', 'stockItem');
                case 'nonStockItem':
                    return ui.localizeEnumMember('@sage/xtrem-master-data/FinanceItemType', 'nonStockItem');
                case 'serviceItem':
                    return ui.localizeEnumMember('@sage/xtrem-master-data/FinanceItemType', 'serviceItem');
                case 'landedCostItem':
                    return ui.localizeEnumMember('@sage/xtrem-master-data/FinanceItemType', 'landedCostItem');
                default:
                    throw new Error(
                        ui.localize(
                            '@sage/xtrem-finance-data/pages__posting_class__finance_item_type_not_valid',
                            '{{financeItemType}} finance item type is not valid.',
                            { financeItemType: value },
                        ),
                    );
            }
        },
        title: 'Item type',
        isHidden() {
            return this.type.value !== 'item' || !this.isDetailed.value;
        },
        isDisabled: false,
        async onChange() {
            if (
                this.$.isServiceOptionEnabled('landedCostOption') &&
                !this.allOptions.some(option => option === 'landedCostItem')
            ) {
                this.allOptions.push('landedCostItem');
            }

            this.isStockItemAllowed.value = false;
            this.isNonStockItemAllowed.value = false;
            this.isServiceItemAllowed.value = false;
            this.isLandedCostItemAllowed.value = false;

            const selectedOptions: FinanceItemType[] = [];
            const unSelectedOptions: FinanceItemType[] = this.allOptions.slice(0, this.allOptions.length);

            if (this.financeItemType.value && this.financeItemType.value.length > 0) {
                if (this.financeItemType.value.some(financeItemType => financeItemType === 'stockItem')) {
                    this.isStockItemAllowed.value = true;
                    selectedOptions.push('stockItem');
                    unSelectedOptions.splice(unSelectedOptions.indexOf('stockItem'), 1);
                }
                if (this.financeItemType.value.some(financeItemType => financeItemType === 'nonStockItem')) {
                    this.isNonStockItemAllowed.value = true;
                    selectedOptions.push('nonStockItem');
                    unSelectedOptions.splice(unSelectedOptions.indexOf('nonStockItem'), 1);
                }
                if (this.financeItemType.value.some(financeItemType => financeItemType === 'serviceItem')) {
                    this.isServiceItemAllowed.value = true;
                    selectedOptions.push('serviceItem');
                    unSelectedOptions.splice(unSelectedOptions.indexOf('serviceItem'), 1);
                }
                if (this.financeItemType.value.some(financeItemType => financeItemType === 'landedCostItem')) {
                    this.isLandedCostItemAllowed.value = true;
                    selectedOptions.push('landedCostItem');
                    unSelectedOptions.splice(unSelectedOptions.indexOf('landedCostItem'), 1);
                }
            }

            if (this.selectedOptionsBeforeChange.length > selectedOptions.length) {
                const removedOptions = this.selectedOptionsBeforeChange.filter(
                    option => !selectedOptions.includes(option as FinanceItemType),
                );
                const lines = this.lines.value.filter(
                    line =>
                        line.definition?.financeItemType?.some(
                            lineFinanceItemType =>
                                lineFinanceItemType && unSelectedOptions.includes(lineFinanceItemType),
                        ) &&
                        !line.definition?.financeItemType?.some(
                            lineFinanceItemType => lineFinanceItemType && selectedOptions.includes(lineFinanceItemType),
                        ),
                );
                if (lines && lines.length) {
                    const confirmation = await utils.confirmDialogToBoolean(
                        this.$.dialog.confirmation(
                            'warn',
                            ui.localize(
                                '@sage/xtrem-finance-data/pages__posting-class_line_deletion_title',
                                'Confirm line deletion',
                            ),
                            ui.localize(
                                '@sage/xtrem-finance-data/pages__posting-class_line_deletion',
                                'You are about to delete all the lines for this posting class type: {{financeItemType}}.',
                                {
                                    financeItemType: removedOptions.length
                                        ? ui.localizeEnumMember(
                                              '@sage/xtrem-finance-data/FinanceItemType',
                                              removedOptions[0],
                                          )
                                        : '',
                                },
                            ),
                            {
                                acceptButton: {
                                    text: ui.localize('@sage/xtrem-finance-data/confirm-update', 'Confirm update'),
                                },
                                cancelButton: {
                                    text: ui.localize('@sage/xtrem-finance-data/cancel', 'Cancel'),
                                },
                            },
                        ),
                    );
                    if (confirmation) {
                        lines.forEach(line => {
                            this.lines.removeRecord(line._id, 0);
                        });
                    }
                }
            }
            this.selectedOptionsBeforeChange = selectedOptions;
        },
    })
    financeItemType: ui.fields.MultiDropdown;

    @ui.decorators.checkboxField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        isHidden: true,
    })
    isStockItemAllowed: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        isHidden: true,
    })
    isNonStockItemAllowed: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        isHidden: true,
    })
    isServiceItemAllowed: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        isHidden: true,
    })
    isLandedCostItemAllowed: ui.fields.Checkbox;

    @ui.decorators.textField<PostingClass>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        width: 'medium',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.nestedGridField<PostingClass, [PostingClassLineNode, PostingClassLineDetailNode]>({
        parent() {
            return this.linesSection;
        },
        bind: 'lines',
        canSelect: false,
        fieldActions() {
            return [this.addLine];
        },
        // setup node, should not raise performance issues. we need all records loaded because we must remove all records according to the selected item type
        pageSize: 200,
        levels: [
            {
                node: '@sage/xtrem-finance-data/PostingClassLine',
                childProperty: 'details',
                orderBy: { chartOfAccount: { name: +1 }, definition: { accountTypeName: +1 } },
                columns: [
                    ui.nestedFields.checkbox({
                        bind: 'updateAccountTaxManagement' as any,
                        isTransientInput: true,
                        isHidden: true,
                    }),
                    ui.nestedFields.reference<PostingClass, PostingClassLineNode, ChartOfAccount>({
                        title: 'Chart of accounts',
                        bind: 'chartOfAccount',
                        node: '@sage/xtrem-structure/ChartOfAccount',
                        valueField: 'name',
                        minLookupCharacters: 0,
                        columns: [
                            ui.nestedFields.text({ bind: 'name' }),
                            ui.nestedFields.technical<PostingClass, ChartOfAccount, Legislation>({
                                bind: 'legislation',
                                node: '@sage/xtrem-structure/Legislation',
                                nestedFields: [
                                    ui.nestedFields.text({ bind: 'name' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: '_id' }),
                                ],
                            }),
                        ],
                        isReadOnly(_value, rowValue) {
                            return rowValue?.hasDetails || false;
                        },
                        async onChange(_rowId, rowData: ui.PartialNodeWithId<PostingClassLineNode>) {
                            // TODO: Remove this workaround when the filter is fixed in the backend
                            // Used to to filter the detail fields when we are creating a new posting class line
                            // https://jira.sage.com/browse/XT-71936
                            this.lines.addOrUpdateRecordValue(rowData, 0, _rowId);
                            this._detailFilter = {
                                categoryIds: await getTaxCategoryIds({
                                    page: this,
                                    legislation: rowData.chartOfAccount?.legislation,
                                }),
                                chartOfAccount: rowData.chartOfAccount,
                            };
                        },
                    }),
                    ui.nestedFields.reference<PostingClass, PostingClassLineNode, PostingClassDefinition>({
                        title: 'Account type name',
                        bind: 'definition',
                        node: '@sage/xtrem-finance-data/PostingClassDefinition',
                        valueField: 'accountTypeName',
                        width: 'large',
                        isMandatory: true,
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'accountTypeName' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'postingClassType' }),
                            ui.nestedFields.technical({ bind: 'isDetailed' }),
                            ui.nestedFields.technical({ bind: 'isStockItemAllowed' }),
                            ui.nestedFields.technical({ bind: 'isNonStockItemAllowed' }),
                            ui.nestedFields.technical({ bind: 'isServiceItemAllowed' }),
                            ui.nestedFields.technical({ bind: 'isLandedCostItemAllowed' }),
                            ui.nestedFields.technical({ bind: 'financeItemType' }),
                            ui.nestedFields.technical({ bind: 'additionalCriteria' }),
                        ],
                        isReadOnly(_value, rowValue) {
                            return rowValue?.hasDetails || false;
                        },
                        filter() {
                            const filter: Filter<PostingClassDefinition> = {
                                postingClassType: this.type.value as PostingClassType,
                                isDetailed: { _eq: this.isDetailed.value },
                            };

                            if (this.type.value === 'item' && this.isDetailed.value) {
                                merge(filter, this.getFinanceItemTypeFilter());
                            }
                            return filter;
                        },
                        onChange(rowId, rowData: ui.PartialNodeWithId<PostingClassLineNode>) {
                            rowData.isStockItemAllowed = rowData.definition?.isStockItemAllowed;
                            rowData.isNonStockItemAllowed = rowData.definition?.isNonStockItemAllowed;
                            rowData.isServiceItemAllowed = rowData.definition?.isServiceItemAllowed;
                            rowData.isLandedCostItemAllowed = rowData.definition?.isLandedCostItemAllowed;
                            this.lines.addOrUpdateRecordValue(rowData, 0, rowId);
                        },
                    }),
                    ui.nestedFields.select<PostingClass, PostingClassLineNode>({
                        title: 'Additional criteria',
                        bind: { definition: { additionalCriteria: true } },
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<PostingClass, PostingClassLineNode, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        valueField: 'name',
                        width: 'large',
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'taxManagement' }),
                        ],
                        async onChange(rowId, rowData) {
                            rowData.updateAccountTaxManagement = await this.taxManagementUpdate(rowData);
                            this.lines.addOrUpdateRecordValue(rowData, 0, rowId);
                        },
                    }),
                    ui.nestedFields.text<PostingClass, PostingClassLineNode>({
                        title: 'Account ID',
                        bind: { account: { id: true } },
                        isReadOnly: true,
                    }),
                    ui.nestedFields.checkbox({
                        title: 'Stock items',
                        bind: 'isStockItemAllowed',
                        isReadOnly: true,
                        isHidden() {
                            return this.type.value !== 'item';
                        },
                    }),
                    ui.nestedFields.checkbox({
                        title: 'Non-stock items',
                        bind: 'isNonStockItemAllowed',
                        isReadOnly: true,
                        isHidden() {
                            return this.type.value !== 'item';
                        },
                    }),
                    ui.nestedFields.checkbox({
                        title: 'Service items',
                        bind: 'isServiceItemAllowed',
                        isReadOnly: true,
                        isHidden() {
                            return this.type.value !== 'item';
                        },
                    }),
                    ui.nestedFields.checkbox({
                        title: 'Landed cost items',
                        bind: 'isLandedCostItemAllowed',
                        isReadOnly: true,
                        isHidden() {
                            return this.type.value !== 'item' || !this.$.isServiceOptionEnabled('landedCostOption');
                        },
                    }),
                    ui.nestedFields.technical({ bind: 'hasDetails' }),
                ],
                dropdownActions: [
                    {
                        icon: 'add',
                        title: 'Add detail',
                        isHidden(_rowId, rowData) {
                            return rowData?.definition ? rowData.definition.additionalCriteria === null : true;
                        },
                        onClick(rowID, rowData) {
                            this.lines.addRecord(
                                {
                                    account: {},
                                    postingClassLine: rowData as unknown as ExtractEdgesPartial<PostingClassLineNode>,
                                    tax: {},
                                },
                                1,
                                rowID,
                            );
                        },
                    },
                    {
                        icon: 'bin',
                        title: 'Delete',
                        isDestructive: true,
                        onClick(rowID, rowData) {
                            if (+rowData._id > 0) {
                                // remember deleted lines only if they are already stored in the DB (positive _id)
                                this.removedLines.push(
                                    rowData as unknown as ui.PartialNodeWithId<PostingClassLineNode>,
                                );
                            }
                            this.lines.removeRecord(rowID, 0);
                        },
                    },
                ],
            },
            {
                node: '@sage/xtrem-finance-data/PostingClassLineDetail',
                orderBy: { tax: { taxCategory: { id: +1 }, name: +1 } },
                columns: [
                    ui.nestedFields.reference<PostingClass, PostingClassLineDetailNode, Tax>({
                        node: '@sage/xtrem-tax/Tax',
                        title: 'Tax',
                        bind: 'tax',
                        valueField: 'name',
                        width: 'large',
                        filter(rowValue) {
                            // TODO: Remove this workaround when the filter is fixed in the backend
                            // Used to to filter the detail fields when we are creating a new posting class line
                            // https://jira.sage.com/browse/XT-71936
                            if (+rowValue._id < 0 && this._detailFilter) {
                                return {
                                    country: {
                                        legislation: { id: this._detailFilter.chartOfAccount?.legislation?.id },
                                    },
                                    taxCategory: { id: { _in: this._detailFilter.categoryIds } },
                                };
                            }
                            return {};
                        },
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                            ui.nestedFields.technical({
                                bind: 'taxCategory',
                                node: '@sage/xtrem-tax/TaxCategory',
                                nestedFields: [
                                    ui.nestedFields.technical({ bind: '_id' }),
                                    ui.nestedFields.technical({ bind: 'id' }),
                                    ui.nestedFields.technical({ bind: 'name' }),
                                ],
                            }),
                            ui.nestedFields.text({ title: 'Country', bind: { country: { name: true } } }),
                        ],
                    }),
                    ui.nestedFields.text<PostingClass, PostingClassLineDetailNode>({
                        title: 'Tax category',
                        bind: { tax: { taxCategory: { name: true } } },
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<PostingClass, PostingClassLineDetailNode, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        valueField: 'name',
                        width: 'large',
                        isMandatory: true,
                        filter(rowValue) {
                            // TODO: Remove this workaround when the filter is fixed in the backend
                            // Used to to filter the detail fields when we are creating a new posting class line/new posting class line detail
                            // https://jira.sage.com/browse/XT-71936
                            if (+rowValue._id < 0 && this._detailFilter) {
                                return { chartOfAccount: { _id: this._detailFilter.chartOfAccount?._id } };
                            }
                            return {};
                        },
                        columns: [
                            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
                            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
                            ui.nestedFields.technical({ bind: 'taxManagement' }),
                            ui.nestedFields.text({
                                title: 'Chart of accounts',
                                bind: { chartOfAccount: { name: true } },
                            }),
                        ],
                    }),
                    ui.nestedFields.text<PostingClass, PostingClassLineDetailNode>({
                        title: 'Account ID',
                        bind: { account: { id: true } },
                        isReadOnly: true,
                    }),
                ],
                dropdownActions: [
                    {
                        icon: 'bin',
                        title: 'Delete',
                        isDestructive: true,
                        onClick(rowID) {
                            this.lines.removeRecord(rowID, 1);
                        },
                    },
                ],
            },
        ],
    })
    lines: ui.fields.NestedGrid<[PostingClassLineNode, PostingClassLineDetailNode], PostingClass>;

    @ui.decorators.pageAction<PostingClass>({
        icon: 'add',
        title: 'Add line',
        async onClick() {
            this.lines.addRecord({ chartOfAccount: this._chartOfAccount }, 0, undefined);
            // TODO: Remove this workaround when the filter is fixed in the backend
            // Use to to filter the detail fields when we are creating a new posting class line
            // https://jira.sage.com/browse/XT-71936
            this._detailFilter = {
                categoryIds: await getTaxCategoryIds({
                    page: this,
                    legislation: this._chartOfAccount?.legislation,
                }),
                chartOfAccount: this._chartOfAccount,
            };
        },
    })
    addLine: ui.PageAction;

    // ----------------------------------------------------------
    // Section dedicated to the Load lines functionality - Begin
    // ----------------------------------------------------------
    @ui.decorators.section<PostingClass>({ title: 'Load lines', isTitleHidden: true })
    loadLinesSection: ui.containers.Section;

    @ui.decorators.block<PostingClass>({
        parent() {
            return this.loadLinesSection;
        },
        title: 'Posting class definitions',
        isTitleHidden: true,
    })
    loadLinesBlock: ui.containers.Block;

    @ui.decorators.referenceField<PostingClass, PostingClassNode>({
        parent() {
            return this.loadLinesBlock;
        },
        title: 'Posting class template',
        isTransient: true,
        placeholder: 'Select posting class',
        node: '@sage/xtrem-finance-data/PostingClass',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select posting class',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.select({
                bind: 'type',
                title: 'Type',
                optionType: '@sage/xtrem-finance-data/PostingClassType',
            }),
            ui.nestedFields.technical({ bind: 'isDetailed' }),
        ],
        async onChange() {
            await this.getAccountTypes();
            if (this.postingClass.value) {
                this.copyAccounts.isDisabled = false;
                if (this.type.value === 'item' && this.isDetailed.value) {
                    await this.$.dialog.message(
                        'info',
                        ui.localize(
                            '@sage/xtrem-finance-data/pages__posting-class-template__info_title',
                            'Posting class',
                        ),
                        ui.localize(
                            '@sage/xtrem-finance-data/pages__posting-class-template__info',
                            'Only the lines that apply to the same item types are selected.',
                        ),
                        {
                            fullScreen: false,
                            rightAligned: false,
                            acceptButton: { isDisabled: false, isHidden: false, text: 'ok' },
                        },
                    );
                }
            } else {
                this.copyAccounts.value = false;
                this.copyAccounts.isDisabled = true;
            }
        },
        filter() {
            const filter: Filter<PostingClassNode> = {
                type: this.type.value as PostingClassType,
                isDetailed: { _eq: this.isDetailed.value },
                _id: { _ne: this._id.value || '0' },
            };
            if (this.type.value === 'item' && this.isDetailed.value) {
                merge(filter, this.getFinanceItemTypeFilter());
            }
            return filter;
        },
    })
    postingClass: ui.fields.Reference<PostingClassNode>;

    @ui.decorators.checkboxField<PostingClass>({
        title: 'Copy accounts',
        isTransient: true,
        parent() {
            return this.loadLinesBlock;
        },
        isDisabled: true,
    })
    copyAccounts: ui.fields.Checkbox;

    @ui.decorators.checkboxField<PostingClass>({
        title: 'Select all lines',
        isTransient: true,
        parent() {
            return this.loadLinesBlock;
        },
        onChange() {
            common.selectUnselectCheckboxes(this.accountTypes, this.selectAll.value);
        },
    })
    selectAll: ui.fields.Checkbox;

    @ui.decorators.tableField<PostingClass, PostingClassLineNode>({
        parent() {
            return this.loadLinesSection;
        },
        isReadOnly: true,
        isTransient: true,
        title: 'Account types',
        isTitleHidden: true,
        isChangeIndicatorDisabled: true,
        node: '@sage/xtrem-finance-data/PostingClassLine',
        orderBy: { chartOfAccount: { legislation: { name: +1 } }, definition: { accountTypeName: +1 } },
        columns: [
            ui.nestedFields.reference<PostingClass, PostingClassLineNode, ChartOfAccount>({
                title: 'Legislation',
                bind: 'chartOfAccount',
                node: '@sage/xtrem-structure/ChartOfAccount',
                valueField: { legislation: { name: true } },
            }),
            ui.nestedFields.reference<PostingClass, PostingClassLineNode, PostingClassDefinition>({
                title: 'Account type name',
                bind: 'definition',
                node: '@sage/xtrem-finance-data/PostingClassDefinition',
                valueField: 'accountTypeName',
            }),
            ui.nestedFields.reference<PostingClass, PostingClassLineNode, Account>({
                bind: 'account',
                node: '@sage/xtrem-finance-data/Account',
                valueField: 'name',
                isHidden: true,
            }),
        ],
    })
    accountTypes: ui.fields.Table<PostingClassLineNode>;

    @ui.decorators.pageAction<PostingClass>({
        title: 'Select lines',
        async onClick() {
            this.copyAccounts.value = false;
            this.copyAccounts.isDisabled = true;
            await this.getAccountTypes();
            if (this.accountTypes.value.length) {
                await this.$.dialog
                    .custom('info', this.loadLinesSection, {
                        dialogTitle: ui.localize(
                            '@sage/xtrem-finance-data/pages__posting_class__selectLines____title',
                            'Select lines',
                        ),
                        cancelButton: { isHidden: false },
                        acceptButton: { isHidden: false, text: 'Ok' },
                    })
                    .then(() => {
                        this.accountTypes.selectedRecords.forEach(selectedAccountType => {
                            const selectedPostingClassDefinition =
                                this.accountTypes.getRecordValue(selectedAccountType);
                            this.lines.addRecord(
                                {
                                    chartOfAccount: selectedPostingClassDefinition?.chartOfAccount,
                                    definition: selectedPostingClassDefinition?.definition,
                                    account:
                                        selectedPostingClassDefinition?.account && this.copyAccounts.value
                                            ? selectedPostingClassDefinition?.account
                                            : undefined,
                                    isStockItemAllowed: selectedPostingClassDefinition?.definition?.isStockItemAllowed,
                                    isNonStockItemAllowed:
                                        selectedPostingClassDefinition?.definition?.isNonStockItemAllowed,
                                    isServiceItemAllowed:
                                        selectedPostingClassDefinition?.definition?.isServiceItemAllowed,
                                    isLandedCostItemAllowed:
                                        selectedPostingClassDefinition?.definition?.isLandedCostItemAllowed,

                                    details: selectedPostingClassDefinition?.details,
                                },
                                0,
                                undefined,
                            );
                        });
                    })
                    .finally(() => {
                        this.accountTypes.unselectAllRecords();
                        this.selectAll.value = false;
                        this.postingClass.value = null;
                        this.copyAccounts.value = false;
                        this.copyAccounts.isDisabled = true;
                    });
            } else {
                await this.$.dialog.message(
                    'info',
                    ui.localize('@sage/xtrem-finance-data/pages__posting-class__load_lines', 'Load lines'),
                    ui.localize(
                        '@sage/xtrem-finance-data/pages__posting-class__no_new_lines',
                        'There are no new lines to add.',
                    ),
                    {
                        fullScreen: false,
                        rightAligned: false,
                        acceptButton: { isDisabled: false, isHidden: false, text: 'ok' },
                    },
                );
            }
        },
    })
    selectLines: ui.PageAction;

    async getAccountTypes() {
        // get the _ids of the deleted (not yet saved) posting class definitions to exclude them from the already assigned ones
        const deletedPostingClassDefinitions = this.removedLines.map(line => line.definition?._id);
        // we need to read the already assigned lines from the DB as this.lines.value only has as many
        // records as have been loaded through paging: 20 directly after loading, between 20 and 40 after the first paging...
        let postingClassLines = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/PostingClassLine')
                .query(
                    ui.queryUtils.edgesSelector(
                        {
                            definition: { _id: true },
                            details: { query: { edges: { node: { _id: true } } } },
                        },
                        {
                            filter: {
                                postingClass: { _id: this._id.value },
                                definition: {
                                    legislation: { isActive: true },
                                    _id: { _nin: deletedPostingClassDefinitions },
                                },
                            },
                            first: 500,
                        },
                    ),
                )
                .execute(),
        );

        // get an array of _ids of already assigned posting class definitions found in the DB (except the ones deleted in the session) and
        // add the ones already assigned in this session (not yet saved = negative _id)
        const usedPostingClassDefinitions: string[] = postingClassLines
            .map(line => line.definition?._id || '0')
            .concat(this.lines.value.filter(line => +line._id < 0).map(line => line.definition?._id || '0'));

        this.selectAll.value = false;
        this.accountTypes.unselectAllRecords();
        this.accountTypes.value = [];

        if (this.postingClass.value) {
            postingClassLines = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-finance-data/PostingClassLine')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                definition: {
                                    _id: true,
                                    id: true,
                                    accountTypeName: true,
                                    isStockItemAllowed: true,
                                    isNonStockItemAllowed: true,
                                    isServiceItemAllowed: true,
                                    isLandedCostItemAllowed: true,
                                    financeItemType: true,
                                    additionalCriteria: true,
                                },
                                chartOfAccount: {
                                    _id: true,
                                    name: true,
                                    legislation: { _id: true, id: true, name: true },
                                },
                                account: { _id: true, id: true, composedDescription: true, name: true },
                                details: {
                                    query: {
                                        edges: {
                                            node: {
                                                tax: {
                                                    _id: true,
                                                    name: true,
                                                    id: true,
                                                    taxCategory: { _id: true, id: true, name: true },
                                                },
                                                account: { _id: true, id: true, name: true },
                                            },
                                        },
                                    },
                                },
                            },
                            {
                                filter: this.getPostingClassLineFilter(
                                    this.postingClass.value._id ?? '',
                                    usedPostingClassDefinitions,
                                    this.type.value as PostingClassType,
                                ),
                            },
                        ),
                    )
                    .execute(),
            );
            postingClassLines.forEach(postingClassLine => {
                this.accountTypes.addRecord(postingClassLine);
            });
        } else {
            const postingClassDefinitions = extractEdges(
                await this.$.graph
                    .node('@sage/xtrem-finance-data/PostingClassDefinition')
                    .query(
                        ui.queryUtils.edgesSelector(
                            {
                                _id: true,
                                legislation: {
                                    _id: true,
                                    id: true,
                                    name: true,
                                    defaultChartOfAccount: {
                                        _id: true,
                                        name: true,
                                        legislation: { _id: true, id: true, name: true },
                                    },
                                },
                                accountTypeName: true,
                                isStockItemAllowed: true,
                                isNonStockItemAllowed: true,
                                isServiceItemAllowed: true,
                                isLandedCostItemAllowed: true,
                                financeItemType: true,
                                additionalCriteria: true,
                            },
                            {
                                filter: this.getPostingClassDefinitionFilter(
                                    usedPostingClassDefinitions,
                                    this.type.value as PostingClassType,
                                ),
                            },
                        ),
                    )
                    .execute(),
            );
            postingClassDefinitions.forEach(postingClassDefinition => {
                this.accountTypes.addRecord({
                    chartOfAccount: postingClassDefinition.legislation.defaultChartOfAccount,
                    definition: postingClassDefinition,
                    account: {},
                });
            });
        }
    }
    // ----------------------------------------------------------
    // Section dedicated to the Load lines functionality - End
    // ----------------------------------------------------------

    async setChartOfAccounts() {
        this._chartOfAccount =
            this.lines.value && this.lines.value.length > 0
                ? this.lines.value[0].chartOfAccount || {}
                : await this.$.graph
                      .node('@sage/xtrem-structure/ChartOfAccount')
                      .queries.getSingleChartOfAccount({ _id: true, isActive: true, name: true }, { dummy: '' })
                      .execute();
    }

    async init() {
        this.removedLines = [];
        await this.setChartOfAccounts();
        this.isSubjectToGlTaxExcludedAmount = await getIsSubjectToGlTaxExcludedAmount(this, 'ZA');
    }

    async taxManagementUpdate(rowData: PostingClassLineNode): Promise<boolean> {
        if (
            this.isSubjectToGlTaxExcludedAmount &&
            this.type.value === 'tax' &&
            rowData.account.taxManagement === 'other' &&
            rowData.chartOfAccount.legislation.id === 'ZA'
        ) {
            const confirmation = await utils.confirmDialogToBoolean(
                this.$.dialog.confirmation(
                    'warn',
                    ui.localize(
                        '@sage/xtrem-finance-data/update-account-tax-management-title',
                        'Confirm account tax management update.',
                    ),
                    ui.localize(
                        '@sage/xtrem-finance-data/update-account-tax-management-context',
                        'You are about to set the account management to Tax.',
                    ),
                    {
                        acceptButton: {
                            text: ui.localize('@sage/xtrem-finance-data/confirm-update', 'Confirm update'),
                        },
                        cancelButton: {
                            text: ui.localize('@sage/xtrem-finance-data/cancel', 'Cancel'),
                        },
                    },
                ),
            );
            if (!confirmation) {
                await this.$.dialog.message(
                    'error',
                    ui.localize('@sage/xtrem-finance-data/error', 'Error'),
                    ui.localize(
                        '@sage/xtrem-finance-data/cannot_set_tax_with_account_not_subjected_to_taxes',
                        'You cannot set a tax detail with an account that is not subjected to taxes.',
                    ),
                    { resolveOnCancel: true },
                );
            }
            return confirmation;
        }
        return false;
    }

    resetItemType() {
        this.isStockItemAllowed.value = false;
        this.isNonStockItemAllowed.value = false;
        this.isServiceItemAllowed.value = false;
        this.isLandedCostItemAllowed.value = false;
        this.financeItemType.value = [];
    }

    getFinanceItemTypeFilter() {
        return {
            _or: this.financeItemType.value?.map(financeItemType => {
                switch (financeItemType) {
                    case 'stockItem':
                        return { isStockItemAllowed: true };
                    case 'nonStockItem':
                        return { isNonStockItemAllowed: true };
                    case 'serviceItem':
                        return { isServiceItemAllowed: true };
                    case 'landedCostItem':
                        return { isLandedCostItemAllowed: true };
                    default:
                        return {};
                }
            }),
        };
    }

    getPostingClassLineFilter(
        postingClassSysId: string,
        usedPostingClassDefinitions: string[],
        postingClassType: PostingClassType,
    ) {
        const postingClassLineFilter: Filter<PostingClassLine> = {
            postingClass: { _id: postingClassSysId },
            definition: {
                legislation: { isActive: true },
                _id: { _nin: usedPostingClassDefinitions },
            },
        };

        if (postingClassType === 'item' && this.isDetailed.value) {
            merge(postingClassLineFilter, this.getFinanceItemTypeFilter());
        }
        return postingClassLineFilter;
    }

    getPostingClassDefinitionFilter(usedPostingClassDefinitions: string[], postingClassType: PostingClassType) {
        const postingClassDefinitionFilter: Filter<PostingClassDefinition> = {
            legislation: { isActive: true },
            isDetailed: this.isDetailed.value || undefined,
            postingClassType,
            _id: { _nin: usedPostingClassDefinitions },
        };

        if (postingClassType === 'item' && this.isDetailed.value) {
            merge(postingClassDefinitionFilter, this.getFinanceItemTypeFilter());
        }
        return postingClassDefinitionFilter;
    }
}
