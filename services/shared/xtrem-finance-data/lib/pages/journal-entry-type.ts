import type {
    Account,
    GraphApi,
    Journal,
    JournalEntryTypeLineBinding,
    JournalEntryType as JournalEntryTypeNode,
    PostingClass,
    PostingClassDefinition,
    PostingClassLineDetail as PostingClassLineDetailNode,
    PostingClassLine as PostingClassLineNode,
} from '@sage/xtrem-finance-data-api';
import type { Legislation } from '@sage/xtrem-structure-api';
import { getLegislationOptionsMenu } from '@sage/xtrem-structure/build/lib/client-functions/options-menu';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { Tax } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<JournalEntryType, JournalEntryTypeNode>({
    title: 'Journal entry type',
    objectTypeSingular: 'Journal entry type',
    objectTypePlural: 'Journal entry types',
    idField() {
        return this.name;
    },
    menuItem: featuresFinancialIntegration,
    priority: 500,
    node: '@sage/xtrem-finance-data/JournalEntryType',
    module: 'finance',
    areNavigationTabsHidden: true,
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'name' }),
            titleRight: ui.nestedFields.reference({ bind: 'legislation', tunnelPage: undefined }),
        },
        optionsMenu(graph) {
            return getLegislationOptionsMenu(graph);
        },
    },
    businessActions() {
        return [this.$standardCancelAction, this.$standardSaveAction];
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    onLoad() {
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
        this.headerAccountType.isMandatory = this.targetDocumentType.value !== 'journalEntry';
        this.headerAccountType.isDisabled = this.targetDocumentType.value === 'journalEntry';
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.$standardSaveAction,
            cancel: this.$standardCancelAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class JournalEntryType extends ui.Page<GraphApi> {
    @ui.decorators.section<JournalEntryType>({ title: 'General', isTitleHidden: true })
    generalSection: ui.containers.Section;

    @ui.decorators.block<JournalEntryType>({
        parent() {
            return this.generalSection;
        },
    })
    generalBlock: ui.containers.Block;

    // Side panel for linked posting class: general section
    @ui.decorators.section<JournalEntryType>({ isHidden: true })
    sidePanelGeneralSection: ui.containers.Section;

    @ui.decorators.block<JournalEntryType>({
        title: 'Header',
        parent() {
            return this.sidePanelGeneralSection;
        },
        isHidden() {
            return !this.headerAccountType.value;
        },
    })
    sidePanelHeaderBlock: ui.containers.Block;

    @ui.decorators.switchField<JournalEntryType>({
        parent() {
            return this.sidePanelHeaderBlock;
        },
        title: 'Detailed',
        isTransient: true,
        width: 'small',
        isReadOnly: true,
    })
    sidePanelHeaderIsDetailed: ui.fields.Switch;

    @ui.decorators.textField<JournalEntryType>({
        parent() {
            return this.sidePanelHeaderBlock;
        },
        title: 'Account type name',
        isTransient: true,
        width: 'small',
        isReadOnly: true,
    })
    sidePanelHeaderAccountTypeName: ui.fields.Text;

    @ui.decorators.nestedGridField<JournalEntryType, [PostingClassLineNode, PostingClassLineDetailNode]>({
        parent() {
            return this.sidePanelGeneralSection;
        },
        canUserHideColumns: false,
        canFilter: false,
        canSelect: false,
        isHelperTextHidden: true,
        isTransient: true,
        isDisabled: true,
        levels: [
            {
                node: '@sage/xtrem-finance-data/PostingClassLine',
                childProperty: 'details',
                orderBy: { postingClass: { name: 1 } },
                columns: [
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineNode, PostingClass>({
                        title: 'Posting class name',
                        bind: 'postingClass',
                        node: '@sage/xtrem-finance-data/PostingClass',
                        valueField: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineNode, Account>({
                        title: 'Account',
                        bind: 'account',
                        node: '@sage/xtrem-finance-data/Account',
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-finance-data/PostingClassLineDetail',
                orderBy: { tax: { name: 1 } },
                columns: [
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineDetailNode, Tax>({
                        node: '@sage/xtrem-tax/Tax',
                        title: 'Tax',
                        bind: 'tax',
                        valueField: 'name',
                        width: 'large',
                    }),
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineDetailNode, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        valueField: 'composedDescription',
                        width: 'large',
                    }),
                ],
            },
        ],
    })
    postingClassHeaderAccounts: ui.fields.NestedGrid<
        [PostingClassLineNode, PostingClassLineDetailNode],
        JournalEntryType
    >;

    @ui.decorators.block<JournalEntryType>({
        title: 'Lines',
        parent() {
            return this.sidePanelGeneralSection;
        },
    })
    sidePanelLinesBlock: ui.containers.Block;

    @ui.decorators.switchField<JournalEntryType>({
        parent() {
            return this.sidePanelLinesBlock;
        },
        title: 'Detailed',
        isTransient: true,
        width: 'small',
        isReadOnly: true,
    })
    sidePanelIsDetailed: ui.fields.Switch;

    @ui.decorators.textField<JournalEntryType>({
        parent() {
            return this.sidePanelLinesBlock;
        },
        title: 'Account type name',
        isTransient: true,
        width: 'small',
        isReadOnly: true,
    })
    sidePanelAccountTypeName: ui.fields.Text;

    @ui.decorators.nestedGridField<JournalEntryType, [PostingClassLineNode, PostingClassLineDetailNode]>({
        parent() {
            return this.sidePanelGeneralSection;
        },
        canUserHideColumns: false,
        canFilter: false,
        canSelect: false,
        isHelperTextHidden: true,
        isTransient: true,
        isDisabled: true,
        levels: [
            {
                node: '@sage/xtrem-finance-data/PostingClassLine',
                childProperty: 'details',
                orderBy: { postingClass: { name: 1 } },
                columns: [
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineNode, PostingClass>({
                        title: 'Posting class name',
                        bind: 'postingClass',
                        node: '@sage/xtrem-finance-data/PostingClass',
                        valueField: 'name',
                        isReadOnly: true,
                    }),
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineNode, Account>({
                        title: 'Account',
                        bind: 'account',
                        node: '@sage/xtrem-finance-data/Account',
                        valueField: 'composedDescription',
                        isReadOnly: true,
                    }),
                ],
            },
            {
                node: '@sage/xtrem-finance-data/PostingClassLineDetail',
                orderBy: { tax: { name: 1 } },
                columns: [
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineDetailNode, Tax>({
                        node: '@sage/xtrem-tax/Tax',
                        title: 'Tax',
                        bind: 'tax',
                        valueField: 'name',
                        width: 'large',
                    }),
                    ui.nestedFields.reference<JournalEntryType, PostingClassLineDetailNode, Account>({
                        node: '@sage/xtrem-finance-data/Account',
                        title: 'Account',
                        bind: 'account',
                        valueField: 'composedDescription',
                        width: 'large',
                    }),
                ],
            },
        ],
    })
    postingClassAccounts: ui.fields.NestedGrid<[PostingClassLineNode, PostingClassLineDetailNode], JournalEntryType>;
    // End side panel

    @ui.decorators.textField<JournalEntryType>({ title: '_id' })
    _id: ui.fields.Text;

    @ui.decorators.switchField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        title: 'Name',
        width: 'medium',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.referenceField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select legislation',
        placeholder: 'Select legislation',
        isMandatory: true,
    })
    legislation: ui.fields.Reference<Legislation>;

    @ui.decorators.switchField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        title: 'Immediate posting',
        width: 'large',
        isReadOnly: true,
    })
    immediatePosting: ui.fields.Switch;

    @ui.decorators.separatorField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    immediatePostingSeparator: ui.fields.Separator;

    @ui.decorators.dropdownListField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        title: 'Document type',
        optionType: '@sage/xtrem-finance-data/FinanceDocumentType',
        width: 'large',
    })
    documentType: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        title: 'Target document type',
        optionType: '@sage/xtrem-finance-data/TargetDocumentType',
        width: 'large',
        onChange() {
            this.headerAccountType.isMandatory = this.targetDocumentType.value !== 'journalEntry';
            this.headerAccountType.isDisabled = this.targetDocumentType.value === 'journalEntry';
            this.headerJournal.isMandatory = this.targetDocumentType.value === 'journalEntry';
            this.headerJournal.isDisabled = this.targetDocumentType.value !== 'journalEntry';
            if (this.targetDocumentType.value === 'journalEntry') {
                this.headerAccountType.value = null;
                this.headerAmountType.value = null;
            } else {
                this.headerJournal.value = null;
                this.headerPostingDate.value = null;
            }
        },
    })
    targetDocumentType: ui.fields.DropdownList;

    @ui.decorators.separatorField<JournalEntryType>({
        parent() {
            return this.generalBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    targetDocumentTypeSeparator: ui.fields.Separator;

    @ui.decorators.block<JournalEntryType>({
        parent() {
            return this.generalSection;
        },
        title: 'Header setup',
    })
    headerSetupBlock: ui.containers.Block;

    @ui.decorators.referenceField<JournalEntryType, Journal>({
        parent() {
            return this.headerSetupBlock;
        },
        node: '@sage/xtrem-finance-data/Journal',
        tunnelPage: '@sage/xtrem-finance-data/Journal',
        title: 'Journal',
        isMandatory() {
            return this.targetDocumentType.value === 'journalEntry';
        },
        isDisabled() {
            return this.targetDocumentType.value !== 'journalEntry';
        },
        valueField: 'name',
        helperTextField: 'id',
        lookupDialogTitle: 'Select journal',
        width: 'large',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.reference<JournalEntryType, Journal, Journal['legislation']>({
                bind: 'legislation',
                valueField: '_id',
            }),
        ],
        filter() {
            return { legislation: { id: { _eq: this.legislation.value?.id } } };
        },
    })
    headerJournal: ui.fields.Reference<Journal>;

    @ui.decorators.dropdownListField<JournalEntryType>({
        parent() {
            return this.headerSetupBlock;
        },
        title: 'Posting date',
        optionType: '@sage/xtrem-finance-data/HeaderPostingDate',
        width: 'large',
        isMandatory() {
            return this.targetDocumentType.value === 'journalEntry';
        },
        isDisabled() {
            return this.targetDocumentType.value !== 'journalEntry';
        },
    })
    headerPostingDate: ui.fields.DropdownList;

    @ui.decorators.dropdownListField<JournalEntryType>({
        parent() {
            return this.headerSetupBlock;
        },
        title: 'Description',
        optionType: '@sage/xtrem-finance-data/HeaderDescription',
        width: 'large',
    })
    headerDescription: ui.fields.DropdownList;

    @ui.decorators.separatorField<JournalEntryType>({
        parent() {
            return this.headerSetupBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    headerDescriptionSeparator: ui.fields.Separator;

    @ui.decorators.referenceField<JournalEntryType>({
        parent() {
            return this.headerSetupBlock;
        },
        node: '@sage/xtrem-finance-data/PostingClassDefinition',
        title: 'Account type',
        isMandatory() {
            return this.targetDocumentType.value !== 'journalEntry';
        },
        isDisabled() {
            return this.targetDocumentType.value === 'journalEntry';
        },
        valueField: 'accountTypeName',
        helperTextField: 'id',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select account type',
        width: 'large',
        columns: [
            ui.nestedFields.text({ bind: 'accountTypeName', title: 'Name' }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.text({ bind: 'postingClassType', title: 'Posting class type' }),
            ui.nestedFields.switch({ bind: 'isDetailed', title: 'Detailed' }),
        ],
    })
    headerAccountType: ui.fields.Reference<PostingClassDefinition>;

    @ui.decorators.dropdownListField<JournalEntryType>({
        parent() {
            return this.headerSetupBlock;
        },
        title: 'Amount type',
        optionType: '@sage/xtrem-finance-data/AmountType',
        width: 'large',
        isMandatory() {
            return this.targetDocumentType.value !== 'journalEntry';
        },
        isDisabled() {
            return this.targetDocumentType.value === 'journalEntry';
        },
    })
    headerAmountType: ui.fields.DropdownList;

    @ui.decorators.tableField<JournalEntryType, JournalEntryTypeLineBinding>({
        parent() {
            return this.generalSection;
        },
        bind: 'lines',
        title: 'Line setup',
        node: '@sage/xtrem-finance-data/JournalEntryTypeLine',
        canSelect: false,
        orderBy: { _sortValue: +1 },
        columns: [
            ui.nestedFields.technical({ bind: '_sortValue' }),
            ui.nestedFields.dropdownList({
                title: 'Movement type',
                bind: 'movementType',
                isMandatory: true,
                optionType: '@sage/xtrem-finance-data/movementType',
            }),
            ui.nestedFields.reference<JournalEntryType, JournalEntryTypeLineBinding, PostingClassDefinition>({
                title: 'Account type',
                bind: 'accountType',
                node: '@sage/xtrem-finance-data/PostingClassDefinition',
                valueField: 'accountTypeName',
                width: 'large',
                isMandatory: true,
                minLookupCharacters: 0,
                columns: [
                    ui.nestedFields.text({ bind: 'accountTypeName', title: 'Name' }),
                    ui.nestedFields.text({ bind: 'id' }),
                    ui.nestedFields.text({ bind: 'postingClassType', title: 'Posting class type' }),
                    ui.nestedFields.switch({ bind: 'isDetailed', title: 'Detailed' }),
                    ui.nestedFields.technical<JournalEntryType, PostingClassDefinition>({
                        bind: 'legislation',
                        node: '@sage/xtrem-structure/Legislation',
                        nestedFields: [ui.nestedFields.technical({ bind: '_id' })],
                    }),
                ],
                filter() {
                    return { legislation: { id: { _eq: this.legislation.value?.id } } };
                },
            }),
            ui.nestedFields.checkbox({ title: 'Stock items', bind: 'isStockItemAllowed', isReadOnly: true }),
            ui.nestedFields.checkbox({ title: 'Non stock items', bind: 'isNonStockItemAllowed', isReadOnly: true }),
            ui.nestedFields.checkbox({ title: 'Service items', bind: 'isServiceItemAllowed', isReadOnly: true }),
            ui.nestedFields.checkbox({
                title: 'Landed cost items',
                bind: 'isLandedCostItemAllowed',
                isReadOnly: true,
                isHidden() {
                    return !this.$.isServiceOptionEnabled('landedCostOption');
                },
            }),
            ui.nestedFields.dropdownList({
                title: 'Amount type',
                bind: 'amountType',
                isMandatory: true,
                optionType: '@sage/xtrem-finance-data/AmountType',
            }),
            ui.nestedFields.select({
                title: 'Sign',
                bind: 'sign',
                isMandatory() {
                    return this.targetDocumentType.value === 'journalEntry';
                },
                isDisabled() {
                    return this.targetDocumentType.value !== 'journalEntry';
                },
                optionType: '@sage/xtrem-finance-data/Sign',
            }),
            ui.nestedFields.dropdownList({
                title: 'Common reference',
                bind: 'commonReference',
                isMandatory: true,
                optionType: '@sage/xtrem-finance-data/CommonReference',
            }),
            ui.nestedFields.text({
                title: 'Contra account type',
                bind: { contraJournalEntryTypeLine: { accountType: { accountTypeName: true } } },
                isReadOnly: true,
            }),
            ui.nestedFields.dropdownList({
                title: 'Contra account amount type',
                bind: { contraJournalEntryTypeLine: { amountType: true } },
                isReadOnly: true,
            }),
        ],
        dropdownActions: [
            {
                icon: 'edit',
                title: 'Linked posting class',
                async onClick(_id, rowData) {
                    // if the header has a linked posting class definition (field headerAccountType)
                    // -> read the corresponding posting class lines to show the accounts
                    if (this.headerAccountType.value) {
                        this.sidePanelHeaderIsDetailed.value = this.headerAccountType.value.isDetailed || null;
                        this.sidePanelHeaderAccountTypeName.value =
                            this.headerAccountType.value.accountTypeName || null;

                        this.postingClassHeaderAccounts.value = (await this.$.graph
                            .node('@sage/xtrem-finance-data/PostingClassLine')
                            .queries.getPostingClassAccounts(
                                {
                                    postingClass: { _id: true, name: true },
                                    account: { _id: true, id: true, name: true, composedDescription: true },
                                    details: {
                                        tax: { id: true, name: true },
                                        account: { id: true, composedDescription: true },
                                    },
                                },
                                { postingClassDefinition: this.headerAccountType.value?._id || '' },
                            )
                            .execute()) as ui.PartialNodeWithId<PostingClassLineNode>[];
                    }

                    this.sidePanelIsDetailed.value = rowData.accountType?.isDetailed || null;
                    this.sidePanelAccountTypeName.value = rowData.accountType?.accountTypeName || null;

                    // read the posting class lines linked to the actual row to show the accounts
                    this.postingClassAccounts.value = (await this.$.graph
                        .node('@sage/xtrem-finance-data/PostingClassLine')
                        .queries.getPostingClassAccounts(
                            {
                                postingClass: { _id: true, name: true },
                                account: { _id: true, id: true, name: true, composedDescription: true },
                                details: {
                                    tax: { id: true, name: true },
                                    account: { id: true, composedDescription: true },
                                },
                            },
                            { postingClassDefinition: rowData.accountType?._id || '' },
                        )
                        .execute()) as ui.PartialNodeWithId<PostingClassLineNode>[];

                    this.sidePanelGeneralSection.isHidden = false;
                    await this.$.dialog
                        .custom('info', this.sidePanelGeneralSection, {
                            cancelButton: { isHidden: false },
                            acceptButton: { isHidden: false },
                            rightAligned: true,
                            size: 'extra-large',
                            dialogTitle: ui.localize(
                                '@sage/xtrem-finance-data/pages__journal_entry_type__side_panel_linked_posting_class_title',
                                'Linked posting class',
                            ),
                        })
                        .then(() => true)
                        .catch(() => false);
                    this.sidePanelGeneralSection.isHidden = true;
                },
            },
        ],
    })
    lines: ui.fields.Table;
}
