import { extractEdges } from '@sage/xtrem-client';
import type { Account as AccountNode, GraphApi } from '@sage/xtrem-finance-data-api';
import type { ChartOfAccount } from '@sage/xtrem-structure-api';
import {
    setApplicativePageCrudActions,
    setOrderOfPageHeaderDropDownActions,
} from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import type { Tax } from '@sage/xtrem-tax-api';
import * as ui from '@sage/xtrem-ui';
import { getCountryIds } from '../client-functions/account-helpers';
import type { DatevIdRange } from '../client-functions/datev';
import {
    checkEmptyDatevId,
    checkEmptyTax,
    formatStringWithLeadingZeros,
    getDatevIdRangeAccounts,
    validateDatevId,
} from '../client-functions/datev';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<Account, AccountNode>({
    module: 'finance',
    title: 'Account',
    objectTypeSingular: 'Account',
    objectTypePlural: 'Accounts',
    idField() {
        return this.name;
    },
    mode: 'tabs',
    menuItem: featuresFinancialIntegration,
    priority: 100,
    node: '@sage/xtrem-finance-data/Account',
    navigationPanel: {
        listItem: {
            title: ui.nestedFields.text({ bind: 'id' }),
            line2: ui.nestedFields.text({ bind: 'name' }),
            line3: ui.nestedFields.switch({ bind: 'isControl', title: 'Control', isHiddenOnMainField: true }),
            line_4: ui.nestedFields.switch({
                bind: 'isDirectEntryForbidden',
                title: 'Direct entry forbidden',
                isHiddenOnMainField: true,
            }),
            line_5: ui.nestedFields.reference({
                bind: 'chartOfAccount',
                node: '@sage/xtrem-structure/ChartOfAccount',
                valueField: 'name',
                title: 'Chart of accounts',
                isHiddenOnMainField: true,
                tunnelPage: undefined,
            }),
            line6: ui.nestedFields.dropdownList({
                bind: 'taxManagement',
                title: 'Tax management',
                optionType: '@sage/xtrem-finance-data/TaxManagement',
                isHiddenOnMainField: true,
            }),
            datevId: ui.nestedFields.text({
                bind: 'datevId',
                title: 'DATEV ID',
                isHiddenOnMainField: true,
            }),
        },

        async optionsMenu(graph) {
            const chartOfAccounts = extractEdges(
                await graph
                    .node('@sage/xtrem-structure/ChartOfAccount')
                    .query(
                        ui.queryUtils.edgesSelector<ChartOfAccount>(
                            { name: true, _id: true },
                            { filter: { isActive: true, legislation: { isActive: true } } },
                        ),
                    )
                    .execute(),
            );

            const charts = chartOfAccounts.map(chartOfAccount => ({
                title: `${chartOfAccount.name}`,
                graphQLFilter: { chartOfAccount: { _id: { _eq: chartOfAccount._id } } },
            }));
            return [
                {
                    title: ui.localize('@sage/xtrem-finance-data/pages__account__option_menu____title__all', 'All'),
                    graphQLFilter: {},
                },
                ...charts,
            ];
        },

        onOptionsMenuValueChange(_mainFilterValue, selectedFilter) {
            if (this.$.storage.get('chartOfAccount') !== selectedFilter) {
                this.$.router.emptyPage().catch(error => {
                    throw error;
                });
            }
            this.$.storage.set('onOptionsMenuValueChange', true);
            this.$.storage.set('chartOfAccount', selectedFilter ?? '');
            this.$.router.refresh().catch(error => {
                throw error;
            });
        },
    },
    createAction() {
        return this.$standardNewAction;
    },
    headerSection() {
        return this.headerSection;
    },
    headerDropDownActions() {
        return setOrderOfPageHeaderDropDownActions({
            remove: [this.$standardDeleteAction],
            dropDownBusinessActions: [this.$standardOpenRecordHistoryAction],
        });
    },
    headerQuickActions() {
        return [this.$standardDuplicateAction];
    },
    businessActions() {
        return [this.$standardCancelAction, this.save];
    },
    async onLoad() {
        this.$.storage.set('onOptionsMenuValueChange', false);
        setApplicativePageCrudActions({
            page: this,
            isDirty: false,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
        if (this.$.recordId) {
            await this.initAccountAttributeDimensionTypes(this.$.recordId);
            if (this.$.isServiceOptionEnabled('datevOption')) {
                this.countryIds = await getCountryIds(this);
                this.tax.isDisabled = !this.isAutomaticAccount.value;
                this.datevIdRange = await getDatevIdRangeAccounts(this);
                this.datevSection.isHidden = this.chartOfAccount.value?.legislation?.id !== 'DE';
                this.datevIdString.value = this.datevId.value
                    ? formatStringWithLeadingZeros(this.datevId.value.toString(), this.datevIdRange.length)
                    : '';
            }
        }
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
            duplicate: this.$standardDuplicateAction,
            remove: this.$standardDeleteAction,
        });
    },
})
export class Account extends ui.Page<GraphApi> {
    countryIds: string[];

    datevIdRange: DatevIdRange;

    @ui.decorators.pageAction<Account>({
        title: 'Save',
        access: { bind: '$update' },
        buttonType: 'primary',
        async onClick() {
            if (this.$.isServiceOptionEnabled('datevOption') && this.chartOfAccount.value?.legislation?.id === 'DE') {
                checkEmptyDatevId({ pageInstance: this });
                checkEmptyTax({ pageInstance: this });
            }
            await this.$standardSaveAction.execute(true);
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<Account>({ title: 'Header section', isTitleHidden: true })
    headerSection: ui.containers.Section;

    @ui.decorators.section<Account>({ title: 'Information', isTitleHidden: true })
    mainSection: ui.containers.Section;

    @ui.decorators.section<Account>({
        isTitleHidden: true,
        title: 'DATEV',
        isHidden() {
            return !this.$.isServiceOptionEnabled('datevOption');
        },
    })
    datevSection: ui.containers.Section;

    @ui.decorators.block<Account>({
        parent() {
            return this.headerSection;
        },
        width: 'extra-large',
    })
    headerBlock: ui.containers.Block;

    @ui.decorators.numericField<Account>({
        isHidden: true,
        title: '_id',
    })
    _id: ui.fields.Numeric;

    @ui.decorators.textField<Account>({
        parent() {
            return this.headerBlock;
        },
        title: 'ID',
        isMandatory: true,
    })
    id: ui.fields.Text;

    @ui.decorators.textField<Account>({
        parent() {
            return this.headerBlock;
        },
        title: 'Name',
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.switchField<Account>({
        parent() {
            return this.headerBlock;
        },
        title: 'Control',
    })
    isControl: ui.fields.Switch;

    @ui.decorators.block<Account>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.switchField<Account>({
        parent() {
            return this.mainBlock;
        },
        title: 'Active',
        fetchesDefaults: true,
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<Account>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.switchField<Account>({
        parent() {
            return this.mainBlock;
        },
        title: 'Direct entry forbidden',
    })
    isDirectEntryForbidden: ui.fields.Switch;

    @ui.decorators.referenceField<Account>({
        parent() {
            return this.mainBlock;
        },
        title: 'Chart of accounts',
        width: 'medium',
        node: '@sage/xtrem-structure/ChartOfAccount',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select chart of account',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.reference({
                bind: 'legislation',
                columns: [ui.nestedFields.technical({ bind: 'name' }), ui.nestedFields.technical({ bind: 'id' })],
            }),
        ],
        async onChange() {
            if (this.$.isServiceOptionEnabled('datevOption')) {
                this.tax.value = null;
                if (this.chartOfAccount.value) {
                    this.countryIds = await getCountryIds(this);
                    this.datevSection.isHidden = (await (await this.chartOfAccount.value.legislation).id) !== 'DE';
                } else {
                    this.datevSection.isHidden = true;
                }
            }
        },
    })
    chartOfAccount: ui.fields.Reference;

    @ui.decorators.block<Account>({
        parent() {
            return this.mainSection;
        },
        title: 'Tax management',
    })
    TaxManagementBlock: ui.containers.Block;

    @ui.decorators.block<Account>({
        parent() {
            return this.mainSection;
        },
        title: 'Required dimensions',
    })
    attributeDimensionTypesBlock: ui.containers.Block;

    @ui.decorators.dropdownListField<Account>({
        parent() {
            return this.TaxManagementBlock;
        },
        title: 'Tax management',
        optionType: '@sage/xtrem-finance-data/TaxManagement',
        isMandatory: true,
        isReadOnly() {
            // TODO: check TaxCategoryOnSubject
            return this.chartOfAccount.value === null;
        },
    })
    taxManagement: ui.fields.DropdownList;

    @ui.decorators.tableField<Account>({
        parent() {
            return this.attributeDimensionTypesBlock;
        },
        isTransient: true,
        isReadOnly: true,
        title: 'Required dimensions',
        canSelect: false,
        isFullWidth: false,
        columns: [
            ui.nestedFields.text({ bind: 'accountAttributeDimensionTypeId', isHidden: true }),
            ui.nestedFields.text({ bind: 'attributeDimensionTypeId', isHidden: true }),
            ui.nestedFields.text({ bind: 'id' }),
            ui.nestedFields.dropdownList({
                title: 'Analytical type',
                bind: 'type',
                optionType: '@sage/xtrem-finance-data/AnalyticalMeasureType',
            }),
        ],
    })
    attributeDimensionTypes: ui.fields.Table;

    @ui.decorators.block<Account>({
        parent() {
            return this.datevSection;
        },
        width: 'extra-large',
    })
    datevBlock: ui.containers.Block;

    @ui.decorators.numericField<Account>({
        isHidden: true,
    })
    datevId: ui.fields.Numeric;

    @ui.decorators.textField<Account>({
        parent() {
            return this.datevBlock;
        },
        title: 'DATEV ID',
        isTransient: true,
        validation(value: string | undefined) {
            return validateDatevId({
                datevId: value,
                first: this.datevIdRange.fromValue,
                last: this.datevIdRange.toValue,
            });
        },
        onChange() {
            this.datevId.value = Number(this.datevIdString.value);
            this.datevIdString.value = this.datevIdString.value
                ? formatStringWithLeadingZeros(this.datevIdString.value, this.datevIdRange.length)
                : '';
        },
    })
    datevIdString: ui.fields.Text;

    @ui.decorators.separatorField<Account>({
        parent() {
            return this.datevBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    datevIdSeparator: ui.fields.Separator;

    @ui.decorators.switchField<Account>({
        parent() {
            return this.datevBlock;
        },
        title: 'Automatic account',
        onChange() {
            if (!this.isAutomaticAccount.value) {
                this.tax.value = null;
            }
            this.tax.isDisabled = !this.isAutomaticAccount.value;
        },
    })
    isAutomaticAccount: ui.fields.Switch;

    @ui.decorators.referenceField<Account, Tax>({
        parent() {
            return this.datevBlock;
        },
        title: 'Tax',
        columns: [
            ui.nestedFields.text({ bind: 'name' }),
            ui.nestedFields.reference({
                lookupDialogTitle: 'Select country',
                bind: 'country',
                columns: [ui.nestedFields.text({ bind: 'name' }), ui.nestedFields.text({ bind: 'id' })],
            }),
        ],
        node: '@sage/xtrem-tax/Tax',
        valueField: 'name',
        minLookupCharacters: 1,
        isDisabled() {
            return !this.isAutomaticAccount.value;
        },
        filter() {
            return {
                country: {
                    id: { _in: this.countryIds },
                },
            };
        },
    })
    tax: ui.fields.Reference<Tax>;

    async getChartOfAccountByName(name: string) {
        const filter = { filter: { name } };
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-structure/ChartOfAccount')
                .query(ui.queryUtils.edgesSelector({ _id: true, name: true }, filter))
                .execute(),
        );

        if (result.length > 0) {
            return result.at(0);
        }
        return null;
    }

    async checkTaxCategoryOnSubject(legislation: string) {
        const filter = { filter: { isSubjectToGlTaxExcludedAmount: true, legislation } };
        const result = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-tax/TaxCategory')
                .query(ui.queryUtils.edgesSelector({ id: true, name: true }, filter))
                .execute(),
        );

        return result.length > 0;
    }

    async initAccountAttributeDimensionTypes(accountId: string) {
        const attributeAndDimension = (await this.getAttributeTypes(accountId)).concat(
            await this.getDimensionTypes(accountId),
        );

        attributeAndDimension.forEach(gridLine => {
            this.attributeDimensionTypes.addOrUpdateRecordValue(gridLine);
        });
        this.$.setPageClean();
    }

    async getAttributeTypes(accountId: string) {
        const attributeTypes = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/AccountAttributeType')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true, attributeType: { name: true, _id: true } },
                        { filter: { account: accountId, isRequired: true } },
                    ),
                )
                .execute(),
        ).map(element => ({
            accountAttributeDimensionTypeId: element._id,
            attributeDimensionTypeId: element.attributeType._id,
            id: element.attributeType.name,
            type: 'attribute',
        }));
        if (attributeTypes) {
            return attributeTypes;
        }
        return [];
    }

    async getDimensionTypes(accountId: string) {
        const dimensionTypes = extractEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/AccountDimensionType')
                .query(
                    ui.queryUtils.edgesSelector(
                        { _id: true, dimensionType: { name: true, _id: true } },
                        { filter: { account: accountId, isRequired: true } },
                    ),
                )
                .execute(),
        ).map(element => ({
            accountAttributeDimensionTypeId: element._id,
            attributeDimensionTypeId: element.dimensionType._id,
            id: element.dimensionType.name,
            type: 'dimension',
        }));
        if (dimensionTypes) {
            return dimensionTypes;
        }
        return [];
    }
}
