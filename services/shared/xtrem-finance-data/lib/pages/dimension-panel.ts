import type { Filter } from '@sage/xtrem-client';
import type { Attribute, Dimension, GraphApi } from '@sage/xtrem-finance-data-api';
import * as ui from '@sage/xtrem-ui';
import { isEmpty } from 'lodash';
import {
    getAttributeTypes,
    manageAttributesDimensions,
    manageAttributeTypeRestrictedToOnPage,
    prepareAttributes,
    prepareDimensions,
    setAttributesLineValues,
} from '../client-functions/attributes-and-dimensions';
import type { Analytical, DimensionsPanelDisabledActions } from '../client-functions/interfaces/dimension';

@ui.decorators.page<DimensionPanel>({
    title: 'Dimensions',
    mode: 'default',
    module: 'finance',
    businessActions() {
        return [this.cancel, this.applyOnNew, this.apply, this.applyAll, this.applyReleasedItem, this.ok];
    },
    async onLoad() {
        await this.init();
    },
})
export class DimensionPanel extends ui.Page<GraphApi> {
    editable: boolean;

    enteredDimensions: Analytical;

    isDefaultDimensionPage: boolean;

    isDimensionPageToInherit: boolean;

    disabledAction: DimensionsPanelDisabledActions;

    calledFromMainGrid: boolean;

    taskRestrictedTo: string | null;

    isTaskLinkedToItem: boolean;

    isProjectLinkedToSite: boolean;

    defaultedFromItem: string;

    @ui.decorators.pageAction<DimensionPanel>({
        title: 'OK',
        buttonType: 'primary',
        onClick() {
            this.$.finish({
                attributes: prepareAttributes(this),
                dimensions: prepareDimensions(this),
            });
        },
    })
    ok: ui.PageAction;

    @ui.decorators.pageAction<DimensionPanel>({
        title: 'Apply to all lines',
        buttonType: 'primary',
        onClick() {
            this.$.finish({
                attributes: prepareAttributes(this),
                dimensions: prepareDimensions(this),
                action: 'apply',
            });
        },
    })
    apply: ui.PageAction;

    @ui.decorators.pageAction<DimensionPanel>({
        title: 'Apply to new lines only',
        buttonType: 'secondary',
        onClick() {
            this.$.finish({
                attributes: prepareAttributes(this),
                dimensions: prepareDimensions(this),
                action: 'applyOnNew',
            });
        },
    })
    applyOnNew: ui.PageAction;

    @ui.decorators.pageAction<DimensionPanel>({
        title: 'Apply to released item only',
        buttonType: 'secondary',
        onClick() {
            this.$.finish({
                attributes: prepareAttributes(this),
                dimensions: prepareDimensions(this),
                action: 'applyHeader',
            });
        },
    })
    applyReleasedItem: ui.PageAction;

    @ui.decorators.pageAction<DimensionPanel>({
        title: 'Apply to all',
        buttonType: 'primary',
        onClick() {
            this.$.finish({
                attributes: prepareAttributes(this),
                dimensions: prepareDimensions(this),
                action: 'applyAll',
            });
        },
    })
    applyAll: ui.PageAction;

    @ui.decorators.pageAction<DimensionPanel>({
        title: 'Cancel',
        buttonType: 'tertiary',
        onClick() {
            this.$.finish();
        },
    })
    cancel: ui.PageAction;

    @ui.decorators.section<DimensionPanel>({ isTitleHidden: true }) dimensionsSection: ui.containers.Section;

    @ui.decorators.block<DimensionPanel>({
        parent() {
            return this.dimensionsSection;
        },
        isTitleHidden: true,
    })
    textBlock: ui.containers.Block;

    @ui.decorators.block<DimensionPanel>({
        parent() {
            return this.dimensionsSection;
        },
    })
    attributeBlock: ui.containers.Block;

    @ui.decorators.block<DimensionPanel>({
        parent() {
            return this.dimensionsSection;
        },
    })
    dimensionBlock: ui.containers.Block;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 01',
        isHidden: true,
        title: 'Dimension 01',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType01' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension01: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 02',
        isHidden: true,
        title: 'Dimension 02',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType02' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension02: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 03',
        isHidden: true,
        title: 'Dimension 03',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType03' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension03: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 04',
        isHidden: true,
        title: 'Dimension 04',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType04' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension04: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 05',
        isHidden: true,
        title: 'Dimension 05',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType05' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension05: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 06',
        isHidden: true,
        title: 'Dimension 06',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType06' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension06: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 07',
        isHidden: true,
        title: 'Dimension 07',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType07' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension07: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 08',
        isHidden: true,
        title: 'Dimension 08',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType08' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension08: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 09',
        isHidden: true,
        title: 'Dimension 09',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType09' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension09: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 10',
        isHidden: true,
        title: 'Dimension 10',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType10' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension10: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 11',
        isHidden: true,
        title: 'Dimension 11',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType11' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension11: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 12',
        isHidden: true,
        title: 'Dimension 12',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType12' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension12: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 13',
        isHidden: true,
        title: 'Dimension 13',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType13' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension13: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 14',
        isHidden: true,
        title: 'Dimension 14',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType14' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension14: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 15',
        isHidden: true,
        title: 'Dimension 15',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType15' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension15: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 16',
        isHidden: true,
        title: 'Dimension 16',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType16' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension16: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 17',
        isHidden: true,
        title: 'Dimension 17',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType17' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension17: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 18',
        isHidden: true,
        title: 'Dimension 18',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType18' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension18: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 19',
        isHidden: true,
        title: 'Dimension 19',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType19' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension19: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Dimension>({
        parent() {
            return this.dimensionBlock;
        },
        node: '@sage/xtrem-finance-data/Dimension',
        valueField: 'name',
        minLookupCharacters: 0,
        lookupDialogTitle: 'Select dimension 20',
        isHidden: true,
        title: 'Dimension 20',
        helperTextField: 'id',
        filter: { dimensionType: { docProperty: { _eq: 'dimensionType20' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),
            ui.nestedFields.reference<DimensionPanel, Dimension, Dimension['dimensionType']>({
                bind: 'dimensionType',
                title: 'Dimension type',
                node: '@sage/xtrem-finance-data/DimensionType',
                isHidden: true,
                valueField: 'docProperty',
                columns: [
                    ui.nestedFields.text({ title: 'Document property', bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'docProperty' }),
                    ui.nestedFields.technical({ bind: 'isActive' }),
                ],
            }),
        ],
    })
    dimension20: ui.fields.Reference<Dimension>;

    @ui.decorators.referenceField<DimensionPanel, Attribute>({
        parent() {
            return this.attributeBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select employee',
        isHidden: true,
        title: 'Employee',
        helperTextField: 'id',
        filter: { attributeType: { id: { _eq: 'employee' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<DimensionPanel, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                isHidden: true,
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
        ],
    })
    employee: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<DimensionPanel, Attribute>({
        parent() {
            return this.attributeBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select project',
        isHidden: true,
        title: 'Project',
        helperTextField: 'id',
        filter: { attributeType: { id: { _eq: 'project' }, isActive: true }, isActive: true },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.reference<DimensionPanel, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
            ui.nestedFields.technical({ bind: 'attributeRestrictedToId' }),
            ui.nestedFields.reference<DimensionPanel>({
                title: 'Site',
                bind: 'site',
                node: '@sage/xtrem-system/Site',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
                isHidden() {
                    return !this.isProjectLinkedToSite;
                },
            }),
        ],
        onChange() {
            manageAttributeTypeRestrictedToOnPage({ page: this, clearTask: true });
        },
    })
    project: ui.fields.Reference<Attribute>;

    @ui.decorators.referenceField<DimensionPanel, Attribute>({
        parent() {
            return this.attributeBlock;
        },
        node: '@sage/xtrem-finance-data/Attribute',
        valueField: 'name',
        minLookupCharacters: 1,
        lookupDialogTitle: 'Select task',
        isHidden: true,
        title: 'Task',
        helperTextField: 'id',
        filter(): Filter<Attribute> {
            return {
                attributeType: { id: { _eq: 'task' }, isActive: true },
                isActive: true,
                attributeRestrictedToId: this.project?.value?.id || '',
            };
        },
        columns: [
            ui.nestedFields.text({ title: 'Name', bind: 'name' }),
            ui.nestedFields.text({ title: 'ID', bind: 'id' }),
            ui.nestedFields.technical({ bind: 'isActive' }),

            ui.nestedFields.reference<DimensionPanel, Attribute, Attribute['attributeType']>({
                title: 'Attribute type',
                bind: 'attributeType',
                node: '@sage/xtrem-finance-data/AttributeType',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
            }),
            ui.nestedFields.technical({ bind: 'attributeRestrictedToId' }),
            ui.nestedFields.technical({ bind: 'name' }),
            ui.nestedFields.technical({ bind: 'id' }),
            ui.nestedFields.reference<DimensionPanel>({
                title: 'Item',
                bind: 'item',
                node: '@sage/xtrem-master-data/Item',
                valueField: 'id',
                columns: [ui.nestedFields.technical({ bind: 'id' }), ui.nestedFields.technical({ bind: 'isActive' })],
                isHidden() {
                    return !this.isTaskLinkedToItem;
                },
            }),
        ],
    })
    task: ui.fields.Reference<Attribute>;

    private async init() {
        this.editable = JSON.parse(this.$.queryParameters.editable as string);
        this.isDefaultDimensionPage = JSON.parse(this.$.queryParameters.isDefaultDimensionPage as string);
        this.isDimensionPageToInherit = JSON.parse(this.$.queryParameters.isDimensionPageToInherit as string);
        this.disabledAction = JSON.parse(this.$.queryParameters.disabledAction as string) || {};
        this.calledFromMainGrid = JSON.parse(this.$.queryParameters.calledFromMainGrid as string);
        this.defaultedFromItem = JSON.parse(this.$.queryParameters.defaultedFromItem as string) || {};
        if (!isEmpty(this.defaultedFromItem)) {
            this.textBlock.title = this.defaultedFromItem;
            this.textBlock.isTitleHidden = false;
        }

        this.ok.isDisabled = this.disabledAction.ok || !this.editable;
        this.apply.isDisabled = this.disabledAction.apply || !this.editable;
        this.applyOnNew.isDisabled = this.disabledAction.applyOnNew || !this.editable;

        await manageAttributesDimensions({
            page: this,
            isEditable: this.editable,
        });

        this.enteredDimensions = JSON.parse(this.$.queryParameters.enteredDimensions as string);
        await setAttributesLineValues({
            page: this,
            inputData: this.enteredDimensions,
        });
        if (this.isDefaultDimensionPage || this.isDimensionPageToInherit) {
            this.ok.isHidden = true;
            if (this.isDefaultDimensionPage) {
                this.applyAll.isHidden = true;
                this.applyReleasedItem.isHidden = true;
            } else {
                this.apply.isHidden = true;
                this.applyOnNew.isHidden = true;
            }
        } else {
            this.applyAll.isHidden = true;
            this.applyReleasedItem.isHidden = true;
            this.applyOnNew.isHidden = true;
            if (this.calledFromMainGrid) {
                this.ok.isHidden = true;
            } else {
                this.apply.isHidden = true;
            }
        }

        // Disable attribute type task if project is null and task is linked to project
        const activeAttributeTypes = await getAttributeTypes(this);
        const attributeTypeTask = activeAttributeTypes.find(attributeType => attributeType.id === 'task');
        const attributeTypeProject = activeAttributeTypes.find(attributeType => attributeType.id === 'project');
        this.taskRestrictedTo = attributeTypeTask?.attributeTypeRestrictedTo?.id || null;
        this.isProjectLinkedToSite = attributeTypeProject?.isLinkedToSite || false;
        this.isTaskLinkedToItem = attributeTypeTask?.isLinkedToItem || false;

        if (this.taskRestrictedTo === 'project' && this.project.value === null) {
            this.task.isDisabled = true;
        } else {
            this.task.isDisabled = this.editable === false;
        }
    }
}
