import { withoutEdges } from '@sage/xtrem-client';
import type { DatevConfiguration as DatevConfigurationNode, GraphApi } from '@sage/xtrem-finance-data-api';
import { setApplicativePageCrudActions } from '@sage/xtrem-system/build/lib/client-functions/applicative-crud-actions';
import * as ui from '@sage/xtrem-ui';
import { inRange } from 'lodash';
import {
    datevConfigurationControlsOnSave,
    formatStringWithLeadingZeros,
    recalculateAndSetRangeStrings,
    setRangeStrings,
    validateDatevId,
} from '../client-functions/datev';
import { featuresFinancialIntegration } from '../menu-items/features-financial-integration';

@ui.decorators.page<DatevConfiguration, DatevConfigurationNode>({
    module: 'finance',
    title: 'DATEV configuration',
    idField() {
        return this.id;
    },
    menuItem: featuresFinancialIntegration,
    priority: 1000,
    node: '@sage/xtrem-finance-data/DatevConfiguration',
    businessActions() {
        return [this.$standardCancelAction, this.save];
    },
    onDirtyStateUpdated(isDirty: boolean) {
        setApplicativePageCrudActions({
            page: this,
            isDirty,
            save: this.save,
            cancel: this.$standardCancelAction,
        });
    },
    async defaultEntry() {
        const [configuration] = withoutEdges(
            await this.$.graph
                .node('@sage/xtrem-finance-data/DatevConfiguration')
                .query(ui.queryUtils.edgesSelector({ _id: true }, { filter: { id: 'DATEV' } }))
                .execute(),
        );
        if (configuration) {
            return configuration._id;
        }
        return '';
    },
    onLoad() {
        setRangeStrings(this);
        this.skrCoaString.value = this.skrCoa.value
            ? formatStringWithLeadingZeros(this.skrCoa.value.toString(), 2)
            : '';
        this.$.setPageClean();
    },
})
export class DatevConfiguration extends ui.Page<GraphApi> {
    @ui.decorators.pageAction<DatevConfiguration>({
        title: 'Save',
        access: { bind: '$update' },
        buttonType: 'primary',
        async onClick() {
            await datevConfigurationControlsOnSave(this);
            await this.$standardSaveAction.execute(true);
        },
    })
    save: ui.PageAction;

    @ui.decorators.section<DatevConfiguration>({
        isTitleHidden: true,
        title: 'General',
    })
    mainSection: ui.containers.Section;

    @ui.decorators.block<DatevConfiguration>({
        parent() {
            return this.mainSection;
        },
        width: 'extra-large',
    })
    mainBlock: ui.containers.Block;

    @ui.decorators.numericField<DatevConfiguration>({
        isHidden: true,
    })
    customerRangeStart: ui.fields.Numeric;

    @ui.decorators.numericField<DatevConfiguration>({
        isHidden: true,
    })
    customerRangeEnd: ui.fields.Numeric;

    @ui.decorators.numericField<DatevConfiguration>({
        isHidden: true,
    })
    supplierRangeStart: ui.fields.Numeric;

    @ui.decorators.numericField<DatevConfiguration>({
        isHidden: true,
    })
    supplierRangeEnd: ui.fields.Numeric;

    @ui.decorators.switchField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        fetchesDefaults: true,
        title: 'Active',
    })
    isActive: ui.fields.Switch;

    @ui.decorators.separatorField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    isActiveSeparator: ui.fields.Separator;

    @ui.decorators.textField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
    })
    name: ui.fields.Text;

    @ui.decorators.textField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
        isReadOnly: true,
    })
    id: ui.fields.Text;

    @ui.decorators.separatorField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    separator: ui.fields.Separator;

    @ui.decorators.numericField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
        validation(value: number | undefined) {
            return validateDatevId({
                datevId: value ? value.toString() : undefined,
                first: 4,
                last: 8,
            });
        },
        onChange() {
            if (this.accountLength.value && inRange(Number(this.accountLength.value), 4, 9)) {
                this.customerSupplierLength.value = this.accountLength.value + 1;
                recalculateAndSetRangeStrings(this);
            }
        },
    })
    accountLength: ui.fields.Numeric;

    @ui.decorators.numericField<DatevConfiguration>({
        isHidden: true,
    })
    skrCoa: ui.fields.Numeric;

    @ui.decorators.textField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
        title: 'SKR',
        isTransient: true,
        validation(value: string | undefined) {
            return validateDatevId({
                datevId: value,
                first: 1,
                last: 99,
            });
        },
        onChange() {
            this.skrCoa.value = Number(this.skrCoaString.value);
            this.skrCoaString.value = this.skrCoaString.value
                ? formatStringWithLeadingZeros(this.skrCoaString.value, 2)
                : '';
        },
    })
    skrCoaString: ui.fields.Text;

    @ui.decorators.separatorField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isFullWidth: true,
        isInvisible: true,
    })
    coaSeparator: ui.fields.Separator;

    @ui.decorators.numericField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isMandatory: true,
        title: 'Customer and supplier ID length',
        validation(value: number | undefined) {
            return validateDatevId({
                datevId: value ? value.toString() : undefined,
                first: 5,
                last: 9,
            });
        },
        onChange() {
            if (this.customerSupplierLength.value && inRange(Number(this.customerSupplierLength.value), 5, 10)) {
                this.accountLength.value = this.customerSupplierLength.value - 1;
                recalculateAndSetRangeStrings(this);
            }
        },
    })
    customerSupplierLength: ui.fields.Numeric;

    @ui.decorators.textField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isTransient: true,
        title: 'Customer ID range',
    })
    customerIdRange: ui.fields.Text;

    @ui.decorators.textField<DatevConfiguration>({
        parent() {
            return this.mainBlock;
        },
        isReadOnly: true,
        isTransient: true,
        title: 'Supplier ID range',
    })
    supplierIdRange: ui.fields.Text;
}
